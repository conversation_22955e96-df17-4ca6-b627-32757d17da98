﻿<!DOCTYPE html>
<html xmlns="http://www.w3.org/1999/xhtml"
      xmlns:th="http://www.thymeleaf.org">
<head>
    <title>左侧是栏目树，右侧是栏目内容列表</title>
    <meta http-equiv="Content-Type" content="text/html; charset=utf-8"/>
    <meta http-equiv="X-UA-Compatible" content="IE=edge" />
    <meta name="viewport" content="width=device-width, initial-scale=1, shrink-to-fit=no"/>
    <!--
    <link href="http://************:8088/simbestui/fonts/iconfont/iconfont.css?v=svn.revision" th:href="@{http://************:8088/simbestui/fonts/iconfont/iconfont.css?v=svn.revision}" rel="stylesheet"/>
    -->
    <link href="../../fonts/iconfont/iconfont.css?v=svn.revision" th:href="@{/fonts/iconfont/iconfont.css?v=svn.revision}" rel="stylesheet"/>
    <link href="http://************:8088/simbestui/js/themes/default/easyui.css?v=svn.revision" th:href="@{http://************:8088/simbestui/js/themes/default/easyui.css?v=svn.revision}" rel="stylesheet"/>
    <link href="http://************:8088/simbestui/js/themes/icon.css?v=svn.revision" th:href="@{http://************:8088/simbestui/js/themes/icon.css?v=svn.revision}" rel="stylesheet"/>
    <link href="http://************:8088/simbestui/css/public.css?v=svn.revision" th:href="@{http://************:8088/simbestui/css/public.css?v=svn.revision}" rel="stylesheet"/>
    <link href="http://************:8088/simbestui/css/pages.css?v=svn.revision" th:href="@{http://************:8088/simbestui/css/pages.css?v=svn.revision}" rel="stylesheet"/>
    <script src="http://************:8088/simbestui/js/jquery.min.js?v=svn.revision" th:src="@{http://************:8088/simbestui/js/jquery.min.js?v=svn.revision}" type="text/javascript"></script>
    <script src="http://************:8088/simbestui/js/jquery.easyui.min.js?v=svn.revision" th:src="@{http://************:8088/simbestui/js/jquery.easyui.min.js?v=svn.revision}" type="text/javascript"></script>
    <script src="http://************:8088/simbestui/js/easyui-lang-zh_CN.js?v=svn.revision" th:src="@{http://************:8088/simbestui/js/easyui-lang-zh_CN.js?v=svn.revision}" type="text/javascript"></script>
    <script src="../../js/jquery.config.js?v=svn.revision" th:src="@{/js/jquery.config.js?v=svn.revision}" type="text/javascript"></script>
    <script src="http://************:8088/simbestui/js/jquery.zjsfile.js?v=svn.revision" th:src="@{http://************:8088/simbestui/js/jquery.zjsfile.js?v=svn.revision}" type="text/javascript"></script>
    <script src="http://************:8088/simbestui/js/jquery.aduq.js?v=svn.revision" th:src="@{http://************:8088/simbestui/js/jquery.aduq.js?v=svn.revision}" type="text/javascript"></script>
    <script src="http://************:8088/simbestui/js/jquery.process.js?v=svn.revision" th:src="@{http://************:8088/simbestui/js/jquery.process.js?v=svn.revision}" type="text/javascript"></script>
</head>
<body class="page_body">
<div class="leftdata orgPosL">
<ul id="programmaTree"></ul>
</div>
<div class="rightData orgPosR">
    <!--searchform-->
    <form id="orgPositionTableQueryForm">
        <table border="0" cellpadding="0" cellspacing="6" width="100%">
            <tr>
                <td width="30" align="right">标题</td><td width="150"><input name="title" type="text" value="" /></td>
                <td>
                    <div class="w100">
                        <a class="btn a_primary fl searchtable"><span>查询</span></a>
                    </div>
                </td>
            </tr>
        </table>
    </form>
    <!--table-->
   <div class="orgPositionTable plf10"><table id="orgPositionTable"></table></div>
</div>
<script type="text/javascript">
	var gps=getQueryString();
	$(function(){
		loadD();
		$("#programmaTree").tree({
			url:web.rootdir+"action/programaInfo/findRootAndNext",
			lines:true,//是否显示树控件上的虚线			
			treeId:'programaCode',
			treePid:'parentProgramaCode',
            fileds:'programaCode|id,programaName|text,parentProgramaCode,programaName',
			animate:true,//节点在展开或折叠的时候是否显示动画效果
            onClick:function(node){
				if(node.children){
					if(node.children.length==0) top.mesAlert("提示信息","该栏目无下级组织！", 'info');
				}else{	
					ajaxgeneral({
						url:"action/programaInfo/findSonByParentCode",
						data:{"parentProgramaCode":node.id},
						success:function(data){
                            loadD(node.id);
							if(data.data.length==0){

							}else{
                                var datas=[];
                                for(var i in data.data){
                                    var datai={};
                                    datai.id=data.data[i].programaCode;
                                    datai.text=data.data[i].programaName;
                                    datai.parentProgramaCode=data.data[i].parentProgramaCode;
                                    datas.push(datai);
                                }
                                $("#programmaTree").tree("append", {
                                    parent : node.target,
                                    data : datas
                                });
                                treeLoadSuccess();
							}
						}
					});
				}
			},
            onSelect:function(node){
                loadD(node.id);
            },
			onLoadSuccess:function(node, data){
				if(gps.mod) treeLoadSuccess("tree");
			}
		});
	});	
	//数据加载成功
	function treeLoadSuccess(type){
		var chooseRow=top.chooseWeb[gps.name]?top.chooseWeb[gps.name]:{};
		if(type=="tree"){
			var nodei=$("#programmaTree").tree("find",chooseRow.data.orgCode);
			if(nodei) 
				$("#programmaTree").tree("select",nodei.target);
		}
		if(type=="data"){
		    if (chooseRow.data.programaDataId){
                var ids=chooseRow.data.programaDataId.split(",");
                for(var i in ids){
                    var index=$("#orgPositionTable").datagrid("getRowIndex",ids[i]);
                    $("#orgPositionTable").datagrid("checkRow",index);
                }
            }
		}		
	};
	window.getchoosedata=function(){
		var data=$("#orgPositionTable").datagrid("getChecked");
		if(data.length==0){
			top.mesAlert("提示信息", "未选择栏目内容！", 'warning');
			return {"state":0};
		}
		var ids=[],name=[];
		for(var i in data){
			ids.push(data[i].id);
			name.push(data[i].title);
		}
		return {"data":{"programaDataId":ids.join(","),"positionDataName":name.join(",")},"state":1,"mod":gps.mod?gps.mod:-1};//state一般用于是否必选(必选时state为１表示选择有内容，为０表示没有选择，可以return {"data":[],"state":0};之前加弹出提示)因为当前不是必选所以state为１
	};
	//表格
	function loadD(programaCode){
		loadGrid({
			"listtable":{
				"listname":"#orgPositionTable",//table列表的id名称，需加#
				"querycmd":"action/approvalForm/findDataFromProgramaCode",//table列表的查询命令
				//"contentType":"application/json; charset=utf-8",//table列表的传参方式，如果是json就写，不写默认kv
				"nowrap": true,//把数据显示在一行里,默认true
                    "styleClass": "noScroll",					
				"checkboxall":false,
				"collapsible":true,
				"queryParams":{"programaCode":programaCode?programaCode:""},
				"frozenColumns":[[
					{ field: "ck",checkbox:true}
				]],//固定在左侧的列
				"columns":[[//列   
					{ title: "标题", field: "title", width: 120},
					{ title: "栏目名", field: "programaDisplayName", width: 120},
                    { title: "发起部门", field: "belongDepartmentName", width: 120}
				]],
				onLoadSuccess:function(data){	
					if(gps.mod) treeLoadSuccess("data");
				}
			}
		});
	};
</script>
</body>
</html>
