package com.simbest.boot.hnjjwz.backstage.indexMenu.service;


import com.simbest.boot.base.service.ILogicService;
import com.simbest.boot.base.web.response.JsonResponse;
import com.simbest.boot.hnjjwz.backstage.indexMenu.model.IndexMenu;
import com.simbest.boot.hnjjwz.backstage.programa.model.ProgramaInfo;
import org.springframework.data.domain.Pageable;

import java.util.List;
import java.util.Map;
import java.util.Set;

/**
 * <strong>Title : </strong><br>
 * <strong>Description : </strong><br>
 * <strong>Create on : 2018/6/19</strong><br>
 * <strong>Modify on : 2018/6/19</strong><br>
 * <strong>Copyright (C) Ltd.</strong><br>
 *
 * <AUTHOR> 刘萌@simbest.com.cn
 * @version <strong>V1.0.0</strong><br>
 * <strong>修改历史:</strong><br>
 * 修改人 修改日期 修改描述<br>
 * -------------------------------------------<br>
 */
public interface IIndexMenuService extends ILogicService<IndexMenu,String> {

    /**
     * 菜单名(模糊)、菜单类型(精确)
     * @param mapObject
     * @param pageable
     * @return
     */
    JsonResponse findAllDim(Map<String,Object> mapObject, Pageable pageable);


    List<IndexMenu> findIndexMenu();


    /**
     * 页面初始化时获取根栏目以及下一级栏目
     * @return
     */
    Set<IndexMenu> findRootAndNext();
}
