<!DOCTYPE html>
<html xmlns="http://www.w3.org/1999/xhtml"
      xmlns:th="http://www.thymeleaf.org">
<head>
    <title>培训资料目录</title>
    <meta http-equiv="Content-Type" content="text/html; charset=utf-8"/>
    <meta http-equiv="X-UA-Compatible" content="IE=edge" />
    <meta name="viewport" content="width=device-width, initial-scale=1, shrink-to-fit=no"/>
    <!--
    <link href="http://************:8088/simbestui/fonts/iconfont/iconfont.css?v=svn.revision" th:href="@{http://************:8088/simbestui/fonts/iconfont/iconfont.css?v=svn.revision}" rel="stylesheet"/>
    -->
    <link href="../../fonts/iconfont/iconfont.css?v=svn.revision" th:href="@{/fonts/iconfont/iconfont.css?v=svn.revision}" rel="stylesheet"/>
    <link href="http://************:8088/simbestui/js/themes/default/easyui.css?v=svn.revision" th:href="@{http://************:8088/simbestui/js/themes/default/easyui.css?v=svn.revision}" rel="stylesheet"/>
    <link href="http://************:8088/simbestui/js/themes/icon.css?v=svn.revision" th:href="@{http://************:8088/simbestui/js/themes/icon.css?v=svn.revision}" rel="stylesheet"/>
    <link href="http://************:8088/simbestui/css/public.css?v=svn.revision" th:href="@{http://************:8088/simbestui/css/public.css?v=svn.revision}" rel="stylesheet"/>
    <link href="http://************:8088/simbestui/css/pages.css?v=svn.revision" th:href="@{http://************:8088/simbestui/css/pages.css?v=svn.revision}" rel="stylesheet"/>
    <script src="http://************:8088/simbestui/js/jquery.min.js?v=svn.revision" th:src="@{http://************:8088/simbestui/js/jquery.min.js?v=svn.revision}" type="text/javascript"></script>
    <script src="http://************:8088/simbestui/js/jquery.easyui.min.js?v=svn.revision" th:src="@{http://************:8088/simbestui/js/jquery.easyui.min.js?v=svn.revision}" type="text/javascript"></script>
    <script src="http://************:8088/simbestui/js/easyui-lang-zh_CN.js?v=svn.revision" th:src="@{http://************:8088/simbestui/js/easyui-lang-zh_CN.js?v=svn.revision}" type="text/javascript"></script>
    <script src="../../js/jquery.config.js?v=svn.revision" th:src="@{/js/jquery.config.js?v=svn.revision}" type="text/javascript"></script>
    <script src="http://************:8088/simbestui/js/jquery.zjsfile.js?v=svn.revision" th:src="@{http://************:8088/simbestui/js/jquery.zjsfile.js?v=svn.revision}" type="text/javascript"></script>
    <script src="http://************:8088/simbestui/js/jquery.aduq.js?v=svn.revision" th:src="@{http://************:8088/simbestui/js/jquery.aduq.js?v=svn.revision}" type="text/javascript"></script>
    <script src="http://************:8088/simbestui/js/jquery.process.js?v=svn.revision" th:src="@{http://************:8088/simbestui/js/jquery.process.js?v=svn.revision}" type="text/javascript"></script>
    <script type="text/javascript">
        var mobileFilePath;
        $(function(){
            //showDialog做为打开对话框的公共class名称   showDialogindex做为打开修改对话框传行的索引   delete做为删除按钮配置命令   deleteid做为删除传值  searchtable做为条件查询的按钮公共class名称
            var pageparam={
                "listtable":{
                    "listname":"#menuExpalinTable",//table列表的id名称，需加#
                    "querycmd":"action/sharingPlatform/findAllDim",//table列表的查询命令
                    "checkboxall":true,
                    "queryParams":{'menuType':gps.menuType},
                    "contentType":"application/json; charset=utf-8",//table列表的传参方式，如果是json就写，不写默认kv
                    "nowrap": true,//把数据显示在一行里,默认true
                    "styleClass": "noScroll",
                    "frozenColumns":[[
                        { field: "ck",checkbox:true}
                    ]],//固定在左侧的列
                    "columns":[[//列
                        { title: "标题", field: "title", width: 120},
                        { title: "创建人", field: "creatorName", width: 120},
                        { title: "创建时间", field: "createdTime", width: 120},
                        {
                            field: "opt", title: "操作", width: 110, rowspan: 1,//align：对齐此列的数据，可以用left、right、center
                            formatter: function (value, row,index) {//单元格的格式化函数，需要三个参数：value：字段的值；rowData：行的记录数据；rowIndex：行的索引
                                mobileFilePath=row.mobileFilePath;
                                //console.log(mobileFilePath);
                                var g = "<a href='#' onclick='find(\""+ row.title + "\",\""+ row.downLoadUrl + "\")' class='showDialog' showDialogindex='" + index + "'>【查看】</a>"
/*
                                    +"<a  href='"+web.appCode+""+row.downLoadUrl+"'  title='下载'>【下载】</a>"
*/
                                    +"<a href='#' class='showDialog'  showDialogindex='" + index + "'>【编辑】</a>"
                                +"<a href='#' delete='action/sharingPlatform/deleteById' deleteid='"+row.id+"'>【删除】</a>";
                                return g;
                            }
                        }
                    ] ],
                    "pagerbar": [{
                        id:"deleteall",
                        iconCls: 'icon-remove',
                        text:"批量删除&nbsp;"
                    }],
                    "deleteall":{//批量删除deleteall.id要与pagerbar.id相同
                        "id":"deleteall",
                        "url":"action/sharingPlatform/deleteAllByIds",
                        "contentType":"application/json; charset=utf-8"
                    }
                },
                "dialogform":{
                    "dialogid":"#buttons",//对话框的id
                    "formname":"#menuExpalinTableAddForm",//新增或修改对话框的formid需加#
                    "insertcmd":"action/sharingPlatform/createDim",//新增命令
                    "updatacmd":"action/sharingPlatform/updateDim",//修改命令
                    "onSubmit":function(data){
                        if($("#id").val()=="") data.displayOrder="1";
                        if($("#roleCode").attr("codeError")){
                            top.mesAlert("提示信息","角色编码已存在,请重新输入！", 'error');
                            return false;
                        }
                        return true;
                    }
                }
            };
            loadGrid(pageparam);
        });
        //form表单填充数据之前做什么,data为填充数据的集合,isupdate表示是新增还是修改,若为新增则data为{}
        function beforerender(data,isupdate){
            if(isupdate){
            }else{
                $("#createdTimeTd").hide();
                $("#createdTimeTdTd").hide();
            }
        };
        //回调函数
        window.programaInfo=function(data){
        };
        //初始化操作代码
        function initsystem(){
            //获取当前用户的信息
            getCurrent();
            $("#creatorName").val(web.currentUser.truename);
            //console.log(gps.menuType);
            $("#menuType").val(gps.menuType);
        };
        
        function find(fileName,downLoadUrl ) {

            document.getElementById("tr1").style.display = "none";
            document.getElementById("listFile").style.display = "inline";
            $("ul.listFile").html("<li><a target='_blank' href='"+mobileFilePath+"'>"+fileName+"</a><a class='ml15' target='_blank' href='/"+web.appCode+downLoadUrl+"'>下载</a></li>");
        }
        

    </script>
</head>
<style>
    .formTable { width: 100%; margin-top: 30px; border-spacing: 0; border-top: 1px solid #dedede; border-left: 1px solid #dedede; }
    .formTable>tbody>tr:not(:first-child)>td { border-right: 1px solid #dedede; border-bottom: 1px solid #dedede; font-size: 14px; height: 36px; }
    .formTable>tbody>tr>td input,.formTable>tbody>tr>td span,.formTable>tbody>tr>td textarea,.formTable>tbody>tr>td .textbox .textbox-text{ border: none; font-size: 14px; }
    .formTable td.lable{ background-color: #ddf1fe; padding: 5px; text-align: left; }
    .formTable td .textAndInput_readonly, .formTable td .textAndInput_readonly .validatebox-readonly{ background-color: #fff; }
    .cselectorImageUL .btn{ right: 3px; top: 3px; }
    .cselectorImageUL input[type='file']{ right: 25px; top: 3px; }
    textarea{ line-height: 20px; letter-spacing: 1px; }
    table.tabForm{ border-color: #dedede; }
</style>
<body class="body_page">
<!--searchform-->
<form id="menuExpalinTableQueryForm" >
    <table border="0" cellpadding="0" cellspacing="6" width="100%">
		<tr>
            <td width="30" align="right">标题</td>
            <td width="150"><input name="title" type="text" value="" /></td>
            <td>
                <div class="w100">
                    <a class="btn a_primary fr searchtable"><span>查询</span></a>   
					<a class="btn a_success showDialog fr mr10"><span>新增</span></a>
                </div>
            </td>
        </tr>
    </table>
</form>
<!--table-->
<div class="menuExpalinTable"><table id="menuExpalinTable"></table></div>
<!--dialog-->
<div id="buttons" title="新增或修改" class="easyui-dialog" style="width:900px;height:660px;">
    <form id="menuExpalinTableAddForm" method="post" contentType="application/json; charset=utf-8" cmd-select="action/sharingPlatform/findById" beforerender="beforerender()"  initsystem="initsystem()"  >
        <input id="id" name="id" type="hidden" />
        <input id="menuType" name="menuType" type="hidden">
        <table border="0" style="width: 100%;" class="tabForm formTable" cellpadding="0" cellspacing="10" >
            <tr>
                <td width="16.6%"></td>
                <td width="32.2%"></td>
                <td width="16.6%"></td>
                <td width="32.2%"></td>
            </tr>
            <!--<tr>
                <td align="right" width="110" class="tit"><font class="col_r">*</font>标题</td>
                <td colspan="3" width="600">
                    <input id="title" name="title" type="text"  class="easyui-validatebox"  required="true" />
                </td>
            </tr>-->
            <tr id="created">
                <td align="right" class="lable">创建人</td>
                <td colspan="3">
                    <input id="creatorName" name="creatorName" type="text"  class="easyui-validatebox" readonly="readonly" required="true" />
                </td>
            </tr>
            <!--<tr>
                <td align="right" width="110" class="tit" ><font class="col_r">*</font>正文</td>
                <td  colspan="3">
                    <textarea id="info" name="info" placeholder="最多输入4000字" class="easyui-validatebox" validType="maxLength[4000,'changeReasonTip']" style="width: 100%; height: 400px; resize: none;" required="true"></textarea>
                    <span class="changeReasonTip"></span>
                </td>
            </tr>-->
            <tr id="tr1">
                <td align="right"  class="lable" style="line-height:38px;">附件</td>
                <td  colspan="3"  valign="top" style="padding:3px;">
                    <input id="infoFileList" name="infoFileList" type="text" file="true" class="cselectorImageUpload" mulaccept="true" btnmsg="<i class='iconfont' title='添加'>&#xe641;</i>"
                           href="sys/file/uploadProcessFiles?pmInsType=A&pmInsTypePart=3"/>
                </td>
            </tr>
        </table>
        <table id="listFile" border="0" cellpadding="0" cellspacing="0" width="100%" style="display:none">
            <tr>
                <td width="50" valign="top"><b class="lh32 f14">附件：</b></td>
                <td>
                    <ul class="list listFile">
                        <!--<li><a>中共中央办公厅印发中共中央办公厅印发中共中央办公厅印发.doc</a></li>
                        <li><a>中共中央办公厅印发中共中央办公厅印发中共中央.doc</a></li>
                        <li><a>中共中央办公厅印发中共中央办公厅印发中共中央办公.doc</a></li>
                        <li><a>中共中央办公厅印发.doc</a></li>
                        <li><a>中共中央办公厅印发中共中央办公厅印印发.doc</a></li>-->
                    </ul>
                </td>
            </tr>
        </table>
    </form>
</div>
</body>
</html>
