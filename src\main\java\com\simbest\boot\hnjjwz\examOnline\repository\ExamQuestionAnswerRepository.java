package com.simbest.boot.hnjjwz.examOnline.repository;

import com.simbest.boot.base.repository.LogicRepository;
import com.simbest.boot.hnjjwz.examOnline.model.ExamAnswer;
import com.simbest.boot.hnjjwz.examOnline.model.ExamQuestionAnswer;
import org.springframework.data.domain.Page;
import org.springframework.data.domain.Pageable;
import org.springframework.data.jpa.repository.Modifying;
import org.springframework.data.jpa.repository.Query;
import org.springframework.data.repository.query.Param;

import java.util.List;

/**
 * <AUTHOR>
 * @Data 201/05/09
 * @Description 题目正确答案
 */
public interface ExamQuestionAnswerRepository extends LogicRepository<ExamQuestionAnswer,String> {


    @Query(value = "select t.*, t.rowid from US_EXAM_QUESTION_ANSWER t where t.question_code=:questionCode and t.enabled=1",
            nativeQuery = true)
    List<ExamQuestionAnswer> findExamAnswerQuestionCode(@Param( "questionCode" )String questionCode);

    /**
     *
     */
    @Query(
            value = "select t.*, t.rowid from US_EXAM_QUESTION_ANSWER t where t.question_code=:questionCode and t.enabled=1",
            countQuery = "select count(*)  from US_EXAM_QUESTION_ANSWER t where t.question_code=:questionCode and t.enabled=1" ,
            nativeQuery = true
    )
    Page<ExamQuestionAnswer> customFindExamAnswer(@Param( "questionCode" )String questionCode, Pageable pageable);

    @Modifying
    @Query(
            value = "update US_EXAM_QUESTION_ANSWER t set t.enabled=0 where t.question_code=:questionCode and t.enabled=1 and t.removed_time is null",
            nativeQuery = true
    )
    int  deleteExamQuestionAnswer(@Param( "questionCode" )String questionCode);
}
