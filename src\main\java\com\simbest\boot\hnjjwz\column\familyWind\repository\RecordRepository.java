package com.simbest.boot.hnjjwz.column.familyWind.repository;/**
 * Created by KZH on 2019/8/5 17:42.
 */

import com.simbest.boot.base.repository.LogicRepository;
import com.simbest.boot.hnjjwz.column.familyWind.model.Record;
import org.springframework.data.jpa.repository.Query;
import org.springframework.data.repository.query.Param;

import java.util.List;

/**
 * <AUTHOR>
 * @create 2019-08-05 17:42
 * @desc
 **/
public interface RecordRepository extends LogicRepository<Record,String> {

   /* @Query(value = "select t.*" +
            "  from us_record t" +
            " where t.user_name =:username" +
            "   and t.enabled = 1" +
            "   and t.created_time like  CONCAT('2019-08-23','%')",//and t.created_time=to_char(sysdate,'yyyy-MM-dd')
            nativeQuery = true)
    List<Record>  findRecord(@Param("username") String username);*/

    @Query(value = "select t.*" +
            "  from us_record t" +
            " where t.user_name =:username" +
            "   and t.enabled = 1" +
            "   and to_char(t.created_time,'yyyy-MM-dd') = to_char(sysdate,'yyyy-MM-dd')",//and t.created_time=to_char(sysdate,'yyyy-MM-dd')
            nativeQuery = true)
    List<Record>  findRecord(@Param("username") String username);
}
