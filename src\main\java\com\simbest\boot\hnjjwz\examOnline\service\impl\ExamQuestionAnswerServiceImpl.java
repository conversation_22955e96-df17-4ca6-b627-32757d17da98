package com.simbest.boot.hnjjwz.examOnline.service.impl;

import com.simbest.boot.base.service.impl.LogicService;
import com.simbest.boot.base.web.response.JsonResponse;
import com.simbest.boot.hnjjwz.examOnline.model.ExamQuestionAnswer;
import com.simbest.boot.hnjjwz.examOnline.repository.ExamQuestionAnswerRepository;
import com.simbest.boot.hnjjwz.examOnline.service.IExamAnswerService;
import com.simbest.boot.hnjjwz.examOnline.service.IExamQuestionAnswerService;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.data.domain.Pageable;
import org.springframework.stereotype.Service;

import java.util.List;

/**
 * <AUTHOR>
 * @Data 2019/05/09
 * @Description 题目正确答案
 */
@Slf4j
@Service
public class ExamQuestionAnswerServiceImpl extends LogicService<ExamQuestionAnswer,String> implements IExamQuestionAnswerService {

    private ExamQuestionAnswerRepository examQuestionAnswerRepository;

    @Autowired
    public ExamQuestionAnswerServiceImpl ( ExamQuestionAnswerRepository examQuestionAnswerRepository) {
        super(examQuestionAnswerRepository);
        this.examQuestionAnswerRepository = examQuestionAnswerRepository;
    }
    @Autowired
    private IExamAnswerService iExamAnswerService;

    /**
     * 根据题目编码获取正确答案
     * @param questionCode
     * @return
     */
    @Override
    public List<ExamQuestionAnswer> findExamAnswerQuestionCode(String questionCode) {
        return examQuestionAnswerRepository.findExamAnswerQuestionCode(questionCode);
    }

    @Override
    public JsonResponse customFindExamAnswer(String questionCode, Pageable pageable) {
        return JsonResponse.success(examQuestionAnswerRepository.customFindExamAnswer(questionCode,pageable));
    }

    @Override
    public int deleteExamQuestionAnswer(String questionCode) {
        int i = examQuestionAnswerRepository.deleteExamQuestionAnswer(questionCode);//删除正确答案表
        if(i>0){//删除和修改要同时维护三张表

            return  iExamAnswerService.deleteExamAnswer(questionCode);//删除题目表
        }
        return i;

    }
}
