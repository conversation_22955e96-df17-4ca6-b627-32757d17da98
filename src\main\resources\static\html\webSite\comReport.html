<!DOCTYPE html>
<html xmlns="http://www.w3.org/1999/xhtml"
      xmlns:th="http://www.thymeleaf.org">
<head>
    <title>信访举报-清廉移动</title>
    <meta http-equiv="Content-Type" content="text/html; charset=utf-8"/>
    <meta http-equiv="X-UA-Compatible" content="IE=edge" />
    <meta name="viewport" content="width=device-width, initial-scale=1, shrink-to-fit=no"/>
    <!--
       <link href="http://************:8088/simbestui/fonts/iconfont/iconfont.css?v=svn.revision" th:href="@{http://************:8088/simbestui/fonts/iconfont/iconfont.css?v=svn.revision}" rel="stylesheet"/>
       -->
    <link href="../../fonts/iconfont/iconfont.css?v=svn.revision" th:href="@{/fonts/iconfont/iconfont.css?v=svn.revision}" rel="stylesheet"/>
    <link href="http://************:8088/simbestui/js/themes/default/easyui.css?v=svn.revision" th:href="@{http://************:8088/simbestui/js/themes/default/easyui.css?v=svn.revision}" rel="stylesheet"/>
    <link href="http://************:8088/simbestui/js/themes/icon.css?v=svn.revision" th:href="@{http://************:8088/simbestui/js/themes/icon.css?v=svn.revision}" rel="stylesheet"/>
    <link href="http://************:8088/simbestui/css/public.css?v=svn.revision" th:href="@{http://************:8088/simbestui/css/public.css?v=svn.revision}" rel="stylesheet"/>
    <link href="http://************:8088/simbestui/css/pages.css?v=svn.revision" th:href="@{http://************:8088/simbestui/css/pages.css?v=svn.revision}" rel="stylesheet"/>
    <link href="../../css/webSite.css?v=svn.revision" th:href="@{/css/webSite.css?v=svn.revision}" rel="stylesheet"/>
    <script src="http://************:8088/simbestui/js/jquery.min.js?v=svn.revision" th:src="@{http://************:8088/simbestui/js/jquery.min.js?v=svn.revision}" type="text/javascript"></script>
    <script src="http://************:8088/simbestui/js/jquery.easyui.min.js?v=svn.revision" th:src="@{http://************:8088/simbestui/js/jquery.easyui.min.js?v=svn.revision}" type="text/javascript"></script>
    <script src="http://************:8088/simbestui/js/easyui-lang-zh_CN.js?v=svn.revision" th:src="@{http://************:8088/simbestui/js/easyui-lang-zh_CN.js?v=svn.revision}" type="text/javascript"></script>
    <script src="../../js/jquery.config.js?v=svn.revision" th:src="@{/js/jquery.config.js?v=svn.revision}" type="text/javascript"></script>
    <script src="http://************:8088/simbestui/js/jquery.zjsfile.js?v=svn.revision" th:src="@{http://************:8088/simbestui/js/jquery.zjsfile.js?v=svn.revision}" type="text/javascript"></script>
    <script src="http://************:8088/simbestui/js/jquery.aduq.js?v=svn.revision" th:src="@{http://************:8088/simbestui/js/jquery.aduq.js?v=svn.revision}" type="text/javascript"></script>
    <script src="http://************:8088/simbestui/js/jquery.process.js?v=svn.revision" th:src="@{http://************:8088/simbestui/js/jquery.process.js?v=svn.revision}" type="text/javascript"></script>
    <script src="../../js/webSite.js?v=svn.revision" th:src="@{/js/webSite.js?v=svn.revision}" type="text/javascript"></script>
    <style type="text/css">
        .tab3 .tab_click,.tab3 .tab_click span,.tab3D{border-color:#d61c19;}
        .tab3D{border-width: 1px 0 0 0;}
        .tab3 .tab_click span{width:auto;}
    </style>
    <script type="text/javascript">
        $(function(){
            webSite();
            ajaxgeneral({
                url:"action/templateLayout/constructPetitionLayout",
                success:function(res) {
                    //渲染模块
                    $(".center .comRe_tit").html(fastrender(res.data,"<a><img src='/"+web.appCode+"{{templateUrl}}'/><p>{{locationName}}</p></a>"));
                    for(var i in res.data) {
                        var dataD = res.data[i];
                        var dH=["<div class=\"ptb15 of_hidden\"><div class='modTit2 modTit7'><strong class='fl'><span class='s_l'></span><span class='s_c'>"+dataD.locationName+"</span><span class='s_r'></span></strong></div><div class='of_hidden p20'>"];
                        if(dataD.templateData.length>1){
                            for(var j in dataD.templateData){
                                dataD.templateData[j].mainBody=htmlDecode(dataD.templateData[j].mainBody);
                            }
                            dH.push("<ul class='tab3'>"+(fastrender(dataD.templateData,"<li><span>{{title}}</span></li>"))+"</ul><div class='w100 tab3D'>"+(fastrender(dataD.templateData,"<div class='p20'>{{mainBody}}</div>"))+"</div>");
                        }else{
                            dH.push(dataD.templateData[0]?htmlDecode(dataD.templateData[0].mainBody):"");
                        }
                        dH.push("</div></div>");
                        $(".center .comRe_D").append(dH.join(""));
                    }
                    $(".center .comRe_tit a:first,.center .tab3 li:first").trigger("click");
                }
            });

            $(document).on("click",".center .tab3 li",function(){
                var index=$(this).index();
                $(this).addClass("tab_click").siblings().removeClass("tab_click");
                $(this).parent().siblings(".tab3D").children("div").eq(index).show().siblings().hide();
            });

            $(document).on("click",".center .comRe_tit a",function(){
                var index=$(this).index();
                $(this).addClass("a_hover").siblings().removeClass("a_hover");
                $(".center .comRe_D>div:eq("+index+")").show().siblings().hide();
                if($(".center .comRe_D>div:eq("+index+")").find(".tab3 li.tab_click").length == 0){
                    $(".center .comRe_D>div:eq("+index+")").find(".tab3 li:first").trigger("click");
                }
            });

            /*var menuDataJson =sessionStorage.getItem("menuDataJson");
            var menuData =JSON.parse(menuDataJson);
            $("#h6").html(menuData[9].menuName+"-清廉移动");*/
        });
    </script>
</head>
<body>
<!--top-->

<div class="nav">
    <ul class="auto1024">
        <!--<li><a>首页</a></li>
        <li><a>信息公开</a></li>
        <li class="li_hover"><a>规章制度</a></li>
        <li><a>巡察工作</a></li>
        <li><a>嵌入式防控监督</a></li>
        <li><a>课题研究</a></li>
        <li><a>共享平台</a></li>
        <li>
            <a>数据报送</a>
            <ul>
                <li><a>领导人员廉洁情况活页夹</a></li>
                <li><a>信息报送</a></li>
            </ul>
        </li>
        <li class="nobor"><a>信访举报</a></li>-->
    </ul>
</div>
<!--center-->
<div class="auto1024 center">
    <div class="navTit"><img class='mr5' src='../../images/er1.jpg'/><a target="_blank" href="index.html">首页</a>><a>信访举报</a></div>
    <div class="comRe_tit of_hidden"></div>
    <div class="comRe_D"></div>
    <!--<div class="ptb15 of_hidden">
        <div class="modTit2 modTit3"><strong class="fl">课题部署</strong><a class="fr">更多>></a></div>
        <ul class="list list_pro">
            <li>中共中央办公厅印发《中国共产党纪律检查机关监督执纪工作规则》 [纪检监察室（巡察工作办公室）]<a class="mr10">[查看</a><a>下载]</a></li>
            <li>中共中央办公厅印发《中国共产党纪律检查机关监督执纪工作规则》 [纪检监察室（巡察工作办公室）]<a class="mr10">[查看</a><a>下载]</a></li>
            <li>中共中央办公厅印发《中国共产党纪律检查机关监督执纪工作规则》 [纪检监察室（巡察工作办公室）]<a class="mr10">[查看</a><a>下载]</a></li>
            <li>中共中央办公厅印发《中国共产党纪律检查机关监督执纪工作规则》 [纪检监察室（巡察工作办公室）]<a class="mr10">[查看</a><a>下载]</a></li>
            <li>中共中央办公厅印发《中国共产党纪律检查机关监督执纪工作规则》 [纪检监察室（巡察工作办公室）]<a class="mr10">[查看</a><a>下载]</a></li>
            <li>中共中央办公厅印发《中国共产党纪律检查机关监督执纪工作规则》 [纪检监察室（巡察工作办公室）]<a class="mr10">[查看</a><a>下载]</a></li>
            <li>中共中央办公厅印发《中国共产党纪律检查机关监督执纪工作规则》 [纪检监察室（巡察工作办公室）]<a class="mr10">[查看</a><a>下载]</a></li>
        </ul>
    </div>-->
</div>
<!--copy-->
<div class="copy">©版权所有 中国移动通信集团河南有限公司</div>
</body>
</html>
