package com.simbest.boot.hnjjwz.newcolumn.service;

import com.simbest.boot.base.service.ILogicService;
import com.simbest.boot.hnjjwz.backstage.announcement.model.Announcement;
import com.simbest.boot.hnjjwz.backstage.slideshow.model.SlideShow;
import com.simbest.boot.hnjjwz.newcolumn.model.SysNewColumnModel;
import com.simbest.boot.hnjjwz.process.wf.model.ProgramaDataForm;

public interface ISysNewProgramaService extends ILogicService<ProgramaDataForm,String> {
    void saveSlideShow(SlideShow resultApprovalForm);

    void saveAnnouncement(Announcement announcement);
}
