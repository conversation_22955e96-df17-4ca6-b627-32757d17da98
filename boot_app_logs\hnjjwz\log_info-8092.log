2025-06-18 14:18:32.004 [restartedMain] INFO  com.simbest.boot.SimbestApplication.logStartupProfileInfo Line:655 - The following profiles are active: obuat
2025-06-18 14:18:38.464 [restartedMain] INFO  org.hibernate.dialect.Dialect.<init> Line:172 - HHH000400: Using dialect: org.hibernate.dialect.Oracle10gDialect
2025-06-18 14:19:09.792 [restartedMain] INFO  com.simbest.boot.config.AppConfig.init Line:243 - ************************************应用配置START**************************************************
2025-06-18 14:19:09.793 [restartedMain] INFO  com.simbest.boot.config.AppConfig.init Line:244 - 应用注册代码【hnjjwz】
2025-06-18 14:19:09.793 [restartedMain] INFO  com.simbest.boot.config.AppConfig.init Line:245 - 应用访问地址【http://************:8088】
2025-06-18 14:19:09.797 [restartedMain] INFO  com.simbest.boot.config.AppConfig.init Line:246 - 应用访问上下文【/hnjjwz】
2025-06-18 14:19:09.797 [restartedMain] INFO  com.simbest.boot.config.AppConfig.init Line:247 - 应用门户单点加密令牌【SIMBEST_SSO】
2025-06-18 14:19:09.798 [restartedMain] INFO  com.simbest.boot.config.AppConfig.init Line:248 - 应用超时时间【3600】秒
2025-06-18 14:19:09.798 [restartedMain] INFO  com.simbest.boot.config.AppConfig.init Line:249 - 应用上传文件大小限制【10MB】
2025-06-18 14:19:09.798 [restartedMain] INFO  com.simbest.boot.config.AppConfig.init Line:250 - 应用请求主数据地址【http://************:8088/uums】
2025-06-18 14:19:09.798 [restartedMain] INFO  com.simbest.boot.config.AppConfig.init Line:251 - 应用请求统一网关地址【http://************:8088/gateway】
2025-06-18 14:19:09.799 [restartedMain] INFO  com.simbest.boot.config.AppConfig.init Line:252 - 应用接口文档地址【http://************:8088/hnjjwz/swagger-ui.html】
2025-06-18 14:19:09.799 [restartedMain] INFO  com.simbest.boot.config.AppConfig.init Line:253 - 应用请求接收访问登录页地址的IP白名单为【uatoa.hq.cmcc,**************,***********,***********,***********,***********,***********,***********,***********,***********,***********,***********,***********,***********,***********,***********,***********,***********,***********,***********,***********,***********,***********,***********,***********】
2025-06-18 14:19:09.799 [restartedMain] INFO  com.simbest.boot.config.AppConfig.init Line:254 - 应用登录验证码开启状态【false】
2025-06-18 14:19:09.799 [restartedMain] INFO  com.simbest.boot.config.AppConfig.init Line:255 - 应用心跳定时器开关打开状态【false】
2025-06-18 14:19:09.800 [restartedMain] INFO  com.simbest.boot.config.AppConfig.init Line:256 - 记录Web请求日志状态【false】
2025-06-18 14:19:09.800 [restartedMain] INFO  com.simbest.boot.config.AppConfig.init Line:257 - 4A生成审计日志文件开关状态【false】
2025-06-18 14:19:09.800 [restartedMain] INFO  com.simbest.boot.config.AppConfig.init Line:258 - 4A上传审计日志文件服务器路径地址【/home/<USER>/gather/】
2025-06-18 14:19:09.801 [restartedMain] INFO  com.simbest.boot.config.AppConfig.init Line:259 - 清理用户缓存开关【true】
2025-06-18 14:19:09.801 [restartedMain] INFO  com.simbest.boot.config.AppConfig.init Line:260 - 清理用户缓存定时任务执行周期【0 0 5 * * ?】
2025-06-18 14:19:09.801 [restartedMain] INFO  com.simbest.boot.config.AppConfig.init Line:261 - SSO单点认证强制启用时间戳【false】
2025-06-18 14:19:09.802 [restartedMain] INFO  com.simbest.boot.config.AppConfig.init Line:262 - SSO单点认证加密盐值【Xianzai@2099】
2025-06-18 14:19:09.802 [restartedMain] INFO  com.simbest.boot.config.AppConfig.init Line:263 - SSO单点认证时间间隔【1】分钟
2025-06-18 14:19:09.802 [restartedMain] INFO  com.simbest.boot.config.AppConfig.init Line:264 - Cors跨域请求访问列表【*】
2025-06-18 14:19:09.802 [restartedMain] INFO  com.simbest.boot.config.AppConfig.init Line:266 - 应用临时文件上传目录为【E:\晟壁\党风廉政视窗\simbest-boot-hnjjwz/springboottmp/hnjjwz】
2025-06-18 14:19:09.802 [restartedMain] INFO  com.simbest.boot.config.AppConfig.init Line:267 - 应用获准访问白名单【localhost,127.0.0.1,portal.ha.cmcc,iportal.ha.cmcc,anddoc.ha.cmcc,andhall.ha.cmcc,#^(fe80|2409):.*$|^0:0:0:0:0:0:0:1$|^::1$|^10\.87\.(57|41|151|9|13)\.\d+|10\.(92|228|88)\.\d+\.\d+】
2025-06-18 14:19:09.802 [restartedMain] INFO  com.simbest.boot.config.AppConfig.init Line:268 - ************************************应用配置END**************************************************
2025-06-18 14:19:09.803 [restartedMain] INFO  com.simbest.boot.config.AppConfig.init Line:269 - 
2025-06-18 14:19:09.803 [restartedMain] INFO  com.simbest.boot.config.AppConfig.init Line:270 - ------------------------------------多线程配置START--------------------------------------------------
2025-06-18 14:19:09.803 [restartedMain] INFO  com.simbest.boot.config.AppConfig.init Line:271 - 多线程核心线程数【10】
2025-06-18 14:19:09.803 [restartedMain] INFO  com.simbest.boot.config.AppConfig.init Line:272 - 多线程最大线程数【20】
2025-06-18 14:19:09.803 [restartedMain] INFO  com.simbest.boot.config.AppConfig.init Line:273 - 多线程缓冲队列【200】
2025-06-18 14:19:09.803 [restartedMain] INFO  com.simbest.boot.config.AppConfig.init Line:274 - 多线程空闲时间【60】
2025-06-18 14:19:09.804 [restartedMain] INFO  com.simbest.boot.config.AppConfig.init Line:275 - ------------------------------------多线程配置END--------------------------------------------------
2025-06-18 14:19:09.804 [restartedMain] INFO  com.simbest.boot.config.AppConfig.init Line:276 - 
2025-06-18 14:19:09.804 [restartedMain] INFO  com.simbest.boot.config.AppConfig.init Line:277 - ====================================数据库配置START==================================================
2025-06-18 14:19:09.804 [restartedMain] INFO  com.simbest.boot.config.AppConfig.init Line:278 - 数据库URL【jdbc:oceanbase://10.92.82.143:2883/hnjjwz?characterEncoding=utf-8&useSSL=false&serverTimezone=GMT%2B8】
2025-06-18 14:19:09.804 [restartedMain] INFO  com.simbest.boot.config.AppConfig.init Line:279 - 数据库账号【hnjjwz@obtest#obdemo】
2025-06-18 14:19:09.804 [restartedMain] INFO  com.simbest.boot.config.AppConfig.init Line:280 - 数据库密码【Test44Hz】
2025-06-18 14:19:09.805 [restartedMain] INFO  com.simbest.boot.config.AppConfig.init Line:281 - ====================================数据库配置END==================================================
2025-06-18 14:19:09.805 [restartedMain] INFO  com.simbest.boot.config.AppConfig.init Line:282 - 
2025-06-18 14:19:09.805 [restartedMain] INFO  com.simbest.boot.config.AppConfig.init Line:283 - ====================================MongoDB数据库配置START==================================================
2025-06-18 14:19:09.805 [restartedMain] INFO  com.simbest.boot.config.AppConfig.init Line:284 - MongoDB数据库URL【】
2025-06-18 14:19:09.806 [restartedMain] INFO  com.simbest.boot.config.AppConfig.init Line:285 - MongoDB数据库【】
2025-06-18 14:19:09.806 [restartedMain] INFO  com.simbest.boot.config.AppConfig.init Line:286 - MongoDB数据库账号【】
2025-06-18 14:19:09.806 [restartedMain] INFO  com.simbest.boot.config.AppConfig.init Line:287 - MongoDB数据库密码【】
2025-06-18 14:19:09.806 [restartedMain] INFO  com.simbest.boot.config.AppConfig.init Line:288 - ====================================MongoDB数据库配置END==================================================
2025-06-18 14:19:09.806 [restartedMain] INFO  com.simbest.boot.config.AppConfig.init Line:289 - 
2025-06-18 14:19:09.806 [restartedMain] INFO  com.simbest.boot.config.AppConfig.init Line:290 - ^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^REDIS缓存配置START^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^
2025-06-18 14:19:09.806 [restartedMain] INFO  com.simbest.boot.config.AppConfig.init Line:291 - Redis配置方式【dictValueRedis】
2025-06-18 14:19:09.807 [restartedMain] INFO  com.simbest.boot.config.AppConfig.init Line:294 - Redis主数据中配置项为【redis-161】
2025-06-18 14:19:09.807 [restartedMain] INFO  com.simbest.boot.config.AppConfig.init Line:307 - Redis密码【Xianzai@0528】
2025-06-18 14:19:09.808 [restartedMain] INFO  com.simbest.boot.config.AppConfig.init Line:308 - Redis重定向次数【3】
2025-06-18 14:19:09.808 [restartedMain] INFO  com.simbest.boot.config.AppConfig.init Line:309 - Redis缓存空间前缀【spring:session:hnjjwz】
2025-06-18 14:19:09.808 [restartedMain] INFO  com.simbest.boot.config.AppConfig.init Line:310 - Redis缓存Key键前缀【cache:key:hnjjwz:】
2025-06-18 14:19:09.809 [restartedMain] INFO  com.simbest.boot.config.AppConfig.init Line:311 - Redis缓存默认等待加锁时间【3】秒
2025-06-18 14:19:09.809 [restartedMain] INFO  com.simbest.boot.config.AppConfig.init Line:312 - Redis缓存默认加锁后释放时间【60】秒
2025-06-18 14:19:09.809 [restartedMain] INFO  com.simbest.boot.config.AppConfig.init Line:313 - Redis缓存连接池最大连接数【100】个
2025-06-18 14:19:09.809 [restartedMain] INFO  com.simbest.boot.config.AppConfig.init Line:314 - Redis缓存连接池最大空闲连接数【50】个
2025-06-18 14:19:09.810 [restartedMain] INFO  com.simbest.boot.config.AppConfig.init Line:315 - Redis缓存连接池最小空闲连接数【5】个
2025-06-18 14:19:09.810 [restartedMain] INFO  com.simbest.boot.config.AppConfig.init Line:316 - ^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^REDIS缓存配置END^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^
2025-06-18 14:19:09.810 [restartedMain] INFO  com.simbest.boot.config.AppConfig.init Line:317 - 
2025-06-18 14:19:09.810 [restartedMain] INFO  com.simbest.boot.config.AppConfig.init Line:318 - ####################################文件存储配置START##################################################
2025-06-18 14:19:09.810 [restartedMain] INFO  com.simbest.boot.config.AppConfig.init Line:319 - 应用文件上传方式【disk】
2025-06-18 14:19:09.810 [restartedMain] INFO  com.simbest.boot.config.AppConfig.init Line:320 - 应用上传文件路径【/home/<USER>/simbestboot】
2025-06-18 14:19:09.810 [restartedMain] INFO  com.simbest.boot.config.AppConfig.init Line:321 - 应用下载文件启用Nginx映射状态【false】
2025-06-18 14:19:09.810 [restartedMain] INFO  com.simbest.boot.config.AppConfig.init Line:326 - ####################################文件存储配置END##################################################
2025-06-18 14:19:09.810 [restartedMain] INFO  com.simbest.boot.config.AppConfig.init Line:327 - 
2025-06-18 14:19:09.820 [restartedMain] INFO  com.simbest.boot.config.RedisConfiguration.redisConnectionFactory Line:189 - 缓存配置信息dictValueRedis
2025-06-18 14:19:09.820 [restartedMain] INFO  com.simbest.boot.config.RedisConfiguration.redisConnectionFactory Line:195 - 基于数据库读取Redis配置
2025-06-18 14:19:10.783 [restartedMain] INFO  com.simbest.boot.config.RedisConfiguration.redisConnectionFactory Line:237 - *************************Redis加载配置节点START******************************
2025-06-18 14:19:10.784 [restartedMain] INFO  com.simbest.boot.config.RedisConfiguration.redisConnectionFactory Line:238 - Redis节点为【10.92.82.151:7001,10.92.82.151:7002,10.92.82.152:7003,10.92.82.152:7004,10.92.82.153:7005,10.92.82.153:7006】
2025-06-18 14:19:10.784 [restartedMain] INFO  com.simbest.boot.config.RedisConfiguration.redisConnectionFactory Line:239 - *************************Redis加载配置节点END********************************
2025-06-18 14:19:11.993 [restartedMain] INFO  com.simbest.boot.config.EmbeddedServletConfiguration.customizeConnector Line:87 - 应用支持的附件上传最大限制为【10MB】
2025-06-18 14:19:11.994 [restartedMain] INFO  com.simbest.boot.config.EmbeddedServletConfiguration.customizeConnector Line:97 - 内置Tomcat将在端口【8092】启动
2025-06-18 14:19:18.845 [restartedMain] INFO  com.simbest.boot.util.AppFileUtil.init Line:153 - 请注意应用没有配置SFTP，请检查配置是否需要，如不需要，则忽略该条警告信息！
2025-06-18 14:19:42.910 [restartedMain] INFO  com.simbest.boot.SimbestApplication.logStarted Line:61 - Started SimbestApplication in 74.241 seconds (JVM running for 75.361)
2025-06-18 14:20:00.021 [pool-14-thread-8] INFO  com.simbest.boot.component.task.AbstractTaskSchedule.checkAndExecute Line:198 - 集群主控节点主机地址【************】运行端口【10031】,当前主机地址【***********】运行端口【8092】, 无法执行定时任务【com.simbest.boot.bps.task.AutoCloseTodoTask】【794】
2025-06-18 14:20:00.021 [pool-14-thread-9] INFO  com.simbest.boot.component.task.AbstractTaskSchedule.checkAndExecute Line:198 - 集群主控节点主机地址【************】运行端口【10031】,当前主机地址【***********】运行端口【8092】, 无法执行定时任务【com.simbest.boot.bps.task.AutoCheckWithUpdateTask】【795】
2025-06-18 14:20:00.021 [pool-14-thread-3] INFO  com.simbest.boot.component.task.AbstractTaskSchedule.checkAndExecute Line:198 - 集群主控节点主机地址【************】运行端口【10031】,当前主机地址【***********】运行端口【8092】, 无法执行定时任务【com.simbest.boot.processdata.pushWorkmanager.task.SyncCommentDataToWorkManagerTask】【789】
2025-06-18 14:20:00.022 [pool-14-thread-4] INFO  com.simbest.boot.component.task.AbstractTaskSchedule.checkAndExecute Line:198 - 集群主控节点主机地址【************】运行端口【10031】,当前主机地址【***********】运行端口【8092】, 无法执行定时任务【com.simbest.boot.processdata.pushWorkmanager.task.SyncProcessDataToWorkManagerTask】【790】
2025-06-18 14:20:00.024 [pool-14-thread-7] INFO  com.simbest.boot.component.task.AbstractTaskSchedule.checkAndExecute Line:198 - 集群主控节点主机地址【************】运行端口【10031】,当前主机地址【***********】运行端口【8092】, 无法执行定时任务【com.simbest.boot.bps.task.AutoAddTodoTask】【793】
2025-06-18 14:20:00.025 [pool-14-thread-2] INFO  com.simbest.boot.component.task.AbstractTaskSchedule.checkAndExecute Line:198 - 集群主控节点主机地址【************】运行端口【10031】,当前主机地址【***********】运行端口【8092】, 无法执行定时任务【com.simbest.boot.processdata.pushWorkmanager.task.SyncTaskDataToWorkManagerTask】【788】
2025-06-18 14:20:00.027 [pool-14-thread-6] INFO  com.simbest.boot.component.task.AbstractTaskSchedule.checkAndExecute Line:198 - 集群主控节点主机地址【************】运行端口【10031】,当前主机地址【***********】运行端口【8092】, 无法执行定时任务【com.simbest.boot.component.task.HeartTestTask】【792】
2025-06-18 14:20:22.615 [http-nio-8092-exec-9] INFO  com.simbest.boot.security.auth.provider.UumsHttpValidationAuthenticationProvider.authenticate Line:107 - @@@@@@@@@@@@@@@@@@@用户【chenhong】不需要通过4A进行认证@@@@@@@@@@@@@@@@@@@
2025-06-18 14:20:23.341 [http-nio-8092-exec-9] INFO  com.simbest.boot.security.auth.provider.UumsHttpValidationAuthenticationProvider.authenticate Line:149 - UUMS登录认证器UUMS主数据认证器处理用户【chenhong】访问【nfwpz】成功！
2025-06-18 14:20:23.905 [http-nio-8092-exec-9] INFO  com.simbest.boot.security.auth.provider.GenericAuthenticationChecker.authChek Line:95 - 用户【chenhong】访问【nfwpz】认证成功！
2025-06-18 14:21:00.010 [pool-14-thread-4] INFO  com.simbest.boot.component.task.AbstractTaskSchedule.checkAndExecute Line:156 - com.simbest.boot.component.task.HeartTestTask当前主机地址【***********】运行端口【8092】是集群主控节点，可以执行定时任务
2025-06-18 14:22:00.015 [pool-14-thread-2] INFO  com.simbest.boot.component.task.AbstractTaskSchedule.checkAndExecute Line:198 - 集群主控节点主机地址【127.0.0.1】运行端口【20250】,当前主机地址【***********】运行端口【8092】, 无法执行定时任务【com.simbest.boot.component.task.HeartTestTask】【788】
2025-06-18 14:23:00.015 [pool-14-thread-1] INFO  com.simbest.boot.component.task.AbstractTaskSchedule.checkAndExecute Line:156 - com.simbest.boot.component.task.HeartTestTask当前主机地址【***********】运行端口【8092】是集群主控节点，可以执行定时任务
2025-06-18 14:24:00.002 [pool-14-thread-6] INFO  com.simbest.boot.component.task.AbstractTaskSchedule.checkAndExecute Line:156 - com.simbest.boot.component.task.HeartTestTask当前主机地址【***********】运行端口【8092】是集群主控节点，可以执行定时任务
2025-06-18 14:25:00.004 [pool-14-thread-3] INFO  com.simbest.boot.component.task.AbstractTaskSchedule.checkAndExecute Line:156 - com.simbest.boot.processdata.pushWorkmanager.task.SyncCommentDataToWorkManagerTask当前主机地址【***********】运行端口【8092】是集群主控节点，可以执行定时任务
2025-06-18 14:25:00.004 [pool-14-thread-9] INFO  com.simbest.boot.component.task.AbstractTaskSchedule.checkAndExecute Line:156 - com.simbest.boot.bps.task.AutoAddTodoTask当前主机地址【***********】运行端口【8092】是集群主控节点，可以执行定时任务
2025-06-18 14:25:00.004 [pool-14-thread-10] INFO  com.simbest.boot.component.task.AbstractTaskSchedule.checkAndExecute Line:156 - com.simbest.boot.processdata.pushWorkmanager.task.SyncTaskDataToWorkManagerTask当前主机地址【***********】运行端口【8092】是集群主控节点，可以执行定时任务
2025-06-18 14:25:00.004 [pool-14-thread-4] INFO  com.simbest.boot.component.task.AbstractTaskSchedule.checkAndExecute Line:156 - com.simbest.boot.processdata.pushWorkmanager.task.SyncProcessDataToWorkManagerTask当前主机地址【***********】运行端口【8092】是集群主控节点，可以执行定时任务
2025-06-18 14:25:00.004 [pool-14-thread-7] INFO  com.simbest.boot.component.task.AbstractTaskSchedule.checkAndExecute Line:156 - com.simbest.boot.bps.task.AutoCheckWithUpdateTask当前主机地址【***********】运行端口【8092】是集群主控节点，可以执行定时任务
2025-06-18 14:25:00.004 [pool-14-thread-5] INFO  com.simbest.boot.component.task.AbstractTaskSchedule.checkAndExecute Line:156 - com.simbest.boot.component.task.HeartTestTask当前主机地址【***********】运行端口【8092】是集群主控节点，可以执行定时任务
2025-06-18 14:25:00.004 [pool-14-thread-2] INFO  com.simbest.boot.component.task.AbstractTaskSchedule.checkAndExecute Line:156 - com.simbest.boot.bps.task.AutoCloseTodoTask当前主机地址【***********】运行端口【8092】是集群主控节点，可以执行定时任务
2025-06-18 14:25:00.010 [pool-14-thread-10] INFO  com.simbest.boot.component.task.AbstractTaskSchedule.checkAndExecute Line:166 - ------------------------------【com.simbest.boot.processdata.pushWorkmanager.task.SyncTaskDataToWorkManagerTask】【796】上锁成功，即将执行定时任务
2025-06-18 14:25:00.010 [pool-14-thread-7] INFO  com.simbest.boot.component.task.AbstractTaskSchedule.checkAndExecute Line:166 - ------------------------------【com.simbest.boot.bps.task.AutoCheckWithUpdateTask】【793】上锁成功，即将执行定时任务
2025-06-18 14:25:00.012 [pool-14-thread-3] INFO  com.simbest.boot.component.task.AbstractTaskSchedule.checkAndExecute Line:166 - ------------------------------【com.simbest.boot.processdata.pushWorkmanager.task.SyncCommentDataToWorkManagerTask】【789】上锁成功，即将执行定时任务
2025-06-18 14:25:00.012 [pool-14-thread-2] INFO  com.simbest.boot.component.task.AbstractTaskSchedule.checkAndExecute Line:166 - ------------------------------【com.simbest.boot.bps.task.AutoCloseTodoTask】【788】上锁成功，即将执行定时任务
2025-06-18 14:25:00.012 [pool-14-thread-9] INFO  com.simbest.boot.component.task.AbstractTaskSchedule.checkAndExecute Line:166 - ------------------------------【com.simbest.boot.bps.task.AutoAddTodoTask】【795】上锁成功，即将执行定时任务
2025-06-18 14:25:00.021 [pool-14-thread-4] INFO  com.simbest.boot.component.task.AbstractTaskSchedule.checkAndExecute Line:166 - ------------------------------【com.simbest.boot.processdata.pushWorkmanager.task.SyncProcessDataToWorkManagerTask】【790】上锁成功，即将执行定时任务
2025-06-18 14:25:00.032 [pool-14-thread-9] INFO  com.simbest.boot.component.task.AbstractTaskSchedule.checkAndExecute Line:168 - ------------------------------【com.simbest.boot.bps.task.AutoAddTodoTask】【795】定时任务执行完毕
2025-06-18 14:25:00.035 [pool-14-thread-9] INFO  com.simbest.boot.component.task.AbstractTaskSchedule.checkAndExecute Line:177 - ------------------------------【com.simbest.boot.bps.task.AutoAddTodoTask】【795】定时任务执行完毕，集群锁已成功释放
2025-06-18 14:25:00.044 [pool-14-thread-2] INFO  com.simbest.boot.component.task.AbstractTaskSchedule.checkAndExecute Line:168 - ------------------------------【com.simbest.boot.bps.task.AutoCloseTodoTask】【788】定时任务执行完毕
2025-06-18 14:25:00.046 [pool-14-thread-3] INFO  com.simbest.boot.component.task.AbstractTaskSchedule.checkAndExecute Line:168 - ------------------------------【com.simbest.boot.processdata.pushWorkmanager.task.SyncCommentDataToWorkManagerTask】【789】定时任务执行完毕
2025-06-18 14:25:00.047 [pool-14-thread-2] INFO  com.simbest.boot.component.task.AbstractTaskSchedule.checkAndExecute Line:177 - ------------------------------【com.simbest.boot.bps.task.AutoCloseTodoTask】【788】定时任务执行完毕，集群锁已成功释放
2025-06-18 14:25:00.049 [pool-14-thread-3] INFO  com.simbest.boot.component.task.AbstractTaskSchedule.checkAndExecute Line:177 - ------------------------------【com.simbest.boot.processdata.pushWorkmanager.task.SyncCommentDataToWorkManagerTask】【789】定时任务执行完毕，集群锁已成功释放
2025-06-18 14:25:00.052 [pool-14-thread-10] INFO  com.simbest.boot.component.task.AbstractTaskSchedule.checkAndExecute Line:168 - ------------------------------【com.simbest.boot.processdata.pushWorkmanager.task.SyncTaskDataToWorkManagerTask】【796】定时任务执行完毕
2025-06-18 14:25:00.052 [pool-14-thread-7] INFO  com.simbest.boot.component.task.AbstractTaskSchedule.checkAndExecute Line:168 - ------------------------------【com.simbest.boot.bps.task.AutoCheckWithUpdateTask】【793】定时任务执行完毕
2025-06-18 14:25:00.053 [pool-14-thread-4] INFO  com.simbest.boot.component.task.AbstractTaskSchedule.checkAndExecute Line:168 - ------------------------------【com.simbest.boot.processdata.pushWorkmanager.task.SyncProcessDataToWorkManagerTask】【790】定时任务执行完毕
2025-06-18 14:25:00.054 [pool-14-thread-7] INFO  com.simbest.boot.component.task.AbstractTaskSchedule.checkAndExecute Line:177 - ------------------------------【com.simbest.boot.bps.task.AutoCheckWithUpdateTask】【793】定时任务执行完毕，集群锁已成功释放
2025-06-18 14:25:00.054 [pool-14-thread-10] INFO  com.simbest.boot.component.task.AbstractTaskSchedule.checkAndExecute Line:177 - ------------------------------【com.simbest.boot.processdata.pushWorkmanager.task.SyncTaskDataToWorkManagerTask】【796】定时任务执行完毕，集群锁已成功释放
2025-06-18 14:25:00.056 [pool-14-thread-4] INFO  com.simbest.boot.component.task.AbstractTaskSchedule.checkAndExecute Line:177 - ------------------------------【com.simbest.boot.processdata.pushWorkmanager.task.SyncProcessDataToWorkManagerTask】【790】定时任务执行完毕，集群锁已成功释放
2025-06-18 14:25:00.086 [pool-14-thread-9] INFO  com.simbest.boot.component.task.AbstractTaskSchedule.checkAndExecute Line:194 - ------------------------------【com.simbest.boot.bps.task.AutoAddTodoTask】【795】定时任务执行完毕，已写入定时任务记录日志
2025-06-18 14:25:00.118 [pool-14-thread-2] INFO  com.simbest.boot.component.task.AbstractTaskSchedule.checkAndExecute Line:194 - ------------------------------【com.simbest.boot.bps.task.AutoCloseTodoTask】【788】定时任务执行完毕，已写入定时任务记录日志
2025-06-18 14:25:00.118 [pool-14-thread-7] INFO  com.simbest.boot.component.task.AbstractTaskSchedule.checkAndExecute Line:194 - ------------------------------【com.simbest.boot.bps.task.AutoCheckWithUpdateTask】【793】定时任务执行完毕，已写入定时任务记录日志
2025-06-18 14:25:00.118 [pool-14-thread-4] INFO  com.simbest.boot.component.task.AbstractTaskSchedule.checkAndExecute Line:194 - ------------------------------【com.simbest.boot.processdata.pushWorkmanager.task.SyncProcessDataToWorkManagerTask】【790】定时任务执行完毕，已写入定时任务记录日志
2025-06-18 14:25:00.118 [pool-14-thread-10] INFO  com.simbest.boot.component.task.AbstractTaskSchedule.checkAndExecute Line:194 - ------------------------------【com.simbest.boot.processdata.pushWorkmanager.task.SyncTaskDataToWorkManagerTask】【796】定时任务执行完毕，已写入定时任务记录日志
2025-06-18 14:25:00.119 [pool-14-thread-3] INFO  com.simbest.boot.component.task.AbstractTaskSchedule.checkAndExecute Line:194 - ------------------------------【com.simbest.boot.processdata.pushWorkmanager.task.SyncCommentDataToWorkManagerTask】【789】定时任务执行完毕，已写入定时任务记录日志
2025-06-18 14:26:00.008 [pool-14-thread-2] INFO  com.simbest.boot.component.task.AbstractTaskSchedule.checkAndExecute Line:156 - com.simbest.boot.component.task.HeartTestTask当前主机地址【***********】运行端口【8092】是集群主控节点，可以执行定时任务
2025-06-18 14:27:00.013 [pool-14-thread-6] INFO  com.simbest.boot.component.task.AbstractTaskSchedule.checkAndExecute Line:156 - com.simbest.boot.component.task.HeartTestTask当前主机地址【***********】运行端口【8092】是集群主控节点，可以执行定时任务
