package com.simbest.boot.hnjjwz.examOnline.web;

import com.simbest.boot.base.web.controller.LogicController;
import com.simbest.boot.base.web.response.JsonResponse;
import com.simbest.boot.hnjjwz.examOnline.model.ExamQuestion;
import com.simbest.boot.hnjjwz.examOnline.model.ExamQuestionBank;
import com.simbest.boot.hnjjwz.examOnline.service.IExamQuestionBankService;
import io.swagger.annotations.Api;
import io.swagger.annotations.ApiOperation;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.web.bind.annotation.*;

import java.util.List;

/**
 * <AUTHOR>
 * @Data 2019/05/09
 * @Description
 */
@Api(description = "题库相关接口")
@Slf4j
@RestController
@RequestMapping(value = "/action/examQuestionBank")
public class ExamQuestionBankController extends LogicController<ExamQuestionBank, String> {

    @Autowired
    private IExamQuestionBankService examQuestionBankService;

    @Autowired
    public ExamQuestionBankController(IExamQuestionBankService examQuestionBankService) {
        super(examQuestionBankService);
        this.examQuestionBankService = examQuestionBankService;
    }

    /**
     * 导入一套题
     * @param
     * @return
     */
    @ApiOperation (value = "导入一套题", notes = "导入一套题")
    @PostMapping (value = {"/importQuestionBank","/importQuestionBank/sso"})
    public JsonResponse importQuestionBank(@RequestBody ExamQuestionBank examQuestionBank) {

        return examQuestionBankService.importQuestionBank(examQuestionBank);
    }






}
