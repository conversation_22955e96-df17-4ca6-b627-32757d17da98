package com.simbest.boot.hnjjwz.newcolumn.web;

import com.simbest.boot.base.web.controller.LogicController;
import com.simbest.boot.base.web.response.JsonResponse;
import com.simbest.boot.hnjjwz.newcolumn.model.SysColumnUserOrg;
import com.simbest.boot.hnjjwz.newcolumn.model.SysNewColumnModel;
import com.simbest.boot.hnjjwz.newcolumn.service.ISysColumnUserOrgService;
import com.simbest.boot.hnjjwz.newcolumn.service.ISysNewColumnService;
import io.swagger.annotations.Api;
import io.swagger.annotations.ApiOperation;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RestController;

import java.util.Map;

@Api(description = "新版栏目配置相关接口")
@Slf4j
@RestController
@RequestMapping(value = "/action/columnuserorg")
public class SysColumnUserOrgController extends LogicController<SysColumnUserOrg, String> {

    private ISysColumnUserOrgService service;

    @Autowired
    public SysColumnUserOrgController(ISysColumnUserOrgService service) {
        super(service);
        this.service = service;
    }

    @ApiOperation(value = "新增一个栏目关联人员或组织的起草查询关系", notes = "新增一个栏目关联人员或组织的起草查询关系")
    @PostMapping(value = {"/createColumnUserOrg", "/createColumnUserOrg/sso", "/createColumnUserOrg/api", "/createColumnUserOrg/anonymous"})
    public JsonResponse createColumnUserOrg(@RequestBody(required = false) Map<String,String> param) {
        return service.createColumnUserOrg(param);
    }
}
