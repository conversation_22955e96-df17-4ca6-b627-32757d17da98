package com.simbest.boot.hnjjwz.sharingPlatform.service.impl;

import com.simbest.boot.base.service.impl.LogicService;
import com.simbest.boot.base.web.response.JsonResponse;
import com.simbest.boot.hnjjwz.constants.Constants;
import com.simbest.boot.hnjjwz.sharingPlatform.model.SharingPlatform;
import com.simbest.boot.hnjjwz.sharingPlatform.repository.SharingPlatformRepository;
import com.simbest.boot.hnjjwz.sharingPlatform.service.ISharingPlatformService;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.data.domain.Page;
import org.springframework.data.domain.Pageable;
import org.springframework.stereotype.Service;

import java.util.Map;

/**
 * 作用：共享平台
 * 作者：刘萌
 * 时间：2019/05/05
 */
@Slf4j
@Service
public class SharingPlatformServiceImpl extends LogicService<SharingPlatform,String> implements ISharingPlatformService {
    private SharingPlatformRepository sharingPlatformRepository;

    @Autowired
    public SharingPlatformServiceImpl(SharingPlatformRepository sharingPlatformRepository) {
        super(sharingPlatformRepository);
        this.sharingPlatformRepository = sharingPlatformRepository;
    }


    @Override
    public JsonResponse findAllDim ( Map<String, Object> sharingPlatformMap, Pageable pageable ) {
        String menuType = (String)sharingPlatformMap.get( Constants.MENU_TYPE_KEY );
        String title = (String)sharingPlatformMap.get( Constants.TITLE_KEY );
        Page<SharingPlatform>  sharingPlatformPage =sharingPlatformRepository.findAllDim(title,menuType,pageable);
        return JsonResponse.success( sharingPlatformPage );
    }
}
