<!DOCTYPE html>
<html xmlns="http://www.w3.org/1999/xhtml"
      xmlns:th="http://www.thymeleaf.org">
<head>
    <title>课题研究信息修改页面</title>
    <meta http-equiv="Content-Type" content="text/html; charset=utf-8"/>
    <meta http-equiv="X-UA-Compatible" content="IE=edge" />
    <meta name="viewport" content="width=device-width, initial-scale=1, shrink-to-fit=no"/>
    <!--
    <link href="http://************:8088/simbestui/fonts/iconfont/iconfont.css?v=svn.revision" th:href="@{http://************:8088/simbestui/fonts/iconfont/iconfont.css?v=svn.revision}" rel="stylesheet"/>
    -->
    <link href="../../fonts/iconfont/iconfont.css?v=svn.revision" th:href="@{/fonts/iconfont/iconfont.css?v=svn.revision}" rel="stylesheet"/>
    <link href="http://************:8088/simbestui/js/themes/default/easyui.css?v=svn.revision" th:href="@{http://************:8088/simbestui/js/themes/default/easyui.css?v=svn.revision}" rel="stylesheet"/>
    <link href="http://************:8088/simbestui/js/themes/icon.css?v=svn.revision" th:href="@{http://************:8088/simbestui/js/themes/icon.css?v=svn.revision}" rel="stylesheet"/>
    <link href="http://************:8088/simbestui/css/public.css?v=svn.revision" th:href="@{http://************:8088/simbestui/css/public.css?v=svn.revision}" rel="stylesheet"/>
    <link href="http://************:8088/simbestui/css/pages.css?v=svn.revision" th:href="@{http://************:8088/simbestui/css/pages.css?v=svn.revision}" rel="stylesheet"/>
    <script src="http://************:8088/simbestui/js/jquery.min.js?v=svn.revision" th:src="@{http://************:8088/simbestui/js/jquery.min.js?v=svn.revision}" type="text/javascript"></script>
    <script src="http://************:8088/simbestui/js/jquery.easyui.min.js?v=svn.revision" th:src="@{http://************:8088/simbestui/js/jquery.easyui.min.js?v=svn.revision}" type="text/javascript"></script>
    <script src="http://************:8088/simbestui/js/easyui-lang-zh_CN.js?v=svn.revision" th:src="@{http://************:8088/simbestui/js/easyui-lang-zh_CN.js?v=svn.revision}" type="text/javascript"></script>
    <script src="http://************:8088/simbestui/js/kindeditor/kindeditor.min.js?v=svn.revision" th:src="@{http://************:8088/simbestui/js/kindeditor.min.js?v=svn.revision}" type="text/javascript"></script>
    <script src="../../js/jquery.config.js?v=svn.revision" th:src="@{/js/jquery.config.js?v=svn.revision}" type="text/javascript"></script>
    <script src="http://************:8088/simbestui/js/jquery.zjsfile.js?v=svn.revision" th:src="@{http://************:8088/simbestui/js/jquery.zjsfile.js?v=svn.revision}" type="text/javascript"></script>
    <script src="http://************:8088/simbestui/js/jquery.aduq.js?v=svn.revision" th:src="@{http://************:8088/simbestui/js/jquery.aduq.js?v=svn.revision}" type="text/javascript"></script>
    <script src="http://************:8088/simbestui/js/jquery.process.js?v=svn.revision" th:src="@{http://************:8088/simbestui/js/jquery.process.js?v=svn.revision}" type="text/javascript"></script>
    <script type="text/javascript">
        $(function(){
            getCurrent();
            //var username=web.currentUser.username;//查看当前登录人是属于分公司还是省公司
            // 加载表单
            loadForm("ProjectDataAddForm",{"pmInstId": gps.id});

        });

        //对表单中的元素进行校验，返回为0代表检验不成功。可以参考appManagementList.html
        window.getchoosedata = function () {
            formsubmit("ProjectDataAddForm");
            return {state: 0};
        };

        //提交表单之后进行的操作
        function submitcallback() {
            //关闭弹出框,resolveNeed为打开此页面的前一个页面appManagementList.html中的的top.dialogP中的回调函数名。
            top.dialogClose("modificAtion");
        };

        //form表单填充数据之前做什么,data为填充数据的集合,isupdate表示是新增还是修改,若为新增则data为{}
        function beforerender(data, isupdate) {
            if (isupdate) {
                $('.update-readonly').hide();
            } else {
                $('.update-readonly').show();
            }
        };

    </script>
</head>
<style>
    .formTable { width: 100%; margin-top: 30px; border-spacing: 0; border-top: 1px solid #dedede; border-left: 1px solid #dedede; }
    .formTable>tbody>tr:not(:first-child)>td { border-right: 1px solid #dedede; border-bottom: 1px solid #dedede; font-size: 14px; height: 36px; }
    .formTable>tbody>tr>td input,.formTable>tbody>tr>td span,.formTable>tbody>tr>td textarea,.formTable>tbody>tr>td .textbox .textbox-text{ border: none; font-size: 14px; }
    .formTable td.lable{ background-color: #ddf1fe; padding: 5px; text-align: left; }
    .formTable td .textAndInput_readonly, .formTable td .textAndInput_readonly .validatebox-readonly{ background-color: #fff; }
    .cselectorImageUL .btn{ right: 3px; top: 3px; }
    .cselectorImageUL input[type='file']{ right: 25px; top: 3px; }
    textarea{ line-height: 20px; letter-spacing: 1px; }
    table.tabForm{ border-color: #dedede; }
</style>
<body class="body_page">
<!--searchform-->

<!--table-->
<form id="ProjectDataAddForm" method="post"
      scontentType="application/json; charset=utf-8"
      cmd-select="action/taskStudy/getTaskStudyPmInsId"
      contentType="application/json; charset=utf-8" submitcallback="submitcallback()"
      cmd-update="action/taskStudy/update">
    <input id="id" name="id" type="hidden"/>


    <table border="0" style="width: 100%;" class="tabForm formTable" cellpadding="0" cellspacing="10">
        <tr>
            <td width="20%"></td>
            <td width="80%"></td>
        </tr>
        <tr>
            <td class="lable">pmInsId</td>
            <td length="100">
                <input id="pmInsId" name="pmInsId" type="text" value="" class="easyui-validatebox"  style="width:800px;height:32px"/>

            </td>
        </tr>
        <tr>
            <td class="lable">课题研究标题</td>
            <td length="100">
                <input id="taskStudyTitle" name="taskStudyTitle" type="text" value="" class="easyui-validatebox"  style="width:800px;height:32px"/>

            </td>
        </tr>
        <tr>
            <td class="lable">所属栏目全路径名</td>
            <td length="100">
                <input id="programaDisplayName" name="programaDisplayName" type="text" value="" class="easyui-validatebox"  style="width:800px;height:32px"/>

            </td>
        </tr>

        <tr>
            <td class="lable">课题研究所在文件的id</td>
            <td length="100">
                <input id="taskStudyId" name="taskStudyId" type="text" value="" class="easyui-validatebox"  style="width:800px;height:32px"/>

            </td>
        </tr>

        <tr>
            <td class="lable">课题研究所在文件的Url</td>
            <td length="100">
                <input id="taskStudyUrl" name="taskStudyUrl" type="text" value="" class="easyui-validatebox"  style="width:800px;height:32px"/>

            </td>
        </tr>
        <tr>
            <td class="lable">置顶标识</td>
            <td length="100">
                <input id="stickFlag" name="stickFlag" type="text" value="" class="easyui-validatebox"  style="width:800px;height:32px"/>

            </td>
        </tr>

        <!--<tr>
            <td align="right" width="110" class="tit" style="line-height:38px;">附件</td>
            <td  colspan="2"  valign="top" style="padding:3px;">
                <input id="accessoryFileList" name="accessoryFileList" type="text" file="true" class="cselectorImageUpload" mulaccept="true"
                       href="sys/file/uploadProcessFiles?pmInsType=A&pmInsTypePart=3"/>
            </td>
        </tr>-->

    </table>

</form>
</body>
</html>
