
package com.cmcc.mss.osb_oa_nma_inquirymeetinginvoicesrv;

import javax.xml.bind.annotation.XmlAccessType;
import javax.xml.bind.annotation.XmlAccessorType;
import javax.xml.bind.annotation.XmlElement;
import javax.xml.bind.annotation.XmlType;


/**
 * <p>OSB_OA_NMA_InquiryMeetingInvoiceSrvOutputItem complex type的 Java 类。
 * 
 * <p>以下模式片段指定包含在此类中的预期内容。
 * 
 * <pre>
 * &lt;complexType name="OSB_OA_NMA_InquiryMeetingInvoiceSrvOutputItem"&gt;
 *   &lt;complexContent&gt;
 *     &lt;restriction base="{http://www.w3.org/2001/XMLSchema}anyType"&gt;
 *       &lt;sequence&gt;
 *         &lt;element name="DATAS_Collection" type="{http://mss.cmcc.com/OSB_OA_NMA_InquiryMeetingInvoiceSrv}DATAS_Collection"/&gt;
 *       &lt;/sequence&gt;
 *     &lt;/restriction&gt;
 *   &lt;/complexContent&gt;
 * &lt;/complexType&gt;
 * </pre>
 * 
 * 
 */
@XmlAccessorType(XmlAccessType.FIELD)
@XmlType(name = "OSB_OA_NMA_InquiryMeetingInvoiceSrvOutputItem", propOrder = {
    "datasCollection"
})
public class OSBOANMAInquiryMeetingInvoiceSrvOutputItem {

    @XmlElement(name = "DATAS_Collection", required = true)
    protected DATASCollection datasCollection;

    /**
     * 获取datasCollection属性的值。
     * 
     * @return
     *     possible object is
     *     {@link DATASCollection }
     *     
     */
    public DATASCollection getDATASCollection() {
        return datasCollection;
    }

    /**
     * 设置datasCollection属性的值。
     * 
     * @param value
     *     allowed object is
     *     {@link DATASCollection }
     *     
     */
    public void setDATASCollection(DATASCollection value) {
        this.datasCollection = value;
    }

}
