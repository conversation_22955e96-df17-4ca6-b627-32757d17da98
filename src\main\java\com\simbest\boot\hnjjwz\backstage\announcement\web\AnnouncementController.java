package com.simbest.boot.hnjjwz.backstage.announcement.web;

import com.simbest.boot.base.repository.Condition;
import com.simbest.boot.base.web.controller.LogicController;
import com.simbest.boot.base.web.response.JsonResponse;
import com.simbest.boot.hnjjwz.backstage.announcement.model.Announcement;
import com.simbest.boot.hnjjwz.backstage.announcement.service.IAnnouncementService;
import com.simbest.boot.hnjjwz.backstage.slideshow.model.SlideShow;
import com.simbest.boot.hnjjwz.constants.Constants;
import com.simbest.boot.hnjjwz.process.wf.model.ProgramaDataForm;
import com.simbest.boot.hnjjwz.util.FileTool;
import com.simbest.boot.security.SimpleOrg;
import com.simbest.boot.security.SimpleUser;
import com.simbest.boot.sys.model.SysFile;
import com.simbest.boot.sys.service.ISysFileService;
import com.simbest.boot.util.ObjectUtil;
import com.simbest.boot.uums.api.org.UumsSysOrgApi;
import com.simbest.boot.uums.api.user.UumsSysUserinfoApi;
import io.swagger.annotations.Api;
import io.swagger.annotations.ApiImplicitParam;
import io.swagger.annotations.ApiImplicitParams;
import io.swagger.annotations.ApiOperation;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.lang3.StringUtils;
import org.apache.poi.ss.formula.functions.T;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.data.domain.Page;
import org.springframework.data.domain.Pageable;
import org.springframework.data.jpa.domain.Specification;
import org.springframework.web.bind.annotation.*;

import java.util.ArrayList;
import java.util.Arrays;
import java.util.List;
import java.util.Map;

/**
 * <AUTHOR>
 * @Data 2019/04/01
 * @Description 公告
 */
@Api(description = "公告")
@Slf4j
@RestController
@RequestMapping(value = "/action/announcement")
public class AnnouncementController extends LogicController<Announcement, String> {

    @Autowired
    private IAnnouncementService announcementService;

    @Autowired
    public AnnouncementController(IAnnouncementService announcementService) {
        super(announcementService);
        this.announcementService = announcementService;
    }

    @Autowired
    private FileTool fileTool;

    @Autowired
    private ISysFileService sysFileService;

    @Autowired
    private UumsSysUserinfoApi uumsSysUserinfoApi;

    @Autowired
    private UumsSysOrgApi uumsSysOrgApi;



    @PostMapping(value = {"/findAll", "/sso/findAll", "/api/findAll"})
    @Override
    public JsonResponse findAll(@RequestParam(required = false, defaultValue = "1") int page, //
                                @RequestParam(required = false, defaultValue = "10") int size, //
                                @RequestParam(required = false) String direction, //
                                @RequestParam(required = false) String properties,
                                @RequestBody Announcement o) {
        //获取分页规则, page第几页 size每页多少条 direction升序还是降序 properties排序规则（属性名称）
        Pageable pageable = announcementService.getPageable(page, size, "desc", "creationTime");
        // 获取查询条件
        Condition condition = new Condition();
        Map<String, Object> params = ObjectUtil.getEntityPersistentFieldValueExceptId(o);
        for (Map.Entry<String, Object> entry : params.entrySet()) {
            condition.eq(entry.getKey(), entry.getValue());
        }
        condition.eq("isPublish", true);
        Specification<Announcement> specification = announcementService.getSpecification(condition);
        // 获取查询结果
        Page<Announcement> pages = announcementService.findAll(specification, pageable);
        return JsonResponse.success(pages);
    }


    /**
     * 提交下一步
     * @param currentUserCode
     * @param workItemId
     * @param outcome
     * @param location
     * @param bodyParam
     * @return
     * @throws Exception
     */
    @ApiOperation(value = "提交起草流程", notes = "通过此接口启动流转审批表单")
    @ApiImplicitParams({
            @ApiImplicitParam(name = "currentUserCode", value = "当前登录人", dataType = "String", paramType = "query",required = true),
            @ApiImplicitParam(name = "workItemId", value = "工作项ID", dataType = "String", paramType = "query"),
            @ApiImplicitParam(name = "outcome", value = "连线规则", dataType = "String", paramType = "query", required = true),
            @ApiImplicitParam(name = "message", value = "审批意见", dataType = "String", paramType = "query",required = true),
            @ApiImplicitParam(name = "location", value = "当前状态", dataType = "String", paramType = "query"),
            @ApiImplicitParam(name = "formId", value = "表单id", dataType = "String", paramType = "query", required = false),
            @ApiImplicitParam(name = "source", value = "来源", dataType = "String", paramType = "query", required = false)
    })
    @PostMapping(value = {"/startSubmitProcess","/startSubmitProcess/sso","/api/startSubmitProcess"})
    public JsonResponse startSubmitProcessMS(@RequestParam String currentUserCode,
                                             @RequestParam(required = false) String workItemId,
                                             @RequestParam String outcome,
                                             @RequestParam(required = false) String location,
                                             @RequestParam(required = false) String source,
                                             @RequestParam(required = false) String formId,
                                             @RequestBody(required = false) Map<String,Object> bodyParam
    ) throws Exception {
        return announcementService.nextStep(currentUserCode, workItemId, outcome, location,formId,source,bodyParam);
    }

    /**
     * 详情办理
     * @param processInstId 流程实例idgetApprovalFromDetail
     * @return
     */
    @ApiOperation(value = "打开详情", notes = "办理")
    @ApiImplicitParams({
            @ApiImplicitParam(name = "processInstId", value = "流程实例id，用于流程中获取详情", dataType = "Long", paramType = "query"),
            @ApiImplicitParam(name = "source", value = "来源", dataType = "String", paramType = "query"),
            @ApiImplicitParam(name = "currentUserCode", value = "当前用户", dataType = "String", paramType = "query"),
            @ApiImplicitParam(name = "pmInstId", value = "表单中的流程id", dataType = "String", paramType = "query"),
    })
    @PostMapping(value = {"/getApprovalFromDetail", "/getApprovalFromDetail/sso","/api/getApprovalFromDetail"})
    public JsonResponse getApprovalFromDetail(@RequestParam(required = false) Long processInstId,
                                              @RequestParam(required = false) String source,
                                              @RequestParam(required = false) String currentUserCode,
                                              @RequestParam(required = false) String pmInstId,
                                              @RequestParam(required = false) String location){
        return announcementService.getApprovalFromDetail(processInstId,source,currentUserCode,pmInstId,location);
    }

    /**
     * 查询决策
     * @param processInstId 流程实例id
     * @param processDefName 流程定义名称
     * @param location       当前环节
     * @return

    @ApiOperation( value = "查询决策" , notes = "根据当前环节提供相应的决策")
    @ApiImplicitParams({
            @ApiImplicitParam(name = "processInstId", value = "流程实例ID", dataType = "String", paramType = "query"),
            @ApiImplicitParam(name = "processDefName", value = "流程定义名称", dataType = "String" ,paramType = "query"),
            @ApiImplicitParam(name = "location", value = "当前环节", dataType = "String", paramType = "query", required = true),
            @ApiImplicitParam(name = "source", value = "来源", dataType = "String", paramType = "query"),
            @ApiImplicitParam(name = "currentUserCode", value = "当前人", dataType = "String", paramType = "query")
    })
    @PostMapping(value = {"/getDecisions","/getDecisions/sso","/api/getDecisions"})
    public JsonResponse getDecisions(@RequestParam(required = false) String processInstId,
                                     @RequestParam(required = false) String processDefName ,
                                     @RequestParam(required = false ) String location,
                                     @RequestParam(required = false ) String source,
                                     @RequestParam(required = false ) String currentUserCode){
        return JsonResponse.success(announcementService.getDecisions( processInstId,processDefName,location,source,currentUserCode ));
    }*/

    /**
     * 注销该流程
     * @param currentUserCode 当前登录人
     * @param pmInstId 流程实例id
     * @param
     * @return
     */
    @ApiOperation(value = "注销该流程2", notes = "注销起草的流程2")
    @ApiImplicitParams({
            @ApiImplicitParam(name = "currentUserCode", value = "当前登录人", dataType = "String", paramType = "query",required = true),
            @ApiImplicitParam(name = "processInstId", value = "流程实例id", dataType = "Long", paramType = "query",required = true),
    })
    @PostMapping(value = {"/terminateProcessInst","/api/terminateProcessInst"})
    public JsonResponse terminateProcessInst  (@RequestParam(required = false) String currentUserCode,
                                               @RequestParam(required = false) Long pmInstId,
                                               @RequestParam(required = false) String source){
        return JsonResponse.success(announcementService.terminateProcessInst(pmInstId),"注销成功！");
    }
    /**
     * 根据公告标题(模糊)、发布人姓名(模糊)、是否显示在首页公告滚动处(精确)
     * @return
     */
    @ApiOperation (value = "根据公告标题(模糊)、发布人姓名(模糊)、是否显示在首页公告滚动处(精确)", notes = "根据公告标题(模糊)、发布人姓名(模糊)、是否显示在首页公告滚动处(精确)")
    @ApiImplicitParams ({ //
            @ApiImplicitParam (name = "page", value = "当前页码", dataType = "int", paramType = "query", //
                    required = true, example = "1"), //
            @ApiImplicitParam(name = "size", value = "每页数量", dataType = "int", paramType = "query", //
                    required = true, example = "10"), //
            @ApiImplicitParam(name = "direction", value = "排序规则（asc/desc）", dataType = "String", //
                    paramType = "query"), //
            @ApiImplicitParam(name = "properties", value = "排序规则（属性名称）", dataType = "String", //
                    paramType = "query")
    })
    @PostMapping (value = {"/findAllDim","/findAllDim/sso"})
    public JsonResponse findAllDim( @RequestParam (required = false, defaultValue = "1") int page, //
                                    @RequestParam(required = false, defaultValue = "10") int size, //
                                    @RequestParam(required = false) String direction, //
                                    @RequestParam(required = false) String properties,
                                    @RequestBody (required = false) Map<String,Object> mapObject ) {
        Pageable pageable = announcementService.getPageable(page, size, direction, properties);
        return announcementService.findAllDim( mapObject,pageable);
    }

    /**
     * 新增公告的信息
     * @return
     */
    @ApiOperation (value = "新增公告的信息", notes = "新增公告的信息")
    @PostMapping (value = {"/createDim","/createDim/sso"})
    public JsonResponse createDim( @RequestBody(required = false) Announcement announcement ) {
        changeFile( announcement);
        return super.create( announcement );
    }

    /**
     * 修改公告的信息
     * @return
     */
    @ApiOperation (value = "修改公告的信息", notes = "修改公告的信息")
    @PostMapping (value = {"/updateDim","/updateDim/sso"})
    public JsonResponse updateDim( @RequestBody(required = false) Announcement announcement ) {
        changeFile( announcement);
        return super.update( announcement );
    }

    /**
     * 放入附件的id
     * @param announcement
     */
    private void changeFile(Announcement announcement){
        //把图片的id取出来，存到数据库中
        List<SysFile> sysFileList = announcement.getAnnouncementFileList();
        announcement.setAnnouncementAccessoryId( fileTool.findFileIds( sysFileList ) );
    }

    /**
     * 根据id获取公告下的内容
     * @param id
     * @return
     */
    @ApiOperation (value = "根据id获取公告下的内容", notes = "根据id获取公告下的内容")
    @ApiImplicitParam(name = "id", value = "公告id", dataType = "String", paramType = "query")
    @PostMapping (value = {"/findByIdDim","/findByIdDim/sso"})
    public JsonResponse findByIdDim(@RequestParam(required = false) String id) {
        Announcement announcement = announcementService.findById( id );
        if(announcement==null){
            return JsonResponse.success( null );
        }
        String fileId = announcement.getAnnouncementAccessoryId();
        List<SysFile> sysFileList = new ArrayList<>(  );
        if( !StringUtils.isEmpty( fileId )){
            List<String> fileList = Arrays.asList( fileId.split( "," ) );
            for(String fileNewId:fileList){
                SysFile sysfile = sysFileService.findById( fileId );
                sysFileList.add( sysfile );
            }
        }
        announcement.setAnnouncementFileList( sysFileList );
        //获取人名以及组织code和全路径
        String username  = announcement.getCreator();
        SimpleUser simpleUser = uumsSysUserinfoApi.findByUsernameFromCurrent( username, Constants.APP_CODE );
        if(simpleUser == null){
            return JsonResponse.fail( null,"" );
        }
        String belongOrgCode = simpleUser.getBelongOrgCode();
        announcement.setPublishName( simpleUser.getTruename() );
        announcement.setPublishOrgCode( belongOrgCode );
        announcement.setPublishOrgName( simpleUser.getBelongOrgName() );
        SimpleOrg simpleOrg = uumsSysOrgApi.findListByOrgCode( Constants.APP_CODE,belongOrgCode );
        announcement.setPublishDisplayName( simpleOrg.getDisplayName() );
        return JsonResponse.success( announcement );
    }

    /**
     * 查询栏目详情列表2
     * @param page
     * @param size
     * @param direction
     * @param properties
     * @param paramsMap
     * @return
     */
    @ApiOperation(value = "查询栏目详情列表", notes = "查询栏目详情列表")
    @ApiImplicitParams( {
            @ApiImplicitParam(name = "page", value = "当前页码", dataType = "int", paramType = "query", example = "1"), //
            @ApiImplicitParam(name = "size", value = "每页数量", dataType = "int", paramType = "query", example = "10"), //
            @ApiImplicitParam(name = "direction", value = "排序规则（asc/desc）", dataType = "String", //
                    paramType = "query"), //
            @ApiImplicitParam(name = "properties", value = "排序规则（属性名称）", dataType = "String", //
                    paramType = "query")
    } )
    @PostMapping (value = {"/findDataDetailList2","/findDataDetailList2/sso"})
    public JsonResponse findDataDetailList2(@RequestParam(required = false, defaultValue = "1") int page, //
                                            @RequestParam(required = false, defaultValue = "10") int size, //
                                            @RequestParam(required = false) String direction, //
                                            @RequestParam(required = false) String properties,
                                            @RequestBody(required = false) Map<String,Object> paramsMap) {
        Pageable pageable = announcementService.getPageable(page, size, "desc", "creation_time");
        return  announcementService.findDataDetailList2(paramsMap, pageable);
    }

    /**
     * 置顶
     * @param
     * @return
     */
    @ApiOperation(value = "置顶", notes = "置顶")
    @PostMapping (value = {"/stick","/stick/sso"})
    public JsonResponse stick( @RequestParam String id) {
        return announcementService.stick(id);
    }

    /**
     * 再根据pm_ins_id字段获取ApprovalForm对象
     * @param map
     * @return
     */
    @ApiOperation(value = "再根据pm_ins_id字段获取ApprovalForm对象", notes = "再根据pm_ins_id字段获取ApprovalForm对象")
    @PostMapping (value = {"/getAnnouncementPmInsId","/getAnnouncementPmInsId/sso"})
    public JsonResponse getAnnouncementPmInsId( @RequestBody Map<String, Object> map) {
        String pmInsId=String.valueOf(map.get("pmInstId"));
        return JsonResponse.success(announcementService.getAnnouncementPmInsId(pmInsId));
    }

    @ApiOperation(value = "保存草稿", notes = "保存草稿")
    @PostMapping(value = {"/saveDraft", "/saveDraft/api"})
    public JsonResponse saveDraft(@RequestParam(required = false) String source,
                                  @RequestParam(required = false) String currentUserCode,
                                  @RequestBody Announcement innovationTopicForm) {
        return announcementService.saveDraft(source, currentUserCode, innovationTopicForm);
    }

    @ApiOperation(value = "废除草稿", notes = "废除草稿")
    @PostMapping(value = {"/deleteDraft", "/deleteDraft/api"})
    public JsonResponse deleteDraft(@RequestParam(required = false) String source,
                                    @RequestParam(required = false) String currentUserCode,
                                    @RequestParam(required = false) String pmInsId,
                                    @RequestBody Announcement innovationTopicForm) {
        return announcementService.deleteDraft(source, currentUserCode, pmInsId, innovationTopicForm);
    }
}
