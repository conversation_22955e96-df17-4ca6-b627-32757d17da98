package com.simbest.boot.hnjjwz.process.query.service;

import com.simbest.boot.base.service.ILogicService;
import com.simbest.boot.base.web.response.JsonResponse;
import com.simbest.boot.bps.process.listener.model.WfOptMsgModel;
/**
 * <AUTHOR>
 * @Data 2018/07/04
 * @Description 流程意见service层
 */
public interface IQueryOpinionHistoryService extends ILogicService<WfOptMsgModel,String> {

    /**
     * 查询工单审批意见
     * @param processInstId
     * @return
     */
    JsonResponse getWfOptMags( Long processInstId, String source, String currentUser);


}
