package com.simbest.boot.hnjjwz.backstage.programaCite.web;

import com.simbest.boot.base.web.controller.LogicController;
import com.simbest.boot.base.web.response.JsonResponse;
import com.simbest.boot.hnjjwz.backstage.programa.model.ProgramaInfo;
import com.simbest.boot.hnjjwz.backstage.programa.service.IProgramaInfoService;
import com.simbest.boot.hnjjwz.backstage.programaCite.model.ProgramaCite;
import com.simbest.boot.hnjjwz.backstage.programaCite.service.IProgramaCiteService;
import io.swagger.annotations.Api;
import io.swagger.annotations.ApiImplicitParam;
import io.swagger.annotations.ApiImplicitParams;
import io.swagger.annotations.ApiOperation;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.data.domain.Pageable;
import org.springframework.util.StringUtils;
import org.springframework.web.bind.annotation.*;

import java.util.Map;

/**
 * <AUTHOR>
 * @Data 2019/03/29
 * @Description 栏目的关联关系
 */
@Api(description = "栏目的关联关系")
@Slf4j
@RestController
@RequestMapping(value = "/action/programaCite")
public class ProgramaCiteController extends LogicController<ProgramaCite, String> {

    private IProgramaCiteService programaCiteService;

    @Autowired
    public ProgramaCiteController(IProgramaCiteService programaCiteService) {
        super(programaCiteService);
        this.programaCiteService = programaCiteService;
    }

    @Autowired
    private IProgramaInfoService programaInfoService;

    /**
     * 新增栏目的关联关系
     * @param programaCite
     * @return
     */
    @ApiOperation (value = "新增栏目的关联关系", notes = "新增栏目的关联关系")
    public JsonResponse create( @RequestBody (required = false) ProgramaCite programaCite) {
        return super.create( programaCite );
    }

    /**
     * 因为页面无法自动判断是新增还是修改，所以新写一个接口用于判断前端传来的页面是新增还是修改
     * @param programaCite
     * @return
     */
    @ApiOperation(value = "因为页面无法自动判断是新增还是修改，所以新写一个接口用于判断前端传来的页面是新增还是修改", notes = "因为页面无法自动判断是新增还是修改，所以新写一个接口用于判断前端传来的页面是新增还是修改")
    @PostMapping(value = {"/createFromPage","/createFromPage/sso"})
    public JsonResponse createFromPage(@RequestBody(required = false) ProgramaCite programaCite) {
        String id = programaCite.getId();
        if( StringUtils.isEmpty( id )){
            return super.create(programaCite);
        }else{
            return super.update(programaCite);
        }
    }

    /**
     *修改栏目的关联关系
     * @param programaCite
     * @return
     */
    @ApiOperation (value = "修改栏目的关联关系", notes = "修改栏目的关联关系")
    public JsonResponse update( @RequestBody (required = false) ProgramaCite programaCite) {
        return super.update(programaCite );
    }

    /**
     *根据id查询栏目关系信息
     * @param id
     * @return
     */
    @ApiOperation(value = "根据id查询栏目关系信息", notes = "根据id查询栏目关系信息")
    @ApiImplicitParam(name = "id", value = "栏目关系id", dataType = "String", paramType = "query")
    @PostMapping({"/findById","/findById/sso"})
    public JsonResponse findById(@RequestParam String id) {
        ProgramaCite programaCite = programaCiteService.findById( id );
        ProgramaInfo programaInfoOne = programaInfoService.findByFromProCode( programaCite.getProgramaOneCode());
        ProgramaInfo programaInfoAnother = programaInfoService.findByFromProCode( programaCite.getProgramaAnotherCode() );
        if(programaInfoOne==null || programaInfoAnother==null){
           return JsonResponse.fail( null,"此条数据有问题，关联的栏目不存在！" );
        }
        programaCite.setProgramaOneName( programaInfoOne.getProgramaName() );
        programaCite.setProgramaAnotherName( programaInfoAnother.getProgramaName() );
        return JsonResponse.success( programaCite );
    }

    /**
     * 根据id逻辑删除
     * @param id
     * @return
     */
    @ApiOperation (value = "删除栏目的关联关系", notes = "删除栏目的关联关系")
    @ApiImplicitParam (name = "id", value = "主键ID",  dataType = "String", paramType = "query")
    public JsonResponse deleteById(@RequestParam (required = false) String id) {
        return super.deleteById( id );
    }

    /**
     * 批量逻辑删除栏目的关联关系
     * @param ids
     * @return JsonResponse
     */
    @ApiOperation(value = "批量逻辑删除栏目的关联关系", notes = "批量逻辑删除栏目的关联关系")
    public JsonResponse deleteAllByIds(@RequestBody(required = false) String[] ids) {
        return  super.deleteAllByIds(ids);
    }

    /**
     * 根据栏目code获取栏目的中间信息
     * @param programaOneCode
     * @return
     */
    @ApiOperation(value = "根据栏目code获取栏目的中间信息", notes = "根据栏目code获取栏目的中间信息")
    @ApiImplicitParam(name = "programaOneCode", value = "栏目编码", dataType = "String", paramType = "query")
    @PostMapping (value = {"/findRelationPrograma","/findRelationPrograma/sso"})
    public JsonResponse findRelationPrograma( @RequestParam(required = false) String programaOneCode)  {
        return programaCiteService.findRelationPrograma(programaOneCode);
    }

    /**
     * 根据栏目关系类型(精确)以及栏目名(模糊)查询
     * @return
     */
    @ApiOperation(value = "根据栏目关系类型(精确)以及栏目名(模糊)查询", notes = "根据栏目关系类型(精确)以及栏目名(模糊)查询")
    @ApiImplicitParams ({ //
            @ApiImplicitParam(name = "page", value = "当前页码", dataType = "int", paramType = "query", //
                    required = true, example = "1"), //
            @ApiImplicitParam(name = "size", value = "每页数量", dataType = "int", paramType = "query", //
                    required = true, example = "10"), //
            @ApiImplicitParam(name = "direction", value = "排序规则（asc/desc）", dataType = "String", //
                    paramType = "query"), //
            @ApiImplicitParam(name = "properties", value = "排序规则（属性名称）", dataType = "String", //
                    paramType = "query")
    })
    @PostMapping (value = {"/findDimProgramaCite","/findDimProgramaCite/sso"})
    public JsonResponse findDimProgramaCite( @RequestParam(required = false, defaultValue = "1") int page, //
                                                 @RequestParam(required = false, defaultValue = "10") int size, //
                                                 @RequestParam(required = false) String direction, //
                                                 @RequestParam(required = false) String properties,
                                                 @RequestBody(required = false) Map<String,Object> mapObject ) {
        Pageable pageable = programaCiteService.getPageable(page, size, direction, properties);
        String  programaName = (String)mapObject.get( "programaOneName" );
        String  programaCiteType = (String)mapObject.get( "searchProgramaCiteType" );
        return programaCiteService.findDimProgramaCite(programaName,programaCiteType,pageable);
    }
}
