<!DOCTYPE html>
<html xmlns="http://www.w3.org/1999/xhtml"
      xmlns:th="http://www.thymeleaf.org">
<head>
    <title>在线测试-河南移动廉政视窗</title>
    <meta http-equiv="Content-Type" content="text/html; charset=utf-8"/>
    <meta http-equiv="X-UA-Compatible" content="IE=edge" />
    <meta name="viewport" content="width=device-width, initial-scale=1, shrink-to-fit=no"/>
    <!--
       <link href="http://************:8088/simbestui/fonts/iconfont/iconfont.css?v=svn.revision" th:href="@{http://************:8088/simbestui/fonts/iconfont/iconfont.css?v=svn.revision}" rel="stylesheet"/>
       -->
    <link href="../../fonts/iconfont/iconfont.css?v=svn.revision" th:href="@{/fonts/iconfont/iconfont.css?v=svn.revision}" rel="stylesheet"/>
    <link href="http://************:8088/simbestui/js/themes/default/easyui.css?v=svn.revision" th:href="@{http://************:8088/simbestui/js/themes/default/easyui.css?v=svn.revision}" rel="stylesheet"/>
    <link href="http://************:8088/simbestui/js/themes/icon.css?v=svn.revision" th:href="@{http://************:8088/simbestui/js/themes/icon.css?v=svn.revision}" rel="stylesheet"/>
    <link href="http://************:8088/simbestui/css/public.css?v=svn.revision" th:href="@{http://************:8088/simbestui/css/public.css?v=svn.revision}" rel="stylesheet"/>
    <link href="http://************:8088/simbestui/css/pages.css?v=svn.revision" th:href="@{http://************:8088/simbestui/css/pages.css?v=svn.revision}" rel="stylesheet"/>
    <link href="../../css/webSite.css?v=svn.revision" th:href="@{/css/webSite.css?v=svn.revision}" rel="stylesheet"/>
    <script src="http://************:8088/simbestui/js/jquery.min.js?v=svn.revision" th:src="@{http://************:8088/simbestui/js/jquery.min.js?v=svn.revision}" type="text/javascript"></script>
    <script src="http://************:8088/simbestui/js/jquery.easyui.min.js?v=svn.revision" th:src="@{http://************:8088/simbestui/js/jquery.easyui.min.js?v=svn.revision}" type="text/javascript"></script>
    <script src="http://************:8088/simbestui/js/easyui-lang-zh_CN.js?v=svn.revision" th:src="@{http://************:8088/simbestui/js/easyui-lang-zh_CN.js?v=svn.revision}" type="text/javascript"></script>
    <script src="../../js/jquery.config.js?v=svn.revision" th:src="@{/js/jquery.config.js?v=svn.revision}" type="text/javascript"></script>
    <script src="http://************:8088/simbestui/js/jquery.zjsfile.js?v=svn.revision" th:src="@{http://************:8088/simbestui/js/jquery.zjsfile.js?v=svn.revision}" type="text/javascript"></script>
    <script src="http://************:8088/simbestui/js/jquery.aduq.js?v=svn.revision" th:src="@{http://************:8088/simbestui/js/jquery.aduq.js?v=svn.revision}" type="text/javascript"></script>
    <script src="http://************:8088/simbestui/js/jquery.process.js?v=svn.revision" th:src="@{http://************:8088/simbestui/js/jquery.process.js?v=svn.revision}" type="text/javascript"></script>
    <script src="../../js/webSite.js?v=svn.revision" th:src="@{/js/webSite.js?v=svn.revision}" type="text/javascript"></script>
    <script type="text/javascript">
        var examMoreData;
        var examSingleData;
        var questionBankCode;//题库编码
        var maxTime;
        var minutes;
        var seconds;
        $(function(){
            //webSite();
            getCurrent();
            var truename=web.currentUser.truename;
            var belongDepartmentName=web.currentUser.belongDepartmentName;
            $("#department").val(belongDepartmentName);
            $("#name").val(truename);

            ajaxgeneral({
                url:"action/examInfo/randomExam",
                data:{"questionBankCode":"A-0001"},
                success:function(res) {
                    //渲染模块

                    $("#table").html(res.data.examName);
                    var dH=[];
                    var dN=[];
                    examMoreData=res.data.examMoreData;
                    examSingleData=res.data.examSingleData;
                    questionBankCode=res.data.questionBankCode;
                    //console.log(res.data);
                    //console.log(examMoreData);
                    //console.log(examSingleData);

                    /**
                     * 单选渲染模块
                     * @type {number}
                     */
                    var sum=1;
                    dH.push("<table border='0' cellpadding='0' cellspacing='15' width='800px' align='center'>");
                    for(var i in examMoreData) {
                        dH.push(
                            "<tr>"+
                            "<td width='500px' length='100'>" +
                                "<p class='MsoNormal' style='margin-left:21.0pt;text-indent:-21.0pt;'>" +
                                    "<span id='"+examMoreData[i].questionCode+"' >"+ sum +" 、" +examMoreData[i].questionName+"</span></p>");
                        sum++;

                        for(var j in examMoreData[i].examAnswerList){
                            var k;
                            switch (j) {
                                case  "0": k="A";
                                    break;
                                case  "1": k="B";
                                    break;
                                case  "2": k="C";
                                    break;
                                case  "3": k="D";

                            }

                            dH.push("<p class='MsoNormal' style='margin-left:21.0pt;text-indent:-21.0pt;'>" +
                                "&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;<input class='wauto' id='"+examMoreData[i].questionCode+j +"' value='"+k+"' type='checkbox' /><label for='"+examMoreData[i].questionCode+j +"'>"+k+" : "+examMoreData[i].examAnswerList[j].answerName+"</label></p>");

                        }

                        dH.push("</td></tr>");
                    }
                    dH.push("</table>");
                    $(".More").append(dH.join(""));

                    /**
                     * 多选渲染模块
                     * @type {number}
                     */
                    var sum2=1;
                    dN.push("<table border='0' cellpadding='0' cellspacing='15' width='800px' align='center'>");
                    for(var i in examSingleData) {
                        dN.push(
                            "<tr>"+
                            "<td width='500px' length='100'>" +
                                "<p class='MsoNormal' style='margin-left:21.0pt;text-indent:-21.0pt;'>" +
                                    "<span id='"+examSingleData[i].questionCode+"'>"+ sum2 +" 、" +examSingleData[i].questionName+"</span></p>");
                        sum2++;
                        for(var j in examSingleData[i].examAnswerList){
                            var k;
                            switch (j) {
                                case  "0": k="A";
                                    break;
                                case  "1": k="B";
                                    break;
                                case  "2": k="C";
                                    break;
                                case  "3": k="D";

                            }
                            dN.push("<p class='MsoNormal' style='margin-left:21.0pt;text-indent:-21.0pt;'>" +
                                "&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;<input class='wauto' id='"+examSingleData[i].questionCode+j +"' value='"+k+"' type='checkbox'/><label for='"+examSingleData[i].questionCode+j +"'>"+k+" : "+examSingleData[i].examAnswerList[j].answerName+"</label></p>");
                        }
                        dN.push("</td></tr>");
                    }
                    dH.push("</table>");
                    $(".Single").append(dN.join(""));


                    maxTime = res.data.setTime*60;
                    var dv=document.getElementById("dv");
                    //var maxTime=60*10;
                    function jia(a){
                        if(a<10)
                            return "0"+a;
                        else
                            return a;
                    }

                    var last=function(){
                         minutes=Math.floor(maxTime/60); //怎么说呢  假如(60*10-1)s 正常应该就是的 9.983333  取整就是 9
                         seconds=maxTime%60;   //余数指定是个整数
                         seconds=jia(seconds);

                        dv.innerHTML="[考试剩余:<font color='red'>" + minutes + "分" + seconds + "秒</font>]";
                        maxTime--;
                    };


                    var timeId =  setInterval(function(){
                        if(maxTime<=0) {
                            clearInterval(timeId);
                            //alert("考试结束");
                            return;
                        }

                        last();
                    },1000);



                }
            });

        });

        function checkForm(){

            maxTime=0;
            var map= {};
            $(".details_div").find(".details").find(".More").each(function(i,v){

                $(v).find("table").each(function(a,b){
                    $(b).find("tbody tr").each(function(x,y){
                        var listMore=[];
                        $(y).find("input").each(function(c,d){
                            if($(this).prop('checked') == true){
                                listMore.push($(d).val());
                            }

                        });
                        var id= $(y).find("span").attr("id");
                        //console.log(listMore);
                        map[id]=listMore;

                    });
                });
            });

            $(".details_div").find(".details").find(".Single").each(function(i,v){

                $(v).find("table").each(function(a,b){

                    $(b).find("tbody tr").each(function(x,y){
                        var listSingle=[];
                        $(y).find("input").each(function(c,d){
                            if($(this).prop('checked') == true){
                                listSingle.push($(d).val());
                            }
                        });
                        var id= $(y).find("span").attr("id");
                        //console.log(listSingle);
                        map[id]=listSingle;

                    });

                });

            });

            //console.log(map);
            //top.dialogClose("detail");
            ajaxgeneral({
                url: "action/examInfo/evaluatingExam",
                data:{
                    map:JSON.stringify(map),
                    department: $('#department').val(),
                    name:$('#name').val(),
                    leftTime:minutes+seconds
                },
                success: function (res) {

                }
            });

        }
    </script>

    <style type="text/css">
        .slide li{position:relative;width:100%;height:314px;overflow:hidden;text-align:center;}
        .slide li img{height:314px;width:512px;}
        .slide li p{position:absolute;bottom:0;background:#eee;left:0;right:0;}
        .noticeL li{overflow:hidden;padding:5px 8px;}
        dl{width:33%;min-height:90px;border-right:1px solid #ccc;float:left;}
        dl dt,dl dd{padding:0 10px 0px 18px;}
        dl dt{line-height:32px;font-weight:bold;font-size:14px;}
        .details{
            margin-left:69px;
            font-size:15px;
        }
        .details p{
            line-height:39px;
        }

        *{
            margin:0;
            padding:0;
        }
        #dv{
            height:20px;
            width:200px;
            text-align: center;
            border:1px solid forestgreen;
            margin:10px auto;
        }

    </style>
</head>
<body>
<!--top-->
<h5  class="txtc f20 p20 fbold col_b "><span id="table"></span></h5>
<div align="center">
    <table border="0" cellpadding="0" cellspacing="10" width="500px" align="center">
        <tr>
            <td width="50px" align="center">专业室</td>
            <td width="120px" length="100">
                <input type="text" id="department" name="department" style="border-left: none;border-top: none;border-right: none;text-align:left;
                             width:120px; border-color:black" value="" ></td>
            <td width="50px" align="center">姓名</td>
            <td width="120px" length="100">
                <input type="text" id="name" name="name" style="border-left: none;border-top: none;border-right: none;text-align:left;
                            width:120px; border-color:black" value="" >
            </td>
        </tr>
    </table>
</div>
<!--center-->
<div class="details_div">
    <div id="dv"></div>

    <div class="details">
        <p class="MsoNormal" style="margin-left:21.0pt;text-indent:-21.0pt;">
            一、	单选题（每小题2分，共100分）</p>
       <div class="More">

       </div>
        <p class="MsoNormal" style="margin-left:21.0pt;text-indent:-21.0pt;">
            二、	多选题（每小题2分，共100分）</p>
        <div class="Single">

        </div>
    </div>
    <h5 class="txtc f20 p20 "><a class="btn fbold" style="padding:5px 10px;float:none;"  align="center" onclick="checkForm()">提交</a></h5>
</div>
<!--copy-->
<div class="copy">©版权所有 中国移动通信集团河南有限公司</div>
</body>
</html>
