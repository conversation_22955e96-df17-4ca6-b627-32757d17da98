package com.simbest.boot.hnjjwz.constants;

/**
 * <strong>Title : </strong><br>
 * <strong>Description : </strong><br>
 * <strong>Create on : 2018/6/26/026</strong><br>
 * <strong>Modify on : 2018/6/26/026</strong><br>
 * <strong>Copyright (C) Ltd.</strong><br>
 *
 * <AUTHOR>
 * @version <strong>V1.0.0</strong><br>
 *          <strong>修改历史:</strong><br>
 *          修改人 修改日期 修改描述<br>
 *          常量类
 *          -------------------------------------------<br>
 */
public class Constants {
    //001
    public static final String ONE = "001";

    //应用编码
    public static final String APP_CODE = "hnjjwz";

    public static final String TARGET_APP_CODE = "exam";

    //应用编码
    public static final String APP_NAME = "河南移动廉政视窗";

    /**
     * 统一待办中系统id
     */
    public static final String APP_SYS_ID = "1082";

    //一级栏目编码位数
    public static final Integer PLACE = 4;

    //数据字典类型为nodeStyle
    public static final String DICT_TYPE = "nodeStyle";

    //数据字典类型为programaType
    public static final String PROGRAMA_TYPE = "programaType";

    //数据字典类型为programaCiteType
    public static final String PROGRAMA_CITE_TYPE = "programaCiteType";

    //数据字典类型为sourceType
    public static final String SOURCE_TYPE = "sourceType";

    //数据字典类型为indexMenuUrlType
    public static final String INDEX_MENU_URL_TYPE = "indexMenuUrlType";

    //流程类型processType
    public static final String DICT_VALUE_PROCESS_TYPE="processType";

    //省公司流程中的部门领导审批环节
    public static final String ACTIVITY_PROVINCE_DEPART = "hnjjwz.depart_manager";

    //分公司流程中省公司部门领导审批环节
    public static final String ACTIVITY_FILIALE_DEPART = "hnjjwz.province_depart_manager";

    //省公司
    public static final String PROVINCIAL_CODE = "01";//01 省公司

    //分公司
    public static final String BRANCH_CODE = "02";//02 地市分公司

    public static final String BRANCH_CODE_03 = "03";//02 地市分公司

    //省公司流程类型
    public static final String PROCESS_A = "A";

    //市公司流程类型
    public static final String PROCESS_B = "B";

    //省公司公告流程类型
    public static final String PROCESS_C = "C";

    //市公司公告流程类型
    public static final String PROCESS_D = "D";

    //省公司轮播图流程类型
    public static final String PROCESS_E = "E";

    //市公司轮播图流程类型
    public static final String PROCESS_F = "F";

    //省公司课题研究流程类型
    public static final String PROCESS_G = "G";

    //市公司课题研究流程类型
    public static final String PROCESS_H = "H";

    //起草环节
    public static final String ACTIVITY_START = "hnjjwz.start";

    //归档环节
    public static final String ACTIVITY_END = "end";

    //废除归档环节
    public static final String ACTIVITY_REJECT_END = "reject_end";

    //驳回环节
    public static final String ACTIVITY_REJECT = "reject";

    /**
     * 管理员账户
     */
    public static final String ADMIN_USERNAEM = "hadmin";

    /*********************************************考试成绩接口***************************************************************/

    public static final String HNJJWZ_NTXL_QUERY_EXAM_INFO = "/action/examInfo/getExamInfoByUsername/sso";

    public static final String HNJJWZ_NTXL_QUERY_EXAM_EFFECTIVE = "/action/summary/sso/findEffectiveExamByExamCode";

    /*********************************************考试成绩接口***************************************************************/
    /**
     * 省公司流程id
     */
    public static final String FLOW_PROVINCIAL = "com.hnjjwz.flow.provincial_company";

    /**
     * 分公司流程id
     */
    public static final String FLOW_BRANCH = "com.hnjjwz.flow.county_company";

    //表单上的上传附件区域之图片
    public static final String IMAGE_FILE = "imageFile";

    //表单上的上传附件区域之授权书
    public static final String AUTH_BOOK_FILE = "authBookFile";

    //表单上的上传附件区域之附件
    public static final String ACCESSORY_FILE = "accessoryFile";

    //表单上的上传附件区域之视频
    public static final String VIDEO_FILE = "videoFile";

    //流转成功
    public static final String SHOWMESSAGESUCCESS = "流转成功！";

    //map的key之menuType
    public static final String MENU_TYPE_KEY = "menuType";

    //map的key之title
    public static final String TITLE_KEY = "title";

    //map的key之menuName
    public static final String MENU_NAME_KEY = "menuName";

    //map的key之menuUrlType
    public static final String MENU_URL_TYPE_KEY = "menuUrlType";

    //map的key之parentMenuName
    public static final String PARENT_MENU_NAME_KEY = "parentMenuName";

    //map的key之announcementTitle
    public static final String ANNOUNCEMENT_TITLE_KEY = "announcementTitle";

    //map的key之creator
    public static final String CREATOR_KEY = "creator";

    //map的key之programaDataRelation
    public static final String PROGRAMA_DATA_RELATION_KEY = "programaDataRelation";

    //map的key之isDisplay
    public static final String IS_DISPLAY_KEY = "isDisplay";

    //map的key之templateName
    public static final String TEMPLATE_NAME_KEY = "templateName";

    //map的key之是否使用此模板
    public static final String TEMPLATE_USE_KEY = "templateUse";

    //map的key之programaCode
    public static final String PROGRAMA_CODE_KEY = "programaCode";

    //map的key之题库
    public static final String QUESTION_BANK_KEY = "questionBank";

    //map的key之试卷的唯一标识
    public static final String ONLY_RECORD_KEY = "onlyRecord";

    //廉闻要论编码
    public static final String L_W_Y_L = "005";

    //以案示警编码
    public static final String Y_A_S_J = "010";

    //纪律审查与监督编码
    public static final String J_L_S_C = "011";

    //分公司动态编码
    public static final String F_G_S = "013";

    //超级管理员
    public static final String SUPER_CONTROL = "ROLE_SUPER";

    //省公司廉政视窗超级管理员
    public static final String ROLE_S_CJGLY_APPLY = "ROLE_S_CJGLY_APPLY";

    //普通用户
    public static final String ROLE_USER = "ROLE_USER";

    //纪检监察人员
    public static final String ROLE_SHENG_JIJIAN = "ROLE_DISCIPLINE";

    //省公司管理层
    public static final String ROLE_MANAGEMENT = "ROLE_MANAGEMENT";

    //分公司廉政视窗起草人员
    public static final String ROLE_FEN_LZSC_APPLY = "ROLE_FEN_LZSC_APPLY";

    /**
     * 操作成功
     */
    public static final String MESSAGE_SUCCESS = "操作成功";

    /**
     * 操作失败
     */
    public static final String MESSAGE_FAIL = "操作失败";

    /*SSO点击sysusergruop需要用到*/
    public static final String USER_GROUP = "/action/user/group/findAll/sso";

    /**
     * 手机端
     */
    public static final String MOBILE = "MOBILE";

    //统一待办接口处理标识返回标识
    public static final String SUCCESS_FLAG = "Y";
    public static final String FAILED_FLAG = "N";
}

