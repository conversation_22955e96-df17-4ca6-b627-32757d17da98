package com.simbest.boot.hnjjwz.todo.service;


import com.simbest.boot.base.service.ILogicService;
import com.simbest.boot.hnjjwz.todo.model.UsTodoModel;

/**
 * <strong>Title : IUsTodoModelService</strong><br>
 * <strong>Description : 用户业务待办业务操作</strong><br>
 * <strong>Create on : $date$</strong><br>
 * <strong>Modify on : $date$</strong><br>
 * <strong>Copyright (C) Ltd.</strong><br>
 *
 * <AUTHOR> <EMAIL>
 * @version <strong>V1.0.0</strong><br>
 * <strong>修改历史:</strong><br>
 * 修改人 修改日期 修改描述<br>
 * -------------------------------------------<br>
 */
public interface IUsTodoModelService extends ILogicService<UsTodoModel,String> {

    /**
     * 保存推送待办数据到本地
     * @param usTodoModel      待办对象
     * @return
     */
    UsTodoModel savaLocalTodoData(UsTodoModel usTodoModel);

    /**
     * 根据工作项ID更新本地待办数据
     * @param usTodoModel      待办对象
     * @return
     */
    boolean updateLocalTodoData(UsTodoModel usTodoModel);
}
