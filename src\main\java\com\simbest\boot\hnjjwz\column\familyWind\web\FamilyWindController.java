package com.simbest.boot.hnjjwz.column.familyWind.web;/**
 * Created by KZH on 2019/7/30 17:24.
 */

import com.simbest.boot.base.web.controller.LogicController;
import com.simbest.boot.hnjjwz.column.familyWind.model.FamilyWind;
import com.simbest.boot.hnjjwz.column.familyWind.service.IFamilyWindService;
import io.swagger.annotations.Api;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RestController;

/**
 * <AUTHOR>
 * @create 2019-07-30 17:24
 * @desc 家风栏目Controller
 **/
@Api(description = "家风栏目")
@Slf4j
@RestController
@RequestMapping(value = "/action/familyWind")
public class FamilyWindController extends LogicController<FamilyWind,String> {

    private IFamilyWindService iFamilyWindService;

    @Autowired
    public FamilyWindController(IFamilyWindService service){
        super(service);
        this.iFamilyWindService=service;

    }

}
