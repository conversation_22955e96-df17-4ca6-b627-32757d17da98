package com.simbest.boot.hnjjwz.process.wf.service;

import com.simbest.boot.base.service.ILogicService;
import com.simbest.boot.hnjjwz.backstage.announcement.model.Announcement;
import com.simbest.boot.hnjjwz.backstage.slideshow.model.SlideShow;
import com.simbest.boot.hnjjwz.backstage.taskStudy.model.TaskStudy;
import com.simbest.boot.hnjjwz.process.wf.model.PmInstence;
import com.simbest.boot.hnjjwz.process.wf.model.ProgramaDataForm;

import java.util.Map;

/**
 * <strong>Title : </strong><br>
 * <strong>Description : </strong><br>
 * <strong>Create on : 2018/6/19</strong><br>
 * <strong>Modify on : 2018/6/19</strong><br>
 * <strong>Copyright (C) Ltd.</strong><br>
 *
 * @<NAME_EMAIL>
 * @version <strong>V1.0.0</strong><br>
 * <strong>修改历史:</strong><br>
 * 修改人 修改日期 修改描述<br>
 * -------------------------------------------<br>
 */
public interface IPmInstenceService extends ILogicService<PmInstence,String> {

    /**
     * 获取到主单据数据
     * @param approvalForm
     * @return
     */
    PmInstence getByPmInsId( ProgramaDataForm approvalForm);

    /**
     * 获取到主单据数据
     * @param announcement
     * @return
     */
    PmInstence getByPmInsId( Announcement announcement);

    /**
     * 获取到主单据数据
     * @param slideShow
     * @return
     */
    PmInstence getByPmInsId( SlideShow slideShow);

    PmInstence getByPmInsId( TaskStudy taskStudy);

    /**
     * 逻辑删除操作
     * @param id
     * @return
     */
    int deleteByPmId(String id);

    /**
     * 获取US_PM_INSTENCE和US_APPROVAL_FORM的所有信息
     * @param bussinessKey
     * @return
     */
    Map<String,Object> findPmApproByBuKey( String bussinessKey);

    /**
     * 根据pmInsId查找主单据
     * @param pmInsId 单据ID
     * @return
     */
    PmInstence findByPmInsId(String pmInsId);
}
