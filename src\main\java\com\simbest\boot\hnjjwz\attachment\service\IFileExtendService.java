package com.simbest.boot.hnjjwz.attachment.service;

import com.simbest.boot.base.service.ILogicService;
import com.simbest.boot.sys.model.SysFile;

import java.util.List;

/**
 * 作用：附件扩展类
 * 作者：zsf
 * 时间：2018/07/28
 */
public interface IFileExtendService extends ILogicService<SysFile,String> {

    /**
     * 更新附件
     * @param pmInsId 主单据id
     * @param pmInsType 流程类型
     * @param id 附近id
     * @return
     */
    int updatePmInsId(String pmInsId, String pmInsType, String id,String pmInsTypePart);


    /**
     * 按照表单不同的地点上传附件并更新
     * @param pmInsId
     * @param id
     * @param pmInsTypePart 在表单上不同的上传地点
     * @return
     */
    int updatePmInsIdPart ( String pmInsId, String id,String pmInsTypePart);

    /**
     * 查询区域附件
     * @param pmInsId
     * @param filePart
     * @return
     */
    List<SysFile> getPartFile ( String pmInsId, String filePart );

    /**
     * 查询区域附件
     * @param pmInsId
     * @return
     */
    List<SysFile> getFile ( String pmInsId );

    /**
     * @Description 逻辑删除附件
     * @Date 2020/8/26 14:53
     * @Param [pmInsId]
     * @return int
     **/
    int deleteByPmInsId(String pmInsId);

}
