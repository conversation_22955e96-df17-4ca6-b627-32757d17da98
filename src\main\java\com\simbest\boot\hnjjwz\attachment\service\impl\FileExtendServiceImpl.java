package com.simbest.boot.hnjjwz.attachment.service.impl;

import com.simbest.boot.base.service.impl.LogicService;
import com.simbest.boot.hnjjwz.attachment.repository.FileExtendRepository;
import com.simbest.boot.hnjjwz.attachment.service.IFileExtendService;
import com.simbest.boot.sys.model.SysFile;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;

import java.util.List;

/**
 * 作用：附件扩展类
 * 作者：zsf
 * 时间：2018/07/28
 */
@Slf4j
@Service
public class FileExtendServiceImpl extends LogicService<SysFile,String> implements IFileExtendService {
    private FileExtendRepository fileExtendRepository;

    @Autowired
    public FileExtendServiceImpl(FileExtendRepository repository) {
        super(repository);
        this.fileExtendRepository = repository;
    }

    /**
     * 更新附件
     * @param pmInsId 主单据id
     * @param pmInsType 流程类型
     * @param id 附近id
     * @return
     */
    @Override
    public int updatePmInsId( String pmInsId, String pmInsType, String id,String pmInsTypePart ) {
        return fileExtendRepository.updatePmInsId(pmInsId,pmInsType,id,pmInsTypePart);
    }

    /**
     * 按照表单不同的地点上传附件并更新
     * @param pmInsId
     * @param id
     * @param pmInsTypePart 在表单上不同的上传地点
     * @return
     */
    @Override
    public int updatePmInsIdPart ( String pmInsId, String id, String pmInsTypePart ) {
        return fileExtendRepository.updatePmInsIdPart(pmInsId,id,pmInsTypePart);
    }

    /**
     * 查询区域附件
     * @param pmInsId 流程实例id
     * @param filePart 区域标识
     * @return
     */
    @Override
    public List<SysFile> getPartFile(String pmInsId, String filePart) {
        return fileExtendRepository.getPartFile(pmInsId,filePart);
    }

    @Override
    public List<SysFile> getFile(String pmInsId) {
        return fileExtendRepository.getFile(pmInsId);
    }

    @Override
    public int deleteByPmInsId(String pmInsId) {
        return fileExtendRepository.deleteByPmInsId(pmInsId);
    }
}
