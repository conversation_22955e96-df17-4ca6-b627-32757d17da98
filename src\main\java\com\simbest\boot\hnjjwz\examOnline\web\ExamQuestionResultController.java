package com.simbest.boot.hnjjwz.examOnline.web;/**
 * Created by GZJ on 2019/6/25 16:25.
 */

import com.simbest.boot.base.web.controller.LogicController;
import com.simbest.boot.base.web.response.JsonResponse;
import com.simbest.boot.hnjjwz.examOnline.model.ExamQuestionResult;
import com.simbest.boot.hnjjwz.examOnline.service.IExamQuestionResultService;
import io.swagger.annotations.Api;
import io.swagger.annotations.ApiImplicitParam;
import io.swagger.annotations.ApiImplicitParams;
import io.swagger.annotations.ApiOperation;
import lombok.extern.slf4j.Slf4j;
import org.springframework.data.domain.Pageable;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RequestParam;
import org.springframework.web.bind.annotation.RestController;

/**
 * <AUTHOR>
 * @create 2019-06-25 16:25
 * @desc
 **/
@Api(description = "试卷所做题目答案相关接口")
@Slf4j
@RestController
@RequestMapping(value = "/action/examQuestionResult")
public class ExamQuestionResultController extends LogicController<ExamQuestionResult,String> {

    private IExamQuestionResultService iExamQuestionResultService;

    public ExamQuestionResultController(IExamQuestionResultService iExamQuestionResultService) {
        super(iExamQuestionResultService);
        this.iExamQuestionResultService=iExamQuestionResultService;
    }


    /**
     * 获取当前登录人的试卷详情
     * @return
     */
    @ApiOperation(value = "获取当前登录人的试卷详情", notes = "获取当前登录人的试卷详情")
    @ApiImplicitParams( {
            @ApiImplicitParam(name = "page", value = "当前页码", dataType = "int", paramType = "query", example = "1"), //
            @ApiImplicitParam(name = "size", value = "每页数量", dataType = "int", paramType = "query", example = "10"), //
            @ApiImplicitParam(name = "direction", value = "排序规则（asc/desc）", dataType = "String", //
                    paramType = "query"), //
            @ApiImplicitParam(name = "properties", value = "排序规则（属性名称）", dataType = "String", //
                    paramType = "query")
    } )
    @PostMapping(value = {"/findOneselfDetails","/findOneselfDetails/sso"})
    public JsonResponse findOneselfDetails(@RequestParam(required = false, defaultValue = "1") int page,
                                    @RequestParam(required = false, defaultValue = "10") int size,
                                    @RequestParam(required = false) String direction,
                                    @RequestParam(required = false) String properties,
                                           @RequestParam(required = false) String truename) {
        Pageable pageable = iExamQuestionResultService.getPageable(page, size, direction, properties);

        return  iExamQuestionResultService.findOneselfDetails(truename,pageable);
    }



}
