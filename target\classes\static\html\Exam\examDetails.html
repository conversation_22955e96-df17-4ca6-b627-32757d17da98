<!DOCTYPE html>
<html xmlns="http://www.w3.org/1999/xhtml"
      xmlns:th="http://www.thymeleaf.org">
<head>
    <title>在线测试个人详情</title>
    <meta http-equiv="Content-Type" content="text/html; charset=utf-8"/>
    <meta http-equiv="X-UA-Compatible" content="IE=edge" />
    <meta name="viewport" content="width=device-width, initial-scale=1, shrink-to-fit=no"/>
    <!--
    <link href="http://************:8088/simbestui/fonts/iconfont/iconfont.css?v=svn.revision" th:href="@{http://************:8088/simbestui/fonts/iconfont/iconfont.css?v=svn.revision}" rel="stylesheet"/>
    -->
    <link href="../../fonts/iconfont/iconfont.css?v=svn.revision" th:href="@{/fonts/iconfont/iconfont.css?v=svn.revision}" rel="stylesheet"/>
    <link href="http://************:8088/simbestui/js/themes/default/easyui.css?v=svn.revision" th:href="@{http://************:8088/simbestui/js/themes/default/easyui.css?v=svn.revision}" rel="stylesheet"/>
    <link href="http://************:8088/simbestui/js/themes/icon.css?v=svn.revision" th:href="@{http://************:8088/simbestui/js/themes/icon.css?v=svn.revision}" rel="stylesheet"/>
    <link href="http://************:8088/simbestui/css/public.css?v=svn.revision" th:href="@{http://************:8088/simbestui/css/public.css?v=svn.revision}" rel="stylesheet"/>
    <link href="http://************:8088/simbestui/css/pages.css?v=svn.revision" th:href="@{http://************:8088/simbestui/css/pages.css?v=svn.revision}" rel="stylesheet"/>
    <script src="http://************:8088/simbestui/js/jquery.min.js?v=svn.revision" th:src="@{http://************:8088/simbestui/js/jquery.min.js?v=svn.revision}" type="text/javascript"></script>
    <script src="http://************:8088/simbestui/js/jquery.easyui.min.js?v=svn.revision" th:src="@{http://************:8088/simbestui/js/jquery.easyui.min.js?v=svn.revision}" type="text/javascript"></script>
    <script src="http://************:8088/simbestui/js/easyui-lang-zh_CN.js?v=svn.revision" th:src="@{http://************:8088/simbestui/js/easyui-lang-zh_CN.js?v=svn.revision}" type="text/javascript"></script>
    <script src="../../js/jquery.config.js?v=svn.revision" th:src="@{/js/jquery.config.js?v=svn.revision}" type="text/javascript"></script>
    <script src="http://************:8088/simbestui/js/jquery.zjsfile.js?v=svn.revision" th:src="@{http://************:8088/simbestui/js/jquery.zjsfile.js?v=svn.revision}" type="text/javascript"></script>
    <script src="http://************:8088/simbestui/js/jquery.aduq.js?v=svn.revision" th:src="@{http://************:8088/simbestui/js/jquery.aduq.js?v=svn.revision}" type="text/javascript"></script>
    <script src="http://************:8088/simbestui/js/jquery.process.js?v=svn.revision" th:src="@{http://************:8088/simbestui/js/jquery.process.js?v=svn.revision}" type="text/javascript"></script>
    <script type="text/javascript">
        $(function(){
            //showDialog做为打开对话框的公共class名称   showDialogindex做为打开修改对话框传行的索引   delete做为删除按钮配置命令   deleteid做为删除传值  searchtable做为条件查询的按钮公共class名称
            getCurrent();
            var truename=web.currentUser.truename;
            var pageparam={
                "listtable":{
                    /*"idFiled":"ID", 用于自定义id的情况，当接口查出的id不为id，比如是ID时，在这里设置字段名 */
                    "listname":"#examDetailsTable",//table列表的id名称，需加# menuExpalinTable
                    "querycmd":"action/examQuestionResult/findOneselfDetails?truename="+gps.publishName,//table列表的查询命令
                    "checkboxall":true,
                    "contentType":"application/json; charset=utf-8",//table列表的传参方式，如果是json就写，不写默认kv
                    "nowrap": true,//把数据显示在一行里,默认true
                    "styleClass": "noScroll",
                    "frozenColumns":[[
                        { field: "ck",checkbox:true}
                    ]],//固定在左侧的列
                    "columns":[[//列
                        { title: "所做题库编码", field: "questionBankCode", width: 120},
                        { title: "所做题目编码", field: "questionCode", width: 120},
                        { title: "所做答案编码", field: "answerCode", width: 120},
                        { title: "答题人姓名", field: "publishName", width: 120},
                        { title: "答题人所在组织名", field: "publishOrgName", width: 120}
                    ] ]
                    /*"pagerbar": [{
                        id:"deleteall",
                        iconCls: 'icon-remove',
                        text:"批量删除&nbsp;"
                    }],
                    "deleteall":{//批量删除deleteall.id要与pagerbar.id相同
                        "id":"deleteall",
                        "url":"action/examQuestionResult/deleteAllByIds",
                        "contentType":"application/json; charset=utf-8"
                    }*/
                },
                "dialogform":{
                    "dialogid":"#buttons",//对话框的id
                    "formname":"#examDetailsTableAddForm",//新增或修改对话框的formid需加#
                    //"insertcmd":"action/examQuestionResult/importQuestionBank",//新增命令
                    //"updatacmd":"action/examQuestionResult/update",//修改命令
                    "onSubmit":function(data){
                        return true;
                    }
                }
            };
            loadGrid(pageparam);
        });
        //form表单填充数据之前做什么,data为填充数据的集合,isupdate表示是新增还是修改,若为新增则data为{}
        function beforerender(data,isupdate){
            if(isupdate){
                $('.update-readonly').hide();
            }else{
                $('.update-readonly').show();
            }
        };
        //回调函数
        window.programaInfo=function(data){
        };

    </script>
</head>
<body class="body_page">
<!--searchform-->
<form id="examDetailsTableQueryForm" >
    <!--<table border="0" cellpadding="0" cellspacing="6" width="100%">
        <input id="programaCode" name="programaCode" type="hidden"/>
        <tr>
            <td width="50" align="right">题库编码</td>
            <td width="150"><input name="questionBankCode" type="text" value="" /></td>
            <td>
                <div class="w100">
                    <a class="btn a_primary fl searchtable"><span>查询</span></a>
                    <a class="btn a_success showDialog fr"><span>新增</span></a>
                </div>
            </td>
        </tr>
    </table>-->
</form>
<!--table-->
<div class="examDetailsTable"><table id="examDetailsTable"></table></div>
<!--新增修改的dialog页面-->
<div id="buttons" title="新增或修改" class="easyui-dialog" style="width:800px;height:500px;">
    <!--<form id="examDetailsTableAddForm" method="post" contentType="application/json; charset=utf-8"  onSubmit="onSubmit()" >
        <input id="id" name="id" type="hidden" />
        <table border="0" cellpadding="0" cellspacing="10">
            <tr>
                <td width="100" align="right"><font class="col_r">*</font>题库编码</td>
                <td colspan="3">
                    <input id="questionBankCode" name="questionBankCode" type="text"  class="easyui-validatebox"  required='required' /></td>
            </tr>
            <tr>
                <td width="100" align="right"><font class="col_r">*</font>题库名</td>
                <td colspan="3">
                    <input id="questionBankName" name="questionBankName" type="text" class="easyui-validatebox"  required='required' /></td>
            </tr>
            <tr>
                <td width="100" align="right"><font class="col_r">*</font>考卷规定时间限制</td>
                <td colspan="3">
                    <input id="setTime" name="setTime" type="text" class="easyui-validatebox"  required='required' /></td>
            </tr>

            <tr >
                <td width="100" align="right">输入题目数量</td>
                <td width="160" >
                    <input id="quantity" name="quantity" type="text" class="easyui-validatebox"/>

                </td>
                <td>
                </td>

            </tr>
        </table>
        <table border="0" cellpadding="0" cellspacing="10" id="newTable">

        </table>

    </form>-->
</div>
</body>
</html>
