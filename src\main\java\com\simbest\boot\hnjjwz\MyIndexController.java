/*
 * 版权所有 © 北京晟壁科技有限公司 2008-2027。保留一切权利!
 */
package com.simbest.boot.hnjjwz;


import com.simbest.boot.security.IUser;
import com.simbest.boot.util.security.SecurityUtils;
import org.springframework.stereotype.Controller;
import org.springframework.ui.Model;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RequestMethod;
import org.springframework.web.servlet.ModelAndView;
/**
 * 用途：
 * 作者: lishuyi
 * 时间: 2018/8/1  15:12
 */
@Controller
public class MyIndexController {
    @RequestMapping(value = "/local", method = {RequestMethod.POST, RequestMethod.GET})
    public ModelAndView index() {
        return new ModelAndView("iconfontL");
    }

    @RequestMapping(value = "/remote", method = {RequestMethod.POST, RequestMethod.GET})
    public ModelAndView remote() {
        return new ModelAndView("iconfontU");
    }
}
