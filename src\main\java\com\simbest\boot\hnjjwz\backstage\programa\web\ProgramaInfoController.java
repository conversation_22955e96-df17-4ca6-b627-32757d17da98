package com.simbest.boot.hnjjwz.backstage.programa.web;

import com.simbest.boot.base.web.controller.LogicController;
import com.simbest.boot.base.web.response.JsonResponse;
import com.simbest.boot.constants.ApplicationConstants;
import com.simbest.boot.hnjjwz.backstage.programa.model.ProgramaInfo;
import com.simbest.boot.hnjjwz.backstage.programa.service.IProgramaInfoService;
import com.simbest.boot.hnjjwz.backstage.templateLayout.model.TemplateLayout;
import com.simbest.boot.hnjjwz.backstage.templateLayout.service.ITemplateLayoutService;
import com.simbest.boot.hnjjwz.util.FileTool;
import com.simbest.boot.sys.model.SysFile;
import com.simbest.boot.sys.service.ISysFileService;
import io.swagger.annotations.Api;
import io.swagger.annotations.ApiImplicitParam;
import io.swagger.annotations.ApiImplicitParams;
import io.swagger.annotations.ApiOperation;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.lang3.StringUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.data.domain.Pageable;
import org.springframework.web.bind.annotation.*;

import java.util.ArrayList;
import java.util.Arrays;
import java.util.List;
import java.util.Map;

/**
 * <AUTHOR>
 * @Data 2019/04/02
 * @Description 栏目基本信息相关接口
 */
@Api(description = "栏目基本信息相关接口")
@Slf4j
@RestController
@RequestMapping(value = "/action/programaInfo")
public class ProgramaInfoController extends LogicController<ProgramaInfo, String> {

    private IProgramaInfoService programaInfoService;

    @Autowired
    public ProgramaInfoController(IProgramaInfoService programaInfoService) {
        super(programaInfoService);
        this.programaInfoService = programaInfoService;
    }

    @Autowired
    private ISysFileService sysFileService;

    @Autowired
    private FileTool fileTool;

    @Autowired
    private ITemplateLayoutService iTemplateLayoutService;

    /**
     * 新建栏目信息。新建栏目信息时，栏目的code要用一套规则来自动生成。
     * @param programaInfo
     * @return
     */
    @ApiOperation (value = "新建栏目信息", notes = "新建栏目信息")
    public JsonResponse create( @RequestBody (required = false) ProgramaInfo programaInfo) {
        String parentProgramaCode = ApplicationConstants.EMPTY;
        if(programaInfo!=null){
            parentProgramaCode = programaInfo.getParentProgramaCode();
        }
        //自动生成栏目编码
        String newCode = programaInfoService.autoGenerationCode(parentProgramaCode);
        programaInfo.setProgramaCode( newCode );
        //把图片的id取出来，存到数据库中
        List<SysFile> sysFileList = programaInfo.getProgramaCoverFile();
        String fileId= fileTool.findFileIds( sysFileList );
        programaInfo.setProgramaCoverId( fileId );
        //设置栏目全路径
        String parentProgramaName = programaInfo.getParentProgramaName();
        String programaDisplayName = "";
        if(StringUtils.isEmpty(parentProgramaName  )){
            programaDisplayName=programaInfo.getProgramaName();
        }else {
            programaDisplayName = parentProgramaName+ApplicationConstants.ROOT_PAGE+programaInfo.getProgramaName();
        }

        programaInfo.setProgramaDisplayName( programaDisplayName );
        return super.create( programaInfo );
    }

    /**
     *修改栏目信息
     * @param programaInfo
     * @return
     */
    @ApiOperation(value = "修改栏目信息", notes = "修改栏目类型信息")
    public JsonResponse update( @RequestBody(required = false) ProgramaInfo programaInfo) {
        //把图片的id取出来，存到数据库中
        List<SysFile> sysFileList = programaInfo.getProgramaCoverFile();
        String fileId= fileTool.findFileIds( sysFileList );
        TemplateLayout templateLayout = iTemplateLayoutService.findlocationName(programaInfo.getProgramaName());

        if(templateLayout!=null){
            String templateUrl = templateLayout.getTemplateUrl();
            if(templateUrl.contains("=")){
                String[] split = templateUrl.split("=");
                templateLayout.setTemplateUrl(split[0]+"="+fileId);
                iTemplateLayoutService.update(templateLayout);
            }
        }

        /**
         * 修改的话 也要去修改栏目全路径
         */

            if(programaInfo.getProgramaDisplayName()!=null&&programaInfo.getProgramaDisplayName().contains("/")){
                String[] split = programaInfo.getProgramaDisplayName().split("/");
                programaInfo.setProgramaDisplayName(split[0]+"/"+programaInfo.getProgramaName());
            }else {
                programaInfo.setProgramaDisplayName(programaInfo.getProgramaName());
            }

        programaInfo.setProgramaCoverId( fileId );
        return super.update(programaInfo );
    }

    /**
     *条件组合查询获取栏目详细信息不分页
     * @param programaInfo
     * @return
     */
    @ApiOperation(value = "条件组合查询获取栏目详细信息不分页", notes = "条件组合查询获取栏目详细信息不分页")
    @PostMapping({"/findAllNoPage","/findAllNoPage/sso"})
    public JsonResponse findAllNoPage( @RequestBody(required = false) ProgramaInfo programaInfo ) {
        return super.findAllNoPage( programaInfo);
    }

    /**
     * 根据id查询栏目信息
     * @param id
     * @return
     */
    @ApiOperation(value = "根据id查询栏目信息", notes = "根据id查询栏目信息")
    @ApiImplicitParam(name = "id", value = "栏目信息ID", dataType = "String", paramType = "query")
    @PostMapping({"/findById","/findById/sso"})
    public JsonResponse findById(@RequestParam(required = false) String id) {
        ProgramaInfo programaInfo = programaInfoService.findById( id );
        if(programaInfo==null){
            return JsonResponse.success( null );
        }
        //查询父栏目的信息
        ProgramaInfo parentProgramaInfo = programaInfoService.findByFromProCode(programaInfo.getParentProgramaCode());
        if(parentProgramaInfo!=null){
            programaInfo.setParentProgramaName( parentProgramaInfo.getProgramaName() );
        }
        String fileId = programaInfo.getProgramaCoverId();//获取封面文件id
        List<SysFile> sysFileList = new ArrayList<>(  );
        if( !StringUtils.isEmpty( fileId )){
            List<String> fileList = Arrays.asList( fileId.split( "," ) );
            for(String fileNewId:fileList){
                SysFile sysfile = sysFileService.findById( fileId );
                sysFileList.add( sysfile );
            }
        }
        programaInfo.setProgramaCoverFile( sysFileList );
        return JsonResponse.success( programaInfo );
    }

    /**
     * 根据id逻辑删除
     * @param id
     * @return
     */
    @ApiOperation(value = "删除栏目信息", notes = "删除栏目信息")
    @ApiImplicitParam (name = "id", value = "主键ID",  dataType = "String", paramType = "query")
    @PostMapping({"/deleteById","/deleteById/sso"})
    public JsonResponse deleteById(@RequestParam (required = false) String id) {
        return super.deleteById( id );
    }

    /**
     * 批量逻辑删除栏目分类信息
     * @param ids
     * @return JsonResponse
     */
    @ApiOperation(value = "批量逻辑删除栏目信息", notes = "批量逻辑删除栏目信息")
    public JsonResponse deleteAllByIds(@RequestBody(required = false) String[] ids) {
        return  super.deleteAllByIds(ids);
    }

    /**
     * 根据所属栏目分类id、结点类型、父栏目名（模糊）、栏目名称（模糊）并分页
     * @return
     */
    @ApiOperation(value = "根据所属栏目分类id、结点类型、父栏目名（模糊）、栏目名称（模糊）并分页", notes = "根据所属栏目分类id、结点类型、父栏目名（模糊）、栏目名称（模糊）并分页")
    @ApiImplicitParams ({ //
            @ApiImplicitParam(name = "page", value = "当前页码", dataType = "int", paramType = "query", //
                    required = true, example = "1"), //
            @ApiImplicitParam(name = "size", value = "每页数量", dataType = "int", paramType = "query", //
                    required = true, example = "10"), //
            @ApiImplicitParam(name = "direction", value = "排序规则（asc/desc）", dataType = "String", //
                    paramType = "query"), //
            @ApiImplicitParam(name = "properties", value = "排序规则（属性名称）", dataType = "String", //
                    paramType = "query")
    })
    @PostMapping (value = {"/findDimProgramaInfo","/findDimProgramaInfo/sso"})
    public JsonResponse findDimProgramaInfo( @RequestParam(required = false, defaultValue = "1") int page, //
                                             @RequestParam(required = false, defaultValue = "10") int size, //
                                             @RequestParam(required = false,defaultValue = "desc") String direction, //
                                             @RequestParam(required = false,defaultValue = "programa_code") String properties, //
                                             @RequestBody(required = false) Map map) {
        Pageable pageable = programaInfoService.getPageable(page, size, direction, properties);
        String programaClassifyIds = (String)map.get("programaClassifyIds");
        String nodeStyles = (String)map.get("nodeStyles");
        String parentProgramaName = (String)map.get("parentProgramaName");
        String programaName = (String)map.get("programaName");
        return programaInfoService.findDimProgramaInfo(programaClassifyIds,nodeStyles,parentProgramaName,programaName,pageable);
    }

    /**
     * 页面初始化时获取根栏目以及下一级栏目
     * @return
     */
    @ApiOperation(value = "页面初始化时获取根栏目以及下一级栏目", notes = "页面初始化时获取根栏目以及下一级栏目")
    @PostMapping(value = {"/findRootAndNext","/findRootAndNext/sso"})
    public JsonResponse findRootAndNext() {
        return JsonResponse.success(programaInfoService.findRootAndNext());
    }

    /**
     * 根据登录人进行页面初始化时获取根栏目以及下一级栏目
     * @return
     */
    @ApiOperation(value = "根据登录人进行页面初始化时获取根栏目以及下一级栏目", notes = "根据登录人进行页面初始化时获取根栏目以及下一级栏目")
    @PostMapping(value = {"/findUserNameRootAndNext","/findUserNameRootAndNext/sso"})
    public JsonResponse findUserNameRootAndNext(@RequestParam(required = false) String CompanyName) {
        return JsonResponse.success(programaInfoService.findUserNameRootAndNext(CompanyName));
    }

    /**
     * 获取栏目的下一级栏目
     * @param parentProgramaCode
     * @return
     */
    @ApiOperation(value = "获取栏目的下一级栏目", notes = "获取栏目的下一级栏目")
    @ApiImplicitParam(name = "parentProgramaCode", value = "栏目编码", dataType = "String", paramType = "query")
    @PostMapping(value = {"/findSonByParentCode","/findSonByParentCode/sso"})
    public JsonResponse findSonByParentCode( @RequestParam(required = false) String parentProgramaCode) {
        return programaInfoService.findSonByParentCode(parentProgramaCode);
    }
}
