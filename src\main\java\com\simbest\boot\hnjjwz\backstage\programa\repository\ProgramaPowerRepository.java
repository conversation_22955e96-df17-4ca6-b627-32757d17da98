package com.simbest.boot.hnjjwz.backstage.programa.repository;/**
 * Created by KZH on 2019/6/26 15:03.
 */

import com.simbest.boot.base.repository.LogicRepository;
import com.simbest.boot.hnjjwz.backstage.programa.model.ProgramaPower;
import org.springframework.data.jpa.repository.Query;
import org.springframework.data.repository.query.Param;

import java.util.List;

/**
 * <AUTHOR>
 * @create 2019-06-26 15:03
 * @desc 栏目权限相关sql
 **/
public interface ProgramaPowerRepository extends LogicRepository<ProgramaPower,String> {


    /**
     * 查看人对应的栏目权限
     * @return
     */
    @Query(value =  "select t.* from US_PROGRAMA_POWER t where t.user_role=:truename and t.enabled=1 ",
            nativeQuery = true)
    List<ProgramaPower> findPower(@Param( "truename" )String truename);

    /**
     * 查看人对应的栏目权限
     * @return
     */
    @Query(value =  "select t.* from US_PROGRAMA_POWER t where t.user_role in (:truename)  and t.enabled=1 ",
            nativeQuery = true)
    List<ProgramaPower> findPower(@Param( "truename" )List<String> truename);
}
