<!DOCTYPE html>
<html xmlns="http://www.w3.org/1999/xhtml"
      xmlns:th="http://www.thymeleaf.org">
<head>
    <title>以案示警-清廉移动</title>
    <meta http-equiv="Content-Type" content="text/html; charset=utf-8"/>
    <meta http-equiv="X-UA-Compatible" content="IE=edge" />
    <meta name="viewport" content="width=device-width, initial-scale=1, shrink-to-fit=no"/>
    <!--
       <link href="http://************:8088/simbestui/fonts/iconfont/iconfont.css?v=svn.revision" th:href="@{http://************:8088/simbestui/fonts/iconfont/iconfont.css?v=svn.revision}" rel="stylesheet"/>
       -->
    <link href="../../fonts/iconfont/iconfont.css?v=svn.revision" th:href="@{/fonts/iconfont/iconfont.css?v=svn.revision}" rel="stylesheet"/>
    <link href="http://************:8088/simbestui/js/themes/default/easyui.css?v=svn.revision" th:href="@{http://************:8088/simbestui/js/themes/default/easyui.css?v=svn.revision}" rel="stylesheet"/>
    <link href="http://************:8088/simbestui/js/themes/icon.css?v=svn.revision" th:href="@{http://************:8088/simbestui/js/themes/icon.css?v=svn.revision}" rel="stylesheet"/>
    <link href="http://************:8088/simbestui/css/public.css?v=svn.revision" th:href="@{http://************:8088/simbestui/css/public.css?v=svn.revision}" rel="stylesheet"/>
    <link href="http://************:8088/simbestui/css/pages.css?v=svn.revision" th:href="@{http://************:8088/simbestui/css/pages.css?v=svn.revision}" rel="stylesheet"/>
    <link href="../../css/webSite.css?v=svn.revision" th:href="@{/css/webSite.css?v=svn.revision}" rel="stylesheet"/>
    <script src="http://************:8088/simbestui/js/jquery.min.js?v=svn.revision" th:src="@{http://************:8088/simbestui/js/jquery.min.js?v=svn.revision}" type="text/javascript"></script>
    <script src="http://************:8088/simbestui/js/jquery.easyui.min.js?v=svn.revision" th:src="@{http://************:8088/simbestui/js/jquery.easyui.min.js?v=svn.revision}" type="text/javascript"></script>
    <script src="http://************:8088/simbestui/js/easyui-lang-zh_CN.js?v=svn.revision" th:src="@{http://************:8088/simbestui/js/easyui-lang-zh_CN.js?v=svn.revision}" type="text/javascript"></script>
    <script src="../../js/jquery.config.js?v=svn.revision" th:src="@{/js/jquery.config.js?v=svn.revision}" type="text/javascript"></script>
    <script src="http://************:8088/simbestui/js/jquery.zjsfile.js?v=svn.revision" th:src="@{http://************:8088/simbestui/js/jquery.zjsfile.js?v=svn.revision}" type="text/javascript"></script>
    <script src="http://************:8088/simbestui/js/jquery.aduq.js?v=svn.revision" th:src="@{http://************:8088/simbestui/js/jquery.aduq.js?v=svn.revision}" type="text/javascript"></script>
    <script src="http://************:8088/simbestui/js/jquery.process.js?v=svn.revision" th:src="@{http://************:8088/simbestui/js/jquery.process.js?v=svn.revision}" type="text/javascript"></script>
    <script src="../../js/webSite.js?v=svn.revision" th:src="@{/js/webSite.js?v=svn.revision}" type="text/javascript"></script>
    <script type="text/javascript">
        $(function(){
            webSite();
            ajaxgeneral({
                url:"action/templateLayout/constructWarnCompany",
                success:function(res) {
                    //渲染模块
                    for(var i in res.data) {
                        var dataD = res.data[i];
                        var dH=["<div class='mt10'><div class='modTit'>"+
                        fastrenderRow(dataD, "<strong class='fl'>{{locationName}}</strong><a target='_blank' href='list.html?id={{locationId}}' class='fr'>+</a>")
                        +"</div><ul class='list list_regulation'>"];
                        for (var j in dataD.templateData) {
                            dataD.templateData[j].creationTime = getdateformat(dataD.templateData[j].creationTime, "yyyy-MM-dd");
                            //if (dataD.templateData[j].programaCoverFile) dataD.templateData[j].downLoadUrl = dataD.templateData[j].programaCoverFile[0].downLoadUrl;
                        }
                        dH.push(fastrender(dataD.templateData, "<li><a target='_blank' href='listDetails.html?id={{id}}&pmInsId={{pmInsId}}'>\n" +
                            "                <h6>{{title}}<span style='float:right'>{{digest}}<font class=\"col_b\">[详情]</font></span></h6>\n" +//[{{belongDepartmentName}}]
                            "            </a></li>"));
                        dH.push("</ul></div>");
                        $(".center").append(dH.join(""));
                    }
                }
            });
        });
    </script>
    <style>
        ul.list_regulation li p {
             padding-top: 0px;
             margin-top: 0px;
        }
    </style>
</head>
<body>
<!--top-->

<div class="nav">
    <ul class="auto1024">

    </ul>
</div>
<!--center-->
<div class="auto1024 center">
<!--    <div class="mt10"><div class="modTit"><strong class="fl">党廉信息动态</strong><a target="_blank" href="list.html?id=013002" class="fr">+</a></div><ul class="list list_regulation"></ul></div>-->
<!--    <div class="mt10"><div class="modTit"><strong class="fl">纪检信息动态</strong><a target="_blank" href="list.html?id=013001" class="fr">+</a></div><ul class="list list_regulation"></ul></div>-->
</div>
<!--copy-->
<div class="copy">©版权所有 中国移动通信集团河南有限公司</div>
</body>
</html>
