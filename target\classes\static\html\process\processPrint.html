﻿<!DOCTYPE html>
<html xmlns="http://www.w3.org/1999/xhtml"
	  xmlns:th="http://www.thymeleaf.org">
<head>
	<title>打印</title>
	<meta http-equiv="Content-Type" content="text/html; charset=utf-8"/>
	<meta http-equiv="X-UA-Compatible" content="IE=edge" />
	<meta name="viewport" content="width=device-width, initial-scale=1, shrink-to-fit=no"/>
	<!--
    <link href="http://************:8088/simbestui/fonts/iconfont/iconfont.css?v=svn.revision" th:href="@{http://************:8088/simbestui/fonts/iconfont/iconfont.css?v=svn.revision}" rel="stylesheet"/>
    -->
    <script src="http://************:8088/simbestui/js/jquery.min.js?v=svn.revision" th:src="@{http://************:8088/simbestui/js/jquery.min.js?v=svn.revision}" type="text/javascript"></script>
    <script src="http://************:8088/simbestui/js/jquery.migrate.js?v=svn.revision" th:src="@{http://************:8088/simbestui/js/jquery.migrate.js?v=svn.revision}" type="text/javascript"></script>
    <script src="http://************:8088/simbestui/js/jquery.easyui.min.js?v=svn.revision" th:src="@{http://************:8088/simbestui/js/jquery.easyui.min.js?v=svn.revision}" type="text/javascript"></script>
	<script src="http://************:8088/simbestui/js/easyui-lang-zh_CN.js?v=svn.revision" th:src="@{http://************:8088/simbestui/js/easyui-lang-zh_CN.js?v=svn.revision}" type="text/javascript"></script>
	<script src="../../js/jquery.config.js?v=svn.revision" th:src="@{/js/jquery.config.js?v=svn.revision}" type="text/javascript"></script>
	<script src="http://************:8088/simbestui/js/jquery.zjsfile.js?v=svn.revision" th:src="@{http://************:8088/simbestui/js/jquery.zjsfile.js?v=svn.revision}" type="text/javascript"></script>
	<script src="http://************:8088/simbestui/js/jquery.aduq.js?v=svn.revision" th:src="@{http://************:8088/simbestui/js/jquery.aduq.js?v=svn.revision}" type="text/javascript"></script>
	<script src="http://************:8088/simbestui/js/jquery.process.js?v=svn.revision" th:src="@{http://************:8088/simbestui/js/jquery.process.js?v=svn.revision}" type="text/javascript"></script>
	<script type="text/javascript">
        $(function(){
            $(".ctable tbody").html("");
            ajaxgeneral({
                url:"action/queryOpinionHistory/getWfOptMags",
                data:{"processInstId":gps.processInstId},
                success:function(data){
                    var html=[];
                    for(var i in data.data){
                        html.push('<tr><td>'+data.data[i].PRODUCERNAME+'</td><td>'+data.data[i].CONTENT+'</td><td>'+data.data[i].MODIFIEDTIME+'</td></tr>');
                    }
                    $(".ctable tbody").html(html.join(""));
                }
            });
            loadForm("approvalInfoForm",gps);

        });
        window.getchoosedata=function(){
            $("#printBody").jqprint({
                debug: false,
                importCSS: true,
                printContainer: true,
                operaSupport: true
            });
			//window.print();
            return {"state":0};
		};
        function beforerender(data){
            data.nmaFileInfo="无";
            if(data.nmaFile) data.nmaFileInfo=data.nmaFile[0].fileName;
            data.beginTimeQunInfo=data.beginTimeQun=="1"?"上午":"下午";
            data.endTimeQunInfo=data.endTimeQun=="1"?"上午":"下午";

        };
	</script>
</head>
<body>
<div id="printBody">
    <link href="../../fonts/iconfont/iconfont.css?v=svn.revision" th:href="@{/fonts/iconfont/iconfont.css?v=svn.revision}" rel="stylesheet"/>
    <link href="http://************:8088/simbestui/js/themes/default/easyui.css?v=svn.revision" th:href="@{http://************:8088/simbestui/js/themes/default/easyui.css?v=svn.revision}" rel="stylesheet"/>
    <link href="http://************:8088/simbestui/js/themes/icon.css?v=svn.revision" th:href="@{http://************:8088/simbestui/js/themes/icon.css?v=svn.revision}" rel="stylesheet"/>
    <link href="http://************:8088/simbestui/css/public.css?v=svn.revision" th:href="@{http://************:8088/simbestui/css/public.css?v=svn.revision}" rel="stylesheet"/>
    <link href="http://************:8088/simbestui/css/pages.css?v=svn.revision" th:href="@{http://************:8088/simbestui/css/pages.css?v=svn.revision}" rel="stylesheet"/>
    <form id="approvalInfoForm" method="post" contentType="application/json; charset=utf-8" cmd-select="action/approvalForm/getApprovalFromDetail" beforerender="beforerender()">
	<h5 class="txtc f18">中国移动通信集团河南有限公司会议活动审批表</h5>
	<table border="1" cellpadding="0" cellspacing="15" class="tabForm tabPrint">
        <tr>
            <td class="titInfo" width="150">会议编号</td><td colspan="3" style="text-align:left;"><span id="recordNum">&nbsp;</span></td>
        </tr>
        <tr>
            <td class="titInfo" width="150">会议名称</td><td colspan="3" style="text-align:left;"><span id="title">&nbsp;</span></td>
        </tr>
        <tr>
            <td class="titInfo" width="150">申请部门</td><td colspan="3" style="text-align:left;"><span id="belongDepartmentName">&nbsp;</span></td>
        </tr>
        <tr>
            <td class="titInfo" width="150">会议类别</td><td width="300" style="text-align:left;"><span id="describe">&nbsp;</span></td>
            <td class="titInfo" width="150">会议标准</td><td width="300" style="text-align:left;"><span id="applyStandard">&nbsp;</span>元/人●天</td>
        </tr>
        <tr>
            <td class="titInfo" width="150">会议开始时间</td><td width="300" style="text-align:left;"><span id="beginTime">&nbsp;</span>&nbsp;<span id="beginTimeQunInfo">&nbsp;</span></td>
            <td class="titInfo" width="150">会议结束时间</td><td width="300" style="text-align:left;"><span id="endTime">&nbsp;</span>&nbsp;<span id="endTimeQunInfo">&nbsp;</span></td>
        </tr>
        <tr>
            <td class="titInfo" width="150">人数</td><td width="300" style="text-align:left;"><span id="personsNumber">&nbsp;</span>人</td>
            <td class="titInfo" width="150">会议时间</td><td width="300" style="text-align:left;"><span id="activiTime">&nbsp;</span>天</td>
        </tr>
        <tr>
            <td class="titInfo" width="150">会议预算</td><td colspan="3" style="text-align:left;"><span id="applyBudget">&nbsp;</span>元</td>
        </tr>
        <tr>
            <td class="titInfo" width="150">参会人员</td><td colspan="3" style="text-align:left;max-width:750px;"><div id="persons" style="max-width:750px;">&nbsp;</div></td>
        </tr>
        <tr>
            <td class="titInfo" width="150">会议地点</td><td colspan="3" style="text-align:left;"><span id="address">&nbsp;</span></td>
        </tr>
        <tr>
            <td class="titInfo" width="150">会议内容</td><td colspan="3" style="text-align:left;max-width:750px;"><div id="content" style="max-width:750px;min-height:85px;">&nbsp;</div></td>
        </tr>
        <tr>
            <td class="titInfo" width="150">附件</td><td colspan="3" style="text-align:left;"><span id="nmaFileInfo">&nbsp;</span></td>
        </tr>
	</table>
	</br>
</form>
<!--table-->
<table border="0" cellpadding="0" cellspacing="0" class="ctable w100">
    <thead>
    <tr>
        <td width="50%">审批意见人</td>
        <td width="30%">审批意见</td>
        <td width="20%">审批时间</td>
    </tr>
    </thead>
    <tbody></tbody>
</table>
</div>
</body>
</html>
