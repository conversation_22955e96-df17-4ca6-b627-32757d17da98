package com.simbest.boot.hnjjwz.newcolumn.model;

import com.simbest.boot.base.annotations.EntityIdPrefix;
import com.simbest.boot.base.model.LogicModel;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.*;
import org.hibernate.annotations.GenericGenerator;

import javax.persistence.Column;
import javax.persistence.Entity;
import javax.persistence.GeneratedValue;
import javax.persistence.Id;
@Data
@EqualsAndHashCode(callSuper = true)
@NoArgsConstructor
@AllArgsConstructor
@Entity(name = "SYS_NEW_COLUMN_MODEL")
@ApiModel(value = "新版栏目分类")
public class SysNewColumnModel extends LogicModel{
    @Id
    @Column(name = "id", length = 40)
    @GeneratedValue(generator = "snowFlakeId")
    @GenericGenerator(name = "snowFlakeId", strategy = "com.simbest.boot.util.distribution.id.SnowflakeId")
    @EntityIdPrefix(prefix = "SNCM") //主键前缀，此为可选项注解
    private String id;


    @ApiModelProperty(value = "栏目名")
    @Column(length = 255)
    private String columnName;

    @ApiModelProperty(value = "栏目ID")
    @Column(length = 50)
    private String columnId;

    @ApiModelProperty(value = "上级栏目ID")
    @Column(length = 50)
    private String parentColumnId;

    @ApiModelProperty(value = "根栏目ID")
    @Column(length = 50)
    private String rootColumnId;

    @ApiModelProperty(value = "根栏全称")
    @Column(length = 1000)
    private String columnAllName;

    @ApiModelProperty(value = "栏目级别")
    @Column(length = 50)
    private Integer columnLevel;

    @ApiModelProperty(value = "栏目排序")
    @Column(length = 50)
    private Integer columnOrder;

    @ApiModelProperty(value = "栏目是否在下拉选择框显示")
    @Column(length = 50)
    private Boolean isDisplay;

    @ApiModelProperty(value = "是否跳转类型")
    @Column(length = 50)
    private Boolean isLink;

    @ApiModelProperty(value = "跳转地址")
    @Column(length = 400)
    private String linkUrl;

    @ApiModelProperty(value = "预留字段")
    @Column(length = 500)
    private String spare01;

    @ApiModelProperty(value = "预留字段")
    @Column(length = 500)
    private String spare02;


    @ApiModelProperty(value = "预留字段")
    @Column(length = 500)
    private String spare03;
}