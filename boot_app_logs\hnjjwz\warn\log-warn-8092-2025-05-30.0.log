2025-05-30 00:00:00.019 [pool-14-thread-6] WARN  com.simbest.boot.base.repository.CustomDynamicWhere.queryForList Line:50 - 自定义查询SQL输出为：【select * from US_TODO_MODEL t  where t.send_flag = 0 and t.work_flag = 0 and t.enabled =1】
2025-05-30 00:00:00.022 [pool-14-thread-5] WARN  com.simbest.boot.base.repository.CustomDynamicWhere.queryForList Line:50 - 自定义查询SQL输出为：【select * from US_TODO_MODEL t  where t.send_flag = 0 and t.work_flag = 1 and t.enabled =1】
2025-05-30 00:00:00.025 [pool-14-thread-4] WARN  com.simbest.boot.cmcc.a4.task.A4TaskCreateXmlFile.execute Line:76 - >>>>>>>>>>>>>>>>>>>>>>>>>>>>>>>>>>>>>>>>>>>>>>>>>>>>>A4TaskCreateXmlFile:执行定时任务但不是Prd环境【2025-05-30T00:00:00.025】
2025-05-30 00:05:00.012 [pool-14-thread-6] WARN  com.simbest.boot.base.repository.CustomDynamicWhere.queryForList Line:50 - 自定义查询SQL输出为：【select * from US_TODO_MODEL t  where t.send_flag = 0 and t.work_flag = 0 and t.enabled =1】
2025-05-30 00:05:00.015 [pool-14-thread-7] WARN  com.simbest.boot.base.repository.CustomDynamicWhere.queryForList Line:50 - 自定义查询SQL输出为：【select * from US_TODO_MODEL t  where t.send_flag = 0 and t.work_flag = 1 and t.enabled =1】
2025-05-30 00:10:00.024 [pool-14-thread-7] WARN  com.simbest.boot.base.repository.CustomDynamicWhere.queryForList Line:50 - 自定义查询SQL输出为：【select * from US_TODO_MODEL t  where t.send_flag = 0 and t.work_flag = 1 and t.enabled =1】
2025-05-30 00:10:00.024 [pool-14-thread-1] WARN  com.simbest.boot.base.repository.CustomDynamicWhere.queryForList Line:50 - 自定义查询SQL输出为：【select * from US_TODO_MODEL t  where t.send_flag = 0 and t.work_flag = 0 and t.enabled =1】
2025-05-30 00:15:00.004 [pool-14-thread-3] WARN  com.simbest.boot.base.repository.CustomDynamicWhere.queryForList Line:50 - 自定义查询SQL输出为：【select * from US_TODO_MODEL t  where t.send_flag = 0 and t.work_flag = 0 and t.enabled =1】
2025-05-30 00:15:00.004 [pool-14-thread-8] WARN  com.simbest.boot.base.repository.CustomDynamicWhere.queryForList Line:50 - 自定义查询SQL输出为：【select * from US_TODO_MODEL t  where t.send_flag = 0 and t.work_flag = 1 and t.enabled =1】
2025-05-30 00:20:00.016 [pool-14-thread-10] WARN  com.simbest.boot.base.repository.CustomDynamicWhere.queryForList Line:50 - 自定义查询SQL输出为：【select * from US_TODO_MODEL t  where t.send_flag = 0 and t.work_flag = 0 and t.enabled =1】
2025-05-30 00:20:00.017 [pool-14-thread-3] WARN  com.simbest.boot.base.repository.CustomDynamicWhere.queryForList Line:50 - 自定义查询SQL输出为：【select * from US_TODO_MODEL t  where t.send_flag = 0 and t.work_flag = 1 and t.enabled =1】
2025-05-30 00:25:00.006 [pool-14-thread-6] WARN  com.simbest.boot.base.repository.CustomDynamicWhere.queryForList Line:50 - 自定义查询SQL输出为：【select * from US_TODO_MODEL t  where t.send_flag = 0 and t.work_flag = 1 and t.enabled =1】
2025-05-30 00:25:00.006 [pool-14-thread-2] WARN  com.simbest.boot.base.repository.CustomDynamicWhere.queryForList Line:50 - 自定义查询SQL输出为：【select * from US_TODO_MODEL t  where t.send_flag = 0 and t.work_flag = 0 and t.enabled =1】
2025-05-30 00:30:00.011 [pool-14-thread-2] WARN  com.simbest.boot.base.repository.CustomDynamicWhere.queryForList Line:50 - 自定义查询SQL输出为：【select * from US_TODO_MODEL t  where t.send_flag = 0 and t.work_flag = 1 and t.enabled =1】
2025-05-30 00:30:00.026 [pool-14-thread-3] WARN  com.simbest.boot.base.repository.CustomDynamicWhere.queryForList Line:50 - 自定义查询SQL输出为：【select * from US_TODO_MODEL t  where t.send_flag = 0 and t.work_flag = 0 and t.enabled =1】
2025-05-30 00:35:00.016 [pool-14-thread-4] WARN  com.simbest.boot.base.repository.CustomDynamicWhere.queryForList Line:50 - 自定义查询SQL输出为：【select * from US_TODO_MODEL t  where t.send_flag = 0 and t.work_flag = 1 and t.enabled =1】
2025-05-30 00:35:00.018 [pool-14-thread-2] WARN  com.simbest.boot.base.repository.CustomDynamicWhere.queryForList Line:50 - 自定义查询SQL输出为：【select * from US_TODO_MODEL t  where t.send_flag = 0 and t.work_flag = 0 and t.enabled =1】
2025-05-30 00:40:00.013 [pool-14-thread-6] WARN  com.simbest.boot.base.repository.CustomDynamicWhere.queryForList Line:50 - 自定义查询SQL输出为：【select * from US_TODO_MODEL t  where t.send_flag = 0 and t.work_flag = 1 and t.enabled =1】
2025-05-30 00:40:00.013 [pool-14-thread-1] WARN  com.simbest.boot.base.repository.CustomDynamicWhere.queryForList Line:50 - 自定义查询SQL输出为：【select * from US_TODO_MODEL t  where t.send_flag = 0 and t.work_flag = 0 and t.enabled =1】
2025-05-30 00:45:00.013 [pool-14-thread-5] WARN  com.simbest.boot.base.repository.CustomDynamicWhere.queryForList Line:50 - 自定义查询SQL输出为：【select * from US_TODO_MODEL t  where t.send_flag = 0 and t.work_flag = 1 and t.enabled =1】
2025-05-30 00:45:00.013 [pool-14-thread-4] WARN  com.simbest.boot.base.repository.CustomDynamicWhere.queryForList Line:50 - 自定义查询SQL输出为：【select * from US_TODO_MODEL t  where t.send_flag = 0 and t.work_flag = 0 and t.enabled =1】
2025-05-30 00:50:00.011 [pool-14-thread-6] WARN  com.simbest.boot.base.repository.CustomDynamicWhere.queryForList Line:50 - 自定义查询SQL输出为：【select * from US_TODO_MODEL t  where t.send_flag = 0 and t.work_flag = 1 and t.enabled =1】
2025-05-30 00:50:00.011 [pool-14-thread-5] WARN  com.simbest.boot.base.repository.CustomDynamicWhere.queryForList Line:50 - 自定义查询SQL输出为：【select * from US_TODO_MODEL t  where t.send_flag = 0 and t.work_flag = 0 and t.enabled =1】
2025-05-30 00:55:00.017 [pool-14-thread-4] WARN  com.simbest.boot.base.repository.CustomDynamicWhere.queryForList Line:50 - 自定义查询SQL输出为：【select * from US_TODO_MODEL t  where t.send_flag = 0 and t.work_flag = 0 and t.enabled =1】
2025-05-30 00:55:00.019 [pool-14-thread-1] WARN  com.simbest.boot.base.repository.CustomDynamicWhere.queryForList Line:50 - 自定义查询SQL输出为：【select * from US_TODO_MODEL t  where t.send_flag = 0 and t.work_flag = 1 and t.enabled =1】
2025-05-30 01:00:00.010 [pool-14-thread-1] WARN  com.simbest.boot.base.repository.CustomDynamicWhere.queryForList Line:50 - 自定义查询SQL输出为：【select * from US_TODO_MODEL t  where t.send_flag = 0 and t.work_flag = 0 and t.enabled =1】
2025-05-30 01:00:00.013 [pool-14-thread-4] WARN  com.simbest.boot.base.repository.CustomDynamicWhere.queryForList Line:50 - 自定义查询SQL输出为：【select * from US_TODO_MODEL t  where t.send_flag = 0 and t.work_flag = 1 and t.enabled =1】
2025-05-30 01:00:00.021 [pool-14-thread-3] WARN  com.simbest.boot.cmcc.a4.task.A4TaskCreateXmlFile.execute Line:76 - >>>>>>>>>>>>>>>>>>>>>>>>>>>>>>>>>>>>>>>>>>>>>>>>>>>>>A4TaskCreateXmlFile:执行定时任务但不是Prd环境【2025-05-30T01:00:00.021】
2025-05-30 01:05:00.020 [pool-14-thread-1] WARN  com.simbest.boot.base.repository.CustomDynamicWhere.queryForList Line:50 - 自定义查询SQL输出为：【select * from US_TODO_MODEL t  where t.send_flag = 0 and t.work_flag = 1 and t.enabled =1】
2025-05-30 01:05:00.020 [pool-14-thread-3] WARN  com.simbest.boot.base.repository.CustomDynamicWhere.queryForList Line:50 - 自定义查询SQL输出为：【select * from US_TODO_MODEL t  where t.send_flag = 0 and t.work_flag = 0 and t.enabled =1】
2025-05-30 01:10:00.018 [pool-14-thread-8] WARN  com.simbest.boot.base.repository.CustomDynamicWhere.queryForList Line:50 - 自定义查询SQL输出为：【select * from US_TODO_MODEL t  where t.send_flag = 0 and t.work_flag = 1 and t.enabled =1】
2025-05-30 01:10:00.019 [pool-14-thread-2] WARN  com.simbest.boot.base.repository.CustomDynamicWhere.queryForList Line:50 - 自定义查询SQL输出为：【select * from US_TODO_MODEL t  where t.send_flag = 0 and t.work_flag = 0 and t.enabled =1】
2025-05-30 01:15:00.020 [pool-14-thread-1] WARN  com.simbest.boot.base.repository.CustomDynamicWhere.queryForList Line:50 - 自定义查询SQL输出为：【select * from US_TODO_MODEL t  where t.send_flag = 0 and t.work_flag = 1 and t.enabled =1】
2025-05-30 01:15:00.023 [pool-14-thread-10] WARN  com.simbest.boot.base.repository.CustomDynamicWhere.queryForList Line:50 - 自定义查询SQL输出为：【select * from US_TODO_MODEL t  where t.send_flag = 0 and t.work_flag = 0 and t.enabled =1】
2025-05-30 01:20:00.014 [pool-14-thread-6] WARN  com.simbest.boot.base.repository.CustomDynamicWhere.queryForList Line:50 - 自定义查询SQL输出为：【select * from US_TODO_MODEL t  where t.send_flag = 0 and t.work_flag = 0 and t.enabled =1】
2025-05-30 01:20:00.018 [pool-14-thread-7] WARN  com.simbest.boot.base.repository.CustomDynamicWhere.queryForList Line:50 - 自定义查询SQL输出为：【select * from US_TODO_MODEL t  where t.send_flag = 0 and t.work_flag = 1 and t.enabled =1】
2025-05-30 01:25:00.015 [pool-14-thread-7] WARN  com.simbest.boot.base.repository.CustomDynamicWhere.queryForList Line:50 - 自定义查询SQL输出为：【select * from US_TODO_MODEL t  where t.send_flag = 0 and t.work_flag = 0 and t.enabled =1】
2025-05-30 01:25:00.015 [pool-14-thread-4] WARN  com.simbest.boot.base.repository.CustomDynamicWhere.queryForList Line:50 - 自定义查询SQL输出为：【select * from US_TODO_MODEL t  where t.send_flag = 0 and t.work_flag = 1 and t.enabled =1】
2025-05-30 01:30:00.009 [pool-14-thread-6] WARN  com.simbest.boot.base.repository.CustomDynamicWhere.queryForList Line:50 - 自定义查询SQL输出为：【select * from US_TODO_MODEL t  where t.send_flag = 0 and t.work_flag = 0 and t.enabled =1】
2025-05-30 01:30:00.009 [pool-14-thread-9] WARN  com.simbest.boot.base.repository.CustomDynamicWhere.queryForList Line:50 - 自定义查询SQL输出为：【select * from US_TODO_MODEL t  where t.send_flag = 0 and t.work_flag = 1 and t.enabled =1】
2025-05-30 01:35:00.016 [pool-14-thread-3] WARN  com.simbest.boot.base.repository.CustomDynamicWhere.queryForList Line:50 - 自定义查询SQL输出为：【select * from US_TODO_MODEL t  where t.send_flag = 0 and t.work_flag = 0 and t.enabled =1】
2025-05-30 01:35:00.016 [pool-14-thread-9] WARN  com.simbest.boot.base.repository.CustomDynamicWhere.queryForList Line:50 - 自定义查询SQL输出为：【select * from US_TODO_MODEL t  where t.send_flag = 0 and t.work_flag = 1 and t.enabled =1】
2025-05-30 01:40:00.015 [pool-14-thread-2] WARN  com.simbest.boot.base.repository.CustomDynamicWhere.queryForList Line:50 - 自定义查询SQL输出为：【select * from US_TODO_MODEL t  where t.send_flag = 0 and t.work_flag = 1 and t.enabled =1】
2025-05-30 01:40:00.020 [pool-14-thread-4] WARN  com.simbest.boot.base.repository.CustomDynamicWhere.queryForList Line:50 - 自定义查询SQL输出为：【select * from US_TODO_MODEL t  where t.send_flag = 0 and t.work_flag = 0 and t.enabled =1】
2025-05-30 01:45:00.010 [pool-14-thread-8] WARN  com.simbest.boot.base.repository.CustomDynamicWhere.queryForList Line:50 - 自定义查询SQL输出为：【select * from US_TODO_MODEL t  where t.send_flag = 0 and t.work_flag = 0 and t.enabled =1】
2025-05-30 01:45:00.012 [pool-14-thread-2] WARN  com.simbest.boot.base.repository.CustomDynamicWhere.queryForList Line:50 - 自定义查询SQL输出为：【select * from US_TODO_MODEL t  where t.send_flag = 0 and t.work_flag = 1 and t.enabled =1】
2025-05-30 01:50:00.014 [pool-14-thread-5] WARN  com.simbest.boot.base.repository.CustomDynamicWhere.queryForList Line:50 - 自定义查询SQL输出为：【select * from US_TODO_MODEL t  where t.send_flag = 0 and t.work_flag = 1 and t.enabled =1】
2025-05-30 01:50:00.015 [pool-14-thread-6] WARN  com.simbest.boot.base.repository.CustomDynamicWhere.queryForList Line:50 - 自定义查询SQL输出为：【select * from US_TODO_MODEL t  where t.send_flag = 0 and t.work_flag = 0 and t.enabled =1】
2025-05-30 01:55:00.013 [pool-14-thread-7] WARN  com.simbest.boot.base.repository.CustomDynamicWhere.queryForList Line:50 - 自定义查询SQL输出为：【select * from US_TODO_MODEL t  where t.send_flag = 0 and t.work_flag = 0 and t.enabled =1】
2025-05-30 01:55:00.014 [pool-14-thread-5] WARN  com.simbest.boot.base.repository.CustomDynamicWhere.queryForList Line:50 - 自定义查询SQL输出为：【select * from US_TODO_MODEL t  where t.send_flag = 0 and t.work_flag = 1 and t.enabled =1】
2025-05-30 02:00:00.013 [pool-14-thread-7] WARN  com.simbest.boot.base.repository.CustomDynamicWhere.queryForList Line:50 - 自定义查询SQL输出为：【select * from US_TODO_MODEL t  where t.send_flag = 0 and t.work_flag = 1 and t.enabled =1】
2025-05-30 02:00:00.014 [pool-14-thread-10] WARN  com.simbest.boot.base.repository.CustomDynamicWhere.queryForList Line:50 - 自定义查询SQL输出为：【select * from US_TODO_MODEL t  where t.send_flag = 0 and t.work_flag = 0 and t.enabled =1】
2025-05-30 02:00:00.028 [pool-14-thread-1] WARN  com.simbest.boot.cmcc.a4.task.A4TaskCreateXmlFile.execute Line:76 - >>>>>>>>>>>>>>>>>>>>>>>>>>>>>>>>>>>>>>>>>>>>>>>>>>>>>A4TaskCreateXmlFile:执行定时任务但不是Prd环境【2025-05-30T02:00:00.028】
2025-05-30 02:05:00.012 [pool-14-thread-8] WARN  com.simbest.boot.base.repository.CustomDynamicWhere.queryForList Line:50 - 自定义查询SQL输出为：【select * from US_TODO_MODEL t  where t.send_flag = 0 and t.work_flag = 1 and t.enabled =1】
2025-05-30 02:05:00.014 [pool-14-thread-7] WARN  com.simbest.boot.base.repository.CustomDynamicWhere.queryForList Line:50 - 自定义查询SQL输出为：【select * from US_TODO_MODEL t  where t.send_flag = 0 and t.work_flag = 0 and t.enabled =1】
2025-05-30 02:10:00.018 [pool-14-thread-10] WARN  com.simbest.boot.base.repository.CustomDynamicWhere.queryForList Line:50 - 自定义查询SQL输出为：【select * from US_TODO_MODEL t  where t.send_flag = 0 and t.work_flag = 1 and t.enabled =1】
2025-05-30 02:10:00.019 [pool-14-thread-3] WARN  com.simbest.boot.base.repository.CustomDynamicWhere.queryForList Line:50 - 自定义查询SQL输出为：【select * from US_TODO_MODEL t  where t.send_flag = 0 and t.work_flag = 0 and t.enabled =1】
2025-05-30 02:15:00.004 [pool-14-thread-4] WARN  com.simbest.boot.base.repository.CustomDynamicWhere.queryForList Line:50 - 自定义查询SQL输出为：【select * from US_TODO_MODEL t  where t.send_flag = 0 and t.work_flag = 1 and t.enabled =1】
2025-05-30 02:15:00.006 [pool-14-thread-8] WARN  com.simbest.boot.base.repository.CustomDynamicWhere.queryForList Line:50 - 自定义查询SQL输出为：【select * from US_TODO_MODEL t  where t.send_flag = 0 and t.work_flag = 0 and t.enabled =1】
2025-05-30 02:20:00.006 [pool-14-thread-5] WARN  com.simbest.boot.base.repository.CustomDynamicWhere.queryForList Line:50 - 自定义查询SQL输出为：【select * from US_TODO_MODEL t  where t.send_flag = 0 and t.work_flag = 0 and t.enabled =1】
2025-05-30 02:20:00.006 [pool-14-thread-8] WARN  com.simbest.boot.base.repository.CustomDynamicWhere.queryForList Line:50 - 自定义查询SQL输出为：【select * from US_TODO_MODEL t  where t.send_flag = 0 and t.work_flag = 1 and t.enabled =1】
2025-05-30 02:25:00.013 [pool-14-thread-2] WARN  com.simbest.boot.base.repository.CustomDynamicWhere.queryForList Line:50 - 自定义查询SQL输出为：【select * from US_TODO_MODEL t  where t.send_flag = 0 and t.work_flag = 1 and t.enabled =1】
2025-05-30 02:25:00.013 [pool-14-thread-9] WARN  com.simbest.boot.base.repository.CustomDynamicWhere.queryForList Line:50 - 自定义查询SQL输出为：【select * from US_TODO_MODEL t  where t.send_flag = 0 and t.work_flag = 0 and t.enabled =1】
2025-05-30 02:30:00.019 [pool-14-thread-4] WARN  com.simbest.boot.base.repository.CustomDynamicWhere.queryForList Line:50 - 自定义查询SQL输出为：【select * from US_TODO_MODEL t  where t.send_flag = 0 and t.work_flag = 1 and t.enabled =1】
2025-05-30 02:30:00.019 [pool-14-thread-3] WARN  com.simbest.boot.base.repository.CustomDynamicWhere.queryForList Line:50 - 自定义查询SQL输出为：【select * from US_TODO_MODEL t  where t.send_flag = 0 and t.work_flag = 0 and t.enabled =1】
2025-05-30 02:35:00.006 [pool-14-thread-1] WARN  com.simbest.boot.base.repository.CustomDynamicWhere.queryForList Line:50 - 自定义查询SQL输出为：【select * from US_TODO_MODEL t  where t.send_flag = 0 and t.work_flag = 0 and t.enabled =1】
2025-05-30 02:35:00.016 [pool-14-thread-4] WARN  com.simbest.boot.base.repository.CustomDynamicWhere.queryForList Line:50 - 自定义查询SQL输出为：【select * from US_TODO_MODEL t  where t.send_flag = 0 and t.work_flag = 1 and t.enabled =1】
2025-05-30 02:40:00.020 [pool-14-thread-1] WARN  com.simbest.boot.base.repository.CustomDynamicWhere.queryForList Line:50 - 自定义查询SQL输出为：【select * from US_TODO_MODEL t  where t.send_flag = 0 and t.work_flag = 1 and t.enabled =1】
2025-05-30 02:40:00.020 [pool-14-thread-7] WARN  com.simbest.boot.base.repository.CustomDynamicWhere.queryForList Line:50 - 自定义查询SQL输出为：【select * from US_TODO_MODEL t  where t.send_flag = 0 and t.work_flag = 0 and t.enabled =1】
2025-05-30 02:45:00.014 [pool-14-thread-3] WARN  com.simbest.boot.base.repository.CustomDynamicWhere.queryForList Line:50 - 自定义查询SQL输出为：【select * from US_TODO_MODEL t  where t.send_flag = 0 and t.work_flag = 1 and t.enabled =1】
2025-05-30 02:45:00.015 [pool-14-thread-4] WARN  com.simbest.boot.base.repository.CustomDynamicWhere.queryForList Line:50 - 自定义查询SQL输出为：【select * from US_TODO_MODEL t  where t.send_flag = 0 and t.work_flag = 0 and t.enabled =1】
2025-05-30 02:50:00.009 [pool-14-thread-6] WARN  com.simbest.boot.base.repository.CustomDynamicWhere.queryForList Line:50 - 自定义查询SQL输出为：【select * from US_TODO_MODEL t  where t.send_flag = 0 and t.work_flag = 0 and t.enabled =1】
2025-05-30 02:50:00.009 [pool-14-thread-7] WARN  com.simbest.boot.base.repository.CustomDynamicWhere.queryForList Line:50 - 自定义查询SQL输出为：【select * from US_TODO_MODEL t  where t.send_flag = 0 and t.work_flag = 1 and t.enabled =1】
2025-05-30 02:55:00.012 [pool-14-thread-8] WARN  com.simbest.boot.base.repository.CustomDynamicWhere.queryForList Line:50 - 自定义查询SQL输出为：【select * from US_TODO_MODEL t  where t.send_flag = 0 and t.work_flag = 0 and t.enabled =1】
2025-05-30 02:55:00.016 [pool-14-thread-7] WARN  com.simbest.boot.base.repository.CustomDynamicWhere.queryForList Line:50 - 自定义查询SQL输出为：【select * from US_TODO_MODEL t  where t.send_flag = 0 and t.work_flag = 1 and t.enabled =1】
2025-05-30 03:00:00.010 [pool-14-thread-4] WARN  com.simbest.boot.base.repository.CustomDynamicWhere.queryForList Line:50 - 自定义查询SQL输出为：【select * from US_TODO_MODEL t  where t.send_flag = 0 and t.work_flag = 0 and t.enabled =1】
2025-05-30 03:00:00.010 [pool-14-thread-5] WARN  com.simbest.boot.base.repository.CustomDynamicWhere.queryForList Line:50 - 自定义查询SQL输出为：【select * from US_TODO_MODEL t  where t.send_flag = 0 and t.work_flag = 1 and t.enabled =1】
2025-05-30 03:00:00.010 [pool-14-thread-2] WARN  com.simbest.boot.cmcc.a4.task.A4TaskCreateXmlFile.execute Line:76 - >>>>>>>>>>>>>>>>>>>>>>>>>>>>>>>>>>>>>>>>>>>>>>>>>>>>>A4TaskCreateXmlFile:执行定时任务但不是Prd环境【2025-05-30T03:00:00.010】
2025-05-30 03:05:00.009 [pool-14-thread-4] WARN  com.simbest.boot.base.repository.CustomDynamicWhere.queryForList Line:50 - 自定义查询SQL输出为：【select * from US_TODO_MODEL t  where t.send_flag = 0 and t.work_flag = 1 and t.enabled =1】
2025-05-30 03:05:00.012 [pool-14-thread-3] WARN  com.simbest.boot.base.repository.CustomDynamicWhere.queryForList Line:50 - 自定义查询SQL输出为：【select * from US_TODO_MODEL t  where t.send_flag = 0 and t.work_flag = 0 and t.enabled =1】
2025-05-30 03:10:00.011 [pool-14-thread-6] WARN  com.simbest.boot.base.repository.CustomDynamicWhere.queryForList Line:50 - 自定义查询SQL输出为：【select * from US_TODO_MODEL t  where t.send_flag = 0 and t.work_flag = 0 and t.enabled =1】
2025-05-30 03:10:00.014 [pool-14-thread-7] WARN  com.simbest.boot.base.repository.CustomDynamicWhere.queryForList Line:50 - 自定义查询SQL输出为：【select * from US_TODO_MODEL t  where t.send_flag = 0 and t.work_flag = 1 and t.enabled =1】
2025-05-30 03:15:00.016 [pool-14-thread-10] WARN  com.simbest.boot.base.repository.CustomDynamicWhere.queryForList Line:50 - 自定义查询SQL输出为：【select * from US_TODO_MODEL t  where t.send_flag = 0 and t.work_flag = 1 and t.enabled =1】
2025-05-30 03:15:00.016 [pool-14-thread-1] WARN  com.simbest.boot.base.repository.CustomDynamicWhere.queryForList Line:50 - 自定义查询SQL输出为：【select * from US_TODO_MODEL t  where t.send_flag = 0 and t.work_flag = 0 and t.enabled =1】
2025-05-30 03:20:00.006 [pool-14-thread-9] WARN  com.simbest.boot.base.repository.CustomDynamicWhere.queryForList Line:50 - 自定义查询SQL输出为：【select * from US_TODO_MODEL t  where t.send_flag = 0 and t.work_flag = 0 and t.enabled =1】
2025-05-30 03:20:00.007 [pool-14-thread-3] WARN  com.simbest.boot.base.repository.CustomDynamicWhere.queryForList Line:50 - 自定义查询SQL输出为：【select * from US_TODO_MODEL t  where t.send_flag = 0 and t.work_flag = 1 and t.enabled =1】
2025-05-30 03:25:00.007 [pool-14-thread-10] WARN  com.simbest.boot.base.repository.CustomDynamicWhere.queryForList Line:50 - 自定义查询SQL输出为：【select * from US_TODO_MODEL t  where t.send_flag = 0 and t.work_flag = 1 and t.enabled =1】
2025-05-30 03:25:00.014 [pool-14-thread-4] WARN  com.simbest.boot.base.repository.CustomDynamicWhere.queryForList Line:50 - 自定义查询SQL输出为：【select * from US_TODO_MODEL t  where t.send_flag = 0 and t.work_flag = 0 and t.enabled =1】
2025-05-30 03:30:00.016 [pool-14-thread-6] WARN  com.simbest.boot.base.repository.CustomDynamicWhere.queryForList Line:50 - 自定义查询SQL输出为：【select * from US_TODO_MODEL t  where t.send_flag = 0 and t.work_flag = 1 and t.enabled =1】
2025-05-30 03:30:00.018 [pool-14-thread-4] WARN  com.simbest.boot.base.repository.CustomDynamicWhere.queryForList Line:50 - 自定义查询SQL输出为：【select * from US_TODO_MODEL t  where t.send_flag = 0 and t.work_flag = 0 and t.enabled =1】
2025-05-30 03:35:00.007 [pool-14-thread-1] WARN  com.simbest.boot.base.repository.CustomDynamicWhere.queryForList Line:50 - 自定义查询SQL输出为：【select * from US_TODO_MODEL t  where t.send_flag = 0 and t.work_flag = 0 and t.enabled =1】
2025-05-30 03:35:00.007 [pool-14-thread-7] WARN  com.simbest.boot.base.repository.CustomDynamicWhere.queryForList Line:50 - 自定义查询SQL输出为：【select * from US_TODO_MODEL t  where t.send_flag = 0 and t.work_flag = 1 and t.enabled =1】
2025-05-30 03:40:00.010 [pool-14-thread-9] WARN  com.simbest.boot.base.repository.CustomDynamicWhere.queryForList Line:50 - 自定义查询SQL输出为：【select * from US_TODO_MODEL t  where t.send_flag = 0 and t.work_flag = 0 and t.enabled =1】
2025-05-30 03:40:00.012 [pool-14-thread-4] WARN  com.simbest.boot.base.repository.CustomDynamicWhere.queryForList Line:50 - 自定义查询SQL输出为：【select * from US_TODO_MODEL t  where t.send_flag = 0 and t.work_flag = 1 and t.enabled =1】
2025-05-30 03:45:00.016 [pool-14-thread-5] WARN  com.simbest.boot.base.repository.CustomDynamicWhere.queryForList Line:50 - 自定义查询SQL输出为：【select * from US_TODO_MODEL t  where t.send_flag = 0 and t.work_flag = 1 and t.enabled =1】
2025-05-30 03:45:00.023 [pool-14-thread-9] WARN  com.simbest.boot.base.repository.CustomDynamicWhere.queryForList Line:50 - 自定义查询SQL输出为：【select * from US_TODO_MODEL t  where t.send_flag = 0 and t.work_flag = 0 and t.enabled =1】
2025-05-30 03:50:00.015 [pool-14-thread-4] WARN  com.simbest.boot.base.repository.CustomDynamicWhere.queryForList Line:50 - 自定义查询SQL输出为：【select * from US_TODO_MODEL t  where t.send_flag = 0 and t.work_flag = 0 and t.enabled =1】
2025-05-30 03:50:00.016 [pool-14-thread-8] WARN  com.simbest.boot.base.repository.CustomDynamicWhere.queryForList Line:50 - 自定义查询SQL输出为：【select * from US_TODO_MODEL t  where t.send_flag = 0 and t.work_flag = 1 and t.enabled =1】
2025-05-30 03:55:00.016 [pool-14-thread-6] WARN  com.simbest.boot.base.repository.CustomDynamicWhere.queryForList Line:50 - 自定义查询SQL输出为：【select * from US_TODO_MODEL t  where t.send_flag = 0 and t.work_flag = 0 and t.enabled =1】
2025-05-30 03:55:00.016 [pool-14-thread-4] WARN  com.simbest.boot.base.repository.CustomDynamicWhere.queryForList Line:50 - 自定义查询SQL输出为：【select * from US_TODO_MODEL t  where t.send_flag = 0 and t.work_flag = 1 and t.enabled =1】
2025-05-30 04:00:00.012 [pool-14-thread-9] WARN  com.simbest.boot.base.repository.CustomDynamicWhere.queryForList Line:50 - 自定义查询SQL输出为：【select * from US_TODO_MODEL t  where t.send_flag = 0 and t.work_flag = 1 and t.enabled =1】
2025-05-30 04:00:00.013 [pool-14-thread-5] WARN  com.simbest.boot.cmcc.a4.task.A4TaskCreateXmlFile.execute Line:76 - >>>>>>>>>>>>>>>>>>>>>>>>>>>>>>>>>>>>>>>>>>>>>>>>>>>>>A4TaskCreateXmlFile:执行定时任务但不是Prd环境【2025-05-30T04:00:00.013】
2025-05-30 04:05:00.014 [pool-14-thread-6] WARN  com.simbest.boot.base.repository.CustomDynamicWhere.queryForList Line:50 - 自定义查询SQL输出为：【select * from US_TODO_MODEL t  where t.send_flag = 0 and t.work_flag = 1 and t.enabled =1】
2025-05-30 04:10:00.016 [pool-14-thread-1] WARN  com.simbest.boot.base.repository.CustomDynamicWhere.queryForList Line:50 - 自定义查询SQL输出为：【select * from US_TODO_MODEL t  where t.send_flag = 0 and t.work_flag = 1 and t.enabled =1】
2025-05-30 04:15:00.011 [pool-14-thread-1] WARN  com.simbest.boot.base.repository.CustomDynamicWhere.queryForList Line:50 - 自定义查询SQL输出为：【select * from US_TODO_MODEL t  where t.send_flag = 0 and t.work_flag = 1 and t.enabled =1】
2025-05-30 04:20:00.018 [pool-14-thread-4] WARN  com.simbest.boot.base.repository.CustomDynamicWhere.queryForList Line:50 - 自定义查询SQL输出为：【select * from US_TODO_MODEL t  where t.send_flag = 0 and t.work_flag = 0 and t.enabled =1】
2025-05-30 04:20:00.018 [pool-14-thread-9] WARN  com.simbest.boot.base.repository.CustomDynamicWhere.queryForList Line:50 - 自定义查询SQL输出为：【select * from US_TODO_MODEL t  where t.send_flag = 0 and t.work_flag = 1 and t.enabled =1】
2025-05-30 04:25:00.011 [pool-14-thread-10] WARN  com.simbest.boot.base.repository.CustomDynamicWhere.queryForList Line:50 - 自定义查询SQL输出为：【select * from US_TODO_MODEL t  where t.send_flag = 0 and t.work_flag = 0 and t.enabled =1】
2025-05-30 04:25:00.012 [pool-14-thread-4] WARN  com.simbest.boot.base.repository.CustomDynamicWhere.queryForList Line:50 - 自定义查询SQL输出为：【select * from US_TODO_MODEL t  where t.send_flag = 0 and t.work_flag = 1 and t.enabled =1】
2025-05-30 04:30:00.006 [pool-14-thread-4] WARN  com.simbest.boot.base.repository.CustomDynamicWhere.queryForList Line:50 - 自定义查询SQL输出为：【select * from US_TODO_MODEL t  where t.send_flag = 0 and t.work_flag = 0 and t.enabled =1】
2025-05-30 04:30:00.007 [pool-14-thread-6] WARN  com.simbest.boot.base.repository.CustomDynamicWhere.queryForList Line:50 - 自定义查询SQL输出为：【select * from US_TODO_MODEL t  where t.send_flag = 0 and t.work_flag = 1 and t.enabled =1】
2025-05-30 04:35:00.005 [pool-14-thread-7] WARN  com.simbest.boot.base.repository.CustomDynamicWhere.queryForList Line:50 - 自定义查询SQL输出为：【select * from US_TODO_MODEL t  where t.send_flag = 0 and t.work_flag = 1 and t.enabled =1】
2025-05-30 04:35:00.006 [pool-14-thread-10] WARN  com.simbest.boot.base.repository.CustomDynamicWhere.queryForList Line:50 - 自定义查询SQL输出为：【select * from US_TODO_MODEL t  where t.send_flag = 0 and t.work_flag = 0 and t.enabled =1】
2025-05-30 04:40:00.009 [pool-14-thread-4] WARN  com.simbest.boot.base.repository.CustomDynamicWhere.queryForList Line:50 - 自定义查询SQL输出为：【select * from US_TODO_MODEL t  where t.send_flag = 0 and t.work_flag = 0 and t.enabled =1】
2025-05-30 04:40:00.009 [pool-14-thread-9] WARN  com.simbest.boot.base.repository.CustomDynamicWhere.queryForList Line:50 - 自定义查询SQL输出为：【select * from US_TODO_MODEL t  where t.send_flag = 0 and t.work_flag = 1 and t.enabled =1】
2025-05-30 04:45:00.016 [pool-14-thread-6] WARN  com.simbest.boot.base.repository.CustomDynamicWhere.queryForList Line:50 - 自定义查询SQL输出为：【select * from US_TODO_MODEL t  where t.send_flag = 0 and t.work_flag = 0 and t.enabled =1】
2025-05-30 04:45:00.018 [pool-14-thread-5] WARN  com.simbest.boot.base.repository.CustomDynamicWhere.queryForList Line:50 - 自定义查询SQL输出为：【select * from US_TODO_MODEL t  where t.send_flag = 0 and t.work_flag = 1 and t.enabled =1】
2025-05-30 04:50:00.021 [pool-14-thread-4] WARN  com.simbest.boot.base.repository.CustomDynamicWhere.queryForList Line:50 - 自定义查询SQL输出为：【select * from US_TODO_MODEL t  where t.send_flag = 0 and t.work_flag = 1 and t.enabled =1】
2025-05-30 04:50:00.023 [pool-14-thread-2] WARN  com.simbest.boot.base.repository.CustomDynamicWhere.queryForList Line:50 - 自定义查询SQL输出为：【select * from US_TODO_MODEL t  where t.send_flag = 0 and t.work_flag = 0 and t.enabled =1】
2025-05-30 04:55:00.011 [pool-14-thread-7] WARN  com.simbest.boot.base.repository.CustomDynamicWhere.queryForList Line:50 - 自定义查询SQL输出为：【select * from US_TODO_MODEL t  where t.send_flag = 0 and t.work_flag = 1 and t.enabled =1】
2025-05-30 04:55:00.012 [pool-14-thread-10] WARN  com.simbest.boot.base.repository.CustomDynamicWhere.queryForList Line:50 - 自定义查询SQL输出为：【select * from US_TODO_MODEL t  where t.send_flag = 0 and t.work_flag = 0 and t.enabled =1】
2025-05-30 05:00:00.014 [pool-14-thread-7] WARN  com.simbest.boot.base.repository.CustomDynamicWhere.queryForList Line:50 - 自定义查询SQL输出为：【select * from US_TODO_MODEL t  where t.send_flag = 0 and t.work_flag = 1 and t.enabled =1】
2025-05-30 05:00:00.015 [pool-14-thread-2] WARN  com.simbest.boot.base.repository.CustomDynamicWhere.queryForList Line:50 - 自定义查询SQL输出为：【select * from US_TODO_MODEL t  where t.send_flag = 0 and t.work_flag = 0 and t.enabled =1】
2025-05-30 05:00:00.025 [pool-14-thread-8] WARN  com.simbest.boot.cmcc.a4.task.A4TaskCreateXmlFile.execute Line:76 - >>>>>>>>>>>>>>>>>>>>>>>>>>>>>>>>>>>>>>>>>>>>>>>>>>>>>A4TaskCreateXmlFile:执行定时任务但不是Prd环境【2025-05-30T05:00:00.025】
2025-05-30 05:05:00.019 [pool-14-thread-3] WARN  com.simbest.boot.base.repository.CustomDynamicWhere.queryForList Line:50 - 自定义查询SQL输出为：【select * from US_TODO_MODEL t  where t.send_flag = 0 and t.work_flag = 1 and t.enabled =1】
2025-05-30 05:05:00.020 [pool-14-thread-6] WARN  com.simbest.boot.base.repository.CustomDynamicWhere.queryForList Line:50 - 自定义查询SQL输出为：【select * from US_TODO_MODEL t  where t.send_flag = 0 and t.work_flag = 0 and t.enabled =1】
2025-05-30 05:10:00.010 [pool-14-thread-8] WARN  com.simbest.boot.base.repository.CustomDynamicWhere.queryForList Line:50 - 自定义查询SQL输出为：【select * from US_TODO_MODEL t  where t.send_flag = 0 and t.work_flag = 1 and t.enabled =1】
2025-05-30 05:10:00.010 [pool-14-thread-1] WARN  com.simbest.boot.base.repository.CustomDynamicWhere.queryForList Line:50 - 自定义查询SQL输出为：【select * from US_TODO_MODEL t  where t.send_flag = 0 and t.work_flag = 0 and t.enabled =1】
2025-05-30 05:15:00.009 [pool-14-thread-10] WARN  com.simbest.boot.base.repository.CustomDynamicWhere.queryForList Line:50 - 自定义查询SQL输出为：【select * from US_TODO_MODEL t  where t.send_flag = 0 and t.work_flag = 1 and t.enabled =1】
2025-05-30 05:15:00.009 [pool-14-thread-4] WARN  com.simbest.boot.base.repository.CustomDynamicWhere.queryForList Line:50 - 自定义查询SQL输出为：【select * from US_TODO_MODEL t  where t.send_flag = 0 and t.work_flag = 0 and t.enabled =1】
2025-05-30 05:20:00.014 [pool-14-thread-1] WARN  com.simbest.boot.base.repository.CustomDynamicWhere.queryForList Line:50 - 自定义查询SQL输出为：【select * from US_TODO_MODEL t  where t.send_flag = 0 and t.work_flag = 1 and t.enabled =1】
2025-05-30 05:20:00.015 [pool-14-thread-8] WARN  com.simbest.boot.base.repository.CustomDynamicWhere.queryForList Line:50 - 自定义查询SQL输出为：【select * from US_TODO_MODEL t  where t.send_flag = 0 and t.work_flag = 0 and t.enabled =1】
2025-05-30 05:25:00.012 [pool-14-thread-1] WARN  com.simbest.boot.base.repository.CustomDynamicWhere.queryForList Line:50 - 自定义查询SQL输出为：【select * from US_TODO_MODEL t  where t.send_flag = 0 and t.work_flag = 0 and t.enabled =1】
2025-05-30 05:25:00.013 [pool-14-thread-3] WARN  com.simbest.boot.base.repository.CustomDynamicWhere.queryForList Line:50 - 自定义查询SQL输出为：【select * from US_TODO_MODEL t  where t.send_flag = 0 and t.work_flag = 1 and t.enabled =1】
2025-05-30 05:30:00.008 [pool-14-thread-10] WARN  com.simbest.boot.base.repository.CustomDynamicWhere.queryForList Line:50 - 自定义查询SQL输出为：【select * from US_TODO_MODEL t  where t.send_flag = 0 and t.work_flag = 0 and t.enabled =1】
2025-05-30 05:30:00.009 [pool-14-thread-5] WARN  com.simbest.boot.base.repository.CustomDynamicWhere.queryForList Line:50 - 自定义查询SQL输出为：【select * from US_TODO_MODEL t  where t.send_flag = 0 and t.work_flag = 1 and t.enabled =1】
2025-05-30 05:35:00.017 [pool-14-thread-6] WARN  com.simbest.boot.base.repository.CustomDynamicWhere.queryForList Line:50 - 自定义查询SQL输出为：【select * from US_TODO_MODEL t  where t.send_flag = 0 and t.work_flag = 0 and t.enabled =1】
2025-05-30 05:35:00.018 [pool-14-thread-7] WARN  com.simbest.boot.base.repository.CustomDynamicWhere.queryForList Line:50 - 自定义查询SQL输出为：【select * from US_TODO_MODEL t  where t.send_flag = 0 and t.work_flag = 1 and t.enabled =1】
2025-05-30 05:40:00.011 [pool-14-thread-4] WARN  com.simbest.boot.base.repository.CustomDynamicWhere.queryForList Line:50 - 自定义查询SQL输出为：【select * from US_TODO_MODEL t  where t.send_flag = 0 and t.work_flag = 1 and t.enabled =1】
2025-05-30 05:40:00.012 [pool-14-thread-6] WARN  com.simbest.boot.base.repository.CustomDynamicWhere.queryForList Line:50 - 自定义查询SQL输出为：【select * from US_TODO_MODEL t  where t.send_flag = 0 and t.work_flag = 0 and t.enabled =1】
2025-05-30 05:45:00.008 [pool-14-thread-2] WARN  com.simbest.boot.base.repository.CustomDynamicWhere.queryForList Line:50 - 自定义查询SQL输出为：【select * from US_TODO_MODEL t  where t.send_flag = 0 and t.work_flag = 1 and t.enabled =1】
2025-05-30 05:45:00.008 [pool-14-thread-6] WARN  com.simbest.boot.base.repository.CustomDynamicWhere.queryForList Line:50 - 自定义查询SQL输出为：【select * from US_TODO_MODEL t  where t.send_flag = 0 and t.work_flag = 0 and t.enabled =1】
2025-05-30 05:50:00.007 [pool-14-thread-9] WARN  com.simbest.boot.base.repository.CustomDynamicWhere.queryForList Line:50 - 自定义查询SQL输出为：【select * from US_TODO_MODEL t  where t.send_flag = 0 and t.work_flag = 1 and t.enabled =1】
2025-05-30 05:50:00.008 [pool-14-thread-7] WARN  com.simbest.boot.base.repository.CustomDynamicWhere.queryForList Line:50 - 自定义查询SQL输出为：【select * from US_TODO_MODEL t  where t.send_flag = 0 and t.work_flag = 0 and t.enabled =1】
2025-05-30 05:55:00.017 [pool-14-thread-9] WARN  com.simbest.boot.base.repository.CustomDynamicWhere.queryForList Line:50 - 自定义查询SQL输出为：【select * from US_TODO_MODEL t  where t.send_flag = 0 and t.work_flag = 0 and t.enabled =1】
2025-05-30 05:55:00.017 [pool-14-thread-7] WARN  com.simbest.boot.base.repository.CustomDynamicWhere.queryForList Line:50 - 自定义查询SQL输出为：【select * from US_TODO_MODEL t  where t.send_flag = 0 and t.work_flag = 1 and t.enabled =1】
2025-05-30 06:00:00.018 [pool-14-thread-4] WARN  com.simbest.boot.base.repository.CustomDynamicWhere.queryForList Line:50 - 自定义查询SQL输出为：【select * from US_TODO_MODEL t  where t.send_flag = 0 and t.work_flag = 0 and t.enabled =1】
2025-05-30 06:00:00.018 [pool-14-thread-8] WARN  com.simbest.boot.cmcc.a4.task.A4TaskCreateXmlFile.execute Line:76 - >>>>>>>>>>>>>>>>>>>>>>>>>>>>>>>>>>>>>>>>>>>>>>>>>>>>>A4TaskCreateXmlFile:执行定时任务但不是Prd环境【2025-05-30T06:00:00.018】
2025-05-30 06:00:00.023 [pool-14-thread-1] WARN  com.simbest.boot.base.repository.CustomDynamicWhere.queryForList Line:50 - 自定义查询SQL输出为：【select * from US_TODO_MODEL t  where t.send_flag = 0 and t.work_flag = 1 and t.enabled =1】
2025-05-30 06:05:00.008 [pool-14-thread-9] WARN  com.simbest.boot.base.repository.CustomDynamicWhere.queryForList Line:50 - 自定义查询SQL输出为：【select * from US_TODO_MODEL t  where t.send_flag = 0 and t.work_flag = 0 and t.enabled =1】
2025-05-30 06:05:00.008 [pool-14-thread-6] WARN  com.simbest.boot.base.repository.CustomDynamicWhere.queryForList Line:50 - 自定义查询SQL输出为：【select * from US_TODO_MODEL t  where t.send_flag = 0 and t.work_flag = 1 and t.enabled =1】
2025-05-30 06:10:00.018 [pool-14-thread-6] WARN  com.simbest.boot.base.repository.CustomDynamicWhere.queryForList Line:50 - 自定义查询SQL输出为：【select * from US_TODO_MODEL t  where t.send_flag = 0 and t.work_flag = 1 and t.enabled =1】
2025-05-30 06:10:00.019 [pool-14-thread-8] WARN  com.simbest.boot.base.repository.CustomDynamicWhere.queryForList Line:50 - 自定义查询SQL输出为：【select * from US_TODO_MODEL t  where t.send_flag = 0 and t.work_flag = 0 and t.enabled =1】
2025-05-30 06:15:00.013 [pool-14-thread-5] WARN  com.simbest.boot.base.repository.CustomDynamicWhere.queryForList Line:50 - 自定义查询SQL输出为：【select * from US_TODO_MODEL t  where t.send_flag = 0 and t.work_flag = 1 and t.enabled =1】
2025-05-30 06:15:00.013 [pool-14-thread-10] WARN  com.simbest.boot.base.repository.CustomDynamicWhere.queryForList Line:50 - 自定义查询SQL输出为：【select * from US_TODO_MODEL t  where t.send_flag = 0 and t.work_flag = 0 and t.enabled =1】
2025-05-30 06:20:00.020 [pool-14-thread-4] WARN  com.simbest.boot.base.repository.CustomDynamicWhere.queryForList Line:50 - 自定义查询SQL输出为：【select * from US_TODO_MODEL t  where t.send_flag = 0 and t.work_flag = 1 and t.enabled =1】
2025-05-30 06:20:00.020 [pool-14-thread-3] WARN  com.simbest.boot.base.repository.CustomDynamicWhere.queryForList Line:50 - 自定义查询SQL输出为：【select * from US_TODO_MODEL t  where t.send_flag = 0 and t.work_flag = 0 and t.enabled =1】
2025-05-30 06:25:00.017 [pool-14-thread-8] WARN  com.simbest.boot.base.repository.CustomDynamicWhere.queryForList Line:50 - 自定义查询SQL输出为：【select * from US_TODO_MODEL t  where t.send_flag = 0 and t.work_flag = 1 and t.enabled =1】
2025-05-30 06:25:00.019 [pool-14-thread-10] WARN  com.simbest.boot.base.repository.CustomDynamicWhere.queryForList Line:50 - 自定义查询SQL输出为：【select * from US_TODO_MODEL t  where t.send_flag = 0 and t.work_flag = 0 and t.enabled =1】
2025-05-30 06:30:00.019 [pool-14-thread-1] WARN  com.simbest.boot.base.repository.CustomDynamicWhere.queryForList Line:50 - 自定义查询SQL输出为：【select * from US_TODO_MODEL t  where t.send_flag = 0 and t.work_flag = 1 and t.enabled =1】
2025-05-30 06:30:00.019 [pool-14-thread-5] WARN  com.simbest.boot.base.repository.CustomDynamicWhere.queryForList Line:50 - 自定义查询SQL输出为：【select * from US_TODO_MODEL t  where t.send_flag = 0 and t.work_flag = 0 and t.enabled =1】
2025-05-30 06:35:00.003 [pool-14-thread-4] WARN  com.simbest.boot.base.repository.CustomDynamicWhere.queryForList Line:50 - 自定义查询SQL输出为：【select * from US_TODO_MODEL t  where t.send_flag = 0 and t.work_flag = 0 and t.enabled =1】
2025-05-30 06:35:00.006 [pool-14-thread-1] WARN  com.simbest.boot.base.repository.CustomDynamicWhere.queryForList Line:50 - 自定义查询SQL输出为：【select * from US_TODO_MODEL t  where t.send_flag = 0 and t.work_flag = 1 and t.enabled =1】
2025-05-30 06:40:00.018 [pool-14-thread-5] WARN  com.simbest.boot.base.repository.CustomDynamicWhere.queryForList Line:50 - 自定义查询SQL输出为：【select * from US_TODO_MODEL t  where t.send_flag = 0 and t.work_flag = 1 and t.enabled =1】
2025-05-30 06:40:00.018 [pool-14-thread-6] WARN  com.simbest.boot.base.repository.CustomDynamicWhere.queryForList Line:50 - 自定义查询SQL输出为：【select * from US_TODO_MODEL t  where t.send_flag = 0 and t.work_flag = 0 and t.enabled =1】
2025-05-30 06:45:00.016 [pool-14-thread-6] WARN  com.simbest.boot.base.repository.CustomDynamicWhere.queryForList Line:50 - 自定义查询SQL输出为：【select * from US_TODO_MODEL t  where t.send_flag = 0 and t.work_flag = 1 and t.enabled =1】
2025-05-30 06:45:00.018 [pool-14-thread-1] WARN  com.simbest.boot.base.repository.CustomDynamicWhere.queryForList Line:50 - 自定义查询SQL输出为：【select * from US_TODO_MODEL t  where t.send_flag = 0 and t.work_flag = 0 and t.enabled =1】
2025-05-30 06:50:00.005 [pool-14-thread-1] WARN  com.simbest.boot.base.repository.CustomDynamicWhere.queryForList Line:50 - 自定义查询SQL输出为：【select * from US_TODO_MODEL t  where t.send_flag = 0 and t.work_flag = 0 and t.enabled =1】
2025-05-30 06:50:00.005 [pool-14-thread-7] WARN  com.simbest.boot.base.repository.CustomDynamicWhere.queryForList Line:50 - 自定义查询SQL输出为：【select * from US_TODO_MODEL t  where t.send_flag = 0 and t.work_flag = 1 and t.enabled =1】
2025-05-30 06:55:00.006 [pool-14-thread-4] WARN  com.simbest.boot.base.repository.CustomDynamicWhere.queryForList Line:50 - 自定义查询SQL输出为：【select * from US_TODO_MODEL t  where t.send_flag = 0 and t.work_flag = 0 and t.enabled =1】
2025-05-30 06:55:00.008 [pool-14-thread-2] WARN  com.simbest.boot.base.repository.CustomDynamicWhere.queryForList Line:50 - 自定义查询SQL输出为：【select * from US_TODO_MODEL t  where t.send_flag = 0 and t.work_flag = 1 and t.enabled =1】
2025-05-30 07:00:00.015 [pool-14-thread-2] WARN  com.simbest.boot.base.repository.CustomDynamicWhere.queryForList Line:50 - 自定义查询SQL输出为：【select * from US_TODO_MODEL t  where t.send_flag = 0 and t.work_flag = 0 and t.enabled =1】
2025-05-30 07:00:00.016 [pool-14-thread-3] WARN  com.simbest.boot.cmcc.a4.task.A4TaskCreateXmlFile.execute Line:76 - >>>>>>>>>>>>>>>>>>>>>>>>>>>>>>>>>>>>>>>>>>>>>>>>>>>>>A4TaskCreateXmlFile:执行定时任务但不是Prd环境【2025-05-30T07:00:00.016】
2025-05-30 07:00:00.022 [pool-14-thread-5] WARN  com.simbest.boot.base.repository.CustomDynamicWhere.queryForList Line:50 - 自定义查询SQL输出为：【select * from US_TODO_MODEL t  where t.send_flag = 0 and t.work_flag = 1 and t.enabled =1】
2025-05-30 07:05:00.013 [pool-14-thread-2] WARN  com.simbest.boot.base.repository.CustomDynamicWhere.queryForList Line:50 - 自定义查询SQL输出为：【select * from US_TODO_MODEL t  where t.send_flag = 0 and t.work_flag = 1 and t.enabled =1】
2025-05-30 07:05:00.013 [pool-14-thread-5] WARN  com.simbest.boot.base.repository.CustomDynamicWhere.queryForList Line:50 - 自定义查询SQL输出为：【select * from US_TODO_MODEL t  where t.send_flag = 0 and t.work_flag = 0 and t.enabled =1】
2025-05-30 07:10:00.020 [pool-14-thread-5] WARN  com.simbest.boot.base.repository.CustomDynamicWhere.queryForList Line:50 - 自定义查询SQL输出为：【select * from US_TODO_MODEL t  where t.send_flag = 0 and t.work_flag = 0 and t.enabled =1】
2025-05-30 07:10:00.020 [pool-14-thread-9] WARN  com.simbest.boot.base.repository.CustomDynamicWhere.queryForList Line:50 - 自定义查询SQL输出为：【select * from US_TODO_MODEL t  where t.send_flag = 0 and t.work_flag = 1 and t.enabled =1】
2025-05-30 07:15:00.009 [pool-14-thread-5] WARN  com.simbest.boot.base.repository.CustomDynamicWhere.queryForList Line:50 - 自定义查询SQL输出为：【select * from US_TODO_MODEL t  where t.send_flag = 0 and t.work_flag = 0 and t.enabled =1】
2025-05-30 07:15:00.009 [pool-14-thread-8] WARN  com.simbest.boot.base.repository.CustomDynamicWhere.queryForList Line:50 - 自定义查询SQL输出为：【select * from US_TODO_MODEL t  where t.send_flag = 0 and t.work_flag = 1 and t.enabled =1】
2025-05-30 07:20:00.018 [pool-14-thread-10] WARN  com.simbest.boot.base.repository.CustomDynamicWhere.queryForList Line:50 - 自定义查询SQL输出为：【select * from US_TODO_MODEL t  where t.send_flag = 0 and t.work_flag = 0 and t.enabled =1】
2025-05-30 07:20:00.018 [pool-14-thread-8] WARN  com.simbest.boot.base.repository.CustomDynamicWhere.queryForList Line:50 - 自定义查询SQL输出为：【select * from US_TODO_MODEL t  where t.send_flag = 0 and t.work_flag = 1 and t.enabled =1】
2025-05-30 07:25:00.016 [pool-14-thread-9] WARN  com.simbest.boot.base.repository.CustomDynamicWhere.queryForList Line:50 - 自定义查询SQL输出为：【select * from US_TODO_MODEL t  where t.send_flag = 0 and t.work_flag = 0 and t.enabled =1】
2025-05-30 07:25:00.016 [pool-14-thread-1] WARN  com.simbest.boot.base.repository.CustomDynamicWhere.queryForList Line:50 - 自定义查询SQL输出为：【select * from US_TODO_MODEL t  where t.send_flag = 0 and t.work_flag = 1 and t.enabled =1】
2025-05-30 07:30:00.017 [pool-14-thread-9] WARN  com.simbest.boot.base.repository.CustomDynamicWhere.queryForList Line:50 - 自定义查询SQL输出为：【select * from US_TODO_MODEL t  where t.send_flag = 0 and t.work_flag = 1 and t.enabled =1】
2025-05-30 07:30:00.018 [pool-14-thread-4] WARN  com.simbest.boot.base.repository.CustomDynamicWhere.queryForList Line:50 - 自定义查询SQL输出为：【select * from US_TODO_MODEL t  where t.send_flag = 0 and t.work_flag = 0 and t.enabled =1】
2025-05-30 07:35:00.009 [pool-14-thread-10] WARN  com.simbest.boot.base.repository.CustomDynamicWhere.queryForList Line:50 - 自定义查询SQL输出为：【select * from US_TODO_MODEL t  where t.send_flag = 0 and t.work_flag = 0 and t.enabled =1】
2025-05-30 07:35:00.009 [pool-14-thread-2] WARN  com.simbest.boot.base.repository.CustomDynamicWhere.queryForList Line:50 - 自定义查询SQL输出为：【select * from US_TODO_MODEL t  where t.send_flag = 0 and t.work_flag = 1 and t.enabled =1】
2025-05-30 07:40:00.016 [pool-14-thread-1] WARN  com.simbest.boot.base.repository.CustomDynamicWhere.queryForList Line:50 - 自定义查询SQL输出为：【select * from US_TODO_MODEL t  where t.send_flag = 0 and t.work_flag = 1 and t.enabled =1】
2025-05-30 07:40:00.016 [pool-14-thread-4] WARN  com.simbest.boot.base.repository.CustomDynamicWhere.queryForList Line:50 - 自定义查询SQL输出为：【select * from US_TODO_MODEL t  where t.send_flag = 0 and t.work_flag = 0 and t.enabled =1】
2025-05-30 07:45:00.014 [pool-14-thread-2] WARN  com.simbest.boot.base.repository.CustomDynamicWhere.queryForList Line:50 - 自定义查询SQL输出为：【select * from US_TODO_MODEL t  where t.send_flag = 0 and t.work_flag = 1 and t.enabled =1】
2025-05-30 07:45:00.014 [pool-14-thread-7] WARN  com.simbest.boot.base.repository.CustomDynamicWhere.queryForList Line:50 - 自定义查询SQL输出为：【select * from US_TODO_MODEL t  where t.send_flag = 0 and t.work_flag = 0 and t.enabled =1】
2025-05-30 07:50:00.015 [pool-14-thread-7] WARN  com.simbest.boot.base.repository.CustomDynamicWhere.queryForList Line:50 - 自定义查询SQL输出为：【select * from US_TODO_MODEL t  where t.send_flag = 0 and t.work_flag = 0 and t.enabled =1】
2025-05-30 07:50:00.015 [pool-14-thread-5] WARN  com.simbest.boot.base.repository.CustomDynamicWhere.queryForList Line:50 - 自定义查询SQL输出为：【select * from US_TODO_MODEL t  where t.send_flag = 0 and t.work_flag = 1 and t.enabled =1】
2025-05-30 07:55:00.017 [pool-14-thread-7] WARN  com.simbest.boot.base.repository.CustomDynamicWhere.queryForList Line:50 - 自定义查询SQL输出为：【select * from US_TODO_MODEL t  where t.send_flag = 0 and t.work_flag = 1 and t.enabled =1】
2025-05-30 07:55:00.029 [pool-14-thread-10] WARN  com.simbest.boot.base.repository.CustomDynamicWhere.queryForList Line:50 - 自定义查询SQL输出为：【select * from US_TODO_MODEL t  where t.send_flag = 0 and t.work_flag = 0 and t.enabled =1】
2025-05-30 08:00:00.018 [pool-14-thread-5] WARN  com.simbest.boot.cmcc.a4.task.A4TaskCreateXmlFile.execute Line:76 - >>>>>>>>>>>>>>>>>>>>>>>>>>>>>>>>>>>>>>>>>>>>>>>>>>>>>A4TaskCreateXmlFile:执行定时任务但不是Prd环境【2025-05-30T08:00:00.018】
2025-05-30 08:00:00.027 [pool-14-thread-3] WARN  com.simbest.boot.base.repository.CustomDynamicWhere.queryForList Line:50 - 自定义查询SQL输出为：【select * from US_TODO_MODEL t  where t.send_flag = 0 and t.work_flag = 0 and t.enabled =1】
2025-05-30 08:00:00.027 [pool-14-thread-2] WARN  com.simbest.boot.base.repository.CustomDynamicWhere.queryForList Line:50 - 自定义查询SQL输出为：【select * from US_TODO_MODEL t  where t.send_flag = 0 and t.work_flag = 1 and t.enabled =1】
2025-05-30 08:05:00.012 [pool-14-thread-5] WARN  com.simbest.boot.base.repository.CustomDynamicWhere.queryForList Line:50 - 自定义查询SQL输出为：【select * from US_TODO_MODEL t  where t.send_flag = 0 and t.work_flag = 1 and t.enabled =1】
2025-05-30 08:05:00.013 [pool-14-thread-9] WARN  com.simbest.boot.base.repository.CustomDynamicWhere.queryForList Line:50 - 自定义查询SQL输出为：【select * from US_TODO_MODEL t  where t.send_flag = 0 and t.work_flag = 0 and t.enabled =1】
2025-05-30 08:10:00.019 [pool-14-thread-1] WARN  com.simbest.boot.base.repository.CustomDynamicWhere.queryForList Line:50 - 自定义查询SQL输出为：【select * from US_TODO_MODEL t  where t.send_flag = 0 and t.work_flag = 1 and t.enabled =1】
2025-05-30 08:10:00.019 [pool-14-thread-2] WARN  com.simbest.boot.base.repository.CustomDynamicWhere.queryForList Line:50 - 自定义查询SQL输出为：【select * from US_TODO_MODEL t  where t.send_flag = 0 and t.work_flag = 0 and t.enabled =1】
2025-05-30 08:15:00.013 [pool-14-thread-4] WARN  com.simbest.boot.base.repository.CustomDynamicWhere.queryForList Line:50 - 自定义查询SQL输出为：【select * from US_TODO_MODEL t  where t.send_flag = 0 and t.work_flag = 0 and t.enabled =1】
2025-05-30 08:15:00.013 [pool-14-thread-7] WARN  com.simbest.boot.base.repository.CustomDynamicWhere.queryForList Line:50 - 自定义查询SQL输出为：【select * from US_TODO_MODEL t  where t.send_flag = 0 and t.work_flag = 1 and t.enabled =1】
2025-05-30 08:20:00.014 [pool-14-thread-2] WARN  com.simbest.boot.base.repository.CustomDynamicWhere.queryForList Line:50 - 自定义查询SQL输出为：【select * from US_TODO_MODEL t  where t.send_flag = 0 and t.work_flag = 0 and t.enabled =1】
2025-05-30 08:20:00.014 [pool-14-thread-6] WARN  com.simbest.boot.base.repository.CustomDynamicWhere.queryForList Line:50 - 自定义查询SQL输出为：【select * from US_TODO_MODEL t  where t.send_flag = 0 and t.work_flag = 1 and t.enabled =1】
2025-05-30 08:25:00.022 [pool-14-thread-2] WARN  com.simbest.boot.base.repository.CustomDynamicWhere.queryForList Line:50 - 自定义查询SQL输出为：【select * from US_TODO_MODEL t  where t.send_flag = 0 and t.work_flag = 1 and t.enabled =1】
2025-05-30 08:25:00.022 [pool-14-thread-4] WARN  com.simbest.boot.base.repository.CustomDynamicWhere.queryForList Line:50 - 自定义查询SQL输出为：【select * from US_TODO_MODEL t  where t.send_flag = 0 and t.work_flag = 0 and t.enabled =1】
2025-05-30 08:30:00.009 [pool-14-thread-2] WARN  com.simbest.boot.base.repository.CustomDynamicWhere.queryForList Line:50 - 自定义查询SQL输出为：【select * from US_TODO_MODEL t  where t.send_flag = 0 and t.work_flag = 0 and t.enabled =1】
2025-05-30 08:30:00.011 [pool-14-thread-1] WARN  com.simbest.boot.base.repository.CustomDynamicWhere.queryForList Line:50 - 自定义查询SQL输出为：【select * from US_TODO_MODEL t  where t.send_flag = 0 and t.work_flag = 1 and t.enabled =1】
2025-05-30 08:35:00.004 [pool-14-thread-1] WARN  com.simbest.boot.base.repository.CustomDynamicWhere.queryForList Line:50 - 自定义查询SQL输出为：【select * from US_TODO_MODEL t  where t.send_flag = 0 and t.work_flag = 0 and t.enabled =1】
2025-05-30 08:35:00.006 [pool-14-thread-6] WARN  com.simbest.boot.base.repository.CustomDynamicWhere.queryForList Line:50 - 自定义查询SQL输出为：【select * from US_TODO_MODEL t  where t.send_flag = 0 and t.work_flag = 1 and t.enabled =1】
2025-05-30 08:40:00.013 [pool-14-thread-5] WARN  com.simbest.boot.base.repository.CustomDynamicWhere.queryForList Line:50 - 自定义查询SQL输出为：【select * from US_TODO_MODEL t  where t.send_flag = 0 and t.work_flag = 0 and t.enabled =1】
2025-05-30 08:40:00.013 [pool-14-thread-10] WARN  com.simbest.boot.base.repository.CustomDynamicWhere.queryForList Line:50 - 自定义查询SQL输出为：【select * from US_TODO_MODEL t  where t.send_flag = 0 and t.work_flag = 1 and t.enabled =1】
2025-05-30 08:44:08.062 [http-nio-8092-exec-5] WARN  com.simbest.boot.security.auth.service.impl.AuthUserCacheServiceImpl.loadCacheUser Line:118 - 无法通过关键字【auth:user:zhanglu16】读取用户主键ID
2025-05-30 08:44:08.316 [http-nio-8092-exec-5] WARN  com.simbest.boot.security.auth.service.impl.AuthUserCacheServiceImpl.saveOrUpdateCacheUser Line:64 - 即将在缓存中将用户权限置为空----SET AuthPermissions EMPTY----【38348】
2025-05-30 08:45:00.017 [pool-14-thread-8] WARN  com.simbest.boot.base.repository.CustomDynamicWhere.queryForList Line:50 - 自定义查询SQL输出为：【select * from US_TODO_MODEL t  where t.send_flag = 0 and t.work_flag = 0 and t.enabled =1】
2025-05-30 08:45:00.019 [pool-14-thread-7] WARN  com.simbest.boot.base.repository.CustomDynamicWhere.queryForList Line:50 - 自定义查询SQL输出为：【select * from US_TODO_MODEL t  where t.send_flag = 0 and t.work_flag = 1 and t.enabled =1】
2025-05-30 08:50:00.013 [pool-14-thread-7] WARN  com.simbest.boot.base.repository.CustomDynamicWhere.queryForList Line:50 - 自定义查询SQL输出为：【select * from US_TODO_MODEL t  where t.send_flag = 0 and t.work_flag = 0 and t.enabled =1】
2025-05-30 08:50:00.018 [pool-14-thread-9] WARN  com.simbest.boot.base.repository.CustomDynamicWhere.queryForList Line:50 - 自定义查询SQL输出为：【select * from US_TODO_MODEL t  where t.send_flag = 0 and t.work_flag = 1 and t.enabled =1】
2025-05-30 08:55:00.021 [pool-14-thread-5] WARN  com.simbest.boot.base.repository.CustomDynamicWhere.queryForList Line:50 - 自定义查询SQL输出为：【select * from US_TODO_MODEL t  where t.send_flag = 0 and t.work_flag = 0 and t.enabled =1】
2025-05-30 08:55:00.022 [pool-14-thread-10] WARN  com.simbest.boot.base.repository.CustomDynamicWhere.queryForList Line:50 - 自定义查询SQL输出为：【select * from US_TODO_MODEL t  where t.send_flag = 0 and t.work_flag = 1 and t.enabled =1】
2025-05-30 09:00:00.012 [pool-14-thread-6] WARN  com.simbest.boot.base.repository.CustomDynamicWhere.queryForList Line:50 - 自定义查询SQL输出为：【select * from US_TODO_MODEL t  where t.send_flag = 0 and t.work_flag = 0 and t.enabled =1】
2025-05-30 09:00:00.015 [pool-14-thread-7] WARN  com.simbest.boot.cmcc.a4.task.A4TaskCreateXmlFile.execute Line:76 - >>>>>>>>>>>>>>>>>>>>>>>>>>>>>>>>>>>>>>>>>>>>>>>>>>>>>A4TaskCreateXmlFile:执行定时任务但不是Prd环境【2025-05-30T09:00:00.015】
2025-05-30 09:00:00.017 [pool-14-thread-5] WARN  com.simbest.boot.base.repository.CustomDynamicWhere.queryForList Line:50 - 自定义查询SQL输出为：【select * from US_TODO_MODEL t  where t.send_flag = 0 and t.work_flag = 1 and t.enabled =1】
2025-05-30 09:05:00.014 [pool-14-thread-4] WARN  com.simbest.boot.base.repository.CustomDynamicWhere.queryForList Line:50 - 自定义查询SQL输出为：【select * from US_TODO_MODEL t  where t.send_flag = 0 and t.work_flag = 0 and t.enabled =1】
2025-05-30 09:05:00.014 [pool-14-thread-8] WARN  com.simbest.boot.base.repository.CustomDynamicWhere.queryForList Line:50 - 自定义查询SQL输出为：【select * from US_TODO_MODEL t  where t.send_flag = 0 and t.work_flag = 1 and t.enabled =1】
2025-05-30 09:10:00.016 [pool-14-thread-5] WARN  com.simbest.boot.base.repository.CustomDynamicWhere.queryForList Line:50 - 自定义查询SQL输出为：【select * from US_TODO_MODEL t  where t.send_flag = 0 and t.work_flag = 1 and t.enabled =1】
2025-05-30 09:10:00.023 [pool-14-thread-10] WARN  com.simbest.boot.base.repository.CustomDynamicWhere.queryForList Line:50 - 自定义查询SQL输出为：【select * from US_TODO_MODEL t  where t.send_flag = 0 and t.work_flag = 0 and t.enabled =1】
2025-05-30 09:15:00.011 [pool-14-thread-2] WARN  com.simbest.boot.base.repository.CustomDynamicWhere.queryForList Line:50 - 自定义查询SQL输出为：【select * from US_TODO_MODEL t  where t.send_flag = 0 and t.work_flag = 1 and t.enabled =1】
2025-05-30 09:15:00.011 [pool-14-thread-9] WARN  com.simbest.boot.base.repository.CustomDynamicWhere.queryForList Line:50 - 自定义查询SQL输出为：【select * from US_TODO_MODEL t  where t.send_flag = 0 and t.work_flag = 0 and t.enabled =1】
2025-05-30 09:20:00.261 [pool-14-thread-1] WARN  com.simbest.boot.base.repository.CustomDynamicWhere.queryForList Line:50 - 自定义查询SQL输出为：【select * from US_TODO_MODEL t  where t.send_flag = 0 and t.work_flag = 1 and t.enabled =1】
2025-05-30 09:20:00.261 [pool-14-thread-2] WARN  com.simbest.boot.base.repository.CustomDynamicWhere.queryForList Line:50 - 自定义查询SQL输出为：【select * from US_TODO_MODEL t  where t.send_flag = 0 and t.work_flag = 0 and t.enabled =1】
2025-05-30 09:25:00.019 [pool-14-thread-7] WARN  com.simbest.boot.base.repository.CustomDynamicWhere.queryForList Line:50 - 自定义查询SQL输出为：【select * from US_TODO_MODEL t  where t.send_flag = 0 and t.work_flag = 1 and t.enabled =1】
2025-05-30 09:25:00.019 [pool-14-thread-10] WARN  com.simbest.boot.base.repository.CustomDynamicWhere.queryForList Line:50 - 自定义查询SQL输出为：【select * from US_TODO_MODEL t  where t.send_flag = 0 and t.work_flag = 0 and t.enabled =1】
2025-05-30 09:30:00.012 [pool-14-thread-9] WARN  com.simbest.boot.base.repository.CustomDynamicWhere.queryForList Line:50 - 自定义查询SQL输出为：【select * from US_TODO_MODEL t  where t.send_flag = 0 and t.work_flag = 0 and t.enabled =1】
2025-05-30 09:30:00.014 [pool-14-thread-7] WARN  com.simbest.boot.base.repository.CustomDynamicWhere.queryForList Line:50 - 自定义查询SQL输出为：【select * from US_TODO_MODEL t  where t.send_flag = 0 and t.work_flag = 1 and t.enabled =1】
2025-05-30 09:35:00.005 [pool-14-thread-7] WARN  com.simbest.boot.base.repository.CustomDynamicWhere.queryForList Line:50 - 自定义查询SQL输出为：【select * from US_TODO_MODEL t  where t.send_flag = 0 and t.work_flag = 1 and t.enabled =1】
2025-05-30 09:35:00.005 [pool-14-thread-6] WARN  com.simbest.boot.base.repository.CustomDynamicWhere.queryForList Line:50 - 自定义查询SQL输出为：【select * from US_TODO_MODEL t  where t.send_flag = 0 and t.work_flag = 0 and t.enabled =1】
2025-05-30 09:40:00.020 [pool-14-thread-10] WARN  com.simbest.boot.base.repository.CustomDynamicWhere.queryForList Line:50 - 自定义查询SQL输出为：【select * from US_TODO_MODEL t  where t.send_flag = 0 and t.work_flag = 1 and t.enabled =1】
2025-05-30 09:40:00.025 [pool-14-thread-1] WARN  com.simbest.boot.base.repository.CustomDynamicWhere.queryForList Line:50 - 自定义查询SQL输出为：【select * from US_TODO_MODEL t  where t.send_flag = 0 and t.work_flag = 0 and t.enabled =1】
2025-05-30 09:45:00.005 [pool-14-thread-5] WARN  com.simbest.boot.base.repository.CustomDynamicWhere.queryForList Line:50 - 自定义查询SQL输出为：【select * from US_TODO_MODEL t  where t.send_flag = 0 and t.work_flag = 0 and t.enabled =1】
2025-05-30 09:45:00.006 [pool-14-thread-3] WARN  com.simbest.boot.base.repository.CustomDynamicWhere.queryForList Line:50 - 自定义查询SQL输出为：【select * from US_TODO_MODEL t  where t.send_flag = 0 and t.work_flag = 1 and t.enabled =1】
2025-05-30 09:50:00.016 [pool-14-thread-2] WARN  com.simbest.boot.base.repository.CustomDynamicWhere.queryForList Line:50 - 自定义查询SQL输出为：【select * from US_TODO_MODEL t  where t.send_flag = 0 and t.work_flag = 1 and t.enabled =1】
2025-05-30 09:50:00.016 [pool-14-thread-10] WARN  com.simbest.boot.base.repository.CustomDynamicWhere.queryForList Line:50 - 自定义查询SQL输出为：【select * from US_TODO_MODEL t  where t.send_flag = 0 and t.work_flag = 0 and t.enabled =1】
2025-05-30 09:55:00.016 [pool-14-thread-4] WARN  com.simbest.boot.base.repository.CustomDynamicWhere.queryForList Line:50 - 自定义查询SQL输出为：【select * from US_TODO_MODEL t  where t.send_flag = 0 and t.work_flag = 0 and t.enabled =1】
2025-05-30 09:55:00.018 [pool-14-thread-9] WARN  com.simbest.boot.base.repository.CustomDynamicWhere.queryForList Line:50 - 自定义查询SQL输出为：【select * from US_TODO_MODEL t  where t.send_flag = 0 and t.work_flag = 1 and t.enabled =1】
2025-05-30 10:00:00.006 [pool-14-thread-3] WARN  com.simbest.boot.base.repository.CustomDynamicWhere.queryForList Line:50 - 自定义查询SQL输出为：【select * from US_TODO_MODEL t  where t.send_flag = 0 and t.work_flag = 0 and t.enabled =1】
2025-05-30 10:00:00.006 [pool-14-thread-6] WARN  com.simbest.boot.base.repository.CustomDynamicWhere.queryForList Line:50 - 自定义查询SQL输出为：【select * from US_TODO_MODEL t  where t.send_flag = 0 and t.work_flag = 1 and t.enabled =1】
2025-05-30 10:00:00.012 [pool-14-thread-9] WARN  com.simbest.boot.cmcc.a4.task.A4TaskCreateXmlFile.execute Line:76 - >>>>>>>>>>>>>>>>>>>>>>>>>>>>>>>>>>>>>>>>>>>>>>>>>>>>>A4TaskCreateXmlFile:执行定时任务但不是Prd环境【2025-05-30T10:00:00.012】
2025-05-30 10:05:00.016 [pool-14-thread-3] WARN  com.simbest.boot.base.repository.CustomDynamicWhere.queryForList Line:50 - 自定义查询SQL输出为：【select * from US_TODO_MODEL t  where t.send_flag = 0 and t.work_flag = 0 and t.enabled =1】
2025-05-30 10:05:00.019 [pool-14-thread-8] WARN  com.simbest.boot.base.repository.CustomDynamicWhere.queryForList Line:50 - 自定义查询SQL输出为：【select * from US_TODO_MODEL t  where t.send_flag = 0 and t.work_flag = 1 and t.enabled =1】
2025-05-30 10:10:00.014 [pool-14-thread-7] WARN  com.simbest.boot.base.repository.CustomDynamicWhere.queryForList Line:50 - 自定义查询SQL输出为：【select * from US_TODO_MODEL t  where t.send_flag = 0 and t.work_flag = 1 and t.enabled =1】
2025-05-30 10:10:00.014 [pool-14-thread-10] WARN  com.simbest.boot.base.repository.CustomDynamicWhere.queryForList Line:50 - 自定义查询SQL输出为：【select * from US_TODO_MODEL t  where t.send_flag = 0 and t.work_flag = 0 and t.enabled =1】
2025-05-30 10:15:00.007 [pool-14-thread-4] WARN  com.simbest.boot.base.repository.CustomDynamicWhere.queryForList Line:50 - 自定义查询SQL输出为：【select * from US_TODO_MODEL t  where t.send_flag = 0 and t.work_flag = 0 and t.enabled =1】
2025-05-30 10:15:00.010 [pool-14-thread-1] WARN  com.simbest.boot.base.repository.CustomDynamicWhere.queryForList Line:50 - 自定义查询SQL输出为：【select * from US_TODO_MODEL t  where t.send_flag = 0 and t.work_flag = 1 and t.enabled =1】
2025-05-30 10:20:00.015 [pool-14-thread-6] WARN  com.simbest.boot.base.repository.CustomDynamicWhere.queryForList Line:50 - 自定义查询SQL输出为：【select * from US_TODO_MODEL t  where t.send_flag = 0 and t.work_flag = 0 and t.enabled =1】
2025-05-30 10:20:00.017 [pool-14-thread-4] WARN  com.simbest.boot.base.repository.CustomDynamicWhere.queryForList Line:50 - 自定义查询SQL输出为：【select * from US_TODO_MODEL t  where t.send_flag = 0 and t.work_flag = 1 and t.enabled =1】
2025-05-30 10:25:00.016 [pool-14-thread-5] WARN  com.simbest.boot.base.repository.CustomDynamicWhere.queryForList Line:50 - 自定义查询SQL输出为：【select * from US_TODO_MODEL t  where t.send_flag = 0 and t.work_flag = 0 and t.enabled =1】
2025-05-30 10:25:00.019 [pool-14-thread-1] WARN  com.simbest.boot.base.repository.CustomDynamicWhere.queryForList Line:50 - 自定义查询SQL输出为：【select * from US_TODO_MODEL t  where t.send_flag = 0 and t.work_flag = 1 and t.enabled =1】
2025-05-30 10:30:00.017 [pool-14-thread-1] WARN  com.simbest.boot.base.repository.CustomDynamicWhere.queryForList Line:50 - 自定义查询SQL输出为：【select * from US_TODO_MODEL t  where t.send_flag = 0 and t.work_flag = 0 and t.enabled =1】
2025-05-30 10:30:00.017 [pool-14-thread-8] WARN  com.simbest.boot.base.repository.CustomDynamicWhere.queryForList Line:50 - 自定义查询SQL输出为：【select * from US_TODO_MODEL t  where t.send_flag = 0 and t.work_flag = 1 and t.enabled =1】
2025-05-30 10:35:00.016 [pool-14-thread-4] WARN  com.simbest.boot.base.repository.CustomDynamicWhere.queryForList Line:50 - 自定义查询SQL输出为：【select * from US_TODO_MODEL t  where t.send_flag = 0 and t.work_flag = 0 and t.enabled =1】
2025-05-30 10:35:00.019 [pool-14-thread-6] WARN  com.simbest.boot.base.repository.CustomDynamicWhere.queryForList Line:50 - 自定义查询SQL输出为：【select * from US_TODO_MODEL t  where t.send_flag = 0 and t.work_flag = 1 and t.enabled =1】
2025-05-30 10:40:00.013 [pool-14-thread-2] WARN  com.simbest.boot.base.repository.CustomDynamicWhere.queryForList Line:50 - 自定义查询SQL输出为：【select * from US_TODO_MODEL t  where t.send_flag = 0 and t.work_flag = 0 and t.enabled =1】
2025-05-30 10:40:00.013 [pool-14-thread-8] WARN  com.simbest.boot.base.repository.CustomDynamicWhere.queryForList Line:50 - 自定义查询SQL输出为：【select * from US_TODO_MODEL t  where t.send_flag = 0 and t.work_flag = 1 and t.enabled =1】
2025-05-30 10:45:00.011 [pool-14-thread-1] WARN  com.simbest.boot.base.repository.CustomDynamicWhere.queryForList Line:50 - 自定义查询SQL输出为：【select * from US_TODO_MODEL t  where t.send_flag = 0 and t.work_flag = 0 and t.enabled =1】
2025-05-30 10:45:00.011 [pool-14-thread-10] WARN  com.simbest.boot.base.repository.CustomDynamicWhere.queryForList Line:50 - 自定义查询SQL输出为：【select * from US_TODO_MODEL t  where t.send_flag = 0 and t.work_flag = 1 and t.enabled =1】
2025-05-30 10:46:35.772 [http-nio-8092-exec-12] WARN  com.simbest.boot.security.auth.service.impl.AuthUserCacheServiceImpl.loadCacheUser Line:118 - 无法通过关键字【auth:user:chenhong】读取用户主键ID
2025-05-30 10:46:36.032 [http-nio-8092-exec-12] WARN  com.simbest.boot.security.auth.service.impl.AuthUserCacheServiceImpl.saveOrUpdateCacheUser Line:64 - 即将在缓存中将用户权限置为空----SET AuthPermissions EMPTY----【12704】
2025-05-30 10:46:55.500 [http-nio-8092-exec-20] WARN  com.simbest.boot.security.auth.service.impl.AuthUserCacheServiceImpl.loadCacheUser Line:118 - 无法通过关键字【auth:user:kongzhijie】读取用户主键ID
2025-05-30 10:46:55.752 [http-nio-8092-exec-20] WARN  com.simbest.boot.security.auth.service.impl.AuthUserCacheServiceImpl.saveOrUpdateCacheUser Line:64 - 即将在缓存中将用户权限置为空----SET AuthPermissions EMPTY----【10683】
2025-05-30 10:47:31.503 [http-nio-8092-exec-10] WARN  com.simbest.boot.util.AppFileUtil.getFileName Line:218 - 文件URL路径【组 2.png】无法通过URL获取路径，直接通过工具类提取文件全称为【组 2.png】
2025-05-30 10:47:31.504 [http-nio-8092-exec-10] WARN  com.simbest.boot.util.AppFileUtil.getFileSuffix Line:274 - 文件URL路径【组 2.png】无法通过URL获取路径，直接通过工具类提取文件后缀为【png】
2025-05-30 10:47:31.509 [http-nio-8092-exec-10] WARN  com.simbest.boot.util.AppFileUtil.getFileBaseName Line:251 - 文件URL路径【组 2.png】无法通过URL获取路径，直接通过工具类提取文件名称为【组 2】
2025-05-30 10:47:31.509 [http-nio-8092-exec-10] WARN  com.simbest.boot.util.AppFileUtil.getFileSuffix Line:274 - 文件URL路径【组 2.png】无法通过URL获取路径，直接通过工具类提取文件后缀为【png】
2025-05-30 10:47:31.521 [http-nio-8092-exec-10] WARN  com.simbest.boot.util.AppFileUtil.getFileSuffix Line:274 - 文件URL路径【组 2.png】无法通过URL获取路径，直接通过工具类提取文件后缀为【png】
2025-05-30 10:47:41.363 [http-nio-8092-exec-2] WARN  com.simbest.boot.bps.process.operate.WfProcessManager.startProcessAndSetRelativeData Line:87 - 流程启动调用BPS流程启动接口用时：3908ms
2025-05-30 10:47:42.010 [http-nio-8092-exec-2] WARN  com.simbest.boot.bps.process.operate.WfProcessManager.startProcessAndSetRelativeData Line:107 - 【BPS Driver】流程启动第一个工作项的状态：【10】
2025-05-30 10:47:42.011 [http-nio-8092-exec-2] WARN  com.simbest.boot.bps.process.operate.WfProcessManager.startProcessAndSetRelativeData Line:118 - 流程启动调用BPS流程工作项查询接口用时：647ms
2025-05-30 10:47:42.171 [http-nio-8092-exec-2] WARN  com.simbest.boot.bps.process.operate.WorkItemManager.finishWorkItemWithRelativeData Line:169 - 流程下一步传递的参数为：{"code":"E1445855282764406784","currentUserCode":"chenhong","inputUserId":"kongzhijie","appCode":"hnjjwz","title":"234234","receiptId":"U870618630754701312","outcome":"hnjjwz.depart_manager"}
2025-05-30 10:47:42.460 [http-nio-8092-exec-2] WARN  com.simbest.boot.bps.unifiedtodo.service.impl.CloseUnifiedTodoServiceImpl.closeTodo Line:68 - 统一待办核销状态：Y
2025-05-30 10:47:42.460 [http-nio-8092-exec-2] WARN  com.simbest.boot.bps.common.HttpNagentDataUtil.queryAgent Line:76 - 获取接口配置对象1：SimpleConfig(id=AC486600246105735169, address=http://************:8088/ntydl, interfaceStyle=ntydl, isOpen=true, isInner=true, provider=北京晟壁)
2025-05-30 10:47:42.461 [http-nio-8092-exec-2] WARN  com.simbest.boot.bps.common.HttpNagentDataUtil.queryAgent Line:80 - 获取接口配置对象2：SimpleConfig(id=AC486600246105735169, address=http://************:8088/ntydl, interfaceStyle=ntydl, isOpen=true, isInner=true, provider=北京晟壁)
2025-05-30 10:47:42.997 [http-nio-8092-exec-2] WARN  com.simbest.boot.bps.process.operate.WorkItemManager.finishWorkItemWithRelativeData Line:264 - 下一个审批人是：kongzhijie，代理人为：[]
2025-05-30 10:47:42.998 [http-nio-8092-exec-2] WARN  com.simbest.boot.bps.process.operate.WorkItemManager.finishWorkItemWithRelativeData Line:275 - 流程提交下一步处理代理人用时：826ms
2025-05-30 10:47:49.147 [http-nio-8092-exec-2] WARN  com.simbest.boot.bps.process.operate.WorkItemManager.finishWorkItemWithRelativeData Line:293 - 流程提交下一步调用BPS流程引擎提交下一步接口用时：6149ms
2025-05-30 10:48:42.352 [http-nio-8092-exec-18] WARN  com.simbest.boot.bps.process.operate.WfProcessManager.startProcessAndSetRelativeData Line:87 - 流程启动调用BPS流程启动接口用时：3593ms
2025-05-30 10:48:42.543 [http-nio-8092-exec-18] WARN  com.simbest.boot.bps.process.operate.WfProcessManager.startProcessAndSetRelativeData Line:107 - 【BPS Driver】流程启动第一个工作项的状态：【10】
2025-05-30 10:48:42.544 [http-nio-8092-exec-18] WARN  com.simbest.boot.bps.process.operate.WfProcessManager.startProcessAndSetRelativeData Line:118 - 流程启动调用BPS流程工作项查询接口用时：188ms
2025-05-30 10:48:42.574 [http-nio-8092-exec-18] WARN  com.simbest.boot.bps.process.operate.WorkItemManager.finishWorkItemWithRelativeData Line:169 - 流程下一步传递的参数为：{"code":"A1445855541146116096","currentUserCode":"kongzhijie","inputUserId":"chenhong","appCode":"hnjjwz","title":"345345","receiptId":"U870618889081884672","outcome":"hnjjwz.depart_manager"}
2025-05-30 10:48:42.869 [http-nio-8092-exec-18] WARN  com.simbest.boot.bps.unifiedtodo.service.impl.CloseUnifiedTodoServiceImpl.closeTodo Line:68 - 统一待办核销状态：Y
2025-05-30 10:48:42.871 [http-nio-8092-exec-18] WARN  com.simbest.boot.bps.common.HttpNagentDataUtil.queryAgent Line:76 - 获取接口配置对象1：SimpleConfig(id=AC486600246105735169, address=http://************:8088/ntydl, interfaceStyle=ntydl, isOpen=true, isInner=true, provider=北京晟壁)
2025-05-30 10:48:42.871 [http-nio-8092-exec-18] WARN  com.simbest.boot.bps.common.HttpNagentDataUtil.queryAgent Line:80 - 获取接口配置对象2：SimpleConfig(id=AC486600246105735169, address=http://************:8088/ntydl, interfaceStyle=ntydl, isOpen=true, isInner=true, provider=北京晟壁)
2025-05-30 10:48:43.397 [http-nio-8092-exec-18] WARN  com.simbest.boot.bps.process.operate.WorkItemManager.finishWorkItemWithRelativeData Line:264 - 下一个审批人是：chenhong，代理人为：[]
2025-05-30 10:48:43.398 [http-nio-8092-exec-18] WARN  com.simbest.boot.bps.process.operate.WorkItemManager.finishWorkItemWithRelativeData Line:275 - 流程提交下一步处理代理人用时：823ms
2025-05-30 10:48:48.536 [http-nio-8092-exec-18] WARN  com.simbest.boot.bps.process.operate.WorkItemManager.finishWorkItemWithRelativeData Line:293 - 流程提交下一步调用BPS流程引擎提交下一步接口用时：5137ms
2025-05-30 10:50:00.012 [pool-14-thread-2] WARN  com.simbest.boot.base.repository.CustomDynamicWhere.queryForList Line:50 - 自定义查询SQL输出为：【select * from US_TODO_MODEL t  where t.send_flag = 0 and t.work_flag = 0 and t.enabled =1】
2025-05-30 10:50:00.018 [pool-14-thread-10] WARN  com.simbest.boot.base.repository.CustomDynamicWhere.queryForList Line:50 - 自定义查询SQL输出为：【select * from US_TODO_MODEL t  where t.send_flag = 0 and t.work_flag = 1 and t.enabled =1】
2025-05-30 10:55:00.018 [pool-14-thread-5] WARN  com.simbest.boot.base.repository.CustomDynamicWhere.queryForList Line:50 - 自定义查询SQL输出为：【select * from US_TODO_MODEL t  where t.send_flag = 0 and t.work_flag = 1 and t.enabled =1】
2025-05-30 10:55:00.019 [pool-14-thread-9] WARN  com.simbest.boot.base.repository.CustomDynamicWhere.queryForList Line:50 - 自定义查询SQL输出为：【select * from US_TODO_MODEL t  where t.send_flag = 0 and t.work_flag = 0 and t.enabled =1】
2025-05-30 11:00:00.010 [pool-14-thread-10] WARN  com.simbest.boot.base.repository.CustomDynamicWhere.queryForList Line:50 - 自定义查询SQL输出为：【select * from US_TODO_MODEL t  where t.send_flag = 0 and t.work_flag = 1 and t.enabled =1】
2025-05-30 11:00:00.012 [pool-14-thread-5] WARN  com.simbest.boot.cmcc.a4.task.A4TaskCreateXmlFile.execute Line:76 - >>>>>>>>>>>>>>>>>>>>>>>>>>>>>>>>>>>>>>>>>>>>>>>>>>>>>A4TaskCreateXmlFile:执行定时任务但不是Prd环境【2025-05-30T11:00:00.012】
2025-05-30 11:00:00.012 [pool-14-thread-2] WARN  com.simbest.boot.base.repository.CustomDynamicWhere.queryForList Line:50 - 自定义查询SQL输出为：【select * from US_TODO_MODEL t  where t.send_flag = 0 and t.work_flag = 0 and t.enabled =1】
2025-05-30 11:05:00.015 [pool-14-thread-3] WARN  com.simbest.boot.base.repository.CustomDynamicWhere.queryForList Line:50 - 自定义查询SQL输出为：【select * from US_TODO_MODEL t  where t.send_flag = 0 and t.work_flag = 0 and t.enabled =1】
2025-05-30 11:05:00.256 [pool-14-thread-1] WARN  com.simbest.boot.base.repository.CustomDynamicWhere.queryForList Line:50 - 自定义查询SQL输出为：【select * from US_TODO_MODEL t  where t.send_flag = 0 and t.work_flag = 1 and t.enabled =1】
2025-05-30 11:10:00.019 [pool-14-thread-3] WARN  com.simbest.boot.base.repository.CustomDynamicWhere.queryForList Line:50 - 自定义查询SQL输出为：【select * from US_TODO_MODEL t  where t.send_flag = 0 and t.work_flag = 0 and t.enabled =1】
2025-05-30 11:10:00.020 [pool-14-thread-10] WARN  com.simbest.boot.base.repository.CustomDynamicWhere.queryForList Line:50 - 自定义查询SQL输出为：【select * from US_TODO_MODEL t  where t.send_flag = 0 and t.work_flag = 1 and t.enabled =1】
2025-05-30 11:15:00.016 [pool-14-thread-1] WARN  com.simbest.boot.base.repository.CustomDynamicWhere.queryForList Line:50 - 自定义查询SQL输出为：【select * from US_TODO_MODEL t  where t.send_flag = 0 and t.work_flag = 1 and t.enabled =1】
2025-05-30 11:15:00.016 [pool-14-thread-3] WARN  com.simbest.boot.base.repository.CustomDynamicWhere.queryForList Line:50 - 自定义查询SQL输出为：【select * from US_TODO_MODEL t  where t.send_flag = 0 and t.work_flag = 0 and t.enabled =1】
2025-05-30 11:20:00.021 [pool-14-thread-9] WARN  com.simbest.boot.base.repository.CustomDynamicWhere.queryForList Line:50 - 自定义查询SQL输出为：【select * from US_TODO_MODEL t  where t.send_flag = 0 and t.work_flag = 0 and t.enabled =1】
2025-05-30 11:20:00.022 [pool-14-thread-5] WARN  com.simbest.boot.base.repository.CustomDynamicWhere.queryForList Line:50 - 自定义查询SQL输出为：【select * from US_TODO_MODEL t  where t.send_flag = 0 and t.work_flag = 1 and t.enabled =1】
2025-05-30 11:25:00.010 [pool-14-thread-4] WARN  com.simbest.boot.base.repository.CustomDynamicWhere.queryForList Line:50 - 自定义查询SQL输出为：【select * from US_TODO_MODEL t  where t.send_flag = 0 and t.work_flag = 1 and t.enabled =1】
2025-05-30 11:25:00.011 [pool-14-thread-1] WARN  com.simbest.boot.base.repository.CustomDynamicWhere.queryForList Line:50 - 自定义查询SQL输出为：【select * from US_TODO_MODEL t  where t.send_flag = 0 and t.work_flag = 0 and t.enabled =1】
2025-05-30 11:30:00.012 [pool-14-thread-3] WARN  com.simbest.boot.base.repository.CustomDynamicWhere.queryForList Line:50 - 自定义查询SQL输出为：【select * from US_TODO_MODEL t  where t.send_flag = 0 and t.work_flag = 0 and t.enabled =1】
2025-05-30 11:30:00.019 [pool-14-thread-5] WARN  com.simbest.boot.base.repository.CustomDynamicWhere.queryForList Line:50 - 自定义查询SQL输出为：【select * from US_TODO_MODEL t  where t.send_flag = 0 and t.work_flag = 1 and t.enabled =1】
2025-05-30 11:35:00.022 [pool-14-thread-3] WARN  com.simbest.boot.base.repository.CustomDynamicWhere.queryForList Line:50 - 自定义查询SQL输出为：【select * from US_TODO_MODEL t  where t.send_flag = 0 and t.work_flag = 1 and t.enabled =1】
2025-05-30 11:35:00.022 [pool-14-thread-1] WARN  com.simbest.boot.base.repository.CustomDynamicWhere.queryForList Line:50 - 自定义查询SQL输出为：【select * from US_TODO_MODEL t  where t.send_flag = 0 and t.work_flag = 0 and t.enabled =1】
2025-05-30 11:40:00.019 [pool-14-thread-6] WARN  com.simbest.boot.base.repository.CustomDynamicWhere.queryForList Line:50 - 自定义查询SQL输出为：【select * from US_TODO_MODEL t  where t.send_flag = 0 and t.work_flag = 0 and t.enabled =1】
2025-05-30 11:40:00.021 [pool-14-thread-1] WARN  com.simbest.boot.base.repository.CustomDynamicWhere.queryForList Line:50 - 自定义查询SQL输出为：【select * from US_TODO_MODEL t  where t.send_flag = 0 and t.work_flag = 1 and t.enabled =1】
2025-05-30 11:45:00.014 [pool-14-thread-7] WARN  com.simbest.boot.base.repository.CustomDynamicWhere.queryForList Line:50 - 自定义查询SQL输出为：【select * from US_TODO_MODEL t  where t.send_flag = 0 and t.work_flag = 0 and t.enabled =1】
2025-05-30 11:45:00.014 [pool-14-thread-2] WARN  com.simbest.boot.base.repository.CustomDynamicWhere.queryForList Line:50 - 自定义查询SQL输出为：【select * from US_TODO_MODEL t  where t.send_flag = 0 and t.work_flag = 1 and t.enabled =1】
2025-05-30 11:50:00.012 [pool-14-thread-1] WARN  com.simbest.boot.base.repository.CustomDynamicWhere.queryForList Line:50 - 自定义查询SQL输出为：【select * from US_TODO_MODEL t  where t.send_flag = 0 and t.work_flag = 0 and t.enabled =1】
2025-05-30 11:50:00.014 [pool-14-thread-2] WARN  com.simbest.boot.base.repository.CustomDynamicWhere.queryForList Line:50 - 自定义查询SQL输出为：【select * from US_TODO_MODEL t  where t.send_flag = 0 and t.work_flag = 1 and t.enabled =1】
2025-05-30 11:55:00.018 [pool-14-thread-3] WARN  com.simbest.boot.base.repository.CustomDynamicWhere.queryForList Line:50 - 自定义查询SQL输出为：【select * from US_TODO_MODEL t  where t.send_flag = 0 and t.work_flag = 0 and t.enabled =1】
2025-05-30 11:55:00.018 [pool-14-thread-10] WARN  com.simbest.boot.base.repository.CustomDynamicWhere.queryForList Line:50 - 自定义查询SQL输出为：【select * from US_TODO_MODEL t  where t.send_flag = 0 and t.work_flag = 1 and t.enabled =1】
2025-05-30 12:00:00.020 [pool-14-thread-10] WARN  com.simbest.boot.cmcc.a4.task.A4TaskCreateXmlFile.execute Line:76 - >>>>>>>>>>>>>>>>>>>>>>>>>>>>>>>>>>>>>>>>>>>>>>>>>>>>>A4TaskCreateXmlFile:执行定时任务但不是Prd环境【2025-05-30T12:00:00.020】
2025-05-30 12:00:00.021 [pool-14-thread-9] WARN  com.simbest.boot.base.repository.CustomDynamicWhere.queryForList Line:50 - 自定义查询SQL输出为：【select * from US_TODO_MODEL t  where t.send_flag = 0 and t.work_flag = 0 and t.enabled =1】
2025-05-30 12:00:00.022 [pool-14-thread-7] WARN  com.simbest.boot.base.repository.CustomDynamicWhere.queryForList Line:50 - 自定义查询SQL输出为：【select * from US_TODO_MODEL t  where t.send_flag = 0 and t.work_flag = 1 and t.enabled =1】
2025-05-30 12:05:00.011 [pool-14-thread-7] WARN  com.simbest.boot.base.repository.CustomDynamicWhere.queryForList Line:50 - 自定义查询SQL输出为：【select * from US_TODO_MODEL t  where t.send_flag = 0 and t.work_flag = 0 and t.enabled =1】
2025-05-30 12:05:00.011 [pool-14-thread-3] WARN  com.simbest.boot.base.repository.CustomDynamicWhere.queryForList Line:50 - 自定义查询SQL输出为：【select * from US_TODO_MODEL t  where t.send_flag = 0 and t.work_flag = 1 and t.enabled =1】
2025-05-30 12:10:00.015 [pool-14-thread-8] WARN  com.simbest.boot.base.repository.CustomDynamicWhere.queryForList Line:50 - 自定义查询SQL输出为：【select * from US_TODO_MODEL t  where t.send_flag = 0 and t.work_flag = 1 and t.enabled =1】
2025-05-30 12:10:00.016 [pool-14-thread-5] WARN  com.simbest.boot.base.repository.CustomDynamicWhere.queryForList Line:50 - 自定义查询SQL输出为：【select * from US_TODO_MODEL t  where t.send_flag = 0 and t.work_flag = 0 and t.enabled =1】
2025-05-30 12:15:00.013 [pool-14-thread-9] WARN  com.simbest.boot.base.repository.CustomDynamicWhere.queryForList Line:50 - 自定义查询SQL输出为：【select * from US_TODO_MODEL t  where t.send_flag = 0 and t.work_flag = 0 and t.enabled =1】
2025-05-30 12:15:00.017 [pool-14-thread-4] WARN  com.simbest.boot.base.repository.CustomDynamicWhere.queryForList Line:50 - 自定义查询SQL输出为：【select * from US_TODO_MODEL t  where t.send_flag = 0 and t.work_flag = 1 and t.enabled =1】
2025-05-30 12:20:00.013 [pool-14-thread-4] WARN  com.simbest.boot.base.repository.CustomDynamicWhere.queryForList Line:50 - 自定义查询SQL输出为：【select * from US_TODO_MODEL t  where t.send_flag = 0 and t.work_flag = 0 and t.enabled =1】
2025-05-30 12:20:00.013 [pool-14-thread-1] WARN  com.simbest.boot.base.repository.CustomDynamicWhere.queryForList Line:50 - 自定义查询SQL输出为：【select * from US_TODO_MODEL t  where t.send_flag = 0 and t.work_flag = 1 and t.enabled =1】
2025-05-30 12:25:00.009 [pool-14-thread-3] WARN  com.simbest.boot.base.repository.CustomDynamicWhere.queryForList Line:50 - 自定义查询SQL输出为：【select * from US_TODO_MODEL t  where t.send_flag = 0 and t.work_flag = 0 and t.enabled =1】
2025-05-30 12:25:00.009 [pool-14-thread-2] WARN  com.simbest.boot.base.repository.CustomDynamicWhere.queryForList Line:50 - 自定义查询SQL输出为：【select * from US_TODO_MODEL t  where t.send_flag = 0 and t.work_flag = 1 and t.enabled =1】
2025-05-30 12:49:38.757 [lettuce-nioEventLoop-4-5] WARN  io.lettuce.core.protocol.ConnectionWatchdog.log Line:153 - Cannot reconnect to [************:7004]: connection timed out: /************:7004
2025-05-30 12:49:38.757 [lettuce-nioEventLoop-4-4] WARN  io.lettuce.core.protocol.ConnectionWatchdog.log Line:153 - Cannot reconnect to [************:7003]: connection timed out: /************:7003
2025-05-30 12:49:38.773 [lettuce-nioEventLoop-4-7] WARN  io.lettuce.core.protocol.ConnectionWatchdog.log Line:153 - Cannot reconnect to [************:7006]: connection timed out: /************:7006
2025-05-30 12:49:38.773 [lettuce-nioEventLoop-4-6] WARN  io.lettuce.core.protocol.ConnectionWatchdog.log Line:153 - Cannot reconnect to [************:7006]: connection timed out: /************:7006
2025-05-30 12:49:38.958 [lettuce-nioEventLoop-4-8] WARN  io.lettuce.core.protocol.ConnectionWatchdog.log Line:153 - Cannot reconnect to [************:7005]: connection timed out: /************:7005
2025-05-30 12:49:47.475 [AMQP Connection ************:5672] WARN  com.rabbitmq.client.impl.ForgivingExceptionHandler.log Line:115 - An unexpected connection driver error occured (Exception message: Connection reset)
2025-05-30 12:49:48.503 [AMQP Connection ************:5672] WARN  com.rabbitmq.client.impl.ForgivingExceptionHandler.log Line:115 - An unexpected connection driver error occured (Exception message: Connection reset)
2025-05-30 12:49:48.594 [AMQP Connection ************:5672] WARN  com.rabbitmq.client.impl.ForgivingExceptionHandler.log Line:115 - An unexpected connection driver error occured (Exception message: Connection reset)
2025-05-30 12:49:48.625 [AMQP Connection ************:5672] WARN  com.rabbitmq.client.impl.ForgivingExceptionHandler.log Line:115 - An unexpected connection driver error occured (Exception message: Connection reset)
2025-05-30 12:49:48.855 [lettuce-nioEventLoop-4-10] WARN  io.lettuce.core.protocol.ConnectionWatchdog.log Line:153 - Cannot reconnect to [************:7004]: connection timed out: /************:7004
2025-05-30 12:49:48.855 [lettuce-nioEventLoop-4-12] WARN  io.lettuce.core.protocol.ConnectionWatchdog.log Line:153 - Cannot reconnect to [************:7004]: connection timed out: /************:7004
2025-05-30 12:49:48.855 [lettuce-nioEventLoop-4-11] WARN  io.lettuce.core.protocol.ConnectionWatchdog.log Line:153 - Cannot reconnect to [************:7004]: connection timed out: /************:7004
2025-05-30 12:49:48.855 [lettuce-nioEventLoop-4-9] WARN  io.lettuce.core.protocol.ConnectionWatchdog.log Line:153 - Cannot reconnect to [************:7003]: connection timed out: /************:7003
2025-05-30 12:49:49.067 [lettuce-nioEventLoop-4-1] WARN  io.lettuce.core.protocol.ConnectionWatchdog.log Line:153 - Cannot reconnect to [************:7005]: connection timed out: /************:7005
2025-05-30 12:49:58.951 [lettuce-nioEventLoop-4-3] WARN  io.lettuce.core.protocol.ConnectionWatchdog.log Line:153 - Cannot reconnect to [************:7004]: connection timed out: /************:7004
2025-05-30 12:49:58.951 [lettuce-nioEventLoop-4-5] WARN  io.lettuce.core.protocol.ConnectionWatchdog.log Line:153 - Cannot reconnect to [************:7003]: connection timed out: /************:7003
2025-05-30 12:49:58.951 [lettuce-nioEventLoop-4-4] WARN  io.lettuce.core.protocol.ConnectionWatchdog.log Line:153 - Cannot reconnect to [************:7003]: connection timed out: /************:7003
2025-05-30 12:49:58.951 [lettuce-nioEventLoop-4-2] WARN  io.lettuce.core.protocol.ConnectionWatchdog.log Line:153 - Cannot reconnect to [************:7003]: connection timed out: /************:7003
2025-05-30 12:49:58.953 [lettuce-nioEventLoop-4-7] WARN  io.lettuce.core.cluster.topology.ClusterTopologyRefresh.lambda$getConnections$0 Line:284 - Unable to connect to [************:7002]: connection timed out: /************:7002
2025-05-30 12:49:58.953 [lettuce-nioEventLoop-4-9] WARN  io.lettuce.core.cluster.topology.ClusterTopologyRefresh.lambda$getConnections$0 Line:284 - Unable to connect to [************:7004]: connection timed out: /************:7004
2025-05-30 12:49:58.953 [lettuce-nioEventLoop-4-8] WARN  io.lettuce.core.cluster.topology.ClusterTopologyRefresh.lambda$getConnections$0 Line:284 - Unable to connect to [************:7003]: connection timed out: /************:7003
2025-05-30 12:49:58.953 [lettuce-nioEventLoop-4-10] WARN  io.lettuce.core.cluster.topology.ClusterTopologyRefresh.lambda$getConnections$0 Line:284 - Unable to connect to [************:7005]: connection timed out: /************:7005
2025-05-30 12:49:58.953 [lettuce-nioEventLoop-4-11] WARN  io.lettuce.core.cluster.topology.ClusterTopologyRefresh.lambda$getConnections$0 Line:284 - Unable to connect to [************:7006]: connection timed out: /************:7006
2025-05-30 12:49:58.953 [lettuce-nioEventLoop-4-6] WARN  io.lettuce.core.cluster.topology.ClusterTopologyRefresh.lambda$getConnections$0 Line:284 - Unable to connect to [************:7001]: connection timed out: /************:7001
2025-05-30 12:49:59.167 [lettuce-nioEventLoop-4-12] WARN  io.lettuce.core.protocol.ConnectionWatchdog.log Line:153 - Cannot reconnect to [************:7005]: connection timed out: /************:7005
2025-05-30 12:50:13.795 [org.springframework.amqp.rabbit.RabbitListenerEndpointContainer#0-2] WARN  org.springframework.amqp.rabbit.listener.SimpleMessageListenerContainer.logConsumerException Line:1440 - Consumer raised exception, processing can restart if the connection factory supports it. Exception summary: org.springframework.amqp.AmqpConnectException: java.net.ConnectException: Connection timed out: connect
2025-05-30 12:50:14.795 [org.springframework.amqp.rabbit.RabbitListenerEndpointContainer#1-2] WARN  org.springframework.amqp.rabbit.listener.SimpleMessageListenerContainer.logConsumerException Line:1440 - Consumer raised exception, processing can restart if the connection factory supports it. Exception summary: org.springframework.amqp.AmqpConnectException: java.net.ConnectException: Connection timed out: connect
2025-05-30 12:50:15.009 [org.springframework.amqp.rabbit.RabbitListenerEndpointContainer#3-2] WARN  org.springframework.amqp.rabbit.listener.SimpleMessageListenerContainer.logConsumerException Line:1440 - Consumer raised exception, processing can restart if the connection factory supports it. Exception summary: org.springframework.amqp.AmqpConnectException: java.net.ConnectException: Connection timed out: connect
2025-05-30 13:30:34.990 [AMQP Connection ************:5672] WARN  com.rabbitmq.client.impl.ForgivingExceptionHandler.log Line:115 - An unexpected connection driver error occured (Exception message: Connection reset)
2025-05-30 13:30:44.201 [AMQP Connection ************:5672] WARN  com.rabbitmq.client.impl.ForgivingExceptionHandler.log Line:115 - An unexpected connection driver error occured (Exception message: Connection reset)
2025-05-30 13:30:45.209 [AMQP Connection ************:5672] WARN  com.rabbitmq.client.impl.ForgivingExceptionHandler.log Line:115 - An unexpected connection driver error occured (Exception message: Connection reset)
2025-05-30 13:30:45.426 [AMQP Connection ************:5672] WARN  com.rabbitmq.client.impl.ForgivingExceptionHandler.log Line:115 - An unexpected connection driver error occured (Exception message: Connection reset)
2025-05-30 13:45:00.020 [pool-14-thread-5] WARN  com.simbest.boot.base.repository.CustomDynamicWhere.queryForList Line:50 - 自定义查询SQL输出为：【select * from US_TODO_MODEL t  where t.send_flag = 0 and t.work_flag = 0 and t.enabled =1】
2025-05-30 13:50:00.018 [pool-14-thread-9] WARN  com.simbest.boot.base.repository.CustomDynamicWhere.queryForList Line:50 - 自定义查询SQL输出为：【select * from US_TODO_MODEL t  where t.send_flag = 0 and t.work_flag = 0 and t.enabled =1】
2025-05-30 14:03:55.815 [AMQP Connection ************:5672] WARN  com.rabbitmq.client.impl.ForgivingExceptionHandler.log Line:115 - An unexpected connection driver error occured (Exception message: Connection reset)
2025-05-30 14:04:04.955 [AMQP Connection ************:5672] WARN  com.rabbitmq.client.impl.ForgivingExceptionHandler.log Line:115 - An unexpected connection driver error occured (Exception message: Connection reset)
2025-05-30 14:04:05.925 [AMQP Connection ************:5672] WARN  com.rabbitmq.client.impl.ForgivingExceptionHandler.log Line:115 - An unexpected connection driver error occured (Exception message: Connection reset)
2025-05-30 14:04:06.204 [AMQP Connection ************:5672] WARN  com.rabbitmq.client.impl.ForgivingExceptionHandler.log Line:115 - An unexpected connection driver error occured (Exception message: Connection reset)
2025-05-30 14:10:00.008 [pool-14-thread-6] WARN  com.simbest.boot.base.repository.CustomDynamicWhere.queryForList Line:50 - 自定义查询SQL输出为：【select * from US_TODO_MODEL t  where t.send_flag = 0 and t.work_flag = 0 and t.enabled =1】
2025-05-30 14:10:09.257 [pool-14-thread-10] WARN  com.simbest.boot.base.repository.CustomDynamicWhere.queryForList Line:50 - 自定义查询SQL输出为：【select * from US_TODO_MODEL t  where t.send_flag = 0 and t.work_flag = 1 and t.enabled =1】
2025-05-30 14:15:00.021 [pool-14-thread-9] WARN  com.simbest.boot.base.repository.CustomDynamicWhere.queryForList Line:50 - 自定义查询SQL输出为：【select * from US_TODO_MODEL t  where t.send_flag = 0 and t.work_flag = 1 and t.enabled =1】
2025-05-30 14:15:00.031 [pool-14-thread-1] WARN  com.simbest.boot.base.repository.CustomDynamicWhere.queryForList Line:50 - 自定义查询SQL输出为：【select * from US_TODO_MODEL t  where t.send_flag = 0 and t.work_flag = 0 and t.enabled =1】
2025-05-30 14:20:00.013 [pool-14-thread-1] WARN  com.simbest.boot.base.repository.CustomDynamicWhere.queryForList Line:50 - 自定义查询SQL输出为：【select * from US_TODO_MODEL t  where t.send_flag = 0 and t.work_flag = 1 and t.enabled =1】
2025-05-30 14:20:00.016 [pool-14-thread-3] WARN  com.simbest.boot.base.repository.CustomDynamicWhere.queryForList Line:50 - 自定义查询SQL输出为：【select * from US_TODO_MODEL t  where t.send_flag = 0 and t.work_flag = 0 and t.enabled =1】
2025-05-30 14:25:00.020 [pool-14-thread-10] WARN  com.simbest.boot.base.repository.CustomDynamicWhere.queryForList Line:50 - 自定义查询SQL输出为：【select * from US_TODO_MODEL t  where t.send_flag = 0 and t.work_flag = 1 and t.enabled =1】
2025-05-30 14:25:00.020 [pool-14-thread-3] WARN  com.simbest.boot.base.repository.CustomDynamicWhere.queryForList Line:50 - 自定义查询SQL输出为：【select * from US_TODO_MODEL t  where t.send_flag = 0 and t.work_flag = 0 and t.enabled =1】
2025-05-30 14:30:00.012 [pool-14-thread-4] WARN  com.simbest.boot.base.repository.CustomDynamicWhere.queryForList Line:50 - 自定义查询SQL输出为：【select * from US_TODO_MODEL t  where t.send_flag = 0 and t.work_flag = 0 and t.enabled =1】
2025-05-30 14:30:00.018 [pool-14-thread-3] WARN  com.simbest.boot.base.repository.CustomDynamicWhere.queryForList Line:50 - 自定义查询SQL输出为：【select * from US_TODO_MODEL t  where t.send_flag = 0 and t.work_flag = 1 and t.enabled =1】
2025-05-30 14:35:00.012 [pool-14-thread-7] WARN  com.simbest.boot.base.repository.CustomDynamicWhere.queryForList Line:50 - 自定义查询SQL输出为：【select * from US_TODO_MODEL t  where t.send_flag = 0 and t.work_flag = 0 and t.enabled =1】
2025-05-30 14:35:00.016 [pool-14-thread-4] WARN  com.simbest.boot.base.repository.CustomDynamicWhere.queryForList Line:50 - 自定义查询SQL输出为：【select * from US_TODO_MODEL t  where t.send_flag = 0 and t.work_flag = 1 and t.enabled =1】
2025-05-30 14:40:00.020 [pool-14-thread-9] WARN  com.simbest.boot.base.repository.CustomDynamicWhere.queryForList Line:50 - 自定义查询SQL输出为：【select * from US_TODO_MODEL t  where t.send_flag = 0 and t.work_flag = 1 and t.enabled =1】
2025-05-30 14:40:00.020 [pool-14-thread-1] WARN  com.simbest.boot.base.repository.CustomDynamicWhere.queryForList Line:50 - 自定义查询SQL输出为：【select * from US_TODO_MODEL t  where t.send_flag = 0 and t.work_flag = 0 and t.enabled =1】
2025-05-30 14:45:00.010 [pool-14-thread-10] WARN  com.simbest.boot.base.repository.CustomDynamicWhere.queryForList Line:50 - 自定义查询SQL输出为：【select * from US_TODO_MODEL t  where t.send_flag = 0 and t.work_flag = 1 and t.enabled =1】
2025-05-30 14:45:00.011 [pool-14-thread-5] WARN  com.simbest.boot.base.repository.CustomDynamicWhere.queryForList Line:50 - 自定义查询SQL输出为：【select * from US_TODO_MODEL t  where t.send_flag = 0 and t.work_flag = 0 and t.enabled =1】
2025-05-30 14:50:00.012 [pool-14-thread-2] WARN  com.simbest.boot.base.repository.CustomDynamicWhere.queryForList Line:50 - 自定义查询SQL输出为：【select * from US_TODO_MODEL t  where t.send_flag = 0 and t.work_flag = 1 and t.enabled =1】
2025-05-30 14:50:00.015 [pool-14-thread-6] WARN  com.simbest.boot.base.repository.CustomDynamicWhere.queryForList Line:50 - 自定义查询SQL输出为：【select * from US_TODO_MODEL t  where t.send_flag = 0 and t.work_flag = 0 and t.enabled =1】
2025-05-30 14:55:00.012 [pool-14-thread-6] WARN  com.simbest.boot.base.repository.CustomDynamicWhere.queryForList Line:50 - 自定义查询SQL输出为：【select * from US_TODO_MODEL t  where t.send_flag = 0 and t.work_flag = 0 and t.enabled =1】
2025-05-30 14:55:00.024 [pool-14-thread-1] WARN  com.simbest.boot.base.repository.CustomDynamicWhere.queryForList Line:50 - 自定义查询SQL输出为：【select * from US_TODO_MODEL t  where t.send_flag = 0 and t.work_flag = 1 and t.enabled =1】
2025-05-30 15:00:00.018 [pool-14-thread-3] WARN  com.simbest.boot.base.repository.CustomDynamicWhere.queryForList Line:50 - 自定义查询SQL输出为：【select * from US_TODO_MODEL t  where t.send_flag = 0 and t.work_flag = 0 and t.enabled =1】
2025-05-30 15:00:00.019 [pool-14-thread-4] WARN  com.simbest.boot.base.repository.CustomDynamicWhere.queryForList Line:50 - 自定义查询SQL输出为：【select * from US_TODO_MODEL t  where t.send_flag = 0 and t.work_flag = 1 and t.enabled =1】
2025-05-30 15:00:00.020 [pool-14-thread-2] WARN  com.simbest.boot.cmcc.a4.task.A4TaskCreateXmlFile.execute Line:76 - >>>>>>>>>>>>>>>>>>>>>>>>>>>>>>>>>>>>>>>>>>>>>>>>>>>>>A4TaskCreateXmlFile:执行定时任务但不是Prd环境【2025-05-30T15:00:00.020】
2025-05-30 15:05:00.012 [pool-14-thread-3] WARN  com.simbest.boot.base.repository.CustomDynamicWhere.queryForList Line:50 - 自定义查询SQL输出为：【select * from US_TODO_MODEL t  where t.send_flag = 0 and t.work_flag = 1 and t.enabled =1】
2025-05-30 15:05:00.016 [pool-14-thread-1] WARN  com.simbest.boot.base.repository.CustomDynamicWhere.queryForList Line:50 - 自定义查询SQL输出为：【select * from US_TODO_MODEL t  where t.send_flag = 0 and t.work_flag = 0 and t.enabled =1】
2025-05-30 15:10:00.014 [pool-14-thread-2] WARN  com.simbest.boot.base.repository.CustomDynamicWhere.queryForList Line:50 - 自定义查询SQL输出为：【select * from US_TODO_MODEL t  where t.send_flag = 0 and t.work_flag = 0 and t.enabled =1】
2025-05-30 15:10:00.015 [pool-14-thread-10] WARN  com.simbest.boot.base.repository.CustomDynamicWhere.queryForList Line:50 - 自定义查询SQL输出为：【select * from US_TODO_MODEL t  where t.send_flag = 0 and t.work_flag = 1 and t.enabled =1】
2025-05-30 15:15:00.008 [pool-14-thread-2] WARN  com.simbest.boot.base.repository.CustomDynamicWhere.queryForList Line:50 - 自定义查询SQL输出为：【select * from US_TODO_MODEL t  where t.send_flag = 0 and t.work_flag = 1 and t.enabled =1】
2025-05-30 15:15:00.009 [pool-14-thread-7] WARN  com.simbest.boot.base.repository.CustomDynamicWhere.queryForList Line:50 - 自定义查询SQL输出为：【select * from US_TODO_MODEL t  where t.send_flag = 0 and t.work_flag = 0 and t.enabled =1】
2025-05-30 15:20:00.018 [pool-14-thread-3] WARN  com.simbest.boot.base.repository.CustomDynamicWhere.queryForList Line:50 - 自定义查询SQL输出为：【select * from US_TODO_MODEL t  where t.send_flag = 0 and t.work_flag = 0 and t.enabled =1】
2025-05-30 15:20:00.019 [pool-14-thread-4] WARN  com.simbest.boot.base.repository.CustomDynamicWhere.queryForList Line:50 - 自定义查询SQL输出为：【select * from US_TODO_MODEL t  where t.send_flag = 0 and t.work_flag = 1 and t.enabled =1】
2025-05-30 15:23:12.832 [http-nio-8092-exec-13] WARN  com.simbest.boot.util.AppFileUtil.getFileName Line:218 - 文件URL路径【组 2.png】无法通过URL获取路径，直接通过工具类提取文件全称为【组 2.png】
2025-05-30 15:23:12.833 [http-nio-8092-exec-13] WARN  com.simbest.boot.util.AppFileUtil.getFileSuffix Line:274 - 文件URL路径【组 2.png】无法通过URL获取路径，直接通过工具类提取文件后缀为【png】
2025-05-30 15:23:12.836 [http-nio-8092-exec-13] WARN  com.simbest.boot.util.AppFileUtil.getFileBaseName Line:251 - 文件URL路径【组 2.png】无法通过URL获取路径，直接通过工具类提取文件名称为【组 2】
2025-05-30 15:23:12.836 [http-nio-8092-exec-13] WARN  com.simbest.boot.util.AppFileUtil.getFileSuffix Line:274 - 文件URL路径【组 2.png】无法通过URL获取路径，直接通过工具类提取文件后缀为【png】
2025-05-30 15:23:12.844 [http-nio-8092-exec-13] WARN  com.simbest.boot.util.AppFileUtil.getFileSuffix Line:274 - 文件URL路径【组 2.png】无法通过URL获取路径，直接通过工具类提取文件后缀为【png】
2025-05-30 15:23:28.902 [http-nio-8092-exec-11] WARN  com.simbest.boot.util.AppFileUtil.getFileName Line:218 - 文件URL路径【组 3.png】无法通过URL获取路径，直接通过工具类提取文件全称为【组 3.png】
2025-05-30 15:23:28.902 [http-nio-8092-exec-11] WARN  com.simbest.boot.util.AppFileUtil.getFileSuffix Line:274 - 文件URL路径【组 3.png】无法通过URL获取路径，直接通过工具类提取文件后缀为【png】
2025-05-30 15:23:28.904 [http-nio-8092-exec-11] WARN  com.simbest.boot.util.AppFileUtil.getFileBaseName Line:251 - 文件URL路径【组 3.png】无法通过URL获取路径，直接通过工具类提取文件名称为【组 3】
2025-05-30 15:23:28.905 [http-nio-8092-exec-11] WARN  com.simbest.boot.util.AppFileUtil.getFileSuffix Line:274 - 文件URL路径【组 3.png】无法通过URL获取路径，直接通过工具类提取文件后缀为【png】
2025-05-30 15:23:28.913 [http-nio-8092-exec-11] WARN  com.simbest.boot.util.AppFileUtil.getFileSuffix Line:274 - 文件URL路径【组 3.png】无法通过URL获取路径，直接通过工具类提取文件后缀为【png】
2025-05-30 15:25:00.007 [pool-14-thread-8] WARN  com.simbest.boot.base.repository.CustomDynamicWhere.queryForList Line:50 - 自定义查询SQL输出为：【select * from US_TODO_MODEL t  where t.send_flag = 0 and t.work_flag = 1 and t.enabled =1】
2025-05-30 15:25:00.007 [pool-14-thread-9] WARN  com.simbest.boot.base.repository.CustomDynamicWhere.queryForList Line:50 - 自定义查询SQL输出为：【select * from US_TODO_MODEL t  where t.send_flag = 0 and t.work_flag = 0 and t.enabled =1】
2025-05-30 15:25:06.386 [http-nio-8092-exec-1] WARN  com.simbest.boot.util.AppFileUtil.getFileName Line:218 - 文件URL路径【组 2.png】无法通过URL获取路径，直接通过工具类提取文件全称为【组 2.png】
2025-05-30 15:25:06.386 [http-nio-8092-exec-1] WARN  com.simbest.boot.util.AppFileUtil.getFileSuffix Line:274 - 文件URL路径【组 2.png】无法通过URL获取路径，直接通过工具类提取文件后缀为【png】
2025-05-30 15:25:06.388 [http-nio-8092-exec-1] WARN  com.simbest.boot.util.AppFileUtil.getFileBaseName Line:251 - 文件URL路径【组 2.png】无法通过URL获取路径，直接通过工具类提取文件名称为【组 2】
2025-05-30 15:25:06.388 [http-nio-8092-exec-1] WARN  com.simbest.boot.util.AppFileUtil.getFileSuffix Line:274 - 文件URL路径【组 2.png】无法通过URL获取路径，直接通过工具类提取文件后缀为【png】
2025-05-30 15:25:06.396 [http-nio-8092-exec-1] WARN  com.simbest.boot.util.AppFileUtil.getFileSuffix Line:274 - 文件URL路径【组 2.png】无法通过URL获取路径，直接通过工具类提取文件后缀为【png】
2025-05-30 15:30:00.012 [pool-14-thread-6] WARN  com.simbest.boot.base.repository.CustomDynamicWhere.queryForList Line:50 - 自定义查询SQL输出为：【select * from US_TODO_MODEL t  where t.send_flag = 0 and t.work_flag = 0 and t.enabled =1】
2025-05-30 15:30:00.023 [pool-14-thread-1] WARN  com.simbest.boot.base.repository.CustomDynamicWhere.queryForList Line:50 - 自定义查询SQL输出为：【select * from US_TODO_MODEL t  where t.send_flag = 0 and t.work_flag = 1 and t.enabled =1】
2025-05-30 15:35:00.020 [pool-14-thread-7] WARN  com.simbest.boot.base.repository.CustomDynamicWhere.queryForList Line:50 - 自定义查询SQL输出为：【select * from US_TODO_MODEL t  where t.send_flag = 0 and t.work_flag = 1 and t.enabled =1】
2025-05-30 15:35:00.020 [pool-14-thread-8] WARN  com.simbest.boot.base.repository.CustomDynamicWhere.queryForList Line:50 - 自定义查询SQL输出为：【select * from US_TODO_MODEL t  where t.send_flag = 0 and t.work_flag = 0 and t.enabled =1】
2025-05-30 15:40:00.018 [pool-14-thread-4] WARN  com.simbest.boot.base.repository.CustomDynamicWhere.queryForList Line:50 - 自定义查询SQL输出为：【select * from US_TODO_MODEL t  where t.send_flag = 0 and t.work_flag = 1 and t.enabled =1】
2025-05-30 15:40:00.031 [pool-14-thread-8] WARN  com.simbest.boot.base.repository.CustomDynamicWhere.queryForList Line:50 - 自定义查询SQL输出为：【select * from US_TODO_MODEL t  where t.send_flag = 0 and t.work_flag = 0 and t.enabled =1】
2025-05-30 15:45:00.014 [pool-14-thread-9] WARN  com.simbest.boot.base.repository.CustomDynamicWhere.queryForList Line:50 - 自定义查询SQL输出为：【select * from US_TODO_MODEL t  where t.send_flag = 0 and t.work_flag = 0 and t.enabled =1】
2025-05-30 15:45:00.021 [pool-14-thread-8] WARN  com.simbest.boot.base.repository.CustomDynamicWhere.queryForList Line:50 - 自定义查询SQL输出为：【select * from US_TODO_MODEL t  where t.send_flag = 0 and t.work_flag = 1 and t.enabled =1】
2025-05-30 15:50:00.009 [pool-14-thread-4] WARN  com.simbest.boot.base.repository.CustomDynamicWhere.queryForList Line:50 - 自定义查询SQL输出为：【select * from US_TODO_MODEL t  where t.send_flag = 0 and t.work_flag = 1 and t.enabled =1】
2025-05-30 15:50:00.015 [pool-14-thread-7] WARN  com.simbest.boot.base.repository.CustomDynamicWhere.queryForList Line:50 - 自定义查询SQL输出为：【select * from US_TODO_MODEL t  where t.send_flag = 0 and t.work_flag = 0 and t.enabled =1】
2025-05-30 15:55:00.018 [pool-14-thread-7] WARN  com.simbest.boot.base.repository.CustomDynamicWhere.queryForList Line:50 - 自定义查询SQL输出为：【select * from US_TODO_MODEL t  where t.send_flag = 0 and t.work_flag = 0 and t.enabled =1】
2025-05-30 15:55:00.020 [pool-14-thread-3] WARN  com.simbest.boot.base.repository.CustomDynamicWhere.queryForList Line:50 - 自定义查询SQL输出为：【select * from US_TODO_MODEL t  where t.send_flag = 0 and t.work_flag = 1 and t.enabled =1】
2025-05-30 16:00:00.018 [pool-14-thread-6] WARN  com.simbest.boot.base.repository.CustomDynamicWhere.queryForList Line:50 - 自定义查询SQL输出为：【select * from US_TODO_MODEL t  where t.send_flag = 0 and t.work_flag = 0 and t.enabled =1】
2025-05-30 16:00:00.018 [pool-14-thread-7] WARN  com.simbest.boot.cmcc.a4.task.A4TaskCreateXmlFile.execute Line:76 - >>>>>>>>>>>>>>>>>>>>>>>>>>>>>>>>>>>>>>>>>>>>>>>>>>>>>A4TaskCreateXmlFile:执行定时任务但不是Prd环境【2025-05-30T16:00:00.018】
2025-05-30 16:00:00.026 [pool-14-thread-3] WARN  com.simbest.boot.base.repository.CustomDynamicWhere.queryForList Line:50 - 自定义查询SQL输出为：【select * from US_TODO_MODEL t  where t.send_flag = 0 and t.work_flag = 1 and t.enabled =1】
2025-05-30 16:05:00.021 [pool-14-thread-4] WARN  com.simbest.boot.base.repository.CustomDynamicWhere.queryForList Line:50 - 自定义查询SQL输出为：【select * from US_TODO_MODEL t  where t.send_flag = 0 and t.work_flag = 0 and t.enabled =1】
2025-05-30 16:05:00.025 [pool-14-thread-8] WARN  com.simbest.boot.base.repository.CustomDynamicWhere.queryForList Line:50 - 自定义查询SQL输出为：【select * from US_TODO_MODEL t  where t.send_flag = 0 and t.work_flag = 1 and t.enabled =1】
2025-05-30 16:10:00.017 [pool-14-thread-5] WARN  com.simbest.boot.base.repository.CustomDynamicWhere.queryForList Line:50 - 自定义查询SQL输出为：【select * from US_TODO_MODEL t  where t.send_flag = 0 and t.work_flag = 0 and t.enabled =1】
2025-05-30 16:10:00.017 [pool-14-thread-8] WARN  com.simbest.boot.base.repository.CustomDynamicWhere.queryForList Line:50 - 自定义查询SQL输出为：【select * from US_TODO_MODEL t  where t.send_flag = 0 and t.work_flag = 1 and t.enabled =1】
2025-05-30 16:15:00.018 [pool-14-thread-9] WARN  com.simbest.boot.base.repository.CustomDynamicWhere.queryForList Line:50 - 自定义查询SQL输出为：【select * from US_TODO_MODEL t  where t.send_flag = 0 and t.work_flag = 1 and t.enabled =1】
2025-05-30 16:15:00.019 [pool-14-thread-4] WARN  com.simbest.boot.base.repository.CustomDynamicWhere.queryForList Line:50 - 自定义查询SQL输出为：【select * from US_TODO_MODEL t  where t.send_flag = 0 and t.work_flag = 0 and t.enabled =1】
2025-05-30 16:16:24.847 [Thread-5] WARN  com.alibaba.nacos.common.http.HttpClientBeanHolder.shutdown Line:108 - [HttpClientBeanHolder] Start destroying common HttpClient
2025-05-30 16:16:24.849 [Thread-11] WARN  com.alibaba.nacos.common.notify.NotifyCenter.shutdown Line:136 - [NotifyCenter] Start destroying Publisher
2025-05-30 16:16:24.850 [Thread-11] WARN  com.alibaba.nacos.common.notify.NotifyCenter.shutdown Line:153 - [NotifyCenter] Destruction of the end
2025-05-30 16:16:24.852 [Thread-5] WARN  com.alibaba.nacos.common.http.HttpClientBeanHolder.shutdown Line:114 - [HttpClientBeanHolder] Destruction of the end
2025-05-30 16:20:07.853 [restartedMain] WARN  com.alibaba.cloud.nacos.client.NacosPropertySourceBuilder.loadNacosData Line:87 - Ignore the empty nacos configuration and get it based on dataId[hnjjwz-custom-obuat.properties] & group[DEFAULT_GROUP]
2025-05-30 16:20:07.865 [restartedMain] WARN  com.alibaba.cloud.nacos.client.NacosPropertySourceBuilder.loadNacosData Line:87 - Ignore the empty nacos configuration and get it based on dataId[hnjjwz] & group[DEFAULT_GROUP]
2025-05-30 16:20:07.875 [restartedMain] WARN  com.alibaba.cloud.nacos.client.NacosPropertySourceBuilder.loadNacosData Line:87 - Ignore the empty nacos configuration and get it based on dataId[hnjjwz.properties] & group[DEFAULT_GROUP]
2025-05-30 16:20:11.072 [restartedMain] WARN  org.springframework.boot.actuate.endpoint.EndpointId.logWarning Line:155 - Endpoint ID 'service-registry' contains invalid characters, please migrate to a valid format.
2025-05-30 16:20:12.110 [restartedMain] WARN  com.alibaba.druid.pool.DruidDataSource.initCheck Line:1314 - removeAbandoned is true, not use in production.
2025-05-30 16:21:17.157 [restartedMain] WARN  com.simbest.boot.SimbestApplication.onApplicationEvent Line:60 - 加载环境信息为: 【obuat】
2025-05-30 16:21:17.157 [restartedMain] WARN  com.simbest.boot.SimbestApplication.onApplicationEvent Line:61 - Application started successfully, lets go and have fun......
2025-05-30 16:21:17.158 [restartedMain] WARN  com.simbest.boot.SimbestApplication.onApplicationEvent Line:71 - 
---------------------------------------------------------
	应用已成功启动，运行地址如下：:
	Local:		http://localhost:8092/hnjjwz
	External:	http://***********:8092/hnjjwz
Aplication started successfully, lets go and have fun......
---------------------------------------------------------

2025-05-30 16:25:00.039 [pool-14-thread-5] WARN  com.simbest.boot.base.repository.CustomDynamicWhere.queryForList Line:50 - 自定义查询SQL输出为：【select * from US_TODO_MODEL t  where t.send_flag = 0 and t.work_flag = 0 and t.enabled =1】
2025-05-30 16:25:00.039 [pool-14-thread-8] WARN  com.simbest.boot.base.repository.CustomDynamicWhere.queryForList Line:50 - 自定义查询SQL输出为：【select * from US_TODO_MODEL t  where t.send_flag = 0 and t.work_flag = 1 and t.enabled =1】
2025-05-30 16:30:00.018 [pool-14-thread-6] WARN  com.simbest.boot.base.repository.CustomDynamicWhere.queryForList Line:50 - 自定义查询SQL输出为：【select * from US_TODO_MODEL t  where t.send_flag = 0 and t.work_flag = 1 and t.enabled =1】
2025-05-30 16:30:00.057 [pool-14-thread-8] WARN  com.simbest.boot.base.repository.CustomDynamicWhere.queryForList Line:50 - 自定义查询SQL输出为：【select * from US_TODO_MODEL t  where t.send_flag = 0 and t.work_flag = 0 and t.enabled =1】
2025-05-30 16:35:00.024 [pool-14-thread-7] WARN  com.simbest.boot.base.repository.CustomDynamicWhere.queryForList Line:50 - 自定义查询SQL输出为：【select * from US_TODO_MODEL t  where t.send_flag = 0 and t.work_flag = 1 and t.enabled =1】
2025-05-30 16:35:00.034 [pool-14-thread-2] WARN  com.simbest.boot.base.repository.CustomDynamicWhere.queryForList Line:50 - 自定义查询SQL输出为：【select * from US_TODO_MODEL t  where t.send_flag = 0 and t.work_flag = 0 and t.enabled =1】
2025-05-30 16:40:00.023 [pool-14-thread-10] WARN  com.simbest.boot.base.repository.CustomDynamicWhere.queryForList Line:50 - 自定义查询SQL输出为：【select * from US_TODO_MODEL t  where t.send_flag = 0 and t.work_flag = 1 and t.enabled =1】
2025-05-30 16:40:00.023 [pool-14-thread-7] WARN  com.simbest.boot.base.repository.CustomDynamicWhere.queryForList Line:50 - 自定义查询SQL输出为：【select * from US_TODO_MODEL t  where t.send_flag = 0 and t.work_flag = 0 and t.enabled =1】
2025-05-30 16:45:00.013 [pool-14-thread-7] WARN  com.simbest.boot.base.repository.CustomDynamicWhere.queryForList Line:50 - 自定义查询SQL输出为：【select * from US_TODO_MODEL t  where t.send_flag = 0 and t.work_flag = 0 and t.enabled =1】
2025-05-30 16:45:00.021 [pool-14-thread-8] WARN  com.simbest.boot.base.repository.CustomDynamicWhere.queryForList Line:50 - 自定义查询SQL输出为：【select * from US_TODO_MODEL t  where t.send_flag = 0 and t.work_flag = 1 and t.enabled =1】
2025-05-30 16:50:00.016 [pool-14-thread-6] WARN  com.simbest.boot.base.repository.CustomDynamicWhere.queryForList Line:50 - 自定义查询SQL输出为：【select * from US_TODO_MODEL t  where t.send_flag = 0 and t.work_flag = 1 and t.enabled =1】
2025-05-30 16:50:00.018 [pool-14-thread-4] WARN  com.simbest.boot.base.repository.CustomDynamicWhere.queryForList Line:50 - 自定义查询SQL输出为：【select * from US_TODO_MODEL t  where t.send_flag = 0 and t.work_flag = 0 and t.enabled =1】
2025-05-30 16:55:00.007 [pool-14-thread-4] WARN  com.simbest.boot.base.repository.CustomDynamicWhere.queryForList Line:50 - 自定义查询SQL输出为：【select * from US_TODO_MODEL t  where t.send_flag = 0 and t.work_flag = 1 and t.enabled =1】
2025-05-30 16:55:00.009 [pool-14-thread-7] WARN  com.simbest.boot.base.repository.CustomDynamicWhere.queryForList Line:50 - 自定义查询SQL输出为：【select * from US_TODO_MODEL t  where t.send_flag = 0 and t.work_flag = 0 and t.enabled =1】
2025-05-30 17:00:00.014 [pool-14-thread-9] WARN  com.simbest.boot.cmcc.a4.task.A4TaskCreateXmlFile.execute Line:76 - >>>>>>>>>>>>>>>>>>>>>>>>>>>>>>>>>>>>>>>>>>>>>>>>>>>>>A4TaskCreateXmlFile:执行定时任务但不是Prd环境【2025-05-30T17:00:00.014】
2025-05-30 17:00:00.020 [pool-14-thread-6] WARN  com.simbest.boot.base.repository.CustomDynamicWhere.queryForList Line:50 - 自定义查询SQL输出为：【select * from US_TODO_MODEL t  where t.send_flag = 0 and t.work_flag = 0 and t.enabled =1】
2025-05-30 17:00:00.027 [pool-14-thread-1] WARN  com.simbest.boot.base.repository.CustomDynamicWhere.queryForList Line:50 - 自定义查询SQL输出为：【select * from US_TODO_MODEL t  where t.send_flag = 0 and t.work_flag = 1 and t.enabled =1】
2025-05-30 17:05:00.004 [pool-14-thread-10] WARN  com.simbest.boot.base.repository.CustomDynamicWhere.queryForList Line:50 - 自定义查询SQL输出为：【select * from US_TODO_MODEL t  where t.send_flag = 0 and t.work_flag = 1 and t.enabled =1】
2025-05-30 17:05:00.004 [pool-14-thread-6] WARN  com.simbest.boot.base.repository.CustomDynamicWhere.queryForList Line:50 - 自定义查询SQL输出为：【select * from US_TODO_MODEL t  where t.send_flag = 0 and t.work_flag = 0 and t.enabled =1】
2025-05-30 17:10:00.022 [pool-14-thread-4] WARN  com.simbest.boot.base.repository.CustomDynamicWhere.queryForList Line:50 - 自定义查询SQL输出为：【select * from US_TODO_MODEL t  where t.send_flag = 0 and t.work_flag = 1 and t.enabled =1】
2025-05-30 17:10:00.022 [pool-14-thread-2] WARN  com.simbest.boot.base.repository.CustomDynamicWhere.queryForList Line:50 - 自定义查询SQL输出为：【select * from US_TODO_MODEL t  where t.send_flag = 0 and t.work_flag = 0 and t.enabled =1】
2025-05-30 17:15:00.029 [pool-14-thread-5] WARN  com.simbest.boot.base.repository.CustomDynamicWhere.queryForList Line:50 - 自定义查询SQL输出为：【select * from US_TODO_MODEL t  where t.send_flag = 0 and t.work_flag = 0 and t.enabled =1】
2025-05-30 17:15:00.030 [pool-14-thread-1] WARN  com.simbest.boot.base.repository.CustomDynamicWhere.queryForList Line:50 - 自定义查询SQL输出为：【select * from US_TODO_MODEL t  where t.send_flag = 0 and t.work_flag = 1 and t.enabled =1】
2025-05-30 17:20:00.015 [pool-14-thread-10] WARN  com.simbest.boot.base.repository.CustomDynamicWhere.queryForList Line:50 - 自定义查询SQL输出为：【select * from US_TODO_MODEL t  where t.send_flag = 0 and t.work_flag = 1 and t.enabled =1】
2025-05-30 17:20:00.014 [pool-14-thread-4] WARN  com.simbest.boot.base.repository.CustomDynamicWhere.queryForList Line:50 - 自定义查询SQL输出为：【select * from US_TODO_MODEL t  where t.send_flag = 0 and t.work_flag = 0 and t.enabled =1】
2025-05-30 17:25:00.012 [pool-14-thread-9] WARN  com.simbest.boot.base.repository.CustomDynamicWhere.queryForList Line:50 - 自定义查询SQL输出为：【select * from US_TODO_MODEL t  where t.send_flag = 0 and t.work_flag = 0 and t.enabled =1】
2025-05-30 17:25:00.013 [pool-14-thread-6] WARN  com.simbest.boot.base.repository.CustomDynamicWhere.queryForList Line:50 - 自定义查询SQL输出为：【select * from US_TODO_MODEL t  where t.send_flag = 0 and t.work_flag = 1 and t.enabled =1】
2025-05-30 17:30:00.024 [pool-14-thread-2] WARN  com.simbest.boot.base.repository.CustomDynamicWhere.queryForList Line:50 - 自定义查询SQL输出为：【select * from US_TODO_MODEL t  where t.send_flag = 0 and t.work_flag = 1 and t.enabled =1】
2025-05-30 17:30:00.038 [pool-14-thread-4] WARN  com.simbest.boot.base.repository.CustomDynamicWhere.queryForList Line:50 - 自定义查询SQL输出为：【select * from US_TODO_MODEL t  where t.send_flag = 0 and t.work_flag = 0 and t.enabled =1】
2025-05-30 17:35:00.018 [pool-14-thread-4] WARN  com.simbest.boot.base.repository.CustomDynamicWhere.queryForList Line:50 - 自定义查询SQL输出为：【select * from US_TODO_MODEL t  where t.send_flag = 0 and t.work_flag = 0 and t.enabled =1】
2025-05-30 17:35:00.020 [pool-14-thread-9] WARN  com.simbest.boot.base.repository.CustomDynamicWhere.queryForList Line:50 - 自定义查询SQL输出为：【select * from US_TODO_MODEL t  where t.send_flag = 0 and t.work_flag = 1 and t.enabled =1】
2025-05-30 17:40:00.020 [pool-14-thread-1] WARN  com.simbest.boot.base.repository.CustomDynamicWhere.queryForList Line:50 - 自定义查询SQL输出为：【select * from US_TODO_MODEL t  where t.send_flag = 0 and t.work_flag = 1 and t.enabled =1】
2025-05-30 17:40:00.020 [pool-14-thread-10] WARN  com.simbest.boot.base.repository.CustomDynamicWhere.queryForList Line:50 - 自定义查询SQL输出为：【select * from US_TODO_MODEL t  where t.send_flag = 0 and t.work_flag = 0 and t.enabled =1】
2025-05-30 17:45:00.025 [pool-14-thread-9] WARN  com.simbest.boot.base.repository.CustomDynamicWhere.queryForList Line:50 - 自定义查询SQL输出为：【select * from US_TODO_MODEL t  where t.send_flag = 0 and t.work_flag = 1 and t.enabled =1】
2025-05-30 17:45:00.025 [pool-14-thread-8] WARN  com.simbest.boot.base.repository.CustomDynamicWhere.queryForList Line:50 - 自定义查询SQL输出为：【select * from US_TODO_MODEL t  where t.send_flag = 0 and t.work_flag = 0 and t.enabled =1】
2025-05-30 17:50:00.015 [pool-14-thread-10] WARN  com.simbest.boot.base.repository.CustomDynamicWhere.queryForList Line:50 - 自定义查询SQL输出为：【select * from US_TODO_MODEL t  where t.send_flag = 0 and t.work_flag = 0 and t.enabled =1】
2025-05-30 17:50:00.022 [pool-14-thread-3] WARN  com.simbest.boot.base.repository.CustomDynamicWhere.queryForList Line:50 - 自定义查询SQL输出为：【select * from US_TODO_MODEL t  where t.send_flag = 0 and t.work_flag = 1 and t.enabled =1】
2025-05-30 17:51:23.558 [nacos.client.cachedata.internal.notifier] WARN  com.alibaba.cloud.nacos.client.NacosPropertySourceBuilder.loadNacosData Line:87 - Ignore the empty nacos configuration and get it based on dataId[hnjjwz-custom-obuat.properties] & group[DEFAULT_GROUP]
2025-05-30 17:51:23.566 [nacos.client.cachedata.internal.notifier] WARN  com.alibaba.cloud.nacos.client.NacosPropertySourceBuilder.loadNacosData Line:87 - Ignore the empty nacos configuration and get it based on dataId[hnjjwz] & group[DEFAULT_GROUP]
2025-05-30 17:51:23.577 [nacos.client.cachedata.internal.notifier] WARN  com.alibaba.cloud.nacos.client.NacosPropertySourceBuilder.loadNacosData Line:87 - Ignore the empty nacos configuration and get it based on dataId[hnjjwz.properties] & group[DEFAULT_GROUP]
2025-05-30 17:55:00.016 [pool-14-thread-7] WARN  com.simbest.boot.base.repository.CustomDynamicWhere.queryForList Line:50 - 自定义查询SQL输出为：【select * from US_TODO_MODEL t  where t.send_flag = 0 and t.work_flag = 1 and t.enabled =1】
2025-05-30 17:55:00.016 [pool-14-thread-3] WARN  com.simbest.boot.base.repository.CustomDynamicWhere.queryForList Line:50 - 自定义查询SQL输出为：【select * from US_TODO_MODEL t  where t.send_flag = 0 and t.work_flag = 0 and t.enabled =1】
2025-05-30 18:00:00.009 [pool-14-thread-4] WARN  com.simbest.boot.cmcc.a4.task.A4TaskCreateXmlFile.execute Line:76 - >>>>>>>>>>>>>>>>>>>>>>>>>>>>>>>>>>>>>>>>>>>>>>>>>>>>>A4TaskCreateXmlFile:执行定时任务但不是Prd环境【2025-05-30T18:00:00.009】
2025-05-30 18:00:00.011 [pool-14-thread-6] WARN  com.simbest.boot.base.repository.CustomDynamicWhere.queryForList Line:50 - 自定义查询SQL输出为：【select * from US_TODO_MODEL t  where t.send_flag = 0 and t.work_flag = 0 and t.enabled =1】
2025-05-30 18:00:00.015 [pool-14-thread-1] WARN  com.simbest.boot.base.repository.CustomDynamicWhere.queryForList Line:50 - 自定义查询SQL输出为：【select * from US_TODO_MODEL t  where t.send_flag = 0 and t.work_flag = 1 and t.enabled =1】
2025-05-30 18:05:00.011 [pool-14-thread-2] WARN  com.simbest.boot.base.repository.CustomDynamicWhere.queryForList Line:50 - 自定义查询SQL输出为：【select * from US_TODO_MODEL t  where t.send_flag = 0 and t.work_flag = 0 and t.enabled =1】
2025-05-30 18:05:00.011 [pool-14-thread-6] WARN  com.simbest.boot.base.repository.CustomDynamicWhere.queryForList Line:50 - 自定义查询SQL输出为：【select * from US_TODO_MODEL t  where t.send_flag = 0 and t.work_flag = 1 and t.enabled =1】
2025-05-30 18:10:00.016 [pool-14-thread-5] WARN  com.simbest.boot.base.repository.CustomDynamicWhere.queryForList Line:50 - 自定义查询SQL输出为：【select * from US_TODO_MODEL t  where t.send_flag = 0 and t.work_flag = 1 and t.enabled =1】
2025-05-30 18:10:00.019 [pool-14-thread-9] WARN  com.simbest.boot.base.repository.CustomDynamicWhere.queryForList Line:50 - 自定义查询SQL输出为：【select * from US_TODO_MODEL t  where t.send_flag = 0 and t.work_flag = 0 and t.enabled =1】
2025-05-30 18:15:00.012 [pool-14-thread-8] WARN  com.simbest.boot.base.repository.CustomDynamicWhere.queryForList Line:50 - 自定义查询SQL输出为：【select * from US_TODO_MODEL t  where t.send_flag = 0 and t.work_flag = 0 and t.enabled =1】
2025-05-30 18:15:00.012 [pool-14-thread-4] WARN  com.simbest.boot.base.repository.CustomDynamicWhere.queryForList Line:50 - 自定义查询SQL输出为：【select * from US_TODO_MODEL t  where t.send_flag = 0 and t.work_flag = 1 and t.enabled =1】
2025-05-30 18:20:00.017 [pool-14-thread-2] WARN  com.simbest.boot.base.repository.CustomDynamicWhere.queryForList Line:50 - 自定义查询SQL输出为：【select * from US_TODO_MODEL t  where t.send_flag = 0 and t.work_flag = 1 and t.enabled =1】
2025-05-30 18:20:00.017 [pool-14-thread-10] WARN  com.simbest.boot.base.repository.CustomDynamicWhere.queryForList Line:50 - 自定义查询SQL输出为：【select * from US_TODO_MODEL t  where t.send_flag = 0 and t.work_flag = 0 and t.enabled =1】
2025-05-30 18:25:00.012 [pool-14-thread-7] WARN  com.simbest.boot.base.repository.CustomDynamicWhere.queryForList Line:50 - 自定义查询SQL输出为：【select * from US_TODO_MODEL t  where t.send_flag = 0 and t.work_flag = 0 and t.enabled =1】
2025-05-30 18:25:00.013 [pool-14-thread-3] WARN  com.simbest.boot.base.repository.CustomDynamicWhere.queryForList Line:50 - 自定义查询SQL输出为：【select * from US_TODO_MODEL t  where t.send_flag = 0 and t.work_flag = 1 and t.enabled =1】
2025-05-30 18:30:00.015 [pool-14-thread-8] WARN  com.simbest.boot.base.repository.CustomDynamicWhere.queryForList Line:50 - 自定义查询SQL输出为：【select * from US_TODO_MODEL t  where t.send_flag = 0 and t.work_flag = 0 and t.enabled =1】
2025-05-30 18:30:00.026 [pool-14-thread-2] WARN  com.simbest.boot.base.repository.CustomDynamicWhere.queryForList Line:50 - 自定义查询SQL输出为：【select * from US_TODO_MODEL t  where t.send_flag = 0 and t.work_flag = 1 and t.enabled =1】
2025-05-30 18:36:32.887 [redisson-netty-5-5] WARN  org.redisson.client.handler.CommandDecoder.completeResponse Line:445 - response has been skipped due to timeout! channel: [id: 0x216c5cfe, L:/***********:62883 - R:************/************:7005], command: (CLUSTER NODES), params: [], result: [ClusterNodeInfo [nodeId=02aff3b78f8c09c43cbfc2346eac68a16a942097, address=redis://************:7003, flags=[MASTER], slaveOf=null, slotRanges=[[5461-10922]]], ClusterNodeInfo [nodeId=85e61682488d3a355264d212607a63832da60e8c, address=redis://************:7006, flags=[SLAVE], slaveOf=02aff3b78f8c09c43cbfc2346eac68a16a942097, slotRanges=[]], ClusterNodeInfo [nodeId=64f45f937e6f8c36d0fb7cf09195403c4cccdf1d, address=redis://************:7004, flags=[MASTER], slaveOf=null, slotRanges=[[0-5460]]], ClusterNodeInfo [nodeId=4dcfd30d1191cb5e65ef69e88dc76592f8b3d14d, address=redis://************:7005, flags=[MASTER, MYSELF], slaveOf=null, slotRanges=[[10923-16383]]], ClusterNodeInfo [nodeId=248f8c5f1a3d07d891f1bcf924c02097c5509e0d, address=redis://************:7001, flags=[SLAVE], slaveOf=64f45f937e6f8c36d0fb7cf09195403c4cccdf1d, slotRanges=[]], ClusterNodeInfo [nodeId=5514edc6d1aaab0526b0093a424e9f352e9914ed, address=redis://************:7002, flags=[SLAVE], slaveOf=4dcfd30d1191cb5e65ef69e88dc76592f8b3d14d, slotRanges=[]]]
2025-05-30 18:36:52.740 [AMQP Connection ************:5672] WARN  com.rabbitmq.client.impl.ForgivingExceptionHandler.log Line:115 - An unexpected connection driver error occured (Exception message: Connection reset)
2025-05-30 18:36:54.110 [AMQP Connection ************:5672] WARN  com.rabbitmq.client.impl.ForgivingExceptionHandler.log Line:115 - An unexpected connection driver error occured (Exception message: Connection reset)
2025-05-30 18:36:54.204 [AMQP Connection ************:5672] WARN  com.rabbitmq.client.impl.ForgivingExceptionHandler.log Line:115 - An unexpected connection driver error occured (Exception message: Connection reset)
2025-05-30 18:36:54.219 [AMQP Connection ************:5672] WARN  com.rabbitmq.client.impl.ForgivingExceptionHandler.log Line:115 - An unexpected connection driver error occured (Exception message: Connection reset)
2025-05-30 18:36:57.021 [pool-14-thread-2] WARN  com.simbest.boot.base.repository.CustomDynamicWhere.queryForList Line:50 - 自定义查询SQL输出为：【select * from US_TODO_MODEL t  where t.send_flag = 0 and t.work_flag = 1 and t.enabled =1】
2025-05-30 18:36:57.022 [pool-14-thread-8] WARN  com.simbest.boot.base.repository.CustomDynamicWhere.queryForList Line:50 - 自定义查询SQL输出为：【select * from US_TODO_MODEL t  where t.send_flag = 0 and t.work_flag = 0 and t.enabled =1】
2025-05-30 18:40:00.014 [pool-14-thread-3] WARN  com.simbest.boot.base.repository.CustomDynamicWhere.queryForList Line:50 - 自定义查询SQL输出为：【select * from US_TODO_MODEL t  where t.send_flag = 0 and t.work_flag = 1 and t.enabled =1】
2025-05-30 18:40:00.015 [pool-14-thread-8] WARN  com.simbest.boot.base.repository.CustomDynamicWhere.queryForList Line:50 - 自定义查询SQL输出为：【select * from US_TODO_MODEL t  where t.send_flag = 0 and t.work_flag = 0 and t.enabled =1】
2025-05-30 18:45:00.006 [pool-14-thread-6] WARN  com.simbest.boot.base.repository.CustomDynamicWhere.queryForList Line:50 - 自定义查询SQL输出为：【select * from US_TODO_MODEL t  where t.send_flag = 0 and t.work_flag = 0 and t.enabled =1】
2025-05-30 18:45:00.007 [pool-14-thread-9] WARN  com.simbest.boot.base.repository.CustomDynamicWhere.queryForList Line:50 - 自定义查询SQL输出为：【select * from US_TODO_MODEL t  where t.send_flag = 0 and t.work_flag = 1 and t.enabled =1】
2025-05-30 18:50:00.007 [pool-14-thread-10] WARN  com.simbest.boot.base.repository.CustomDynamicWhere.queryForList Line:50 - 自定义查询SQL输出为：【select * from US_TODO_MODEL t  where t.send_flag = 0 and t.work_flag = 0 and t.enabled =1】
2025-05-30 18:50:00.008 [pool-14-thread-9] WARN  com.simbest.boot.base.repository.CustomDynamicWhere.queryForList Line:50 - 自定义查询SQL输出为：【select * from US_TODO_MODEL t  where t.send_flag = 0 and t.work_flag = 1 and t.enabled =1】
2025-05-30 18:55:00.026 [pool-14-thread-4] WARN  com.simbest.boot.base.repository.CustomDynamicWhere.queryForList Line:50 - 自定义查询SQL输出为：【select * from US_TODO_MODEL t  where t.send_flag = 0 and t.work_flag = 1 and t.enabled =1】
2025-05-30 18:55:00.026 [pool-14-thread-10] WARN  com.simbest.boot.base.repository.CustomDynamicWhere.queryForList Line:50 - 自定义查询SQL输出为：【select * from US_TODO_MODEL t  where t.send_flag = 0 and t.work_flag = 0 and t.enabled =1】
2025-05-30 19:17:54.263 [AMQP Connection ************:5672] WARN  com.rabbitmq.client.impl.ForgivingExceptionHandler.log Line:115 - An unexpected connection driver error occured (Exception message: Connection reset)
2025-05-30 19:17:56.013 [AMQP Connection ************:5672] WARN  com.rabbitmq.client.impl.ForgivingExceptionHandler.log Line:115 - An unexpected connection driver error occured (Exception message: Connection reset)
2025-05-30 19:17:56.277 [AMQP Connection ************:5672] WARN  com.rabbitmq.client.impl.ForgivingExceptionHandler.log Line:115 - An unexpected connection driver error occured (Exception message: Connection reset)
2025-05-30 19:17:57.383 [AMQP Connection ************:5672] WARN  com.rabbitmq.client.impl.ForgivingExceptionHandler.log Line:115 - An unexpected connection driver error occured (Exception message: Connection reset)
2025-05-30 19:20:03.066 [pool-14-thread-4] WARN  com.simbest.boot.base.repository.CustomDynamicWhere.queryForList Line:50 - 自定义查询SQL输出为：【select * from US_TODO_MODEL t  where t.send_flag = 0 and t.work_flag = 0 and t.enabled =1】
2025-05-30 19:25:00.015 [pool-14-thread-1] WARN  com.simbest.boot.base.repository.CustomDynamicWhere.queryForList Line:50 - 自定义查询SQL输出为：【select * from US_TODO_MODEL t  where t.send_flag = 0 and t.work_flag = 0 and t.enabled =1】
2025-05-30 19:25:09.266 [pool-14-thread-5] WARN  com.simbest.boot.base.repository.CustomDynamicWhere.queryForList Line:50 - 自定义查询SQL输出为：【select * from US_TODO_MODEL t  where t.send_flag = 0 and t.work_flag = 1 and t.enabled =1】
2025-05-30 19:30:00.010 [pool-14-thread-4] WARN  com.simbest.boot.base.repository.CustomDynamicWhere.queryForList Line:50 - 自定义查询SQL输出为：【select * from US_TODO_MODEL t  where t.send_flag = 0 and t.work_flag = 0 and t.enabled =1】
2025-05-30 19:30:00.013 [pool-14-thread-3] WARN  com.simbest.boot.base.repository.CustomDynamicWhere.queryForList Line:50 - 自定义查询SQL输出为：【select * from US_TODO_MODEL t  where t.send_flag = 0 and t.work_flag = 1 and t.enabled =1】
2025-05-30 19:35:00.014 [pool-14-thread-9] WARN  com.simbest.boot.base.repository.CustomDynamicWhere.queryForList Line:50 - 自定义查询SQL输出为：【select * from US_TODO_MODEL t  where t.send_flag = 0 and t.work_flag = 1 and t.enabled =1】
2025-05-30 19:35:00.014 [pool-14-thread-10] WARN  com.simbest.boot.base.repository.CustomDynamicWhere.queryForList Line:50 - 自定义查询SQL输出为：【select * from US_TODO_MODEL t  where t.send_flag = 0 and t.work_flag = 0 and t.enabled =1】
2025-05-30 19:58:25.528 [AMQP Connection ************:5672] WARN  com.rabbitmq.client.impl.ForgivingExceptionHandler.log Line:115 - An unexpected connection driver error occured (Exception message: Connection reset)
2025-05-30 19:58:27.348 [AMQP Connection ************:5672] WARN  com.rabbitmq.client.impl.ForgivingExceptionHandler.log Line:115 - An unexpected connection driver error occured (Exception message: Connection reset)
2025-05-30 19:58:27.551 [AMQP Connection ************:5672] WARN  com.rabbitmq.client.impl.ForgivingExceptionHandler.log Line:115 - An unexpected connection driver error occured (Exception message: Connection reset)
2025-05-30 19:58:28.954 [AMQP Connection ************:5672] WARN  com.rabbitmq.client.impl.ForgivingExceptionHandler.log Line:115 - An unexpected connection driver error occured (Exception message: Connection reset)
2025-05-30 20:05:09.234 [pool-14-thread-2] WARN  com.simbest.boot.base.repository.CustomDynamicWhere.queryForList Line:50 - 自定义查询SQL输出为：【select * from US_TODO_MODEL t  where t.send_flag = 0 and t.work_flag = 1 and t.enabled =1】
2025-05-30 20:10:00.011 [pool-14-thread-5] WARN  com.simbest.boot.base.repository.CustomDynamicWhere.queryForList Line:50 - 自定义查询SQL输出为：【select * from US_TODO_MODEL t  where t.send_flag = 0 and t.work_flag = 1 and t.enabled =1】
2025-05-30 20:15:00.017 [pool-14-thread-7] WARN  com.simbest.boot.base.repository.CustomDynamicWhere.queryForList Line:50 - 自定义查询SQL输出为：【select * from US_TODO_MODEL t  where t.send_flag = 0 and t.work_flag = 1 and t.enabled =1】
2025-05-30 20:23:22.293 [AMQP Connection ************:5672] WARN  com.rabbitmq.client.impl.ForgivingExceptionHandler.log Line:115 - An unexpected connection driver error occured (Exception message: Connection reset)
2025-05-30 20:23:24.097 [AMQP Connection ************:5672] WARN  com.rabbitmq.client.impl.ForgivingExceptionHandler.log Line:115 - An unexpected connection driver error occured (Exception message: Connection reset)
2025-05-30 20:23:24.315 [AMQP Connection ************:5672] WARN  com.rabbitmq.client.impl.ForgivingExceptionHandler.log Line:115 - An unexpected connection driver error occured (Exception message: Connection reset)
2025-05-30 20:23:25.619 [AMQP Connection ************:5672] WARN  com.rabbitmq.client.impl.ForgivingExceptionHandler.log Line:115 - An unexpected connection driver error occured (Exception message: Connection reset)
2025-05-30 20:23:37.233 [pool-14-thread-6] WARN  com.simbest.boot.base.repository.CustomDynamicWhere.queryForList Line:50 - 自定义查询SQL输出为：【select * from US_TODO_MODEL t  where t.send_flag = 0 and t.work_flag = 0 and t.enabled =1】
2025-05-30 20:23:37.245 [pool-14-thread-2] WARN  com.simbest.boot.base.repository.CustomDynamicWhere.queryForList Line:50 - 自定义查询SQL输出为：【select * from US_TODO_MODEL t  where t.send_flag = 0 and t.work_flag = 1 and t.enabled =1】
2025-05-30 20:30:00.013 [pool-14-thread-7] WARN  com.simbest.boot.base.repository.CustomDynamicWhere.queryForList Line:50 - 自定义查询SQL输出为：【select * from US_TODO_MODEL t  where t.send_flag = 0 and t.work_flag = 0 and t.enabled =1】
2025-05-30 20:30:00.016 [pool-14-thread-10] WARN  com.simbest.boot.base.repository.CustomDynamicWhere.queryForList Line:50 - 自定义查询SQL输出为：【select * from US_TODO_MODEL t  where t.send_flag = 0 and t.work_flag = 1 and t.enabled =1】
2025-05-30 20:35:00.009 [pool-14-thread-10] WARN  com.simbest.boot.base.repository.CustomDynamicWhere.queryForList Line:50 - 自定义查询SQL输出为：【select * from US_TODO_MODEL t  where t.send_flag = 0 and t.work_flag = 1 and t.enabled =1】
2025-05-30 20:35:00.010 [pool-14-thread-4] WARN  com.simbest.boot.base.repository.CustomDynamicWhere.queryForList Line:50 - 自定义查询SQL输出为：【select * from US_TODO_MODEL t  where t.send_flag = 0 and t.work_flag = 0 and t.enabled =1】
2025-05-30 20:40:00.008 [pool-14-thread-3] WARN  com.simbest.boot.base.repository.CustomDynamicWhere.queryForList Line:50 - 自定义查询SQL输出为：【select * from US_TODO_MODEL t  where t.send_flag = 0 and t.work_flag = 1 and t.enabled =1】
2025-05-30 20:40:00.008 [pool-14-thread-6] WARN  com.simbest.boot.base.repository.CustomDynamicWhere.queryForList Line:50 - 自定义查询SQL输出为：【select * from US_TODO_MODEL t  where t.send_flag = 0 and t.work_flag = 0 and t.enabled =1】
2025-05-30 20:46:33.647 [redisson-netty-5-8] WARN  org.redisson.client.handler.CommandDecoder.completeResponse Line:445 - response has been skipped due to timeout! channel: [id: 0xd9ae0e3a, L:/***********:61379 - R:************/************:7003], command: (CLUSTER NODES), params: [], result: [ClusterNodeInfo [nodeId=4dcfd30d1191cb5e65ef69e88dc76592f8b3d14d, address=redis://************:7005, flags=[MASTER], slaveOf=null, slotRanges=[[10923-16383]]], ClusterNodeInfo [nodeId=248f8c5f1a3d07d891f1bcf924c02097c5509e0d, address=redis://************:7001, flags=[SLAVE], slaveOf=64f45f937e6f8c36d0fb7cf09195403c4cccdf1d, slotRanges=[]], ClusterNodeInfo [nodeId=02aff3b78f8c09c43cbfc2346eac68a16a942097, address=redis://************:7003, flags=[MASTER, MYSELF], slaveOf=null, slotRanges=[[5461-10922]]], ClusterNodeInfo [nodeId=85e61682488d3a355264d212607a63832da60e8c, address=redis://************:7006, flags=[SLAVE], slaveOf=02aff3b78f8c09c43cbfc2346eac68a16a942097, slotRanges=[]], ClusterNodeInfo [nodeId=64f45f937e6f8c36d0fb7cf09195403c4cccdf1d, address=redis://************:7004, flags=[MASTER], slaveOf=null, slotRanges=[[0-5460]]], ClusterNodeInfo [nodeId=5514edc6d1aaab0526b0093a424e9f352e9914ed, address=redis://************:7002, flags=[SLAVE], slaveOf=4dcfd30d1191cb5e65ef69e88dc76592f8b3d14d, slotRanges=[]]]
2025-05-30 20:46:45.724 [redisson-netty-5-8] WARN  org.redisson.client.handler.CommandDecoder.completeResponse Line:445 - response has been skipped due to timeout! channel: [id: 0x54e488bd, L:/***********:55426 - R:************/************:7003], command: (CLUSTER NODES), params: [], result: [ClusterNodeInfo [nodeId=4dcfd30d1191cb5e65ef69e88dc76592f8b3d14d, address=redis://************:7005, flags=[MASTER], slaveOf=null, slotRanges=[[10923-16383]]], ClusterNodeInfo [nodeId=248f8c5f1a3d07d891f1bcf924c02097c5509e0d, address=redis://************:7001, flags=[SLAVE], slaveOf=64f45f937e6f8c36d0fb7cf09195403c4cccdf1d, slotRanges=[]], ClusterNodeInfo [nodeId=02aff3b78f8c09c43cbfc2346eac68a16a942097, address=redis://************:7003, flags=[MASTER, MYSELF], slaveOf=null, slotRanges=[[5461-10922]]], ClusterNodeInfo [nodeId=85e61682488d3a355264d212607a63832da60e8c, address=redis://************:7006, flags=[SLAVE], slaveOf=02aff3b78f8c09c43cbfc2346eac68a16a942097, slotRanges=[]], ClusterNodeInfo [nodeId=64f45f937e6f8c36d0fb7cf09195403c4cccdf1d, address=redis://************:7004, flags=[MASTER], slaveOf=null, slotRanges=[[0-5460]]], ClusterNodeInfo [nodeId=5514edc6d1aaab0526b0093a424e9f352e9914ed, address=redis://************:7002, flags=[SLAVE], slaveOf=4dcfd30d1191cb5e65ef69e88dc76592f8b3d14d, slotRanges=[]]]
2025-05-30 20:47:00.015 [pool-14-thread-1] WARN  com.simbest.boot.base.repository.CustomDynamicWhere.queryForList Line:50 - 自定义查询SQL输出为：【select * from US_TODO_MODEL t  where t.send_flag = 0 and t.work_flag = 1 and t.enabled =1】
2025-05-30 20:47:00.016 [pool-14-thread-10] WARN  com.simbest.boot.base.repository.CustomDynamicWhere.queryForList Line:50 - 自定义查询SQL输出为：【select * from US_TODO_MODEL t  where t.send_flag = 0 and t.work_flag = 0 and t.enabled =1】
2025-05-30 20:47:12.371 [AMQP Connection ************:5672] WARN  com.rabbitmq.client.impl.ForgivingExceptionHandler.log Line:115 - An unexpected connection driver error occured (Exception message: Connection reset)
2025-05-30 20:47:14.297 [AMQP Connection ************:5672] WARN  com.rabbitmq.client.impl.ForgivingExceptionHandler.log Line:115 - An unexpected connection driver error occured (Exception message: Connection reset)
2025-05-30 20:47:14.436 [AMQP Connection ************:5672] WARN  com.rabbitmq.client.impl.ForgivingExceptionHandler.log Line:115 - An unexpected connection driver error occured (Exception message: Connection reset)
2025-05-30 20:47:15.716 [AMQP Connection ************:5672] WARN  com.rabbitmq.client.impl.ForgivingExceptionHandler.log Line:115 - An unexpected connection driver error occured (Exception message: Connection reset)
2025-05-30 20:50:00.015 [pool-14-thread-5] WARN  com.simbest.boot.base.repository.CustomDynamicWhere.queryForList Line:50 - 自定义查询SQL输出为：【select * from US_TODO_MODEL t  where t.send_flag = 0 and t.work_flag = 0 and t.enabled =1】
2025-05-30 20:50:00.015 [pool-14-thread-6] WARN  com.simbest.boot.base.repository.CustomDynamicWhere.queryForList Line:50 - 自定义查询SQL输出为：【select * from US_TODO_MODEL t  where t.send_flag = 0 and t.work_flag = 1 and t.enabled =1】
2025-05-30 20:55:00.016 [pool-14-thread-1] WARN  com.simbest.boot.base.repository.CustomDynamicWhere.queryForList Line:50 - 自定义查询SQL输出为：【select * from US_TODO_MODEL t  where t.send_flag = 0 and t.work_flag = 0 and t.enabled =1】
2025-05-30 20:55:00.016 [pool-14-thread-9] WARN  com.simbest.boot.base.repository.CustomDynamicWhere.queryForList Line:50 - 自定义查询SQL输出为：【select * from US_TODO_MODEL t  where t.send_flag = 0 and t.work_flag = 1 and t.enabled =1】
2025-05-30 20:59:59.938 [pool-14-thread-7] WARN  com.simbest.boot.cmcc.a4.task.A4TaskCreateXmlFile.execute Line:76 - >>>>>>>>>>>>>>>>>>>>>>>>>>>>>>>>>>>>>>>>>>>>>>>>>>>>>A4TaskCreateXmlFile:执行定时任务但不是Prd环境【2025-05-30T20:59:59.938】
2025-05-30 21:00:00.018 [pool-14-thread-8] WARN  com.simbest.boot.base.repository.CustomDynamicWhere.queryForList Line:50 - 自定义查询SQL输出为：【select * from US_TODO_MODEL t  where t.send_flag = 0 and t.work_flag = 1 and t.enabled =1】
2025-05-30 21:00:00.018 [pool-14-thread-2] WARN  com.simbest.boot.base.repository.CustomDynamicWhere.queryForList Line:50 - 自定义查询SQL输出为：【select * from US_TODO_MODEL t  where t.send_flag = 0 and t.work_flag = 0 and t.enabled =1】
2025-05-30 21:05:00.017 [pool-14-thread-4] WARN  com.simbest.boot.base.repository.CustomDynamicWhere.queryForList Line:50 - 自定义查询SQL输出为：【select * from US_TODO_MODEL t  where t.send_flag = 0 and t.work_flag = 1 and t.enabled =1】
2025-05-30 21:05:00.018 [pool-14-thread-1] WARN  com.simbest.boot.base.repository.CustomDynamicWhere.queryForList Line:50 - 自定义查询SQL输出为：【select * from US_TODO_MODEL t  where t.send_flag = 0 and t.work_flag = 0 and t.enabled =1】
2025-05-30 21:10:03.854 [pool-14-thread-7] WARN  com.simbest.boot.base.repository.CustomDynamicWhere.queryForList Line:50 - 自定义查询SQL输出为：【select * from US_TODO_MODEL t  where t.send_flag = 0 and t.work_flag = 0 and t.enabled =1】
2025-05-30 21:10:03.881 [pool-14-thread-9] WARN  com.simbest.boot.base.repository.CustomDynamicWhere.queryForList Line:50 - 自定义查询SQL输出为：【select * from US_TODO_MODEL t  where t.send_flag = 0 and t.work_flag = 1 and t.enabled =1】
2025-05-30 21:10:32.546 [AMQP Connection ************:5672] WARN  com.rabbitmq.client.impl.ForgivingExceptionHandler.log Line:115 - An unexpected connection driver error occured (Exception message: Connection reset)
2025-05-30 21:10:34.415 [AMQP Connection ************:5672] WARN  com.rabbitmq.client.impl.ForgivingExceptionHandler.log Line:115 - An unexpected connection driver error occured (Exception message: Connection reset)
2025-05-30 21:10:34.555 [AMQP Connection ************:5672] WARN  com.rabbitmq.client.impl.ForgivingExceptionHandler.log Line:115 - An unexpected connection driver error occured (Exception message: Connection reset)
2025-05-30 21:10:35.828 [AMQP Connection ************:5672] WARN  com.rabbitmq.client.impl.ForgivingExceptionHandler.log Line:115 - An unexpected connection driver error occured (Exception message: Connection reset)
2025-05-30 21:15:00.006 [pool-14-thread-2] WARN  com.simbest.boot.base.repository.CustomDynamicWhere.queryForList Line:50 - 自定义查询SQL输出为：【select * from US_TODO_MODEL t  where t.send_flag = 0 and t.work_flag = 0 and t.enabled =1】
2025-05-30 21:15:00.007 [pool-14-thread-10] WARN  com.simbest.boot.base.repository.CustomDynamicWhere.queryForList Line:50 - 自定义查询SQL输出为：【select * from US_TODO_MODEL t  where t.send_flag = 0 and t.work_flag = 1 and t.enabled =1】
2025-05-30 21:20:00.011 [pool-14-thread-9] WARN  com.simbest.boot.base.repository.CustomDynamicWhere.queryForList Line:50 - 自定义查询SQL输出为：【select * from US_TODO_MODEL t  where t.send_flag = 0 and t.work_flag = 1 and t.enabled =1】
2025-05-30 21:20:00.011 [pool-14-thread-5] WARN  com.simbest.boot.base.repository.CustomDynamicWhere.queryForList Line:50 - 自定义查询SQL输出为：【select * from US_TODO_MODEL t  where t.send_flag = 0 and t.work_flag = 0 and t.enabled =1】
2025-05-30 21:25:00.005 [pool-14-thread-7] WARN  com.simbest.boot.base.repository.CustomDynamicWhere.queryForList Line:50 - 自定义查询SQL输出为：【select * from US_TODO_MODEL t  where t.send_flag = 0 and t.work_flag = 1 and t.enabled =1】
2025-05-30 21:25:00.006 [pool-14-thread-9] WARN  com.simbest.boot.base.repository.CustomDynamicWhere.queryForList Line:50 - 自定义查询SQL输出为：【select * from US_TODO_MODEL t  where t.send_flag = 0 and t.work_flag = 0 and t.enabled =1】
2025-05-30 21:30:00.013 [pool-14-thread-3] WARN  com.simbest.boot.base.repository.CustomDynamicWhere.queryForList Line:50 - 自定义查询SQL输出为：【select * from US_TODO_MODEL t  where t.send_flag = 0 and t.work_flag = 1 and t.enabled =1】
2025-05-30 21:30:00.013 [pool-14-thread-10] WARN  com.simbest.boot.base.repository.CustomDynamicWhere.queryForList Line:50 - 自定义查询SQL输出为：【select * from US_TODO_MODEL t  where t.send_flag = 0 and t.work_flag = 0 and t.enabled =1】
2025-05-30 21:33:53.430 [AMQP Connection ************:5672] WARN  com.rabbitmq.client.impl.ForgivingExceptionHandler.log Line:115 - An unexpected connection driver error occured (Exception message: Connection reset)
2025-05-30 21:33:55.342 [AMQP Connection ************:5672] WARN  com.rabbitmq.client.impl.ForgivingExceptionHandler.log Line:115 - An unexpected connection driver error occured (Exception message: Connection reset)
2025-05-30 21:33:55.483 [AMQP Connection ************:5672] WARN  com.rabbitmq.client.impl.ForgivingExceptionHandler.log Line:115 - An unexpected connection driver error occured (Exception message: Connection reset)
2025-05-30 21:33:56.647 [AMQP Connection ************:5672] WARN  com.rabbitmq.client.impl.ForgivingExceptionHandler.log Line:115 - An unexpected connection driver error occured (Exception message: Connection reset)
2025-05-30 21:34:59.980 [pool-14-thread-8] WARN  com.simbest.boot.base.repository.CustomDynamicWhere.queryForList Line:50 - 自定义查询SQL输出为：【select * from US_TODO_MODEL t  where t.send_flag = 0 and t.work_flag = 1 and t.enabled =1】
2025-05-30 21:34:59.980 [pool-14-thread-5] WARN  com.simbest.boot.base.repository.CustomDynamicWhere.queryForList Line:50 - 自定义查询SQL输出为：【select * from US_TODO_MODEL t  where t.send_flag = 0 and t.work_flag = 0 and t.enabled =1】
2025-05-30 21:57:07.276 [redisson-netty-5-3] WARN  org.redisson.client.handler.CommandDecoder.completeResponse Line:445 - response has been skipped due to timeout! channel: [id: 0xd66200b8, L:/***********:55093 - R:************/************:7003], command: (CLUSTER NODES), params: [], result: [ClusterNodeInfo [nodeId=4dcfd30d1191cb5e65ef69e88dc76592f8b3d14d, address=redis://************:7005, flags=[MASTER], slaveOf=null, slotRanges=[[10923-16383]]], ClusterNodeInfo [nodeId=248f8c5f1a3d07d891f1bcf924c02097c5509e0d, address=redis://************:7001, flags=[SLAVE], slaveOf=64f45f937e6f8c36d0fb7cf09195403c4cccdf1d, slotRanges=[]], ClusterNodeInfo [nodeId=02aff3b78f8c09c43cbfc2346eac68a16a942097, address=redis://************:7003, flags=[MASTER, MYSELF], slaveOf=null, slotRanges=[[5461-10922]]], ClusterNodeInfo [nodeId=85e61682488d3a355264d212607a63832da60e8c, address=redis://************:7006, flags=[SLAVE], slaveOf=02aff3b78f8c09c43cbfc2346eac68a16a942097, slotRanges=[]], ClusterNodeInfo [nodeId=64f45f937e6f8c36d0fb7cf09195403c4cccdf1d, address=redis://************:7004, flags=[MASTER], slaveOf=null, slotRanges=[[0-5460]]], ClusterNodeInfo [nodeId=5514edc6d1aaab0526b0093a424e9f352e9914ed, address=redis://************:7002, flags=[SLAVE], slaveOf=4dcfd30d1191cb5e65ef69e88dc76592f8b3d14d, slotRanges=[]]]
2025-05-30 21:57:44.396 [AMQP Connection ************:5672] WARN  com.rabbitmq.client.impl.ForgivingExceptionHandler.log Line:115 - An unexpected connection driver error occured (Exception message: Connection reset)
2025-05-30 21:57:46.316 [AMQP Connection ************:5672] WARN  com.rabbitmq.client.impl.ForgivingExceptionHandler.log Line:115 - An unexpected connection driver error occured (Exception message: Connection reset)
2025-05-30 21:57:46.441 [AMQP Connection ************:5672] WARN  com.rabbitmq.client.impl.ForgivingExceptionHandler.log Line:115 - An unexpected connection driver error occured (Exception message: Connection reset)
2025-05-30 21:57:47.638 [AMQP Connection ************:5672] WARN  com.rabbitmq.client.impl.ForgivingExceptionHandler.log Line:115 - An unexpected connection driver error occured (Exception message: Connection reset)
2025-05-30 21:59:59.911 [pool-14-thread-3] WARN  com.simbest.boot.cmcc.a4.task.A4TaskCreateXmlFile.execute Line:76 - >>>>>>>>>>>>>>>>>>>>>>>>>>>>>>>>>>>>>>>>>>>>>>>>>>>>>A4TaskCreateXmlFile:执行定时任务但不是Prd环境【2025-05-30T21:59:59.911】
2025-05-30 22:20:50.740 [redisson-netty-5-8] WARN  org.redisson.client.handler.CommandDecoder.completeResponse Line:445 - response has been skipped due to timeout! channel: [id: 0xf87246b4, L:/***********:60243 - R:************/************:7003], command: (CLUSTER NODES), params: [], result: [ClusterNodeInfo [nodeId=4dcfd30d1191cb5e65ef69e88dc76592f8b3d14d, address=redis://************:7005, flags=[MASTER], slaveOf=null, slotRanges=[[10923-16383]]], ClusterNodeInfo [nodeId=248f8c5f1a3d07d891f1bcf924c02097c5509e0d, address=redis://************:7001, flags=[SLAVE], slaveOf=64f45f937e6f8c36d0fb7cf09195403c4cccdf1d, slotRanges=[]], ClusterNodeInfo [nodeId=02aff3b78f8c09c43cbfc2346eac68a16a942097, address=redis://************:7003, flags=[MASTER, MYSELF], slaveOf=null, slotRanges=[[5461-10922]]], ClusterNodeInfo [nodeId=85e61682488d3a355264d212607a63832da60e8c, address=redis://************:7006, flags=[SLAVE], slaveOf=02aff3b78f8c09c43cbfc2346eac68a16a942097, slotRanges=[]], ClusterNodeInfo [nodeId=64f45f937e6f8c36d0fb7cf09195403c4cccdf1d, address=redis://************:7004, flags=[MASTER], slaveOf=null, slotRanges=[[0-5460]]], ClusterNodeInfo [nodeId=5514edc6d1aaab0526b0093a424e9f352e9914ed, address=redis://************:7002, flags=[SLAVE], slaveOf=4dcfd30d1191cb5e65ef69e88dc76592f8b3d14d, slotRanges=[]]]
2025-05-30 22:21:05.344 [AMQP Connection ************:5672] WARN  com.rabbitmq.client.impl.ForgivingExceptionHandler.log Line:115 - An unexpected connection driver error occured (Exception message: Connection reset)
2025-05-30 22:21:07.319 [AMQP Connection ************:5672] WARN  com.rabbitmq.client.impl.ForgivingExceptionHandler.log Line:115 - An unexpected connection driver error occured (Exception message: Connection reset)
2025-05-30 22:21:07.396 [AMQP Connection ************:5672] WARN  com.rabbitmq.client.impl.ForgivingExceptionHandler.log Line:115 - An unexpected connection driver error occured (Exception message: Connection reset)
2025-05-30 22:21:08.612 [AMQP Connection ************:5672] WARN  com.rabbitmq.client.impl.ForgivingExceptionHandler.log Line:115 - An unexpected connection driver error occured (Exception message: Connection reset)
2025-05-30 22:44:25.404 [AMQP Connection ************:5672] WARN  com.rabbitmq.client.impl.ForgivingExceptionHandler.log Line:115 - An unexpected connection driver error occured (Exception message: Connection reset)
2025-05-30 22:44:27.383 [AMQP Connection ************:5672] WARN  com.rabbitmq.client.impl.ForgivingExceptionHandler.log Line:115 - An unexpected connection driver error occured (Exception message: Connection reset)
2025-05-30 22:44:27.447 [AMQP Connection ************:5672] WARN  com.rabbitmq.client.impl.ForgivingExceptionHandler.log Line:115 - An unexpected connection driver error occured (Exception message: Connection reset)
2025-05-30 22:44:28.692 [AMQP Connection ************:5672] WARN  com.rabbitmq.client.impl.ForgivingExceptionHandler.log Line:115 - An unexpected connection driver error occured (Exception message: Connection reset)
2025-05-30 22:50:00.009 [pool-14-thread-7] WARN  com.simbest.boot.base.repository.CustomDynamicWhere.queryForList Line:50 - 自定义查询SQL输出为：【select * from US_TODO_MODEL t  where t.send_flag = 0 and t.work_flag = 0 and t.enabled =1】
2025-05-30 22:50:00.011 [pool-14-thread-2] WARN  com.simbest.boot.base.repository.CustomDynamicWhere.queryForList Line:50 - 自定义查询SQL输出为：【select * from US_TODO_MODEL t  where t.send_flag = 0 and t.work_flag = 1 and t.enabled =1】
2025-05-30 22:55:00.015 [pool-14-thread-4] WARN  com.simbest.boot.base.repository.CustomDynamicWhere.queryForList Line:50 - 自定义查询SQL输出为：【select * from US_TODO_MODEL t  where t.send_flag = 0 and t.work_flag = 0 and t.enabled =1】
2025-05-30 22:55:00.015 [pool-14-thread-7] WARN  com.simbest.boot.base.repository.CustomDynamicWhere.queryForList Line:50 - 自定义查询SQL输出为：【select * from US_TODO_MODEL t  where t.send_flag = 0 and t.work_flag = 1 and t.enabled =1】
2025-05-30 22:59:59.954 [pool-14-thread-2] WARN  com.simbest.boot.cmcc.a4.task.A4TaskCreateXmlFile.execute Line:76 - >>>>>>>>>>>>>>>>>>>>>>>>>>>>>>>>>>>>>>>>>>>>>>>>>>>>>A4TaskCreateXmlFile:执行定时任务但不是Prd环境【2025-05-30T22:59:59.954】
2025-05-30 23:00:00.015 [pool-14-thread-2] WARN  com.simbest.boot.base.repository.CustomDynamicWhere.queryForList Line:50 - 自定义查询SQL输出为：【select * from US_TODO_MODEL t  where t.send_flag = 0 and t.work_flag = 0 and t.enabled =1】
2025-05-30 23:00:00.016 [pool-14-thread-9] WARN  com.simbest.boot.base.repository.CustomDynamicWhere.queryForList Line:50 - 自定义查询SQL输出为：【select * from US_TODO_MODEL t  where t.send_flag = 0 and t.work_flag = 1 and t.enabled =1】
2025-05-30 23:08:01.016 [pool-14-thread-7] WARN  com.simbest.boot.base.repository.CustomDynamicWhere.queryForList Line:50 - 自定义查询SQL输出为：【select * from US_TODO_MODEL t  where t.send_flag = 0 and t.work_flag = 0 and t.enabled =1】
2025-05-30 23:08:01.017 [pool-14-thread-2] WARN  com.simbest.boot.base.repository.CustomDynamicWhere.queryForList Line:50 - 自定义查询SQL输出为：【select * from US_TODO_MODEL t  where t.send_flag = 0 and t.work_flag = 1 and t.enabled =1】
2025-05-30 23:08:16.233 [AMQP Connection ************:5672] WARN  com.rabbitmq.client.impl.ForgivingExceptionHandler.log Line:115 - An unexpected connection driver error occured (Exception message: Connection reset)
2025-05-30 23:08:18.058 [AMQP Connection ************:5672] WARN  com.rabbitmq.client.impl.ForgivingExceptionHandler.log Line:115 - An unexpected connection driver error occured (Exception message: Connection reset)
2025-05-30 23:08:18.216 [AMQP Connection ************:5672] WARN  com.rabbitmq.client.impl.ForgivingExceptionHandler.log Line:115 - An unexpected connection driver error occured (Exception message: Connection reset)
2025-05-30 23:08:19.403 [AMQP Connection ************:5672] WARN  com.rabbitmq.client.impl.ForgivingExceptionHandler.log Line:115 - An unexpected connection driver error occured (Exception message: Connection reset)
2025-05-30 23:31:09.895 [redisson-netty-5-24] WARN  org.redisson.client.handler.CommandDecoder.completeResponse Line:445 - response has been skipped due to timeout! channel: [id: 0xcb17766f, L:/***********:51195 - R:************/************:7003], command: (CLUSTER NODES), params: [], result: [ClusterNodeInfo [nodeId=4dcfd30d1191cb5e65ef69e88dc76592f8b3d14d, address=redis://************:7005, flags=[MASTER], slaveOf=null, slotRanges=[[10923-16383]]], ClusterNodeInfo [nodeId=248f8c5f1a3d07d891f1bcf924c02097c5509e0d, address=redis://************:7001, flags=[SLAVE], slaveOf=64f45f937e6f8c36d0fb7cf09195403c4cccdf1d, slotRanges=[]], ClusterNodeInfo [nodeId=02aff3b78f8c09c43cbfc2346eac68a16a942097, address=redis://************:7003, flags=[MASTER, MYSELF], slaveOf=null, slotRanges=[[5461-10922]]], ClusterNodeInfo [nodeId=85e61682488d3a355264d212607a63832da60e8c, address=redis://************:7006, flags=[SLAVE], slaveOf=02aff3b78f8c09c43cbfc2346eac68a16a942097, slotRanges=[]], ClusterNodeInfo [nodeId=64f45f937e6f8c36d0fb7cf09195403c4cccdf1d, address=redis://************:7004, flags=[MASTER], slaveOf=null, slotRanges=[[0-5460]]], ClusterNodeInfo [nodeId=5514edc6d1aaab0526b0093a424e9f352e9914ed, address=redis://************:7002, flags=[SLAVE], slaveOf=4dcfd30d1191cb5e65ef69e88dc76592f8b3d14d, slotRanges=[]]]
2025-05-30 23:31:36.836 [AMQP Connection ************:5672] WARN  com.rabbitmq.client.impl.ForgivingExceptionHandler.log Line:115 - An unexpected connection driver error occured (Exception message: Connection reset)
2025-05-30 23:31:38.741 [AMQP Connection ************:5672] WARN  com.rabbitmq.client.impl.ForgivingExceptionHandler.log Line:115 - An unexpected connection driver error occured (Exception message: Connection reset)
2025-05-30 23:31:38.897 [AMQP Connection ************:5672] WARN  com.rabbitmq.client.impl.ForgivingExceptionHandler.log Line:115 - An unexpected connection driver error occured (Exception message: Connection reset)
2025-05-30 23:31:39.970 [AMQP Connection ************:5672] WARN  com.rabbitmq.client.impl.ForgivingExceptionHandler.log Line:115 - An unexpected connection driver error occured (Exception message: Connection reset)
2025-05-30 23:54:41.723 [redisson-netty-5-22] WARN  org.redisson.client.handler.CommandDecoder.completeResponse Line:445 - response has been skipped due to timeout! channel: [id: 0x0509b680, L:/***********:64167 - R:************/************:7003], command: (CLUSTER NODES), params: [], result: [ClusterNodeInfo [nodeId=4dcfd30d1191cb5e65ef69e88dc76592f8b3d14d, address=redis://************:7005, flags=[MASTER], slaveOf=null, slotRanges=[[10923-16383]]], ClusterNodeInfo [nodeId=248f8c5f1a3d07d891f1bcf924c02097c5509e0d, address=redis://************:7001, flags=[SLAVE], slaveOf=64f45f937e6f8c36d0fb7cf09195403c4cccdf1d, slotRanges=[]], ClusterNodeInfo [nodeId=02aff3b78f8c09c43cbfc2346eac68a16a942097, address=redis://************:7003, flags=[MASTER, MYSELF], slaveOf=null, slotRanges=[[5461-10922]]], ClusterNodeInfo [nodeId=85e61682488d3a355264d212607a63832da60e8c, address=redis://************:7006, flags=[SLAVE], slaveOf=02aff3b78f8c09c43cbfc2346eac68a16a942097, slotRanges=[]], ClusterNodeInfo [nodeId=64f45f937e6f8c36d0fb7cf09195403c4cccdf1d, address=redis://************:7004, flags=[MASTER], slaveOf=null, slotRanges=[[0-5460]]], ClusterNodeInfo [nodeId=5514edc6d1aaab0526b0093a424e9f352e9914ed, address=redis://************:7002, flags=[SLAVE], slaveOf=4dcfd30d1191cb5e65ef69e88dc76592f8b3d14d, slotRanges=[]]]
2025-05-30 23:54:59.113 [AMQP Connection ************:5672] WARN  com.rabbitmq.client.impl.ForgivingExceptionHandler.log Line:115 - An unexpected connection driver error occured (Exception message: Connection reset)
2025-05-30 23:55:01.070 [AMQP Connection ************:5672] WARN  com.rabbitmq.client.impl.ForgivingExceptionHandler.log Line:115 - An unexpected connection driver error occured (Exception message: Connection reset)
2025-05-30 23:55:01.148 [AMQP Connection ************:5672] WARN  com.rabbitmq.client.impl.ForgivingExceptionHandler.log Line:115 - An unexpected connection driver error occured (Exception message: Connection reset)
2025-05-30 23:55:02.238 [AMQP Connection ************:5672] WARN  com.rabbitmq.client.impl.ForgivingExceptionHandler.log Line:115 - An unexpected connection driver error occured (Exception message: Connection reset)
2025-05-30 23:59:59.916 [pool-14-thread-9] WARN  com.simbest.boot.cmcc.a4.task.A4TaskCreateXmlFile.execute Line:76 - >>>>>>>>>>>>>>>>>>>>>>>>>>>>>>>>>>>>>>>>>>>>>>>>>>>>>A4TaskCreateXmlFile:执行定时任务但不是Prd环境【2025-05-30T23:59:59.916】
