package com.simbest.boot.hnjjwz.examOnline.service;


import com.simbest.boot.base.service.ILogicService;
import com.simbest.boot.base.web.response.JsonResponse;
import com.simbest.boot.hnjjwz.examOnline.model.ExamQuestion;
import org.springframework.data.domain.Pageable;

import java.util.List;

/**
 * <strong>Title : </strong><br>
 * <strong>Description : </strong><br>
 * <strong>Create on : 2018/6/19</strong><br>
 * <strong>Modify on : 2018/6/19</strong><br>
 * <strong>Copyright (C) Ltd.</strong><br>
 *
 * <AUTHOR> 刘萌@simbest.com.cn
 * @version <strong>V1.0.0</strong><br>
 * <strong>修改历史:</strong><br>
 * 修改人 修改日期 修改描述<br>
 * -------------------------------------------<br>
 */
public interface IExamQuestionService extends ILogicService<ExamQuestion,String> {

    /**
     * 查询可用的题库内容
     * @return
     */
    JsonResponse findEnabledExamQuestion();

    /**
     * 获取随机试题
     * @return
     */
    List<ExamQuestion> customFindAll(String questionBankCode,String choice,String questionType);

    /**
     * 获取全部试题
     * @return
     */
    JsonResponse findAllExamQuestion(Pageable pageable);

}
