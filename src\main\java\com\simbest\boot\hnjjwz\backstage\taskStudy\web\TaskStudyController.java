package com.simbest.boot.hnjjwz.backstage.taskStudy.web;/**
 * Created by KZH on 2019/6/13 9:36.
 */

import com.simbest.boot.base.web.controller.LogicController;
import com.simbest.boot.base.web.response.JsonResponse;
import com.simbest.boot.hnjjwz.backstage.taskStudy.model.TaskStudy;
import com.simbest.boot.hnjjwz.backstage.taskStudy.service.ITaskStudyService;
import com.simbest.boot.hnjjwz.util.FileTool;
import com.simbest.boot.sys.service.ISysFileService;
import io.swagger.annotations.Api;
import io.swagger.annotations.ApiImplicitParam;
import io.swagger.annotations.ApiImplicitParams;
import io.swagger.annotations.ApiOperation;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.data.domain.Pageable;
import org.springframework.web.bind.annotation.*;

import java.util.Map;

/**
 * <AUTHOR>
 * @create 2019-06-13 9:36
 * @desc
 **/
@Api(description = "课题研究")
@Slf4j
@RestController
@RequestMapping(value = "/action/taskStudy")
public class TaskStudyController extends LogicController<TaskStudy, String> {


    private ITaskStudyService iTaskStudyService;

    public TaskStudyController(ITaskStudyService iTaskStudyService) {
        super(iTaskStudyService);
        this.iTaskStudyService=iTaskStudyService;
    }

    @Autowired
    private ISysFileService sysFileService;

    @Autowired
    private FileTool fileTool;

    /**
     * 提交下一步
     * @param currentUserCode
     * @param workItemId
     * @param outcome
     * @param location
     * @param bodyParam
     * @return
     * @throws Exception
     */
    @ApiOperation(value = "提交起草流程", notes = "通过此接口启动流转审批表单")
    @ApiImplicitParams({
            @ApiImplicitParam(name = "currentUserCode", value = "当前登录人", dataType = "String", paramType = "query",required = true),
            @ApiImplicitParam(name = "workItemId", value = "工作项ID", dataType = "String", paramType = "query"),
            @ApiImplicitParam(name = "outcome", value = "连线规则", dataType = "String", paramType = "query", required = true),
            @ApiImplicitParam(name = "message", value = "审批意见", dataType = "String", paramType = "query",required = true),
            @ApiImplicitParam(name = "location", value = "当前状态", dataType = "String", paramType = "query"),
            @ApiImplicitParam(name = "formId", value = "表单id", dataType = "String", paramType = "query", required = false),
            @ApiImplicitParam(name = "source", value = "来源", dataType = "String", paramType = "query", required = false)
    })
    @PostMapping(value = {"/startSubmitProcess","/startSubmitProcess/sso","/api/startSubmitProcess"})
    public JsonResponse startSubmitProcessMS(@RequestParam String currentUserCode,
                                             @RequestParam(required = false) String workItemId,
                                             @RequestParam String outcome,
                                             @RequestParam(required = false) String location,
                                             @RequestParam(required = false) String source,
                                             @RequestParam(required = false) String formId,
                                             @RequestBody(required = false) Map<String,Object> bodyParam
    ) throws Exception {
        return iTaskStudyService.nextStep(currentUserCode, workItemId, outcome, location,formId,source,bodyParam);
    }

    /**
     * 详情办理
     * @param processInstId 流程实例idgetApprovalFromDetail
     * @return
     */
    @ApiOperation(value = "打开详情", notes = "办理")
    @ApiImplicitParams({
            @ApiImplicitParam(name = "processInstId", value = "流程实例id，用于流程中获取详情", dataType = "Long", paramType = "query"),
            @ApiImplicitParam(name = "source", value = "来源", dataType = "String", paramType = "query"),
            @ApiImplicitParam(name = "currentUserCode", value = "当前用户", dataType = "String", paramType = "query"),
            @ApiImplicitParam(name = "pmInstId", value = "表单中的流程id", dataType = "String", paramType = "query"),
    })
    @PostMapping(value = {"/getApprovalFromDetail", "/getApprovalFromDetail/sso","/api/getApprovalFromDetail"})
    public JsonResponse getApprovalFromDetail(@RequestParam(required = false) Long processInstId,
                                              @RequestParam(required = false) String source,
                                              @RequestParam(required = false) String currentUserCode,
                                              @RequestParam(required = false) String pmInsId,
                                              @RequestParam(required = false) String location){
        return iTaskStudyService.getApprovalFromDetail(processInstId,source,currentUserCode,pmInsId,location);
    }

    /**
     * 查询决策
     * @param processInstId 流程实例id
     * @param processDefName 流程定义名称
     * @param location       当前环节
     * @return

    @ApiOperation( value = "查询决策" , notes = "根据当前环节提供相应的决策")
    @ApiImplicitParams({
            @ApiImplicitParam(name = "processInstId", value = "流程实例ID", dataType = "String", paramType = "query"),
            @ApiImplicitParam(name = "processDefName", value = "流程定义名称", dataType = "String" ,paramType = "query"),
            @ApiImplicitParam(name = "location", value = "当前环节", dataType = "String", paramType = "query", required = true),
            @ApiImplicitParam(name = "source", value = "来源", dataType = "String", paramType = "query"),
            @ApiImplicitParam(name = "currentUserCode", value = "当前人", dataType = "String", paramType = "query")
    })
    @PostMapping(value = {"/getDecisions","/getDecisions/sso","/api/getDecisions"})
    public JsonResponse getDecisions(@RequestParam(required = false) String processInstId,
                                     @RequestParam(required = false) String processDefName ,
                                     @RequestParam(required = false ) String location,
                                     @RequestParam(required = false ) String source,
                                     @RequestParam(required = false ) String currentUserCode){
        return JsonResponse.success(iTaskStudyService.getDecisions( processInstId,processDefName,location,source,currentUserCode ));
    }*/

    /**
     * 注销该流程
     * @param currentUserCode 当前登录人
     * @param pmInstId 流程实例id
     * @param
     * @return
     */
    @ApiOperation(value = "注销该流程2", notes = "注销起草的流程2")
    @ApiImplicitParams({
            @ApiImplicitParam(name = "currentUserCode", value = "当前登录人", dataType = "String", paramType = "query",required = true),
            @ApiImplicitParam(name = "processInstId", value = "流程实例id", dataType = "Long", paramType = "query",required = true),
    })
    @PostMapping(value = {"/terminateProcessInst","/api/terminateProcessInst"})
    public JsonResponse terminateProcessInst  (@RequestParam(required = false) String currentUserCode,
                                               @RequestParam(required = false) Long pmInstId,
                                               @RequestParam(required = false) String source){
        return JsonResponse.success(iTaskStudyService.terminateProcessInst(pmInstId),"注销成功！");
    }


    /**
     * 查询栏目详情列表2
     * @param page
     * @param size
     * @param direction
     * @param properties
     * @param paramsMap
     * @return
     */
    @ApiOperation(value = "查询栏目详情列表", notes = "查询栏目详情列表")
    @ApiImplicitParams( {
            @ApiImplicitParam(name = "page", value = "当前页码", dataType = "int", paramType = "query", example = "1"), //
            @ApiImplicitParam(name = "size", value = "每页数量", dataType = "int", paramType = "query", example = "10"), //
            @ApiImplicitParam(name = "direction", value = "排序规则（asc/desc）", dataType = "String", //
                    paramType = "query"), //
            @ApiImplicitParam(name = "properties", value = "排序规则（属性名称）", dataType = "String", //
                    paramType = "query")
    } )
    @PostMapping (value = {"/findDataDetailList2","/findDataDetailList2/sso"})
    public JsonResponse findDataDetailList2(@RequestParam(required = false, defaultValue = "1") int page, //
                                            @RequestParam(required = false, defaultValue = "10") int size, //
                                            @RequestParam(required = false) String direction, //
                                            @RequestParam(required = false) String properties,
                                            @RequestBody(required = false) Map<String,Object> paramsMap) {
        Pageable pageable = iTaskStudyService.getPageable(page, size, "desc", "creation_time");
        return  iTaskStudyService.findDataDetailList2(paramsMap, pageable);
    }


    /**
     * 再根据pm_ins_id字段获取ApprovalForm对象
     * @param map
     * @return
     */
    @ApiOperation(value = "再根据pm_ins_id字段获取ApprovalForm对象", notes = "再根据pm_ins_id字段获取ApprovalForm对象")
    @PostMapping (value = {"/getTaskStudyPmInsId","/getTaskStudyPmInsId/sso"})
    public JsonResponse getTaskStudyPmInsId( @RequestBody Map<String, Object> map) {
        String pmInsId=String.valueOf(map.get("pmInstId"));
        return JsonResponse.success(iTaskStudyService.getTaskStudyPmInsId(pmInsId));
    }

    @ApiOperation(value = "保存草稿", notes = "保存草稿")
    @PostMapping(value = {"/saveDraft", "/saveDraft/api"})
    public JsonResponse saveDraft(@RequestParam(required = false) String source,
                                  @RequestParam(required = false) String currentUserCode,
                                  @RequestBody TaskStudy innovationTopicForm) {
        return iTaskStudyService.saveDraft(source, currentUserCode, innovationTopicForm);
    }

    @ApiOperation(value = "废除草稿", notes = "废除草稿")
    @PostMapping(value = {"/deleteDraft", "/deleteDraft/api"})
    public JsonResponse deleteDraft(@RequestParam(required = false) String source,
                                    @RequestParam(required = false) String currentUserCode,
                                    @RequestParam(required = false) String pmInsId,
                                    @RequestBody TaskStudy innovationTopicForm) {
        return iTaskStudyService.deleteDraft(source, currentUserCode, pmInsId, innovationTopicForm);
    }
}
