package com.simbest.boot.hnjjwz.util;

import org.springframework.beans.factory.annotation.Value;
import org.springframework.stereotype.Component;

@Component
//@PropertySource (value = {"classpath:bpsConfig.properties"},encoding="utf-8")
public class BpsConfig {

    @Value( "${app.bps.tenant}" )
    public String bpsTenant;

    @Value( "${app.bps.tenant_id}" )
    public String bpsTenantId;

    @Value( "${app.host.port}" )
    public String hostPost;
}
