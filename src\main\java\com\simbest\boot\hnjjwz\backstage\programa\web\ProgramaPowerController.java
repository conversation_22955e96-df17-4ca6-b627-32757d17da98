package com.simbest.boot.hnjjwz.backstage.programa.web;/**
 * Created by KZH on 2019/6/26 15:10.
 */

import com.simbest.boot.base.web.controller.LogicController;
import com.simbest.boot.hnjjwz.backstage.programa.model.ProgramaPower;
import com.simbest.boot.hnjjwz.backstage.programa.service.IProgramaPowerImpl;
import io.swagger.annotations.Api;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RestController;

/**
 * <AUTHOR>
 * @create 2019-06-26 15:10
 * @desc 栏目权限相关接口
 **/
@Api(description = "栏目权限相关接口")
@Slf4j
@RestController
@RequestMapping(value = "/action/programaPower")
public class ProgramaPowerController extends LogicController<ProgramaPower,String> {

    private IProgramaPowerImpl iProgramaPower;
    @Autowired
    public ProgramaPowerController(IProgramaPowerImpl iProgramaPower){
        super(iProgramaPower);
        this.iProgramaPower=iProgramaPower;

    }
}
