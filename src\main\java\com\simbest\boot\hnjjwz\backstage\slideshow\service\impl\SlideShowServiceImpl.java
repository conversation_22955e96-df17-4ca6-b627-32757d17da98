package com.simbest.boot.hnjjwz.backstage.slideshow.service.impl;

import cn.hutool.core.collection.CollUtil;
import cn.hutool.core.collection.CollectionUtil;
import cn.hutool.core.util.ObjectUtil;
import cn.hutool.core.util.StrUtil;
import com.alibaba.druid.util.StringUtils;
import com.fasterxml.jackson.core.type.TypeReference;
import com.google.common.collect.Maps;
import com.simbest.boot.base.exception.Exceptions;
import com.simbest.boot.base.service.impl.LogicService;

import com.simbest.boot.base.web.response.JsonResponse;
import com.simbest.boot.bps.process.bussiness.model.ActBusinessStatus;
import com.simbest.boot.bps.process.bussiness.service.IActBusinessStatusService;
import com.simbest.boot.constants.ApplicationConstants;
import com.simbest.boot.hnjjwz.attachment.service.IFileExtendService;
import com.simbest.boot.hnjjwz.backstage.announcement.model.Announcement;
import com.simbest.boot.hnjjwz.backstage.slideshow.model.SlideShow;
import com.simbest.boot.hnjjwz.backstage.slideshow.repository.SlideShowRepository;
import com.simbest.boot.hnjjwz.backstage.slideshow.service.ISlideShowService;
import com.simbest.boot.hnjjwz.backstage.taskStudy.model.TaskStudy;
import com.simbest.boot.hnjjwz.constants.Constants;
import com.simbest.boot.hnjjwz.newcolumn.service.ISysNewProgramaService;
import com.simbest.boot.hnjjwz.process.wf.model.PmInstence;
import com.simbest.boot.hnjjwz.process.wf.model.ProgramaDataForm;
import com.simbest.boot.hnjjwz.process.wf.service.IPmInstenceService;
import com.simbest.boot.hnjjwz.process.wf.service.IProgramaDataFormService;
import com.simbest.boot.hnjjwz.util.GZIPUtils;
import com.simbest.boot.hnjjwz.util.ImgToBase64Tool;
import com.simbest.boot.hnjjwz.util.OperateLogTool;
import com.simbest.boot.hnjjwz.util.UumsQuerySqlUtil;
import com.simbest.boot.security.*;
import com.simbest.boot.sys.model.SysFile;
import com.simbest.boot.sys.model.SysOperateLog;
import com.simbest.boot.sys.service.ISysDictValueService;
import com.simbest.boot.sys.service.ISysFileService;
import com.simbest.boot.sys.service.ISysOperateLogService;
import com.simbest.boot.templates.MessageEnum;
import com.simbest.boot.util.DateUtil;
import com.simbest.boot.util.distribution.id.IdGenerator;
import com.simbest.boot.util.distribution.id.RedisIdGenerator;
import com.simbest.boot.util.json.JacksonUtils;
import com.simbest.boot.util.security.SecurityUtils;
import com.simbest.boot.uums.api.app.UumsSysAppDecisionApi;
import com.simbest.boot.uums.api.group.UumsSysGroupApi;
import com.simbest.boot.uums.api.org.UumsSysOrgApi;
import com.simbest.boot.uums.api.user.UumsSysUserinfoApi;
import com.simbest.boot.wf.process.service.IProcessInstanceService;
import com.simbest.boot.wf.process.service.IWorkItemService;
import com.simbest.boot.wf.unitfytodo.IProcessTodoDataService;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.data.domain.Page;
import org.springframework.data.domain.PageImpl;
import org.springframework.data.domain.Pageable;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;
import org.springframework.transaction.interceptor.TransactionAspectSupport;

import java.time.LocalDateTime;
import java.util.*;

/**
 * <AUTHOR>
 * @Data 2019/03/29
 * @Description
 */
@Slf4j
@Service
public class SlideShowServiceImpl extends LogicService<SlideShow, String> implements ISlideShowService {

    private SlideShowRepository slideShowRepository;

    @Autowired
    private ISysFileService sysFileService;

    @Autowired
    private IFileExtendService fileExtendService;

    @Autowired
    private IProcessInstanceService iProcessInstanceService;

    @Autowired
    private UumsQuerySqlUtil uumsQuerySqlUtil;

    @Autowired
    public SlideShowServiceImpl(SlideShowRepository slideShowRepository) {
        super(slideShowRepository);
        this.slideShowRepository = slideShowRepository;
    }

    @Autowired
    private UumsSysUserinfoApi uumsSysUserinfoApi;

    @Autowired
    private IPmInstenceService pmInstenceService;

    @Autowired
    private IProcessInstanceService processInstanceService;

    @Autowired
    private IWorkItemService workItemService;

    @Autowired
    private IActBusinessStatusService statusService;

    @Autowired
    private RedisIdGenerator idGenerator;

    @Autowired
    private ISysOperateLogService operateLogService;

    @Autowired
    private OperateLogTool operateLogTool;

    @Value("${app.host.port}")
    private String appIp;

    private final String param1 = "/action/slideShow";

    @Autowired
    public ISysDictValueService sysDictValueService;


    @Autowired
    private ISysNewProgramaService newProgramaService;

    @Override
    public List<SlideShow> findSlideShow() {
        List<SlideShow> slideShow = slideShowRepository.findSlideShow();

        for (SlideShow s : slideShow) {
            List<SysFile> sysFileList = new ArrayList<>();
            String slideShowPhotoId = s.getSlideShowPhotoId();
            if (!StringUtils.isEmpty(slideShowPhotoId)) {
                SysFile sysfile = sysFileService.findById(slideShowPhotoId);
                sysFileList.add(sysfile);
                s.setSlideShowFile(sysFileList);
            }
        }
        return slideShow;
    }

    /**
     * 流转下一步
     *
     * @param currentUserCode
     * @param workItemId
     * @param outcome
     * @param location
     * @param bodyParam       前台传来的下一步的数据
     * @return
     */
    @Override
    public JsonResponse nextStep(String currentUserCode, String workItemId, String outcome, String location, String formId, String source, Map<String, Object> bodyParam) {
        if (source == null && !("PC".equals(source) || "MOBILE".equals(source))) {
            source = "PC";
        }
        Map flowParam = (Map) bodyParam.get("flowParam");
        String message = (String) flowParam.get("message");
        //获取form表单
        Object formData = flowParam.get("formData");
        SlideShow slideShow = new SlideShow();
        if (formData != null) {
            String formDataJson = JacksonUtils.obj2json(formData);
            slideShow = JacksonUtils.json2Type(formDataJson, new TypeReference<SlideShow>() {
            });
        } else {
            if (!org.springframework.util.StringUtils.isEmpty(formId) && "MOBILE".equals(source)) {
                slideShow = this.findById(formId);
            }
        }
        Object nextUserNameObject = flowParam.get("nextUserName");
        String nextUsernameJson = JacksonUtils.obj2json(nextUserNameObject);
        List<Map<String, Object>> nextUsernameList = JacksonUtils.json2Type(nextUsernameJson, new TypeReference<List<Map<String, Object>>>() {
        });
        String nextUserName = new String();

        int resultNum = 0;

        /**
         *
         * 废除归档
         */
        if (Constants.ACTIVITY_REJECT_END.equals(flowParam.get("decisionId"))) {

            for (Map<String, Object> nextUsernameMap : nextUsernameList) {
                String display = (String) nextUsernameMap.get("display");
                if ("user".equals(display)) {
                    resultNum = saveSubmitTask(slideShow, workItemId, outcome, message, nextUserName, location, source, currentUserCode);//审批
                }
                if (resultNum == 0) {
                    return JsonResponse.fail(-1, "流转不成功！");
                }
            }
        } else if (Constants.ACTIVITY_END.equals(outcome)) {
            //如果是归档，更新发布时间以及是否发布
            resultNum = saveSubmitTask(slideShow, workItemId, outcome, message, nextUserName, location, source, currentUserCode);//审批
            if (resultNum == 0) {
                return JsonResponse.fail(-1, "流转不成功！");
            }

        } else {
            if (nextUsernameList == null || nextUsernameList.isEmpty()) {
                return JsonResponse.fail(-1, "流转时没有人！");
            }

            for (Map<String, Object> nextUsernameMap : nextUsernameList) {
                String display = (String) nextUsernameMap.get("display");
                if ("user".equals(display)) {
                    nextUserName = (String) nextUsernameMap.get("value");

                    //纪检网站没有归档，所以如果是省公司流程中的部门领导审批(不传人)或者是分公司流程中省公司部门领导审核(不传人)或者传人的时候才可以流转
                    if (Constants.ACTIVITY_PROVINCE_DEPART.equals(location) || Constants.ACTIVITY_FILIALE_DEPART.equals(location) || (!org.springframework.util.StringUtils.isEmpty(nextUserName) && !"null".equals(nextUserName))) {

                        if (slideShow.getId() != null && workItemId != null && !"".equals(workItemId)) {

                            resultNum = saveSubmitTask(slideShow, workItemId, outcome, message, nextUserName, location, source, currentUserCode);//审批
                        } else {
                            resultNum = startProcess(slideShow, nextUserName, outcome, message, source, currentUserCode);//创建提交
                        }
                    } else {
                        return JsonResponse.fail(null, "流转时的下一个人不能为空！");
                    }
                    if (resultNum > 0) {
                        continue;
                    } else {
                        return JsonResponse.fail(-1, "流转不成功！");
                    }
                } else {
                    //没有为组织的情况
                    log.debug("传来的人员组织信息不正确！不能为组织！");
                    return JsonResponse.fail(-1, "传来的人员组织信息不正确！不能为组织！");
                }
            }
        }


        String showMessage = this.getTemplate(nextUserName);
        return JsonResponse.success(1, showMessage);
    }

    /**
     * 起草发起流程
     *
     * @param slideShow    会议活动表单
     * @param nextUserName 审批人
     * @param outcome      连线规则
     * @param message      审批意见
     */
    @Override
    public int startProcess(SlideShow slideShow, String nextUserName, String outcome, String message, String source, String currentUserCode) {
        int ret = 1;
        //准备操作日志参数
        SysOperateLog operateLog = new SysOperateLog();
        String param2 = "/startSubmitProcess";
        String params = "plan=" + slideShow.toString() + ",source=" + source + ",userCode=" + currentUserCode + ",outcome=" + outcome + ",message=" + message + ",nextUserName=" + nextUserName;
        operateLog.setInterfaceParam(params);
        //主单据
        PmInstence pmInstence = new PmInstence();
        try {
            //判断是否是从手机端还是PC端记录操作日志
            JsonResponse returnObj = operateLogTool.operationSource(source, currentUserCode, param1, param2, operateLog);
            if (returnObj != null) {
                log.debug("判断是手机端还是pc端操作日志有误！");
                ret = 0;
            }
            IUser iuser = SecurityUtils.getCurrentUser();
            String companyTypeDictDesc = iuser.getBelongCompanyTypeDictValue();
            /**校验表单和下一步审批人是否为空**/
            if (StrUtil.isNotEmpty(nextUserName)) {
                /**获取登录人所在公司应启动的流程**/
                String processUserType = "";
                if ("01".equals(companyTypeDictDesc)) {
                    processUserType = "E";
                } else {
                    processUserType = "F";
                }
                Map<String, String> map = this.getUserProcessMap(processUserType);
                String processDefId = map.get("processName");
                String processType = map.get("processType");
                if (StrUtil.isNotEmpty(processDefId) && StrUtil.isNotEmpty(processType)) {
                    boolean flag = false;
                    //草稿判断
                    PmInstence usPmInstence = new PmInstence();
                    if (slideShow.getId() != null && slideShow.getPmInsId() != null) {
                        super.update(slideShow);
                        this.updateFileByPmInsId(slideShow, slideShow.getPmInsId(), usPmInstence.getPmInsType());
                        usPmInstence = pmInstenceService.findByPmInsId(slideShow.getPmInsId());
                        if (!StrUtil.equals(slideShow.getSlideShowTitle(), usPmInstence.getPmInsTitle())) {
                            usPmInstence.setPmInsTitle(slideShow.getSlideShowTitle());
                            pmInstenceService.update(usPmInstence);
                        }
                        flag = true;
                    } else {
                        usPmInstence.setPmInsType(processType);
                        /**保存业务数据**/
                        flag = this.savePlanTask(slideShow, usPmInstence);
                    }
                    /**启动发起流程**/
                    if (flag) {
                        Map<String, Object> variables = Maps.newHashMap();
                        String currentUserCode1 = iuser.getUsername();
                        String currentUserName = iuser.getTruename();
                        variables.put("inputUserId", currentUserCode1);
                        variables.put("receiptId", usPmInstence.getId());
                        variables.put("title", usPmInstence.getPmInsTitle());
                        variables.put("code", usPmInstence.getPmInsId());
                        variables.put("currentUserCode", currentUserCode1);
                        variables.put("activityDefID", Constants.ACTIVITY_START);
                        variables.put("appCode", Constants.APP_CODE);

                        //第一个参数为流程定义名称
                        Long workItemId = processInstanceService.startProcessAndSetRelativeData(processDefId, usPmInstence.getPmInsTitle(), usPmInstence.getPmInsTitle(), false, variables);
                        if (workItemId != 0) {
                            /**提交表单审批处理**/
                            if (StrUtil.isNotEmpty(nextUserName)) {
                                ret = this.processApproval(workItemId, currentUserCode1, currentUserName, nextUserName, outcome, message, usPmInstence);
                            } else {
                                operateLog.setErrorMsg("获取审批人失败");
                                JsonResponse.fail(null, "获取审批人失败");
                            }
                        } else {
                            operateLog.setErrorMsg("启动流程失败");
                            JsonResponse.fail(null, "启动流程失败");
                        }
                    } else {
                        operateLog.setErrorMsg("保存失败");
                        JsonResponse.fail(null, "保存失败");
                    }
                } else {
                    operateLog.setErrorMsg("获取流程失败");
                    JsonResponse.fail(null, "操作失败，获取流程失败!");
                }
            } else {
                operateLog.setErrorMsg("表单为空或审批人为空");
                JsonResponse.fail(null, "操作失败，请确认申请表单和审批人!");
            }
        } catch (Exception e) {
            ret = 0;
            operateLog.setErrorMsg(e.toString());
            Exceptions.printException(e);
        } finally {
            /**保存操作记录**/
            operateLogService.saveLog(operateLog);
            return ret;
        }
    }

    /**
     * 工单流转
     *
     * @param slideShow       会议活动表单
     * @param workItemID      工作项ID
     * @param outcome         连线规则
     * @param message         审批意见
     * @param nextUserName    审批人
     * @param location        当前环节
     * @param source          来源
     * @param currentUserCode
     * @return
     */
    @Override
    public int saveSubmitTask(SlideShow slideShow, String workItemID, String outcome, String message, String nextUserName, String location, String source, String currentUserCode) {
        int ret = 1;
        //获取用户，手机端和pc端是不一样的
        IUser user = null;
        //source默认为PC
        if (source == null && !("PC".equals(source) || "MOBILE".equals(source))) {
            source = "PC";
        }
        //准备操作参数，用于日志记录
        SysOperateLog operateLog = new SysOperateLog();
        String param2 = "/startSubmitProcess";
        String params = "plan=" + ",workItemId=" + workItemID + ",outcome=" + outcome + ",message=" + message + ",nextUserName=" + nextUserName + ",location=" + location + ",copyLocation=" + null + ",copyMessage"
                + null + ",copyNextUserNames=" + null + ",notificationId=" + null + ",source=" + source + ",userCode=" + currentUserCode;
        operateLog.setInterfaceParam(params);
        try {
            //表单数据不为空
            if (slideShow != null) {
                //如果是归档，更新发布时间以及是否发布
                if (Constants.ACTIVITY_END.equals(outcome)) {
                    slideShow.setCreationTime(DateUtil.getCurrentTimestamp());
                    slideShow.setIsPublish(true);
                }
                String pmInsId = slideShow.getPmInsId();
                //废除归档环节
                if (Constants.ACTIVITY_REJECT_END.equals(outcome)) {
                    if (pmInsId != null) {
                        // ActBusinessStatus actBusinessStatus = statusService.getByProcessInst(processInstId);
                      /*  ActBusinessStatus actBusinessStatus = (ActBusinessStatus)iProcessTodoDataService.queryActBusinessStatusByPmInstId(pmInsId);
                        ret = this.terminateProcessInst(actBusinessStatus.getProcessInstId());*/
                        slideShow.setIsPublish(false);

                    } else {
                        operateLog.setErrorMsg("pmInstence为空！pmInsId = " + pmInsId);
                        ret = 0;
                    }

                }
                if (Constants.ACTIVITY_START.equals(location) && org.apache.commons.lang3.StringUtils.isNotEmpty(workItemID) || "hnjjwz.general_manager".equals(location)) {
                    slideShow.setInMobile(false);//退回修改不能修改
                } else {

                }
                PmInstence pmInstence = pmInstenceService.getByPmInsId(slideShow);
                if (pmInstence != null) {
                    //判断是否是从手机端还是PC端记录操作日志
                    JsonResponse returnObj = operateLogTool.operationSource(source, currentUserCode, param1, param2, operateLog);
                    if (returnObj != null) {
                        log.debug("判断失败！");
                        operateLog.setErrorMsg("判断失败！！pmInsId = " + pmInsId);
                        ret = 0;
                    }
                    //获取用户

                    user = judgeUsername(source, currentUserCode, operateLog);
                    log.debug("user的值为" + user);
                    if (user == null) {
                        ret = 0;
                    }
                    pmInstence.setPmInsCurrentActivity(location);
                    pmInstence.setPmInsTitle(slideShow.getSlideShowTitle());
                    pmInstence.setPmInsTitle(slideShow.getSlideShowTitle());
                    //更新US_PM_INSTENCE表
                    pmInstenceService.update(pmInstence);
                    //更新US_APPROVAL_FORM表
                    SlideShow resultApprovalForm = this.update(slideShow);
                    //如果是已发布需要向栏目表插入一条记录
                    if(resultApprovalForm.getIsPublish()){
                        newProgramaService.saveSlideShow(resultApprovalForm);
                    }
                    //更新ACT_BUSINESS_STATUS表的RECEIPT_TITLE字段，需要调用wf的接口
                    int isSuccess = processInstanceService.updateTitleByBusinessKey(pmInstence.getId(), slideShow.getSlideShowTitle());
                    if (isSuccess == 0) {
                        throw new Exception("更新ACT_BUSINESS_STATUS表的RECEIPT_TITLE字段失败！");
                    }

                    this.updatePmInsId(slideShow);//更新附件。
                    ret = processApproval(Long.parseLong(workItemID), user.getUsername(), user.getTruename(), nextUserName, outcome, message, pmInstence);

                } else {
                    operateLog.setErrorMsg("pmInstence为空！pmInsId = " + pmInsId);
                    ret = 0;
                }
            } else {
                operateLog.setErrorMsg("approvalForm为空！plan = " + slideShow.toString());
                ret = 0;
            }
        } catch (Exception e) {
            Exceptions.printException(e);
            operateLog.setErrorMsg(e.toString());
            ret = 0;
        } finally {
            operateLogService.saveLog(operateLog);
            log.debug("ret的值为：" + ret);
            return ret;
        }
    }

    /**
     * 流程审批
     *
     * @param workItemID      工作项id
     * @param currentUserCode 当前登录人code
     * @param currentUserName 当前登录人名称
     * @param nextUserName    审批人
     * @param outcome         连线规则
     * @param message         审批意见
     * @return
     */
    @Override
    public int processApproval(Long workItemID, String currentUserCode, String currentUserName, String nextUserName, String outcome, String message, PmInstence pmInstence) {
        int ret = 0;
        Map<String, Object> map = new HashMap<>();
        if (nextUserName != null) {
            map.put("inputUserId", nextUserName);//指定下一审批人
        }
        map.put("outcome", outcome);
        map.put("receiptId", String.valueOf(pmInstence.getId()));
        map.put("title", pmInstence.getPmInsTitle());
        map.put("code", pmInstence.getPmInsId());
        map.put("currentUserCode", currentUserCode);
        map.put("appCode", Constants.APP_CODE);
        try {
            //添加流程审批意见
            int status = workItemService.submitApprovalMsg(workItemID, message);
            if (status == 1) {
                //根据工作项ID完成工作项 如果第三个参数为true，则启用事务分割；如果第二个参数为false，则不启用事务分割
                long workItemIdNext = workItemService.finishWorkItemWithRelativeData(workItemID, map, false);
                if (workItemIdNext > 0) {
                    ret = 1;
                } else {
                    ret = 0;
                }
            } else {
                ret = 0;
            }
        } catch (Exception e) {
            e.printStackTrace();
            ret = 0;
        }
        return ret;
    }

    /**
     * 查询决策
     * @param processInstId
     * @param processDefName
     * @param location
     * @return
     *//*
    @Override
    public List<SimpleAppDecision> getDecisions(String processInstId, String processDefName, String location, String source, String currentUser) {
        if(source==null && !("PC".equals( source )||"MOBILE".equals( source ))){
            source = "PC";
        }
        *//**处理操作参数**//*
        SysOperateLog operateLog = new SysOperateLog();
        String param1 = "/action/networkMigrationForm";
        String param2 = "/getDecisions";
        String params = "processInstId="+processInstId+",processDefName="+processDefName+",location="+location+",source="+source+",userCode="+currentUser;
        operateLog.setInterfaceParam( params );
        operateLog.setBussinessKey( "processInstId="+processInstId );
        List<SimpleAppDecision> decisions = new ArrayList<>(  );
        try{
            *//**判断是否是从手机端还是PC端记录操作日志**//*
            JsonResponse returnObj = operateLogTool.operationSource( source, currentUser,param1, param2, operateLog);
            if ( returnObj != null ){
                log.debug( "判断失败！" );
                operateLog.setErrorMsg( "判断失败！");
                return null;
            }
            *//**选择流程处理**//*
            if ((processDefName == null || "".equals( processDefName )) &&   Constants.ACTIVITY_START.equals( location )){
                processDefName = this.getProcessName();
            }
            *//**当前环节下所有决策**//*
            Map<String,String> map =  new HashMap<>(  );
            map.put("appCode",Constants.APP_CODE  );
            map.put("processDefId",processDefName  );
            map.put("activityDefId",location  );
            //根据是pc端还是手机来判断调用主数据哪个接口
            decisions = uumsSysAppDecisionApi.findDecisions(Constants.APP_CODE,map );
        }catch (Exception e){
            operateLog.setErrorMsg( e.toString() );
            Exceptions.printException( e);
        }finally {
            operateLogService.saveLog( operateLog );
            return decisions;
        }
    }*/

    /**
     * 注销流程2
     *
     * @param pmInstId
     * @return
     */
    @Override
    public int terminateProcessInst(Long pmInstId) {
        return iProcessInstanceService.terminateProcessInst(pmInstId);
    }


    /**
     * 获取详情
     *
     * @param processInstId 流程实例id
     * @return
     */
    @Override
    public JsonResponse getApprovalFromDetail(Long processInstId, String source, String currentUserCode, String pmInstId, String location) {
        if (source == null && !("PC".equals(source) || "MOBILE".equals(source))) {
            source = "PC";
        }
        /**操作日志记录参数**/
        SysOperateLog operateLog = new SysOperateLog();
        String param2 = "/getApprovalFromDetail";
        String params = "source=" + source + ",currentUserCode" + currentUserCode;
        operateLog.setInterfaceParam(params);
        SlideShow slideShow = new SlideShow();
        try {
            /**判断是否是从手机端还是PC端记录操作日志**/
            JsonResponse returnObj = operateLogTool.operationSource(source, currentUserCode, param1, param2, operateLog);
            if (returnObj != null) {
                return returnObj;
            }
            if (processInstId != null) {
                ActBusinessStatus actBusinessStatus = statusService.getByProcessInst(processInstId);
                if (actBusinessStatus != null) {
                    String id = actBusinessStatus.getReceiptCode();
                    slideShow = slideShowRepository.getApprovalFromDetailFromInstId(id);
                 /*   String imgToBase64 = imgToBase64Tool.ImgToBase64(slideShow.getMainBody());
                    String compressBase =  GZIPUtils.compress(imgToBase64);
                    slideShow.setCompressBase(compressBase);*/
                    String compressBase = GZIPUtils.compress(slideShow.getMainBody());
                    slideShow.setCompressBase(compressBase);

                  /*  if (!"PC".equals(source)){
                        slideShow.setMainBody(null);
                    }*/
                }
            } else {
                slideShow = slideShowRepository.getApprovalFromDetailFromInstId(pmInstId);
            }
            if (slideShow.getPmInsId() != null) {
                //获取附件信息
                List<SysFile> imageFileList = fileExtendService.getPartFile(slideShow.getPmInsId(), Constants.IMAGE_FILE);
                putFile(imageFileList, slideShow, Constants.IMAGE_FILE);
            }
            if (Constants.MOBILE.equals(source) && slideShow != null) {
                if (Constants.ACTIVITY_START.equals(location)) {
                    slideShow.setInMobile(false);//退回修改不能修改
                } else {
                    slideShow.setInMobile(true);//
                }
            }

        } catch (Exception e) {
            operateLog.setErrorMsg(e.toString());
            Exceptions.printException(e);
        } finally {
            operateLogService.saveLog(operateLog);
            return JsonResponse.success(slideShow);
        }
    }

    @Override
    public JsonResponse findDataDetailList2(Map<String, Object> paramsMap, Pageable pageable) {
        String username = SecurityUtils.getCurrentUserName();
        IUser currentUser = SecurityUtils.getCurrentUser();
        Page<Map<String, Object>> slideShowDataFormPage;
        String title = (String) paramsMap.get("title");

        boolean Super_Control = false;
        //String programaCodes = (String)paramsMap.get( "programaCode" );

        Set<SimpleRole> authRoles = (Set<SimpleRole>) currentUser.getAuthRoles();

        for (SimpleRole simpleRole : authRoles) {//遍历所有的角色
            if (Constants.SUPER_CONTROL.equals(simpleRole.getRoleCode()) || Constants.ROLE_S_CJGLY_APPLY.equals(simpleRole.getRoleCode()) || Constants.ROLE_MANAGEMENT.equals(simpleRole.getRoleCode())) {
                Super_Control = true;// 如果所有角色中有纪检超级管理员就设置超级管理状态 进行管理查询
            }
        }

        if (Super_Control) {

            if (title == null) {
                slideShowDataFormPage = slideShowRepository.findDataDetailListNoCode(pageable);//超级查询
            } else {
                //List<String> programaCodeList = Arrays.asList( programaCodes.split( ApplicationConstants.COMMA ) );

                slideShowDataFormPage = slideShowRepository.findDataDetailList(title, pageable);
            }
        } else {//普通权限
            if (title == null) {
//                slideShowDataFormPage= slideShowRepository.findUserNameDetailList(username, pageable);
                Map<String, String> sql1 = new HashMap<>();
                sql1.put("firstParam", username);
                List<Map<String, Object>> query = this.uumsQuerySqlUtil.uumsSelectBySql(sql1, "hnjjwz_003");
                slideShowDataFormPage = new PageImpl(query, pageable, query.size());

            } else {
                //List<String> programaCodeList = Arrays.asList( programaCodes.split( ApplicationConstants.COMMA ) );

                slideShowDataFormPage = slideShowRepository.findDataDetailList(title, pageable);
            }
        }

        if (slideShowDataFormPage == null) {//如果都是空
            //如果当前登录人 没有起草过 就去查审批过的起草进行显示
//            slideShowDataFormPage= slideShowRepository.findUserNameApprovalDetailList(username, pageable);
            Map<String, String> sql1 = new HashMap<>();
            sql1.put("firstParam", username);
            List<Map<String, Object>> query = this.uumsQuerySqlUtil.uumsSelectBySql(sql1, "hnjjwz_004");
            slideShowDataFormPage = new PageImpl(query, pageable, query.size());
        }
        return JsonResponse.success(slideShowDataFormPage);
    }

    @Override
    public JsonResponse stick(String id) {
        SlideShow slideShow = slideShowRepository.findFlashback();


        int stickFlagBack = slideShow.getStickFlag() + 1;
        SlideShow relation = slideShowRepository.findSlideShowId(id);
        relation.setStickFlag(stickFlagBack);
        SlideShow update = this.update(relation);
        if (update != null) {
            return JsonResponse.success(1, "置顶成功");
        } else {
            return JsonResponse.fail(-1, "置顶失败");

        }

    }

    @Override
    public SlideShow getSlideShowPmInsId(String pmInsId) {
        return slideShowRepository.getApprovalFromDetailFromInstId(pmInsId);
    }


    /**
     * 向获得的工单信息中放入附件信息
     *
     * @param fileList
     * @param slideShow
     */
    private void putFile(List<SysFile> fileList, SlideShow slideShow, String fileType) {
        if (fileList != null && fileList.size() > 0) {
            if (Constants.IMAGE_FILE.equals(fileType)) {
                slideShow.setSlideShowFile(fileList);
            }
        }
    }

    /**
     * 起草阶段根据起草人所在公司起草相应的流程，针对pc端
     *
     * @return
     */
    private String getProcessName() {
        String processName = null;
        IUser iUser = SecurityUtils.getCurrentUser();
        /**根据起草人所在的公司起草相应的流程**/
        /**根据起草人所在的公司起草相应的流程**/
        switch (iUser.getBelongCompanyTypeDictValue()) {
            case Constants.PROVINCIAL_CODE:
                processName = Constants.FLOW_PROVINCIAL;
                break;
            case Constants.BRANCH_CODE_03:
                processName = Constants.FLOW_BRANCH;
                break;
            case Constants.BRANCH_CODE:
                processName = Constants.FLOW_BRANCH;
                break;
            default:
                processName = Constants.FLOW_BRANCH;
                break;
        }
        return processName;
    }

    /**
     * 起草阶段根据起草人所在公司起草相应的流程，针对手机端
     *
     * @param iUser 当前人
     * @return
     */
    private String getProcessNameMobile(IUser iUser) {
        String processName = null;
        /**根据起草人所在的公司起草相应的流程**/
        /**根据起草人所在的公司起草相应的流程**/
        switch (iUser.getBelongCompanyTypeDictValue()) {
            case Constants.PROVINCIAL_CODE:
                processName = Constants.FLOW_PROVINCIAL;
                break;
            case Constants.BRANCH_CODE_03:
                processName = Constants.FLOW_BRANCH;
                break;
            case Constants.BRANCH_CODE:
                processName = Constants.FLOW_BRANCH;
                break;
            default:
                processName = Constants.FLOW_BRANCH;
                break;
        }
        return processName;
    }

    /**
     * 起草阶段根据流程类型生成pmInstId，用于PC端
     *
     * @return
     */
    private String getPmInstId() {
        IUser iUser = SecurityUtils.getCurrentUser();
        return pmInstIdNormal(iUser);
    }

    /**
     * 起草阶段根据起草人所在公司生成相应的订单编号，用于手机端
     *
     * @param iUser
     * @return
     */
    private String getPmInstIdMobile(IUser iUser) {
        return pmInstIdNormal(iUser);
    }

    /**
     * 通用获取pmInstId
     *
     * @param iUser
     * @return
     */
    private String pmInstIdNormal(IUser iUser) {
        //获取会议类型
        String pmInstId = idGenerator.getDateId().toString();
        switch (iUser.getBelongCompanyTypeDictValue()) {
            case Constants.PROVINCIAL_CODE:
                pmInstId = Constants.PROCESS_E + pmInstId;
                break;
            case Constants.BRANCH_CODE:
                pmInstId = Constants.PROCESS_F + pmInstId;
                break;
            default:
                break;
        }
        return pmInstId;
    }

    /**
     * 获取到流转到下一步提示信息
     *
     * @param nextUserName 审批人
     * @return
     */
    private String getTemplate(String nextUserName) {
        Map<String, String> paramMap = Maps.newHashMap();
        String showMessage = "";
        try {
            if (!org.springframework.util.StringUtils.isEmpty(nextUserName)) {
                // 有代理时才放开
                // String agentUser = getAgentUser( nextUserName );
                IUser user = uumsSysUserinfoApi.findByKey(nextUserName, IAuthService.KeyType.username, Constants.APP_CODE); //审批人
                if (user != null) {
                    List<SimplePosition> simplePositionList = new ArrayList(user.getAuthPositions());
                    paramMap.put("companyName", user.getBelongCompanyName());
                    paramMap.put("departmentName", user.getBelongDepartmentName());
                    paramMap.put("trueName", user.getTruename());
                    paramMap.put("positionName", simplePositionList != null ? simplePositionList.get(0).getPositionName() : "");
                    //有代理时才放开
                    /* if(!StringUtils.isEmpty( agentUser )){
                        IUser userAgent = uumsSysUserinfoApi.findByKey(agentUser, IAuthService.KeyType.username, Constants.APP_CODE); //代理人
                        List<SimplePosition> simpleAgentPositionList = new ArrayList(userAgent.getAuthPositions());
                        paramMap.put("agentedCompanyName", userAgent.getBelongCompanyName());
                        paramMap.put("agentedDepartmentName", userAgent.getBelongDepartmentName());
                        paramMap.put("agentedTrueName", userAgent.getTruename());
                        paramMap.put("agentedPositionName", simpleAgentPositionList != null ? simpleAgentPositionList.get(0).getPositionName() : "");
                        showMessage = MessageEnum.MW000005.getMessage((Map) paramMap);
                    }else{*/
                    showMessage = MessageEnum.MW000001.getMessage((Map) paramMap);
                    //}
                }
            } else {
                showMessage = Constants.SHOWMESSAGESUCCESS;
            }
        } catch (Exception e) {
            log.debug(e.toString());
        }
        return showMessage;
    }

    /**
     * 更新附件sys_file的pm_ins_id字段
     *
     * @param slideShow
     */
    private void updatePmInsId(SlideShow slideShow) {
        List<SysFile> imageFileList = slideShow.getSlideShowFile();

        String pmInsId = slideShow.getPmInsId();
        try {
            if (imageFileList != null && imageFileList.size() > 0) {
                for (SysFile imageFile : imageFileList) {
                    fileExtendService.updatePmInsIdPart(pmInsId, imageFile.getId(), Constants.IMAGE_FILE);
                }
            }

        } catch (Exception e) {
            e.printStackTrace();
        }
    }

    /**
     * 获取用户
     *
     * @param source
     * @param currentUserCode
     * @param operateLog
     * @return
     */
    private IUser judgeUsername(String source, String currentUserCode, SysOperateLog operateLog) {
        IUser user = null;
        Map map = new LinkedHashMap();
        //获取用户
        user = SecurityUtils.getCurrentUser();
        if (user == null) {
            log.debug("用户为空！");
            operateLog.setErrorMsg("请联系管理员，用户信息为空！");
            return null;
        }
        return user;
    }


    /**
     * 保存草稿
     *
     * @return
     */
    @Override
    @Transactional
    public JsonResponse saveDraft(String source, String currentUserCode, SlideShow newTopicFrom) {
        /**准备操作参数**/
        SysOperateLog operateLog = new SysOperateLog();
        String param2 = "/saveDraft";
        String params = "slideShow=" + newTopicFrom.toString() + ",source=" + source + ",userCode=" + currentUserCode;
        operateLog.setInterfaceParam(params);
        try {
            /**判断是否是从手机端还是PC端记录操作日志**/
            JsonResponse returnObj = operateLogTool.operationSource(source, currentUserCode, param1, param2, operateLog);
            if (returnObj != null) {
                return returnObj;
            }

            // 保存草稿
            if (StrUtil.isEmpty(newTopicFrom.getId())) {
                newTopicFrom.setId(null);
                IUser iuser = SecurityUtils.getCurrentUser();
                String companyTypeDictDesc = iuser.getBelongCompanyTypeDictValue();
                /**校验表单和下一步审批人是否为空**/
                /**获取登录人所在公司应启动的流程**/
                String processUserType = "";
                if ("01".equals(companyTypeDictDesc)) {
                    processUserType = Constants.PROCESS_E;
                } else {
                    processUserType = Constants.PROCESS_F;
                }
                // 保存业务单据信息
                PmInstence usPmInstence = new PmInstence();
                usPmInstence.setPmInsType(processUserType);
                this.savePlanTask(newTopicFrom, usPmInstence);
            }
            // 更新草稿
            else {
                // 更新表单数据
                newTopicFrom.setModifiedTime(LocalDateTime.now());
                this.update(newTopicFrom);
                // 更新主单据
                PmInstence selectPm = pmInstenceService.findByPmInsId(newTopicFrom.getPmInsId());
                selectPm.setPmInsTitle(newTopicFrom.getSlideShowTitle());
                selectPm.setModifiedTime(LocalDateTime.now());
                PmInstence newSelectPm = pmInstenceService.update(selectPm);
                //  更新附件
                this.updateFileByPmInsId(newTopicFrom, newTopicFrom.getPmInsId(), newSelectPm.getPmInsType());
            }
        } catch (Exception e) {
            operateLog.setErrorMsg(e.toString());
            Exceptions.printException(e);
            return JsonResponse.fail(null, Constants.MESSAGE_FAIL);
        } finally {
            operateLogService.saveLog(operateLog);
        }
        return JsonResponse.success(newTopicFrom, Constants.MESSAGE_SUCCESS);
    }

    /**
     * 废除草稿
     *
     * @return
     */
    @Override
    @Transactional
    public JsonResponse deleteDraft(String source, String currentUserCode, String pmInsId, SlideShow innovationTopicForm) {
        /**准备操作参数**/
        SysOperateLog operateLog = new SysOperateLog();
        String param2 = "/deleteDraft";
        String params = "pmInsId=" + pmInsId + ",source=" + source + ",userCode=" + currentUserCode;
        operateLog.setInterfaceParam(params);
        try {
            /**判断是否是从手机端还是PC端记录操作日志**/
            JsonResponse returnObj = operateLogTool.operationSource(source, currentUserCode, param1, param2, operateLog);
            if (returnObj != null) {
                return returnObj;
            }
            // 删除主单据数据
            PmInstence pmInstence = pmInstenceService.findByPmInsId(pmInsId);
            pmInstenceService.delete(pmInstence);
            slideShowRepository.deleteByPmInsId(pmInsId);
            // 删除业务单据数据
//            this.delete(innovationTopicForm);
        } catch (Exception e) {
            operateLog.setErrorMsg(e.toString());
            Exceptions.printException(e);
            return JsonResponse.fail(null, Constants.MESSAGE_FAIL);
        } finally {
            operateLogService.saveLog(operateLog);
        }
        return JsonResponse.success(null, Constants.MESSAGE_SUCCESS);
    }

    @Override
    public List<SlideShow> getSlideShowArticleList() {
        List<SlideShow> slideShowArticleList = slideShowRepository.getSlideShowArticleList();
        for (SlideShow s : slideShowArticleList) {
            //获取附件信息
            List<SysFile> imageFileList = fileExtendService.getPartFile(s.getPmInsId(), Constants.IMAGE_FILE);
            if (CollectionUtil.isNotEmpty(imageFileList)){
                putFile(imageFileList, s, Constants.IMAGE_FILE);
            }

        }
        return slideShowArticleList;
    }

    @Override
    public SlideShow getDataArticleById(String articleId) {

        SlideShow byId = this.findById(articleId);
        if (ObjectUtil.isNotEmpty(byId)) {
            int viewsNumber = byId.getViewsNumber();
            byId.setViewsNumber(viewsNumber + 1);
            SlideShow update = this.update(byId);

            List<SysFile> imageFileList = fileExtendService.getPartFile(update.getPmInsId(), Constants.IMAGE_FILE);
            if (imageFileList.size() > 0) {
                putFile(imageFileList, update, Constants.IMAGE_FILE);
            }
            return update;
        }
        return byId;
    }

    /**
     * 根据登录人所在公司获取流程
     *
     * @return
     */
    private Map<String, String> getUserProcessMap(String processUserType) {
        Map<String, String> map = Maps.newHashMap();
        switch (processUserType) {
            case Constants.PROCESS_E:
                map.put("processName", Constants.FLOW_PROVINCIAL);
                map.put("processType", "E"); //省公司人员流程
                break;
            case Constants.PROCESS_F:
                map.put("processName", Constants.FLOW_BRANCH);
                map.put("processType", "F");//市公司人员流程
                break;
            default:
                throw new IllegalStateException("Unexpected value: " + processUserType);
        }
        return map;
    }

    /**
     * 更新附件
     *
     * @param slideShow 表单
     */
    private void updateFileByPmInsId(SlideShow slideShow, String pmInsId, String pmInsType) {
        List<SysFile> files = slideShow.getSlideShowFile();
        try {
            fileExtendService.deleteByPmInsId(pmInsId);
            if (files != null && !files.isEmpty()) {
                for (SysFile file : files) {
                    fileExtendService.updatePmInsId(pmInsId, pmInsType, file.getId(), Constants.IMAGE_FILE);
                }
            }

        } catch (Exception e) {
            e.printStackTrace();
        }
    }

    /**
     * 保存业务数据
     *
     * @return
     * @throws Exception
     */
    boolean savePlanTask(SlideShow slideShow, PmInstence pmInstence) {
        boolean flag = false;
        IUser iuser = SecurityUtils.getCurrentUser();
        /**保存申请表单任务**/
        try {
            /**保存主单据**/
            if (StrUtil.isEmpty(slideShow.getId())) {
                String pmInsId = pmInstence.getPmInsType() + String.valueOf(IdGenerator.idWorker.nextId());//获取到pmInsId
                pmInstence.setPmInsId(pmInsId);
                pmInstence.setPmInsTitle(slideShow.getSlideShowTitle());
                pmInstence.setPmInsCurrentActivity(Constants.ACTIVITY_START);
                pmInstence.setPmInsCreatorCode(iuser.getUsername());
                pmInstence.setPmInsCreatorName(iuser.getTruename());
                pmInstence.setBelongCompanyCode(iuser.getBelongCompanyCode());
                pmInstence.setBelongCompanyTypeDictValue(iuser.getBelongCompanyTypeDictValue());
                pmInstence.setBelongDepartmentCode(iuser.getBelongDepartmentCode());
                pmInstence.setBelongOrgCode(iuser.getBelongOrgCode());
                pmInstenceService.insert(pmInstence);
            }
            /**保存表单数据**/
            if (pmInstence.getId() != null) {
//                Map<String, Object> viewUserOrg = iProgramaDataFormService.findViewUserOrg(iuser.getUsername());
                IUser user = uumsSysUserinfoApi.findByKey(iuser.getUsername(), IAuthService.KeyType.username, Constants.APP_CODE); //审批人
                slideShow.setUsername(iuser.getUsername());
                slideShow.setTruename(iuser.getTruename());
                slideShow.setBelongCompanyCode(iuser.getBelongCompanyCode());
                slideShow.setBelongCompanyTypeDictValue(iuser.getBelongCompanyTypeDictValue());
                slideShow.setBelongDepartmentCode(iuser.getBelongDepartmentCode());
                slideShow.setBelongOrgCode(iuser.getBelongOrgCode());
                slideShow.setBelongCompanyName(iuser.getBelongCompanyName());
                slideShow.setPmInsId(pmInstence.getPmInsId());

                slideShow.setCompany(iuser.getBelongCompanyName());//起草人公司
                slideShow.setDepartmentName(iuser.getBelongDepartmentName());//起草人部门
                slideShow.setDisplayName(iuser.getBelongCompanyName() + "\\" + iuser.getBelongDepartmentName());//起草人组织路径
               /* slideShow.setCompany(String.valueOf(viewUserOrg.get("COMPANYNAME")));//起草人公司
                slideShow.setDepartmentName(String.valueOf(viewUserOrg.get("DEPARTMENTNAME")));//起草人部门
                slideShow.setDisplayName(String.valueOf(viewUserOrg.get("DISPLAYNAME")));//起草人组织路径*/
                slideShow.setIsPublish(false);

                List<SysFile> imageFileList = slideShow.getSlideShowFile();
                for (SysFile sysFile : imageFileList) {
                    slideShow.setSlideShowPhotoId(sysFile.getId());
                }
                SlideShow slideShow1 = this.insert(slideShow);
                this.updatePmInsId(slideShow);//更新附件
                if (slideShow1 != null) {
                    flag = true;
                }
            }
        } catch (Exception e) {
            flag = false;
            TransactionAspectSupport.currentTransactionStatus().setRollbackOnly();
            log.debug(e.getMessage());
            return flag;
        }
        return flag;
    }
}
