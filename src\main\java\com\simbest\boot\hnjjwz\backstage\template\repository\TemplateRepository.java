package com.simbest.boot.hnjjwz.backstage.template.repository;

import com.simbest.boot.base.repository.LogicRepository;
import com.simbest.boot.hnjjwz.backstage.template.model.Template;
import org.springframework.data.domain.Page;
import org.springframework.data.domain.Pageable;
import org.springframework.data.jpa.repository.Query;
import org.springframework.data.repository.query.Param;

import java.util.List;

/**
 * <AUTHOR>
 * @Data 201/04/02
 * @Description 模板
 */
public interface TemplateRepository extends LogicRepository<Template,String> {

    List<Template> findByTemplateUse( Boolean templateUse);

    /**
     * 根据模板名称(模糊)、是否使用此模板(精确)
     * @return
     */
    @Query (value =  "select ut.* " +
            " FROM US_TEMPLATE ut " +
            " WHERE ut.template_name like concat( concat('%',:templateName),'%') " +
            " AND ut.template_use IN (:templateUse) " +
            " AND ut.enabled = 1 AND ut.removed_time IS NULL",
            countQuery = "SELECT COUNT(*)" +
                    " FROM US_TEMPLATE ut " +
                    " WHERE ut.template_name like concat( concat('%',:templateName),'%') " +
                    " AND ut.template_use IN (:templateUse) " +
                    " AND ut.enabled = 1 AND ut.removed_time IS NULL",
            nativeQuery = true)
    Page<Template> findAllDim( @Param ( "templateName" ) String templateName, @Param ( "templateUse" ) List<Boolean> templateUse, Pageable pageable );
}
