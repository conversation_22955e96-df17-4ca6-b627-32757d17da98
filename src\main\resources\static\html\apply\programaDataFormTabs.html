<!DOCTYPE html>
<html xmlns="http://www.w3.org/1999/xhtml"
      xmlns:th="http://www.thymeleaf.org">
<head>
    <title>栏目内容选项卡</title>
    <meta http-equiv="Content-Type" content="text/html; charset=utf-8"/>
    <meta http-equiv="X-UA-Compatible" content="IE=edge" />
    <meta name="viewport" content="width=device-width, initial-scale=1, shrink-to-fit=no"/>
    <!--
    <link href="http://************:8088/simbestui/fonts/iconfont/iconfont.css?v=svn.revision" th:href="@{http://************:8088/simbestui/fonts/iconfont/iconfont.css?v=svn.revision}" rel="stylesheet"/>
    -->
    <link href="../../fonts/iconfont/iconfont.css?v=svn.revision" th:href="@{/fonts/iconfont/iconfont.css?v=svn.revision}" rel="stylesheet"/>
    <link href="http://************:8088/simbestui/js/themes/default/easyui.css?v=svn.revision" th:href="@{http://************:8088/simbestui/js/themes/default/easyui.css?v=svn.revision}" rel="stylesheet"/>
    <link href="http://************:8088/simbestui/js/themes/icon.css?v=svn.revision" th:href="@{http://************:8088/simbestui/js/themes/icon.css?v=svn.revision}" rel="stylesheet"/>
    <link href="http://************:8088/simbestui/css/public.css?v=svn.revision" th:href="@{http://************:8088/simbestui/css/public.css?v=svn.revision}" rel="stylesheet"/>
    <link href="http://************:8088/simbestui/css/pages.css?v=svn.revision" th:href="@{http://************:8088/simbestui/css/pages.css?v=svn.revision}" rel="stylesheet"/>
    <script src="http://************:8088/simbestui/js/jquery.min.js?v=svn.revision" th:src="@{http://************:8088/simbestui/js/jquery.min.js?v=svn.revision}" type="text/javascript"></script>
    <script src="http://************:8088/simbestui/js/jquery.easyui.min.js?v=svn.revision" th:src="@{http://************:8088/simbestui/js/jquery.easyui.min.js?v=svn.revision}" type="text/javascript"></script>
    <script src="http://************:8088/simbestui/js/easyui-lang-zh_CN.js?v=svn.revision" th:src="@{http://************:8088/simbestui/js/easyui-lang-zh_CN.js?v=svn.revision}" type="text/javascript"></script>
    <!-- KindEditor 已移除，使用 TinyMCE 替代 -->
    <script src="../../js/tinymce/richText.js"></script>
    <link href="../../js/tinymce/richText.css" th:href="@{../../js/tinymce/richText.css}" rel="stylesheet"/>
    <script src="../../js/tinymce/tinymce.js"></script>
    <script src="../../js/jquery.config.js?v=svn.revision" th:src="@{/js/jquery.config.js?v=svn.revision}" type="text/javascript"></script>
    <script src="http://************:8088/simbestui/js/jquery.zjsfile.js?v=svn.revision" th:src="@{http://************:8088/simbestui/js/jquery.zjsfile.js?v=svn.revision}" type="text/javascript"></script>
    <script src="http://************:8088/simbestui/js/jquery.aduq.js?v=svn.revision" th:src="@{http://************:8088/simbestui/js/jquery.aduq.js?v=svn.revision}" type="text/javascript"></script>
    <script src="http://************:8088/simbestui/js/jquery.process.js?v=svn.revision" th:src="@{http://************:8088/simbestui/js/jquery.process.js?v=svn.revision}" type="text/javascript"></script>
    <script type="text/javascript">
        var aF;
        var columnA;

        //获取当前登录人
        getCurrent();
        var tinyID = 'artHtmlContentCopy';

        //查看当前登录人是属于分公司还是省公司
        var CompanyName=web.currentUser.belongCompanyName;

        //提交之前校验
        function beforesubmit(data){
            // var pcolumn=$("input[name='pcolumn']:checked").val();
            // var pcolumnId=$("input[name='pcolumn']:checked").attr('id');
            // var zcolumn=$("input[name='zcolumn']:checked").val();
            // var zcolumnId=$("input[name='zcolumn']:checked").attr('id');
            // var ncolumn=$("input[name='ncolumn']:checked").val();
            // var ncolumnId=$("input[name='ncolumn']:checked").attr('id');
            // //console.log(zcolumn ==undefined );//js判空

            // if(ncolumn!=undefined){
            //     data.programaDisplayName=ncolumn;
            //     data.programaCode=ncolumnId;
            // }else {
            //     if (zcolumn ==undefined) {
            //         data.programaDisplayName=pcolumn;
            //         data.programaCode=pcolumnId;
            //     }else {
            //         data.programaDisplayName=zcolumn;
            //         data.programaCode=zcolumnId;
            //     }
            // }
            //console.log("父菜单id："+pcolumnId);
            //console.log("子菜单id："+zcolumnId);
            //console.log("父菜单："+pcolumn);
            //console.log("子菜单："+zcolumn);
            //console.log("最后值："+data.programaDisplayName);
             if(!data.programaDisplayName){
                top.mesAlert("提示信息", "请选择所属栏目!", 'info');
                return false;


            }
            return true;

        };
        function loadF(){
            $("#belongCompanyTypeDictValue").val(web.currentUser.belongCompanyTypeDictValue);
            $("#belongDepartmentCode").val(web.currentUser.belongDepartmentCode);
            $("#belongDepartmentName").val(web.currentUser.belongCompanyTypeDictValue=="03"?web.currentUser.belongCompanyName:web.currentUser.belongDepartmentName);
            $("#belongCompanyCode").val(web.currentUser.belongCompanyCode);
        };
        //校验
        function activeMoney(){
            if(aF){
                clearTimeout(aF);
            }
            aF=setTimeout(function(){
                activeD();
            },1500);
        };
        //保存草稿关闭页面
        function approvalS(data){
            top.tabClick("processTask");
            top.tabClose("li_meetingForm");
        };
         // 同步 TinyMCE 内容到隐藏的 mainBody 字段
        function syncTinyMCEContent() {
            try {
                if (typeof tinymce !== 'undefined' && tinymce.get(tinyID)) {
                    var content = tinymce.get(tinyID).getContent();
                    $("#mainBody").val(content);
                    console.log('TinyMCE 内容已同步到 mainBody:', content);
                    return content;
                } else {
                    console.log('TinyMCE 编辑器未找到或未初始化');
                    return '';
                }
            } catch (error) {
                console.error('同步 TinyMCE 内容时出错:', error);
                return '';
            }
        }

        // 设置 TinyMCE 内容同步机制
        function setupTinyMCESync() {
            try {
                if (typeof tinymce !== 'undefined' && tinymce.get(tinyID)) {
                    var editor = tinymce.get(tinyID);

                    // 监听内容变化事件
                    editor.on('change keyup', function() {
                        syncTinyMCEContent();
                    });

                    // 监听失去焦点事件
                    editor.on('blur', function() {
                        syncTinyMCEContent();
                    });

                    console.log('TinyMCE 内容同步机制已设置');
                } else {
                    console.log('TinyMCE 编辑器未找到，无法设置同步机制');
                }
            } catch (error) {
                console.error('设置 TinyMCE 同步机制时出错:', error);
            }
        }

        // HTML解码函数
        function decodeHtmlEntities(str) {
            if (!str) return '';

            var textarea = document.createElement('textarea');
            textarea.innerHTML = str;
            return textarea.value;
        }

        // 设置 TinyMCE 内容（用于回显）
        function setTinyMCEContent(content) {
            try {
                if (typeof tinymce !== 'undefined' && tinymce.get(tinyID)) {
                    // 处理HTML内容，确保正确显示
                    var processedContent = content || '';

                    // 如果内容被HTML编码了，先解码
                    if (processedContent.includes('&lt;') || processedContent.includes('&gt;') || processedContent.includes('&amp;')) {
                        processedContent = decodeHtmlEntities(processedContent);
                        console.log('检测到HTML编码，已解码:', processedContent);
                    }

                    // TinyMCE会自动解析HTML标签并正确显示
                    tinymce.get(tinyID).setContent(processedContent);

                    // 同时更新隐藏字段
                    $("#mainBody").val(processedContent);

                    console.log('TinyMCE 内容已设置，原始内容:', content);
                    console.log('TinyMCE 处理后内容:', processedContent);
                } else {
                    console.log('TinyMCE 编辑器未找到，无法设置内容');
                    // 如果TinyMCE未初始化，先设置到隐藏字段
                    $("#mainBody").val(content || '');
                }
            } catch (error) {
                console.error('设置 TinyMCE 内容时出错:', error);
                // 出错时也要设置隐藏字段
                $("#mainBody").val(content || '');
            }
        }
        $(function(){
            findAllByUser()

            // 初始化 TinyMCE 富文本编辑器
            initTiny(function() {
                console.log('TinyMCE 初始化完成');
                // 在编辑器初始化完成后，设置内容同步
                setupTinyMCESync();

                // 添加调试信息
                var editor = tinymce.get(tinyID);
                if (editor) {
                    console.log('TinyMCE 编辑器实例:', editor);
                    console.log('TinyMCE 当前内容:', editor.getContent());
                }
            });

            //获取全部栏目
            // ajaxgeneral({
            //     url:"action/programaInfo/findUserNameRootAndNext",
            //     data:{"CompanyName":CompanyName},
            //     success:function(data){
            //         columnA=data.data;

            //         document.getElementById("twoStair").style.display = "none";
            //         var pcolH=[];
            //         for(var i in columnA){
            //             /*if(columnA[i].programaDisplayName.indexOf("/")==-1){
            //                 pcolH.push("<div class='w2 fl'><input class='wauto' name='pcolumn' type='radio' id='"+columnA[i].programaCode+"' value='"+columnA[i].programaDisplayName+"'/><label for='"+columnA[i].id+"'>"+columnA[i].programaDisplayName+"</label></div>");
            //             }*/
            //             var oneStair=columnA[i].oneStair;
            //             pcolH.push("<div class='w2 fl'><input class='wauto' name='pcolumn' type='radio' id='"+oneStair.programaCode+"' value='"+oneStair.programaDisplayName+"'/><label for='"+oneStair.programaCode+"'>"+oneStair.programaDisplayName+"</label></div>");

            //         }
            //         $(".pcol").html(pcolH.join(""));
            //     }
            // });
            $(document).on("click",".pcol input,.pcol label",function(){

                document.getElementById("threeStair").style.display = "none";
                $(".tcol").html("");
                var name=$(this).parent().find("label").html();
                var zcolH=[];
                for(var i in columnA){
                    /*if(columnA[i].programaDisplayName.indexOf(name+"/")>-1){
                        zcolH.push("<div class='w2 fl'><input class='wauto' name='zcolumn' type='radio' id='"+columnA[i].programaCode+"' value='"+columnA[i].programaDisplayName+"'/><label for='"+columnA[i].id+"'>"+columnA[i].programaDisplayName+"</label></div>");
                    }*/
                    var twoStair=columnA[i].twoStair;
                    for(var j in twoStair){
                        if(twoStair[j].programaDisplayName.indexOf(name+"/")>-1) {
                            document.getElementById("twoStair").style.display = "inline";
                            zcolH.push("<div class='w2 fl'><input class='wauto' name='zcolumn' type='radio' id='" + twoStair[j].programaCode + "' value='" + twoStair[j].programaDisplayName + "'/><label for='" + twoStair[j].programaCode + "'>" + twoStair[j].programaDisplayName + "</label></div>");
                        }
                    }
                }
                $(".zcol").html(zcolH.join(""));
            });

            $(document).on("click",".zcol input,.zcol label",function(){

                var pcolumn2=$("input[name='pcolumn']:checked").val();

                var name=$(this).parent().find("label").html();
                var tcolH=[];
                for(var i in columnA){
                    if(columnA[i].oneStair.programaDisplayName==pcolumn2){
                        if(columnA[i].threeStair.length>0){
                            document.getElementById("threeStair").style.display = "inline";
                            var threeStair=columnA[i].threeStair;
                            for(var j in threeStair){
                                //console.log(threeStair[j]);
                                for(var k in threeStair[j]){
                                    if(threeStair[j][k].programaDisplayName.indexOf(name+"/")>-1) {
                                        tcolH.push("<div class='w2 fl'><input class='wauto' name='ncolumn' type='radio' id='" + threeStair[j][k].programaCode + "' value='" + threeStair[j][k].programaDisplayName + "'/><label for='" + threeStair[j][k].programaCode + "'>" + threeStair[j][k].programaDisplayName + "</label></div>");
                                    }
                                }

                            }

                        }
                    }

                }
                $(".tcol").html(tcolH.join(""));
            });

            // 加载表单
            //loadForm("programaDataForm",{"pmInstId":gps.pmInstId});
            var param={
                "htmlName":"programaDataFormTabs",
                "formId":"programaDataForm",
                "processNextCmd":"action/approvalForm/startSubmitProcess",
                "processDeleteCmd":"action/approvalForm/terminateProcessInst",
                "processDraftDeleteCmd": "action/approvalForm/deleteDraft",
                "processNextBeforeSubmit":function(data){
                    // 同步 TinyMCE 内容到隐藏字段
                    syncTinyMCEContent();

                    // radio没有选中时，设置值为空
                    if(!$('input[name="stickFlag"]').is(':checked')){
                        data.formData.stickFlag="";
                    }else{
                        data.formData.stickFlag=$('input[name="stickFlag"]:checked').val();
                    }
                     if(!$('input[name="importantFlag"]').is(':checked')){
                        data.formData.importantFlag="";
                    }else{
                        data.formData.importantFlag=$('input[name="importantFlag"]:checked').val();
                    }

                    return true;
                }
            };
            loadProcess(param);


            //tab选项卡
            $(".tab li").click(function(){
                var pcolumn=$("input[name='pcolumn']:checked").val();
                var zcolumn=$("input[name='zcolumn']:checked").val();
                // console.log(pcolumn+"------"+zcolumn);
                var zcol=$(".zcol").html();


                if($("input[name='pcolumn']")&&pcolumn!=undefined){
                    if(zcol!=""){
                        if($("input[name='zcolumn']")&&zcolumn!=undefined){
                            $(this).addClass("tab_click").siblings().removeClass("tab_click");
                            $(".tabD>div:eq("+$(this).index()+")").show().siblings().hide();
                        }else{
                            alert("请选择子板块");
                            return false;
                            }
                    }else {
                        $(this).addClass("tab_click").siblings().removeClass("tab_click");
                        $(".tabD>div:eq("+$(this).index()+")").show().siblings().hide();
                        }
                }else {
                    alert("请选择板块");
                    return false;
                    }

                if(zcolumn=="廉洁文化/视频"){//显示必填或不必填
                    // document.getElementById("authBookFile").style.display="inline";
                    // document.getElementById("coverImageFile").style.display="inline";
                    // document.getElementById("videoFile").style.display="inline";
                    $("#authBookFile").show();
                    $("#coverImageFile").show();
                    $("#videoFile").show();


                    $('#mainBody').attr("required", true);
                    $('#coverImageFileList').attr("required", true);
                    $('#authBookFileList').attr("required", true);
                    $('#videoFileList').attr("required", true);
                }else {
                    // document.getElementById("authBookFile").style.display="inline";
                    // document.getElementById("coverImageFile").style.display="inline";
                    // document.getElementById("videoFile").style.display="inline";
                    $("#authBookFile").show();
                    $("#coverImageFile").show();
                    $("#videoFile").hide();


                    $('#mainBody').attr("required", true);
                    //$('#coverImageFileList').prop("required", true);
                    $('#authBookFileList').attr("required", true);
                    // $('#videoFileList').attr("required", false);
                }

                if(pcolumn=="信息公开"||pcolumn=="规章制度"||pcolumn=="巡察工作"||pcolumn=="嵌入式防控监督"||pcolumn=="信访举报"){

                    // document.getElementById("authBookFile").style.display="none";
                    // document.getElementById("coverImageFile").style.display="none";
                    // document.getElementById("videoFile").style.display="none";
                    $("#authBookFile").hide();
                    $("#coverImageFile").hide();
                    $("#videoFile").hide();


                    $('#mainBody').attr("required", true);
                    $('#accessoryFileList').attr("required", false);
                    $('#authBookFileList').attr("required", false);

                    // $('#videoFileList').attr("required",false);

                }else if(pcolumn!="廉洁文化"){
                    // document.getElementById("authBookFile").style.display="none";
                    // document.getElementById("coverImageFile").style.display="none";
                    // document.getElementById("videoFile").style.display="none";
                    // document.getElementById("accessoryFile").style.display="inline";
                    $("#authBookFile").hide();
                    $("#coverImageFile").hide();
                    $("#videoFile").hide();
                    $("#accessoryFile").show();


                    $('#mainBody').attr("required", true);
                    $('#authBookFileList').attr("required", false);
                }
                if(pcolumn=="公司工作动态"||pcolumn=="廉闻要论"||pcolumn=="以案示警"||pcolumn=="纪律审查与监督"||pcolumn=="分公司动态"||pcolumn=="廉洁教育"){
                    // document.getElementById("authBookFile").style.display="none";
                    // document.getElementById("coverImageFile").style.display="none";
                    // document.getElementById("videoFile").style.display="none";
                    $("#authBookFile").hide();
                    $("#coverImageFile").hide();
                    $("#videoFile").hide();
                    $("#accessoryFile").show();


                    $('#mainBody').attr("required", true);
                    $('#accessoryFileList').attr("required", false);
                    $('#authBookFileList').attr("required", false);
                }else if(pcolumn!="廉洁文化"){
                    // document.getElementById("authBookFile").style.display="none";
                    // document.getElementById("coverImageFile").style.display="none";
                    // document.getElementById("videoFile").style.display="none";
                    // document.getElementById("accessoryFile").style.display="inline";
                    $("#authBookFile").hide();
                    $("#coverImageFile").hide();
                    $("#videoFile").hide();
                    $("#accessoryFile").show();


                    $('#mainBody').attr("required", true);
                    $('#authBookFileList').attr("required", false);
                }


            });

            // 详情预览
            $(".preview").on("click", function () {
                // 同步 TinyMCE 内容到隐藏字段
                syncTinyMCEContent();

                // 获取表单数据
                if(formValidate("programaDataForm")){
                    var formData = getFormValue("programaDataForm");

                    // 验证必填项
                    if(!formData.title || formData.title.trim() === ''){
                        top.mesShow("温馨提示", "请填写标题", 2000, "red");
                        return;
                    }
                    if(!formData.programaDisplayName){
                        top.mesShow("温馨提示", "请选择所属栏目", 2000, "red");
                        return;
                    }
                    if(!formData.mainBody || formData.mainBody.trim() === ''){
                        top.mesShow("温馨提示", "请填写正文内容", 2000, "red");
                        return;
                    }
                    if (!$('input[name="importantFlag"]').is(':checked')) {
                       formData.importantFlag = "";
                    } else {
                        formData.importantFlag = $('input[name="importantFlag"]:checked').val();
                    }

                    console.log(formData,'formData')
                    console.log('正文内容:', formData.mainBody);

                    // 实现预览功能
                    formData.type='programaData';
                    sessionStorage.setItem('formData',JSON.stringify(formData))
                    var url = window.location.origin+'/hnjjwz/html/apply/previewNew.html'
                    window.open(url,'_blank');
                }
            });

         /*   //保存草稿
            $(document).on("click", ".saveDraft", function () {
                if(formValidate("programaDataForm")){
                    formsubmit('programaDataForm','action/approvalForm/saveDraft?source=PC');
                }
            });*/
        });

        function getcallback(data){
            //处理渲染数据之后的操作代码,data为接口返回数据。一般用于对html标签的处理。
            if (!gps.location || "hnjjwz.start" == gps.location || undefined == gps.location) {
                $('#selectPrograma').show();
                $("input[name='stickFlag']").prop("disabled", false);
                $("input[name='importantFlag']").prop("disabled", false);
                 // 根据值选择正确的radio按钮
                if (data.stickFlag === 0) {

                    $("#stickFlagNo").prop("disabled", false).prop("checked", true);
                } else if (data.stickFlag === 1) {
                    $("#stickFlagYes").prop("disabled", false).prop("checked", true);
                }

                // 根据值选择正确的radio按钮
                if (data.importantFlag === 0) {
                    $("#importantFlagNo").prop("disabled", false).prop("checked", true);
                } else if (data.importantFlag === 1) {

                    $("#importantFlagYes").prop("disabled", false).prop("checked", true);
                }

            } else {
                $('#selectPrograma').hide();
                $('#slideShowTitleTr').show();
                    // 根据值选择正确的radio按钮
                if (data.stickFlag === 0) {

                    $("#stickFlagNo").prop("disabled", true).prop("checked", true);
                } else if (data.stickFlag === 1) {
                    $("#stickFlagYes").prop("disabled", true).prop("checked", true);
                }

                // 根据值选择正确的radio按钮
                if (data.importantFlag === 0) {
                    $("#importantFlagNo").prop("disabled", true).prop("checked", true);
                } else if (data.importantFlag === 1) {

                    $("#importantFlagYes").prop("disabled", true).prop("checked", true);
                }

            }
            if( ("hnjjwz.start"==gps.location || undefined == gps.location ) && "examine"!=gps.type ){
            }else if("hnjjwz.general_manager"==gps.location&&"join"==gps.type){
                //formReadonlyNo("programaDataForm");
                $(".nextBtn").hide();
                //$(".wfmgModifyBtn").hide();
                $(".cancel").hide();
                $(".formReset").hide();
                //$(".printOut").hide();
                idReadonly("belongDepartmentName");
            }else if("hnjjwz.general_manager"==gps.location&&"task"==gps.type){
                formReadonlyNo("programaDataForm");
                $(".cancel").hide();
                $(".formReset").hide();
                //$("#td3").hide();
            }
            if(true){
                $("#videoFile").css('display','table-row');
            }
            if("examine"==gps.type){
                formReadonly("programaDataForm");
            }
             // 根据 programaCode 回显联动信息
            if (data && data.programaCode) {
                console.log("获取到 programaCode:", data.programaCode);
                setColumnSelectsByProgramaCode(data.programaCode);
            }
            // 回显 TinyMCE 内容
            if (data && data.mainBody) {
                console.log("回显正文内容:", data.mainBody);
                console.log("内容类型:", typeof data.mainBody);

                // 多次尝试设置内容，确保 TinyMCE 已经完全初始化
                var attempts = 0;
                var maxAttempts = 10;

                function trySetContent() {
                    attempts++;
                    if (typeof tinymce !== 'undefined' && tinymce.get(tinyID) && tinymce.get(tinyID).initialized) {
                        setTinyMCEContent(data.mainBody);
                        console.log("TinyMCE 内容设置成功，尝试次数:", attempts);
                    } else if (attempts < maxAttempts) {
                        setTimeout(trySetContent, 200);
                    } else {
                        console.log("TinyMCE 初始化超时，直接设置到隐藏字段");
                        $("#mainBody").val(data.mainBody);
                    }
                }

                trySetContent();
            }

            // TinyMCE 只读模式处理
            if($("#mainBody").attr("readonly")){
                setTimeout(function() {
                    if (typeof tinymce !== 'undefined' && tinymce.get(tinyID)) {
                        setRichTextReadonly();
                        console.log('TinyMCE 已设置为只读模式');
                    }
                }, 500);
            }

            $('#programaDataForm').resize()
        };

        function nextBtnOther() {
            if(CompanyName=="省公司"){
                return {"processType": "A"};
            }else {
                return {"processType": "B"};
            }

        };
         // 获取三级联动
        function findAllByUser() {
            var ajaxopts = {
                url: "action/newcolumn/findAllByUser",
                contentType: "application/json; charset=utf-8",
                success: function (res) {
                    // 显示保存成功提示
                    console.log(res)
                    // 处理数据并初始化下拉框
                    if (res && res.data) {
                        columnData = res.data;
                    }
                    initColumnSelects();
                },
                error: function (xhr, status, error) {
                }
            };
            ajaxgeneral(ajaxopts);

        }

        // 全局变量存储栏目数据
        var columnData = [];

        // 初始化栏目选择器
        function initColumnSelects() {
            console.log("初始化栏目选择器，数据:", columnData);

            // 清空所有下拉框
            $("#firstLevelColumn").empty().append('<option value="">--请选择一级栏目--</option>');
            $("#secondLevelColumn").empty().append('<option value="">--请选择二级栏目--</option>').prop('disabled', true);
            $("#thirdLevelColumn").empty().append('<option value="">--请选择三级栏目--</option>').prop('disabled', true);

            // 填充一级栏目
            var firstLevelColumns = columnData.filter(function (item) {
                return item.columnLevel === 1 && item.isDisplay;
            });

            firstLevelColumns.forEach(function (column) {
                $("#firstLevelColumn").append('<option value="' + column.columnId + '">' + column.columnName + '</option>');
            });

            // 绑定一级栏目变化事件
            $("#firstLevelColumn").off('change').on('change', function () {
                var selectedValue = $(this).val();
                loadSecondLevelColumns(selectedValue);
                updateSelectedColumnInfo();
            });

            // 绑定二级栏目变化事件
            $("#secondLevelColumn").off('change').on('change', function () {
                var selectedValue = $(this).val();
                loadThirdLevelColumns(selectedValue);
                updateSelectedColumnInfo();
            });

            // 绑定三级栏目变化事件
            $("#thirdLevelColumn").off('change').on('change', function () {
                updateSelectedColumnInfo();
            });
        }

        // 加载二级栏目
        function loadSecondLevelColumns(parentColumnId) {
            $("#secondLevelColumn").empty().append('<option value="">--请选择二级栏目--</option>');
            $("#thirdLevelColumn").empty().append('<option value="">--请选择三级栏目--</option>').prop('disabled', true);

            if (!parentColumnId) {
                $("#secondLevelColumn").prop('disabled', true);
                return;
            }

            var secondLevelColumns = columnData.filter(function (item) {
                return item.columnLevel === 2 && item.parentColumnId === parentColumnId && item.isDisplay;
            });

            if (secondLevelColumns.length > 0) {
                $("#secondLevelColumn").prop('disabled', false);
                secondLevelColumns.forEach(function (column) {
                    $("#secondLevelColumn").append('<option value="' + column.columnId + '">' + column.columnName + '</option>');
                });
            } else {
                $("#secondLevelColumn").prop('disabled', true);
            }
        }

        // 加载三级栏目
        function loadThirdLevelColumns(parentColumnId) {
            $("#thirdLevelColumn").empty().append('<option value="">--请选择三级栏目--</option>');

            if (!parentColumnId) {
                $("#thirdLevelColumn").prop('disabled', true);
                return;
            }

            var thirdLevelColumns = columnData.filter(function (item) {
                return item.columnLevel === 3 && item.parentColumnId === parentColumnId && item.isDisplay;
            });

            if (thirdLevelColumns.length > 0) {
                $("#thirdLevelColumn").prop('disabled', false);
                thirdLevelColumns.forEach(function (column) {
                    $("#thirdLevelColumn").append('<option value="' + column.columnId + '">' + column.columnName + '</option>');
                });
            } else {
                $("#thirdLevelColumn").prop('disabled', true);
            }
        }

        // 获取选中的栏目信息
        function getSelectedColumnInfo() {
            var firstLevel = $("#firstLevelColumn").val();
            var secondLevel = $("#secondLevelColumn").val();
            var thirdLevel = $("#thirdLevelColumn").val();

            var selectedColumn = null;
            if (thirdLevel) {
                selectedColumn = columnData.find(function (item) {
                    return item.columnId === thirdLevel;
                });
            } else if (secondLevel) {
                selectedColumn = columnData.find(function (item) {
                    return item.columnId === secondLevel;
                });
            } else if (firstLevel) {
                selectedColumn = columnData.find(function (item) {
                    return item.columnId === firstLevel;
                });
            }

            return selectedColumn;
        }

        // 更新选中的栏目信息到隐藏字段
        function updateSelectedColumnInfo() {
            var selectedColumn = getSelectedColumnInfo();
            if (selectedColumn) {
                $("#programaCode").val(selectedColumn.columnId);
                $("#programaDisplayName").val(selectedColumn.columnAllName);
            } else {
                $("#programaCode").val("");
                $("#programaDisplayName").val("");
            }
        }
         // 根据 programaCode 设置联动选择器的值
        function setColumnSelectsByProgramaCode(programaCode) {
            if (!programaCode || !columnData || columnData.length === 0) {
                console.log("programaCode 为空或栏目数据未加载");
                return;
            }

            console.log("开始根据 programaCode 设置联动选择器:", programaCode);

            // 查找对应的栏目信息
            var targetColumn = columnData.find(function(item) {
                return item.columnId === programaCode;
            });

            if (!targetColumn) {
                console.log("未找到对应的栏目信息:", programaCode);
                return;
            }

            console.log("找到目标栏目:", targetColumn);

            // 根据栏目层级设置对应的选择器
            if (targetColumn.columnLevel === 1) {
                // 一级栏目
                $("#firstLevelColumn").val(targetColumn.columnId);
                loadSecondLevelColumns(targetColumn.columnId);
            } else if (targetColumn.columnLevel === 2) {
                // 二级栏目，需要先设置一级栏目
                $("#firstLevelColumn").val(targetColumn.parentColumnId);
                loadSecondLevelColumns(targetColumn.parentColumnId);
                // 延迟设置二级栏目，确保选项已加载
                setTimeout(function() {
                    $("#secondLevelColumn").val(targetColumn.columnId);
                    loadThirdLevelColumns(targetColumn.columnId);
                }, 100);
            } else if (targetColumn.columnLevel === 3) {
                // 三级栏目，需要先设置一级和二级栏目
                var secondLevelColumn = columnData.find(function(item) {
                    return item.columnId === targetColumn.parentColumnId;
                });

                if (secondLevelColumn) {
                    $("#firstLevelColumn").val(secondLevelColumn.parentColumnId);
                    loadSecondLevelColumns(secondLevelColumn.parentColumnId);

                    // 延迟设置二级和三级栏目
                    setTimeout(function() {
                        $("#secondLevelColumn").val(secondLevelColumn.columnId);
                        loadThirdLevelColumns(secondLevelColumn.columnId);

                        setTimeout(function() {
                            $("#thirdLevelColumn").val(targetColumn.columnId);
                            updateSelectedColumnInfo();
                        }, 100);
                    }, 100);
                }
            }

            // 设置 programaCode 隐藏字段
            $("#programaCode").val(programaCode);

            console.log("联动选择器设置完成");
        }


 /*       //保存草稿
        $(document).on("click", ".saveDraft", function () {
            if(formValidate("programaDataForm")){
                top.tabClick("processDraft");
                top.tabClose("li_programaDataFormTabs");
            }
        });*/




      /*  /!**
         * 保存草稿
         *!/
        function saveDraft() {
            if(formValidate("programaDataForm")){
                formsubmit('programaDataForm','action/approvalForm/saveDraft?source=PC');
                top.tabClick("processDraft");
                top.tabClose("li_programaDataFormTabs");
            }
        }*/

        /**
         * 保存草稿
         */
        function saveDraft() {
            if(formValidate("programaDataForm")){
                var data=getFormValue('programaDataForm');
                ajaxgeneral({
                    url: "action/approvalForm/saveDraft?source=PC",
                    data: data,
                    contentType: "application/json; charset=utf-8",
                    success: function (res) {
                        top.tabClick("processDraft");
                        top.tabClose("li_programaDataFormTabs");
                        top.mesShow("温馨提示", "操作成功", 1500);
                    }
                });


            /*    formsubmit('programaDataForm','action/approvalForm/saveDraft?source=PC');
                top.tabClick("processDraft");
                top.tabClose("li_programaDataFormTabs");*/
            }
        }

    </script>
</head>
<style>
    .formTable { width: 100%; margin-top: 30px; border-spacing: 0; border-top: 1px solid #dedede; border-left: 1px solid #dedede; }
    .formTable>tbody>tr:not(:first-child)>td { border-right: 1px solid #dedede; border-bottom: 1px solid #dedede; font-size: 14px; line-height: 28px; }
    .formTable>tbody>tr>td input,.formTable>tbody>tr>td span,.formTable>tbody>tr>td textarea,.formTable>tbody>tr>td .textbox .textbox-text{ border: none; font-size: 14px; }
    .formTable td.lable{ background-color: #ddf1fe; padding: 5px; text-align: left; }
    .formTable td .textAndInput_readonly, .formTable td .textAndInput_readonly .validatebox-readonly{ background-color: #fff; }
    .cselectorImageUL .btn{ right: 3px; top: 3px; }
    .cselectorImageUL input[type='file']{ right: 25px; top: 3px; }
    textarea{ line-height: 20px; letter-spacing: 1px; }
    table.tabForm{ border-color: #dedede; }
    .textbox{
        width: 100% !important;
    }

    /* 栏目选择样式 */
    .lan {
        display: flex;
        justify-content: space-between;
        align-items: center;
        margin: 20px 0;
        padding: 15px;
        background-color: #f8f9fa;
        border: 1px solid #e9ecef;
        border-radius: 4px;
    }

    .lan .left {
        display: flex;
        align-items: center;
        flex: 1;
    }

    .lan .label {
        font-weight: bold;
        margin-right: 15px;
        color: #333;
        min-width: 80px;
    }

    .lan-select {
        display: flex;
        gap: 10px;
        flex: 1;
    }

    .column-select {
        flex: 1;
        height: 32px;
        padding: 5px 10px;
        border: 1px solid #ddd;
        border-radius: 4px;
        background-color: #fff;
        font-size: 14px;
        color: #333;
        cursor: pointer;
    }

    .column-select:focus {
        border-color: #007bff;
        outline: none;
        box-shadow: 0 0 0 2px rgba(0, 123, 255, 0.25);
    }

    .column-select:disabled {
        background-color: #f5f5f5;
        color: #999;
        cursor: not-allowed;
    }

    .lan .right {
        margin-left: 15px;
    }
    /* TinyMCE 富文本编辑器样式 */
    .richText {
        width: 100% !important;
    }

    .richText textarea {
        width: 100% !important;
    }

    .tox-tinymce {
        width: 100% !important;
        max-width: 100% !important;
    }

    .tox-editor-container {
        width: 100% !important;
    }
</style>
<body class="body_page" style="padding-top:85px;"><!--noNextUserLocation无下一审批人的节点比如归档，可以为多个节点中间用|隔开-->
<form id="programaDataForm" method="post" formLocation="hnjjwz.start" archiveLocation="hnjjwz.confirm" noNextUserDecisionId="end" contentType="application/json; charset=utf-8"
      nextBtnOther="nextBtnOther()" cmd-select="action/approvalForm/getApprovalFromDetail"  beforesubmit="beforesubmit()" submitcallback="approvalS()" getcallback="getcallback()" ><!--cmd-select="action/approvalForm/getApprovalFromDetail"-->
    <div class="pageInfo">
        <div class="pageInfoD">
            <a class="hide btn small fl mr15 nextBtn"><i class="iconfont">&#xe688;</i><font>流转下一步</font></a>
          <!--  <a class="hide btn small fl mr15 saveDraft" onclick="formsubmit('programaDataForm','action/approvalForm/saveDraft')"><i class="iconfont">&#xe63a;</i><font>保存草稿</font></a>
            <a class="hide btn small fl mr15 abolish"><i class="iconfont">&#xe6ec;</i><font>废除草稿</font></a>-->
            <a class="hide btn small fl mr15 saveDraft" onclick="saveDraft()"><i class="iconfont">&#xe63a;</i><font>保存草稿</font></a>
            <a class="hide btn small fl mr15 abolish"><i class="iconfont">&#xe6ec;</i><font>废除草稿</font></a>
            <a class="hide btn small fl mr15 cancel"><i class="iconfont">&#xe6ec;</i><font>注销</font></a>
            <a class="hide btn small fl mr15 flowTrack"><i class="iconfont">&#xe68c;</i><font>流程跟踪</font></a>
            <a class="hide btn small fl mr15 viewComments"><i class="iconfont">&#xe629;</i><font>查看意见</font></a>
            <a class="hide btn small fl mr15 formReset" onclick="formreset('programaDataForm')"><i class="iconfont">&#xe646;</i><font>重置</font></a>
<!--            <a class="hide btn small fl mr15 printOut"><i class="iconfont">&#xe678;</i><font>打印</font></a>-->
            <a class="hide btn small fl mr15 processImg"><i class="iconfont">&#xe6bd;</i><font>流程图</font></a>
        </div>
    </div>

    <fieldset class="title">
        <legend><font>栏目内容信息</font></legend>
    </fieldset>

    <!-- <ul class="tab" style="height: 32px; width:1000px;">
        <li class="w1 tab_click" style="width:50%">选择板块</li>
        <li class="w1" style="width:50%">基本信息</li>
    </ul> -->
    <div class="tabD">
        <!-- <div style="width: 1200px;">
            <table border="0" cellpadding="0" cellspacing="20">
                <tr>
                    <td style="width: 150px">
                        <h5>栏目类型:</h5>
                    </td>
                    <td style="width: 850px">
                        <div class='pcol of_hidden'></div>
                    </td>
                </tr>
            </table>
            <table border="0" cellpadding="0" cellspacing="10">
                <tr id="twoStair" style="display:none">
                    <td style="width: 150px">
                        <h5>子栏目类型:</h5>
                    </td>
                    <td style="width: 850px">
                        <div class='zcol of_hidden'></div>
                    </td>
                </tr>
            </table>
            <table border="0" cellpadding="0" cellspacing="10">
                <tr id="threeStair" style="display:none">
                    <td style="width: 150px">
                        <h5>三级栏目:</h5>
                    </td>
                    <td style="width: 850px">
                        <div class='tcol'></div>
                    </td>
                </tr>
            </table>
        </div> -->
        <div  >
            <input id="id" name="id" type="hidden"/>
            <input id="belongCompanyTypeDictValue" name="belongCompanyTypeDictValue" type="hidden" noReset="true"/>
            <input id="belongDepartmentCode" name="belongDepartmentCode" type="hidden" noReset="true"/>
            <input id="belongCompanyCode" name="belongCompanyCode" type="hidden" noReset="true"/>
            <input id="pmInsId" name="pmInsId" type="hidden"/>
            <input id="describe" name="describe" type="hidden"/>
               <!-- 栏目选择相关隐藏字段 -->
            <input id="programaCode" name="programaCode" type="hidden" />
            <!-- <input id="programaDisplayName" name="programaDisplayName" type="hidden" /> -->
            <div class="lan" id="selectPrograma">
                <div class="left">
                    <div class="label">所属栏目：</div>
                    <div class="lan-select">
                        <select id="firstLevelColumn" name="firstLevelColumn" class="column-select">
                            <option value="">--请选择一级栏目--</option>
                        </select>
                        <select id="secondLevelColumn" name="secondLevelColumn" class="column-select">
                            <option value="">--请选择二级栏目--</option>
                        </select>
                        <select id="thirdLevelColumn" name="thirdLevelColumn" class="column-select">
                            <option value="">--请选择三级栏目--</option>
                        </select>
                    </div>
                </div>
                <div class="right">
                    <a class="btn small fl mr15 preview">
                        <font>详情预览</font>
                    </a>
                </div>
            </div>

            <!-- <table border="1" cellpadding="0" cellspacing="15" class="tabForm" style="width: 1000px;height: 750px"> -->
            <table border="0" style="width: 100%;margin-bottom:20px" class="tabForm formTable" cellpadding="0" cellspacing="10" >
                <tr class="firstTr">
                    <td width="11.1%"></td>
                    <td width="22.2%"></td>
                    <td width="11.1%"></td>
                    <td width="22.2%"></td>
                    <td width="11.1%"></td>
                    <td width="22.2%"></td>
                </tr>
                <!-- <tr>
                    <td align="right" class="lable">申请部门</td>
                    <td>
                        <input id="belongDepartmentName" name="belongDepartmentName" type="text" readonly="readonly" noReset="true" required="required"/>
                    </td>
                </tr> -->
                <tr>
                    <td align="right" class="lable">标题<font class="col_r">*</font></td>
                    <td colspan="3">
                        <input id="title" name="title" type="text" class="easyui-validatebox"  required="required" />
                    </td>
                     <td align="left" class="tit">文章来源</td>
                    <td>
                        <input id="articleFrom" name="articleFrom" type="text" class="easyui-validatebox"   />

                    </td>

                </tr>
                 <tr >
                     <td align="left" class="tit">发布时间</td>
                    <td >
                        <input id="articleStartTime"  name="articleStartTime" type="text" class="easyui-datetimebox"
                            data-options="panelHeight:'auto',editable:false"
                            style="width:100%;height:32px;"/>
                    </td>
                    <td align="left" class="lable" >是否置顶显示</td>
                    <td>
                        <input style="width: 30px;" type="radio" id="stickFlagYes" name="stickFlag" value="1" noReset="true"/>
                        <label for="stickFlagYes">是</label>
                        <input style="width: 30px;" type="radio" id="stickFlagNo" name="stickFlag" value="0" noReset="true"/>
                        <label for="stickFlagNo">否</label>
                    </td>
                    <td align="left" class="lable">是否着重显示</td>
                    <td >
                        <input style="width: 30px;" type="radio" id="importantFlagYes" name="importantFlag" value="1" noReset="true"/>
                        <label for="importantFlagYes">是</label>
                        <input style="width: 30px;" type="radio" id="importantFlagNo" name="importantFlag" value="0" noReset="true"/>
                        <label for="importantFlagNo">否</label>
                    </td>
                </tr>
                 <tr id="slideShowTitleTr" style="display: none;">
                    <td align="left" class="lable">所属栏目
                    </td>
                    <td colspan="5">
                        <input id="programaDisplayName" name="programaDisplayName" type="text" class="easyui-validatebox"
                            />
                    </td>
                </tr>



<!--                <tr>
                    <td align="right" class="lable" >摘要</td>
                    <td >

                        <textarea id="digest" name="digest" class="easyui-validatebox" validType="maxLength[4000]" style="width: 100%; height: 120px; resize: none;" required="true"></textarea>

                        <textarea id="digest" name="digest" placeholder="最多输入2000字"
                                  validType="maxLength[2000,'digestTip']" class="easyui-validatebox"
                                  style="width:100%;height:120px;resize:none;"></textarea>
                        <span class="digestTip"></span>
                    </td>
                </tr>-->
                    <tr>
                <td colspan="6" style="padding: 0" class="richText">
                    <textarea id="artHtmlContentCopy" name="artHtmlContentCopy"
                              style="width:100%;height:450px"></textarea>
                    <input type="text" id="mainBody" name="mainBody" class="hide"/>
                </td>
            </tr>
                <!-- <tr>
                    <td align="right" height="450" class="lable">正文<font class="col_r">*</font></td>
                    <td colspan="5">
                        <textarea id="mainBody" name="mainBody" class="kindeditor" style="width:100%;height: 500px;visibility:hidden;" ></textarea>
                    </td>
                </tr> -->
                <!--流程中能看到，但在首页看不到-->
                <!-- <tr id="coverImageFile" style="display: none">
                    <td align="right" class="lable">封面图像<font class="col_r">*</font></td>
                    <td valign="top"  colspan="5">
                        <input id="coverImageFileList" name="coverImageFileList" type="text" mulaccept="image/*" class="cselectorImageUpload" sizelength="1" extension="gif,jpg,jpeg,png"
                               btnmsg="<i class='iconfont' title='添加'>&#xe641;</i>" href="sys/file/uploadProcessFiles?pmInsType=A&pmInsTypePart=1" />
                    </td>
                </tr> -->
                <!--流程中能看到，但在首页看不到-->
                <!-- <tr id="authBookFile" style="display: none">
                    <td align="right" class="lable" >授权书<font class="col_r">*</font></td>
                    <td valign="top" colspan="5">
                        <input id="authBookFileList" name="authBookFileList" type="text" file="true" class="cselectorImageUpload" mulaccept="true" btnmsg="<i class='iconfont' title='添加'>&#xe641;</i>"
                               href="sys/file/uploadProcessFiles?pmInsType=A&pmInsTypePart=2" />
                    </td>
                </tr> -->
                <tr id="accessoryFile" >
                    <td align="right" class="lable" >附件</td>
                    <td valign="center"  colspan="5">
                        <input id="accessoryFileList" name="accessoryFileList" type="text" file="true" class="cselectorImageUpload" mulaccept="true" btnmsg="<i class='iconfont' title='添加'>&#xe641;</i>"
                               href="sys/file/uploadProcessFiles?pmInsType=A&pmInsTypePart=3"/>
                    </td>
                </tr>
                <!-- <tr id="videoFile" style="display: none">
                    <td align="right" class="lable" >视频<font class="col_r">支持mp4、rm、rmvb、flv格式</font></td>
                    <td valign="center"  colspan="5">
                        <input id="videoFileList" name="videoFileList" type="text" file="true" class="cselectorImageUpload" mulaccept="true" btnmsg="<i class='iconfont' title='添加'>&#xe641;</i>"
                               href="sys/file/uploadProcessFiles?pmInsType=A&pmInsTypePart=4" />
                    </td>
                </tr> -->
            </table>
        </div>
    </div>
</form>
</body>
</html>
