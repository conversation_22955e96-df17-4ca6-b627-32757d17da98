<!DOCTYPE html>
<html xmlns="http://www.w3.org/1999/xhtml"
      xmlns:th="http://www.thymeleaf.org">
<head>
    <meta charset="UTF-8">
    <title>家风</title>
    <meta http-equiv="Content-Type" content="text/html; charset=utf-8"/>
    <meta http-equiv="X-UA-Compatible" content="IE=edge" />
    <meta name="viewport" content="width=device-width, initial-scale=1, shrink-to-fit=no"/>

    <!--    <meta http-equiv=“X-UA-Compatible” content=“IE=8″>-->
    <!--
    <link href="http://************:8088/simbestui/fonts/iconfont/iconfont.css?v=svn.revision" th:href="@{http://************:8088/simbestui/fonts/iconfont/iconfont.css?v=svn.revision}" rel="stylesheet"/>
    -->
    <link href="../../css/webSite.css?v=svn.revision" th:href="@{/css/webSite.css?v=svn.revision}" rel="stylesheet"/>
    <link href="../../fonts/iconfont/iconfont.css?v=svn.revision" th:href="@{/fonts/iconfont/iconfont.css?v=svn.revision}" rel="stylesheet"/>
    <link href="http://************:8088/simbestui/js/themes/default/easyui.css?v=svn.revision" th:href="@{http://************:8088/simbestui/js/themes/default/easyui.css?v=svn.revision}" rel="stylesheet"/>
    <link href="http://************:8088/simbestui/js/themes/icon.css?v=svn.revision" th:href="@{http://************:8088/simbestui/js/themes/icon.css?v=svn.revision}" rel="stylesheet"/>
    <link href="http://************:8088/simbestui/css/public.css?v=svn.revision" th:href="@{http://************:8088/simbestui/css/public.css?v=svn.revision}" rel="stylesheet"/>
    <link href="http://************:8088/simbestui/css/pages.css?v=svn.revision" th:href="@{http://************:8088/simbestui/css/pages.css?v=svn.revision}" rel="stylesheet"/>
    <script src="http://************:8088/simbestui/js/jquery.min.js?v=svn.revision" th:src="@{http://************:8088/simbestui/js/jquery.min.js?v=svn.revision}" type="text/javascript"></script>
    <script src="http://************:8088/simbestui/js/jquery.easyui.min.js?v=svn.revision" th:src="@{http://************:8088/simbestui/js/jquery.easyui.min.js?v=svn.revision}" type="text/javascript"></script>
    <script src="http://************:8088/simbestui/js/easyui-lang-zh_CN.js?v=svn.revision" th:src="@{http://************:8088/simbestui/js/easyui-lang-zh_CN.js?v=svn.revision}" type="text/javascript"></script>
    <script src="http://************:8088/simbestui/js/kindeditor/kindeditor.min.js?v=svn.revision" th:src="@{http://************:8088/simbestui/js/kindeditor.min.js?v=svn.revision}" type="text/javascript"></script>
    <script src="../../js/jquery.config.js?v=svn.revision" th:src="@{/js/jquery.config.js?v=svn.revision}" type="text/javascript"></script>
    <script src="http://************:8088/simbestui/js/jquery.zjsfile.js?v=svn.revision" th:src="@{http://************:8088/simbestui/js/jquery.zjsfile.js?v=svn.revision}" type="text/javascript"></script>
    <script src="http://************:8088/simbestui/js/jquery.aduq.js?v=svn.revision" th:src="@{http://************:8088/simbestui/js/jquery.aduq.js?v=svn.revision}" type="text/javascript"></script>
    <script src="http://************:8088/simbestui/js/jquery.process.js?v=svn.revision" th:src="@{http://************:8088/simbestui/js/jquery.process.js?v=svn.revision}" type="text/javascript"></script>
</head>
<style>
    body{background: #f5f5f5;text-align: center;}
    .imgtobig{position: fixed;width: 70%;height: 70%;left: 15%;top: 15%;}
    .imgtobig i{color: #909090;}
</style>
<body>
<div  class="head" style="box-shadow: 0px 10px 20px -20px #626262;height: 70px;width:100%;background: white;position: fixed;z-index: 88;display: none;"></div>
<div class="logo_top">
    <img src="../../images/family_logo1.jpg" alt="" class="logo_img"/>
</div>
<div id="wrapper_body">
    <img src="../../images/yinyan8.jpg" alt="" class="wrapper_img">
    <div class="order_details">
        <ul class="order_details_ul">
            <li class="fortab prepaids checked">绘画</li>
            <li class="fortab prepaids2">书法</li>
            <li class="fortab prepaids3">摄影</li>
            <li class="fortab prepaids4">征文</li>
        </ul>
    </div>
    <!--        <div style="float: right;margin-right: 105px;margin-top: 0px;">-->
    <!--            <h6 style="display: inline-block;color: #ba7d20">每位员工最多投五票</h6>-->
    <!--            <img src="../../images/dianzan.png" alt="点赞" style="width: 16px;height: 16px;margin-top: -6px;">-->
    <!--        </div>-->
    <div>
        <div class="tablelist showdomdiv showsss tablelist1">
        </div>
        <div class="tablelist hidedomdiv showsss tablelist2">
        </div>
        <div class="tablelist hidedomdiv showsss tablelist3" >
        </div>
        <div class="tablelist hidedomdiv showsss tablelist4" >
        </div>
    </div>
</div>
<div style="width: 100%;position: fixed;top: 0;left: 0;z-index: 100;display: none;filter: blur(5px); filter:alpha(opacity=30);background: black;opacity:0.3" class="pop-up1"></div>
<div class="pop-up">
    <img src="../../images/heye2.png" alt="" style="width: 100%;height: 100%;">
    <img src="../../images/yinyan2.jpg" alt="" class="pop-up_img" title="点击查看原图">
    <i class='iconfont icon-guanbi guanbi' style="color:#909090;font-size: 30px;position: fixed;left: 71%;top: 21%;cursor: pointer;z-index: 1000;"></i>
    <div style="position: fixed;top: 25%;left: 55%;font-size: 20px;font-weight:bold;color:rgba(232,114,78,1);" class="img_jianjie">图片简介</div>
    <div class="pop_up_title" ></div>
    <div class="pop_up_text"></div>
    <div class="pop_up_user"></div>
    <div class="newsImg newsImg1" style="position: fixed;top: 71%;left: 47%;"></div>
</div>
<div class="result" id="outdiv">
    <div class="indiv">
        <img class="imgresult" id="bigimg" src="">
    </div>
</div>
<div class="copy" style="background: #f5f5f5;color: black;">©版权所有 中国移动通信集团河南有限公司</div>



</body>
<script type="text/javascript">
    var a = 1;
    $(document).ready(function(){
        // 改为鼠标移上的事件只需把click改为mousemove
        $('.tablelist2').css('display','none');
        $('.tablelist3').css('display','none');
        $('.tablelist4').css('display','none');
        $(".fortab").mousemove(function(){
            var number=$(".fortab").index(this);
            $(this).addClass("checked");
            $(this).siblings().removeClass("checked");
            $(".tablelist:eq("+number+")").show();
            $(".tablelist:eq("+number+")").siblings().hide();
        });
    });
    $(function(){
        $('.newsContent').hover(function(){
            $(this).stop().animate({
                'margin-left':'-7px',
                'margin-right':'17px'
            })
        },function(){
            $(this).stop().animate({
                'margin-left':'0',
                'margin-right':'10px'
            })
        })
    });


    $(function() {
        //获取当前登录人
        var username;
        var gps=getQueryString();

        var url="action/templateLayout/constructDanColumnsLayout/sso?locationType=danColumns2019&appcode=hnjjwz&loginuser=" + gps.loginuser;
        if(gps.from=="oa"){
            ajaxgeneral({
                url: "getCurrentUser/sso?appcode=hnjjwz&loginuser=" + gps.loginuser,
                success: function (ress) {
                    username=ress.data.username;
                }
            });
        }else {
            getCurrent();
            username=web.currentUser.username;
            url="action/templateLayout/constructDanColumnsLayout?locationType=danColumns2019";
        }

        ajaxgeneral({
            url: url,
            success: function (res) {
                var data = res.data;
                //绘画
                if(data[2].templateData){
                    for(var j=0;j<data[2].templateData.length;j++){
                        var paintingData2 = data[2].templateData[j];
                        var voteQuantity2 = paintingData2.voteQuantity;
                        if(voteQuantity2 == null){
                            voteQuantity2 = 0
                        };
                        var div_list =['<div class="list_content"><div class="newsContent" dataid="'+ paintingData2.id+'" imgUrl=" '+ paintingData2.previewUrl+'" Title=" '+ paintingData2.familyTitle +'" vote="'+ paintingData2.voteQuantity +'" text="'+paintingData2.mainBody+'" com="'+paintingData2.company+'" user="'+paintingData2.people+'">',
                            '<img class="content_top" src=" '+ paintingData2.loadingUrl+'">',
                            '<p style="height:30px;font-size: 16px;margin-top: 5px;">" '+ paintingData2.familyTitle +' "</p>',
                            ' <div class="content_bottom" dataid="'+ paintingData2.id+'" datatype="绘画" isvote="'+paintingData2.vote+'"><i class="iconfont icon-zan"></i>&nbsp;'+voteQuantity2+'</div>',
                            '</div></div>'
                        ];
                        $(".tablelist1").append(div_list.join(""));
                        if(paintingData2.vote){
                            $('.content_bottom[dataid="'+ paintingData2.id+'"]').css('color','white');
                            $('.newsImg1').css('color','white');
                        }else{
                            $('.content_bottom[dataid="'+ paintingData2.id+'"]').css('color','#FFE400');
                            $('.content_bottom[dataid="'+ paintingData2.id+'"]').css('font-weight','bold');
                            $('.newsImg1').css('color','#FFE400');
                        }
                        $('.content_bottom').css('color','#D3D3D3');
                        $('.newsImg1').css('color','#D3D3D3');


                        // var userAgent = navigator.userAgent; //取得浏览器的userAgent字符串
                        // var isIE = userAgent.indexOf("compatible") > -1 && userAgent.indexOf("MSIE") > -1; //判断是否IE<11浏览器
                        // var reIE = new RegExp("MSIE (\\d+\\.\\d+);");
                        // reIE.test(userAgent);
                        // var fIEVersion = parseFloat(RegExp["$1"]);
                        // if(fIEVersion == 8) {
                        //     $('.content_bottom').css('left','17%')
                        //
                        // }
                    }
                }
                // 书法
                if(data[4].templateData){
                    for(var j=0;j<data[4].templateData.length;j++){
                        var paintingData4 = data[4].templateData[j];
                        var voteQuantity4 = paintingData4.voteQuantity;
                        if(voteQuantity4 == null){
                            voteQuantity4 = 0
                        };
                        var div_list =['<div class="list_content"><div class="newsContent" imgUrl=" '+ paintingData4.previewUrl+'" Title=" '+ paintingData4.familyTitle +'" vote="'+ paintingData4.voteQuantity +'" text="'+paintingData4.mainBody+'" com="'+paintingData4.company+'" user="'+paintingData4.people+'">',
                            '<img class="content_top" src=" '+ paintingData4.loadingUrl+'">',
                            '<p style="height:30px;font-size: 16px;margin-top: 5px;">" '+ paintingData4.familyTitle +'"</span>',
                            ' <div class="content_bottom" dataid="'+ paintingData4.id+'" datatype="书法" isvote="'+paintingData4.vote+'"><i class="iconfont icon-zan"></i>&nbsp;'+voteQuantity4+'</div>',
                            '</div></div>'
                        ];
                        $(".tablelist2").append(div_list.join(""));
                        if(paintingData4.vote){
                            $('.content_bottom[dataid="'+ paintingData4.id+'"]').css('color','white');
                            $('.newsImg1').css('color','white');
                        }else{
                            $('.content_bottom[dataid="'+ paintingData4.id+'"]').css('color','#FFE400');
                            $('.content_bottom[dataid="'+ paintingData4.id+'"]').css('font-weight','bold');
                            $('.newsImg1').css('color','#FFE400');
                        }
                        $('.content_bottom').css('color','#D3D3D3');
                        $('.newsImg1').css('color','#D3D3D3');
                    }
                }
                //  摄影
                if(data[1].templateData){
                    for(var j=0;j<data[1].templateData.length;j++){
                        var paintingData1 = data[1].templateData[j];
                        var voteQuantity1 = paintingData1.voteQuantity;
                        if(voteQuantity1 == null){
                            voteQuantity1 = 0
                        };
                        var div_list =['<div class="list_content"><div class="newsContent" imgUrl=" '+ paintingData1.previewUrl+'" Title=" '+ paintingData1.familyTitle +'" vote="'+ paintingData1.voteQuantity +'" text="'+paintingData1.mainBody+'" com="'+paintingData1.company+'" user="'+paintingData1.people+'">',
                            '<img class="content_top" src=" '+ paintingData1.loadingUrl+'">',
                            '<p style="height:30px;font-size: 16px;margin-top: 5px;">" '+ paintingData1.familyTitle +'"</span>',
                            ' <div class="content_bottom" dataid="'+ paintingData1.id+'" datatype="摄影" isvote="'+paintingData1.vote+'"><i class="iconfont icon-zan"></i>&nbsp;'+voteQuantity1+'</div>',
                            '</div></div>'
                        ];
                        $(".tablelist3").append(div_list.join(""));
                        if(paintingData1.vote){
                            $('.content_bottom[dataid="'+ paintingData1.id+'"]').css('color','white');
                            $('.newsImg1').css('color','white');
                        }else{
                            $('.content_bottom[dataid="'+ paintingData1.id+'"]').css('color','#FFE400');
                            $('.content_bottom[dataid="'+ paintingData1.id+'"]').css('font-weight','bold');
                            $('.newsImg1').css('color','#FFE400');
                        }
                        $('.content_bottom').css('color','#D3D3D3');
                        $('.newsImg1').css('color','#D3D3D3');
                    }
                }
                $('.content_bottom,.newsImg1').click(function (event) {

                    if(a == 1){
                        alert('投票活动已截止,谢谢！');
                        event.stopPropagation();
                    }else{
                        var isvote =$(this).attr('isvote');
                        var dataids = $(this).attr('dataid');
                        if(isvote == "false"){
                            alert('请勿重复投票,谢谢！');
                            return false;
                        }else{
                            $this = $(this);
                            var dataid = $(this).attr('dataid');
                            var dataType = $(this).attr('dataType');
                            var poll = parseInt($this.text()) + 1;

                            ajaxgeneral({
                                url:"action/recordPerson/vote",
                                contentType: "application/json;charset=UTF-8",
                                data:{"Id":dataid,"userName":username,"type":dataType},
                                success:function (res) {
                                    if(res.data ==  false){
                                        // top.mesAlert("提示信息", "总计时间需要小于或等于8小时!", 'info');
                                        alert('此类作品您的投票次数已超上限(五票)！');
                                        return false;
                                    }else{
                                        alert('投票成功！');
                                        for(var i =0;i<$('.content_bottom').length;i++){
                                            if($('.content_bottom').eq(i).attr('dataid') == dataid){
                                                $('.content_bottom').eq(i).html('<i class="iconfont icon-zan"></i>'+'&nbsp;'+ poll);
                                            }
                                        }
                                        $('.newsImg1').text(poll);
                                        $this.html('<i class="iconfont icon-zan"></i>'+'&nbsp;'+ poll);
                                        // $('.content_bottom[dataid="'+ dataids+'"]')
                                        $('.content_bottom[dataid="'+ dataids+'"]').css('color','#FFE400');
                                        $('.content_bottom[dataid="'+ dataids+'"]').css('font-weight','bold');
                                        $('.content_bottom[dataid="'+ dataids+'"]').attr('isvote','false');
                                        $('.newsImg1').attr('isvote','false');
                                        $('.newsImg1').css('color','#FFE400');
                                    }
                                }
                            })
                            return false;
                        }

                    }

                });
                $('.newsContent').click(function(){
                    $('.pop-up_img').attr('src',$(this).attr('imgUrl'));
                    if($(this).attr('text') == 'null'){
                        $('.pop_up_text').html();
                    }else{
                        $('.pop_up_text').html($(this).attr('text'));
                    }
                    $('.pop_up_title').text($(this).attr('Title'));
                    // $('.newsImg1').text($(this).find('.content_bottom').text());
                    $('.newsImg1').html('<i class="iconfont icon-zan"></i>'+'&nbsp;'+ $(this).find('.content_bottom').text());
                    $('.newsImg1').attr('dataid',$(this).find('.content_bottom').attr('dataid'));
                    $('.newsImg1').attr('dataType',$(this).find('.content_bottom').attr('dataType'));
                    $('.newsImg1').attr('vote',$(this).find('.content_bottom').text());
                    $('.newsImg1').attr('isvote',$(this).find('.content_bottom').attr('isvote'));
                    if($(this).find('.content_bottom').attr('isvote') == 'false'){
                        $('.newsImg1').css('color','#FFE400')
                    }else{
                        $('.newsImg1').css('color','white')
                    }
                    $('.newsImg1').css('color','#D3D3D3');
                    $('.pop_up_user').text($(this).attr('com')+$(this).attr('user'));
                    $('.pop-up1').css('display','block');
                    $('.pop-up').css('display','block');
                    // $("body").css("overflow-y","hidden");
                })
                // 征文
                if(data[3].templateData){
                    for(var j=0;j<data[3].templateData.length;j++){
                        var paintingData3 = data[3].templateData[j];
                        var voteQuantity = paintingData3.voteQuantity;
                        if(voteQuantity == null){
                            voteQuantity = 0
                        };
                        var div_list = ['<div class="newsCollect">',
                            '<div class="newsBtn"></div>',
                            '<div class="newsSpan" dataid="'+ paintingData3.id+'" dataTitle="'+ paintingData3.familyTitle+'" dataabout="'+ paintingData3.about+'"><a target="_blank" href="familyList.html?id='+paintingData3.id+'" style="color: black">'+paintingData3.familyTitle+'</a>  <a href="" style="color:rgba(186,134,53,1); class="jianjie"> [简介] </a></div>',
                            '<div class="newsImg newsImg2" dataid="'+ paintingData3.id+'" datatype="征文" isvote="'+paintingData3.vote+'"><i class="iconfont icon-zan"></i>&nbsp;'+voteQuantity+'</div>',
                            '<div class="newsSelect">'+paintingData3.about+'</div>',
                            '</div>'
                        ];
                        $(".tablelist4").append(div_list.join(""));
                        if(paintingData3.vote){
                            // $('.content_bottom[dataid="'+ dataids+'"]')
                            $('.newsImg2[dataid="'+ paintingData3.id+'"]').css('color','white');
                        }else{
                            $('.newsImg2[dataid="'+ paintingData3.id+'"]').css('color','#FFE400');
                            $('.newsImg2[dataid="'+ paintingData3.id+'"]').css('font-weight','bold');
                        }
                        $('.newsImg2').css('color','#D3D3D3');
                        $($('.newsSpan[dataid="'+ paintingData3.id+'"]').children()[1]).hover(function () {
                            var title = $(this).parent().attr('dataTitle').length;
                            var about = $(this).parent().attr('dataabout').length;
                            $(this).parent().parent().find(".newsSelect").css('left',title*20 + 'px')
                            // $(this).parent().parent().find(".newsSelect").css('height',about*3 + 'px')
                            $(this).parent().parent().find(".newsSelect").css('display','block')
                        },function(){
                            $(this).parent().parent().find(".newsSelect").css('display','none')
                        })
                    }
                }
                $('.newsImg2').click(function (event) {
                    if(a == 1){
                        alert('投票活动已截止,谢谢！');
                    }else{
                        var isvote = $(this).attr('isvote');
                        var dataids = $(this).attr('dataid');
                        if(isvote == "false"){
                            alert('请勿重复投票,谢谢！');
                            return false;
                        }else{
                            var dataid = $(this).attr('dataid');
                            var dataType = $(this).attr('dataType');
                            var poll = parseInt($(this).text()) + 1;
                            //获取当前登录人
                            getCurrent();
                            var username=web.currentUser.username;
                            ajaxgeneral({
                                url: "action/recordPerson/vote",
                                contentType: "application/json;charset=UTF-8",
                                data: {"Id": dataid, "userName": username, "type": dataType},
                                success: function (res) {
                                    if(res.data ==  false){
                                        alert('此类作品您的投票次数已超上限(五票)！');
                                        return false;
                                    }else{
                                        alert('投票成功');
                                        $('.newsImg2[dataid="'+ dataids+'"]').html('<i class="iconfont icon-zan"></i>'+'&nbsp;'+ poll);
                                        $('.newsImg2[dataid="'+ dataids+'"]').css('color','#FFE400');
                                        $('.newsImg2[dataid="'+ dataids+'"]').css('font-weight','bold');
                                        $('.newsImg2[dataid="'+ dataids+'"]').attr('isvote','false');
                                    }
                                }
                            });
                        }

                    }

                });
                $('.pop-up_img').click(function(){
                    // var thiselement=$(this);
                    tobigimg($(this)[0]);
                    // showImg("#outdiv",".indiv","#bigimg",thiselement);
                })
            }
        });
    });
    //固定四个选项在顶部
    $(function(){
        window.onscroll = function (ev) {
            var scrollTop = document.documentElement.scrollTop || document.body.scrollTop;
            if(scrollTop > 600){
                $(".order_details").addClass('silde');
                $(".head").css('display','block');
            }else{
                $(".order_details").removeClass('silde');
                $(".head").css('display','none');
            }
        }
    });
    $(function () {
        $('.pop-up1').css('height',$(window).height());
        $('.guanbi').on('click',function () {
            $('.pop-up1').css('display','none');
            $('.pop-up').css('display','none');
            // $("body").css("overflow-y","auto");
        });
    });
    //兼容ie浏览器
    $(function isIE() { //ie?
        if (!!window.ActiveXObject || "ActiveXObject" in window){
            $(" .pop-up_img,.pop_up_title,.pop_up_text,.pop_up_user,.newsImg1,.img_jianjie").css('position','absolute')
            $('.pop-up_img').css('left','5%');
            $('.pop-up_img').css('top','10%');
            $('.pop_up_title').css('top','18%');
            $('.pop_up_title').css('left','60%');
            $('.img_jianjie').css('top','11%');
            $('.img_jianjie').css('left','55%');
            $('.pop_up_user').css('right','3%');
            $('.pop_up_user').css('top','72%');
            $('.newsImg1').css('top','83%');
            $('.newsImg1').css('left','41%');
            $('.pop_up_text').css('left','60%');
            $('.pop_up_text').css('top','26%');


        }
    });

    //查看原图
    function showImg(outdiv,indiv,bigimg,thiselement){
        var winW = $(window).width();
        var winH = $(window).height();
        var src = $(thiselement).attr('src');
        $(bigimg).attr("src",src);
        $("<img/>").attr("src",src).load(function(){
            var imgW = this.width;
            var imgH = this.height;
            var scale= imgW/imgH;
            if( imgH > winH ){
                $("body").css("overflow-y","hidden");
                $(outdiv).css("overflow","auto");
                $(bigimg).css("width","auto").css("height",winH+'px');
                imgH = winW/scale;
                var h=(winH-imgH)/2;
                $(indiv).css({"left":0,"top":h});
            }else{
                $(bigimg).css("width",imgW+'px').css("height",imgH+'px');
                var w=(winW-imgW)/2;
                var h=(winH-imgH)/2;
                $(indiv).css({"left":w,"top":h});
            }
            $(outdiv).fadeIn("fast");
            $(outdiv).click(function(){
                $(this).fadeOut("fast");
                $("body").css("overflow-y","auto");
            });
        });
    }
</script>
</html>