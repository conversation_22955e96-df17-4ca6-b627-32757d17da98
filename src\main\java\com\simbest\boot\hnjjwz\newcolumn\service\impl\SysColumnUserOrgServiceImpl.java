package com.simbest.boot.hnjjwz.newcolumn.service.impl;

import cn.hutool.core.map.MapUtil;
import com.simbest.boot.base.service.impl.LogicService;
import com.simbest.boot.base.web.response.JsonResponse;
import com.simbest.boot.constants.ApplicationConstants;
import com.simbest.boot.hnjjwz.newcolumn.model.SysColumnUserOrg;
import com.simbest.boot.hnjjwz.newcolumn.model.SysNewColumnModel;
import com.simbest.boot.hnjjwz.newcolumn.repository.SysColumnUserOrgRepository;
import com.simbest.boot.hnjjwz.newcolumn.service.ISysColumnUserOrgService;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;

import java.util.ArrayList;
import java.util.Arrays;
import java.util.List;
import java.util.Map;

@Service
@Slf4j
public class SysColumnUserOrgServiceImpl extends LogicService<SysColumnUserOrg, String> implements ISysColumnUserOrgService {

    private SysColumnUserOrgRepository repository;

    @Autowired
    public SysColumnUserOrgServiceImpl(SysColumnUserOrgRepository repository) {
        super(repository);
        this.repository = repository;
    }

    @Override
    public JsonResponse createColumnUserOrg(Map<String, String> param) {
        String columnId = MapUtil.getStr(param, "columnId");
        String value = MapUtil.getStr(param, "value");
        List<String> values = Arrays.asList(value.split(","));

        for (String s : values) {
            SysColumnUserOrg model = new SysColumnUserOrg();
            model.setCreator(ApplicationConstants.ADMINISTRATOR);
            model.setModifier(ApplicationConstants.ADMINISTRATOR);
            model.setEnabled(Boolean.TRUE);
            model.setColumnId(columnId);
            model.setValue(s);
            this.insert(model);
        }
        return null;
    }
}
