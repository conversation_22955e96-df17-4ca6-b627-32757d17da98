package com.simbest.boot.hnjjwz.process.query.service.impl;

import com.google.common.collect.Maps;
import com.simbest.boot.base.exception.Exceptions;
import com.simbest.boot.base.service.impl.GenericService;
import com.simbest.boot.base.web.response.JsonResponse;
import com.simbest.boot.bps.process.bussiness.model.ActBusinessStatus;
import com.simbest.boot.hnjjwz.constants.Constants;
import com.simbest.boot.hnjjwz.process.query.service.IQueryActBusinessStatusService;
import com.simbest.boot.hnjjwz.util.OperateLogTool;
import com.simbest.boot.sys.model.SysDictValue;
import com.simbest.boot.sys.model.SysOperateLog;
import com.simbest.boot.sys.service.ISysOperateLogService;
import com.simbest.boot.sys.service.impl.SysDictValueService;
import com.simbest.boot.util.security.SecurityUtils;
import com.simbest.boot.wf.unitfytodo.IProcessTodoDataService;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.lang3.StringUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.data.domain.Page;
import org.springframework.data.domain.PageRequest;
import org.springframework.data.domain.Pageable;
import org.springframework.data.domain.Sort;
import org.springframework.stereotype.Service;

import java.util.List;
import java.util.Map;

/**
 * 用途：查询待办已办
 * 作者：zhangshaofeng
 * 时间：2018/07/05
 */
@Slf4j
@Service(value = "queryActBusinessStatus")
public class QueryActBusinessStatusImpl extends GenericService<ActBusinessStatus,String> implements IQueryActBusinessStatusService {

    @Autowired
    private OperateLogTool operateLogTool;

    @Autowired
    private IProcessTodoDataService processTodoDataService;

    @Autowired
    private SysDictValueService sysDictValueService;

    @Autowired
    private ISysOperateLogService operateLogService;

    private final String param1 = "/action/queryActBusinessStatus";

    /**
     * 我的待办
     * @param pageindex
     * @param pagesize
     * @param title
     * @return
     */
    @Override
    public  JsonResponse myTaskToDo(Integer pageindex, Integer pagesize, String title,String source,String currentUser) {
        if(source==null && !("PC".equals( source )||"MOBILE".equals( source ))){
            source = "PC";
        }
        /**准备操作日志参数**/
        SysOperateLog operateLog = new SysOperateLog();
        String param2 = "/myTaskToDo";
        String params = "pageindex="+pageindex.toString()+",pagesize="+pagesize.toString()+",title="+title+",source="+source+",userCode"+currentUser;
        operateLog.setInterfaceParam( params );
        Page<ActBusinessStatus> actBusinessStatuses = null;
        try{
            /**判断是否是从手机端 true是，false否**/

            JsonResponse returnObj = operateLogTool.operationSource( source, currentUser, param1, param2, operateLog);
            if ( returnObj != null ){
                return  returnObj;
            }
            Map<String,String> paramMap = Maps.newHashMap();
            //需要去调应用的字典值
            SysDictValue sysDictValue = new SysDictValue(  );
            sysDictValue.setDictType( Constants.DICT_VALUE_PROCESS_TYPE );
            List<SysDictValue> sysDictValueList = sysDictValueService.findDictValue(sysDictValue);
            StringBuffer pnInsType = new StringBuffer(  );
            int num = 0;
            for(SysDictValue sysDictValueNew:sysDictValueList){
                if(num < sysDictValueList.size()-1){
                    pnInsType.append( sysDictValueNew.getValue() ).append(",");
                }else{
                    pnInsType.append( sysDictValueNew.getValue() );
                }
            }
            paramMap.put( "pmInsType", pnInsType.toString());
            paramMap.put("participant",SecurityUtils.getCurrentUserName());
            paramMap.put("title",title);
            Pageable page = this.getPageablePrivate(pageindex,pagesize,null,null);
            actBusinessStatuses = (Page<ActBusinessStatus>)processTodoDataService.getTodoByUserNamePage( paramMap, page);
        }catch (Exception e ){
            operateLog.setErrorMsg( e.toString() );
            Exceptions.printException( e );
            return JsonResponse.success( actBusinessStatuses,"获取待办失败！" );
        }finally {
            operateLogService.saveLog( operateLog );
            return JsonResponse.success( actBusinessStatuses,"获取待办成功！" );
        }

    }

    /**
     * 我的已办
     * @param pageindex
     * @param pagesize
     * @param title
     * @return
     */
    @Override
    public JsonResponse queryMyJoin(Integer pageindex, Integer pagesize, String title,String source,String currentUser) {
        if(source==null && !("PC".equals( source )||"MOBILE".equals( source ))){
            source = "PC";
        }
        /**准备操作数据**/
        SysOperateLog operateLog = new SysOperateLog();
        String param2 = "/queryMyJoin";
        String params = "pageindex="+pageindex.toString()+",pagesize="+pagesize.toString()+",title="+title+",source="+source+",userCode"+currentUser;
        operateLog.setInterfaceParam( params );
        Map<String,String> paramMap = Maps.newHashMap();
        Page<ActBusinessStatus> actBusinessStatuses = null;
        try{
            /**判断是否是从手机端 true是，false否**/
            JsonResponse returnObj = operateLogTool.operationSource( source, currentUser, param1, param2, operateLog);
            if ( returnObj != null ){
                return returnObj;
            }
            paramMap.put("pmInsType", "A,B,C,D,E,F,G,H");
            paramMap.put("assistant",SecurityUtils.getCurrentUserName());
            paramMap.put("title",title);
            Pageable page = this.getPageablePrivate(pageindex,pagesize,null,null);
            actBusinessStatuses = (Page<ActBusinessStatus>)processTodoDataService.getAreadyDoneByUserIdSubFlowPage( paramMap,page);
            List<ActBusinessStatus> statuses = actBusinessStatuses.getContent();
            for (ActBusinessStatus status : statuses) {
                if ("hnjjwz.abolish".equals(status.getActivityDefId())) {
                    status.setCurrentState(8);
//                    status.setActivityInstName("废除归档");
                }
            }
        }catch ( Exception e ){
            operateLog.setErrorMsg( e.toString() );
            Exceptions.printException( e );
            return JsonResponse.success( actBusinessStatuses,"获取待办失败！" );
        }finally {
            operateLogService.saveLog( operateLog );
            return JsonResponse.success( actBusinessStatuses,"获取已办成功！" );
        }
    }

    private Pageable getPageablePrivate(int page, int size, String direction, String properties) {
        int pagePage = page < 1 ? 0 : page - 1;
        int pageSize = size < 1 ? 1 : (size > 100 ? 100 : size);
        PageRequest pageable;
        if (StringUtils.isNotEmpty(direction) && StringUtils.isNotEmpty(properties)) {
            Sort.Direction sortDirection;
            try {
                direction = direction.toUpperCase();
                sortDirection = Sort.Direction.valueOf(direction);
            } catch (IllegalArgumentException var11) {
                var11.printStackTrace();
                sortDirection = Sort.Direction.ASC;
            }

            String[] sortProperties = properties.split(",");
            Sort sort =  Sort.by(sortDirection, sortProperties);
            pageable = PageRequest.of(pagePage, pageSize, sort);
        } else {
            pageable = PageRequest.of(pagePage, pageSize);
        }

        return pageable;
    }

    /**
     * 我的待阅
     *
     * @param pageindex 页码
     * @param pagesize  数量
     * @param title     标题
     * @param source    是否是从手机端调用
     * @param userCode  OA账号
     * @return
     */
    @Override
    public JsonResponse queryMyPending(Integer pageindex, Integer pagesize, String title, String pmInsType, String source, String userCode) {
        Page<Map<String, Object>> actBusinessStatuses = null;
        Map<String, String> paramMap = Maps.newHashMap();
        /**准备操作数据**/
        SysOperateLog operateLog = new SysOperateLog();
        String param2 = "/queryMyPending";
        String params = "pageindex=" + pageindex.toString() + ",pagesize=" + pagesize.toString() + ",title=" + title + ",source=" + source + ",userCode=" + userCode;
        operateLog.setInterfaceParam(params);
        try {
            Pageable page = getPageable(pageindex, pagesize, null, null);
            /**判断是否是从手机端还是PC端**/
            JsonResponse returnObj = operateLogTool.operationSource(source, userCode, param1, param2, operateLog);
            if (returnObj != null) {
                return returnObj;
            }
            /**查询我的待阅**/
            paramMap.put("title", title);
            if (StringUtils.isNotEmpty(pmInsType)) {
                paramMap.put("pmInsType", pmInsType);
            } else {
                // 获取流程类型
                //StringBuffer processTypes = queryDictValueService.getProcessTypes(source, appFlag);
                //paramMap.put("pmInsType", processTypes.toString());
                paramMap.put("pmInsType", "A,B,C,D,E,F,G,H");
            }
            paramMap.put("recipient", SecurityUtils.getCurrentUserName());
            actBusinessStatuses = processTodoDataService.getMyTodoReadByUserNamePageMap(paramMap, page);
        } catch (Exception e) {
            operateLog.setErrorMsg(e.toString());
            Exceptions.printException(e);
        } finally {
            operateLogService.saveLog(operateLog);
        }
        return JsonResponse.success(actBusinessStatuses);
    }

    /**
     * 我的已阅
     *
     * @param pageindex 页码
     * @param pagesize  数量
     * @param title     标题
     * @param source    是否是从手机端调用
     * @param userCode  OA账号
     * @return
     */
    @Override
    public JsonResponse queryMyRead(Integer pageindex, Integer pagesize, String title, String pmInsType, String source, String userCode) {
        Page<Map<String, Object>> actBusinessStatuses = null;
        Map<String, String> paramMap = Maps.newHashMap();

        /**准备操作数据**/
        SysOperateLog operateLog = new SysOperateLog();
        String param2 = "/queryMyRead";
        String params = "pageindex=" + pageindex.toString() + ",pagesize=" + pagesize.toString() + ",title=" + title + ",source=" + source + ",userCode=" + userCode;
        operateLog.setInterfaceParam(params);
        try {
            Pageable page = getPageable(pageindex, pagesize, null, null);
            /**判断是否是从手机端还是PC端**/
            JsonResponse returnObj = operateLogTool.operationSource(source, userCode, param1, param2, operateLog);
            if (returnObj != null) {
                return returnObj;
            }
            /**查询我的已阅**/
            paramMap.put("title", title);
            if (StringUtils.isNotEmpty(pmInsType)) {
                paramMap.put("pmInsType", pmInsType);
            } else {
                // 获取流程类型
                //StringBuffer processTypes = queryDictValueService.getProcessTypes(source, appFlag);
                //paramMap.put("pmInsType", processTypes.toString());
                paramMap.put("pmInsType", "A,B,C,D,E,F,G,H");
            }
            paramMap.put("recipient", SecurityUtils.getCurrentUserName());
            actBusinessStatuses = processTodoDataService.getMyAreadyReadByUserNamePageMap(paramMap, page);
        } catch (Exception e) {
            Exceptions.printException(e);
            operateLog.setErrorMsg(e.toString());
        } finally {
            operateLogService.saveLog(operateLog);
        }
        return JsonResponse.success(actBusinessStatuses);
    }

    /**
     * 查询草稿
     *
     * @param pageindex 页码
     * @param pagesize  数量
     * @param title     标题
     * @param source    来源
     * @param userCode  OA账号
     * @return
     */
    @Override
    public JsonResponse myDraftToDo(Integer pageindex, Integer pagesize, String title, String source, String userCode) {
        Map<String, String> paramMap = Maps.newHashMap();
        Page<Map<String, Object>> draftTodo = null;
        /**准备操作日志参数**/
        SysOperateLog operateLog = new SysOperateLog();
        String param2 = "/myDraftToDo";
        String params = "pageindex=" + pageindex.toString() + ",pagesize=" + pagesize.toString() + ",title=" + title + ",source=" + source + ",userCode" + userCode;
        operateLog.setInterfaceParam(params);
        try {
            /**判断是否是从手机端 true是，false否**/
            JsonResponse returnObj = operateLogTool.operationSource(source, userCode, param1, param2, operateLog);
            if (returnObj != null) {
                return returnObj;
            }
            /**查询草稿**/
            if (!org.apache.commons.lang.StringUtils.isEmpty(title)) {
                paramMap.put("title", title);
            }
            paramMap.put("createUser", SecurityUtils.getCurrentUserName());
            paramMap.put("pmInsType", "A,B,C,D,E,F,G,H");
            Pageable page = getPageable(pageindex, pagesize, null, null);
            draftTodo = processTodoDataService.getMyApplyPage(paramMap, page);
            List<Map<String, Object>> content = draftTodo.getContent();// TODO 上面查询草稿接口未过滤已删除数据，暂时这样处理
            for (Map<String, Object> map : content) {
                if (map.get("ENABLED").equals("0")) {
                    //removeList.add(map);
                    content.remove(map);
                }
            }
        } catch (Exception e) {
            operateLog.setErrorMsg(e.toString());
        } finally {
            /**操作日志记录**/
            operateLogService.saveLog(operateLog);
        }
        return JsonResponse.success(draftTodo);
    }

}
