var userAgent = navigator.userAgent; //取得浏览器的userAgent字符串
var isOpera = userAgent.indexOf("Opera") > -1;
var gps=getQueryString();
var module=[
    {"index":3,"className":"work","template":"<li><a target='_blank' href='listDetails.html?id={{id}}&pmInsId={{pmInsId}}'><b>·</b>{{title}}<font>({{creationTime}})</font></a></li>"},//[{{belongDepartmentName}}]
    {"index":4,"className":"cheap","template":"<li><a target='_blank' href='listDetails.html?id={{id}}&pmInsId={{pmInsId}}'><b>◆</b>{{title}}</a><font>({{creationTime}})</font></li>"},
    // {"index":9,"className":"reviewDL","template":"<li><a target='_blank' href='listDetails.html?id={{id}}&pmInsId={{pmInsId}}'><b>■</b>{{title}}</a><font>({{creationTime}})</font></li>"},
    {"index":10,"className":"reviewDR","template":"<li><a target='_blank' href='listDetails.html?id={{id}}&pmInsId={{pmInsId}}'><b>■</b>{{title}}</a></li>"},
    {"index":11,"className":"reviewDB","htmlClass":"ul.listRB","template":"<li><a target='_blank' href='list.html?id={{programaCode}}'><img src='/"+web.appCode+"{{downLoadUrl}}'/><p>{{programaName}}</p></a></li>"},
    // {"index":12,"className":"company","template":"<li><a target='_blank' href='listDetails.html?id={{id}}&pmInsId={{pmInsId}}'><b>◎</b>{{title}}<font>({{creationTime}})</font></a></li>"},//[{{belongDepartmentName}}]
    // {"index":13,"className":"jjinfo","template":"<li><a target='_blank' href='listDetails.html?id={{id}}&pmInsId={{pmInsId}}'><b>◎</b>{{title}}<font>({{creationTime}})</font></a></li>"},//[{{belongDepartmentName}}]
    // {"index":14,"className":"dlinfo","template":"<li><a target='_blank' href='listDetails.html?id={{id}}&pmInsId={{pmInsId}}'><b>◎</b>{{title}}<font>({{creationTime}})</font></a></li>"},//[{{belongDepartmentName}}]
    {"index":15,"className":"link","htmlClass":"ul.linkU","template":"<li><a target='_blank' href='{{spare1}}'><img src='/"+web.appCode+"{{downLoadUrl}}'/></a></li>"}
];
function webSite(type){

    if (type && type == "index") {
        if(undefined == gps.loginuser){
            indexAjax(type);
        }else {
            ajaxgeneral({
                //url: "action/templateLayout/simulatedLanding/sso?appcode=" + gps.appcode + "&loginuser=" + gps.loginuser,
                url: "getCurrentUser/sso?appcode=" + gps.appcode + "&loginuser=" + gps.loginuser,
                async: false,
                success: function (ress) {
                    indexAjax(type);

                }
            });


        }
    }else{
        indexAjax(type);
    }

    getCurrent();
    // 通过sso进入页面的手动记录日志
    ajaxgeneral({
        url: "action/templateLayout/recordSuccessLogin",
        data:{"username":web.currentUser.username,"truename":web.currentUser.truename,"belongOrgName":web.currentUser.belongOrgName},
        contentType:"application/json; charset=utf-8",
        success: function (ress) {
            indexAjax(type);
        }
    });

};
function indexAjax(type){

    ajaxgeneral({
        url:"action/templateLayout/constructTemplateLayout",
        async:false,
        success:function(res) {
            //menu
            var navH = [];
            var menuData = res.data[0].templateData;
            var menuDataJson=JSON.stringify(menuData);
            //sessionStorage.setItem("menuDataJson",menuDataJson);
            for (var i in menuData) {
                var currentMenu = false;
                if (!gps.id && i == 0) currentMenu = true;
                if (gps.id && gps.id == menuData[i].id) currentMenu = true;
                if(menuData[i].menuName=="廉洁活页夹"&&userAgent.indexOf("compatible") > -1 && userAgent.indexOf("MSIE") > -1 && !isOpera){
                    navH.push("<li class='"+ (menuData[i].menuName=='嵌入式防控监督' ? "li_qr" : "") + (currentMenu ? " li_hover" : "") + (i == menuData.length - 1 ? " nobor" : "") + "'><a onclick='hyjClick(this)'" + (menuData[i].menuChildren.length>0 ? "":(" id='" + menuData[i].id + " href='javascript:void(0);'")) + ">" + menuData[i].menuName + "</a>");
                }else {
                    navH.push("<li class='"+ (menuData[i].menuName=='嵌入式防控监督' ? "li_qr" : "") + (currentMenu ? " li_hover" : "") + (i == menuData.length - 1 ? " nobor" : "") + "'><a " + (menuData[i].menuChildren.length>0 ? "":(" id='" + menuData[i].id + "' target='_blank' href='" + menuData[i].menuUrl + "?id="+menuData[i].id+"'")) + ">" + menuData[i].menuName + "</a>");
                }
                var menuChidren = menuData[i].menuChildren;
                if (menuChidren.length > 0) {
                    for(var j in menuChidren){
                        var urlO={"id":menuChidren[j].id};
                        if(menuChidren[j].pmInsId) urlO.pmInsId=menuChidren[j].pmInsId;
                        menuChidren[j].submenuUrl=tourl(menuChidren[j].submenuUrl,urlO);
                    }
                    navH.push("<ul>"+fastrender(menuChidren,"<li><a target='_blank' id='{{id}}'  href='{{submenuUrl}}'>{{submenuName}}</a></li>")+"</ul>");
                }
                navH.push("</li>");
            }
            $(".nav ul").html(navH.join(""));
            if (type && type == "index") {
                //notice
                if(res.data[1].templateData.length>0) {
                    $(".notice a").attr("href","list.html"+"?id=999");
                    $(".notice dl dt").html(fastrenderRow(res.data[1].templateData[0], "<a target='_blank' href='listDetails.html?id={{id}}&pmInsId={{pmInsId}}&notice=1&programaDataRelation={{programaDataRelation}}'>{{announcementTitle}}</a>"));
                    var noH=[];
                    for(var i=1;i<res.data[1].templateData.length;i++){
                        noH.push(fastrenderRow(res.data[1].templateData[i], "<a target='_blank' href='listDetails.html?id={{id}}&pmInsId={{pmInsId}}&notice=1&programaDataRelation={{programaDataRelation}}'>{{announcementTitle}}</a>"));
                        if((i+1)<res.data[1].templateData.length) noH.push("<span></span>");
                    }
                    $(".notice dl dd").html(noH.join(""));
                    //$(".notice marquee").html(fastrender(res.data[1].templateData, "<a target='_blank' style='margin-right:26px;' href='listDetails.html?id={{id}}&pmInsId={{pmInsId}}&notice=1&programaDataRelation={{programaDataRelation}}'>·{{announcementTitle}}</a>"));
                }
                //focus
                var focusD = res.data[2].templateData;
                for (var i in focusD) {
                    //focusD[i].downLoadUrl = focusD[i].slideShowFile[0] ? ("/" + web.appCode + (focusD[i].slideShowFile[0].downLoadUrl).replace("download","open")) : '';
                    focusD[i].downLoadUrl = focusD[i].slideShowFile && focusD[i].slideShowFile[0] ? focusD[i].slideShowFile[0].mobileFilePath : '';
                }
                $(".focus .cfocus ul").html(fastrender(focusD, "<li class='item'><a target='_blank' href='listDetails.html?id={{id}}&pmInsId={{pmInsId}}&focus=1'><img height='315' src='{{downLoadUrl}}'/><p class='txtc'>{{programaDataTtitle}}</p></a></li>"));
                initCfocus($("#indexCfocus"));
                //图片模块
                var imgH = [];
                for (var i = 5; i < 9; i++) {
                    //console.log(res.data[i].pointUrl);
                    var pointUrl;
                    if(res.data[i].pointUrl.indexOf("/")!=-1){
                        pointUrl=res.data[i].pointUrl.split("/");
                    }else {
                        pointUrl="index";
                    }
                    imgH.push("<a class='" + (i % 2 == 0 ? "a_even" : "") + "' target='_blank' href='"+(pointUrl[0] || "list.html")+"?id=" + (res.data[i].locationName=="他山之石"?res.data[i].locationId:res.data[i].id) + "'><img src='/" + web.appCode + res.data[i].templateUrl + "'/></a>");//<font>" + res.data[i].locationName + "</font>
                }
                $(".imgMod").html(imgH.join(""));
                //渲染模块
                for (var i in module) {
                    var dataD = res.data[module[i].index];
                    //console.log(dataD.pointUrl);
                    var indexPointUrl=dataD.pointUrl.split("/");
                    $("." + module[i].className + " .modTit").html("<strong class='fl'>"+dataD.locationName+"</strong><a target='_blank' href='"+(indexPointUrl[0] ||"list.html")+"?id=" + dataD.locationId + "' class='fr'>+</a>");//
                    if (dataD.templateData.length) {
                        for (var j in dataD.templateData) {
                            dataD.templateData[j].creationTime = getdateformat(dataD.templateData[j].creationTime, "yyyy-MM-dd");
                            if(dataD.templateData[j].programaCoverFile) dataD.templateData[j].downLoadUrl=dataD.templateData[j].programaCoverFile[0].downLoadUrl;
                        }
                        $("." + module[i].className + " " + (module[i].htmlClass || "ul.list")).html(fastrender(dataD.templateData, module[i].template));
                    } else {
                        moduleF(dataD.templateUrl, module[i]);
                    }
                }

                var totalW = $("#noticeTitle").width()+$("#noticeSubTitle").width();
                $("#noticeDl").width(totalW+100);
            }
            //右下角
            for(var i=0;i<$(".tab_three_tabs span").length;i++){
                $(".tab_three_tabs span").eq(i).mouseenter(function () {
                    var i=$(this).parent().index()
                    $(this).parent().siblings().find("span").removeClass("current")
                    $(this).addClass("current")
                    if(i < 2){
                        $(".tab_three_content .img").hide().siblings("ul.list").show();
                        $(".tab_three_content").find("ul").empty()
                        for(var j=0;j<3;j++) {
                            if (res.data[16 + i].templateData[j] != undefined) {
                                $(".tab_three_content").find("ul").append(fastrenderRow(res.data[16 + i].templateData[j],
                                    '<li style="width:440px;margin-top: 10px;"><a target="_blank" href="listDetails.html?id={{id}}&pmInsId={{pmInsId}}" style="width: 80%;text-decoration: none"><b>◎</b>{{title}}<font></font></a>'))
                                $(".tab_three_content").find("font").eq(j).text("(" + res.data[16 + i].templateData[j].modifiedTime.substr(0, 10) + ")")
                                if(i==2){
                                    $(".tab_three_content").find("ul").empty()
                                    $(".tab_three_content").find("ul").append(fastrenderRow(res.data[16 + i],
                                        '<li style="width:440px;margin-top: 20px;"><a target="_blank" href="{{pointUrl}}" style="width: 80%;text-decoration: none">正在建设</a>'))
                                }
                            }
                        }
                    }else{
                        $(".tab_three_content .img").show().siblings("ul.list").hide();
                        //$(".tab_three_content .img a").attr("href","http://************:8088/hnjjwz/sso?appcode="+gps.appcode+"&loginuser="+gps.loginuser);
                        $(".tab_three_content .img a").attr("href","http://*************:8088/hnjjwz/index");

                    }
                })
            }
            $(".tab_three_tabs span").eq(0).trigger("mouseenter")
            //分公司动态
            for(var i=0;i<$(".tab_two_tabs span").length;i++){
                $(".tab_two_tabs span").eq(i).mouseenter(function () {
                    var i=$(this).parent().index()
                    $(this).parent().siblings().find("span").removeClass("current_act")
                    $(this).addClass("current_act")
                    $(".tab_two_content .img").hide().siblings("ul.list").show();
                    $(".tab_two_content").find("ul").empty()
                    for(var j=0;j<6;j++) {
                        if (res.data[13 + i].templateData[j] != undefined) {
                            $(".tab_two_content").find("ul").append(fastrenderRow(res.data[13 + i].templateData[j],
                                '<li style="width:440px;"><a target="_blank" href="listDetails.html?id={{id}}&pmInsId={{pmInsId}}" style="width: 80%;text-decoration: none"><b>◎</b>{{title}}<font></font></a>'))
                            $(".tab_two_content").find("font").eq(j).text("(" + res.data[13 + i].templateData[j].modifiedTime.substr(0, 10) + ")")
                        }
                    }
                })
            }
            $(".tab_two_tabs span").eq(0).trigger("mouseenter")
            //以案示警
            for(var i=0;i<$(".tab_one_tabs span").length;i++){
                $(".tab_one_tabs span").eq(i).mouseenter(function () {
                    var i=$(this).parent().index()
                    $(this).parent().siblings().find("span").removeClass("current_act")
                    $(this).addClass("current_act")
                    $(".tab_one_content .img").hide().siblings("ul.list").show();
                    $(".tab_one_content").find("ul").empty()
                    for(var j=0;j<6;j++) {
                        if (res.data[19 + i].templateData[j] != undefined) {
                            $(".tab_one_content").find("ul").append(fastrenderRow(res.data[19 + i].templateData[j],
                                '<li style="width:440px;"><a target="_blank" href="listDetails.html?id={{id}}&pmInsId={{pmInsId}}" style="width: 80%;text-decoration: none"><b>◎</b>{{title}}<font></font></a>'))
                            $(".tab_one_content").find("font").eq(j).text("(" + res.data[19 + i].templateData[j].modifiedTime.substr(0, 10) + ")")
                        }
                    }
                })
            }
            $(".tab_one_tabs span").eq(0).trigger("mouseenter")
        }
    });
};
function moduleF(templateUrl,modulei){

    ajaxgeneral({
        url: templateUrl,
        success: function (ress) {
            var data=ress.data.content || ress.data;
            for(var i in data){
                data[i].creationTime=getdateformat(data[i].creationTime,"yyyy-MM-dd");

                if(data[i].programaCoverFile) data[i].downLoadUrl=data[i].programaCoverFile[0].downLoadUrl;
                //2022年8月18日，中国移动纪检组网站链接新增?iv-user="+gps.loginuser
                if(modulei.className=="link" && data[i].programaName=="中国移动纪检组网站") data[i].spare1=data[i].spare1+"&iv-user="+gps.loginuser;
            }
            $("."+modulei.className+" "+(modulei.htmlClass || "ul.list")).html(fastrender(data,modulei.template));
        }
    });
}

//活页夹跳转判断
function hyjClick(oneself){

    getparent().mesShow("温馨提示","活页夹暂不支持IE浏览器，请复制以下链接至火狐浏览器打开"+"<br/><a  href='javascript:void(0);' onclick='copyAddress(this)'>http://*************:8088/nffcl/login</a>", 200000);
}

function copyAddress() {
    //var url = location.href;
    //window.clipboardData.setData('http://************:8088/nffcl/login',url);
    var oInput = document.createElement('input');
    oInput.value = "http://*************:8088/nffcl/login";
    document.body.appendChild(oInput);
    oInput.select(); // 选择对象
    document.execCommand("Copy"); // 执行浏览器复制命令
    oInput.className = 'oInput';
    oInput.style.display='none';
    alert('复制成功');



}

// $(function(){
//     console.log(document.body.scrollHeight);
//     if(document.body.scrollHeight > 0){
//         console.log(1);
//         $(".copy").removeClass("copyFixed");
//     }else{
//         console.log(2);
//         $("body").height($(window).height());
//         $(".copy").addClass(" copyFixed");
//     }
// })
