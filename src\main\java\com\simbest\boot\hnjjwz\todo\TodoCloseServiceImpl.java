package com.simbest.boot.hnjjwz.todo;

import com.simbest.boot.base.exception.Exceptions;
import com.simbest.boot.bps.process.bussiness.model.ActBusinessStatus;
import com.simbest.boot.bps.task.todo.TodoCloseInterface;
import com.simbest.boot.hnjjwz.constants.Constants;
import com.simbest.boot.security.SimpleApp;
import com.simbest.boot.uums.api.app.UumsSysAppApi;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;

/**
 * <strong>Title : TodoCloseServiceImpl</strong><br>
 * <strong>Description : 核销统一代办</strong><br>
 * <strong>Create on : $date$</strong><br>
 * <strong>Modify on : $date$</strong><br>
 * <strong>Copyright (C) Ltd.</strong><br>
 *
 * <AUTHOR>
 * @version <strong>V1.0.0</strong><br>
 * <strong>修改历史:</strong><br>
 * 修改人 修改日期 修改描述<br>
 * -------------------------------------------<br>
 */
@Slf4j
@Service
public class TodoCloseServiceImpl implements TodoCloseInterface {

    @Autowired
    private TodoBusOperatorService todoBusOperatorService;

    @Autowired
    private UumsSysAppApi uumsSysAppApi;

    /**
     * 核销统一代办
     * @param businessStatus     业务流程操作对象
     * @param sendUser           审批人
     */
    @Override
    public void execution ( ActBusinessStatus businessStatus, String sendUser ) {
        try {
            Boolean isTodoFlag = true;  //false代表不推送  true推送
            //Boolean isTodoFlag = false;  //false代表不推送  true推送

            SimpleApp simpleApp = uumsSysAppApi.findAppByAppCode( Constants.APP_CODE,sendUser );
            if ( simpleApp != null ){
                isTodoFlag = simpleApp.getTodoOpen();
            }
            if ( isTodoFlag ) {
                todoBusOperatorService.closeTodo( businessStatus, sendUser );
            }
        }catch ( Exception e ){
            Exceptions.printException( e );
            throw e;
        }
    }
}
