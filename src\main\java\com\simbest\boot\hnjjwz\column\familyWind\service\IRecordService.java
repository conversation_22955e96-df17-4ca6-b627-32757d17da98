package com.simbest.boot.hnjjwz.column.familyWind.service;/**
 * Created by KZH on 2019/8/5 17:42.
 */

import com.simbest.boot.base.service.ILogicService;
import com.simbest.boot.base.web.response.JsonResponse;
import com.simbest.boot.hnjjwz.column.familyWind.model.Record;

import java.util.List;

/**
 * <AUTHOR>
 * @create 2019-08-05 17:42
 * @desc
 **/
public interface IRecordService extends ILogicService<Record,String> {

    JsonResponse findRecord( String username);

    List<Record> findRecordList(String username);
}
