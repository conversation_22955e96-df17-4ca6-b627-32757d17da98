package com.simbest.boot.hnjjwz.process.wf.model;

import com.simbest.boot.base.annotations.EntityIdPrefix;
import com.simbest.boot.cmcc.wf.model.WfFormModel;
import com.simbest.boot.sys.model.SysFile;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.*;
import org.hibernate.annotations.GenericGenerator;

import javax.persistence.*;
import java.util.List;

/**
 * <AUTHOR>
 * @Data 2019/04/17
 * Description 栏目数据审批表
 */
@Data
@EqualsAndHashCode(callSuper = true)
@NoArgsConstructor
@AllArgsConstructor
@Entity(name = "us_programa_data_form")
@ApiModel(value = "栏目数据审批表")
public class ProgramaDataForm extends WfFormModel {
    @Id
    @Column(name = "id", length = 40)
    @GeneratedValue(generator = "snowFlakeId")
    @GenericGenerator (name = "snowFlakeId", strategy = "com.simbest.boot.util.distribution.id.SnowflakeId")
    @EntityIdPrefix (prefix = "UP") //主键前缀，此为可选项注解
    private String id;

    @Setter
    @Getter
    @Column(name = "pmInsId",nullable = false,length = 100)
    @ApiModelProperty(value = "业务实例id")
    private String pmInsId;

    @Setter
    @Getter
    @Column(length = 100)
    @ApiModelProperty(value = "所属栏目编码")
    private String programaCode;

    @Setter
    @Getter
    @Column(length = 100)
    @ApiModelProperty(value = "所属栏目全路径名")
    private String programaDisplayName;//所属栏目全路径名

    @Setter
    @Getter
    @Column(length = 200)
    @ApiModelProperty(value = "标题")
    private String title;

    @Setter
    @Getter
    @Column(length = 100)
    @ApiModelProperty(value = "发布时间")
    private String creationTime;//审核发布后的时间

    @Setter
    @Getter
    @Column(length = 10)
    @ApiModelProperty(value = "是否发布")
    private Boolean isPublish;//是否发布

    @Setter
    @Getter
    @Column(length = 4000)
    @ApiModelProperty(value = "摘要")
    private String digest;

    @Lob
    @Basic(fetch = FetchType.LAZY)
    @ApiModelProperty(value = "正文")
    private String mainBody;

    @Setter
    @Getter
    @Column( length = 40)
    @ApiModelProperty(value = "起草人oa账号")
    @NonNull
    private String username;

    @Setter
    @Getter
    @Column( length = 40)
    @ApiModelProperty(value = "起草人姓名")
    @NonNull
    private String truename;

    @Setter
    @Getter
    @Column( length = 240)
    @ApiModelProperty(value = "起草人公司")
    @NonNull
    private String company;

    @Setter
    @Getter
    @Column( length = 240)
    @ApiModelProperty(value = "起草人部门")
    @NonNull
    private String departmentName;

    @Setter
    @Getter
    @Column( length = 240)
    @ApiModelProperty(value = "起草人组织路径")
    @NonNull
    private String displayName;

    @Setter
    @Getter
    @Column(columnDefinition="int default 0")
    @ApiModelProperty(value = "置顶标识")
    private int stickFlag;

    @Setter
    @Getter
    @Column(columnDefinition="int default 0")
    @ApiModelProperty(value = "是否着重显示")
    private int importantFlag;

    @Setter
    @Getter
    @Column(columnDefinition="int default 0")
    @ApiModelProperty(value = "浏览次数")
    private int viewsNumber;

    @Setter
    @Getter
    @Column( length = 240)
    @ApiModelProperty(value = "文章来源")
    private String articleFrom;

    @Setter
    @Getter
    @Column( length = 240)
    @ApiModelProperty(value = "文章发布时间")
    private String articleStartTime;

    @Setter
    @Getter
    @Column( length = 240)
    @ApiModelProperty(value = "预留字段1")
    private String ireserved1;

    @Setter
    @Getter
    @Column( length = 240)
    @ApiModelProperty(value = "预留字段2")
    private String ireserved2;

    @Setter
    @Getter
    @Column(  length = 240)
    @ApiModelProperty(value = "预留字段3")
    private String ireserved3;

    @Setter
    @Getter
    @Column(length = 240)
    @ApiModelProperty(value = "预留字段4")
    private String ireserved4;

    @Setter
    @Getter
    @Column(length = 240)
    @ApiModelProperty(value = "预留字段5")
    private String ireserved5;

    @Setter
    @Getter
    @Transient
    private String pointUrl;

    @Setter
    @Getter
    @Transient
    private String iportalUrl;

    @Transient
    private List<SysFile> coverImageFileList;//存放封面图像文件信息

    @Transient
    private List<SysFile> authBookFileList;//存放授权书文件信息

    @Transient
    private List<SysFile> accessoryFileList;//存放附件的文件信息

    @Transient
    private List<SysFile> videoFileList;//存放视频的文件信息

    @Transient
    private String belongDepartmentDisplayName;//所属部门全路径名

    @Transient
    @ApiModelProperty(value = "手机端判断 true和1可以在手机端处理",required = true)
    private boolean inMobile;

    @Transient
    @ApiModelProperty(value = "富文本压缩base64手机端用")
    private String compressBase;

}
