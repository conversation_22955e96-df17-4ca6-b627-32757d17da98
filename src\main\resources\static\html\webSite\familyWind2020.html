<!DOCTYPE html>
<html xmlns="http://www.w3.org/1999/xhtml"
      xmlns:th="http://www.thymeleaf.org">
<head>
    <meta charset="UTF-8">
    <title>家风</title>
    <meta http-equiv="Content-Type" content="text/html; charset=utf-8"/>
    <meta http-equiv="X-UA-Compatible" content="IE=edge" />
    <meta name="viewport" content="width=device-width, initial-scale=1, shrink-to-fit=no"/>

    <!--    <meta http-equiv=“X-UA-Compatible” content=“IE=8″>-->
    <!--
    <link href="http://************:8088/simbestui/fonts/iconfont/iconfont.css?v=svn.revision" th:href="@{http://************:8088/simbestui/fonts/iconfont/iconfont.css?v=svn.revision}" rel="stylesheet"/>
    -->
    <link href="../../css/webSite.css?v=svn.revision" th:href="@{/css/webSite.css?v=svn.revision}" rel="stylesheet"/>
    <link href="../../fonts/iconfont/iconfont.css?v=svn.revision" th:href="@{/fonts/iconfont/iconfont.css?v=svn.revision}" rel="stylesheet"/>
    <link href="http://************:8088/simbestui/js/themes/default/easyui.css?v=svn.revision" th:href="@{http://************:8088/simbestui/js/themes/default/easyui.css?v=svn.revision}" rel="stylesheet"/>
    <link href="http://************:8088/simbestui/js/themes/icon.css?v=svn.revision" th:href="@{http://************:8088/simbestui/js/themes/icon.css?v=svn.revision}" rel="stylesheet"/>
    <link href="http://************:8088/simbestui/css/public.css?v=svn.revision" th:href="@{http://************:8088/simbestui/css/public.css?v=svn.revision}" rel="stylesheet"/>
    <link href="http://************:8088/simbestui/css/pages.css?v=svn.revision" th:href="@{http://************:8088/simbestui/css/pages.css?v=svn.revision}" rel="stylesheet"/>
    <script src="http://************:8088/simbestui/js/jquery.min.js?v=svn.revision" th:src="@{http://************:8088/simbestui/js/jquery.min.js?v=svn.revision}" type="text/javascript"></script>
    <script src="http://************:8088/simbestui/js/jquery.easyui.min.js?v=svn.revision" th:src="@{http://************:8088/simbestui/js/jquery.easyui.min.js?v=svn.revision}" type="text/javascript"></script>
    <script src="http://************:8088/simbestui/js/easyui-lang-zh_CN.js?v=svn.revision" th:src="@{http://************:8088/simbestui/js/easyui-lang-zh_CN.js?v=svn.revision}" type="text/javascript"></script>
    <script src="http://************:8088/simbestui/js/kindeditor/kindeditor.min.js?v=svn.revision" th:src="@{http://************:8088/simbestui/js/kindeditor.min.js?v=svn.revision}" type="text/javascript"></script>
    <script src="../../js/jquery.config.js?v=svn.revision" th:src="@{/js/jquery.config.js?v=svn.revision}" type="text/javascript"></script>
    <script src="http://************:8088/simbestui/js/jquery.zjsfile.js?v=svn.revision" th:src="@{http://************:8088/simbestui/js/jquery.zjsfile.js?v=svn.revision}" type="text/javascript"></script>
    <script src="http://************:8088/simbestui/js/jquery.aduq.js?v=svn.revision" th:src="@{http://************:8088/simbestui/js/jquery.aduq.js?v=svn.revision}" type="text/javascript"></script>
    <script src="http://************:8088/simbestui/js/jquery.process.js?v=svn.revision" th:src="@{http://************:8088/simbestui/js/jquery.process.js?v=svn.revision}" type="text/javascript"></script>
</head>
<style>
    body{background: #f5f5f5;text-align: center;}
    .imgtobig{position: fixed;width: 70%;height: 70%;left: 15%;top: 15%;}
    .imgtobig i{color: #909090;}
    button:focus{outline: none;border: 1px solid white;}
</style>
<body>
<div  class="head" style="box-shadow: 0px 10px 20px -20px #626262;height: 70px;width:100%;background: white;position: fixed;z-index: 88;display: none;"></div>
<div class="logo_top">
    <img src="../../images/2020_tittle.jpg" alt="" class="logo_img"/>
</div>
<div id="wrapper_body">
    <img src="../../images/introduce.jpg" alt="" class="wrapper_img">
    <div class="order_details">
        <ul class="order_details_ul">
            <li class="fortab prepaids2020 checked">廉家事</li>
            <li class="fortab prepaids20202" >廉家宝</li>
            <li class="fortab prepaids20203">廉家训</li>
        </ul>
    </div>
    <!--        <div style="float: right;margin-right: 105px;margin-top: 0px;">-->
    <!--            <h6 style="display: inline-block;color: #ba7d20">每位员工最多投五票</h6>-->
    <!--            <img src="../../images/dianzan.png" alt="点赞" style="width: 16px;height: 16px;margin-top: -6px;">-->
    <!--        </div>-->
    <div>
        <div class="tablelist showdomdiv showsss tablelist1">
        </div>
        <div class="tablelist hidedomdiv showsss tablelist2">
        </div>
        <div class="tablelist hidedomdiv showsss tablelist3" >
        </div>
    </div>
</div>
<div style="width: 100%;position: fixed;top: 0;left: 0;z-index: 100;display: none;filter: blur(5px); filter:alpha(opacity=30);background: black;opacity:0.3" class="pop-up1"></div>
<div class="result" id="outdiv">
    <div class="indiv">
        <img class="imgresult" id="bigimg" src="">
    </div>
</div>
<div class="copy" style="background: #f5f5f5;color: black;">©版权所有 中国移动通信集团河南有限公司</div>

</body>
<script type="text/javascript">
    var a = 0;
    $(document).ready(function(){
        // 改为鼠标移上的事件只需把click改为mousemove
        //$('.tablelist1').css('display','block');
        $('.tablelist2').css('display','none');
        $('.tablelist3').css('display','none');
        $(".fortab").mousemove(function(){
            var number=$(".fortab").index(this);
            $(this).addClass("checked");
            $(this).siblings().removeClass("checked");
            $(".tablelist:eq("+number+")").show();
            $(".tablelist:eq("+number+")").siblings().hide();
        });
    });
    $(function(){
        $('.newsContent').hover(function(){
            $(this).stop().animate({
                'margin-left':'-7px',
                'margin-right':'17px'
            })
        },function(){
            $(this).stop().animate({
                'margin-left':'0',
                'margin-right':'10px'
            })
        })
    });


    $(function() {
        //获取当前登录人
        var username;
        var gps=getQueryString();
        var url="action/templateLayout/constructDanColumnsLayout/sso?locationType=danColumns2020&appcode=hnjjwz&loginuser=" + gps.loginuser;
        if(gps.from=="oa"){
            ajaxgeneral({
                url: "getCurrentUser/sso?appcode=hnjjwz&loginuser=" + gps.loginuser,
                success: function (ress) {
                    username=ress.data.username;
                }
            });
        }else {
            getCurrent();
            username=web.currentUser.username;
            url="action/templateLayout/constructDanColumnsLayout?locationType=danColumns2020";
        }

        ajaxgeneral({
            url: url,
            success: function (res) {
                var data = res.data;
                // 廉家事
                if(data[0].templateData){
                    for(var j=0;j<data[0].templateData.length;j++){
                        var paintingData3 = data[0].templateData[j];
                        var voteQuantity = paintingData3.voteQuantity;
                        if(voteQuantity == null){
                            voteQuantity = 0
                        };
                        var div_list = ['<div class="newsCollect">',
                            '<div class="newsBtn"></div>',
                            '<div class="newsSpan" dataid="'+ paintingData3.id+'" dataTitle="'+ paintingData3.familyTitle+'" dataabout="'+ paintingData3.about+'"><a target="_blank" href="familyList.html?id='+paintingData3.id+'" style="color: black">'+paintingData3.familyTitle+'</a>  <a href="" style="color:#93dccf; class="jianjie"> [简介] </a></div>',
                            '<button class="newsImg newsImg2" dataid="'+ paintingData3.id+'" datatype="廉家事"' + 'isvote="'+paintingData3.vote+'"><img src="../../images/dianzan_1.png" alt="" class="iconfont ">'+voteQuantity+'</button>',
                            //'<a class="newsImg newsImg2" dataid="'+ paintingData3.id+'" datatype="廉家事"' + 'isvote="'+paintingData3.vote+'"><img src="../../images/dianzan_1.png" alt="" class="iconfont ">'+voteQuantity+'</a>',
                            '<div class="newsSelect">'+paintingData3.about+'</div>',
                            '</div>'
                        ];
                        $(".tablelist1").append(div_list.join(""));
                        if(paintingData3.vote){
                            // $('.content_bottom[dataid="'+ dataids+'"]')
                            $('.newsImg2[dataid="'+ paintingData3.id+'"]').css('color','white');
                        }else{
                            $('.newsImg2[dataid="'+ paintingData3.id+'"]').css('color','#FFE400');
                            $('.newsImg2[dataid="'+ paintingData3.id+'"]').css('font-weight','bold');
                        }
                        $('.newsImg2').css('color','#D3D3D3');
                        $($('.newsSpan[dataid="'+ paintingData3.id+'"]').children()[1]).hover(function () {
                            var title = $(this).parent().attr('dataTitle').length;
                            var about = $(this).parent().attr('dataabout').length;
                            $(this).parent().parent().find(".newsSelect").css('left',title*20 + 'px')
                            // $(this).parent().parent().find(".newsSelect").css('height',about*3 + 'px')
                            $(this).parent().parent().find(".newsSelect").css('display','block')
                        },function(){
                            $(this).parent().parent().find(".newsSelect").css('display','none')
                        })
                    }
                }
                // 廉家宝
                if(data[1].templateData){
                    for(var j=0;j<data[1].templateData.length;j++){
                        var paintingData3 = data[1].templateData[j];
                        var voteQuantity = paintingData3.voteQuantity;
                        if(voteQuantity == null){
                            voteQuantity = 0
                        };
                        var div_list = ['<div class="newsCollect">',
                            '<div class="newsBtn"></div>',
                            '<div class="newsSpan" dataid="'+ paintingData3.id+'" dataTitle="'+ paintingData3.familyTitle+'" dataabout="'+ paintingData3.about+'"><a target="_blank" href="familyList.html?id='+paintingData3.id+'" style="color: black">'+paintingData3.familyTitle+'</a>  <a href="" style="color:#93dccf; class="jianjie"> [简介] </a></div>',
                            '<button class="newsImg newsImg2" dataid="'+ paintingData3.id+'" datatype="廉家宝"' + 'isvote="'+paintingData3.vote+'"><img src="../../images/dianzan_1.png" alt="" class="iconfont ">'+voteQuantity+'</button>',
                            '<div class="newsSelect">'+paintingData3.about+'</div>',
                            '</div>'
                        ];
                        $(".tablelist2").append(div_list.join(""));
                        if(paintingData3.vote){
                            // $('.content_bottom[dataid="'+ dataids+'"]')
                            $('.newsImg2[dataid="'+ paintingData3.id+'"]').css('color','white');
                        }else{
                            $('.newsImg2[dataid="'+ paintingData3.id+'"]').css('color','#FFE400');
                            $('.newsImg2[dataid="'+ paintingData3.id+'"]').css('font-weight','bold');
                        }
                        $('.newsImg2').css('color','#D3D3D3');
                        $($('.newsSpan[dataid="'+ paintingData3.id+'"]').children()[1]).hover(function () {
                            var title = $(this).parent().attr('dataTitle').length;
                            var about = $(this).parent().attr('dataabout').length;
                            $(this).parent().parent().find(".newsSelect").css('left',title*20 + 'px')
                            // $(this).parent().parent().find(".newsSelect").css('height',about*3 + 'px')
                            $(this).parent().parent().find(".newsSelect").css('display','block')
                        },function(){
                            $(this).parent().parent().find(".newsSelect").css('display','none')
                        })
                    }
                }


                // 廉家训
                if(data[2].templateData){
                    for(var j=0;j<data[2].templateData.length;j++){
                        var paintingData3 = data[2].templateData[j];
                        var voteQuantity = paintingData3.voteQuantity;
                        if(voteQuantity == null){
                            voteQuantity = 0
                        };
                        var div_list = ['<div class="newsCollect">',
                            '<div class="newsBtn"></div>',
                            '<div class="newsSpan" dataid="'+ paintingData3.id+'" dataTitle="'+ paintingData3.familyTitle+'" dataabout="'+ paintingData3.about+'"><a target="_blank" href="familyList.html?id='+paintingData3.id+'" style="color: black">'+paintingData3.familyTitle+'</a>  <a href="" style="color:#93dccf; class="jianjie"> [简介] </a></div>',
                            '<button class="newsImg newsImg2" dataid="'+ paintingData3.id+'" datatype="廉家训"' + 'isvote="'+paintingData3.vote+'"><img src="../../images/dianzan_1.png" alt="" class="iconfont " >'+voteQuantity+'</button>',
                            '<div class="newsSelect">'+paintingData3.about+'</div>',
                            '</div>'
                        ];
                        $(".tablelist3").append(div_list.join(""));
                        if(paintingData3.vote){
                            // $('.content_bottom[dataid="'+ dataids+'"]')
                            $('.newsImg2[dataid="'+ paintingData3.id+'"]').css('color','white');
                        }else{
                            $('.newsImg2[dataid="'+ paintingData3.id+'"]').css('color','#FFE400');
                            $('.newsImg2[dataid="'+ paintingData3.id+'"]').css('font-weight','bold');
                        }
                        $('.newsImg2').css('color','#D3D3D3');
                        $($('.newsSpan[dataid="'+ paintingData3.id+'"]').children()[1]).hover(function () {
                            var title = $(this).parent().attr('dataTitle').length;
                            var about = $(this).parent().attr('dataabout').length;
                            $(this).parent().parent().find(".newsSelect").css('left',title*20 + 'px')
                            // $(this).parent().parent().find(".newsSelect").css('height',about*3 + 'px')
                            $(this).parent().parent().find(".newsSelect").css('display','block')
                        },function(){
                            $(this).parent().parent().find(".newsSelect").css('display','none')
                        })
                    }
                }
                $('.newsImg2').click(function (event) {
                    if(a == 0){
                        alert('投票活动已截止,谢谢！');
                    }else{
                        var isvote = $(this).attr('isvote');
                        var dataids = $(this).attr('dataid');
                        if(isvote == "false"){
                            alert('请勿重复投票,谢谢！');
                            return false;
                        }else{
                            var dataid = $(this).attr('dataid');
                            var dataType = $(this).attr('dataType');
                            var poll = parseInt($(this).text()) + 1;
                            //获取当前登录人
                            getCurrent();
                            var username=web.currentUser.username;
                            ajaxgeneral({
                                url: "action/recordPerson/vote",
                                contentType: "application/json;charset=UTF-8",
                                data: {"Id": dataid, "userName": username, "type": dataType},
                                success: function (res) {
                                    if(res.data ==  false){
                                        alert('此类作品您的投票次数已超上限(五票)！');
                                        return false;
                                    }else{
                                        alert('投票成功');
                                        $('.newsImg2[dataid="'+ dataids+'"]').html('<i class="iconfont icon-zan"></i>'+'&nbsp;'+ poll);
                                        $('.newsImg2[dataid="'+ dataids+'"]').css('color','#FFE400');
                                        $('.newsImg2[dataid="'+ dataids+'"]').css('font-weight','bold');
                                        $('.newsImg2[dataid="'+ dataids+'"]').attr('isvote','false');
                                    }
                                }
                            });
                        }

                    }

                });
                // $('.pop-up_img').click(function(){
                //     // var thiselement=$(this);
                //     tobigimg($(this)[0]);
                //     // showImg("#outdiv",".indiv","#bigimg",thiselement);
                // })
            }
        });
    });
    //固定四个选项在顶部
    $(function(){
        window.onscroll = function (ev) {
            var scrollTop = document.documentElement.scrollTop || document.body.scrollTop;
            if(scrollTop > 600){
                $(".order_details").addClass('silde');
                $(".head").css('display','block');
            }else{
                $(".order_details").removeClass('silde');
                $(".head").css('display','none');
            }
        }
    });
    $(function () {
        $('.pop-up1').css('height',$(window).height());
        $('.guanbi').on('click',function () {
            $('.pop-up1').css('display','none');
            $('.pop-up').css('display','none');
            // $("body").css("overflow-y","auto");
        });
    });
    //兼容ie浏览器
    $(function isIE() { //ie?
        if (!!window.ActiveXObject || "ActiveXObject" in window){
            $(" .pop-up_img,.pop_up_title,.pop_up_text,.pop_up_user,.newsImg1,.img_jianjie").css('position','absolute')
            $('.pop-up_img').css('left','5%');
            $('.pop-up_img').css('top','10%');
            $('.pop_up_title').css('top','18%');
            $('.pop_up_title').css('left','60%');
            $('.img_jianjie').css('top','11%');
            $('.img_jianjie').css('left','55%');
            $('.pop_up_user').css('right','3%');
            $('.pop_up_user').css('top','72%');
            $('.newsImg1').css('top','83%');
            $('.newsImg1').css('left','41%');
            $('.pop_up_text').css('left','60%');
            $('.pop_up_text').css('top','26%');


        }
    });

</script>
</html>