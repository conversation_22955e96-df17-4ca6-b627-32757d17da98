<?xml version="1.0" encoding="UTF-8"?>
<configuration>
    <!--
        用途：日志文件配置
        作者：lishuyi
    -->

    <!--
       说明：
       1、日志级别及文件
           日志记录采用分级记录，级别与日志文件名相对应，不同级别的日志信息记录到不同的日志文件中
           例如：error级别记录到log_error.log（该文件为当前记录的日志文件），而log_error_xxx.log为归档日志，
           日志文件按日期记录，同一天内，若日志文件大小等于或大于10MB，则按0、1、2...顺序分别命名
           例如log-level-2013-12-21.0.log
           其它级别的日志也是如此。
       2、Appender
           FILEERROR对应error级别，文件名以log-error-xxx.log形式命名
           FILEWARN对应warn级别，文件名以log-warn-xxx.log形式命名
           FILEINFO对应info级别，文件名以log-info-xxx.log形式命名
           FILEDEBUG对应debug级别，文件名以log-debug-xxx.log形式命名
           stdout将日志信息输出到控制上，为方便开发测试使用
    -->
    <!--
        说明：
            1. 文件的命名和加载顺序有关
               logback.xml早于application.yml加载，logback-spring.xml晚于application.yml加载
               如果logback配置需要使用application.yml中的属性，需要命名为logback-spring.xml
            2. logback使用application.yml中的属性
               使用springProperty才可使用application.yml中的值 可以设置默认值

        -->

    <!--application.yml 传递参数，不能使用logback 自带的<property>标签 -->
    <springProperty scope="context" name="groupId" source="logback.groupId"/>
    <springProperty scope="context" name="artifactId" source="logback.artifactId"/>
    <springProperty scope="context" name="port" source="server.port"/>
    <!--设置日志打印上下文-->
    <contextName>${artifactId}</contextName>
    <!--设置系统日志目录-->
    <property name="LOG_PATH" value="./boot_app_logs" />
    <property name="APP_DIR" value="${artifactId}" />
    <!--
    <property name="LOG_PATTERN" value="%contextName %d{yyyy-MM-dd HH:mm:ss.SSS} [%thread] %-5level %logger Line:%-3L - %msg%n" />
    -->
    <property name="LOG_PATTERN" value="%d{yyyy-MM-dd HH:mm:ss.SSS} [%thread] %-5level %logger.%M Line:%line - %msg%n" />
    <property name="CHARSET" value="UTF-8" />

    <!-- 不带彩色的日志在控制台输出时候的设置 -->
    <appender name="CONSOLE" class="ch.qos.logback.core.ConsoleAppender">
        <encoder>
            <!-- 格式化输出：%d表示日期，%thread表示线程名，%-5level：级别从左显示5个字符宽度%msg：日志消息，%n是换行符-->
            <pattern>${LOG_PATTERN}</pattern>
        </encoder>
    </appender>


    <!-- 日志记录器，日期滚动记录 -->
    <appender name="FILEERROR" class="ch.qos.logback.core.rolling.RollingFileAppender">
        <!-- 正在记录的日志文件的路径及文件名 -->
        <file>${LOG_PATH}/${APP_DIR}/log_error-${port}.log</file>
        <!-- 日志记录器的滚动策略，按日期，按大小记录 -->
        <rollingPolicy class="ch.qos.logback.core.rolling.TimeBasedRollingPolicy">
            <!-- 归档的日志文件的路径，例如今天是2013-12-21日志，当前写的日志文件路径为file节点指定，可以将此文件与file指定文件路径设置为不同路径，从而将当前日志文件或归档日志文件置不同的目录。
            而2013-12-21的日志文件在由fileNamePattern指定。%d{yyyy-MM-dd}指定日期格式，%i指定索引 -->
            <fileNamePattern>${LOG_PATH}/${APP_DIR}/error/log-error-${port}-%d{yyyy-MM-dd}.%i.log</fileNamePattern>
            <!-- 除按日志记录之外，还配置了日志文件不能超过10MB，若超过10MB，日志文件会以索引0开始，
            命名日志文件，例如log-error-2013-12-21.0.log -->
            <timeBasedFileNamingAndTriggeringPolicy class="ch.qos.logback.core.rolling.SizeAndTimeBasedFNATP">
                <maxFileSize>10MB</maxFileSize>
            </timeBasedFileNamingAndTriggeringPolicy>
        </rollingPolicy>
        <!-- 追加方式记录日志 -->
        <append>true</append>
        <!-- 日志文件的格式 -->
        <encoder class="ch.qos.logback.classic.encoder.PatternLayoutEncoder">
            <pattern>${LOG_PATTERN}</pattern>
            <charset>${CHARSET}</charset>
        </encoder>
        <!-- 此日志文件只记录error级别的 -->
        <filter class="ch.qos.logback.classic.filter.LevelFilter">
            <level>error</level>
            <onMatch>ACCEPT</onMatch>
            <onMismatch>DENY</onMismatch>
        </filter>
    </appender>

    <!-- 日志记录器，日期滚动记录 -->
    <appender name="FILEWARN" class="ch.qos.logback.core.rolling.RollingFileAppender">
        <!-- 正在记录的日志文件的路径及文件名 -->
        <file>${LOG_PATH}/${APP_DIR}/log_warn-${port}.log</file>
        <!-- 日志记录器的滚动策略，按日期，按大小记录 -->
        <rollingPolicy class="ch.qos.logback.core.rolling.TimeBasedRollingPolicy">
            <!-- 归档的日志文件的路径，例如今天是2013-12-21日志，当前写的日志文件路径为file节点指定，可以将此文件与file指定文件路径设置为不同路径，从而将当前日志文件或归档日志文件置不同的目录。
            而2013-12-21的日志文件在由fileNamePattern指定。%d{yyyy-MM-dd}指定日期格式，%i指定索引 -->
            <fileNamePattern>${LOG_PATH}/${APP_DIR}/warn/log-warn-${port}-%d{yyyy-MM-dd}.%i.log</fileNamePattern>
            <!-- 除按日志记录之外，还配置了日志文件不能超过10MB，若超过10MB，日志文件会以索引0开始，
            命名日志文件，例如log-error-2013-12-21.0.log -->
            <timeBasedFileNamingAndTriggeringPolicy class="ch.qos.logback.core.rolling.SizeAndTimeBasedFNATP">
                <maxFileSize>10MB</maxFileSize>
            </timeBasedFileNamingAndTriggeringPolicy>
        </rollingPolicy>
        <!-- 追加方式记录日志 -->
        <append>true</append>
        <!-- 日志文件的格式 -->
        <encoder class="ch.qos.logback.classic.encoder.PatternLayoutEncoder">
            <pattern>${LOG_PATTERN}</pattern>
            <charset>${CHARSET}</charset>
        </encoder>
        <!-- 此日志文件只记录warn级别的 -->
        <filter class="ch.qos.logback.classic.filter.LevelFilter">
            <level>warn</level>
            <onMatch>ACCEPT</onMatch>
            <onMismatch>DENY</onMismatch>
        </filter>
    </appender>

    <!-- 日志记录器，日期滚动记录 -->
    <appender name="FILEINFO" class="ch.qos.logback.core.rolling.RollingFileAppender">
        <!-- 正在记录的日志文件的路径及文件名 -->
        <file>${LOG_PATH}/${APP_DIR}/log_info-${port}.log</file>
        <!-- 日志记录器的滚动策略，按日期，按大小记录 -->
        <rollingPolicy class="ch.qos.logback.core.rolling.TimeBasedRollingPolicy">
            <!-- 归档的日志文件的路径，例如今天是2013-12-21日志，当前写的日志文件路径为file节点指定，可以将此文件与file指定文件路径设置为不同路径，从而将当前日志文件或归档日志文件置不同的目录。
            而2013-12-21的日志文件在由fileNamePattern指定。%d{yyyy-MM-dd}指定日期格式，%i指定索引 -->
            <fileNamePattern>${LOG_PATH}/${APP_DIR}/info/log-info-${port}-%d{yyyy-MM-dd}.%i.log</fileNamePattern>
            <!-- 除按日志记录之外，还配置了日志文件不能超过10MB，若超过10MB，日志文件会以索引0开始，
            命名日志文件，例如log-error-2013-12-21.0.log -->
            <timeBasedFileNamingAndTriggeringPolicy class="ch.qos.logback.core.rolling.SizeAndTimeBasedFNATP">
                <maxFileSize>10MB</maxFileSize>
            </timeBasedFileNamingAndTriggeringPolicy>
        </rollingPolicy>
        <!-- 追加方式记录日志 -->
        <append>true</append>
        <!-- 日志文件的格式 -->
        <encoder class="ch.qos.logback.classic.encoder.PatternLayoutEncoder">
            <pattern>${LOG_PATTERN}</pattern>
            <charset>${CHARSET}</charset>
        </encoder>
        <!-- 此日志文件只记录info级别的 -->
        <filter class="ch.qos.logback.classic.filter.LevelFilter">
            <level>info</level>
            <onMatch>ACCEPT</onMatch>
            <onMismatch>DENY</onMismatch>
        </filter>
    </appender>

    <!-- 日志记录器，日期滚动记录 -->
    <appender name="FILEDEBUG" class="ch.qos.logback.core.rolling.RollingFileAppender">
        <!-- 正在记录的日志文件的路径及文件名 -->
        <file>${LOG_PATH}/${APP_DIR}/log_debug-${port}.log</file>
        <!-- 日志记录器的滚动策略，按日期，按大小记录 -->
        <rollingPolicy class="ch.qos.logback.core.rolling.TimeBasedRollingPolicy">
            <!-- 归档的日志文件的路径，例如今天是2013-12-21日志，当前写的日志文件路径为file节点指定，可以将此文件与file指定文件路径设置为不同路径，从而将当前日志文件或归档日志文件置不同的目录。
            而2013-12-21的日志文件在由fileNamePattern指定。%d{yyyy-MM-dd}指定日期格式，%i指定索引 -->
            <fileNamePattern>${LOG_PATH}/${APP_DIR}/debug/log-debug-${port}-%d{yyyy-MM-dd}.%i.log</fileNamePattern>
            <!-- 除按日志记录之外，还配置了日志文件不能超过10MB，若超过10MB，日志文件会以索引0开始，
            命名日志文件，例如log-error-2013-12-21.0.log -->
            <timeBasedFileNamingAndTriggeringPolicy class="ch.qos.logback.core.rolling.SizeAndTimeBasedFNATP">
                <maxFileSize>10MB</maxFileSize>
            </timeBasedFileNamingAndTriggeringPolicy>
        </rollingPolicy>
        <!-- 追加方式记录日志 -->
        <append>true</append>
        <!-- 日志文件的格式 -->
        <encoder class="ch.qos.logback.classic.encoder.PatternLayoutEncoder">
            <pattern>${LOG_PATTERN}</pattern>
            <charset>${CHARSET}</charset>
        </encoder>
    </appender>


    <!-- project default level -->
    <logger name="${groupId}" level="DEBUG" />

    <logger name="springfox.documentation" level="WARN" />
    <logger name="com.zaxxer.hikari" level="WARN" />
    <logger name="org.thymeleaf" level="WARN" />
    <logger name="com.mzlion" level="WARN" />
    <logger name="druid.sql" level="WARN" />
    <logger name="com.alibaba" level="WARN" />
    <logger name="org.apache" level="WARN" />
    <logger name="io.netty" level="WARN" />
    <logger name="io.lettuce" level="WARN" />
    <logger name="org.redisson" level="WARN" />
    <logger name="reactor." level="WARN" />
    <logger name="de.codecentric" level="ERROR" />

    <!-- show parameters for hibernate sql 专为 Hibernate 定制 -->
    <logger name="org.hibernate" level="WARN" />
    <logger name="org.hibernate.SQL" level="DEBUG" />
    <logger name="org.hibernate.dialect.Dialect" level="DEBUG" />
    <logger name="org.hibernate.type.descriptor.sql.BasicBinder" level="TRACE" />
    <logger name="org.hibernate.hql.internal.ast.QueryTranslatorImpl" level="WARN" />
    <logger name="org.hibernate.engine.jdbc.env.internal.JdbcEnvironmentInitiator" level="DEBUG" />

    <!--log4jdbc -->
    <logger name="jdbc.sqltiming" level="WARN"/>
    <logger name="com.ibatis" level="WARN" />
    <logger name="com.ibatis.common.jdbc.SimpleDataSource" level="WARN" />
    <logger name="com.ibatis.common.jdbc.ScriptRunner" level="WARN" />
    <logger name="com.ibatis.sqlmap.engine.impl.SqlMapClientDelegate" level="WARN" />
    <logger name="java.sql.Connection" level="INFO" />
    <logger name="java.sql.Statement" level="INFO" />
    <logger name="java.sql.PreparedStatement" level="INFO" />
    <logger name="java.sql.ResultSet" level="INFO" />

    <logger name="org.springframework" level="WARN" />
    <logger name="org.springframework.security.web" level="WARN" />
    <logger name="com.ulisesbocchio.jasyptspringboot" level="WARN" />

    <logger name="com.simbest.boot.base.web.filter.CsrfProtectFilter" level="WARN" />
    <logger name="com.simbest.boot.base.web.log.GlobalWebRequestLogger" level="WARN" />


    <!-- 生产环境下，将此级别配置为适合的级别，以免日志文件太多或影响程序性能 -->
    <springProfile name="dev,uat,obuat,test">
        <root level="DEBUG">
            <appender-ref ref="FILEERROR" /> <!--只记录ERRRO级别以上日志-->
            <appender-ref ref="FILEWARN" />  <!--只记录WARN级别以上日志-->
            <appender-ref ref="FILEINFO" />  <!--只记录INFO级别以上日志-->
            <appender-ref ref="FILEDEBUG" /> <!--记录DEBUG级别以上日志，但接收logger向上传递-->
            <appender-ref ref="CONSOLE" />
        </root>
    </springProfile>

    <springProfile name="prd">
        <root level="INFO">
            <appender-ref ref="FILEERROR" /> <!--只记录ERRRO级别以上日志-->
            <appender-ref ref="FILEWARN" />  <!--只记录WARN级别以上日志-->
            <appender-ref ref="FILEINFO" />  <!--只记录INFO级别以上日志-->
            <appender-ref ref="FILEDEBUG" /> <!--记录DEBUG级别以上日志，但接收logger向上传递-->
            <appender-ref ref="CONSOLE" />
        </root>
    </springProfile>
    <springProfile name="dev,uat,test,obprd">
        <root level="DEBUG">
            <appender-ref ref="FILEERROR" /> <!--只记录ERRRO级别以上日志-->
            <appender-ref ref="FILEWARN" /> <!--只记录WARN级别以上日志-->
            <appender-ref ref="FILEINFO" /> <!--只记录INFO级别以上日志-->
            <appender-ref ref="FILEDEBUG" /> <!--记录DEBUG级别以上日志，但接收logger向上传递-->
            <appender-ref ref="CONSOLE" />
        </root>
    </springProfile>
    <!--日志异步到数据库 -->
    <!-- <appender name="DB" class="ch.qos.logback.classic.db.DBAppender">
        日志异步到数据库
        <connectionSource class="ch.qos.logback.core.db.DriverManagerConnectionSource">
           连接池
           <dataSource class="com.mchange.v2.c3p0.ComboPooledDataSource">
              <driverClass>com.mysql.jdbc.Driver</driverClass>
              <url>****************************************</url>
              <user>root</user>
              <password>root</password>
            </dataSource>
        </connectionSource>
  </appender> -->


</configuration>