package com.simbest.boot.hnjjwz.column.familyWind.model;/**
 * Created by KZH on 2019/7/30 18:09.
 */

import com.simbest.boot.base.annotations.EntityIdPrefix;
import com.simbest.boot.base.model.LogicModel;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.AllArgsConstructor;
import lombok.Data;
import lombok.EqualsAndHashCode;
import lombok.NoArgsConstructor;
import org.hibernate.annotations.GenericGenerator;

import javax.persistence.Column;
import javax.persistence.Entity;
import javax.persistence.GeneratedValue;
import javax.persistence.Id;

/**
 * <AUTHOR>
 * @create 2019-07-30 18:09
 * @desc 记录人
 **/
@Data
@EqualsAndHashCode(callSuper = true)
@NoArgsConstructor
@AllArgsConstructor
@Entity(name = "us_record_person")
@ApiModel(value = "记录人")
public class Record<PERSON>erson extends LogicModel {

    @Id
    @Column(name = "id", length = 40)
    @GeneratedValue(generator = "snowFlakeId")
    @GenericGenerator(name = "snowFlakeId", strategy = "com.simbest.boot.util.distribution.id.SnowflakeId")
    @EntityIdPrefix(prefix = "RP") //主键前缀，此为可选项注解
    private String id;

    @Column(length = 40)
    @ApiModelProperty(value = "OA账号")
    private String userName;

    @Column(columnDefinition="int default 5")
    @ApiModelProperty(value = "书法次数")
    private Integer calligraphyVote;

    @Column(columnDefinition="int default 5")
    @ApiModelProperty(value = "绘画次数")
    private Integer paintingVote;

    @Column(columnDefinition="int default 5")
    @ApiModelProperty(value = "征文投票次数")
    private Integer collectarticleVote;

    @Column(columnDefinition="int default 5")
    @ApiModelProperty(value = "摄影投票次数")
    private Integer photographyVote;

    @Column(columnDefinition="int default 5")
    @ApiModelProperty(value = "廉家事")
    private Integer model1;

    @Column(columnDefinition="int default 5")
    @ApiModelProperty(value = "廉家宝")
    private Integer model2;

    @Column(columnDefinition="int default 5")
    @ApiModelProperty(value = "廉家训")
    private Integer model3;

}
