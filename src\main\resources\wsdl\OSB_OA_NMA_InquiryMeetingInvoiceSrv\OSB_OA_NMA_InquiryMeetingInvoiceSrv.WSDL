<?xml version = "1.0" encoding = "UTF-8"?>
<wsdl:definitions xmlns:wsdl="http://schemas.xmlsoap.org/wsdl/"
      xmlns:soap="http://schemas.xmlsoap.org/wsdl/soap/"
      xmlns:tns="http://mss.cmcc.com/OSB_OA_NMA_InquiryMeetingInvoiceSrv"
      name="OSB_OA_NMA_InquiryMeetingInvoiceSrv"
      targetNamespace="http://mss.cmcc.com/OSB_OA_NMA_InquiryMeetingInvoiceSrv">
 
            <wsdl:types>
               <schema xmlns="http://www.w3.org/2001/XMLSchema">
                      <import namespace="http://mss.cmcc.com/OSB_OA_NMA_InquiryMeetingInvoiceSrv" schemaLocation="OSB_OA_NMA_InquiryMeetingInvoiceSrv.xsd"/>
               </schema>
            </wsdl:types>
 
            <wsdl:message name="OSB_OA_NMA_InquiryMeetingInvoiceSrvRequestMessage">
                <wsdl:part name="payload" element="tns:OSB_OA_NMA_InquiryMeetingInvoiceSrvRequest"/>
            </wsdl:message>
 
            <wsdl:message name="OSB_OA_NMA_InquiryMeetingInvoiceSrvResponseMessage">
                <wsdl:part name="payload" element="tns:OSB_OA_NMA_InquiryMeetingInvoiceSrvResponse"/>
            </wsdl:message>
 
            <wsdl:portType name="OSB_OA_NMA_InquiryMeetingInvoiceSrv">
                <wsdl:operation name="process">
                    <wsdl:input message="tns:OSB_OA_NMA_InquiryMeetingInvoiceSrvRequestMessage"/>
                    <wsdl:output message="tns:OSB_OA_NMA_InquiryMeetingInvoiceSrvResponseMessage"/>
                </wsdl:operation>
            </wsdl:portType>
 
            <wsdl:binding name="OSB_OA_NMA_InquiryMeetingInvoiceSrvBinding" type="tns:OSB_OA_NMA_InquiryMeetingInvoiceSrv">
                <soap:binding transport="http://schemas.xmlsoap.org/soap/http"/>
                    <wsdl:operation name="process">
                     <soap:operation style="document" soapAction="process"/>
                        <wsdl:input>
                           <soap:body use="literal"/>
                        </wsdl:input>
                        <wsdl:output>
                           <soap:body use="literal"/>
                        </wsdl:output>
                   </wsdl:operation>
             </wsdl:binding>
 
            <wsdl:service name="OSB_OA_NMA_InquiryMeetingInvoiceSrv">
                <wsdl:port name="OSB_OA_NMA_InquiryMeetingInvoiceSrvPort" binding="tns:OSB_OA_NMA_InquiryMeetingInvoiceSrvBinding">
                    <soap:address location="http://localhost:8080"/>
                </wsdl:port>
            </wsdl:service>
 
</wsdl:definitions> 
