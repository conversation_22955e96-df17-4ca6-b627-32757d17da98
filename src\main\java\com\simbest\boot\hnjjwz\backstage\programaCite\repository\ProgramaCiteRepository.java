package com.simbest.boot.hnjjwz.backstage.programaCite.repository;

import com.simbest.boot.base.repository.LogicRepository;
import com.simbest.boot.hnjjwz.backstage.programaCite.model.ProgramaCite;
import org.springframework.data.domain.Page;
import org.springframework.data.domain.Pageable;
import org.springframework.data.jpa.repository.Query;
import org.springframework.data.repository.query.Param;

import java.util.List;
import java.util.Map;
import java.util.Set;

/**
 * <AUTHOR>
 * @Data 201/04/01
 * @Description 栏目的关联关系
 */
public interface ProgramaCiteRepository extends LogicRepository<ProgramaCite,String> {

    @Query (value = " SELECT pc.id as id ,pc.enabled as enabled," +
            " pc.programa_cite_type as forgramaCiteType,pc.programa_another_code as programaAnotherCode," +
            " pc.programa_one_code as programaOneCode,pc.spare1 as spare1,pc.spare2 as spare2,pc.spare3 as spare3," +
            " rpi.programa_name as programaAnotherName " +
            " from  us_programa_cite pc,us_programa_info lpi,us_programa_info rpi " +
            " where pc.programa_one_code = lpi.programa_code AND pc.programa_another_code = rpi.programa_code " +
            " AND  lpi.programa_code = :programaOneCode " +
            " AND pc.enabled=1 AND pc.removed_time is null " +
            " AND lpi.enabled=1 AND lpi.removed_time is null " +
            " AND rpi.enabled=1 AND rpi.removed_time is null ", nativeQuery = true)
    Set<Map<String,Object>> findRelationPrograma( @Param( "programaOneCode" ) String programaOneCode);

    @Query (value = " SELECT pc.enabled enabled,pc.id id,pc.programa_one_code programaOneCode," +
            " pia.programa_name programaOneName,pc.programa_another_code programaAnotherCode,pib.programa_name programaAnotherName," +
            " pc.programa_cite_type programaCiteType,dv.name programaCiteName  " +
            " FROM us_programa_cite pc,us_programa_info pia,us_programa_info pib,sys_dict_value dv " +
            " WHERE pia.programa_name LIKE concat( concat('%',:programaName),'%') AND " +
            " pc.programa_cite_type in(:programaCiteTypeList) AND" +
            " dv.dict_type = 'programaCiteType' AND " +
            " pc.programa_one_code = pia.programa_code AND pc.programa_another_code=pib.programa_code AND " +
            " pc.programa_cite_type = dv.value " +
            " AND pc.enabled=1 AND pc.removed_time IS NULL" +
            " AND pia.enabled=1 AND pia.removed_time IS NULL " +
            " AND pib.enabled=1 AND pib.removed_time IS NULL" +
            " AND dv.enabled =1 AND dv.removed_time IS NULL ",
            countQuery = " SELECT COUNT(*) " +
                    " FROM us_programa_cite pc,us_programa_info pia,us_programa_info pib,sys_dict_value dv " +
                    " WHERE pia.programa_name LIKE concat( concat('%',:programaName),'%') AND " +
                    " pc.programa_cite_type in(:programaCiteTypeList) AND" +
                    " dv.dict_type = 'programaCiteType' AND " +
                    " pc.programa_one_code = pia.programa_code AND pc.programa_another_code=pib.programa_code AND " +
                    " pc.programa_cite_type = dv.value " +
                    " AND pc.enabled=1 AND pc.removed_time IS NULL" +
                    " AND pia.enabled=1 AND pia.removed_time IS NULL " +
                    " AND pib.enabled=1 AND pib.removed_time IS NULL" +
                    " AND dv.enabled =1 AND dv.removed_time IS NULL ",nativeQuery = true)
    Page<Map<String,Object>> findDim( @Param( "programaName" ) String programaName, @Param( "programaCiteTypeList" ) List<String> programaCiteTypeList, Pageable pageable );


}
