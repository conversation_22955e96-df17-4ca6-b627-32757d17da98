<!DOCTYPE html>
<html xmlns="http://www.w3.org/1999/xhtml"
      xmlns:th="http://www.thymeleaf.org">
<head>
    <title>规章制度-清廉移动</title>
    <meta http-equiv="Content-Type" content="text/html; charset=utf-8"/>
    <meta http-equiv="X-UA-Compatible" content="IE=edge" />
    <meta name="viewport" content="width=device-width, initial-scale=1, shrink-to-fit=no"/>
    <!--
       <link href="http://************:8088/simbestui/fonts/iconfont/iconfont.css?v=svn.revision" th:href="@{http://************:8088/simbestui/fonts/iconfont/iconfont.css?v=svn.revision}" rel="stylesheet"/>
       -->
    <link href="../../fonts/iconfont/iconfont.css?v=svn.revision" th:href="@{/fonts/iconfont/iconfont.css?v=svn.revision}" rel="stylesheet"/>
    <link href="http://************:8088/simbestui/js/themes/default/easyui.css?v=svn.revision" th:href="@{http://************:8088/simbestui/js/themes/default/easyui.css?v=svn.revision}" rel="stylesheet"/>
    <link href="http://************:8088/simbestui/js/themes/icon.css?v=svn.revision" th:href="@{http://************:8088/simbestui/js/themes/icon.css?v=svn.revision}" rel="stylesheet"/>
    <link href="http://************:8088/simbestui/css/public.css?v=svn.revision" th:href="@{http://************:8088/simbestui/css/public.css?v=svn.revision}" rel="stylesheet"/>
    <link href="http://************:8088/simbestui/css/pages.css?v=svn.revision" th:href="@{http://************:8088/simbestui/css/pages.css?v=svn.revision}" rel="stylesheet"/>
    <link href="../../css/webSite.css?v=svn.revision" th:href="@{/css/webSite.css?v=svn.revision}" rel="stylesheet"/>
    <script src="http://************:8088/simbestui/js/jquery.min.js?v=svn.revision" th:src="@{http://************:8088/simbestui/js/jquery.min.js?v=svn.revision}" type="text/javascript"></script>
    <script src="http://************:8088/simbestui/js/jquery.easyui.min.js?v=svn.revision" th:src="@{http://************:8088/simbestui/js/jquery.easyui.min.js?v=svn.revision}" type="text/javascript"></script>
    <script src="http://************:8088/simbestui/js/easyui-lang-zh_CN.js?v=svn.revision" th:src="@{http://************:8088/simbestui/js/easyui-lang-zh_CN.js?v=svn.revision}" type="text/javascript"></script>
    <script src="../../js/jquery.config.js?v=svn.revision" th:src="@{/js/jquery.config.js?v=svn.revision}" type="text/javascript"></script>
    <script src="http://************:8088/simbestui/js/jquery.zjsfile.js?v=svn.revision" th:src="@{http://************:8088/simbestui/js/jquery.zjsfile.js?v=svn.revision}" type="text/javascript"></script>
    <script src="http://************:8088/simbestui/js/jquery.aduq.js?v=svn.revision" th:src="@{http://************:8088/simbestui/js/jquery.aduq.js?v=svn.revision}" type="text/javascript"></script>
    <script src="http://************:8088/simbestui/js/jquery.process.js?v=svn.revision" th:src="@{http://************:8088/simbestui/js/jquery.process.js?v=svn.revision}" type="text/javascript"></script>
    <script src="../../js/webSite.js?v=svn.revision" th:src="@{/js/webSite.js?v=svn.revision}" type="text/javascript"></script>
    <script type="text/javascript">
        $(function(){
            webSite();
            ajaxgeneral({
                url:"action/templateLayout/constructRulesGaugeLayout",
                success:function(res) {
                    //渲染模块
                    for(var i in res.data) {
                        var dataD = res.data[i];
                        var dH=["<div class='mt10'><div class='modTit'>"+
                        fastrenderRow(dataD, "<strong class='fl'>{{locationName}}</strong><a target='_blank' href='list.html?id={{locationId}}' class='fr'>+</a>")
                        +"</div><ul class='list list_regulation'>"];
                        var templateData=dataD.templateData;

                            for (var j in templateData) {
                                templateData[j].creationTime = getdateformat(templateData[j].creationTime, "yyyy-MM-dd");
                                //if (dataD.templateData[j].programaCoverFile) dataD.templateData[j].downLoadUrl = dataD.templateData[j].programaCoverFile[0].downLoadUrl;
                                var ProgramaInfo= templateData[j];

                                    if(ProgramaInfo.programaName!=undefined){
                                        //dH.push(fastrender(ProgramaInfo, "<li><strong class='fl'>{{programaName}}</strong><a target='_blank' href='list.html?id={{programaCode}}' class='fr'>+</a></li>"));
                                        dH.push("<li style='border:0;'><strong class='fl' style='color:#d61c19;'>"+ProgramaInfo.programaName+"</strong><a style='color:#d61c19;font-size: 18px;' target='_blank' href='list.html?id="+ProgramaInfo.programaCode+"' class='fr'>+</a></li>");
                                        var ProgramaData=  ProgramaInfo.templateData;//获取三级栏目下的数据
                                        if(ProgramaData.length>0){
                                            for(var k in ProgramaData){
                                                dH.push("<li style='margin-left:20px;'><a target='_blank' href='listDetails.html?id="+ProgramaData[k].id+"&pmInsId="+ProgramaData[k].pmInsId+"'>" +
                                                    "                <h6>"+ProgramaData[k].title+"</h6>" +
                                                   /* "                <p>"+ProgramaData[k].digest+"<font class=\"col_b\">[详情]</font></p>" +*/
                                                    "            </a></li>");
                                            }
                                        }

                                    }else {

                                        dH.push("<li><a target='_blank' href='listDetails.html?id="+templateData[j].id+"&pmInsId="+templateData[j].pmInsId+"'>" +
                                            "                <h6>"+templateData[j].title+"</h6>" +
                                            /*"                <p>"+templateData[j].digest+"<font class=\"col_b\">[详情]</font></p>" +*/
                                            "            </a></li>");
                                    }


                            }

                            dH.push("</ul></div>");
                        $(".center").append(dH.join(""));
                    }
                }
            });
            /*var menuDataJson =sessionStorage.getItem("menuDataJson");
            var menuData =JSON.parse(menuDataJson);
            $("#h6").html(menuData[2].menuName+"-清廉移动");*/
        });
    </script>
</head>
<body>
<!--top-->

<div class="nav">
    <ul class="auto1024">
        
    </ul>
</div>
<!--center-->
<div class="auto1024 center">

</div>
<!--copy-->
<div class="copy">©版权所有 中国移动通信集团河南有限公司</div>
</body>
</html>
