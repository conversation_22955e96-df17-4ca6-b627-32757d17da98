package com.simbest.boot.hnjjwz.backstage.programa.service;/**
 * Created by GZJ on 2019/6/26 15:05.
 */

import com.simbest.boot.base.service.ILogicService;
import com.simbest.boot.hnjjwz.backstage.programa.model.ProgramaPower;

import java.util.List;

/**
 * <AUTHOR>
 * @create 2019-06-26 15:05
 * @desc 栏目权限Service
 **/
public interface IProgramaPowerImpl extends ILogicService<ProgramaPower,String> {

    /**
     * 查看人对应的栏目权限
     * @param truename
     * @return
     */
    List<ProgramaPower> findPower(String truename);
}
