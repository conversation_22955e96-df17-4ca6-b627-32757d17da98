<!DOCTYPE html>
<html lang="zh-CN">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>功能模块测试</title>
    <style>
        body {
            font-family: Arial, sans-serif;
            margin: 0;
            padding: 20px;
            background-color: #f5f5f5;
        }
        
        .container {
            max-width: 1200px;
            margin: 0 auto;
        }
        
        .function-entries {
            width: 100%;
            display: flex;
            justify-content: space-between;
            margin-bottom: 30px;
        }
        
        .function-entry {
            width: 32%;
            height: 150px;
            display: flex;
            flex-direction: column;
            align-items: center;
            justify-content: center;
            background-color: #fff;
            box-shadow: 0 2px 8px rgba(0, 0, 0, 0.1);
            cursor: pointer;
            transition: all 0.3s;
            border-radius: 8px;
            position: relative;
            overflow: hidden;
        }
        
        .function-entry::before {
            content: "";
            position: absolute;
            top: 0;
            left: 0;
            width: 100%;
            height: 4px;
            background: linear-gradient(to right, #e60000, #ffcc00);
            opacity: 0;
            transition: opacity 0.3s;
        }
        
        .function-entry:hover {
            transform: translateY(-5px);
            box-shadow: 0 8px 20px rgba(0, 0, 0, 0.15);
        }
        
        .function-entry:hover::before {
            opacity: 1;
        }
        
        .function-entry:hover i {
            color: #e60000;
            background-color: #ffeeee;
            transform: scale(1.1);
        }
        
        .function-entry:hover span {
            color: #e60000;
        }
        
        .function-entry.active {
            background: linear-gradient(to right, #e60000, #ffcc00);
        }
        
        .function-entry.active::before {
            opacity: 0;
        }
        
        .function-entry.active i {
            color: #fff;
            background-color: rgba(255, 255, 255, 0.2);
            transform: scale(1.1);
        }
        
        .function-entry.active span {
            color: #fff;
        }
        
        .function-entry i {
            font-size: 40px;
            color: #333;
            margin-bottom: 20px;
            transition: all 0.3s;
            width: 70px;
            height: 70px;
            display: flex;
            align-items: center;
            justify-content: center;
            border-radius: 50%;
            background-color: #f5f5f5;
            box-shadow: 0 2px 10px rgba(0, 0, 0, 0.05);
        }
        
        .function-entry span {
            font-size: 18px;
            color: #333;
            font-weight: bold;
            transition: color 0.3s;
            letter-spacing: 1px;
        }
        
        .news-section {
            background-color: #fff;
            padding: 20px;
            border-radius: 8px;
            box-shadow: 0 2px 8px rgba(0, 0, 0, 0.1);
            border-left: 4px solid #e60000;
        }
        
        .news-section h2 {
            font-size: 28px;
            color: #333;
            margin-top: 0;
            padding-left: 10px;
            margin-bottom: 15px;
            font-weight: bold;
            position: relative;
            padding-bottom: 15px;
        }
        
        .news-section h2::after {
            content: "";
            position: absolute;
            bottom: 0;
            left: 0;
            width: 80px;
            height: 3px;
            background: linear-gradient(to right, #e60000, #ffcc00);
        }
    </style>
    <link href="https://cdnjs.cloudflare.com/ajax/libs/font-awesome/5.15.4/css/all.min.css" rel="stylesheet"/>
</head>
<body>
    <div class="container">
        <h1>功能模块测试</h1>
        
        <div class="function-entries">
            <div class="function-entry directive active" data-type="directive" onclick="handleFunctionEntryClick(this); return false;">
                <i class="fas fa-file-alt"></i>
                <span>上级精神</span>
            </div>
            <div class="function-entry regulation" data-type="regulation" onclick="handleFunctionEntryClick(this); return false;">
                <i class="fas fa-gavel"></i>
                <span>纪法知识</span>
            </div>
            <div class="function-entry warning" data-type="warning" onclick="handleFunctionEntryClick(this); return false;">
                <i class="fas fa-bell"></i>
                <span>警示曝光</span>
            </div>
        </div>
        
        <div class="news-section">
            <h2>上级精神</h2>
            <div id="news-list">
                <p>这里是新闻列表内容</p>
            </div>
        </div>
    </div>
    
    <script>
        // 模拟getEightRegulationsData函数
        function getEightRegulationsData(page, category) {
            console.log('getEightRegulationsData called with page: ' + page + ', category: ' + category);
            document.getElementById('news-list').innerHTML = '<p>加载了' + category + '类型的数据</p>';
        }
        
        // 直接添加内联点击处理函数
        function handleFunctionEntryClick(element) {
            console.log('handleFunctionEntryClick called');
            
            // 获取当前点击的功能入口的类型和文字
            var type = element.getAttribute('data-type');
            var title = element.querySelector('span').innerText;
            console.log('Type: ' + type + ', Title: ' + title);
            
            // 移除所有功能入口的active类
            document.querySelectorAll('.function-entry').forEach(function(el) {
                el.classList.remove('active');
            });
            
            // 给当前点击的功能入口添加active类
            element.classList.add('active');
            console.log('Added active class to: ' + type);
            
            // 设置新闻列表标题
            var newsTitle = document.querySelector('.news-section h2');
            if (newsTitle) {
                newsTitle.innerText = title;
                console.log('Updated news title to: ' + title);
            }
            
            // 加载相应的数据
            getEightRegulationsData(1, type);
            
            return false;
        }
    </script>
</body>
</html>
