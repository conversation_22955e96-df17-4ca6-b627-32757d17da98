package com.simbest.boot.hnjjwz.backstage.announcement.service;


import com.simbest.boot.base.service.ILogicService;
import com.simbest.boot.base.web.response.JsonResponse;
import com.simbest.boot.hnjjwz.backstage.announcement.model.Announcement;
import com.simbest.boot.hnjjwz.backstage.slideshow.model.SlideShow;
import com.simbest.boot.hnjjwz.process.wf.model.PmInstence;
import com.simbest.boot.hnjjwz.process.wf.model.ProgramaDataForm;
import com.simbest.boot.security.IAppDecision;
import com.simbest.boot.security.SimpleAppDecision;
import org.springframework.data.domain.Page;
import org.springframework.data.domain.Pageable;

import java.util.List;
import java.util.Map;

/**
 * <strong>Title : </strong><br>
 * <strong>Description : </strong><br>
 * <strong>Create on : 2018/6/19</strong><br>
 * <strong>Modify on : 2018/6/19</strong><br>
 * <strong>Copyright (C) Ltd.</strong><br>
 *
 * <AUTHOR> 刘萌@simbest.com.cn
 * @version <strong>V1.0.0</strong><br>
 * <strong>修改历史:</strong><br>
 * 修改人 修改日期 修改描述<br>
 * -------------------------------------------<br>
 */
public interface IAnnouncementService extends ILogicService<Announcement,String> {

    /**
     * 根据公告标题(模糊)、发布人姓名(模糊)、关联的栏目内容(精确)、是否显示在首页公告滚动处(精确)
     * @param mapObject
     * @param pageable
     * @return
     */
    JsonResponse findAllDim( Map<String,Object> mapObject, Pageable pageable);

    List<Announcement> findAnnouncement();

    /**
     * 提交下一步
     * @param currentUserCode
     * @param workItemId
     * @param outcome
     * @param location
     * @param bodyParam
     * @return
     */
    JsonResponse nextStep(String currentUserCode,String workItemId,String outcome, String location,String formId,String source,Map<String,Object> bodyParam);

    /**
     * 起草发起流程
     * @param announcement
     * @param userId
     * @param outcome
     * @param message
     */
    int startProcess(Announcement announcement,String userId, String outcome,String message,String source,String currentUserCode);

    /**
     *
     * @param announcement
     * @param workItemID
     * @param outcome
     * @param message
     * @param userId
     * @param location
     */
    int saveSubmitTask(Announcement announcement, String workItemID, String outcome, String message,  String userId, String location,String source,String currentUserCode);

    /**
     * 流程审批
     * @param workItemID
     * @param currentUserCode
     * @param currentUserName
     * @param userId
     * @param outcome
     * @param message
     * @param announcement
     * @return
     */
    int processApproval( Long workItemID, String currentUserCode , String currentUserName, String userId, String outcome, String message, PmInstence pmInstence );


    /**
     * 查询决策
     * @param processInstId
     * @param processDefName
     * @param location
     * @return

    List<SimpleAppDecision> getDecisions(String processInstId, String processDefName, String location, String source, String currentUser);
     */
    /**
     * 注销流程2
     * @param pmInstId
     * @return
     */
    int terminateProcessInst(Long pmInstId);

    /**
     * 根据决策查找人员
     * @param sysAppDecision
     * @return

    JsonResponse  getOrgAndUser(String processInstId, IAppDecision sysAppDecision, String source, String currentUser);
     */
    /**
     * 详情办理
     * @param processInstId
     * @return
     */
    JsonResponse getApprovalFromDetail(Long processInstId,String source,String currentUserCode,String pmInstId ,String location);


    JsonResponse findDataDetailList2(Map<String,Object> paramsMap,Pageable pageable);

    /**
     * 置顶功能
     * @param
     * @return
     */
    JsonResponse stick( String id);

    /**
     * 再根据pm_ins_id字段获取ApprovalForm对象
     * @param pmInsId
     * @return
     */
    Announcement getAnnouncementPmInsId(String pmInsId);

    /**
     * 保存草稿
     *
     * @return
     */
    JsonResponse saveDraft(String source, String currentUserCode, Announcement innovationTopicForm);


    /**
     * 废除草稿
     *
     * @return
     */
    JsonResponse deleteDraft(String source, String currentUserCode, String pmInsId, Announcement innovationTopicForm);

    List<Announcement> getAnnouncementArticleList();

    Page<Announcement> findAnnouncementPageByColumnId(String columnId, int page, int size);

    Announcement getDataArticleById(String articleId);
}
