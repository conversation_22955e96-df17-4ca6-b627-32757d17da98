<!DOCTYPE html>
<html xmlns="http://www.w3.org/1999/xhtml"
      xmlns:th="http://www.thymeleaf.org">
<head>
    <title>子菜单列表</title>
    <meta http-equiv="Content-Type" content="text/html; charset=utf-8"/>
    <meta http-equiv="X-UA-Compatible" content="IE=edge" />
    <meta name="viewport" content="width=device-width, initial-scale=1, shrink-to-fit=no"/>
    <!--
    <link href="http://************:8088/simbestui/fonts/iconfont/iconfont.css?v=svn.revision" th:href="@{http://************:8088/simbestui/fonts/iconfont/iconfont.css?v=svn.revision}" rel="stylesheet"/>
    -->
    <link href="../../fonts/iconfont/iconfont.css?v=svn.revision" th:href="@{/fonts/iconfont/iconfont.css?v=svn.revision}" rel="stylesheet"/>
    <link href="http://************:8088/simbestui/js/themes/default/easyui.css?v=svn.revision" th:href="@{http://************:8088/simbestui/js/themes/default/easyui.css?v=svn.revision}" rel="stylesheet"/>
    <link href="http://************:8088/simbestui/js/themes/icon.css?v=svn.revision" th:href="@{http://************:8088/simbestui/js/themes/icon.css?v=svn.revision}" rel="stylesheet"/>
    <link href="http://************:8088/simbestui/css/public.css?v=svn.revision" th:href="@{http://************:8088/simbestui/css/public.css?v=svn.revision}" rel="stylesheet"/>
    <link href="http://************:8088/simbestui/css/pages.css?v=svn.revision" th:href="@{http://************:8088/simbestui/css/pages.css?v=svn.revision}" rel="stylesheet"/>
    <script src="http://************:8088/simbestui/js/jquery.min.js?v=svn.revision" th:src="@{http://************:8088/simbestui/js/jquery.min.js?v=svn.revision}" type="text/javascript"></script>
    <script src="http://************:8088/simbestui/js/jquery.easyui.min.js?v=svn.revision" th:src="@{http://************:8088/simbestui/js/jquery.easyui.min.js?v=svn.revision}" type="text/javascript"></script>
    <script src="http://************:8088/simbestui/js/easyui-lang-zh_CN.js?v=svn.revision" th:src="@{http://************:8088/simbestui/js/easyui-lang-zh_CN.js?v=svn.revision}" type="text/javascript"></script>
    <script src="../../js/jquery.config.js?v=svn.revision" th:src="@{/js/jquery.config.js?v=svn.revision}" type="text/javascript"></script>
    <script src="http://************:8088/simbestui/js/jquery.zjsfile.js?v=svn.revision" th:src="@{http://************:8088/simbestui/js/jquery.zjsfile.js?v=svn.revision}" type="text/javascript"></script>
    <script src="http://************:8088/simbestui/js/jquery.aduq.js?v=svn.revision" th:src="@{http://************:8088/simbestui/js/jquery.aduq.js?v=svn.revision}" type="text/javascript"></script>
    <script src="http://************:8088/simbestui/js/jquery.process.js?v=svn.revision" th:src="@{http://************:8088/simbestui/js/jquery.process.js?v=svn.revision}" type="text/javascript"></script>
    <script type="text/javascript">
        var i=1;
        $(function(){
            //showDialog做为打开对话框的公共class名称   showDialogindex做为打开修改对话框传行的索引   delete做为删除按钮配置命令   deleteid做为删除传值  searchtable做为条件查询的按钮公共class名称
            var pageparam={
                "listtable":{
                    /*"idFiled":"ID", 用于自定义id的情况，当接口查出的id不为id，比如是ID时，在这里设置字段名 */
                    "listname":"#submenuMenuTable",//table列表的id名称，需加# menuExpalinTable
                    "querycmd":"action/submenuMenu/findAll",//table列表的查询命令
                    "checkboxall":true,
                    "contentType":"application/json; charset=utf-8",//table列表的传参方式，如果是json就写，不写默认kv
                    "nowrap": true,//把数据显示在一行里,默认true
                    "styleClass": "noScroll",
                    "frozenColumns":[[
                        { field: "ck",checkbox:true}
                    ]],//固定在左侧的列
                    "columns":[[//列
                        { title: "父菜单名", field: "menuName", width: 120},
                        { title: "子菜单名", field: "submenuName", width: 120},
                        { title: "指向内容Id", field: "pmInsId", width: 120},
                        { title: "连接页面", field: "submenuUrl", width: 120},
                        {
                            field: "opt", title: "操作", width: 110, rowspan: 1,//align：对齐此列的数据，可以用left、right、center
                            formatter: function (value, row,index) {//单元格的格式化函数，需要三个参数：value：字段的值；rowData：行的记录数据；rowIndex：行的索引
                                var g = "<a href='#' class='showDialog'  showDialogindex='" + index + "'>【修改】</a>"
                                    +"<a href='#' delete='action/submenuMenu/deleteById' deleteid='"+row.id+"'>【删除】</a>";
                                return g;
                            }
                        }
                    ] ],
                    "pagerbar": [{
                        id:"deleteall",
                        iconCls: 'icon-remove',
                        text:"批量删除&nbsp;"
                    }],
                    "deleteall":{//批量删除deleteall.id要与pagerbar.id相同
                        "id":"deleteall",
                        "url":"action/submenuMenu/deleteAllByIds",
                        "contentType":"application/json; charset=utf-8"
                    }
                },
                "dialogform":{
                    "dialogid":"#buttons",//对话框的id
                    "formname":"#submenuMenuTableAddForm",//新增或修改对话框的formid需加#
                    "insertcmd":"action/submenuMenu/create",//新增命令
                    "updatacmd":"action/submenuMenu/update",//修改命令
                    "onSubmit":function(data){
                        return true;
                    }
                }
            };
            loadGrid(pageparam);

            //点击打开栏目树
            $(".chooseparentPrograma").on("click",function(){
                var gps=getQueryString();
                //第一个参数是页面的url(multi为0表示单选，为1表示多选),第二个参数是当前对话框对应iframe的name（若是有好多次iframe请按从内到外的顺序以|分隔）,第三个参数选择人界面的标题,
                //第四个参数是选择人完成之后的回调函数名称,第五个参数对话框是否有底部按钮来设置表示默认有(若需要设置宽高,若需要就写false 不需要写true) 第6是宽默认1000，第7是高默认550
                //第二个参数必须是唯一的
                var href={"multi":"0","name":"chooseParentProgramaVal"};
                var chooseRow=top.chooseWeb.chooseParentProgramaVal?top.chooseWeb.chooseParentProgramaVal.data:[];
                if($("#menuName").val()!=""){
                    var datas=[];
                    var names=$("#menuCode").val().split(",");
                    var codes=$("#menuName").val().split(",");
                    for(var i in codes){
                        var datai={};
                        datai.id=codes[i];
                        datai.name=names[i];
                        datas.push(datai);
                    }
                    top.chooseWeb.chooseParentProgramaVal={"data":datas};
                }else{//表示新增
                    top.chooseWeb.chooseParentProgramaVal={"data":[]};
                }
                var url=tourl((gps.form?"hnjjwz/":"")+'html/indexMenu/SubmenuMenuListThree.html?',href);
                top.dialogP(url,gps.form?"programaInfoList":'programaInfoList','选择父菜单','chooseParentPrograma',false,'800');
                //记住decisionOptF这个参数是打开的页面里的名字
            });

        });
        //form表单填充数据之前做什么,data为填充数据的集合,isupdate表示是新增还是修改,若为新增则data为{}
        function beforerender(data,isupdate){
            if(isupdate){
                $('.update-readonly').hide();
            }else{
                $('.update-readonly').show();
            }
        };
        //回调函数
        window.programaInfo=function(data){
        };



        //选择栏目树后用于回显
        window.chooseParentPrograma=function(data){
            var menuCode = "";
            var menuName = "";
            var dataLength = data.data.length;
            for(var i=0;i<dataLength;i++){
                menuCode = menuCode + data.data[i].orgCode;
                menuName =  menuName + data.data[i].displayName;
                if(i<dataLength-1){
                    menuCode = menuCode + ",";
                    menuName = menuName + ",";
                }
            }
            $("#menuCode").val(menuCode);
            $("#menuName").val(menuName);
        };


    </script>
</head>
<style>
    .formTable { width: 100%; margin-top: 30px; border-spacing: 0; border-top: 1px solid #dedede; border-left: 1px solid #dedede; }
    .formTable>tbody>tr:not(:first-child)>td { border-right: 1px solid #dedede; border-bottom: 1px solid #dedede; font-size: 14px; height: 36px; }
    .formTable>tbody>tr>td input,.formTable>tbody>tr>td span,.formTable>tbody>tr>td textarea,.formTable>tbody>tr>td .textbox .textbox-text{ border: none; font-size: 14px; }
    .formTable td.lable{ background-color: #ddf1fe; padding: 5px; text-align: left; }
    .formTable td .textAndInput_readonly, .formTable td .textAndInput_readonly .validatebox-readonly{ background-color: #fff; }
    .cselectorImageUL .btn{ right: 3px; top: 3px; }
    .cselectorImageUL input[type='file']{ right: 25px; top: 3px; }
    textarea{ line-height: 20px; letter-spacing: 1px; }
    table.tabForm{ border-color: #dedede; }
</style>
<body class="body_page">
<!--searchform-->
<form id="submenuMenuTableQueryForm" >
    <table border="0" cellpadding="0" cellspacing="6" width="100%">
        <input id="programaCode" name="programaCode" type="hidden"/>
        <tr>
            <td width="100" align="right">子菜单名</td>
            <td width="150">
                <input name="submenuName" type="text" value="" />
            </td>

            <td></td>
            <td>
                <div class="w100">
                    <a class="btn a_primary fr searchtable"><span>查询</span></a>   
					<a class="btn a_success showDialog fr mr10"><span>新增</span></a>
                </div>
            </td>
        </tr>
    </table>
</form>
<!--table-->
<div class="submenuMenuTable"><table id="submenuMenuTable"></table></div>
<!--新增修改的dialog页面-->
<div id="buttons" title="新增或修改" class="easyui-dialog" style="width:800px;height:500px;">
    <form id="submenuMenuTableAddForm" method="post" contentType="application/json; charset=utf-8" cmd-select="action/submenuMenu/findById" beforerender="beforerender()">
        <input id="id" name="id" type="hidden" />
        <table border="0" style="width: 100%;" class="tabForm formTable" cellpadding="0" cellspacing="10" >
            <tr>
                <td width="16.6%"></td>
                <td width="32.2%"></td>
                <td width="16.6%"></td>
                <td width="32.2%"></td>
            </tr>

            <tr style="display: none">
                <td align="right" class="lable">父栏目编码</td>
                <td colspan="3"><input id="menuCode" name="menuCode" type="text" class="easyui-validatebox"  />
            </td>
            </tr>
            <tr>
                <td align="right" class="lable">选择父栏目</td>
                <td colspan="3"><input id="menuName" name="menuName" type="text"  class="easyui-validatebox" style="width: calc(100% - 60px);" />
                    <a class="btn a_warning chooseparentPrograma fr"><i class="iconfont">&#xe634;</i></a>
                </td>
            </tr>
            <tr>

                <td align="right" class="lable">子菜单名<font class="col_r">*</font></td>
                <td colspan="3">
                    <input id="submenuName" name="submenuName" type="text"  class="easyui-validatebox"  required='required' />
                </td>
            </tr>
            <tr>
                <td align="right" class="lable">指向内容Id<font class="col_r">*</font></td>
                <td colspan="3">
                    <input id="pmInsId" name="pmInsId" type="text"  class="easyui-validatebox"  required='required' />
                </td>
            </tr>

            <tr>
                <td align="right" class="lable">连接页面<font class="col_r">*</font></td>
                <td colspan="3">
                    <input id="submenuUrl" name="submenuUrl" type="text"  class="easyui-validatebox"  required='required' />
                </td>
            </tr>


        </table>

    </form>
</div>
</body>
</html>
