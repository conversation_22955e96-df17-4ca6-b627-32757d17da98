package com.simbest.boot.hnjjwz.sharingPlatform.web;

import com.simbest.boot.base.web.controller.LogicController;
import com.simbest.boot.base.web.response.JsonResponse;
import com.simbest.boot.constants.ApplicationConstants;
import com.simbest.boot.hnjjwz.sharingPlatform.model.MenuDeploy;
import com.simbest.boot.hnjjwz.sharingPlatform.service.IMenuDeployService;
import com.simbest.boot.hnjjwz.util.FileTool;
import com.simbest.boot.sys.service.ISysFileService;
import io.swagger.annotations.Api;
import io.swagger.annotations.ApiImplicitParam;
import io.swagger.annotations.ApiImplicitParams;
import io.swagger.annotations.ApiOperation;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.data.domain.Pageable;
import org.springframework.web.bind.annotation.*;

import java.util.Map;

/**
 * <AUTHOR>
 * @Data 2019/05/06
 * @Description 菜单配置相关接口
 */
@Api(description = "菜单配置相关接口")
@Slf4j
@RestController
@RequestMapping(value = "/action/menuDeploy")
public class MenuDeployController extends LogicController<MenuDeploy, String> {

    private IMenuDeployService menuDeployService;


    @Autowired
    public MenuDeployController ( IMenuDeployService menuDeployService) {
        super(menuDeployService);
        this.menuDeployService = menuDeployService;
    }

    @Autowired
    private FileTool fileTool;

    @Autowired
    private ISysFileService sysFileService;

    /**
     * 根据菜单名(模糊)以及父菜单名(模糊)查询分页
     * @param page
     * @param size
     * @param direction
     * @param properties
     * @param menuDeployMap
     * @return
     */
    @ApiOperation (value = "根据菜单名(模糊)以及父菜单名(模糊)查询分页", notes = "根据菜单名(模糊)以及父菜单名(模糊)查询分页")
    @ApiImplicitParams ({ //
            @ApiImplicitParam (name = "page", value = "当前页码", dataType = "int", paramType = "query", //
                    required = true, example = "1"), //
            @ApiImplicitParam(name = "size", value = "每页数量", dataType = "int", paramType = "query", //
                    required = true, example = "10"), //
            @ApiImplicitParam(name = "direction", value = "排序规则（asc/desc）", dataType = "String", //
                    paramType = "query"), //
            @ApiImplicitParam(name = "properties", value = "排序规则（属性名称）", dataType = "String", //
                    paramType = "query") //
    })
    @PostMapping (value = {"/findAllDim","/findAllDim/sso"})
    public JsonResponse findAllDim( @RequestParam (required = false, defaultValue = "1") int page, //
                                 @RequestParam(required = false, defaultValue = "10") int size, //
                                 @RequestParam(required = false) String direction, //
                                 @RequestParam(required = false) String properties,
                                 @RequestBody(required = false) Map<String,Object> menuDeployMap //
    ) {
        Pageable pageable = menuDeployService.getPageable(page, size, direction, properties);
        return menuDeployService.findAllDim(menuDeployMap,pageable);
    }

    /**
     * 新增一个菜单的信息
     * @return
     */
    @ApiOperation (value = "新增一个菜单的信息", notes = "新增一个菜单的信息")
    @PostMapping (value = {"/createDim","/createDim/sso"})
    public JsonResponse createDim( @RequestBody(required = false) MenuDeploy menuDeploy ) {
        changeName(menuDeploy);
        return super.create( menuDeploy );
    }

    /**
     * 修改一个菜单的信息
     * @return
     */
    @ApiOperation (value = "修改一个菜单的信息", notes = "修改一个菜单的信息")
    @PostMapping (value = {"/updateDim","/updateDim/sso"})
    public JsonResponse updateDim( @RequestBody(required = false) MenuDeploy menuDeploy ) {
        changeName(menuDeploy);
        return super.update( menuDeploy );
    }

    /**
     * 获取父菜单名以及自己的菜单全路径名
     * @param menuDeploy
     */
    private void changeName(MenuDeploy menuDeploy){
        //todo 菜单传来的父菜单的信息为父菜单的全路径名以及父菜单的id
        String menuName = menuDeploy.getMenuName();
        //获取父菜单名以及自己的菜单全路径名
        MenuDeploy menuDeployParent = menuDeployService.findById( menuDeploy.getParentMenuId() );
        if(menuDeployParent==null ){
            menuDeploy.setParentMenuName( null );
            menuDeploy.setDisplayName( menuName );
        }else{
            menuDeploy.setParentMenuName( menuDeployParent.getMenuName() );
            menuDeploy.setDisplayName( menuDeployParent.getDisplayName() + ApplicationConstants.ROOT_PAGE + menuName);
        }
    }
    
}
