<!DOCTYPE html>
<html xmlns="http://www.w3.org/1999/xhtml"
      xmlns:th="http://www.thymeleaf.org">
<head>
    <title>应用配置</title>
    <meta http-equiv="Content-Type" content="text/html; charset=utf-8"/>
    <meta http-equiv="X-UA-Compatible" content="IE=edge" />
    <meta name="viewport" content="width=device-width, initial-scale=1, shrink-to-fit=no"/>
    <!--
    <link href="http://************:8088/simbestui/fonts/iconfont/iconfont.css?v=svn.revision" th:href="@{http://************:8088/simbestui/fonts/iconfont/iconfont.css?v=svn.revision}" rel="stylesheet"/>
    -->
    <link href="../../fonts/iconfont/iconfont.css?v=svn.revision" th:href="@{/fonts/iconfont/iconfont.css?v=svn.revision}" rel="stylesheet"/>
    <link href="http://************:8088/simbestui/js/themes/default/easyui.css?v=svn.revision" th:href="@{http://************:8088/simbestui/js/themes/default/easyui.css?v=svn.revision}" rel="stylesheet"/>
    <link href="http://************:8088/simbestui/js/themes/icon.css?v=svn.revision" th:href="@{http://************:8088/simbestui/js/themes/icon.css?v=svn.revision}" rel="stylesheet"/>
    <link href="http://************:8088/simbestui/css/public.css?v=svn.revision" th:href="@{http://************:8088/simbestui/css/public.css?v=svn.revision}" rel="stylesheet"/>
    <link href="http://************:8088/simbestui/css/pages.css?v=svn.revision" th:href="@{http://************:8088/simbestui/css/pages.css?v=svn.revision}" rel="stylesheet"/>
    <script src="http://************:8088/simbestui/js/jquery.min.js?v=svn.revision" th:src="@{http://************:8088/simbestui/js/jquery.min.js?v=svn.revision}" type="text/javascript"></script>
    <script src="http://************:8088/simbestui/js/jquery.easyui.min.js?v=svn.revision" th:src="@{http://************:8088/simbestui/js/jquery.easyui.min.js?v=svn.revision}" type="text/javascript"></script>
    <script src="http://************:8088/simbestui/js/easyui-lang-zh_CN.js?v=svn.revision" th:src="@{http://************:8088/simbestui/js/easyui-lang-zh_CN.js?v=svn.revision}" type="text/javascript"></script>
    <script src="../../js/jquery.config.js?v=svn.revision" th:src="@{/js/jquery.config.js?v=svn.revision}" type="text/javascript"></script>
    <script src="http://************:8088/simbestui/js/jquery.zjsfile.js?v=svn.revision" th:src="@{http://************:8088/simbestui/js/jquery.zjsfile.js?v=svn.revision}" type="text/javascript"></script>
    <script src="http://************:8088/simbestui/js/jquery.aduq.js?v=svn.revision" th:src="@{http://************:8088/simbestui/js/jquery.aduq.js?v=svn.revision}" type="text/javascript"></script>
    <script src="http://************:8088/simbestui/js/jquery.process.js?v=svn.revision" th:src="@{http://************:8088/simbestui/js/jquery.process.js?v=svn.revision}" type="text/javascript"></script>
    <script type="text/javascript">
        $(function () {
            // 加载表单
            loadForm("programaCiteForm",{"programaOneCode":gps.programaOneCode});
            $("#programaOneCode").val(gps.programaOneCode);
            //点击打开栏目树
            $(".chooseProgramaAnother").on("click",function(){
                var gps=getQueryString();
                //第一个参数是页面的url(multi为0表示单选，为1表示多选),第二个参数是当前对话框对应iframe的name（若是有好多次iframe请按从内到外的顺序以|分隔）,第三个参数选择人界面的标题,
                //第四个参数是选择人完成之后的回调函数名称,第五个参数对话框是否有底部按钮来设置表示默认有(若需要设置宽高,若需要就写false 不需要写true) 第6是宽默认1000，第7是高默认550
                //第二个参数必须是唯一的
                var href={"multi":"0","name":"chooseProgramaCiteVal"};
                var chooseRow=top.chooseWeb.chooseProgramaCiteVal?top.chooseWeb.chooseProgramaCiteVal.data:[];
                if($("#programaAnotherCode").val()!=""){
                    var datas=[];
                    var names=$("#programaAnotherName").val().split(",");
                    var codes=$("#programaAnotherCode").val().split(",");
                    for(var i in codes){
                        var datai={};
                        datai.id=codes[i];
                        datai.name=names[i];
                        datas.push(datai);
                    }
                    top.chooseWeb.chooseProgramaCiteVal={"data":datas};
                }else{//表示新增
                    top.chooseWeb.chooseProgramaCiteVal={"data":[]};
                }
                var url=tourl((gps.form?"hnjjwz/":"")+'html/programaClassifyInfo/programaInfoTree.html?',href);
                top.dialogP(url,gps.form?"programaCiteConfigF":'programaCiteConfigF','选择栏目','chooseProgramaCite',false,'800');
                //记住decisionOptF这个参数是打开的页面里的名字
            });
        });
        //对表单中的元素进行校验，返回为0代表检验不成功。可以参考appManagementList.html
        window.getchoosedata=function(){
            formsubmit("programaCiteForm");
            return {state:0};
        };
		//提交表单之后进行的操作
		function submitcallback(){
		    //关闭弹出框,appConfig为打开此页面的前一个页面appManagementList.html中的的top.dialogP中的回调函数名。
            top.dialogClose("programaCiteConfig");
        };
        //选择栏目树后用于回显
        window.chooseProgramaCite=function(data){
            var programaAnotherCode = "";
            var programaAnotherName = "";
            var dataLength = data.data.length;
            for(var i=0;i<dataLength;i++){
                programaAnotherCode = programaAnotherCode + data.data[i].orgCode;
                programaAnotherName =  programaAnotherName + data.data[i].displayName;
                if(i<dataLength-1){
                    programaAnotherCode = programaAnotherCode + ",";
                    programaAnotherName = programaAnotherName + ",";
                }
            }
            $("#programaAnotherCode").val(programaAnotherCode);
            $("#programaAnotherName").val(programaAnotherName);
        };
    </script>
</head>
<!--此处只有cmd-insert，因为何时调cmd-insert或者cmd-update是根据连接上的id来调的。因为此页面上没有id，所以此页面必调cmd-insert，然后在后端判断是新增还是删除-->
<body class="body_page" >
<form id="programaCiteForm" method="post" contentType="application/json; charset=utf-8" submitcallback="submitcallback()" cmd-select="action/programaCite/findRelationPrograma" cmd-insert="action/programaCite/createFromPage">
    <input id="id" name="id"  type="hidden" />
    <table border="0" cellpadding="0" cellspacing="10">
        <tr class="update-readonly">
            <td width="100" align="right">栏目编码</td><td width="160"><input id="programaOneCode" name="programaOneCode"   type="text" class="easyui-validatebox" required='required' /></td>
            <td colspan="3"></td>
        </tr>
        <tr>
            <td width="100" align="right">选择关联栏目</td><td colspan="3"><input id="programaAnotherName" name="programaAnotherName" type="text" readonly="readonly" class="easyui-validatebox"  />
            </td>
            <td><a class="btn a_warning chooseProgramaAnother"><i class="iconfont">&#xe634;</i></a></td>
        </tr>
        <tr ><!--style="display: none"-->
            <td width="100" align="right">选择关联栏目编码</td><td colspan="3"><input id="programaAnotherCode" name="programaAnotherCode" type="text" readonly="readonly" class="easyui-validatebox"  />
            </td>
        </tr>
        <tr>
            <td width="100" align="right"><font class="col_r">*</font>关联关系类型:</td>
            <td width="160">
                <input id="programaCiteType" name="programaCiteType" class="easyui-combobox"  style="width: 100%; height: 32px;" data-options="
                         valueField: 'value',
                              panelHeight:'auto',
                              ischooseall:true,
                              ischooseallTxt:'请选择',
                              textField: 'name',
                              queryParams:{'dictType':'programaCiteType'},
                              contentType:'application/json; charset=utf-8',
                              url: web.rootdir+'sys/dictValue/findDictValue'"/>
            </td>
            <td></td>
        </tr>
    </table>
</form>
</body>
</html>
