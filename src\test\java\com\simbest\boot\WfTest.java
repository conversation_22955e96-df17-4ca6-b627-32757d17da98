package com.simbest.boot;

import com.simbest.boot.wf.process.service.IProcessInstanceService;
import org.springframework.beans.factory.annotation.Autowired;

import java.util.HashMap;
import java.util.Map;

/**
 * <strong>Title : </strong><br>
 * <strong>Description : </strong><br>
 * <strong>Create on : 2018/6/19</strong><br>
 * <strong>Modify on : 2018/6/19</strong><br>
 * <strong>Copyright (C) Ltd.</strong><br>
 *
 * <AUTHOR>
 * @version <strong>V1.0.0</strong><br>
 * <strong>修改历史:</strong><br>
 * 修改人 修改日期 修改描述<br>
 * -------------------------------------------<br>
 */
public class WfTest {
    @Autowired
    private static IProcessInstanceService processInstanceService;

    public static void main(String[] args) {
        System.out.println( "ok" );
        Map<String, Object> variables = new HashMap<String,Object>();
        String inputUserId ="";
        String username = "";
        variables.put("inputUserId", "11");
        variables.put("receiptId", "11");
        variables.put("title", "流程");
        variables.put("code", "11");
        variables.put("currentUserCode", "11");
        variables.put("activityDefID", "nma.start");
        try {
            Long workItemId =  processInstanceService.startProcessAndSetRelativeData("com.nma.flow.provincial_company","流程","流程",false, variables);

        }catch ( Exception e ){
            e.printStackTrace();
        }

    }
}
