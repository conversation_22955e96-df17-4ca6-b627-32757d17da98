package com.simbest.boot.hnjjwz.backstage.programa.service.impl;

import com.simbest.boot.base.exception.Exceptions;
import com.simbest.boot.base.service.impl.LogicService;
import com.simbest.boot.base.web.response.JsonResponse;
import com.simbest.boot.constants.ApplicationConstants;
import com.simbest.boot.hnjjwz.backstage.programa.model.ProgramaClassifyInfo;
import com.simbest.boot.hnjjwz.backstage.programa.repository.ProgramaClassifyInfoRepository;
import com.simbest.boot.hnjjwz.backstage.programa.service.IProgramaClassifyInfoService;
import com.simbest.boot.hnjjwz.constants.Constants;
import com.simbest.boot.sys.model.SysDictValue;
import com.simbest.boot.sys.service.impl.SysDictValueService;
import com.simbest.boot.util.MapUtil;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.data.domain.Page;
import org.springframework.data.domain.PageImpl;
import org.springframework.data.domain.Pageable;
import org.springframework.stereotype.Service;
import org.springframework.util.StringUtils;

import java.util.*;

/**
 * <AUTHOR>
 * @Data 2019/04/02
 * @Description
 */
@Slf4j
@Service
public class ProgramaClassifyInfoServiceImpl extends LogicService<ProgramaClassifyInfo,String> implements IProgramaClassifyInfoService {

    private ProgramaClassifyInfoRepository programaClassifyInfoRepository;

    @Autowired
    public ProgramaClassifyInfoServiceImpl ( ProgramaClassifyInfoRepository programaClassifyInfoRepository) {
        super(programaClassifyInfoRepository);
        this.programaClassifyInfoRepository = programaClassifyInfoRepository;
    }

    @Autowired
    public SysDictValueService sysDictValueService;

    /**
     * 根据栏目类型(精确)以及栏目分类名称(模糊)查询
     * @param programaClassifyName
     * @param programaType
     * @return
     */
    @Override
    public JsonResponse findDimProgramaClassify ( String programaClassifyName, String programaType , Pageable pageable) {
        if( StringUtils.isEmpty( programaClassifyName ) ){
            programaClassifyName = "";
        }
        List<String> programaTypeList = new ArrayList<>(  );
        SysDictValue sysDictValue = new SysDictValue(  );
        sysDictValue.setDictType( Constants.PROGRAMA_TYPE );
        if(StringUtils.isEmpty( programaType )){
            //查询所有的nodeStyles
            List<SysDictValue> sysDictValueList  = sysDictValueService.findDictValue ( sysDictValue);
            for(SysDictValue sysDictValueNew:sysDictValueList){
                programaTypeList.add( sysDictValueNew.getValue() );
            }
        }else{
            programaTypeList.add( programaType );
        }
        Page<Map<String,Object>> programaClassifyInfoPage = programaClassifyInfoRepository.findUserOrgDim( programaClassifyName,programaTypeList ,pageable);
        List<Map<String,Object>> programaClassifyInfoOldList = programaClassifyInfoPage.getContent();
        List<ProgramaClassifyInfo> programaClassifyInfoList = new ArrayList<>(  );
        for(Map<String,Object> programaClassifyInfoMap:programaClassifyInfoOldList){
            try {
                ProgramaClassifyInfo programaClassifyInfo  = (ProgramaClassifyInfo) MapUtil.mapToObject(programaClassifyInfoMap,ProgramaClassifyInfo.class  );
                programaClassifyInfoList.add( programaClassifyInfo );
            } catch ( Exception e ) {
                Exceptions.printException( e );
            }
        }
        Page<ProgramaClassifyInfo> programaClassifyInfoPageNew = new PageImpl<ProgramaClassifyInfo>(programaClassifyInfoList,pageable,programaClassifyInfoPage.getTotalElements() );

        return JsonResponse.success( programaClassifyInfoPageNew,"获取栏目分类信息成功！" );
    }

    /**
     * 查询所有的栏目类型id，用逗号拼接
     * @return
     */
    @Override
    public List<String> findAllProgramaClassifyIds ( ) {
        List<ProgramaClassifyInfo> programaClassifyInfoList = (List<ProgramaClassifyInfo>)super.findAllNoPage();
        StringBuffer programaClassifyIds = new StringBuffer(  );
        int num = 0;
        for(ProgramaClassifyInfo programaClassifyInfo:programaClassifyInfoList){
            programaClassifyIds.append( programaClassifyInfo.getId() );
            if(num<programaClassifyInfoList.size()-1){
                programaClassifyIds.append( ApplicationConstants.COMMA );
            }
            num++;
        }
        String programaClassifyIdStr = programaClassifyIds.toString();
        List<String> programaClassifyIdList = Arrays.asList( programaClassifyIdStr.split( ApplicationConstants.COMMA ) );
        return programaClassifyIdList;
    }

}
