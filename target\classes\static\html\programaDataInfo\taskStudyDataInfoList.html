<!DOCTYPE html>
<html xmlns="http://www.w3.org/1999/xhtml"
      xmlns:th="http://www.thymeleaf.org">
<head>
    <title>课题研究信息管理</title>
    <meta http-equiv="Content-Type" content="text/html; charset=utf-8"/>
    <meta http-equiv="X-UA-Compatible" content="IE=edge" />
    <meta name="viewport" content="width=device-width, initial-scale=1, shrink-to-fit=no"/>
    <!--
    <link href="http://************:8088/simbestui/fonts/iconfont/iconfont.css?v=svn.revision" th:href="@{http://************:8088/simbestui/fonts/iconfont/iconfont.css?v=svn.revision}" rel="stylesheet"/>
    -->
    <link href="../../fonts/iconfont/iconfont.css?v=svn.revision" th:href="@{/fonts/iconfont/iconfont.css?v=svn.revision}" rel="stylesheet"/>
    <link href="http://************:8088/simbestui/js/themes/default/easyui.css?v=svn.revision" th:href="@{http://************:8088/simbestui/js/themes/default/easyui.css?v=svn.revision}" rel="stylesheet"/>
    <link href="http://************:8088/simbestui/js/themes/icon.css?v=svn.revision" th:href="@{http://************:8088/simbestui/js/themes/icon.css?v=svn.revision}" rel="stylesheet"/>
    <link href="http://************:8088/simbestui/css/public.css?v=svn.revision" th:href="@{http://************:8088/simbestui/css/public.css?v=svn.revision}" rel="stylesheet"/>
    <link href="http://************:8088/simbestui/css/pages.css?v=svn.revision" th:href="@{http://************:8088/simbestui/css/pages.css?v=svn.revision}" rel="stylesheet"/>
    <script src="http://************:8088/simbestui/js/jquery.min.js?v=svn.revision" th:src="@{http://************:8088/simbestui/js/jquery.min.js?v=svn.revision}" type="text/javascript"></script>
    <script src="http://************:8088/simbestui/js/jquery.easyui.min.js?v=svn.revision" th:src="@{http://************:8088/simbestui/js/jquery.easyui.min.js?v=svn.revision}" type="text/javascript"></script>
    <script src="http://************:8088/simbestui/js/easyui-lang-zh_CN.js?v=svn.revision" th:src="@{http://************:8088/simbestui/js/easyui-lang-zh_CN.js?v=svn.revision}" type="text/javascript"></script>
    <script src="../../js/jquery.config.js?v=svn.revision" th:src="@{/js/jquery.config.js?v=svn.revision}" type="text/javascript"></script>
    <script src="http://************:8088/simbestui/js/jquery.zjsfile.js?v=svn.revision" th:src="@{http://************:8088/simbestui/js/jquery.zjsfile.js?v=svn.revision}" type="text/javascript"></script>
    <script src="http://************:8088/simbestui/js/jquery.aduq.js?v=svn.revision" th:src="@{http://************:8088/simbestui/js/jquery.aduq.js?v=svn.revision}" type="text/javascript"></script>
    <script src="http://************:8088/simbestui/js/jquery.process.js?v=svn.revision" th:src="@{http://************:8088/simbestui/js/jquery.process.js?v=svn.revision}" type="text/javascript"></script>
    <script type="text/javascript">
        $(function(){
            getCurrent();
            var username=web.currentUser.username;//查看当前登录人是属于分公司还是省公司

            //showDialog做为打开对话框的公共class名称   showDialogindex做为打开修改对话框传行的索引   delete做为删除按钮配置命令   deleteid做为删除传值  searchtable做为条件查询的按钮公共class名称
            var pageparam={
                "listtable":{
                    "listname":"#programaDataInfoTable",//table列表的id名称，需加#  programaCiteTable
                    "querycmd":"action/taskStudy/findDataDetailList2",//table列表的查询命令
                    "checkboxall":true,
                    "contentType":"application/json; charset=utf-8",//table列表的传参方式，如果是json就写，不写默认kv
                    "nowrap": true,//把数据显示在一行里,默认true
                    "styleClass": "noScroll",
                    "frozenColumns":[[
                        { field: "ck",checkbox:true}
                    ]],//固定在左侧的列
                    "columns":[[//列
                        { title: "标题", field: "TASK_STUDY_TITLE", width: 120},
                        { title: "所属栏目", field: "PROGRAMA_DISPLAY_NAME", width: 120},
                        { title: "发起人", field: "TRUENAME", width: 120},
                        { title: "发起部门", field: "DEPARTMENT_NAME", width: 120},
                        { title: "发布时间", field: "CREATION_TIME", width: 120},
                        {
                            field: "opt", title: "操作", width: 110, rowspan: 1,//align：对齐此列的数据，可以用left、right、center
                            formatter: function (value, row,index) {//单元格的格式化函数，需要三个参数：value：字段的值；rowData：行的记录数据；rowIndex：行的索引
                                var g;
                                if(username=="hadmin"){
                                    g = "<a class='programaInfo' ptitle='栏目信息' path='html/taskStudy/TaskStudy.html?location=province_depart_manager&type=examine&pmInstId="+row.PM_INS_ID+"'>【查看】</a>"+
                                        "<a class='modificAtion' ptitle='修改' path='html/programaDataInfo/taskStudyModificAtion.html?id=" + row.PM_INS_ID + "'>【修改】</a>";
                                    "<a href='#' delete='action/taskStudy/deleteById' deleteid='"+row.id+"'>【删除】</a>"

                                }else{
                                    g = "<a class='programaInfo' ptitle='栏目信息' path='html/taskStudy/TaskStudy.html?location=province_depart_manager&type=examine&pmInstId="+row.PM_INS_ID+"'>【查看】</a>";
                                }

                                return g;
                            }
                        }
                    ] ],
                    "pagerbar": [{
                        id:"deleteall",
                        iconCls: 'icon-remove',
                        text:"批量删除&nbsp;"
                    }],
                    "deleteall":{//批量删除deleteall.id要与pagerbar.id相同
                        "id":"deleteall",
                        "url":"action/taskStudy/deleteAllByIds",
                        "contentType":"application/json; charset=utf-8"
                    }
                },
                "dialogform":{
                    "dialogid":"#buttons",//对话框的id
                    "formname":"#programaDataInfoTableAddForm",//新增或修改对话框的formid需加#
                    "insertcmd":"action/taskStudy/create",//新增命令
                    "updatacmd":"action/taskStudy/update",//修改命令
                    "onSubmit":function(data){
                        if($("#id").val()=="") data.displayOrder="1";
                        if($("#roleCode").attr("codeError")){
                            top.mesAlert("提示信息","角色编码已存在,请重新输入！", 'error');
                            return false;
                        }
                        return true;
                    }
                }
            };
            loadGrid(pageparam);
            //点击打开栏目树
            $(".chooseProgramaName").on("click",function(){
                var gps=getQueryString();
                //第一个参数是页面的url(multi为0表示单选，为1表示多选),第二个参数是当前对话框对应iframe的name（若是有好多次iframe请按从内到外的顺序以|分隔）,第三个参数选择人界面的标题,
                //第四个参数是选择人完成之后的回调函数名称,第五个参数对话框是否有底部按钮来设置表示默认有(若需要设置宽高,若需要就写false 不需要写true) 第6是宽默认1000，第7是高默认550
                //第二个参数必须是唯一的
                var href={"multi":"1","name":"chooseProgramaNameVal"};
                var chooseRow=top.chooseWeb.chooseProgramaNameVal?top.chooseWeb.chooseProgramaNameVal.data:[];
                if($("#programaCode").val()!=""){
                    var datas=[];
                    var names=$("#programaDisplayName").val().split(",");
                    var codes=$("#programaCode").val().split(",");
                    for(var i in codes){
                        var datai={};
                        datai.id=codes[i];
                        datai.name=names[i];
                        datas.push(datai);
                    }
                    top.chooseWeb.chooseProgramaNameVal={"data":datas};
                }else{//表示新增
                    top.chooseWeb.chooseProgramaNameVal={"data":[]};
                }
                var url=tourl((gps.form?"hnjjwz/":"")+'html/programaClassifyInfo/programaInfoTree.html?',href);
                top.dialogP(url,gps.form?"taskStudyDataInfoList":'taskStudyDataInfoList','选择栏目','chooseProgramaName',false,'800');
                //记住decisionOptF这个参数是打开的页面里的名字
            });
            //打开查看页面
            $(document).on("click",".programaInfo",function(){
                var gps=getQueryString();
                var $t=$(this);
                //从应用配置按钮处获取地址以及参数
                var url=$t.attr("path");
                top.dialogP ( url, gps.form ? "programaDataInfoList" : "rightiframe", '栏目内容详情', 'programaInfo', true, '1200', '550' );
            });

            //打开修改页面
            $(document).on("click",".modificAtion",function(){
                var gps=getQueryString();
                var $t=$(this);
                //从应用配置按钮处获取地址以及参数
                var url=$t.attr("path");
                top.dialogP ( url, gps.form ? "programaDataInfoList" : "rightiframe", '修改页面', 'modificAtion', false, '1200', '600' );
            });
        });
        //选择栏目树后用于回显
        window.chooseProgramaName=function(data){
            var programaCode = "";
            var programaName = "";
            var dataLength = data.data.length;
            for(var i=0;i<dataLength;i++){
                programaCode = programaCode + data.data[i].orgCode;
                programaName =  programaName + data.data[i].displayName;
                if(i<dataLength-1){
                    programaCode = programaCode + ",";
                    programaName = programaName + ",";
                }
            }
            $("#programaCode").val(programaCode);
            $("#programaDisplayName").val(programaName);
        };
        //form表单填充数据之前做什么,data为填充数据的集合,isupdate表示是新增还是修改,若为新增则data为{}
        function beforerender(data,isupdate){
            if(isupdate){
                $('.update-readonly').hide();
            }else{
                $('.update-readonly').show();
            }
        };
        //回调函数
        window.programaInfo=function(data){
        };

    </script>
</head>

<body class="body_page">
<!--searchform-->
<form id="programaDataInfoTableQueryForm" >
    <table border="0" cellpadding="0" cellspacing="6" width="100%">
<!--
        <input id="programaCode" name="programaCode" type="hidden"/>
-->
        <tr>
            <!--<td align="right" width="60" class="tit">选择板块</td>
            <td width="260">
                <input id="programaDisplayName" name="programaDisplayName" type="text"  class="easyui-validatebox" readonly="readonly" />
            </td>
            <td width="50" id="td3" >
                <a class="btn fr a_warning chooseProgramaName" style="width:100%;"><i class=" iconfont" >&#xe634;</i></a>
            </td>-->
            <td width="150" align="right">标题</td>
            <td width="150"><input name="title" type="text" value="" /></td>
            <td>
                <div class="w100">
                    <a class="btn a_primary fr searchtable"><span>查询</span></a>
                </div>
            </td>
        </tr>
    </table>
</form>
<!--table-->
<div class="programaDataInfoTable"><table id="programaDataInfoTable"></table></div>
</body>
</html>
