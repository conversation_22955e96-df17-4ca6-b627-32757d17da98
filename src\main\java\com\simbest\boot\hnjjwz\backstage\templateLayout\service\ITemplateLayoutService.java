package com.simbest.boot.hnjjwz.backstage.templateLayout.service;/**
 * Created by KZH on 2019/5/30 17:23.
 */

import com.simbest.boot.base.service.ILogicService;
import com.simbest.boot.base.web.response.JsonResponse;
import com.simbest.boot.hnjjwz.backstage.templateLayout.model.TemplateLayout;

import javax.servlet.http.HttpServletRequest;
import java.util.List;
import java.util.Map;

/**
 * <AUTHOR>
 * @create 2019-05-30 17:23
 * @desc
 **/
public interface ITemplateLayoutService extends ILogicService<TemplateLayout,String> {

    /**
     *  首页模板
     * @return
     */
    JsonResponse findTemplateLayout();

    /**
     * 规章制度模板
     */
    JsonResponse findRulesGaugeLayout();

    /**
     * 巡察工作模板
     */
    JsonResponse findInspectionWorkLayout();

    /**
     *嵌入式防控监督模板
     */
    JsonResponse findEmbeddedLayout();

    /**
     * 课题研究模板
     */
    JsonResponse findResearchLayout();

    /**
     * 廉洁教育模板
     */
    JsonResponse findEducationLayout();

    /**
     * 廉洁文化模板
     */
    JsonResponse findCultureLayout();

    /**
     * 信访举报模板
     */
    JsonResponse findPetitionLetter();
    /**
     * 纪律审查与监督模板
     */
    JsonResponse findSupervisionLayout();

    /**
     * 家风栏目模板
     */
    JsonResponse findDanColumnsLayout(String locationType);

    void simulatedLanding(String loginUser,String appCode);

    void recordSuccessLogin(Map<String,Object> paramMap, HttpServletRequest request);

    List<TemplateLayout> findtemplateLayout(String id);

    TemplateLayout findlocationName(String locationName);

    /**
     * 分公司拆分模块
     */
    JsonResponse findIndexTemplateDownCompany();


    /**
     * 以案示警拆分模块
     */
    JsonResponse findIndexTemplateWarnCompany();
}
