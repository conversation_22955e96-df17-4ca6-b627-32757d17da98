package com.simbest.boot.hnjjwz.examOnline.web;

import com.simbest.boot.base.web.controller.LogicController;
import com.simbest.boot.base.web.response.JsonResponse;
import com.simbest.boot.hnjjwz.examOnline.model.ExamAnswer;
import com.simbest.boot.hnjjwz.examOnline.model.ExamQuestion;
import com.simbest.boot.hnjjwz.examOnline.model.ExamQuestionAnswer;
import com.simbest.boot.hnjjwz.examOnline.service.IExamAnswerService;
import com.simbest.boot.hnjjwz.examOnline.service.IExamQuestionAnswerService;
import com.simbest.boot.hnjjwz.examOnline.service.IExamQuestionService;
import io.swagger.annotations.Api;
import io.swagger.annotations.ApiImplicitParam;
import io.swagger.annotations.ApiImplicitParams;
import io.swagger.annotations.ApiOperation;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.data.domain.Pageable;
import org.springframework.web.bind.annotation.*;

/**
 * <AUTHOR>
 * @Data 2019/05/08
 * @Description
 */
@Api(description = "题目问题相关接口")
@Slf4j
@RestController
@RequestMapping(value = "/action/examQuestion")
public class ExamQuestionController extends LogicController<ExamQuestion, String> {

    @Autowired
    private IExamQuestionService examQuestionService;

    @Autowired
    public ExamQuestionController(IExamQuestionService examQuestionService) {
        super(examQuestionService);
        this.examQuestionService = examQuestionService;
    }

    @Autowired
    private IExamQuestionAnswerService iExamQuestionAnswerService;//正确答案

    @Autowired
    private  IExamAnswerService iExamAnswerService;

    /**
     * 查询可用的套题
     * @return
     */
    @ApiOperation (value = "查询可用的套题", notes = "查询可用的套题")
    @PostMapping (value = {"/findEnabledExamQuestion","/findEnabledExamQuestion/sso"})
    public JsonResponse findEnabledExamQuestion( ) {
        return examQuestionService.findEnabledExamQuestion();
    }

    /**
     * 根据题目获取答案
     * @return
     */
    @ApiOperation(value = "根据题目获取答案", notes = "根据题目获取答案")
    @ApiImplicitParams( {
            @ApiImplicitParam(name = "page", value = "当前页码", dataType = "int", paramType = "query", example = "1"),
            @ApiImplicitParam(name = "size", value = "每页数量", dataType = "int", paramType = "query", example = "10"),
    } )
    @PostMapping(value = {"/customFindExamAnswer","/customFindExamAnswer/sso"})
    public JsonResponse customFindExamAnswer(@RequestParam(required = false, defaultValue = "1") int page,
                                            @RequestParam(required = false, defaultValue = "10") int size,
                                            @RequestParam(required = false) String questionCode)
    {
        Pageable pageable = iExamAnswerService.getPageable(page, size, "asc", "answer_code");

        return  iExamAnswerService.customFindExamAnswer(questionCode,pageable);
    }

    /**
     * 修改答案
     * @return
     */
    @ApiOperation (value = "修改答案", notes = "修改答案")
    @PostMapping (value = {"/updateExamAnswer","/updateExamAnswer/sso"})
    public JsonResponse updateExamAnswer(@RequestBody(required =false) ExamAnswer examAnswer) {

        return JsonResponse.success(iExamAnswerService.update(examAnswer));
    }
    /**
     * 删除试题 并删除答案
     * @return
     */
    @ApiOperation (value = "删除试题 并删除答案", notes = "删除试题 并删除答案")
    @PostMapping (value = {"/deleteByIdAnd","/deleteByIdAnd/sso"})
    public JsonResponse deleteByIdAnd(@RequestParam(required =false)String id) {
        ExamQuestion byId = examQuestionService.findById(id);

        if(byId!=null){

            int i = iExamQuestionAnswerService.deleteExamQuestionAnswer(byId.getQuestionCode());
            if(i>0){
                this.deleteById(id);
                return JsonResponse.success(1,"删除成功");
            }
            return JsonResponse.fail(-1,"删除失败");

        }


        return JsonResponse.fail(-1,"删除失败");
    }


    /**
     * 获取全部试题
     * @return
     */
    @ApiOperation(value = "获取全部试题", notes = "获取全部试题")
    @ApiImplicitParams( {
            @ApiImplicitParam(name = "page", value = "当前页码", dataType = "int", paramType = "query", example = "1"),
            @ApiImplicitParam(name = "size", value = "每页数量", dataType = "int", paramType = "query", example = "10"),
    } )
    @PostMapping(value = {"/findAllExamQuestion","/findAllExamQuestion/sso"})
    public JsonResponse findAllExamQuestion(@RequestParam(required = false, defaultValue = "1") int page,
                                            @RequestParam(required = false, defaultValue = "10") int size)
    {
        Pageable pageable = examQuestionService.getPageable(page, size, "asc", "question_type");

        return  examQuestionService.findAllExamQuestion(pageable);
    }
}
