<?xml version="1.0" encoding="UTF-8"?>
<project version="4">
  <component name="RemoteRepositoriesConfiguration">
    <remote-repository>
      <option name="id" value="central" />
      <option name="name" value="Central Repository" />
      <option name="url" value="https://repo.maven.apache.org/maven2" />
    </remote-repository>
    <remote-repository>
      <option name="id" value="releases" />
      <option name="name" value="releases" />
      <option name="url" value="http://10.87.57.26:8082/nexus/repository/releases/" />
    </remote-repository>
    <remote-repository>
      <option name="id" value="thirdparty" />
      <option name="name" value="thirdparty" />
      <option name="url" value="http://10.87.57.26:8082/nexus/repository/maven-thirdparty/" />
    </remote-repository>
    <remote-repository>
      <option name="id" value="central" />
      <option name="name" value="Maven Central repository" />
      <option name="url" value="https://repo1.maven.org/maven2" />
    </remote-repository>
    <remote-repository>
      <option name="id" value="jboss.community" />
      <option name="name" value="JBoss Community repository" />
      <option name="url" value="https://repository.jboss.org/nexus/content/repositories/public/" />
    </remote-repository>
    <remote-repository>
      <option name="id" value="maven-thirdparty" />
      <option name="name" value="maven-thirdparty" />
      <option name="url" value="http://10.87.57.26:8082/nexus/repository/maven-thirdparty/" />
    </remote-repository>
  </component>
</project>