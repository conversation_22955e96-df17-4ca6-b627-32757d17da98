package com.simbest.boot.hnjjwz.process.wf.service.impl;

import com.simbest.boot.base.service.impl.LogicService;
import com.simbest.boot.hnjjwz.backstage.announcement.model.Announcement;
import com.simbest.boot.hnjjwz.backstage.slideshow.model.SlideShow;
import com.simbest.boot.hnjjwz.backstage.taskStudy.model.TaskStudy;
import com.simbest.boot.hnjjwz.process.wf.model.PmInstence;
import com.simbest.boot.hnjjwz.process.wf.model.ProgramaDataForm;
import com.simbest.boot.hnjjwz.process.wf.repository.PmInstenceRepository;
import com.simbest.boot.hnjjwz.process.wf.service.IPmInstenceService;

import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;

import java.util.Map;

/**
 * <strong>Title : 主单据</strong><br>
 * <strong>Description : </strong><br>
 * <strong>Create on : 2018/6/19</strong><br>
 * <strong>Modify on : 2018/6/19</strong><br>
 * <strong>Copyright (C) Ltd.</strong><br>
 *
 * @<NAME_EMAIL>
 * @version <strong>V1.0.0</strong><br>
 * <strong>修改历史:</strong><br>
 * 修改人 修改日期 修改描述<br>
 * -------------------------------------------<br>
 */
@Slf4j
@Service (value = "pmInstenceService")
public class PmInstenceServiceImpl extends LogicService<PmInstence,String> implements IPmInstenceService {

    private PmInstenceRepository pmInstenceRepository;

    @Autowired
    public PmInstenceServiceImpl ( PmInstenceRepository pmInstenceRepository ) {
        super( pmInstenceRepository );
        this.pmInstenceRepository = pmInstenceRepository;
    }

    /**
     * 查询主单据数据
     * @param approvalForm
     * @return
     */
    @Override
    public PmInstence getByPmInsId ( ProgramaDataForm approvalForm ) {
        return pmInstenceRepository.findByPmInsId(approvalForm.getPmInsId());
    }

    /**
     * 查询主单据数据
     * @param announcement
     * @return
     */
    @Override
    public PmInstence getByPmInsId(Announcement announcement) {
        return pmInstenceRepository.findByPmInsId(announcement.getPmInsId());
    }

    /**
     * 查询主单据数据
     * @param slideShow
     * @return
     */
    @Override
    public PmInstence getByPmInsId(SlideShow slideShow) {
        return pmInstenceRepository.findByPmInsId(slideShow.getPmInsId());
    }

    /**
     * 查询主单据数据
     * @param taskStudy
     * @return
     */
    @Override
    public PmInstence getByPmInsId(TaskStudy taskStudy) {
        return pmInstenceRepository.findByPmInsId(taskStudy.getPmInsId());
    }

    /**
     * 逻辑删除操作
     * @param id
     * @return
     */
    @Override
    public int deleteByPmId(String id) {
        return pmInstenceRepository.deleteByFromId(id);
    }

    /**
     * 获取US_PM_INSTENCE和US_APPROVAL_FORM的所有信息
     * @param bussinessKey
     * @return
     */
    @Override
    public Map<String, Object> findPmApproByBuKey ( String bussinessKey ) {
        return pmInstenceRepository.findPmApproByBuKey( bussinessKey );
    }

    /**
     * 查找主单据
     * @param pmInsId 单据ID
     * @return
     */
    @Override
    public PmInstence findByPmInsId(String pmInsId) {
        return pmInstenceRepository.findByPmInsId(pmInsId);
    }
}
