package com.simbest.boot.hnjjwz.process.query.service;

import com.simbest.boot.base.service.ILogicService;
import com.simbest.boot.bps.process.listener.model.WfWorkItemModel;

import java.util.List;
import java.util.Map;

/**
 * 用途：查询审批流程 service层
 * 作者：zhangshaofeng
 * 时间：2018/07/05
 */
public interface IQueryProcessHistoryService extends ILogicService<WfWorkItemModel,String> {

    /**
     * 查询流转过的工作项
     * @param processInstId
     * @return
     */
    List<Map<String,Object>> getWorkItems( Long processInstId);
}
