package com.simbest.boot.hnjjwz.process.query.service.impl;

import com.simbest.boot.base.repository.LogicRepository;
import com.simbest.boot.base.service.impl.LogicService;
import com.simbest.boot.bps.process.listener.model.WfWorkItemModel;
import com.simbest.boot.hnjjwz.process.query.service.IQueryProcessHistoryService;
import com.simbest.boot.util.security.SecurityUtils;
import com.simbest.boot.wf.process.service.IWorkItemService;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;

import java.util.LinkedHashMap;
import java.util.List;
import java.util.Map;

/**
 * 用途：查询审批流程
 * 作者：zhangshaofeng
 * 时间：2018/07/05
 */
@Slf4j
@Service(value = "queryProcessHistory")
public class QueryProcessHistoryImpl extends LogicService<WfWorkItemModel,String> implements IQueryProcessHistoryService {
    @Autowired
    private IWorkItemService workItemManager;

    public QueryProcessHistoryImpl(LogicRepository<WfWorkItemModel, String> logicRepository) {
        super(logicRepository);
    }

    /**
     * 查询流转过的工作项
     * @param processInstId
     * @return
     */
    @Override
    public List<Map<String,Object>> getWorkItems(Long processInstId) {
        Map map = new LinkedHashMap<>(  );
        map.put( "processInsId", processInstId);
        map.put( "currentUser", SecurityUtils.getCurrentUserName());
        return  workItemManager.queryWorkITtemDataMap( map );
    }
}
