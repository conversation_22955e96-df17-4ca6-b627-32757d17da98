
package com.cmcc.mss.osb_oa_nma_inquirymeetinginvoicesrv;

import java.util.ArrayList;
import java.util.List;
import javax.xml.bind.annotation.XmlAccessType;
import javax.xml.bind.annotation.XmlAccessorType;
import javax.xml.bind.annotation.XmlElement;
import javax.xml.bind.annotation.XmlType;


/**
 * <p>DATAS_Collection complex type的 Java 类。
 * 
 * <p>以下模式片段指定包含在此类中的预期内容。
 * 
 * <pre>
 * &lt;complexType name="DATAS_Collection"&gt;
 *   &lt;complexContent&gt;
 *     &lt;restriction base="{http://www.w3.org/2001/XMLSchema}anyType"&gt;
 *       &lt;sequence&gt;
 *         &lt;element name="DATAS_Item" type="{http://mss.cmcc.com/OSB_OA_NMA_InquiryMeetingInvoiceSrv}DATAS_Item" maxOccurs="unbounded" minOccurs="0"/&gt;
 *       &lt;/sequence&gt;
 *     &lt;/restriction&gt;
 *   &lt;/complexContent&gt;
 * &lt;/complexType&gt;
 * </pre>
 * 
 * 
 */
@XmlAccessorType(XmlAccessType.FIELD)
@XmlType(name = "DATAS_Collection", propOrder = {
    "datasItem"
})
public class DATASCollection {

    @XmlElement(name = "DATAS_Item")
    protected List<DATASItem> datasItem;

    /**
     * Gets the value of the datasItem property.
     * 
     * <p>
     * This accessor method returns a reference to the live list,
     * not a snapshot. Therefore any modification you make to the
     * returned list will be present inside the JAXB object.
     * This is why there is not a <CODE>set</CODE> method for the datasItem property.
     * 
     * <p>
     * For example, to add a new item, do as follows:
     * <pre>
     *    getDATASItem().add(newItem);
     * </pre>
     * 
     * 
     * <p>
     * Objects of the following type(s) are allowed in the list
     * {@link DATASItem }
     * 
     * 
     */
    public List<DATASItem> getDATASItem() {
        if (datasItem == null) {
            datasItem = new ArrayList<DATASItem>();
        }
        return this.datasItem;
    }

    public void setDatasItem(List<DATASItem> datasItem) {
        this.datasItem = datasItem;
    }
}
