package com.simbest.boot.hnjjwz.backstage.template.service;


import com.simbest.boot.base.service.ILogicService;
import com.simbest.boot.base.web.response.JsonResponse;
import com.simbest.boot.hnjjwz.backstage.template.model.Template;
import org.springframework.data.domain.Pageable;

import java.util.Map;

/**
 * <strong>Title : </strong><br>
 * <strong>Description : </strong><br>
 * <strong>Create on : 2018/6/19</strong><br>
 * <strong>Modify on : 2018/6/19</strong><br>
 * <strong>Copyright (C) Ltd.</strong><br>
 *
 * <AUTHOR> 刘萌@simbest.com.cn
 * @version <strong>V1.0.0</strong><br>
 * <strong>修改历史:</strong><br>
 * 修改人 修改日期 修改描述<br>
 * -------------------------------------------<br>
 */
public interface ITemplateService extends ILogicService<Template,String> {

    /**
     * 获取可以使用的模板，如果有一个则只取一个
     * @return
     */
    JsonResponse getTemplateUse();

    /**
     * 根据模板名称(模糊)、是否使用此模板(精确)
     * @param mapObject
     * @param pageable
     * @return
     */
    JsonResponse findAllDim(Map<String,Object> mapObject, Pageable pageable);
}
