package com.simbest.boot.hnjjwz.backstage.indexMenu.web;

import com.simbest.boot.base.web.controller.LogicController;
import com.simbest.boot.base.web.response.JsonResponse;
import com.simbest.boot.hnjjwz.backstage.indexMenu.model.IndexMenu;
import com.simbest.boot.hnjjwz.backstage.indexMenu.service.IIndexMenuService;
import io.swagger.annotations.Api;
import io.swagger.annotations.ApiImplicitParam;
import io.swagger.annotations.ApiImplicitParams;
import io.swagger.annotations.ApiOperation;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.data.domain.Pageable;
import org.springframework.web.bind.annotation.*;

import java.util.Map;

/**
 * <AUTHOR>
 * @Data 2019/03/29
 * @Description 首页菜单
 */
@Api(description = "首页菜单")
@Slf4j
@RestController
@RequestMapping(value = "/action/indexMenu")
public class IndexMenuController extends LogicController<IndexMenu, String> {

    @Autowired
    private IIndexMenuService indexMenuService;

    @Autowired
    public IndexMenuController(IIndexMenuService indexMenuService) {
        super(indexMenuService);
        this.indexMenuService = indexMenuService;
    }

    /**
     * 菜单名(模糊)、菜单类型(精确)有分页
     * @return
     */
    @ApiOperation (value = "菜单名(模糊)、菜单类型(精确)有分页", notes = "菜单名(模糊)、菜单类型(精确)有分页")
    @ApiImplicitParams ({ //
            @ApiImplicitParam (name = "page", value = "当前页码", dataType = "int", paramType = "query", //
                    required = true, example = "1"), //
            @ApiImplicitParam(name = "size", value = "每页数量", dataType = "int", paramType = "query", //
                    required = true, example = "10"), //
            @ApiImplicitParam(name = "direction", value = "排序规则（asc/desc）", dataType = "String", //
                    paramType = "query"), //
            @ApiImplicitParam(name = "properties", value = "排序规则（属性名称）", dataType = "String", //
                    paramType = "query")
    })
    @PostMapping (value = {"/findAllDim","/findAllDim/sso"})
    public JsonResponse findAllDim( @RequestParam (required = false, defaultValue = "1") int page, //
                                    @RequestParam(required = false, defaultValue = "10") int size, //
                                    @RequestParam(required = false) String direction, //
                                    @RequestParam(required = false) String properties,
                                    @RequestBody (required = false) Map<String,Object> mapObject ) {
        Pageable pageable = indexMenuService.getPageable(page, size, direction, properties);
        return indexMenuService.findAllDim( mapObject,pageable);
    }

    /**
     * 页面初始化时获取菜单
     * @return
     */
    @ApiOperation(value = "页面初始化时获取菜单", notes = "页面初始化时获取菜单")
    @PostMapping(value = {"/findRootAndNext","/findRootAndNext/sso"})
    public JsonResponse findRootAndNext() {

        return JsonResponse.success(indexMenuService.findRootAndNext());
    }
}
