package com.simbest.boot.hnjjwz.backstage.taskStudy.repository;/**
 * Created by KZH on 2019/6/13 9:35.
 */

import com.simbest.boot.base.repository.LogicRepository;
import com.simbest.boot.hnjjwz.backstage.taskStudy.model.TaskStudy;
import org.springframework.data.domain.Page;
import org.springframework.data.domain.Pageable;
import org.springframework.data.jpa.repository.Modifying;
import org.springframework.data.jpa.repository.Query;
import org.springframework.data.repository.query.Param;
import org.springframework.transaction.annotation.Transactional;

import java.util.List;
import java.util.Map;

/**
 * <AUTHOR>
 * @create 2019-06-13 9:35
 * @desc
 **/
public interface TaskStudyRepository  extends LogicRepository<TaskStudy,String> {

    @Transactional
    @Query(
            value = "select a.* from us_task_study a,us_pm_instence p where a.enabled = 1 and a.pm_ins_id = p.pm_ins_id and p.id=:id",
            nativeQuery = true
    )
    TaskStudy getApprovalFromDetail(@Param("id") String id);

    @Query(
            value = "select a.* from us_task_study a where a.enabled = 1 and a.pm_ins_id=:pmInstId ",
            nativeQuery = true
    )
    TaskStudy getApprovalFromDetailFromInstId(@Param("pmInstId") String pmInstId);

    @Query(
            value = "select t.*, t.rowid from US_TASK_STUDY t where t.programa_display_name=:programaDisplayName and t.enabled = 1 and t.is_publish=1 order by t.creation_time desc",
            nativeQuery = true
    )
    List<TaskStudy> findTaskStudy(@Param("programaDisplayName")String programaDisplayName);

  /*  *//**
     * 查询当前登录人起草审批的的报送
     * @param trueName
     * @param pageable
     * @return
     *//*
    @Query(
            value = "select t.*, vuoo.displayname as belongDepartmentDisplayName" +
                    "  from US_TASK_STUDY t, uums.v_org vuoo" +
                    " WHERE t.username = :trueName" +
                    "   AND t.belong_department_code = vuoo.orgcode" +
                    "   AND t.enabled = 1 order by t.creation_time desc",
            countQuery = "select count(*)" +
                    "  from US_TASK_STUDY t, uums.v_org vuoo" +
                    " WHERE t.username = :trueName" +
                    "   AND t.belong_department_code = vuoo.orgcode" +
                    "   AND t.enabled = 1 order by t.creation_time desc",
            nativeQuery = true
    )
    Page<Map<String,Object>> findUserNameDetailList(@Param ("trueName")String trueName, Pageable pageable);*/


    /**
     * 查看栏目列表，选择了栏目时
     * @param title
     * @param pageable
     * @return
     */
    @Query (value =  " select pdi.*" +
            " from US_TASK_STUDY pdi" +
            " WHERE pdi.task_study_title LIKE concat( concat('%',:title),'%') " +
            " AND pdi.enabled=1 " +
            " AND pdi.removed_time IS NULL " +
            " AND pdi.is_publish = 1 order by pdi.creation_time desc",
            countQuery = "SELECT count(*) " +
                    " from US_TASK_STUDY pdi" +
                    " WHERE  pdi.task_study_title LIKE concat( concat('%',:title),'%') " +
                    " AND pdi.enabled=1 AND pdi.removed_time IS NULL " +
                    " AND pdi.is_publish = 1 order by pdi.creation_time desc",
            nativeQuery = true)
    Page<Map<String,Object>> findDataDetailList(@Param ( "title" ) String title, Pageable pageable );


    /**
     * 查看栏目列表
     * @param pageable
     * @return
     */
    @Query (value =  " select pdi.*" +
            " from US_TASK_STUDY pdi" +
            " WHERE pdi.enabled=1 AND pdi.removed_time IS NULL " +
            "AND pdi.is_publish = 1 order by pdi.creation_time desc",
            countQuery = "SELECT count(*) " +
                    " from US_TASK_STUDY pdi  " +
                    " WHERE pdi.enabled=1 AND pdi.removed_time IS NULL " +
                    "AND pdi.is_publish = 1 order by pdi.creation_time desc",
            nativeQuery = true)
    Page<Map<String,Object>> findDataDetailListNoCode( Pageable pageable );

/*    *//**
     * 查询当前登录人审批过的的报送
     * @param trueName
     * @param pageable
     * @return
     *//*
    @Query(
            value = "select t.*, vuoo.displayname as belongDepartmentDisplayName" +
                    "  from US_TASK_STUDY t, uums.v_org vuoo, WF_WORKITEM_MODEL wf  " +
                    "  where  wf.assistant= :trueName" +
                    "  and t.pm_ins_id=wf.receipt_code"+
                    "  and t.belong_department_code = vuoo.orgcode" +
                    "  and t.enabled = 1 ",
            countQuery = "select count(*)" +
                    "  from US_TASK_STUDY t, uums.v_org vuoo, WF_WORKITEM_MODEL wf  " +
                    "  where  wf.assistant= :trueName"+
                    "  and t.pm_ins_id=wf.receipt_code"+
                    "  and t.belong_department_code = vuoo.orgcode"+
                    "  and t.enabled = 1 ",
            nativeQuery = true
    )
    Page<Map<String,Object>> findUserNameApprovalDetailList(@Param ("trueName")String trueName,Pageable pageable);*/

    @Transactional
    @Modifying
    @Query(value = "update US_TASK_STUDY set enabled=0 where   pm_ins_id=:pmInsId",
            nativeQuery = true)
    void deleteByPmInsId(@Param ( "pmInsId" )String pmInsId);

}
