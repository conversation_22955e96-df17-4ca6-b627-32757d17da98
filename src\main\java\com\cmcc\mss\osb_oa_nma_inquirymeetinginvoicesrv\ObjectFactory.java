
package com.cmcc.mss.osb_oa_nma_inquirymeetinginvoicesrv;

import javax.xml.bind.JAXBElement;
import javax.xml.bind.annotation.XmlElementDecl;
import javax.xml.bind.annotation.XmlRegistry;
import javax.xml.namespace.QName;


/**
 * This object contains factory methods for each 
 * Java content interface and Java element interface 
 * generated in the com.cmcc.mss.osb_oa_nma_inquirymeetinginvoicesrv package. 
 * <p>An ObjectFactory allows you to programatically 
 * construct new instances of the Java representation 
 * for XML content. The Java representation of XML 
 * content can consist of schema derived interfaces 
 * and classes representing the binding of schema 
 * type definitions, element declarations and model 
 * groups.  Factory methods for each of these are 
 * provided in this class.
 * 
 */
@XmlRegistry
public class ObjectFactory {

    private final static QName _OSBOANMAInquiryMeetingInvoiceSrvRequest_QNAME = new QName("http://mss.cmcc.com/OSB_OA_NMA_InquiryMeetingInvoiceSrv", "OSB_OA_NMA_InquiryMeetingInvoiceSrvRequest");
    private final static QName _OSBOANMAInquiryMeetingInvoiceSrvResponse_QNAME = new QName("http://mss.cmcc.com/OSB_OA_NMA_InquiryMeetingInvoiceSrv", "OSB_OA_NMA_InquiryMeetingInvoiceSrvResponse");

    /**
     * Create a new ObjectFactory that can be used to create new instances of schema derived classes for package: com.cmcc.mss.osb_oa_nma_inquirymeetinginvoicesrv
     * 
     */
    public ObjectFactory() {
    }

    /**
     * Create an instance of {@link OSBOANMAInquiryMeetingInvoiceSrvRequest }
     * 
     */
    public OSBOANMAInquiryMeetingInvoiceSrvRequest createOSBOANMAInquiryMeetingInvoiceSrvRequest() {
        return new OSBOANMAInquiryMeetingInvoiceSrvRequest();
    }

    /**
     * Create an instance of {@link OSBOANMAInquiryMeetingInvoiceSrvResponse }
     * 
     */
    public OSBOANMAInquiryMeetingInvoiceSrvResponse createOSBOANMAInquiryMeetingInvoiceSrvResponse() {
        return new OSBOANMAInquiryMeetingInvoiceSrvResponse();
    }

    /**
     * Create an instance of {@link OSBOANMAInquiryMeetingInvoiceSrvOutputCollection }
     * 
     */
    public OSBOANMAInquiryMeetingInvoiceSrvOutputCollection createOSBOANMAInquiryMeetingInvoiceSrvOutputCollection() {
        return new OSBOANMAInquiryMeetingInvoiceSrvOutputCollection();
    }

    /**
     * Create an instance of {@link OSBOANMAInquiryMeetingInvoiceSrvOutputItem }
     * 
     */
    public OSBOANMAInquiryMeetingInvoiceSrvOutputItem createOSBOANMAInquiryMeetingInvoiceSrvOutputItem() {
        return new OSBOANMAInquiryMeetingInvoiceSrvOutputItem();
    }

    /**
     * Create an instance of {@link DATASCollection }
     * 
     */
    public DATASCollection createDATASCollection() {
        return new DATASCollection();
    }

    /**
     * Create an instance of {@link DATASItem }
     * 
     */
    public DATASItem createDATASItem() {
        return new DATASItem();
    }

    /**
     * Create an instance of {@link JAXBElement }{@code <}{@link OSBOANMAInquiryMeetingInvoiceSrvRequest }{@code >}}
     * 
     */
    @XmlElementDecl(namespace = "http://mss.cmcc.com/OSB_OA_NMA_InquiryMeetingInvoiceSrv", name = "OSB_OA_NMA_InquiryMeetingInvoiceSrvRequest")
    public JAXBElement<OSBOANMAInquiryMeetingInvoiceSrvRequest> createOSBOANMAInquiryMeetingInvoiceSrvRequest(OSBOANMAInquiryMeetingInvoiceSrvRequest value) {
        return new JAXBElement<OSBOANMAInquiryMeetingInvoiceSrvRequest>(_OSBOANMAInquiryMeetingInvoiceSrvRequest_QNAME, OSBOANMAInquiryMeetingInvoiceSrvRequest.class, null, value);
    }

    /**
     * Create an instance of {@link JAXBElement }{@code <}{@link OSBOANMAInquiryMeetingInvoiceSrvResponse }{@code >}}
     * 
     */
    @XmlElementDecl(namespace = "http://mss.cmcc.com/OSB_OA_NMA_InquiryMeetingInvoiceSrv", name = "OSB_OA_NMA_InquiryMeetingInvoiceSrvResponse")
    public JAXBElement<OSBOANMAInquiryMeetingInvoiceSrvResponse> createOSBOANMAInquiryMeetingInvoiceSrvResponse(OSBOANMAInquiryMeetingInvoiceSrvResponse value) {
        return new JAXBElement<OSBOANMAInquiryMeetingInvoiceSrvResponse>(_OSBOANMAInquiryMeetingInvoiceSrvResponse_QNAME, OSBOANMAInquiryMeetingInvoiceSrvResponse.class, null, value);
    }

}
