package com.simbest.boot.hnjjwz.backstage.template.service.impl;

import com.simbest.boot.base.service.impl.LogicService;
import com.simbest.boot.base.web.response.JsonResponse;
import com.simbest.boot.hnjjwz.backstage.template.model.TemplatePrograma;
import com.simbest.boot.hnjjwz.backstage.template.repository.TemplateProgramaRepository;
import com.simbest.boot.hnjjwz.backstage.template.service.ITemplateProgramaService;
import com.simbest.boot.hnjjwz.constants.Constants;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.data.domain.Page;
import org.springframework.data.domain.Pageable;
import org.springframework.stereotype.Service;

import java.util.Map;

/**
 * <AUTHOR>
 * @Data 2019/05/06
 * @Description
 */
@Slf4j
@Service
public class TemplateProgramaServiceImpl extends LogicService<TemplatePrograma,String> implements ITemplateProgramaService {

    private TemplateProgramaRepository templateProgramaRepository;

    @Autowired
    public TemplateProgramaServiceImpl ( TemplateProgramaRepository templateProgramaRepository) {
        super(templateProgramaRepository);
        this.templateProgramaRepository = templateProgramaRepository;
    }

    /**
     * 根据模板名称(模糊)、栏目编码(精确)、查询模板与栏目的列表并分页
     * @param mapObject
     * @param pageable
     * @return
     */
    @Override
    public JsonResponse findAllDim ( Map<String, Object> mapObject, Pageable pageable ) {
        String templateName = (String) mapObject.get( Constants.TEMPLATE_NAME_KEY );
        String programaCode = (String) mapObject.get( Constants.PROGRAMA_CODE_KEY );
        Page<Map<String,Object>> map = templateProgramaRepository.findAllDim( templateName,  programaCode, pageable );
        return JsonResponse.success( map );
    }

    /**
     * 根据id查询模板与栏目的关联
     * @param id
     * @return
     */
    @Override
    public JsonResponse findByIdDim ( String id ) {
        Map<String,Object> map = templateProgramaRepository.findByIdDim(id);
        return JsonResponse.success( map );
    }
}
