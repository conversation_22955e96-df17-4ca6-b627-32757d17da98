package com.simbest.boot.hnjjwz.backstage.indexMenu.model;/**
 * Created by KZH on 2019/5/30 16:47.
 */

import com.simbest.boot.base.annotations.EntityIdPrefix;
import com.simbest.boot.base.model.LogicModel;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.*;
import org.hibernate.annotations.GenericGenerator;

import javax.persistence.Column;
import javax.persistence.Entity;
import javax.persistence.GeneratedValue;
import javax.persistence.Id;

/**
 * <AUTHOR>
 * @create 2019-05-30 16:47
 * @desc 子菜单
 **/
@Data
@EqualsAndHashCode(callSuper = true)
@NoArgsConstructor
@AllArgsConstructor
@Entity(name = "us_submenu_menu")
@ApiModel(value = "子菜单")
public class SubmenuMenu extends LogicModel {
    @Id
    @Column(name = "id", length = 40)
    @GeneratedValue(generator = "snowFlakeId")
    @GenericGenerator(name = "snowFlakeId", strategy = "com.simbest.boot.util.distribution.id.SnowflakeId")
    @EntityIdPrefix(prefix = "SM") //主键前缀，此为可选项注解
    private String id;

    @Column(length = 250)
    @Setter
    @Getter
    @ApiModelProperty(value = "父菜单名", required = true)
    private String menuName;

    @Column(length = 250)
    @Setter
    @Getter
    @ApiModelProperty(value = "子菜单名", required = true)
    private String submenuName;

    @Column(length = 250)
    @Setter
    @Getter
    @ApiModelProperty(value = "指向内容Id", required = true)
    private String pmInsId;

    @Column(length = 250)
    @Setter
    @Getter
    @ApiModelProperty(value = "连接地址", required = true)
    private String submenuUrl;
}
