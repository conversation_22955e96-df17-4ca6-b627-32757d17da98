package com.simbest.boot.hnjjwz.backstage.templateLayout.model;/**
 * Created by KZH on 2019/5/30 17:11.
 */

import com.simbest.boot.base.annotations.EntityIdPrefix;
import com.simbest.boot.base.model.LogicModel;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.*;
import org.hibernate.annotations.GenericGenerator;

import javax.persistence.*;
import java.util.ArrayList;
import java.util.HashMap;
import java.util.List;
import java.util.Map;

/**
 * <AUTHOR>
 * @create 2019-05-30 17:11
 * @desc 模板布局
 **/
@Data
@EqualsAndHashCode(callSuper = true)
@NoArgsConstructor
@AllArgsConstructor
@Entity(name = "us_template_layout")
@ApiModel(value = "模板布局")
public class TemplateLayout  extends LogicModel {

    @Id
    @Column(name = "id", length = 40)
    @GeneratedValue(generator = "snowFlakeId")
    @GenericGenerator(name = "snowFlakeId", strategy = "com.simbest.boot.util.distribution.id.SnowflakeId")
    @EntityIdPrefix(prefix = "TL") //主键前缀，此为可选项注解
    private String id;

    @Column(length = 250)
    @Setter
    @Getter
    @ApiModelProperty(value = "模板类型", required = true)
    private String templateType;

    @Column(length = 250)
    @Setter
    @Getter
    @ApiModelProperty(value = "模板位置", required = true)
    private String templateLocation;

    @Column(length = 250)
    @Setter
    @Getter
    @ApiModelProperty(value = "位置类型", required = true)
    private String locationType;

    @Column(length = 250)
    @Setter
    @Getter
    @ApiModelProperty(value = "位置名称Id", required = true)
    private String locationId;

    @Column(length = 250)
    @Setter
    @Getter
    @ApiModelProperty(value = "位置名称", required = true)
    private String locationName;

    @Transient
    private Object templateData=new Object();//接口数据

    @Column(length = 250)
    @Setter
    @Getter
    @ApiModelProperty(value = "图片地址", required = true)
    private String templateUrl;

    @Column(length = 250)
    @Setter
    @Getter
    @ApiModelProperty(value = "指向地址", required = true)
    private String pointUrl;

    @Column(length = 250)
    @Setter
    @Getter
    @ApiModelProperty(value = "位置编码", required = true)
    private String templateRows;

}
