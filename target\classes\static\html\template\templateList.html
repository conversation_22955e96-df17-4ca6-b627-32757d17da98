<!DOCTYPE html>
<html xmlns="http://www.w3.org/1999/xhtml"
      xmlns:th="http://www.thymeleaf.org">
<head>
    <title>模板管理</title>
    <meta http-equiv="Content-Type" content="text/html; charset=utf-8"/>
    <meta http-equiv="X-UA-Compatible" content="IE=edge" />
    <meta name="viewport" content="width=device-width, initial-scale=1, shrink-to-fit=no"/>
    <!--
       <link href="http://************:8088/simbestui/fonts/iconfont/iconfont.css?v=svn.revision" th:href="@{http://************:8088/simbestui/fonts/iconfont/iconfont.css?v=svn.revision}" rel="stylesheet"/>
       -->
    <link href="../../fonts/iconfont/iconfont.css?v=svn.revision" th:href="@{/fonts/iconfont/iconfont.css?v=svn.revision}" rel="stylesheet"/>
    <link href="http://************:8088/simbestui/js/themes/default/easyui.css?v=svn.revision" th:href="@{http://************:8088/simbestui/js/themes/default/easyui.css?v=svn.revision}" rel="stylesheet"/>
    <link href="http://************:8088/simbestui/js/themes/icon.css?v=svn.revision" th:href="@{http://************:8088/simbestui/js/themes/icon.css?v=svn.revision}" rel="stylesheet"/>
    <link href="http://************:8088/simbestui/css/public.css?v=svn.revision" th:href="@{http://************:8088/simbestui/css/public.css?v=svn.revision}" rel="stylesheet"/>
    <link href="http://************:8088/simbestui/css/pages.css?v=svn.revision" th:href="@{http://************:8088/simbestui/css/pages.css?v=svn.revision}" rel="stylesheet"/>
    <script src="http://************:8088/simbestui/js/jquery.min.js?v=svn.revision" th:src="@{http://************:8088/simbestui/js/jquery.min.js?v=svn.revision}" type="text/javascript"></script>
    <script src="http://************:8088/simbestui/js/jquery.easyui.min.js?v=svn.revision" th:src="@{http://************:8088/simbestui/js/jquery.easyui.min.js?v=svn.revision}" type="text/javascript"></script>
    <script src="http://************:8088/simbestui/js/easyui-lang-zh_CN.js?v=svn.revision" th:src="@{http://************:8088/simbestui/js/easyui-lang-zh_CN.js?v=svn.revision}" type="text/javascript"></script>
    <script src="../../js/jquery.config.js?v=svn.revision" th:src="@{/js/jquery.config.js?v=svn.revision}" type="text/javascript"></script>
    <script src="http://************:8088/simbestui/js/jquery.zjsfile.js?v=svn.revision" th:src="@{http://************:8088/simbestui/js/jquery.zjsfile.js?v=svn.revision}" type="text/javascript"></script>
    <script src="http://************:8088/simbestui/js/jquery.aduq.js?v=svn.revision" th:src="@{http://************:8088/simbestui/js/jquery.aduq.js?v=svn.revision}" type="text/javascript"></script>
    <script src="http://************:8088/simbestui/js/jquery.process.js?v=svn.revision" th:src="@{http://************:8088/simbestui/js/jquery.process.js?v=svn.revision}" type="text/javascript"></script>
    <script type="text/javascript">
        $(function(){
            //showDialog做为打开对话框的公共class名称   showDialogindex做为打开修改对话框传行的索引   delete做为删除按钮配置命令   deleteid做为删除传值  searchtable做为条件查询的按钮公共class名称
            var pageparam={
                "listtable":{
                    "listname":"#templateTable",//table列表的id名称，需加# programaClassifyTable
                    "querycmd":"action/templateLayout/findAll",//table列表的查询命令
                    "checkboxall":true,
                    "contentType":"application/json; charset=utf-8",//table列表的传参方式，如果是json就写，不写默认kv
                    "nowrap": true,//把数据显示在一行里,默认true
                    "styleClass": "noScroll",
                    "frozenColumns":[[
                        { field: "ck",checkbox:true}
                    ]],//固定在左侧的列
                    "columns":[[//列
                        { title: "模板类型", field: "templateType", width: 120},
                        { title: "模板位置", field: "templateLocation", width: 120},
                        { title: "位置编码", field: "templateRows", width: 120},
                        { title: "位置名称Id", field: "locationId", width: 120},
                        { title: "位置类型", field: "locationType", width: 120},
                        { title: "位置名称", field: "locationName", width: 120},
                        { title: "图片地址", field: "templateUrl", width: 120},
                        { title: "指向地址", field: "pointUrl", width: 120},

                        {
                            field: "opt", title: "操作", width: 150, rowspan: 1,//align：对齐此列的数据，可以用left、right、center
                            formatter: function (value, row,index) {//单元格的格式化函数，需要三个参数：value：字段的值；rowData：行的记录数据；rowIndex：行的索引
                                var g = "<a href='#' class='showDialog'  showDialogindex='" + index + "'>【修改】</a>"
                                    +"<a href='#' delete='action/template/deleteById' deleteid='"+row.id+"'>【删除】</a>";
                                /*g=g+"<a class='templateUse' ptitle='设置模板可用' >【应用配置】</a>";*/
                                g=g+"<a href='#' delete='action/template/setTemplateUse' deleteid='"+row.id+"' >【设置模板可用】</a>";
                                return g;
                            }
                        }
                    ] ],
                    "pagerbar": [{
                        id:"deleteall",
                        iconCls: 'icon-remove',
                        text:"批量删除&nbsp;"
                    }],
                    "deleteall":{//批量删除deleteall.id要与pagerbar.id相同
                        "id":"deleteall",
                        "url":"action/template/deleteAllByIds",
                        "contentType":"application/json; charset=utf-8"
                    }
                },
                "dialogform":{
                    "dialogid":"#buttons",//对话框的id
                    "formname":"#templateTableAddForm",//新增或修改对话框的formid需加#
                    "insertcmd":"action/template/create",//新增命令
                    "updatacmd":"action/template/update",//修改命令
                    "onSubmit":function(data){
                        if($("#id").val()=="") data.displayOrder="1";
                        if($("#roleCode").attr("codeError")){
                            top.mesAlert("提示信息","角色编码已存在,请重新输入！", 'error');
                            return false;
                        }
                        return true;
                    }
                }
            };
            loadGrid(pageparam);
            //应用配置
            $(document).on("click",".templateUse",function(){

                ajaxgeneral({
                    url:"action/template/setTemplateUse",
                    data:{"id":id},
                    success:function(datas){
                        (top.window["auditF"] || top).window.formLoad();
                        top.dialogClose("opDetailRead");
                    }
                });
            });
        });
        //form表单填充数据之前做什么,data为填充数据的集合,isupdate表示是新增还是修改,若为新增则data为{}
        function beforerender(data,isupdate){
            if(isupdate){
                $('.update-readonly').hide();
            }else{
                $('.update-readonly').show();
            }
        };
    </script>
</head>
<style>
    .formTable { width: 100%; margin-top: 30px; border-spacing: 0; border-top: 1px solid #dedede; border-left: 1px solid #dedede; }
    .formTable>tbody>tr:not(:first-child)>td { border-right: 1px solid #dedede; border-bottom: 1px solid #dedede; font-size: 14px; height: 36px; }
    .formTable>tbody>tr>td input,.formTable>tbody>tr>td span,.formTable>tbody>tr>td textarea,.formTable>tbody>tr>td .textbox .textbox-text{ border: none; font-size: 14px; }
    .formTable td.lable{ background-color: #ddf1fe; padding: 5px; text-align: left; }
    .formTable td .textAndInput_readonly, .formTable td .textAndInput_readonly .validatebox-readonly{ background-color: #fff; }
    .cselectorImageUL .btn{ right: 3px; top: 3px; }
    .cselectorImageUL input[type='file']{ right: 25px; top: 3px; }
    textarea{ line-height: 20px; letter-spacing: 1px; }
    table.tabForm{ border-color: #dedede; }
</style>

<body class="body_page">
<!--searchform-->
<form id="templateTableQueryForm">
    <table border="0" cellpadding="0" cellspacing="6" width="100%">
        <tr>
            <td class="lable">模板名称：</td><td width="150"><input name="templateName" type="text" value="" /></td>
            <td width="150" align="right">是否使用的模板</td>
            <td width="150">
                <select class="easyui-combobox" id="searchTemplateUse" name="templateUse" style="width:100%; height:32px;">
                    <option selected value="">-请选择-</option>
                    <option  value="true">是</option>
                    <option  value="false">否</option>
                </select>
            </td>
            <td>
                <div class="w100">
                    <a class="btn a_primary fr searchtable"><span>查询</span></a>   
					<a class="btn a_success showDialog fr mr10"><span>新增</span></a>
                </div>
            </td>
        </tr>
    </table>
</form>
<!--table-->
<div class="templateTable"><table id="templateTable"></table></div>
<!--dialog-->
<div id="buttons" title="新增或修改" class="easyui-dialog" style="width:800px;height:500px;">
    <form id="templateTableAddForm" method="post" contentType="application/json; charset=utf-8"  beforerender="beforerender()">
        <input id="id" name="id" type="hidden"/>
        <table border="0" style="width: 100%;" class="tabForm formTable" cellpadding="0" cellspacing="10">
            <tr>
                <td width="20%"></td>
                <td width="80%"></td>
            </tr>
            <tr >
            <td class="lable">模板类型<font class="col_r">*</font></td>
            <td>
                <input id="templateType" name="templateType" type="text" class="easyui-validatebox" required='required' />
            </td>
            </tr>
            <tr>
                <td class="lable">模板位置<font class="col_r">*</font></td>
                <td>
                    <input id="templateLocation" name="templateLocation" type="text" class="easyui-validatebox" required='required' />
                </td>
            </tr>
            <tr>
                <td class="lable">位置编码<font class="col_r">*</font></td>
                <td>
                    <input id="templateRows" name="templateRows" type="text" class="easyui-validatebox" required='required' />
                </td>

            </tr>
            <tr >
                <td class="lable">位置名称Id</td>
                <td>
                    <input id="locationId" name="locationId" type="text" class="easyui-validatebox" required='required' />
                </td>

            </tr>
            <tr>
                <td class="lable">位置类型<font class="col_r">*</font></td>
                <td>
                    <input id="locationType" name="locationType" type="text" class="easyui-validatebox" required='required' />
                </td>

            </tr>
            <tr>
                <td class="lable">位置名称<font class="col_r">*</font></td>
                <td>
                    <input id="locationName" name="locationName" type="text" class="easyui-validatebox" required='required' />
                </td>

            </tr>
            <tr>
                <td class="lable">接口地址</td>
                <td>
                    <input id="templateUrl" name="templateUrl" type="text" class="easyui-validatebox"  />
                </td>

            </tr>
            <tr>
                <td class="lable">指向地址</td>
                <td>
                    <input id="pointUrl" name="pointUrl" type="text" class="easyui-validatebox"  />
                </td>

            </tr>

        </table>
    </form>
</div>
</body>
</html>
