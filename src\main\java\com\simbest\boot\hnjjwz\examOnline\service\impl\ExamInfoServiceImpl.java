package com.simbest.boot.hnjjwz.examOnline.service.impl;

import com.simbest.boot.base.exception.Exceptions;
import com.simbest.boot.base.service.impl.LogicService;
import com.simbest.boot.base.web.response.JsonResponse;
import com.simbest.boot.hnjjwz.examOnline.model.*;
import com.simbest.boot.hnjjwz.examOnline.repository.ExamInfoRepository;
import com.simbest.boot.hnjjwz.examOnline.service.*;
import com.simbest.boot.security.IUser;
import com.simbest.boot.util.DateUtil;
import com.simbest.boot.util.distribution.id.RedisIdGenerator;
import com.simbest.boot.util.json.JacksonUtils;
import com.simbest.boot.util.security.SecurityUtils;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;
import springfox.documentation.spring.web.json.Json;

import org.springframework.data.domain.Pageable;
import java.time.LocalDateTime;
import java.util.*;
import java.util.stream.Collectors;

/**
 * <AUTHOR>
 * @Data 2019/05/09
 * @Description 试卷
 */
@Slf4j
@Service
public class ExamInfoServiceImpl extends LogicService<ExamInfo,String> implements IExamInfoService {

    private ExamInfoRepository examInfoRepository;

    @Autowired
    public ExamInfoServiceImpl ( ExamInfoRepository examInfoRepository) {
        super(examInfoRepository);
        this.examInfoRepository = examInfoRepository;
    }

    @Autowired
    private IExamQuestionResultService examQuestionResultService;

    @Autowired
    private RedisIdGenerator idGenerator;

    @Autowired
    private IExamInfoService examInfoService;

    @Autowired
    private IExamQuestionService examQuestionService;

    @Autowired
    private IExamAttributeService iExamAttributeService;

    @Autowired
    private IExamAnswerService iExamAnswerService;

    @Autowired
    private IExamQuestionAnswerService examQuestionAnswerService;

    @Autowired
    private  IExamQuestionResultService iExamQuestionResultService;

    /**
     * 查询保存后的试卷内容
     * @param onlyRecord
     * @return
     */
    @Override
    public JsonResponse findSaveQuestion ( String onlyRecord ) {
        Set<Map<String,Object>> mapSet = examInfoRepository.findSaveQuestion(onlyRecord);
        return JsonResponse.success( mapSet );
    }

    /**
     * 保存试卷
     * @return
     */
    @Override
    public JsonResponse saveExamInfo ( ExamInfo examInfo ) {
        if(examInfo == null){
            return JsonResponse.fail( null ,"提交的试卷内容为空！" );
        }
        try{
            saveExamInfoNormal( examInfo);
        }catch ( Exception e ){
            Exceptions.printException( e );
            return JsonResponse.fail( null,"保存试卷不成功!" );
        }
        return JsonResponse.success( null,"报错试卷成功!" );
    }

    private void saveExamInfoNormal(ExamInfo examInfo) throws Exception{
        //todo 要考虑一个东西，就是页面上一直会进行倒计时。
        //todo 会有个定时保存试卷，放在前端来完成，定时去调接口。
        ExamQuestionResult examQuestionResultNew = new ExamQuestionResult();
        LocalDateTime localDateTimeCreated = examInfo.getCreatedTime();
        Date dateCreated = DateUtil.localDateTime2Date( localDateTimeCreated);
        LocalDateTime localDateTimeNow = LocalDateTime.now();
        Date dateNow = DateUtil.localDateTime2Date( localDateTimeNow);
        long differenceMillions = DateUtil.secondBetweenDates(dateCreated, dateNow);
        examInfo.setLeftTime( differenceMillions );
        examInfoService.insert( examInfo );
        List<ExamQuestionResult> examQuestionResultList = examInfo.getExamQuestionResultList();
        for(ExamQuestionResult examQuestionResult:examQuestionResultList){
            //examQuestionResultNew.setOnlyRecord( examInfo.getOnlyRecord() );
            examQuestionResultNew.setQuestionBankCode( examQuestionResult.getQuestionBankCode() );
            examQuestionResultNew.setQuestionCode( examQuestionResult.getQuestionCode() );
            examQuestionResultNew.setAnswerCode( examQuestionResult.getAnswerCode() );
            examQuestionResultNew.setQuestionValue( examQuestionResult.getQuestionValue() );
            examQuestionResultService.insert( examQuestionResultNew );
        }
    }

    /**
     * 进行评分
     * @param examInfo
     * @return
     */
    @Override
    public JsonResponse gradExam ( ExamInfo examInfo ) {
        if(examInfo==null){
            JsonResponse.fail( null,"评分出错！" );
        }
        int totalScore = 0;
        List<ExamQuestionResult> examQuestionResultList = examInfo.getExamQuestionResultList();
        for(ExamQuestionResult examQuestionResult:examQuestionResultList){
            if(examQuestionResult!=null){
                Integer questionValue = examQuestionResult.getQuestionValue();
                if( examQuestionResult.getIsCorrect()){
                    //去获取
                    totalScore += questionValue;
                }
            }
        }
        examInfo.setIsComplete( true );
        examInfo.setExamScore( totalScore );
        try{
            insert( examInfo );
            saveExamInfoNormal( examInfo);
        }catch ( Exception e ){
            Exceptions.printException( e );
            return JsonResponse.fail( null,"评分出错！" );
        }
        return JsonResponse.success( totalScore );
    }

    /**
     * 生成试卷
     * @return
     */
    @Override
    public Long createExamInfo() throws Exception{
        ExamInfo examInfo = new ExamInfo(  );
        Long onlyRecord = idGenerator.getDateId();
        examInfo.setIsComplete( false );
        //examInfo.setOnlyRecord( onlyRecord );
        try{
            examInfoService.insert(examInfo);
        }catch ( Exception e ){
            throw e;
        }
        return onlyRecord;
    }

    @Override
    public JsonResponse randomExam(String questionBankCode) {
        ExamAttribute examAttributeList = iExamAttributeService.findExamAttribute(questionBankCode);

            List<ExamQuestion> examQuestionsMoreList = examQuestionService.customFindAll(questionBankCode,examAttributeList.getMoreChoice(),"单选题");//根据单选比例获取题目
            List<ExamQuestion> examQuestionsSingleList = examQuestionService.customFindAll(questionBankCode,examAttributeList.getSingleChoice(),"多选题");//根据多选比例获取题目


        for(ExamQuestion examQuestionsMore:examQuestionsMoreList){
                examQuestionsMore.setExamAnswerList(iExamAnswerService.customFindExamAnswer(examQuestionsMore.getQuestionCode()));
            }
        for(ExamQuestion examQuestionsSingle:examQuestionsSingleList){
                examQuestionsSingle.setExamAnswerList(iExamAnswerService.customFindExamAnswer(examQuestionsSingle.getQuestionCode()));

            }
            examAttributeList.setExamName("2019纪检网站在线测试");
            examAttributeList.setExamMoreData(examQuestionsMoreList);
            examAttributeList.setExamSingleData(examQuestionsSingleList);

        /*Iterable<ExamQuestion> allNoPage = examQuestionService.findAllNoPage();
        allNoPage.forEach(examQuestion -> {

        });*/

        return JsonResponse.success(examAttributeList);
    }

    @Override
    public JsonResponse evaluatingExam(Map<Object, List<Object>> map) {

        Map sumMap = JacksonUtils.json2obj(String.valueOf(map.get("map")), Map.class);

        List<String> correctList=new ArrayList<>();//答对的

        List<ExamQuestionResult> examQuestionResultList=new ArrayList<>();
        for (Object key : sumMap.keySet()) {
            List<String> allList=new ArrayList<>();
            //log.debug(String.valueOf(key));

            //ExamQuestionResult examQuestionResult=new ExamQuestionResult();

            List<String> list=(List<String>)sumMap.get(key);

            List<ExamQuestionAnswer> ExamQuestionAnswer = examQuestionAnswerService.findExamAnswerQuestionCode(String.valueOf(key));
            int i=0;
            ExamQuestionResult examQuestionResult=new ExamQuestionResult();
            for(String str :list){
                examQuestionResult.setQuestionBankCode(String.valueOf(map.get("questionBankCode")));
                examQuestionResult.setQuestionCode(String.valueOf(key));
                examQuestionResult.setPublishName(String.valueOf(map.get("name")));
                examQuestionResult.setPublishOrgName(String.valueOf(map.get("department")));

                switch (ExamQuestionAnswer.size()){
                    case 1:
                        for(ExamQuestionAnswer examQuestionAnswer:ExamQuestionAnswer){
                            if(str.contains(examQuestionAnswer.getAnswerCode())){
                                correctList.add(String.valueOf(key));
                            }
                        }
                        break;
                    case 2:
                        for(ExamQuestionAnswer examQuestionAnswer:ExamQuestionAnswer){
                            if(str.contains(examQuestionAnswer.getAnswerCode())){
                                i++;
                            }
                            if(i>=2){
                                correctList.add(String.valueOf(key));
                            }
                        }
                        break;
                    case 3:
                        for(ExamQuestionAnswer examQuestionAnswer:ExamQuestionAnswer){
                            if(str.contains(examQuestionAnswer.getAnswerCode())){
                                i++;
                            }
                            if(i>=3){
                                correctList.add(String.valueOf(key));
                            }
                        }
                        break;
                    case 4:
                        for(ExamQuestionAnswer examQuestionAnswer:ExamQuestionAnswer){
                            if(str.contains(examQuestionAnswer.getAnswerCode())){
                                i++;
                            }
                            if(i>=4){
                                correctList.add(String.valueOf(key));
                            }
                        }
                }
                allList.add(str);
                examQuestionResult.setAnswerCode(String.valueOf(allList));
            }
            examQuestionResultList.add(examQuestionResult);
        }
        List newList=correctList.stream().distinct().collect(Collectors.toList());//java 8新特性List去重
        int s= newList.size()*2;
        ExamInfo examInfo=new ExamInfo();
        examInfo.setExamScore(s);
        examInfo.setIsComplete(true);
        examInfo.setLeftTime(Long.parseLong(String.valueOf(map.get("leftTime"))));
        examInfo.setPublishName(String.valueOf(map.get("name")));
        this.insert(examInfo);

        iExamQuestionResultService.saveAll(examQuestionResultList);
        //System.out.println(examInfo.toString());
        //System.out.println(newList.toString());
        //System.out.println(examQuestionResultList.toString());
        return JsonResponse.success( 1,"试卷提交完成,你的成绩为:"+s+"详情在在线测试个人信息页面" );
    }

    @Override
    public JsonResponse findOneself(Pageable pageable) {
        IUser currentUser = SecurityUtils.getCurrentUser();
        return JsonResponse.success(examInfoRepository.findOneself(currentUser.getTruename(),pageable));
    }
}
