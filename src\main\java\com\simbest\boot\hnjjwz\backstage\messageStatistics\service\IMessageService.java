package com.simbest.boot.hnjjwz.backstage.messageStatistics.service;/**
 * Created by KZH on 2019/7/1 10:16.
 */

import com.simbest.boot.base.service.ILogicService;
import com.simbest.boot.base.web.response.JsonResponse;
import com.simbest.boot.hnjjwz.backstage.messageStatistics.model.Message;
import org.springframework.data.domain.Pageable;

import javax.servlet.http.HttpServletRequest;
import javax.servlet.http.HttpServletResponse;
import java.util.List;
import java.util.Map;

/**
 * <AUTHOR>
 * @create 2019-07-01 10:16
 * @desc
 **/
public interface IMessageService extends ILogicService<Message,String> {

    /**
     * 根据id获取数据字典值
     * @param dictType
     * @return
     */
    JsonResponse findDictValue(String dictType);

    /**
     * 信息统计
     * @return
     */
    List<Map<String, Object>> findMessageStatistics(Map<String,String> mapObject);

    /**
     * 导出Excel
     * @return
     */
    void exportNDExcel(HttpServletRequest request, HttpServletResponse response, Map<String, String> mapObject)throws Exception;
}
