package com.simbest.boot.hnjjwz.examOnline.model;

import com.simbest.boot.base.annotations.EntityIdPrefix;
import com.simbest.boot.base.model.LogicModel;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.*;
import org.hibernate.annotations.GenericGenerator;

import javax.persistence.Column;
import javax.persistence.Entity;
import javax.persistence.GeneratedValue;
import javax.persistence.Id;

/**
 * <AUTHOR>
 * @Data 2019/05/08
 * Description 题库题目表
 */
@Data
@EqualsAndHashCode(callSuper = true)
@NoArgsConstructor
@AllArgsConstructor
@Entity(name = "us_exam_question_bank_question")
@ApiModel(value = "题库题目表")
public class ExamQuestionBankQuestion extends LogicModel {
    @Id
    @Column(name = "id", length = 40)
    @GeneratedValue(generator = "snowFlakeId")
    @GenericGenerator (name = "snowFlakeId", strategy = "com.simbest.boot.util.distribution.id.SnowflakeId")
    @EntityIdPrefix (prefix = "QBQ") //主键前缀，此为可选项注解
    private String id;

    @Column(length = 250)
    @Setter
    @Getter
    @ApiModelProperty(value = "题库编码", required = true)
    private String questionBankCode;

    @Column(length = 250)
    @Setter
    @Getter
    @ApiModelProperty(value = "题目编码", required = true)
    private String questionCode;

    @Column(length = 250)
    @Setter
    @Getter
    @ApiModelProperty(value = "大题位置", required = true)
    private String bigLocation;

    @Column(length = 250)
    @Setter
    @Getter
    @ApiModelProperty(value = "小题位置", required = true)
    private String smallLocation;

    @Column(length = 250)
    @Setter
    @Getter
    @ApiModelProperty(value = "额外字段1", required = true)
    private String spare1;

    @Column(length = 250)
    @Setter
    @Getter
    @ApiModelProperty(value = "额外字段2", required = true)
    private String spare2;

    @Column(length = 250)
    @Setter
    @Getter
    @ApiModelProperty(value = "额外字段3", required = true)
    private String spare3;

}
