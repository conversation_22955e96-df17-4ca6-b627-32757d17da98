//获取富文本内容以及html
function getRichTextValue() {
    return {
        "richTextHtml": tinyMCE.activeEditor.getContent(),
        "richText": tinyMCE.editors[tinyID].getContent({format: 'text'})
    }
}

//回显设置内容
function setRichTextValue(richTextHtml) {
    tinyMCE.editors[tinyID].setContent(richTextHtml);
}

//设置富文本不可编辑
function setRichTextReadonly() {
    tinymce.activeEditor.mode.set('readonly')
}

//设置富文本可编辑
function setRichTextReadonlyNo() {
    tinymce.activeEditor.mode.set('design')
}

function initTiny(fun) {
    var theUA = window.navigator.userAgent.toLowerCase();
    if ((theUA.match(/msie\s\d+/) && theUA.match(/msie\s\d+/)[0]) || (theUA.match(/trident\s?\d+/) && theUA.match(/trident\s?\d+/)[0])) {
        var ieVersion = theUA.match(/msie\s\d+/)[0].match(/\d+/)[0] || theUA.match(/trident\s?\d+/)[0];
        if (ieVersion < 11) {
            top.mesAlert("温馨提示！", "您的浏览器版本过低，为了您更好的使用体验<br>请升级为IE11浏览器或者使用其他浏览器<br>如：Edge浏览器、谷歌浏览器、360浏览器", "info")
            return false
        }
        ;
    }
    tinymce.init({
        // image_dimensions: false,//上传图片去掉宽高属性
        selector: '#' + tinyID,
        language: 'zh_CN',
        width: '100%',  // 设置编辑器宽度为100%
        height: 600,
        menubar: '',//上方文字菜单--edit insert view format table tools help file
        statusbar: false,//隐藏底部状态栏
        // branding: false,//隐藏右下角技术支持
        // custom_colors: false,//禁用关于颜色设置的调色盘
        block_formats: 'Paragraph=p;Header 1=h1;Header 2=h2;Header 3=h3;Header 4=h4;Header 5=h5;Header 6=h6;',//设置标题列表formatselect
        fontsize_formats: '11px 12px 14px 16px 18px 24px 36px 48px',//设置字体大小列表fontsizeselect
        font_formats: '微软雅黑=Microsoft YaHei,Helvetica Neue,PingFang SC,sans-serif;苹果苹方=PingFang SC,Microsoft YaHei,sans-serif;宋体=simsun,serif;仿宋_GB2312=FangSong_GB2312,serif;黑体=SimHei,sans-serif;Arial=arial,helvetica,sans-serif;Arial Black=arial black,avant garde;Book Antiqua=book antiqua,palatino;',//设置字体样式列表fontselect
        toolbar: 'code undo redo paste | bold italic underline strikethrough hr | alignleft aligncenter alignright alignjustify indent outdent lineheight | image media link preview | forecolor backcolor removeformat | formatselect fontselect fontsizeselect | table anchor',
        plugins: 'code image media link table hr anchor paste preview',//插件配置项
        convert_urls: false,//不使用相对路径
        media_alt_source: false,
        media_poster: false,
        content_style: '*{margin:0;padding:0;box-sizing:border-box;}img{max-width:770px;height:auto;}p{margin: 0;line-height: 25px;text-align: justify;text-justify: distribute;}',//设置图片样式
        //文件上传
        //文件上传只能使用 file_picker_callback 写回调。
        file_picker_types: 'file image media',
        //file_picker_callback仅为一个自定义钩子，具体上传功能需要自己实现。
        file_picker_callback: function (callback, value, meta) {
            switch (meta.filetype) {
                case 'image':
                    filetype = '.jpg, .jpeg, .png, .gif';
                    upurl = '/' + web.appCode + '/sys/file/uploadProcessFiles';
                    break;
                case 'media':
                    filetype = '.rmvb, .mp3, .mp4, .3gp, .mpeg, .wmv, .avi, .mov, .mpv';
                    upurl = '/' + web.appCode + '/sys/file/uploadProcessFiles';
                    break;
                case 'file':
                    filetype = '.pdf, .txt, .zip, .rar, .7z, .doc, .docx, .xls, .xlsx, .ppt, .pptx, .mp3, .mp4';
                    upurl = '/' + web.appCode + '/sys/file/uploadProcessFiles';
                    break;
            }
            //模拟出一个input用于添加本地文件
            var input = document.createElement('input');
            $(input).attr("type", "file").attr("accept", filetype).css("display", "none");
            $("body").eq(0).append(input);
            $(input).trigger('click');
            input.oninput = function () {
                var file = this.files[0];
                var xhr, formData;
                xhr = new XMLHttpRequest();
                // xhr.withCredentials = false;
                xhr.open('POST', upurl);
                xhr.onload = function () {
                    $(".upLoading").hide()
                    var json;
                    if (xhr.status != 200) {
                        top.mesAlert('提示', '上传失败！', 'info');
                    } else {
                        top.mesAlert('提示', '上传成功！', 'success');
                        var jsstr = xhr.responseText.slice(xhr.responseText.indexOf("result=") + 7, xhr.responseText.lastIndexOf("<"));
                        var responseData = JSON.parse(jsstr);
                        //此函数是由TinyMCE定义的，将字段的新值作为第一个参数，并将对话框中其他字段的元信息作为第二个参数
                        callback(responseData.data.sysFiles[0].mobileFilePath,
                            {
                                text: responseData.data.sysFiles[0].fileName,//文件
                                title: responseData.data.sysFiles[0].fileName,//文件
                                alt: responseData.data.sysFiles[0].fileName,//图片
                                //poster //视频
                            });
                    }
                };
                formData = new FormData();
                formData.append('file', file, file.name);
                xhr.send(formData);
                $(".upLoading").show()
            };
        },
        // video_template_callback: function(data) {
        //     return "<video style='max-width: 770px;height: 150px;'  controls autoplay><source  src='"+data.source+"' type='video/mp4'></source></video>"
        // },
        //粘贴事件
        paste_preprocess: function (plugin, args) {
            var srcReg = /src=[\'\"]?([^\'\"]*)[\'\"]?/gi;//获取所有src的正则表达式
            var srcArr = args.content.match(srcReg);//arr2 为包含所有src标签的数组
            if (srcArr) {
                for (var i = 0; i < srcArr.length; i++) {
                    args.content = args.content.replace(srcArr[i], 'src="http://iportal.ha.cmcc/portalweb/portalui/ncms/ncms/breakPicture.png"')
                }
            }
        },
        paste_enable_default_filters: false,//关闭默认过滤器（否则丢失文字样式）
        init_instance_callback: function () {
            if (fun) fun();
            // 设置TinyMCE编辑器宽度为100%
            $(".tox-tinymce").css("width", "100%");
            console.log('TinyMCE 宽度已设置为100%');
        }
    });
}