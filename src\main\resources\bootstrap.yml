## Configuration loading order by : Project Config > extension-configs > shared-configs
## 配置加载顺序 : 项目DataID主配值 > 扩展配置 > 共享配置
spring:
  profiles:
    active: ${profileActive}
logback:
  groupId: ${logback.groupId}
  artifactId: ${logback.artifactId}
server:
  port: 8092
---
#测试环境
spring:
  main:
    allow-bean-definition-overriding: true
  profiles: uat
  application:
    name: ${logback.artifactId}
  cloud:
    nacos:
      discovery:
        server-addr: ${spring.cloud.nacos.config.server-addr}
        username: ${spring.cloud.nacos.config.username}
        password: ${spring.cloud.nacos.config.password}
        group: DEFAULT_GROUP
        namespace: 2e74d7ec-48cc-4ac5-bb2d-b271dc9f7b59
      config:
        server-addr: ${spring.cloud.nacos.config.server-addr}
        username: ${spring.cloud.nacos.config.username}
        password: ${spring.cloud.nacos.config.password}
        group: DEFAULT_GROUP
        namespace: 2e74d7ec-48cc-4ac5-bb2d-b271dc9f7b59
        file-extension: properties
        refresh-enabled: true
        extension-configs[0]:
          data-id: ${logback.artifactId}-custom-uat.properties
          group: DEFAULT_GROUP
          refresh: true
        shared-configs[0]:
          data-id: commons.properties
          group: DEFAULT_GROUP
          refresh: true
        shared-configs[1]:
          data-id: spring-RabbitMQ.properties
          group: DEFAULT_GROUP
          refresh: true
---
---
#测试环境
spring:
  main:
    allow-bean-definition-overriding: true
  profiles: obuat
  application:
    name: ${logback.artifactId}
  cloud:
    nacos:
      discovery:
        server-addr: ${spring.cloud.nacos.config.server-addr}
        username: ${spring.cloud.nacos.config.username}
        password: ${spring.cloud.nacos.config.password}
        group: DEFAULT_GROUP
        namespace: e3bdfaec-cb06-494e-a785-bbee0e26854b
      config:
        server-addr: ${spring.cloud.nacos.config.server-addr}
        username: ${spring.cloud.nacos.config.username}
        password: ${spring.cloud.nacos.config.password}
        group: DEFAULT_GROUP
        namespace: e3bdfaec-cb06-494e-a785-bbee0e26854b
        file-extension: properties
        refresh-enabled: true
        extension-configs[0]:
          data-id: ${logback.artifactId}-custom-obuat.properties
          group: DEFAULT_GROUP
          refresh: true
        shared-configs[0]:
          data-id: commons.properties
          group: DEFAULT_GROUP
          refresh: true
        shared-configs[1]:
          data-id: spring-RabbitMQ.properties
          group: DEFAULT_GROUP
          refresh: true
---
#克隆环境
spring:
  main:
    allow-bean-definition-overriding: true
  profiles: clone
  application:
    name: ${logback.artifactId}
  cloud:
    nacos:
      discovery:
        server-addr: ${spring.cloud.nacos.config.server-addr}
        username: ${spring.cloud.nacos.config.username}
        password: ${spring.cloud.nacos.config.password}
        group: DEFAULT_GROUP
        namespace: 6c465ad4-49cb-4bc7-8be4-232faa90efe2
      config:
        server-addr: ${spring.cloud.nacos.config.server-addr}
        username: ${spring.cloud.nacos.config.username}
        password: ${spring.cloud.nacos.config.password}
        group: DEFAULT_GROUP
        namespace: 6c465ad4-49cb-4bc7-8be4-232faa90efe2
        file-extension: properties
        refresh-enabled: true
        #extension-configs[0]:
        #  data-id: ${logback.artifactId}-custom-clone.properties
        #  group: DEFAULT_GROUP
        #  refresh: true
        shared-configs[0]:
          data-id: commons.properties
          group: DEFAULT_GROUP
          refresh: true
        shared-configs[1]:
          data-id: spring-RabbitMQ.properties
          group: DEFAULT_GROUP
          refresh: true
---
#生产环境
spring:
  main:
    allow-bean-definition-overriding: true
  profiles: prd
  application:
    name: ${logback.artifactId}
  cloud:
    nacos:
      discovery:
        server-addr: ${spring.cloud.nacos.config.server-addr}
        username: ${spring.cloud.nacos.config.username}
        password: ${spring.cloud.nacos.config.password}
        group: DEFAULT_GROUP
        namespace: 85ec0673-9f07-41f7-9cd0-7f67836a115c
      config:
        server-addr: ${spring.cloud.nacos.config.server-addr}
        username: ${spring.cloud.nacos.config.username}
        password: ${spring.cloud.nacos.config.password}
        group: DEFAULT_GROUP
        namespace: 85ec0673-9f07-41f7-9cd0-7f67836a115c
        file-extension: properties
        refresh-enabled: true
        extension-configs[0]:
          data-id: ${logback.artifactId}-custom-prd.properties
          group: DEFAULT_GROUP
          refresh: true
        shared-configs[0]:
          data-id: commons.properties
          group: DEFAULT_GROUP
          refresh: true
        shared-configs[1]:
          data-id: spring-RabbitMQ.properties
          group: DEFAULT_GROUP
          refresh: true

---
#ob-prd
spring:
  main:
    allow-bean-definition-overriding: true
  profiles: obprd
  application:
    name: ${logback.artifactId}
  cloud:
    nacos:
      discovery:
        server-addr: ${spring.cloud.nacos.config.server-addr}
        username: ${spring.cloud.nacos.config.username}
        password: ${spring.cloud.nacos.config.password}
        group: DEFAULT_GROUP
        namespace: b50d29f9-9e7c-41be-b551-88597acc4b64
      config:
        server-addr: ${spring.cloud.nacos.config.server-addr}
        username: ${spring.cloud.nacos.config.username}
        password: ${spring.cloud.nacos.config.password}
        group: DEFAULT_GROUP
        namespace: b50d29f9-9e7c-41be-b551-88597acc4b64
        file-extension: properties
        refresh-enabled: true
        extension-configs[0]:
          data-id: ${logback.artifactId}-custom-obprd.properties
          group: DEFAULT_GROUP
          refresh: true
        shared-configs[0]:
          data-id: commons.properties
          group: DEFAULT_GROUP
          refresh: true
        shared-configs[1]:
          data-id: spring-RabbitMQ.properties
          group: DEFAULT_GROUP
          refresh: true