package com.simbest.boot.hnjjwz.backstage.indexMenu.web;/**
 * Created by KZH on 2019/5/30 17:00.
 */

import com.simbest.boot.base.web.controller.LogicController;
import com.simbest.boot.base.web.response.JsonResponse;
import com.simbest.boot.hnjjwz.backstage.indexMenu.model.SubmenuMenu;
import com.simbest.boot.hnjjwz.backstage.indexMenu.service.ISubmenuMenuService;
import io.swagger.annotations.Api;
import io.swagger.annotations.ApiImplicitParam;
import io.swagger.annotations.ApiImplicitParams;
import io.swagger.annotations.ApiOperation;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.data.domain.Pageable;
import org.springframework.web.bind.annotation.*;

import java.util.Map;

/**
 * <AUTHOR>
 * @create 2019-05-30 17:00
 * @desc
 **/
@Api(description = "子菜单")
@Slf4j
@RestController
@RequestMapping(value = "/action/submenuMenu")
public class SubmenuMenuController extends LogicController<SubmenuMenu,String> {

    @Autowired
    private ISubmenuMenuService iSubmenuMenuService;

    @Autowired
    public SubmenuMenuController(ISubmenuMenuService iSubmenuMenuService){
        super(iSubmenuMenuService);
        this.iSubmenuMenuService=iSubmenuMenuService;
    }


}
