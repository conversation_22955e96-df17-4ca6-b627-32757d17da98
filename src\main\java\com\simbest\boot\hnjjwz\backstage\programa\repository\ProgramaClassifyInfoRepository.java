package com.simbest.boot.hnjjwz.backstage.programa.repository;

import com.simbest.boot.base.repository.LogicRepository;
import com.simbest.boot.hnjjwz.backstage.programa.model.ProgramaClassifyInfo;
import org.springframework.data.domain.Page;
import org.springframework.data.domain.Pageable;
import org.springframework.data.jpa.repository.Query;
import org.springframework.data.repository.query.Param;

import java.util.List;
import java.util.Map;

/**
 * <AUTHOR>
 * @Data 2019/04/02
 * @Description 栏目分类
 */
public interface ProgramaClassifyInfoRepository extends LogicRepository<ProgramaClassifyInfo,String> {

    /**
     * 根据栏目类型(精确)以及栏目分类名称(模糊)查询并分页
     * @param programaClassifyName
     * @param programaTypeList
     * @return
     */
    @Query (value =  " select pci.enabled enabled,pci.id id,pci.programa_classify_name programaClassifyName," +
            " pci.programa_type programaType,pci.display_order displayOrder,dv.name programaTypeName " +
            " from us_programa_classify_info pci,sys_dict_value dv " +
            " WHERE pci.programa_classify_name LIKE concat( concat('%',:programaClassifyName),'%') " +
            " AND pci.programa_type IN (:programaTypeList) " +
            " AND pci.programa_type = dv.value AND dv.dict_type = 'programaType' " +
            " AND pci.programa_type = dv.value " +
            " AND pci.enabled=1 AND pci.removed_time IS NULL " +
            " AND dv.enabled=1 AND dv.removed_time IS NULL ",
            countQuery = "select COUNT(*) " +
                    "   from us_programa_classify_info pci,sys_dict_value dv " +
                    "   WHERE pci.programa_classify_name LIKE concat( concat('%',:programaClassifyName),'%') " +
                    "   AND pci.programa_type IN (:programaTypeList) " +
                    "   AND pci.programa_type = dv.value AND dv.dict_type = 'programaType' " +
                    "   AND pci.programa_type = dv.value " +
                    "   AND pci.enabled=1 AND pci.removed_time IS NULL " +
                    "   AND dv.enabled=1 AND dv.removed_time IS NULL ",
            nativeQuery = true)
    Page<Map<String,Object>> findUserOrgDim( @Param ( "programaClassifyName" ) String programaClassifyName, @Param("programaTypeList") List programaTypeList, Pageable pageable);

}
