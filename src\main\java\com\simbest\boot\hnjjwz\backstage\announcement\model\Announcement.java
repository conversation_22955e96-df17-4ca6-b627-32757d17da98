package com.simbest.boot.hnjjwz.backstage.announcement.model;

import com.simbest.boot.base.annotations.EntityIdPrefix;
import com.simbest.boot.base.model.LogicModel;
import com.simbest.boot.cmcc.wf.model.WfFormModel;
import com.simbest.boot.sys.model.SysFile;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.*;
import org.hibernate.annotations.GenericGenerator;

import javax.persistence.*;
import java.util.List;

/**
 * <AUTHOR>
 * @Data 2019/04/01
 * Description 公告
 */
@Data
@EqualsAndHashCode(callSuper = true)
@NoArgsConstructor
@AllArgsConstructor
@Entity(name = "us_announcement")
@ApiModel(value = "公告")
public class Announcement extends WfFormModel {
    @Id
    @Column(name = "id", length = 40)
    @GeneratedValue(generator = "snowFlakeId")
    @GenericGenerator (name = "snowFlakeId", strategy = "com.simbest.boot.util.distribution.id.SnowflakeId")
    @EntityIdPrefix (prefix = "A") //主键前缀，此为可选项注解
    private String id;

    @Setter
    @Getter
    @Column(name = "pmInsId",nullable = false,length = 100)
    @ApiModelProperty(value = "业务实例id")
    private String pmInsId;

    @Setter
    @Getter
    @Column(length = 100)
    @ApiModelProperty(value = "所属栏目全路径名")
    private String programaDisplayName;//所属栏目全路径名

    @Setter
    @Getter
    @Column(length = 100)
    @ApiModelProperty(value = "发布时间")
    private String creationTime;//审核发布后的时间

    @Setter
    @Getter
    @Column(length = 10)
    @ApiModelProperty(value = "是否发布")
    private Boolean isPublish;//是否发布

    @Setter
    @Getter
    @Column( length = 40)
    @ApiModelProperty(value = "起草人oa账号")
    @NonNull
    private String username;

    @Setter
    @Getter
    @Column( length = 40)
    @ApiModelProperty(value = "起草人姓名")
    @NonNull
    private String truename;

    @Setter
    @Getter
    @Column( length = 240)
    @ApiModelProperty(value = "起草人公司")
    @NonNull
    private String company;

    @Setter
    @Getter
    @Column( length = 240)
    @ApiModelProperty(value = "起草人部门")
    @NonNull
    private String departmentName;

    @Setter
    @Getter
    @Column( length = 240)
    @ApiModelProperty(value = "起草人组织路径")
    @NonNull
    private String displayName;

    @Column(length = 250)
    @Setter
    @Getter
    @ApiModelProperty(value = "公告标题", required = true)
    private String announcementTitle;

    @Setter
    @Getter
    @Column(length = 100)
    @ApiModelProperty(value = "公告编码", required = true)
    private String announcementCoding;

    //公告滚动间隔时间（建议放到字典表里管理）

    @Setter
    @Getter
    @Lob
    @Basic(fetch = FetchType.LAZY)
    @ApiModelProperty(value = "公告内容", required = true)
    private String announcementInfo;//公告内容

    @Column(length = 250)
    @Setter
    @Getter
    @ApiModelProperty(value = "公告附件id", required = true)
    private String announcementAccessoryId;//上传的公告附件的id

    @Column(length = 250)
    @Setter
    @Getter
    @ApiModelProperty(value = "关联的栏目内容的id", required = true)
    private String programaDataRelation;//公告与栏目下内容的id关联

    @Setter
    @Getter
    @ApiModelProperty(value = "关联内容标题", required = true)
    private String programaDataTtitle;

    //是否显示
    @Column(length = 10)
    @Setter
    @Getter
    @ApiModelProperty(value = "是否显示在首页公告滚动处", required = true)
    private Boolean isDisplay;

    //用removed_time标记什么时候过期

    @Setter
    @Getter
    @Column(columnDefinition="int default 0")
    @ApiModelProperty(value = "置顶标识")
    private int stickFlag;

    @Setter
    @Getter
    @Column(columnDefinition="int default 0")
    @ApiModelProperty(value = "浏览次数")
    private int viewsNumber;

    @Transient
    private String publishName;

    @Transient
    private String publishOrgCode;//发布人所在组织code

    @Transient
    private String publishOrgName;//发布人所在组织名

    @Transient
    private String publishDisplayName;//发布人所在组织全路径

    @Column(length = 250)
    @Setter
    @Getter
    @ApiModelProperty(value = "公告开始时间", required = true)
    private String startTime;

    @Column(length = 250)
    @Setter
    @Getter
    @ApiModelProperty(value = "公告结束时间", required = true)
    private String endTime;


    @Setter
    @Getter
    @Column(columnDefinition="int default 0")
    @ApiModelProperty(value = "是否着重显示")
    private int importantFlag;

    @Setter
    @Getter
    @ApiModelProperty(value = "图显示顺序", required = true)
    private String displayOrder;//按照顺序来从左到右展示

    @Column(length = 250)
    @Setter
    @Getter
    @ApiModelProperty(value = "额外字段1", required = true)
    private String spare1;

    @Column(length = 250)
    @Setter
    @Getter
    @ApiModelProperty(value = "额外字段2", required = true)
    private String spare2;

    @Column(length = 250)
    @Setter
    @Getter
    @ApiModelProperty(value = "额外字段3", required = true)
    private String spare3;

    @Transient
    private List<SysFile> announcementFileList;//存放查询公告信息中的文件信息

    @Transient
    @ApiModelProperty(value = "手机端判断 true和1可以在手机端处理",required = true)
    private boolean inMobile;


    @Transient
    @ApiModelProperty(value = "富文本压缩base64手机端用")
    private String compressBase;

}
