package com.simbest.boot.hnjjwz.examOnline.service.impl;

import com.simbest.boot.base.service.impl.LogicService;
import com.simbest.boot.base.web.response.JsonResponse;
import com.simbest.boot.hnjjwz.examOnline.model.ExamQuestionResult;
import com.simbest.boot.hnjjwz.examOnline.repository.ExamQuestionResultRepository;
import com.simbest.boot.hnjjwz.examOnline.service.IExamQuestionResultService;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.data.domain.Pageable;
import org.springframework.stereotype.Service;

/**
 * <AUTHOR>
 * @Data 2019/05/08
 * @Description 试卷内容
 */
@Slf4j
@Service
public class ExamQuestionResultServiceImpl extends LogicService<ExamQuestionResult,String> implements IExamQuestionResultService {

    private ExamQuestionResultRepository examQuestionResultRepository;

    @Autowired
    public ExamQuestionResultServiceImpl ( ExamQuestionResultRepository examQuestionResultRepository) {
        super(examQuestionResultRepository);
        this.examQuestionResultRepository = examQuestionResultRepository;
    }

    @Override
    public JsonResponse findOneselfDetails(String truename, Pageable pageable) {
        return JsonResponse.success(examQuestionResultRepository.findOneselfDetails(truename,pageable));
    }
}
