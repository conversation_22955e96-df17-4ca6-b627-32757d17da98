package com.simbest.boot.hnjjwz.sharingPlatform.service;

import com.simbest.boot.base.service.ILogicService;
import com.simbest.boot.base.web.response.JsonResponse;
import com.simbest.boot.hnjjwz.sharingPlatform.model.MenuDeploy;
import org.springframework.data.domain.Pageable;

import java.util.Map;

/**
 * 作用：菜单配置
 * 作者：刘萌
 * 时间：2019/05/06
 */
public interface IMenuDeployService extends ILogicService<MenuDeploy,String> {

    /**
     * 根据菜单名(模糊)以及父菜单名(模糊)查询分页
     * @param menuDeployMap
     * @param pageable
     * @return
     */
    JsonResponse findAllDim( Map<String,Object> menuDeployMap, Pageable pageable);


}
