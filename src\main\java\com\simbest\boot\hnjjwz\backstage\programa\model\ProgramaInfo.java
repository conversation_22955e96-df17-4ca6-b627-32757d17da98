package com.simbest.boot.hnjjwz.backstage.programa.model;

import com.simbest.boot.base.annotations.EntityIdPrefix;
import com.simbest.boot.base.model.LogicModel;
import com.simbest.boot.sys.model.SysFile;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.*;
import org.hibernate.annotations.GenericGenerator;
import org.hibernate.annotations.Table;

import javax.persistence.*;
import java.util.List;

/**
 * <AUTHOR>
 * @Data 2019/03/29
 * Description 栏目详细信息表
 */
@Data
@EqualsAndHashCode(callSuper = true)
@NoArgsConstructor
@AllArgsConstructor
@Entity (name = "us_programa_info")
@Table ( appliesTo="us_programa_info",comment="栏目详细信息表" )
@ApiModel(value = "栏目详细信息表")
public class ProgramaInfo extends LogicModel{
    @Id
    @Column (name = "id", length = 40)
    @GeneratedValue (generator = "snowFlakeId")
    @GenericGenerator (name = "snowFlakeId", strategy = "com.simbest.boot.util.distribution.id.SnowflakeId")
    @EntityIdPrefix (prefix = "PI") //主键前缀，此为可选项注解
    private String id;

    @Setter
    @Getter
    @Column(length=50)
    @ApiModelProperty(value = "所属栏目分类id")
    private String programaClassifyId;

    @Transient
    private String programaClassifyName;//所属栏目分类名字

    @Setter
    @Getter
    @Column(length=50)
    @ApiModelProperty(value = "父栏目编码")
    private String parentProgramaCode;

    @Transient
    private String parentProgramaName;//父栏目名

    @Setter
    @Getter
    @Column(length=50)
    @ApiModelProperty(value = "栏目编码")
    private String programaCode;//按照一套规则自动生成

    @Setter
    @Getter
    @Column(length=50)
    @ApiModelProperty(value = "栏目名称")
    private String programaName;

    @Setter
    @Getter
    @Column(length=200)
    @ApiModelProperty(value = "栏目全路径")
    private String programaDisplayName;

    @Setter
    @Getter
    @Column(length=20)
    @ApiModelProperty(value = "结点类型")
    private String nodeStyle;//结点类型，是顶级栏目，底级栏目还是中间栏目。

    @Transient
    private String nodeStyleName;//结点类型名

    @Setter
    @Getter
    @Column(length=20)
    @ApiModelProperty(value = "同一个父栏目下的排序")
    private String displayOrder;

    @Setter
    @Getter
    @Column(length=20)
    @ApiModelProperty(value = "栏目封面文件id")
    private String programaCoverId;

    @Setter
    @Getter
    @Column(length=200)
    @ApiModelProperty(value = "额外字段1")
    private String spare1;

    @Setter
    @Getter
    @Column(length=200)
    @ApiModelProperty(value = "额外字段2")
    private String spare2;

    @Setter
    @Getter
    @Column(length=200)
    @ApiModelProperty(value = "额外字段3")
    private String spare3;

    @Transient
    private List<SysFile> programaCoverFile;//存放栏目的封面信息

    @Transient
    private Object templateData=new Object();//接口数据

}
