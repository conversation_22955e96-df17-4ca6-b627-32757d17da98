2025-06-18 14:18:31.945 [restartedMain] WARN  com.alibaba.cloud.nacos.client.NacosPropertySourceBuilder.loadNacosData Line:87 - Ignore the empty nacos configuration and get it based on dataId[hnjjwz-custom-obuat.properties] & group[DEFAULT_GROUP]
2025-06-18 14:18:31.963 [restartedMain] WARN  com.alibaba.cloud.nacos.client.NacosPropertySourceBuilder.loadNacosData Line:87 - Ignore the empty nacos configuration and get it based on dataId[hnjjwz] & group[DEFAULT_GROUP]
2025-06-18 14:18:31.978 [restartedMain] WARN  com.alibaba.cloud.nacos.client.NacosPropertySourceBuilder.loadNacosData Line:87 - Ignore the empty nacos configuration and get it based on dataId[hnjjwz.properties] & group[DEFAULT_GROUP]
2025-06-18 14:18:32.004 [restartedMain] INFO  com.simbest.boot.SimbestApplication.logStartupProfileInfo Line:655 - The following profiles are active: obuat
2025-06-18 14:18:32.313 [restartedMain] DEBUG com.simbest.boot.processdata.pushWorkmanager.config.ConditionPushWorkManager.matches Line:31 - is.push.workmanager.open=true
2025-06-18 14:18:32.314 [restartedMain] DEBUG com.simbest.boot.processdata.pushWorkmanager.config.ConditionPushWorkManager.matches Line:32 - process.engine.type=BPS
2025-06-18 14:18:32.330 [restartedMain] DEBUG com.simbest.boot.processdata.pushWorkmanager.config.ConditionPushWorkManager.matches Line:31 - is.push.workmanager.open=true
2025-06-18 14:18:32.330 [restartedMain] DEBUG com.simbest.boot.processdata.pushWorkmanager.config.ConditionPushWorkManager.matches Line:32 - process.engine.type=BPS
2025-06-18 14:18:32.824 [restartedMain] DEBUG com.simbest.boot.processdata.pushWorkmanager.config.ConditionPushWorkManager.matches Line:31 - is.push.workmanager.open=true
2025-06-18 14:18:32.824 [restartedMain] DEBUG com.simbest.boot.processdata.pushWorkmanager.config.ConditionPushWorkManager.matches Line:32 - process.engine.type=BPS
2025-06-18 14:18:32.828 [restartedMain] DEBUG com.simbest.boot.processdata.pushWorkmanager.config.ConditionPushWorkManager.matches Line:31 - is.push.workmanager.open=true
2025-06-18 14:18:32.828 [restartedMain] DEBUG com.simbest.boot.processdata.pushWorkmanager.config.ConditionPushWorkManager.matches Line:32 - process.engine.type=BPS
2025-06-18 14:18:33.971 [restartedMain] DEBUG com.simbest.boot.processdata.pushWorkmanager.config.ConditionPushWorkManager.matches Line:31 - is.push.workmanager.open=true
2025-06-18 14:18:33.971 [restartedMain] DEBUG com.simbest.boot.processdata.pushWorkmanager.config.ConditionPushWorkManager.matches Line:32 - process.engine.type=BPS
2025-06-18 14:18:33.971 [restartedMain] DEBUG com.simbest.boot.processdata.pushWorkmanager.config.ConditionPushWorkManager.matches Line:31 - is.push.workmanager.open=true
2025-06-18 14:18:33.971 [restartedMain] DEBUG com.simbest.boot.processdata.pushWorkmanager.config.ConditionPushWorkManager.matches Line:32 - process.engine.type=BPS
2025-06-18 14:18:36.171 [restartedMain] WARN  org.springframework.boot.actuate.endpoint.EndpointId.logWarning Line:155 - Endpoint ID 'service-registry' contains invalid characters, please migrate to a valid format.
2025-06-18 14:18:36.901 [restartedMain] DEBUG net.bull.javamelody.debug Line:36 - spring interceptor initialized
2025-06-18 14:18:36.903 [restartedMain] DEBUG net.bull.javamelody.debug Line:36 - spring interceptor initialized
2025-06-18 14:18:36.903 [restartedMain] DEBUG net.bull.javamelody.debug Line:36 - spring interceptor initialized
2025-06-18 14:18:36.904 [restartedMain] DEBUG net.bull.javamelody.debug Line:36 - spring interceptor initialized
2025-06-18 14:18:36.906 [restartedMain] DEBUG net.bull.javamelody.debug Line:36 - spring interceptor initialized
2025-06-18 14:18:36.906 [restartedMain] DEBUG net.bull.javamelody.debug Line:36 - spring interceptor initialized
2025-06-18 14:18:36.907 [restartedMain] DEBUG net.bull.javamelody.debug Line:36 - spring interceptor initialized
2025-06-18 14:18:36.908 [restartedMain] DEBUG net.bull.javamelody.debug Line:36 - spring interceptor initialized
2025-06-18 14:18:37.410 [restartedMain] WARN  com.alibaba.druid.pool.DruidDataSource.initCheck Line:1314 - removeAbandoned is true, not use in production.
2025-06-18 14:18:37.619 [restartedMain] DEBUG net.bull.javamelody.debug Line:36 - Spring datasource wrapped: defaultDataSource
2025-06-18 14:18:38.450 [restartedMain] DEBUG org.hibernate.engine.jdbc.env.internal.JdbcEnvironmentInitiator.initiateService Line:72 - Database ->
       name : OceanBase
    version : 4.2.2.1
      major : 5
      minor : 6
2025-06-18 14:18:38.451 [restartedMain] DEBUG org.hibernate.engine.jdbc.env.internal.JdbcEnvironmentInitiator.initiateService Line:83 - Driver ->
       name : OceanBase Connector/J
    version : 2.4.9
      major : 2
      minor : 4
2025-06-18 14:18:38.451 [restartedMain] DEBUG org.hibernate.engine.jdbc.env.internal.JdbcEnvironmentInitiator.debugf Line:389 - JDBC version : 4.2
2025-06-18 14:18:38.464 [restartedMain] INFO  org.hibernate.dialect.Dialect.<init> Line:172 - HHH000400: Using dialect: org.hibernate.dialect.Oracle10gDialect
2025-06-18 14:19:09.792 [restartedMain] INFO  com.simbest.boot.config.AppConfig.init Line:243 - ************************************应用配置START**************************************************
2025-06-18 14:19:09.793 [restartedMain] INFO  com.simbest.boot.config.AppConfig.init Line:244 - 应用注册代码【hnjjwz】
2025-06-18 14:19:09.793 [restartedMain] INFO  com.simbest.boot.config.AppConfig.init Line:245 - 应用访问地址【http://************:8088】
2025-06-18 14:19:09.797 [restartedMain] INFO  com.simbest.boot.config.AppConfig.init Line:246 - 应用访问上下文【/hnjjwz】
2025-06-18 14:19:09.797 [restartedMain] INFO  com.simbest.boot.config.AppConfig.init Line:247 - 应用门户单点加密令牌【SIMBEST_SSO】
2025-06-18 14:19:09.798 [restartedMain] INFO  com.simbest.boot.config.AppConfig.init Line:248 - 应用超时时间【3600】秒
2025-06-18 14:19:09.798 [restartedMain] INFO  com.simbest.boot.config.AppConfig.init Line:249 - 应用上传文件大小限制【10MB】
2025-06-18 14:19:09.798 [restartedMain] INFO  com.simbest.boot.config.AppConfig.init Line:250 - 应用请求主数据地址【http://************:8088/uums】
2025-06-18 14:19:09.798 [restartedMain] INFO  com.simbest.boot.config.AppConfig.init Line:251 - 应用请求统一网关地址【http://************:8088/gateway】
2025-06-18 14:19:09.799 [restartedMain] INFO  com.simbest.boot.config.AppConfig.init Line:252 - 应用接口文档地址【http://************:8088/hnjjwz/swagger-ui.html】
2025-06-18 14:19:09.799 [restartedMain] INFO  com.simbest.boot.config.AppConfig.init Line:253 - 应用请求接收访问登录页地址的IP白名单为【uatoa.hq.cmcc,**************,***********,***********,***********,***********,***********,***********,***********,***********,***********,***********,***********,***********,***********,***********,***********,***********,***********,***********,***********,***********,***********,***********,***********】
2025-06-18 14:19:09.799 [restartedMain] INFO  com.simbest.boot.config.AppConfig.init Line:254 - 应用登录验证码开启状态【false】
2025-06-18 14:19:09.799 [restartedMain] INFO  com.simbest.boot.config.AppConfig.init Line:255 - 应用心跳定时器开关打开状态【false】
2025-06-18 14:19:09.800 [restartedMain] INFO  com.simbest.boot.config.AppConfig.init Line:256 - 记录Web请求日志状态【false】
2025-06-18 14:19:09.800 [restartedMain] INFO  com.simbest.boot.config.AppConfig.init Line:257 - 4A生成审计日志文件开关状态【false】
2025-06-18 14:19:09.800 [restartedMain] INFO  com.simbest.boot.config.AppConfig.init Line:258 - 4A上传审计日志文件服务器路径地址【/home/<USER>/gather/】
2025-06-18 14:19:09.801 [restartedMain] INFO  com.simbest.boot.config.AppConfig.init Line:259 - 清理用户缓存开关【true】
2025-06-18 14:19:09.801 [restartedMain] INFO  com.simbest.boot.config.AppConfig.init Line:260 - 清理用户缓存定时任务执行周期【0 0 5 * * ?】
2025-06-18 14:19:09.801 [restartedMain] INFO  com.simbest.boot.config.AppConfig.init Line:261 - SSO单点认证强制启用时间戳【false】
2025-06-18 14:19:09.802 [restartedMain] INFO  com.simbest.boot.config.AppConfig.init Line:262 - SSO单点认证加密盐值【Xianzai@2099】
2025-06-18 14:19:09.802 [restartedMain] INFO  com.simbest.boot.config.AppConfig.init Line:263 - SSO单点认证时间间隔【1】分钟
2025-06-18 14:19:09.802 [restartedMain] INFO  com.simbest.boot.config.AppConfig.init Line:264 - Cors跨域请求访问列表【*】
2025-06-18 14:19:09.802 [restartedMain] INFO  com.simbest.boot.config.AppConfig.init Line:266 - 应用临时文件上传目录为【E:\晟壁\党风廉政视窗\simbest-boot-hnjjwz/springboottmp/hnjjwz】
2025-06-18 14:19:09.802 [restartedMain] INFO  com.simbest.boot.config.AppConfig.init Line:267 - 应用获准访问白名单【localhost,127.0.0.1,portal.ha.cmcc,iportal.ha.cmcc,anddoc.ha.cmcc,andhall.ha.cmcc,#^(fe80|2409):.*$|^0:0:0:0:0:0:0:1$|^::1$|^10\.87\.(57|41|151|9|13)\.\d+|10\.(92|228|88)\.\d+\.\d+】
2025-06-18 14:19:09.802 [restartedMain] INFO  com.simbest.boot.config.AppConfig.init Line:268 - ************************************应用配置END**************************************************
2025-06-18 14:19:09.803 [restartedMain] INFO  com.simbest.boot.config.AppConfig.init Line:269 - 
2025-06-18 14:19:09.803 [restartedMain] INFO  com.simbest.boot.config.AppConfig.init Line:270 - ------------------------------------多线程配置START--------------------------------------------------
2025-06-18 14:19:09.803 [restartedMain] INFO  com.simbest.boot.config.AppConfig.init Line:271 - 多线程核心线程数【10】
2025-06-18 14:19:09.803 [restartedMain] INFO  com.simbest.boot.config.AppConfig.init Line:272 - 多线程最大线程数【20】
2025-06-18 14:19:09.803 [restartedMain] INFO  com.simbest.boot.config.AppConfig.init Line:273 - 多线程缓冲队列【200】
2025-06-18 14:19:09.803 [restartedMain] INFO  com.simbest.boot.config.AppConfig.init Line:274 - 多线程空闲时间【60】
2025-06-18 14:19:09.804 [restartedMain] INFO  com.simbest.boot.config.AppConfig.init Line:275 - ------------------------------------多线程配置END--------------------------------------------------
2025-06-18 14:19:09.804 [restartedMain] INFO  com.simbest.boot.config.AppConfig.init Line:276 - 
2025-06-18 14:19:09.804 [restartedMain] INFO  com.simbest.boot.config.AppConfig.init Line:277 - ====================================数据库配置START==================================================
2025-06-18 14:19:09.804 [restartedMain] INFO  com.simbest.boot.config.AppConfig.init Line:278 - 数据库URL【*****************************************************************************************************】
2025-06-18 14:19:09.804 [restartedMain] INFO  com.simbest.boot.config.AppConfig.init Line:279 - 数据库账号【hnjjwz@obtest#obdemo】
2025-06-18 14:19:09.804 [restartedMain] INFO  com.simbest.boot.config.AppConfig.init Line:280 - 数据库密码【Test44Hz】
2025-06-18 14:19:09.805 [restartedMain] INFO  com.simbest.boot.config.AppConfig.init Line:281 - ====================================数据库配置END==================================================
2025-06-18 14:19:09.805 [restartedMain] INFO  com.simbest.boot.config.AppConfig.init Line:282 - 
2025-06-18 14:19:09.805 [restartedMain] INFO  com.simbest.boot.config.AppConfig.init Line:283 - ====================================MongoDB数据库配置START==================================================
2025-06-18 14:19:09.805 [restartedMain] INFO  com.simbest.boot.config.AppConfig.init Line:284 - MongoDB数据库URL【】
2025-06-18 14:19:09.806 [restartedMain] INFO  com.simbest.boot.config.AppConfig.init Line:285 - MongoDB数据库【】
2025-06-18 14:19:09.806 [restartedMain] INFO  com.simbest.boot.config.AppConfig.init Line:286 - MongoDB数据库账号【】
2025-06-18 14:19:09.806 [restartedMain] INFO  com.simbest.boot.config.AppConfig.init Line:287 - MongoDB数据库密码【】
2025-06-18 14:19:09.806 [restartedMain] INFO  com.simbest.boot.config.AppConfig.init Line:288 - ====================================MongoDB数据库配置END==================================================
2025-06-18 14:19:09.806 [restartedMain] INFO  com.simbest.boot.config.AppConfig.init Line:289 - 
2025-06-18 14:19:09.806 [restartedMain] INFO  com.simbest.boot.config.AppConfig.init Line:290 - ^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^REDIS缓存配置START^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^
2025-06-18 14:19:09.806 [restartedMain] INFO  com.simbest.boot.config.AppConfig.init Line:291 - Redis配置方式【dictValueRedis】
2025-06-18 14:19:09.807 [restartedMain] INFO  com.simbest.boot.config.AppConfig.init Line:294 - Redis主数据中配置项为【redis-161】
2025-06-18 14:19:09.807 [restartedMain] INFO  com.simbest.boot.config.AppConfig.init Line:307 - Redis密码【Xianzai@0528】
2025-06-18 14:19:09.808 [restartedMain] INFO  com.simbest.boot.config.AppConfig.init Line:308 - Redis重定向次数【3】
2025-06-18 14:19:09.808 [restartedMain] INFO  com.simbest.boot.config.AppConfig.init Line:309 - Redis缓存空间前缀【spring:session:hnjjwz】
2025-06-18 14:19:09.808 [restartedMain] INFO  com.simbest.boot.config.AppConfig.init Line:310 - Redis缓存Key键前缀【cache:key:hnjjwz:】
2025-06-18 14:19:09.809 [restartedMain] INFO  com.simbest.boot.config.AppConfig.init Line:311 - Redis缓存默认等待加锁时间【3】秒
2025-06-18 14:19:09.809 [restartedMain] INFO  com.simbest.boot.config.AppConfig.init Line:312 - Redis缓存默认加锁后释放时间【60】秒
2025-06-18 14:19:09.809 [restartedMain] INFO  com.simbest.boot.config.AppConfig.init Line:313 - Redis缓存连接池最大连接数【100】个
2025-06-18 14:19:09.809 [restartedMain] INFO  com.simbest.boot.config.AppConfig.init Line:314 - Redis缓存连接池最大空闲连接数【50】个
2025-06-18 14:19:09.810 [restartedMain] INFO  com.simbest.boot.config.AppConfig.init Line:315 - Redis缓存连接池最小空闲连接数【5】个
2025-06-18 14:19:09.810 [restartedMain] INFO  com.simbest.boot.config.AppConfig.init Line:316 - ^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^REDIS缓存配置END^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^
2025-06-18 14:19:09.810 [restartedMain] INFO  com.simbest.boot.config.AppConfig.init Line:317 - 
2025-06-18 14:19:09.810 [restartedMain] INFO  com.simbest.boot.config.AppConfig.init Line:318 - ####################################文件存储配置START##################################################
2025-06-18 14:19:09.810 [restartedMain] INFO  com.simbest.boot.config.AppConfig.init Line:319 - 应用文件上传方式【disk】
2025-06-18 14:19:09.810 [restartedMain] INFO  com.simbest.boot.config.AppConfig.init Line:320 - 应用上传文件路径【/home/<USER>/simbestboot】
2025-06-18 14:19:09.810 [restartedMain] INFO  com.simbest.boot.config.AppConfig.init Line:321 - 应用下载文件启用Nginx映射状态【false】
2025-06-18 14:19:09.810 [restartedMain] INFO  com.simbest.boot.config.AppConfig.init Line:326 - ####################################文件存储配置END##################################################
2025-06-18 14:19:09.810 [restartedMain] INFO  com.simbest.boot.config.AppConfig.init Line:327 - 
2025-06-18 14:19:09.820 [restartedMain] INFO  com.simbest.boot.config.RedisConfiguration.redisConnectionFactory Line:189 - 缓存配置信息dictValueRedis
2025-06-18 14:19:09.820 [restartedMain] INFO  com.simbest.boot.config.RedisConfiguration.redisConnectionFactory Line:195 - 基于数据库读取Redis配置
2025-06-18 14:19:09.820 [restartedMain] DEBUG com.simbest.boot.config.RedisConfiguration.redisConnectionFactory Line:196 - 即将通过UUMS主数据【http://************:8088/uums】读取Redis配置项【redis-161】的Redis节点信息
2025-06-18 14:19:09.821 [restartedMain] DEBUG com.simbest.boot.util.encrypt.AbstractEncryptor.encryptBefore Line:20 - ++++++++++encrypt source before value is: hadmin
2025-06-18 14:19:09.992 [restartedMain] DEBUG com.simbest.boot.util.encrypt.AbstractEncryptor.encryptAfter Line:24 - ++++++++++encrypt source after value is: cq+DdHFotno=
2025-06-18 14:19:10.783 [restartedMain] INFO  com.simbest.boot.config.RedisConfiguration.redisConnectionFactory Line:237 - *************************Redis加载配置节点START******************************
2025-06-18 14:19:10.784 [restartedMain] INFO  com.simbest.boot.config.RedisConfiguration.redisConnectionFactory Line:238 - Redis节点为【************:7001,************:7002,************:7003,************:7004,************:7005,************:7006】
2025-06-18 14:19:10.784 [restartedMain] INFO  com.simbest.boot.config.RedisConfiguration.redisConnectionFactory Line:239 - *************************Redis加载配置节点END********************************
2025-06-18 14:19:11.103 [restartedMain] DEBUG reactor.util.Loggers$LoggerFactory.debug Line:249 - Using Slf4j logging framework
2025-06-18 14:19:11.790 [restartedMain] DEBUG com.simbest.boot.config.MultipartConfiguration.multipartConfigElement Line:47 - 创建文件上传临时目录成功，临时路径地址：【E:\晟壁\党风廉政视窗\simbest-boot-hnjjwz\springboottmp\hnjjwz】
2025-06-18 14:19:11.993 [restartedMain] INFO  com.simbest.boot.config.EmbeddedServletConfiguration.customizeConnector Line:87 - 应用支持的附件上传最大限制为【10MB】
2025-06-18 14:19:11.994 [restartedMain] INFO  com.simbest.boot.config.EmbeddedServletConfiguration.customizeConnector Line:97 - 内置Tomcat将在端口【8092】启动
2025-06-18 14:19:12.025 [restartedMain] DEBUG com.simbest.boot.config.EmbeddedServletConfiguration$1.getArchiveFileDocumentRoot Line:81 - Code archive: C:\Users\<USER>\.m2\repository\org\springframework\boot\spring-boot\2.2.2.RELEASE\spring-boot-2.2.2.RELEASE.jar
2025-06-18 14:19:12.026 [restartedMain] DEBUG com.simbest.boot.config.EmbeddedServletConfiguration$1.getExplodedWarFileDocumentRoot Line:125 - Code archive: C:\Users\<USER>\.m2\repository\org\springframework\boot\spring-boot\2.2.2.RELEASE\spring-boot-2.2.2.RELEASE.jar
2025-06-18 14:19:12.026 [restartedMain] DEBUG com.simbest.boot.config.EmbeddedServletConfiguration$1.logNoDocumentRoots Line:149 - None of the document roots [src/main/webapp, public, static] point to a directory and will be ignored.
2025-06-18 14:19:12.912 [restartedMain] DEBUG io.micrometer.core.util.internal.logging.InternalLoggerFactory.newDefaultFactory Line:61 - Using SLF4J as the default logging framework
2025-06-18 14:19:13.476 [restartedMain] DEBUG com.simbest.boot.mq.conf.MultipleRabbitMQConfig.masterConnectionFactory Line:184 - 加载rabbitmq连接
2025-06-18 14:19:13.813 [restartedMain] DEBUG com.simbest.boot.mq.conf.MultipleRabbitMQConfig.masterRabbitTemplate Line:155 - ---------------------------->主集群连接加载完成
2025-06-18 14:19:14.026 [restartedMain] DEBUG com.simbest.boot.mq.conf.MultipleRabbitMQConfig.publicConnectionFactory Line:276 - master
2025-06-18 14:19:14.034 [restartedMain] DEBUG com.simbest.boot.mq.conf.MultipleRabbitMQConfig.publicRabbitTemplate Line:243 - ---------------------------->备用集群连接加载完成
2025-06-18 14:19:14.633 [restartedMain] DEBUG com.simbest.boot.component.distributed.lock.AppRuntimeMaster.checkMasterIsMe Line:139 - 当前主机地址【***********】 
2025-06-18 14:19:15.618 [restartedMain] DEBUG com.simbest.boot.component.distributed.lock.AppRuntimeMaster.checkMasterIsMe Line:141 - 集群主机地址【************】 
2025-06-18 14:19:15.619 [restartedMain] DEBUG com.simbest.boot.component.distributed.lock.AppRuntimeMaster.checkMasterIsMe Line:142 - 当前主机端口【8092】
2025-06-18 14:19:15.628 [restartedMain] DEBUG com.simbest.boot.component.distributed.lock.AppRuntimeMaster.checkMasterIsMe Line:144 - 集群主机端口【10031】
2025-06-18 14:19:15.628 [restartedMain] DEBUG com.simbest.boot.component.distributed.lock.AppRuntimeMaster.checkMasterIsMe Line:146 - 判断当前主机是否是集群主控节点结果为【false】
2025-06-18 14:19:17.112 [restartedMain] DEBUG net.bull.javamelody.debug Line:36 - JavaMelody listener init started
2025-06-18 14:19:17.114 [restartedMain] DEBUG net.bull.javamelody.debug Line:36 - datasources found in JNDI: []
2025-06-18 14:19:17.115 [restartedMain] DEBUG net.bull.javamelody.debug Line:36 - JavaMelody listener init done in 3 ms
2025-06-18 14:19:17.115 [restartedMain] DEBUG net.bull.javamelody.debug Line:36 - JavaMelody listener init started
2025-06-18 14:19:17.115 [restartedMain] DEBUG net.bull.javamelody.debug Line:36 - datasources found in JNDI: []
2025-06-18 14:19:17.115 [restartedMain] DEBUG net.bull.javamelody.debug Line:36 - JavaMelody listener init done in 0 ms
2025-06-18 14:19:17.118 [restartedMain] DEBUG com.simbest.boot.security.auth.filter.RestCaptchaAuthenticationFilter.init Line:242 - Filter 'restCaptchaAuthenticationFilter' configured for use
2025-06-18 14:19:17.118 [restartedMain] DEBUG com.simbest.boot.security.auth.filter.RestRsaAuthenticationFilter.init Line:242 - Filter 'restRsaAuthenticationFilter' configured for use
2025-06-18 14:19:17.118 [restartedMain] DEBUG com.simbest.boot.security.auth.filter.RsaAuthenticationFilter.init Line:242 - Filter 'rsaAuthenticationFilter' configured for use
2025-06-18 14:19:17.122 [restartedMain] DEBUG net.bull.javamelody.debug Line:36 - JavaMelody filter init started
2025-06-18 14:19:17.123 [restartedMain] DEBUG net.bull.javamelody.debug Line:36 - OS: Windows 10 , amd64/64
2025-06-18 14:19:17.123 [restartedMain] DEBUG net.bull.javamelody.debug Line:36 - Java: Java(TM) SE Runtime Environment, 1.8.0_201-b09
2025-06-18 14:19:17.123 [restartedMain] DEBUG net.bull.javamelody.debug Line:36 - Server: Apache Tomcat/9.0.65
2025-06-18 14:19:17.123 [restartedMain] DEBUG net.bull.javamelody.debug Line:36 - Webapp context: /hnjjwz
2025-06-18 14:19:17.123 [restartedMain] DEBUG net.bull.javamelody.debug Line:36 - JavaMelody version: 1.76.0
2025-06-18 14:19:17.124 [restartedMain] DEBUG net.bull.javamelody.debug Line:36 - JavaMelody classes loaded from: file:/C:/Users/<USER>/.m2/repository/net/bull/javamelody/javamelody-core/1.76.0/javamelody-core-1.76.0.jar
2025-06-18 14:19:17.124 [restartedMain] DEBUG net.bull.javamelody.debug Line:36 - Application type: Classic
2025-06-18 14:19:17.133 [restartedMain] DEBUG net.bull.javamelody.debug Line:36 - Application version: null
2025-06-18 14:19:17.133 [restartedMain] DEBUG net.bull.javamelody.debug Line:36 - Host: DESKTOP-HUIVSCK@***********
2025-06-18 14:19:17.134 [restartedMain] DEBUG net.bull.javamelody.debug Line:36 - parameter defined: storage-directory=E:\晟壁\党风廉政视窗\simbest-boot-hnjjwz/springboottmp/hnjjwz
2025-06-18 14:19:17.151 [restartedMain] DEBUG net.bull.javamelody.debug Line:36 - log listeners initialized
2025-06-18 14:19:17.151 [restartedMain] DEBUG net.bull.javamelody.debug Line:36 - datasources found in JNDI: []
2025-06-18 14:19:17.168 [restartedMain] DEBUG net.bull.javamelody.debug Line:36 - counters initialized
2025-06-18 14:19:17.188 [restartedMain] DEBUG net.bull.javamelody.debug Line:36 - counters data read from files in E:\晟壁\党风廉政视窗\simbest-boot-hnjjwz\springboottmp\hnjjwz\hnjjwz_DESKTOP-HUIVSCK
2025-06-18 14:19:17.244 [restartedMain] DEBUG net.bull.javamelody.debug Line:36 - collect task scheduled every 60s
2025-06-18 14:19:18.452 [restartedMain] DEBUG net.bull.javamelody.debug Line:36 - first collect of data done
2025-06-18 14:19:18.454 [restartedMain] DEBUG net.bull.javamelody.debug Line:36 - JavaMelody filter init done in 1332 ms
2025-06-18 14:19:18.455 [restartedMain] DEBUG com.simbest.boot.cmcc.hq.web.filter.HqSsoAuthenticationFilter.init Line:242 - Filter 'hqSsoAuthenticationFilter' configured for use
2025-06-18 14:19:18.455 [restartedMain] DEBUG com.simbest.boot.security.auth.filter.UumsAuthenticationFilter.init Line:242 - Filter 'uumsAuthenticationFilter' configured for use
2025-06-18 14:19:18.458 [restartedMain] DEBUG com.simbest.boot.security.auth.filter.RestUumsAuthenticationFilter.init Line:242 - Filter 'restUumsAuthenticationFilter' configured for use
2025-06-18 14:19:18.459 [restartedMain] DEBUG com.simbest.boot.security.auth.filter.SsoAuthenticationFilter.init Line:242 - Filter 'ssoAuthenticationFilter' configured for use
2025-06-18 14:19:18.459 [restartedMain] DEBUG com.simbest.boot.security.auth.filter.CaptchaAuthenticationFilter.init Line:242 - Filter 'captchaAuthenticationFilter' configured for use
2025-06-18 14:19:18.845 [restartedMain] INFO  com.simbest.boot.util.AppFileUtil.init Line:153 - 请注意应用没有配置SFTP，请检查配置是否需要，如不需要，则忽略该条警告信息！
2025-06-18 14:19:24.300 [restartedMain] DEBUG com.simbest.boot.util.distribution.id.RedisIdGenerator.startup Line:70 - START***********************应用即将启动，从数据库提取当天REDIS ID保存至JVM Map***********************START
2025-06-18 14:19:24.354 [restartedMain] DEBUG com.simbest.boot.base.service.impl.GenericService.findAllNoPage Line:331 - @Generic Repository Service object by findAllNoPage
2025-06-18 14:19:24.420 [restartedMain] DEBUG org.hibernate.SQL.logStatement Line:127 - /* select generatedAlias0 from SysRedisIdKey as generatedAlias0 where generatedAlias0.day=:param0 */ select sysredisid0_.id as id1_26_, sysredisid0_.day as day2_26_, sysredisid0_.name as name3_26_, sysredisid0_.value as value4_26_ from sys_redis_id_key sysredisid0_ where sysredisid0_.day=?
2025-06-18 14:19:24.427 [restartedMain] TRACE org.hibernate.type.descriptor.sql.BasicBinder.bind Line:64 - binding parameter [1] as [VARCHAR] - [2025-06-18]
2025-06-18 14:19:24.456 [restartedMain] DEBUG com.simbest.boot.util.distribution.id.RedisIdGenerator.startup Line:78 - END***********************应用即将启动，从数据库提取当天REDIS ID保存至JVM Map***********************END
2025-06-18 14:19:30.645 [restartedMain] DEBUG com.simbest.boot.util.encrypt.AbstractEncryptor.encryptBefore Line:20 - ++++++++++encrypt source before value is: hadmin
2025-06-18 14:19:30.647 [restartedMain] DEBUG com.simbest.boot.util.encrypt.AbstractEncryptor.encryptAfter Line:24 - ++++++++++encrypt source after value is: qhzziwg+37Y0lXWZ3w6OxrFh84JD/KDaEtC+kjJWd4N3Y0VGpMS7PteZB6us9OBmfEcn2HDTO6vWSfq2fNyB4oW21dCvqa00BnYBgs4GwOBjgTiNoOGC9s2jR6Yq9QSK7Lr7wzCRuD7RA3QZ6lYlyrgZlZo0Df9Q5VamrT8/ToY=
2025-06-18 14:19:34.340 [restartedMain] DEBUG com.simbest.boot.config.RedisConfiguration.redissonClient Line:423 - Congratulations------------------------------------------------Redis 进程实例已创建成功
2025-06-18 14:19:35.077 [restartedMain] DEBUG com.simbest.boot.mq.conf.MultipleRabbitMQConfig.getSimpleRabbitListenerContainerFactory Line:122 - 创建连接工厂
2025-06-18 14:19:35.088 [restartedMain] DEBUG com.simbest.boot.mq.conf.MultipleRabbitMQConfig.getSimpleRabbitListenerContainerFactory Line:122 - 创建连接工厂
2025-06-18 14:19:35.097 [restartedMain] DEBUG com.simbest.boot.mq.conf.MultipleRabbitMQConfig.getSimpleRabbitListenerContainerFactory Line:122 - 创建连接工厂
2025-06-18 14:19:35.099 [restartedMain] DEBUG com.simbest.boot.mq.conf.MultipleRabbitMQConfig.getSimpleRabbitListenerContainerFactory Line:122 - 创建连接工厂
2025-06-18 14:19:35.108 [restartedMain] DEBUG com.simbest.boot.mq.conf.SysMqConf.logQueue1 Line:146 - 创建系统队列
2025-06-18 14:19:35.164 [restartedMain] DEBUG com.simbest.boot.mq.conf.SysMqConf.logQueue1 Line:152 - 创建系统队列结束
2025-06-18 14:19:35.561 [restartedMain] DEBUG org.hibernate.SQL.logStatement Line:127 - /* select generatedAlias0 from sys_4a_audit_config as generatedAlias0 */ select a4logconfi0_.id as id1_12_, a4logconfi0_.created_time as created_time2_12_, a4logconfi0_.modified_time as modified_time3_12_, a4logconfi0_.boby_params as boby_params4_12_, a4logconfi0_.identity_name as identity_name5_12_, a4logconfi0_.method_name as method_name6_12_, a4logconfi0_.module_id as module_id7_12_, a4logconfi0_.module_names as module_names8_12_, a4logconfi0_.op_level_id as op_level_id9_12_, a4logconfi0_.op_type_id as op_type_id10_12_, a4logconfi0_.op_type_name as op_type_name11_12_, a4logconfi0_.resource_code as resource_code12_12_, a4logconfi0_.spare01 as spare13_12_, a4logconfi0_.spare02 as spare14_12_, a4logconfi0_.spare03 as spare15_12_, a4logconfi0_.url_params as url_params16_12_, a4logconfi0_.web_name as web_name17_12_ from sys_4a_audit_config a4logconfi0_
2025-06-18 14:19:36.428 [restartedMain] DEBUG com.simbest.boot.security.auth.config.MultiHttpSecurityConfig.configureGlobal Line:77 - 系统将自动注册自定义认证提供器: class com.simbest.boot.security.auth.provider.PhoneTokenAuthenticationProvider
2025-06-18 14:19:36.429 [restartedMain] DEBUG com.simbest.boot.security.auth.config.MultiHttpSecurityConfig.configureGlobal Line:77 - 系统将自动注册自定义认证提供器: class com.simbest.boot.security.auth.provider.PhoneCodeTokenAuthenticationProvider
2025-06-18 14:19:37.412 [restartedMain] DEBUG com.simbest.boot.util.encrypt.AbstractEncryptor.decryptBefore Line:28 - ----------decrypt source before value is: T0FlR2VqMzIvU0pQazBSczBqWEIrN09Zb1E1dllpOXArVW00L1F3RzFOc3pYZGFmN1pNaWp0YzBpc3kxZ1JzZjN3ejgzOUV6OGE5cWxEVitUQ3JzbXA2Rzh5eVA1SWdrL1BVek9NVU8yK0EzQTVtQmxWYXRSYTJWY1dMbmpHTnpYUjZHdXY5eDByU01CVUpVeDR1TG9FUU1wZkc5L0lpRnB2MEp3SmhmVXBvPQ==
2025-06-18 14:19:37.412 [restartedMain] DEBUG com.simbest.boot.util.encrypt.AbstractEncryptor.decryptAfter Line:32 - ----------decrypt source after value is: OAeGej32/SJPk0Rs0jXB+7OYoQ5vYi9p+Um4/QwG1NszXdaf7ZMijtc0isy1gRsf3wz839Ez8a9qlDV+TCrsmp6G8yyP5Igk/PUzOMUO2+A3A5mBlVatRa2VcWLnjGNzXR6Guv9x0rSMBUJUx4uLoEQMpfG9/IiFpv0JwJhfUpo=
2025-06-18 14:19:37.412 [restartedMain] DEBUG com.simbest.boot.util.encrypt.AbstractEncryptor.decryptBefore Line:28 - ----------decrypt source before value is: OAeGej32/SJPk0Rs0jXB+7OYoQ5vYi9p+Um4/QwG1NszXdaf7ZMijtc0isy1gRsf3wz839Ez8a9qlDV+TCrsmp6G8yyP5Igk/PUzOMUO2+A3A5mBlVatRa2VcWLnjGNzXR6Guv9x0rSMBUJUx4uLoEQMpfG9/IiFpv0JwJhfUpo=
2025-06-18 14:19:37.415 [restartedMain] DEBUG com.simbest.boot.util.encrypt.AbstractEncryptor.decryptAfter Line:32 - ----------decrypt source after value is: 9999-07-31 08:00:00
2025-06-18 14:19:38.134 [restartedMain] DEBUG com.simbest.boot.security.auth.config.FormSecurityConfigurer.configure Line:263 - 系统将注册自定义过滤器【class com.simbest.boot.cmcc.hq.web.filter.HqSsoAuthenticationFilter】
2025-06-18 14:19:42.785 [restartedMain] WARN  com.simbest.boot.SimbestApplication.onApplicationEvent Line:60 - 加载环境信息为: 【obuat】
2025-06-18 14:19:42.786 [restartedMain] WARN  com.simbest.boot.SimbestApplication.onApplicationEvent Line:61 - Application started successfully, lets go and have fun......
2025-06-18 14:19:42.787 [restartedMain] WARN  com.simbest.boot.SimbestApplication.onApplicationEvent Line:71 - 
---------------------------------------------------------
	应用已成功启动，运行地址如下：:
	Local:		http://localhost:8092/hnjjwz
	External:	http://***********:8092/hnjjwz
Aplication started successfully, lets go and have fun......
---------------------------------------------------------

2025-06-18 14:19:42.910 [restartedMain] INFO  com.simbest.boot.SimbestApplication.logStarted Line:61 - Started SimbestApplication in 74.241 seconds (JVM running for 75.361)
2025-06-18 14:19:42.916 [restartedMain] DEBUG com.simbest.boot.util.encrypt.AbstractEncryptor.encryptBefore Line:20 - ++++++++++encrypt source before value is: hadmin
2025-06-18 14:19:42.916 [restartedMain] DEBUG com.simbest.boot.util.encrypt.AbstractEncryptor.encryptAfter Line:24 - ++++++++++encrypt source after value is: XM3f80nUf0qR9HJy57XqmNEJKxjEXR/OKwnuR1v+/s3RiiXTjcYCFw82QBoRhkdM1EW8PMaAJDD/ampFLr2BNOxYPz7iQC95OFd6eL+Wi92csnMJjHELJTWUQchlmM3nCjKf8nZ0NC4MkjaQQaBp5+SivXsJvXbjTyfCXUcV/vA=
2025-06-18 14:19:45.578 [RMI TCP Connection(4)-***********] WARN  org.springframework.boot.actuate.ldap.LdapHealthIndicator.health Line:87 - LDAP health check failed
org.springframework.ldap.CommunicationException: localhost:389; nested exception is javax.naming.CommunicationException: localhost:389 [Root exception is java.net.ConnectException: Connection refused: connect]
	at org.springframework.ldap.support.LdapUtils.convertLdapException(LdapUtils.java:108)
	at org.springframework.ldap.core.support.AbstractContextSource.createContext(AbstractContextSource.java:355)
	at org.springframework.ldap.core.support.AbstractContextSource.doGetContext(AbstractContextSource.java:139)
	at org.springframework.ldap.core.support.AbstractContextSource.getReadOnlyContext(AbstractContextSource.java:158)
	at org.springframework.ldap.core.LdapTemplate.executeReadOnly(LdapTemplate.java:802)
	at org.springframework.boot.actuate.ldap.LdapHealthIndicator.doHealthCheck(LdapHealthIndicator.java:50)
	at org.springframework.boot.actuate.health.AbstractHealthIndicator.health(AbstractHealthIndicator.java:82)
	at org.springframework.boot.actuate.health.HealthIndicator.getHealth(HealthIndicator.java:37)
	at org.springframework.boot.actuate.health.HealthEndpoint.getHealth(HealthEndpoint.java:81)
	at org.springframework.boot.actuate.health.HealthEndpoint.getHealth(HealthEndpoint.java:38)
	at org.springframework.boot.actuate.health.HealthEndpointSupport.getContribution(HealthEndpointSupport.java:108)
	at org.springframework.boot.actuate.health.HealthEndpointSupport.getAggregateHealth(HealthEndpointSupport.java:119)
	at org.springframework.boot.actuate.health.HealthEndpointSupport.getContribution(HealthEndpointSupport.java:105)
	at org.springframework.boot.actuate.health.HealthEndpointSupport.getHealth(HealthEndpointSupport.java:83)
	at org.springframework.boot.actuate.health.HealthEndpointSupport.getHealth(HealthEndpointSupport.java:70)
	at org.springframework.boot.actuate.health.HealthEndpoint.health(HealthEndpoint.java:75)
	at org.springframework.boot.actuate.health.HealthEndpoint.health(HealthEndpoint.java:65)
	at sun.reflect.NativeMethodAccessorImpl.invoke0(Native Method)
	at sun.reflect.NativeMethodAccessorImpl.invoke(NativeMethodAccessorImpl.java:62)
	at sun.reflect.DelegatingMethodAccessorImpl.invoke(DelegatingMethodAccessorImpl.java:43)
	at java.lang.reflect.Method.invoke(Method.java:498)
	at org.springframework.util.ReflectionUtils.invokeMethod(ReflectionUtils.java:282)
	at org.springframework.boot.actuate.endpoint.invoke.reflect.ReflectiveOperationInvoker.invoke(ReflectiveOperationInvoker.java:77)
	at org.springframework.boot.actuate.endpoint.annotation.AbstractDiscoveredOperation.invoke(AbstractDiscoveredOperation.java:60)
	at org.springframework.boot.actuate.endpoint.jmx.EndpointMBean.invoke(EndpointMBean.java:121)
	at org.springframework.boot.actuate.endpoint.jmx.EndpointMBean.invoke(EndpointMBean.java:96)
	at com.sun.jmx.interceptor.DefaultMBeanServerInterceptor.invoke(DefaultMBeanServerInterceptor.java:819)
	at com.sun.jmx.mbeanserver.JmxMBeanServer.invoke(JmxMBeanServer.java:801)
	at javax.management.remote.rmi.RMIConnectionImpl.doOperation(RMIConnectionImpl.java:1468)
	at javax.management.remote.rmi.RMIConnectionImpl.access$300(RMIConnectionImpl.java:76)
	at javax.management.remote.rmi.RMIConnectionImpl$PrivilegedOperation.run(RMIConnectionImpl.java:1309)
	at javax.management.remote.rmi.RMIConnectionImpl.doPrivilegedOperation(RMIConnectionImpl.java:1401)
	at javax.management.remote.rmi.RMIConnectionImpl.invoke(RMIConnectionImpl.java:829)
	at sun.reflect.GeneratedMethodAccessor330.invoke(Unknown Source)
	at sun.reflect.DelegatingMethodAccessorImpl.invoke(DelegatingMethodAccessorImpl.java:43)
	at java.lang.reflect.Method.invoke(Method.java:498)
	at sun.rmi.server.UnicastServerRef.dispatch(UnicastServerRef.java:357)
	at sun.rmi.transport.Transport$1.run(Transport.java:200)
	at sun.rmi.transport.Transport$1.run(Transport.java:197)
	at java.security.AccessController.doPrivileged(Native Method)
	at sun.rmi.transport.Transport.serviceCall(Transport.java:196)
	at sun.rmi.transport.tcp.TCPTransport.handleMessages(TCPTransport.java:573)
	at sun.rmi.transport.tcp.TCPTransport$ConnectionHandler.run0(TCPTransport.java:834)
	at sun.rmi.transport.tcp.TCPTransport$ConnectionHandler.lambda$run$0(TCPTransport.java:688)
	at java.security.AccessController.doPrivileged(Native Method)
	at sun.rmi.transport.tcp.TCPTransport$ConnectionHandler.run(TCPTransport.java:687)
	at java.util.concurrent.ThreadPoolExecutor.runWorker(ThreadPoolExecutor.java:1149)
	at java.util.concurrent.ThreadPoolExecutor$Worker.run(ThreadPoolExecutor.java:624)
	at java.lang.Thread.run(Thread.java:748)
Caused by: javax.naming.CommunicationException: localhost:389
	at com.sun.jndi.ldap.Connection.<init>(Connection.java:238)
	at com.sun.jndi.ldap.LdapClient.<init>(LdapClient.java:137)
	at com.sun.jndi.ldap.LdapClient.getInstance(LdapClient.java:1609)
	at com.sun.jndi.ldap.LdapCtx.connect(LdapCtx.java:2749)
	at com.sun.jndi.ldap.LdapCtx.<init>(LdapCtx.java:319)
	at com.sun.jndi.ldap.LdapCtxFactory.getUsingURL(LdapCtxFactory.java:192)
	at com.sun.jndi.ldap.LdapCtxFactory.getUsingURLs(LdapCtxFactory.java:210)
	at com.sun.jndi.ldap.LdapCtxFactory.getLdapCtxInstance(LdapCtxFactory.java:153)
	at com.sun.jndi.ldap.LdapCtxFactory.getInitialContext(LdapCtxFactory.java:83)
	at javax.naming.spi.NamingManager.getInitialContext(NamingManager.java:684)
	at javax.naming.InitialContext.getDefaultInitCtx(InitialContext.java:313)
	at javax.naming.InitialContext.init(InitialContext.java:244)
	at javax.naming.ldap.InitialLdapContext.<init>(InitialLdapContext.java:154)
	at org.springframework.ldap.core.support.LdapContextSource.getDirContextInstance(LdapContextSource.java:42)
	at org.springframework.ldap.core.support.AbstractContextSource.createContext(AbstractContextSource.java:343)
	... 47 common frames omitted
Caused by: java.net.ConnectException: Connection refused: connect
	at java.net.DualStackPlainSocketImpl.connect0(Native Method)
	at java.net.DualStackPlainSocketImpl.socketConnect(DualStackPlainSocketImpl.java:79)
	at java.net.AbstractPlainSocketImpl.doConnect(AbstractPlainSocketImpl.java:350)
	at java.net.AbstractPlainSocketImpl.connectToAddress(AbstractPlainSocketImpl.java:206)
	at java.net.AbstractPlainSocketImpl.connect(AbstractPlainSocketImpl.java:188)
	at java.net.PlainSocketImpl.connect(PlainSocketImpl.java:172)
	at java.net.SocksSocketImpl.connect(SocksSocketImpl.java:392)
	at java.net.Socket.connect(Socket.java:589)
	at java.net.Socket.connect(Socket.java:538)
	at java.net.Socket.<init>(Socket.java:434)
	at java.net.Socket.<init>(Socket.java:211)
	at com.sun.jndi.ldap.Connection.createSocket(Connection.java:375)
	at com.sun.jndi.ldap.Connection.<init>(Connection.java:215)
	... 61 common frames omitted
2025-06-18 14:20:00.021 [pool-14-thread-8] INFO  com.simbest.boot.component.task.AbstractTaskSchedule.checkAndExecute Line:198 - 集群主控节点主机地址【************】运行端口【10031】,当前主机地址【***********】运行端口【8092】, 无法执行定时任务【com.simbest.boot.bps.task.AutoCloseTodoTask】【794】
2025-06-18 14:20:00.021 [pool-14-thread-9] INFO  com.simbest.boot.component.task.AbstractTaskSchedule.checkAndExecute Line:198 - 集群主控节点主机地址【************】运行端口【10031】,当前主机地址【***********】运行端口【8092】, 无法执行定时任务【com.simbest.boot.bps.task.AutoCheckWithUpdateTask】【795】
2025-06-18 14:20:00.022 [pool-14-thread-4] INFO  com.simbest.boot.component.task.AbstractTaskSchedule.checkAndExecute Line:198 - 集群主控节点主机地址【************】运行端口【10031】,当前主机地址【***********】运行端口【8092】, 无法执行定时任务【com.simbest.boot.processdata.pushWorkmanager.task.SyncProcessDataToWorkManagerTask】【790】
2025-06-18 14:20:00.021 [pool-14-thread-3] INFO  com.simbest.boot.component.task.AbstractTaskSchedule.checkAndExecute Line:198 - 集群主控节点主机地址【************】运行端口【10031】,当前主机地址【***********】运行端口【8092】, 无法执行定时任务【com.simbest.boot.processdata.pushWorkmanager.task.SyncCommentDataToWorkManagerTask】【789】
2025-06-18 14:20:00.024 [pool-14-thread-7] INFO  com.simbest.boot.component.task.AbstractTaskSchedule.checkAndExecute Line:198 - 集群主控节点主机地址【************】运行端口【10031】,当前主机地址【***********】运行端口【8092】, 无法执行定时任务【com.simbest.boot.bps.task.AutoAddTodoTask】【793】
2025-06-18 14:20:00.025 [pool-14-thread-2] INFO  com.simbest.boot.component.task.AbstractTaskSchedule.checkAndExecute Line:198 - 集群主控节点主机地址【************】运行端口【10031】,当前主机地址【***********】运行端口【8092】, 无法执行定时任务【com.simbest.boot.processdata.pushWorkmanager.task.SyncTaskDataToWorkManagerTask】【788】
2025-06-18 14:20:00.027 [pool-14-thread-6] INFO  com.simbest.boot.component.task.AbstractTaskSchedule.checkAndExecute Line:198 - 集群主控节点主机地址【************】运行端口【10031】,当前主机地址【***********】运行端口【8092】, 无法执行定时任务【com.simbest.boot.component.task.HeartTestTask】【792】
2025-06-18 14:20:01.059 [pool-14-thread-1] DEBUG com.simbest.boot.component.distributed.lock.AppRuntimeMaster.becameMasertIfNotExist Line:182 - 集群主控节点不可用，当前主机将成为主控节点, 主机地址【***********】运行端口【8092】
2025-06-18 14:20:22.567 [http-nio-8092-exec-9] DEBUG com.simbest.boot.security.auth.filter.UumsAuthenticationFilter.doFilter Line:206 - Request is to process authentication
2025-06-18 14:20:22.569 [http-nio-8092-exec-9] DEBUG com.simbest.boot.util.redis.RedisRetryLoginCache.getKey Line:26 - 当前用户【chenhong】错误登录缓存键值为【LOGIN_FAILED:chenhong】
2025-06-18 14:20:22.571 [http-nio-8092-exec-9] DEBUG com.simbest.boot.util.redis.RedisRetryLoginCache.getTryTimes Line:38 - 当前用户【chenhong】错误登录已达到【0】次
2025-06-18 14:20:22.571 [http-nio-8092-exec-9] DEBUG com.simbest.boot.security.auth.filter.UumsAuthenticationFilter.attemptAuthentication Line:42 - UUMS主数据过滤器处理用户【chenhong】访问【nfwpz】，所持凭证信息为【OxZ9U9N1RIT4wcjWBtXOBnH9er9BMAgaC/ZSE/e+xuaGn6KJ1wMqPluFg/i8vwAwdyBMi5rcmq9qg6EeCLJWMTkYzf7ZBi/xblL2GS3T0ZPxzuiuNiWjmemzq4ouJcYMBDvyOIuCjeckDBgD8tzNHG7E2BARfStoOK5gTLPn0Sw=】
2025-06-18 14:20:22.573 [http-nio-8092-exec-9] DEBUG com.simbest.boot.security.util.AuthenticationUtil.authenticationIsRequired Line:77 - Authentication认证类型为【null】，认证主体为【chenhong】，判断是否需要进行认证结果为【true】
2025-06-18 14:20:22.578 [http-nio-8092-exec-9] DEBUG com.simbest.boot.util.encrypt.AbstractEncryptor.decryptBefore Line:28 - ----------decrypt source before value is: OxZ9U9N1RIT4wcjWBtXOBnH9er9BMAgaC/ZSE/e+xuaGn6KJ1wMqPluFg/i8vwAwdyBMi5rcmq9qg6EeCLJWMTkYzf7ZBi/xblL2GS3T0ZPxzuiuNiWjmemzq4ouJcYMBDvyOIuCjeckDBgD8tzNHG7E2BARfStoOK5gTLPn0Sw=
2025-06-18 14:20:22.582 [http-nio-8092-exec-9] DEBUG com.simbest.boot.util.encrypt.AbstractEncryptor.decryptAfter Line:32 - ----------decrypt source after value is: 111.com
2025-06-18 14:20:22.588 [http-nio-8092-exec-9] DEBUG com.simbest.boot.util.encrypt.AbstractEncryptor.encryptBefore Line:20 - ++++++++++encrypt source before value is: chenhong
2025-06-18 14:20:22.588 [http-nio-8092-exec-9] DEBUG com.simbest.boot.util.encrypt.AbstractEncryptor.encryptAfter Line:24 - ++++++++++encrypt source after value is: a1m1rzDBPBrlRppf0lZoe267Zwmz0Pp+Pixts2c7RrFZ8XqyhylBTMY7zfpCHJbJIBhzARDxp7BwHqZr746f2DlhJ+rocS5mXM/btiQUd3tyelQ1fI2HdntbfEu9ER6Jz79L0DkimfkvxSxCtw92W/3xorDaatadq/s3FTSIgxM=
2025-06-18 14:20:22.615 [http-nio-8092-exec-9] INFO  com.simbest.boot.security.auth.provider.UumsHttpValidationAuthenticationProvider.authenticate Line:107 - @@@@@@@@@@@@@@@@@@@用户【chenhong】不需要通过4A进行认证@@@@@@@@@@@@@@@@@@@
2025-06-18 14:20:22.618 [http-nio-8092-exec-9] DEBUG com.simbest.boot.security.auth.provider.UumsHttpValidationAuthenticationProvider.authenticate Line:115 - UUMS主数据认证器处理用户【chenhong】访问【nfwpz】，即将向UUMS发起Http认证请求，所持凭证信息为【OxZ9U9N1RIT4wcjWBtXOBnH9er9BMAgaC/ZSE/e+xuaGn6KJ1wMqPluFg/i8vwAwdyBMi5rcmq9qg6EeCLJWMTkYzf7ZBi/xblL2GS3T0ZPxzuiuNiWjmemzq4ouJcYMBDvyOIuCjeckDBgD8tzNHG7E2BARfStoOK5gTLPn0Sw=】
2025-06-18 14:20:23.341 [http-nio-8092-exec-9] DEBUG com.simbest.boot.security.auth.provider.UumsHttpValidationAuthenticationProvider.authenticate Line:148 - UUMS登录认证器恭喜用户【chenhong】通过密码【111.com】访问【nfwpz】鉴权认证在UUMS远程认证成功，可以登录成功！Congratulations user 【chenhong】 auth cache password successfully !
2025-06-18 14:20:23.341 [http-nio-8092-exec-9] INFO  com.simbest.boot.security.auth.provider.UumsHttpValidationAuthenticationProvider.authenticate Line:149 - UUMS登录认证器UUMS主数据认证器处理用户【chenhong】访问【nfwpz】成功！
2025-06-18 14:20:23.368 [http-nio-8092-exec-9] DEBUG com.simbest.boot.security.auth.service.AbstractAuthService.findByKey Line:129 - 通过关键字【chenhong】和关键字类型【username】应用代码【hnjjwz】获取用户信息为【com.simbest.boot.security.SimpleUser@335dee85[id=12704,username=chenhong,password=<null>,truename=陈虹,nickname=陈虹,email=<EMAIL>,preferredMobile=***********,telephoneNumber=613711,genderDictValue=2,isCmcc=true,startTime=2009-04-07T13:45,endTime=2099-12-30T12:00,status=0,passwordModifiedDate=2025-01-20T16:00:12,displayOrder=10000,employeeNumber=E0025310008,duty=00250074003000000000-HA0013,positionLevel=7,employeeTypeDictValue=1,employeeTypeDictDesc=<null>,photo=<null>,userType=1,openid=AA,unionid=<null>,enabled=true,accountNonExpired=true,accountNonLocked=true,credentialsNonExpired=true,currentBloc=cmcc,currentBlocCode=00,authBlocs=[SimpleBloc(id=cmcc, blocCode=00, blocName=河南移动, blocDesc=null)],currentCorp=1,currentCorpCode=00,authCorps=[SimpleCorp(id=1, blocId=cmcc, corpCode=00, corpName=省公司, corpShortName=null, corpDesc=null, corpContent=null, corpMail=null, corpTel=null, corpLegalPerson=null, postcode=null, address=null, industry=null, registerLocation=null, belongArea=null, webUrl=null, linkMan=null, linkManPhone=null, linkManMail=null)],authOrgs=[SimpleOrg(id=2574, orgCode=6812818319486287321, orgName=综合组织室, parentOrgCode=4772340095222653632, displayOrder=10000, displayName=省公司\纪委办公室（直属机关纪委）\综合组织室, belongCompanyCode=4772338661636601428, belongDepartmentCode=4772340095222653632, levelDictValue=3, reserve1=null, reserve2=null, reserve3=null, reserve4=null, reserve5=null, styleDictValue=07, companyTypeDictValue=01, erpId=null, corpId=1, isCorpRoot=false)],authPositions=[SimplePosition(id=57, positionName=三级经理, positionCompany=S, positionCode=6, positionType=null)],authRoles=[SimpleRole(id=391, roleCode=ROLE_CP_FY_SFJ, roleName=个人综合测评省公司三级经理角色, isApplicationRole=true, displayOrder=391, authority=ROLE_CP_FY_SFJ), SimpleRole(id=403, roleCode=ROLE_CP_SSJ_ZZ, roleName=个人综合测评省公司三级经理（组织评价使用）, isApplicationRole=true, displayOrder=403, authority=ROLE_CP_SSJ_ZZ), SimpleRole(id=85, roleCode=ROLE_SHENG, roleName=省公司一般员工, isApplicationRole=true, displayOrder=85, authority=ROLE_SHENG), SimpleRole(id=ncmssb001, roleCode=ROLE_NCMSSB_ADMIN, roleName=分公司信息上报-纪检监察室新闻宣传员, isApplicationRole=true, displayOrder=150, authority=ROLE_NCMSSB_ADMIN), SimpleRole(id=R617022674316558336, roleCode=ROLE_TWO_XWXC, roleName=二级新闻宣传员, isApplicationRole=true, displayOrder=1, authority=ROLE_TWO_XWXC), SimpleRole(id=75, roleCode=NO_MANAGER, roleName=全体人员不包括二级以上, isApplicationRole=true, displayOrder=75, authority=NO_MANAGER), SimpleRole(id=rcsc_003, roleCode=ROLE_GWBM_RCSC, roleName=人才市场-人员明细表、岗位报名, isApplicationRole=true, displayOrder=1, authority=ROLE_GWBM_RCSC), SimpleRole(id=97, roleCode=ROLE_SY_YGZZ, roleName=个人综合测评省公司一般员工角色, isApplicationRole=true, displayOrder=97, authority=ROLE_SY_YGZZ), SimpleRole(id=nhqyd03, roleCode=ROLE_NHQYD_QUERY_THREE, roleName=后勤移动管理三级查询权限人员, isApplicationRole=true, displayOrder=3, authority=ROLE_NHQYD_QUERY_THREE), SimpleRole(id=89, roleCode=ROLE_HYJ, roleName=省纪检活页夹访问权限, isApplicationRole=true, displayOrder=89, authority=ROLE_HYJ), SimpleRole(id=40, roleCode=ROLE_USER, roleName=普通用户, isApplicationRole=false, displayOrder=40, authority=ROLE_USER), SimpleRole(id=119, roleCode=ROLE_SHENG_JIJIAN, roleName=省公司纪检监察全部人员, isApplicationRole=true, displayOrder=119, authority=ROLE_SHENG_JIJIAN), SimpleRole(id=126, roleCode=ROLE_USER_NO, roleName=普通用户（除特殊人员）, isApplicationRole=true, displayOrder=126, authority=ROLE_USER_NO), SimpleRole(id=109, roleCode=ROLE_5G_CG, roleName=5G参观审批起草人员, isApplicationRole=true, displayOrder=109, authority=ROLE_5G_CG), SimpleRole(id=ejjlcp_361, roleCode=ROLE_YGMY_CP, roleName=员工满意度测评人员, isApplicationRole=true, displayOrder=1, authority=ROLE_YGMY_CP), SimpleRole(id=niafif_001, roleCode=ROLE_NIAFIF_DEPARTMENT_MANAGER, roleName=内部请示-部门考勤管理员, isApplicationRole=true, displayOrder=1, authority=ROLE_NIAFIF_DEPARTMENT_MANAGER), SimpleRole(id=72, roleCode=ROLE_PROVINCE_COMPANY_ALLUSER, roleName=省公司全体人员, isApplicationRole=true, displayOrder=72, authority=ROLE_PROVINCE_COMPANY_ALLUSER), SimpleRole(id=zjrc04, roleCode=ROLE_ZJRC_QUERY_FOUR, roleName=专家人才四级查询权限人员, isApplicationRole=true, displayOrder=4, authority=ROLE_ZJRC_QUERY_FOUR), SimpleRole(id=80, roleCode=ROLE_DISCIPLINE, roleName=纪检监察人员, isApplicationRole=true, displayOrder=80, authority=ROLE_DISCIPLINE), SimpleRole(id=172, roleCode=ROLE_PROVINCE_COMPANY_ALLUSER_NO, roleName=省公司全体人员(不包含管理层), isApplicationRole=true, displayOrder=172, authority=ROLE_PROVINCE_COMPANY_ALLUSER_NO)],authPermissions=<null>,authorities=[MySimpleGrantedAuthority(authority=ROLE_SHENG), MySimpleGrantedAuthority(authority=ROLE_YGMY_CP), MySimpleGrantedAuthority(authority=NO_MANAGER), MySimpleGrantedAuthority(authority=ROLE_GWBM_RCSC), MySimpleGrantedAuthority(authority=ROLE_SY_YGZZ), MySimpleGrantedAuthority(authority=ROLE_NIAFIF_DEPARTMENT_MANAGER), MySimpleGrantedAuthority(authority=ROLE_USER), MySimpleGrantedAuthority(authority=ROLE_CP_SSJ_ZZ), MySimpleGrantedAuthority(authority=ROLE_DISCIPLINE), MySimpleGrantedAuthority(authority=ROLE_TWO_XWXC), MySimpleGrantedAuthority(authority=ROLE_PROVINCE_COMPANY_ALLUSER), MySimpleGrantedAuthority(authority=ROLE_NHQYD_QUERY_THREE), MySimpleGrantedAuthority(authority=ROLE_ZJRC_QUERY_FOUR), MySimpleGrantedAuthority(authority=ROLE_5G_CG), MySimpleGrantedAuthority(authority=ROLE_USER_NO), MySimpleGrantedAuthority(authority=ROLE_CP_FY_SFJ), MySimpleGrantedAuthority(authority=ROLE_NCMSSB_ADMIN), MySimpleGrantedAuthority(authority=ROLE_PROVINCE_COMPANY_ALLUSER_NO), MySimpleGrantedAuthority(authority=ROLE_SHENG_JIJIAN), MySimpleGrantedAuthority(authority=ROLE_HYJ)],authUserOrgs=[SimpleUserOrg(id=UO572633246844583936, orgCode=6812818319486287321, username=chenhong, positionId=57, displayOrder=0, status=0)],belongCompanyCode=4772338661636601428,belongCompanyName=省公司,belongCompanyCodeParent=00000000000000000000,belongCompanyNameParent=河南移动,belongCompanyTypeDictValue=01,belongCompanyTypeDictDesc=<null>,belongDepartmentCode=4772340095222653632,belongDepartmentName=纪委办公室（直属机关纪委）,belongOrgCode=6812818319486287321,belongOrgName=综合组织室,reserve1=<null>,reserve2=<null>,reserve3=<null>,reserve4=rcZR95fmN7dzbl39EqGiPQ3D7fiDOiaAOKGmWo3vv/kZqBW2pYVvvXEU58htJcgtTKmhw1l+28mkQbr7k3XZKoLCC2eBf0aXp53QuMITi1c6kGHZ1PN8Yi7WWILix0yqFYFD0jxUtjZSFjh5823ykF6gBGkGhj3rkit3HpylCys=,reserve5=00]】
2025-06-18 14:20:23.394 [http-nio-8092-exec-9] DEBUG com.simbest.boot.uums.api.user.UumsSysUserinfoApi.checkUserAccessAppNoramal Line:740 - Http remote request user by username: chenhong
2025-06-18 14:20:23.395 [http-nio-8092-exec-9] DEBUG com.simbest.boot.util.encrypt.AbstractEncryptor.encryptBefore Line:20 - ++++++++++encrypt source before value is: chenhong
2025-06-18 14:20:23.395 [http-nio-8092-exec-9] DEBUG com.simbest.boot.util.encrypt.AbstractEncryptor.encryptAfter Line:24 - ++++++++++encrypt source after value is: Hn0xtrcq8wfoq6gKNSWaup7l9z1dR/lIyONEN6S5AnC19a2L5ow4BjgXQ4G1YdAzG3SdfpQ2ZwNaTzrQdrwqn6SOk33FmB4pYeSkuU3126vNlKdFU1uUqdQdtyAuunwNOoPQ+6pFoqkpaBQWfNvFiye7qx8cVh/P9Dx7IyFNsqc=
2025-06-18 14:20:23.600 [http-nio-8092-exec-9] DEBUG com.simbest.boot.uums.api.user.UumsSysUserinfoApi.findPermissionByAppUserNormal Line:788 - Http remote request user by username: chenhong
2025-06-18 14:20:23.600 [http-nio-8092-exec-9] DEBUG com.simbest.boot.util.encrypt.AbstractEncryptor.encryptBefore Line:20 - ++++++++++encrypt source before value is: chenhong
2025-06-18 14:20:23.601 [http-nio-8092-exec-9] DEBUG com.simbest.boot.util.encrypt.AbstractEncryptor.encryptAfter Line:24 - ++++++++++encrypt source after value is: IpWbpo5QgSw7Pr1zHJJu1zwWvzsquSfW5yHA27xl6Dz45Izvu5xVbhiLnKFlGtrLv3TDZf9aAcRsyy1xktfD9AFZcyhKS4z8mTXtgLaO6d7SL6dbB2L9l3TNirPJDfoW1LjDgdVAz+pMAd0SX1u/ppG86o5d8E/YpalgWaSi88k=
2025-06-18 14:20:23.894 [http-nio-8092-exec-9] DEBUG com.simbest.boot.security.auth.provider.GenericAuthenticationChecker.authChek Line:79 - 即将为用户【chenhong】在应用【nfwpz】追加【4】项权限，追加的具体权限为【[SimplePermission(id=20, permissionCode=mywork:todo, description=我的待办, url=html/process/processTask.html, icon=null, menuLevel=3, displayOrder=1, type=访问路径, parentId=19, remark=我的待办, authority=mywork:todo), SimplePermission(id=19, permissionCode=mywork:module, description=我的工作, url=null, icon=null, menuLevel=2, displayOrder=1, type=功能模块, parentId=18, remark=我的工作, authority=mywork:module), SimplePermission(id=800, permissionCode=help, description=帮助手册, url=html/help/help.html, icon=null, menuLevel=2, displayOrder=4, type=功能模块, parentId=18, remark=帮助手册, authority=help), SimplePermission(id=21, permissionCode=mywork:jointodo, description=我的已办, url=html/process/processJoin.html, icon=null, menuLevel=3, displayOrder=2, type=访问路径, parentId=19, remark=我的已办, authority=mywork:jointodo)]】
2025-06-18 14:20:23.905 [http-nio-8092-exec-9] INFO  com.simbest.boot.security.auth.provider.GenericAuthenticationChecker.authChek Line:95 - 用户【chenhong】访问【nfwpz】认证成功！
2025-06-18 14:20:23.907 [http-nio-8092-exec-9] DEBUG com.simbest.boot.security.auth.filter.UumsAuthenticationFilter.successfulAuthentication Line:312 - Authentication success. Updating SecurityContextHolder to contain: com.simbest.boot.security.auth.authentication.GenericAuthentication@6b79086f[principal=com.simbest.boot.security.SimpleUser@335dee85[id=12704,username=chenhong,password=<null>,truename=陈虹,nickname=陈虹,email=<EMAIL>,preferredMobile=***********,telephoneNumber=613711,genderDictValue=2,isCmcc=true,startTime=2009-04-07T13:45,endTime=2099-12-30T12:00,status=0,passwordModifiedDate=2025-01-20T16:00:12,displayOrder=10000,employeeNumber=E0025310008,duty=00250074003000000000-HA0013,positionLevel=7,employeeTypeDictValue=1,employeeTypeDictDesc=<null>,photo=<null>,userType=1,openid=AA,unionid=<null>,enabled=true,accountNonExpired=true,accountNonLocked=true,credentialsNonExpired=true,currentBloc=cmcc,currentBlocCode=00,authBlocs=[SimpleBloc(id=cmcc, blocCode=00, blocName=河南移动, blocDesc=null)],currentCorp=1,currentCorpCode=00,authCorps=[SimpleCorp(id=1, blocId=cmcc, corpCode=00, corpName=省公司, corpShortName=null, corpDesc=null, corpContent=null, corpMail=null, corpTel=null, corpLegalPerson=null, postcode=null, address=null, industry=null, registerLocation=null, belongArea=null, webUrl=null, linkMan=null, linkManPhone=null, linkManMail=null)],authOrgs=[SimpleOrg(id=2574, orgCode=6812818319486287321, orgName=综合组织室, parentOrgCode=4772340095222653632, displayOrder=10000, displayName=省公司\纪委办公室（直属机关纪委）\综合组织室, belongCompanyCode=4772338661636601428, belongDepartmentCode=4772340095222653632, levelDictValue=3, reserve1=null, reserve2=null, reserve3=null, reserve4=null, reserve5=null, styleDictValue=07, companyTypeDictValue=01, erpId=null, corpId=1, isCorpRoot=false)],authPositions=[SimplePosition(id=57, positionName=三级经理, positionCompany=S, positionCode=6, positionType=null)],authRoles=[SimpleRole(id=391, roleCode=ROLE_CP_FY_SFJ, roleName=个人综合测评省公司三级经理角色, isApplicationRole=true, displayOrder=391, authority=ROLE_CP_FY_SFJ), SimpleRole(id=403, roleCode=ROLE_CP_SSJ_ZZ, roleName=个人综合测评省公司三级经理（组织评价使用）, isApplicationRole=true, displayOrder=403, authority=ROLE_CP_SSJ_ZZ), SimpleRole(id=85, roleCode=ROLE_SHENG, roleName=省公司一般员工, isApplicationRole=true, displayOrder=85, authority=ROLE_SHENG), SimpleRole(id=ncmssb001, roleCode=ROLE_NCMSSB_ADMIN, roleName=分公司信息上报-纪检监察室新闻宣传员, isApplicationRole=true, displayOrder=150, authority=ROLE_NCMSSB_ADMIN), SimpleRole(id=R617022674316558336, roleCode=ROLE_TWO_XWXC, roleName=二级新闻宣传员, isApplicationRole=true, displayOrder=1, authority=ROLE_TWO_XWXC), SimpleRole(id=75, roleCode=NO_MANAGER, roleName=全体人员不包括二级以上, isApplicationRole=true, displayOrder=75, authority=NO_MANAGER), SimpleRole(id=rcsc_003, roleCode=ROLE_GWBM_RCSC, roleName=人才市场-人员明细表、岗位报名, isApplicationRole=true, displayOrder=1, authority=ROLE_GWBM_RCSC), SimpleRole(id=97, roleCode=ROLE_SY_YGZZ, roleName=个人综合测评省公司一般员工角色, isApplicationRole=true, displayOrder=97, authority=ROLE_SY_YGZZ), SimpleRole(id=nhqyd03, roleCode=ROLE_NHQYD_QUERY_THREE, roleName=后勤移动管理三级查询权限人员, isApplicationRole=true, displayOrder=3, authority=ROLE_NHQYD_QUERY_THREE), SimpleRole(id=89, roleCode=ROLE_HYJ, roleName=省纪检活页夹访问权限, isApplicationRole=true, displayOrder=89, authority=ROLE_HYJ), SimpleRole(id=40, roleCode=ROLE_USER, roleName=普通用户, isApplicationRole=false, displayOrder=40, authority=ROLE_USER), SimpleRole(id=119, roleCode=ROLE_SHENG_JIJIAN, roleName=省公司纪检监察全部人员, isApplicationRole=true, displayOrder=119, authority=ROLE_SHENG_JIJIAN), SimpleRole(id=126, roleCode=ROLE_USER_NO, roleName=普通用户（除特殊人员）, isApplicationRole=true, displayOrder=126, authority=ROLE_USER_NO), SimpleRole(id=109, roleCode=ROLE_5G_CG, roleName=5G参观审批起草人员, isApplicationRole=true, displayOrder=109, authority=ROLE_5G_CG), SimpleRole(id=ejjlcp_361, roleCode=ROLE_YGMY_CP, roleName=员工满意度测评人员, isApplicationRole=true, displayOrder=1, authority=ROLE_YGMY_CP), SimpleRole(id=niafif_001, roleCode=ROLE_NIAFIF_DEPARTMENT_MANAGER, roleName=内部请示-部门考勤管理员, isApplicationRole=true, displayOrder=1, authority=ROLE_NIAFIF_DEPARTMENT_MANAGER), SimpleRole(id=72, roleCode=ROLE_PROVINCE_COMPANY_ALLUSER, roleName=省公司全体人员, isApplicationRole=true, displayOrder=72, authority=ROLE_PROVINCE_COMPANY_ALLUSER), SimpleRole(id=zjrc04, roleCode=ROLE_ZJRC_QUERY_FOUR, roleName=专家人才四级查询权限人员, isApplicationRole=true, displayOrder=4, authority=ROLE_ZJRC_QUERY_FOUR), SimpleRole(id=80, roleCode=ROLE_DISCIPLINE, roleName=纪检监察人员, isApplicationRole=true, displayOrder=80, authority=ROLE_DISCIPLINE), SimpleRole(id=172, roleCode=ROLE_PROVINCE_COMPANY_ALLUSER_NO, roleName=省公司全体人员(不包含管理层), isApplicationRole=true, displayOrder=172, authority=ROLE_PROVINCE_COMPANY_ALLUSER_NO)],authPermissions=[SimplePermission(id=20, permissionCode=mywork:todo, description=我的待办, url=html/process/processTask.html, icon=null, menuLevel=3, displayOrder=1, type=访问路径, parentId=19, remark=我的待办, authority=mywork:todo), SimplePermission(id=19, permissionCode=mywork:module, description=我的工作, url=null, icon=null, menuLevel=2, displayOrder=1, type=功能模块, parentId=18, remark=我的工作, authority=mywork:module), SimplePermission(id=800, permissionCode=help, description=帮助手册, url=html/help/help.html, icon=null, menuLevel=2, displayOrder=4, type=功能模块, parentId=18, remark=帮助手册, authority=help), SimplePermission(id=21, permissionCode=mywork:jointodo, description=我的已办, url=html/process/processJoin.html, icon=null, menuLevel=3, displayOrder=2, type=访问路径, parentId=19, remark=我的已办, authority=mywork:jointodo)],authorities=[MySimpleGrantedAuthority(authority=ROLE_SHENG), MySimpleGrantedAuthority(authority=ROLE_YGMY_CP), MySimpleGrantedAuthority(authority=NO_MANAGER), MySimpleGrantedAuthority(authority=ROLE_GWBM_RCSC), MySimpleGrantedAuthority(authority=ROLE_SY_YGZZ), MySimpleGrantedAuthority(authority=ROLE_NIAFIF_DEPARTMENT_MANAGER), MySimpleGrantedAuthority(authority=help), MySimpleGrantedAuthority(authority=ROLE_USER), MySimpleGrantedAuthority(authority=ROLE_CP_SSJ_ZZ), MySimpleGrantedAuthority(authority=ROLE_DISCIPLINE), MySimpleGrantedAuthority(authority=mywork:module), MySimpleGrantedAuthority(authority=ROLE_TWO_XWXC), MySimpleGrantedAuthority(authority=mywork:todo), MySimpleGrantedAuthority(authority=mywork:jointodo), MySimpleGrantedAuthority(authority=ROLE_PROVINCE_COMPANY_ALLUSER), MySimpleGrantedAuthority(authority=ROLE_NHQYD_QUERY_THREE), MySimpleGrantedAuthority(authority=ROLE_ZJRC_QUERY_FOUR), MySimpleGrantedAuthority(authority=ROLE_5G_CG), MySimpleGrantedAuthority(authority=ROLE_USER_NO), MySimpleGrantedAuthority(authority=ROLE_CP_FY_SFJ), MySimpleGrantedAuthority(authority=ROLE_NCMSSB_ADMIN), MySimpleGrantedAuthority(authority=ROLE_PROVINCE_COMPANY_ALLUSER_NO), MySimpleGrantedAuthority(authority=ROLE_SHENG_JIJIAN), MySimpleGrantedAuthority(authority=ROLE_HYJ)],authUserOrgs=[SimpleUserOrg(id=UO572633246844583936, orgCode=6812818319486287321, username=chenhong, positionId=57, displayOrder=0, status=0)],belongCompanyCode=4772338661636601428,belongCompanyName=省公司,belongCompanyCodeParent=00000000000000000000,belongCompanyNameParent=河南移动,belongCompanyTypeDictValue=01,belongCompanyTypeDictDesc=<null>,belongDepartmentCode=4772340095222653632,belongDepartmentName=纪委办公室（直属机关纪委）,belongOrgCode=6812818319486287321,belongOrgName=综合组织室,reserve1=<null>,reserve2=<null>,reserve3=<null>,reserve4=rcZR95fmN7dzbl39EqGiPQ3D7fiDOiaAOKGmWo3vv/kZqBW2pYVvvXEU58htJcgtTKmhw1l+28mkQbr7k3XZKoLCC2eBf0aXp53QuMITi1c6kGHZ1PN8Yi7WWILix0yqFYFD0jxUtjZSFjh5823ykF6gBGkGhj3rkit3HpylCys=,reserve5=00],credentials=com.simbest.boot.security.auth.authentication.UumsAuthenticationCredentials@1223bc65[password=<null>,appcode=nfwpz],authorities=[MySimpleGrantedAuthority(authority=ROLE_SHENG), MySimpleGrantedAuthority(authority=ROLE_YGMY_CP), MySimpleGrantedAuthority(authority=NO_MANAGER), MySimpleGrantedAuthority(authority=ROLE_GWBM_RCSC), MySimpleGrantedAuthority(authority=ROLE_SY_YGZZ), MySimpleGrantedAuthority(authority=ROLE_NIAFIF_DEPARTMENT_MANAGER), MySimpleGrantedAuthority(authority=help), MySimpleGrantedAuthority(authority=ROLE_USER), MySimpleGrantedAuthority(authority=ROLE_CP_SSJ_ZZ), MySimpleGrantedAuthority(authority=ROLE_DISCIPLINE), MySimpleGrantedAuthority(authority=mywork:module), MySimpleGrantedAuthority(authority=ROLE_TWO_XWXC), MySimpleGrantedAuthority(authority=mywork:todo), MySimpleGrantedAuthority(authority=mywork:jointodo), MySimpleGrantedAuthority(authority=ROLE_PROVINCE_COMPANY_ALLUSER), MySimpleGrantedAuthority(authority=ROLE_NHQYD_QUERY_THREE), MySimpleGrantedAuthority(authority=ROLE_ZJRC_QUERY_FOUR), MySimpleGrantedAuthority(authority=ROLE_5G_CG), MySimpleGrantedAuthority(authority=ROLE_USER_NO), MySimpleGrantedAuthority(authority=ROLE_CP_FY_SFJ), MySimpleGrantedAuthority(authority=ROLE_NCMSSB_ADMIN), MySimpleGrantedAuthority(authority=ROLE_PROVINCE_COMPANY_ALLUSER_NO), MySimpleGrantedAuthority(authority=ROLE_SHENG_JIJIAN), MySimpleGrantedAuthority(authority=ROLE_HYJ)],details=LoginWebAuthenticationDetails(details=LoginPreWebAuthenticationDetails(remoteAddress=0:0:0:0:0:0:0:1, sessionId=b6a71246-4c10-406a-ac13-11048ea03468), applyUsername=null),authenticated=true]
2025-06-18 14:20:23.909 [http-nio-8092-exec-9] DEBUG com.simbest.boot.security.auth.handle.SuccessLoginHandler.onAuthenticationSuccess Line:37 - 用户【chenhong】登录成功，用户身份详细信息为【com.simbest.boot.security.SimpleUser@335dee85[id=12704,username=chenhong,password=<null>,truename=陈虹,nickname=陈虹,email=<EMAIL>,preferredMobile=***********,telephoneNumber=613711,genderDictValue=2,isCmcc=true,startTime=2009-04-07T13:45,endTime=2099-12-30T12:00,status=0,passwordModifiedDate=2025-01-20T16:00:12,displayOrder=10000,employeeNumber=E0025310008,duty=00250074003000000000-HA0013,positionLevel=7,employeeTypeDictValue=1,employeeTypeDictDesc=<null>,photo=<null>,userType=1,openid=AA,unionid=<null>,enabled=true,accountNonExpired=true,accountNonLocked=true,credentialsNonExpired=true,currentBloc=cmcc,currentBlocCode=00,authBlocs=[SimpleBloc(id=cmcc, blocCode=00, blocName=河南移动, blocDesc=null)],currentCorp=1,currentCorpCode=00,authCorps=[SimpleCorp(id=1, blocId=cmcc, corpCode=00, corpName=省公司, corpShortName=null, corpDesc=null, corpContent=null, corpMail=null, corpTel=null, corpLegalPerson=null, postcode=null, address=null, industry=null, registerLocation=null, belongArea=null, webUrl=null, linkMan=null, linkManPhone=null, linkManMail=null)],authOrgs=[SimpleOrg(id=2574, orgCode=6812818319486287321, orgName=综合组织室, parentOrgCode=4772340095222653632, displayOrder=10000, displayName=省公司\纪委办公室（直属机关纪委）\综合组织室, belongCompanyCode=4772338661636601428, belongDepartmentCode=4772340095222653632, levelDictValue=3, reserve1=null, reserve2=null, reserve3=null, reserve4=null, reserve5=null, styleDictValue=07, companyTypeDictValue=01, erpId=null, corpId=1, isCorpRoot=false)],authPositions=[SimplePosition(id=57, positionName=三级经理, positionCompany=S, positionCode=6, positionType=null)],authRoles=[SimpleRole(id=391, roleCode=ROLE_CP_FY_SFJ, roleName=个人综合测评省公司三级经理角色, isApplicationRole=true, displayOrder=391, authority=ROLE_CP_FY_SFJ), SimpleRole(id=403, roleCode=ROLE_CP_SSJ_ZZ, roleName=个人综合测评省公司三级经理（组织评价使用）, isApplicationRole=true, displayOrder=403, authority=ROLE_CP_SSJ_ZZ), SimpleRole(id=85, roleCode=ROLE_SHENG, roleName=省公司一般员工, isApplicationRole=true, displayOrder=85, authority=ROLE_SHENG), SimpleRole(id=ncmssb001, roleCode=ROLE_NCMSSB_ADMIN, roleName=分公司信息上报-纪检监察室新闻宣传员, isApplicationRole=true, displayOrder=150, authority=ROLE_NCMSSB_ADMIN), SimpleRole(id=R617022674316558336, roleCode=ROLE_TWO_XWXC, roleName=二级新闻宣传员, isApplicationRole=true, displayOrder=1, authority=ROLE_TWO_XWXC), SimpleRole(id=75, roleCode=NO_MANAGER, roleName=全体人员不包括二级以上, isApplicationRole=true, displayOrder=75, authority=NO_MANAGER), SimpleRole(id=rcsc_003, roleCode=ROLE_GWBM_RCSC, roleName=人才市场-人员明细表、岗位报名, isApplicationRole=true, displayOrder=1, authority=ROLE_GWBM_RCSC), SimpleRole(id=97, roleCode=ROLE_SY_YGZZ, roleName=个人综合测评省公司一般员工角色, isApplicationRole=true, displayOrder=97, authority=ROLE_SY_YGZZ), SimpleRole(id=nhqyd03, roleCode=ROLE_NHQYD_QUERY_THREE, roleName=后勤移动管理三级查询权限人员, isApplicationRole=true, displayOrder=3, authority=ROLE_NHQYD_QUERY_THREE), SimpleRole(id=89, roleCode=ROLE_HYJ, roleName=省纪检活页夹访问权限, isApplicationRole=true, displayOrder=89, authority=ROLE_HYJ), SimpleRole(id=40, roleCode=ROLE_USER, roleName=普通用户, isApplicationRole=false, displayOrder=40, authority=ROLE_USER), SimpleRole(id=119, roleCode=ROLE_SHENG_JIJIAN, roleName=省公司纪检监察全部人员, isApplicationRole=true, displayOrder=119, authority=ROLE_SHENG_JIJIAN), SimpleRole(id=126, roleCode=ROLE_USER_NO, roleName=普通用户（除特殊人员）, isApplicationRole=true, displayOrder=126, authority=ROLE_USER_NO), SimpleRole(id=109, roleCode=ROLE_5G_CG, roleName=5G参观审批起草人员, isApplicationRole=true, displayOrder=109, authority=ROLE_5G_CG), SimpleRole(id=ejjlcp_361, roleCode=ROLE_YGMY_CP, roleName=员工满意度测评人员, isApplicationRole=true, displayOrder=1, authority=ROLE_YGMY_CP), SimpleRole(id=niafif_001, roleCode=ROLE_NIAFIF_DEPARTMENT_MANAGER, roleName=内部请示-部门考勤管理员, isApplicationRole=true, displayOrder=1, authority=ROLE_NIAFIF_DEPARTMENT_MANAGER), SimpleRole(id=72, roleCode=ROLE_PROVINCE_COMPANY_ALLUSER, roleName=省公司全体人员, isApplicationRole=true, displayOrder=72, authority=ROLE_PROVINCE_COMPANY_ALLUSER), SimpleRole(id=zjrc04, roleCode=ROLE_ZJRC_QUERY_FOUR, roleName=专家人才四级查询权限人员, isApplicationRole=true, displayOrder=4, authority=ROLE_ZJRC_QUERY_FOUR), SimpleRole(id=80, roleCode=ROLE_DISCIPLINE, roleName=纪检监察人员, isApplicationRole=true, displayOrder=80, authority=ROLE_DISCIPLINE), SimpleRole(id=172, roleCode=ROLE_PROVINCE_COMPANY_ALLUSER_NO, roleName=省公司全体人员(不包含管理层), isApplicationRole=true, displayOrder=172, authority=ROLE_PROVINCE_COMPANY_ALLUSER_NO)],authPermissions=[SimplePermission(id=20, permissionCode=mywork:todo, description=我的待办, url=html/process/processTask.html, icon=null, menuLevel=3, displayOrder=1, type=访问路径, parentId=19, remark=我的待办, authority=mywork:todo), SimplePermission(id=19, permissionCode=mywork:module, description=我的工作, url=null, icon=null, menuLevel=2, displayOrder=1, type=功能模块, parentId=18, remark=我的工作, authority=mywork:module), SimplePermission(id=800, permissionCode=help, description=帮助手册, url=html/help/help.html, icon=null, menuLevel=2, displayOrder=4, type=功能模块, parentId=18, remark=帮助手册, authority=help), SimplePermission(id=21, permissionCode=mywork:jointodo, description=我的已办, url=html/process/processJoin.html, icon=null, menuLevel=3, displayOrder=2, type=访问路径, parentId=19, remark=我的已办, authority=mywork:jointodo)],authorities=[MySimpleGrantedAuthority(authority=ROLE_SHENG), MySimpleGrantedAuthority(authority=ROLE_YGMY_CP), MySimpleGrantedAuthority(authority=NO_MANAGER), MySimpleGrantedAuthority(authority=ROLE_GWBM_RCSC), MySimpleGrantedAuthority(authority=ROLE_SY_YGZZ), MySimpleGrantedAuthority(authority=ROLE_NIAFIF_DEPARTMENT_MANAGER), MySimpleGrantedAuthority(authority=help), MySimpleGrantedAuthority(authority=ROLE_USER), MySimpleGrantedAuthority(authority=ROLE_CP_SSJ_ZZ), MySimpleGrantedAuthority(authority=ROLE_DISCIPLINE), MySimpleGrantedAuthority(authority=mywork:module), MySimpleGrantedAuthority(authority=ROLE_TWO_XWXC), MySimpleGrantedAuthority(authority=mywork:todo), MySimpleGrantedAuthority(authority=mywork:jointodo), MySimpleGrantedAuthority(authority=ROLE_PROVINCE_COMPANY_ALLUSER), MySimpleGrantedAuthority(authority=ROLE_NHQYD_QUERY_THREE), MySimpleGrantedAuthority(authority=ROLE_ZJRC_QUERY_FOUR), MySimpleGrantedAuthority(authority=ROLE_5G_CG), MySimpleGrantedAuthority(authority=ROLE_USER_NO), MySimpleGrantedAuthority(authority=ROLE_CP_FY_SFJ), MySimpleGrantedAuthority(authority=ROLE_NCMSSB_ADMIN), MySimpleGrantedAuthority(authority=ROLE_PROVINCE_COMPANY_ALLUSER_NO), MySimpleGrantedAuthority(authority=ROLE_SHENG_JIJIAN), MySimpleGrantedAuthority(authority=ROLE_HYJ)],authUserOrgs=[SimpleUserOrg(id=UO572633246844583936, orgCode=6812818319486287321, username=chenhong, positionId=57, displayOrder=0, status=0)],belongCompanyCode=4772338661636601428,belongCompanyName=省公司,belongCompanyCodeParent=00000000000000000000,belongCompanyNameParent=河南移动,belongCompanyTypeDictValue=01,belongCompanyTypeDictDesc=<null>,belongDepartmentCode=4772340095222653632,belongDepartmentName=纪委办公室（直属机关纪委）,belongOrgCode=6812818319486287321,belongOrgName=综合组织室,reserve1=<null>,reserve2=<null>,reserve3=<null>,reserve4=rcZR95fmN7dzbl39EqGiPQ3D7fiDOiaAOKGmWo3vv/kZqBW2pYVvvXEU58htJcgtTKmhw1l+28mkQbr7k3XZKoLCC2eBf0aXp53QuMITi1c6kGHZ1PN8Yi7WWILix0yqFYFD0jxUtjZSFjh5823ykF6gBGkGhj3rkit3HpylCys=,reserve5=00]】
2025-06-18 14:20:23.911 [http-nio-8092-exec-9] DEBUG com.simbest.boot.util.redis.RedisRetryLoginCache.getKey Line:26 - 当前用户【chenhong】错误登录缓存键值为【LOGIN_FAILED:chenhong】
2025-06-18 14:20:23.914 [http-nio-8092-exec-9] DEBUG com.simbest.boot.util.redis.RedisRetryLoginCache.cleanTryTimes Line:60 - 清空用户【chenhong】的历史登录错误记录数结果返回为【false】
2025-06-18 14:20:23.922 [http-nio-8092-exec-9] DEBUG com.simbest.boot.util.security.LoginUtils.recordLog Line:234 - 记录登录日志RecordLoginLogAdmin【SysLogLoginAdmin(id=null, account=chenhong, loginType=0, loginEntry=0, sessionid=b6a71246-4c10-406a-ac13-11048ea03468, ip=0:0:0:0:0:0:0:1, mac=null, loginTime=Wed Jun 18 14:20:23 CST 2025, logoutTime=null, isSuccess=true, remark=null, trueName=陈虹, belongOrgName=综合组织室, osFamily=Windows, os=Windows 10 or Windows Server 2016, browserName=Chrome, browserVersion=*********, browserEngine=Webkit, browserEngineVersion=537.36, isMobile=false, serverip=***********)】
2025-06-18 14:20:23.932 [http-nio-8092-exec-9] DEBUG com.simbest.boot.base.service.impl.GenericService.insert Line:359 - @Generic Repository Service create new object: SysLogLoginAdmin(id=null, account=chenhong, loginType=0, loginEntry=0, sessionid=b6a71246-4c10-406a-ac13-11048ea03468, ip=0:0:0:0:0:0:0:1, mac=null, loginTime=Wed Jun 18 14:20:23 CST 2025, logoutTime=null, isSuccess=true, remark=null, trueName=陈虹, belongOrgName=综合组织室, osFamily=Windows, os=Windows 10 or Windows Server 2016, browserName=Chrome, browserVersion=*********, browserEngine=Webkit, browserEngineVersion=537.36, isMobile=false, serverip=***********)
2025-06-18 14:20:23.945 [http-nio-8092-exec-9] DEBUG com.simbest.boot.base.repository.LogicDeleteRepositoryImpl.save Line:346 - 对象没有联合索引和联合主键，即将更新对象【com.simbest.boot.sys.model.SysLogLoginAdmin@27969b59[id=<null>,account=chenhong,loginType=0,loginEntry=0,sessionid=b6a71246-4c10-406a-ac13-11048ea03468,ip=0:0:0:0:0:0:0:1,mac=<null>,loginTime=Wed Jun 18 14:20:23 CST 2025,logoutTime=<null>,isSuccess=true,remark=<null>,trueName=陈虹,belongOrgName=综合组织室,osFamily=Windows,os=Windows 10 or Windows Server 2016,browserName=Chrome,browserVersion=*********,browserEngine=Webkit,browserEngineVersion=537.36,isMobile=false,serverip=***********,orderByClause=<null>,ssDate=<null>,eeDate=<null>,pageIndex=<null>,pagesize=<null>]】
2025-06-18 14:20:24.162 [http-nio-8092-exec-9] DEBUG com.simbest.boot.base.repository.LogicDeleteRepositoryImpl.save Line:348 - 对象没有联合索引和联合主键，更新对象后为【com.simbest.boot.sys.model.SysLogLoginAdmin@27969b59[id=L877557549991784448,account=chenhong,loginType=0,loginEntry=0,sessionid=b6a71246-4c10-406a-ac13-11048ea03468,ip=0:0:0:0:0:0:0:1,mac=<null>,loginTime=Wed Jun 18 14:20:23 CST 2025,logoutTime=<null>,isSuccess=true,remark=<null>,trueName=陈虹,belongOrgName=综合组织室,osFamily=Windows,os=Windows 10 or Windows Server 2016,browserName=Chrome,browserVersion=*********,browserEngine=Webkit,browserEngineVersion=537.36,isMobile=false,serverip=***********,orderByClause=<null>,ssDate=<null>,eeDate=<null>,pageIndex=<null>,pagesize=<null>]】
2025-06-18 14:20:24.164 [http-nio-8092-exec-9] DEBUG com.simbest.boot.base.service.impl.GenericService.insert Line:362 - @Generic Repository Service create new object successful： SysLogLoginAdmin(id=L877557549991784448, account=chenhong, loginType=0, loginEntry=0, sessionid=b6a71246-4c10-406a-ac13-11048ea03468, ip=0:0:0:0:0:0:0:1, mac=null, loginTime=Wed Jun 18 14:20:23 CST 2025, logoutTime=null, isSuccess=true, remark=null, trueName=陈虹, belongOrgName=综合组织室, osFamily=Windows, os=Windows 10 or Windows Server 2016, browserName=Chrome, browserVersion=*********, browserEngine=Webkit, browserEngineVersion=537.36, isMobile=false, serverip=***********)
2025-06-18 14:20:24.176 [http-nio-8092-exec-9] DEBUG org.hibernate.SQL.logStatement Line:127 - /* insert com.simbest.boot.sys.model.SysLogLoginAdmin */ insert into sys_log_login_admin (account, belong_org_name, browser_engine, browser_engine_version, browser_name, browser_version, ip, is_mobile, is_success, login_entry, login_time, login_type, logout_time, mac, os, os_family, remark, serverip, sessionid, true_name, id) values (?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?)
2025-06-18 14:20:24.177 [http-nio-8092-exec-9] TRACE org.hibernate.type.descriptor.sql.BasicBinder.bind Line:64 - binding parameter [1] as [VARCHAR] - [chenhong]
2025-06-18 14:20:24.177 [http-nio-8092-exec-9] TRACE org.hibernate.type.descriptor.sql.BasicBinder.bind Line:64 - binding parameter [2] as [VARCHAR] - [综合组织室]
2025-06-18 14:20:24.177 [http-nio-8092-exec-9] TRACE org.hibernate.type.descriptor.sql.BasicBinder.bind Line:64 - binding parameter [3] as [VARCHAR] - [Webkit]
2025-06-18 14:20:24.177 [http-nio-8092-exec-9] TRACE org.hibernate.type.descriptor.sql.BasicBinder.bind Line:64 - binding parameter [4] as [VARCHAR] - [537.36]
2025-06-18 14:20:24.178 [http-nio-8092-exec-9] TRACE org.hibernate.type.descriptor.sql.BasicBinder.bind Line:64 - binding parameter [5] as [VARCHAR] - [Chrome]
2025-06-18 14:20:24.178 [http-nio-8092-exec-9] TRACE org.hibernate.type.descriptor.sql.BasicBinder.bind Line:64 - binding parameter [6] as [VARCHAR] - [*********]
2025-06-18 14:20:24.178 [http-nio-8092-exec-9] TRACE org.hibernate.type.descriptor.sql.BasicBinder.bind Line:64 - binding parameter [7] as [VARCHAR] - [0:0:0:0:0:0:0:1]
2025-06-18 14:20:24.178 [http-nio-8092-exec-9] TRACE org.hibernate.type.descriptor.sql.BasicBinder.bind Line:64 - binding parameter [8] as [BIT] - [false]
2025-06-18 14:20:24.179 [http-nio-8092-exec-9] TRACE org.hibernate.type.descriptor.sql.BasicBinder.bind Line:64 - binding parameter [9] as [BIT] - [true]
2025-06-18 14:20:24.180 [http-nio-8092-exec-9] TRACE org.hibernate.type.descriptor.sql.BasicBinder.bind Line:64 - binding parameter [10] as [INTEGER] - [0]
2025-06-18 14:20:24.181 [http-nio-8092-exec-9] TRACE org.hibernate.type.descriptor.sql.BasicBinder.bind Line:64 - binding parameter [11] as [TIMESTAMP] - [Wed Jun 18 14:20:23 CST 2025]
2025-06-18 14:20:24.182 [http-nio-8092-exec-9] TRACE org.hibernate.type.descriptor.sql.BasicBinder.bind Line:64 - binding parameter [12] as [INTEGER] - [0]
2025-06-18 14:20:24.182 [http-nio-8092-exec-9] TRACE org.hibernate.type.descriptor.sql.BasicBinder.bind Line:52 - binding parameter [13] as [TIMESTAMP] - [null]
2025-06-18 14:20:24.182 [http-nio-8092-exec-9] TRACE org.hibernate.type.descriptor.sql.BasicBinder.bind Line:52 - binding parameter [14] as [VARCHAR] - [null]
2025-06-18 14:20:24.183 [http-nio-8092-exec-9] TRACE org.hibernate.type.descriptor.sql.BasicBinder.bind Line:64 - binding parameter [15] as [VARCHAR] - [Windows 10 or Windows Server 2016]
2025-06-18 14:20:24.183 [http-nio-8092-exec-9] TRACE org.hibernate.type.descriptor.sql.BasicBinder.bind Line:64 - binding parameter [16] as [VARCHAR] - [Windows]
2025-06-18 14:20:24.183 [http-nio-8092-exec-9] TRACE org.hibernate.type.descriptor.sql.BasicBinder.bind Line:52 - binding parameter [17] as [VARCHAR] - [null]
2025-06-18 14:20:24.184 [http-nio-8092-exec-9] TRACE org.hibernate.type.descriptor.sql.BasicBinder.bind Line:64 - binding parameter [18] as [VARCHAR] - [***********]
2025-06-18 14:20:24.184 [http-nio-8092-exec-9] TRACE org.hibernate.type.descriptor.sql.BasicBinder.bind Line:64 - binding parameter [19] as [VARCHAR] - [b6a71246-4c10-406a-ac13-11048ea03468]
2025-06-18 14:20:24.184 [http-nio-8092-exec-9] TRACE org.hibernate.type.descriptor.sql.BasicBinder.bind Line:64 - binding parameter [20] as [VARCHAR] - [陈虹]
2025-06-18 14:20:24.184 [http-nio-8092-exec-9] TRACE org.hibernate.type.descriptor.sql.BasicBinder.bind Line:64 - binding parameter [21] as [VARCHAR] - [L877557549991784448]
2025-06-18 14:20:24.263 [http-nio-8092-exec-9] DEBUG com.simbest.boot.security.auth.controller.IndexController.home Line:78 - 登录成功后获取url中的参数null
2025-06-18 14:20:24.551 [http-nio-8092-exec-14] DEBUG com.simbest.boot.uums.api.user.UumsSysUserinfoApi.findPermissionByAppUserNormal Line:788 - Http remote request user by username: chenhong
2025-06-18 14:20:24.551 [http-nio-8092-exec-14] DEBUG com.simbest.boot.util.encrypt.AbstractEncryptor.encryptBefore Line:20 - ++++++++++encrypt source before value is: chenhong
2025-06-18 14:20:24.552 [http-nio-8092-exec-14] DEBUG com.simbest.boot.util.encrypt.AbstractEncryptor.encryptAfter Line:24 - ++++++++++encrypt source after value is: sSL8gD6rGv06MmM0oFpMNkgpAW7/aRKBJpSHkGI3vq36BvU65CFL+QqHXSra2qi4gKbm8dN03nqSX5NtfWTScFn6wYvcw1n3DPh27oEFS/s198jvvfBHZHMTYNkX1qHN667IZ7NCkW2/3CAyh5FVnn7VwQwI2US7JOYtdPLzoX8=
2025-06-18 14:20:24.649 [http-nio-8092-exec-15] DEBUG com.simbest.boot.hnjjwz.util.OperateLogTool.operationSource Line:76 - session中有人，人为:chenhong
2025-06-18 14:20:24.655 [http-nio-8092-exec-15] DEBUG com.simbest.boot.sys.service.impl.SysDictValueCacheUtil.loadByParameters Line:89 - 通过Key键【SYS_DICT_VALUE_CACHE:hnjjwz:processType】返回数据字典值【null】
2025-06-18 14:20:24.720 [http-nio-8092-exec-15] DEBUG org.hibernate.SQL.logStatement Line:127 - /* dynamic native SQL query */ SELECT dv.* from sys_dict d,sys_dict_value dv WHERE d.dict_type=dv.dict_type and d.enabled=1 and dv.enabled=1 AND d.dict_type=? AND dv.dict_type=?  order by dv.display_order asc
2025-06-18 14:20:24.722 [http-nio-8092-exec-15] TRACE org.hibernate.type.descriptor.sql.BasicBinder.bind Line:64 - binding parameter [1] as [VARCHAR] - [processType]
2025-06-18 14:20:24.722 [http-nio-8092-exec-15] TRACE org.hibernate.type.descriptor.sql.BasicBinder.bind Line:64 - binding parameter [2] as [VARCHAR] - [processType]
2025-06-18 14:20:24.772 [http-nio-8092-exec-15] DEBUG org.hibernate.SQL.logStatement Line:127 - /* dynamic native SQL query */  SELECT act.id, act.business_key, act.create_org_code, act.create_org_name, to_char(act.CREATE_TIME,'yyyy-MM-dd HH24:mi:ss') as CREATE_TIME, act.create_user_code, act.create_user_id, act.create_user_name,        act.current_state, act.duration, decode(act.enabled,0,'false',1,'true') as enabled, act.end_time, act.parent_proc_id, act.previous_assistant, to_char(act.previous_assistant_date,'yyyy-MM-dd HH24:mi:ss') as PREVIOUS_ASSISTANT_DATE, act.previous_assistant_name,        act.previous_assistant_org_code, act.previous_assistant_org_name, act.process_ch_name, act.process_def_id, act.process_def_name, act.process_inst_id,        act.receipt_code, act.receipt_title,decode(act.removed,0,'false',1,'true') as removed,to_char(act.start_time,'yyyy-MM-dd HH24:mi:ss') as START_TIME, to_char(act.update_time,'yyyy-MM-dd HH24:mi:ss') as UPDATE_TIME,       REGEXP_SUBSTR(act.receipt_code, '[a-zA-Z]+') as PM_INS_TYPE, wk.WORK_ITEM_ID, wk.ACTIVITY_DEF_ID, wk.ACTIVITY_INST_NAME,       wk.participant, wk.assistant,to_char(wk.start_Time,'yyyy-MM-dd HH24:mi:ss') as WORK_ITEM_START_TIME,to_char(wk.end_time,'yyyy-MM-dd HH24:mi:ss') as WORK_ITEM_END_TIME FROM act_business_status act,       us_pm_instence us,       wf_workitem_model wk WHERE act.PROCESS_INST_ID = wk.PROCESS_INST_ID   and act.BUSINESS_KEY = us.id   and us.pm_ins_type in (?, ?, ?, ?, ?, ?, ?, ?)   and wk.participant = ?   and act.RECEIPT_TITLE like concat(concat('%', ?), '%')   and wk.current_State = 10   and act.enabled = 1   and wk.enabled = 1   and us.enabled = 1 order by wk.START_TIME desc
2025-06-18 14:20:24.774 [http-nio-8092-exec-15] TRACE org.hibernate.type.descriptor.sql.BasicBinder.bind Line:64 - binding parameter [1] as [VARCHAR] - [C]
2025-06-18 14:20:24.775 [http-nio-8092-exec-15] TRACE org.hibernate.type.descriptor.sql.BasicBinder.bind Line:64 - binding parameter [2] as [VARCHAR] - [E]
2025-06-18 14:20:24.776 [http-nio-8092-exec-15] TRACE org.hibernate.type.descriptor.sql.BasicBinder.bind Line:64 - binding parameter [3] as [VARCHAR] - [H]
2025-06-18 14:20:24.777 [http-nio-8092-exec-15] TRACE org.hibernate.type.descriptor.sql.BasicBinder.bind Line:64 - binding parameter [4] as [VARCHAR] - [D]
2025-06-18 14:20:24.777 [http-nio-8092-exec-15] TRACE org.hibernate.type.descriptor.sql.BasicBinder.bind Line:64 - binding parameter [5] as [VARCHAR] - [F]
2025-06-18 14:20:24.777 [http-nio-8092-exec-15] TRACE org.hibernate.type.descriptor.sql.BasicBinder.bind Line:64 - binding parameter [6] as [VARCHAR] - [G]
2025-06-18 14:20:24.778 [http-nio-8092-exec-15] TRACE org.hibernate.type.descriptor.sql.BasicBinder.bind Line:64 - binding parameter [7] as [VARCHAR] - [A]
2025-06-18 14:20:24.778 [http-nio-8092-exec-15] TRACE org.hibernate.type.descriptor.sql.BasicBinder.bind Line:64 - binding parameter [8] as [VARCHAR] - [B]
2025-06-18 14:20:24.778 [http-nio-8092-exec-15] TRACE org.hibernate.type.descriptor.sql.BasicBinder.bind Line:64 - binding parameter [9] as [VARCHAR] - [chenhong]
2025-06-18 14:20:24.778 [http-nio-8092-exec-15] TRACE org.hibernate.type.descriptor.sql.BasicBinder.bind Line:64 - binding parameter [10] as [VARCHAR] - []
2025-06-18 14:20:24.822 [http-nio-8092-exec-15] DEBUG org.hibernate.SQL.logStatement Line:127 - /* insert com.simbest.boot.sys.model.SysOperateLog */ insert into sys_log_operate (created_time, modified_time, creator, enabled, modifier, removed_time, bussiness_key, error_msg, operate_flag, operate_interface, result_msg, id, interface_param) values (?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?)
2025-06-18 14:20:24.825 [http-nio-8092-exec-15] TRACE org.hibernate.type.descriptor.sql.BasicBinder.bind Line:64 - binding parameter [1] as [TIMESTAMP] - [Wed Jun 18 14:20:24 CST 2025]
2025-06-18 14:20:24.825 [http-nio-8092-exec-15] TRACE org.hibernate.type.descriptor.sql.BasicBinder.bind Line:64 - binding parameter [2] as [TIMESTAMP] - [Wed Jun 18 14:20:24 CST 2025]
2025-06-18 14:20:24.827 [http-nio-8092-exec-15] TRACE org.hibernate.type.descriptor.sql.BasicBinder.bind Line:64 - binding parameter [3] as [VARCHAR] - [chenhong]
2025-06-18 14:20:24.827 [http-nio-8092-exec-15] TRACE org.hibernate.type.descriptor.sql.BasicBinder.bind Line:64 - binding parameter [4] as [BIT] - [true]
2025-06-18 14:20:24.827 [http-nio-8092-exec-15] TRACE org.hibernate.type.descriptor.sql.BasicBinder.bind Line:64 - binding parameter [5] as [VARCHAR] - [chenhong]
2025-06-18 14:20:24.828 [http-nio-8092-exec-15] TRACE org.hibernate.type.descriptor.sql.BasicBinder.bind Line:52 - binding parameter [6] as [TIMESTAMP] - [null]
2025-06-18 14:20:24.828 [http-nio-8092-exec-15] TRACE org.hibernate.type.descriptor.sql.BasicBinder.bind Line:52 - binding parameter [7] as [VARCHAR] - [null]
2025-06-18 14:20:24.828 [http-nio-8092-exec-15] TRACE org.hibernate.type.descriptor.sql.BasicBinder.bind Line:52 - binding parameter [8] as [VARCHAR] - [null]
2025-06-18 14:20:24.829 [http-nio-8092-exec-15] TRACE org.hibernate.type.descriptor.sql.BasicBinder.bind Line:64 - binding parameter [9] as [VARCHAR] - [PC]
2025-06-18 14:20:24.829 [http-nio-8092-exec-15] TRACE org.hibernate.type.descriptor.sql.BasicBinder.bind Line:64 - binding parameter [10] as [VARCHAR] - [/action/queryActBusinessStatus/myTaskToDo]
2025-06-18 14:20:24.829 [http-nio-8092-exec-15] TRACE org.hibernate.type.descriptor.sql.BasicBinder.bind Line:52 - binding parameter [11] as [VARCHAR] - [null]
2025-06-18 14:20:24.829 [http-nio-8092-exec-15] TRACE org.hibernate.type.descriptor.sql.BasicBinder.bind Line:64 - binding parameter [12] as [VARCHAR] - [L877557552818745344]
2025-06-18 14:20:24.830 [http-nio-8092-exec-15] TRACE org.hibernate.type.descriptor.sql.BasicBinder.bind Line:64 - binding parameter [13] as [CLOB] - [pageindex=1,pagesize=10,title=null,source=PC,userCodenull]
2025-06-18 14:20:24.894 [http-nio-8092-exec-15] DEBUG com.simbest.boot.base.service.impl.LogicService.insert Line:248 - 对象【SysOperateLog(id=L877557552818745344, bussinessKey=null, operateInterface=/action/queryActBusinessStatus/myTaskToDo, interfaceParam=pageindex=1,pagesize=10,title=null,source=PC,userCodenull, operateFlag=PC, errorMsg=null, resultMsg=null)】保存成功
2025-06-18 14:20:27.606 [http-nio-8092-exec-1] DEBUG org.hibernate.SQL.logStatement Line:127 - /* dynamic native SQL query */ select * from sys_new_column_model  where enabled = 1   order by column_level,column_order
2025-06-18 14:21:00.010 [pool-14-thread-4] INFO  com.simbest.boot.component.task.AbstractTaskSchedule.checkAndExecute Line:156 - com.simbest.boot.component.task.HeartTestTask当前主机地址【***********】运行端口【8092】是集群主控节点，可以执行定时任务
2025-06-18 14:21:01.046 [pool-14-thread-7] DEBUG com.simbest.boot.component.distributed.lock.AppRuntimeMaster.becameMasertIfNotExist Line:182 - 集群主控节点不可用，当前主机将成为主控节点, 主机地址【***********】运行端口【8092】
2025-06-18 14:22:00.015 [pool-14-thread-2] INFO  com.simbest.boot.component.task.AbstractTaskSchedule.checkAndExecute Line:198 - 集群主控节点主机地址【127.0.0.1】运行端口【20250】,当前主机地址【***********】运行端口【8092】, 无法执行定时任务【com.simbest.boot.component.task.HeartTestTask】【788】
2025-06-18 14:22:01.028 [pool-14-thread-7] DEBUG com.simbest.boot.component.distributed.lock.AppRuntimeMaster.becameMasertIfNotExist Line:182 - 集群主控节点不可用，当前主机将成为主控节点, 主机地址【***********】运行端口【8092】
2025-06-18 14:23:00.015 [pool-14-thread-1] INFO  com.simbest.boot.component.task.AbstractTaskSchedule.checkAndExecute Line:156 - com.simbest.boot.component.task.HeartTestTask当前主机地址【***********】运行端口【8092】是集群主控节点，可以执行定时任务
2025-06-18 14:23:01.052 [pool-14-thread-7] DEBUG com.simbest.boot.component.distributed.lock.AppRuntimeMaster.becameMasertIfNotExist Line:182 - 集群主控节点不可用，当前主机将成为主控节点, 主机地址【***********】运行端口【8092】
2025-06-18 14:24:00.002 [pool-14-thread-6] INFO  com.simbest.boot.component.task.AbstractTaskSchedule.checkAndExecute Line:156 - com.simbest.boot.component.task.HeartTestTask当前主机地址【***********】运行端口【8092】是集群主控节点，可以执行定时任务
2025-06-18 14:24:01.029 [pool-14-thread-5] DEBUG com.simbest.boot.component.distributed.lock.AppRuntimeMaster.becameMasertIfNotExist Line:182 - 集群主控节点不可用，当前主机将成为主控节点, 主机地址【***********】运行端口【8092】
2025-06-18 14:25:00.004 [pool-14-thread-3] INFO  com.simbest.boot.component.task.AbstractTaskSchedule.checkAndExecute Line:156 - com.simbest.boot.processdata.pushWorkmanager.task.SyncCommentDataToWorkManagerTask当前主机地址【***********】运行端口【8092】是集群主控节点，可以执行定时任务
2025-06-18 14:25:00.004 [pool-14-thread-10] INFO  com.simbest.boot.component.task.AbstractTaskSchedule.checkAndExecute Line:156 - com.simbest.boot.processdata.pushWorkmanager.task.SyncTaskDataToWorkManagerTask当前主机地址【***********】运行端口【8092】是集群主控节点，可以执行定时任务
2025-06-18 14:25:00.004 [pool-14-thread-9] INFO  com.simbest.boot.component.task.AbstractTaskSchedule.checkAndExecute Line:156 - com.simbest.boot.bps.task.AutoAddTodoTask当前主机地址【***********】运行端口【8092】是集群主控节点，可以执行定时任务
2025-06-18 14:25:00.004 [pool-14-thread-4] INFO  com.simbest.boot.component.task.AbstractTaskSchedule.checkAndExecute Line:156 - com.simbest.boot.processdata.pushWorkmanager.task.SyncProcessDataToWorkManagerTask当前主机地址【***********】运行端口【8092】是集群主控节点，可以执行定时任务
2025-06-18 14:25:00.004 [pool-14-thread-5] INFO  com.simbest.boot.component.task.AbstractTaskSchedule.checkAndExecute Line:156 - com.simbest.boot.component.task.HeartTestTask当前主机地址【***********】运行端口【8092】是集群主控节点，可以执行定时任务
2025-06-18 14:25:00.004 [pool-14-thread-2] INFO  com.simbest.boot.component.task.AbstractTaskSchedule.checkAndExecute Line:156 - com.simbest.boot.bps.task.AutoCloseTodoTask当前主机地址【***********】运行端口【8092】是集群主控节点，可以执行定时任务
2025-06-18 14:25:00.004 [pool-14-thread-7] INFO  com.simbest.boot.component.task.AbstractTaskSchedule.checkAndExecute Line:156 - com.simbest.boot.bps.task.AutoCheckWithUpdateTask当前主机地址【***********】运行端口【8092】是集群主控节点，可以执行定时任务
2025-06-18 14:25:00.010 [pool-14-thread-10] INFO  com.simbest.boot.component.task.AbstractTaskSchedule.checkAndExecute Line:166 - ------------------------------【com.simbest.boot.processdata.pushWorkmanager.task.SyncTaskDataToWorkManagerTask】【796】上锁成功，即将执行定时任务
2025-06-18 14:25:00.010 [pool-14-thread-7] INFO  com.simbest.boot.component.task.AbstractTaskSchedule.checkAndExecute Line:166 - ------------------------------【com.simbest.boot.bps.task.AutoCheckWithUpdateTask】【793】上锁成功，即将执行定时任务
2025-06-18 14:25:00.012 [pool-14-thread-3] INFO  com.simbest.boot.component.task.AbstractTaskSchedule.checkAndExecute Line:166 - ------------------------------【com.simbest.boot.processdata.pushWorkmanager.task.SyncCommentDataToWorkManagerTask】【789】上锁成功，即将执行定时任务
2025-06-18 14:25:00.012 [pool-14-thread-2] INFO  com.simbest.boot.component.task.AbstractTaskSchedule.checkAndExecute Line:166 - ------------------------------【com.simbest.boot.bps.task.AutoCloseTodoTask】【788】上锁成功，即将执行定时任务
2025-06-18 14:25:00.012 [pool-14-thread-9] INFO  com.simbest.boot.component.task.AbstractTaskSchedule.checkAndExecute Line:166 - ------------------------------【com.simbest.boot.bps.task.AutoAddTodoTask】【795】上锁成功，即将执行定时任务
2025-06-18 14:25:00.021 [pool-14-thread-4] INFO  com.simbest.boot.component.task.AbstractTaskSchedule.checkAndExecute Line:166 - ------------------------------【com.simbest.boot.processdata.pushWorkmanager.task.SyncProcessDataToWorkManagerTask】【790】上锁成功，即将执行定时任务
2025-06-18 14:25:00.023 [pool-14-thread-9] WARN  com.simbest.boot.base.repository.CustomDynamicWhere.queryForList Line:50 - 自定义查询SQL输出为：【select * from US_TODO_MODEL t  where t.send_flag = 0 and t.work_flag = 1 and t.enabled =1】
2025-06-18 14:25:00.023 [pool-14-thread-2] WARN  com.simbest.boot.base.repository.CustomDynamicWhere.queryForList Line:50 - 自定义查询SQL输出为：【select * from US_TODO_MODEL t  where t.send_flag = 0 and t.work_flag = 0 and t.enabled =1】
2025-06-18 14:25:00.032 [pool-14-thread-9] INFO  com.simbest.boot.component.task.AbstractTaskSchedule.checkAndExecute Line:168 - ------------------------------【com.simbest.boot.bps.task.AutoAddTodoTask】【795】定时任务执行完毕
2025-06-18 14:25:00.032 [pool-14-thread-3] DEBUG org.hibernate.SQL.logStatement Line:127 - /* dynamic native SQL query */ select t.id,       to_char(t.created_time, 'yyyy-mm-dd hh:mm:ss') as created_time,       to_char(t.modified_time, 'yyyy-mm-dd hh:mm:ss') as modified_time,       t.creator,       t.enabled,       t.modifier,       t.removed_time,       t.activityinstid,       t.content,       t.correlationid,       t.correlationtype,       t.messageid,       t.operationtype,       t.processdefid,       t.processinstid,       t.producer,       t.producer_name,       t.receipt_code,       t.receipt_id,       t.receipt_title,       t.receiver,       t.root_proc_inst_id,       t.workitemid,       t.producer_identity,       t.p_belong_company_code,       t.p_belong_company_name,       t.p_belong_department_code,       t.p_belong_department_name,       t.p_belong_org_code,       t.p_belong_org_name,       t.p_company_type_dict_desc,       t.p_company_type_dict_value,       t.spare01 from WF_OPTMSG_MODEL t where t.spare01 is null and t.created_time >= to_date(?, 'yyyy-MM-dd HH24:mi:ss') and t.created_time <= to_date(?, 'yyyy-MM-dd HH24:mi:ss') and rownum <= 50
2025-06-18 14:25:00.034 [pool-14-thread-3] TRACE org.hibernate.type.descriptor.sql.BasicBinder.bind Line:64 - binding parameter [1] as [VARCHAR] - []
2025-06-18 14:25:00.034 [pool-14-thread-3] TRACE org.hibernate.type.descriptor.sql.BasicBinder.bind Line:64 - binding parameter [2] as [VARCHAR] - []
2025-06-18 14:25:00.035 [pool-14-thread-9] INFO  com.simbest.boot.component.task.AbstractTaskSchedule.checkAndExecute Line:177 - ------------------------------【com.simbest.boot.bps.task.AutoAddTodoTask】【795】定时任务执行完毕，集群锁已成功释放
2025-06-18 14:25:00.044 [pool-14-thread-2] INFO  com.simbest.boot.component.task.AbstractTaskSchedule.checkAndExecute Line:168 - ------------------------------【com.simbest.boot.bps.task.AutoCloseTodoTask】【788】定时任务执行完毕
2025-06-18 14:25:00.045 [pool-14-thread-4] DEBUG org.hibernate.SQL.logStatement Line:127 - /* dynamic native SQL query */ select  t.id,       to_char(t.created_time, 'yyyy-mm-dd hh:mm:ss') as created_time,       to_char(t.modified_time, 'yyyy-mm-dd hh:mm:ss') as modified_time,       t.creator,       t.enabled,       t.modifier,       t.removed_time,       t.catalog_name,       t.cataloguuid,       t.create_time,       t.current_state,       to_char(t.end_time, 'yyyy-mm-dd hh:mm:ss') as end_time,       t.final_time,       t.is_time_out,       t.owner,       t.parent_act_id,       t.parent_proc_id,       t.process_def_id,       t.process_def_name,       t.process_inst_desc,       t.process_inst_id,       t.process_inst_name,       t.receipt_code,       t.receipt_id,       t.receipt_title,       t.remind_time,       to_char(t.start_time, 'yyyy-mm-dd hh:mm:ss') as start_time,       t.time_out_num,       t.time_out_num_desc,       t.update_version from WF_PROCESS_INST_MODEL t where t.spare01 is null and t.created_time >= to_date(?, 'yyyy-MM-dd HH24:mi:ss') and t.created_time <= to_date(?, 'yyyy-MM-dd HH24:mi:ss') and rownum <= 50
2025-06-18 14:25:00.045 [pool-14-thread-10] DEBUG org.hibernate.SQL.logStatement Line:127 - /* dynamic native SQL query */ select t.id,       to_char(t.created_time, 'yyyy-mm-dd hh:mm:ss') as created_time,       to_char(t.modified_time, 'yyyy-mm-dd hh:mm:ss') as modified_time,       t.creator,       t.enabled,       t.modifier,       t.removed_time,       t.a_belong_company_code,       t.a_belong_company_name,       t.a_belong_department_code,       t.a_belong_department_name,       t.a_belong_org_code,       t.a_belong_org_name,       t.a_company_type_dict_desc,       t.a_company_type_dict_value,       t.actionurl,       t.activity_def_id,       t.activity_inst_id,       t.activity_inst_name,       t.agent_user,       t.agent_user_name,       t.allow_agent,       t.assistant,       t.biz_state,       t.catalog_name,       t.cataloguuid,       t.create_time,       t.current_state,       to_char(t.end_time, 'yyyy-mm-dd hh:mm:ss') as end_time,       t.final_time,       t.is_time_out,       t.p_belong_company_code,       t.p_belong_company_name,       t.p_belong_department_code,       t.p_belong_department_name,       t.p_belong_org_code,       t.p_belong_org_name,       t.p_company_type_dict_desc,       t.p_company_type_dict_value,       t.parti_name,       t.participant,       t.participant_identity,       t.priority,       t.process_ch_name,       t.process_def_id,       t.process_def_name,       t.process_inst_id,       t.process_inst_name,       t.receipt_code,       t.receipt_id,       t.receipt_title,       t.remind_time,       t.root_proc_inst_id,       to_char(t.start_time, 'yyyy-mm-dd hh:mm:ss') as start_time,       t.url_type,       t.work_item_desc,       t.work_item_id,       t.work_item_name from WF_WORKITEM_MODEL t where t.spare01 is null and t.created_time >= to_date(?, 'yyyy-MM-dd HH24:mi:ss') and t.created_time <= to_date(?, 'yyyy-MM-dd HH24:mi:ss') and rownum <= 50
2025-06-18 14:25:00.046 [pool-14-thread-4] TRACE org.hibernate.type.descriptor.sql.BasicBinder.bind Line:64 - binding parameter [1] as [VARCHAR] - []
2025-06-18 14:25:00.046 [pool-14-thread-10] TRACE org.hibernate.type.descriptor.sql.BasicBinder.bind Line:64 - binding parameter [1] as [VARCHAR] - []
2025-06-18 14:25:00.046 [pool-14-thread-4] TRACE org.hibernate.type.descriptor.sql.BasicBinder.bind Line:64 - binding parameter [2] as [VARCHAR] - []
2025-06-18 14:25:00.046 [pool-14-thread-3] INFO  com.simbest.boot.component.task.AbstractTaskSchedule.checkAndExecute Line:168 - ------------------------------【com.simbest.boot.processdata.pushWorkmanager.task.SyncCommentDataToWorkManagerTask】【789】定时任务执行完毕
2025-06-18 14:25:00.046 [pool-14-thread-10] TRACE org.hibernate.type.descriptor.sql.BasicBinder.bind Line:64 - binding parameter [2] as [VARCHAR] - []
2025-06-18 14:25:00.047 [pool-14-thread-2] INFO  com.simbest.boot.component.task.AbstractTaskSchedule.checkAndExecute Line:177 - ------------------------------【com.simbest.boot.bps.task.AutoCloseTodoTask】【788】定时任务执行完毕，集群锁已成功释放
2025-06-18 14:25:00.047 [pool-14-thread-7] DEBUG org.hibernate.SQL.logStatement Line:127 - /* dynamic native SQL query */ select * from wf_process_error_log t where spare1 is null and ENABLED=1 and to_char(t.created_time,'yyyy-mm-dd') >= to_char(sysdate,'yyyy-mm-dd')
2025-06-18 14:25:00.049 [pool-14-thread-3] INFO  com.simbest.boot.component.task.AbstractTaskSchedule.checkAndExecute Line:177 - ------------------------------【com.simbest.boot.processdata.pushWorkmanager.task.SyncCommentDataToWorkManagerTask】【789】定时任务执行完毕，集群锁已成功释放
2025-06-18 14:25:00.051 [pool-14-thread-9] DEBUG com.simbest.boot.base.repository.LogicDeleteRepositoryImpl.save Line:346 - 对象没有联合索引和联合主键，即将更新对象【com.simbest.boot.sys.model.SysTaskExecutedLog@5f0063f3[id=<null>,taskName=AutoAddTodoTask,hostname=***********,port=8092,durationTime=27,content=795【bps】本次需要重新推送的待办列表为：【[]】,executeFlag=true,createdTime=<null>,modifiedTime=<null>,orderByClause=<null>,ssDate=<null>,eeDate=<null>,pageIndex=<null>,pagesize=<null>]】
2025-06-18 14:25:00.052 [pool-14-thread-10] INFO  com.simbest.boot.component.task.AbstractTaskSchedule.checkAndExecute Line:168 - ------------------------------【com.simbest.boot.processdata.pushWorkmanager.task.SyncTaskDataToWorkManagerTask】【796】定时任务执行完毕
2025-06-18 14:25:00.052 [pool-14-thread-9] DEBUG com.simbest.boot.base.repository.LogicDeleteRepositoryImpl.save Line:348 - 对象没有联合索引和联合主键，更新对象后为【com.simbest.boot.sys.model.SysTaskExecutedLog@5f0063f3[id=TL877558707217035264,taskName=AutoAddTodoTask,hostname=***********,port=8092,durationTime=27,content=795【bps】本次需要重新推送的待办列表为：【[]】,executeFlag=true,createdTime=<null>,modifiedTime=<null>,orderByClause=<null>,ssDate=<null>,eeDate=<null>,pageIndex=<null>,pagesize=<null>]】
2025-06-18 14:25:00.052 [pool-14-thread-7] INFO  com.simbest.boot.component.task.AbstractTaskSchedule.checkAndExecute Line:168 - ------------------------------【com.simbest.boot.bps.task.AutoCheckWithUpdateTask】【793】定时任务执行完毕
2025-06-18 14:25:00.052 [pool-14-thread-9] DEBUG org.hibernate.SQL.logStatement Line:127 - /* insert com.simbest.boot.sys.model.SysTaskExecutedLog */ insert into sys_task_executed_log (created_time, modified_time, content, duration_time, execute_flag, hostname, port, task_name, id) values (?, ?, ?, ?, ?, ?, ?, ?, ?)
2025-06-18 14:25:00.053 [pool-14-thread-9] TRACE org.hibernate.type.descriptor.sql.BasicBinder.bind Line:64 - binding parameter [1] as [TIMESTAMP] - [Wed Jun 18 14:25:00 CST 2025]
2025-06-18 14:25:00.053 [pool-14-thread-9] TRACE org.hibernate.type.descriptor.sql.BasicBinder.bind Line:64 - binding parameter [2] as [TIMESTAMP] - [Wed Jun 18 14:25:00 CST 2025]
2025-06-18 14:25:00.053 [pool-14-thread-4] INFO  com.simbest.boot.component.task.AbstractTaskSchedule.checkAndExecute Line:168 - ------------------------------【com.simbest.boot.processdata.pushWorkmanager.task.SyncProcessDataToWorkManagerTask】【790】定时任务执行完毕
2025-06-18 14:25:00.053 [pool-14-thread-9] TRACE org.hibernate.type.descriptor.sql.BasicBinder.bind Line:64 - binding parameter [3] as [VARCHAR] - [795【bps】本次需要重新推送的待办列表为：【[]】]
2025-06-18 14:25:00.054 [pool-14-thread-7] INFO  com.simbest.boot.component.task.AbstractTaskSchedule.checkAndExecute Line:177 - ------------------------------【com.simbest.boot.bps.task.AutoCheckWithUpdateTask】【793】定时任务执行完毕，集群锁已成功释放
2025-06-18 14:25:00.054 [pool-14-thread-10] INFO  com.simbest.boot.component.task.AbstractTaskSchedule.checkAndExecute Line:177 - ------------------------------【com.simbest.boot.processdata.pushWorkmanager.task.SyncTaskDataToWorkManagerTask】【796】定时任务执行完毕，集群锁已成功释放
2025-06-18 14:25:00.056 [pool-14-thread-9] TRACE org.hibernate.type.descriptor.sql.BasicBinder.bind Line:64 - binding parameter [4] as [BIGINT] - [27]
2025-06-18 14:25:00.056 [pool-14-thread-4] INFO  com.simbest.boot.component.task.AbstractTaskSchedule.checkAndExecute Line:177 - ------------------------------【com.simbest.boot.processdata.pushWorkmanager.task.SyncProcessDataToWorkManagerTask】【790】定时任务执行完毕，集群锁已成功释放
2025-06-18 14:25:00.060 [pool-14-thread-9] TRACE org.hibernate.type.descriptor.sql.BasicBinder.bind Line:64 - binding parameter [5] as [BIT] - [true]
2025-06-18 14:25:00.060 [pool-14-thread-9] TRACE org.hibernate.type.descriptor.sql.BasicBinder.bind Line:64 - binding parameter [6] as [VARCHAR] - [***********]
2025-06-18 14:25:00.060 [pool-14-thread-9] TRACE org.hibernate.type.descriptor.sql.BasicBinder.bind Line:64 - binding parameter [7] as [INTEGER] - [8092]
2025-06-18 14:25:00.061 [pool-14-thread-9] TRACE org.hibernate.type.descriptor.sql.BasicBinder.bind Line:64 - binding parameter [8] as [VARCHAR] - [AutoAddTodoTask]
2025-06-18 14:25:00.061 [pool-14-thread-9] TRACE org.hibernate.type.descriptor.sql.BasicBinder.bind Line:64 - binding parameter [9] as [VARCHAR] - [TL877558707217035264]
2025-06-18 14:25:00.061 [pool-14-thread-2] DEBUG com.simbest.boot.base.repository.LogicDeleteRepositoryImpl.save Line:346 - 对象没有联合索引和联合主键，即将更新对象【com.simbest.boot.sys.model.SysTaskExecutedLog@2922255b[id=<null>,taskName=AutoCloseTodoTask,hostname=***********,port=8092,durationTime=39,content=788【bps】本次需要重新核销的待办列表为：【[]】,executeFlag=true,createdTime=<null>,modifiedTime=<null>,orderByClause=<null>,ssDate=<null>,eeDate=<null>,pageIndex=<null>,pagesize=<null>]】
2025-06-18 14:25:00.062 [pool-14-thread-2] DEBUG com.simbest.boot.base.repository.LogicDeleteRepositoryImpl.save Line:348 - 对象没有联合索引和联合主键，更新对象后为【com.simbest.boot.sys.model.SysTaskExecutedLog@2922255b[id=TL877558707258978304,taskName=AutoCloseTodoTask,hostname=***********,port=8092,durationTime=39,content=788【bps】本次需要重新核销的待办列表为：【[]】,executeFlag=true,createdTime=<null>,modifiedTime=<null>,orderByClause=<null>,ssDate=<null>,eeDate=<null>,pageIndex=<null>,pagesize=<null>]】
2025-06-18 14:25:00.063 [pool-14-thread-2] DEBUG org.hibernate.SQL.logStatement Line:127 - /* insert com.simbest.boot.sys.model.SysTaskExecutedLog */ insert into sys_task_executed_log (created_time, modified_time, content, duration_time, execute_flag, hostname, port, task_name, id) values (?, ?, ?, ?, ?, ?, ?, ?, ?)
2025-06-18 14:25:00.063 [pool-14-thread-2] TRACE org.hibernate.type.descriptor.sql.BasicBinder.bind Line:64 - binding parameter [1] as [TIMESTAMP] - [Wed Jun 18 14:25:00 CST 2025]
2025-06-18 14:25:00.063 [pool-14-thread-2] TRACE org.hibernate.type.descriptor.sql.BasicBinder.bind Line:64 - binding parameter [2] as [TIMESTAMP] - [Wed Jun 18 14:25:00 CST 2025]
2025-06-18 14:25:00.063 [pool-14-thread-2] TRACE org.hibernate.type.descriptor.sql.BasicBinder.bind Line:64 - binding parameter [3] as [VARCHAR] - [788【bps】本次需要重新核销的待办列表为：【[]】]
2025-06-18 14:25:00.063 [pool-14-thread-2] TRACE org.hibernate.type.descriptor.sql.BasicBinder.bind Line:64 - binding parameter [4] as [BIGINT] - [39]
2025-06-18 14:25:00.064 [pool-14-thread-2] TRACE org.hibernate.type.descriptor.sql.BasicBinder.bind Line:64 - binding parameter [5] as [BIT] - [true]
2025-06-18 14:25:00.064 [pool-14-thread-2] TRACE org.hibernate.type.descriptor.sql.BasicBinder.bind Line:64 - binding parameter [6] as [VARCHAR] - [***********]
2025-06-18 14:25:00.064 [pool-14-thread-2] TRACE org.hibernate.type.descriptor.sql.BasicBinder.bind Line:64 - binding parameter [7] as [INTEGER] - [8092]
2025-06-18 14:25:00.065 [pool-14-thread-2] TRACE org.hibernate.type.descriptor.sql.BasicBinder.bind Line:64 - binding parameter [8] as [VARCHAR] - [AutoCloseTodoTask]
2025-06-18 14:25:00.065 [pool-14-thread-2] TRACE org.hibernate.type.descriptor.sql.BasicBinder.bind Line:64 - binding parameter [9] as [VARCHAR] - [TL877558707258978304]
2025-06-18 14:25:00.069 [pool-14-thread-3] DEBUG com.simbest.boot.base.repository.LogicDeleteRepositoryImpl.save Line:346 - 对象没有联合索引和联合主键，即将更新对象【com.simbest.boot.sys.model.SysTaskExecutedLog@12bc81e5[id=<null>,taskName=SyncCommentDataToWorkManagerTask,hostname=***********,port=8092,durationTime=41,content=789null,executeFlag=true,createdTime=<null>,modifiedTime=<null>,orderByClause=<null>,ssDate=<null>,eeDate=<null>,pageIndex=<null>,pagesize=<null>]】
2025-06-18 14:25:00.070 [pool-14-thread-3] DEBUG com.simbest.boot.base.repository.LogicDeleteRepositoryImpl.save Line:348 - 对象没有联合索引和联合主键，更新对象后为【com.simbest.boot.sys.model.SysTaskExecutedLog@12bc81e5[id=TL877558707288338432,taskName=SyncCommentDataToWorkManagerTask,hostname=***********,port=8092,durationTime=41,content=789null,executeFlag=true,createdTime=<null>,modifiedTime=<null>,orderByClause=<null>,ssDate=<null>,eeDate=<null>,pageIndex=<null>,pagesize=<null>]】
2025-06-18 14:25:00.070 [pool-14-thread-3] DEBUG org.hibernate.SQL.logStatement Line:127 - /* insert com.simbest.boot.sys.model.SysTaskExecutedLog */ insert into sys_task_executed_log (created_time, modified_time, content, duration_time, execute_flag, hostname, port, task_name, id) values (?, ?, ?, ?, ?, ?, ?, ?, ?)
2025-06-18 14:25:00.071 [pool-14-thread-7] DEBUG com.simbest.boot.base.repository.LogicDeleteRepositoryImpl.save Line:346 - 对象没有联合索引和联合主键，即将更新对象【com.simbest.boot.sys.model.SysTaskExecutedLog@7342e7d4[id=<null>,taskName=AutoCheckWithUpdateTask,hostname=***********,port=8092,durationTime=45,content=793【bps】本次处理0条异常数据！,executeFlag=true,createdTime=<null>,modifiedTime=<null>,orderByClause=<null>,ssDate=<null>,eeDate=<null>,pageIndex=<null>,pagesize=<null>]】
2025-06-18 14:25:00.071 [pool-14-thread-3] TRACE org.hibernate.type.descriptor.sql.BasicBinder.bind Line:64 - binding parameter [1] as [TIMESTAMP] - [Wed Jun 18 14:25:00 CST 2025]
2025-06-18 14:25:00.071 [pool-14-thread-7] DEBUG com.simbest.boot.base.repository.LogicDeleteRepositoryImpl.save Line:348 - 对象没有联合索引和联合主键，更新对象后为【com.simbest.boot.sys.model.SysTaskExecutedLog@7342e7d4[id=TL877558707296727040,taskName=AutoCheckWithUpdateTask,hostname=***********,port=8092,durationTime=45,content=793【bps】本次处理0条异常数据！,executeFlag=true,createdTime=<null>,modifiedTime=<null>,orderByClause=<null>,ssDate=<null>,eeDate=<null>,pageIndex=<null>,pagesize=<null>]】
2025-06-18 14:25:00.071 [pool-14-thread-3] TRACE org.hibernate.type.descriptor.sql.BasicBinder.bind Line:64 - binding parameter [2] as [TIMESTAMP] - [Wed Jun 18 14:25:00 CST 2025]
2025-06-18 14:25:00.071 [pool-14-thread-3] TRACE org.hibernate.type.descriptor.sql.BasicBinder.bind Line:64 - binding parameter [3] as [VARCHAR] - [789null]
2025-06-18 14:25:00.072 [pool-14-thread-7] DEBUG org.hibernate.SQL.logStatement Line:127 - /* insert com.simbest.boot.sys.model.SysTaskExecutedLog */ insert into sys_task_executed_log (created_time, modified_time, content, duration_time, execute_flag, hostname, port, task_name, id) values (?, ?, ?, ?, ?, ?, ?, ?, ?)
2025-06-18 14:25:00.072 [pool-14-thread-10] DEBUG com.simbest.boot.base.repository.LogicDeleteRepositoryImpl.save Line:346 - 对象没有联合索引和联合主键，即将更新对象【com.simbest.boot.sys.model.SysTaskExecutedLog@7abdd29c[id=<null>,taskName=SyncTaskDataToWorkManagerTask,hostname=***********,port=8092,durationTime=46,content=796null,executeFlag=true,createdTime=<null>,modifiedTime=<null>,orderByClause=<null>,ssDate=<null>,eeDate=<null>,pageIndex=<null>,pagesize=<null>]】
2025-06-18 14:25:00.072 [pool-14-thread-3] TRACE org.hibernate.type.descriptor.sql.BasicBinder.bind Line:64 - binding parameter [4] as [BIGINT] - [41]
2025-06-18 14:25:00.072 [pool-14-thread-3] TRACE org.hibernate.type.descriptor.sql.BasicBinder.bind Line:64 - binding parameter [5] as [BIT] - [true]
2025-06-18 14:25:00.072 [pool-14-thread-7] TRACE org.hibernate.type.descriptor.sql.BasicBinder.bind Line:64 - binding parameter [1] as [TIMESTAMP] - [Wed Jun 18 14:25:00 CST 2025]
2025-06-18 14:25:00.072 [pool-14-thread-10] DEBUG com.simbest.boot.base.repository.LogicDeleteRepositoryImpl.save Line:348 - 对象没有联合索引和联合主键，更新对象后为【com.simbest.boot.sys.model.SysTaskExecutedLog@7abdd29c[id=TL877558707300921344,taskName=SyncTaskDataToWorkManagerTask,hostname=***********,port=8092,durationTime=46,content=796null,executeFlag=true,createdTime=<null>,modifiedTime=<null>,orderByClause=<null>,ssDate=<null>,eeDate=<null>,pageIndex=<null>,pagesize=<null>]】
2025-06-18 14:25:00.072 [pool-14-thread-3] TRACE org.hibernate.type.descriptor.sql.BasicBinder.bind Line:64 - binding parameter [6] as [VARCHAR] - [***********]
2025-06-18 14:25:00.072 [pool-14-thread-7] TRACE org.hibernate.type.descriptor.sql.BasicBinder.bind Line:64 - binding parameter [2] as [TIMESTAMP] - [Wed Jun 18 14:25:00 CST 2025]
2025-06-18 14:25:00.072 [pool-14-thread-3] TRACE org.hibernate.type.descriptor.sql.BasicBinder.bind Line:64 - binding parameter [7] as [INTEGER] - [8092]
2025-06-18 14:25:00.072 [pool-14-thread-10] DEBUG org.hibernate.SQL.logStatement Line:127 - /* insert com.simbest.boot.sys.model.SysTaskExecutedLog */ insert into sys_task_executed_log (created_time, modified_time, content, duration_time, execute_flag, hostname, port, task_name, id) values (?, ?, ?, ?, ?, ?, ?, ?, ?)
2025-06-18 14:25:00.072 [pool-14-thread-7] TRACE org.hibernate.type.descriptor.sql.BasicBinder.bind Line:64 - binding parameter [3] as [VARCHAR] - [793【bps】本次处理0条异常数据！]
2025-06-18 14:25:00.072 [pool-14-thread-3] TRACE org.hibernate.type.descriptor.sql.BasicBinder.bind Line:64 - binding parameter [8] as [VARCHAR] - [SyncCommentDataToWorkManagerTask]
2025-06-18 14:25:00.073 [pool-14-thread-3] TRACE org.hibernate.type.descriptor.sql.BasicBinder.bind Line:64 - binding parameter [9] as [VARCHAR] - [TL877558707288338432]
2025-06-18 14:25:00.073 [pool-14-thread-7] TRACE org.hibernate.type.descriptor.sql.BasicBinder.bind Line:64 - binding parameter [4] as [BIGINT] - [45]
2025-06-18 14:25:00.073 [pool-14-thread-7] TRACE org.hibernate.type.descriptor.sql.BasicBinder.bind Line:64 - binding parameter [5] as [BIT] - [true]
2025-06-18 14:25:00.073 [pool-14-thread-7] TRACE org.hibernate.type.descriptor.sql.BasicBinder.bind Line:64 - binding parameter [6] as [VARCHAR] - [***********]
2025-06-18 14:25:00.073 [pool-14-thread-4] DEBUG com.simbest.boot.base.repository.LogicDeleteRepositoryImpl.save Line:346 - 对象没有联合索引和联合主键，即将更新对象【com.simbest.boot.sys.model.SysTaskExecutedLog@75a07301[id=<null>,taskName=SyncProcessDataToWorkManagerTask,hostname=***********,port=8092,durationTime=48,content=790null,executeFlag=true,createdTime=<null>,modifiedTime=<null>,orderByClause=<null>,ssDate=<null>,eeDate=<null>,pageIndex=<null>,pagesize=<null>]】
2025-06-18 14:25:00.073 [pool-14-thread-10] TRACE org.hibernate.type.descriptor.sql.BasicBinder.bind Line:64 - binding parameter [1] as [TIMESTAMP] - [Wed Jun 18 14:25:00 CST 2025]
2025-06-18 14:25:00.073 [pool-14-thread-7] TRACE org.hibernate.type.descriptor.sql.BasicBinder.bind Line:64 - binding parameter [7] as [INTEGER] - [8092]
2025-06-18 14:25:00.073 [pool-14-thread-7] TRACE org.hibernate.type.descriptor.sql.BasicBinder.bind Line:64 - binding parameter [8] as [VARCHAR] - [AutoCheckWithUpdateTask]
2025-06-18 14:25:00.073 [pool-14-thread-4] DEBUG com.simbest.boot.base.repository.LogicDeleteRepositoryImpl.save Line:348 - 对象没有联合索引和联合主键，更新对象后为【com.simbest.boot.sys.model.SysTaskExecutedLog@75a07301[id=TL877558707305115648,taskName=SyncProcessDataToWorkManagerTask,hostname=***********,port=8092,durationTime=48,content=790null,executeFlag=true,createdTime=<null>,modifiedTime=<null>,orderByClause=<null>,ssDate=<null>,eeDate=<null>,pageIndex=<null>,pagesize=<null>]】
2025-06-18 14:25:00.074 [pool-14-thread-7] TRACE org.hibernate.type.descriptor.sql.BasicBinder.bind Line:64 - binding parameter [9] as [VARCHAR] - [TL877558707296727040]
2025-06-18 14:25:00.073 [pool-14-thread-10] TRACE org.hibernate.type.descriptor.sql.BasicBinder.bind Line:64 - binding parameter [2] as [TIMESTAMP] - [Wed Jun 18 14:25:00 CST 2025]
2025-06-18 14:25:00.074 [pool-14-thread-4] DEBUG org.hibernate.SQL.logStatement Line:127 - /* insert com.simbest.boot.sys.model.SysTaskExecutedLog */ insert into sys_task_executed_log (created_time, modified_time, content, duration_time, execute_flag, hostname, port, task_name, id) values (?, ?, ?, ?, ?, ?, ?, ?, ?)
2025-06-18 14:25:00.074 [pool-14-thread-10] TRACE org.hibernate.type.descriptor.sql.BasicBinder.bind Line:64 - binding parameter [3] as [VARCHAR] - [796null]
2025-06-18 14:25:00.074 [pool-14-thread-10] TRACE org.hibernate.type.descriptor.sql.BasicBinder.bind Line:64 - binding parameter [4] as [BIGINT] - [46]
2025-06-18 14:25:00.074 [pool-14-thread-4] TRACE org.hibernate.type.descriptor.sql.BasicBinder.bind Line:64 - binding parameter [1] as [TIMESTAMP] - [Wed Jun 18 14:25:00 CST 2025]
2025-06-18 14:25:00.074 [pool-14-thread-10] TRACE org.hibernate.type.descriptor.sql.BasicBinder.bind Line:64 - binding parameter [5] as [BIT] - [true]
2025-06-18 14:25:00.074 [pool-14-thread-4] TRACE org.hibernate.type.descriptor.sql.BasicBinder.bind Line:64 - binding parameter [2] as [TIMESTAMP] - [Wed Jun 18 14:25:00 CST 2025]
2025-06-18 14:25:00.074 [pool-14-thread-10] TRACE org.hibernate.type.descriptor.sql.BasicBinder.bind Line:64 - binding parameter [6] as [VARCHAR] - [***********]
2025-06-18 14:25:00.076 [pool-14-thread-10] TRACE org.hibernate.type.descriptor.sql.BasicBinder.bind Line:64 - binding parameter [7] as [INTEGER] - [8092]
2025-06-18 14:25:00.076 [pool-14-thread-4] TRACE org.hibernate.type.descriptor.sql.BasicBinder.bind Line:64 - binding parameter [3] as [VARCHAR] - [790null]
2025-06-18 14:25:00.076 [pool-14-thread-10] TRACE org.hibernate.type.descriptor.sql.BasicBinder.bind Line:64 - binding parameter [8] as [VARCHAR] - [SyncTaskDataToWorkManagerTask]
2025-06-18 14:25:00.076 [pool-14-thread-10] TRACE org.hibernate.type.descriptor.sql.BasicBinder.bind Line:64 - binding parameter [9] as [VARCHAR] - [TL877558707300921344]
2025-06-18 14:25:00.076 [pool-14-thread-4] TRACE org.hibernate.type.descriptor.sql.BasicBinder.bind Line:64 - binding parameter [4] as [BIGINT] - [48]
2025-06-18 14:25:00.076 [pool-14-thread-4] TRACE org.hibernate.type.descriptor.sql.BasicBinder.bind Line:64 - binding parameter [5] as [BIT] - [true]
2025-06-18 14:25:00.076 [pool-14-thread-4] TRACE org.hibernate.type.descriptor.sql.BasicBinder.bind Line:64 - binding parameter [6] as [VARCHAR] - [***********]
2025-06-18 14:25:00.076 [pool-14-thread-4] TRACE org.hibernate.type.descriptor.sql.BasicBinder.bind Line:64 - binding parameter [7] as [INTEGER] - [8092]
2025-06-18 14:25:00.076 [pool-14-thread-4] TRACE org.hibernate.type.descriptor.sql.BasicBinder.bind Line:64 - binding parameter [8] as [VARCHAR] - [SyncProcessDataToWorkManagerTask]
2025-06-18 14:25:00.076 [pool-14-thread-4] TRACE org.hibernate.type.descriptor.sql.BasicBinder.bind Line:64 - binding parameter [9] as [VARCHAR] - [TL877558707305115648]
2025-06-18 14:25:00.086 [pool-14-thread-9] INFO  com.simbest.boot.component.task.AbstractTaskSchedule.checkAndExecute Line:194 - ------------------------------【com.simbest.boot.bps.task.AutoAddTodoTask】【795】定时任务执行完毕，已写入定时任务记录日志
2025-06-18 14:25:00.118 [pool-14-thread-2] INFO  com.simbest.boot.component.task.AbstractTaskSchedule.checkAndExecute Line:194 - ------------------------------【com.simbest.boot.bps.task.AutoCloseTodoTask】【788】定时任务执行完毕，已写入定时任务记录日志
2025-06-18 14:25:00.118 [pool-14-thread-7] INFO  com.simbest.boot.component.task.AbstractTaskSchedule.checkAndExecute Line:194 - ------------------------------【com.simbest.boot.bps.task.AutoCheckWithUpdateTask】【793】定时任务执行完毕，已写入定时任务记录日志
2025-06-18 14:25:00.118 [pool-14-thread-4] INFO  com.simbest.boot.component.task.AbstractTaskSchedule.checkAndExecute Line:194 - ------------------------------【com.simbest.boot.processdata.pushWorkmanager.task.SyncProcessDataToWorkManagerTask】【790】定时任务执行完毕，已写入定时任务记录日志
2025-06-18 14:25:00.118 [pool-14-thread-10] INFO  com.simbest.boot.component.task.AbstractTaskSchedule.checkAndExecute Line:194 - ------------------------------【com.simbest.boot.processdata.pushWorkmanager.task.SyncTaskDataToWorkManagerTask】【796】定时任务执行完毕，已写入定时任务记录日志
2025-06-18 14:25:00.119 [pool-14-thread-3] INFO  com.simbest.boot.component.task.AbstractTaskSchedule.checkAndExecute Line:194 - ------------------------------【com.simbest.boot.processdata.pushWorkmanager.task.SyncCommentDataToWorkManagerTask】【789】定时任务执行完毕，已写入定时任务记录日志
2025-06-18 14:25:01.058 [pool-14-thread-1] DEBUG com.simbest.boot.component.distributed.lock.AppRuntimeMaster.becameMasertIfNotExist Line:182 - 集群主控节点不可用，当前主机将成为主控节点, 主机地址【***********】运行端口【8092】
2025-06-18 14:26:00.008 [pool-14-thread-2] INFO  com.simbest.boot.component.task.AbstractTaskSchedule.checkAndExecute Line:156 - com.simbest.boot.component.task.HeartTestTask当前主机地址【***********】运行端口【8092】是集群主控节点，可以执行定时任务
2025-06-18 14:26:01.059 [pool-14-thread-9] DEBUG com.simbest.boot.component.distributed.lock.AppRuntimeMaster.becameMasertIfNotExist Line:182 - 集群主控节点不可用，当前主机将成为主控节点, 主机地址【***********】运行端口【8092】
2025-06-18 14:27:00.013 [pool-14-thread-6] INFO  com.simbest.boot.component.task.AbstractTaskSchedule.checkAndExecute Line:156 - com.simbest.boot.component.task.HeartTestTask当前主机地址【***********】运行端口【8092】是集群主控节点，可以执行定时任务
2025-06-18 14:27:01.047 [pool-14-thread-2] DEBUG com.simbest.boot.component.distributed.lock.AppRuntimeMaster.becameMasertIfNotExist Line:182 - 集群主控节点不可用，当前主机将成为主控节点, 主机地址【***********】运行端口【8092】
