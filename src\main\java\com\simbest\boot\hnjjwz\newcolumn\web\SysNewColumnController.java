package com.simbest.boot.hnjjwz.newcolumn.web;


import com.simbest.boot.base.web.controller.LogicController;
import com.simbest.boot.base.web.response.JsonResponse;
import com.simbest.boot.hnjjwz.newcolumn.model.SysNewColumnModel;
import com.simbest.boot.hnjjwz.newcolumn.service.ISysNewColumnService;
import io.swagger.annotations.Api;
import io.swagger.annotations.ApiOperation;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.web.bind.annotation.*;

@Api(description = "新版栏目配置相关接口")
@Slf4j
@RestController
@RequestMapping(value = "/action/newcolumn")
public class SysNewColumnController extends LogicController<SysNewColumnModel, String> {
    private ISysNewColumnService service;

    @Autowired
    public SysNewColumnController(ISysNewColumnService service) {
        super(service);
        this.service = service;
    }

    @ApiOperation(value = "新增一个栏目", notes = "新增一个栏目")
    @PostMapping(value = {"/createnewcolumn", "/createnewcolumn/sso", "/createnewcolumn/api", "/createnewcolumn/anonymous"})
    public JsonResponse createnewcolumn(@RequestBody(required = false) SysNewColumnModel model) {
        return service.createnewcolumn(model);
    }

    @ApiOperation(value = "根据当前登录人查询有起草权限的栏目", notes = "根据当前登录人查询有起草权限的栏目")
    @PostMapping(value = {"/findAllByUser", "/findAllByUser/sso", "/findAllByUser/api", "/findAllByUser/anonymous"})
    public JsonResponse findAllByUser() {
        return service.findAllByUser();
    }

/**
 * @description  首页用 不分页
 * <AUTHOR> enlai
 * @time
 */
    @ApiOperation(value = "根据当前栏目下的文章信息", notes = "根据当前栏目下的文章信息")
    @PostMapping(value = {"/findAllArticleByColumnId", "/findAllArticleByColumnId/sso", "/findAllArticleByColumnId/api", "/findAllArticleByColumnId/anonymous"})
    public JsonResponse findAllArticleByColumnId(@RequestParam String columnId) {
        return service.findAllArticleByColumnId(columnId);
    }

    /**
    * @description 根据文章ID取所有下级栏目
    * <AUTHOR> enlai
    * @time
    */
    @ApiOperation(value = "取当前栏目对应的根栏目及所有下级栏目", notes = "取当前栏目对应的根栏目及所有下级栏目")
    @PostMapping(value = {"/findColumnByArticleID", "/findColumnByArticleID/sso", "/findColumnByArticleID/api", "/findColumnByArticleID/anonymous"})
    public JsonResponse findColumnByArticleID(@RequestParam String columnId){
        return service.findColumnByArticleID(columnId);
    }

    @ApiOperation(value = "根据当前栏目下的文章列表信息并分页", notes = "根据当前栏目下的文章信息")
    @PostMapping(value = {"/findArticlePageByColumnId", "/findArticlePageByColumnId/sso", "/findArticlePageByColumnId/api", "/findArticlePageByColumnId/anonymous"})
    public JsonResponse findArticlePageByColumnId(@RequestParam String columnId,
                                                  @RequestParam(required = false,defaultValue = "1") int page,
                                                  @RequestParam(required = false,defaultValue = "10") int size) {
        return service.findArticlePageByColumnId(columnId,page,size);
    }

    @ApiOperation(value = "公告分页", notes = "公告分页")
    @PostMapping(value = {"/findAnnouncementPageByColumnId", "/findAnnouncementPageByColumnId/sso", "/findAnnouncementPageByColumnId/api", "/findAnnouncementPageByColumnId/anonymous"})
    public JsonResponse findAnnouncementPageByColumnId(@RequestParam String columnId,
                                                  @RequestParam(required = false,defaultValue = "1") int page,
                                                  @RequestParam(required = false,defaultValue = "10") int size) {
        return service.findAnnouncementPageByColumnId(columnId,page,size);
    }

    @ApiOperation(value = "查看详情", notes = "查看详情")
    @PostMapping(value = {"/getDataArticleById", "/getDataArticleById/sso", "/getDataArticleById/api", "/getDataArticleById/anonymous"})
    public JsonResponse getDataArticleById(@RequestParam String columnId,@RequestParam String articleId) {
        return service.getDataArticleById(columnId,articleId);
    }


    @ApiOperation(value = "文章推荐", notes = "文章推荐")
    @PostMapping(value = {"/recommendedArticle", "/recommendedArticle/sso", "/recommendedArticle/api", "/recommendedArticle/anonymous"})
    public JsonResponse recommendedArticle(@RequestParam String pmInsId) {
        return service.recommendedArticle(pmInsId);
    }
}
