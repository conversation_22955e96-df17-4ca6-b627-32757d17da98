2025-05-29 00:04:18.491 [lettuce-nioEventLoop-4-4] WARN  io.lettuce.core.cluster.topology.ClusterTopologyRefresh.lambda$getConnections$0 Line:284 - Unable to connect to [************:7006]: No route to host: no further information: /************:7006
2025-05-29 00:04:18.489 [lettuce-nioEventLoop-4-11] WARN  io.lettuce.core.cluster.topology.ClusterTopologyRefresh.lambda$getConnections$0 Line:284 - Unable to connect to [************:7001]: No route to host: no further information: /************:7001
2025-05-29 00:04:18.491 [lettuce-nioEventLoop-4-3] WARN  io.lettuce.core.cluster.topology.ClusterTopologyRefresh.lambda$getConnections$0 Line:284 - Unable to connect to [************:7005]: No route to host: no further information: /************:7005
2025-05-29 00:04:18.489 [lettuce-nioEventLoop-4-12] WARN  io.lettuce.core.cluster.topology.ClusterTopologyRefresh.lambda$getConnections$0 Line:284 - Unable to connect to [************:7002]: No route to host: no further information: /************:7002
2025-05-29 00:04:18.489 [lettuce-nioEventLoop-4-2] WARN  io.lettuce.core.cluster.topology.ClusterTopologyRefresh.lambda$getConnections$0 Line:284 - Unable to connect to [************:7004]: No route to host: no further information: /************:7004
2025-05-29 00:04:18.489 [lettuce-nioEventLoop-4-1] WARN  io.lettuce.core.cluster.topology.ClusterTopologyRefresh.lambda$getConnections$0 Line:284 - Unable to connect to [************:7003]: No route to host: no further information: /************:7003
2025-05-29 00:04:18.504 [lettuce-nioEventLoop-4-5] WARN  io.lettuce.core.cluster.topology.ClusterTopologyRefresh.lambda$getConnections$0 Line:284 - Unable to connect to [************:7006]: No route to host: no further information: /************:7006
2025-05-29 00:04:18.505 [lettuce-nioEventLoop-4-6] WARN  io.lettuce.core.cluster.topology.ClusterTopologyRefresh.lambda$getConnections$0 Line:284 - Unable to connect to [************:7004]: No route to host: no further information: /************:7004
2025-05-29 00:04:18.506 [lettuce-nioEventLoop-4-7] WARN  io.lettuce.core.cluster.topology.ClusterTopologyRefresh.lambda$getConnections$0 Line:284 - Unable to connect to [************:7005]: No route to host: no further information: /************:7005
2025-05-29 00:04:18.507 [lettuce-nioEventLoop-4-8] WARN  io.lettuce.core.cluster.topology.ClusterTopologyRefresh.lambda$getConnections$0 Line:284 - Unable to connect to [************:7002]: No route to host: no further information: /************:7002
2025-05-29 00:04:18.509 [lettuce-nioEventLoop-4-9] WARN  io.lettuce.core.cluster.topology.ClusterTopologyRefresh.lambda$getConnections$0 Line:284 - Unable to connect to [************:7003]: No route to host: no further information: /************:7003
2025-05-29 00:04:18.511 [lettuce-nioEventLoop-4-10] WARN  io.lettuce.core.cluster.topology.ClusterTopologyRefresh.lambda$getConnections$0 Line:284 - Unable to connect to [************:7001]: No route to host: no further information: /************:7001
2025-05-29 00:04:18.536 [lettuce-eventExecutorLoop-1-7] WARN  io.lettuce.core.cluster.ClusterTopologyRefreshScheduler.doRun Line:258 - Cannot refresh Redis Cluster topology
io.lettuce.core.RedisException: Cannot retrieve initial cluster partitions from initial URIs [RedisURI [host='************', port=7006], RedisURI [host='************', port=7004], RedisURI [host='************', port=7005], RedisURI [host='************', port=7002], RedisURI [host='************', port=7003], RedisURI [host='************', port=7001]]
	at io.lettuce.core.cluster.RedisClusterClient.loadPartitions(RedisClusterClient.java:848)
	at io.lettuce.core.cluster.RedisClusterClient.reloadPartitions(RedisClusterClient.java:784)
	at io.lettuce.core.cluster.ClusterTopologyRefreshScheduler$ClusterTopologyRefreshTask.doRun(ClusterTopologyRefreshScheduler.java:256)
	at io.lettuce.core.cluster.ClusterTopologyRefreshScheduler$ClusterTopologyRefreshTask.run(ClusterTopologyRefreshScheduler.java:236)
	at io.netty.util.concurrent.PromiseTask.runTask(PromiseTask.java:98)
	at io.netty.util.concurrent.PromiseTask.run(PromiseTask.java:106)
	at io.netty.util.concurrent.DefaultEventExecutor.run(DefaultEventExecutor.java:66)
	at io.netty.util.concurrent.SingleThreadEventExecutor$6.run(SingleThreadEventExecutor.java:1050)
	at io.netty.util.internal.ThreadExecutorMap$2.run(ThreadExecutorMap.java:74)
	at io.netty.util.concurrent.FastThreadLocalRunnable.run(FastThreadLocalRunnable.java:30)
	at java.lang.Thread.run(Thread.java:748)
	Suppressed: io.lettuce.core.RedisConnectionException: Unable to establish a connection to Redis Cluster at [RedisURI [host='************', port=7001], RedisURI [host='************', port=7002], RedisURI [host='************', port=7003], RedisURI [host='************', port=7004], RedisURI [host='************', port=7005], RedisURI [host='************', port=7006]]
		at io.lettuce.core.cluster.topology.AsyncConnections.get(AsyncConnections.java:89)
		at io.lettuce.core.cluster.topology.ClusterTopologyRefresh.loadViews(ClusterTopologyRefresh.java:76)
		at io.lettuce.core.cluster.RedisClusterClient.doLoadPartitions(RedisClusterClient.java:865)
		at io.lettuce.core.cluster.RedisClusterClient.loadPartitions(RedisClusterClient.java:838)
		... 10 common frames omitted
		Suppressed: io.lettuce.core.RedisConnectionException: Unable to connect to [************:7001]: No route to host: no further information: /************:7001
			at io.lettuce.core.cluster.topology.ClusterTopologyRefresh.lambda$getConnections$0(ClusterTopologyRefresh.java:290)
			at java.util.concurrent.CompletableFuture.uniWhenComplete(CompletableFuture.java:760)
			at java.util.concurrent.CompletableFuture$UniWhenComplete.tryFire(CompletableFuture.java:736)
			at java.util.concurrent.CompletableFuture.postComplete(CompletableFuture.java:474)
			at java.util.concurrent.CompletableFuture.completeExceptionally(CompletableFuture.java:1977)
			at io.lettuce.core.AbstractRedisClient.lambda$initializeChannelAsync0$4(AbstractRedisClient.java:329)
			at io.netty.util.concurrent.DefaultPromise.notifyListener0(DefaultPromise.java:577)
			at io.netty.util.concurrent.DefaultPromise.notifyListeners0(DefaultPromise.java:570)
			at io.netty.util.concurrent.DefaultPromise.notifyListenersNow(DefaultPromise.java:549)
			at io.netty.util.concurrent.DefaultPromise.notifyListeners(DefaultPromise.java:490)
			at io.netty.util.concurrent.DefaultPromise.setValue0(DefaultPromise.java:615)
			at io.netty.util.concurrent.DefaultPromise.setFailure0(DefaultPromise.java:608)
			at io.netty.util.concurrent.DefaultPromise.tryFailure(DefaultPromise.java:117)
			at io.netty.channel.nio.AbstractNioChannel$AbstractNioUnsafe.fulfillConnectPromise(AbstractNioChannel.java:321)
			at io.netty.channel.nio.AbstractNioChannel$AbstractNioUnsafe.finishConnect(AbstractNioChannel.java:337)
			at io.netty.channel.nio.NioEventLoop.processSelectedKey(NioEventLoop.java:688)
			at io.netty.channel.nio.NioEventLoop.processSelectedKeysOptimized(NioEventLoop.java:635)
			at io.netty.channel.nio.NioEventLoop.processSelectedKeys(NioEventLoop.java:552)
			at io.netty.channel.nio.NioEventLoop.run(NioEventLoop.java:514)
			... 4 common frames omitted
		Caused by: io.netty.channel.AbstractChannel$AnnotatedNoRouteToHostException: No route to host: no further information: /************:7001
		Caused by: java.net.NoRouteToHostException: No route to host: no further information
			at sun.nio.ch.SocketChannelImpl.checkConnect(Native Method)
			at sun.nio.ch.SocketChannelImpl.finishConnect(SocketChannelImpl.java:717)
			at io.netty.channel.socket.nio.NioSocketChannel.doFinishConnect(NioSocketChannel.java:330)
			at io.netty.channel.nio.AbstractNioChannel$AbstractNioUnsafe.finishConnect(AbstractNioChannel.java:334)
			at io.netty.channel.nio.NioEventLoop.processSelectedKey(NioEventLoop.java:688)
			at io.netty.channel.nio.NioEventLoop.processSelectedKeysOptimized(NioEventLoop.java:635)
			at io.netty.channel.nio.NioEventLoop.processSelectedKeys(NioEventLoop.java:552)
			at io.netty.channel.nio.NioEventLoop.run(NioEventLoop.java:514)
			at io.netty.util.concurrent.SingleThreadEventExecutor$6.run(SingleThreadEventExecutor.java:1050)
			at io.netty.util.internal.ThreadExecutorMap$2.run(ThreadExecutorMap.java:74)
			at io.netty.util.concurrent.FastThreadLocalRunnable.run(FastThreadLocalRunnable.java:30)
			at java.lang.Thread.run(Thread.java:748)
		Suppressed: io.lettuce.core.RedisConnectionException: Unable to connect to [************:7005]: No route to host: no further information: /************:7005
			at io.lettuce.core.cluster.topology.ClusterTopologyRefresh.lambda$getConnections$0(ClusterTopologyRefresh.java:290)
			at java.util.concurrent.CompletableFuture.uniWhenComplete(CompletableFuture.java:760)
			at java.util.concurrent.CompletableFuture$UniWhenComplete.tryFire(CompletableFuture.java:736)
			at java.util.concurrent.CompletableFuture.postComplete(CompletableFuture.java:474)
			at java.util.concurrent.CompletableFuture.completeExceptionally(CompletableFuture.java:1977)
			at io.lettuce.core.AbstractRedisClient.lambda$initializeChannelAsync0$4(AbstractRedisClient.java:329)
			at io.netty.util.concurrent.DefaultPromise.notifyListener0(DefaultPromise.java:577)
			at io.netty.util.concurrent.DefaultPromise.notifyListeners0(DefaultPromise.java:570)
			at io.netty.util.concurrent.DefaultPromise.notifyListenersNow(DefaultPromise.java:549)
			at io.netty.util.concurrent.DefaultPromise.notifyListeners(DefaultPromise.java:490)
			at io.netty.util.concurrent.DefaultPromise.setValue0(DefaultPromise.java:615)
			at io.netty.util.concurrent.DefaultPromise.setFailure0(DefaultPromise.java:608)
			at io.netty.util.concurrent.DefaultPromise.tryFailure(DefaultPromise.java:117)
			at io.netty.channel.nio.AbstractNioChannel$AbstractNioUnsafe.fulfillConnectPromise(AbstractNioChannel.java:321)
			at io.netty.channel.nio.AbstractNioChannel$AbstractNioUnsafe.finishConnect(AbstractNioChannel.java:337)
			at io.netty.channel.nio.NioEventLoop.processSelectedKey(NioEventLoop.java:688)
			at io.netty.channel.nio.NioEventLoop.processSelectedKeysOptimized(NioEventLoop.java:635)
			at io.netty.channel.nio.NioEventLoop.processSelectedKeys(NioEventLoop.java:552)
			at io.netty.channel.nio.NioEventLoop.run(NioEventLoop.java:514)
			... 4 common frames omitted
		Caused by: io.netty.channel.AbstractChannel$AnnotatedNoRouteToHostException: No route to host: no further information: /************:7005
		Caused by: java.net.NoRouteToHostException: No route to host: no further information
			at sun.nio.ch.SocketChannelImpl.checkConnect(Native Method)
			at sun.nio.ch.SocketChannelImpl.finishConnect(SocketChannelImpl.java:717)
			at io.netty.channel.socket.nio.NioSocketChannel.doFinishConnect(NioSocketChannel.java:330)
			at io.netty.channel.nio.AbstractNioChannel$AbstractNioUnsafe.finishConnect(AbstractNioChannel.java:334)
			at io.netty.channel.nio.NioEventLoop.processSelectedKey(NioEventLoop.java:688)
			at io.netty.channel.nio.NioEventLoop.processSelectedKeysOptimized(NioEventLoop.java:635)
			at io.netty.channel.nio.NioEventLoop.processSelectedKeys(NioEventLoop.java:552)
			at io.netty.channel.nio.NioEventLoop.run(NioEventLoop.java:514)
			at io.netty.util.concurrent.SingleThreadEventExecutor$6.run(SingleThreadEventExecutor.java:1050)
			at io.netty.util.internal.ThreadExecutorMap$2.run(ThreadExecutorMap.java:74)
			at io.netty.util.concurrent.FastThreadLocalRunnable.run(FastThreadLocalRunnable.java:30)
			at java.lang.Thread.run(Thread.java:748)
		Suppressed: io.lettuce.core.RedisConnectionException: Unable to connect to [************:7002]: No route to host: no further information: /************:7002
			at io.lettuce.core.cluster.topology.ClusterTopologyRefresh.lambda$getConnections$0(ClusterTopologyRefresh.java:290)
			at java.util.concurrent.CompletableFuture.uniWhenComplete(CompletableFuture.java:760)
			at java.util.concurrent.CompletableFuture$UniWhenComplete.tryFire(CompletableFuture.java:736)
			at java.util.concurrent.CompletableFuture.postComplete(CompletableFuture.java:474)
			at java.util.concurrent.CompletableFuture.completeExceptionally(CompletableFuture.java:1977)
			at io.lettuce.core.AbstractRedisClient.lambda$initializeChannelAsync0$4(AbstractRedisClient.java:329)
			at io.netty.util.concurrent.DefaultPromise.notifyListener0(DefaultPromise.java:577)
			at io.netty.util.concurrent.DefaultPromise.notifyListeners0(DefaultPromise.java:570)
			at io.netty.util.concurrent.DefaultPromise.notifyListenersNow(DefaultPromise.java:549)
			at io.netty.util.concurrent.DefaultPromise.notifyListeners(DefaultPromise.java:490)
			at io.netty.util.concurrent.DefaultPromise.setValue0(DefaultPromise.java:615)
			at io.netty.util.concurrent.DefaultPromise.setFailure0(DefaultPromise.java:608)
			at io.netty.util.concurrent.DefaultPromise.tryFailure(DefaultPromise.java:117)
			at io.netty.channel.nio.AbstractNioChannel$AbstractNioUnsafe.fulfillConnectPromise(AbstractNioChannel.java:321)
			at io.netty.channel.nio.AbstractNioChannel$AbstractNioUnsafe.finishConnect(AbstractNioChannel.java:337)
			at io.netty.channel.nio.NioEventLoop.processSelectedKey(NioEventLoop.java:688)
			at io.netty.channel.nio.NioEventLoop.processSelectedKeysOptimized(NioEventLoop.java:635)
			at io.netty.channel.nio.NioEventLoop.processSelectedKeys(NioEventLoop.java:552)
			at io.netty.channel.nio.NioEventLoop.run(NioEventLoop.java:514)
			... 4 common frames omitted
		Caused by: io.netty.channel.AbstractChannel$AnnotatedNoRouteToHostException: No route to host: no further information: /************:7002
		Caused by: java.net.NoRouteToHostException: No route to host: no further information
			at sun.nio.ch.SocketChannelImpl.checkConnect(Native Method)
			at sun.nio.ch.SocketChannelImpl.finishConnect(SocketChannelImpl.java:717)
			at io.netty.channel.socket.nio.NioSocketChannel.doFinishConnect(NioSocketChannel.java:330)
			at io.netty.channel.nio.AbstractNioChannel$AbstractNioUnsafe.finishConnect(AbstractNioChannel.java:334)
			at io.netty.channel.nio.NioEventLoop.processSelectedKey(NioEventLoop.java:688)
			at io.netty.channel.nio.NioEventLoop.processSelectedKeysOptimized(NioEventLoop.java:635)
			at io.netty.channel.nio.NioEventLoop.processSelectedKeys(NioEventLoop.java:552)
			at io.netty.channel.nio.NioEventLoop.run(NioEventLoop.java:514)
			at io.netty.util.concurrent.SingleThreadEventExecutor$6.run(SingleThreadEventExecutor.java:1050)
			at io.netty.util.internal.ThreadExecutorMap$2.run(ThreadExecutorMap.java:74)
			at io.netty.util.concurrent.FastThreadLocalRunnable.run(FastThreadLocalRunnable.java:30)
			at java.lang.Thread.run(Thread.java:748)
		Suppressed: io.lettuce.core.RedisConnectionException: Unable to connect to [************:7004]: No route to host: no further information: /************:7004
			at io.lettuce.core.cluster.topology.ClusterTopologyRefresh.lambda$getConnections$0(ClusterTopologyRefresh.java:290)
			at java.util.concurrent.CompletableFuture.uniWhenComplete(CompletableFuture.java:760)
			at java.util.concurrent.CompletableFuture$UniWhenComplete.tryFire(CompletableFuture.java:736)
			at java.util.concurrent.CompletableFuture.postComplete(CompletableFuture.java:474)
			at java.util.concurrent.CompletableFuture.completeExceptionally(CompletableFuture.java:1977)
			at io.lettuce.core.AbstractRedisClient.lambda$initializeChannelAsync0$4(AbstractRedisClient.java:329)
			at io.netty.util.concurrent.DefaultPromise.notifyListener0(DefaultPromise.java:577)
			at io.netty.util.concurrent.DefaultPromise.notifyListeners0(DefaultPromise.java:570)
			at io.netty.util.concurrent.DefaultPromise.notifyListenersNow(DefaultPromise.java:549)
			at io.netty.util.concurrent.DefaultPromise.notifyListeners(DefaultPromise.java:490)
			at io.netty.util.concurrent.DefaultPromise.setValue0(DefaultPromise.java:615)
			at io.netty.util.concurrent.DefaultPromise.setFailure0(DefaultPromise.java:608)
			at io.netty.util.concurrent.DefaultPromise.tryFailure(DefaultPromise.java:117)
			at io.netty.channel.nio.AbstractNioChannel$AbstractNioUnsafe.fulfillConnectPromise(AbstractNioChannel.java:321)
			at io.netty.channel.nio.AbstractNioChannel$AbstractNioUnsafe.finishConnect(AbstractNioChannel.java:337)
			at io.netty.channel.nio.NioEventLoop.processSelectedKey(NioEventLoop.java:688)
			at io.netty.channel.nio.NioEventLoop.processSelectedKeysOptimized(NioEventLoop.java:635)
			at io.netty.channel.nio.NioEventLoop.processSelectedKeys(NioEventLoop.java:552)
			at io.netty.channel.nio.NioEventLoop.run(NioEventLoop.java:514)
			... 4 common frames omitted
		Caused by: io.netty.channel.AbstractChannel$AnnotatedNoRouteToHostException: No route to host: no further information: /************:7004
		Caused by: java.net.NoRouteToHostException: No route to host: no further information
			at sun.nio.ch.SocketChannelImpl.checkConnect(Native Method)
			at sun.nio.ch.SocketChannelImpl.finishConnect(SocketChannelImpl.java:717)
			at io.netty.channel.socket.nio.NioSocketChannel.doFinishConnect(NioSocketChannel.java:330)
			at io.netty.channel.nio.AbstractNioChannel$AbstractNioUnsafe.finishConnect(AbstractNioChannel.java:334)
			at io.netty.channel.nio.NioEventLoop.processSelectedKey(NioEventLoop.java:688)
			at io.netty.channel.nio.NioEventLoop.processSelectedKeysOptimized(NioEventLoop.java:635)
			at io.netty.channel.nio.NioEventLoop.processSelectedKeys(NioEventLoop.java:552)
			at io.netty.channel.nio.NioEventLoop.run(NioEventLoop.java:514)
			at io.netty.util.concurrent.SingleThreadEventExecutor$6.run(SingleThreadEventExecutor.java:1050)
			at io.netty.util.internal.ThreadExecutorMap$2.run(ThreadExecutorMap.java:74)
			at io.netty.util.concurrent.FastThreadLocalRunnable.run(FastThreadLocalRunnable.java:30)
			at java.lang.Thread.run(Thread.java:748)
		Suppressed: io.lettuce.core.RedisConnectionException: Unable to connect to [************:7003]: No route to host: no further information: /************:7003
			at io.lettuce.core.cluster.topology.ClusterTopologyRefresh.lambda$getConnections$0(ClusterTopologyRefresh.java:290)
			at java.util.concurrent.CompletableFuture.uniWhenComplete(CompletableFuture.java:760)
			at java.util.concurrent.CompletableFuture$UniWhenComplete.tryFire(CompletableFuture.java:736)
			at java.util.concurrent.CompletableFuture.postComplete(CompletableFuture.java:474)
			at java.util.concurrent.CompletableFuture.completeExceptionally(CompletableFuture.java:1977)
			at io.lettuce.core.AbstractRedisClient.lambda$initializeChannelAsync0$4(AbstractRedisClient.java:329)
			at io.netty.util.concurrent.DefaultPromise.notifyListener0(DefaultPromise.java:577)
			at io.netty.util.concurrent.DefaultPromise.notifyListeners0(DefaultPromise.java:570)
			at io.netty.util.concurrent.DefaultPromise.notifyListenersNow(DefaultPromise.java:549)
			at io.netty.util.concurrent.DefaultPromise.notifyListeners(DefaultPromise.java:490)
			at io.netty.util.concurrent.DefaultPromise.setValue0(DefaultPromise.java:615)
			at io.netty.util.concurrent.DefaultPromise.setFailure0(DefaultPromise.java:608)
			at io.netty.util.concurrent.DefaultPromise.tryFailure(DefaultPromise.java:117)
			at io.netty.channel.nio.AbstractNioChannel$AbstractNioUnsafe.fulfillConnectPromise(AbstractNioChannel.java:321)
			at io.netty.channel.nio.AbstractNioChannel$AbstractNioUnsafe.finishConnect(AbstractNioChannel.java:337)
			at io.netty.channel.nio.NioEventLoop.processSelectedKey(NioEventLoop.java:688)
			at io.netty.channel.nio.NioEventLoop.processSelectedKeysOptimized(NioEventLoop.java:635)
			at io.netty.channel.nio.NioEventLoop.processSelectedKeys(NioEventLoop.java:552)
			at io.netty.channel.nio.NioEventLoop.run(NioEventLoop.java:514)
			... 4 common frames omitted
		Caused by: io.netty.channel.AbstractChannel$AnnotatedNoRouteToHostException: No route to host: no further information: /************:7003
		Caused by: java.net.NoRouteToHostException: No route to host: no further information
			at sun.nio.ch.SocketChannelImpl.checkConnect(Native Method)
			at sun.nio.ch.SocketChannelImpl.finishConnect(SocketChannelImpl.java:717)
			at io.netty.channel.socket.nio.NioSocketChannel.doFinishConnect(NioSocketChannel.java:330)
			at io.netty.channel.nio.AbstractNioChannel$AbstractNioUnsafe.finishConnect(AbstractNioChannel.java:334)
			at io.netty.channel.nio.NioEventLoop.processSelectedKey(NioEventLoop.java:688)
			at io.netty.channel.nio.NioEventLoop.processSelectedKeysOptimized(NioEventLoop.java:635)
			at io.netty.channel.nio.NioEventLoop.processSelectedKeys(NioEventLoop.java:552)
			at io.netty.channel.nio.NioEventLoop.run(NioEventLoop.java:514)
			at io.netty.util.concurrent.SingleThreadEventExecutor$6.run(SingleThreadEventExecutor.java:1050)
			at io.netty.util.internal.ThreadExecutorMap$2.run(ThreadExecutorMap.java:74)
			at io.netty.util.concurrent.FastThreadLocalRunnable.run(FastThreadLocalRunnable.java:30)
			at java.lang.Thread.run(Thread.java:748)
	Caused by: io.lettuce.core.RedisConnectionException: Unable to connect to [************:7006]: No route to host: no further information: /************:7006
		at io.lettuce.core.cluster.topology.ClusterTopologyRefresh.lambda$getConnections$0(ClusterTopologyRefresh.java:290)
		at java.util.concurrent.CompletableFuture.uniWhenComplete(CompletableFuture.java:760)
		at java.util.concurrent.CompletableFuture$UniWhenComplete.tryFire(CompletableFuture.java:736)
		at java.util.concurrent.CompletableFuture.postComplete(CompletableFuture.java:474)
		at java.util.concurrent.CompletableFuture.completeExceptionally(CompletableFuture.java:1977)
		at io.lettuce.core.AbstractRedisClient.lambda$initializeChannelAsync0$4(AbstractRedisClient.java:329)
		at io.netty.util.concurrent.DefaultPromise.notifyListener0(DefaultPromise.java:577)
		at io.netty.util.concurrent.DefaultPromise.notifyListeners0(DefaultPromise.java:570)
		at io.netty.util.concurrent.DefaultPromise.notifyListenersNow(DefaultPromise.java:549)
		at io.netty.util.concurrent.DefaultPromise.notifyListeners(DefaultPromise.java:490)
		at io.netty.util.concurrent.DefaultPromise.setValue0(DefaultPromise.java:615)
		at io.netty.util.concurrent.DefaultPromise.setFailure0(DefaultPromise.java:608)
		at io.netty.util.concurrent.DefaultPromise.tryFailure(DefaultPromise.java:117)
		at io.netty.channel.nio.AbstractNioChannel$AbstractNioUnsafe.fulfillConnectPromise(AbstractNioChannel.java:321)
		at io.netty.channel.nio.AbstractNioChannel$AbstractNioUnsafe.finishConnect(AbstractNioChannel.java:337)
		at io.netty.channel.nio.NioEventLoop.processSelectedKey(NioEventLoop.java:688)
		at io.netty.channel.nio.NioEventLoop.processSelectedKeysOptimized(NioEventLoop.java:635)
		at io.netty.channel.nio.NioEventLoop.processSelectedKeys(NioEventLoop.java:552)
		at io.netty.channel.nio.NioEventLoop.run(NioEventLoop.java:514)
		... 4 common frames omitted
	Caused by: io.netty.channel.AbstractChannel$AnnotatedNoRouteToHostException: No route to host: no further information: /************:7006
	Caused by: java.net.NoRouteToHostException: No route to host: no further information
		at sun.nio.ch.SocketChannelImpl.checkConnect(Native Method)
		at sun.nio.ch.SocketChannelImpl.finishConnect(SocketChannelImpl.java:717)
		at io.netty.channel.socket.nio.NioSocketChannel.doFinishConnect(NioSocketChannel.java:330)
		at io.netty.channel.nio.AbstractNioChannel$AbstractNioUnsafe.finishConnect(AbstractNioChannel.java:334)
		at io.netty.channel.nio.NioEventLoop.processSelectedKey(NioEventLoop.java:688)
		at io.netty.channel.nio.NioEventLoop.processSelectedKeysOptimized(NioEventLoop.java:635)
		at io.netty.channel.nio.NioEventLoop.processSelectedKeys(NioEventLoop.java:552)
		at io.netty.channel.nio.NioEventLoop.run(NioEventLoop.java:514)
		at io.netty.util.concurrent.SingleThreadEventExecutor$6.run(SingleThreadEventExecutor.java:1050)
		at io.netty.util.internal.ThreadExecutorMap$2.run(ThreadExecutorMap.java:74)
		at io.netty.util.concurrent.FastThreadLocalRunnable.run(FastThreadLocalRunnable.java:30)
		at java.lang.Thread.run(Thread.java:748)
Caused by: io.lettuce.core.RedisConnectionException: Unable to establish a connection to Redis Cluster at [RedisURI [host='************', port=7001], RedisURI [host='************', port=7002], RedisURI [host='************', port=7003], RedisURI [host='************', port=7004], RedisURI [host='************', port=7005], RedisURI [host='************', port=7006]]
	at io.lettuce.core.cluster.topology.AsyncConnections.get(AsyncConnections.java:89)
	at io.lettuce.core.cluster.topology.ClusterTopologyRefresh.loadViews(ClusterTopologyRefresh.java:76)
	at io.lettuce.core.cluster.RedisClusterClient.doLoadPartitions(RedisClusterClient.java:865)
	at io.lettuce.core.cluster.RedisClusterClient.loadPartitions(RedisClusterClient.java:845)
	... 10 common frames omitted
	Suppressed: io.lettuce.core.RedisConnectionException: Unable to connect to [************:7006]: No route to host: no further information: /************:7006
		at io.lettuce.core.cluster.topology.ClusterTopologyRefresh.lambda$getConnections$0(ClusterTopologyRefresh.java:290)
		at java.util.concurrent.CompletableFuture.uniWhenComplete(CompletableFuture.java:760)
		at java.util.concurrent.CompletableFuture$UniWhenComplete.tryFire(CompletableFuture.java:736)
		at java.util.concurrent.CompletableFuture.postComplete(CompletableFuture.java:474)
		at java.util.concurrent.CompletableFuture.completeExceptionally(CompletableFuture.java:1977)
		at io.lettuce.core.AbstractRedisClient.lambda$initializeChannelAsync0$4(AbstractRedisClient.java:329)
		at io.netty.util.concurrent.DefaultPromise.notifyListener0(DefaultPromise.java:577)
		at io.netty.util.concurrent.DefaultPromise.notifyListeners0(DefaultPromise.java:570)
		at io.netty.util.concurrent.DefaultPromise.notifyListenersNow(DefaultPromise.java:549)
		at io.netty.util.concurrent.DefaultPromise.notifyListeners(DefaultPromise.java:490)
		at io.netty.util.concurrent.DefaultPromise.setValue0(DefaultPromise.java:615)
		at io.netty.util.concurrent.DefaultPromise.setFailure0(DefaultPromise.java:608)
		at io.netty.util.concurrent.DefaultPromise.tryFailure(DefaultPromise.java:117)
		at io.netty.channel.nio.AbstractNioChannel$AbstractNioUnsafe.fulfillConnectPromise(AbstractNioChannel.java:321)
		at io.netty.channel.nio.AbstractNioChannel$AbstractNioUnsafe.finishConnect(AbstractNioChannel.java:337)
		at io.netty.channel.nio.NioEventLoop.processSelectedKey(NioEventLoop.java:688)
		at io.netty.channel.nio.NioEventLoop.processSelectedKeysOptimized(NioEventLoop.java:635)
		at io.netty.channel.nio.NioEventLoop.processSelectedKeys(NioEventLoop.java:552)
		at io.netty.channel.nio.NioEventLoop.run(NioEventLoop.java:514)
		... 4 common frames omitted
	Caused by: io.netty.channel.AbstractChannel$AnnotatedNoRouteToHostException: No route to host: no further information: /************:7006
	Caused by: java.net.NoRouteToHostException: No route to host: no further information
		at sun.nio.ch.SocketChannelImpl.checkConnect(Native Method)
		at sun.nio.ch.SocketChannelImpl.finishConnect(SocketChannelImpl.java:717)
		at io.netty.channel.socket.nio.NioSocketChannel.doFinishConnect(NioSocketChannel.java:330)
		at io.netty.channel.nio.AbstractNioChannel$AbstractNioUnsafe.finishConnect(AbstractNioChannel.java:334)
		at io.netty.channel.nio.NioEventLoop.processSelectedKey(NioEventLoop.java:688)
		at io.netty.channel.nio.NioEventLoop.processSelectedKeysOptimized(NioEventLoop.java:635)
		at io.netty.channel.nio.NioEventLoop.processSelectedKeys(NioEventLoop.java:552)
		at io.netty.channel.nio.NioEventLoop.run(NioEventLoop.java:514)
		at io.netty.util.concurrent.SingleThreadEventExecutor$6.run(SingleThreadEventExecutor.java:1050)
		at io.netty.util.internal.ThreadExecutorMap$2.run(ThreadExecutorMap.java:74)
		at io.netty.util.concurrent.FastThreadLocalRunnable.run(FastThreadLocalRunnable.java:30)
		at java.lang.Thread.run(Thread.java:748)
	Suppressed: io.lettuce.core.RedisConnectionException: Unable to connect to [************:7005]: No route to host: no further information: /************:7005
		at io.lettuce.core.cluster.topology.ClusterTopologyRefresh.lambda$getConnections$0(ClusterTopologyRefresh.java:290)
		at java.util.concurrent.CompletableFuture.uniWhenComplete(CompletableFuture.java:760)
		at java.util.concurrent.CompletableFuture$UniWhenComplete.tryFire(CompletableFuture.java:736)
		at java.util.concurrent.CompletableFuture.postComplete(CompletableFuture.java:474)
		at java.util.concurrent.CompletableFuture.completeExceptionally(CompletableFuture.java:1977)
		at io.lettuce.core.AbstractRedisClient.lambda$initializeChannelAsync0$4(AbstractRedisClient.java:329)
		at io.netty.util.concurrent.DefaultPromise.notifyListener0(DefaultPromise.java:577)
		at io.netty.util.concurrent.DefaultPromise.notifyListeners0(DefaultPromise.java:570)
		at io.netty.util.concurrent.DefaultPromise.notifyListenersNow(DefaultPromise.java:549)
		at io.netty.util.concurrent.DefaultPromise.notifyListeners(DefaultPromise.java:490)
		at io.netty.util.concurrent.DefaultPromise.setValue0(DefaultPromise.java:615)
		at io.netty.util.concurrent.DefaultPromise.setFailure0(DefaultPromise.java:608)
		at io.netty.util.concurrent.DefaultPromise.tryFailure(DefaultPromise.java:117)
		at io.netty.channel.nio.AbstractNioChannel$AbstractNioUnsafe.fulfillConnectPromise(AbstractNioChannel.java:321)
		at io.netty.channel.nio.AbstractNioChannel$AbstractNioUnsafe.finishConnect(AbstractNioChannel.java:337)
		at io.netty.channel.nio.NioEventLoop.processSelectedKey(NioEventLoop.java:688)
		at io.netty.channel.nio.NioEventLoop.processSelectedKeysOptimized(NioEventLoop.java:635)
		at io.netty.channel.nio.NioEventLoop.processSelectedKeys(NioEventLoop.java:552)
		at io.netty.channel.nio.NioEventLoop.run(NioEventLoop.java:514)
		... 4 common frames omitted
	Caused by: io.netty.channel.AbstractChannel$AnnotatedNoRouteToHostException: No route to host: no further information: /************:7005
	Caused by: java.net.NoRouteToHostException: No route to host: no further information
		at sun.nio.ch.SocketChannelImpl.checkConnect(Native Method)
		at sun.nio.ch.SocketChannelImpl.finishConnect(SocketChannelImpl.java:717)
		at io.netty.channel.socket.nio.NioSocketChannel.doFinishConnect(NioSocketChannel.java:330)
		at io.netty.channel.nio.AbstractNioChannel$AbstractNioUnsafe.finishConnect(AbstractNioChannel.java:334)
		at io.netty.channel.nio.NioEventLoop.processSelectedKey(NioEventLoop.java:688)
		at io.netty.channel.nio.NioEventLoop.processSelectedKeysOptimized(NioEventLoop.java:635)
		at io.netty.channel.nio.NioEventLoop.processSelectedKeys(NioEventLoop.java:552)
		at io.netty.channel.nio.NioEventLoop.run(NioEventLoop.java:514)
		at io.netty.util.concurrent.SingleThreadEventExecutor$6.run(SingleThreadEventExecutor.java:1050)
		at io.netty.util.internal.ThreadExecutorMap$2.run(ThreadExecutorMap.java:74)
		at io.netty.util.concurrent.FastThreadLocalRunnable.run(FastThreadLocalRunnable.java:30)
		at java.lang.Thread.run(Thread.java:748)
	Suppressed: io.lettuce.core.RedisConnectionException: Unable to connect to [************:7002]: No route to host: no further information: /************:7002
		at io.lettuce.core.cluster.topology.ClusterTopologyRefresh.lambda$getConnections$0(ClusterTopologyRefresh.java:290)
		at java.util.concurrent.CompletableFuture.uniWhenComplete(CompletableFuture.java:760)
		at java.util.concurrent.CompletableFuture$UniWhenComplete.tryFire(CompletableFuture.java:736)
		at java.util.concurrent.CompletableFuture.postComplete(CompletableFuture.java:474)
		at java.util.concurrent.CompletableFuture.completeExceptionally(CompletableFuture.java:1977)
		at io.lettuce.core.AbstractRedisClient.lambda$initializeChannelAsync0$4(AbstractRedisClient.java:329)
		at io.netty.util.concurrent.DefaultPromise.notifyListener0(DefaultPromise.java:577)
		at io.netty.util.concurrent.DefaultPromise.notifyListeners0(DefaultPromise.java:570)
		at io.netty.util.concurrent.DefaultPromise.notifyListenersNow(DefaultPromise.java:549)
		at io.netty.util.concurrent.DefaultPromise.notifyListeners(DefaultPromise.java:490)
		at io.netty.util.concurrent.DefaultPromise.setValue0(DefaultPromise.java:615)
		at io.netty.util.concurrent.DefaultPromise.setFailure0(DefaultPromise.java:608)
		at io.netty.util.concurrent.DefaultPromise.tryFailure(DefaultPromise.java:117)
		at io.netty.channel.nio.AbstractNioChannel$AbstractNioUnsafe.fulfillConnectPromise(AbstractNioChannel.java:321)
		at io.netty.channel.nio.AbstractNioChannel$AbstractNioUnsafe.finishConnect(AbstractNioChannel.java:337)
		at io.netty.channel.nio.NioEventLoop.processSelectedKey(NioEventLoop.java:688)
		at io.netty.channel.nio.NioEventLoop.processSelectedKeysOptimized(NioEventLoop.java:635)
		at io.netty.channel.nio.NioEventLoop.processSelectedKeys(NioEventLoop.java:552)
		at io.netty.channel.nio.NioEventLoop.run(NioEventLoop.java:514)
		... 4 common frames omitted
	Caused by: io.netty.channel.AbstractChannel$AnnotatedNoRouteToHostException: No route to host: no further information: /************:7002
	Caused by: java.net.NoRouteToHostException: No route to host: no further information
		at sun.nio.ch.SocketChannelImpl.checkConnect(Native Method)
		at sun.nio.ch.SocketChannelImpl.finishConnect(SocketChannelImpl.java:717)
		at io.netty.channel.socket.nio.NioSocketChannel.doFinishConnect(NioSocketChannel.java:330)
		at io.netty.channel.nio.AbstractNioChannel$AbstractNioUnsafe.finishConnect(AbstractNioChannel.java:334)
		at io.netty.channel.nio.NioEventLoop.processSelectedKey(NioEventLoop.java:688)
		at io.netty.channel.nio.NioEventLoop.processSelectedKeysOptimized(NioEventLoop.java:635)
		at io.netty.channel.nio.NioEventLoop.processSelectedKeys(NioEventLoop.java:552)
		at io.netty.channel.nio.NioEventLoop.run(NioEventLoop.java:514)
		at io.netty.util.concurrent.SingleThreadEventExecutor$6.run(SingleThreadEventExecutor.java:1050)
		at io.netty.util.internal.ThreadExecutorMap$2.run(ThreadExecutorMap.java:74)
		at io.netty.util.concurrent.FastThreadLocalRunnable.run(FastThreadLocalRunnable.java:30)
		at java.lang.Thread.run(Thread.java:748)
	Suppressed: io.lettuce.core.RedisConnectionException: Unable to connect to [************:7003]: No route to host: no further information: /************:7003
		at io.lettuce.core.cluster.topology.ClusterTopologyRefresh.lambda$getConnections$0(ClusterTopologyRefresh.java:290)
		at java.util.concurrent.CompletableFuture.uniWhenComplete(CompletableFuture.java:760)
		at java.util.concurrent.CompletableFuture$UniWhenComplete.tryFire(CompletableFuture.java:736)
		at java.util.concurrent.CompletableFuture.postComplete(CompletableFuture.java:474)
		at java.util.concurrent.CompletableFuture.completeExceptionally(CompletableFuture.java:1977)
		at io.lettuce.core.AbstractRedisClient.lambda$initializeChannelAsync0$4(AbstractRedisClient.java:329)
		at io.netty.util.concurrent.DefaultPromise.notifyListener0(DefaultPromise.java:577)
		at io.netty.util.concurrent.DefaultPromise.notifyListeners0(DefaultPromise.java:570)
		at io.netty.util.concurrent.DefaultPromise.notifyListenersNow(DefaultPromise.java:549)
		at io.netty.util.concurrent.DefaultPromise.notifyListeners(DefaultPromise.java:490)
		at io.netty.util.concurrent.DefaultPromise.setValue0(DefaultPromise.java:615)
		at io.netty.util.concurrent.DefaultPromise.setFailure0(DefaultPromise.java:608)
		at io.netty.util.concurrent.DefaultPromise.tryFailure(DefaultPromise.java:117)
		at io.netty.channel.nio.AbstractNioChannel$AbstractNioUnsafe.fulfillConnectPromise(AbstractNioChannel.java:321)
		at io.netty.channel.nio.AbstractNioChannel$AbstractNioUnsafe.finishConnect(AbstractNioChannel.java:337)
		at io.netty.channel.nio.NioEventLoop.processSelectedKey(NioEventLoop.java:688)
		at io.netty.channel.nio.NioEventLoop.processSelectedKeysOptimized(NioEventLoop.java:635)
		at io.netty.channel.nio.NioEventLoop.processSelectedKeys(NioEventLoop.java:552)
		at io.netty.channel.nio.NioEventLoop.run(NioEventLoop.java:514)
		... 4 common frames omitted
	Caused by: io.netty.channel.AbstractChannel$AnnotatedNoRouteToHostException: No route to host: no further information: /************:7003
	Caused by: java.net.NoRouteToHostException: No route to host: no further information
		at sun.nio.ch.SocketChannelImpl.checkConnect(Native Method)
		at sun.nio.ch.SocketChannelImpl.finishConnect(SocketChannelImpl.java:717)
		at io.netty.channel.socket.nio.NioSocketChannel.doFinishConnect(NioSocketChannel.java:330)
		at io.netty.channel.nio.AbstractNioChannel$AbstractNioUnsafe.finishConnect(AbstractNioChannel.java:334)
		at io.netty.channel.nio.NioEventLoop.processSelectedKey(NioEventLoop.java:688)
		at io.netty.channel.nio.NioEventLoop.processSelectedKeysOptimized(NioEventLoop.java:635)
		at io.netty.channel.nio.NioEventLoop.processSelectedKeys(NioEventLoop.java:552)
		at io.netty.channel.nio.NioEventLoop.run(NioEventLoop.java:514)
		at io.netty.util.concurrent.SingleThreadEventExecutor$6.run(SingleThreadEventExecutor.java:1050)
		at io.netty.util.internal.ThreadExecutorMap$2.run(ThreadExecutorMap.java:74)
		at io.netty.util.concurrent.FastThreadLocalRunnable.run(FastThreadLocalRunnable.java:30)
		at java.lang.Thread.run(Thread.java:748)
	Suppressed: io.lettuce.core.RedisConnectionException: Unable to connect to [************:7001]: No route to host: no further information: /************:7001
		at io.lettuce.core.cluster.topology.ClusterTopologyRefresh.lambda$getConnections$0(ClusterTopologyRefresh.java:290)
		at java.util.concurrent.CompletableFuture.uniWhenComplete(CompletableFuture.java:760)
		at java.util.concurrent.CompletableFuture$UniWhenComplete.tryFire(CompletableFuture.java:736)
		at java.util.concurrent.CompletableFuture.postComplete(CompletableFuture.java:474)
		at java.util.concurrent.CompletableFuture.completeExceptionally(CompletableFuture.java:1977)
		at io.lettuce.core.AbstractRedisClient.lambda$initializeChannelAsync0$4(AbstractRedisClient.java:329)
		at io.netty.util.concurrent.DefaultPromise.notifyListener0(DefaultPromise.java:577)
		at io.netty.util.concurrent.DefaultPromise.notifyListeners0(DefaultPromise.java:570)
		at io.netty.util.concurrent.DefaultPromise.notifyListenersNow(DefaultPromise.java:549)
		at io.netty.util.concurrent.DefaultPromise.notifyListeners(DefaultPromise.java:490)
		at io.netty.util.concurrent.DefaultPromise.setValue0(DefaultPromise.java:615)
		at io.netty.util.concurrent.DefaultPromise.setFailure0(DefaultPromise.java:608)
		at io.netty.util.concurrent.DefaultPromise.tryFailure(DefaultPromise.java:117)
		at io.netty.channel.nio.AbstractNioChannel$AbstractNioUnsafe.fulfillConnectPromise(AbstractNioChannel.java:321)
		at io.netty.channel.nio.AbstractNioChannel$AbstractNioUnsafe.finishConnect(AbstractNioChannel.java:337)
		at io.netty.channel.nio.NioEventLoop.processSelectedKey(NioEventLoop.java:688)
		at io.netty.channel.nio.NioEventLoop.processSelectedKeysOptimized(NioEventLoop.java:635)
		at io.netty.channel.nio.NioEventLoop.processSelectedKeys(NioEventLoop.java:552)
		at io.netty.channel.nio.NioEventLoop.run(NioEventLoop.java:514)
		... 4 common frames omitted
	Caused by: io.netty.channel.AbstractChannel$AnnotatedNoRouteToHostException: No route to host: no further information: /************:7001
	Caused by: java.net.NoRouteToHostException: No route to host: no further information
		at sun.nio.ch.SocketChannelImpl.checkConnect(Native Method)
		at sun.nio.ch.SocketChannelImpl.finishConnect(SocketChannelImpl.java:717)
		at io.netty.channel.socket.nio.NioSocketChannel.doFinishConnect(NioSocketChannel.java:330)
		at io.netty.channel.nio.AbstractNioChannel$AbstractNioUnsafe.finishConnect(AbstractNioChannel.java:334)
		at io.netty.channel.nio.NioEventLoop.processSelectedKey(NioEventLoop.java:688)
		at io.netty.channel.nio.NioEventLoop.processSelectedKeysOptimized(NioEventLoop.java:635)
		at io.netty.channel.nio.NioEventLoop.processSelectedKeys(NioEventLoop.java:552)
		at io.netty.channel.nio.NioEventLoop.run(NioEventLoop.java:514)
		at io.netty.util.concurrent.SingleThreadEventExecutor$6.run(SingleThreadEventExecutor.java:1050)
		at io.netty.util.internal.ThreadExecutorMap$2.run(ThreadExecutorMap.java:74)
		at io.netty.util.concurrent.FastThreadLocalRunnable.run(FastThreadLocalRunnable.java:30)
		at java.lang.Thread.run(Thread.java:748)
Caused by: io.lettuce.core.RedisConnectionException: Unable to connect to [************:7004]: No route to host: no further information: /************:7004
	at io.lettuce.core.cluster.topology.ClusterTopologyRefresh.lambda$getConnections$0(ClusterTopologyRefresh.java:290)
	at java.util.concurrent.CompletableFuture.uniWhenComplete(CompletableFuture.java:760)
	at java.util.concurrent.CompletableFuture$UniWhenComplete.tryFire(CompletableFuture.java:736)
	at java.util.concurrent.CompletableFuture.postComplete(CompletableFuture.java:474)
	at java.util.concurrent.CompletableFuture.completeExceptionally(CompletableFuture.java:1977)
	at io.lettuce.core.AbstractRedisClient.lambda$initializeChannelAsync0$4(AbstractRedisClient.java:329)
	at io.netty.util.concurrent.DefaultPromise.notifyListener0(DefaultPromise.java:577)
	at io.netty.util.concurrent.DefaultPromise.notifyListeners0(DefaultPromise.java:570)
	at io.netty.util.concurrent.DefaultPromise.notifyListenersNow(DefaultPromise.java:549)
	at io.netty.util.concurrent.DefaultPromise.notifyListeners(DefaultPromise.java:490)
	at io.netty.util.concurrent.DefaultPromise.setValue0(DefaultPromise.java:615)
	at io.netty.util.concurrent.DefaultPromise.setFailure0(DefaultPromise.java:608)
	at io.netty.util.concurrent.DefaultPromise.tryFailure(DefaultPromise.java:117)
	at io.netty.channel.nio.AbstractNioChannel$AbstractNioUnsafe.fulfillConnectPromise(AbstractNioChannel.java:321)
	at io.netty.channel.nio.AbstractNioChannel$AbstractNioUnsafe.finishConnect(AbstractNioChannel.java:337)
	at io.netty.channel.nio.NioEventLoop.processSelectedKey(NioEventLoop.java:688)
	at io.netty.channel.nio.NioEventLoop.processSelectedKeysOptimized(NioEventLoop.java:635)
	at io.netty.channel.nio.NioEventLoop.processSelectedKeys(NioEventLoop.java:552)
	at io.netty.channel.nio.NioEventLoop.run(NioEventLoop.java:514)
	... 4 common frames omitted
Caused by: io.netty.channel.AbstractChannel$AnnotatedNoRouteToHostException: No route to host: no further information: /************:7004
Caused by: java.net.NoRouteToHostException: No route to host: no further information
	at sun.nio.ch.SocketChannelImpl.checkConnect(Native Method)
	at sun.nio.ch.SocketChannelImpl.finishConnect(SocketChannelImpl.java:717)
	at io.netty.channel.socket.nio.NioSocketChannel.doFinishConnect(NioSocketChannel.java:330)
	at io.netty.channel.nio.AbstractNioChannel$AbstractNioUnsafe.finishConnect(AbstractNioChannel.java:334)
	at io.netty.channel.nio.NioEventLoop.processSelectedKey(NioEventLoop.java:688)
	at io.netty.channel.nio.NioEventLoop.processSelectedKeysOptimized(NioEventLoop.java:635)
	at io.netty.channel.nio.NioEventLoop.processSelectedKeys(NioEventLoop.java:552)
	at io.netty.channel.nio.NioEventLoop.run(NioEventLoop.java:514)
	at io.netty.util.concurrent.SingleThreadEventExecutor$6.run(SingleThreadEventExecutor.java:1050)
	at io.netty.util.internal.ThreadExecutorMap$2.run(ThreadExecutorMap.java:74)
	at io.netty.util.concurrent.FastThreadLocalRunnable.run(FastThreadLocalRunnable.java:30)
	at java.lang.Thread.run(Thread.java:748)
2025-05-29 00:04:18.538 [lettuce-nioEventLoop-4-11] WARN  io.lettuce.core.cluster.topology.ClusterTopologyRefresh.lambda$getConnections$0 Line:284 - Unable to connect to [************:7001]: No route to host: no further information: /************:7001
2025-05-29 00:04:18.538 [lettuce-nioEventLoop-4-12] WARN  io.lettuce.core.cluster.topology.ClusterTopologyRefresh.lambda$getConnections$0 Line:284 - Unable to connect to [************:7002]: No route to host: no further information: /************:7002
2025-05-29 00:04:18.539 [lettuce-nioEventLoop-4-2] WARN  io.lettuce.core.cluster.topology.ClusterTopologyRefresh.lambda$getConnections$0 Line:284 - Unable to connect to [************:7004]: No route to host: no further information: /************:7004
2025-05-29 00:04:18.538 [lettuce-nioEventLoop-4-1] WARN  io.lettuce.core.cluster.topology.ClusterTopologyRefresh.lambda$getConnections$0 Line:284 - Unable to connect to [************:7003]: No route to host: no further information: /************:7003
2025-05-29 00:04:18.539 [lettuce-nioEventLoop-4-3] WARN  io.lettuce.core.cluster.topology.ClusterTopologyRefresh.lambda$getConnections$0 Line:284 - Unable to connect to [************:7005]: No route to host: no further information: /************:7005
2025-05-29 00:04:18.540 [lettuce-nioEventLoop-4-4] WARN  io.lettuce.core.cluster.topology.ClusterTopologyRefresh.lambda$getConnections$0 Line:284 - Unable to connect to [************:7006]: No route to host: no further information: /************:7006
2025-05-29 00:04:18.541 [lettuce-nioEventLoop-4-5] WARN  io.lettuce.core.cluster.topology.ClusterTopologyRefresh.lambda$getConnections$0 Line:284 - Unable to connect to [************:7006]: No route to host: no further information: /************:7006
2025-05-29 00:04:18.541 [lettuce-nioEventLoop-4-7] WARN  io.lettuce.core.cluster.topology.ClusterTopologyRefresh.lambda$getConnections$0 Line:284 - Unable to connect to [************:7005]: No route to host: no further information: /************:7005
2025-05-29 00:04:18.542 [lettuce-nioEventLoop-4-6] WARN  io.lettuce.core.cluster.topology.ClusterTopologyRefresh.lambda$getConnections$0 Line:284 - Unable to connect to [************:7004]: No route to host: no further information: /************:7004
2025-05-29 00:04:18.542 [lettuce-nioEventLoop-4-8] WARN  io.lettuce.core.cluster.topology.ClusterTopologyRefresh.lambda$getConnections$0 Line:284 - Unable to connect to [************:7002]: No route to host: no further information: /************:7002
2025-05-29 00:04:18.542 [lettuce-nioEventLoop-4-9] WARN  io.lettuce.core.cluster.topology.ClusterTopologyRefresh.lambda$getConnections$0 Line:284 - Unable to connect to [************:7003]: No route to host: no further information: /************:7003
2025-05-29 00:04:18.542 [lettuce-nioEventLoop-4-10] WARN  io.lettuce.core.cluster.topology.ClusterTopologyRefresh.lambda$getConnections$0 Line:284 - Unable to connect to [************:7001]: No route to host: no further information: /************:7001
2025-05-29 00:04:18.549 [lettuce-eventExecutorLoop-1-7] WARN  io.lettuce.core.cluster.ClusterTopologyRefreshScheduler.doRun Line:258 - Cannot refresh Redis Cluster topology
io.lettuce.core.RedisException: Cannot retrieve initial cluster partitions from initial URIs [RedisURI [host='************', port=7006], RedisURI [host='************', port=7004], RedisURI [host='************', port=7005], RedisURI [host='************', port=7002], RedisURI [host='************', port=7003], RedisURI [host='************', port=7001]]
	at io.lettuce.core.cluster.RedisClusterClient.loadPartitions(RedisClusterClient.java:848)
	at io.lettuce.core.cluster.RedisClusterClient.reloadPartitions(RedisClusterClient.java:784)
	at io.lettuce.core.cluster.ClusterTopologyRefreshScheduler$ClusterTopologyRefreshTask.doRun(ClusterTopologyRefreshScheduler.java:256)
	at io.lettuce.core.cluster.ClusterTopologyRefreshScheduler$ClusterTopologyRefreshTask.run(ClusterTopologyRefreshScheduler.java:236)
	at io.netty.util.concurrent.PromiseTask.runTask(PromiseTask.java:98)
	at io.netty.util.concurrent.PromiseTask.run(PromiseTask.java:106)
	at io.netty.util.concurrent.DefaultEventExecutor.run(DefaultEventExecutor.java:66)
	at io.netty.util.concurrent.SingleThreadEventExecutor$6.run(SingleThreadEventExecutor.java:1050)
	at io.netty.util.internal.ThreadExecutorMap$2.run(ThreadExecutorMap.java:74)
	at io.netty.util.concurrent.FastThreadLocalRunnable.run(FastThreadLocalRunnable.java:30)
	at java.lang.Thread.run(Thread.java:748)
	Suppressed: io.lettuce.core.RedisConnectionException: Unable to establish a connection to Redis Cluster at [RedisURI [host='************', port=7001], RedisURI [host='************', port=7002], RedisURI [host='************', port=7003], RedisURI [host='************', port=7004], RedisURI [host='************', port=7005], RedisURI [host='************', port=7006]]
		at io.lettuce.core.cluster.topology.AsyncConnections.get(AsyncConnections.java:89)
		at io.lettuce.core.cluster.topology.ClusterTopologyRefresh.loadViews(ClusterTopologyRefresh.java:76)
		at io.lettuce.core.cluster.RedisClusterClient.doLoadPartitions(RedisClusterClient.java:865)
		at io.lettuce.core.cluster.RedisClusterClient.loadPartitions(RedisClusterClient.java:838)
		... 10 common frames omitted
		Suppressed: io.lettuce.core.RedisConnectionException: Unable to connect to [************:7002]: No route to host: no further information: /************:7002
			at io.lettuce.core.cluster.topology.ClusterTopologyRefresh.lambda$getConnections$0(ClusterTopologyRefresh.java:290)
			at java.util.concurrent.CompletableFuture.uniWhenComplete(CompletableFuture.java:760)
			at java.util.concurrent.CompletableFuture$UniWhenComplete.tryFire(CompletableFuture.java:736)
			at java.util.concurrent.CompletableFuture.postComplete(CompletableFuture.java:474)
			at java.util.concurrent.CompletableFuture.completeExceptionally(CompletableFuture.java:1977)
			at io.lettuce.core.AbstractRedisClient.lambda$initializeChannelAsync0$4(AbstractRedisClient.java:329)
			at io.netty.util.concurrent.DefaultPromise.notifyListener0(DefaultPromise.java:577)
			at io.netty.util.concurrent.DefaultPromise.notifyListeners0(DefaultPromise.java:570)
			at io.netty.util.concurrent.DefaultPromise.notifyListenersNow(DefaultPromise.java:549)
			at io.netty.util.concurrent.DefaultPromise.notifyListeners(DefaultPromise.java:490)
			at io.netty.util.concurrent.DefaultPromise.setValue0(DefaultPromise.java:615)
			at io.netty.util.concurrent.DefaultPromise.setFailure0(DefaultPromise.java:608)
			at io.netty.util.concurrent.DefaultPromise.tryFailure(DefaultPromise.java:117)
			at io.netty.channel.nio.AbstractNioChannel$AbstractNioUnsafe.fulfillConnectPromise(AbstractNioChannel.java:321)
			at io.netty.channel.nio.AbstractNioChannel$AbstractNioUnsafe.finishConnect(AbstractNioChannel.java:337)
			at io.netty.channel.nio.NioEventLoop.processSelectedKey(NioEventLoop.java:688)
			at io.netty.channel.nio.NioEventLoop.processSelectedKeysOptimized(NioEventLoop.java:635)
			at io.netty.channel.nio.NioEventLoop.processSelectedKeys(NioEventLoop.java:552)
			at io.netty.channel.nio.NioEventLoop.run(NioEventLoop.java:514)
			... 4 common frames omitted
		Caused by: io.netty.channel.AbstractChannel$AnnotatedNoRouteToHostException: No route to host: no further information: /************:7002
		Caused by: java.net.NoRouteToHostException: No route to host: no further information
			at sun.nio.ch.SocketChannelImpl.checkConnect(Native Method)
			at sun.nio.ch.SocketChannelImpl.finishConnect(SocketChannelImpl.java:717)
			at io.netty.channel.socket.nio.NioSocketChannel.doFinishConnect(NioSocketChannel.java:330)
			at io.netty.channel.nio.AbstractNioChannel$AbstractNioUnsafe.finishConnect(AbstractNioChannel.java:334)
			at io.netty.channel.nio.NioEventLoop.processSelectedKey(NioEventLoop.java:688)
			at io.netty.channel.nio.NioEventLoop.processSelectedKeysOptimized(NioEventLoop.java:635)
			at io.netty.channel.nio.NioEventLoop.processSelectedKeys(NioEventLoop.java:552)
			at io.netty.channel.nio.NioEventLoop.run(NioEventLoop.java:514)
			at io.netty.util.concurrent.SingleThreadEventExecutor$6.run(SingleThreadEventExecutor.java:1050)
			at io.netty.util.internal.ThreadExecutorMap$2.run(ThreadExecutorMap.java:74)
			at io.netty.util.concurrent.FastThreadLocalRunnable.run(FastThreadLocalRunnable.java:30)
			at java.lang.Thread.run(Thread.java:748)
		Suppressed: io.lettuce.core.RedisConnectionException: Unable to connect to [************:7004]: No route to host: no further information: /************:7004
			at io.lettuce.core.cluster.topology.ClusterTopologyRefresh.lambda$getConnections$0(ClusterTopologyRefresh.java:290)
			at java.util.concurrent.CompletableFuture.uniWhenComplete(CompletableFuture.java:760)
			at java.util.concurrent.CompletableFuture$UniWhenComplete.tryFire(CompletableFuture.java:736)
			at java.util.concurrent.CompletableFuture.postComplete(CompletableFuture.java:474)
			at java.util.concurrent.CompletableFuture.completeExceptionally(CompletableFuture.java:1977)
			at io.lettuce.core.AbstractRedisClient.lambda$initializeChannelAsync0$4(AbstractRedisClient.java:329)
			at io.netty.util.concurrent.DefaultPromise.notifyListener0(DefaultPromise.java:577)
			at io.netty.util.concurrent.DefaultPromise.notifyListeners0(DefaultPromise.java:570)
			at io.netty.util.concurrent.DefaultPromise.notifyListenersNow(DefaultPromise.java:549)
			at io.netty.util.concurrent.DefaultPromise.notifyListeners(DefaultPromise.java:490)
			at io.netty.util.concurrent.DefaultPromise.setValue0(DefaultPromise.java:615)
			at io.netty.util.concurrent.DefaultPromise.setFailure0(DefaultPromise.java:608)
			at io.netty.util.concurrent.DefaultPromise.tryFailure(DefaultPromise.java:117)
			at io.netty.channel.nio.AbstractNioChannel$AbstractNioUnsafe.fulfillConnectPromise(AbstractNioChannel.java:321)
			at io.netty.channel.nio.AbstractNioChannel$AbstractNioUnsafe.finishConnect(AbstractNioChannel.java:337)
			at io.netty.channel.nio.NioEventLoop.processSelectedKey(NioEventLoop.java:688)
			at io.netty.channel.nio.NioEventLoop.processSelectedKeysOptimized(NioEventLoop.java:635)
			at io.netty.channel.nio.NioEventLoop.processSelectedKeys(NioEventLoop.java:552)
			at io.netty.channel.nio.NioEventLoop.run(NioEventLoop.java:514)
			... 4 common frames omitted
		Caused by: io.netty.channel.AbstractChannel$AnnotatedNoRouteToHostException: No route to host: no further information: /************:7004
		Caused by: java.net.NoRouteToHostException: No route to host: no further information
			at sun.nio.ch.SocketChannelImpl.checkConnect(Native Method)
			at sun.nio.ch.SocketChannelImpl.finishConnect(SocketChannelImpl.java:717)
			at io.netty.channel.socket.nio.NioSocketChannel.doFinishConnect(NioSocketChannel.java:330)
			at io.netty.channel.nio.AbstractNioChannel$AbstractNioUnsafe.finishConnect(AbstractNioChannel.java:334)
			at io.netty.channel.nio.NioEventLoop.processSelectedKey(NioEventLoop.java:688)
			at io.netty.channel.nio.NioEventLoop.processSelectedKeysOptimized(NioEventLoop.java:635)
			at io.netty.channel.nio.NioEventLoop.processSelectedKeys(NioEventLoop.java:552)
			at io.netty.channel.nio.NioEventLoop.run(NioEventLoop.java:514)
			at io.netty.util.concurrent.SingleThreadEventExecutor$6.run(SingleThreadEventExecutor.java:1050)
			at io.netty.util.internal.ThreadExecutorMap$2.run(ThreadExecutorMap.java:74)
			at io.netty.util.concurrent.FastThreadLocalRunnable.run(FastThreadLocalRunnable.java:30)
			at java.lang.Thread.run(Thread.java:748)
		Suppressed: io.lettuce.core.RedisConnectionException: Unable to connect to [************:7003]: No route to host: no further information: /************:7003
			at io.lettuce.core.cluster.topology.ClusterTopologyRefresh.lambda$getConnections$0(ClusterTopologyRefresh.java:290)
			at java.util.concurrent.CompletableFuture.uniWhenComplete(CompletableFuture.java:760)
			at java.util.concurrent.CompletableFuture$UniWhenComplete.tryFire(CompletableFuture.java:736)
			at java.util.concurrent.CompletableFuture.postComplete(CompletableFuture.java:474)
			at java.util.concurrent.CompletableFuture.completeExceptionally(CompletableFuture.java:1977)
			at io.lettuce.core.AbstractRedisClient.lambda$initializeChannelAsync0$4(AbstractRedisClient.java:329)
			at io.netty.util.concurrent.DefaultPromise.notifyListener0(DefaultPromise.java:577)
			at io.netty.util.concurrent.DefaultPromise.notifyListeners0(DefaultPromise.java:570)
			at io.netty.util.concurrent.DefaultPromise.notifyListenersNow(DefaultPromise.java:549)
			at io.netty.util.concurrent.DefaultPromise.notifyListeners(DefaultPromise.java:490)
			at io.netty.util.concurrent.DefaultPromise.setValue0(DefaultPromise.java:615)
			at io.netty.util.concurrent.DefaultPromise.setFailure0(DefaultPromise.java:608)
			at io.netty.util.concurrent.DefaultPromise.tryFailure(DefaultPromise.java:117)
			at io.netty.channel.nio.AbstractNioChannel$AbstractNioUnsafe.fulfillConnectPromise(AbstractNioChannel.java:321)
			at io.netty.channel.nio.AbstractNioChannel$AbstractNioUnsafe.finishConnect(AbstractNioChannel.java:337)
			at io.netty.channel.nio.NioEventLoop.processSelectedKey(NioEventLoop.java:688)
			at io.netty.channel.nio.NioEventLoop.processSelectedKeysOptimized(NioEventLoop.java:635)
			at io.netty.channel.nio.NioEventLoop.processSelectedKeys(NioEventLoop.java:552)
			at io.netty.channel.nio.NioEventLoop.run(NioEventLoop.java:514)
			... 4 common frames omitted
		Caused by: io.netty.channel.AbstractChannel$AnnotatedNoRouteToHostException: No route to host: no further information: /************:7003
		Caused by: java.net.NoRouteToHostException: No route to host: no further information
			at sun.nio.ch.SocketChannelImpl.checkConnect(Native Method)
			at sun.nio.ch.SocketChannelImpl.finishConnect(SocketChannelImpl.java:717)
			at io.netty.channel.socket.nio.NioSocketChannel.doFinishConnect(NioSocketChannel.java:330)
			at io.netty.channel.nio.AbstractNioChannel$AbstractNioUnsafe.finishConnect(AbstractNioChannel.java:334)
			at io.netty.channel.nio.NioEventLoop.processSelectedKey(NioEventLoop.java:688)
			at io.netty.channel.nio.NioEventLoop.processSelectedKeysOptimized(NioEventLoop.java:635)
			at io.netty.channel.nio.NioEventLoop.processSelectedKeys(NioEventLoop.java:552)
			at io.netty.channel.nio.NioEventLoop.run(NioEventLoop.java:514)
			at io.netty.util.concurrent.SingleThreadEventExecutor$6.run(SingleThreadEventExecutor.java:1050)
			at io.netty.util.internal.ThreadExecutorMap$2.run(ThreadExecutorMap.java:74)
			at io.netty.util.concurrent.FastThreadLocalRunnable.run(FastThreadLocalRunnable.java:30)
			at java.lang.Thread.run(Thread.java:748)
		Suppressed: io.lettuce.core.RedisConnectionException: Unable to connect to [************:7005]: No route to host: no further information: /************:7005
			at io.lettuce.core.cluster.topology.ClusterTopologyRefresh.lambda$getConnections$0(ClusterTopologyRefresh.java:290)
			at java.util.concurrent.CompletableFuture.uniWhenComplete(CompletableFuture.java:760)
			at java.util.concurrent.CompletableFuture$UniWhenComplete.tryFire(CompletableFuture.java:736)
			at java.util.concurrent.CompletableFuture.postComplete(CompletableFuture.java:474)
			at java.util.concurrent.CompletableFuture.completeExceptionally(CompletableFuture.java:1977)
			at io.lettuce.core.AbstractRedisClient.lambda$initializeChannelAsync0$4(AbstractRedisClient.java:329)
			at io.netty.util.concurrent.DefaultPromise.notifyListener0(DefaultPromise.java:577)
			at io.netty.util.concurrent.DefaultPromise.notifyListeners0(DefaultPromise.java:570)
			at io.netty.util.concurrent.DefaultPromise.notifyListenersNow(DefaultPromise.java:549)
			at io.netty.util.concurrent.DefaultPromise.notifyListeners(DefaultPromise.java:490)
			at io.netty.util.concurrent.DefaultPromise.setValue0(DefaultPromise.java:615)
			at io.netty.util.concurrent.DefaultPromise.setFailure0(DefaultPromise.java:608)
			at io.netty.util.concurrent.DefaultPromise.tryFailure(DefaultPromise.java:117)
			at io.netty.channel.nio.AbstractNioChannel$AbstractNioUnsafe.fulfillConnectPromise(AbstractNioChannel.java:321)
			at io.netty.channel.nio.AbstractNioChannel$AbstractNioUnsafe.finishConnect(AbstractNioChannel.java:337)
			at io.netty.channel.nio.NioEventLoop.processSelectedKey(NioEventLoop.java:688)
			at io.netty.channel.nio.NioEventLoop.processSelectedKeysOptimized(NioEventLoop.java:635)
			at io.netty.channel.nio.NioEventLoop.processSelectedKeys(NioEventLoop.java:552)
			at io.netty.channel.nio.NioEventLoop.run(NioEventLoop.java:514)
			... 4 common frames omitted
		Caused by: io.netty.channel.AbstractChannel$AnnotatedNoRouteToHostException: No route to host: no further information: /************:7005
		Caused by: java.net.NoRouteToHostException: No route to host: no further information
			at sun.nio.ch.SocketChannelImpl.checkConnect(Native Method)
			at sun.nio.ch.SocketChannelImpl.finishConnect(SocketChannelImpl.java:717)
			at io.netty.channel.socket.nio.NioSocketChannel.doFinishConnect(NioSocketChannel.java:330)
			at io.netty.channel.nio.AbstractNioChannel$AbstractNioUnsafe.finishConnect(AbstractNioChannel.java:334)
			at io.netty.channel.nio.NioEventLoop.processSelectedKey(NioEventLoop.java:688)
			at io.netty.channel.nio.NioEventLoop.processSelectedKeysOptimized(NioEventLoop.java:635)
			at io.netty.channel.nio.NioEventLoop.processSelectedKeys(NioEventLoop.java:552)
			at io.netty.channel.nio.NioEventLoop.run(NioEventLoop.java:514)
			at io.netty.util.concurrent.SingleThreadEventExecutor$6.run(SingleThreadEventExecutor.java:1050)
			at io.netty.util.internal.ThreadExecutorMap$2.run(ThreadExecutorMap.java:74)
			at io.netty.util.concurrent.FastThreadLocalRunnable.run(FastThreadLocalRunnable.java:30)
			at java.lang.Thread.run(Thread.java:748)
		Suppressed: io.lettuce.core.RedisConnectionException: Unable to connect to [************:7006]: No route to host: no further information: /************:7006
			at io.lettuce.core.cluster.topology.ClusterTopologyRefresh.lambda$getConnections$0(ClusterTopologyRefresh.java:290)
			at java.util.concurrent.CompletableFuture.uniWhenComplete(CompletableFuture.java:760)
			at java.util.concurrent.CompletableFuture$UniWhenComplete.tryFire(CompletableFuture.java:736)
			at java.util.concurrent.CompletableFuture.postComplete(CompletableFuture.java:474)
			at java.util.concurrent.CompletableFuture.completeExceptionally(CompletableFuture.java:1977)
			at io.lettuce.core.AbstractRedisClient.lambda$initializeChannelAsync0$4(AbstractRedisClient.java:329)
			at io.netty.util.concurrent.DefaultPromise.notifyListener0(DefaultPromise.java:577)
			at io.netty.util.concurrent.DefaultPromise.notifyListeners0(DefaultPromise.java:570)
			at io.netty.util.concurrent.DefaultPromise.notifyListenersNow(DefaultPromise.java:549)
			at io.netty.util.concurrent.DefaultPromise.notifyListeners(DefaultPromise.java:490)
			at io.netty.util.concurrent.DefaultPromise.setValue0(DefaultPromise.java:615)
			at io.netty.util.concurrent.DefaultPromise.setFailure0(DefaultPromise.java:608)
			at io.netty.util.concurrent.DefaultPromise.tryFailure(DefaultPromise.java:117)
			at io.netty.channel.nio.AbstractNioChannel$AbstractNioUnsafe.fulfillConnectPromise(AbstractNioChannel.java:321)
			at io.netty.channel.nio.AbstractNioChannel$AbstractNioUnsafe.finishConnect(AbstractNioChannel.java:337)
			at io.netty.channel.nio.NioEventLoop.processSelectedKey(NioEventLoop.java:688)
			at io.netty.channel.nio.NioEventLoop.processSelectedKeysOptimized(NioEventLoop.java:635)
			at io.netty.channel.nio.NioEventLoop.processSelectedKeys(NioEventLoop.java:552)
			at io.netty.channel.nio.NioEventLoop.run(NioEventLoop.java:514)
			... 4 common frames omitted
		Caused by: io.netty.channel.AbstractChannel$AnnotatedNoRouteToHostException: No route to host: no further information: /************:7006
		Caused by: java.net.NoRouteToHostException: No route to host: no further information
			at sun.nio.ch.SocketChannelImpl.checkConnect(Native Method)
			at sun.nio.ch.SocketChannelImpl.finishConnect(SocketChannelImpl.java:717)
			at io.netty.channel.socket.nio.NioSocketChannel.doFinishConnect(NioSocketChannel.java:330)
			at io.netty.channel.nio.AbstractNioChannel$AbstractNioUnsafe.finishConnect(AbstractNioChannel.java:334)
			at io.netty.channel.nio.NioEventLoop.processSelectedKey(NioEventLoop.java:688)
			at io.netty.channel.nio.NioEventLoop.processSelectedKeysOptimized(NioEventLoop.java:635)
			at io.netty.channel.nio.NioEventLoop.processSelectedKeys(NioEventLoop.java:552)
			at io.netty.channel.nio.NioEventLoop.run(NioEventLoop.java:514)
			at io.netty.util.concurrent.SingleThreadEventExecutor$6.run(SingleThreadEventExecutor.java:1050)
			at io.netty.util.internal.ThreadExecutorMap$2.run(ThreadExecutorMap.java:74)
			at io.netty.util.concurrent.FastThreadLocalRunnable.run(FastThreadLocalRunnable.java:30)
			at java.lang.Thread.run(Thread.java:748)
	Caused by: io.lettuce.core.RedisConnectionException: Unable to connect to [************:7001]: No route to host: no further information: /************:7001
		at io.lettuce.core.cluster.topology.ClusterTopologyRefresh.lambda$getConnections$0(ClusterTopologyRefresh.java:290)
		at java.util.concurrent.CompletableFuture.uniWhenComplete(CompletableFuture.java:760)
		at java.util.concurrent.CompletableFuture$UniWhenComplete.tryFire(CompletableFuture.java:736)
		at java.util.concurrent.CompletableFuture.postComplete(CompletableFuture.java:474)
		at java.util.concurrent.CompletableFuture.completeExceptionally(CompletableFuture.java:1977)
		at io.lettuce.core.AbstractRedisClient.lambda$initializeChannelAsync0$4(AbstractRedisClient.java:329)
		at io.netty.util.concurrent.DefaultPromise.notifyListener0(DefaultPromise.java:577)
		at io.netty.util.concurrent.DefaultPromise.notifyListeners0(DefaultPromise.java:570)
		at io.netty.util.concurrent.DefaultPromise.notifyListenersNow(DefaultPromise.java:549)
		at io.netty.util.concurrent.DefaultPromise.notifyListeners(DefaultPromise.java:490)
		at io.netty.util.concurrent.DefaultPromise.setValue0(DefaultPromise.java:615)
		at io.netty.util.concurrent.DefaultPromise.setFailure0(DefaultPromise.java:608)
		at io.netty.util.concurrent.DefaultPromise.tryFailure(DefaultPromise.java:117)
		at io.netty.channel.nio.AbstractNioChannel$AbstractNioUnsafe.fulfillConnectPromise(AbstractNioChannel.java:321)
		at io.netty.channel.nio.AbstractNioChannel$AbstractNioUnsafe.finishConnect(AbstractNioChannel.java:337)
		at io.netty.channel.nio.NioEventLoop.processSelectedKey(NioEventLoop.java:688)
		at io.netty.channel.nio.NioEventLoop.processSelectedKeysOptimized(NioEventLoop.java:635)
		at io.netty.channel.nio.NioEventLoop.processSelectedKeys(NioEventLoop.java:552)
		at io.netty.channel.nio.NioEventLoop.run(NioEventLoop.java:514)
		... 4 common frames omitted
	Caused by: io.netty.channel.AbstractChannel$AnnotatedNoRouteToHostException: No route to host: no further information: /************:7001
	Caused by: java.net.NoRouteToHostException: No route to host: no further information
		at sun.nio.ch.SocketChannelImpl.checkConnect(Native Method)
		at sun.nio.ch.SocketChannelImpl.finishConnect(SocketChannelImpl.java:717)
		at io.netty.channel.socket.nio.NioSocketChannel.doFinishConnect(NioSocketChannel.java:330)
		at io.netty.channel.nio.AbstractNioChannel$AbstractNioUnsafe.finishConnect(AbstractNioChannel.java:334)
		at io.netty.channel.nio.NioEventLoop.processSelectedKey(NioEventLoop.java:688)
		at io.netty.channel.nio.NioEventLoop.processSelectedKeysOptimized(NioEventLoop.java:635)
		at io.netty.channel.nio.NioEventLoop.processSelectedKeys(NioEventLoop.java:552)
		at io.netty.channel.nio.NioEventLoop.run(NioEventLoop.java:514)
		at io.netty.util.concurrent.SingleThreadEventExecutor$6.run(SingleThreadEventExecutor.java:1050)
		at io.netty.util.internal.ThreadExecutorMap$2.run(ThreadExecutorMap.java:74)
		at io.netty.util.concurrent.FastThreadLocalRunnable.run(FastThreadLocalRunnable.java:30)
		at java.lang.Thread.run(Thread.java:748)
Caused by: io.lettuce.core.RedisConnectionException: Unable to establish a connection to Redis Cluster at [RedisURI [host='************', port=7001], RedisURI [host='************', port=7002], RedisURI [host='************', port=7003], RedisURI [host='************', port=7004], RedisURI [host='************', port=7005], RedisURI [host='************', port=7006]]
	at io.lettuce.core.cluster.topology.AsyncConnections.get(AsyncConnections.java:89)
	at io.lettuce.core.cluster.topology.ClusterTopologyRefresh.loadViews(ClusterTopologyRefresh.java:76)
	at io.lettuce.core.cluster.RedisClusterClient.doLoadPartitions(RedisClusterClient.java:865)
	at io.lettuce.core.cluster.RedisClusterClient.loadPartitions(RedisClusterClient.java:845)
	... 10 common frames omitted
	Suppressed: io.lettuce.core.RedisConnectionException: Unable to connect to [************:7005]: No route to host: no further information: /************:7005
		at io.lettuce.core.cluster.topology.ClusterTopologyRefresh.lambda$getConnections$0(ClusterTopologyRefresh.java:290)
		at java.util.concurrent.CompletableFuture.uniWhenComplete(CompletableFuture.java:760)
		at java.util.concurrent.CompletableFuture$UniWhenComplete.tryFire(CompletableFuture.java:736)
		at java.util.concurrent.CompletableFuture.postComplete(CompletableFuture.java:474)
		at java.util.concurrent.CompletableFuture.completeExceptionally(CompletableFuture.java:1977)
		at io.lettuce.core.AbstractRedisClient.lambda$initializeChannelAsync0$4(AbstractRedisClient.java:329)
		at io.netty.util.concurrent.DefaultPromise.notifyListener0(DefaultPromise.java:577)
		at io.netty.util.concurrent.DefaultPromise.notifyListeners0(DefaultPromise.java:570)
		at io.netty.util.concurrent.DefaultPromise.notifyListenersNow(DefaultPromise.java:549)
		at io.netty.util.concurrent.DefaultPromise.notifyListeners(DefaultPromise.java:490)
		at io.netty.util.concurrent.DefaultPromise.setValue0(DefaultPromise.java:615)
		at io.netty.util.concurrent.DefaultPromise.setFailure0(DefaultPromise.java:608)
		at io.netty.util.concurrent.DefaultPromise.tryFailure(DefaultPromise.java:117)
		at io.netty.channel.nio.AbstractNioChannel$AbstractNioUnsafe.fulfillConnectPromise(AbstractNioChannel.java:321)
		at io.netty.channel.nio.AbstractNioChannel$AbstractNioUnsafe.finishConnect(AbstractNioChannel.java:337)
		at io.netty.channel.nio.NioEventLoop.processSelectedKey(NioEventLoop.java:688)
		at io.netty.channel.nio.NioEventLoop.processSelectedKeysOptimized(NioEventLoop.java:635)
		at io.netty.channel.nio.NioEventLoop.processSelectedKeys(NioEventLoop.java:552)
		at io.netty.channel.nio.NioEventLoop.run(NioEventLoop.java:514)
		... 4 common frames omitted
	Caused by: io.netty.channel.AbstractChannel$AnnotatedNoRouteToHostException: No route to host: no further information: /************:7005
	Caused by: java.net.NoRouteToHostException: No route to host: no further information
		at sun.nio.ch.SocketChannelImpl.checkConnect(Native Method)
		at sun.nio.ch.SocketChannelImpl.finishConnect(SocketChannelImpl.java:717)
		at io.netty.channel.socket.nio.NioSocketChannel.doFinishConnect(NioSocketChannel.java:330)
		at io.netty.channel.nio.AbstractNioChannel$AbstractNioUnsafe.finishConnect(AbstractNioChannel.java:334)
		at io.netty.channel.nio.NioEventLoop.processSelectedKey(NioEventLoop.java:688)
		at io.netty.channel.nio.NioEventLoop.processSelectedKeysOptimized(NioEventLoop.java:635)
		at io.netty.channel.nio.NioEventLoop.processSelectedKeys(NioEventLoop.java:552)
		at io.netty.channel.nio.NioEventLoop.run(NioEventLoop.java:514)
		at io.netty.util.concurrent.SingleThreadEventExecutor$6.run(SingleThreadEventExecutor.java:1050)
		at io.netty.util.internal.ThreadExecutorMap$2.run(ThreadExecutorMap.java:74)
		at io.netty.util.concurrent.FastThreadLocalRunnable.run(FastThreadLocalRunnable.java:30)
		at java.lang.Thread.run(Thread.java:748)
	Suppressed: io.lettuce.core.RedisConnectionException: Unable to connect to [************:7004]: No route to host: no further information: /************:7004
		at io.lettuce.core.cluster.topology.ClusterTopologyRefresh.lambda$getConnections$0(ClusterTopologyRefresh.java:290)
		at java.util.concurrent.CompletableFuture.uniWhenComplete(CompletableFuture.java:760)
		at java.util.concurrent.CompletableFuture$UniWhenComplete.tryFire(CompletableFuture.java:736)
		at java.util.concurrent.CompletableFuture.postComplete(CompletableFuture.java:474)
		at java.util.concurrent.CompletableFuture.completeExceptionally(CompletableFuture.java:1977)
		at io.lettuce.core.AbstractRedisClient.lambda$initializeChannelAsync0$4(AbstractRedisClient.java:329)
		at io.netty.util.concurrent.DefaultPromise.notifyListener0(DefaultPromise.java:577)
		at io.netty.util.concurrent.DefaultPromise.notifyListeners0(DefaultPromise.java:570)
		at io.netty.util.concurrent.DefaultPromise.notifyListenersNow(DefaultPromise.java:549)
		at io.netty.util.concurrent.DefaultPromise.notifyListeners(DefaultPromise.java:490)
		at io.netty.util.concurrent.DefaultPromise.setValue0(DefaultPromise.java:615)
		at io.netty.util.concurrent.DefaultPromise.setFailure0(DefaultPromise.java:608)
		at io.netty.util.concurrent.DefaultPromise.tryFailure(DefaultPromise.java:117)
		at io.netty.channel.nio.AbstractNioChannel$AbstractNioUnsafe.fulfillConnectPromise(AbstractNioChannel.java:321)
		at io.netty.channel.nio.AbstractNioChannel$AbstractNioUnsafe.finishConnect(AbstractNioChannel.java:337)
		at io.netty.channel.nio.NioEventLoop.processSelectedKey(NioEventLoop.java:688)
		at io.netty.channel.nio.NioEventLoop.processSelectedKeysOptimized(NioEventLoop.java:635)
		at io.netty.channel.nio.NioEventLoop.processSelectedKeys(NioEventLoop.java:552)
		at io.netty.channel.nio.NioEventLoop.run(NioEventLoop.java:514)
		... 4 common frames omitted
	Caused by: io.netty.channel.AbstractChannel$AnnotatedNoRouteToHostException: No route to host: no further information: /************:7004
	Caused by: java.net.NoRouteToHostException: No route to host: no further information
		at sun.nio.ch.SocketChannelImpl.checkConnect(Native Method)
		at sun.nio.ch.SocketChannelImpl.finishConnect(SocketChannelImpl.java:717)
		at io.netty.channel.socket.nio.NioSocketChannel.doFinishConnect(NioSocketChannel.java:330)
		at io.netty.channel.nio.AbstractNioChannel$AbstractNioUnsafe.finishConnect(AbstractNioChannel.java:334)
		at io.netty.channel.nio.NioEventLoop.processSelectedKey(NioEventLoop.java:688)
		at io.netty.channel.nio.NioEventLoop.processSelectedKeysOptimized(NioEventLoop.java:635)
		at io.netty.channel.nio.NioEventLoop.processSelectedKeys(NioEventLoop.java:552)
		at io.netty.channel.nio.NioEventLoop.run(NioEventLoop.java:514)
		at io.netty.util.concurrent.SingleThreadEventExecutor$6.run(SingleThreadEventExecutor.java:1050)
		at io.netty.util.internal.ThreadExecutorMap$2.run(ThreadExecutorMap.java:74)
		at io.netty.util.concurrent.FastThreadLocalRunnable.run(FastThreadLocalRunnable.java:30)
		at java.lang.Thread.run(Thread.java:748)
	Suppressed: io.lettuce.core.RedisConnectionException: Unable to connect to [************:7002]: No route to host: no further information: /************:7002
		at io.lettuce.core.cluster.topology.ClusterTopologyRefresh.lambda$getConnections$0(ClusterTopologyRefresh.java:290)
		at java.util.concurrent.CompletableFuture.uniWhenComplete(CompletableFuture.java:760)
		at java.util.concurrent.CompletableFuture$UniWhenComplete.tryFire(CompletableFuture.java:736)
		at java.util.concurrent.CompletableFuture.postComplete(CompletableFuture.java:474)
		at java.util.concurrent.CompletableFuture.completeExceptionally(CompletableFuture.java:1977)
		at io.lettuce.core.AbstractRedisClient.lambda$initializeChannelAsync0$4(AbstractRedisClient.java:329)
		at io.netty.util.concurrent.DefaultPromise.notifyListener0(DefaultPromise.java:577)
		at io.netty.util.concurrent.DefaultPromise.notifyListeners0(DefaultPromise.java:570)
		at io.netty.util.concurrent.DefaultPromise.notifyListenersNow(DefaultPromise.java:549)
		at io.netty.util.concurrent.DefaultPromise.notifyListeners(DefaultPromise.java:490)
		at io.netty.util.concurrent.DefaultPromise.setValue0(DefaultPromise.java:615)
		at io.netty.util.concurrent.DefaultPromise.setFailure0(DefaultPromise.java:608)
		at io.netty.util.concurrent.DefaultPromise.tryFailure(DefaultPromise.java:117)
		at io.netty.channel.nio.AbstractNioChannel$AbstractNioUnsafe.fulfillConnectPromise(AbstractNioChannel.java:321)
		at io.netty.channel.nio.AbstractNioChannel$AbstractNioUnsafe.finishConnect(AbstractNioChannel.java:337)
		at io.netty.channel.nio.NioEventLoop.processSelectedKey(NioEventLoop.java:688)
		at io.netty.channel.nio.NioEventLoop.processSelectedKeysOptimized(NioEventLoop.java:635)
		at io.netty.channel.nio.NioEventLoop.processSelectedKeys(NioEventLoop.java:552)
		at io.netty.channel.nio.NioEventLoop.run(NioEventLoop.java:514)
		... 4 common frames omitted
	Caused by: io.netty.channel.AbstractChannel$AnnotatedNoRouteToHostException: No route to host: no further information: /************:7002
	Caused by: java.net.NoRouteToHostException: No route to host: no further information
		at sun.nio.ch.SocketChannelImpl.checkConnect(Native Method)
		at sun.nio.ch.SocketChannelImpl.finishConnect(SocketChannelImpl.java:717)
		at io.netty.channel.socket.nio.NioSocketChannel.doFinishConnect(NioSocketChannel.java:330)
		at io.netty.channel.nio.AbstractNioChannel$AbstractNioUnsafe.finishConnect(AbstractNioChannel.java:334)
		at io.netty.channel.nio.NioEventLoop.processSelectedKey(NioEventLoop.java:688)
		at io.netty.channel.nio.NioEventLoop.processSelectedKeysOptimized(NioEventLoop.java:635)
		at io.netty.channel.nio.NioEventLoop.processSelectedKeys(NioEventLoop.java:552)
		at io.netty.channel.nio.NioEventLoop.run(NioEventLoop.java:514)
		at io.netty.util.concurrent.SingleThreadEventExecutor$6.run(SingleThreadEventExecutor.java:1050)
		at io.netty.util.internal.ThreadExecutorMap$2.run(ThreadExecutorMap.java:74)
		at io.netty.util.concurrent.FastThreadLocalRunnable.run(FastThreadLocalRunnable.java:30)
		at java.lang.Thread.run(Thread.java:748)
	Suppressed: io.lettuce.core.RedisConnectionException: Unable to connect to [************:7003]: No route to host: no further information: /************:7003
		at io.lettuce.core.cluster.topology.ClusterTopologyRefresh.lambda$getConnections$0(ClusterTopologyRefresh.java:290)
		at java.util.concurrent.CompletableFuture.uniWhenComplete(CompletableFuture.java:760)
		at java.util.concurrent.CompletableFuture$UniWhenComplete.tryFire(CompletableFuture.java:736)
		at java.util.concurrent.CompletableFuture.postComplete(CompletableFuture.java:474)
		at java.util.concurrent.CompletableFuture.completeExceptionally(CompletableFuture.java:1977)
		at io.lettuce.core.AbstractRedisClient.lambda$initializeChannelAsync0$4(AbstractRedisClient.java:329)
		at io.netty.util.concurrent.DefaultPromise.notifyListener0(DefaultPromise.java:577)
		at io.netty.util.concurrent.DefaultPromise.notifyListeners0(DefaultPromise.java:570)
		at io.netty.util.concurrent.DefaultPromise.notifyListenersNow(DefaultPromise.java:549)
		at io.netty.util.concurrent.DefaultPromise.notifyListeners(DefaultPromise.java:490)
		at io.netty.util.concurrent.DefaultPromise.setValue0(DefaultPromise.java:615)
		at io.netty.util.concurrent.DefaultPromise.setFailure0(DefaultPromise.java:608)
		at io.netty.util.concurrent.DefaultPromise.tryFailure(DefaultPromise.java:117)
		at io.netty.channel.nio.AbstractNioChannel$AbstractNioUnsafe.fulfillConnectPromise(AbstractNioChannel.java:321)
		at io.netty.channel.nio.AbstractNioChannel$AbstractNioUnsafe.finishConnect(AbstractNioChannel.java:337)
		at io.netty.channel.nio.NioEventLoop.processSelectedKey(NioEventLoop.java:688)
		at io.netty.channel.nio.NioEventLoop.processSelectedKeysOptimized(NioEventLoop.java:635)
		at io.netty.channel.nio.NioEventLoop.processSelectedKeys(NioEventLoop.java:552)
		at io.netty.channel.nio.NioEventLoop.run(NioEventLoop.java:514)
		... 4 common frames omitted
	Caused by: io.netty.channel.AbstractChannel$AnnotatedNoRouteToHostException: No route to host: no further information: /************:7003
	Caused by: java.net.NoRouteToHostException: No route to host: no further information
		at sun.nio.ch.SocketChannelImpl.checkConnect(Native Method)
		at sun.nio.ch.SocketChannelImpl.finishConnect(SocketChannelImpl.java:717)
		at io.netty.channel.socket.nio.NioSocketChannel.doFinishConnect(NioSocketChannel.java:330)
		at io.netty.channel.nio.AbstractNioChannel$AbstractNioUnsafe.finishConnect(AbstractNioChannel.java:334)
		at io.netty.channel.nio.NioEventLoop.processSelectedKey(NioEventLoop.java:688)
		at io.netty.channel.nio.NioEventLoop.processSelectedKeysOptimized(NioEventLoop.java:635)
		at io.netty.channel.nio.NioEventLoop.processSelectedKeys(NioEventLoop.java:552)
		at io.netty.channel.nio.NioEventLoop.run(NioEventLoop.java:514)
		at io.netty.util.concurrent.SingleThreadEventExecutor$6.run(SingleThreadEventExecutor.java:1050)
		at io.netty.util.internal.ThreadExecutorMap$2.run(ThreadExecutorMap.java:74)
		at io.netty.util.concurrent.FastThreadLocalRunnable.run(FastThreadLocalRunnable.java:30)
		at java.lang.Thread.run(Thread.java:748)
	Suppressed: io.lettuce.core.RedisConnectionException: Unable to connect to [************:7001]: No route to host: no further information: /************:7001
		at io.lettuce.core.cluster.topology.ClusterTopologyRefresh.lambda$getConnections$0(ClusterTopologyRefresh.java:290)
		at java.util.concurrent.CompletableFuture.uniWhenComplete(CompletableFuture.java:760)
		at java.util.concurrent.CompletableFuture$UniWhenComplete.tryFire(CompletableFuture.java:736)
		at java.util.concurrent.CompletableFuture.postComplete(CompletableFuture.java:474)
		at java.util.concurrent.CompletableFuture.completeExceptionally(CompletableFuture.java:1977)
		at io.lettuce.core.AbstractRedisClient.lambda$initializeChannelAsync0$4(AbstractRedisClient.java:329)
		at io.netty.util.concurrent.DefaultPromise.notifyListener0(DefaultPromise.java:577)
		at io.netty.util.concurrent.DefaultPromise.notifyListeners0(DefaultPromise.java:570)
		at io.netty.util.concurrent.DefaultPromise.notifyListenersNow(DefaultPromise.java:549)
		at io.netty.util.concurrent.DefaultPromise.notifyListeners(DefaultPromise.java:490)
		at io.netty.util.concurrent.DefaultPromise.setValue0(DefaultPromise.java:615)
		at io.netty.util.concurrent.DefaultPromise.setFailure0(DefaultPromise.java:608)
		at io.netty.util.concurrent.DefaultPromise.tryFailure(DefaultPromise.java:117)
		at io.netty.channel.nio.AbstractNioChannel$AbstractNioUnsafe.fulfillConnectPromise(AbstractNioChannel.java:321)
		at io.netty.channel.nio.AbstractNioChannel$AbstractNioUnsafe.finishConnect(AbstractNioChannel.java:337)
		at io.netty.channel.nio.NioEventLoop.processSelectedKey(NioEventLoop.java:688)
		at io.netty.channel.nio.NioEventLoop.processSelectedKeysOptimized(NioEventLoop.java:635)
		at io.netty.channel.nio.NioEventLoop.processSelectedKeys(NioEventLoop.java:552)
		at io.netty.channel.nio.NioEventLoop.run(NioEventLoop.java:514)
		... 4 common frames omitted
	Caused by: io.netty.channel.AbstractChannel$AnnotatedNoRouteToHostException: No route to host: no further information: /************:7001
	Caused by: java.net.NoRouteToHostException: No route to host: no further information
		at sun.nio.ch.SocketChannelImpl.checkConnect(Native Method)
		at sun.nio.ch.SocketChannelImpl.finishConnect(SocketChannelImpl.java:717)
		at io.netty.channel.socket.nio.NioSocketChannel.doFinishConnect(NioSocketChannel.java:330)
		at io.netty.channel.nio.AbstractNioChannel$AbstractNioUnsafe.finishConnect(AbstractNioChannel.java:334)
		at io.netty.channel.nio.NioEventLoop.processSelectedKey(NioEventLoop.java:688)
		at io.netty.channel.nio.NioEventLoop.processSelectedKeysOptimized(NioEventLoop.java:635)
		at io.netty.channel.nio.NioEventLoop.processSelectedKeys(NioEventLoop.java:552)
		at io.netty.channel.nio.NioEventLoop.run(NioEventLoop.java:514)
		at io.netty.util.concurrent.SingleThreadEventExecutor$6.run(SingleThreadEventExecutor.java:1050)
		at io.netty.util.internal.ThreadExecutorMap$2.run(ThreadExecutorMap.java:74)
		at io.netty.util.concurrent.FastThreadLocalRunnable.run(FastThreadLocalRunnable.java:30)
		at java.lang.Thread.run(Thread.java:748)
Caused by: io.lettuce.core.RedisConnectionException: Unable to connect to [************:7006]: No route to host: no further information: /************:7006
	at io.lettuce.core.cluster.topology.ClusterTopologyRefresh.lambda$getConnections$0(ClusterTopologyRefresh.java:290)
	at java.util.concurrent.CompletableFuture.uniWhenComplete(CompletableFuture.java:760)
	at java.util.concurrent.CompletableFuture$UniWhenComplete.tryFire(CompletableFuture.java:736)
	at java.util.concurrent.CompletableFuture.postComplete(CompletableFuture.java:474)
	at java.util.concurrent.CompletableFuture.completeExceptionally(CompletableFuture.java:1977)
	at io.lettuce.core.AbstractRedisClient.lambda$initializeChannelAsync0$4(AbstractRedisClient.java:329)
	at io.netty.util.concurrent.DefaultPromise.notifyListener0(DefaultPromise.java:577)
	at io.netty.util.concurrent.DefaultPromise.notifyListeners0(DefaultPromise.java:570)
	at io.netty.util.concurrent.DefaultPromise.notifyListenersNow(DefaultPromise.java:549)
	at io.netty.util.concurrent.DefaultPromise.notifyListeners(DefaultPromise.java:490)
	at io.netty.util.concurrent.DefaultPromise.setValue0(DefaultPromise.java:615)
	at io.netty.util.concurrent.DefaultPromise.setFailure0(DefaultPromise.java:608)
	at io.netty.util.concurrent.DefaultPromise.tryFailure(DefaultPromise.java:117)
	at io.netty.channel.nio.AbstractNioChannel$AbstractNioUnsafe.fulfillConnectPromise(AbstractNioChannel.java:321)
	at io.netty.channel.nio.AbstractNioChannel$AbstractNioUnsafe.finishConnect(AbstractNioChannel.java:337)
	at io.netty.channel.nio.NioEventLoop.processSelectedKey(NioEventLoop.java:688)
	at io.netty.channel.nio.NioEventLoop.processSelectedKeysOptimized(NioEventLoop.java:635)
	at io.netty.channel.nio.NioEventLoop.processSelectedKeys(NioEventLoop.java:552)
	at io.netty.channel.nio.NioEventLoop.run(NioEventLoop.java:514)
	... 4 common frames omitted
Caused by: io.netty.channel.AbstractChannel$AnnotatedNoRouteToHostException: No route to host: no further information: /************:7006
Caused by: java.net.NoRouteToHostException: No route to host: no further information
	at sun.nio.ch.SocketChannelImpl.checkConnect(Native Method)
	at sun.nio.ch.SocketChannelImpl.finishConnect(SocketChannelImpl.java:717)
	at io.netty.channel.socket.nio.NioSocketChannel.doFinishConnect(NioSocketChannel.java:330)
	at io.netty.channel.nio.AbstractNioChannel$AbstractNioUnsafe.finishConnect(AbstractNioChannel.java:334)
	at io.netty.channel.nio.NioEventLoop.processSelectedKey(NioEventLoop.java:688)
	at io.netty.channel.nio.NioEventLoop.processSelectedKeysOptimized(NioEventLoop.java:635)
	at io.netty.channel.nio.NioEventLoop.processSelectedKeys(NioEventLoop.java:552)
	at io.netty.channel.nio.NioEventLoop.run(NioEventLoop.java:514)
	at io.netty.util.concurrent.SingleThreadEventExecutor$6.run(SingleThreadEventExecutor.java:1050)
	at io.netty.util.internal.ThreadExecutorMap$2.run(ThreadExecutorMap.java:74)
	at io.netty.util.concurrent.FastThreadLocalRunnable.run(FastThreadLocalRunnable.java:30)
	at java.lang.Thread.run(Thread.java:748)
2025-05-29 00:04:49.050 [AMQP Connection ************:5672] WARN  com.rabbitmq.client.impl.ForgivingExceptionHandler.log Line:115 - An unexpected connection driver error occured (Exception message: Connection reset)
2025-05-29 00:04:50.018 [AMQP Connection ************:5672] WARN  com.rabbitmq.client.impl.ForgivingExceptionHandler.log Line:115 - An unexpected connection driver error occured (Exception message: Connection reset)
2025-05-29 00:04:50.110 [AMQP Connection ************:5672] WARN  com.rabbitmq.client.impl.ForgivingExceptionHandler.log Line:115 - An unexpected connection driver error occured (Exception message: Connection reset)
2025-05-29 00:04:50.249 [AMQP Connection ************:5672] WARN  com.rabbitmq.client.impl.ForgivingExceptionHandler.log Line:115 - An unexpected connection driver error occured (Exception message: Connection reset)
2025-05-29 00:45:21.270 [AMQP Connection ************:5672] WARN  com.rabbitmq.client.impl.ForgivingExceptionHandler.log Line:115 - An unexpected connection driver error occured (Exception message: Connection reset)
2025-05-29 00:45:22.377 [AMQP Connection ************:5672] WARN  com.rabbitmq.client.impl.ForgivingExceptionHandler.log Line:115 - An unexpected connection driver error occured (Exception message: Connection reset)
2025-05-29 00:45:22.456 [AMQP Connection ************:5672] WARN  com.rabbitmq.client.impl.ForgivingExceptionHandler.log Line:115 - An unexpected connection driver error occured (Exception message: Connection reset)
2025-05-29 00:45:22.471 [AMQP Connection ************:5672] WARN  com.rabbitmq.client.impl.ForgivingExceptionHandler.log Line:115 - An unexpected connection driver error occured (Exception message: Connection reset)
2025-05-29 01:26:22.033 [AMQP Connection ************:5672] WARN  com.rabbitmq.client.impl.ForgivingExceptionHandler.log Line:115 - An unexpected connection driver error occured (Exception message: Connection reset)
2025-05-29 01:26:23.117 [AMQP Connection ************:5672] WARN  com.rabbitmq.client.impl.ForgivingExceptionHandler.log Line:115 - An unexpected connection driver error occured (Exception message: Connection reset)
2025-05-29 01:26:23.132 [AMQP Connection ************:5672] WARN  com.rabbitmq.client.impl.ForgivingExceptionHandler.log Line:115 - An unexpected connection driver error occured (Exception message: Connection reset)
2025-05-29 01:26:23.148 [AMQP Connection ************:5672] WARN  com.rabbitmq.client.impl.ForgivingExceptionHandler.log Line:115 - An unexpected connection driver error occured (Exception message: Connection reset)
2025-05-29 01:40:00.016 [pool-14-thread-8] WARN  com.simbest.boot.base.repository.CustomDynamicWhere.queryForList Line:50 - 自定义查询SQL输出为：【select * from US_TODO_MODEL t  where t.send_flag = 0 and t.work_flag = 0 and t.enabled =1】
2025-05-29 01:45:00.009 [pool-14-thread-8] WARN  com.simbest.boot.base.repository.CustomDynamicWhere.queryForList Line:50 - 自定义查询SQL输出为：【select * from US_TODO_MODEL t  where t.send_flag = 0 and t.work_flag = 0 and t.enabled =1】
2025-05-29 01:46:07.377 [lettuce-nioEventLoop-4-7] WARN  io.lettuce.core.cluster.topology.ClusterTopologyRefresh.lambda$getConnections$0 Line:284 - Unable to connect to [************:7001]: No route to host: no further information: /************:7001
2025-05-29 01:46:07.377 [lettuce-nioEventLoop-4-9] WARN  io.lettuce.core.cluster.topology.ClusterTopologyRefresh.lambda$getConnections$0 Line:284 - Unable to connect to [************:7003]: No route to host: no further information: /************:7003
2025-05-29 01:46:07.377 [lettuce-nioEventLoop-4-10] WARN  io.lettuce.core.cluster.topology.ClusterTopologyRefresh.lambda$getConnections$0 Line:284 - Unable to connect to [************:7004]: No route to host: no further information: /************:7004
2025-05-29 01:46:07.377 [lettuce-nioEventLoop-4-12] WARN  io.lettuce.core.cluster.topology.ClusterTopologyRefresh.lambda$getConnections$0 Line:284 - Unable to connect to [************:7006]: No route to host: no further information: /************:7006
2025-05-29 01:46:07.377 [lettuce-nioEventLoop-4-8] WARN  io.lettuce.core.cluster.topology.ClusterTopologyRefresh.lambda$getConnections$0 Line:284 - Unable to connect to [************:7002]: No route to host: no further information: /************:7002
2025-05-29 01:46:07.377 [lettuce-nioEventLoop-4-11] WARN  io.lettuce.core.cluster.topology.ClusterTopologyRefresh.lambda$getConnections$0 Line:284 - Unable to connect to [************:7005]: No route to host: no further information: /************:7005
2025-05-29 01:46:07.379 [lettuce-nioEventLoop-4-1] WARN  io.lettuce.core.cluster.topology.ClusterTopologyRefresh.lambda$getConnections$0 Line:284 - Unable to connect to [************:7006]: No route to host: no further information: /************:7006
2025-05-29 01:46:07.380 [lettuce-nioEventLoop-4-5] WARN  io.lettuce.core.cluster.topology.ClusterTopologyRefresh.lambda$getConnections$0 Line:284 - Unable to connect to [************:7003]: No route to host: no further information: /************:7003
2025-05-29 01:46:07.380 [lettuce-nioEventLoop-4-6] WARN  io.lettuce.core.cluster.topology.ClusterTopologyRefresh.lambda$getConnections$0 Line:284 - Unable to connect to [************:7001]: No route to host: no further information: /************:7001
2025-05-29 01:46:07.380 [lettuce-nioEventLoop-4-3] WARN  io.lettuce.core.cluster.topology.ClusterTopologyRefresh.lambda$getConnections$0 Line:284 - Unable to connect to [************:7005]: No route to host: no further information: /************:7005
2025-05-29 01:46:07.392 [lettuce-nioEventLoop-4-4] WARN  io.lettuce.core.cluster.topology.ClusterTopologyRefresh.lambda$getConnections$0 Line:284 - Unable to connect to [************:7002]: No route to host: no further information: /************:7002
2025-05-29 01:46:07.392 [lettuce-nioEventLoop-4-2] WARN  io.lettuce.core.cluster.topology.ClusterTopologyRefresh.lambda$getConnections$0 Line:284 - Unable to connect to [************:7004]: No route to host: no further information: /************:7004
2025-05-29 01:46:07.400 [lettuce-eventExecutorLoop-1-11] WARN  io.lettuce.core.cluster.ClusterTopologyRefreshScheduler.doRun Line:258 - Cannot refresh Redis Cluster topology
io.lettuce.core.RedisException: Cannot retrieve initial cluster partitions from initial URIs [RedisURI [host='************', port=7006], RedisURI [host='************', port=7004], RedisURI [host='************', port=7005], RedisURI [host='************', port=7002], RedisURI [host='************', port=7003], RedisURI [host='************', port=7001]]
	at io.lettuce.core.cluster.RedisClusterClient.loadPartitions(RedisClusterClient.java:848)
	at io.lettuce.core.cluster.RedisClusterClient.reloadPartitions(RedisClusterClient.java:784)
	at io.lettuce.core.cluster.ClusterTopologyRefreshScheduler$ClusterTopologyRefreshTask.doRun(ClusterTopologyRefreshScheduler.java:256)
	at io.lettuce.core.cluster.ClusterTopologyRefreshScheduler$ClusterTopologyRefreshTask.run(ClusterTopologyRefreshScheduler.java:236)
	at io.netty.util.concurrent.PromiseTask.runTask(PromiseTask.java:98)
	at io.netty.util.concurrent.PromiseTask.run(PromiseTask.java:106)
	at io.netty.util.concurrent.DefaultEventExecutor.run(DefaultEventExecutor.java:66)
	at io.netty.util.concurrent.SingleThreadEventExecutor$6.run(SingleThreadEventExecutor.java:1050)
	at io.netty.util.internal.ThreadExecutorMap$2.run(ThreadExecutorMap.java:74)
	at io.netty.util.concurrent.FastThreadLocalRunnable.run(FastThreadLocalRunnable.java:30)
	at java.lang.Thread.run(Thread.java:748)
	Suppressed: io.lettuce.core.RedisConnectionException: Unable to establish a connection to Redis Cluster at [RedisURI [host='************', port=7001], RedisURI [host='************', port=7002], RedisURI [host='************', port=7003], RedisURI [host='************', port=7004], RedisURI [host='************', port=7005], RedisURI [host='************', port=7006]]
		at io.lettuce.core.cluster.topology.AsyncConnections.get(AsyncConnections.java:89)
		at io.lettuce.core.cluster.topology.ClusterTopologyRefresh.loadViews(ClusterTopologyRefresh.java:76)
		at io.lettuce.core.cluster.RedisClusterClient.doLoadPartitions(RedisClusterClient.java:865)
		at io.lettuce.core.cluster.RedisClusterClient.loadPartitions(RedisClusterClient.java:838)
		... 10 common frames omitted
		Suppressed: io.lettuce.core.RedisConnectionException: Unable to connect to [************:7003]: No route to host: no further information: /************:7003
			at io.lettuce.core.cluster.topology.ClusterTopologyRefresh.lambda$getConnections$0(ClusterTopologyRefresh.java:290)
			at java.util.concurrent.CompletableFuture.uniWhenComplete(CompletableFuture.java:760)
			at java.util.concurrent.CompletableFuture$UniWhenComplete.tryFire(CompletableFuture.java:736)
			at java.util.concurrent.CompletableFuture.postComplete(CompletableFuture.java:474)
			at java.util.concurrent.CompletableFuture.completeExceptionally(CompletableFuture.java:1977)
			at io.lettuce.core.AbstractRedisClient.lambda$initializeChannelAsync0$4(AbstractRedisClient.java:329)
			at io.netty.util.concurrent.DefaultPromise.notifyListener0(DefaultPromise.java:577)
			at io.netty.util.concurrent.DefaultPromise.notifyListeners0(DefaultPromise.java:570)
			at io.netty.util.concurrent.DefaultPromise.notifyListenersNow(DefaultPromise.java:549)
			at io.netty.util.concurrent.DefaultPromise.notifyListeners(DefaultPromise.java:490)
			at io.netty.util.concurrent.DefaultPromise.setValue0(DefaultPromise.java:615)
			at io.netty.util.concurrent.DefaultPromise.setFailure0(DefaultPromise.java:608)
			at io.netty.util.concurrent.DefaultPromise.tryFailure(DefaultPromise.java:117)
			at io.netty.channel.nio.AbstractNioChannel$AbstractNioUnsafe.fulfillConnectPromise(AbstractNioChannel.java:321)
			at io.netty.channel.nio.AbstractNioChannel$AbstractNioUnsafe.finishConnect(AbstractNioChannel.java:337)
			at io.netty.channel.nio.NioEventLoop.processSelectedKey(NioEventLoop.java:688)
			at io.netty.channel.nio.NioEventLoop.processSelectedKeysOptimized(NioEventLoop.java:635)
			at io.netty.channel.nio.NioEventLoop.processSelectedKeys(NioEventLoop.java:552)
			at io.netty.channel.nio.NioEventLoop.run(NioEventLoop.java:514)
			... 4 common frames omitted
		Caused by: io.netty.channel.AbstractChannel$AnnotatedNoRouteToHostException: No route to host: no further information: /************:7003
		Caused by: java.net.NoRouteToHostException: No route to host: no further information
			at sun.nio.ch.SocketChannelImpl.checkConnect(Native Method)
			at sun.nio.ch.SocketChannelImpl.finishConnect(SocketChannelImpl.java:717)
			at io.netty.channel.socket.nio.NioSocketChannel.doFinishConnect(NioSocketChannel.java:330)
			at io.netty.channel.nio.AbstractNioChannel$AbstractNioUnsafe.finishConnect(AbstractNioChannel.java:334)
			at io.netty.channel.nio.NioEventLoop.processSelectedKey(NioEventLoop.java:688)
			at io.netty.channel.nio.NioEventLoop.processSelectedKeysOptimized(NioEventLoop.java:635)
			at io.netty.channel.nio.NioEventLoop.processSelectedKeys(NioEventLoop.java:552)
			at io.netty.channel.nio.NioEventLoop.run(NioEventLoop.java:514)
			at io.netty.util.concurrent.SingleThreadEventExecutor$6.run(SingleThreadEventExecutor.java:1050)
			at io.netty.util.internal.ThreadExecutorMap$2.run(ThreadExecutorMap.java:74)
			at io.netty.util.concurrent.FastThreadLocalRunnable.run(FastThreadLocalRunnable.java:30)
			at java.lang.Thread.run(Thread.java:748)
		Suppressed: io.lettuce.core.RedisConnectionException: Unable to connect to [************:7004]: No route to host: no further information: /************:7004
			at io.lettuce.core.cluster.topology.ClusterTopologyRefresh.lambda$getConnections$0(ClusterTopologyRefresh.java:290)
			at java.util.concurrent.CompletableFuture.uniWhenComplete(CompletableFuture.java:760)
			at java.util.concurrent.CompletableFuture$UniWhenComplete.tryFire(CompletableFuture.java:736)
			at java.util.concurrent.CompletableFuture.postComplete(CompletableFuture.java:474)
			at java.util.concurrent.CompletableFuture.completeExceptionally(CompletableFuture.java:1977)
			at io.lettuce.core.AbstractRedisClient.lambda$initializeChannelAsync0$4(AbstractRedisClient.java:329)
			at io.netty.util.concurrent.DefaultPromise.notifyListener0(DefaultPromise.java:577)
			at io.netty.util.concurrent.DefaultPromise.notifyListeners0(DefaultPromise.java:570)
			at io.netty.util.concurrent.DefaultPromise.notifyListenersNow(DefaultPromise.java:549)
			at io.netty.util.concurrent.DefaultPromise.notifyListeners(DefaultPromise.java:490)
			at io.netty.util.concurrent.DefaultPromise.setValue0(DefaultPromise.java:615)
			at io.netty.util.concurrent.DefaultPromise.setFailure0(DefaultPromise.java:608)
			at io.netty.util.concurrent.DefaultPromise.tryFailure(DefaultPromise.java:117)
			at io.netty.channel.nio.AbstractNioChannel$AbstractNioUnsafe.fulfillConnectPromise(AbstractNioChannel.java:321)
			at io.netty.channel.nio.AbstractNioChannel$AbstractNioUnsafe.finishConnect(AbstractNioChannel.java:337)
			at io.netty.channel.nio.NioEventLoop.processSelectedKey(NioEventLoop.java:688)
			at io.netty.channel.nio.NioEventLoop.processSelectedKeysOptimized(NioEventLoop.java:635)
			at io.netty.channel.nio.NioEventLoop.processSelectedKeys(NioEventLoop.java:552)
			at io.netty.channel.nio.NioEventLoop.run(NioEventLoop.java:514)
			... 4 common frames omitted
		Caused by: io.netty.channel.AbstractChannel$AnnotatedNoRouteToHostException: No route to host: no further information: /************:7004
		Caused by: java.net.NoRouteToHostException: No route to host: no further information
			at sun.nio.ch.SocketChannelImpl.checkConnect(Native Method)
			at sun.nio.ch.SocketChannelImpl.finishConnect(SocketChannelImpl.java:717)
			at io.netty.channel.socket.nio.NioSocketChannel.doFinishConnect(NioSocketChannel.java:330)
			at io.netty.channel.nio.AbstractNioChannel$AbstractNioUnsafe.finishConnect(AbstractNioChannel.java:334)
			at io.netty.channel.nio.NioEventLoop.processSelectedKey(NioEventLoop.java:688)
			at io.netty.channel.nio.NioEventLoop.processSelectedKeysOptimized(NioEventLoop.java:635)
			at io.netty.channel.nio.NioEventLoop.processSelectedKeys(NioEventLoop.java:552)
			at io.netty.channel.nio.NioEventLoop.run(NioEventLoop.java:514)
			at io.netty.util.concurrent.SingleThreadEventExecutor$6.run(SingleThreadEventExecutor.java:1050)
			at io.netty.util.internal.ThreadExecutorMap$2.run(ThreadExecutorMap.java:74)
			at io.netty.util.concurrent.FastThreadLocalRunnable.run(FastThreadLocalRunnable.java:30)
			at java.lang.Thread.run(Thread.java:748)
		Suppressed: io.lettuce.core.RedisConnectionException: Unable to connect to [************:7006]: No route to host: no further information: /************:7006
			at io.lettuce.core.cluster.topology.ClusterTopologyRefresh.lambda$getConnections$0(ClusterTopologyRefresh.java:290)
			at java.util.concurrent.CompletableFuture.uniWhenComplete(CompletableFuture.java:760)
			at java.util.concurrent.CompletableFuture$UniWhenComplete.tryFire(CompletableFuture.java:736)
			at java.util.concurrent.CompletableFuture.postComplete(CompletableFuture.java:474)
			at java.util.concurrent.CompletableFuture.completeExceptionally(CompletableFuture.java:1977)
			at io.lettuce.core.AbstractRedisClient.lambda$initializeChannelAsync0$4(AbstractRedisClient.java:329)
			at io.netty.util.concurrent.DefaultPromise.notifyListener0(DefaultPromise.java:577)
			at io.netty.util.concurrent.DefaultPromise.notifyListeners0(DefaultPromise.java:570)
			at io.netty.util.concurrent.DefaultPromise.notifyListenersNow(DefaultPromise.java:549)
			at io.netty.util.concurrent.DefaultPromise.notifyListeners(DefaultPromise.java:490)
			at io.netty.util.concurrent.DefaultPromise.setValue0(DefaultPromise.java:615)
			at io.netty.util.concurrent.DefaultPromise.setFailure0(DefaultPromise.java:608)
			at io.netty.util.concurrent.DefaultPromise.tryFailure(DefaultPromise.java:117)
			at io.netty.channel.nio.AbstractNioChannel$AbstractNioUnsafe.fulfillConnectPromise(AbstractNioChannel.java:321)
			at io.netty.channel.nio.AbstractNioChannel$AbstractNioUnsafe.finishConnect(AbstractNioChannel.java:337)
			at io.netty.channel.nio.NioEventLoop.processSelectedKey(NioEventLoop.java:688)
			at io.netty.channel.nio.NioEventLoop.processSelectedKeysOptimized(NioEventLoop.java:635)
			at io.netty.channel.nio.NioEventLoop.processSelectedKeys(NioEventLoop.java:552)
			at io.netty.channel.nio.NioEventLoop.run(NioEventLoop.java:514)
			... 4 common frames omitted
		Caused by: io.netty.channel.AbstractChannel$AnnotatedNoRouteToHostException: No route to host: no further information: /************:7006
		Caused by: java.net.NoRouteToHostException: No route to host: no further information
			at sun.nio.ch.SocketChannelImpl.checkConnect(Native Method)
			at sun.nio.ch.SocketChannelImpl.finishConnect(SocketChannelImpl.java:717)
			at io.netty.channel.socket.nio.NioSocketChannel.doFinishConnect(NioSocketChannel.java:330)
			at io.netty.channel.nio.AbstractNioChannel$AbstractNioUnsafe.finishConnect(AbstractNioChannel.java:334)
			at io.netty.channel.nio.NioEventLoop.processSelectedKey(NioEventLoop.java:688)
			at io.netty.channel.nio.NioEventLoop.processSelectedKeysOptimized(NioEventLoop.java:635)
			at io.netty.channel.nio.NioEventLoop.processSelectedKeys(NioEventLoop.java:552)
			at io.netty.channel.nio.NioEventLoop.run(NioEventLoop.java:514)
			at io.netty.util.concurrent.SingleThreadEventExecutor$6.run(SingleThreadEventExecutor.java:1050)
			at io.netty.util.internal.ThreadExecutorMap$2.run(ThreadExecutorMap.java:74)
			at io.netty.util.concurrent.FastThreadLocalRunnable.run(FastThreadLocalRunnable.java:30)
			at java.lang.Thread.run(Thread.java:748)
		Suppressed: io.lettuce.core.RedisConnectionException: Unable to connect to [************:7002]: No route to host: no further information: /************:7002
			at io.lettuce.core.cluster.topology.ClusterTopologyRefresh.lambda$getConnections$0(ClusterTopologyRefresh.java:290)
			at java.util.concurrent.CompletableFuture.uniWhenComplete(CompletableFuture.java:760)
			at java.util.concurrent.CompletableFuture$UniWhenComplete.tryFire(CompletableFuture.java:736)
			at java.util.concurrent.CompletableFuture.postComplete(CompletableFuture.java:474)
			at java.util.concurrent.CompletableFuture.completeExceptionally(CompletableFuture.java:1977)
			at io.lettuce.core.AbstractRedisClient.lambda$initializeChannelAsync0$4(AbstractRedisClient.java:329)
			at io.netty.util.concurrent.DefaultPromise.notifyListener0(DefaultPromise.java:577)
			at io.netty.util.concurrent.DefaultPromise.notifyListeners0(DefaultPromise.java:570)
			at io.netty.util.concurrent.DefaultPromise.notifyListenersNow(DefaultPromise.java:549)
			at io.netty.util.concurrent.DefaultPromise.notifyListeners(DefaultPromise.java:490)
			at io.netty.util.concurrent.DefaultPromise.setValue0(DefaultPromise.java:615)
			at io.netty.util.concurrent.DefaultPromise.setFailure0(DefaultPromise.java:608)
			at io.netty.util.concurrent.DefaultPromise.tryFailure(DefaultPromise.java:117)
			at io.netty.channel.nio.AbstractNioChannel$AbstractNioUnsafe.fulfillConnectPromise(AbstractNioChannel.java:321)
			at io.netty.channel.nio.AbstractNioChannel$AbstractNioUnsafe.finishConnect(AbstractNioChannel.java:337)
			at io.netty.channel.nio.NioEventLoop.processSelectedKey(NioEventLoop.java:688)
			at io.netty.channel.nio.NioEventLoop.processSelectedKeysOptimized(NioEventLoop.java:635)
			at io.netty.channel.nio.NioEventLoop.processSelectedKeys(NioEventLoop.java:552)
			at io.netty.channel.nio.NioEventLoop.run(NioEventLoop.java:514)
			... 4 common frames omitted
		Caused by: io.netty.channel.AbstractChannel$AnnotatedNoRouteToHostException: No route to host: no further information: /************:7002
		Caused by: java.net.NoRouteToHostException: No route to host: no further information
			at sun.nio.ch.SocketChannelImpl.checkConnect(Native Method)
			at sun.nio.ch.SocketChannelImpl.finishConnect(SocketChannelImpl.java:717)
			at io.netty.channel.socket.nio.NioSocketChannel.doFinishConnect(NioSocketChannel.java:330)
			at io.netty.channel.nio.AbstractNioChannel$AbstractNioUnsafe.finishConnect(AbstractNioChannel.java:334)
			at io.netty.channel.nio.NioEventLoop.processSelectedKey(NioEventLoop.java:688)
			at io.netty.channel.nio.NioEventLoop.processSelectedKeysOptimized(NioEventLoop.java:635)
			at io.netty.channel.nio.NioEventLoop.processSelectedKeys(NioEventLoop.java:552)
			at io.netty.channel.nio.NioEventLoop.run(NioEventLoop.java:514)
			at io.netty.util.concurrent.SingleThreadEventExecutor$6.run(SingleThreadEventExecutor.java:1050)
			at io.netty.util.internal.ThreadExecutorMap$2.run(ThreadExecutorMap.java:74)
			at io.netty.util.concurrent.FastThreadLocalRunnable.run(FastThreadLocalRunnable.java:30)
			at java.lang.Thread.run(Thread.java:748)
		Suppressed: io.lettuce.core.RedisConnectionException: Unable to connect to [************:7005]: No route to host: no further information: /************:7005
			at io.lettuce.core.cluster.topology.ClusterTopologyRefresh.lambda$getConnections$0(ClusterTopologyRefresh.java:290)
			at java.util.concurrent.CompletableFuture.uniWhenComplete(CompletableFuture.java:760)
			at java.util.concurrent.CompletableFuture$UniWhenComplete.tryFire(CompletableFuture.java:736)
			at java.util.concurrent.CompletableFuture.postComplete(CompletableFuture.java:474)
			at java.util.concurrent.CompletableFuture.completeExceptionally(CompletableFuture.java:1977)
			at io.lettuce.core.AbstractRedisClient.lambda$initializeChannelAsync0$4(AbstractRedisClient.java:329)
			at io.netty.util.concurrent.DefaultPromise.notifyListener0(DefaultPromise.java:577)
			at io.netty.util.concurrent.DefaultPromise.notifyListeners0(DefaultPromise.java:570)
			at io.netty.util.concurrent.DefaultPromise.notifyListenersNow(DefaultPromise.java:549)
			at io.netty.util.concurrent.DefaultPromise.notifyListeners(DefaultPromise.java:490)
			at io.netty.util.concurrent.DefaultPromise.setValue0(DefaultPromise.java:615)
			at io.netty.util.concurrent.DefaultPromise.setFailure0(DefaultPromise.java:608)
			at io.netty.util.concurrent.DefaultPromise.tryFailure(DefaultPromise.java:117)
			at io.netty.channel.nio.AbstractNioChannel$AbstractNioUnsafe.fulfillConnectPromise(AbstractNioChannel.java:321)
			at io.netty.channel.nio.AbstractNioChannel$AbstractNioUnsafe.finishConnect(AbstractNioChannel.java:337)
			at io.netty.channel.nio.NioEventLoop.processSelectedKey(NioEventLoop.java:688)
			at io.netty.channel.nio.NioEventLoop.processSelectedKeysOptimized(NioEventLoop.java:635)
			at io.netty.channel.nio.NioEventLoop.processSelectedKeys(NioEventLoop.java:552)
			at io.netty.channel.nio.NioEventLoop.run(NioEventLoop.java:514)
			... 4 common frames omitted
		Caused by: io.netty.channel.AbstractChannel$AnnotatedNoRouteToHostException: No route to host: no further information: /************:7005
		Caused by: java.net.NoRouteToHostException: No route to host: no further information
			at sun.nio.ch.SocketChannelImpl.checkConnect(Native Method)
			at sun.nio.ch.SocketChannelImpl.finishConnect(SocketChannelImpl.java:717)
			at io.netty.channel.socket.nio.NioSocketChannel.doFinishConnect(NioSocketChannel.java:330)
			at io.netty.channel.nio.AbstractNioChannel$AbstractNioUnsafe.finishConnect(AbstractNioChannel.java:334)
			at io.netty.channel.nio.NioEventLoop.processSelectedKey(NioEventLoop.java:688)
			at io.netty.channel.nio.NioEventLoop.processSelectedKeysOptimized(NioEventLoop.java:635)
			at io.netty.channel.nio.NioEventLoop.processSelectedKeys(NioEventLoop.java:552)
			at io.netty.channel.nio.NioEventLoop.run(NioEventLoop.java:514)
			at io.netty.util.concurrent.SingleThreadEventExecutor$6.run(SingleThreadEventExecutor.java:1050)
			at io.netty.util.internal.ThreadExecutorMap$2.run(ThreadExecutorMap.java:74)
			at io.netty.util.concurrent.FastThreadLocalRunnable.run(FastThreadLocalRunnable.java:30)
			at java.lang.Thread.run(Thread.java:748)
	Caused by: io.lettuce.core.RedisConnectionException: Unable to connect to [************:7001]: No route to host: no further information: /************:7001
		at io.lettuce.core.cluster.topology.ClusterTopologyRefresh.lambda$getConnections$0(ClusterTopologyRefresh.java:290)
		at java.util.concurrent.CompletableFuture.uniWhenComplete(CompletableFuture.java:760)
		at java.util.concurrent.CompletableFuture$UniWhenComplete.tryFire(CompletableFuture.java:736)
		at java.util.concurrent.CompletableFuture.postComplete(CompletableFuture.java:474)
		at java.util.concurrent.CompletableFuture.completeExceptionally(CompletableFuture.java:1977)
		at io.lettuce.core.AbstractRedisClient.lambda$initializeChannelAsync0$4(AbstractRedisClient.java:329)
		at io.netty.util.concurrent.DefaultPromise.notifyListener0(DefaultPromise.java:577)
		at io.netty.util.concurrent.DefaultPromise.notifyListeners0(DefaultPromise.java:570)
		at io.netty.util.concurrent.DefaultPromise.notifyListenersNow(DefaultPromise.java:549)
		at io.netty.util.concurrent.DefaultPromise.notifyListeners(DefaultPromise.java:490)
		at io.netty.util.concurrent.DefaultPromise.setValue0(DefaultPromise.java:615)
		at io.netty.util.concurrent.DefaultPromise.setFailure0(DefaultPromise.java:608)
		at io.netty.util.concurrent.DefaultPromise.tryFailure(DefaultPromise.java:117)
		at io.netty.channel.nio.AbstractNioChannel$AbstractNioUnsafe.fulfillConnectPromise(AbstractNioChannel.java:321)
		at io.netty.channel.nio.AbstractNioChannel$AbstractNioUnsafe.finishConnect(AbstractNioChannel.java:337)
		at io.netty.channel.nio.NioEventLoop.processSelectedKey(NioEventLoop.java:688)
		at io.netty.channel.nio.NioEventLoop.processSelectedKeysOptimized(NioEventLoop.java:635)
		at io.netty.channel.nio.NioEventLoop.processSelectedKeys(NioEventLoop.java:552)
		at io.netty.channel.nio.NioEventLoop.run(NioEventLoop.java:514)
		... 4 common frames omitted
	Caused by: io.netty.channel.AbstractChannel$AnnotatedNoRouteToHostException: No route to host: no further information: /************:7001
	Caused by: java.net.NoRouteToHostException: No route to host: no further information
		at sun.nio.ch.SocketChannelImpl.checkConnect(Native Method)
		at sun.nio.ch.SocketChannelImpl.finishConnect(SocketChannelImpl.java:717)
		at io.netty.channel.socket.nio.NioSocketChannel.doFinishConnect(NioSocketChannel.java:330)
		at io.netty.channel.nio.AbstractNioChannel$AbstractNioUnsafe.finishConnect(AbstractNioChannel.java:334)
		at io.netty.channel.nio.NioEventLoop.processSelectedKey(NioEventLoop.java:688)
		at io.netty.channel.nio.NioEventLoop.processSelectedKeysOptimized(NioEventLoop.java:635)
		at io.netty.channel.nio.NioEventLoop.processSelectedKeys(NioEventLoop.java:552)
		at io.netty.channel.nio.NioEventLoop.run(NioEventLoop.java:514)
		at io.netty.util.concurrent.SingleThreadEventExecutor$6.run(SingleThreadEventExecutor.java:1050)
		at io.netty.util.internal.ThreadExecutorMap$2.run(ThreadExecutorMap.java:74)
		at io.netty.util.concurrent.FastThreadLocalRunnable.run(FastThreadLocalRunnable.java:30)
		at java.lang.Thread.run(Thread.java:748)
Caused by: io.lettuce.core.RedisConnectionException: Unable to establish a connection to Redis Cluster at [RedisURI [host='************', port=7001], RedisURI [host='************', port=7002], RedisURI [host='************', port=7003], RedisURI [host='************', port=7004], RedisURI [host='************', port=7005], RedisURI [host='************', port=7006]]
	at io.lettuce.core.cluster.topology.AsyncConnections.get(AsyncConnections.java:89)
	at io.lettuce.core.cluster.topology.ClusterTopologyRefresh.loadViews(ClusterTopologyRefresh.java:76)
	at io.lettuce.core.cluster.RedisClusterClient.doLoadPartitions(RedisClusterClient.java:865)
	at io.lettuce.core.cluster.RedisClusterClient.loadPartitions(RedisClusterClient.java:845)
	... 10 common frames omitted
	Suppressed: io.lettuce.core.RedisConnectionException: Unable to connect to [************:7003]: No route to host: no further information: /************:7003
		at io.lettuce.core.cluster.topology.ClusterTopologyRefresh.lambda$getConnections$0(ClusterTopologyRefresh.java:290)
		at java.util.concurrent.CompletableFuture.uniWhenComplete(CompletableFuture.java:760)
		at java.util.concurrent.CompletableFuture$UniWhenComplete.tryFire(CompletableFuture.java:736)
		at java.util.concurrent.CompletableFuture.postComplete(CompletableFuture.java:474)
		at java.util.concurrent.CompletableFuture.completeExceptionally(CompletableFuture.java:1977)
		at io.lettuce.core.AbstractRedisClient.lambda$initializeChannelAsync0$4(AbstractRedisClient.java:329)
		at io.netty.util.concurrent.DefaultPromise.notifyListener0(DefaultPromise.java:577)
		at io.netty.util.concurrent.DefaultPromise.notifyListeners0(DefaultPromise.java:570)
		at io.netty.util.concurrent.DefaultPromise.notifyListenersNow(DefaultPromise.java:549)
		at io.netty.util.concurrent.DefaultPromise.notifyListeners(DefaultPromise.java:490)
		at io.netty.util.concurrent.DefaultPromise.setValue0(DefaultPromise.java:615)
		at io.netty.util.concurrent.DefaultPromise.setFailure0(DefaultPromise.java:608)
		at io.netty.util.concurrent.DefaultPromise.tryFailure(DefaultPromise.java:117)
		at io.netty.channel.nio.AbstractNioChannel$AbstractNioUnsafe.fulfillConnectPromise(AbstractNioChannel.java:321)
		at io.netty.channel.nio.AbstractNioChannel$AbstractNioUnsafe.finishConnect(AbstractNioChannel.java:337)
		at io.netty.channel.nio.NioEventLoop.processSelectedKey(NioEventLoop.java:688)
		at io.netty.channel.nio.NioEventLoop.processSelectedKeysOptimized(NioEventLoop.java:635)
		at io.netty.channel.nio.NioEventLoop.processSelectedKeys(NioEventLoop.java:552)
		at io.netty.channel.nio.NioEventLoop.run(NioEventLoop.java:514)
		... 4 common frames omitted
	Caused by: io.netty.channel.AbstractChannel$AnnotatedNoRouteToHostException: No route to host: no further information: /************:7003
	Caused by: java.net.NoRouteToHostException: No route to host: no further information
		at sun.nio.ch.SocketChannelImpl.checkConnect(Native Method)
		at sun.nio.ch.SocketChannelImpl.finishConnect(SocketChannelImpl.java:717)
		at io.netty.channel.socket.nio.NioSocketChannel.doFinishConnect(NioSocketChannel.java:330)
		at io.netty.channel.nio.AbstractNioChannel$AbstractNioUnsafe.finishConnect(AbstractNioChannel.java:334)
		at io.netty.channel.nio.NioEventLoop.processSelectedKey(NioEventLoop.java:688)
		at io.netty.channel.nio.NioEventLoop.processSelectedKeysOptimized(NioEventLoop.java:635)
		at io.netty.channel.nio.NioEventLoop.processSelectedKeys(NioEventLoop.java:552)
		at io.netty.channel.nio.NioEventLoop.run(NioEventLoop.java:514)
		at io.netty.util.concurrent.SingleThreadEventExecutor$6.run(SingleThreadEventExecutor.java:1050)
		at io.netty.util.internal.ThreadExecutorMap$2.run(ThreadExecutorMap.java:74)
		at io.netty.util.concurrent.FastThreadLocalRunnable.run(FastThreadLocalRunnable.java:30)
		at java.lang.Thread.run(Thread.java:748)
	Suppressed: io.lettuce.core.RedisConnectionException: Unable to connect to [************:7005]: No route to host: no further information: /************:7005
		at io.lettuce.core.cluster.topology.ClusterTopologyRefresh.lambda$getConnections$0(ClusterTopologyRefresh.java:290)
		at java.util.concurrent.CompletableFuture.uniWhenComplete(CompletableFuture.java:760)
		at java.util.concurrent.CompletableFuture$UniWhenComplete.tryFire(CompletableFuture.java:736)
		at java.util.concurrent.CompletableFuture.postComplete(CompletableFuture.java:474)
		at java.util.concurrent.CompletableFuture.completeExceptionally(CompletableFuture.java:1977)
		at io.lettuce.core.AbstractRedisClient.lambda$initializeChannelAsync0$4(AbstractRedisClient.java:329)
		at io.netty.util.concurrent.DefaultPromise.notifyListener0(DefaultPromise.java:577)
		at io.netty.util.concurrent.DefaultPromise.notifyListeners0(DefaultPromise.java:570)
		at io.netty.util.concurrent.DefaultPromise.notifyListenersNow(DefaultPromise.java:549)
		at io.netty.util.concurrent.DefaultPromise.notifyListeners(DefaultPromise.java:490)
		at io.netty.util.concurrent.DefaultPromise.setValue0(DefaultPromise.java:615)
		at io.netty.util.concurrent.DefaultPromise.setFailure0(DefaultPromise.java:608)
		at io.netty.util.concurrent.DefaultPromise.tryFailure(DefaultPromise.java:117)
		at io.netty.channel.nio.AbstractNioChannel$AbstractNioUnsafe.fulfillConnectPromise(AbstractNioChannel.java:321)
		at io.netty.channel.nio.AbstractNioChannel$AbstractNioUnsafe.finishConnect(AbstractNioChannel.java:337)
		at io.netty.channel.nio.NioEventLoop.processSelectedKey(NioEventLoop.java:688)
		at io.netty.channel.nio.NioEventLoop.processSelectedKeysOptimized(NioEventLoop.java:635)
		at io.netty.channel.nio.NioEventLoop.processSelectedKeys(NioEventLoop.java:552)
		at io.netty.channel.nio.NioEventLoop.run(NioEventLoop.java:514)
		... 4 common frames omitted
	Caused by: io.netty.channel.AbstractChannel$AnnotatedNoRouteToHostException: No route to host: no further information: /************:7005
	Caused by: java.net.NoRouteToHostException: No route to host: no further information
		at sun.nio.ch.SocketChannelImpl.checkConnect(Native Method)
		at sun.nio.ch.SocketChannelImpl.finishConnect(SocketChannelImpl.java:717)
		at io.netty.channel.socket.nio.NioSocketChannel.doFinishConnect(NioSocketChannel.java:330)
		at io.netty.channel.nio.AbstractNioChannel$AbstractNioUnsafe.finishConnect(AbstractNioChannel.java:334)
		at io.netty.channel.nio.NioEventLoop.processSelectedKey(NioEventLoop.java:688)
		at io.netty.channel.nio.NioEventLoop.processSelectedKeysOptimized(NioEventLoop.java:635)
		at io.netty.channel.nio.NioEventLoop.processSelectedKeys(NioEventLoop.java:552)
		at io.netty.channel.nio.NioEventLoop.run(NioEventLoop.java:514)
		at io.netty.util.concurrent.SingleThreadEventExecutor$6.run(SingleThreadEventExecutor.java:1050)
		at io.netty.util.internal.ThreadExecutorMap$2.run(ThreadExecutorMap.java:74)
		at io.netty.util.concurrent.FastThreadLocalRunnable.run(FastThreadLocalRunnable.java:30)
		at java.lang.Thread.run(Thread.java:748)
	Suppressed: io.lettuce.core.RedisConnectionException: Unable to connect to [************:7001]: No route to host: no further information: /************:7001
		at io.lettuce.core.cluster.topology.ClusterTopologyRefresh.lambda$getConnections$0(ClusterTopologyRefresh.java:290)
		at java.util.concurrent.CompletableFuture.uniWhenComplete(CompletableFuture.java:760)
		at java.util.concurrent.CompletableFuture$UniWhenComplete.tryFire(CompletableFuture.java:736)
		at java.util.concurrent.CompletableFuture.postComplete(CompletableFuture.java:474)
		at java.util.concurrent.CompletableFuture.completeExceptionally(CompletableFuture.java:1977)
		at io.lettuce.core.AbstractRedisClient.lambda$initializeChannelAsync0$4(AbstractRedisClient.java:329)
		at io.netty.util.concurrent.DefaultPromise.notifyListener0(DefaultPromise.java:577)
		at io.netty.util.concurrent.DefaultPromise.notifyListeners0(DefaultPromise.java:570)
		at io.netty.util.concurrent.DefaultPromise.notifyListenersNow(DefaultPromise.java:549)
		at io.netty.util.concurrent.DefaultPromise.notifyListeners(DefaultPromise.java:490)
		at io.netty.util.concurrent.DefaultPromise.setValue0(DefaultPromise.java:615)
		at io.netty.util.concurrent.DefaultPromise.setFailure0(DefaultPromise.java:608)
		at io.netty.util.concurrent.DefaultPromise.tryFailure(DefaultPromise.java:117)
		at io.netty.channel.nio.AbstractNioChannel$AbstractNioUnsafe.fulfillConnectPromise(AbstractNioChannel.java:321)
		at io.netty.channel.nio.AbstractNioChannel$AbstractNioUnsafe.finishConnect(AbstractNioChannel.java:337)
		at io.netty.channel.nio.NioEventLoop.processSelectedKey(NioEventLoop.java:688)
		at io.netty.channel.nio.NioEventLoop.processSelectedKeysOptimized(NioEventLoop.java:635)
		at io.netty.channel.nio.NioEventLoop.processSelectedKeys(NioEventLoop.java:552)
		at io.netty.channel.nio.NioEventLoop.run(NioEventLoop.java:514)
		... 4 common frames omitted
	Caused by: io.netty.channel.AbstractChannel$AnnotatedNoRouteToHostException: No route to host: no further information: /************:7001
	Caused by: java.net.NoRouteToHostException: No route to host: no further information
		at sun.nio.ch.SocketChannelImpl.checkConnect(Native Method)
		at sun.nio.ch.SocketChannelImpl.finishConnect(SocketChannelImpl.java:717)
		at io.netty.channel.socket.nio.NioSocketChannel.doFinishConnect(NioSocketChannel.java:330)
		at io.netty.channel.nio.AbstractNioChannel$AbstractNioUnsafe.finishConnect(AbstractNioChannel.java:334)
		at io.netty.channel.nio.NioEventLoop.processSelectedKey(NioEventLoop.java:688)
		at io.netty.channel.nio.NioEventLoop.processSelectedKeysOptimized(NioEventLoop.java:635)
		at io.netty.channel.nio.NioEventLoop.processSelectedKeys(NioEventLoop.java:552)
		at io.netty.channel.nio.NioEventLoop.run(NioEventLoop.java:514)
		at io.netty.util.concurrent.SingleThreadEventExecutor$6.run(SingleThreadEventExecutor.java:1050)
		at io.netty.util.internal.ThreadExecutorMap$2.run(ThreadExecutorMap.java:74)
		at io.netty.util.concurrent.FastThreadLocalRunnable.run(FastThreadLocalRunnable.java:30)
		at java.lang.Thread.run(Thread.java:748)
	Suppressed: io.lettuce.core.RedisConnectionException: Unable to connect to [************:7002]: No route to host: no further information: /************:7002
		at io.lettuce.core.cluster.topology.ClusterTopologyRefresh.lambda$getConnections$0(ClusterTopologyRefresh.java:290)
		at java.util.concurrent.CompletableFuture.uniWhenComplete(CompletableFuture.java:760)
		at java.util.concurrent.CompletableFuture$UniWhenComplete.tryFire(CompletableFuture.java:736)
		at java.util.concurrent.CompletableFuture.postComplete(CompletableFuture.java:474)
		at java.util.concurrent.CompletableFuture.completeExceptionally(CompletableFuture.java:1977)
		at io.lettuce.core.AbstractRedisClient.lambda$initializeChannelAsync0$4(AbstractRedisClient.java:329)
		at io.netty.util.concurrent.DefaultPromise.notifyListener0(DefaultPromise.java:577)
		at io.netty.util.concurrent.DefaultPromise.notifyListeners0(DefaultPromise.java:570)
		at io.netty.util.concurrent.DefaultPromise.notifyListenersNow(DefaultPromise.java:549)
		at io.netty.util.concurrent.DefaultPromise.notifyListeners(DefaultPromise.java:490)
		at io.netty.util.concurrent.DefaultPromise.setValue0(DefaultPromise.java:615)
		at io.netty.util.concurrent.DefaultPromise.setFailure0(DefaultPromise.java:608)
		at io.netty.util.concurrent.DefaultPromise.tryFailure(DefaultPromise.java:117)
		at io.netty.channel.nio.AbstractNioChannel$AbstractNioUnsafe.fulfillConnectPromise(AbstractNioChannel.java:321)
		at io.netty.channel.nio.AbstractNioChannel$AbstractNioUnsafe.finishConnect(AbstractNioChannel.java:337)
		at io.netty.channel.nio.NioEventLoop.processSelectedKey(NioEventLoop.java:688)
		at io.netty.channel.nio.NioEventLoop.processSelectedKeysOptimized(NioEventLoop.java:635)
		at io.netty.channel.nio.NioEventLoop.processSelectedKeys(NioEventLoop.java:552)
		at io.netty.channel.nio.NioEventLoop.run(NioEventLoop.java:514)
		... 4 common frames omitted
	Caused by: io.netty.channel.AbstractChannel$AnnotatedNoRouteToHostException: No route to host: no further information: /************:7002
	Caused by: java.net.NoRouteToHostException: No route to host: no further information
		at sun.nio.ch.SocketChannelImpl.checkConnect(Native Method)
		at sun.nio.ch.SocketChannelImpl.finishConnect(SocketChannelImpl.java:717)
		at io.netty.channel.socket.nio.NioSocketChannel.doFinishConnect(NioSocketChannel.java:330)
		at io.netty.channel.nio.AbstractNioChannel$AbstractNioUnsafe.finishConnect(AbstractNioChannel.java:334)
		at io.netty.channel.nio.NioEventLoop.processSelectedKey(NioEventLoop.java:688)
		at io.netty.channel.nio.NioEventLoop.processSelectedKeysOptimized(NioEventLoop.java:635)
		at io.netty.channel.nio.NioEventLoop.processSelectedKeys(NioEventLoop.java:552)
		at io.netty.channel.nio.NioEventLoop.run(NioEventLoop.java:514)
		at io.netty.util.concurrent.SingleThreadEventExecutor$6.run(SingleThreadEventExecutor.java:1050)
		at io.netty.util.internal.ThreadExecutorMap$2.run(ThreadExecutorMap.java:74)
		at io.netty.util.concurrent.FastThreadLocalRunnable.run(FastThreadLocalRunnable.java:30)
		at java.lang.Thread.run(Thread.java:748)
	Suppressed: io.lettuce.core.RedisConnectionException: Unable to connect to [************:7004]: No route to host: no further information: /************:7004
		at io.lettuce.core.cluster.topology.ClusterTopologyRefresh.lambda$getConnections$0(ClusterTopologyRefresh.java:290)
		at java.util.concurrent.CompletableFuture.uniWhenComplete(CompletableFuture.java:760)
		at java.util.concurrent.CompletableFuture$UniWhenComplete.tryFire(CompletableFuture.java:736)
		at java.util.concurrent.CompletableFuture.postComplete(CompletableFuture.java:474)
		at java.util.concurrent.CompletableFuture.completeExceptionally(CompletableFuture.java:1977)
		at io.lettuce.core.AbstractRedisClient.lambda$initializeChannelAsync0$4(AbstractRedisClient.java:329)
		at io.netty.util.concurrent.DefaultPromise.notifyListener0(DefaultPromise.java:577)
		at io.netty.util.concurrent.DefaultPromise.notifyListeners0(DefaultPromise.java:570)
		at io.netty.util.concurrent.DefaultPromise.notifyListenersNow(DefaultPromise.java:549)
		at io.netty.util.concurrent.DefaultPromise.notifyListeners(DefaultPromise.java:490)
		at io.netty.util.concurrent.DefaultPromise.setValue0(DefaultPromise.java:615)
		at io.netty.util.concurrent.DefaultPromise.setFailure0(DefaultPromise.java:608)
		at io.netty.util.concurrent.DefaultPromise.tryFailure(DefaultPromise.java:117)
		at io.netty.channel.nio.AbstractNioChannel$AbstractNioUnsafe.fulfillConnectPromise(AbstractNioChannel.java:321)
		at io.netty.channel.nio.AbstractNioChannel$AbstractNioUnsafe.finishConnect(AbstractNioChannel.java:337)
		at io.netty.channel.nio.NioEventLoop.processSelectedKey(NioEventLoop.java:688)
		at io.netty.channel.nio.NioEventLoop.processSelectedKeysOptimized(NioEventLoop.java:635)
		at io.netty.channel.nio.NioEventLoop.processSelectedKeys(NioEventLoop.java:552)
		at io.netty.channel.nio.NioEventLoop.run(NioEventLoop.java:514)
		... 4 common frames omitted
	Caused by: io.netty.channel.AbstractChannel$AnnotatedNoRouteToHostException: No route to host: no further information: /************:7004
	Caused by: java.net.NoRouteToHostException: No route to host: no further information
		at sun.nio.ch.SocketChannelImpl.checkConnect(Native Method)
		at sun.nio.ch.SocketChannelImpl.finishConnect(SocketChannelImpl.java:717)
		at io.netty.channel.socket.nio.NioSocketChannel.doFinishConnect(NioSocketChannel.java:330)
		at io.netty.channel.nio.AbstractNioChannel$AbstractNioUnsafe.finishConnect(AbstractNioChannel.java:334)
		at io.netty.channel.nio.NioEventLoop.processSelectedKey(NioEventLoop.java:688)
		at io.netty.channel.nio.NioEventLoop.processSelectedKeysOptimized(NioEventLoop.java:635)
		at io.netty.channel.nio.NioEventLoop.processSelectedKeys(NioEventLoop.java:552)
		at io.netty.channel.nio.NioEventLoop.run(NioEventLoop.java:514)
		at io.netty.util.concurrent.SingleThreadEventExecutor$6.run(SingleThreadEventExecutor.java:1050)
		at io.netty.util.internal.ThreadExecutorMap$2.run(ThreadExecutorMap.java:74)
		at io.netty.util.concurrent.FastThreadLocalRunnable.run(FastThreadLocalRunnable.java:30)
		at java.lang.Thread.run(Thread.java:748)
Caused by: io.lettuce.core.RedisConnectionException: Unable to connect to [************:7006]: No route to host: no further information: /************:7006
	at io.lettuce.core.cluster.topology.ClusterTopologyRefresh.lambda$getConnections$0(ClusterTopologyRefresh.java:290)
	at java.util.concurrent.CompletableFuture.uniWhenComplete(CompletableFuture.java:760)
	at java.util.concurrent.CompletableFuture$UniWhenComplete.tryFire(CompletableFuture.java:736)
	at java.util.concurrent.CompletableFuture.postComplete(CompletableFuture.java:474)
	at java.util.concurrent.CompletableFuture.completeExceptionally(CompletableFuture.java:1977)
	at io.lettuce.core.AbstractRedisClient.lambda$initializeChannelAsync0$4(AbstractRedisClient.java:329)
	at io.netty.util.concurrent.DefaultPromise.notifyListener0(DefaultPromise.java:577)
	at io.netty.util.concurrent.DefaultPromise.notifyListeners0(DefaultPromise.java:570)
	at io.netty.util.concurrent.DefaultPromise.notifyListenersNow(DefaultPromise.java:549)
	at io.netty.util.concurrent.DefaultPromise.notifyListeners(DefaultPromise.java:490)
	at io.netty.util.concurrent.DefaultPromise.setValue0(DefaultPromise.java:615)
	at io.netty.util.concurrent.DefaultPromise.setFailure0(DefaultPromise.java:608)
	at io.netty.util.concurrent.DefaultPromise.tryFailure(DefaultPromise.java:117)
	at io.netty.channel.nio.AbstractNioChannel$AbstractNioUnsafe.fulfillConnectPromise(AbstractNioChannel.java:321)
	at io.netty.channel.nio.AbstractNioChannel$AbstractNioUnsafe.finishConnect(AbstractNioChannel.java:337)
	at io.netty.channel.nio.NioEventLoop.processSelectedKey(NioEventLoop.java:688)
	at io.netty.channel.nio.NioEventLoop.processSelectedKeysOptimized(NioEventLoop.java:635)
	at io.netty.channel.nio.NioEventLoop.processSelectedKeys(NioEventLoop.java:552)
	at io.netty.channel.nio.NioEventLoop.run(NioEventLoop.java:514)
	... 4 common frames omitted
Caused by: io.netty.channel.AbstractChannel$AnnotatedNoRouteToHostException: No route to host: no further information: /************:7006
Caused by: java.net.NoRouteToHostException: No route to host: no further information
	at sun.nio.ch.SocketChannelImpl.checkConnect(Native Method)
	at sun.nio.ch.SocketChannelImpl.finishConnect(SocketChannelImpl.java:717)
	at io.netty.channel.socket.nio.NioSocketChannel.doFinishConnect(NioSocketChannel.java:330)
	at io.netty.channel.nio.AbstractNioChannel$AbstractNioUnsafe.finishConnect(AbstractNioChannel.java:334)
	at io.netty.channel.nio.NioEventLoop.processSelectedKey(NioEventLoop.java:688)
	at io.netty.channel.nio.NioEventLoop.processSelectedKeysOptimized(NioEventLoop.java:635)
	at io.netty.channel.nio.NioEventLoop.processSelectedKeys(NioEventLoop.java:552)
	at io.netty.channel.nio.NioEventLoop.run(NioEventLoop.java:514)
	at io.netty.util.concurrent.SingleThreadEventExecutor$6.run(SingleThreadEventExecutor.java:1050)
	at io.netty.util.internal.ThreadExecutorMap$2.run(ThreadExecutorMap.java:74)
	at io.netty.util.concurrent.FastThreadLocalRunnable.run(FastThreadLocalRunnable.java:30)
	at java.lang.Thread.run(Thread.java:748)
2025-05-29 02:06:53.366 [AMQP Connection ************:5672] WARN  com.rabbitmq.client.impl.ForgivingExceptionHandler.log Line:115 - An unexpected connection driver error occured (Exception message: Connection reset)
2025-05-29 02:06:54.354 [AMQP Connection ************:5672] WARN  com.rabbitmq.client.impl.ForgivingExceptionHandler.log Line:115 - An unexpected connection driver error occured (Exception message: Connection reset)
2025-05-29 02:06:54.431 [AMQP Connection ************:5672] WARN  com.rabbitmq.client.impl.ForgivingExceptionHandler.log Line:115 - An unexpected connection driver error occured (Exception message: Connection reset)
2025-05-29 02:06:55.372 [AMQP Connection ************:5672] WARN  com.rabbitmq.client.impl.ForgivingExceptionHandler.log Line:115 - An unexpected connection driver error occured (Exception message: Connection reset)
2025-05-29 02:10:03.018 [pool-14-thread-6] WARN  com.simbest.boot.base.repository.CustomDynamicWhere.queryForList Line:50 - 自定义查询SQL输出为：【select * from US_TODO_MODEL t  where t.send_flag = 0 and t.work_flag = 0 and t.enabled =1】
2025-05-29 02:47:25.384 [AMQP Connection ************:5672] WARN  com.rabbitmq.client.impl.ForgivingExceptionHandler.log Line:115 - An unexpected connection driver error occured (Exception message: Connection reset)
2025-05-29 02:47:26.410 [AMQP Connection ************:5672] WARN  com.rabbitmq.client.impl.ForgivingExceptionHandler.log Line:115 - An unexpected connection driver error occured (Exception message: Connection reset)
2025-05-29 02:47:26.503 [AMQP Connection ************:5672] WARN  com.rabbitmq.client.impl.ForgivingExceptionHandler.log Line:115 - An unexpected connection driver error occured (Exception message: Connection reset)
2025-05-29 02:47:27.406 [AMQP Connection ************:5672] WARN  com.rabbitmq.client.impl.ForgivingExceptionHandler.log Line:115 - An unexpected connection driver error occured (Exception message: Connection reset)
2025-05-29 03:28:26.390 [AMQP Connection ************:5672] WARN  com.rabbitmq.client.impl.ForgivingExceptionHandler.log Line:115 - An unexpected connection driver error occured (Exception message: Connection reset)
2025-05-29 03:28:27.423 [AMQP Connection ************:5672] WARN  com.rabbitmq.client.impl.ForgivingExceptionHandler.log Line:115 - An unexpected connection driver error occured (Exception message: Connection reset)
2025-05-29 03:28:27.502 [AMQP Connection ************:5672] WARN  com.rabbitmq.client.impl.ForgivingExceptionHandler.log Line:115 - An unexpected connection driver error occured (Exception message: Connection reset)
2025-05-29 03:28:28.398 [AMQP Connection ************:5672] WARN  com.rabbitmq.client.impl.ForgivingExceptionHandler.log Line:115 - An unexpected connection driver error occured (Exception message: Connection reset)
2025-05-29 04:08:57.363 [AMQP Connection ************:5672] WARN  com.rabbitmq.client.impl.ForgivingExceptionHandler.log Line:115 - An unexpected connection driver error occured (Exception message: Connection reset)
2025-05-29 04:08:58.366 [AMQP Connection ************:5672] WARN  com.rabbitmq.client.impl.ForgivingExceptionHandler.log Line:115 - An unexpected connection driver error occured (Exception message: Connection reset)
2025-05-29 04:08:58.473 [AMQP Connection ************:5672] WARN  com.rabbitmq.client.impl.ForgivingExceptionHandler.log Line:115 - An unexpected connection driver error occured (Exception message: Connection reset)
2025-05-29 04:08:59.367 [AMQP Connection ************:5672] WARN  com.rabbitmq.client.impl.ForgivingExceptionHandler.log Line:115 - An unexpected connection driver error occured (Exception message: Connection reset)
2025-05-29 04:49:28.341 [AMQP Connection ************:5672] WARN  com.rabbitmq.client.impl.ForgivingExceptionHandler.log Line:115 - An unexpected connection driver error occured (Exception message: Connection reset)
2025-05-29 04:49:29.346 [AMQP Connection ************:5672] WARN  com.rabbitmq.client.impl.ForgivingExceptionHandler.log Line:115 - An unexpected connection driver error occured (Exception message: Connection reset)
2025-05-29 04:49:29.489 [AMQP Connection ************:5672] WARN  com.rabbitmq.client.impl.ForgivingExceptionHandler.log Line:115 - An unexpected connection driver error occured (Exception message: Connection reset)
2025-05-29 04:49:30.341 [AMQP Connection ************:5672] WARN  com.rabbitmq.client.impl.ForgivingExceptionHandler.log Line:115 - An unexpected connection driver error occured (Exception message: Connection reset)
2025-05-29 05:05:00.012 [pool-14-thread-2] WARN  com.simbest.boot.base.repository.CustomDynamicWhere.queryForList Line:50 - 自定义查询SQL输出为：【select * from US_TODO_MODEL t  where t.send_flag = 0 and t.work_flag = 0 and t.enabled =1】
2025-05-29 05:30:29.814 [AMQP Connection ************:5672] WARN  com.rabbitmq.client.impl.ForgivingExceptionHandler.log Line:115 - An unexpected connection driver error occured (Exception message: Connection reset)
2025-05-29 05:30:30.768 [AMQP Connection ************:5672] WARN  com.rabbitmq.client.impl.ForgivingExceptionHandler.log Line:115 - An unexpected connection driver error occured (Exception message: Connection reset)
2025-05-29 05:30:30.847 [AMQP Connection ************:5672] WARN  com.rabbitmq.client.impl.ForgivingExceptionHandler.log Line:115 - An unexpected connection driver error occured (Exception message: Connection reset)
2025-05-29 05:30:31.765 [AMQP Connection ************:5672] WARN  com.rabbitmq.client.impl.ForgivingExceptionHandler.log Line:115 - An unexpected connection driver error occured (Exception message: Connection reset)
2025-05-29 06:10:28.597 [lettuce-nioEventLoop-4-6] WARN  io.lettuce.core.protocol.ConnectionWatchdog.log Line:107 - Cannot reconnect to [************:7004]: No route to host: no further information: /************:7004
io.netty.channel.AbstractChannel$AnnotatedNoRouteToHostException: No route to host: no further information: /************:7004
Caused by: java.net.NoRouteToHostException: No route to host: no further information
	at sun.nio.ch.SocketChannelImpl.checkConnect(Native Method)
	at sun.nio.ch.SocketChannelImpl.finishConnect(SocketChannelImpl.java:717)
	at io.netty.channel.socket.nio.NioSocketChannel.doFinishConnect(NioSocketChannel.java:330)
	at io.netty.channel.nio.AbstractNioChannel$AbstractNioUnsafe.finishConnect(AbstractNioChannel.java:334)
	at io.netty.channel.nio.NioEventLoop.processSelectedKey(NioEventLoop.java:688)
	at io.netty.channel.nio.NioEventLoop.processSelectedKeysOptimized(NioEventLoop.java:635)
	at io.netty.channel.nio.NioEventLoop.processSelectedKeys(NioEventLoop.java:552)
	at io.netty.channel.nio.NioEventLoop.run(NioEventLoop.java:514)
	at io.netty.util.concurrent.SingleThreadEventExecutor$6.run(SingleThreadEventExecutor.java:1050)
	at io.netty.util.internal.ThreadExecutorMap$2.run(ThreadExecutorMap.java:74)
	at io.netty.util.concurrent.FastThreadLocalRunnable.run(FastThreadLocalRunnable.java:30)
	at java.lang.Thread.run(Thread.java:748)
2025-05-29 06:10:29.429 [lettuce-nioEventLoop-4-11] WARN  io.lettuce.core.cluster.topology.ClusterTopologyRefresh.lambda$getConnections$0 Line:284 - Unable to connect to [************:7002]: No route to host: no further information: /************:7002
2025-05-29 06:10:29.429 [lettuce-nioEventLoop-4-1] WARN  io.lettuce.core.cluster.topology.ClusterTopologyRefresh.lambda$getConnections$0 Line:284 - Unable to connect to [************:7004]: No route to host: no further information: /************:7004
2025-05-29 06:10:29.429 [lettuce-nioEventLoop-4-12] WARN  io.lettuce.core.cluster.topology.ClusterTopologyRefresh.lambda$getConnections$0 Line:284 - Unable to connect to [************:7003]: No route to host: no further information: /************:7003
2025-05-29 06:10:29.429 [lettuce-nioEventLoop-4-10] WARN  io.lettuce.core.cluster.topology.ClusterTopologyRefresh.lambda$getConnections$0 Line:284 - Unable to connect to [************:7001]: No route to host: no further information: /************:7001
2025-05-29 06:10:29.429 [lettuce-nioEventLoop-4-2] WARN  io.lettuce.core.cluster.topology.ClusterTopologyRefresh.lambda$getConnections$0 Line:284 - Unable to connect to [************:7005]: No route to host: no further information: /************:7005
2025-05-29 06:10:29.458 [lettuce-nioEventLoop-4-3] WARN  io.lettuce.core.cluster.topology.ClusterTopologyRefresh.lambda$getConnections$0 Line:284 - Unable to connect to [************:7006]: No route to host: no further information: /************:7006
2025-05-29 06:10:29.460 [lettuce-nioEventLoop-4-5] WARN  io.lettuce.core.cluster.topology.ClusterTopologyRefresh.lambda$getConnections$0 Line:284 - Unable to connect to [************:7004]: No route to host: no further information: /************:7004
2025-05-29 06:10:29.460 [lettuce-nioEventLoop-4-6] WARN  io.lettuce.core.cluster.topology.ClusterTopologyRefresh.lambda$getConnections$0 Line:284 - Unable to connect to [************:7005]: No route to host: no further information: /************:7005
2025-05-29 06:10:29.460 [lettuce-nioEventLoop-4-4] WARN  io.lettuce.core.cluster.topology.ClusterTopologyRefresh.lambda$getConnections$0 Line:284 - Unable to connect to [************:7006]: No route to host: no further information: /************:7006
2025-05-29 06:10:29.460 [lettuce-nioEventLoop-4-7] WARN  io.lettuce.core.cluster.topology.ClusterTopologyRefresh.lambda$getConnections$0 Line:284 - Unable to connect to [************:7002]: No route to host: no further information: /************:7002
2025-05-29 06:10:29.460 [lettuce-nioEventLoop-4-8] WARN  io.lettuce.core.cluster.topology.ClusterTopologyRefresh.lambda$getConnections$0 Line:284 - Unable to connect to [************:7003]: No route to host: no further information: /************:7003
2025-05-29 06:10:29.460 [lettuce-nioEventLoop-4-9] WARN  io.lettuce.core.cluster.topology.ClusterTopologyRefresh.lambda$getConnections$0 Line:284 - Unable to connect to [************:7001]: No route to host: no further information: /************:7001
2025-05-29 06:10:29.480 [lettuce-eventExecutorLoop-1-2] WARN  io.lettuce.core.cluster.ClusterTopologyRefreshScheduler.doRun Line:258 - Cannot refresh Redis Cluster topology
io.lettuce.core.RedisException: Cannot retrieve initial cluster partitions from initial URIs [RedisURI [host='************', port=7006], RedisURI [host='************', port=7004], RedisURI [host='************', port=7005], RedisURI [host='************', port=7002], RedisURI [host='************', port=7003], RedisURI [host='************', port=7001]]
	at io.lettuce.core.cluster.RedisClusterClient.loadPartitions(RedisClusterClient.java:848)
	at io.lettuce.core.cluster.RedisClusterClient.reloadPartitions(RedisClusterClient.java:784)
	at io.lettuce.core.cluster.ClusterTopologyRefreshScheduler$ClusterTopologyRefreshTask.doRun(ClusterTopologyRefreshScheduler.java:256)
	at io.lettuce.core.cluster.ClusterTopologyRefreshScheduler$ClusterTopologyRefreshTask.run(ClusterTopologyRefreshScheduler.java:236)
	at io.netty.util.concurrent.PromiseTask.runTask(PromiseTask.java:98)
	at io.netty.util.concurrent.PromiseTask.run(PromiseTask.java:106)
	at io.netty.util.concurrent.DefaultEventExecutor.run(DefaultEventExecutor.java:66)
	at io.netty.util.concurrent.SingleThreadEventExecutor$6.run(SingleThreadEventExecutor.java:1050)
	at io.netty.util.internal.ThreadExecutorMap$2.run(ThreadExecutorMap.java:74)
	at io.netty.util.concurrent.FastThreadLocalRunnable.run(FastThreadLocalRunnable.java:30)
	at java.lang.Thread.run(Thread.java:748)
	Suppressed: io.lettuce.core.RedisConnectionException: Unable to establish a connection to Redis Cluster at [RedisURI [host='************', port=7001], RedisURI [host='************', port=7002], RedisURI [host='************', port=7003], RedisURI [host='************', port=7004], RedisURI [host='************', port=7005], RedisURI [host='************', port=7006]]
		at io.lettuce.core.cluster.topology.AsyncConnections.get(AsyncConnections.java:89)
		at io.lettuce.core.cluster.topology.ClusterTopologyRefresh.loadViews(ClusterTopologyRefresh.java:76)
		at io.lettuce.core.cluster.RedisClusterClient.doLoadPartitions(RedisClusterClient.java:865)
		at io.lettuce.core.cluster.RedisClusterClient.loadPartitions(RedisClusterClient.java:838)
		... 10 common frames omitted
		Suppressed: io.lettuce.core.RedisConnectionException: Unable to connect to [************:7004]: No route to host: no further information: /************:7004
			at io.lettuce.core.cluster.topology.ClusterTopologyRefresh.lambda$getConnections$0(ClusterTopologyRefresh.java:290)
			at java.util.concurrent.CompletableFuture.uniWhenComplete(CompletableFuture.java:760)
			at java.util.concurrent.CompletableFuture$UniWhenComplete.tryFire(CompletableFuture.java:736)
			at java.util.concurrent.CompletableFuture.postComplete(CompletableFuture.java:474)
			at java.util.concurrent.CompletableFuture.completeExceptionally(CompletableFuture.java:1977)
			at io.lettuce.core.AbstractRedisClient.lambda$initializeChannelAsync0$4(AbstractRedisClient.java:329)
			at io.netty.util.concurrent.DefaultPromise.notifyListener0(DefaultPromise.java:577)
			at io.netty.util.concurrent.DefaultPromise.notifyListeners0(DefaultPromise.java:570)
			at io.netty.util.concurrent.DefaultPromise.notifyListenersNow(DefaultPromise.java:549)
			at io.netty.util.concurrent.DefaultPromise.notifyListeners(DefaultPromise.java:490)
			at io.netty.util.concurrent.DefaultPromise.setValue0(DefaultPromise.java:615)
			at io.netty.util.concurrent.DefaultPromise.setFailure0(DefaultPromise.java:608)
			at io.netty.util.concurrent.DefaultPromise.tryFailure(DefaultPromise.java:117)
			at io.netty.channel.nio.AbstractNioChannel$AbstractNioUnsafe.fulfillConnectPromise(AbstractNioChannel.java:321)
			at io.netty.channel.nio.AbstractNioChannel$AbstractNioUnsafe.finishConnect(AbstractNioChannel.java:337)
			at io.netty.channel.nio.NioEventLoop.processSelectedKey(NioEventLoop.java:688)
			at io.netty.channel.nio.NioEventLoop.processSelectedKeysOptimized(NioEventLoop.java:635)
			at io.netty.channel.nio.NioEventLoop.processSelectedKeys(NioEventLoop.java:552)
			at io.netty.channel.nio.NioEventLoop.run(NioEventLoop.java:514)
			... 4 common frames omitted
		Caused by: io.netty.channel.AbstractChannel$AnnotatedNoRouteToHostException: No route to host: no further information: /************:7004
		Caused by: java.net.NoRouteToHostException: No route to host: no further information
			at sun.nio.ch.SocketChannelImpl.checkConnect(Native Method)
			at sun.nio.ch.SocketChannelImpl.finishConnect(SocketChannelImpl.java:717)
			at io.netty.channel.socket.nio.NioSocketChannel.doFinishConnect(NioSocketChannel.java:330)
			at io.netty.channel.nio.AbstractNioChannel$AbstractNioUnsafe.finishConnect(AbstractNioChannel.java:334)
			at io.netty.channel.nio.NioEventLoop.processSelectedKey(NioEventLoop.java:688)
			at io.netty.channel.nio.NioEventLoop.processSelectedKeysOptimized(NioEventLoop.java:635)
			at io.netty.channel.nio.NioEventLoop.processSelectedKeys(NioEventLoop.java:552)
			at io.netty.channel.nio.NioEventLoop.run(NioEventLoop.java:514)
			at io.netty.util.concurrent.SingleThreadEventExecutor$6.run(SingleThreadEventExecutor.java:1050)
			at io.netty.util.internal.ThreadExecutorMap$2.run(ThreadExecutorMap.java:74)
			at io.netty.util.concurrent.FastThreadLocalRunnable.run(FastThreadLocalRunnable.java:30)
			at java.lang.Thread.run(Thread.java:748)
		Suppressed: io.lettuce.core.RedisConnectionException: Unable to connect to [************:7003]: No route to host: no further information: /************:7003
			at io.lettuce.core.cluster.topology.ClusterTopologyRefresh.lambda$getConnections$0(ClusterTopologyRefresh.java:290)
			at java.util.concurrent.CompletableFuture.uniWhenComplete(CompletableFuture.java:760)
			at java.util.concurrent.CompletableFuture$UniWhenComplete.tryFire(CompletableFuture.java:736)
			at java.util.concurrent.CompletableFuture.postComplete(CompletableFuture.java:474)
			at java.util.concurrent.CompletableFuture.completeExceptionally(CompletableFuture.java:1977)
			at io.lettuce.core.AbstractRedisClient.lambda$initializeChannelAsync0$4(AbstractRedisClient.java:329)
			at io.netty.util.concurrent.DefaultPromise.notifyListener0(DefaultPromise.java:577)
			at io.netty.util.concurrent.DefaultPromise.notifyListeners0(DefaultPromise.java:570)
			at io.netty.util.concurrent.DefaultPromise.notifyListenersNow(DefaultPromise.java:549)
			at io.netty.util.concurrent.DefaultPromise.notifyListeners(DefaultPromise.java:490)
			at io.netty.util.concurrent.DefaultPromise.setValue0(DefaultPromise.java:615)
			at io.netty.util.concurrent.DefaultPromise.setFailure0(DefaultPromise.java:608)
			at io.netty.util.concurrent.DefaultPromise.tryFailure(DefaultPromise.java:117)
			at io.netty.channel.nio.AbstractNioChannel$AbstractNioUnsafe.fulfillConnectPromise(AbstractNioChannel.java:321)
			at io.netty.channel.nio.AbstractNioChannel$AbstractNioUnsafe.finishConnect(AbstractNioChannel.java:337)
			at io.netty.channel.nio.NioEventLoop.processSelectedKey(NioEventLoop.java:688)
			at io.netty.channel.nio.NioEventLoop.processSelectedKeysOptimized(NioEventLoop.java:635)
			at io.netty.channel.nio.NioEventLoop.processSelectedKeys(NioEventLoop.java:552)
			at io.netty.channel.nio.NioEventLoop.run(NioEventLoop.java:514)
			... 4 common frames omitted
		Caused by: io.netty.channel.AbstractChannel$AnnotatedNoRouteToHostException: No route to host: no further information: /************:7003
		Caused by: java.net.NoRouteToHostException: No route to host: no further information
			at sun.nio.ch.SocketChannelImpl.checkConnect(Native Method)
			at sun.nio.ch.SocketChannelImpl.finishConnect(SocketChannelImpl.java:717)
			at io.netty.channel.socket.nio.NioSocketChannel.doFinishConnect(NioSocketChannel.java:330)
			at io.netty.channel.nio.AbstractNioChannel$AbstractNioUnsafe.finishConnect(AbstractNioChannel.java:334)
			at io.netty.channel.nio.NioEventLoop.processSelectedKey(NioEventLoop.java:688)
			at io.netty.channel.nio.NioEventLoop.processSelectedKeysOptimized(NioEventLoop.java:635)
			at io.netty.channel.nio.NioEventLoop.processSelectedKeys(NioEventLoop.java:552)
			at io.netty.channel.nio.NioEventLoop.run(NioEventLoop.java:514)
			at io.netty.util.concurrent.SingleThreadEventExecutor$6.run(SingleThreadEventExecutor.java:1050)
			at io.netty.util.internal.ThreadExecutorMap$2.run(ThreadExecutorMap.java:74)
			at io.netty.util.concurrent.FastThreadLocalRunnable.run(FastThreadLocalRunnable.java:30)
			at java.lang.Thread.run(Thread.java:748)
		Suppressed: io.lettuce.core.RedisConnectionException: Unable to connect to [************:7001]: No route to host: no further information: /************:7001
			at io.lettuce.core.cluster.topology.ClusterTopologyRefresh.lambda$getConnections$0(ClusterTopologyRefresh.java:290)
			at java.util.concurrent.CompletableFuture.uniWhenComplete(CompletableFuture.java:760)
			at java.util.concurrent.CompletableFuture$UniWhenComplete.tryFire(CompletableFuture.java:736)
			at java.util.concurrent.CompletableFuture.postComplete(CompletableFuture.java:474)
			at java.util.concurrent.CompletableFuture.completeExceptionally(CompletableFuture.java:1977)
			at io.lettuce.core.AbstractRedisClient.lambda$initializeChannelAsync0$4(AbstractRedisClient.java:329)
			at io.netty.util.concurrent.DefaultPromise.notifyListener0(DefaultPromise.java:577)
			at io.netty.util.concurrent.DefaultPromise.notifyListeners0(DefaultPromise.java:570)
			at io.netty.util.concurrent.DefaultPromise.notifyListenersNow(DefaultPromise.java:549)
			at io.netty.util.concurrent.DefaultPromise.notifyListeners(DefaultPromise.java:490)
			at io.netty.util.concurrent.DefaultPromise.setValue0(DefaultPromise.java:615)
			at io.netty.util.concurrent.DefaultPromise.setFailure0(DefaultPromise.java:608)
			at io.netty.util.concurrent.DefaultPromise.tryFailure(DefaultPromise.java:117)
			at io.netty.channel.nio.AbstractNioChannel$AbstractNioUnsafe.fulfillConnectPromise(AbstractNioChannel.java:321)
			at io.netty.channel.nio.AbstractNioChannel$AbstractNioUnsafe.finishConnect(AbstractNioChannel.java:337)
			at io.netty.channel.nio.NioEventLoop.processSelectedKey(NioEventLoop.java:688)
			at io.netty.channel.nio.NioEventLoop.processSelectedKeysOptimized(NioEventLoop.java:635)
			at io.netty.channel.nio.NioEventLoop.processSelectedKeys(NioEventLoop.java:552)
			at io.netty.channel.nio.NioEventLoop.run(NioEventLoop.java:514)
			... 4 common frames omitted
		Caused by: io.netty.channel.AbstractChannel$AnnotatedNoRouteToHostException: No route to host: no further information: /************:7001
		Caused by: java.net.NoRouteToHostException: No route to host: no further information
			at sun.nio.ch.SocketChannelImpl.checkConnect(Native Method)
			at sun.nio.ch.SocketChannelImpl.finishConnect(SocketChannelImpl.java:717)
			at io.netty.channel.socket.nio.NioSocketChannel.doFinishConnect(NioSocketChannel.java:330)
			at io.netty.channel.nio.AbstractNioChannel$AbstractNioUnsafe.finishConnect(AbstractNioChannel.java:334)
			at io.netty.channel.nio.NioEventLoop.processSelectedKey(NioEventLoop.java:688)
			at io.netty.channel.nio.NioEventLoop.processSelectedKeysOptimized(NioEventLoop.java:635)
			at io.netty.channel.nio.NioEventLoop.processSelectedKeys(NioEventLoop.java:552)
			at io.netty.channel.nio.NioEventLoop.run(NioEventLoop.java:514)
			at io.netty.util.concurrent.SingleThreadEventExecutor$6.run(SingleThreadEventExecutor.java:1050)
			at io.netty.util.internal.ThreadExecutorMap$2.run(ThreadExecutorMap.java:74)
			at io.netty.util.concurrent.FastThreadLocalRunnable.run(FastThreadLocalRunnable.java:30)
			at java.lang.Thread.run(Thread.java:748)
		Suppressed: io.lettuce.core.RedisConnectionException: Unable to connect to [************:7005]: No route to host: no further information: /************:7005
			at io.lettuce.core.cluster.topology.ClusterTopologyRefresh.lambda$getConnections$0(ClusterTopologyRefresh.java:290)
			at java.util.concurrent.CompletableFuture.uniWhenComplete(CompletableFuture.java:760)
			at java.util.concurrent.CompletableFuture$UniWhenComplete.tryFire(CompletableFuture.java:736)
			at java.util.concurrent.CompletableFuture.postComplete(CompletableFuture.java:474)
			at java.util.concurrent.CompletableFuture.completeExceptionally(CompletableFuture.java:1977)
			at io.lettuce.core.AbstractRedisClient.lambda$initializeChannelAsync0$4(AbstractRedisClient.java:329)
			at io.netty.util.concurrent.DefaultPromise.notifyListener0(DefaultPromise.java:577)
			at io.netty.util.concurrent.DefaultPromise.notifyListeners0(DefaultPromise.java:570)
			at io.netty.util.concurrent.DefaultPromise.notifyListenersNow(DefaultPromise.java:549)
			at io.netty.util.concurrent.DefaultPromise.notifyListeners(DefaultPromise.java:490)
			at io.netty.util.concurrent.DefaultPromise.setValue0(DefaultPromise.java:615)
			at io.netty.util.concurrent.DefaultPromise.setFailure0(DefaultPromise.java:608)
			at io.netty.util.concurrent.DefaultPromise.tryFailure(DefaultPromise.java:117)
			at io.netty.channel.nio.AbstractNioChannel$AbstractNioUnsafe.fulfillConnectPromise(AbstractNioChannel.java:321)
			at io.netty.channel.nio.AbstractNioChannel$AbstractNioUnsafe.finishConnect(AbstractNioChannel.java:337)
			at io.netty.channel.nio.NioEventLoop.processSelectedKey(NioEventLoop.java:688)
			at io.netty.channel.nio.NioEventLoop.processSelectedKeysOptimized(NioEventLoop.java:635)
			at io.netty.channel.nio.NioEventLoop.processSelectedKeys(NioEventLoop.java:552)
			at io.netty.channel.nio.NioEventLoop.run(NioEventLoop.java:514)
			... 4 common frames omitted
		Caused by: io.netty.channel.AbstractChannel$AnnotatedNoRouteToHostException: No route to host: no further information: /************:7005
		Caused by: java.net.NoRouteToHostException: No route to host: no further information
			at sun.nio.ch.SocketChannelImpl.checkConnect(Native Method)
			at sun.nio.ch.SocketChannelImpl.finishConnect(SocketChannelImpl.java:717)
			at io.netty.channel.socket.nio.NioSocketChannel.doFinishConnect(NioSocketChannel.java:330)
			at io.netty.channel.nio.AbstractNioChannel$AbstractNioUnsafe.finishConnect(AbstractNioChannel.java:334)
			at io.netty.channel.nio.NioEventLoop.processSelectedKey(NioEventLoop.java:688)
			at io.netty.channel.nio.NioEventLoop.processSelectedKeysOptimized(NioEventLoop.java:635)
			at io.netty.channel.nio.NioEventLoop.processSelectedKeys(NioEventLoop.java:552)
			at io.netty.channel.nio.NioEventLoop.run(NioEventLoop.java:514)
			at io.netty.util.concurrent.SingleThreadEventExecutor$6.run(SingleThreadEventExecutor.java:1050)
			at io.netty.util.internal.ThreadExecutorMap$2.run(ThreadExecutorMap.java:74)
			at io.netty.util.concurrent.FastThreadLocalRunnable.run(FastThreadLocalRunnable.java:30)
			at java.lang.Thread.run(Thread.java:748)
		Suppressed: io.lettuce.core.RedisConnectionException: Unable to connect to [************:7006]: No route to host: no further information: /************:7006
			at io.lettuce.core.cluster.topology.ClusterTopologyRefresh.lambda$getConnections$0(ClusterTopologyRefresh.java:290)
			at java.util.concurrent.CompletableFuture.uniWhenComplete(CompletableFuture.java:760)
			at java.util.concurrent.CompletableFuture$UniWhenComplete.tryFire(CompletableFuture.java:736)
			at java.util.concurrent.CompletableFuture.postComplete(CompletableFuture.java:474)
			at java.util.concurrent.CompletableFuture.completeExceptionally(CompletableFuture.java:1977)
			at io.lettuce.core.AbstractRedisClient.lambda$initializeChannelAsync0$4(AbstractRedisClient.java:329)
			at io.netty.util.concurrent.DefaultPromise.notifyListener0(DefaultPromise.java:577)
			at io.netty.util.concurrent.DefaultPromise.notifyListeners0(DefaultPromise.java:570)
			at io.netty.util.concurrent.DefaultPromise.notifyListenersNow(DefaultPromise.java:549)
			at io.netty.util.concurrent.DefaultPromise.notifyListeners(DefaultPromise.java:490)
			at io.netty.util.concurrent.DefaultPromise.setValue0(DefaultPromise.java:615)
			at io.netty.util.concurrent.DefaultPromise.setFailure0(DefaultPromise.java:608)
			at io.netty.util.concurrent.DefaultPromise.tryFailure(DefaultPromise.java:117)
			at io.netty.channel.nio.AbstractNioChannel$AbstractNioUnsafe.fulfillConnectPromise(AbstractNioChannel.java:321)
			at io.netty.channel.nio.AbstractNioChannel$AbstractNioUnsafe.finishConnect(AbstractNioChannel.java:337)
			at io.netty.channel.nio.NioEventLoop.processSelectedKey(NioEventLoop.java:688)
			at io.netty.channel.nio.NioEventLoop.processSelectedKeysOptimized(NioEventLoop.java:635)
			at io.netty.channel.nio.NioEventLoop.processSelectedKeys(NioEventLoop.java:552)
			at io.netty.channel.nio.NioEventLoop.run(NioEventLoop.java:514)
			... 4 common frames omitted
		Caused by: io.netty.channel.AbstractChannel$AnnotatedNoRouteToHostException: No route to host: no further information: /************:7006
		Caused by: java.net.NoRouteToHostException: No route to host: no further information
			at sun.nio.ch.SocketChannelImpl.checkConnect(Native Method)
			at sun.nio.ch.SocketChannelImpl.finishConnect(SocketChannelImpl.java:717)
			at io.netty.channel.socket.nio.NioSocketChannel.doFinishConnect(NioSocketChannel.java:330)
			at io.netty.channel.nio.AbstractNioChannel$AbstractNioUnsafe.finishConnect(AbstractNioChannel.java:334)
			at io.netty.channel.nio.NioEventLoop.processSelectedKey(NioEventLoop.java:688)
			at io.netty.channel.nio.NioEventLoop.processSelectedKeysOptimized(NioEventLoop.java:635)
			at io.netty.channel.nio.NioEventLoop.processSelectedKeys(NioEventLoop.java:552)
			at io.netty.channel.nio.NioEventLoop.run(NioEventLoop.java:514)
			at io.netty.util.concurrent.SingleThreadEventExecutor$6.run(SingleThreadEventExecutor.java:1050)
			at io.netty.util.internal.ThreadExecutorMap$2.run(ThreadExecutorMap.java:74)
			at io.netty.util.concurrent.FastThreadLocalRunnable.run(FastThreadLocalRunnable.java:30)
			at java.lang.Thread.run(Thread.java:748)
	Caused by: io.lettuce.core.RedisConnectionException: Unable to connect to [************:7002]: No route to host: no further information: /************:7002
		at io.lettuce.core.cluster.topology.ClusterTopologyRefresh.lambda$getConnections$0(ClusterTopologyRefresh.java:290)
		at java.util.concurrent.CompletableFuture.uniWhenComplete(CompletableFuture.java:760)
		at java.util.concurrent.CompletableFuture$UniWhenComplete.tryFire(CompletableFuture.java:736)
		at java.util.concurrent.CompletableFuture.postComplete(CompletableFuture.java:474)
		at java.util.concurrent.CompletableFuture.completeExceptionally(CompletableFuture.java:1977)
		at io.lettuce.core.AbstractRedisClient.lambda$initializeChannelAsync0$4(AbstractRedisClient.java:329)
		at io.netty.util.concurrent.DefaultPromise.notifyListener0(DefaultPromise.java:577)
		at io.netty.util.concurrent.DefaultPromise.notifyListeners0(DefaultPromise.java:570)
		at io.netty.util.concurrent.DefaultPromise.notifyListenersNow(DefaultPromise.java:549)
		at io.netty.util.concurrent.DefaultPromise.notifyListeners(DefaultPromise.java:490)
		at io.netty.util.concurrent.DefaultPromise.setValue0(DefaultPromise.java:615)
		at io.netty.util.concurrent.DefaultPromise.setFailure0(DefaultPromise.java:608)
		at io.netty.util.concurrent.DefaultPromise.tryFailure(DefaultPromise.java:117)
		at io.netty.channel.nio.AbstractNioChannel$AbstractNioUnsafe.fulfillConnectPromise(AbstractNioChannel.java:321)
		at io.netty.channel.nio.AbstractNioChannel$AbstractNioUnsafe.finishConnect(AbstractNioChannel.java:337)
		at io.netty.channel.nio.NioEventLoop.processSelectedKey(NioEventLoop.java:688)
		at io.netty.channel.nio.NioEventLoop.processSelectedKeysOptimized(NioEventLoop.java:635)
		at io.netty.channel.nio.NioEventLoop.processSelectedKeys(NioEventLoop.java:552)
		at io.netty.channel.nio.NioEventLoop.run(NioEventLoop.java:514)
		... 4 common frames omitted
	Caused by: io.netty.channel.AbstractChannel$AnnotatedNoRouteToHostException: No route to host: no further information: /************:7002
	Caused by: java.net.NoRouteToHostException: No route to host: no further information
		at sun.nio.ch.SocketChannelImpl.checkConnect(Native Method)
		at sun.nio.ch.SocketChannelImpl.finishConnect(SocketChannelImpl.java:717)
		at io.netty.channel.socket.nio.NioSocketChannel.doFinishConnect(NioSocketChannel.java:330)
		at io.netty.channel.nio.AbstractNioChannel$AbstractNioUnsafe.finishConnect(AbstractNioChannel.java:334)
		at io.netty.channel.nio.NioEventLoop.processSelectedKey(NioEventLoop.java:688)
		at io.netty.channel.nio.NioEventLoop.processSelectedKeysOptimized(NioEventLoop.java:635)
		at io.netty.channel.nio.NioEventLoop.processSelectedKeys(NioEventLoop.java:552)
		at io.netty.channel.nio.NioEventLoop.run(NioEventLoop.java:514)
		at io.netty.util.concurrent.SingleThreadEventExecutor$6.run(SingleThreadEventExecutor.java:1050)
		at io.netty.util.internal.ThreadExecutorMap$2.run(ThreadExecutorMap.java:74)
		at io.netty.util.concurrent.FastThreadLocalRunnable.run(FastThreadLocalRunnable.java:30)
		at java.lang.Thread.run(Thread.java:748)
Caused by: io.lettuce.core.RedisConnectionException: Unable to establish a connection to Redis Cluster at [RedisURI [host='************', port=7001], RedisURI [host='************', port=7002], RedisURI [host='************', port=7003], RedisURI [host='************', port=7004], RedisURI [host='************', port=7005], RedisURI [host='************', port=7006]]
	at io.lettuce.core.cluster.topology.AsyncConnections.get(AsyncConnections.java:89)
	at io.lettuce.core.cluster.topology.ClusterTopologyRefresh.loadViews(ClusterTopologyRefresh.java:76)
	at io.lettuce.core.cluster.RedisClusterClient.doLoadPartitions(RedisClusterClient.java:865)
	at io.lettuce.core.cluster.RedisClusterClient.loadPartitions(RedisClusterClient.java:845)
	... 10 common frames omitted
	Suppressed: io.lettuce.core.RedisConnectionException: Unable to connect to [************:7002]: No route to host: no further information: /************:7002
		at io.lettuce.core.cluster.topology.ClusterTopologyRefresh.lambda$getConnections$0(ClusterTopologyRefresh.java:290)
		at java.util.concurrent.CompletableFuture.uniWhenComplete(CompletableFuture.java:760)
		at java.util.concurrent.CompletableFuture$UniWhenComplete.tryFire(CompletableFuture.java:736)
		at java.util.concurrent.CompletableFuture.postComplete(CompletableFuture.java:474)
		at java.util.concurrent.CompletableFuture.completeExceptionally(CompletableFuture.java:1977)
		at io.lettuce.core.AbstractRedisClient.lambda$initializeChannelAsync0$4(AbstractRedisClient.java:329)
		at io.netty.util.concurrent.DefaultPromise.notifyListener0(DefaultPromise.java:577)
		at io.netty.util.concurrent.DefaultPromise.notifyListeners0(DefaultPromise.java:570)
		at io.netty.util.concurrent.DefaultPromise.notifyListenersNow(DefaultPromise.java:549)
		at io.netty.util.concurrent.DefaultPromise.notifyListeners(DefaultPromise.java:490)
		at io.netty.util.concurrent.DefaultPromise.setValue0(DefaultPromise.java:615)
		at io.netty.util.concurrent.DefaultPromise.setFailure0(DefaultPromise.java:608)
		at io.netty.util.concurrent.DefaultPromise.tryFailure(DefaultPromise.java:117)
		at io.netty.channel.nio.AbstractNioChannel$AbstractNioUnsafe.fulfillConnectPromise(AbstractNioChannel.java:321)
		at io.netty.channel.nio.AbstractNioChannel$AbstractNioUnsafe.finishConnect(AbstractNioChannel.java:337)
		at io.netty.channel.nio.NioEventLoop.processSelectedKey(NioEventLoop.java:688)
		at io.netty.channel.nio.NioEventLoop.processSelectedKeysOptimized(NioEventLoop.java:635)
		at io.netty.channel.nio.NioEventLoop.processSelectedKeys(NioEventLoop.java:552)
		at io.netty.channel.nio.NioEventLoop.run(NioEventLoop.java:514)
		... 4 common frames omitted
	Caused by: io.netty.channel.AbstractChannel$AnnotatedNoRouteToHostException: No route to host: no further information: /************:7002
	Caused by: java.net.NoRouteToHostException: No route to host: no further information
		at sun.nio.ch.SocketChannelImpl.checkConnect(Native Method)
		at sun.nio.ch.SocketChannelImpl.finishConnect(SocketChannelImpl.java:717)
		at io.netty.channel.socket.nio.NioSocketChannel.doFinishConnect(NioSocketChannel.java:330)
		at io.netty.channel.nio.AbstractNioChannel$AbstractNioUnsafe.finishConnect(AbstractNioChannel.java:334)
		at io.netty.channel.nio.NioEventLoop.processSelectedKey(NioEventLoop.java:688)
		at io.netty.channel.nio.NioEventLoop.processSelectedKeysOptimized(NioEventLoop.java:635)
		at io.netty.channel.nio.NioEventLoop.processSelectedKeys(NioEventLoop.java:552)
		at io.netty.channel.nio.NioEventLoop.run(NioEventLoop.java:514)
		at io.netty.util.concurrent.SingleThreadEventExecutor$6.run(SingleThreadEventExecutor.java:1050)
		at io.netty.util.internal.ThreadExecutorMap$2.run(ThreadExecutorMap.java:74)
		at io.netty.util.concurrent.FastThreadLocalRunnable.run(FastThreadLocalRunnable.java:30)
		at java.lang.Thread.run(Thread.java:748)
	Suppressed: io.lettuce.core.RedisConnectionException: Unable to connect to [************:7006]: No route to host: no further information: /************:7006
		at io.lettuce.core.cluster.topology.ClusterTopologyRefresh.lambda$getConnections$0(ClusterTopologyRefresh.java:290)
		at java.util.concurrent.CompletableFuture.uniWhenComplete(CompletableFuture.java:760)
		at java.util.concurrent.CompletableFuture$UniWhenComplete.tryFire(CompletableFuture.java:736)
		at java.util.concurrent.CompletableFuture.postComplete(CompletableFuture.java:474)
		at java.util.concurrent.CompletableFuture.completeExceptionally(CompletableFuture.java:1977)
		at io.lettuce.core.AbstractRedisClient.lambda$initializeChannelAsync0$4(AbstractRedisClient.java:329)
		at io.netty.util.concurrent.DefaultPromise.notifyListener0(DefaultPromise.java:577)
		at io.netty.util.concurrent.DefaultPromise.notifyListeners0(DefaultPromise.java:570)
		at io.netty.util.concurrent.DefaultPromise.notifyListenersNow(DefaultPromise.java:549)
		at io.netty.util.concurrent.DefaultPromise.notifyListeners(DefaultPromise.java:490)
		at io.netty.util.concurrent.DefaultPromise.setValue0(DefaultPromise.java:615)
		at io.netty.util.concurrent.DefaultPromise.setFailure0(DefaultPromise.java:608)
		at io.netty.util.concurrent.DefaultPromise.tryFailure(DefaultPromise.java:117)
		at io.netty.channel.nio.AbstractNioChannel$AbstractNioUnsafe.fulfillConnectPromise(AbstractNioChannel.java:321)
		at io.netty.channel.nio.AbstractNioChannel$AbstractNioUnsafe.finishConnect(AbstractNioChannel.java:337)
		at io.netty.channel.nio.NioEventLoop.processSelectedKey(NioEventLoop.java:688)
		at io.netty.channel.nio.NioEventLoop.processSelectedKeysOptimized(NioEventLoop.java:635)
		at io.netty.channel.nio.NioEventLoop.processSelectedKeys(NioEventLoop.java:552)
		at io.netty.channel.nio.NioEventLoop.run(NioEventLoop.java:514)
		... 4 common frames omitted
	Caused by: io.netty.channel.AbstractChannel$AnnotatedNoRouteToHostException: No route to host: no further information: /************:7006
	Caused by: java.net.NoRouteToHostException: No route to host: no further information
		at sun.nio.ch.SocketChannelImpl.checkConnect(Native Method)
		at sun.nio.ch.SocketChannelImpl.finishConnect(SocketChannelImpl.java:717)
		at io.netty.channel.socket.nio.NioSocketChannel.doFinishConnect(NioSocketChannel.java:330)
		at io.netty.channel.nio.AbstractNioChannel$AbstractNioUnsafe.finishConnect(AbstractNioChannel.java:334)
		at io.netty.channel.nio.NioEventLoop.processSelectedKey(NioEventLoop.java:688)
		at io.netty.channel.nio.NioEventLoop.processSelectedKeysOptimized(NioEventLoop.java:635)
		at io.netty.channel.nio.NioEventLoop.processSelectedKeys(NioEventLoop.java:552)
		at io.netty.channel.nio.NioEventLoop.run(NioEventLoop.java:514)
		at io.netty.util.concurrent.SingleThreadEventExecutor$6.run(SingleThreadEventExecutor.java:1050)
		at io.netty.util.internal.ThreadExecutorMap$2.run(ThreadExecutorMap.java:74)
		at io.netty.util.concurrent.FastThreadLocalRunnable.run(FastThreadLocalRunnable.java:30)
		at java.lang.Thread.run(Thread.java:748)
	Suppressed: io.lettuce.core.RedisConnectionException: Unable to connect to [************:7005]: No route to host: no further information: /************:7005
		at io.lettuce.core.cluster.topology.ClusterTopologyRefresh.lambda$getConnections$0(ClusterTopologyRefresh.java:290)
		at java.util.concurrent.CompletableFuture.uniWhenComplete(CompletableFuture.java:760)
		at java.util.concurrent.CompletableFuture$UniWhenComplete.tryFire(CompletableFuture.java:736)
		at java.util.concurrent.CompletableFuture.postComplete(CompletableFuture.java:474)
		at java.util.concurrent.CompletableFuture.completeExceptionally(CompletableFuture.java:1977)
		at io.lettuce.core.AbstractRedisClient.lambda$initializeChannelAsync0$4(AbstractRedisClient.java:329)
		at io.netty.util.concurrent.DefaultPromise.notifyListener0(DefaultPromise.java:577)
		at io.netty.util.concurrent.DefaultPromise.notifyListeners0(DefaultPromise.java:570)
		at io.netty.util.concurrent.DefaultPromise.notifyListenersNow(DefaultPromise.java:549)
		at io.netty.util.concurrent.DefaultPromise.notifyListeners(DefaultPromise.java:490)
		at io.netty.util.concurrent.DefaultPromise.setValue0(DefaultPromise.java:615)
		at io.netty.util.concurrent.DefaultPromise.setFailure0(DefaultPromise.java:608)
		at io.netty.util.concurrent.DefaultPromise.tryFailure(DefaultPromise.java:117)
		at io.netty.channel.nio.AbstractNioChannel$AbstractNioUnsafe.fulfillConnectPromise(AbstractNioChannel.java:321)
		at io.netty.channel.nio.AbstractNioChannel$AbstractNioUnsafe.finishConnect(AbstractNioChannel.java:337)
		at io.netty.channel.nio.NioEventLoop.processSelectedKey(NioEventLoop.java:688)
		at io.netty.channel.nio.NioEventLoop.processSelectedKeysOptimized(NioEventLoop.java:635)
		at io.netty.channel.nio.NioEventLoop.processSelectedKeys(NioEventLoop.java:552)
		at io.netty.channel.nio.NioEventLoop.run(NioEventLoop.java:514)
		... 4 common frames omitted
	Caused by: io.netty.channel.AbstractChannel$AnnotatedNoRouteToHostException: No route to host: no further information: /************:7005
	Caused by: java.net.NoRouteToHostException: No route to host: no further information
		at sun.nio.ch.SocketChannelImpl.checkConnect(Native Method)
		at sun.nio.ch.SocketChannelImpl.finishConnect(SocketChannelImpl.java:717)
		at io.netty.channel.socket.nio.NioSocketChannel.doFinishConnect(NioSocketChannel.java:330)
		at io.netty.channel.nio.AbstractNioChannel$AbstractNioUnsafe.finishConnect(AbstractNioChannel.java:334)
		at io.netty.channel.nio.NioEventLoop.processSelectedKey(NioEventLoop.java:688)
		at io.netty.channel.nio.NioEventLoop.processSelectedKeysOptimized(NioEventLoop.java:635)
		at io.netty.channel.nio.NioEventLoop.processSelectedKeys(NioEventLoop.java:552)
		at io.netty.channel.nio.NioEventLoop.run(NioEventLoop.java:514)
		at io.netty.util.concurrent.SingleThreadEventExecutor$6.run(SingleThreadEventExecutor.java:1050)
		at io.netty.util.internal.ThreadExecutorMap$2.run(ThreadExecutorMap.java:74)
		at io.netty.util.concurrent.FastThreadLocalRunnable.run(FastThreadLocalRunnable.java:30)
		at java.lang.Thread.run(Thread.java:748)
	Suppressed: io.lettuce.core.RedisConnectionException: Unable to connect to [************:7001]: No route to host: no further information: /************:7001
		at io.lettuce.core.cluster.topology.ClusterTopologyRefresh.lambda$getConnections$0(ClusterTopologyRefresh.java:290)
		at java.util.concurrent.CompletableFuture.uniWhenComplete(CompletableFuture.java:760)
		at java.util.concurrent.CompletableFuture$UniWhenComplete.tryFire(CompletableFuture.java:736)
		at java.util.concurrent.CompletableFuture.postComplete(CompletableFuture.java:474)
		at java.util.concurrent.CompletableFuture.completeExceptionally(CompletableFuture.java:1977)
		at io.lettuce.core.AbstractRedisClient.lambda$initializeChannelAsync0$4(AbstractRedisClient.java:329)
		at io.netty.util.concurrent.DefaultPromise.notifyListener0(DefaultPromise.java:577)
		at io.netty.util.concurrent.DefaultPromise.notifyListeners0(DefaultPromise.java:570)
		at io.netty.util.concurrent.DefaultPromise.notifyListenersNow(DefaultPromise.java:549)
		at io.netty.util.concurrent.DefaultPromise.notifyListeners(DefaultPromise.java:490)
		at io.netty.util.concurrent.DefaultPromise.setValue0(DefaultPromise.java:615)
		at io.netty.util.concurrent.DefaultPromise.setFailure0(DefaultPromise.java:608)
		at io.netty.util.concurrent.DefaultPromise.tryFailure(DefaultPromise.java:117)
		at io.netty.channel.nio.AbstractNioChannel$AbstractNioUnsafe.fulfillConnectPromise(AbstractNioChannel.java:321)
		at io.netty.channel.nio.AbstractNioChannel$AbstractNioUnsafe.finishConnect(AbstractNioChannel.java:337)
		at io.netty.channel.nio.NioEventLoop.processSelectedKey(NioEventLoop.java:688)
		at io.netty.channel.nio.NioEventLoop.processSelectedKeysOptimized(NioEventLoop.java:635)
		at io.netty.channel.nio.NioEventLoop.processSelectedKeys(NioEventLoop.java:552)
		at io.netty.channel.nio.NioEventLoop.run(NioEventLoop.java:514)
		... 4 common frames omitted
	Caused by: io.netty.channel.AbstractChannel$AnnotatedNoRouteToHostException: No route to host: no further information: /************:7001
	Caused by: java.net.NoRouteToHostException: No route to host: no further information
		at sun.nio.ch.SocketChannelImpl.checkConnect(Native Method)
		at sun.nio.ch.SocketChannelImpl.finishConnect(SocketChannelImpl.java:717)
		at io.netty.channel.socket.nio.NioSocketChannel.doFinishConnect(NioSocketChannel.java:330)
		at io.netty.channel.nio.AbstractNioChannel$AbstractNioUnsafe.finishConnect(AbstractNioChannel.java:334)
		at io.netty.channel.nio.NioEventLoop.processSelectedKey(NioEventLoop.java:688)
		at io.netty.channel.nio.NioEventLoop.processSelectedKeysOptimized(NioEventLoop.java:635)
		at io.netty.channel.nio.NioEventLoop.processSelectedKeys(NioEventLoop.java:552)
		at io.netty.channel.nio.NioEventLoop.run(NioEventLoop.java:514)
		at io.netty.util.concurrent.SingleThreadEventExecutor$6.run(SingleThreadEventExecutor.java:1050)
		at io.netty.util.internal.ThreadExecutorMap$2.run(ThreadExecutorMap.java:74)
		at io.netty.util.concurrent.FastThreadLocalRunnable.run(FastThreadLocalRunnable.java:30)
		at java.lang.Thread.run(Thread.java:748)
	Suppressed: io.lettuce.core.RedisConnectionException: Unable to connect to [************:7003]: No route to host: no further information: /************:7003
		at io.lettuce.core.cluster.topology.ClusterTopologyRefresh.lambda$getConnections$0(ClusterTopologyRefresh.java:290)
		at java.util.concurrent.CompletableFuture.uniWhenComplete(CompletableFuture.java:760)
		at java.util.concurrent.CompletableFuture$UniWhenComplete.tryFire(CompletableFuture.java:736)
		at java.util.concurrent.CompletableFuture.postComplete(CompletableFuture.java:474)
		at java.util.concurrent.CompletableFuture.completeExceptionally(CompletableFuture.java:1977)
		at io.lettuce.core.AbstractRedisClient.lambda$initializeChannelAsync0$4(AbstractRedisClient.java:329)
		at io.netty.util.concurrent.DefaultPromise.notifyListener0(DefaultPromise.java:577)
		at io.netty.util.concurrent.DefaultPromise.notifyListeners0(DefaultPromise.java:570)
		at io.netty.util.concurrent.DefaultPromise.notifyListenersNow(DefaultPromise.java:549)
		at io.netty.util.concurrent.DefaultPromise.notifyListeners(DefaultPromise.java:490)
		at io.netty.util.concurrent.DefaultPromise.setValue0(DefaultPromise.java:615)
		at io.netty.util.concurrent.DefaultPromise.setFailure0(DefaultPromise.java:608)
		at io.netty.util.concurrent.DefaultPromise.tryFailure(DefaultPromise.java:117)
		at io.netty.channel.nio.AbstractNioChannel$AbstractNioUnsafe.fulfillConnectPromise(AbstractNioChannel.java:321)
		at io.netty.channel.nio.AbstractNioChannel$AbstractNioUnsafe.finishConnect(AbstractNioChannel.java:337)
		at io.netty.channel.nio.NioEventLoop.processSelectedKey(NioEventLoop.java:688)
		at io.netty.channel.nio.NioEventLoop.processSelectedKeysOptimized(NioEventLoop.java:635)
		at io.netty.channel.nio.NioEventLoop.processSelectedKeys(NioEventLoop.java:552)
		at io.netty.channel.nio.NioEventLoop.run(NioEventLoop.java:514)
		... 4 common frames omitted
	Caused by: io.netty.channel.AbstractChannel$AnnotatedNoRouteToHostException: No route to host: no further information: /************:7003
	Caused by: java.net.NoRouteToHostException: No route to host: no further information
		at sun.nio.ch.SocketChannelImpl.checkConnect(Native Method)
		at sun.nio.ch.SocketChannelImpl.finishConnect(SocketChannelImpl.java:717)
		at io.netty.channel.socket.nio.NioSocketChannel.doFinishConnect(NioSocketChannel.java:330)
		at io.netty.channel.nio.AbstractNioChannel$AbstractNioUnsafe.finishConnect(AbstractNioChannel.java:334)
		at io.netty.channel.nio.NioEventLoop.processSelectedKey(NioEventLoop.java:688)
		at io.netty.channel.nio.NioEventLoop.processSelectedKeysOptimized(NioEventLoop.java:635)
		at io.netty.channel.nio.NioEventLoop.processSelectedKeys(NioEventLoop.java:552)
		at io.netty.channel.nio.NioEventLoop.run(NioEventLoop.java:514)
		at io.netty.util.concurrent.SingleThreadEventExecutor$6.run(SingleThreadEventExecutor.java:1050)
		at io.netty.util.internal.ThreadExecutorMap$2.run(ThreadExecutorMap.java:74)
		at io.netty.util.concurrent.FastThreadLocalRunnable.run(FastThreadLocalRunnable.java:30)
		at java.lang.Thread.run(Thread.java:748)
Caused by: io.lettuce.core.RedisConnectionException: Unable to connect to [************:7004]: No route to host: no further information: /************:7004
	at io.lettuce.core.cluster.topology.ClusterTopologyRefresh.lambda$getConnections$0(ClusterTopologyRefresh.java:290)
	at java.util.concurrent.CompletableFuture.uniWhenComplete(CompletableFuture.java:760)
	at java.util.concurrent.CompletableFuture$UniWhenComplete.tryFire(CompletableFuture.java:736)
	at java.util.concurrent.CompletableFuture.postComplete(CompletableFuture.java:474)
	at java.util.concurrent.CompletableFuture.completeExceptionally(CompletableFuture.java:1977)
	at io.lettuce.core.AbstractRedisClient.lambda$initializeChannelAsync0$4(AbstractRedisClient.java:329)
	at io.netty.util.concurrent.DefaultPromise.notifyListener0(DefaultPromise.java:577)
	at io.netty.util.concurrent.DefaultPromise.notifyListeners0(DefaultPromise.java:570)
	at io.netty.util.concurrent.DefaultPromise.notifyListenersNow(DefaultPromise.java:549)
	at io.netty.util.concurrent.DefaultPromise.notifyListeners(DefaultPromise.java:490)
	at io.netty.util.concurrent.DefaultPromise.setValue0(DefaultPromise.java:615)
	at io.netty.util.concurrent.DefaultPromise.setFailure0(DefaultPromise.java:608)
	at io.netty.util.concurrent.DefaultPromise.tryFailure(DefaultPromise.java:117)
	at io.netty.channel.nio.AbstractNioChannel$AbstractNioUnsafe.fulfillConnectPromise(AbstractNioChannel.java:321)
	at io.netty.channel.nio.AbstractNioChannel$AbstractNioUnsafe.finishConnect(AbstractNioChannel.java:337)
	at io.netty.channel.nio.NioEventLoop.processSelectedKey(NioEventLoop.java:688)
	at io.netty.channel.nio.NioEventLoop.processSelectedKeysOptimized(NioEventLoop.java:635)
	at io.netty.channel.nio.NioEventLoop.processSelectedKeys(NioEventLoop.java:552)
	at io.netty.channel.nio.NioEventLoop.run(NioEventLoop.java:514)
	... 4 common frames omitted
Caused by: io.netty.channel.AbstractChannel$AnnotatedNoRouteToHostException: No route to host: no further information: /************:7004
Caused by: java.net.NoRouteToHostException: No route to host: no further information
	at sun.nio.ch.SocketChannelImpl.checkConnect(Native Method)
	at sun.nio.ch.SocketChannelImpl.finishConnect(SocketChannelImpl.java:717)
	at io.netty.channel.socket.nio.NioSocketChannel.doFinishConnect(NioSocketChannel.java:330)
	at io.netty.channel.nio.AbstractNioChannel$AbstractNioUnsafe.finishConnect(AbstractNioChannel.java:334)
	at io.netty.channel.nio.NioEventLoop.processSelectedKey(NioEventLoop.java:688)
	at io.netty.channel.nio.NioEventLoop.processSelectedKeysOptimized(NioEventLoop.java:635)
	at io.netty.channel.nio.NioEventLoop.processSelectedKeys(NioEventLoop.java:552)
	at io.netty.channel.nio.NioEventLoop.run(NioEventLoop.java:514)
	at io.netty.util.concurrent.SingleThreadEventExecutor$6.run(SingleThreadEventExecutor.java:1050)
	at io.netty.util.internal.ThreadExecutorMap$2.run(ThreadExecutorMap.java:74)
	at io.netty.util.concurrent.FastThreadLocalRunnable.run(FastThreadLocalRunnable.java:30)
	at java.lang.Thread.run(Thread.java:748)
2025-05-29 06:10:29.487 [lettuce-nioEventLoop-4-3] WARN  io.lettuce.core.cluster.topology.ClusterTopologyRefresh.lambda$getConnections$0 Line:284 - Unable to connect to [************:7005]: No route to host: no further information: /************:7005
2025-05-29 06:10:29.487 [lettuce-nioEventLoop-4-2] WARN  io.lettuce.core.cluster.topology.ClusterTopologyRefresh.lambda$getConnections$0 Line:284 - Unable to connect to [************:7004]: No route to host: no further information: /************:7004
2025-05-29 06:10:29.487 [lettuce-nioEventLoop-4-1] WARN  io.lettuce.core.cluster.topology.ClusterTopologyRefresh.lambda$getConnections$0 Line:284 - Unable to connect to [************:7003]: No route to host: no further information: /************:7003
2025-05-29 06:10:29.487 [lettuce-nioEventLoop-4-11] WARN  io.lettuce.core.cluster.topology.ClusterTopologyRefresh.lambda$getConnections$0 Line:284 - Unable to connect to [************:7001]: No route to host: no further information: /************:7001
2025-05-29 06:10:29.487 [lettuce-nioEventLoop-4-12] WARN  io.lettuce.core.cluster.topology.ClusterTopologyRefresh.lambda$getConnections$0 Line:284 - Unable to connect to [************:7002]: No route to host: no further information: /************:7002
2025-05-29 06:10:29.487 [lettuce-nioEventLoop-4-4] WARN  io.lettuce.core.cluster.topology.ClusterTopologyRefresh.lambda$getConnections$0 Line:284 - Unable to connect to [************:7006]: No route to host: no further information: /************:7006
2025-05-29 06:10:29.493 [lettuce-nioEventLoop-4-5] WARN  io.lettuce.core.cluster.topology.ClusterTopologyRefresh.lambda$getConnections$0 Line:284 - Unable to connect to [************:7006]: No route to host: no further information: /************:7006
2025-05-29 06:10:29.493 [lettuce-nioEventLoop-4-8] WARN  io.lettuce.core.cluster.topology.ClusterTopologyRefresh.lambda$getConnections$0 Line:284 - Unable to connect to [************:7002]: No route to host: no further information: /************:7002
2025-05-29 06:10:29.493 [lettuce-nioEventLoop-4-7] WARN  io.lettuce.core.cluster.topology.ClusterTopologyRefresh.lambda$getConnections$0 Line:284 - Unable to connect to [************:7005]: No route to host: no further information: /************:7005
2025-05-29 06:10:29.493 [lettuce-nioEventLoop-4-9] WARN  io.lettuce.core.cluster.topology.ClusterTopologyRefresh.lambda$getConnections$0 Line:284 - Unable to connect to [************:7003]: No route to host: no further information: /************:7003
2025-05-29 06:10:29.493 [lettuce-nioEventLoop-4-10] WARN  io.lettuce.core.cluster.topology.ClusterTopologyRefresh.lambda$getConnections$0 Line:284 - Unable to connect to [************:7001]: No route to host: no further information: /************:7001
2025-05-29 06:10:29.493 [lettuce-nioEventLoop-4-6] WARN  io.lettuce.core.cluster.topology.ClusterTopologyRefresh.lambda$getConnections$0 Line:284 - Unable to connect to [************:7004]: No route to host: no further information: /************:7004
2025-05-29 06:10:29.511 [lettuce-eventExecutorLoop-1-3] WARN  io.lettuce.core.cluster.ClusterTopologyRefreshScheduler.doRun Line:258 - Cannot refresh Redis Cluster topology
io.lettuce.core.RedisException: Cannot retrieve initial cluster partitions from initial URIs [RedisURI [host='************', port=7006], RedisURI [host='************', port=7004], RedisURI [host='************', port=7005], RedisURI [host='************', port=7002], RedisURI [host='************', port=7003], RedisURI [host='************', port=7001]]
	at io.lettuce.core.cluster.RedisClusterClient.loadPartitions(RedisClusterClient.java:848)
	at io.lettuce.core.cluster.RedisClusterClient.reloadPartitions(RedisClusterClient.java:784)
	at io.lettuce.core.cluster.ClusterTopologyRefreshScheduler$ClusterTopologyRefreshTask.doRun(ClusterTopologyRefreshScheduler.java:256)
	at io.lettuce.core.cluster.ClusterTopologyRefreshScheduler$ClusterTopologyRefreshTask.run(ClusterTopologyRefreshScheduler.java:236)
	at io.netty.util.concurrent.PromiseTask.runTask(PromiseTask.java:98)
	at io.netty.util.concurrent.PromiseTask.run(PromiseTask.java:106)
	at io.netty.util.concurrent.DefaultEventExecutor.run(DefaultEventExecutor.java:66)
	at io.netty.util.concurrent.SingleThreadEventExecutor$6.run(SingleThreadEventExecutor.java:1050)
	at io.netty.util.internal.ThreadExecutorMap$2.run(ThreadExecutorMap.java:74)
	at io.netty.util.concurrent.FastThreadLocalRunnable.run(FastThreadLocalRunnable.java:30)
	at java.lang.Thread.run(Thread.java:748)
	Suppressed: io.lettuce.core.RedisConnectionException: Unable to establish a connection to Redis Cluster at [RedisURI [host='************', port=7001], RedisURI [host='************', port=7002], RedisURI [host='************', port=7003], RedisURI [host='************', port=7004], RedisURI [host='************', port=7005], RedisURI [host='************', port=7006]]
		at io.lettuce.core.cluster.topology.AsyncConnections.get(AsyncConnections.java:89)
		at io.lettuce.core.cluster.topology.ClusterTopologyRefresh.loadViews(ClusterTopologyRefresh.java:76)
		at io.lettuce.core.cluster.RedisClusterClient.doLoadPartitions(RedisClusterClient.java:865)
		at io.lettuce.core.cluster.RedisClusterClient.loadPartitions(RedisClusterClient.java:838)
		... 10 common frames omitted
		Suppressed: io.lettuce.core.RedisConnectionException: Unable to connect to [************:7005]: No route to host: no further information: /************:7005
			at io.lettuce.core.cluster.topology.ClusterTopologyRefresh.lambda$getConnections$0(ClusterTopologyRefresh.java:290)
			at java.util.concurrent.CompletableFuture.uniWhenComplete(CompletableFuture.java:760)
			at java.util.concurrent.CompletableFuture$UniWhenComplete.tryFire(CompletableFuture.java:736)
			at java.util.concurrent.CompletableFuture.postComplete(CompletableFuture.java:474)
			at java.util.concurrent.CompletableFuture.completeExceptionally(CompletableFuture.java:1977)
			at io.lettuce.core.AbstractRedisClient.lambda$initializeChannelAsync0$4(AbstractRedisClient.java:329)
			at io.netty.util.concurrent.DefaultPromise.notifyListener0(DefaultPromise.java:577)
			at io.netty.util.concurrent.DefaultPromise.notifyListeners0(DefaultPromise.java:570)
			at io.netty.util.concurrent.DefaultPromise.notifyListenersNow(DefaultPromise.java:549)
			at io.netty.util.concurrent.DefaultPromise.notifyListeners(DefaultPromise.java:490)
			at io.netty.util.concurrent.DefaultPromise.setValue0(DefaultPromise.java:615)
			at io.netty.util.concurrent.DefaultPromise.setFailure0(DefaultPromise.java:608)
			at io.netty.util.concurrent.DefaultPromise.tryFailure(DefaultPromise.java:117)
			at io.netty.channel.nio.AbstractNioChannel$AbstractNioUnsafe.fulfillConnectPromise(AbstractNioChannel.java:321)
			at io.netty.channel.nio.AbstractNioChannel$AbstractNioUnsafe.finishConnect(AbstractNioChannel.java:337)
			at io.netty.channel.nio.NioEventLoop.processSelectedKey(NioEventLoop.java:688)
			at io.netty.channel.nio.NioEventLoop.processSelectedKeysOptimized(NioEventLoop.java:635)
			at io.netty.channel.nio.NioEventLoop.processSelectedKeys(NioEventLoop.java:552)
			at io.netty.channel.nio.NioEventLoop.run(NioEventLoop.java:514)
			... 4 common frames omitted
		Caused by: io.netty.channel.AbstractChannel$AnnotatedNoRouteToHostException: No route to host: no further information: /************:7005
		Caused by: java.net.NoRouteToHostException: No route to host: no further information
			at sun.nio.ch.SocketChannelImpl.checkConnect(Native Method)
			at sun.nio.ch.SocketChannelImpl.finishConnect(SocketChannelImpl.java:717)
			at io.netty.channel.socket.nio.NioSocketChannel.doFinishConnect(NioSocketChannel.java:330)
			at io.netty.channel.nio.AbstractNioChannel$AbstractNioUnsafe.finishConnect(AbstractNioChannel.java:334)
			at io.netty.channel.nio.NioEventLoop.processSelectedKey(NioEventLoop.java:688)
			at io.netty.channel.nio.NioEventLoop.processSelectedKeysOptimized(NioEventLoop.java:635)
			at io.netty.channel.nio.NioEventLoop.processSelectedKeys(NioEventLoop.java:552)
			at io.netty.channel.nio.NioEventLoop.run(NioEventLoop.java:514)
			at io.netty.util.concurrent.SingleThreadEventExecutor$6.run(SingleThreadEventExecutor.java:1050)
			at io.netty.util.internal.ThreadExecutorMap$2.run(ThreadExecutorMap.java:74)
			at io.netty.util.concurrent.FastThreadLocalRunnable.run(FastThreadLocalRunnable.java:30)
			at java.lang.Thread.run(Thread.java:748)
		Suppressed: io.lettuce.core.RedisConnectionException: Unable to connect to [************:7003]: No route to host: no further information: /************:7003
			at io.lettuce.core.cluster.topology.ClusterTopologyRefresh.lambda$getConnections$0(ClusterTopologyRefresh.java:290)
			at java.util.concurrent.CompletableFuture.uniWhenComplete(CompletableFuture.java:760)
			at java.util.concurrent.CompletableFuture$UniWhenComplete.tryFire(CompletableFuture.java:736)
			at java.util.concurrent.CompletableFuture.postComplete(CompletableFuture.java:474)
			at java.util.concurrent.CompletableFuture.completeExceptionally(CompletableFuture.java:1977)
			at io.lettuce.core.AbstractRedisClient.lambda$initializeChannelAsync0$4(AbstractRedisClient.java:329)
			at io.netty.util.concurrent.DefaultPromise.notifyListener0(DefaultPromise.java:577)
			at io.netty.util.concurrent.DefaultPromise.notifyListeners0(DefaultPromise.java:570)
			at io.netty.util.concurrent.DefaultPromise.notifyListenersNow(DefaultPromise.java:549)
			at io.netty.util.concurrent.DefaultPromise.notifyListeners(DefaultPromise.java:490)
			at io.netty.util.concurrent.DefaultPromise.setValue0(DefaultPromise.java:615)
			at io.netty.util.concurrent.DefaultPromise.setFailure0(DefaultPromise.java:608)
			at io.netty.util.concurrent.DefaultPromise.tryFailure(DefaultPromise.java:117)
			at io.netty.channel.nio.AbstractNioChannel$AbstractNioUnsafe.fulfillConnectPromise(AbstractNioChannel.java:321)
			at io.netty.channel.nio.AbstractNioChannel$AbstractNioUnsafe.finishConnect(AbstractNioChannel.java:337)
			at io.netty.channel.nio.NioEventLoop.processSelectedKey(NioEventLoop.java:688)
			at io.netty.channel.nio.NioEventLoop.processSelectedKeysOptimized(NioEventLoop.java:635)
			at io.netty.channel.nio.NioEventLoop.processSelectedKeys(NioEventLoop.java:552)
			at io.netty.channel.nio.NioEventLoop.run(NioEventLoop.java:514)
			... 4 common frames omitted
		Caused by: io.netty.channel.AbstractChannel$AnnotatedNoRouteToHostException: No route to host: no further information: /************:7003
		Caused by: java.net.NoRouteToHostException: No route to host: no further information
			at sun.nio.ch.SocketChannelImpl.checkConnect(Native Method)
			at sun.nio.ch.SocketChannelImpl.finishConnect(SocketChannelImpl.java:717)
			at io.netty.channel.socket.nio.NioSocketChannel.doFinishConnect(NioSocketChannel.java:330)
			at io.netty.channel.nio.AbstractNioChannel$AbstractNioUnsafe.finishConnect(AbstractNioChannel.java:334)
			at io.netty.channel.nio.NioEventLoop.processSelectedKey(NioEventLoop.java:688)
			at io.netty.channel.nio.NioEventLoop.processSelectedKeysOptimized(NioEventLoop.java:635)
			at io.netty.channel.nio.NioEventLoop.processSelectedKeys(NioEventLoop.java:552)
			at io.netty.channel.nio.NioEventLoop.run(NioEventLoop.java:514)
			at io.netty.util.concurrent.SingleThreadEventExecutor$6.run(SingleThreadEventExecutor.java:1050)
			at io.netty.util.internal.ThreadExecutorMap$2.run(ThreadExecutorMap.java:74)
			at io.netty.util.concurrent.FastThreadLocalRunnable.run(FastThreadLocalRunnable.java:30)
			at java.lang.Thread.run(Thread.java:748)
		Suppressed: io.lettuce.core.RedisConnectionException: Unable to connect to [************:7001]: No route to host: no further information: /************:7001
			at io.lettuce.core.cluster.topology.ClusterTopologyRefresh.lambda$getConnections$0(ClusterTopologyRefresh.java:290)
			at java.util.concurrent.CompletableFuture.uniWhenComplete(CompletableFuture.java:760)
			at java.util.concurrent.CompletableFuture$UniWhenComplete.tryFire(CompletableFuture.java:736)
			at java.util.concurrent.CompletableFuture.postComplete(CompletableFuture.java:474)
			at java.util.concurrent.CompletableFuture.completeExceptionally(CompletableFuture.java:1977)
			at io.lettuce.core.AbstractRedisClient.lambda$initializeChannelAsync0$4(AbstractRedisClient.java:329)
			at io.netty.util.concurrent.DefaultPromise.notifyListener0(DefaultPromise.java:577)
			at io.netty.util.concurrent.DefaultPromise.notifyListeners0(DefaultPromise.java:570)
			at io.netty.util.concurrent.DefaultPromise.notifyListenersNow(DefaultPromise.java:549)
			at io.netty.util.concurrent.DefaultPromise.notifyListeners(DefaultPromise.java:490)
			at io.netty.util.concurrent.DefaultPromise.setValue0(DefaultPromise.java:615)
			at io.netty.util.concurrent.DefaultPromise.setFailure0(DefaultPromise.java:608)
			at io.netty.util.concurrent.DefaultPromise.tryFailure(DefaultPromise.java:117)
			at io.netty.channel.nio.AbstractNioChannel$AbstractNioUnsafe.fulfillConnectPromise(AbstractNioChannel.java:321)
			at io.netty.channel.nio.AbstractNioChannel$AbstractNioUnsafe.finishConnect(AbstractNioChannel.java:337)
			at io.netty.channel.nio.NioEventLoop.processSelectedKey(NioEventLoop.java:688)
			at io.netty.channel.nio.NioEventLoop.processSelectedKeysOptimized(NioEventLoop.java:635)
			at io.netty.channel.nio.NioEventLoop.processSelectedKeys(NioEventLoop.java:552)
			at io.netty.channel.nio.NioEventLoop.run(NioEventLoop.java:514)
			... 4 common frames omitted
		Caused by: io.netty.channel.AbstractChannel$AnnotatedNoRouteToHostException: No route to host: no further information: /************:7001
		Caused by: java.net.NoRouteToHostException: No route to host: no further information
			at sun.nio.ch.SocketChannelImpl.checkConnect(Native Method)
			at sun.nio.ch.SocketChannelImpl.finishConnect(SocketChannelImpl.java:717)
			at io.netty.channel.socket.nio.NioSocketChannel.doFinishConnect(NioSocketChannel.java:330)
			at io.netty.channel.nio.AbstractNioChannel$AbstractNioUnsafe.finishConnect(AbstractNioChannel.java:334)
			at io.netty.channel.nio.NioEventLoop.processSelectedKey(NioEventLoop.java:688)
			at io.netty.channel.nio.NioEventLoop.processSelectedKeysOptimized(NioEventLoop.java:635)
			at io.netty.channel.nio.NioEventLoop.processSelectedKeys(NioEventLoop.java:552)
			at io.netty.channel.nio.NioEventLoop.run(NioEventLoop.java:514)
			at io.netty.util.concurrent.SingleThreadEventExecutor$6.run(SingleThreadEventExecutor.java:1050)
			at io.netty.util.internal.ThreadExecutorMap$2.run(ThreadExecutorMap.java:74)
			at io.netty.util.concurrent.FastThreadLocalRunnable.run(FastThreadLocalRunnable.java:30)
			at java.lang.Thread.run(Thread.java:748)
		Suppressed: io.lettuce.core.RedisConnectionException: Unable to connect to [************:7002]: No route to host: no further information: /************:7002
			at io.lettuce.core.cluster.topology.ClusterTopologyRefresh.lambda$getConnections$0(ClusterTopologyRefresh.java:290)
			at java.util.concurrent.CompletableFuture.uniWhenComplete(CompletableFuture.java:760)
			at java.util.concurrent.CompletableFuture$UniWhenComplete.tryFire(CompletableFuture.java:736)
			at java.util.concurrent.CompletableFuture.postComplete(CompletableFuture.java:474)
			at java.util.concurrent.CompletableFuture.completeExceptionally(CompletableFuture.java:1977)
			at io.lettuce.core.AbstractRedisClient.lambda$initializeChannelAsync0$4(AbstractRedisClient.java:329)
			at io.netty.util.concurrent.DefaultPromise.notifyListener0(DefaultPromise.java:577)
			at io.netty.util.concurrent.DefaultPromise.notifyListeners0(DefaultPromise.java:570)
			at io.netty.util.concurrent.DefaultPromise.notifyListenersNow(DefaultPromise.java:549)
			at io.netty.util.concurrent.DefaultPromise.notifyListeners(DefaultPromise.java:490)
			at io.netty.util.concurrent.DefaultPromise.setValue0(DefaultPromise.java:615)
			at io.netty.util.concurrent.DefaultPromise.setFailure0(DefaultPromise.java:608)
			at io.netty.util.concurrent.DefaultPromise.tryFailure(DefaultPromise.java:117)
			at io.netty.channel.nio.AbstractNioChannel$AbstractNioUnsafe.fulfillConnectPromise(AbstractNioChannel.java:321)
			at io.netty.channel.nio.AbstractNioChannel$AbstractNioUnsafe.finishConnect(AbstractNioChannel.java:337)
			at io.netty.channel.nio.NioEventLoop.processSelectedKey(NioEventLoop.java:688)
			at io.netty.channel.nio.NioEventLoop.processSelectedKeysOptimized(NioEventLoop.java:635)
			at io.netty.channel.nio.NioEventLoop.processSelectedKeys(NioEventLoop.java:552)
			at io.netty.channel.nio.NioEventLoop.run(NioEventLoop.java:514)
			... 4 common frames omitted
		Caused by: io.netty.channel.AbstractChannel$AnnotatedNoRouteToHostException: No route to host: no further information: /************:7002
		Caused by: java.net.NoRouteToHostException: No route to host: no further information
			at sun.nio.ch.SocketChannelImpl.checkConnect(Native Method)
			at sun.nio.ch.SocketChannelImpl.finishConnect(SocketChannelImpl.java:717)
			at io.netty.channel.socket.nio.NioSocketChannel.doFinishConnect(NioSocketChannel.java:330)
			at io.netty.channel.nio.AbstractNioChannel$AbstractNioUnsafe.finishConnect(AbstractNioChannel.java:334)
			at io.netty.channel.nio.NioEventLoop.processSelectedKey(NioEventLoop.java:688)
			at io.netty.channel.nio.NioEventLoop.processSelectedKeysOptimized(NioEventLoop.java:635)
			at io.netty.channel.nio.NioEventLoop.processSelectedKeys(NioEventLoop.java:552)
			at io.netty.channel.nio.NioEventLoop.run(NioEventLoop.java:514)
			at io.netty.util.concurrent.SingleThreadEventExecutor$6.run(SingleThreadEventExecutor.java:1050)
			at io.netty.util.internal.ThreadExecutorMap$2.run(ThreadExecutorMap.java:74)
			at io.netty.util.concurrent.FastThreadLocalRunnable.run(FastThreadLocalRunnable.java:30)
			at java.lang.Thread.run(Thread.java:748)
		Suppressed: io.lettuce.core.RedisConnectionException: Unable to connect to [************:7006]: No route to host: no further information: /************:7006
			at io.lettuce.core.cluster.topology.ClusterTopologyRefresh.lambda$getConnections$0(ClusterTopologyRefresh.java:290)
			at java.util.concurrent.CompletableFuture.uniWhenComplete(CompletableFuture.java:760)
			at java.util.concurrent.CompletableFuture$UniWhenComplete.tryFire(CompletableFuture.java:736)
			at java.util.concurrent.CompletableFuture.postComplete(CompletableFuture.java:474)
			at java.util.concurrent.CompletableFuture.completeExceptionally(CompletableFuture.java:1977)
			at io.lettuce.core.AbstractRedisClient.lambda$initializeChannelAsync0$4(AbstractRedisClient.java:329)
			at io.netty.util.concurrent.DefaultPromise.notifyListener0(DefaultPromise.java:577)
			at io.netty.util.concurrent.DefaultPromise.notifyListeners0(DefaultPromise.java:570)
			at io.netty.util.concurrent.DefaultPromise.notifyListenersNow(DefaultPromise.java:549)
			at io.netty.util.concurrent.DefaultPromise.notifyListeners(DefaultPromise.java:490)
			at io.netty.util.concurrent.DefaultPromise.setValue0(DefaultPromise.java:615)
			at io.netty.util.concurrent.DefaultPromise.setFailure0(DefaultPromise.java:608)
			at io.netty.util.concurrent.DefaultPromise.tryFailure(DefaultPromise.java:117)
			at io.netty.channel.nio.AbstractNioChannel$AbstractNioUnsafe.fulfillConnectPromise(AbstractNioChannel.java:321)
			at io.netty.channel.nio.AbstractNioChannel$AbstractNioUnsafe.finishConnect(AbstractNioChannel.java:337)
			at io.netty.channel.nio.NioEventLoop.processSelectedKey(NioEventLoop.java:688)
			at io.netty.channel.nio.NioEventLoop.processSelectedKeysOptimized(NioEventLoop.java:635)
			at io.netty.channel.nio.NioEventLoop.processSelectedKeys(NioEventLoop.java:552)
			at io.netty.channel.nio.NioEventLoop.run(NioEventLoop.java:514)
			... 4 common frames omitted
		Caused by: io.netty.channel.AbstractChannel$AnnotatedNoRouteToHostException: No route to host: no further information: /************:7006
		Caused by: java.net.NoRouteToHostException: No route to host: no further information
			at sun.nio.ch.SocketChannelImpl.checkConnect(Native Method)
			at sun.nio.ch.SocketChannelImpl.finishConnect(SocketChannelImpl.java:717)
			at io.netty.channel.socket.nio.NioSocketChannel.doFinishConnect(NioSocketChannel.java:330)
			at io.netty.channel.nio.AbstractNioChannel$AbstractNioUnsafe.finishConnect(AbstractNioChannel.java:334)
			at io.netty.channel.nio.NioEventLoop.processSelectedKey(NioEventLoop.java:688)
			at io.netty.channel.nio.NioEventLoop.processSelectedKeysOptimized(NioEventLoop.java:635)
			at io.netty.channel.nio.NioEventLoop.processSelectedKeys(NioEventLoop.java:552)
			at io.netty.channel.nio.NioEventLoop.run(NioEventLoop.java:514)
			at io.netty.util.concurrent.SingleThreadEventExecutor$6.run(SingleThreadEventExecutor.java:1050)
			at io.netty.util.internal.ThreadExecutorMap$2.run(ThreadExecutorMap.java:74)
			at io.netty.util.concurrent.FastThreadLocalRunnable.run(FastThreadLocalRunnable.java:30)
			at java.lang.Thread.run(Thread.java:748)
	Caused by: io.lettuce.core.RedisConnectionException: Unable to connect to [************:7004]: No route to host: no further information: /************:7004
		at io.lettuce.core.cluster.topology.ClusterTopologyRefresh.lambda$getConnections$0(ClusterTopologyRefresh.java:290)
		at java.util.concurrent.CompletableFuture.uniWhenComplete(CompletableFuture.java:760)
		at java.util.concurrent.CompletableFuture$UniWhenComplete.tryFire(CompletableFuture.java:736)
		at java.util.concurrent.CompletableFuture.postComplete(CompletableFuture.java:474)
		at java.util.concurrent.CompletableFuture.completeExceptionally(CompletableFuture.java:1977)
		at io.lettuce.core.AbstractRedisClient.lambda$initializeChannelAsync0$4(AbstractRedisClient.java:329)
		at io.netty.util.concurrent.DefaultPromise.notifyListener0(DefaultPromise.java:577)
		at io.netty.util.concurrent.DefaultPromise.notifyListeners0(DefaultPromise.java:570)
		at io.netty.util.concurrent.DefaultPromise.notifyListenersNow(DefaultPromise.java:549)
		at io.netty.util.concurrent.DefaultPromise.notifyListeners(DefaultPromise.java:490)
		at io.netty.util.concurrent.DefaultPromise.setValue0(DefaultPromise.java:615)
		at io.netty.util.concurrent.DefaultPromise.setFailure0(DefaultPromise.java:608)
		at io.netty.util.concurrent.DefaultPromise.tryFailure(DefaultPromise.java:117)
		at io.netty.channel.nio.AbstractNioChannel$AbstractNioUnsafe.fulfillConnectPromise(AbstractNioChannel.java:321)
		at io.netty.channel.nio.AbstractNioChannel$AbstractNioUnsafe.finishConnect(AbstractNioChannel.java:337)
		at io.netty.channel.nio.NioEventLoop.processSelectedKey(NioEventLoop.java:688)
		at io.netty.channel.nio.NioEventLoop.processSelectedKeysOptimized(NioEventLoop.java:635)
		at io.netty.channel.nio.NioEventLoop.processSelectedKeys(NioEventLoop.java:552)
		at io.netty.channel.nio.NioEventLoop.run(NioEventLoop.java:514)
		... 4 common frames omitted
	Caused by: io.netty.channel.AbstractChannel$AnnotatedNoRouteToHostException: No route to host: no further information: /************:7004
	Caused by: java.net.NoRouteToHostException: No route to host: no further information
		at sun.nio.ch.SocketChannelImpl.checkConnect(Native Method)
		at sun.nio.ch.SocketChannelImpl.finishConnect(SocketChannelImpl.java:717)
		at io.netty.channel.socket.nio.NioSocketChannel.doFinishConnect(NioSocketChannel.java:330)
		at io.netty.channel.nio.AbstractNioChannel$AbstractNioUnsafe.finishConnect(AbstractNioChannel.java:334)
		at io.netty.channel.nio.NioEventLoop.processSelectedKey(NioEventLoop.java:688)
		at io.netty.channel.nio.NioEventLoop.processSelectedKeysOptimized(NioEventLoop.java:635)
		at io.netty.channel.nio.NioEventLoop.processSelectedKeys(NioEventLoop.java:552)
		at io.netty.channel.nio.NioEventLoop.run(NioEventLoop.java:514)
		at io.netty.util.concurrent.SingleThreadEventExecutor$6.run(SingleThreadEventExecutor.java:1050)
		at io.netty.util.internal.ThreadExecutorMap$2.run(ThreadExecutorMap.java:74)
		at io.netty.util.concurrent.FastThreadLocalRunnable.run(FastThreadLocalRunnable.java:30)
		at java.lang.Thread.run(Thread.java:748)
Caused by: io.lettuce.core.RedisConnectionException: Unable to establish a connection to Redis Cluster at [RedisURI [host='************', port=7001], RedisURI [host='************', port=7002], RedisURI [host='************', port=7003], RedisURI [host='************', port=7004], RedisURI [host='************', port=7005], RedisURI [host='************', port=7006]]
	at io.lettuce.core.cluster.topology.AsyncConnections.get(AsyncConnections.java:89)
	at io.lettuce.core.cluster.topology.ClusterTopologyRefresh.loadViews(ClusterTopologyRefresh.java:76)
	at io.lettuce.core.cluster.RedisClusterClient.doLoadPartitions(RedisClusterClient.java:865)
	at io.lettuce.core.cluster.RedisClusterClient.loadPartitions(RedisClusterClient.java:845)
	... 10 common frames omitted
	Suppressed: io.lettuce.core.RedisConnectionException: Unable to connect to [************:7005]: No route to host: no further information: /************:7005
		at io.lettuce.core.cluster.topology.ClusterTopologyRefresh.lambda$getConnections$0(ClusterTopologyRefresh.java:290)
		at java.util.concurrent.CompletableFuture.uniWhenComplete(CompletableFuture.java:760)
		at java.util.concurrent.CompletableFuture$UniWhenComplete.tryFire(CompletableFuture.java:736)
		at java.util.concurrent.CompletableFuture.postComplete(CompletableFuture.java:474)
		at java.util.concurrent.CompletableFuture.completeExceptionally(CompletableFuture.java:1977)
		at io.lettuce.core.AbstractRedisClient.lambda$initializeChannelAsync0$4(AbstractRedisClient.java:329)
		at io.netty.util.concurrent.DefaultPromise.notifyListener0(DefaultPromise.java:577)
		at io.netty.util.concurrent.DefaultPromise.notifyListeners0(DefaultPromise.java:570)
		at io.netty.util.concurrent.DefaultPromise.notifyListenersNow(DefaultPromise.java:549)
		at io.netty.util.concurrent.DefaultPromise.notifyListeners(DefaultPromise.java:490)
		at io.netty.util.concurrent.DefaultPromise.setValue0(DefaultPromise.java:615)
		at io.netty.util.concurrent.DefaultPromise.setFailure0(DefaultPromise.java:608)
		at io.netty.util.concurrent.DefaultPromise.tryFailure(DefaultPromise.java:117)
		at io.netty.channel.nio.AbstractNioChannel$AbstractNioUnsafe.fulfillConnectPromise(AbstractNioChannel.java:321)
		at io.netty.channel.nio.AbstractNioChannel$AbstractNioUnsafe.finishConnect(AbstractNioChannel.java:337)
		at io.netty.channel.nio.NioEventLoop.processSelectedKey(NioEventLoop.java:688)
		at io.netty.channel.nio.NioEventLoop.processSelectedKeysOptimized(NioEventLoop.java:635)
		at io.netty.channel.nio.NioEventLoop.processSelectedKeys(NioEventLoop.java:552)
		at io.netty.channel.nio.NioEventLoop.run(NioEventLoop.java:514)
		... 4 common frames omitted
	Caused by: io.netty.channel.AbstractChannel$AnnotatedNoRouteToHostException: No route to host: no further information: /************:7005
	Caused by: java.net.NoRouteToHostException: No route to host: no further information
		at sun.nio.ch.SocketChannelImpl.checkConnect(Native Method)
		at sun.nio.ch.SocketChannelImpl.finishConnect(SocketChannelImpl.java:717)
		at io.netty.channel.socket.nio.NioSocketChannel.doFinishConnect(NioSocketChannel.java:330)
		at io.netty.channel.nio.AbstractNioChannel$AbstractNioUnsafe.finishConnect(AbstractNioChannel.java:334)
		at io.netty.channel.nio.NioEventLoop.processSelectedKey(NioEventLoop.java:688)
		at io.netty.channel.nio.NioEventLoop.processSelectedKeysOptimized(NioEventLoop.java:635)
		at io.netty.channel.nio.NioEventLoop.processSelectedKeys(NioEventLoop.java:552)
		at io.netty.channel.nio.NioEventLoop.run(NioEventLoop.java:514)
		at io.netty.util.concurrent.SingleThreadEventExecutor$6.run(SingleThreadEventExecutor.java:1050)
		at io.netty.util.internal.ThreadExecutorMap$2.run(ThreadExecutorMap.java:74)
		at io.netty.util.concurrent.FastThreadLocalRunnable.run(FastThreadLocalRunnable.java:30)
		at java.lang.Thread.run(Thread.java:748)
	Suppressed: io.lettuce.core.RedisConnectionException: Unable to connect to [************:7003]: No route to host: no further information: /************:7003
		at io.lettuce.core.cluster.topology.ClusterTopologyRefresh.lambda$getConnections$0(ClusterTopologyRefresh.java:290)
		at java.util.concurrent.CompletableFuture.uniWhenComplete(CompletableFuture.java:760)
		at java.util.concurrent.CompletableFuture$UniWhenComplete.tryFire(CompletableFuture.java:736)
		at java.util.concurrent.CompletableFuture.postComplete(CompletableFuture.java:474)
		at java.util.concurrent.CompletableFuture.completeExceptionally(CompletableFuture.java:1977)
		at io.lettuce.core.AbstractRedisClient.lambda$initializeChannelAsync0$4(AbstractRedisClient.java:329)
		at io.netty.util.concurrent.DefaultPromise.notifyListener0(DefaultPromise.java:577)
		at io.netty.util.concurrent.DefaultPromise.notifyListeners0(DefaultPromise.java:570)
		at io.netty.util.concurrent.DefaultPromise.notifyListenersNow(DefaultPromise.java:549)
		at io.netty.util.concurrent.DefaultPromise.notifyListeners(DefaultPromise.java:490)
		at io.netty.util.concurrent.DefaultPromise.setValue0(DefaultPromise.java:615)
		at io.netty.util.concurrent.DefaultPromise.setFailure0(DefaultPromise.java:608)
		at io.netty.util.concurrent.DefaultPromise.tryFailure(DefaultPromise.java:117)
		at io.netty.channel.nio.AbstractNioChannel$AbstractNioUnsafe.fulfillConnectPromise(AbstractNioChannel.java:321)
		at io.netty.channel.nio.AbstractNioChannel$AbstractNioUnsafe.finishConnect(AbstractNioChannel.java:337)
		at io.netty.channel.nio.NioEventLoop.processSelectedKey(NioEventLoop.java:688)
		at io.netty.channel.nio.NioEventLoop.processSelectedKeysOptimized(NioEventLoop.java:635)
		at io.netty.channel.nio.NioEventLoop.processSelectedKeys(NioEventLoop.java:552)
		at io.netty.channel.nio.NioEventLoop.run(NioEventLoop.java:514)
		... 4 common frames omitted
	Caused by: io.netty.channel.AbstractChannel$AnnotatedNoRouteToHostException: No route to host: no further information: /************:7003
	Caused by: java.net.NoRouteToHostException: No route to host: no further information
		at sun.nio.ch.SocketChannelImpl.checkConnect(Native Method)
		at sun.nio.ch.SocketChannelImpl.finishConnect(SocketChannelImpl.java:717)
		at io.netty.channel.socket.nio.NioSocketChannel.doFinishConnect(NioSocketChannel.java:330)
		at io.netty.channel.nio.AbstractNioChannel$AbstractNioUnsafe.finishConnect(AbstractNioChannel.java:334)
		at io.netty.channel.nio.NioEventLoop.processSelectedKey(NioEventLoop.java:688)
		at io.netty.channel.nio.NioEventLoop.processSelectedKeysOptimized(NioEventLoop.java:635)
		at io.netty.channel.nio.NioEventLoop.processSelectedKeys(NioEventLoop.java:552)
		at io.netty.channel.nio.NioEventLoop.run(NioEventLoop.java:514)
		at io.netty.util.concurrent.SingleThreadEventExecutor$6.run(SingleThreadEventExecutor.java:1050)
		at io.netty.util.internal.ThreadExecutorMap$2.run(ThreadExecutorMap.java:74)
		at io.netty.util.concurrent.FastThreadLocalRunnable.run(FastThreadLocalRunnable.java:30)
		at java.lang.Thread.run(Thread.java:748)
	Suppressed: io.lettuce.core.RedisConnectionException: Unable to connect to [************:7002]: No route to host: no further information: /************:7002
		at io.lettuce.core.cluster.topology.ClusterTopologyRefresh.lambda$getConnections$0(ClusterTopologyRefresh.java:290)
		at java.util.concurrent.CompletableFuture.uniWhenComplete(CompletableFuture.java:760)
		at java.util.concurrent.CompletableFuture$UniWhenComplete.tryFire(CompletableFuture.java:736)
		at java.util.concurrent.CompletableFuture.postComplete(CompletableFuture.java:474)
		at java.util.concurrent.CompletableFuture.completeExceptionally(CompletableFuture.java:1977)
		at io.lettuce.core.AbstractRedisClient.lambda$initializeChannelAsync0$4(AbstractRedisClient.java:329)
		at io.netty.util.concurrent.DefaultPromise.notifyListener0(DefaultPromise.java:577)
		at io.netty.util.concurrent.DefaultPromise.notifyListeners0(DefaultPromise.java:570)
		at io.netty.util.concurrent.DefaultPromise.notifyListenersNow(DefaultPromise.java:549)
		at io.netty.util.concurrent.DefaultPromise.notifyListeners(DefaultPromise.java:490)
		at io.netty.util.concurrent.DefaultPromise.setValue0(DefaultPromise.java:615)
		at io.netty.util.concurrent.DefaultPromise.setFailure0(DefaultPromise.java:608)
		at io.netty.util.concurrent.DefaultPromise.tryFailure(DefaultPromise.java:117)
		at io.netty.channel.nio.AbstractNioChannel$AbstractNioUnsafe.fulfillConnectPromise(AbstractNioChannel.java:321)
		at io.netty.channel.nio.AbstractNioChannel$AbstractNioUnsafe.finishConnect(AbstractNioChannel.java:337)
		at io.netty.channel.nio.NioEventLoop.processSelectedKey(NioEventLoop.java:688)
		at io.netty.channel.nio.NioEventLoop.processSelectedKeysOptimized(NioEventLoop.java:635)
		at io.netty.channel.nio.NioEventLoop.processSelectedKeys(NioEventLoop.java:552)
		at io.netty.channel.nio.NioEventLoop.run(NioEventLoop.java:514)
		... 4 common frames omitted
	Caused by: io.netty.channel.AbstractChannel$AnnotatedNoRouteToHostException: No route to host: no further information: /************:7002
	Caused by: java.net.NoRouteToHostException: No route to host: no further information
		at sun.nio.ch.SocketChannelImpl.checkConnect(Native Method)
		at sun.nio.ch.SocketChannelImpl.finishConnect(SocketChannelImpl.java:717)
		at io.netty.channel.socket.nio.NioSocketChannel.doFinishConnect(NioSocketChannel.java:330)
		at io.netty.channel.nio.AbstractNioChannel$AbstractNioUnsafe.finishConnect(AbstractNioChannel.java:334)
		at io.netty.channel.nio.NioEventLoop.processSelectedKey(NioEventLoop.java:688)
		at io.netty.channel.nio.NioEventLoop.processSelectedKeysOptimized(NioEventLoop.java:635)
		at io.netty.channel.nio.NioEventLoop.processSelectedKeys(NioEventLoop.java:552)
		at io.netty.channel.nio.NioEventLoop.run(NioEventLoop.java:514)
		at io.netty.util.concurrent.SingleThreadEventExecutor$6.run(SingleThreadEventExecutor.java:1050)
		at io.netty.util.internal.ThreadExecutorMap$2.run(ThreadExecutorMap.java:74)
		at io.netty.util.concurrent.FastThreadLocalRunnable.run(FastThreadLocalRunnable.java:30)
		at java.lang.Thread.run(Thread.java:748)
	Suppressed: io.lettuce.core.RedisConnectionException: Unable to connect to [************:7001]: No route to host: no further information: /************:7001
		at io.lettuce.core.cluster.topology.ClusterTopologyRefresh.lambda$getConnections$0(ClusterTopologyRefresh.java:290)
		at java.util.concurrent.CompletableFuture.uniWhenComplete(CompletableFuture.java:760)
		at java.util.concurrent.CompletableFuture$UniWhenComplete.tryFire(CompletableFuture.java:736)
		at java.util.concurrent.CompletableFuture.postComplete(CompletableFuture.java:474)
		at java.util.concurrent.CompletableFuture.completeExceptionally(CompletableFuture.java:1977)
		at io.lettuce.core.AbstractRedisClient.lambda$initializeChannelAsync0$4(AbstractRedisClient.java:329)
		at io.netty.util.concurrent.DefaultPromise.notifyListener0(DefaultPromise.java:577)
		at io.netty.util.concurrent.DefaultPromise.notifyListeners0(DefaultPromise.java:570)
		at io.netty.util.concurrent.DefaultPromise.notifyListenersNow(DefaultPromise.java:549)
		at io.netty.util.concurrent.DefaultPromise.notifyListeners(DefaultPromise.java:490)
		at io.netty.util.concurrent.DefaultPromise.setValue0(DefaultPromise.java:615)
		at io.netty.util.concurrent.DefaultPromise.setFailure0(DefaultPromise.java:608)
		at io.netty.util.concurrent.DefaultPromise.tryFailure(DefaultPromise.java:117)
		at io.netty.channel.nio.AbstractNioChannel$AbstractNioUnsafe.fulfillConnectPromise(AbstractNioChannel.java:321)
		at io.netty.channel.nio.AbstractNioChannel$AbstractNioUnsafe.finishConnect(AbstractNioChannel.java:337)
		at io.netty.channel.nio.NioEventLoop.processSelectedKey(NioEventLoop.java:688)
		at io.netty.channel.nio.NioEventLoop.processSelectedKeysOptimized(NioEventLoop.java:635)
		at io.netty.channel.nio.NioEventLoop.processSelectedKeys(NioEventLoop.java:552)
		at io.netty.channel.nio.NioEventLoop.run(NioEventLoop.java:514)
		... 4 common frames omitted
	Caused by: io.netty.channel.AbstractChannel$AnnotatedNoRouteToHostException: No route to host: no further information: /************:7001
	Caused by: java.net.NoRouteToHostException: No route to host: no further information
		at sun.nio.ch.SocketChannelImpl.checkConnect(Native Method)
		at sun.nio.ch.SocketChannelImpl.finishConnect(SocketChannelImpl.java:717)
		at io.netty.channel.socket.nio.NioSocketChannel.doFinishConnect(NioSocketChannel.java:330)
		at io.netty.channel.nio.AbstractNioChannel$AbstractNioUnsafe.finishConnect(AbstractNioChannel.java:334)
		at io.netty.channel.nio.NioEventLoop.processSelectedKey(NioEventLoop.java:688)
		at io.netty.channel.nio.NioEventLoop.processSelectedKeysOptimized(NioEventLoop.java:635)
		at io.netty.channel.nio.NioEventLoop.processSelectedKeys(NioEventLoop.java:552)
		at io.netty.channel.nio.NioEventLoop.run(NioEventLoop.java:514)
		at io.netty.util.concurrent.SingleThreadEventExecutor$6.run(SingleThreadEventExecutor.java:1050)
		at io.netty.util.internal.ThreadExecutorMap$2.run(ThreadExecutorMap.java:74)
		at io.netty.util.concurrent.FastThreadLocalRunnable.run(FastThreadLocalRunnable.java:30)
		at java.lang.Thread.run(Thread.java:748)
	Suppressed: io.lettuce.core.RedisConnectionException: Unable to connect to [************:7004]: No route to host: no further information: /************:7004
		at io.lettuce.core.cluster.topology.ClusterTopologyRefresh.lambda$getConnections$0(ClusterTopologyRefresh.java:290)
		at java.util.concurrent.CompletableFuture.uniWhenComplete(CompletableFuture.java:760)
		at java.util.concurrent.CompletableFuture$UniWhenComplete.tryFire(CompletableFuture.java:736)
		at java.util.concurrent.CompletableFuture.postComplete(CompletableFuture.java:474)
		at java.util.concurrent.CompletableFuture.completeExceptionally(CompletableFuture.java:1977)
		at io.lettuce.core.AbstractRedisClient.lambda$initializeChannelAsync0$4(AbstractRedisClient.java:329)
		at io.netty.util.concurrent.DefaultPromise.notifyListener0(DefaultPromise.java:577)
		at io.netty.util.concurrent.DefaultPromise.notifyListeners0(DefaultPromise.java:570)
		at io.netty.util.concurrent.DefaultPromise.notifyListenersNow(DefaultPromise.java:549)
		at io.netty.util.concurrent.DefaultPromise.notifyListeners(DefaultPromise.java:490)
		at io.netty.util.concurrent.DefaultPromise.setValue0(DefaultPromise.java:615)
		at io.netty.util.concurrent.DefaultPromise.setFailure0(DefaultPromise.java:608)
		at io.netty.util.concurrent.DefaultPromise.tryFailure(DefaultPromise.java:117)
		at io.netty.channel.nio.AbstractNioChannel$AbstractNioUnsafe.fulfillConnectPromise(AbstractNioChannel.java:321)
		at io.netty.channel.nio.AbstractNioChannel$AbstractNioUnsafe.finishConnect(AbstractNioChannel.java:337)
		at io.netty.channel.nio.NioEventLoop.processSelectedKey(NioEventLoop.java:688)
		at io.netty.channel.nio.NioEventLoop.processSelectedKeysOptimized(NioEventLoop.java:635)
		at io.netty.channel.nio.NioEventLoop.processSelectedKeys(NioEventLoop.java:552)
		at io.netty.channel.nio.NioEventLoop.run(NioEventLoop.java:514)
		... 4 common frames omitted
	Caused by: io.netty.channel.AbstractChannel$AnnotatedNoRouteToHostException: No route to host: no further information: /************:7004
	Caused by: java.net.NoRouteToHostException: No route to host: no further information
		at sun.nio.ch.SocketChannelImpl.checkConnect(Native Method)
		at sun.nio.ch.SocketChannelImpl.finishConnect(SocketChannelImpl.java:717)
		at io.netty.channel.socket.nio.NioSocketChannel.doFinishConnect(NioSocketChannel.java:330)
		at io.netty.channel.nio.AbstractNioChannel$AbstractNioUnsafe.finishConnect(AbstractNioChannel.java:334)
		at io.netty.channel.nio.NioEventLoop.processSelectedKey(NioEventLoop.java:688)
		at io.netty.channel.nio.NioEventLoop.processSelectedKeysOptimized(NioEventLoop.java:635)
		at io.netty.channel.nio.NioEventLoop.processSelectedKeys(NioEventLoop.java:552)
		at io.netty.channel.nio.NioEventLoop.run(NioEventLoop.java:514)
		at io.netty.util.concurrent.SingleThreadEventExecutor$6.run(SingleThreadEventExecutor.java:1050)
		at io.netty.util.internal.ThreadExecutorMap$2.run(ThreadExecutorMap.java:74)
		at io.netty.util.concurrent.FastThreadLocalRunnable.run(FastThreadLocalRunnable.java:30)
		at java.lang.Thread.run(Thread.java:748)
Caused by: io.lettuce.core.RedisConnectionException: Unable to connect to [************:7006]: No route to host: no further information: /************:7006
	at io.lettuce.core.cluster.topology.ClusterTopologyRefresh.lambda$getConnections$0(ClusterTopologyRefresh.java:290)
	at java.util.concurrent.CompletableFuture.uniWhenComplete(CompletableFuture.java:760)
	at java.util.concurrent.CompletableFuture$UniWhenComplete.tryFire(CompletableFuture.java:736)
	at java.util.concurrent.CompletableFuture.postComplete(CompletableFuture.java:474)
	at java.util.concurrent.CompletableFuture.completeExceptionally(CompletableFuture.java:1977)
	at io.lettuce.core.AbstractRedisClient.lambda$initializeChannelAsync0$4(AbstractRedisClient.java:329)
	at io.netty.util.concurrent.DefaultPromise.notifyListener0(DefaultPromise.java:577)
	at io.netty.util.concurrent.DefaultPromise.notifyListeners0(DefaultPromise.java:570)
	at io.netty.util.concurrent.DefaultPromise.notifyListenersNow(DefaultPromise.java:549)
	at io.netty.util.concurrent.DefaultPromise.notifyListeners(DefaultPromise.java:490)
	at io.netty.util.concurrent.DefaultPromise.setValue0(DefaultPromise.java:615)
	at io.netty.util.concurrent.DefaultPromise.setFailure0(DefaultPromise.java:608)
	at io.netty.util.concurrent.DefaultPromise.tryFailure(DefaultPromise.java:117)
	at io.netty.channel.nio.AbstractNioChannel$AbstractNioUnsafe.fulfillConnectPromise(AbstractNioChannel.java:321)
	at io.netty.channel.nio.AbstractNioChannel$AbstractNioUnsafe.finishConnect(AbstractNioChannel.java:337)
	at io.netty.channel.nio.NioEventLoop.processSelectedKey(NioEventLoop.java:688)
	at io.netty.channel.nio.NioEventLoop.processSelectedKeysOptimized(NioEventLoop.java:635)
	at io.netty.channel.nio.NioEventLoop.processSelectedKeys(NioEventLoop.java:552)
	at io.netty.channel.nio.NioEventLoop.run(NioEventLoop.java:514)
	... 4 common frames omitted
Caused by: io.netty.channel.AbstractChannel$AnnotatedNoRouteToHostException: No route to host: no further information: /************:7006
Caused by: java.net.NoRouteToHostException: No route to host: no further information
	at sun.nio.ch.SocketChannelImpl.checkConnect(Native Method)
	at sun.nio.ch.SocketChannelImpl.finishConnect(SocketChannelImpl.java:717)
	at io.netty.channel.socket.nio.NioSocketChannel.doFinishConnect(NioSocketChannel.java:330)
	at io.netty.channel.nio.AbstractNioChannel$AbstractNioUnsafe.finishConnect(AbstractNioChannel.java:334)
	at io.netty.channel.nio.NioEventLoop.processSelectedKey(NioEventLoop.java:688)
	at io.netty.channel.nio.NioEventLoop.processSelectedKeysOptimized(NioEventLoop.java:635)
	at io.netty.channel.nio.NioEventLoop.processSelectedKeys(NioEventLoop.java:552)
	at io.netty.channel.nio.NioEventLoop.run(NioEventLoop.java:514)
	at io.netty.util.concurrent.SingleThreadEventExecutor$6.run(SingleThreadEventExecutor.java:1050)
	at io.netty.util.internal.ThreadExecutorMap$2.run(ThreadExecutorMap.java:74)
	at io.netty.util.concurrent.FastThreadLocalRunnable.run(FastThreadLocalRunnable.java:30)
	at java.lang.Thread.run(Thread.java:748)
2025-05-29 06:10:29.520 [lettuce-nioEventLoop-4-11] WARN  io.lettuce.core.cluster.topology.ClusterTopologyRefresh.lambda$getConnections$0 Line:284 - Unable to connect to [************:7001]: No route to host: no further information: /************:7001
2025-05-29 06:10:29.520 [lettuce-nioEventLoop-4-3] WARN  io.lettuce.core.cluster.topology.ClusterTopologyRefresh.lambda$getConnections$0 Line:284 - Unable to connect to [************:7005]: No route to host: no further information: /************:7005
2025-05-29 06:10:29.520 [lettuce-nioEventLoop-4-4] WARN  io.lettuce.core.cluster.topology.ClusterTopologyRefresh.lambda$getConnections$0 Line:284 - Unable to connect to [************:7006]: No route to host: no further information: /************:7006
2025-05-29 06:10:29.520 [lettuce-nioEventLoop-4-12] WARN  io.lettuce.core.cluster.topology.ClusterTopologyRefresh.lambda$getConnections$0 Line:284 - Unable to connect to [************:7002]: No route to host: no further information: /************:7002
2025-05-29 06:10:29.520 [lettuce-nioEventLoop-4-2] WARN  io.lettuce.core.cluster.topology.ClusterTopologyRefresh.lambda$getConnections$0 Line:284 - Unable to connect to [************:7004]: No route to host: no further information: /************:7004
2025-05-29 06:10:29.520 [lettuce-nioEventLoop-4-1] WARN  io.lettuce.core.cluster.topology.ClusterTopologyRefresh.lambda$getConnections$0 Line:284 - Unable to connect to [************:7003]: No route to host: no further information: /************:7003
2025-05-29 06:10:29.525 [lettuce-nioEventLoop-4-7] WARN  io.lettuce.core.cluster.topology.ClusterTopologyRefresh.lambda$getConnections$0 Line:284 - Unable to connect to [************:7005]: No route to host: no further information: /************:7005
2025-05-29 06:10:29.525 [lettuce-nioEventLoop-4-6] WARN  io.lettuce.core.cluster.topology.ClusterTopologyRefresh.lambda$getConnections$0 Line:284 - Unable to connect to [************:7004]: No route to host: no further information: /************:7004
2025-05-29 06:10:29.525 [lettuce-nioEventLoop-4-5] WARN  io.lettuce.core.cluster.topology.ClusterTopologyRefresh.lambda$getConnections$0 Line:284 - Unable to connect to [************:7006]: No route to host: no further information: /************:7006
2025-05-29 06:10:29.525 [lettuce-nioEventLoop-4-8] WARN  io.lettuce.core.cluster.topology.ClusterTopologyRefresh.lambda$getConnections$0 Line:284 - Unable to connect to [************:7002]: No route to host: no further information: /************:7002
2025-05-29 06:10:29.525 [lettuce-nioEventLoop-4-9] WARN  io.lettuce.core.cluster.topology.ClusterTopologyRefresh.lambda$getConnections$0 Line:284 - Unable to connect to [************:7003]: No route to host: no further information: /************:7003
2025-05-29 06:10:29.525 [lettuce-nioEventLoop-4-10] WARN  io.lettuce.core.cluster.topology.ClusterTopologyRefresh.lambda$getConnections$0 Line:284 - Unable to connect to [************:7001]: No route to host: no further information: /************:7001
2025-05-29 06:10:29.543 [lettuce-eventExecutorLoop-1-3] WARN  io.lettuce.core.cluster.ClusterTopologyRefreshScheduler.doRun Line:258 - Cannot refresh Redis Cluster topology
io.lettuce.core.RedisException: Cannot retrieve initial cluster partitions from initial URIs [RedisURI [host='************', port=7006], RedisURI [host='************', port=7004], RedisURI [host='************', port=7005], RedisURI [host='************', port=7002], RedisURI [host='************', port=7003], RedisURI [host='************', port=7001]]
	at io.lettuce.core.cluster.RedisClusterClient.loadPartitions(RedisClusterClient.java:848)
	at io.lettuce.core.cluster.RedisClusterClient.reloadPartitions(RedisClusterClient.java:784)
	at io.lettuce.core.cluster.ClusterTopologyRefreshScheduler$ClusterTopologyRefreshTask.doRun(ClusterTopologyRefreshScheduler.java:256)
	at io.lettuce.core.cluster.ClusterTopologyRefreshScheduler$ClusterTopologyRefreshTask.run(ClusterTopologyRefreshScheduler.java:236)
	at io.netty.util.concurrent.PromiseTask.runTask(PromiseTask.java:98)
	at io.netty.util.concurrent.PromiseTask.run(PromiseTask.java:106)
	at io.netty.util.concurrent.DefaultEventExecutor.run(DefaultEventExecutor.java:66)
	at io.netty.util.concurrent.SingleThreadEventExecutor$6.run(SingleThreadEventExecutor.java:1050)
	at io.netty.util.internal.ThreadExecutorMap$2.run(ThreadExecutorMap.java:74)
	at io.netty.util.concurrent.FastThreadLocalRunnable.run(FastThreadLocalRunnable.java:30)
	at java.lang.Thread.run(Thread.java:748)
	Suppressed: io.lettuce.core.RedisConnectionException: Unable to establish a connection to Redis Cluster at [RedisURI [host='************', port=7001], RedisURI [host='************', port=7002], RedisURI [host='************', port=7003], RedisURI [host='************', port=7004], RedisURI [host='************', port=7005], RedisURI [host='************', port=7006]]
		at io.lettuce.core.cluster.topology.AsyncConnections.get(AsyncConnections.java:89)
		at io.lettuce.core.cluster.topology.ClusterTopologyRefresh.loadViews(ClusterTopologyRefresh.java:76)
		at io.lettuce.core.cluster.RedisClusterClient.doLoadPartitions(RedisClusterClient.java:865)
		at io.lettuce.core.cluster.RedisClusterClient.loadPartitions(RedisClusterClient.java:838)
		... 10 common frames omitted
		Suppressed: io.lettuce.core.RedisConnectionException: Unable to connect to [************:7005]: No route to host: no further information: /************:7005
			at io.lettuce.core.cluster.topology.ClusterTopologyRefresh.lambda$getConnections$0(ClusterTopologyRefresh.java:290)
			at java.util.concurrent.CompletableFuture.uniWhenComplete(CompletableFuture.java:760)
			at java.util.concurrent.CompletableFuture$UniWhenComplete.tryFire(CompletableFuture.java:736)
			at java.util.concurrent.CompletableFuture.postComplete(CompletableFuture.java:474)
			at java.util.concurrent.CompletableFuture.completeExceptionally(CompletableFuture.java:1977)
			at io.lettuce.core.AbstractRedisClient.lambda$initializeChannelAsync0$4(AbstractRedisClient.java:329)
			at io.netty.util.concurrent.DefaultPromise.notifyListener0(DefaultPromise.java:577)
			at io.netty.util.concurrent.DefaultPromise.notifyListeners0(DefaultPromise.java:570)
			at io.netty.util.concurrent.DefaultPromise.notifyListenersNow(DefaultPromise.java:549)
			at io.netty.util.concurrent.DefaultPromise.notifyListeners(DefaultPromise.java:490)
			at io.netty.util.concurrent.DefaultPromise.setValue0(DefaultPromise.java:615)
			at io.netty.util.concurrent.DefaultPromise.setFailure0(DefaultPromise.java:608)
			at io.netty.util.concurrent.DefaultPromise.tryFailure(DefaultPromise.java:117)
			at io.netty.channel.nio.AbstractNioChannel$AbstractNioUnsafe.fulfillConnectPromise(AbstractNioChannel.java:321)
			at io.netty.channel.nio.AbstractNioChannel$AbstractNioUnsafe.finishConnect(AbstractNioChannel.java:337)
			at io.netty.channel.nio.NioEventLoop.processSelectedKey(NioEventLoop.java:688)
			at io.netty.channel.nio.NioEventLoop.processSelectedKeysOptimized(NioEventLoop.java:635)
			at io.netty.channel.nio.NioEventLoop.processSelectedKeys(NioEventLoop.java:552)
			at io.netty.channel.nio.NioEventLoop.run(NioEventLoop.java:514)
			... 4 common frames omitted
		Caused by: io.netty.channel.AbstractChannel$AnnotatedNoRouteToHostException: No route to host: no further information: /************:7005
		Caused by: java.net.NoRouteToHostException: No route to host: no further information
			at sun.nio.ch.SocketChannelImpl.checkConnect(Native Method)
			at sun.nio.ch.SocketChannelImpl.finishConnect(SocketChannelImpl.java:717)
			at io.netty.channel.socket.nio.NioSocketChannel.doFinishConnect(NioSocketChannel.java:330)
			at io.netty.channel.nio.AbstractNioChannel$AbstractNioUnsafe.finishConnect(AbstractNioChannel.java:334)
			at io.netty.channel.nio.NioEventLoop.processSelectedKey(NioEventLoop.java:688)
			at io.netty.channel.nio.NioEventLoop.processSelectedKeysOptimized(NioEventLoop.java:635)
			at io.netty.channel.nio.NioEventLoop.processSelectedKeys(NioEventLoop.java:552)
			at io.netty.channel.nio.NioEventLoop.run(NioEventLoop.java:514)
			at io.netty.util.concurrent.SingleThreadEventExecutor$6.run(SingleThreadEventExecutor.java:1050)
			at io.netty.util.internal.ThreadExecutorMap$2.run(ThreadExecutorMap.java:74)
			at io.netty.util.concurrent.FastThreadLocalRunnable.run(FastThreadLocalRunnable.java:30)
			at java.lang.Thread.run(Thread.java:748)
		Suppressed: io.lettuce.core.RedisConnectionException: Unable to connect to [************:7006]: No route to host: no further information: /************:7006
			at io.lettuce.core.cluster.topology.ClusterTopologyRefresh.lambda$getConnections$0(ClusterTopologyRefresh.java:290)
			at java.util.concurrent.CompletableFuture.uniWhenComplete(CompletableFuture.java:760)
			at java.util.concurrent.CompletableFuture$UniWhenComplete.tryFire(CompletableFuture.java:736)
			at java.util.concurrent.CompletableFuture.postComplete(CompletableFuture.java:474)
			at java.util.concurrent.CompletableFuture.completeExceptionally(CompletableFuture.java:1977)
			at io.lettuce.core.AbstractRedisClient.lambda$initializeChannelAsync0$4(AbstractRedisClient.java:329)
			at io.netty.util.concurrent.DefaultPromise.notifyListener0(DefaultPromise.java:577)
			at io.netty.util.concurrent.DefaultPromise.notifyListeners0(DefaultPromise.java:570)
			at io.netty.util.concurrent.DefaultPromise.notifyListenersNow(DefaultPromise.java:549)
			at io.netty.util.concurrent.DefaultPromise.notifyListeners(DefaultPromise.java:490)
			at io.netty.util.concurrent.DefaultPromise.setValue0(DefaultPromise.java:615)
			at io.netty.util.concurrent.DefaultPromise.setFailure0(DefaultPromise.java:608)
			at io.netty.util.concurrent.DefaultPromise.tryFailure(DefaultPromise.java:117)
			at io.netty.channel.nio.AbstractNioChannel$AbstractNioUnsafe.fulfillConnectPromise(AbstractNioChannel.java:321)
			at io.netty.channel.nio.AbstractNioChannel$AbstractNioUnsafe.finishConnect(AbstractNioChannel.java:337)
			at io.netty.channel.nio.NioEventLoop.processSelectedKey(NioEventLoop.java:688)
			at io.netty.channel.nio.NioEventLoop.processSelectedKeysOptimized(NioEventLoop.java:635)
			at io.netty.channel.nio.NioEventLoop.processSelectedKeys(NioEventLoop.java:552)
			at io.netty.channel.nio.NioEventLoop.run(NioEventLoop.java:514)
			... 4 common frames omitted
		Caused by: io.netty.channel.AbstractChannel$AnnotatedNoRouteToHostException: No route to host: no further information: /************:7006
		Caused by: java.net.NoRouteToHostException: No route to host: no further information
			at sun.nio.ch.SocketChannelImpl.checkConnect(Native Method)
			at sun.nio.ch.SocketChannelImpl.finishConnect(SocketChannelImpl.java:717)
			at io.netty.channel.socket.nio.NioSocketChannel.doFinishConnect(NioSocketChannel.java:330)
			at io.netty.channel.nio.AbstractNioChannel$AbstractNioUnsafe.finishConnect(AbstractNioChannel.java:334)
			at io.netty.channel.nio.NioEventLoop.processSelectedKey(NioEventLoop.java:688)
			at io.netty.channel.nio.NioEventLoop.processSelectedKeysOptimized(NioEventLoop.java:635)
			at io.netty.channel.nio.NioEventLoop.processSelectedKeys(NioEventLoop.java:552)
			at io.netty.channel.nio.NioEventLoop.run(NioEventLoop.java:514)
			at io.netty.util.concurrent.SingleThreadEventExecutor$6.run(SingleThreadEventExecutor.java:1050)
			at io.netty.util.internal.ThreadExecutorMap$2.run(ThreadExecutorMap.java:74)
			at io.netty.util.concurrent.FastThreadLocalRunnable.run(FastThreadLocalRunnable.java:30)
			at java.lang.Thread.run(Thread.java:748)
		Suppressed: io.lettuce.core.RedisConnectionException: Unable to connect to [************:7002]: No route to host: no further information: /************:7002
			at io.lettuce.core.cluster.topology.ClusterTopologyRefresh.lambda$getConnections$0(ClusterTopologyRefresh.java:290)
			at java.util.concurrent.CompletableFuture.uniWhenComplete(CompletableFuture.java:760)
			at java.util.concurrent.CompletableFuture$UniWhenComplete.tryFire(CompletableFuture.java:736)
			at java.util.concurrent.CompletableFuture.postComplete(CompletableFuture.java:474)
			at java.util.concurrent.CompletableFuture.completeExceptionally(CompletableFuture.java:1977)
			at io.lettuce.core.AbstractRedisClient.lambda$initializeChannelAsync0$4(AbstractRedisClient.java:329)
			at io.netty.util.concurrent.DefaultPromise.notifyListener0(DefaultPromise.java:577)
			at io.netty.util.concurrent.DefaultPromise.notifyListeners0(DefaultPromise.java:570)
			at io.netty.util.concurrent.DefaultPromise.notifyListenersNow(DefaultPromise.java:549)
			at io.netty.util.concurrent.DefaultPromise.notifyListeners(DefaultPromise.java:490)
			at io.netty.util.concurrent.DefaultPromise.setValue0(DefaultPromise.java:615)
			at io.netty.util.concurrent.DefaultPromise.setFailure0(DefaultPromise.java:608)
			at io.netty.util.concurrent.DefaultPromise.tryFailure(DefaultPromise.java:117)
			at io.netty.channel.nio.AbstractNioChannel$AbstractNioUnsafe.fulfillConnectPromise(AbstractNioChannel.java:321)
			at io.netty.channel.nio.AbstractNioChannel$AbstractNioUnsafe.finishConnect(AbstractNioChannel.java:337)
			at io.netty.channel.nio.NioEventLoop.processSelectedKey(NioEventLoop.java:688)
			at io.netty.channel.nio.NioEventLoop.processSelectedKeysOptimized(NioEventLoop.java:635)
			at io.netty.channel.nio.NioEventLoop.processSelectedKeys(NioEventLoop.java:552)
			at io.netty.channel.nio.NioEventLoop.run(NioEventLoop.java:514)
			... 4 common frames omitted
		Caused by: io.netty.channel.AbstractChannel$AnnotatedNoRouteToHostException: No route to host: no further information: /************:7002
		Caused by: java.net.NoRouteToHostException: No route to host: no further information
			at sun.nio.ch.SocketChannelImpl.checkConnect(Native Method)
			at sun.nio.ch.SocketChannelImpl.finishConnect(SocketChannelImpl.java:717)
			at io.netty.channel.socket.nio.NioSocketChannel.doFinishConnect(NioSocketChannel.java:330)
			at io.netty.channel.nio.AbstractNioChannel$AbstractNioUnsafe.finishConnect(AbstractNioChannel.java:334)
			at io.netty.channel.nio.NioEventLoop.processSelectedKey(NioEventLoop.java:688)
			at io.netty.channel.nio.NioEventLoop.processSelectedKeysOptimized(NioEventLoop.java:635)
			at io.netty.channel.nio.NioEventLoop.processSelectedKeys(NioEventLoop.java:552)
			at io.netty.channel.nio.NioEventLoop.run(NioEventLoop.java:514)
			at io.netty.util.concurrent.SingleThreadEventExecutor$6.run(SingleThreadEventExecutor.java:1050)
			at io.netty.util.internal.ThreadExecutorMap$2.run(ThreadExecutorMap.java:74)
			at io.netty.util.concurrent.FastThreadLocalRunnable.run(FastThreadLocalRunnable.java:30)
			at java.lang.Thread.run(Thread.java:748)
		Suppressed: io.lettuce.core.RedisConnectionException: Unable to connect to [************:7004]: No route to host: no further information: /************:7004
			at io.lettuce.core.cluster.topology.ClusterTopologyRefresh.lambda$getConnections$0(ClusterTopologyRefresh.java:290)
			at java.util.concurrent.CompletableFuture.uniWhenComplete(CompletableFuture.java:760)
			at java.util.concurrent.CompletableFuture$UniWhenComplete.tryFire(CompletableFuture.java:736)
			at java.util.concurrent.CompletableFuture.postComplete(CompletableFuture.java:474)
			at java.util.concurrent.CompletableFuture.completeExceptionally(CompletableFuture.java:1977)
			at io.lettuce.core.AbstractRedisClient.lambda$initializeChannelAsync0$4(AbstractRedisClient.java:329)
			at io.netty.util.concurrent.DefaultPromise.notifyListener0(DefaultPromise.java:577)
			at io.netty.util.concurrent.DefaultPromise.notifyListeners0(DefaultPromise.java:570)
			at io.netty.util.concurrent.DefaultPromise.notifyListenersNow(DefaultPromise.java:549)
			at io.netty.util.concurrent.DefaultPromise.notifyListeners(DefaultPromise.java:490)
			at io.netty.util.concurrent.DefaultPromise.setValue0(DefaultPromise.java:615)
			at io.netty.util.concurrent.DefaultPromise.setFailure0(DefaultPromise.java:608)
			at io.netty.util.concurrent.DefaultPromise.tryFailure(DefaultPromise.java:117)
			at io.netty.channel.nio.AbstractNioChannel$AbstractNioUnsafe.fulfillConnectPromise(AbstractNioChannel.java:321)
			at io.netty.channel.nio.AbstractNioChannel$AbstractNioUnsafe.finishConnect(AbstractNioChannel.java:337)
			at io.netty.channel.nio.NioEventLoop.processSelectedKey(NioEventLoop.java:688)
			at io.netty.channel.nio.NioEventLoop.processSelectedKeysOptimized(NioEventLoop.java:635)
			at io.netty.channel.nio.NioEventLoop.processSelectedKeys(NioEventLoop.java:552)
			at io.netty.channel.nio.NioEventLoop.run(NioEventLoop.java:514)
			... 4 common frames omitted
		Caused by: io.netty.channel.AbstractChannel$AnnotatedNoRouteToHostException: No route to host: no further information: /************:7004
		Caused by: java.net.NoRouteToHostException: No route to host: no further information
			at sun.nio.ch.SocketChannelImpl.checkConnect(Native Method)
			at sun.nio.ch.SocketChannelImpl.finishConnect(SocketChannelImpl.java:717)
			at io.netty.channel.socket.nio.NioSocketChannel.doFinishConnect(NioSocketChannel.java:330)
			at io.netty.channel.nio.AbstractNioChannel$AbstractNioUnsafe.finishConnect(AbstractNioChannel.java:334)
			at io.netty.channel.nio.NioEventLoop.processSelectedKey(NioEventLoop.java:688)
			at io.netty.channel.nio.NioEventLoop.processSelectedKeysOptimized(NioEventLoop.java:635)
			at io.netty.channel.nio.NioEventLoop.processSelectedKeys(NioEventLoop.java:552)
			at io.netty.channel.nio.NioEventLoop.run(NioEventLoop.java:514)
			at io.netty.util.concurrent.SingleThreadEventExecutor$6.run(SingleThreadEventExecutor.java:1050)
			at io.netty.util.internal.ThreadExecutorMap$2.run(ThreadExecutorMap.java:74)
			at io.netty.util.concurrent.FastThreadLocalRunnable.run(FastThreadLocalRunnable.java:30)
			at java.lang.Thread.run(Thread.java:748)
		Suppressed: io.lettuce.core.RedisConnectionException: Unable to connect to [************:7003]: No route to host: no further information: /************:7003
			at io.lettuce.core.cluster.topology.ClusterTopologyRefresh.lambda$getConnections$0(ClusterTopologyRefresh.java:290)
			at java.util.concurrent.CompletableFuture.uniWhenComplete(CompletableFuture.java:760)
			at java.util.concurrent.CompletableFuture$UniWhenComplete.tryFire(CompletableFuture.java:736)
			at java.util.concurrent.CompletableFuture.postComplete(CompletableFuture.java:474)
			at java.util.concurrent.CompletableFuture.completeExceptionally(CompletableFuture.java:1977)
			at io.lettuce.core.AbstractRedisClient.lambda$initializeChannelAsync0$4(AbstractRedisClient.java:329)
			at io.netty.util.concurrent.DefaultPromise.notifyListener0(DefaultPromise.java:577)
			at io.netty.util.concurrent.DefaultPromise.notifyListeners0(DefaultPromise.java:570)
			at io.netty.util.concurrent.DefaultPromise.notifyListenersNow(DefaultPromise.java:549)
			at io.netty.util.concurrent.DefaultPromise.notifyListeners(DefaultPromise.java:490)
			at io.netty.util.concurrent.DefaultPromise.setValue0(DefaultPromise.java:615)
			at io.netty.util.concurrent.DefaultPromise.setFailure0(DefaultPromise.java:608)
			at io.netty.util.concurrent.DefaultPromise.tryFailure(DefaultPromise.java:117)
			at io.netty.channel.nio.AbstractNioChannel$AbstractNioUnsafe.fulfillConnectPromise(AbstractNioChannel.java:321)
			at io.netty.channel.nio.AbstractNioChannel$AbstractNioUnsafe.finishConnect(AbstractNioChannel.java:337)
			at io.netty.channel.nio.NioEventLoop.processSelectedKey(NioEventLoop.java:688)
			at io.netty.channel.nio.NioEventLoop.processSelectedKeysOptimized(NioEventLoop.java:635)
			at io.netty.channel.nio.NioEventLoop.processSelectedKeys(NioEventLoop.java:552)
			at io.netty.channel.nio.NioEventLoop.run(NioEventLoop.java:514)
			... 4 common frames omitted
		Caused by: io.netty.channel.AbstractChannel$AnnotatedNoRouteToHostException: No route to host: no further information: /************:7003
		Caused by: java.net.NoRouteToHostException: No route to host: no further information
			at sun.nio.ch.SocketChannelImpl.checkConnect(Native Method)
			at sun.nio.ch.SocketChannelImpl.finishConnect(SocketChannelImpl.java:717)
			at io.netty.channel.socket.nio.NioSocketChannel.doFinishConnect(NioSocketChannel.java:330)
			at io.netty.channel.nio.AbstractNioChannel$AbstractNioUnsafe.finishConnect(AbstractNioChannel.java:334)
			at io.netty.channel.nio.NioEventLoop.processSelectedKey(NioEventLoop.java:688)
			at io.netty.channel.nio.NioEventLoop.processSelectedKeysOptimized(NioEventLoop.java:635)
			at io.netty.channel.nio.NioEventLoop.processSelectedKeys(NioEventLoop.java:552)
			at io.netty.channel.nio.NioEventLoop.run(NioEventLoop.java:514)
			at io.netty.util.concurrent.SingleThreadEventExecutor$6.run(SingleThreadEventExecutor.java:1050)
			at io.netty.util.internal.ThreadExecutorMap$2.run(ThreadExecutorMap.java:74)
			at io.netty.util.concurrent.FastThreadLocalRunnable.run(FastThreadLocalRunnable.java:30)
			at java.lang.Thread.run(Thread.java:748)
	Caused by: io.lettuce.core.RedisConnectionException: Unable to connect to [************:7001]: No route to host: no further information: /************:7001
		at io.lettuce.core.cluster.topology.ClusterTopologyRefresh.lambda$getConnections$0(ClusterTopologyRefresh.java:290)
		at java.util.concurrent.CompletableFuture.uniWhenComplete(CompletableFuture.java:760)
		at java.util.concurrent.CompletableFuture$UniWhenComplete.tryFire(CompletableFuture.java:736)
		at java.util.concurrent.CompletableFuture.postComplete(CompletableFuture.java:474)
		at java.util.concurrent.CompletableFuture.completeExceptionally(CompletableFuture.java:1977)
		at io.lettuce.core.AbstractRedisClient.lambda$initializeChannelAsync0$4(AbstractRedisClient.java:329)
		at io.netty.util.concurrent.DefaultPromise.notifyListener0(DefaultPromise.java:577)
		at io.netty.util.concurrent.DefaultPromise.notifyListeners0(DefaultPromise.java:570)
		at io.netty.util.concurrent.DefaultPromise.notifyListenersNow(DefaultPromise.java:549)
		at io.netty.util.concurrent.DefaultPromise.notifyListeners(DefaultPromise.java:490)
		at io.netty.util.concurrent.DefaultPromise.setValue0(DefaultPromise.java:615)
		at io.netty.util.concurrent.DefaultPromise.setFailure0(DefaultPromise.java:608)
		at io.netty.util.concurrent.DefaultPromise.tryFailure(DefaultPromise.java:117)
		at io.netty.channel.nio.AbstractNioChannel$AbstractNioUnsafe.fulfillConnectPromise(AbstractNioChannel.java:321)
		at io.netty.channel.nio.AbstractNioChannel$AbstractNioUnsafe.finishConnect(AbstractNioChannel.java:337)
		at io.netty.channel.nio.NioEventLoop.processSelectedKey(NioEventLoop.java:688)
		at io.netty.channel.nio.NioEventLoop.processSelectedKeysOptimized(NioEventLoop.java:635)
		at io.netty.channel.nio.NioEventLoop.processSelectedKeys(NioEventLoop.java:552)
		at io.netty.channel.nio.NioEventLoop.run(NioEventLoop.java:514)
		... 4 common frames omitted
	Caused by: io.netty.channel.AbstractChannel$AnnotatedNoRouteToHostException: No route to host: no further information: /************:7001
	Caused by: java.net.NoRouteToHostException: No route to host: no further information
		at sun.nio.ch.SocketChannelImpl.checkConnect(Native Method)
		at sun.nio.ch.SocketChannelImpl.finishConnect(SocketChannelImpl.java:717)
		at io.netty.channel.socket.nio.NioSocketChannel.doFinishConnect(NioSocketChannel.java:330)
		at io.netty.channel.nio.AbstractNioChannel$AbstractNioUnsafe.finishConnect(AbstractNioChannel.java:334)
		at io.netty.channel.nio.NioEventLoop.processSelectedKey(NioEventLoop.java:688)
		at io.netty.channel.nio.NioEventLoop.processSelectedKeysOptimized(NioEventLoop.java:635)
		at io.netty.channel.nio.NioEventLoop.processSelectedKeys(NioEventLoop.java:552)
		at io.netty.channel.nio.NioEventLoop.run(NioEventLoop.java:514)
		at io.netty.util.concurrent.SingleThreadEventExecutor$6.run(SingleThreadEventExecutor.java:1050)
		at io.netty.util.internal.ThreadExecutorMap$2.run(ThreadExecutorMap.java:74)
		at io.netty.util.concurrent.FastThreadLocalRunnable.run(FastThreadLocalRunnable.java:30)
		at java.lang.Thread.run(Thread.java:748)
Caused by: io.lettuce.core.RedisConnectionException: Unable to establish a connection to Redis Cluster at [RedisURI [host='************', port=7001], RedisURI [host='************', port=7002], RedisURI [host='************', port=7003], RedisURI [host='************', port=7004], RedisURI [host='************', port=7005], RedisURI [host='************', port=7006]]
	at io.lettuce.core.cluster.topology.AsyncConnections.get(AsyncConnections.java:89)
	at io.lettuce.core.cluster.topology.ClusterTopologyRefresh.loadViews(ClusterTopologyRefresh.java:76)
	at io.lettuce.core.cluster.RedisClusterClient.doLoadPartitions(RedisClusterClient.java:865)
	at io.lettuce.core.cluster.RedisClusterClient.loadPartitions(RedisClusterClient.java:845)
	... 10 common frames omitted
	Suppressed: io.lettuce.core.RedisConnectionException: Unable to connect to [************:7004]: No route to host: no further information: /************:7004
		at io.lettuce.core.cluster.topology.ClusterTopologyRefresh.lambda$getConnections$0(ClusterTopologyRefresh.java:290)
		at java.util.concurrent.CompletableFuture.uniWhenComplete(CompletableFuture.java:760)
		at java.util.concurrent.CompletableFuture$UniWhenComplete.tryFire(CompletableFuture.java:736)
		at java.util.concurrent.CompletableFuture.postComplete(CompletableFuture.java:474)
		at java.util.concurrent.CompletableFuture.completeExceptionally(CompletableFuture.java:1977)
		at io.lettuce.core.AbstractRedisClient.lambda$initializeChannelAsync0$4(AbstractRedisClient.java:329)
		at io.netty.util.concurrent.DefaultPromise.notifyListener0(DefaultPromise.java:577)
		at io.netty.util.concurrent.DefaultPromise.notifyListeners0(DefaultPromise.java:570)
		at io.netty.util.concurrent.DefaultPromise.notifyListenersNow(DefaultPromise.java:549)
		at io.netty.util.concurrent.DefaultPromise.notifyListeners(DefaultPromise.java:490)
		at io.netty.util.concurrent.DefaultPromise.setValue0(DefaultPromise.java:615)
		at io.netty.util.concurrent.DefaultPromise.setFailure0(DefaultPromise.java:608)
		at io.netty.util.concurrent.DefaultPromise.tryFailure(DefaultPromise.java:117)
		at io.netty.channel.nio.AbstractNioChannel$AbstractNioUnsafe.fulfillConnectPromise(AbstractNioChannel.java:321)
		at io.netty.channel.nio.AbstractNioChannel$AbstractNioUnsafe.finishConnect(AbstractNioChannel.java:337)
		at io.netty.channel.nio.NioEventLoop.processSelectedKey(NioEventLoop.java:688)
		at io.netty.channel.nio.NioEventLoop.processSelectedKeysOptimized(NioEventLoop.java:635)
		at io.netty.channel.nio.NioEventLoop.processSelectedKeys(NioEventLoop.java:552)
		at io.netty.channel.nio.NioEventLoop.run(NioEventLoop.java:514)
		... 4 common frames omitted
	Caused by: io.netty.channel.AbstractChannel$AnnotatedNoRouteToHostException: No route to host: no further information: /************:7004
	Caused by: java.net.NoRouteToHostException: No route to host: no further information
		at sun.nio.ch.SocketChannelImpl.checkConnect(Native Method)
		at sun.nio.ch.SocketChannelImpl.finishConnect(SocketChannelImpl.java:717)
		at io.netty.channel.socket.nio.NioSocketChannel.doFinishConnect(NioSocketChannel.java:330)
		at io.netty.channel.nio.AbstractNioChannel$AbstractNioUnsafe.finishConnect(AbstractNioChannel.java:334)
		at io.netty.channel.nio.NioEventLoop.processSelectedKey(NioEventLoop.java:688)
		at io.netty.channel.nio.NioEventLoop.processSelectedKeysOptimized(NioEventLoop.java:635)
		at io.netty.channel.nio.NioEventLoop.processSelectedKeys(NioEventLoop.java:552)
		at io.netty.channel.nio.NioEventLoop.run(NioEventLoop.java:514)
		at io.netty.util.concurrent.SingleThreadEventExecutor$6.run(SingleThreadEventExecutor.java:1050)
		at io.netty.util.internal.ThreadExecutorMap$2.run(ThreadExecutorMap.java:74)
		at io.netty.util.concurrent.FastThreadLocalRunnable.run(FastThreadLocalRunnable.java:30)
		at java.lang.Thread.run(Thread.java:748)
	Suppressed: io.lettuce.core.RedisConnectionException: Unable to connect to [************:7006]: No route to host: no further information: /************:7006
		at io.lettuce.core.cluster.topology.ClusterTopologyRefresh.lambda$getConnections$0(ClusterTopologyRefresh.java:290)
		at java.util.concurrent.CompletableFuture.uniWhenComplete(CompletableFuture.java:760)
		at java.util.concurrent.CompletableFuture$UniWhenComplete.tryFire(CompletableFuture.java:736)
		at java.util.concurrent.CompletableFuture.postComplete(CompletableFuture.java:474)
		at java.util.concurrent.CompletableFuture.completeExceptionally(CompletableFuture.java:1977)
		at io.lettuce.core.AbstractRedisClient.lambda$initializeChannelAsync0$4(AbstractRedisClient.java:329)
		at io.netty.util.concurrent.DefaultPromise.notifyListener0(DefaultPromise.java:577)
		at io.netty.util.concurrent.DefaultPromise.notifyListeners0(DefaultPromise.java:570)
		at io.netty.util.concurrent.DefaultPromise.notifyListenersNow(DefaultPromise.java:549)
		at io.netty.util.concurrent.DefaultPromise.notifyListeners(DefaultPromise.java:490)
		at io.netty.util.concurrent.DefaultPromise.setValue0(DefaultPromise.java:615)
		at io.netty.util.concurrent.DefaultPromise.setFailure0(DefaultPromise.java:608)
		at io.netty.util.concurrent.DefaultPromise.tryFailure(DefaultPromise.java:117)
		at io.netty.channel.nio.AbstractNioChannel$AbstractNioUnsafe.fulfillConnectPromise(AbstractNioChannel.java:321)
		at io.netty.channel.nio.AbstractNioChannel$AbstractNioUnsafe.finishConnect(AbstractNioChannel.java:337)
		at io.netty.channel.nio.NioEventLoop.processSelectedKey(NioEventLoop.java:688)
		at io.netty.channel.nio.NioEventLoop.processSelectedKeysOptimized(NioEventLoop.java:635)
		at io.netty.channel.nio.NioEventLoop.processSelectedKeys(NioEventLoop.java:552)
		at io.netty.channel.nio.NioEventLoop.run(NioEventLoop.java:514)
		... 4 common frames omitted
	Caused by: io.netty.channel.AbstractChannel$AnnotatedNoRouteToHostException: No route to host: no further information: /************:7006
	Caused by: java.net.NoRouteToHostException: No route to host: no further information
		at sun.nio.ch.SocketChannelImpl.checkConnect(Native Method)
		at sun.nio.ch.SocketChannelImpl.finishConnect(SocketChannelImpl.java:717)
		at io.netty.channel.socket.nio.NioSocketChannel.doFinishConnect(NioSocketChannel.java:330)
		at io.netty.channel.nio.AbstractNioChannel$AbstractNioUnsafe.finishConnect(AbstractNioChannel.java:334)
		at io.netty.channel.nio.NioEventLoop.processSelectedKey(NioEventLoop.java:688)
		at io.netty.channel.nio.NioEventLoop.processSelectedKeysOptimized(NioEventLoop.java:635)
		at io.netty.channel.nio.NioEventLoop.processSelectedKeys(NioEventLoop.java:552)
		at io.netty.channel.nio.NioEventLoop.run(NioEventLoop.java:514)
		at io.netty.util.concurrent.SingleThreadEventExecutor$6.run(SingleThreadEventExecutor.java:1050)
		at io.netty.util.internal.ThreadExecutorMap$2.run(ThreadExecutorMap.java:74)
		at io.netty.util.concurrent.FastThreadLocalRunnable.run(FastThreadLocalRunnable.java:30)
		at java.lang.Thread.run(Thread.java:748)
	Suppressed: io.lettuce.core.RedisConnectionException: Unable to connect to [************:7002]: No route to host: no further information: /************:7002
		at io.lettuce.core.cluster.topology.ClusterTopologyRefresh.lambda$getConnections$0(ClusterTopologyRefresh.java:290)
		at java.util.concurrent.CompletableFuture.uniWhenComplete(CompletableFuture.java:760)
		at java.util.concurrent.CompletableFuture$UniWhenComplete.tryFire(CompletableFuture.java:736)
		at java.util.concurrent.CompletableFuture.postComplete(CompletableFuture.java:474)
		at java.util.concurrent.CompletableFuture.completeExceptionally(CompletableFuture.java:1977)
		at io.lettuce.core.AbstractRedisClient.lambda$initializeChannelAsync0$4(AbstractRedisClient.java:329)
		at io.netty.util.concurrent.DefaultPromise.notifyListener0(DefaultPromise.java:577)
		at io.netty.util.concurrent.DefaultPromise.notifyListeners0(DefaultPromise.java:570)
		at io.netty.util.concurrent.DefaultPromise.notifyListenersNow(DefaultPromise.java:549)
		at io.netty.util.concurrent.DefaultPromise.notifyListeners(DefaultPromise.java:490)
		at io.netty.util.concurrent.DefaultPromise.setValue0(DefaultPromise.java:615)
		at io.netty.util.concurrent.DefaultPromise.setFailure0(DefaultPromise.java:608)
		at io.netty.util.concurrent.DefaultPromise.tryFailure(DefaultPromise.java:117)
		at io.netty.channel.nio.AbstractNioChannel$AbstractNioUnsafe.fulfillConnectPromise(AbstractNioChannel.java:321)
		at io.netty.channel.nio.AbstractNioChannel$AbstractNioUnsafe.finishConnect(AbstractNioChannel.java:337)
		at io.netty.channel.nio.NioEventLoop.processSelectedKey(NioEventLoop.java:688)
		at io.netty.channel.nio.NioEventLoop.processSelectedKeysOptimized(NioEventLoop.java:635)
		at io.netty.channel.nio.NioEventLoop.processSelectedKeys(NioEventLoop.java:552)
		at io.netty.channel.nio.NioEventLoop.run(NioEventLoop.java:514)
		... 4 common frames omitted
	Caused by: io.netty.channel.AbstractChannel$AnnotatedNoRouteToHostException: No route to host: no further information: /************:7002
	Caused by: java.net.NoRouteToHostException: No route to host: no further information
		at sun.nio.ch.SocketChannelImpl.checkConnect(Native Method)
		at sun.nio.ch.SocketChannelImpl.finishConnect(SocketChannelImpl.java:717)
		at io.netty.channel.socket.nio.NioSocketChannel.doFinishConnect(NioSocketChannel.java:330)
		at io.netty.channel.nio.AbstractNioChannel$AbstractNioUnsafe.finishConnect(AbstractNioChannel.java:334)
		at io.netty.channel.nio.NioEventLoop.processSelectedKey(NioEventLoop.java:688)
		at io.netty.channel.nio.NioEventLoop.processSelectedKeysOptimized(NioEventLoop.java:635)
		at io.netty.channel.nio.NioEventLoop.processSelectedKeys(NioEventLoop.java:552)
		at io.netty.channel.nio.NioEventLoop.run(NioEventLoop.java:514)
		at io.netty.util.concurrent.SingleThreadEventExecutor$6.run(SingleThreadEventExecutor.java:1050)
		at io.netty.util.internal.ThreadExecutorMap$2.run(ThreadExecutorMap.java:74)
		at io.netty.util.concurrent.FastThreadLocalRunnable.run(FastThreadLocalRunnable.java:30)
		at java.lang.Thread.run(Thread.java:748)
	Suppressed: io.lettuce.core.RedisConnectionException: Unable to connect to [************:7003]: No route to host: no further information: /************:7003
		at io.lettuce.core.cluster.topology.ClusterTopologyRefresh.lambda$getConnections$0(ClusterTopologyRefresh.java:290)
		at java.util.concurrent.CompletableFuture.uniWhenComplete(CompletableFuture.java:760)
		at java.util.concurrent.CompletableFuture$UniWhenComplete.tryFire(CompletableFuture.java:736)
		at java.util.concurrent.CompletableFuture.postComplete(CompletableFuture.java:474)
		at java.util.concurrent.CompletableFuture.completeExceptionally(CompletableFuture.java:1977)
		at io.lettuce.core.AbstractRedisClient.lambda$initializeChannelAsync0$4(AbstractRedisClient.java:329)
		at io.netty.util.concurrent.DefaultPromise.notifyListener0(DefaultPromise.java:577)
		at io.netty.util.concurrent.DefaultPromise.notifyListeners0(DefaultPromise.java:570)
		at io.netty.util.concurrent.DefaultPromise.notifyListenersNow(DefaultPromise.java:549)
		at io.netty.util.concurrent.DefaultPromise.notifyListeners(DefaultPromise.java:490)
		at io.netty.util.concurrent.DefaultPromise.setValue0(DefaultPromise.java:615)
		at io.netty.util.concurrent.DefaultPromise.setFailure0(DefaultPromise.java:608)
		at io.netty.util.concurrent.DefaultPromise.tryFailure(DefaultPromise.java:117)
		at io.netty.channel.nio.AbstractNioChannel$AbstractNioUnsafe.fulfillConnectPromise(AbstractNioChannel.java:321)
		at io.netty.channel.nio.AbstractNioChannel$AbstractNioUnsafe.finishConnect(AbstractNioChannel.java:337)
		at io.netty.channel.nio.NioEventLoop.processSelectedKey(NioEventLoop.java:688)
		at io.netty.channel.nio.NioEventLoop.processSelectedKeysOptimized(NioEventLoop.java:635)
		at io.netty.channel.nio.NioEventLoop.processSelectedKeys(NioEventLoop.java:552)
		at io.netty.channel.nio.NioEventLoop.run(NioEventLoop.java:514)
		... 4 common frames omitted
	Caused by: io.netty.channel.AbstractChannel$AnnotatedNoRouteToHostException: No route to host: no further information: /************:7003
	Caused by: java.net.NoRouteToHostException: No route to host: no further information
		at sun.nio.ch.SocketChannelImpl.checkConnect(Native Method)
		at sun.nio.ch.SocketChannelImpl.finishConnect(SocketChannelImpl.java:717)
		at io.netty.channel.socket.nio.NioSocketChannel.doFinishConnect(NioSocketChannel.java:330)
		at io.netty.channel.nio.AbstractNioChannel$AbstractNioUnsafe.finishConnect(AbstractNioChannel.java:334)
		at io.netty.channel.nio.NioEventLoop.processSelectedKey(NioEventLoop.java:688)
		at io.netty.channel.nio.NioEventLoop.processSelectedKeysOptimized(NioEventLoop.java:635)
		at io.netty.channel.nio.NioEventLoop.processSelectedKeys(NioEventLoop.java:552)
		at io.netty.channel.nio.NioEventLoop.run(NioEventLoop.java:514)
		at io.netty.util.concurrent.SingleThreadEventExecutor$6.run(SingleThreadEventExecutor.java:1050)
		at io.netty.util.internal.ThreadExecutorMap$2.run(ThreadExecutorMap.java:74)
		at io.netty.util.concurrent.FastThreadLocalRunnable.run(FastThreadLocalRunnable.java:30)
		at java.lang.Thread.run(Thread.java:748)
	Suppressed: io.lettuce.core.RedisConnectionException: Unable to connect to [************:7001]: No route to host: no further information: /************:7001
		at io.lettuce.core.cluster.topology.ClusterTopologyRefresh.lambda$getConnections$0(ClusterTopologyRefresh.java:290)
		at java.util.concurrent.CompletableFuture.uniWhenComplete(CompletableFuture.java:760)
		at java.util.concurrent.CompletableFuture$UniWhenComplete.tryFire(CompletableFuture.java:736)
		at java.util.concurrent.CompletableFuture.postComplete(CompletableFuture.java:474)
		at java.util.concurrent.CompletableFuture.completeExceptionally(CompletableFuture.java:1977)
		at io.lettuce.core.AbstractRedisClient.lambda$initializeChannelAsync0$4(AbstractRedisClient.java:329)
		at io.netty.util.concurrent.DefaultPromise.notifyListener0(DefaultPromise.java:577)
		at io.netty.util.concurrent.DefaultPromise.notifyListeners0(DefaultPromise.java:570)
		at io.netty.util.concurrent.DefaultPromise.notifyListenersNow(DefaultPromise.java:549)
		at io.netty.util.concurrent.DefaultPromise.notifyListeners(DefaultPromise.java:490)
		at io.netty.util.concurrent.DefaultPromise.setValue0(DefaultPromise.java:615)
		at io.netty.util.concurrent.DefaultPromise.setFailure0(DefaultPromise.java:608)
		at io.netty.util.concurrent.DefaultPromise.tryFailure(DefaultPromise.java:117)
		at io.netty.channel.nio.AbstractNioChannel$AbstractNioUnsafe.fulfillConnectPromise(AbstractNioChannel.java:321)
		at io.netty.channel.nio.AbstractNioChannel$AbstractNioUnsafe.finishConnect(AbstractNioChannel.java:337)
		at io.netty.channel.nio.NioEventLoop.processSelectedKey(NioEventLoop.java:688)
		at io.netty.channel.nio.NioEventLoop.processSelectedKeysOptimized(NioEventLoop.java:635)
		at io.netty.channel.nio.NioEventLoop.processSelectedKeys(NioEventLoop.java:552)
		at io.netty.channel.nio.NioEventLoop.run(NioEventLoop.java:514)
		... 4 common frames omitted
	Caused by: io.netty.channel.AbstractChannel$AnnotatedNoRouteToHostException: No route to host: no further information: /************:7001
	Caused by: java.net.NoRouteToHostException: No route to host: no further information
		at sun.nio.ch.SocketChannelImpl.checkConnect(Native Method)
		at sun.nio.ch.SocketChannelImpl.finishConnect(SocketChannelImpl.java:717)
		at io.netty.channel.socket.nio.NioSocketChannel.doFinishConnect(NioSocketChannel.java:330)
		at io.netty.channel.nio.AbstractNioChannel$AbstractNioUnsafe.finishConnect(AbstractNioChannel.java:334)
		at io.netty.channel.nio.NioEventLoop.processSelectedKey(NioEventLoop.java:688)
		at io.netty.channel.nio.NioEventLoop.processSelectedKeysOptimized(NioEventLoop.java:635)
		at io.netty.channel.nio.NioEventLoop.processSelectedKeys(NioEventLoop.java:552)
		at io.netty.channel.nio.NioEventLoop.run(NioEventLoop.java:514)
		at io.netty.util.concurrent.SingleThreadEventExecutor$6.run(SingleThreadEventExecutor.java:1050)
		at io.netty.util.internal.ThreadExecutorMap$2.run(ThreadExecutorMap.java:74)
		at io.netty.util.concurrent.FastThreadLocalRunnable.run(FastThreadLocalRunnable.java:30)
		at java.lang.Thread.run(Thread.java:748)
Caused by: io.lettuce.core.RedisConnectionException: Unable to connect to [************:7005]: No route to host: no further information: /************:7005
	at io.lettuce.core.cluster.topology.ClusterTopologyRefresh.lambda$getConnections$0(ClusterTopologyRefresh.java:290)
	at java.util.concurrent.CompletableFuture.uniWhenComplete(CompletableFuture.java:760)
	at java.util.concurrent.CompletableFuture$UniWhenComplete.tryFire(CompletableFuture.java:736)
	at java.util.concurrent.CompletableFuture.postComplete(CompletableFuture.java:474)
	at java.util.concurrent.CompletableFuture.completeExceptionally(CompletableFuture.java:1977)
	at io.lettuce.core.AbstractRedisClient.lambda$initializeChannelAsync0$4(AbstractRedisClient.java:329)
	at io.netty.util.concurrent.DefaultPromise.notifyListener0(DefaultPromise.java:577)
	at io.netty.util.concurrent.DefaultPromise.notifyListeners0(DefaultPromise.java:570)
	at io.netty.util.concurrent.DefaultPromise.notifyListenersNow(DefaultPromise.java:549)
	at io.netty.util.concurrent.DefaultPromise.notifyListeners(DefaultPromise.java:490)
	at io.netty.util.concurrent.DefaultPromise.setValue0(DefaultPromise.java:615)
	at io.netty.util.concurrent.DefaultPromise.setFailure0(DefaultPromise.java:608)
	at io.netty.util.concurrent.DefaultPromise.tryFailure(DefaultPromise.java:117)
	at io.netty.channel.nio.AbstractNioChannel$AbstractNioUnsafe.fulfillConnectPromise(AbstractNioChannel.java:321)
	at io.netty.channel.nio.AbstractNioChannel$AbstractNioUnsafe.finishConnect(AbstractNioChannel.java:337)
	at io.netty.channel.nio.NioEventLoop.processSelectedKey(NioEventLoop.java:688)
	at io.netty.channel.nio.NioEventLoop.processSelectedKeysOptimized(NioEventLoop.java:635)
	at io.netty.channel.nio.NioEventLoop.processSelectedKeys(NioEventLoop.java:552)
	at io.netty.channel.nio.NioEventLoop.run(NioEventLoop.java:514)
	... 4 common frames omitted
Caused by: io.netty.channel.AbstractChannel$AnnotatedNoRouteToHostException: No route to host: no further information: /************:7005
Caused by: java.net.NoRouteToHostException: No route to host: no further information
	at sun.nio.ch.SocketChannelImpl.checkConnect(Native Method)
	at sun.nio.ch.SocketChannelImpl.finishConnect(SocketChannelImpl.java:717)
	at io.netty.channel.socket.nio.NioSocketChannel.doFinishConnect(NioSocketChannel.java:330)
	at io.netty.channel.nio.AbstractNioChannel$AbstractNioUnsafe.finishConnect(AbstractNioChannel.java:334)
	at io.netty.channel.nio.NioEventLoop.processSelectedKey(NioEventLoop.java:688)
	at io.netty.channel.nio.NioEventLoop.processSelectedKeysOptimized(NioEventLoop.java:635)
	at io.netty.channel.nio.NioEventLoop.processSelectedKeys(NioEventLoop.java:552)
	at io.netty.channel.nio.NioEventLoop.run(NioEventLoop.java:514)
	at io.netty.util.concurrent.SingleThreadEventExecutor$6.run(SingleThreadEventExecutor.java:1050)
	at io.netty.util.internal.ThreadExecutorMap$2.run(ThreadExecutorMap.java:74)
	at io.netty.util.concurrent.FastThreadLocalRunnable.run(FastThreadLocalRunnable.java:30)
	at java.lang.Thread.run(Thread.java:748)
2025-05-29 06:11:01.317 [AMQP Connection ************:5672] WARN  com.rabbitmq.client.impl.ForgivingExceptionHandler.log Line:115 - An unexpected connection driver error occured (Exception message: Connection reset)
2025-05-29 06:11:02.294 [AMQP Connection ************:5672] WARN  com.rabbitmq.client.impl.ForgivingExceptionHandler.log Line:115 - An unexpected connection driver error occured (Exception message: Connection reset)
2025-05-29 06:11:02.355 [AMQP Connection ************:5672] WARN  com.rabbitmq.client.impl.ForgivingExceptionHandler.log Line:115 - An unexpected connection driver error occured (Exception message: Connection reset)
2025-05-29 06:11:03.300 [AMQP Connection ************:5672] WARN  com.rabbitmq.client.impl.ForgivingExceptionHandler.log Line:115 - An unexpected connection driver error occured (Exception message: Connection reset)
2025-05-29 06:51:32.405 [AMQP Connection ************:5672] WARN  com.rabbitmq.client.impl.ForgivingExceptionHandler.log Line:115 - An unexpected connection driver error occured (Exception message: Connection reset)
2025-05-29 06:51:33.384 [AMQP Connection ************:5672] WARN  com.rabbitmq.client.impl.ForgivingExceptionHandler.log Line:115 - An unexpected connection driver error occured (Exception message: Connection reset)
2025-05-29 06:51:33.384 [AMQP Connection ************:5672] WARN  com.rabbitmq.client.impl.ForgivingExceptionHandler.log Line:115 - An unexpected connection driver error occured (Exception message: Connection reset)
2025-05-29 06:51:34.287 [AMQP Connection ************:5672] WARN  com.rabbitmq.client.impl.ForgivingExceptionHandler.log Line:115 - An unexpected connection driver error occured (Exception message: Connection reset)
2025-05-29 07:32:33.717 [AMQP Connection ************:5672] WARN  com.rabbitmq.client.impl.ForgivingExceptionHandler.log Line:115 - An unexpected connection driver error occured (Exception message: Connection reset)
2025-05-29 07:32:34.702 [AMQP Connection ************:5672] WARN  com.rabbitmq.client.impl.ForgivingExceptionHandler.log Line:115 - An unexpected connection driver error occured (Exception message: Connection reset)
2025-05-29 07:32:34.733 [AMQP Connection ************:5672] WARN  com.rabbitmq.client.impl.ForgivingExceptionHandler.log Line:115 - An unexpected connection driver error occured (Exception message: Connection reset)
2025-05-29 07:32:35.596 [AMQP Connection ************:5672] WARN  com.rabbitmq.client.impl.ForgivingExceptionHandler.log Line:115 - An unexpected connection driver error occured (Exception message: Connection reset)
2025-05-29 08:13:04.084 [AMQP Connection ************:5672] WARN  com.rabbitmq.client.impl.ForgivingExceptionHandler.log Line:115 - An unexpected connection driver error occured (Exception message: Connection reset)
2025-05-29 08:13:05.109 [AMQP Connection ************:5672] WARN  com.rabbitmq.client.impl.ForgivingExceptionHandler.log Line:115 - An unexpected connection driver error occured (Exception message: Connection reset)
2025-05-29 08:13:05.155 [AMQP Connection ************:5672] WARN  com.rabbitmq.client.impl.ForgivingExceptionHandler.log Line:115 - An unexpected connection driver error occured (Exception message: Connection reset)
2025-05-29 08:13:05.960 [AMQP Connection ************:5672] WARN  com.rabbitmq.client.impl.ForgivingExceptionHandler.log Line:115 - An unexpected connection driver error occured (Exception message: Connection reset)
2025-05-29 08:27:18.403 [http-nio-8092-exec-18] WARN  com.simbest.boot.security.auth.provider.UumsHttpValidationAuthenticationProvider.authenticate Line:189 - UUMS登录认证器UUMS认证 【zhanglu16】 失败， 捕获【Redis exception; nested exception is io.lettuce.core.RedisException: java.io.IOException: 远程主机强迫关闭了一个现有的连接。; nested exception is org.springframework.data.redis.RedisSystemException: Redis exception; nested exception is io.lettuce.core.RedisException: java.io.IOException: 远程主机强迫关闭了一个现有的连接。】异常!
2025-05-29 08:27:18.720 [http-nio-8092-exec-18] WARN  com.simbest.boot.security.auth.service.impl.AuthUserCacheServiceImpl.loadCacheUser Line:118 - 无法通过关键字【auth:user:zhanglu16】读取用户主键ID
2025-05-29 08:27:18.953 [http-nio-8092-exec-18] WARN  com.simbest.boot.security.auth.service.impl.AuthUserCacheServiceImpl.saveOrUpdateCacheUser Line:64 - 即将在缓存中将用户权限置为空----SET AuthPermissions EMPTY----【38348】
2025-05-29 08:27:18.994 [http-nio-8092-exec-18] WARN  org.springframework.security.crypto.bcrypt.BCryptPasswordEncoder.matches Line:113 - Empty encoded password
2025-05-29 08:27:18.994 [http-nio-8092-exec-18] WARN  com.simbest.boot.security.auth.provider.CustomDaoAuthenticationProvider.additionalAuthenticationChecks Line:123 - CustomDaoAuthenticationProvider认证用户【zhanglu16】密码【null】加密密码【hNGqoOToG2bZk6NTm1y73OW/o5sEr9N8t3sqENhIe6FKmpAUwNfJHNZRHukDJUegcB8o5IpVdm3IIvQsOBeGWDTIldPuXHJbNEnYw6mZzMdqIpdMqB5T4Lg1MlMLBgktct5jAv860Pjza2imP2GNxeVCRhRSfySl8o0GsvmcQ6U=】解密密码【111.com】认证失败
2025-05-29 08:27:19.003 [http-nio-8092-exec-18] WARN  com.simbest.boot.security.auth.handle.FailedAccessDeniedHandler.handleResponse Line:59 - 无权限访问【/hnjjwz/restuumslogin】，即将返回HttpStatus.UNAUTHORIZED，状态码【401】
2025-05-29 08:27:19.004 [http-nio-8092-exec-18] WARN  com.simbest.boot.base.web.response.JsonResponse.unauthorized Line:272 - 无权限访问，即将返回【{"errcode":401,"timestamp":"2025-05-29 08:27:19","status":401,"error":"账号密码校验错误","message":"权限不足，禁止访问!","path":null,"data":null}】
2025-05-29 08:27:19.004 [http-nio-8092-exec-18] WARN  com.simbest.boot.security.auth.handle.FailedAccessDeniedHandler.handleResponse Line:62 - 登录认证发生【BadCredentialsException】异常，错误信息为【账号密码校验错误】
2025-05-29 08:27:19.004 [http-nio-8092-exec-18] WARN  com.simbest.boot.security.auth.handle.FailedAccessDeniedHandler.handleResponse Line:89 - 访问控制校验异常，即将返回【{"errcode":401,"timestamp":"2025-05-29 08:27:19","status":401,"error":"账号或密码错误","message":"账号或密码错误:账号密码校验错误","path":null,"data":null}】
2025-05-29 08:27:19.038 [http-nio-8092-exec-16] WARN  com.simbest.boot.security.auth.entryPoint.RestAccessDeniedEntryPoint.commence Line:28 - 无权限访问【/hnjjwz/getCurrentUser】，即将返回HttpStatus.UNAUTHORIZED，状态码【401】
2025-05-29 08:27:19.038 [http-nio-8092-exec-16] WARN  com.simbest.boot.base.web.response.JsonResponse.unauthorized Line:272 - 无权限访问，即将返回【{"errcode":401,"timestamp":"2025-05-29 08:27:19","status":401,"error":"Full authentication is required to access this resource","message":"权限不足，禁止访问!","path":null,"data":null}】
2025-05-29 08:30:00.018 [pool-14-thread-2] WARN  com.simbest.boot.base.repository.CustomDynamicWhere.queryForList Line:50 - 自定义查询SQL输出为：【select * from US_TODO_MODEL t  where t.send_flag = 0 and t.work_flag = 0 and t.enabled =1】
2025-05-29 08:35:00.022 [pool-14-thread-2] WARN  com.simbest.boot.base.repository.CustomDynamicWhere.queryForList Line:50 - 自定义查询SQL输出为：【select * from US_TODO_MODEL t  where t.send_flag = 0 and t.work_flag = 0 and t.enabled =1】
2025-05-29 08:40:00.012 [pool-14-thread-10] WARN  com.simbest.boot.base.repository.CustomDynamicWhere.queryForList Line:50 - 自定义查询SQL输出为：【select * from US_TODO_MODEL t  where t.send_flag = 0 and t.work_flag = 0 and t.enabled =1】
2025-05-29 08:40:09.225 [pool-14-thread-7] WARN  com.simbest.boot.base.repository.CustomDynamicWhere.queryForList Line:50 - 自定义查询SQL输出为：【select * from US_TODO_MODEL t  where t.send_flag = 0 and t.work_flag = 1 and t.enabled =1】
2025-05-29 08:45:00.017 [pool-14-thread-10] WARN  com.simbest.boot.base.repository.CustomDynamicWhere.queryForList Line:50 - 自定义查询SQL输出为：【select * from US_TODO_MODEL t  where t.send_flag = 0 and t.work_flag = 1 and t.enabled =1】
2025-05-29 08:45:00.017 [pool-14-thread-8] WARN  com.simbest.boot.base.repository.CustomDynamicWhere.queryForList Line:50 - 自定义查询SQL输出为：【select * from US_TODO_MODEL t  where t.send_flag = 0 and t.work_flag = 0 and t.enabled =1】
2025-05-29 08:50:00.009 [pool-14-thread-9] WARN  com.simbest.boot.base.repository.CustomDynamicWhere.queryForList Line:50 - 自定义查询SQL输出为：【select * from US_TODO_MODEL t  where t.send_flag = 0 and t.work_flag = 0 and t.enabled =1】
2025-05-29 08:50:00.012 [pool-14-thread-1] WARN  com.simbest.boot.base.repository.CustomDynamicWhere.queryForList Line:50 - 自定义查询SQL输出为：【select * from US_TODO_MODEL t  where t.send_flag = 0 and t.work_flag = 1 and t.enabled =1】
2025-05-29 08:55:00.015 [pool-14-thread-7] WARN  com.simbest.boot.base.repository.CustomDynamicWhere.queryForList Line:50 - 自定义查询SQL输出为：【select * from US_TODO_MODEL t  where t.send_flag = 0 and t.work_flag = 1 and t.enabled =1】
2025-05-29 08:55:00.021 [pool-14-thread-5] WARN  com.simbest.boot.base.repository.CustomDynamicWhere.queryForList Line:50 - 自定义查询SQL输出为：【select * from US_TODO_MODEL t  where t.send_flag = 0 and t.work_flag = 0 and t.enabled =1】
2025-05-29 09:00:00.005 [pool-14-thread-7] WARN  com.simbest.boot.base.repository.CustomDynamicWhere.queryForList Line:50 - 自定义查询SQL输出为：【select * from US_TODO_MODEL t  where t.send_flag = 0 and t.work_flag = 0 and t.enabled =1】
2025-05-29 09:00:00.005 [pool-14-thread-3] WARN  com.simbest.boot.cmcc.a4.task.A4TaskCreateXmlFile.execute Line:76 - >>>>>>>>>>>>>>>>>>>>>>>>>>>>>>>>>>>>>>>>>>>>>>>>>>>>>A4TaskCreateXmlFile:执行定时任务但不是Prd环境【2025-05-29T09:00:00.005】
2025-05-29 09:00:00.011 [pool-14-thread-1] WARN  com.simbest.boot.base.repository.CustomDynamicWhere.queryForList Line:50 - 自定义查询SQL输出为：【select * from US_TODO_MODEL t  where t.send_flag = 0 and t.work_flag = 1 and t.enabled =1】
2025-05-29 09:05:00.021 [pool-14-thread-3] WARN  com.simbest.boot.base.repository.CustomDynamicWhere.queryForList Line:50 - 自定义查询SQL输出为：【select * from US_TODO_MODEL t  where t.send_flag = 0 and t.work_flag = 1 and t.enabled =1】
2025-05-29 09:05:00.241 [pool-14-thread-10] WARN  com.simbest.boot.base.repository.CustomDynamicWhere.queryForList Line:50 - 自定义查询SQL输出为：【select * from US_TODO_MODEL t  where t.send_flag = 0 and t.work_flag = 0 and t.enabled =1】
2025-05-29 09:10:00.008 [pool-14-thread-5] WARN  com.simbest.boot.base.repository.CustomDynamicWhere.queryForList Line:50 - 自定义查询SQL输出为：【select * from US_TODO_MODEL t  where t.send_flag = 0 and t.work_flag = 1 and t.enabled =1】
2025-05-29 09:10:00.008 [pool-14-thread-9] WARN  com.simbest.boot.base.repository.CustomDynamicWhere.queryForList Line:50 - 自定义查询SQL输出为：【select * from US_TODO_MODEL t  where t.send_flag = 0 and t.work_flag = 0 and t.enabled =1】
2025-05-29 09:15:00.016 [pool-14-thread-5] WARN  com.simbest.boot.base.repository.CustomDynamicWhere.queryForList Line:50 - 自定义查询SQL输出为：【select * from US_TODO_MODEL t  where t.send_flag = 0 and t.work_flag = 0 and t.enabled =1】
2025-05-29 09:15:00.192 [pool-14-thread-8] WARN  com.simbest.boot.base.repository.CustomDynamicWhere.queryForList Line:50 - 自定义查询SQL输出为：【select * from US_TODO_MODEL t  where t.send_flag = 0 and t.work_flag = 1 and t.enabled =1】
2025-05-29 09:20:00.019 [pool-14-thread-7] WARN  com.simbest.boot.base.repository.CustomDynamicWhere.queryForList Line:50 - 自定义查询SQL输出为：【select * from US_TODO_MODEL t  where t.send_flag = 0 and t.work_flag = 0 and t.enabled =1】
2025-05-29 09:20:00.020 [pool-14-thread-10] WARN  com.simbest.boot.base.repository.CustomDynamicWhere.queryForList Line:50 - 自定义查询SQL输出为：【select * from US_TODO_MODEL t  where t.send_flag = 0 and t.work_flag = 1 and t.enabled =1】
2025-05-29 09:25:00.008 [pool-14-thread-8] WARN  com.simbest.boot.base.repository.CustomDynamicWhere.queryForList Line:50 - 自定义查询SQL输出为：【select * from US_TODO_MODEL t  where t.send_flag = 0 and t.work_flag = 1 and t.enabled =1】
2025-05-29 09:25:00.011 [pool-14-thread-7] WARN  com.simbest.boot.base.repository.CustomDynamicWhere.queryForList Line:50 - 自定义查询SQL输出为：【select * from US_TODO_MODEL t  where t.send_flag = 0 and t.work_flag = 0 and t.enabled =1】
2025-05-29 09:30:00.013 [pool-14-thread-2] WARN  com.simbest.boot.base.repository.CustomDynamicWhere.queryForList Line:50 - 自定义查询SQL输出为：【select * from US_TODO_MODEL t  where t.send_flag = 0 and t.work_flag = 0 and t.enabled =1】
2025-05-29 09:30:00.028 [pool-14-thread-6] WARN  com.simbest.boot.base.repository.CustomDynamicWhere.queryForList Line:50 - 自定义查询SQL输出为：【select * from US_TODO_MODEL t  where t.send_flag = 0 and t.work_flag = 1 and t.enabled =1】
2025-05-29 09:35:00.020 [pool-14-thread-6] WARN  com.simbest.boot.base.repository.CustomDynamicWhere.queryForList Line:50 - 自定义查询SQL输出为：【select * from US_TODO_MODEL t  where t.send_flag = 0 and t.work_flag = 0 and t.enabled =1】
2025-05-29 09:35:00.020 [pool-14-thread-3] WARN  com.simbest.boot.base.repository.CustomDynamicWhere.queryForList Line:50 - 自定义查询SQL输出为：【select * from US_TODO_MODEL t  where t.send_flag = 0 and t.work_flag = 1 and t.enabled =1】
2025-05-29 09:40:00.006 [pool-14-thread-10] WARN  com.simbest.boot.base.repository.CustomDynamicWhere.queryForList Line:50 - 自定义查询SQL输出为：【select * from US_TODO_MODEL t  where t.send_flag = 0 and t.work_flag = 0 and t.enabled =1】
2025-05-29 09:40:00.007 [pool-14-thread-9] WARN  com.simbest.boot.base.repository.CustomDynamicWhere.queryForList Line:50 - 自定义查询SQL输出为：【select * from US_TODO_MODEL t  where t.send_flag = 0 and t.work_flag = 1 and t.enabled =1】
2025-05-29 09:45:00.014 [pool-14-thread-3] WARN  com.simbest.boot.base.repository.CustomDynamicWhere.queryForList Line:50 - 自定义查询SQL输出为：【select * from US_TODO_MODEL t  where t.send_flag = 0 and t.work_flag = 1 and t.enabled =1】
2025-05-29 09:45:00.014 [pool-14-thread-2] WARN  com.simbest.boot.base.repository.CustomDynamicWhere.queryForList Line:50 - 自定义查询SQL输出为：【select * from US_TODO_MODEL t  where t.send_flag = 0 and t.work_flag = 0 and t.enabled =1】
2025-05-29 09:50:00.008 [pool-14-thread-6] WARN  com.simbest.boot.base.repository.CustomDynamicWhere.queryForList Line:50 - 自定义查询SQL输出为：【select * from US_TODO_MODEL t  where t.send_flag = 0 and t.work_flag = 1 and t.enabled =1】
2025-05-29 09:50:00.012 [pool-14-thread-1] WARN  com.simbest.boot.base.repository.CustomDynamicWhere.queryForList Line:50 - 自定义查询SQL输出为：【select * from US_TODO_MODEL t  where t.send_flag = 0 and t.work_flag = 0 and t.enabled =1】
2025-05-29 09:55:00.021 [pool-14-thread-9] WARN  com.simbest.boot.base.repository.CustomDynamicWhere.queryForList Line:50 - 自定义查询SQL输出为：【select * from US_TODO_MODEL t  where t.send_flag = 0 and t.work_flag = 0 and t.enabled =1】
2025-05-29 09:55:00.021 [pool-14-thread-1] WARN  com.simbest.boot.base.repository.CustomDynamicWhere.queryForList Line:50 - 自定义查询SQL输出为：【select * from US_TODO_MODEL t  where t.send_flag = 0 and t.work_flag = 1 and t.enabled =1】
2025-05-29 10:00:00.012 [pool-14-thread-5] WARN  com.simbest.boot.base.repository.CustomDynamicWhere.queryForList Line:50 - 自定义查询SQL输出为：【select * from US_TODO_MODEL t  where t.send_flag = 0 and t.work_flag = 1 and t.enabled =1】
2025-05-29 10:00:00.013 [pool-14-thread-2] WARN  com.simbest.boot.cmcc.a4.task.A4TaskCreateXmlFile.execute Line:76 - >>>>>>>>>>>>>>>>>>>>>>>>>>>>>>>>>>>>>>>>>>>>>>>>>>>>>A4TaskCreateXmlFile:执行定时任务但不是Prd环境【2025-05-29T10:00:00.012】
2025-05-29 10:00:00.022 [pool-14-thread-1] WARN  com.simbest.boot.base.repository.CustomDynamicWhere.queryForList Line:50 - 自定义查询SQL输出为：【select * from US_TODO_MODEL t  where t.send_flag = 0 and t.work_flag = 0 and t.enabled =1】
2025-05-29 10:05:00.007 [pool-14-thread-7] WARN  com.simbest.boot.base.repository.CustomDynamicWhere.queryForList Line:50 - 自定义查询SQL输出为：【select * from US_TODO_MODEL t  where t.send_flag = 0 and t.work_flag = 1 and t.enabled =1】
2025-05-29 10:05:00.008 [pool-14-thread-6] WARN  com.simbest.boot.base.repository.CustomDynamicWhere.queryForList Line:50 - 自定义查询SQL输出为：【select * from US_TODO_MODEL t  where t.send_flag = 0 and t.work_flag = 0 and t.enabled =1】
2025-05-29 10:10:00.017 [pool-14-thread-2] WARN  com.simbest.boot.base.repository.CustomDynamicWhere.queryForList Line:50 - 自定义查询SQL输出为：【select * from US_TODO_MODEL t  where t.send_flag = 0 and t.work_flag = 0 and t.enabled =1】
2025-05-29 10:10:00.018 [pool-14-thread-7] WARN  com.simbest.boot.base.repository.CustomDynamicWhere.queryForList Line:50 - 自定义查询SQL输出为：【select * from US_TODO_MODEL t  where t.send_flag = 0 and t.work_flag = 1 and t.enabled =1】
2025-05-29 10:15:00.028 [pool-14-thread-7] WARN  com.simbest.boot.base.repository.CustomDynamicWhere.queryForList Line:50 - 自定义查询SQL输出为：【select * from US_TODO_MODEL t  where t.send_flag = 0 and t.work_flag = 1 and t.enabled =1】
2025-05-29 10:15:00.029 [pool-14-thread-2] WARN  com.simbest.boot.base.repository.CustomDynamicWhere.queryForList Line:50 - 自定义查询SQL输出为：【select * from US_TODO_MODEL t  where t.send_flag = 0 and t.work_flag = 0 and t.enabled =1】
2025-05-29 10:19:30.399 [http-nio-8092-exec-8] WARN  com.simbest.boot.security.auth.service.impl.AuthUserCacheServiceImpl.loadCacheUser Line:118 - 无法通过关键字【auth:user:zhanglu16】读取用户主键ID
2025-05-29 10:19:30.657 [http-nio-8092-exec-8] WARN  com.simbest.boot.security.auth.service.impl.AuthUserCacheServiceImpl.saveOrUpdateCacheUser Line:64 - 即将在缓存中将用户权限置为空----SET AuthPermissions EMPTY----【38348】
2025-05-29 10:20:00.010 [pool-14-thread-1] WARN  com.simbest.boot.base.repository.CustomDynamicWhere.queryForList Line:50 - 自定义查询SQL输出为：【select * from US_TODO_MODEL t  where t.send_flag = 0 and t.work_flag = 1 and t.enabled =1】
2025-05-29 10:20:00.011 [pool-14-thread-10] WARN  com.simbest.boot.base.repository.CustomDynamicWhere.queryForList Line:50 - 自定义查询SQL输出为：【select * from US_TODO_MODEL t  where t.send_flag = 0 and t.work_flag = 0 and t.enabled =1】
2025-05-29 10:25:00.013 [pool-14-thread-10] WARN  com.simbest.boot.base.repository.CustomDynamicWhere.queryForList Line:50 - 自定义查询SQL输出为：【select * from US_TODO_MODEL t  where t.send_flag = 0 and t.work_flag = 1 and t.enabled =1】
2025-05-29 10:25:00.014 [pool-14-thread-4] WARN  com.simbest.boot.base.repository.CustomDynamicWhere.queryForList Line:50 - 自定义查询SQL输出为：【select * from US_TODO_MODEL t  where t.send_flag = 0 and t.work_flag = 0 and t.enabled =1】
2025-05-29 10:30:00.020 [pool-14-thread-7] WARN  com.simbest.boot.base.repository.CustomDynamicWhere.queryForList Line:50 - 自定义查询SQL输出为：【select * from US_TODO_MODEL t  where t.send_flag = 0 and t.work_flag = 0 and t.enabled =1】
2025-05-29 10:30:00.021 [pool-14-thread-5] WARN  com.simbest.boot.base.repository.CustomDynamicWhere.queryForList Line:50 - 自定义查询SQL输出为：【select * from US_TODO_MODEL t  where t.send_flag = 0 and t.work_flag = 1 and t.enabled =1】
2025-05-29 10:35:00.010 [pool-14-thread-10] WARN  com.simbest.boot.base.repository.CustomDynamicWhere.queryForList Line:50 - 自定义查询SQL输出为：【select * from US_TODO_MODEL t  where t.send_flag = 0 and t.work_flag = 0 and t.enabled =1】
2025-05-29 10:35:00.013 [pool-14-thread-6] WARN  com.simbest.boot.base.repository.CustomDynamicWhere.queryForList Line:50 - 自定义查询SQL输出为：【select * from US_TODO_MODEL t  where t.send_flag = 0 and t.work_flag = 1 and t.enabled =1】
2025-05-29 10:40:00.016 [pool-14-thread-2] WARN  com.simbest.boot.base.repository.CustomDynamicWhere.queryForList Line:50 - 自定义查询SQL输出为：【select * from US_TODO_MODEL t  where t.send_flag = 0 and t.work_flag = 1 and t.enabled =1】
2025-05-29 10:40:00.017 [pool-14-thread-3] WARN  com.simbest.boot.base.repository.CustomDynamicWhere.queryForList Line:50 - 自定义查询SQL输出为：【select * from US_TODO_MODEL t  where t.send_flag = 0 and t.work_flag = 0 and t.enabled =1】
2025-05-29 10:45:00.017 [pool-14-thread-3] WARN  com.simbest.boot.base.repository.CustomDynamicWhere.queryForList Line:50 - 自定义查询SQL输出为：【select * from US_TODO_MODEL t  where t.send_flag = 0 and t.work_flag = 0 and t.enabled =1】
2025-05-29 10:45:00.017 [pool-14-thread-2] WARN  com.simbest.boot.base.repository.CustomDynamicWhere.queryForList Line:50 - 自定义查询SQL输出为：【select * from US_TODO_MODEL t  where t.send_flag = 0 and t.work_flag = 1 and t.enabled =1】
2025-05-29 10:50:00.018 [pool-14-thread-1] WARN  com.simbest.boot.base.repository.CustomDynamicWhere.queryForList Line:50 - 自定义查询SQL输出为：【select * from US_TODO_MODEL t  where t.send_flag = 0 and t.work_flag = 0 and t.enabled =1】
2025-05-29 10:50:00.120 [pool-14-thread-9] WARN  com.simbest.boot.base.repository.CustomDynamicWhere.queryForList Line:50 - 自定义查询SQL输出为：【select * from US_TODO_MODEL t  where t.send_flag = 0 and t.work_flag = 1 and t.enabled =1】
2025-05-29 10:55:00.023 [pool-14-thread-1] WARN  com.simbest.boot.base.repository.CustomDynamicWhere.queryForList Line:50 - 自定义查询SQL输出为：【select * from US_TODO_MODEL t  where t.send_flag = 0 and t.work_flag = 0 and t.enabled =1】
2025-05-29 10:55:00.023 [pool-14-thread-4] WARN  com.simbest.boot.base.repository.CustomDynamicWhere.queryForList Line:50 - 自定义查询SQL输出为：【select * from US_TODO_MODEL t  where t.send_flag = 0 and t.work_flag = 1 and t.enabled =1】
2025-05-29 11:00:00.012 [pool-14-thread-2] WARN  com.simbest.boot.base.repository.CustomDynamicWhere.queryForList Line:50 - 自定义查询SQL输出为：【select * from US_TODO_MODEL t  where t.send_flag = 0 and t.work_flag = 1 and t.enabled =1】
2025-05-29 11:00:00.013 [pool-14-thread-7] WARN  com.simbest.boot.base.repository.CustomDynamicWhere.queryForList Line:50 - 自定义查询SQL输出为：【select * from US_TODO_MODEL t  where t.send_flag = 0 and t.work_flag = 0 and t.enabled =1】
2025-05-29 11:00:00.017 [pool-14-thread-3] WARN  com.simbest.boot.cmcc.a4.task.A4TaskCreateXmlFile.execute Line:76 - >>>>>>>>>>>>>>>>>>>>>>>>>>>>>>>>>>>>>>>>>>>>>>>>>>>>>A4TaskCreateXmlFile:执行定时任务但不是Prd环境【2025-05-29T11:00:00.017】
2025-05-29 11:05:00.012 [pool-14-thread-6] WARN  com.simbest.boot.base.repository.CustomDynamicWhere.queryForList Line:50 - 自定义查询SQL输出为：【select * from US_TODO_MODEL t  where t.send_flag = 0 and t.work_flag = 1 and t.enabled =1】
2025-05-29 11:05:00.012 [pool-14-thread-2] WARN  com.simbest.boot.base.repository.CustomDynamicWhere.queryForList Line:50 - 自定义查询SQL输出为：【select * from US_TODO_MODEL t  where t.send_flag = 0 and t.work_flag = 0 and t.enabled =1】
2025-05-29 11:10:00.022 [pool-14-thread-5] WARN  com.simbest.boot.base.repository.CustomDynamicWhere.queryForList Line:50 - 自定义查询SQL输出为：【select * from US_TODO_MODEL t  where t.send_flag = 0 and t.work_flag = 1 and t.enabled =1】
2025-05-29 11:10:00.024 [pool-14-thread-6] WARN  com.simbest.boot.base.repository.CustomDynamicWhere.queryForList Line:50 - 自定义查询SQL输出为：【select * from US_TODO_MODEL t  where t.send_flag = 0 and t.work_flag = 0 and t.enabled =1】
2025-05-29 11:15:00.009 [pool-14-thread-9] WARN  com.simbest.boot.base.repository.CustomDynamicWhere.queryForList Line:50 - 自定义查询SQL输出为：【select * from US_TODO_MODEL t  where t.send_flag = 0 and t.work_flag = 1 and t.enabled =1】
2025-05-29 11:15:00.012 [pool-14-thread-1] WARN  com.simbest.boot.base.repository.CustomDynamicWhere.queryForList Line:50 - 自定义查询SQL输出为：【select * from US_TODO_MODEL t  where t.send_flag = 0 and t.work_flag = 0 and t.enabled =1】
2025-05-29 11:20:00.019 [pool-14-thread-2] WARN  com.simbest.boot.base.repository.CustomDynamicWhere.queryForList Line:50 - 自定义查询SQL输出为：【select * from US_TODO_MODEL t  where t.send_flag = 0 and t.work_flag = 1 and t.enabled =1】
2025-05-29 11:20:00.020 [pool-14-thread-9] WARN  com.simbest.boot.base.repository.CustomDynamicWhere.queryForList Line:50 - 自定义查询SQL输出为：【select * from US_TODO_MODEL t  where t.send_flag = 0 and t.work_flag = 0 and t.enabled =1】
2025-05-29 11:25:00.004 [pool-14-thread-5] WARN  com.simbest.boot.base.repository.CustomDynamicWhere.queryForList Line:50 - 自定义查询SQL输出为：【select * from US_TODO_MODEL t  where t.send_flag = 0 and t.work_flag = 1 and t.enabled =1】
2025-05-29 11:25:00.018 [pool-14-thread-6] WARN  com.simbest.boot.base.repository.CustomDynamicWhere.queryForList Line:50 - 自定义查询SQL输出为：【select * from US_TODO_MODEL t  where t.send_flag = 0 and t.work_flag = 0 and t.enabled =1】
2025-05-29 11:30:00.015 [pool-14-thread-9] WARN  com.simbest.boot.base.repository.CustomDynamicWhere.queryForList Line:50 - 自定义查询SQL输出为：【select * from US_TODO_MODEL t  where t.send_flag = 0 and t.work_flag = 0 and t.enabled =1】
2025-05-29 11:30:00.015 [pool-14-thread-3] WARN  com.simbest.boot.base.repository.CustomDynamicWhere.queryForList Line:50 - 自定义查询SQL输出为：【select * from US_TODO_MODEL t  where t.send_flag = 0 and t.work_flag = 1 and t.enabled =1】
2025-05-29 11:35:00.016 [pool-14-thread-10] WARN  com.simbest.boot.base.repository.CustomDynamicWhere.queryForList Line:50 - 自定义查询SQL输出为：【select * from US_TODO_MODEL t  where t.send_flag = 0 and t.work_flag = 1 and t.enabled =1】
2025-05-29 11:35:00.018 [pool-14-thread-2] WARN  com.simbest.boot.base.repository.CustomDynamicWhere.queryForList Line:50 - 自定义查询SQL输出为：【select * from US_TODO_MODEL t  where t.send_flag = 0 and t.work_flag = 0 and t.enabled =1】
2025-05-29 11:40:00.010 [pool-14-thread-9] WARN  com.simbest.boot.base.repository.CustomDynamicWhere.queryForList Line:50 - 自定义查询SQL输出为：【select * from US_TODO_MODEL t  where t.send_flag = 0 and t.work_flag = 1 and t.enabled =1】
2025-05-29 11:40:00.011 [pool-14-thread-6] WARN  com.simbest.boot.base.repository.CustomDynamicWhere.queryForList Line:50 - 自定义查询SQL输出为：【select * from US_TODO_MODEL t  where t.send_flag = 0 and t.work_flag = 0 and t.enabled =1】
2025-05-29 11:45:00.025 [pool-14-thread-6] WARN  com.simbest.boot.base.repository.CustomDynamicWhere.queryForList Line:50 - 自定义查询SQL输出为：【select * from US_TODO_MODEL t  where t.send_flag = 0 and t.work_flag = 0 and t.enabled =1】
2025-05-29 11:45:00.048 [pool-14-thread-4] WARN  com.simbest.boot.base.repository.CustomDynamicWhere.queryForList Line:50 - 自定义查询SQL输出为：【select * from US_TODO_MODEL t  where t.send_flag = 0 and t.work_flag = 1 and t.enabled =1】
2025-05-29 11:50:00.009 [pool-14-thread-2] WARN  com.simbest.boot.base.repository.CustomDynamicWhere.queryForList Line:50 - 自定义查询SQL输出为：【select * from US_TODO_MODEL t  where t.send_flag = 0 and t.work_flag = 0 and t.enabled =1】
2025-05-29 11:50:00.010 [pool-14-thread-5] WARN  com.simbest.boot.base.repository.CustomDynamicWhere.queryForList Line:50 - 自定义查询SQL输出为：【select * from US_TODO_MODEL t  where t.send_flag = 0 and t.work_flag = 1 and t.enabled =1】
2025-05-29 11:55:00.010 [pool-14-thread-10] WARN  com.simbest.boot.base.repository.CustomDynamicWhere.queryForList Line:50 - 自定义查询SQL输出为：【select * from US_TODO_MODEL t  where t.send_flag = 0 and t.work_flag = 1 and t.enabled =1】
2025-05-29 11:55:00.011 [pool-14-thread-6] WARN  com.simbest.boot.base.repository.CustomDynamicWhere.queryForList Line:50 - 自定义查询SQL输出为：【select * from US_TODO_MODEL t  where t.send_flag = 0 and t.work_flag = 0 and t.enabled =1】
2025-05-29 12:00:00.017 [pool-14-thread-5] WARN  com.simbest.boot.base.repository.CustomDynamicWhere.queryForList Line:50 - 自定义查询SQL输出为：【select * from US_TODO_MODEL t  where t.send_flag = 0 and t.work_flag = 0 and t.enabled =1】
2025-05-29 12:00:00.017 [pool-14-thread-3] WARN  com.simbest.boot.base.repository.CustomDynamicWhere.queryForList Line:50 - 自定义查询SQL输出为：【select * from US_TODO_MODEL t  where t.send_flag = 0 and t.work_flag = 1 and t.enabled =1】
2025-05-29 12:00:00.018 [pool-14-thread-1] WARN  com.simbest.boot.cmcc.a4.task.A4TaskCreateXmlFile.execute Line:76 - >>>>>>>>>>>>>>>>>>>>>>>>>>>>>>>>>>>>>>>>>>>>>>>>>>>>>A4TaskCreateXmlFile:执行定时任务但不是Prd环境【2025-05-29T12:00:00.018】
2025-05-29 12:05:00.008 [pool-14-thread-2] WARN  com.simbest.boot.base.repository.CustomDynamicWhere.queryForList Line:50 - 自定义查询SQL输出为：【select * from US_TODO_MODEL t  where t.send_flag = 0 and t.work_flag = 1 and t.enabled =1】
2025-05-29 12:05:00.009 [pool-14-thread-1] WARN  com.simbest.boot.base.repository.CustomDynamicWhere.queryForList Line:50 - 自定义查询SQL输出为：【select * from US_TODO_MODEL t  where t.send_flag = 0 and t.work_flag = 0 and t.enabled =1】
2025-05-29 12:10:00.014 [pool-14-thread-7] WARN  com.simbest.boot.base.repository.CustomDynamicWhere.queryForList Line:50 - 自定义查询SQL输出为：【select * from US_TODO_MODEL t  where t.send_flag = 0 and t.work_flag = 0 and t.enabled =1】
2025-05-29 12:10:00.014 [pool-14-thread-6] WARN  com.simbest.boot.base.repository.CustomDynamicWhere.queryForList Line:50 - 自定义查询SQL输出为：【select * from US_TODO_MODEL t  where t.send_flag = 0 and t.work_flag = 1 and t.enabled =1】
2025-05-29 12:15:00.012 [pool-14-thread-2] WARN  com.simbest.boot.base.repository.CustomDynamicWhere.queryForList Line:50 - 自定义查询SQL输出为：【select * from US_TODO_MODEL t  where t.send_flag = 0 and t.work_flag = 0 and t.enabled =1】
2025-05-29 12:15:00.012 [pool-14-thread-6] WARN  com.simbest.boot.base.repository.CustomDynamicWhere.queryForList Line:50 - 自定义查询SQL输出为：【select * from US_TODO_MODEL t  where t.send_flag = 0 and t.work_flag = 1 and t.enabled =1】
2025-05-29 12:20:00.017 [pool-14-thread-1] WARN  com.simbest.boot.base.repository.CustomDynamicWhere.queryForList Line:50 - 自定义查询SQL输出为：【select * from US_TODO_MODEL t  where t.send_flag = 0 and t.work_flag = 0 and t.enabled =1】
2025-05-29 12:20:00.018 [pool-14-thread-2] WARN  com.simbest.boot.base.repository.CustomDynamicWhere.queryForList Line:50 - 自定义查询SQL输出为：【select * from US_TODO_MODEL t  where t.send_flag = 0 and t.work_flag = 1 and t.enabled =1】
2025-05-29 12:25:00.007 [pool-14-thread-9] WARN  com.simbest.boot.base.repository.CustomDynamicWhere.queryForList Line:50 - 自定义查询SQL输出为：【select * from US_TODO_MODEL t  where t.send_flag = 0 and t.work_flag = 0 and t.enabled =1】
2025-05-29 12:25:00.008 [pool-14-thread-2] WARN  com.simbest.boot.base.repository.CustomDynamicWhere.queryForList Line:50 - 自定义查询SQL输出为：【select * from US_TODO_MODEL t  where t.send_flag = 0 and t.work_flag = 1 and t.enabled =1】
2025-05-29 12:30:00.007 [pool-14-thread-2] WARN  com.simbest.boot.base.repository.CustomDynamicWhere.queryForList Line:50 - 自定义查询SQL输出为：【select * from US_TODO_MODEL t  where t.send_flag = 0 and t.work_flag = 0 and t.enabled =1】
2025-05-29 12:30:00.007 [pool-14-thread-5] WARN  com.simbest.boot.base.repository.CustomDynamicWhere.queryForList Line:50 - 自定义查询SQL输出为：【select * from US_TODO_MODEL t  where t.send_flag = 0 and t.work_flag = 1 and t.enabled =1】
2025-05-29 12:35:00.005 [pool-14-thread-10] WARN  com.simbest.boot.base.repository.CustomDynamicWhere.queryForList Line:50 - 自定义查询SQL输出为：【select * from US_TODO_MODEL t  where t.send_flag = 0 and t.work_flag = 1 and t.enabled =1】
2025-05-29 12:35:00.010 [pool-14-thread-6] WARN  com.simbest.boot.base.repository.CustomDynamicWhere.queryForList Line:50 - 自定义查询SQL输出为：【select * from US_TODO_MODEL t  where t.send_flag = 0 and t.work_flag = 0 and t.enabled =1】
2025-05-29 12:58:35.522 [redisson-netty-5-21] WARN  org.redisson.client.handler.CommandDecoder.completeResponse Line:445 - response has been skipped due to timeout! channel: [id: 0x0f45aa1f, L:/***********:65500 - R:************/************:7003], command: (CLUSTER NODES), params: [], result: [ClusterNodeInfo [nodeId=4dcfd30d1191cb5e65ef69e88dc76592f8b3d14d, address=redis://************:7005, flags=[MASTER], slaveOf=null, slotRanges=[[10923-16383]]], ClusterNodeInfo [nodeId=248f8c5f1a3d07d891f1bcf924c02097c5509e0d, address=redis://************:7001, flags=[SLAVE], slaveOf=64f45f937e6f8c36d0fb7cf09195403c4cccdf1d, slotRanges=[]], ClusterNodeInfo [nodeId=02aff3b78f8c09c43cbfc2346eac68a16a942097, address=redis://************:7003, flags=[MASTER, MYSELF], slaveOf=null, slotRanges=[[5461-10922]]], ClusterNodeInfo [nodeId=85e61682488d3a355264d212607a63832da60e8c, address=redis://************:7006, flags=[SLAVE], slaveOf=02aff3b78f8c09c43cbfc2346eac68a16a942097, slotRanges=[]], ClusterNodeInfo [nodeId=64f45f937e6f8c36d0fb7cf09195403c4cccdf1d, address=redis://************:7004, flags=[MASTER], slaveOf=null, slotRanges=[[0-5460]]], ClusterNodeInfo [nodeId=5514edc6d1aaab0526b0093a424e9f352e9914ed, address=redis://************:7002, flags=[SLAVE], slaveOf=4dcfd30d1191cb5e65ef69e88dc76592f8b3d14d, slotRanges=[]]]
2025-05-29 12:58:50.124 [AMQP Connection ************:5672] WARN  com.rabbitmq.client.impl.ForgivingExceptionHandler.log Line:115 - An unexpected connection driver error occured (Exception message: Connection reset)
2025-05-29 12:59:07.374 [AMQP Connection ************:5672] WARN  com.rabbitmq.client.impl.ForgivingExceptionHandler.log Line:115 - An unexpected connection driver error occured (Exception message: Connection reset)
2025-05-29 12:59:07.374 [AMQP Connection ************:5672] WARN  com.rabbitmq.client.impl.ForgivingExceptionHandler.log Line:115 - An unexpected connection driver error occured (Exception message: Connection reset)
2025-05-29 12:59:11.160 [lettuce-nioEventLoop-4-5] WARN  io.lettuce.core.cluster.topology.ClusterTopologyRefresh.lambda$getConnections$0 Line:284 - Unable to connect to [************:7001]: connection timed out: /************:7001
2025-05-29 12:59:11.160 [lettuce-nioEventLoop-4-10] WARN  io.lettuce.core.cluster.topology.ClusterTopologyRefresh.lambda$getConnections$0 Line:284 - Unable to connect to [************:7006]: connection timed out: /************:7006
2025-05-29 12:59:11.160 [lettuce-nioEventLoop-4-8] WARN  io.lettuce.core.cluster.topology.ClusterTopologyRefresh.lambda$getConnections$0 Line:284 - Unable to connect to [************:7004]: connection timed out: /************:7004
2025-05-29 12:59:11.160 [lettuce-nioEventLoop-4-7] WARN  io.lettuce.core.cluster.topology.ClusterTopologyRefresh.lambda$getConnections$0 Line:284 - Unable to connect to [************:7003]: connection timed out: /************:7003
2025-05-29 12:59:11.160 [lettuce-nioEventLoop-4-6] WARN  io.lettuce.core.cluster.topology.ClusterTopologyRefresh.lambda$getConnections$0 Line:284 - Unable to connect to [************:7002]: connection timed out: /************:7002
2025-05-29 12:59:11.160 [lettuce-nioEventLoop-4-9] WARN  io.lettuce.core.cluster.topology.ClusterTopologyRefresh.lambda$getConnections$0 Line:284 - Unable to connect to [************:7005]: connection timed out: /************:7005
2025-05-29 12:59:16.702 [org.springframework.amqp.rabbit.RabbitListenerEndpointContainer#1-23] WARN  org.springframework.amqp.rabbit.listener.SimpleMessageListenerContainer.logConsumerException Line:1440 - Consumer raised exception, processing can restart if the connection factory supports it. Exception summary: org.springframework.amqp.AmqpConnectException: java.net.ConnectException: Connection timed out: connect
2025-05-29 13:20:09.295 [redisson-netty-5-10] WARN  org.redisson.client.handler.CommandDecoder.completeResponse Line:445 - response has been skipped due to timeout! channel: [id: 0x1886e738, L:/***********:55960 - R:************/************:7003], command: (CLUSTER NODES), params: [], result: [ClusterNodeInfo [nodeId=4dcfd30d1191cb5e65ef69e88dc76592f8b3d14d, address=redis://************:7005, flags=[MASTER], slaveOf=null, slotRanges=[[10923-16383]]], ClusterNodeInfo [nodeId=248f8c5f1a3d07d891f1bcf924c02097c5509e0d, address=redis://************:7001, flags=[SLAVE], slaveOf=64f45f937e6f8c36d0fb7cf09195403c4cccdf1d, slotRanges=[]], ClusterNodeInfo [nodeId=02aff3b78f8c09c43cbfc2346eac68a16a942097, address=redis://************:7003, flags=[MASTER, MYSELF], slaveOf=null, slotRanges=[[5461-10922]]], ClusterNodeInfo [nodeId=85e61682488d3a355264d212607a63832da60e8c, address=redis://************:7006, flags=[SLAVE], slaveOf=02aff3b78f8c09c43cbfc2346eac68a16a942097, slotRanges=[]], ClusterNodeInfo [nodeId=64f45f937e6f8c36d0fb7cf09195403c4cccdf1d, address=redis://************:7004, flags=[MASTER], slaveOf=null, slotRanges=[[0-5460]]], ClusterNodeInfo [nodeId=5514edc6d1aaab0526b0093a424e9f352e9914ed, address=redis://************:7002, flags=[SLAVE], slaveOf=4dcfd30d1191cb5e65ef69e88dc76592f8b3d14d, slotRanges=[]]]
2025-05-29 13:20:19.917 [redisson-netty-5-15] WARN  org.redisson.client.handler.CommandDecoder.completeResponse Line:445 - response has been skipped due to timeout! channel: [id: 0x2a7ae765, L:/***********:61959 - R:************/************:7003], command: (CLUSTER NODES), params: [], result: [ClusterNodeInfo [nodeId=4dcfd30d1191cb5e65ef69e88dc76592f8b3d14d, address=redis://************:7005, flags=[MASTER], slaveOf=null, slotRanges=[[10923-16383]]], ClusterNodeInfo [nodeId=248f8c5f1a3d07d891f1bcf924c02097c5509e0d, address=redis://************:7001, flags=[SLAVE], slaveOf=64f45f937e6f8c36d0fb7cf09195403c4cccdf1d, slotRanges=[]], ClusterNodeInfo [nodeId=02aff3b78f8c09c43cbfc2346eac68a16a942097, address=redis://************:7003, flags=[MASTER, MYSELF], slaveOf=null, slotRanges=[[5461-10922]]], ClusterNodeInfo [nodeId=85e61682488d3a355264d212607a63832da60e8c, address=redis://************:7006, flags=[SLAVE], slaveOf=02aff3b78f8c09c43cbfc2346eac68a16a942097, slotRanges=[]], ClusterNodeInfo [nodeId=64f45f937e6f8c36d0fb7cf09195403c4cccdf1d, address=redis://************:7004, flags=[MASTER], slaveOf=null, slotRanges=[[0-5460]]], ClusterNodeInfo [nodeId=5514edc6d1aaab0526b0093a424e9f352e9914ed, address=redis://************:7002, flags=[SLAVE], slaveOf=4dcfd30d1191cb5e65ef69e88dc76592f8b3d14d, slotRanges=[]]]
2025-05-29 13:25:00.005 [pool-14-thread-7] WARN  com.simbest.boot.base.repository.CustomDynamicWhere.queryForList Line:50 - 自定义查询SQL输出为：【select * from US_TODO_MODEL t  where t.send_flag = 0 and t.work_flag = 0 and t.enabled =1】
2025-05-29 13:25:00.005 [pool-14-thread-9] WARN  com.simbest.boot.base.repository.CustomDynamicWhere.queryForList Line:50 - 自定义查询SQL输出为：【select * from US_TODO_MODEL t  where t.send_flag = 0 and t.work_flag = 1 and t.enabled =1】
2025-05-29 13:30:00.013 [pool-14-thread-4] WARN  com.simbest.boot.base.repository.CustomDynamicWhere.queryForList Line:50 - 自定义查询SQL输出为：【select * from US_TODO_MODEL t  where t.send_flag = 0 and t.work_flag = 1 and t.enabled =1】
2025-05-29 13:30:00.013 [pool-14-thread-8] WARN  com.simbest.boot.base.repository.CustomDynamicWhere.queryForList Line:50 - 自定义查询SQL输出为：【select * from US_TODO_MODEL t  where t.send_flag = 0 and t.work_flag = 0 and t.enabled =1】
2025-05-29 13:35:00.012 [pool-14-thread-10] WARN  com.simbest.boot.base.repository.CustomDynamicWhere.queryForList Line:50 - 自定义查询SQL输出为：【select * from US_TODO_MODEL t  where t.send_flag = 0 and t.work_flag = 1 and t.enabled =1】
2025-05-29 13:35:00.021 [pool-14-thread-2] WARN  com.simbest.boot.base.repository.CustomDynamicWhere.queryForList Line:50 - 自定义查询SQL输出为：【select * from US_TODO_MODEL t  where t.send_flag = 0 and t.work_flag = 0 and t.enabled =1】
2025-05-29 13:40:00.020 [pool-14-thread-1] WARN  com.simbest.boot.base.repository.CustomDynamicWhere.queryForList Line:50 - 自定义查询SQL输出为：【select * from US_TODO_MODEL t  where t.send_flag = 0 and t.work_flag = 0 and t.enabled =1】
2025-05-29 13:40:00.020 [pool-14-thread-3] WARN  com.simbest.boot.base.repository.CustomDynamicWhere.queryForList Line:50 - 自定义查询SQL输出为：【select * from US_TODO_MODEL t  where t.send_flag = 0 and t.work_flag = 1 and t.enabled =1】
2025-05-29 13:41:10.825 [redisson-netty-5-1] WARN  org.redisson.client.handler.CommandDecoder.completeResponse Line:445 - response has been skipped due to timeout! channel: [id: 0xb28ede0c, L:/***********:62073 - R:************/************:7003], command: (CLUSTER NODES), params: [], result: [ClusterNodeInfo [nodeId=4dcfd30d1191cb5e65ef69e88dc76592f8b3d14d, address=redis://************:7005, flags=[MASTER], slaveOf=null, slotRanges=[[10923-16383]]], ClusterNodeInfo [nodeId=248f8c5f1a3d07d891f1bcf924c02097c5509e0d, address=redis://************:7001, flags=[SLAVE], slaveOf=64f45f937e6f8c36d0fb7cf09195403c4cccdf1d, slotRanges=[]], ClusterNodeInfo [nodeId=02aff3b78f8c09c43cbfc2346eac68a16a942097, address=redis://************:7003, flags=[MASTER, MYSELF], slaveOf=null, slotRanges=[[5461-10922]]], ClusterNodeInfo [nodeId=85e61682488d3a355264d212607a63832da60e8c, address=redis://************:7006, flags=[SLAVE], slaveOf=02aff3b78f8c09c43cbfc2346eac68a16a942097, slotRanges=[]], ClusterNodeInfo [nodeId=64f45f937e6f8c36d0fb7cf09195403c4cccdf1d, address=redis://************:7004, flags=[MASTER], slaveOf=null, slotRanges=[[0-5460]]], ClusterNodeInfo [nodeId=5514edc6d1aaab0526b0093a424e9f352e9914ed, address=redis://************:7002, flags=[SLAVE], slaveOf=4dcfd30d1191cb5e65ef69e88dc76592f8b3d14d, slotRanges=[]]]
2025-05-29 14:03:55.739 [http-nio-8092-exec-7] WARN  org.springframework.web.servlet.mvc.method.annotation.ExceptionHandlerExceptionResolver.doResolveHandlerMethodException Line:414 - Failure in @ExceptionHandler com.simbest.boot.base.exception.RestControllerExceptionHandler#handleErrorException(HttpServletRequest, Exception)
org.apache.catalina.connector.ClientAbortException: java.io.IOException: 你的主机中的软件中止了一个已建立的连接。
	at org.apache.catalina.connector.OutputBuffer.doFlush(OutputBuffer.java:310)
	at org.apache.catalina.connector.OutputBuffer.flush(OutputBuffer.java:273)
	at org.apache.catalina.connector.CoyoteOutputStream.flush(CoyoteOutputStream.java:118)
	at org.springframework.session.web.http.OnCommittedResponseWrapper$SaveContextServletOutputStream.flush(OnCommittedResponseWrapper.java:499)
	at org.springframework.security.web.util.OnCommittedResponseWrapper$SaveContextServletOutputStream.flush(OnCommittedResponseWrapper.java:520)
	at org.springframework.security.web.util.OnCommittedResponseWrapper$SaveContextServletOutputStream.flush(OnCommittedResponseWrapper.java:520)
	at net.bull.javamelody.internal.web.FilterServletOutputStream.flush(FilterServletOutputStream.java:69)
	at com.fasterxml.jackson.core.json.UTF8JsonGenerator.flush(UTF8JsonGenerator.java:1153)
	at com.fasterxml.jackson.databind.ObjectWriter.writeValue(ObjectWriter.java:923)
	at org.springframework.http.converter.json.AbstractJackson2HttpMessageConverter.writeInternal(AbstractJackson2HttpMessageConverter.java:287)
	at org.springframework.http.converter.AbstractGenericHttpMessageConverter.write(AbstractGenericHttpMessageConverter.java:104)
	at org.springframework.web.servlet.mvc.method.annotation.AbstractMessageConverterMethodProcessor.writeWithMessageConverters(AbstractMessageConverterMethodProcessor.java:295)
	at org.springframework.web.servlet.mvc.method.annotation.RequestResponseBodyMethodProcessor.handleReturnValue(RequestResponseBodyMethodProcessor.java:181)
	at org.springframework.web.method.support.HandlerMethodReturnValueHandlerComposite.handleReturnValue(HandlerMethodReturnValueHandlerComposite.java:82)
	at org.springframework.web.servlet.mvc.method.annotation.ServletInvocableHandlerMethod.invokeAndHandle(ServletInvocableHandlerMethod.java:124)
	at org.springframework.web.servlet.mvc.method.annotation.ExceptionHandlerExceptionResolver.doResolveHandlerMethodException(ExceptionHandlerExceptionResolver.java:403)
	at org.springframework.web.servlet.handler.AbstractHandlerMethodExceptionResolver.doResolveException(AbstractHandlerMethodExceptionResolver.java:61)
	at org.springframework.web.servlet.handler.AbstractHandlerExceptionResolver.resolveException(AbstractHandlerExceptionResolver.java:141)
	at org.springframework.web.servlet.handler.HandlerExceptionResolverComposite.resolveException(HandlerExceptionResolverComposite.java:80)
	at org.springframework.web.servlet.DispatcherServlet.processHandlerException(DispatcherServlet.java:1300)
	at org.springframework.web.servlet.DispatcherServlet.processDispatchResult(DispatcherServlet.java:1111)
	at org.springframework.web.servlet.DispatcherServlet.doDispatch(DispatcherServlet.java:1057)
	at org.springframework.web.servlet.DispatcherServlet.doService(DispatcherServlet.java:943)
	at org.springframework.web.servlet.FrameworkServlet.processRequest(FrameworkServlet.java:1006)
	at org.springframework.web.servlet.FrameworkServlet.doPost(FrameworkServlet.java:909)
	at javax.servlet.http.HttpServlet.service(HttpServlet.java:681)
	at org.springframework.web.servlet.FrameworkServlet.service(FrameworkServlet.java:883)
	at javax.servlet.http.HttpServlet.service(HttpServlet.java:764)
	at org.apache.catalina.core.ApplicationFilterChain.internalDoFilter(ApplicationFilterChain.java:227)
	at org.apache.catalina.core.ApplicationFilterChain.doFilter(ApplicationFilterChain.java:162)
	at org.apache.tomcat.websocket.server.WsFilter.doFilter(WsFilter.java:53)
	at org.apache.catalina.core.ApplicationFilterChain.internalDoFilter(ApplicationFilterChain.java:189)
	at org.apache.catalina.core.ApplicationFilterChain.doFilter(ApplicationFilterChain.java:162)
	at com.simbest.boot.security.auth.filter.CaptchaAuthenticationFilter.doFilter(CaptchaAuthenticationFilter.java:46)
	at org.apache.catalina.core.ApplicationFilterChain.internalDoFilter(ApplicationFilterChain.java:189)
	at org.apache.catalina.core.ApplicationFilterChain.doFilter(ApplicationFilterChain.java:162)
	at com.simbest.boot.security.auth.filter.CaptchaAuthenticationFilter.doFilter(CaptchaAuthenticationFilter.java:46)
	at org.apache.catalina.core.ApplicationFilterChain.internalDoFilter(ApplicationFilterChain.java:189)
	at org.apache.catalina.core.ApplicationFilterChain.doFilter(ApplicationFilterChain.java:162)
	at org.springframework.security.web.authentication.AbstractAuthenticationProcessingFilter.doFilter(AbstractAuthenticationProcessingFilter.java:200)
	at org.apache.catalina.core.ApplicationFilterChain.internalDoFilter(ApplicationFilterChain.java:189)
	at org.apache.catalina.core.ApplicationFilterChain.doFilter(ApplicationFilterChain.java:162)
	at org.springframework.security.web.authentication.logout.LogoutFilter.doFilter(LogoutFilter.java:116)
	at org.apache.catalina.core.ApplicationFilterChain.internalDoFilter(ApplicationFilterChain.java:189)
	at org.apache.catalina.core.ApplicationFilterChain.doFilter(ApplicationFilterChain.java:162)
	at org.springframework.security.web.authentication.AbstractAuthenticationProcessingFilter.doFilter(AbstractAuthenticationProcessingFilter.java:200)
	at org.apache.catalina.core.ApplicationFilterChain.internalDoFilter(ApplicationFilterChain.java:189)
	at org.apache.catalina.core.ApplicationFilterChain.doFilter(ApplicationFilterChain.java:162)
	at org.springframework.security.web.authentication.AbstractAuthenticationProcessingFilter.doFilter(AbstractAuthenticationProcessingFilter.java:200)
	at org.apache.catalina.core.ApplicationFilterChain.internalDoFilter(ApplicationFilterChain.java:189)
	at org.apache.catalina.core.ApplicationFilterChain.doFilter(ApplicationFilterChain.java:162)
	at org.springframework.security.web.authentication.AbstractAuthenticationProcessingFilter.doFilter(AbstractAuthenticationProcessingFilter.java:200)
	at org.apache.catalina.core.ApplicationFilterChain.internalDoFilter(ApplicationFilterChain.java:189)
	at org.apache.catalina.core.ApplicationFilterChain.doFilter(ApplicationFilterChain.java:162)
	at org.springframework.security.web.authentication.AbstractAuthenticationProcessingFilter.doFilter(AbstractAuthenticationProcessingFilter.java:200)
	at org.apache.catalina.core.ApplicationFilterChain.internalDoFilter(ApplicationFilterChain.java:189)
	at org.apache.catalina.core.ApplicationFilterChain.doFilter(ApplicationFilterChain.java:162)
	at org.springframework.security.web.authentication.AbstractAuthenticationProcessingFilter.doFilter(AbstractAuthenticationProcessingFilter.java:200)
	at org.apache.catalina.core.ApplicationFilterChain.internalDoFilter(ApplicationFilterChain.java:189)
	at org.apache.catalina.core.ApplicationFilterChain.doFilter(ApplicationFilterChain.java:162)
	at com.simbest.boot.base.web.filter.CsrfProtectFilter.doFilter(CsrfProtectFilter.java:164)
	at org.apache.catalina.core.ApplicationFilterChain.internalDoFilter(ApplicationFilterChain.java:189)
	at org.apache.catalina.core.ApplicationFilterChain.doFilter(ApplicationFilterChain.java:162)
	at org.springframework.web.servlet.resource.ResourceUrlEncodingFilter.doFilter(ResourceUrlEncodingFilter.java:64)
	at org.apache.catalina.core.ApplicationFilterChain.internalDoFilter(ApplicationFilterChain.java:189)
	at org.apache.catalina.core.ApplicationFilterChain.doFilter(ApplicationFilterChain.java:162)
	at net.bull.javamelody.MonitoringFilter.doFilter(MonitoringFilter.java:199)
	at org.apache.catalina.core.ApplicationFilterChain.internalDoFilter(ApplicationFilterChain.java:189)
	at org.apache.catalina.core.ApplicationFilterChain.doFilter(ApplicationFilterChain.java:162)
	at net.bull.javamelody.MonitoringFilter.doFilter(MonitoringFilter.java:239)
	at net.bull.javamelody.MonitoringFilter.doFilter(MonitoringFilter.java:215)
	at org.apache.catalina.core.ApplicationFilterChain.internalDoFilter(ApplicationFilterChain.java:189)
	at org.apache.catalina.core.ApplicationFilterChain.doFilter(ApplicationFilterChain.java:162)
	at org.springframework.security.web.FilterChainProxy$VirtualFilterChain.doFilter(FilterChainProxy.java:320)
	at org.springframework.security.web.access.intercept.FilterSecurityInterceptor.invoke(FilterSecurityInterceptor.java:126)
	at org.springframework.security.web.access.intercept.FilterSecurityInterceptor.doFilter(FilterSecurityInterceptor.java:90)
	at org.springframework.security.web.FilterChainProxy$VirtualFilterChain.doFilter(FilterChainProxy.java:334)
	at org.springframework.security.web.access.ExceptionTranslationFilter.doFilter(ExceptionTranslationFilter.java:118)
	at org.springframework.security.web.FilterChainProxy$VirtualFilterChain.doFilter(FilterChainProxy.java:334)
	at org.springframework.security.web.session.SessionManagementFilter.doFilter(SessionManagementFilter.java:137)
	at org.springframework.security.web.FilterChainProxy$VirtualFilterChain.doFilter(FilterChainProxy.java:334)
	at org.springframework.security.web.authentication.AnonymousAuthenticationFilter.doFilter(AnonymousAuthenticationFilter.java:111)
	at org.springframework.security.web.FilterChainProxy$VirtualFilterChain.doFilter(FilterChainProxy.java:334)
	at org.springframework.security.web.servletapi.SecurityContextHolderAwareRequestFilter.doFilter(SecurityContextHolderAwareRequestFilter.java:158)
	at org.springframework.security.web.FilterChainProxy$VirtualFilterChain.doFilter(FilterChainProxy.java:334)
	at org.springframework.security.web.savedrequest.RequestCacheAwareFilter.doFilter(RequestCacheAwareFilter.java:63)
	at org.springframework.security.web.FilterChainProxy$VirtualFilterChain.doFilter(FilterChainProxy.java:334)
	at org.springframework.security.web.session.ConcurrentSessionFilter.doFilter(ConcurrentSessionFilter.java:152)
	at org.springframework.security.web.FilterChainProxy$VirtualFilterChain.doFilter(FilterChainProxy.java:334)
	at org.springframework.security.web.authentication.AbstractAuthenticationProcessingFilter.doFilter(AbstractAuthenticationProcessingFilter.java:200)
	at org.springframework.security.web.FilterChainProxy$VirtualFilterChain.doFilter(FilterChainProxy.java:334)
	at org.springframework.security.web.authentication.AbstractAuthenticationProcessingFilter.doFilter(AbstractAuthenticationProcessingFilter.java:200)
	at org.springframework.security.web.FilterChainProxy$VirtualFilterChain.doFilter(FilterChainProxy.java:334)
	at org.springframework.security.web.authentication.AbstractAuthenticationProcessingFilter.doFilter(AbstractAuthenticationProcessingFilter.java:200)
	at org.springframework.security.web.FilterChainProxy$VirtualFilterChain.doFilter(FilterChainProxy.java:334)
	at org.springframework.security.web.authentication.AbstractAuthenticationProcessingFilter.doFilter(AbstractAuthenticationProcessingFilter.java:200)
	at org.springframework.security.web.FilterChainProxy$VirtualFilterChain.doFilter(FilterChainProxy.java:334)
	at org.springframework.security.web.authentication.AbstractAuthenticationProcessingFilter.doFilter(AbstractAuthenticationProcessingFilter.java:200)
	at org.springframework.security.web.FilterChainProxy$VirtualFilterChain.doFilter(FilterChainProxy.java:334)
	at org.springframework.security.web.authentication.AbstractAuthenticationProcessingFilter.doFilter(AbstractAuthenticationProcessingFilter.java:200)
	at org.springframework.security.web.FilterChainProxy$VirtualFilterChain.doFilter(FilterChainProxy.java:334)
	at org.springframework.security.web.authentication.AbstractAuthenticationProcessingFilter.doFilter(AbstractAuthenticationProcessingFilter.java:200)
	at org.springframework.security.web.FilterChainProxy$VirtualFilterChain.doFilter(FilterChainProxy.java:334)
	at com.simbest.boot.security.auth.filter.CaptchaAuthenticationFilter.doFilter(CaptchaAuthenticationFilter.java:46)
	at org.springframework.security.web.FilterChainProxy$VirtualFilterChain.doFilter(FilterChainProxy.java:334)
	at com.simbest.boot.security.auth.filter.CaptchaAuthenticationFilter.doFilter(CaptchaAuthenticationFilter.java:46)
	at org.springframework.security.web.FilterChainProxy$VirtualFilterChain.doFilter(FilterChainProxy.java:334)
	at org.springframework.security.web.authentication.logout.LogoutFilter.doFilter(LogoutFilter.java:116)
	at org.springframework.security.web.FilterChainProxy$VirtualFilterChain.doFilter(FilterChainProxy.java:334)
	at org.springframework.web.filter.CorsFilter.doFilterInternal(CorsFilter.java:92)
	at org.springframework.web.filter.OncePerRequestFilter.doFilter(OncePerRequestFilter.java:119)
	at org.springframework.security.web.FilterChainProxy$VirtualFilterChain.doFilter(FilterChainProxy.java:334)
	at org.springframework.security.web.header.HeaderWriterFilter.doHeadersAfter(HeaderWriterFilter.java:92)
	at org.springframework.security.web.header.HeaderWriterFilter.doFilterInternal(HeaderWriterFilter.java:77)
	at org.springframework.web.filter.OncePerRequestFilter.doFilter(OncePerRequestFilter.java:119)
	at org.springframework.security.web.FilterChainProxy$VirtualFilterChain.doFilter(FilterChainProxy.java:334)
	at com.simbest.boot.security.auth.filter.CloudHeaderTokenAuthenticationFilter.doFilterInternal(CloudHeaderTokenAuthenticationFilter.java:171)
	at org.springframework.web.filter.OncePerRequestFilter.doFilter(OncePerRequestFilter.java:119)
	at org.springframework.security.web.FilterChainProxy$VirtualFilterChain.doFilter(FilterChainProxy.java:334)
	at org.springframework.security.web.context.SecurityContextPersistenceFilter.doFilter(SecurityContextPersistenceFilter.java:105)
	at org.springframework.security.web.FilterChainProxy$VirtualFilterChain.doFilter(FilterChainProxy.java:334)
	at org.springframework.security.web.context.request.async.WebAsyncManagerIntegrationFilter.doFilterInternal(WebAsyncManagerIntegrationFilter.java:56)
	at org.springframework.web.filter.OncePerRequestFilter.doFilter(OncePerRequestFilter.java:119)
	at org.springframework.security.web.FilterChainProxy$VirtualFilterChain.doFilter(FilterChainProxy.java:334)
	at org.springframework.security.web.FilterChainProxy.doFilterInternal(FilterChainProxy.java:215)
	at org.springframework.security.web.FilterChainProxy.doFilter(FilterChainProxy.java:178)
	at org.springframework.web.filter.DelegatingFilterProxy.invokeDelegate(DelegatingFilterProxy.java:358)
	at org.springframework.web.filter.DelegatingFilterProxy.doFilter(DelegatingFilterProxy.java:271)
	at org.apache.catalina.core.ApplicationFilterChain.internalDoFilter(ApplicationFilterChain.java:189)
	at org.apache.catalina.core.ApplicationFilterChain.doFilter(ApplicationFilterChain.java:162)
	at org.springframework.session.web.http.SessionRepositoryFilter.doFilterInternal(SessionRepositoryFilter.java:141)
	at org.springframework.session.web.http.OncePerRequestFilter.doFilter(OncePerRequestFilter.java:82)
	at org.apache.catalina.core.ApplicationFilterChain.internalDoFilter(ApplicationFilterChain.java:189)
	at org.apache.catalina.core.ApplicationFilterChain.doFilter(ApplicationFilterChain.java:162)
	at org.springframework.boot.actuate.metrics.web.servlet.WebMvcMetricsFilter.doFilterInternal(WebMvcMetricsFilter.java:108)
	at org.springframework.web.filter.OncePerRequestFilter.doFilter(OncePerRequestFilter.java:119)
	at org.apache.catalina.core.ApplicationFilterChain.internalDoFilter(ApplicationFilterChain.java:189)
	at org.apache.catalina.core.ApplicationFilterChain.doFilter(ApplicationFilterChain.java:162)
	at org.springframework.web.filter.CharacterEncodingFilter.doFilterInternal(CharacterEncodingFilter.java:201)
	at org.springframework.web.filter.OncePerRequestFilter.doFilter(OncePerRequestFilter.java:119)
	at org.apache.catalina.core.ApplicationFilterChain.internalDoFilter(ApplicationFilterChain.java:189)
	at org.apache.catalina.core.ApplicationFilterChain.doFilter(ApplicationFilterChain.java:162)
	at org.apache.catalina.core.StandardWrapperValve.invoke(StandardWrapperValve.java:197)
	at org.apache.catalina.core.StandardContextValve.invoke(StandardContextValve.java:97)
	at org.apache.catalina.authenticator.AuthenticatorBase.invoke(AuthenticatorBase.java:541)
	at org.apache.catalina.core.StandardHostValve.invoke(StandardHostValve.java:135)
	at org.apache.catalina.valves.ErrorReportValve.invoke(ErrorReportValve.java:92)
	at org.apache.catalina.core.StandardEngineValve.invoke(StandardEngineValve.java:78)
	at org.apache.catalina.connector.CoyoteAdapter.service(CoyoteAdapter.java:360)
	at org.apache.coyote.http11.Http11Processor.service(Http11Processor.java:399)
	at org.apache.coyote.AbstractProcessorLight.process(AbstractProcessorLight.java:65)
	at org.apache.coyote.AbstractProtocol$ConnectionHandler.process(AbstractProtocol.java:890)
	at org.apache.tomcat.util.net.NioEndpoint$SocketProcessor.doRun(NioEndpoint.java:1789)
	at org.apache.tomcat.util.net.SocketProcessorBase.run(SocketProcessorBase.java:49)
	at org.apache.tomcat.util.threads.ThreadPoolExecutor.runWorker(ThreadPoolExecutor.java:1191)
	at org.apache.tomcat.util.threads.ThreadPoolExecutor$Worker.run(ThreadPoolExecutor.java:659)
	at org.apache.tomcat.util.threads.TaskThread$WrappingRunnable.run(TaskThread.java:61)
	at java.lang.Thread.run(Thread.java:748)
Caused by: java.io.IOException: 你的主机中的软件中止了一个已建立的连接。
	at sun.nio.ch.SocketDispatcher.write0(Native Method)
	at sun.nio.ch.SocketDispatcher.write(SocketDispatcher.java:51)
	at sun.nio.ch.IOUtil.writeFromNativeBuffer(IOUtil.java:93)
	at sun.nio.ch.IOUtil.write(IOUtil.java:65)
	at sun.nio.ch.SocketChannelImpl.write(SocketChannelImpl.java:471)
	at org.apache.tomcat.util.net.NioChannel.write(NioChannel.java:135)
	at org.apache.tomcat.util.net.NioEndpoint$NioSocketWrapper.doWrite(NioEndpoint.java:1424)
	at org.apache.tomcat.util.net.SocketWrapperBase.doWrite(SocketWrapperBase.java:768)
	at org.apache.tomcat.util.net.SocketWrapperBase.flushBlocking(SocketWrapperBase.java:732)
	at org.apache.tomcat.util.net.SocketWrapperBase.flush(SocketWrapperBase.java:716)
	at org.apache.coyote.http11.Http11OutputBuffer$SocketOutputBuffer.flush(Http11OutputBuffer.java:573)
	at org.apache.coyote.http11.filters.ChunkedOutputFilter.flush(ChunkedOutputFilter.java:157)
	at org.apache.coyote.http11.Http11OutputBuffer.flush(Http11OutputBuffer.java:221)
	at org.apache.coyote.http11.Http11Processor.flush(Http11Processor.java:1255)
	at org.apache.coyote.AbstractProcessor.action(AbstractProcessor.java:402)
	at org.apache.coyote.Response.action(Response.java:209)
	at org.apache.catalina.connector.OutputBuffer.doFlush(OutputBuffer.java:306)
	... 157 common frames omitted
2025-05-29 14:43:11.916 [http-nio-8092-exec-18] WARN  org.hibernate.engine.jdbc.spi.SqlExceptionHelper.logExceptions Line:137 - SQL Error: 900, SQLState: 42000
2025-05-29 15:12:03.232 [http-nio-8092-exec-8] WARN  org.hibernate.engine.jdbc.spi.SqlExceptionHelper.logExceptions Line:137 - SQL Error: 900, SQLState: 42000
2025-05-29 15:12:22.279 [http-nio-8092-exec-9] WARN  org.hibernate.engine.jdbc.spi.SqlExceptionHelper.logExceptions Line:137 - SQL Error: 900, SQLState: 42000
2025-05-29 15:12:22.396 [http-nio-8092-exec-11] WARN  org.hibernate.engine.jdbc.spi.SqlExceptionHelper.logExceptions Line:137 - SQL Error: 900, SQLState: 42000
2025-05-29 15:21:25.615 [http-nio-8092-exec-16] WARN  org.hibernate.engine.jdbc.spi.SqlExceptionHelper.logExceptions Line:137 - SQL Error: 900, SQLState: 42000
2025-05-29 15:21:25.618 [http-nio-8092-exec-20] WARN  org.hibernate.engine.jdbc.spi.SqlExceptionHelper.logExceptions Line:137 - SQL Error: 900, SQLState: 42000
2025-05-29 15:21:25.625 [http-nio-8092-exec-18] WARN  org.hibernate.engine.jdbc.spi.SqlExceptionHelper.logExceptions Line:137 - SQL Error: 900, SQLState: 42000
2025-05-29 15:24:55.991 [http-nio-8092-exec-6] WARN  org.hibernate.engine.jdbc.spi.SqlExceptionHelper.logExceptions Line:137 - SQL Error: 900, SQLState: 42000
2025-05-29 15:24:55.995 [http-nio-8092-exec-9] WARN  org.hibernate.engine.jdbc.spi.SqlExceptionHelper.logExceptions Line:137 - SQL Error: 900, SQLState: 42000
2025-05-29 15:24:56.037 [http-nio-8092-exec-8] WARN  org.hibernate.engine.jdbc.spi.SqlExceptionHelper.logExceptions Line:137 - SQL Error: 900, SQLState: 42000
2025-05-29 15:26:58.042 [http-nio-8092-exec-16] WARN  org.hibernate.engine.jdbc.spi.SqlExceptionHelper.logExceptions Line:137 - SQL Error: 900, SQLState: 42000
2025-05-29 15:26:58.044 [http-nio-8092-exec-20] WARN  org.hibernate.engine.jdbc.spi.SqlExceptionHelper.logExceptions Line:137 - SQL Error: 900, SQLState: 42000
2025-05-29 15:26:58.047 [http-nio-8092-exec-18] WARN  org.hibernate.engine.jdbc.spi.SqlExceptionHelper.logExceptions Line:137 - SQL Error: 900, SQLState: 42000
2025-05-29 15:31:50.611 [http-nio-8092-exec-13] WARN  org.hibernate.engine.jdbc.spi.SqlExceptionHelper.logExceptions Line:137 - SQL Error: 900, SQLState: 42000
2025-05-29 15:31:50.611 [http-nio-8092-exec-11] WARN  org.hibernate.engine.jdbc.spi.SqlExceptionHelper.logExceptions Line:137 - SQL Error: 900, SQLState: 42000
2025-05-29 15:31:50.632 [http-nio-8092-exec-15] WARN  org.hibernate.engine.jdbc.spi.SqlExceptionHelper.logExceptions Line:137 - SQL Error: 900, SQLState: 42000
2025-05-29 15:35:00.013 [pool-14-thread-7] WARN  com.simbest.boot.base.repository.CustomDynamicWhere.queryForList Line:50 - 自定义查询SQL输出为：【select * from US_TODO_MODEL t  where t.send_flag = 0 and t.work_flag = 1 and t.enabled =1】
2025-05-29 15:35:00.013 [pool-14-thread-9] WARN  com.simbest.boot.base.repository.CustomDynamicWhere.queryForList Line:50 - 自定义查询SQL输出为：【select * from US_TODO_MODEL t  where t.send_flag = 0 and t.work_flag = 0 and t.enabled =1】
2025-05-29 15:36:04.077 [http-nio-8092-exec-16] WARN  org.hibernate.engine.jdbc.spi.SqlExceptionHelper.logExceptions Line:137 - SQL Error: 900, SQLState: 42000
2025-05-29 15:36:04.084 [http-nio-8092-exec-14] WARN  org.hibernate.engine.jdbc.spi.SqlExceptionHelper.logExceptions Line:137 - SQL Error: 900, SQLState: 42000
2025-05-29 15:36:04.092 [http-nio-8092-exec-7] WARN  org.hibernate.engine.jdbc.spi.SqlExceptionHelper.logExceptions Line:137 - SQL Error: 900, SQLState: 42000
2025-05-29 15:37:13.897 [http-nio-8092-exec-19] WARN  org.hibernate.engine.jdbc.spi.SqlExceptionHelper.logExceptions Line:137 - SQL Error: 900, SQLState: 42000
2025-05-29 15:37:13.901 [http-nio-8092-exec-17] WARN  org.hibernate.engine.jdbc.spi.SqlExceptionHelper.logExceptions Line:137 - SQL Error: 900, SQLState: 42000
2025-05-29 15:37:14.391 [http-nio-8092-exec-9] WARN  org.hibernate.engine.jdbc.spi.SqlExceptionHelper.logExceptions Line:137 - SQL Error: 900, SQLState: 42000
2025-05-29 15:37:14.766 [http-nio-8092-exec-8] WARN  org.hibernate.engine.jdbc.spi.SqlExceptionHelper.logExceptions Line:137 - SQL Error: 900, SQLState: 42000
2025-05-29 15:37:50.797 [http-nio-8092-exec-20] WARN  org.hibernate.engine.jdbc.spi.SqlExceptionHelper.logExceptions Line:137 - SQL Error: 900, SQLState: 42000
2025-05-29 15:37:50.797 [http-nio-8092-exec-16] WARN  org.hibernate.engine.jdbc.spi.SqlExceptionHelper.logExceptions Line:137 - SQL Error: 900, SQLState: 42000
2025-05-29 15:37:51.197 [http-nio-8092-exec-3] WARN  org.hibernate.engine.jdbc.spi.SqlExceptionHelper.logExceptions Line:137 - SQL Error: 900, SQLState: 42000
2025-05-29 15:37:51.705 [http-nio-8092-exec-17] WARN  org.hibernate.engine.jdbc.spi.SqlExceptionHelper.logExceptions Line:137 - SQL Error: 900, SQLState: 42000
2025-05-29 17:13:41.710 [http-nio-8092-exec-13] WARN  org.springframework.web.servlet.mvc.method.annotation.ExceptionHandlerExceptionResolver.doResolveHandlerMethodException Line:414 - Failure in @ExceptionHandler com.simbest.boot.base.exception.RestControllerExceptionHandler#handleErrorException(HttpServletRequest, Exception)
org.apache.catalina.connector.ClientAbortException: java.io.IOException: 你的主机中的软件中止了一个已建立的连接。
	at org.apache.catalina.connector.OutputBuffer.realWriteBytes(OutputBuffer.java:353)
	at org.apache.catalina.connector.OutputBuffer.flushByteBuffer(OutputBuffer.java:784)
	at org.apache.catalina.connector.OutputBuffer.append(OutputBuffer.java:689)
	at org.apache.catalina.connector.OutputBuffer.writeBytes(OutputBuffer.java:388)
	at org.apache.catalina.connector.OutputBuffer.write(OutputBuffer.java:366)
	at org.apache.catalina.connector.CoyoteOutputStream.write(CoyoteOutputStream.java:96)
	at org.springframework.session.web.http.OnCommittedResponseWrapper$SaveContextServletOutputStream.write(OnCommittedResponseWrapper.java:624)
	at org.springframework.security.web.util.OnCommittedResponseWrapper$SaveContextServletOutputStream.write(OnCommittedResponseWrapper.java:645)
	at org.springframework.security.web.util.OnCommittedResponseWrapper$SaveContextServletOutputStream.write(OnCommittedResponseWrapper.java:645)
	at net.bull.javamelody.internal.web.FilterServletOutputStream.write(FilterServletOutputStream.java:88)
	at net.bull.javamelody.internal.web.CounterResponseStream.write(CounterResponseStream.java:81)
	at com.fasterxml.jackson.core.json.UTF8JsonGenerator._flushBuffer(UTF8JsonGenerator.java:2136)
	at com.fasterxml.jackson.core.json.UTF8JsonGenerator.flush(UTF8JsonGenerator.java:1150)
	at com.fasterxml.jackson.databind.ObjectWriter.writeValue(ObjectWriter.java:923)
	at org.springframework.http.converter.json.AbstractJackson2HttpMessageConverter.writeInternal(AbstractJackson2HttpMessageConverter.java:287)
	at org.springframework.http.converter.AbstractGenericHttpMessageConverter.write(AbstractGenericHttpMessageConverter.java:104)
	at org.springframework.web.servlet.mvc.method.annotation.AbstractMessageConverterMethodProcessor.writeWithMessageConverters(AbstractMessageConverterMethodProcessor.java:295)
	at org.springframework.web.servlet.mvc.method.annotation.RequestResponseBodyMethodProcessor.handleReturnValue(RequestResponseBodyMethodProcessor.java:181)
	at org.springframework.web.method.support.HandlerMethodReturnValueHandlerComposite.handleReturnValue(HandlerMethodReturnValueHandlerComposite.java:82)
	at org.springframework.web.servlet.mvc.method.annotation.ServletInvocableHandlerMethod.invokeAndHandle(ServletInvocableHandlerMethod.java:124)
	at org.springframework.web.servlet.mvc.method.annotation.ExceptionHandlerExceptionResolver.doResolveHandlerMethodException(ExceptionHandlerExceptionResolver.java:403)
	at org.springframework.web.servlet.handler.AbstractHandlerMethodExceptionResolver.doResolveException(AbstractHandlerMethodExceptionResolver.java:61)
	at org.springframework.web.servlet.handler.AbstractHandlerExceptionResolver.resolveException(AbstractHandlerExceptionResolver.java:141)
	at org.springframework.web.servlet.handler.HandlerExceptionResolverComposite.resolveException(HandlerExceptionResolverComposite.java:80)
	at org.springframework.web.servlet.DispatcherServlet.processHandlerException(DispatcherServlet.java:1300)
	at org.springframework.web.servlet.DispatcherServlet.processDispatchResult(DispatcherServlet.java:1111)
	at org.springframework.web.servlet.DispatcherServlet.doDispatch(DispatcherServlet.java:1057)
	at org.springframework.web.servlet.DispatcherServlet.doService(DispatcherServlet.java:943)
	at org.springframework.web.servlet.FrameworkServlet.processRequest(FrameworkServlet.java:1006)
	at org.springframework.web.servlet.FrameworkServlet.doPost(FrameworkServlet.java:909)
	at javax.servlet.http.HttpServlet.service(HttpServlet.java:681)
	at org.springframework.web.servlet.FrameworkServlet.service(FrameworkServlet.java:883)
	at javax.servlet.http.HttpServlet.service(HttpServlet.java:764)
	at org.apache.catalina.core.ApplicationFilterChain.internalDoFilter(ApplicationFilterChain.java:227)
	at org.apache.catalina.core.ApplicationFilterChain.doFilter(ApplicationFilterChain.java:162)
	at org.apache.tomcat.websocket.server.WsFilter.doFilter(WsFilter.java:53)
	at org.apache.catalina.core.ApplicationFilterChain.internalDoFilter(ApplicationFilterChain.java:189)
	at org.apache.catalina.core.ApplicationFilterChain.doFilter(ApplicationFilterChain.java:162)
	at com.simbest.boot.security.auth.filter.CaptchaAuthenticationFilter.doFilter(CaptchaAuthenticationFilter.java:46)
	at org.apache.catalina.core.ApplicationFilterChain.internalDoFilter(ApplicationFilterChain.java:189)
	at org.apache.catalina.core.ApplicationFilterChain.doFilter(ApplicationFilterChain.java:162)
	at com.simbest.boot.security.auth.filter.CaptchaAuthenticationFilter.doFilter(CaptchaAuthenticationFilter.java:46)
	at org.apache.catalina.core.ApplicationFilterChain.internalDoFilter(ApplicationFilterChain.java:189)
	at org.apache.catalina.core.ApplicationFilterChain.doFilter(ApplicationFilterChain.java:162)
	at org.springframework.security.web.authentication.AbstractAuthenticationProcessingFilter.doFilter(AbstractAuthenticationProcessingFilter.java:200)
	at org.apache.catalina.core.ApplicationFilterChain.internalDoFilter(ApplicationFilterChain.java:189)
	at org.apache.catalina.core.ApplicationFilterChain.doFilter(ApplicationFilterChain.java:162)
	at org.springframework.security.web.authentication.logout.LogoutFilter.doFilter(LogoutFilter.java:116)
	at org.apache.catalina.core.ApplicationFilterChain.internalDoFilter(ApplicationFilterChain.java:189)
	at org.apache.catalina.core.ApplicationFilterChain.doFilter(ApplicationFilterChain.java:162)
	at org.springframework.security.web.authentication.AbstractAuthenticationProcessingFilter.doFilter(AbstractAuthenticationProcessingFilter.java:200)
	at org.apache.catalina.core.ApplicationFilterChain.internalDoFilter(ApplicationFilterChain.java:189)
	at org.apache.catalina.core.ApplicationFilterChain.doFilter(ApplicationFilterChain.java:162)
	at org.springframework.security.web.authentication.AbstractAuthenticationProcessingFilter.doFilter(AbstractAuthenticationProcessingFilter.java:200)
	at org.apache.catalina.core.ApplicationFilterChain.internalDoFilter(ApplicationFilterChain.java:189)
	at org.apache.catalina.core.ApplicationFilterChain.doFilter(ApplicationFilterChain.java:162)
	at org.springframework.security.web.authentication.AbstractAuthenticationProcessingFilter.doFilter(AbstractAuthenticationProcessingFilter.java:200)
	at org.apache.catalina.core.ApplicationFilterChain.internalDoFilter(ApplicationFilterChain.java:189)
	at org.apache.catalina.core.ApplicationFilterChain.doFilter(ApplicationFilterChain.java:162)
	at org.springframework.security.web.authentication.AbstractAuthenticationProcessingFilter.doFilter(AbstractAuthenticationProcessingFilter.java:200)
	at org.apache.catalina.core.ApplicationFilterChain.internalDoFilter(ApplicationFilterChain.java:189)
	at org.apache.catalina.core.ApplicationFilterChain.doFilter(ApplicationFilterChain.java:162)
	at org.springframework.security.web.authentication.AbstractAuthenticationProcessingFilter.doFilter(AbstractAuthenticationProcessingFilter.java:200)
	at org.apache.catalina.core.ApplicationFilterChain.internalDoFilter(ApplicationFilterChain.java:189)
	at org.apache.catalina.core.ApplicationFilterChain.doFilter(ApplicationFilterChain.java:162)
	at com.simbest.boot.base.web.filter.CsrfProtectFilter.doFilter(CsrfProtectFilter.java:164)
	at org.apache.catalina.core.ApplicationFilterChain.internalDoFilter(ApplicationFilterChain.java:189)
	at org.apache.catalina.core.ApplicationFilterChain.doFilter(ApplicationFilterChain.java:162)
	at org.springframework.web.servlet.resource.ResourceUrlEncodingFilter.doFilter(ResourceUrlEncodingFilter.java:64)
	at org.apache.catalina.core.ApplicationFilterChain.internalDoFilter(ApplicationFilterChain.java:189)
	at org.apache.catalina.core.ApplicationFilterChain.doFilter(ApplicationFilterChain.java:162)
	at net.bull.javamelody.MonitoringFilter.doFilter(MonitoringFilter.java:199)
	at org.apache.catalina.core.ApplicationFilterChain.internalDoFilter(ApplicationFilterChain.java:189)
	at org.apache.catalina.core.ApplicationFilterChain.doFilter(ApplicationFilterChain.java:162)
	at net.bull.javamelody.MonitoringFilter.doFilter(MonitoringFilter.java:239)
	at net.bull.javamelody.MonitoringFilter.doFilter(MonitoringFilter.java:215)
	at org.apache.catalina.core.ApplicationFilterChain.internalDoFilter(ApplicationFilterChain.java:189)
	at org.apache.catalina.core.ApplicationFilterChain.doFilter(ApplicationFilterChain.java:162)
	at org.springframework.security.web.FilterChainProxy$VirtualFilterChain.doFilter(FilterChainProxy.java:320)
	at org.springframework.security.web.access.intercept.FilterSecurityInterceptor.invoke(FilterSecurityInterceptor.java:126)
	at org.springframework.security.web.access.intercept.FilterSecurityInterceptor.doFilter(FilterSecurityInterceptor.java:90)
	at org.springframework.security.web.FilterChainProxy$VirtualFilterChain.doFilter(FilterChainProxy.java:334)
	at org.springframework.security.web.access.ExceptionTranslationFilter.doFilter(ExceptionTranslationFilter.java:118)
	at org.springframework.security.web.FilterChainProxy$VirtualFilterChain.doFilter(FilterChainProxy.java:334)
	at org.springframework.security.web.session.SessionManagementFilter.doFilter(SessionManagementFilter.java:137)
	at org.springframework.security.web.FilterChainProxy$VirtualFilterChain.doFilter(FilterChainProxy.java:334)
	at org.springframework.security.web.authentication.AnonymousAuthenticationFilter.doFilter(AnonymousAuthenticationFilter.java:111)
	at org.springframework.security.web.FilterChainProxy$VirtualFilterChain.doFilter(FilterChainProxy.java:334)
	at org.springframework.security.web.servletapi.SecurityContextHolderAwareRequestFilter.doFilter(SecurityContextHolderAwareRequestFilter.java:158)
	at org.springframework.security.web.FilterChainProxy$VirtualFilterChain.doFilter(FilterChainProxy.java:334)
	at org.springframework.security.web.savedrequest.RequestCacheAwareFilter.doFilter(RequestCacheAwareFilter.java:63)
	at org.springframework.security.web.FilterChainProxy$VirtualFilterChain.doFilter(FilterChainProxy.java:334)
	at org.springframework.security.web.session.ConcurrentSessionFilter.doFilter(ConcurrentSessionFilter.java:152)
	at org.springframework.security.web.FilterChainProxy$VirtualFilterChain.doFilter(FilterChainProxy.java:334)
	at org.springframework.security.web.authentication.AbstractAuthenticationProcessingFilter.doFilter(AbstractAuthenticationProcessingFilter.java:200)
	at org.springframework.security.web.FilterChainProxy$VirtualFilterChain.doFilter(FilterChainProxy.java:334)
	at org.springframework.security.web.authentication.AbstractAuthenticationProcessingFilter.doFilter(AbstractAuthenticationProcessingFilter.java:200)
	at org.springframework.security.web.FilterChainProxy$VirtualFilterChain.doFilter(FilterChainProxy.java:334)
	at org.springframework.security.web.authentication.AbstractAuthenticationProcessingFilter.doFilter(AbstractAuthenticationProcessingFilter.java:200)
	at org.springframework.security.web.FilterChainProxy$VirtualFilterChain.doFilter(FilterChainProxy.java:334)
	at org.springframework.security.web.authentication.AbstractAuthenticationProcessingFilter.doFilter(AbstractAuthenticationProcessingFilter.java:200)
	at org.springframework.security.web.FilterChainProxy$VirtualFilterChain.doFilter(FilterChainProxy.java:334)
	at org.springframework.security.web.authentication.AbstractAuthenticationProcessingFilter.doFilter(AbstractAuthenticationProcessingFilter.java:200)
	at org.springframework.security.web.FilterChainProxy$VirtualFilterChain.doFilter(FilterChainProxy.java:334)
	at org.springframework.security.web.authentication.AbstractAuthenticationProcessingFilter.doFilter(AbstractAuthenticationProcessingFilter.java:200)
	at org.springframework.security.web.FilterChainProxy$VirtualFilterChain.doFilter(FilterChainProxy.java:334)
	at org.springframework.security.web.authentication.AbstractAuthenticationProcessingFilter.doFilter(AbstractAuthenticationProcessingFilter.java:200)
	at org.springframework.security.web.FilterChainProxy$VirtualFilterChain.doFilter(FilterChainProxy.java:334)
	at com.simbest.boot.security.auth.filter.CaptchaAuthenticationFilter.doFilter(CaptchaAuthenticationFilter.java:46)
	at org.springframework.security.web.FilterChainProxy$VirtualFilterChain.doFilter(FilterChainProxy.java:334)
	at com.simbest.boot.security.auth.filter.CaptchaAuthenticationFilter.doFilter(CaptchaAuthenticationFilter.java:46)
	at org.springframework.security.web.FilterChainProxy$VirtualFilterChain.doFilter(FilterChainProxy.java:334)
	at org.springframework.security.web.authentication.logout.LogoutFilter.doFilter(LogoutFilter.java:116)
	at org.springframework.security.web.FilterChainProxy$VirtualFilterChain.doFilter(FilterChainProxy.java:334)
	at org.springframework.web.filter.CorsFilter.doFilterInternal(CorsFilter.java:92)
	at org.springframework.web.filter.OncePerRequestFilter.doFilter(OncePerRequestFilter.java:119)
	at org.springframework.security.web.FilterChainProxy$VirtualFilterChain.doFilter(FilterChainProxy.java:334)
	at org.springframework.security.web.header.HeaderWriterFilter.doHeadersAfter(HeaderWriterFilter.java:92)
	at org.springframework.security.web.header.HeaderWriterFilter.doFilterInternal(HeaderWriterFilter.java:77)
	at org.springframework.web.filter.OncePerRequestFilter.doFilter(OncePerRequestFilter.java:119)
	at org.springframework.security.web.FilterChainProxy$VirtualFilterChain.doFilter(FilterChainProxy.java:334)
	at com.simbest.boot.security.auth.filter.CloudHeaderTokenAuthenticationFilter.doFilterInternal(CloudHeaderTokenAuthenticationFilter.java:171)
	at org.springframework.web.filter.OncePerRequestFilter.doFilter(OncePerRequestFilter.java:119)
	at org.springframework.security.web.FilterChainProxy$VirtualFilterChain.doFilter(FilterChainProxy.java:334)
	at org.springframework.security.web.context.SecurityContextPersistenceFilter.doFilter(SecurityContextPersistenceFilter.java:105)
	at org.springframework.security.web.FilterChainProxy$VirtualFilterChain.doFilter(FilterChainProxy.java:334)
	at org.springframework.security.web.context.request.async.WebAsyncManagerIntegrationFilter.doFilterInternal(WebAsyncManagerIntegrationFilter.java:56)
	at org.springframework.web.filter.OncePerRequestFilter.doFilter(OncePerRequestFilter.java:119)
	at org.springframework.security.web.FilterChainProxy$VirtualFilterChain.doFilter(FilterChainProxy.java:334)
	at org.springframework.security.web.FilterChainProxy.doFilterInternal(FilterChainProxy.java:215)
	at org.springframework.security.web.FilterChainProxy.doFilter(FilterChainProxy.java:178)
	at org.springframework.web.filter.DelegatingFilterProxy.invokeDelegate(DelegatingFilterProxy.java:358)
	at org.springframework.web.filter.DelegatingFilterProxy.doFilter(DelegatingFilterProxy.java:271)
	at org.apache.catalina.core.ApplicationFilterChain.internalDoFilter(ApplicationFilterChain.java:189)
	at org.apache.catalina.core.ApplicationFilterChain.doFilter(ApplicationFilterChain.java:162)
	at org.springframework.session.web.http.SessionRepositoryFilter.doFilterInternal(SessionRepositoryFilter.java:141)
	at org.springframework.session.web.http.OncePerRequestFilter.doFilter(OncePerRequestFilter.java:82)
	at org.apache.catalina.core.ApplicationFilterChain.internalDoFilter(ApplicationFilterChain.java:189)
	at org.apache.catalina.core.ApplicationFilterChain.doFilter(ApplicationFilterChain.java:162)
	at org.springframework.boot.actuate.metrics.web.servlet.WebMvcMetricsFilter.doFilterInternal(WebMvcMetricsFilter.java:108)
	at org.springframework.web.filter.OncePerRequestFilter.doFilter(OncePerRequestFilter.java:119)
	at org.apache.catalina.core.ApplicationFilterChain.internalDoFilter(ApplicationFilterChain.java:189)
	at org.apache.catalina.core.ApplicationFilterChain.doFilter(ApplicationFilterChain.java:162)
	at org.springframework.web.filter.CharacterEncodingFilter.doFilterInternal(CharacterEncodingFilter.java:201)
	at org.springframework.web.filter.OncePerRequestFilter.doFilter(OncePerRequestFilter.java:119)
	at org.apache.catalina.core.ApplicationFilterChain.internalDoFilter(ApplicationFilterChain.java:189)
	at org.apache.catalina.core.ApplicationFilterChain.doFilter(ApplicationFilterChain.java:162)
	at org.apache.catalina.core.StandardWrapperValve.invoke(StandardWrapperValve.java:197)
	at org.apache.catalina.core.StandardContextValve.invoke(StandardContextValve.java:97)
	at org.apache.catalina.authenticator.AuthenticatorBase.invoke(AuthenticatorBase.java:541)
	at org.apache.catalina.core.StandardHostValve.invoke(StandardHostValve.java:135)
	at org.apache.catalina.valves.ErrorReportValve.invoke(ErrorReportValve.java:92)
	at org.apache.catalina.core.StandardEngineValve.invoke(StandardEngineValve.java:78)
	at org.apache.catalina.connector.CoyoteAdapter.service(CoyoteAdapter.java:360)
	at org.apache.coyote.http11.Http11Processor.service(Http11Processor.java:399)
	at org.apache.coyote.AbstractProcessorLight.process(AbstractProcessorLight.java:65)
	at org.apache.coyote.AbstractProtocol$ConnectionHandler.process(AbstractProtocol.java:890)
	at org.apache.tomcat.util.net.NioEndpoint$SocketProcessor.doRun(NioEndpoint.java:1789)
	at org.apache.tomcat.util.net.SocketProcessorBase.run(SocketProcessorBase.java:49)
	at org.apache.tomcat.util.threads.ThreadPoolExecutor.runWorker(ThreadPoolExecutor.java:1191)
	at org.apache.tomcat.util.threads.ThreadPoolExecutor$Worker.run(ThreadPoolExecutor.java:659)
	at org.apache.tomcat.util.threads.TaskThread$WrappingRunnable.run(TaskThread.java:61)
	at java.lang.Thread.run(Thread.java:748)
Caused by: java.io.IOException: 你的主机中的软件中止了一个已建立的连接。
	at sun.nio.ch.SocketDispatcher.write0(Native Method)
	at sun.nio.ch.SocketDispatcher.write(SocketDispatcher.java:51)
	at sun.nio.ch.IOUtil.writeFromNativeBuffer(IOUtil.java:93)
	at sun.nio.ch.IOUtil.write(IOUtil.java:65)
	at sun.nio.ch.SocketChannelImpl.write(SocketChannelImpl.java:471)
	at org.apache.tomcat.util.net.NioChannel.write(NioChannel.java:135)
	at org.apache.tomcat.util.net.NioEndpoint$NioSocketWrapper.doWrite(NioEndpoint.java:1424)
	at org.apache.tomcat.util.net.SocketWrapperBase.doWrite(SocketWrapperBase.java:768)
	at org.apache.tomcat.util.net.SocketWrapperBase.writeBlocking(SocketWrapperBase.java:593)
	at org.apache.tomcat.util.net.SocketWrapperBase.write(SocketWrapperBase.java:537)
	at org.apache.coyote.http11.Http11OutputBuffer$SocketOutputBuffer.doWrite(Http11OutputBuffer.java:547)
	at org.apache.coyote.http11.filters.ChunkedOutputFilter.doWrite(ChunkedOutputFilter.java:110)
	at org.apache.coyote.http11.Http11OutputBuffer.doWrite(Http11OutputBuffer.java:194)
	at org.apache.coyote.Response.doWrite(Response.java:615)
	at org.apache.catalina.connector.OutputBuffer.realWriteBytes(OutputBuffer.java:340)
	... 162 common frames omitted
2025-05-29 17:25:53.396 [http-nio-8092-exec-14] WARN  org.springframework.web.servlet.mvc.method.annotation.ExceptionHandlerExceptionResolver.doResolveHandlerMethodException Line:414 - Failure in @ExceptionHandler com.simbest.boot.base.exception.RestControllerExceptionHandler#handleErrorException(HttpServletRequest, Exception)
org.apache.catalina.connector.ClientAbortException: java.io.IOException: 你的主机中的软件中止了一个已建立的连接。
	at org.apache.catalina.connector.OutputBuffer.realWriteBytes(OutputBuffer.java:353)
	at org.apache.catalina.connector.OutputBuffer.flushByteBuffer(OutputBuffer.java:784)
	at org.apache.catalina.connector.OutputBuffer.append(OutputBuffer.java:689)
	at org.apache.catalina.connector.OutputBuffer.writeBytes(OutputBuffer.java:388)
	at org.apache.catalina.connector.OutputBuffer.write(OutputBuffer.java:366)
	at org.apache.catalina.connector.CoyoteOutputStream.write(CoyoteOutputStream.java:96)
	at org.springframework.session.web.http.OnCommittedResponseWrapper$SaveContextServletOutputStream.write(OnCommittedResponseWrapper.java:624)
	at org.springframework.security.web.util.OnCommittedResponseWrapper$SaveContextServletOutputStream.write(OnCommittedResponseWrapper.java:645)
	at org.springframework.security.web.util.OnCommittedResponseWrapper$SaveContextServletOutputStream.write(OnCommittedResponseWrapper.java:645)
	at net.bull.javamelody.internal.web.FilterServletOutputStream.write(FilterServletOutputStream.java:88)
	at net.bull.javamelody.internal.web.CounterResponseStream.write(CounterResponseStream.java:81)
	at com.fasterxml.jackson.core.json.UTF8JsonGenerator._flushBuffer(UTF8JsonGenerator.java:2136)
	at com.fasterxml.jackson.core.json.UTF8JsonGenerator.flush(UTF8JsonGenerator.java:1150)
	at com.fasterxml.jackson.databind.ObjectWriter.writeValue(ObjectWriter.java:923)
	at org.springframework.http.converter.json.AbstractJackson2HttpMessageConverter.writeInternal(AbstractJackson2HttpMessageConverter.java:287)
	at org.springframework.http.converter.AbstractGenericHttpMessageConverter.write(AbstractGenericHttpMessageConverter.java:104)
	at org.springframework.web.servlet.mvc.method.annotation.AbstractMessageConverterMethodProcessor.writeWithMessageConverters(AbstractMessageConverterMethodProcessor.java:295)
	at org.springframework.web.servlet.mvc.method.annotation.RequestResponseBodyMethodProcessor.handleReturnValue(RequestResponseBodyMethodProcessor.java:181)
	at org.springframework.web.method.support.HandlerMethodReturnValueHandlerComposite.handleReturnValue(HandlerMethodReturnValueHandlerComposite.java:82)
	at org.springframework.web.servlet.mvc.method.annotation.ServletInvocableHandlerMethod.invokeAndHandle(ServletInvocableHandlerMethod.java:124)
	at org.springframework.web.servlet.mvc.method.annotation.ExceptionHandlerExceptionResolver.doResolveHandlerMethodException(ExceptionHandlerExceptionResolver.java:403)
	at org.springframework.web.servlet.handler.AbstractHandlerMethodExceptionResolver.doResolveException(AbstractHandlerMethodExceptionResolver.java:61)
	at org.springframework.web.servlet.handler.AbstractHandlerExceptionResolver.resolveException(AbstractHandlerExceptionResolver.java:141)
	at org.springframework.web.servlet.handler.HandlerExceptionResolverComposite.resolveException(HandlerExceptionResolverComposite.java:80)
	at org.springframework.web.servlet.DispatcherServlet.processHandlerException(DispatcherServlet.java:1300)
	at org.springframework.web.servlet.DispatcherServlet.processDispatchResult(DispatcherServlet.java:1111)
	at org.springframework.web.servlet.DispatcherServlet.doDispatch(DispatcherServlet.java:1057)
	at org.springframework.web.servlet.DispatcherServlet.doService(DispatcherServlet.java:943)
	at org.springframework.web.servlet.FrameworkServlet.processRequest(FrameworkServlet.java:1006)
	at org.springframework.web.servlet.FrameworkServlet.doPost(FrameworkServlet.java:909)
	at javax.servlet.http.HttpServlet.service(HttpServlet.java:681)
	at org.springframework.web.servlet.FrameworkServlet.service(FrameworkServlet.java:883)
	at javax.servlet.http.HttpServlet.service(HttpServlet.java:764)
	at org.apache.catalina.core.ApplicationFilterChain.internalDoFilter(ApplicationFilterChain.java:227)
	at org.apache.catalina.core.ApplicationFilterChain.doFilter(ApplicationFilterChain.java:162)
	at org.apache.tomcat.websocket.server.WsFilter.doFilter(WsFilter.java:53)
	at org.apache.catalina.core.ApplicationFilterChain.internalDoFilter(ApplicationFilterChain.java:189)
	at org.apache.catalina.core.ApplicationFilterChain.doFilter(ApplicationFilterChain.java:162)
	at com.simbest.boot.security.auth.filter.CaptchaAuthenticationFilter.doFilter(CaptchaAuthenticationFilter.java:46)
	at org.apache.catalina.core.ApplicationFilterChain.internalDoFilter(ApplicationFilterChain.java:189)
	at org.apache.catalina.core.ApplicationFilterChain.doFilter(ApplicationFilterChain.java:162)
	at com.simbest.boot.security.auth.filter.CaptchaAuthenticationFilter.doFilter(CaptchaAuthenticationFilter.java:46)
	at org.apache.catalina.core.ApplicationFilterChain.internalDoFilter(ApplicationFilterChain.java:189)
	at org.apache.catalina.core.ApplicationFilterChain.doFilter(ApplicationFilterChain.java:162)
	at org.springframework.security.web.authentication.AbstractAuthenticationProcessingFilter.doFilter(AbstractAuthenticationProcessingFilter.java:200)
	at org.apache.catalina.core.ApplicationFilterChain.internalDoFilter(ApplicationFilterChain.java:189)
	at org.apache.catalina.core.ApplicationFilterChain.doFilter(ApplicationFilterChain.java:162)
	at org.springframework.security.web.authentication.logout.LogoutFilter.doFilter(LogoutFilter.java:116)
	at org.apache.catalina.core.ApplicationFilterChain.internalDoFilter(ApplicationFilterChain.java:189)
	at org.apache.catalina.core.ApplicationFilterChain.doFilter(ApplicationFilterChain.java:162)
	at org.springframework.security.web.authentication.AbstractAuthenticationProcessingFilter.doFilter(AbstractAuthenticationProcessingFilter.java:200)
	at org.apache.catalina.core.ApplicationFilterChain.internalDoFilter(ApplicationFilterChain.java:189)
	at org.apache.catalina.core.ApplicationFilterChain.doFilter(ApplicationFilterChain.java:162)
	at org.springframework.security.web.authentication.AbstractAuthenticationProcessingFilter.doFilter(AbstractAuthenticationProcessingFilter.java:200)
	at org.apache.catalina.core.ApplicationFilterChain.internalDoFilter(ApplicationFilterChain.java:189)
	at org.apache.catalina.core.ApplicationFilterChain.doFilter(ApplicationFilterChain.java:162)
	at org.springframework.security.web.authentication.AbstractAuthenticationProcessingFilter.doFilter(AbstractAuthenticationProcessingFilter.java:200)
	at org.apache.catalina.core.ApplicationFilterChain.internalDoFilter(ApplicationFilterChain.java:189)
	at org.apache.catalina.core.ApplicationFilterChain.doFilter(ApplicationFilterChain.java:162)
	at org.springframework.security.web.authentication.AbstractAuthenticationProcessingFilter.doFilter(AbstractAuthenticationProcessingFilter.java:200)
	at org.apache.catalina.core.ApplicationFilterChain.internalDoFilter(ApplicationFilterChain.java:189)
	at org.apache.catalina.core.ApplicationFilterChain.doFilter(ApplicationFilterChain.java:162)
	at org.springframework.security.web.authentication.AbstractAuthenticationProcessingFilter.doFilter(AbstractAuthenticationProcessingFilter.java:200)
	at org.apache.catalina.core.ApplicationFilterChain.internalDoFilter(ApplicationFilterChain.java:189)
	at org.apache.catalina.core.ApplicationFilterChain.doFilter(ApplicationFilterChain.java:162)
	at com.simbest.boot.base.web.filter.CsrfProtectFilter.doFilter(CsrfProtectFilter.java:164)
	at org.apache.catalina.core.ApplicationFilterChain.internalDoFilter(ApplicationFilterChain.java:189)
	at org.apache.catalina.core.ApplicationFilterChain.doFilter(ApplicationFilterChain.java:162)
	at org.springframework.web.servlet.resource.ResourceUrlEncodingFilter.doFilter(ResourceUrlEncodingFilter.java:64)
	at org.apache.catalina.core.ApplicationFilterChain.internalDoFilter(ApplicationFilterChain.java:189)
	at org.apache.catalina.core.ApplicationFilterChain.doFilter(ApplicationFilterChain.java:162)
	at net.bull.javamelody.MonitoringFilter.doFilter(MonitoringFilter.java:199)
	at org.apache.catalina.core.ApplicationFilterChain.internalDoFilter(ApplicationFilterChain.java:189)
	at org.apache.catalina.core.ApplicationFilterChain.doFilter(ApplicationFilterChain.java:162)
	at net.bull.javamelody.MonitoringFilter.doFilter(MonitoringFilter.java:239)
	at net.bull.javamelody.MonitoringFilter.doFilter(MonitoringFilter.java:215)
	at org.apache.catalina.core.ApplicationFilterChain.internalDoFilter(ApplicationFilterChain.java:189)
	at org.apache.catalina.core.ApplicationFilterChain.doFilter(ApplicationFilterChain.java:162)
	at org.springframework.security.web.FilterChainProxy$VirtualFilterChain.doFilter(FilterChainProxy.java:320)
	at org.springframework.security.web.access.intercept.FilterSecurityInterceptor.invoke(FilterSecurityInterceptor.java:126)
	at org.springframework.security.web.access.intercept.FilterSecurityInterceptor.doFilter(FilterSecurityInterceptor.java:90)
	at org.springframework.security.web.FilterChainProxy$VirtualFilterChain.doFilter(FilterChainProxy.java:334)
	at org.springframework.security.web.access.ExceptionTranslationFilter.doFilter(ExceptionTranslationFilter.java:118)
	at org.springframework.security.web.FilterChainProxy$VirtualFilterChain.doFilter(FilterChainProxy.java:334)
	at org.springframework.security.web.session.SessionManagementFilter.doFilter(SessionManagementFilter.java:137)
	at org.springframework.security.web.FilterChainProxy$VirtualFilterChain.doFilter(FilterChainProxy.java:334)
	at org.springframework.security.web.authentication.AnonymousAuthenticationFilter.doFilter(AnonymousAuthenticationFilter.java:111)
	at org.springframework.security.web.FilterChainProxy$VirtualFilterChain.doFilter(FilterChainProxy.java:334)
	at org.springframework.security.web.servletapi.SecurityContextHolderAwareRequestFilter.doFilter(SecurityContextHolderAwareRequestFilter.java:158)
	at org.springframework.security.web.FilterChainProxy$VirtualFilterChain.doFilter(FilterChainProxy.java:334)
	at org.springframework.security.web.savedrequest.RequestCacheAwareFilter.doFilter(RequestCacheAwareFilter.java:63)
	at org.springframework.security.web.FilterChainProxy$VirtualFilterChain.doFilter(FilterChainProxy.java:334)
	at org.springframework.security.web.session.ConcurrentSessionFilter.doFilter(ConcurrentSessionFilter.java:152)
	at org.springframework.security.web.FilterChainProxy$VirtualFilterChain.doFilter(FilterChainProxy.java:334)
	at org.springframework.security.web.authentication.AbstractAuthenticationProcessingFilter.doFilter(AbstractAuthenticationProcessingFilter.java:200)
	at org.springframework.security.web.FilterChainProxy$VirtualFilterChain.doFilter(FilterChainProxy.java:334)
	at org.springframework.security.web.authentication.AbstractAuthenticationProcessingFilter.doFilter(AbstractAuthenticationProcessingFilter.java:200)
	at org.springframework.security.web.FilterChainProxy$VirtualFilterChain.doFilter(FilterChainProxy.java:334)
	at org.springframework.security.web.authentication.AbstractAuthenticationProcessingFilter.doFilter(AbstractAuthenticationProcessingFilter.java:200)
	at org.springframework.security.web.FilterChainProxy$VirtualFilterChain.doFilter(FilterChainProxy.java:334)
	at org.springframework.security.web.authentication.AbstractAuthenticationProcessingFilter.doFilter(AbstractAuthenticationProcessingFilter.java:200)
	at org.springframework.security.web.FilterChainProxy$VirtualFilterChain.doFilter(FilterChainProxy.java:334)
	at org.springframework.security.web.authentication.AbstractAuthenticationProcessingFilter.doFilter(AbstractAuthenticationProcessingFilter.java:200)
	at org.springframework.security.web.FilterChainProxy$VirtualFilterChain.doFilter(FilterChainProxy.java:334)
	at org.springframework.security.web.authentication.AbstractAuthenticationProcessingFilter.doFilter(AbstractAuthenticationProcessingFilter.java:200)
	at org.springframework.security.web.FilterChainProxy$VirtualFilterChain.doFilter(FilterChainProxy.java:334)
	at org.springframework.security.web.authentication.AbstractAuthenticationProcessingFilter.doFilter(AbstractAuthenticationProcessingFilter.java:200)
	at org.springframework.security.web.FilterChainProxy$VirtualFilterChain.doFilter(FilterChainProxy.java:334)
	at com.simbest.boot.security.auth.filter.CaptchaAuthenticationFilter.doFilter(CaptchaAuthenticationFilter.java:46)
	at org.springframework.security.web.FilterChainProxy$VirtualFilterChain.doFilter(FilterChainProxy.java:334)
	at com.simbest.boot.security.auth.filter.CaptchaAuthenticationFilter.doFilter(CaptchaAuthenticationFilter.java:46)
	at org.springframework.security.web.FilterChainProxy$VirtualFilterChain.doFilter(FilterChainProxy.java:334)
	at org.springframework.security.web.authentication.logout.LogoutFilter.doFilter(LogoutFilter.java:116)
	at org.springframework.security.web.FilterChainProxy$VirtualFilterChain.doFilter(FilterChainProxy.java:334)
	at org.springframework.web.filter.CorsFilter.doFilterInternal(CorsFilter.java:92)
	at org.springframework.web.filter.OncePerRequestFilter.doFilter(OncePerRequestFilter.java:119)
	at org.springframework.security.web.FilterChainProxy$VirtualFilterChain.doFilter(FilterChainProxy.java:334)
	at org.springframework.security.web.header.HeaderWriterFilter.doHeadersAfter(HeaderWriterFilter.java:92)
	at org.springframework.security.web.header.HeaderWriterFilter.doFilterInternal(HeaderWriterFilter.java:77)
	at org.springframework.web.filter.OncePerRequestFilter.doFilter(OncePerRequestFilter.java:119)
	at org.springframework.security.web.FilterChainProxy$VirtualFilterChain.doFilter(FilterChainProxy.java:334)
	at com.simbest.boot.security.auth.filter.CloudHeaderTokenAuthenticationFilter.doFilterInternal(CloudHeaderTokenAuthenticationFilter.java:171)
	at org.springframework.web.filter.OncePerRequestFilter.doFilter(OncePerRequestFilter.java:119)
	at org.springframework.security.web.FilterChainProxy$VirtualFilterChain.doFilter(FilterChainProxy.java:334)
	at org.springframework.security.web.context.SecurityContextPersistenceFilter.doFilter(SecurityContextPersistenceFilter.java:105)
	at org.springframework.security.web.FilterChainProxy$VirtualFilterChain.doFilter(FilterChainProxy.java:334)
	at org.springframework.security.web.context.request.async.WebAsyncManagerIntegrationFilter.doFilterInternal(WebAsyncManagerIntegrationFilter.java:56)
	at org.springframework.web.filter.OncePerRequestFilter.doFilter(OncePerRequestFilter.java:119)
	at org.springframework.security.web.FilterChainProxy$VirtualFilterChain.doFilter(FilterChainProxy.java:334)
	at org.springframework.security.web.FilterChainProxy.doFilterInternal(FilterChainProxy.java:215)
	at org.springframework.security.web.FilterChainProxy.doFilter(FilterChainProxy.java:178)
	at org.springframework.web.filter.DelegatingFilterProxy.invokeDelegate(DelegatingFilterProxy.java:358)
	at org.springframework.web.filter.DelegatingFilterProxy.doFilter(DelegatingFilterProxy.java:271)
	at org.apache.catalina.core.ApplicationFilterChain.internalDoFilter(ApplicationFilterChain.java:189)
	at org.apache.catalina.core.ApplicationFilterChain.doFilter(ApplicationFilterChain.java:162)
	at org.springframework.session.web.http.SessionRepositoryFilter.doFilterInternal(SessionRepositoryFilter.java:141)
	at org.springframework.session.web.http.OncePerRequestFilter.doFilter(OncePerRequestFilter.java:82)
	at org.apache.catalina.core.ApplicationFilterChain.internalDoFilter(ApplicationFilterChain.java:189)
	at org.apache.catalina.core.ApplicationFilterChain.doFilter(ApplicationFilterChain.java:162)
	at org.springframework.boot.actuate.metrics.web.servlet.WebMvcMetricsFilter.doFilterInternal(WebMvcMetricsFilter.java:108)
	at org.springframework.web.filter.OncePerRequestFilter.doFilter(OncePerRequestFilter.java:119)
	at org.apache.catalina.core.ApplicationFilterChain.internalDoFilter(ApplicationFilterChain.java:189)
	at org.apache.catalina.core.ApplicationFilterChain.doFilter(ApplicationFilterChain.java:162)
	at org.springframework.web.filter.CharacterEncodingFilter.doFilterInternal(CharacterEncodingFilter.java:201)
	at org.springframework.web.filter.OncePerRequestFilter.doFilter(OncePerRequestFilter.java:119)
	at org.apache.catalina.core.ApplicationFilterChain.internalDoFilter(ApplicationFilterChain.java:189)
	at org.apache.catalina.core.ApplicationFilterChain.doFilter(ApplicationFilterChain.java:162)
	at org.apache.catalina.core.StandardWrapperValve.invoke(StandardWrapperValve.java:197)
	at org.apache.catalina.core.StandardContextValve.invoke(StandardContextValve.java:97)
	at org.apache.catalina.authenticator.AuthenticatorBase.invoke(AuthenticatorBase.java:541)
	at org.apache.catalina.core.StandardHostValve.invoke(StandardHostValve.java:135)
	at org.apache.catalina.valves.ErrorReportValve.invoke(ErrorReportValve.java:92)
	at org.apache.catalina.core.StandardEngineValve.invoke(StandardEngineValve.java:78)
	at org.apache.catalina.connector.CoyoteAdapter.service(CoyoteAdapter.java:360)
	at org.apache.coyote.http11.Http11Processor.service(Http11Processor.java:399)
	at org.apache.coyote.AbstractProcessorLight.process(AbstractProcessorLight.java:65)
	at org.apache.coyote.AbstractProtocol$ConnectionHandler.process(AbstractProtocol.java:890)
	at org.apache.tomcat.util.net.NioEndpoint$SocketProcessor.doRun(NioEndpoint.java:1789)
	at org.apache.tomcat.util.net.SocketProcessorBase.run(SocketProcessorBase.java:49)
	at org.apache.tomcat.util.threads.ThreadPoolExecutor.runWorker(ThreadPoolExecutor.java:1191)
	at org.apache.tomcat.util.threads.ThreadPoolExecutor$Worker.run(ThreadPoolExecutor.java:659)
	at org.apache.tomcat.util.threads.TaskThread$WrappingRunnable.run(TaskThread.java:61)
	at java.lang.Thread.run(Thread.java:748)
Caused by: java.io.IOException: 你的主机中的软件中止了一个已建立的连接。
	at sun.nio.ch.SocketDispatcher.write0(Native Method)
	at sun.nio.ch.SocketDispatcher.write(SocketDispatcher.java:51)
	at sun.nio.ch.IOUtil.writeFromNativeBuffer(IOUtil.java:93)
	at sun.nio.ch.IOUtil.write(IOUtil.java:65)
	at sun.nio.ch.SocketChannelImpl.write(SocketChannelImpl.java:471)
	at org.apache.tomcat.util.net.NioChannel.write(NioChannel.java:135)
	at org.apache.tomcat.util.net.NioEndpoint$NioSocketWrapper.doWrite(NioEndpoint.java:1424)
	at org.apache.tomcat.util.net.SocketWrapperBase.doWrite(SocketWrapperBase.java:768)
	at org.apache.tomcat.util.net.SocketWrapperBase.writeBlocking(SocketWrapperBase.java:593)
	at org.apache.tomcat.util.net.SocketWrapperBase.write(SocketWrapperBase.java:537)
	at org.apache.coyote.http11.Http11OutputBuffer$SocketOutputBuffer.doWrite(Http11OutputBuffer.java:547)
	at org.apache.coyote.http11.filters.ChunkedOutputFilter.doWrite(ChunkedOutputFilter.java:110)
	at org.apache.coyote.http11.Http11OutputBuffer.doWrite(Http11OutputBuffer.java:194)
	at org.apache.coyote.Response.doWrite(Response.java:615)
	at org.apache.catalina.connector.OutputBuffer.realWriteBytes(OutputBuffer.java:340)
	... 162 common frames omitted
2025-05-29 17:25:53.634 [http-nio-8092-exec-12] WARN  org.springframework.web.servlet.mvc.method.annotation.ExceptionHandlerExceptionResolver.doResolveHandlerMethodException Line:414 - Failure in @ExceptionHandler com.simbest.boot.base.exception.RestControllerExceptionHandler#handleErrorException(HttpServletRequest, Exception)
org.apache.catalina.connector.ClientAbortException: java.io.IOException: 你的主机中的软件中止了一个已建立的连接。
	at org.apache.catalina.connector.OutputBuffer.realWriteBytes(OutputBuffer.java:353)
	at org.apache.catalina.connector.OutputBuffer.flushByteBuffer(OutputBuffer.java:784)
	at org.apache.catalina.connector.OutputBuffer.append(OutputBuffer.java:689)
	at org.apache.catalina.connector.OutputBuffer.writeBytes(OutputBuffer.java:388)
	at org.apache.catalina.connector.OutputBuffer.write(OutputBuffer.java:366)
	at org.apache.catalina.connector.CoyoteOutputStream.write(CoyoteOutputStream.java:96)
	at org.springframework.session.web.http.OnCommittedResponseWrapper$SaveContextServletOutputStream.write(OnCommittedResponseWrapper.java:624)
	at org.springframework.security.web.util.OnCommittedResponseWrapper$SaveContextServletOutputStream.write(OnCommittedResponseWrapper.java:645)
	at org.springframework.security.web.util.OnCommittedResponseWrapper$SaveContextServletOutputStream.write(OnCommittedResponseWrapper.java:645)
	at net.bull.javamelody.internal.web.FilterServletOutputStream.write(FilterServletOutputStream.java:88)
	at net.bull.javamelody.internal.web.CounterResponseStream.write(CounterResponseStream.java:81)
	at com.fasterxml.jackson.core.json.UTF8JsonGenerator._flushBuffer(UTF8JsonGenerator.java:2136)
	at com.fasterxml.jackson.core.json.UTF8JsonGenerator.flush(UTF8JsonGenerator.java:1150)
	at com.fasterxml.jackson.databind.ObjectWriter.writeValue(ObjectWriter.java:923)
	at org.springframework.http.converter.json.AbstractJackson2HttpMessageConverter.writeInternal(AbstractJackson2HttpMessageConverter.java:287)
	at org.springframework.http.converter.AbstractGenericHttpMessageConverter.write(AbstractGenericHttpMessageConverter.java:104)
	at org.springframework.web.servlet.mvc.method.annotation.AbstractMessageConverterMethodProcessor.writeWithMessageConverters(AbstractMessageConverterMethodProcessor.java:295)
	at org.springframework.web.servlet.mvc.method.annotation.RequestResponseBodyMethodProcessor.handleReturnValue(RequestResponseBodyMethodProcessor.java:181)
	at org.springframework.web.method.support.HandlerMethodReturnValueHandlerComposite.handleReturnValue(HandlerMethodReturnValueHandlerComposite.java:82)
	at org.springframework.web.servlet.mvc.method.annotation.ServletInvocableHandlerMethod.invokeAndHandle(ServletInvocableHandlerMethod.java:124)
	at org.springframework.web.servlet.mvc.method.annotation.ExceptionHandlerExceptionResolver.doResolveHandlerMethodException(ExceptionHandlerExceptionResolver.java:403)
	at org.springframework.web.servlet.handler.AbstractHandlerMethodExceptionResolver.doResolveException(AbstractHandlerMethodExceptionResolver.java:61)
	at org.springframework.web.servlet.handler.AbstractHandlerExceptionResolver.resolveException(AbstractHandlerExceptionResolver.java:141)
	at org.springframework.web.servlet.handler.HandlerExceptionResolverComposite.resolveException(HandlerExceptionResolverComposite.java:80)
	at org.springframework.web.servlet.DispatcherServlet.processHandlerException(DispatcherServlet.java:1300)
	at org.springframework.web.servlet.DispatcherServlet.processDispatchResult(DispatcherServlet.java:1111)
	at org.springframework.web.servlet.DispatcherServlet.doDispatch(DispatcherServlet.java:1057)
	at org.springframework.web.servlet.DispatcherServlet.doService(DispatcherServlet.java:943)
	at org.springframework.web.servlet.FrameworkServlet.processRequest(FrameworkServlet.java:1006)
	at org.springframework.web.servlet.FrameworkServlet.doPost(FrameworkServlet.java:909)
	at javax.servlet.http.HttpServlet.service(HttpServlet.java:681)
	at org.springframework.web.servlet.FrameworkServlet.service(FrameworkServlet.java:883)
	at javax.servlet.http.HttpServlet.service(HttpServlet.java:764)
	at org.apache.catalina.core.ApplicationFilterChain.internalDoFilter(ApplicationFilterChain.java:227)
	at org.apache.catalina.core.ApplicationFilterChain.doFilter(ApplicationFilterChain.java:162)
	at org.apache.tomcat.websocket.server.WsFilter.doFilter(WsFilter.java:53)
	at org.apache.catalina.core.ApplicationFilterChain.internalDoFilter(ApplicationFilterChain.java:189)
	at org.apache.catalina.core.ApplicationFilterChain.doFilter(ApplicationFilterChain.java:162)
	at com.simbest.boot.security.auth.filter.CaptchaAuthenticationFilter.doFilter(CaptchaAuthenticationFilter.java:46)
	at org.apache.catalina.core.ApplicationFilterChain.internalDoFilter(ApplicationFilterChain.java:189)
	at org.apache.catalina.core.ApplicationFilterChain.doFilter(ApplicationFilterChain.java:162)
	at com.simbest.boot.security.auth.filter.CaptchaAuthenticationFilter.doFilter(CaptchaAuthenticationFilter.java:46)
	at org.apache.catalina.core.ApplicationFilterChain.internalDoFilter(ApplicationFilterChain.java:189)
	at org.apache.catalina.core.ApplicationFilterChain.doFilter(ApplicationFilterChain.java:162)
	at org.springframework.security.web.authentication.AbstractAuthenticationProcessingFilter.doFilter(AbstractAuthenticationProcessingFilter.java:200)
	at org.apache.catalina.core.ApplicationFilterChain.internalDoFilter(ApplicationFilterChain.java:189)
	at org.apache.catalina.core.ApplicationFilterChain.doFilter(ApplicationFilterChain.java:162)
	at org.springframework.security.web.authentication.logout.LogoutFilter.doFilter(LogoutFilter.java:116)
	at org.apache.catalina.core.ApplicationFilterChain.internalDoFilter(ApplicationFilterChain.java:189)
	at org.apache.catalina.core.ApplicationFilterChain.doFilter(ApplicationFilterChain.java:162)
	at org.springframework.security.web.authentication.AbstractAuthenticationProcessingFilter.doFilter(AbstractAuthenticationProcessingFilter.java:200)
	at org.apache.catalina.core.ApplicationFilterChain.internalDoFilter(ApplicationFilterChain.java:189)
	at org.apache.catalina.core.ApplicationFilterChain.doFilter(ApplicationFilterChain.java:162)
	at org.springframework.security.web.authentication.AbstractAuthenticationProcessingFilter.doFilter(AbstractAuthenticationProcessingFilter.java:200)
	at org.apache.catalina.core.ApplicationFilterChain.internalDoFilter(ApplicationFilterChain.java:189)
	at org.apache.catalina.core.ApplicationFilterChain.doFilter(ApplicationFilterChain.java:162)
	at org.springframework.security.web.authentication.AbstractAuthenticationProcessingFilter.doFilter(AbstractAuthenticationProcessingFilter.java:200)
	at org.apache.catalina.core.ApplicationFilterChain.internalDoFilter(ApplicationFilterChain.java:189)
	at org.apache.catalina.core.ApplicationFilterChain.doFilter(ApplicationFilterChain.java:162)
	at org.springframework.security.web.authentication.AbstractAuthenticationProcessingFilter.doFilter(AbstractAuthenticationProcessingFilter.java:200)
	at org.apache.catalina.core.ApplicationFilterChain.internalDoFilter(ApplicationFilterChain.java:189)
	at org.apache.catalina.core.ApplicationFilterChain.doFilter(ApplicationFilterChain.java:162)
	at org.springframework.security.web.authentication.AbstractAuthenticationProcessingFilter.doFilter(AbstractAuthenticationProcessingFilter.java:200)
	at org.apache.catalina.core.ApplicationFilterChain.internalDoFilter(ApplicationFilterChain.java:189)
	at org.apache.catalina.core.ApplicationFilterChain.doFilter(ApplicationFilterChain.java:162)
	at com.simbest.boot.base.web.filter.CsrfProtectFilter.doFilter(CsrfProtectFilter.java:164)
	at org.apache.catalina.core.ApplicationFilterChain.internalDoFilter(ApplicationFilterChain.java:189)
	at org.apache.catalina.core.ApplicationFilterChain.doFilter(ApplicationFilterChain.java:162)
	at org.springframework.web.servlet.resource.ResourceUrlEncodingFilter.doFilter(ResourceUrlEncodingFilter.java:64)
	at org.apache.catalina.core.ApplicationFilterChain.internalDoFilter(ApplicationFilterChain.java:189)
	at org.apache.catalina.core.ApplicationFilterChain.doFilter(ApplicationFilterChain.java:162)
	at net.bull.javamelody.MonitoringFilter.doFilter(MonitoringFilter.java:199)
	at org.apache.catalina.core.ApplicationFilterChain.internalDoFilter(ApplicationFilterChain.java:189)
	at org.apache.catalina.core.ApplicationFilterChain.doFilter(ApplicationFilterChain.java:162)
	at net.bull.javamelody.MonitoringFilter.doFilter(MonitoringFilter.java:239)
	at net.bull.javamelody.MonitoringFilter.doFilter(MonitoringFilter.java:215)
	at org.apache.catalina.core.ApplicationFilterChain.internalDoFilter(ApplicationFilterChain.java:189)
	at org.apache.catalina.core.ApplicationFilterChain.doFilter(ApplicationFilterChain.java:162)
	at org.springframework.security.web.FilterChainProxy$VirtualFilterChain.doFilter(FilterChainProxy.java:320)
	at org.springframework.security.web.access.intercept.FilterSecurityInterceptor.invoke(FilterSecurityInterceptor.java:126)
	at org.springframework.security.web.access.intercept.FilterSecurityInterceptor.doFilter(FilterSecurityInterceptor.java:90)
	at org.springframework.security.web.FilterChainProxy$VirtualFilterChain.doFilter(FilterChainProxy.java:334)
	at org.springframework.security.web.access.ExceptionTranslationFilter.doFilter(ExceptionTranslationFilter.java:118)
	at org.springframework.security.web.FilterChainProxy$VirtualFilterChain.doFilter(FilterChainProxy.java:334)
	at org.springframework.security.web.session.SessionManagementFilter.doFilter(SessionManagementFilter.java:137)
	at org.springframework.security.web.FilterChainProxy$VirtualFilterChain.doFilter(FilterChainProxy.java:334)
	at org.springframework.security.web.authentication.AnonymousAuthenticationFilter.doFilter(AnonymousAuthenticationFilter.java:111)
	at org.springframework.security.web.FilterChainProxy$VirtualFilterChain.doFilter(FilterChainProxy.java:334)
	at org.springframework.security.web.servletapi.SecurityContextHolderAwareRequestFilter.doFilter(SecurityContextHolderAwareRequestFilter.java:158)
	at org.springframework.security.web.FilterChainProxy$VirtualFilterChain.doFilter(FilterChainProxy.java:334)
	at org.springframework.security.web.savedrequest.RequestCacheAwareFilter.doFilter(RequestCacheAwareFilter.java:63)
	at org.springframework.security.web.FilterChainProxy$VirtualFilterChain.doFilter(FilterChainProxy.java:334)
	at org.springframework.security.web.session.ConcurrentSessionFilter.doFilter(ConcurrentSessionFilter.java:152)
	at org.springframework.security.web.FilterChainProxy$VirtualFilterChain.doFilter(FilterChainProxy.java:334)
	at org.springframework.security.web.authentication.AbstractAuthenticationProcessingFilter.doFilter(AbstractAuthenticationProcessingFilter.java:200)
	at org.springframework.security.web.FilterChainProxy$VirtualFilterChain.doFilter(FilterChainProxy.java:334)
	at org.springframework.security.web.authentication.AbstractAuthenticationProcessingFilter.doFilter(AbstractAuthenticationProcessingFilter.java:200)
	at org.springframework.security.web.FilterChainProxy$VirtualFilterChain.doFilter(FilterChainProxy.java:334)
	at org.springframework.security.web.authentication.AbstractAuthenticationProcessingFilter.doFilter(AbstractAuthenticationProcessingFilter.java:200)
	at org.springframework.security.web.FilterChainProxy$VirtualFilterChain.doFilter(FilterChainProxy.java:334)
	at org.springframework.security.web.authentication.AbstractAuthenticationProcessingFilter.doFilter(AbstractAuthenticationProcessingFilter.java:200)
	at org.springframework.security.web.FilterChainProxy$VirtualFilterChain.doFilter(FilterChainProxy.java:334)
	at org.springframework.security.web.authentication.AbstractAuthenticationProcessingFilter.doFilter(AbstractAuthenticationProcessingFilter.java:200)
	at org.springframework.security.web.FilterChainProxy$VirtualFilterChain.doFilter(FilterChainProxy.java:334)
	at org.springframework.security.web.authentication.AbstractAuthenticationProcessingFilter.doFilter(AbstractAuthenticationProcessingFilter.java:200)
	at org.springframework.security.web.FilterChainProxy$VirtualFilterChain.doFilter(FilterChainProxy.java:334)
	at org.springframework.security.web.authentication.AbstractAuthenticationProcessingFilter.doFilter(AbstractAuthenticationProcessingFilter.java:200)
	at org.springframework.security.web.FilterChainProxy$VirtualFilterChain.doFilter(FilterChainProxy.java:334)
	at com.simbest.boot.security.auth.filter.CaptchaAuthenticationFilter.doFilter(CaptchaAuthenticationFilter.java:46)
	at org.springframework.security.web.FilterChainProxy$VirtualFilterChain.doFilter(FilterChainProxy.java:334)
	at com.simbest.boot.security.auth.filter.CaptchaAuthenticationFilter.doFilter(CaptchaAuthenticationFilter.java:46)
	at org.springframework.security.web.FilterChainProxy$VirtualFilterChain.doFilter(FilterChainProxy.java:334)
	at org.springframework.security.web.authentication.logout.LogoutFilter.doFilter(LogoutFilter.java:116)
	at org.springframework.security.web.FilterChainProxy$VirtualFilterChain.doFilter(FilterChainProxy.java:334)
	at org.springframework.web.filter.CorsFilter.doFilterInternal(CorsFilter.java:92)
	at org.springframework.web.filter.OncePerRequestFilter.doFilter(OncePerRequestFilter.java:119)
	at org.springframework.security.web.FilterChainProxy$VirtualFilterChain.doFilter(FilterChainProxy.java:334)
	at org.springframework.security.web.header.HeaderWriterFilter.doHeadersAfter(HeaderWriterFilter.java:92)
	at org.springframework.security.web.header.HeaderWriterFilter.doFilterInternal(HeaderWriterFilter.java:77)
	at org.springframework.web.filter.OncePerRequestFilter.doFilter(OncePerRequestFilter.java:119)
	at org.springframework.security.web.FilterChainProxy$VirtualFilterChain.doFilter(FilterChainProxy.java:334)
	at com.simbest.boot.security.auth.filter.CloudHeaderTokenAuthenticationFilter.doFilterInternal(CloudHeaderTokenAuthenticationFilter.java:171)
	at org.springframework.web.filter.OncePerRequestFilter.doFilter(OncePerRequestFilter.java:119)
	at org.springframework.security.web.FilterChainProxy$VirtualFilterChain.doFilter(FilterChainProxy.java:334)
	at org.springframework.security.web.context.SecurityContextPersistenceFilter.doFilter(SecurityContextPersistenceFilter.java:105)
	at org.springframework.security.web.FilterChainProxy$VirtualFilterChain.doFilter(FilterChainProxy.java:334)
	at org.springframework.security.web.context.request.async.WebAsyncManagerIntegrationFilter.doFilterInternal(WebAsyncManagerIntegrationFilter.java:56)
	at org.springframework.web.filter.OncePerRequestFilter.doFilter(OncePerRequestFilter.java:119)
	at org.springframework.security.web.FilterChainProxy$VirtualFilterChain.doFilter(FilterChainProxy.java:334)
	at org.springframework.security.web.FilterChainProxy.doFilterInternal(FilterChainProxy.java:215)
	at org.springframework.security.web.FilterChainProxy.doFilter(FilterChainProxy.java:178)
	at org.springframework.web.filter.DelegatingFilterProxy.invokeDelegate(DelegatingFilterProxy.java:358)
	at org.springframework.web.filter.DelegatingFilterProxy.doFilter(DelegatingFilterProxy.java:271)
	at org.apache.catalina.core.ApplicationFilterChain.internalDoFilter(ApplicationFilterChain.java:189)
	at org.apache.catalina.core.ApplicationFilterChain.doFilter(ApplicationFilterChain.java:162)
	at org.springframework.session.web.http.SessionRepositoryFilter.doFilterInternal(SessionRepositoryFilter.java:141)
	at org.springframework.session.web.http.OncePerRequestFilter.doFilter(OncePerRequestFilter.java:82)
	at org.apache.catalina.core.ApplicationFilterChain.internalDoFilter(ApplicationFilterChain.java:189)
	at org.apache.catalina.core.ApplicationFilterChain.doFilter(ApplicationFilterChain.java:162)
	at org.springframework.boot.actuate.metrics.web.servlet.WebMvcMetricsFilter.doFilterInternal(WebMvcMetricsFilter.java:108)
	at org.springframework.web.filter.OncePerRequestFilter.doFilter(OncePerRequestFilter.java:119)
	at org.apache.catalina.core.ApplicationFilterChain.internalDoFilter(ApplicationFilterChain.java:189)
	at org.apache.catalina.core.ApplicationFilterChain.doFilter(ApplicationFilterChain.java:162)
	at org.springframework.web.filter.CharacterEncodingFilter.doFilterInternal(CharacterEncodingFilter.java:201)
	at org.springframework.web.filter.OncePerRequestFilter.doFilter(OncePerRequestFilter.java:119)
	at org.apache.catalina.core.ApplicationFilterChain.internalDoFilter(ApplicationFilterChain.java:189)
	at org.apache.catalina.core.ApplicationFilterChain.doFilter(ApplicationFilterChain.java:162)
	at org.apache.catalina.core.StandardWrapperValve.invoke(StandardWrapperValve.java:197)
	at org.apache.catalina.core.StandardContextValve.invoke(StandardContextValve.java:97)
	at org.apache.catalina.authenticator.AuthenticatorBase.invoke(AuthenticatorBase.java:541)
	at org.apache.catalina.core.StandardHostValve.invoke(StandardHostValve.java:135)
	at org.apache.catalina.valves.ErrorReportValve.invoke(ErrorReportValve.java:92)
	at org.apache.catalina.core.StandardEngineValve.invoke(StandardEngineValve.java:78)
	at org.apache.catalina.connector.CoyoteAdapter.service(CoyoteAdapter.java:360)
	at org.apache.coyote.http11.Http11Processor.service(Http11Processor.java:399)
	at org.apache.coyote.AbstractProcessorLight.process(AbstractProcessorLight.java:65)
	at org.apache.coyote.AbstractProtocol$ConnectionHandler.process(AbstractProtocol.java:890)
	at org.apache.tomcat.util.net.NioEndpoint$SocketProcessor.doRun(NioEndpoint.java:1789)
	at org.apache.tomcat.util.net.SocketProcessorBase.run(SocketProcessorBase.java:49)
	at org.apache.tomcat.util.threads.ThreadPoolExecutor.runWorker(ThreadPoolExecutor.java:1191)
	at org.apache.tomcat.util.threads.ThreadPoolExecutor$Worker.run(ThreadPoolExecutor.java:659)
	at org.apache.tomcat.util.threads.TaskThread$WrappingRunnable.run(TaskThread.java:61)
	at java.lang.Thread.run(Thread.java:748)
Caused by: java.io.IOException: 你的主机中的软件中止了一个已建立的连接。
	at sun.nio.ch.SocketDispatcher.write0(Native Method)
	at sun.nio.ch.SocketDispatcher.write(SocketDispatcher.java:51)
	at sun.nio.ch.IOUtil.writeFromNativeBuffer(IOUtil.java:93)
	at sun.nio.ch.IOUtil.write(IOUtil.java:65)
	at sun.nio.ch.SocketChannelImpl.write(SocketChannelImpl.java:471)
	at org.apache.tomcat.util.net.NioChannel.write(NioChannel.java:135)
	at org.apache.tomcat.util.net.NioEndpoint$NioSocketWrapper.doWrite(NioEndpoint.java:1424)
	at org.apache.tomcat.util.net.SocketWrapperBase.doWrite(SocketWrapperBase.java:768)
	at org.apache.tomcat.util.net.SocketWrapperBase.writeBlocking(SocketWrapperBase.java:593)
	at org.apache.tomcat.util.net.SocketWrapperBase.write(SocketWrapperBase.java:537)
	at org.apache.coyote.http11.Http11OutputBuffer$SocketOutputBuffer.doWrite(Http11OutputBuffer.java:547)
	at org.apache.coyote.http11.filters.ChunkedOutputFilter.doWrite(ChunkedOutputFilter.java:110)
	at org.apache.coyote.http11.Http11OutputBuffer.doWrite(Http11OutputBuffer.java:194)
	at org.apache.coyote.Response.doWrite(Response.java:615)
	at org.apache.catalina.connector.OutputBuffer.realWriteBytes(OutputBuffer.java:340)
	... 162 common frames omitted
2025-05-29 17:25:53.651 [http-nio-8092-exec-1] WARN  org.springframework.web.servlet.mvc.method.annotation.ExceptionHandlerExceptionResolver.doResolveHandlerMethodException Line:414 - Failure in @ExceptionHandler com.simbest.boot.base.exception.RestControllerExceptionHandler#handleErrorException(HttpServletRequest, Exception)
org.apache.catalina.connector.ClientAbortException: java.io.IOException: 你的主机中的软件中止了一个已建立的连接。
	at org.apache.catalina.connector.OutputBuffer.realWriteBytes(OutputBuffer.java:353)
	at org.apache.catalina.connector.OutputBuffer.flushByteBuffer(OutputBuffer.java:784)
	at org.apache.catalina.connector.OutputBuffer.append(OutputBuffer.java:689)
	at org.apache.catalina.connector.OutputBuffer.writeBytes(OutputBuffer.java:388)
	at org.apache.catalina.connector.OutputBuffer.write(OutputBuffer.java:366)
	at org.apache.catalina.connector.CoyoteOutputStream.write(CoyoteOutputStream.java:96)
	at org.springframework.session.web.http.OnCommittedResponseWrapper$SaveContextServletOutputStream.write(OnCommittedResponseWrapper.java:624)
	at org.springframework.security.web.util.OnCommittedResponseWrapper$SaveContextServletOutputStream.write(OnCommittedResponseWrapper.java:645)
	at org.springframework.security.web.util.OnCommittedResponseWrapper$SaveContextServletOutputStream.write(OnCommittedResponseWrapper.java:645)
	at net.bull.javamelody.internal.web.FilterServletOutputStream.write(FilterServletOutputStream.java:88)
	at net.bull.javamelody.internal.web.CounterResponseStream.write(CounterResponseStream.java:81)
	at com.fasterxml.jackson.core.json.UTF8JsonGenerator._flushBuffer(UTF8JsonGenerator.java:2136)
	at com.fasterxml.jackson.core.json.UTF8JsonGenerator.flush(UTF8JsonGenerator.java:1150)
	at com.fasterxml.jackson.databind.ObjectWriter.writeValue(ObjectWriter.java:923)
	at org.springframework.http.converter.json.AbstractJackson2HttpMessageConverter.writeInternal(AbstractJackson2HttpMessageConverter.java:287)
	at org.springframework.http.converter.AbstractGenericHttpMessageConverter.write(AbstractGenericHttpMessageConverter.java:104)
	at org.springframework.web.servlet.mvc.method.annotation.AbstractMessageConverterMethodProcessor.writeWithMessageConverters(AbstractMessageConverterMethodProcessor.java:295)
	at org.springframework.web.servlet.mvc.method.annotation.RequestResponseBodyMethodProcessor.handleReturnValue(RequestResponseBodyMethodProcessor.java:181)
	at org.springframework.web.method.support.HandlerMethodReturnValueHandlerComposite.handleReturnValue(HandlerMethodReturnValueHandlerComposite.java:82)
	at org.springframework.web.servlet.mvc.method.annotation.ServletInvocableHandlerMethod.invokeAndHandle(ServletInvocableHandlerMethod.java:124)
	at org.springframework.web.servlet.mvc.method.annotation.ExceptionHandlerExceptionResolver.doResolveHandlerMethodException(ExceptionHandlerExceptionResolver.java:403)
	at org.springframework.web.servlet.handler.AbstractHandlerMethodExceptionResolver.doResolveException(AbstractHandlerMethodExceptionResolver.java:61)
	at org.springframework.web.servlet.handler.AbstractHandlerExceptionResolver.resolveException(AbstractHandlerExceptionResolver.java:141)
	at org.springframework.web.servlet.handler.HandlerExceptionResolverComposite.resolveException(HandlerExceptionResolverComposite.java:80)
	at org.springframework.web.servlet.DispatcherServlet.processHandlerException(DispatcherServlet.java:1300)
	at org.springframework.web.servlet.DispatcherServlet.processDispatchResult(DispatcherServlet.java:1111)
	at org.springframework.web.servlet.DispatcherServlet.doDispatch(DispatcherServlet.java:1057)
	at org.springframework.web.servlet.DispatcherServlet.doService(DispatcherServlet.java:943)
	at org.springframework.web.servlet.FrameworkServlet.processRequest(FrameworkServlet.java:1006)
	at org.springframework.web.servlet.FrameworkServlet.doPost(FrameworkServlet.java:909)
	at javax.servlet.http.HttpServlet.service(HttpServlet.java:681)
	at org.springframework.web.servlet.FrameworkServlet.service(FrameworkServlet.java:883)
	at javax.servlet.http.HttpServlet.service(HttpServlet.java:764)
	at org.apache.catalina.core.ApplicationFilterChain.internalDoFilter(ApplicationFilterChain.java:227)
	at org.apache.catalina.core.ApplicationFilterChain.doFilter(ApplicationFilterChain.java:162)
	at org.apache.tomcat.websocket.server.WsFilter.doFilter(WsFilter.java:53)
	at org.apache.catalina.core.ApplicationFilterChain.internalDoFilter(ApplicationFilterChain.java:189)
	at org.apache.catalina.core.ApplicationFilterChain.doFilter(ApplicationFilterChain.java:162)
	at com.simbest.boot.security.auth.filter.CaptchaAuthenticationFilter.doFilter(CaptchaAuthenticationFilter.java:46)
	at org.apache.catalina.core.ApplicationFilterChain.internalDoFilter(ApplicationFilterChain.java:189)
	at org.apache.catalina.core.ApplicationFilterChain.doFilter(ApplicationFilterChain.java:162)
	at com.simbest.boot.security.auth.filter.CaptchaAuthenticationFilter.doFilter(CaptchaAuthenticationFilter.java:46)
	at org.apache.catalina.core.ApplicationFilterChain.internalDoFilter(ApplicationFilterChain.java:189)
	at org.apache.catalina.core.ApplicationFilterChain.doFilter(ApplicationFilterChain.java:162)
	at org.springframework.security.web.authentication.AbstractAuthenticationProcessingFilter.doFilter(AbstractAuthenticationProcessingFilter.java:200)
	at org.apache.catalina.core.ApplicationFilterChain.internalDoFilter(ApplicationFilterChain.java:189)
	at org.apache.catalina.core.ApplicationFilterChain.doFilter(ApplicationFilterChain.java:162)
	at org.springframework.security.web.authentication.logout.LogoutFilter.doFilter(LogoutFilter.java:116)
	at org.apache.catalina.core.ApplicationFilterChain.internalDoFilter(ApplicationFilterChain.java:189)
	at org.apache.catalina.core.ApplicationFilterChain.doFilter(ApplicationFilterChain.java:162)
	at org.springframework.security.web.authentication.AbstractAuthenticationProcessingFilter.doFilter(AbstractAuthenticationProcessingFilter.java:200)
	at org.apache.catalina.core.ApplicationFilterChain.internalDoFilter(ApplicationFilterChain.java:189)
	at org.apache.catalina.core.ApplicationFilterChain.doFilter(ApplicationFilterChain.java:162)
	at org.springframework.security.web.authentication.AbstractAuthenticationProcessingFilter.doFilter(AbstractAuthenticationProcessingFilter.java:200)
	at org.apache.catalina.core.ApplicationFilterChain.internalDoFilter(ApplicationFilterChain.java:189)
	at org.apache.catalina.core.ApplicationFilterChain.doFilter(ApplicationFilterChain.java:162)
	at org.springframework.security.web.authentication.AbstractAuthenticationProcessingFilter.doFilter(AbstractAuthenticationProcessingFilter.java:200)
	at org.apache.catalina.core.ApplicationFilterChain.internalDoFilter(ApplicationFilterChain.java:189)
	at org.apache.catalina.core.ApplicationFilterChain.doFilter(ApplicationFilterChain.java:162)
	at org.springframework.security.web.authentication.AbstractAuthenticationProcessingFilter.doFilter(AbstractAuthenticationProcessingFilter.java:200)
	at org.apache.catalina.core.ApplicationFilterChain.internalDoFilter(ApplicationFilterChain.java:189)
	at org.apache.catalina.core.ApplicationFilterChain.doFilter(ApplicationFilterChain.java:162)
	at org.springframework.security.web.authentication.AbstractAuthenticationProcessingFilter.doFilter(AbstractAuthenticationProcessingFilter.java:200)
	at org.apache.catalina.core.ApplicationFilterChain.internalDoFilter(ApplicationFilterChain.java:189)
	at org.apache.catalina.core.ApplicationFilterChain.doFilter(ApplicationFilterChain.java:162)
	at com.simbest.boot.base.web.filter.CsrfProtectFilter.doFilter(CsrfProtectFilter.java:164)
	at org.apache.catalina.core.ApplicationFilterChain.internalDoFilter(ApplicationFilterChain.java:189)
	at org.apache.catalina.core.ApplicationFilterChain.doFilter(ApplicationFilterChain.java:162)
	at org.springframework.web.servlet.resource.ResourceUrlEncodingFilter.doFilter(ResourceUrlEncodingFilter.java:64)
	at org.apache.catalina.core.ApplicationFilterChain.internalDoFilter(ApplicationFilterChain.java:189)
	at org.apache.catalina.core.ApplicationFilterChain.doFilter(ApplicationFilterChain.java:162)
	at net.bull.javamelody.MonitoringFilter.doFilter(MonitoringFilter.java:199)
	at org.apache.catalina.core.ApplicationFilterChain.internalDoFilter(ApplicationFilterChain.java:189)
	at org.apache.catalina.core.ApplicationFilterChain.doFilter(ApplicationFilterChain.java:162)
	at net.bull.javamelody.MonitoringFilter.doFilter(MonitoringFilter.java:239)
	at net.bull.javamelody.MonitoringFilter.doFilter(MonitoringFilter.java:215)
	at org.apache.catalina.core.ApplicationFilterChain.internalDoFilter(ApplicationFilterChain.java:189)
	at org.apache.catalina.core.ApplicationFilterChain.doFilter(ApplicationFilterChain.java:162)
	at org.springframework.security.web.FilterChainProxy$VirtualFilterChain.doFilter(FilterChainProxy.java:320)
	at org.springframework.security.web.access.intercept.FilterSecurityInterceptor.invoke(FilterSecurityInterceptor.java:126)
	at org.springframework.security.web.access.intercept.FilterSecurityInterceptor.doFilter(FilterSecurityInterceptor.java:90)
	at org.springframework.security.web.FilterChainProxy$VirtualFilterChain.doFilter(FilterChainProxy.java:334)
	at org.springframework.security.web.access.ExceptionTranslationFilter.doFilter(ExceptionTranslationFilter.java:118)
	at org.springframework.security.web.FilterChainProxy$VirtualFilterChain.doFilter(FilterChainProxy.java:334)
	at org.springframework.security.web.session.SessionManagementFilter.doFilter(SessionManagementFilter.java:137)
	at org.springframework.security.web.FilterChainProxy$VirtualFilterChain.doFilter(FilterChainProxy.java:334)
	at org.springframework.security.web.authentication.AnonymousAuthenticationFilter.doFilter(AnonymousAuthenticationFilter.java:111)
	at org.springframework.security.web.FilterChainProxy$VirtualFilterChain.doFilter(FilterChainProxy.java:334)
	at org.springframework.security.web.servletapi.SecurityContextHolderAwareRequestFilter.doFilter(SecurityContextHolderAwareRequestFilter.java:158)
	at org.springframework.security.web.FilterChainProxy$VirtualFilterChain.doFilter(FilterChainProxy.java:334)
	at org.springframework.security.web.savedrequest.RequestCacheAwareFilter.doFilter(RequestCacheAwareFilter.java:63)
	at org.springframework.security.web.FilterChainProxy$VirtualFilterChain.doFilter(FilterChainProxy.java:334)
	at org.springframework.security.web.session.ConcurrentSessionFilter.doFilter(ConcurrentSessionFilter.java:152)
	at org.springframework.security.web.FilterChainProxy$VirtualFilterChain.doFilter(FilterChainProxy.java:334)
	at org.springframework.security.web.authentication.AbstractAuthenticationProcessingFilter.doFilter(AbstractAuthenticationProcessingFilter.java:200)
	at org.springframework.security.web.FilterChainProxy$VirtualFilterChain.doFilter(FilterChainProxy.java:334)
	at org.springframework.security.web.authentication.AbstractAuthenticationProcessingFilter.doFilter(AbstractAuthenticationProcessingFilter.java:200)
	at org.springframework.security.web.FilterChainProxy$VirtualFilterChain.doFilter(FilterChainProxy.java:334)
	at org.springframework.security.web.authentication.AbstractAuthenticationProcessingFilter.doFilter(AbstractAuthenticationProcessingFilter.java:200)
	at org.springframework.security.web.FilterChainProxy$VirtualFilterChain.doFilter(FilterChainProxy.java:334)
	at org.springframework.security.web.authentication.AbstractAuthenticationProcessingFilter.doFilter(AbstractAuthenticationProcessingFilter.java:200)
	at org.springframework.security.web.FilterChainProxy$VirtualFilterChain.doFilter(FilterChainProxy.java:334)
	at org.springframework.security.web.authentication.AbstractAuthenticationProcessingFilter.doFilter(AbstractAuthenticationProcessingFilter.java:200)
	at org.springframework.security.web.FilterChainProxy$VirtualFilterChain.doFilter(FilterChainProxy.java:334)
	at org.springframework.security.web.authentication.AbstractAuthenticationProcessingFilter.doFilter(AbstractAuthenticationProcessingFilter.java:200)
	at org.springframework.security.web.FilterChainProxy$VirtualFilterChain.doFilter(FilterChainProxy.java:334)
	at org.springframework.security.web.authentication.AbstractAuthenticationProcessingFilter.doFilter(AbstractAuthenticationProcessingFilter.java:200)
	at org.springframework.security.web.FilterChainProxy$VirtualFilterChain.doFilter(FilterChainProxy.java:334)
	at com.simbest.boot.security.auth.filter.CaptchaAuthenticationFilter.doFilter(CaptchaAuthenticationFilter.java:46)
	at org.springframework.security.web.FilterChainProxy$VirtualFilterChain.doFilter(FilterChainProxy.java:334)
	at com.simbest.boot.security.auth.filter.CaptchaAuthenticationFilter.doFilter(CaptchaAuthenticationFilter.java:46)
	at org.springframework.security.web.FilterChainProxy$VirtualFilterChain.doFilter(FilterChainProxy.java:334)
	at org.springframework.security.web.authentication.logout.LogoutFilter.doFilter(LogoutFilter.java:116)
	at org.springframework.security.web.FilterChainProxy$VirtualFilterChain.doFilter(FilterChainProxy.java:334)
	at org.springframework.web.filter.CorsFilter.doFilterInternal(CorsFilter.java:92)
	at org.springframework.web.filter.OncePerRequestFilter.doFilter(OncePerRequestFilter.java:119)
	at org.springframework.security.web.FilterChainProxy$VirtualFilterChain.doFilter(FilterChainProxy.java:334)
	at org.springframework.security.web.header.HeaderWriterFilter.doHeadersAfter(HeaderWriterFilter.java:92)
	at org.springframework.security.web.header.HeaderWriterFilter.doFilterInternal(HeaderWriterFilter.java:77)
	at org.springframework.web.filter.OncePerRequestFilter.doFilter(OncePerRequestFilter.java:119)
	at org.springframework.security.web.FilterChainProxy$VirtualFilterChain.doFilter(FilterChainProxy.java:334)
	at com.simbest.boot.security.auth.filter.CloudHeaderTokenAuthenticationFilter.doFilterInternal(CloudHeaderTokenAuthenticationFilter.java:171)
	at org.springframework.web.filter.OncePerRequestFilter.doFilter(OncePerRequestFilter.java:119)
	at org.springframework.security.web.FilterChainProxy$VirtualFilterChain.doFilter(FilterChainProxy.java:334)
	at org.springframework.security.web.context.SecurityContextPersistenceFilter.doFilter(SecurityContextPersistenceFilter.java:105)
	at org.springframework.security.web.FilterChainProxy$VirtualFilterChain.doFilter(FilterChainProxy.java:334)
	at org.springframework.security.web.context.request.async.WebAsyncManagerIntegrationFilter.doFilterInternal(WebAsyncManagerIntegrationFilter.java:56)
	at org.springframework.web.filter.OncePerRequestFilter.doFilter(OncePerRequestFilter.java:119)
	at org.springframework.security.web.FilterChainProxy$VirtualFilterChain.doFilter(FilterChainProxy.java:334)
	at org.springframework.security.web.FilterChainProxy.doFilterInternal(FilterChainProxy.java:215)
	at org.springframework.security.web.FilterChainProxy.doFilter(FilterChainProxy.java:178)
	at org.springframework.web.filter.DelegatingFilterProxy.invokeDelegate(DelegatingFilterProxy.java:358)
	at org.springframework.web.filter.DelegatingFilterProxy.doFilter(DelegatingFilterProxy.java:271)
	at org.apache.catalina.core.ApplicationFilterChain.internalDoFilter(ApplicationFilterChain.java:189)
	at org.apache.catalina.core.ApplicationFilterChain.doFilter(ApplicationFilterChain.java:162)
	at org.springframework.session.web.http.SessionRepositoryFilter.doFilterInternal(SessionRepositoryFilter.java:141)
	at org.springframework.session.web.http.OncePerRequestFilter.doFilter(OncePerRequestFilter.java:82)
	at org.apache.catalina.core.ApplicationFilterChain.internalDoFilter(ApplicationFilterChain.java:189)
	at org.apache.catalina.core.ApplicationFilterChain.doFilter(ApplicationFilterChain.java:162)
	at org.springframework.boot.actuate.metrics.web.servlet.WebMvcMetricsFilter.doFilterInternal(WebMvcMetricsFilter.java:108)
	at org.springframework.web.filter.OncePerRequestFilter.doFilter(OncePerRequestFilter.java:119)
	at org.apache.catalina.core.ApplicationFilterChain.internalDoFilter(ApplicationFilterChain.java:189)
	at org.apache.catalina.core.ApplicationFilterChain.doFilter(ApplicationFilterChain.java:162)
	at org.springframework.web.filter.CharacterEncodingFilter.doFilterInternal(CharacterEncodingFilter.java:201)
	at org.springframework.web.filter.OncePerRequestFilter.doFilter(OncePerRequestFilter.java:119)
	at org.apache.catalina.core.ApplicationFilterChain.internalDoFilter(ApplicationFilterChain.java:189)
	at org.apache.catalina.core.ApplicationFilterChain.doFilter(ApplicationFilterChain.java:162)
	at org.apache.catalina.core.StandardWrapperValve.invoke(StandardWrapperValve.java:197)
	at org.apache.catalina.core.StandardContextValve.invoke(StandardContextValve.java:97)
	at org.apache.catalina.authenticator.AuthenticatorBase.invoke(AuthenticatorBase.java:541)
	at org.apache.catalina.core.StandardHostValve.invoke(StandardHostValve.java:135)
	at org.apache.catalina.valves.ErrorReportValve.invoke(ErrorReportValve.java:92)
	at org.apache.catalina.core.StandardEngineValve.invoke(StandardEngineValve.java:78)
	at org.apache.catalina.connector.CoyoteAdapter.service(CoyoteAdapter.java:360)
	at org.apache.coyote.http11.Http11Processor.service(Http11Processor.java:399)
	at org.apache.coyote.AbstractProcessorLight.process(AbstractProcessorLight.java:65)
	at org.apache.coyote.AbstractProtocol$ConnectionHandler.process(AbstractProtocol.java:890)
	at org.apache.tomcat.util.net.NioEndpoint$SocketProcessor.doRun(NioEndpoint.java:1789)
	at org.apache.tomcat.util.net.SocketProcessorBase.run(SocketProcessorBase.java:49)
	at org.apache.tomcat.util.threads.ThreadPoolExecutor.runWorker(ThreadPoolExecutor.java:1191)
	at org.apache.tomcat.util.threads.ThreadPoolExecutor$Worker.run(ThreadPoolExecutor.java:659)
	at org.apache.tomcat.util.threads.TaskThread$WrappingRunnable.run(TaskThread.java:61)
	at java.lang.Thread.run(Thread.java:748)
Caused by: java.io.IOException: 你的主机中的软件中止了一个已建立的连接。
	at sun.nio.ch.SocketDispatcher.write0(Native Method)
	at sun.nio.ch.SocketDispatcher.write(SocketDispatcher.java:51)
	at sun.nio.ch.IOUtil.writeFromNativeBuffer(IOUtil.java:93)
	at sun.nio.ch.IOUtil.write(IOUtil.java:65)
	at sun.nio.ch.SocketChannelImpl.write(SocketChannelImpl.java:471)
	at org.apache.tomcat.util.net.NioChannel.write(NioChannel.java:135)
	at org.apache.tomcat.util.net.NioEndpoint$NioSocketWrapper.doWrite(NioEndpoint.java:1424)
	at org.apache.tomcat.util.net.SocketWrapperBase.doWrite(SocketWrapperBase.java:768)
	at org.apache.tomcat.util.net.SocketWrapperBase.writeBlocking(SocketWrapperBase.java:593)
	at org.apache.tomcat.util.net.SocketWrapperBase.write(SocketWrapperBase.java:537)
	at org.apache.coyote.http11.Http11OutputBuffer$SocketOutputBuffer.doWrite(Http11OutputBuffer.java:547)
	at org.apache.coyote.http11.filters.ChunkedOutputFilter.doWrite(ChunkedOutputFilter.java:110)
	at org.apache.coyote.http11.Http11OutputBuffer.doWrite(Http11OutputBuffer.java:194)
	at org.apache.coyote.Response.doWrite(Response.java:615)
	at org.apache.catalina.connector.OutputBuffer.realWriteBytes(OutputBuffer.java:340)
	... 162 common frames omitted
2025-05-29 17:25:53.780 [http-nio-8092-exec-11] WARN  org.springframework.web.servlet.mvc.method.annotation.ExceptionHandlerExceptionResolver.doResolveHandlerMethodException Line:414 - Failure in @ExceptionHandler com.simbest.boot.base.exception.RestControllerExceptionHandler#handleErrorException(HttpServletRequest, Exception)
org.apache.catalina.connector.ClientAbortException: java.io.IOException: 你的主机中的软件中止了一个已建立的连接。
	at org.apache.catalina.connector.OutputBuffer.realWriteBytes(OutputBuffer.java:353)
	at org.apache.catalina.connector.OutputBuffer.flushByteBuffer(OutputBuffer.java:784)
	at org.apache.catalina.connector.OutputBuffer.append(OutputBuffer.java:689)
	at org.apache.catalina.connector.OutputBuffer.writeBytes(OutputBuffer.java:388)
	at org.apache.catalina.connector.OutputBuffer.write(OutputBuffer.java:366)
	at org.apache.catalina.connector.CoyoteOutputStream.write(CoyoteOutputStream.java:96)
	at org.springframework.session.web.http.OnCommittedResponseWrapper$SaveContextServletOutputStream.write(OnCommittedResponseWrapper.java:624)
	at org.springframework.security.web.util.OnCommittedResponseWrapper$SaveContextServletOutputStream.write(OnCommittedResponseWrapper.java:645)
	at org.springframework.security.web.util.OnCommittedResponseWrapper$SaveContextServletOutputStream.write(OnCommittedResponseWrapper.java:645)
	at net.bull.javamelody.internal.web.FilterServletOutputStream.write(FilterServletOutputStream.java:88)
	at net.bull.javamelody.internal.web.CounterResponseStream.write(CounterResponseStream.java:81)
	at com.fasterxml.jackson.core.json.UTF8JsonGenerator._flushBuffer(UTF8JsonGenerator.java:2136)
	at com.fasterxml.jackson.core.json.UTF8JsonGenerator.flush(UTF8JsonGenerator.java:1150)
	at com.fasterxml.jackson.databind.ObjectWriter.writeValue(ObjectWriter.java:923)
	at org.springframework.http.converter.json.AbstractJackson2HttpMessageConverter.writeInternal(AbstractJackson2HttpMessageConverter.java:287)
	at org.springframework.http.converter.AbstractGenericHttpMessageConverter.write(AbstractGenericHttpMessageConverter.java:104)
	at org.springframework.web.servlet.mvc.method.annotation.AbstractMessageConverterMethodProcessor.writeWithMessageConverters(AbstractMessageConverterMethodProcessor.java:295)
	at org.springframework.web.servlet.mvc.method.annotation.RequestResponseBodyMethodProcessor.handleReturnValue(RequestResponseBodyMethodProcessor.java:181)
	at org.springframework.web.method.support.HandlerMethodReturnValueHandlerComposite.handleReturnValue(HandlerMethodReturnValueHandlerComposite.java:82)
	at org.springframework.web.servlet.mvc.method.annotation.ServletInvocableHandlerMethod.invokeAndHandle(ServletInvocableHandlerMethod.java:124)
	at org.springframework.web.servlet.mvc.method.annotation.ExceptionHandlerExceptionResolver.doResolveHandlerMethodException(ExceptionHandlerExceptionResolver.java:403)
	at org.springframework.web.servlet.handler.AbstractHandlerMethodExceptionResolver.doResolveException(AbstractHandlerMethodExceptionResolver.java:61)
	at org.springframework.web.servlet.handler.AbstractHandlerExceptionResolver.resolveException(AbstractHandlerExceptionResolver.java:141)
	at org.springframework.web.servlet.handler.HandlerExceptionResolverComposite.resolveException(HandlerExceptionResolverComposite.java:80)
	at org.springframework.web.servlet.DispatcherServlet.processHandlerException(DispatcherServlet.java:1300)
	at org.springframework.web.servlet.DispatcherServlet.processDispatchResult(DispatcherServlet.java:1111)
	at org.springframework.web.servlet.DispatcherServlet.doDispatch(DispatcherServlet.java:1057)
	at org.springframework.web.servlet.DispatcherServlet.doService(DispatcherServlet.java:943)
	at org.springframework.web.servlet.FrameworkServlet.processRequest(FrameworkServlet.java:1006)
	at org.springframework.web.servlet.FrameworkServlet.doPost(FrameworkServlet.java:909)
	at javax.servlet.http.HttpServlet.service(HttpServlet.java:681)
	at org.springframework.web.servlet.FrameworkServlet.service(FrameworkServlet.java:883)
	at javax.servlet.http.HttpServlet.service(HttpServlet.java:764)
	at org.apache.catalina.core.ApplicationFilterChain.internalDoFilter(ApplicationFilterChain.java:227)
	at org.apache.catalina.core.ApplicationFilterChain.doFilter(ApplicationFilterChain.java:162)
	at org.apache.tomcat.websocket.server.WsFilter.doFilter(WsFilter.java:53)
	at org.apache.catalina.core.ApplicationFilterChain.internalDoFilter(ApplicationFilterChain.java:189)
	at org.apache.catalina.core.ApplicationFilterChain.doFilter(ApplicationFilterChain.java:162)
	at com.simbest.boot.security.auth.filter.CaptchaAuthenticationFilter.doFilter(CaptchaAuthenticationFilter.java:46)
	at org.apache.catalina.core.ApplicationFilterChain.internalDoFilter(ApplicationFilterChain.java:189)
	at org.apache.catalina.core.ApplicationFilterChain.doFilter(ApplicationFilterChain.java:162)
	at com.simbest.boot.security.auth.filter.CaptchaAuthenticationFilter.doFilter(CaptchaAuthenticationFilter.java:46)
	at org.apache.catalina.core.ApplicationFilterChain.internalDoFilter(ApplicationFilterChain.java:189)
	at org.apache.catalina.core.ApplicationFilterChain.doFilter(ApplicationFilterChain.java:162)
	at org.springframework.security.web.authentication.AbstractAuthenticationProcessingFilter.doFilter(AbstractAuthenticationProcessingFilter.java:200)
	at org.apache.catalina.core.ApplicationFilterChain.internalDoFilter(ApplicationFilterChain.java:189)
	at org.apache.catalina.core.ApplicationFilterChain.doFilter(ApplicationFilterChain.java:162)
	at org.springframework.security.web.authentication.logout.LogoutFilter.doFilter(LogoutFilter.java:116)
	at org.apache.catalina.core.ApplicationFilterChain.internalDoFilter(ApplicationFilterChain.java:189)
	at org.apache.catalina.core.ApplicationFilterChain.doFilter(ApplicationFilterChain.java:162)
	at org.springframework.security.web.authentication.AbstractAuthenticationProcessingFilter.doFilter(AbstractAuthenticationProcessingFilter.java:200)
	at org.apache.catalina.core.ApplicationFilterChain.internalDoFilter(ApplicationFilterChain.java:189)
	at org.apache.catalina.core.ApplicationFilterChain.doFilter(ApplicationFilterChain.java:162)
	at org.springframework.security.web.authentication.AbstractAuthenticationProcessingFilter.doFilter(AbstractAuthenticationProcessingFilter.java:200)
	at org.apache.catalina.core.ApplicationFilterChain.internalDoFilter(ApplicationFilterChain.java:189)
	at org.apache.catalina.core.ApplicationFilterChain.doFilter(ApplicationFilterChain.java:162)
	at org.springframework.security.web.authentication.AbstractAuthenticationProcessingFilter.doFilter(AbstractAuthenticationProcessingFilter.java:200)
	at org.apache.catalina.core.ApplicationFilterChain.internalDoFilter(ApplicationFilterChain.java:189)
	at org.apache.catalina.core.ApplicationFilterChain.doFilter(ApplicationFilterChain.java:162)
	at org.springframework.security.web.authentication.AbstractAuthenticationProcessingFilter.doFilter(AbstractAuthenticationProcessingFilter.java:200)
	at org.apache.catalina.core.ApplicationFilterChain.internalDoFilter(ApplicationFilterChain.java:189)
	at org.apache.catalina.core.ApplicationFilterChain.doFilter(ApplicationFilterChain.java:162)
	at org.springframework.security.web.authentication.AbstractAuthenticationProcessingFilter.doFilter(AbstractAuthenticationProcessingFilter.java:200)
	at org.apache.catalina.core.ApplicationFilterChain.internalDoFilter(ApplicationFilterChain.java:189)
	at org.apache.catalina.core.ApplicationFilterChain.doFilter(ApplicationFilterChain.java:162)
	at com.simbest.boot.base.web.filter.CsrfProtectFilter.doFilter(CsrfProtectFilter.java:164)
	at org.apache.catalina.core.ApplicationFilterChain.internalDoFilter(ApplicationFilterChain.java:189)
	at org.apache.catalina.core.ApplicationFilterChain.doFilter(ApplicationFilterChain.java:162)
	at org.springframework.web.servlet.resource.ResourceUrlEncodingFilter.doFilter(ResourceUrlEncodingFilter.java:64)
	at org.apache.catalina.core.ApplicationFilterChain.internalDoFilter(ApplicationFilterChain.java:189)
	at org.apache.catalina.core.ApplicationFilterChain.doFilter(ApplicationFilterChain.java:162)
	at net.bull.javamelody.MonitoringFilter.doFilter(MonitoringFilter.java:199)
	at org.apache.catalina.core.ApplicationFilterChain.internalDoFilter(ApplicationFilterChain.java:189)
	at org.apache.catalina.core.ApplicationFilterChain.doFilter(ApplicationFilterChain.java:162)
	at net.bull.javamelody.MonitoringFilter.doFilter(MonitoringFilter.java:239)
	at net.bull.javamelody.MonitoringFilter.doFilter(MonitoringFilter.java:215)
	at org.apache.catalina.core.ApplicationFilterChain.internalDoFilter(ApplicationFilterChain.java:189)
	at org.apache.catalina.core.ApplicationFilterChain.doFilter(ApplicationFilterChain.java:162)
	at org.springframework.security.web.FilterChainProxy$VirtualFilterChain.doFilter(FilterChainProxy.java:320)
	at org.springframework.security.web.access.intercept.FilterSecurityInterceptor.invoke(FilterSecurityInterceptor.java:126)
	at org.springframework.security.web.access.intercept.FilterSecurityInterceptor.doFilter(FilterSecurityInterceptor.java:90)
	at org.springframework.security.web.FilterChainProxy$VirtualFilterChain.doFilter(FilterChainProxy.java:334)
	at org.springframework.security.web.access.ExceptionTranslationFilter.doFilter(ExceptionTranslationFilter.java:118)
	at org.springframework.security.web.FilterChainProxy$VirtualFilterChain.doFilter(FilterChainProxy.java:334)
	at org.springframework.security.web.session.SessionManagementFilter.doFilter(SessionManagementFilter.java:137)
	at org.springframework.security.web.FilterChainProxy$VirtualFilterChain.doFilter(FilterChainProxy.java:334)
	at org.springframework.security.web.authentication.AnonymousAuthenticationFilter.doFilter(AnonymousAuthenticationFilter.java:111)
	at org.springframework.security.web.FilterChainProxy$VirtualFilterChain.doFilter(FilterChainProxy.java:334)
	at org.springframework.security.web.servletapi.SecurityContextHolderAwareRequestFilter.doFilter(SecurityContextHolderAwareRequestFilter.java:158)
	at org.springframework.security.web.FilterChainProxy$VirtualFilterChain.doFilter(FilterChainProxy.java:334)
	at org.springframework.security.web.savedrequest.RequestCacheAwareFilter.doFilter(RequestCacheAwareFilter.java:63)
	at org.springframework.security.web.FilterChainProxy$VirtualFilterChain.doFilter(FilterChainProxy.java:334)
	at org.springframework.security.web.session.ConcurrentSessionFilter.doFilter(ConcurrentSessionFilter.java:152)
	at org.springframework.security.web.FilterChainProxy$VirtualFilterChain.doFilter(FilterChainProxy.java:334)
	at org.springframework.security.web.authentication.AbstractAuthenticationProcessingFilter.doFilter(AbstractAuthenticationProcessingFilter.java:200)
	at org.springframework.security.web.FilterChainProxy$VirtualFilterChain.doFilter(FilterChainProxy.java:334)
	at org.springframework.security.web.authentication.AbstractAuthenticationProcessingFilter.doFilter(AbstractAuthenticationProcessingFilter.java:200)
	at org.springframework.security.web.FilterChainProxy$VirtualFilterChain.doFilter(FilterChainProxy.java:334)
	at org.springframework.security.web.authentication.AbstractAuthenticationProcessingFilter.doFilter(AbstractAuthenticationProcessingFilter.java:200)
	at org.springframework.security.web.FilterChainProxy$VirtualFilterChain.doFilter(FilterChainProxy.java:334)
	at org.springframework.security.web.authentication.AbstractAuthenticationProcessingFilter.doFilter(AbstractAuthenticationProcessingFilter.java:200)
	at org.springframework.security.web.FilterChainProxy$VirtualFilterChain.doFilter(FilterChainProxy.java:334)
	at org.springframework.security.web.authentication.AbstractAuthenticationProcessingFilter.doFilter(AbstractAuthenticationProcessingFilter.java:200)
	at org.springframework.security.web.FilterChainProxy$VirtualFilterChain.doFilter(FilterChainProxy.java:334)
	at org.springframework.security.web.authentication.AbstractAuthenticationProcessingFilter.doFilter(AbstractAuthenticationProcessingFilter.java:200)
	at org.springframework.security.web.FilterChainProxy$VirtualFilterChain.doFilter(FilterChainProxy.java:334)
	at org.springframework.security.web.authentication.AbstractAuthenticationProcessingFilter.doFilter(AbstractAuthenticationProcessingFilter.java:200)
	at org.springframework.security.web.FilterChainProxy$VirtualFilterChain.doFilter(FilterChainProxy.java:334)
	at com.simbest.boot.security.auth.filter.CaptchaAuthenticationFilter.doFilter(CaptchaAuthenticationFilter.java:46)
	at org.springframework.security.web.FilterChainProxy$VirtualFilterChain.doFilter(FilterChainProxy.java:334)
	at com.simbest.boot.security.auth.filter.CaptchaAuthenticationFilter.doFilter(CaptchaAuthenticationFilter.java:46)
	at org.springframework.security.web.FilterChainProxy$VirtualFilterChain.doFilter(FilterChainProxy.java:334)
	at org.springframework.security.web.authentication.logout.LogoutFilter.doFilter(LogoutFilter.java:116)
	at org.springframework.security.web.FilterChainProxy$VirtualFilterChain.doFilter(FilterChainProxy.java:334)
	at org.springframework.web.filter.CorsFilter.doFilterInternal(CorsFilter.java:92)
	at org.springframework.web.filter.OncePerRequestFilter.doFilter(OncePerRequestFilter.java:119)
	at org.springframework.security.web.FilterChainProxy$VirtualFilterChain.doFilter(FilterChainProxy.java:334)
	at org.springframework.security.web.header.HeaderWriterFilter.doHeadersAfter(HeaderWriterFilter.java:92)
	at org.springframework.security.web.header.HeaderWriterFilter.doFilterInternal(HeaderWriterFilter.java:77)
	at org.springframework.web.filter.OncePerRequestFilter.doFilter(OncePerRequestFilter.java:119)
	at org.springframework.security.web.FilterChainProxy$VirtualFilterChain.doFilter(FilterChainProxy.java:334)
	at com.simbest.boot.security.auth.filter.CloudHeaderTokenAuthenticationFilter.doFilterInternal(CloudHeaderTokenAuthenticationFilter.java:171)
	at org.springframework.web.filter.OncePerRequestFilter.doFilter(OncePerRequestFilter.java:119)
	at org.springframework.security.web.FilterChainProxy$VirtualFilterChain.doFilter(FilterChainProxy.java:334)
	at org.springframework.security.web.context.SecurityContextPersistenceFilter.doFilter(SecurityContextPersistenceFilter.java:105)
	at org.springframework.security.web.FilterChainProxy$VirtualFilterChain.doFilter(FilterChainProxy.java:334)
	at org.springframework.security.web.context.request.async.WebAsyncManagerIntegrationFilter.doFilterInternal(WebAsyncManagerIntegrationFilter.java:56)
	at org.springframework.web.filter.OncePerRequestFilter.doFilter(OncePerRequestFilter.java:119)
	at org.springframework.security.web.FilterChainProxy$VirtualFilterChain.doFilter(FilterChainProxy.java:334)
	at org.springframework.security.web.FilterChainProxy.doFilterInternal(FilterChainProxy.java:215)
	at org.springframework.security.web.FilterChainProxy.doFilter(FilterChainProxy.java:178)
	at org.springframework.web.filter.DelegatingFilterProxy.invokeDelegate(DelegatingFilterProxy.java:358)
	at org.springframework.web.filter.DelegatingFilterProxy.doFilter(DelegatingFilterProxy.java:271)
	at org.apache.catalina.core.ApplicationFilterChain.internalDoFilter(ApplicationFilterChain.java:189)
	at org.apache.catalina.core.ApplicationFilterChain.doFilter(ApplicationFilterChain.java:162)
	at org.springframework.session.web.http.SessionRepositoryFilter.doFilterInternal(SessionRepositoryFilter.java:141)
	at org.springframework.session.web.http.OncePerRequestFilter.doFilter(OncePerRequestFilter.java:82)
	at org.apache.catalina.core.ApplicationFilterChain.internalDoFilter(ApplicationFilterChain.java:189)
	at org.apache.catalina.core.ApplicationFilterChain.doFilter(ApplicationFilterChain.java:162)
	at org.springframework.boot.actuate.metrics.web.servlet.WebMvcMetricsFilter.doFilterInternal(WebMvcMetricsFilter.java:108)
	at org.springframework.web.filter.OncePerRequestFilter.doFilter(OncePerRequestFilter.java:119)
	at org.apache.catalina.core.ApplicationFilterChain.internalDoFilter(ApplicationFilterChain.java:189)
	at org.apache.catalina.core.ApplicationFilterChain.doFilter(ApplicationFilterChain.java:162)
	at org.springframework.web.filter.CharacterEncodingFilter.doFilterInternal(CharacterEncodingFilter.java:201)
	at org.springframework.web.filter.OncePerRequestFilter.doFilter(OncePerRequestFilter.java:119)
	at org.apache.catalina.core.ApplicationFilterChain.internalDoFilter(ApplicationFilterChain.java:189)
	at org.apache.catalina.core.ApplicationFilterChain.doFilter(ApplicationFilterChain.java:162)
	at org.apache.catalina.core.StandardWrapperValve.invoke(StandardWrapperValve.java:197)
	at org.apache.catalina.core.StandardContextValve.invoke(StandardContextValve.java:97)
	at org.apache.catalina.authenticator.AuthenticatorBase.invoke(AuthenticatorBase.java:541)
	at org.apache.catalina.core.StandardHostValve.invoke(StandardHostValve.java:135)
	at org.apache.catalina.valves.ErrorReportValve.invoke(ErrorReportValve.java:92)
	at org.apache.catalina.core.StandardEngineValve.invoke(StandardEngineValve.java:78)
	at org.apache.catalina.connector.CoyoteAdapter.service(CoyoteAdapter.java:360)
	at org.apache.coyote.http11.Http11Processor.service(Http11Processor.java:399)
	at org.apache.coyote.AbstractProcessorLight.process(AbstractProcessorLight.java:65)
	at org.apache.coyote.AbstractProtocol$ConnectionHandler.process(AbstractProtocol.java:890)
	at org.apache.tomcat.util.net.NioEndpoint$SocketProcessor.doRun(NioEndpoint.java:1789)
	at org.apache.tomcat.util.net.SocketProcessorBase.run(SocketProcessorBase.java:49)
	at org.apache.tomcat.util.threads.ThreadPoolExecutor.runWorker(ThreadPoolExecutor.java:1191)
	at org.apache.tomcat.util.threads.ThreadPoolExecutor$Worker.run(ThreadPoolExecutor.java:659)
	at org.apache.tomcat.util.threads.TaskThread$WrappingRunnable.run(TaskThread.java:61)
	at java.lang.Thread.run(Thread.java:748)
Caused by: java.io.IOException: 你的主机中的软件中止了一个已建立的连接。
	at sun.nio.ch.SocketDispatcher.write0(Native Method)
	at sun.nio.ch.SocketDispatcher.write(SocketDispatcher.java:51)
	at sun.nio.ch.IOUtil.writeFromNativeBuffer(IOUtil.java:93)
	at sun.nio.ch.IOUtil.write(IOUtil.java:65)
	at sun.nio.ch.SocketChannelImpl.write(SocketChannelImpl.java:471)
	at org.apache.tomcat.util.net.NioChannel.write(NioChannel.java:135)
	at org.apache.tomcat.util.net.NioEndpoint$NioSocketWrapper.doWrite(NioEndpoint.java:1424)
	at org.apache.tomcat.util.net.SocketWrapperBase.doWrite(SocketWrapperBase.java:768)
	at org.apache.tomcat.util.net.SocketWrapperBase.writeBlocking(SocketWrapperBase.java:593)
	at org.apache.tomcat.util.net.SocketWrapperBase.write(SocketWrapperBase.java:537)
	at org.apache.coyote.http11.Http11OutputBuffer$SocketOutputBuffer.doWrite(Http11OutputBuffer.java:547)
	at org.apache.coyote.http11.filters.ChunkedOutputFilter.doWrite(ChunkedOutputFilter.java:110)
	at org.apache.coyote.http11.Http11OutputBuffer.doWrite(Http11OutputBuffer.java:194)
	at org.apache.coyote.Response.doWrite(Response.java:615)
	at org.apache.catalina.connector.OutputBuffer.realWriteBytes(OutputBuffer.java:340)
	... 162 common frames omitted
2025-05-29 18:06:29.875 [Thread-5] WARN  com.alibaba.nacos.common.http.HttpClientBeanHolder.shutdown Line:108 - [HttpClientBeanHolder] Start destroying common HttpClient
2025-05-29 18:06:29.876 [Thread-11] WARN  com.alibaba.nacos.common.notify.NotifyCenter.shutdown Line:136 - [NotifyCenter] Start destroying Publisher
2025-05-29 18:06:29.877 [Thread-11] WARN  com.alibaba.nacos.common.notify.NotifyCenter.shutdown Line:153 - [NotifyCenter] Destruction of the end
2025-05-29 18:06:29.880 [Thread-5] WARN  com.alibaba.nacos.common.http.HttpClientBeanHolder.shutdown Line:114 - [HttpClientBeanHolder] Destruction of the end
2025-05-29 18:06:30.014 [SpringContextShutdownHook] WARN  com.simbest.boot.component.GracefulShutdown.onApplicationEvent Line:161 - executor非ThreadPoolExecutor，具体类型为【org.apache.tomcat.util.threads.ThreadPoolExecutor】
2025-05-29 18:40:20.507 [restartedMain] WARN  com.alibaba.cloud.nacos.client.NacosPropertySourceBuilder.loadNacosData Line:87 - Ignore the empty nacos configuration and get it based on dataId[hnjjwz-custom-obuat.properties] & group[DEFAULT_GROUP]
2025-05-29 18:40:20.518 [restartedMain] WARN  com.alibaba.cloud.nacos.client.NacosPropertySourceBuilder.loadNacosData Line:87 - Ignore the empty nacos configuration and get it based on dataId[hnjjwz] & group[DEFAULT_GROUP]
2025-05-29 18:40:20.529 [restartedMain] WARN  com.alibaba.cloud.nacos.client.NacosPropertySourceBuilder.loadNacosData Line:87 - Ignore the empty nacos configuration and get it based on dataId[hnjjwz.properties] & group[DEFAULT_GROUP]
2025-05-29 18:40:23.391 [restartedMain] WARN  org.springframework.boot.actuate.endpoint.EndpointId.logWarning Line:155 - Endpoint ID 'service-registry' contains invalid characters, please migrate to a valid format.
2025-05-29 18:40:24.315 [restartedMain] WARN  com.alibaba.druid.pool.DruidDataSource.initCheck Line:1314 - removeAbandoned is true, not use in production.
2025-05-29 18:41:24.105 [restartedMain] WARN  com.simbest.boot.SimbestApplication.onApplicationEvent Line:60 - 加载环境信息为: 【obuat】
2025-05-29 18:41:24.105 [restartedMain] WARN  com.simbest.boot.SimbestApplication.onApplicationEvent Line:61 - Application started successfully, lets go and have fun......
2025-05-29 18:41:24.106 [restartedMain] WARN  com.simbest.boot.SimbestApplication.onApplicationEvent Line:71 - 
---------------------------------------------------------
	应用已成功启动，运行地址如下：:
	Local:		http://localhost:8092/hnjjwz
	External:	http://***********:8092/hnjjwz
Aplication started successfully, lets go and have fun......
---------------------------------------------------------

2025-05-29 19:27:50.564 [http-nio-8092-exec-11] WARN  com.simbest.boot.security.auth.service.impl.AuthUserCacheServiceImpl.loadCacheUser Line:118 - 无法通过关键字【auth:user:chenhong】读取用户主键ID
2025-05-29 19:27:50.860 [http-nio-8092-exec-11] WARN  com.simbest.boot.security.auth.service.impl.AuthUserCacheServiceImpl.saveOrUpdateCacheUser Line:64 - 即将在缓存中将用户权限置为空----SET AuthPermissions EMPTY----【12704】
2025-05-29 20:16:33.445 [http-nio-8092-exec-14] WARN  org.springframework.web.servlet.mvc.method.annotation.ExceptionHandlerExceptionResolver.doResolveHandlerMethodException Line:414 - Failure in @ExceptionHandler com.simbest.boot.base.exception.RestControllerExceptionHandler#handleErrorException(HttpServletRequest, Exception)
org.apache.catalina.connector.ClientAbortException: java.io.IOException: 你的主机中的软件中止了一个已建立的连接。
	at org.apache.catalina.connector.OutputBuffer.realWriteBytes(OutputBuffer.java:353)
	at org.apache.catalina.connector.OutputBuffer.flushByteBuffer(OutputBuffer.java:784)
	at org.apache.catalina.connector.OutputBuffer.append(OutputBuffer.java:689)
	at org.apache.catalina.connector.OutputBuffer.writeBytes(OutputBuffer.java:388)
	at org.apache.catalina.connector.OutputBuffer.write(OutputBuffer.java:366)
	at org.apache.catalina.connector.CoyoteOutputStream.write(CoyoteOutputStream.java:96)
	at org.springframework.session.web.http.OnCommittedResponseWrapper$SaveContextServletOutputStream.write(OnCommittedResponseWrapper.java:624)
	at org.springframework.security.web.util.OnCommittedResponseWrapper$SaveContextServletOutputStream.write(OnCommittedResponseWrapper.java:645)
	at org.springframework.security.web.util.OnCommittedResponseWrapper$SaveContextServletOutputStream.write(OnCommittedResponseWrapper.java:645)
	at net.bull.javamelody.internal.web.FilterServletOutputStream.write(FilterServletOutputStream.java:88)
	at net.bull.javamelody.internal.web.CounterResponseStream.write(CounterResponseStream.java:81)
	at com.fasterxml.jackson.core.json.UTF8JsonGenerator._flushBuffer(UTF8JsonGenerator.java:2136)
	at com.fasterxml.jackson.core.json.UTF8JsonGenerator.flush(UTF8JsonGenerator.java:1150)
	at com.fasterxml.jackson.databind.ObjectWriter.writeValue(ObjectWriter.java:923)
	at org.springframework.http.converter.json.AbstractJackson2HttpMessageConverter.writeInternal(AbstractJackson2HttpMessageConverter.java:287)
	at org.springframework.http.converter.AbstractGenericHttpMessageConverter.write(AbstractGenericHttpMessageConverter.java:104)
	at org.springframework.web.servlet.mvc.method.annotation.AbstractMessageConverterMethodProcessor.writeWithMessageConverters(AbstractMessageConverterMethodProcessor.java:295)
	at org.springframework.web.servlet.mvc.method.annotation.RequestResponseBodyMethodProcessor.handleReturnValue(RequestResponseBodyMethodProcessor.java:181)
	at org.springframework.web.method.support.HandlerMethodReturnValueHandlerComposite.handleReturnValue(HandlerMethodReturnValueHandlerComposite.java:82)
	at org.springframework.web.servlet.mvc.method.annotation.ServletInvocableHandlerMethod.invokeAndHandle(ServletInvocableHandlerMethod.java:124)
	at org.springframework.web.servlet.mvc.method.annotation.ExceptionHandlerExceptionResolver.doResolveHandlerMethodException(ExceptionHandlerExceptionResolver.java:403)
	at org.springframework.web.servlet.handler.AbstractHandlerMethodExceptionResolver.doResolveException(AbstractHandlerMethodExceptionResolver.java:61)
	at org.springframework.web.servlet.handler.AbstractHandlerExceptionResolver.resolveException(AbstractHandlerExceptionResolver.java:141)
	at org.springframework.web.servlet.handler.HandlerExceptionResolverComposite.resolveException(HandlerExceptionResolverComposite.java:80)
	at org.springframework.web.servlet.DispatcherServlet.processHandlerException(DispatcherServlet.java:1300)
	at org.springframework.web.servlet.DispatcherServlet.processDispatchResult(DispatcherServlet.java:1111)
	at org.springframework.web.servlet.DispatcherServlet.doDispatch(DispatcherServlet.java:1057)
	at org.springframework.web.servlet.DispatcherServlet.doService(DispatcherServlet.java:943)
	at org.springframework.web.servlet.FrameworkServlet.processRequest(FrameworkServlet.java:1006)
	at org.springframework.web.servlet.FrameworkServlet.doPost(FrameworkServlet.java:909)
	at javax.servlet.http.HttpServlet.service(HttpServlet.java:681)
	at org.springframework.web.servlet.FrameworkServlet.service(FrameworkServlet.java:883)
	at javax.servlet.http.HttpServlet.service(HttpServlet.java:764)
	at org.apache.catalina.core.ApplicationFilterChain.internalDoFilter(ApplicationFilterChain.java:227)
	at org.apache.catalina.core.ApplicationFilterChain.doFilter(ApplicationFilterChain.java:162)
	at org.apache.tomcat.websocket.server.WsFilter.doFilter(WsFilter.java:53)
	at org.apache.catalina.core.ApplicationFilterChain.internalDoFilter(ApplicationFilterChain.java:189)
	at org.apache.catalina.core.ApplicationFilterChain.doFilter(ApplicationFilterChain.java:162)
	at com.simbest.boot.security.auth.filter.CaptchaAuthenticationFilter.doFilter(CaptchaAuthenticationFilter.java:46)
	at org.apache.catalina.core.ApplicationFilterChain.internalDoFilter(ApplicationFilterChain.java:189)
	at org.apache.catalina.core.ApplicationFilterChain.doFilter(ApplicationFilterChain.java:162)
	at com.simbest.boot.security.auth.filter.CaptchaAuthenticationFilter.doFilter(CaptchaAuthenticationFilter.java:46)
	at org.apache.catalina.core.ApplicationFilterChain.internalDoFilter(ApplicationFilterChain.java:189)
	at org.apache.catalina.core.ApplicationFilterChain.doFilter(ApplicationFilterChain.java:162)
	at org.springframework.security.web.authentication.AbstractAuthenticationProcessingFilter.doFilter(AbstractAuthenticationProcessingFilter.java:200)
	at org.apache.catalina.core.ApplicationFilterChain.internalDoFilter(ApplicationFilterChain.java:189)
	at org.apache.catalina.core.ApplicationFilterChain.doFilter(ApplicationFilterChain.java:162)
	at org.springframework.security.web.authentication.logout.LogoutFilter.doFilter(LogoutFilter.java:116)
	at org.apache.catalina.core.ApplicationFilterChain.internalDoFilter(ApplicationFilterChain.java:189)
	at org.apache.catalina.core.ApplicationFilterChain.doFilter(ApplicationFilterChain.java:162)
	at org.springframework.security.web.authentication.AbstractAuthenticationProcessingFilter.doFilter(AbstractAuthenticationProcessingFilter.java:200)
	at org.apache.catalina.core.ApplicationFilterChain.internalDoFilter(ApplicationFilterChain.java:189)
	at org.apache.catalina.core.ApplicationFilterChain.doFilter(ApplicationFilterChain.java:162)
	at org.springframework.security.web.authentication.AbstractAuthenticationProcessingFilter.doFilter(AbstractAuthenticationProcessingFilter.java:200)
	at org.apache.catalina.core.ApplicationFilterChain.internalDoFilter(ApplicationFilterChain.java:189)
	at org.apache.catalina.core.ApplicationFilterChain.doFilter(ApplicationFilterChain.java:162)
	at org.springframework.security.web.authentication.AbstractAuthenticationProcessingFilter.doFilter(AbstractAuthenticationProcessingFilter.java:200)
	at org.apache.catalina.core.ApplicationFilterChain.internalDoFilter(ApplicationFilterChain.java:189)
	at org.apache.catalina.core.ApplicationFilterChain.doFilter(ApplicationFilterChain.java:162)
	at org.springframework.security.web.authentication.AbstractAuthenticationProcessingFilter.doFilter(AbstractAuthenticationProcessingFilter.java:200)
	at org.apache.catalina.core.ApplicationFilterChain.internalDoFilter(ApplicationFilterChain.java:189)
	at org.apache.catalina.core.ApplicationFilterChain.doFilter(ApplicationFilterChain.java:162)
	at org.springframework.security.web.authentication.AbstractAuthenticationProcessingFilter.doFilter(AbstractAuthenticationProcessingFilter.java:200)
	at org.apache.catalina.core.ApplicationFilterChain.internalDoFilter(ApplicationFilterChain.java:189)
	at org.apache.catalina.core.ApplicationFilterChain.doFilter(ApplicationFilterChain.java:162)
	at com.simbest.boot.base.web.filter.CsrfProtectFilter.doFilter(CsrfProtectFilter.java:164)
	at org.apache.catalina.core.ApplicationFilterChain.internalDoFilter(ApplicationFilterChain.java:189)
	at org.apache.catalina.core.ApplicationFilterChain.doFilter(ApplicationFilterChain.java:162)
	at org.springframework.web.servlet.resource.ResourceUrlEncodingFilter.doFilter(ResourceUrlEncodingFilter.java:64)
	at org.apache.catalina.core.ApplicationFilterChain.internalDoFilter(ApplicationFilterChain.java:189)
	at org.apache.catalina.core.ApplicationFilterChain.doFilter(ApplicationFilterChain.java:162)
	at net.bull.javamelody.MonitoringFilter.doFilter(MonitoringFilter.java:199)
	at org.apache.catalina.core.ApplicationFilterChain.internalDoFilter(ApplicationFilterChain.java:189)
	at org.apache.catalina.core.ApplicationFilterChain.doFilter(ApplicationFilterChain.java:162)
	at net.bull.javamelody.MonitoringFilter.doFilter(MonitoringFilter.java:239)
	at net.bull.javamelody.MonitoringFilter.doFilter(MonitoringFilter.java:215)
	at org.apache.catalina.core.ApplicationFilterChain.internalDoFilter(ApplicationFilterChain.java:189)
	at org.apache.catalina.core.ApplicationFilterChain.doFilter(ApplicationFilterChain.java:162)
	at org.springframework.security.web.FilterChainProxy$VirtualFilterChain.doFilter(FilterChainProxy.java:320)
	at org.springframework.security.web.access.intercept.FilterSecurityInterceptor.invoke(FilterSecurityInterceptor.java:126)
	at org.springframework.security.web.access.intercept.FilterSecurityInterceptor.doFilter(FilterSecurityInterceptor.java:90)
	at org.springframework.security.web.FilterChainProxy$VirtualFilterChain.doFilter(FilterChainProxy.java:334)
	at org.springframework.security.web.access.ExceptionTranslationFilter.doFilter(ExceptionTranslationFilter.java:118)
	at org.springframework.security.web.FilterChainProxy$VirtualFilterChain.doFilter(FilterChainProxy.java:334)
	at org.springframework.security.web.session.SessionManagementFilter.doFilter(SessionManagementFilter.java:137)
	at org.springframework.security.web.FilterChainProxy$VirtualFilterChain.doFilter(FilterChainProxy.java:334)
	at org.springframework.security.web.authentication.AnonymousAuthenticationFilter.doFilter(AnonymousAuthenticationFilter.java:111)
	at org.springframework.security.web.FilterChainProxy$VirtualFilterChain.doFilter(FilterChainProxy.java:334)
	at org.springframework.security.web.servletapi.SecurityContextHolderAwareRequestFilter.doFilter(SecurityContextHolderAwareRequestFilter.java:158)
	at org.springframework.security.web.FilterChainProxy$VirtualFilterChain.doFilter(FilterChainProxy.java:334)
	at org.springframework.security.web.savedrequest.RequestCacheAwareFilter.doFilter(RequestCacheAwareFilter.java:63)
	at org.springframework.security.web.FilterChainProxy$VirtualFilterChain.doFilter(FilterChainProxy.java:334)
	at org.springframework.security.web.session.ConcurrentSessionFilter.doFilter(ConcurrentSessionFilter.java:152)
	at org.springframework.security.web.FilterChainProxy$VirtualFilterChain.doFilter(FilterChainProxy.java:334)
	at org.springframework.security.web.authentication.AbstractAuthenticationProcessingFilter.doFilter(AbstractAuthenticationProcessingFilter.java:200)
	at org.springframework.security.web.FilterChainProxy$VirtualFilterChain.doFilter(FilterChainProxy.java:334)
	at org.springframework.security.web.authentication.AbstractAuthenticationProcessingFilter.doFilter(AbstractAuthenticationProcessingFilter.java:200)
	at org.springframework.security.web.FilterChainProxy$VirtualFilterChain.doFilter(FilterChainProxy.java:334)
	at org.springframework.security.web.authentication.AbstractAuthenticationProcessingFilter.doFilter(AbstractAuthenticationProcessingFilter.java:200)
	at org.springframework.security.web.FilterChainProxy$VirtualFilterChain.doFilter(FilterChainProxy.java:334)
	at org.springframework.security.web.authentication.AbstractAuthenticationProcessingFilter.doFilter(AbstractAuthenticationProcessingFilter.java:200)
	at org.springframework.security.web.FilterChainProxy$VirtualFilterChain.doFilter(FilterChainProxy.java:334)
	at org.springframework.security.web.authentication.AbstractAuthenticationProcessingFilter.doFilter(AbstractAuthenticationProcessingFilter.java:200)
	at org.springframework.security.web.FilterChainProxy$VirtualFilterChain.doFilter(FilterChainProxy.java:334)
	at org.springframework.security.web.authentication.AbstractAuthenticationProcessingFilter.doFilter(AbstractAuthenticationProcessingFilter.java:200)
	at org.springframework.security.web.FilterChainProxy$VirtualFilterChain.doFilter(FilterChainProxy.java:334)
	at org.springframework.security.web.authentication.AbstractAuthenticationProcessingFilter.doFilter(AbstractAuthenticationProcessingFilter.java:200)
	at org.springframework.security.web.FilterChainProxy$VirtualFilterChain.doFilter(FilterChainProxy.java:334)
	at com.simbest.boot.security.auth.filter.CaptchaAuthenticationFilter.doFilter(CaptchaAuthenticationFilter.java:46)
	at org.springframework.security.web.FilterChainProxy$VirtualFilterChain.doFilter(FilterChainProxy.java:334)
	at com.simbest.boot.security.auth.filter.CaptchaAuthenticationFilter.doFilter(CaptchaAuthenticationFilter.java:46)
	at org.springframework.security.web.FilterChainProxy$VirtualFilterChain.doFilter(FilterChainProxy.java:334)
	at org.springframework.security.web.authentication.logout.LogoutFilter.doFilter(LogoutFilter.java:116)
	at org.springframework.security.web.FilterChainProxy$VirtualFilterChain.doFilter(FilterChainProxy.java:334)
	at org.springframework.web.filter.CorsFilter.doFilterInternal(CorsFilter.java:92)
	at org.springframework.web.filter.OncePerRequestFilter.doFilter(OncePerRequestFilter.java:119)
	at org.springframework.security.web.FilterChainProxy$VirtualFilterChain.doFilter(FilterChainProxy.java:334)
	at org.springframework.security.web.header.HeaderWriterFilter.doHeadersAfter(HeaderWriterFilter.java:92)
	at org.springframework.security.web.header.HeaderWriterFilter.doFilterInternal(HeaderWriterFilter.java:77)
	at org.springframework.web.filter.OncePerRequestFilter.doFilter(OncePerRequestFilter.java:119)
	at org.springframework.security.web.FilterChainProxy$VirtualFilterChain.doFilter(FilterChainProxy.java:334)
	at com.simbest.boot.security.auth.filter.CloudHeaderTokenAuthenticationFilter.doFilterInternal(CloudHeaderTokenAuthenticationFilter.java:171)
	at org.springframework.web.filter.OncePerRequestFilter.doFilter(OncePerRequestFilter.java:119)
	at org.springframework.security.web.FilterChainProxy$VirtualFilterChain.doFilter(FilterChainProxy.java:334)
	at org.springframework.security.web.context.SecurityContextPersistenceFilter.doFilter(SecurityContextPersistenceFilter.java:105)
	at org.springframework.security.web.FilterChainProxy$VirtualFilterChain.doFilter(FilterChainProxy.java:334)
	at org.springframework.security.web.context.request.async.WebAsyncManagerIntegrationFilter.doFilterInternal(WebAsyncManagerIntegrationFilter.java:56)
	at org.springframework.web.filter.OncePerRequestFilter.doFilter(OncePerRequestFilter.java:119)
	at org.springframework.security.web.FilterChainProxy$VirtualFilterChain.doFilter(FilterChainProxy.java:334)
	at org.springframework.security.web.FilterChainProxy.doFilterInternal(FilterChainProxy.java:215)
	at org.springframework.security.web.FilterChainProxy.doFilter(FilterChainProxy.java:178)
	at org.springframework.web.filter.DelegatingFilterProxy.invokeDelegate(DelegatingFilterProxy.java:358)
	at org.springframework.web.filter.DelegatingFilterProxy.doFilter(DelegatingFilterProxy.java:271)
	at org.apache.catalina.core.ApplicationFilterChain.internalDoFilter(ApplicationFilterChain.java:189)
	at org.apache.catalina.core.ApplicationFilterChain.doFilter(ApplicationFilterChain.java:162)
	at org.springframework.session.web.http.SessionRepositoryFilter.doFilterInternal(SessionRepositoryFilter.java:141)
	at org.springframework.session.web.http.OncePerRequestFilter.doFilter(OncePerRequestFilter.java:82)
	at org.apache.catalina.core.ApplicationFilterChain.internalDoFilter(ApplicationFilterChain.java:189)
	at org.apache.catalina.core.ApplicationFilterChain.doFilter(ApplicationFilterChain.java:162)
	at org.springframework.boot.actuate.metrics.web.servlet.WebMvcMetricsFilter.doFilterInternal(WebMvcMetricsFilter.java:108)
	at org.springframework.web.filter.OncePerRequestFilter.doFilter(OncePerRequestFilter.java:119)
	at org.apache.catalina.core.ApplicationFilterChain.internalDoFilter(ApplicationFilterChain.java:189)
	at org.apache.catalina.core.ApplicationFilterChain.doFilter(ApplicationFilterChain.java:162)
	at org.springframework.web.filter.CharacterEncodingFilter.doFilterInternal(CharacterEncodingFilter.java:201)
	at org.springframework.web.filter.OncePerRequestFilter.doFilter(OncePerRequestFilter.java:119)
	at org.apache.catalina.core.ApplicationFilterChain.internalDoFilter(ApplicationFilterChain.java:189)
	at org.apache.catalina.core.ApplicationFilterChain.doFilter(ApplicationFilterChain.java:162)
	at org.apache.catalina.core.StandardWrapperValve.invoke(StandardWrapperValve.java:197)
	at org.apache.catalina.core.StandardContextValve.invoke(StandardContextValve.java:97)
	at org.apache.catalina.authenticator.AuthenticatorBase.invoke(AuthenticatorBase.java:541)
	at org.apache.catalina.core.StandardHostValve.invoke(StandardHostValve.java:135)
	at org.apache.catalina.valves.ErrorReportValve.invoke(ErrorReportValve.java:92)
	at org.apache.catalina.core.StandardEngineValve.invoke(StandardEngineValve.java:78)
	at org.apache.catalina.connector.CoyoteAdapter.service(CoyoteAdapter.java:360)
	at org.apache.coyote.http11.Http11Processor.service(Http11Processor.java:399)
	at org.apache.coyote.AbstractProcessorLight.process(AbstractProcessorLight.java:65)
	at org.apache.coyote.AbstractProtocol$ConnectionHandler.process(AbstractProtocol.java:890)
	at org.apache.tomcat.util.net.NioEndpoint$SocketProcessor.doRun(NioEndpoint.java:1789)
	at org.apache.tomcat.util.net.SocketProcessorBase.run(SocketProcessorBase.java:49)
	at org.apache.tomcat.util.threads.ThreadPoolExecutor.runWorker(ThreadPoolExecutor.java:1191)
	at org.apache.tomcat.util.threads.ThreadPoolExecutor$Worker.run(ThreadPoolExecutor.java:659)
	at org.apache.tomcat.util.threads.TaskThread$WrappingRunnable.run(TaskThread.java:61)
	at java.lang.Thread.run(Thread.java:748)
Caused by: java.io.IOException: 你的主机中的软件中止了一个已建立的连接。
	at sun.nio.ch.SocketDispatcher.write0(Native Method)
	at sun.nio.ch.SocketDispatcher.write(SocketDispatcher.java:51)
	at sun.nio.ch.IOUtil.writeFromNativeBuffer(IOUtil.java:93)
	at sun.nio.ch.IOUtil.write(IOUtil.java:65)
	at sun.nio.ch.SocketChannelImpl.write(SocketChannelImpl.java:471)
	at org.apache.tomcat.util.net.NioChannel.write(NioChannel.java:135)
	at org.apache.tomcat.util.net.NioEndpoint$NioSocketWrapper.doWrite(NioEndpoint.java:1424)
	at org.apache.tomcat.util.net.SocketWrapperBase.doWrite(SocketWrapperBase.java:768)
	at org.apache.tomcat.util.net.SocketWrapperBase.writeBlocking(SocketWrapperBase.java:593)
	at org.apache.tomcat.util.net.SocketWrapperBase.write(SocketWrapperBase.java:537)
	at org.apache.coyote.http11.Http11OutputBuffer$SocketOutputBuffer.doWrite(Http11OutputBuffer.java:547)
	at org.apache.coyote.http11.filters.ChunkedOutputFilter.doWrite(ChunkedOutputFilter.java:110)
	at org.apache.coyote.http11.Http11OutputBuffer.doWrite(Http11OutputBuffer.java:194)
	at org.apache.coyote.Response.doWrite(Response.java:615)
	at org.apache.catalina.connector.OutputBuffer.realWriteBytes(OutputBuffer.java:340)
	... 162 common frames omitted
2025-05-29 20:16:33.796 [http-nio-8092-exec-3] WARN  org.springframework.web.servlet.mvc.method.annotation.ExceptionHandlerExceptionResolver.doResolveHandlerMethodException Line:414 - Failure in @ExceptionHandler com.simbest.boot.base.exception.RestControllerExceptionHandler#handleErrorException(HttpServletRequest, Exception)
org.apache.catalina.connector.ClientAbortException: java.io.IOException: 你的主机中的软件中止了一个已建立的连接。
	at org.apache.catalina.connector.OutputBuffer.realWriteBytes(OutputBuffer.java:353)
	at org.apache.catalina.connector.OutputBuffer.flushByteBuffer(OutputBuffer.java:784)
	at org.apache.catalina.connector.OutputBuffer.append(OutputBuffer.java:689)
	at org.apache.catalina.connector.OutputBuffer.writeBytes(OutputBuffer.java:388)
	at org.apache.catalina.connector.OutputBuffer.write(OutputBuffer.java:366)
	at org.apache.catalina.connector.CoyoteOutputStream.write(CoyoteOutputStream.java:96)
	at org.springframework.session.web.http.OnCommittedResponseWrapper$SaveContextServletOutputStream.write(OnCommittedResponseWrapper.java:624)
	at org.springframework.security.web.util.OnCommittedResponseWrapper$SaveContextServletOutputStream.write(OnCommittedResponseWrapper.java:645)
	at org.springframework.security.web.util.OnCommittedResponseWrapper$SaveContextServletOutputStream.write(OnCommittedResponseWrapper.java:645)
	at net.bull.javamelody.internal.web.FilterServletOutputStream.write(FilterServletOutputStream.java:88)
	at net.bull.javamelody.internal.web.CounterResponseStream.write(CounterResponseStream.java:81)
	at com.fasterxml.jackson.core.json.UTF8JsonGenerator._flushBuffer(UTF8JsonGenerator.java:2136)
	at com.fasterxml.jackson.core.json.UTF8JsonGenerator.flush(UTF8JsonGenerator.java:1150)
	at com.fasterxml.jackson.databind.ObjectWriter.writeValue(ObjectWriter.java:923)
	at org.springframework.http.converter.json.AbstractJackson2HttpMessageConverter.writeInternal(AbstractJackson2HttpMessageConverter.java:287)
	at org.springframework.http.converter.AbstractGenericHttpMessageConverter.write(AbstractGenericHttpMessageConverter.java:104)
	at org.springframework.web.servlet.mvc.method.annotation.AbstractMessageConverterMethodProcessor.writeWithMessageConverters(AbstractMessageConverterMethodProcessor.java:295)
	at org.springframework.web.servlet.mvc.method.annotation.RequestResponseBodyMethodProcessor.handleReturnValue(RequestResponseBodyMethodProcessor.java:181)
	at org.springframework.web.method.support.HandlerMethodReturnValueHandlerComposite.handleReturnValue(HandlerMethodReturnValueHandlerComposite.java:82)
	at org.springframework.web.servlet.mvc.method.annotation.ServletInvocableHandlerMethod.invokeAndHandle(ServletInvocableHandlerMethod.java:124)
	at org.springframework.web.servlet.mvc.method.annotation.ExceptionHandlerExceptionResolver.doResolveHandlerMethodException(ExceptionHandlerExceptionResolver.java:403)
	at org.springframework.web.servlet.handler.AbstractHandlerMethodExceptionResolver.doResolveException(AbstractHandlerMethodExceptionResolver.java:61)
	at org.springframework.web.servlet.handler.AbstractHandlerExceptionResolver.resolveException(AbstractHandlerExceptionResolver.java:141)
	at org.springframework.web.servlet.handler.HandlerExceptionResolverComposite.resolveException(HandlerExceptionResolverComposite.java:80)
	at org.springframework.web.servlet.DispatcherServlet.processHandlerException(DispatcherServlet.java:1300)
	at org.springframework.web.servlet.DispatcherServlet.processDispatchResult(DispatcherServlet.java:1111)
	at org.springframework.web.servlet.DispatcherServlet.doDispatch(DispatcherServlet.java:1057)
	at org.springframework.web.servlet.DispatcherServlet.doService(DispatcherServlet.java:943)
	at org.springframework.web.servlet.FrameworkServlet.processRequest(FrameworkServlet.java:1006)
	at org.springframework.web.servlet.FrameworkServlet.doPost(FrameworkServlet.java:909)
	at javax.servlet.http.HttpServlet.service(HttpServlet.java:681)
	at org.springframework.web.servlet.FrameworkServlet.service(FrameworkServlet.java:883)
	at javax.servlet.http.HttpServlet.service(HttpServlet.java:764)
	at org.apache.catalina.core.ApplicationFilterChain.internalDoFilter(ApplicationFilterChain.java:227)
	at org.apache.catalina.core.ApplicationFilterChain.doFilter(ApplicationFilterChain.java:162)
	at org.apache.tomcat.websocket.server.WsFilter.doFilter(WsFilter.java:53)
	at org.apache.catalina.core.ApplicationFilterChain.internalDoFilter(ApplicationFilterChain.java:189)
	at org.apache.catalina.core.ApplicationFilterChain.doFilter(ApplicationFilterChain.java:162)
	at com.simbest.boot.security.auth.filter.CaptchaAuthenticationFilter.doFilter(CaptchaAuthenticationFilter.java:46)
	at org.apache.catalina.core.ApplicationFilterChain.internalDoFilter(ApplicationFilterChain.java:189)
	at org.apache.catalina.core.ApplicationFilterChain.doFilter(ApplicationFilterChain.java:162)
	at com.simbest.boot.security.auth.filter.CaptchaAuthenticationFilter.doFilter(CaptchaAuthenticationFilter.java:46)
	at org.apache.catalina.core.ApplicationFilterChain.internalDoFilter(ApplicationFilterChain.java:189)
	at org.apache.catalina.core.ApplicationFilterChain.doFilter(ApplicationFilterChain.java:162)
	at org.springframework.security.web.authentication.AbstractAuthenticationProcessingFilter.doFilter(AbstractAuthenticationProcessingFilter.java:200)
	at org.apache.catalina.core.ApplicationFilterChain.internalDoFilter(ApplicationFilterChain.java:189)
	at org.apache.catalina.core.ApplicationFilterChain.doFilter(ApplicationFilterChain.java:162)
	at org.springframework.security.web.authentication.logout.LogoutFilter.doFilter(LogoutFilter.java:116)
	at org.apache.catalina.core.ApplicationFilterChain.internalDoFilter(ApplicationFilterChain.java:189)
	at org.apache.catalina.core.ApplicationFilterChain.doFilter(ApplicationFilterChain.java:162)
	at org.springframework.security.web.authentication.AbstractAuthenticationProcessingFilter.doFilter(AbstractAuthenticationProcessingFilter.java:200)
	at org.apache.catalina.core.ApplicationFilterChain.internalDoFilter(ApplicationFilterChain.java:189)
	at org.apache.catalina.core.ApplicationFilterChain.doFilter(ApplicationFilterChain.java:162)
	at org.springframework.security.web.authentication.AbstractAuthenticationProcessingFilter.doFilter(AbstractAuthenticationProcessingFilter.java:200)
	at org.apache.catalina.core.ApplicationFilterChain.internalDoFilter(ApplicationFilterChain.java:189)
	at org.apache.catalina.core.ApplicationFilterChain.doFilter(ApplicationFilterChain.java:162)
	at org.springframework.security.web.authentication.AbstractAuthenticationProcessingFilter.doFilter(AbstractAuthenticationProcessingFilter.java:200)
	at org.apache.catalina.core.ApplicationFilterChain.internalDoFilter(ApplicationFilterChain.java:189)
	at org.apache.catalina.core.ApplicationFilterChain.doFilter(ApplicationFilterChain.java:162)
	at org.springframework.security.web.authentication.AbstractAuthenticationProcessingFilter.doFilter(AbstractAuthenticationProcessingFilter.java:200)
	at org.apache.catalina.core.ApplicationFilterChain.internalDoFilter(ApplicationFilterChain.java:189)
	at org.apache.catalina.core.ApplicationFilterChain.doFilter(ApplicationFilterChain.java:162)
	at org.springframework.security.web.authentication.AbstractAuthenticationProcessingFilter.doFilter(AbstractAuthenticationProcessingFilter.java:200)
	at org.apache.catalina.core.ApplicationFilterChain.internalDoFilter(ApplicationFilterChain.java:189)
	at org.apache.catalina.core.ApplicationFilterChain.doFilter(ApplicationFilterChain.java:162)
	at com.simbest.boot.base.web.filter.CsrfProtectFilter.doFilter(CsrfProtectFilter.java:164)
	at org.apache.catalina.core.ApplicationFilterChain.internalDoFilter(ApplicationFilterChain.java:189)
	at org.apache.catalina.core.ApplicationFilterChain.doFilter(ApplicationFilterChain.java:162)
	at org.springframework.web.servlet.resource.ResourceUrlEncodingFilter.doFilter(ResourceUrlEncodingFilter.java:64)
	at org.apache.catalina.core.ApplicationFilterChain.internalDoFilter(ApplicationFilterChain.java:189)
	at org.apache.catalina.core.ApplicationFilterChain.doFilter(ApplicationFilterChain.java:162)
	at net.bull.javamelody.MonitoringFilter.doFilter(MonitoringFilter.java:199)
	at org.apache.catalina.core.ApplicationFilterChain.internalDoFilter(ApplicationFilterChain.java:189)
	at org.apache.catalina.core.ApplicationFilterChain.doFilter(ApplicationFilterChain.java:162)
	at net.bull.javamelody.MonitoringFilter.doFilter(MonitoringFilter.java:239)
	at net.bull.javamelody.MonitoringFilter.doFilter(MonitoringFilter.java:215)
	at org.apache.catalina.core.ApplicationFilterChain.internalDoFilter(ApplicationFilterChain.java:189)
	at org.apache.catalina.core.ApplicationFilterChain.doFilter(ApplicationFilterChain.java:162)
	at org.springframework.security.web.FilterChainProxy$VirtualFilterChain.doFilter(FilterChainProxy.java:320)
	at org.springframework.security.web.access.intercept.FilterSecurityInterceptor.invoke(FilterSecurityInterceptor.java:126)
	at org.springframework.security.web.access.intercept.FilterSecurityInterceptor.doFilter(FilterSecurityInterceptor.java:90)
	at org.springframework.security.web.FilterChainProxy$VirtualFilterChain.doFilter(FilterChainProxy.java:334)
	at org.springframework.security.web.access.ExceptionTranslationFilter.doFilter(ExceptionTranslationFilter.java:118)
	at org.springframework.security.web.FilterChainProxy$VirtualFilterChain.doFilter(FilterChainProxy.java:334)
	at org.springframework.security.web.session.SessionManagementFilter.doFilter(SessionManagementFilter.java:137)
	at org.springframework.security.web.FilterChainProxy$VirtualFilterChain.doFilter(FilterChainProxy.java:334)
	at org.springframework.security.web.authentication.AnonymousAuthenticationFilter.doFilter(AnonymousAuthenticationFilter.java:111)
	at org.springframework.security.web.FilterChainProxy$VirtualFilterChain.doFilter(FilterChainProxy.java:334)
	at org.springframework.security.web.servletapi.SecurityContextHolderAwareRequestFilter.doFilter(SecurityContextHolderAwareRequestFilter.java:158)
	at org.springframework.security.web.FilterChainProxy$VirtualFilterChain.doFilter(FilterChainProxy.java:334)
	at org.springframework.security.web.savedrequest.RequestCacheAwareFilter.doFilter(RequestCacheAwareFilter.java:63)
	at org.springframework.security.web.FilterChainProxy$VirtualFilterChain.doFilter(FilterChainProxy.java:334)
	at org.springframework.security.web.session.ConcurrentSessionFilter.doFilter(ConcurrentSessionFilter.java:152)
	at org.springframework.security.web.FilterChainProxy$VirtualFilterChain.doFilter(FilterChainProxy.java:334)
	at org.springframework.security.web.authentication.AbstractAuthenticationProcessingFilter.doFilter(AbstractAuthenticationProcessingFilter.java:200)
	at org.springframework.security.web.FilterChainProxy$VirtualFilterChain.doFilter(FilterChainProxy.java:334)
	at org.springframework.security.web.authentication.AbstractAuthenticationProcessingFilter.doFilter(AbstractAuthenticationProcessingFilter.java:200)
	at org.springframework.security.web.FilterChainProxy$VirtualFilterChain.doFilter(FilterChainProxy.java:334)
	at org.springframework.security.web.authentication.AbstractAuthenticationProcessingFilter.doFilter(AbstractAuthenticationProcessingFilter.java:200)
	at org.springframework.security.web.FilterChainProxy$VirtualFilterChain.doFilter(FilterChainProxy.java:334)
	at org.springframework.security.web.authentication.AbstractAuthenticationProcessingFilter.doFilter(AbstractAuthenticationProcessingFilter.java:200)
	at org.springframework.security.web.FilterChainProxy$VirtualFilterChain.doFilter(FilterChainProxy.java:334)
	at org.springframework.security.web.authentication.AbstractAuthenticationProcessingFilter.doFilter(AbstractAuthenticationProcessingFilter.java:200)
	at org.springframework.security.web.FilterChainProxy$VirtualFilterChain.doFilter(FilterChainProxy.java:334)
	at org.springframework.security.web.authentication.AbstractAuthenticationProcessingFilter.doFilter(AbstractAuthenticationProcessingFilter.java:200)
	at org.springframework.security.web.FilterChainProxy$VirtualFilterChain.doFilter(FilterChainProxy.java:334)
	at org.springframework.security.web.authentication.AbstractAuthenticationProcessingFilter.doFilter(AbstractAuthenticationProcessingFilter.java:200)
	at org.springframework.security.web.FilterChainProxy$VirtualFilterChain.doFilter(FilterChainProxy.java:334)
	at com.simbest.boot.security.auth.filter.CaptchaAuthenticationFilter.doFilter(CaptchaAuthenticationFilter.java:46)
	at org.springframework.security.web.FilterChainProxy$VirtualFilterChain.doFilter(FilterChainProxy.java:334)
	at com.simbest.boot.security.auth.filter.CaptchaAuthenticationFilter.doFilter(CaptchaAuthenticationFilter.java:46)
	at org.springframework.security.web.FilterChainProxy$VirtualFilterChain.doFilter(FilterChainProxy.java:334)
	at org.springframework.security.web.authentication.logout.LogoutFilter.doFilter(LogoutFilter.java:116)
	at org.springframework.security.web.FilterChainProxy$VirtualFilterChain.doFilter(FilterChainProxy.java:334)
	at org.springframework.web.filter.CorsFilter.doFilterInternal(CorsFilter.java:92)
	at org.springframework.web.filter.OncePerRequestFilter.doFilter(OncePerRequestFilter.java:119)
	at org.springframework.security.web.FilterChainProxy$VirtualFilterChain.doFilter(FilterChainProxy.java:334)
	at org.springframework.security.web.header.HeaderWriterFilter.doHeadersAfter(HeaderWriterFilter.java:92)
	at org.springframework.security.web.header.HeaderWriterFilter.doFilterInternal(HeaderWriterFilter.java:77)
	at org.springframework.web.filter.OncePerRequestFilter.doFilter(OncePerRequestFilter.java:119)
	at org.springframework.security.web.FilterChainProxy$VirtualFilterChain.doFilter(FilterChainProxy.java:334)
	at com.simbest.boot.security.auth.filter.CloudHeaderTokenAuthenticationFilter.doFilterInternal(CloudHeaderTokenAuthenticationFilter.java:171)
	at org.springframework.web.filter.OncePerRequestFilter.doFilter(OncePerRequestFilter.java:119)
	at org.springframework.security.web.FilterChainProxy$VirtualFilterChain.doFilter(FilterChainProxy.java:334)
	at org.springframework.security.web.context.SecurityContextPersistenceFilter.doFilter(SecurityContextPersistenceFilter.java:105)
	at org.springframework.security.web.FilterChainProxy$VirtualFilterChain.doFilter(FilterChainProxy.java:334)
	at org.springframework.security.web.context.request.async.WebAsyncManagerIntegrationFilter.doFilterInternal(WebAsyncManagerIntegrationFilter.java:56)
	at org.springframework.web.filter.OncePerRequestFilter.doFilter(OncePerRequestFilter.java:119)
	at org.springframework.security.web.FilterChainProxy$VirtualFilterChain.doFilter(FilterChainProxy.java:334)
	at org.springframework.security.web.FilterChainProxy.doFilterInternal(FilterChainProxy.java:215)
	at org.springframework.security.web.FilterChainProxy.doFilter(FilterChainProxy.java:178)
	at org.springframework.web.filter.DelegatingFilterProxy.invokeDelegate(DelegatingFilterProxy.java:358)
	at org.springframework.web.filter.DelegatingFilterProxy.doFilter(DelegatingFilterProxy.java:271)
	at org.apache.catalina.core.ApplicationFilterChain.internalDoFilter(ApplicationFilterChain.java:189)
	at org.apache.catalina.core.ApplicationFilterChain.doFilter(ApplicationFilterChain.java:162)
	at org.springframework.session.web.http.SessionRepositoryFilter.doFilterInternal(SessionRepositoryFilter.java:141)
	at org.springframework.session.web.http.OncePerRequestFilter.doFilter(OncePerRequestFilter.java:82)
	at org.apache.catalina.core.ApplicationFilterChain.internalDoFilter(ApplicationFilterChain.java:189)
	at org.apache.catalina.core.ApplicationFilterChain.doFilter(ApplicationFilterChain.java:162)
	at org.springframework.boot.actuate.metrics.web.servlet.WebMvcMetricsFilter.doFilterInternal(WebMvcMetricsFilter.java:108)
	at org.springframework.web.filter.OncePerRequestFilter.doFilter(OncePerRequestFilter.java:119)
	at org.apache.catalina.core.ApplicationFilterChain.internalDoFilter(ApplicationFilterChain.java:189)
	at org.apache.catalina.core.ApplicationFilterChain.doFilter(ApplicationFilterChain.java:162)
	at org.springframework.web.filter.CharacterEncodingFilter.doFilterInternal(CharacterEncodingFilter.java:201)
	at org.springframework.web.filter.OncePerRequestFilter.doFilter(OncePerRequestFilter.java:119)
	at org.apache.catalina.core.ApplicationFilterChain.internalDoFilter(ApplicationFilterChain.java:189)
	at org.apache.catalina.core.ApplicationFilterChain.doFilter(ApplicationFilterChain.java:162)
	at org.apache.catalina.core.StandardWrapperValve.invoke(StandardWrapperValve.java:197)
	at org.apache.catalina.core.StandardContextValve.invoke(StandardContextValve.java:97)
	at org.apache.catalina.authenticator.AuthenticatorBase.invoke(AuthenticatorBase.java:541)
	at org.apache.catalina.core.StandardHostValve.invoke(StandardHostValve.java:135)
	at org.apache.catalina.valves.ErrorReportValve.invoke(ErrorReportValve.java:92)
	at org.apache.catalina.core.StandardEngineValve.invoke(StandardEngineValve.java:78)
	at org.apache.catalina.connector.CoyoteAdapter.service(CoyoteAdapter.java:360)
	at org.apache.coyote.http11.Http11Processor.service(Http11Processor.java:399)
	at org.apache.coyote.AbstractProcessorLight.process(AbstractProcessorLight.java:65)
	at org.apache.coyote.AbstractProtocol$ConnectionHandler.process(AbstractProtocol.java:890)
	at org.apache.tomcat.util.net.NioEndpoint$SocketProcessor.doRun(NioEndpoint.java:1789)
	at org.apache.tomcat.util.net.SocketProcessorBase.run(SocketProcessorBase.java:49)
	at org.apache.tomcat.util.threads.ThreadPoolExecutor.runWorker(ThreadPoolExecutor.java:1191)
	at org.apache.tomcat.util.threads.ThreadPoolExecutor$Worker.run(ThreadPoolExecutor.java:659)
	at org.apache.tomcat.util.threads.TaskThread$WrappingRunnable.run(TaskThread.java:61)
	at java.lang.Thread.run(Thread.java:748)
Caused by: java.io.IOException: 你的主机中的软件中止了一个已建立的连接。
	at sun.nio.ch.SocketDispatcher.write0(Native Method)
	at sun.nio.ch.SocketDispatcher.write(SocketDispatcher.java:51)
	at sun.nio.ch.IOUtil.writeFromNativeBuffer(IOUtil.java:93)
	at sun.nio.ch.IOUtil.write(IOUtil.java:65)
	at sun.nio.ch.SocketChannelImpl.write(SocketChannelImpl.java:471)
	at org.apache.tomcat.util.net.NioChannel.write(NioChannel.java:135)
	at org.apache.tomcat.util.net.NioEndpoint$NioSocketWrapper.doWrite(NioEndpoint.java:1424)
	at org.apache.tomcat.util.net.SocketWrapperBase.doWrite(SocketWrapperBase.java:768)
	at org.apache.tomcat.util.net.SocketWrapperBase.writeBlocking(SocketWrapperBase.java:593)
	at org.apache.tomcat.util.net.SocketWrapperBase.write(SocketWrapperBase.java:537)
	at org.apache.coyote.http11.Http11OutputBuffer$SocketOutputBuffer.doWrite(Http11OutputBuffer.java:547)
	at org.apache.coyote.http11.filters.ChunkedOutputFilter.doWrite(ChunkedOutputFilter.java:110)
	at org.apache.coyote.http11.Http11OutputBuffer.doWrite(Http11OutputBuffer.java:194)
	at org.apache.coyote.Response.doWrite(Response.java:615)
	at org.apache.catalina.connector.OutputBuffer.realWriteBytes(OutputBuffer.java:340)
	... 162 common frames omitted
2025-05-29 20:33:49.229 [http-nio-8092-exec-5] WARN  com.simbest.boot.util.AppFileUtil.getFileName Line:218 - 文件URL路径【组 2.png】无法通过URL获取路径，直接通过工具类提取文件全称为【组 2.png】
2025-05-29 20:33:49.230 [http-nio-8092-exec-5] WARN  com.simbest.boot.util.AppFileUtil.getFileSuffix Line:274 - 文件URL路径【组 2.png】无法通过URL获取路径，直接通过工具类提取文件后缀为【png】
2025-05-29 20:33:49.237 [http-nio-8092-exec-5] WARN  com.simbest.boot.util.AppFileUtil.getFileBaseName Line:251 - 文件URL路径【组 2.png】无法通过URL获取路径，直接通过工具类提取文件名称为【组 2】
2025-05-29 20:33:49.238 [http-nio-8092-exec-5] WARN  com.simbest.boot.util.AppFileUtil.getFileSuffix Line:274 - 文件URL路径【组 2.png】无法通过URL获取路径，直接通过工具类提取文件后缀为【png】
2025-05-29 20:33:49.254 [http-nio-8092-exec-5] WARN  com.simbest.boot.util.AppFileUtil.getFileSuffix Line:274 - 文件URL路径【组 2.png】无法通过URL获取路径，直接通过工具类提取文件后缀为【png】
2025-05-29 20:33:59.650 [http-nio-8092-exec-14] WARN  com.simbest.boot.bps.process.operate.WfProcessManager.startProcessAndSetRelativeData Line:87 - 流程启动调用BPS流程启动接口用时：5268ms
2025-05-29 20:34:00.203 [http-nio-8092-exec-14] WARN  com.simbest.boot.bps.process.operate.WfProcessManager.startProcessAndSetRelativeData Line:107 - 【BPS Driver】流程启动第一个工作项的状态：【10】
2025-05-29 20:34:00.203 [http-nio-8092-exec-14] WARN  com.simbest.boot.bps.process.operate.WfProcessManager.startProcessAndSetRelativeData Line:118 - 流程启动调用BPS流程工作项查询接口用时：540ms
2025-05-29 20:34:00.290 [http-nio-8092-exec-14] WARN  com.simbest.boot.bps.process.operate.WorkItemManager.finishWorkItemWithRelativeData Line:169 - 流程下一步传递的参数为：{"code":"E1445640438383599616","currentUserCode":"zhanglu16","inputUserId":"kongzhijie","appCode":"hnjjwz","title":"345345","receiptId":"U870403786424225792","outcome":"hnjjwz.depart_manager"}
2025-05-29 20:34:00.989 [http-nio-8092-exec-14] WARN  com.simbest.boot.bps.unifiedtodo.service.impl.CloseUnifiedTodoServiceImpl.closeTodo Line:68 - 统一待办核销状态：Y
2025-05-29 20:34:00.994 [http-nio-8092-exec-14] WARN  com.simbest.boot.bps.common.HttpNagentDataUtil.queryAgent Line:76 - 获取接口配置对象1：SimpleConfig(id=AC486600246105735169, address=http://************:8088/ntydl, interfaceStyle=ntydl, isOpen=true, isInner=true, provider=北京晟壁)
2025-05-29 20:34:00.995 [http-nio-8092-exec-14] WARN  com.simbest.boot.bps.common.HttpNagentDataUtil.queryAgent Line:80 - 获取接口配置对象2：SimpleConfig(id=AC486600246105735169, address=http://************:8088/ntydl, interfaceStyle=ntydl, isOpen=true, isInner=true, provider=北京晟壁)
2025-05-29 20:34:01.618 [http-nio-8092-exec-14] WARN  com.simbest.boot.bps.process.operate.WorkItemManager.finishWorkItemWithRelativeData Line:264 - 下一个审批人是：kongzhijie，代理人为：[]
2025-05-29 20:34:01.619 [http-nio-8092-exec-14] WARN  com.simbest.boot.bps.process.operate.WorkItemManager.finishWorkItemWithRelativeData Line:275 - 流程提交下一步处理代理人用时：1301ms
2025-05-29 20:34:07.705 [http-nio-8092-exec-14] WARN  com.simbest.boot.bps.process.operate.WorkItemManager.finishWorkItemWithRelativeData Line:293 - 流程提交下一步调用BPS流程引擎提交下一步接口用时：6085ms
2025-05-29 20:36:26.453 [http-nio-8092-exec-7] WARN  com.simbest.boot.util.AppFileUtil.getFileName Line:218 - 文件URL路径【组 2.png】无法通过URL获取路径，直接通过工具类提取文件全称为【组 2.png】
2025-05-29 20:36:26.454 [http-nio-8092-exec-7] WARN  com.simbest.boot.util.AppFileUtil.getFileSuffix Line:274 - 文件URL路径【组 2.png】无法通过URL获取路径，直接通过工具类提取文件后缀为【png】
2025-05-29 20:36:26.461 [http-nio-8092-exec-7] WARN  com.simbest.boot.util.AppFileUtil.getFileBaseName Line:251 - 文件URL路径【组 2.png】无法通过URL获取路径，直接通过工具类提取文件名称为【组 2】
2025-05-29 20:36:26.461 [http-nio-8092-exec-7] WARN  com.simbest.boot.util.AppFileUtil.getFileSuffix Line:274 - 文件URL路径【组 2.png】无法通过URL获取路径，直接通过工具类提取文件后缀为【png】
2025-05-29 20:36:26.473 [http-nio-8092-exec-7] WARN  com.simbest.boot.util.AppFileUtil.getFileSuffix Line:274 - 文件URL路径【组 2.png】无法通过URL获取路径，直接通过工具类提取文件后缀为【png】
2025-05-29 20:36:35.543 [http-nio-8092-exec-11] WARN  com.simbest.boot.bps.process.operate.WfProcessManager.startProcessAndSetRelativeData Line:87 - 流程启动调用BPS流程启动接口用时：3430ms
2025-05-29 20:36:35.742 [http-nio-8092-exec-11] WARN  com.simbest.boot.bps.process.operate.WfProcessManager.startProcessAndSetRelativeData Line:107 - 【BPS Driver】流程启动第一个工作项的状态：【10】
2025-05-29 20:36:35.742 [http-nio-8092-exec-11] WARN  com.simbest.boot.bps.process.operate.WfProcessManager.startProcessAndSetRelativeData Line:118 - 流程启动调用BPS流程工作项查询接口用时：197ms
2025-05-29 20:36:35.758 [http-nio-8092-exec-11] WARN  com.simbest.boot.bps.process.operate.WorkItemManager.finishWorkItemWithRelativeData Line:169 - 流程下一步传递的参数为：{"code":"E1445641100181856256","currentUserCode":"chenhong","inputUserId":"kongzhijie","appCode":"hnjjwz","title":"请问请问","receiptId":"U870404448126013440","outcome":"hnjjwz.depart_manager"}
2025-05-29 20:36:35.948 [http-nio-8092-exec-11] WARN  com.simbest.boot.bps.unifiedtodo.service.impl.CloseUnifiedTodoServiceImpl.closeTodo Line:68 - 统一待办核销状态：Y
2025-05-29 20:36:35.949 [http-nio-8092-exec-11] WARN  com.simbest.boot.bps.common.HttpNagentDataUtil.queryAgent Line:76 - 获取接口配置对象1：SimpleConfig(id=AC486600246105735169, address=http://************:8088/ntydl, interfaceStyle=ntydl, isOpen=true, isInner=true, provider=北京晟壁)
2025-05-29 20:36:35.950 [http-nio-8092-exec-11] WARN  com.simbest.boot.bps.common.HttpNagentDataUtil.queryAgent Line:80 - 获取接口配置对象2：SimpleConfig(id=AC486600246105735169, address=http://************:8088/ntydl, interfaceStyle=ntydl, isOpen=true, isInner=true, provider=北京晟壁)
2025-05-29 20:36:36.547 [http-nio-8092-exec-11] WARN  com.simbest.boot.bps.process.operate.WorkItemManager.finishWorkItemWithRelativeData Line:264 - 下一个审批人是：kongzhijie，代理人为：[]
2025-05-29 20:36:36.548 [http-nio-8092-exec-11] WARN  com.simbest.boot.bps.process.operate.WorkItemManager.finishWorkItemWithRelativeData Line:275 - 流程提交下一步处理代理人用时：789ms
2025-05-29 20:36:41.695 [http-nio-8092-exec-11] WARN  com.simbest.boot.bps.process.operate.WorkItemManager.finishWorkItemWithRelativeData Line:293 - 流程提交下一步调用BPS流程引擎提交下一步接口用时：5146ms
2025-05-29 20:38:09.678 [http-nio-8092-exec-17] WARN  com.simbest.boot.bps.process.operate.WorkItemManager.finishWorkItemWithRelativeData Line:169 - 流程下一步传递的参数为：{"code":"E1445641100181856256","currentUserCode":"kongzhijie","inputUserId":"chenhong","appCode":"hnjjwz","title":"请问请问","receiptId":"U870404448126013440","outcome":"return"}
2025-05-29 20:38:09.976 [http-nio-8092-exec-17] WARN  com.simbest.boot.bps.unifiedtodo.service.impl.CloseUnifiedTodoServiceImpl.closeTodo Line:68 - 统一待办核销状态：Y
2025-05-29 20:38:09.978 [http-nio-8092-exec-17] WARN  com.simbest.boot.bps.common.HttpNagentDataUtil.queryAgent Line:76 - 获取接口配置对象1：SimpleConfig(id=AC486600246105735169, address=http://************:8088/ntydl, interfaceStyle=ntydl, isOpen=true, isInner=true, provider=北京晟壁)
2025-05-29 20:38:09.978 [http-nio-8092-exec-17] WARN  com.simbest.boot.bps.common.HttpNagentDataUtil.queryAgent Line:80 - 获取接口配置对象2：SimpleConfig(id=AC486600246105735169, address=http://************:8088/ntydl, interfaceStyle=ntydl, isOpen=true, isInner=true, provider=北京晟壁)
2025-05-29 20:38:10.562 [http-nio-8092-exec-17] WARN  com.simbest.boot.bps.process.operate.WorkItemManager.finishWorkItemWithRelativeData Line:264 - 下一个审批人是：chenhong，代理人为：[]
2025-05-29 20:38:10.563 [http-nio-8092-exec-17] WARN  com.simbest.boot.bps.process.operate.WorkItemManager.finishWorkItemWithRelativeData Line:275 - 流程提交下一步处理代理人用时：885ms
2025-05-29 20:38:18.463 [http-nio-8092-exec-17] WARN  com.simbest.boot.bps.process.operate.WorkItemManager.finishWorkItemWithRelativeData Line:293 - 流程提交下一步调用BPS流程引擎提交下一步接口用时：7898ms
2025-05-29 20:51:22.785 [http-nio-8092-exec-10] WARN  com.simbest.boot.bps.process.operate.WfProcessManager.startProcessAndSetRelativeData Line:87 - 流程启动调用BPS流程启动接口用时：4477ms
2025-05-29 20:51:23.121 [http-nio-8092-exec-10] WARN  com.simbest.boot.bps.process.operate.WfProcessManager.startProcessAndSetRelativeData Line:107 - 【BPS Driver】流程启动第一个工作项的状态：【10】
2025-05-29 20:51:23.124 [http-nio-8092-exec-10] WARN  com.simbest.boot.bps.process.operate.WfProcessManager.startProcessAndSetRelativeData Line:118 - 流程启动调用BPS流程工作项查询接口用时：335ms
2025-05-29 20:51:23.228 [http-nio-8092-exec-10] WARN  com.simbest.boot.bps.process.operate.WorkItemManager.finishWorkItemWithRelativeData Line:169 - 流程下一步传递的参数为：{"code":"A1445644817421524992","currentUserCode":"chenhong","inputUserId":"kongzhijie","appCode":"hnjjwz","title":"玩儿玩儿玩儿玩儿玩儿玩儿完","receiptId":"U870408165344710656","outcome":"hnjjwz.depart_manager"}
2025-05-29 20:51:23.548 [http-nio-8092-exec-10] WARN  com.simbest.boot.bps.unifiedtodo.service.impl.CloseUnifiedTodoServiceImpl.closeTodo Line:68 - 统一待办核销状态：Y
2025-05-29 20:51:23.548 [http-nio-8092-exec-10] WARN  com.simbest.boot.bps.common.HttpNagentDataUtil.queryAgent Line:76 - 获取接口配置对象1：SimpleConfig(id=AC486600246105735169, address=http://************:8088/ntydl, interfaceStyle=ntydl, isOpen=true, isInner=true, provider=北京晟壁)
2025-05-29 20:51:23.548 [http-nio-8092-exec-10] WARN  com.simbest.boot.bps.common.HttpNagentDataUtil.queryAgent Line:80 - 获取接口配置对象2：SimpleConfig(id=AC486600246105735169, address=http://************:8088/ntydl, interfaceStyle=ntydl, isOpen=true, isInner=true, provider=北京晟壁)
2025-05-29 20:51:24.124 [http-nio-8092-exec-10] WARN  com.simbest.boot.bps.process.operate.WorkItemManager.finishWorkItemWithRelativeData Line:264 - 下一个审批人是：kongzhijie，代理人为：[]
2025-05-29 20:51:24.125 [http-nio-8092-exec-10] WARN  com.simbest.boot.bps.process.operate.WorkItemManager.finishWorkItemWithRelativeData Line:275 - 流程提交下一步处理代理人用时：897ms
2025-05-29 20:51:29.959 [http-nio-8092-exec-10] WARN  com.simbest.boot.bps.process.operate.WorkItemManager.finishWorkItemWithRelativeData Line:293 - 流程提交下一步调用BPS流程引擎提交下一步接口用时：5833ms
2025-05-29 20:52:03.295 [http-nio-8092-exec-17] WARN  com.simbest.boot.bps.process.operate.WfProcessManager.startProcessAndSetRelativeData Line:87 - 流程启动调用BPS流程启动接口用时：3994ms
2025-05-29 20:52:03.444 [http-nio-8092-exec-17] WARN  com.simbest.boot.bps.process.operate.WfProcessManager.startProcessAndSetRelativeData Line:107 - 【BPS Driver】流程启动第一个工作项的状态：【10】
2025-05-29 20:52:03.444 [http-nio-8092-exec-17] WARN  com.simbest.boot.bps.process.operate.WfProcessManager.startProcessAndSetRelativeData Line:118 - 流程启动调用BPS流程工作项查询接口用时：149ms
2025-05-29 20:52:03.531 [http-nio-8092-exec-17] WARN  com.simbest.boot.bps.process.operate.WorkItemManager.finishWorkItemWithRelativeData Line:169 - 流程下一步传递的参数为：{"code":"C1445644988779814912","currentUserCode":"chenhong","inputUserId":"kongzhijie","appCode":"hnjjwz","title":"法国恢复规划法国恢复干哈","receiptId":"U870408336711389184","outcome":"hnjjwz.depart_manager"}
2025-05-29 20:52:03.885 [http-nio-8092-exec-17] WARN  com.simbest.boot.bps.unifiedtodo.service.impl.CloseUnifiedTodoServiceImpl.closeTodo Line:68 - 统一待办核销状态：Y
2025-05-29 20:52:03.886 [http-nio-8092-exec-17] WARN  com.simbest.boot.bps.common.HttpNagentDataUtil.queryAgent Line:76 - 获取接口配置对象1：SimpleConfig(id=AC486600246105735169, address=http://************:8088/ntydl, interfaceStyle=ntydl, isOpen=true, isInner=true, provider=北京晟壁)
2025-05-29 20:52:03.887 [http-nio-8092-exec-17] WARN  com.simbest.boot.bps.common.HttpNagentDataUtil.queryAgent Line:80 - 获取接口配置对象2：SimpleConfig(id=AC486600246105735169, address=http://************:8088/ntydl, interfaceStyle=ntydl, isOpen=true, isInner=true, provider=北京晟壁)
2025-05-29 20:52:04.453 [http-nio-8092-exec-17] WARN  com.simbest.boot.bps.process.operate.WorkItemManager.finishWorkItemWithRelativeData Line:264 - 下一个审批人是：kongzhijie，代理人为：[]
2025-05-29 20:52:04.453 [http-nio-8092-exec-17] WARN  com.simbest.boot.bps.process.operate.WorkItemManager.finishWorkItemWithRelativeData Line:275 - 流程提交下一步处理代理人用时：922ms
2025-05-29 20:52:10.120 [http-nio-8092-exec-17] WARN  com.simbest.boot.bps.process.operate.WorkItemManager.finishWorkItemWithRelativeData Line:293 - 流程提交下一步调用BPS流程引擎提交下一步接口用时：5666ms
2025-05-29 20:54:55.476 [http-nio-8092-exec-18] WARN  com.simbest.boot.bps.process.operate.WfProcessManager.startProcessAndSetRelativeData Line:87 - 流程启动调用BPS流程启动接口用时：3995ms
2025-05-29 20:54:55.674 [http-nio-8092-exec-18] WARN  com.simbest.boot.bps.process.operate.WfProcessManager.startProcessAndSetRelativeData Line:107 - 【BPS Driver】流程启动第一个工作项的状态：【10】
2025-05-29 20:54:55.675 [http-nio-8092-exec-18] WARN  com.simbest.boot.bps.process.operate.WfProcessManager.startProcessAndSetRelativeData Line:118 - 流程启动调用BPS流程工作项查询接口用时：197ms
2025-05-29 20:54:55.698 [http-nio-8092-exec-18] WARN  com.simbest.boot.bps.process.operate.WorkItemManager.finishWorkItemWithRelativeData Line:169 - 流程下一步传递的参数为：{"code":"C1445645711236096000","currentUserCode":"chenhong","inputUserId":"kongzhijie","appCode":"hnjjwz","title":"234234234234234234","receiptId":"U870409059205419008","outcome":"hnjjwz.depart_manager"}
2025-05-29 20:54:55.915 [http-nio-8092-exec-18] WARN  com.simbest.boot.bps.unifiedtodo.service.impl.CloseUnifiedTodoServiceImpl.closeTodo Line:68 - 统一待办核销状态：Y
2025-05-29 20:54:55.916 [http-nio-8092-exec-18] WARN  com.simbest.boot.bps.common.HttpNagentDataUtil.queryAgent Line:76 - 获取接口配置对象1：SimpleConfig(id=AC486600246105735169, address=http://************:8088/ntydl, interfaceStyle=ntydl, isOpen=true, isInner=true, provider=北京晟壁)
2025-05-29 20:54:55.917 [http-nio-8092-exec-18] WARN  com.simbest.boot.bps.common.HttpNagentDataUtil.queryAgent Line:80 - 获取接口配置对象2：SimpleConfig(id=AC486600246105735169, address=http://************:8088/ntydl, interfaceStyle=ntydl, isOpen=true, isInner=true, provider=北京晟壁)
2025-05-29 20:54:56.493 [http-nio-8092-exec-18] WARN  com.simbest.boot.bps.process.operate.WorkItemManager.finishWorkItemWithRelativeData Line:264 - 下一个审批人是：kongzhijie，代理人为：[]
2025-05-29 20:54:56.493 [http-nio-8092-exec-18] WARN  com.simbest.boot.bps.process.operate.WorkItemManager.finishWorkItemWithRelativeData Line:275 - 流程提交下一步处理代理人用时：794ms
2025-05-29 20:55:02.161 [http-nio-8092-exec-18] WARN  com.simbest.boot.bps.process.operate.WorkItemManager.finishWorkItemWithRelativeData Line:293 - 流程提交下一步调用BPS流程引擎提交下一步接口用时：5667ms
2025-05-29 20:55:18.797 [http-nio-8092-exec-20] WARN  com.simbest.boot.security.auth.provider.UumsHttpValidationAuthenticationProvider.authenticate Line:153 - UUMS登录认证器UUMS认证用户 【kongzhijie·】 通过密码【111.com】访问 【nfwpz】 失败, 错误信息: 【账号密码校验错误】
2025-05-29 20:55:18.797 [http-nio-8092-exec-20] WARN  com.simbest.boot.security.auth.provider.UumsHttpValidationAuthenticationProvider.authenticate Line:173 - UUMS登录认证器UUMS认证 【kongzhijie·】 失败， 捕获【账号密码校验错误】异常!
2025-05-29 20:55:19.118 [http-nio-8092-exec-20] WARN  com.simbest.boot.security.auth.service.impl.AuthUserCacheServiceImpl.loadCacheUser Line:118 - 无法通过关键字【auth:user:kongzhijie·】读取用户主键ID
2025-05-29 20:55:19.474 [http-nio-8092-exec-20] WARN  com.simbest.boot.security.auth.handle.FailedLoginHandler.onAuthenticationFailure Line:42 - 用户【kongzhijie·】尝试登录失败
2025-05-29 22:20:00.030 [pool-14-thread-7] WARN  com.simbest.boot.base.repository.CustomDynamicWhere.queryForList Line:50 - 自定义查询SQL输出为：【select * from US_TODO_MODEL t  where t.send_flag = 0 and t.work_flag = 0 and t.enabled =1】
2025-05-29 22:20:00.041 [pool-14-thread-6] WARN  com.simbest.boot.base.repository.CustomDynamicWhere.queryForList Line:50 - 自定义查询SQL输出为：【select * from US_TODO_MODEL t  where t.send_flag = 0 and t.work_flag = 1 and t.enabled =1】
2025-05-29 22:25:00.014 [pool-14-thread-1] WARN  com.simbest.boot.base.repository.CustomDynamicWhere.queryForList Line:50 - 自定义查询SQL输出为：【select * from US_TODO_MODEL t  where t.send_flag = 0 and t.work_flag = 1 and t.enabled =1】
2025-05-29 22:25:00.014 [pool-14-thread-3] WARN  com.simbest.boot.base.repository.CustomDynamicWhere.queryForList Line:50 - 自定义查询SQL输出为：【select * from US_TODO_MODEL t  where t.send_flag = 0 and t.work_flag = 0 and t.enabled =1】
2025-05-29 22:30:00.010 [pool-14-thread-3] WARN  com.simbest.boot.base.repository.CustomDynamicWhere.queryForList Line:50 - 自定义查询SQL输出为：【select * from US_TODO_MODEL t  where t.send_flag = 0 and t.work_flag = 0 and t.enabled =1】
2025-05-29 22:30:00.014 [pool-14-thread-7] WARN  com.simbest.boot.base.repository.CustomDynamicWhere.queryForList Line:50 - 自定义查询SQL输出为：【select * from US_TODO_MODEL t  where t.send_flag = 0 and t.work_flag = 1 and t.enabled =1】
2025-05-29 22:35:00.021 [pool-14-thread-9] WARN  com.simbest.boot.base.repository.CustomDynamicWhere.queryForList Line:50 - 自定义查询SQL输出为：【select * from US_TODO_MODEL t  where t.send_flag = 0 and t.work_flag = 0 and t.enabled =1】
2025-05-29 22:35:00.021 [pool-14-thread-5] WARN  com.simbest.boot.base.repository.CustomDynamicWhere.queryForList Line:50 - 自定义查询SQL输出为：【select * from US_TODO_MODEL t  where t.send_flag = 0 and t.work_flag = 1 and t.enabled =1】
2025-05-29 22:40:00.014 [pool-14-thread-8] WARN  com.simbest.boot.base.repository.CustomDynamicWhere.queryForList Line:50 - 自定义查询SQL输出为：【select * from US_TODO_MODEL t  where t.send_flag = 0 and t.work_flag = 0 and t.enabled =1】
2025-05-29 22:40:00.014 [pool-14-thread-3] WARN  com.simbest.boot.base.repository.CustomDynamicWhere.queryForList Line:50 - 自定义查询SQL输出为：【select * from US_TODO_MODEL t  where t.send_flag = 0 and t.work_flag = 1 and t.enabled =1】
2025-05-29 22:45:00.015 [pool-14-thread-9] WARN  com.simbest.boot.base.repository.CustomDynamicWhere.queryForList Line:50 - 自定义查询SQL输出为：【select * from US_TODO_MODEL t  where t.send_flag = 0 and t.work_flag = 0 and t.enabled =1】
2025-05-29 22:45:00.015 [pool-14-thread-1] WARN  com.simbest.boot.base.repository.CustomDynamicWhere.queryForList Line:50 - 自定义查询SQL输出为：【select * from US_TODO_MODEL t  where t.send_flag = 0 and t.work_flag = 1 and t.enabled =1】
2025-05-29 22:50:00.017 [pool-14-thread-6] WARN  com.simbest.boot.base.repository.CustomDynamicWhere.queryForList Line:50 - 自定义查询SQL输出为：【select * from US_TODO_MODEL t  where t.send_flag = 0 and t.work_flag = 0 and t.enabled =1】
2025-05-29 22:50:00.017 [pool-14-thread-5] WARN  com.simbest.boot.base.repository.CustomDynamicWhere.queryForList Line:50 - 自定义查询SQL输出为：【select * from US_TODO_MODEL t  where t.send_flag = 0 and t.work_flag = 1 and t.enabled =1】
2025-05-29 22:55:00.006 [pool-14-thread-8] WARN  com.simbest.boot.base.repository.CustomDynamicWhere.queryForList Line:50 - 自定义查询SQL输出为：【select * from US_TODO_MODEL t  where t.send_flag = 0 and t.work_flag = 1 and t.enabled =1】
2025-05-29 22:55:00.008 [pool-14-thread-5] WARN  com.simbest.boot.base.repository.CustomDynamicWhere.queryForList Line:50 - 自定义查询SQL输出为：【select * from US_TODO_MODEL t  where t.send_flag = 0 and t.work_flag = 0 and t.enabled =1】
2025-05-29 23:00:00.016 [pool-14-thread-1] WARN  com.simbest.boot.base.repository.CustomDynamicWhere.queryForList Line:50 - 自定义查询SQL输出为：【select * from US_TODO_MODEL t  where t.send_flag = 0 and t.work_flag = 1 and t.enabled =1】
2025-05-29 23:00:00.016 [pool-14-thread-5] WARN  com.simbest.boot.base.repository.CustomDynamicWhere.queryForList Line:50 - 自定义查询SQL输出为：【select * from US_TODO_MODEL t  where t.send_flag = 0 and t.work_flag = 0 and t.enabled =1】
2025-05-29 23:00:00.018 [pool-14-thread-4] WARN  com.simbest.boot.cmcc.a4.task.A4TaskCreateXmlFile.execute Line:76 - >>>>>>>>>>>>>>>>>>>>>>>>>>>>>>>>>>>>>>>>>>>>>>>>>>>>>A4TaskCreateXmlFile:执行定时任务但不是Prd环境【2025-05-29T23:00:00.018】
2025-05-29 23:05:00.019 [pool-14-thread-6] WARN  com.simbest.boot.base.repository.CustomDynamicWhere.queryForList Line:50 - 自定义查询SQL输出为：【select * from US_TODO_MODEL t  where t.send_flag = 0 and t.work_flag = 1 and t.enabled =1】
2025-05-29 23:05:00.019 [pool-14-thread-7] WARN  com.simbest.boot.base.repository.CustomDynamicWhere.queryForList Line:50 - 自定义查询SQL输出为：【select * from US_TODO_MODEL t  where t.send_flag = 0 and t.work_flag = 0 and t.enabled =1】
2025-05-29 23:10:00.020 [pool-14-thread-5] WARN  com.simbest.boot.base.repository.CustomDynamicWhere.queryForList Line:50 - 自定义查询SQL输出为：【select * from US_TODO_MODEL t  where t.send_flag = 0 and t.work_flag = 1 and t.enabled =1】
2025-05-29 23:10:00.020 [pool-14-thread-4] WARN  com.simbest.boot.base.repository.CustomDynamicWhere.queryForList Line:50 - 自定义查询SQL输出为：【select * from US_TODO_MODEL t  where t.send_flag = 0 and t.work_flag = 0 and t.enabled =1】
2025-05-29 23:15:00.014 [pool-14-thread-5] WARN  com.simbest.boot.base.repository.CustomDynamicWhere.queryForList Line:50 - 自定义查询SQL输出为：【select * from US_TODO_MODEL t  where t.send_flag = 0 and t.work_flag = 1 and t.enabled =1】
2025-05-29 23:15:00.014 [pool-14-thread-3] WARN  com.simbest.boot.base.repository.CustomDynamicWhere.queryForList Line:50 - 自定义查询SQL输出为：【select * from US_TODO_MODEL t  where t.send_flag = 0 and t.work_flag = 0 and t.enabled =1】
2025-05-29 23:20:00.018 [pool-14-thread-4] WARN  com.simbest.boot.base.repository.CustomDynamicWhere.queryForList Line:50 - 自定义查询SQL输出为：【select * from US_TODO_MODEL t  where t.send_flag = 0 and t.work_flag = 1 and t.enabled =1】
2025-05-29 23:20:00.020 [pool-14-thread-9] WARN  com.simbest.boot.base.repository.CustomDynamicWhere.queryForList Line:50 - 自定义查询SQL输出为：【select * from US_TODO_MODEL t  where t.send_flag = 0 and t.work_flag = 0 and t.enabled =1】
2025-05-29 23:25:00.020 [pool-14-thread-8] WARN  com.simbest.boot.base.repository.CustomDynamicWhere.queryForList Line:50 - 自定义查询SQL输出为：【select * from US_TODO_MODEL t  where t.send_flag = 0 and t.work_flag = 0 and t.enabled =1】
2025-05-29 23:25:00.022 [pool-14-thread-3] WARN  com.simbest.boot.base.repository.CustomDynamicWhere.queryForList Line:50 - 自定义查询SQL输出为：【select * from US_TODO_MODEL t  where t.send_flag = 0 and t.work_flag = 1 and t.enabled =1】
2025-05-29 23:30:00.014 [pool-14-thread-3] WARN  com.simbest.boot.base.repository.CustomDynamicWhere.queryForList Line:50 - 自定义查询SQL输出为：【select * from US_TODO_MODEL t  where t.send_flag = 0 and t.work_flag = 1 and t.enabled =1】
2025-05-29 23:30:00.019 [pool-14-thread-9] WARN  com.simbest.boot.base.repository.CustomDynamicWhere.queryForList Line:50 - 自定义查询SQL输出为：【select * from US_TODO_MODEL t  where t.send_flag = 0 and t.work_flag = 0 and t.enabled =1】
2025-05-29 23:35:00.019 [pool-14-thread-4] WARN  com.simbest.boot.base.repository.CustomDynamicWhere.queryForList Line:50 - 自定义查询SQL输出为：【select * from US_TODO_MODEL t  where t.send_flag = 0 and t.work_flag = 1 and t.enabled =1】
2025-05-29 23:35:00.020 [pool-14-thread-2] WARN  com.simbest.boot.base.repository.CustomDynamicWhere.queryForList Line:50 - 自定义查询SQL输出为：【select * from US_TODO_MODEL t  where t.send_flag = 0 and t.work_flag = 0 and t.enabled =1】
2025-05-29 23:40:00.018 [pool-14-thread-7] WARN  com.simbest.boot.base.repository.CustomDynamicWhere.queryForList Line:50 - 自定义查询SQL输出为：【select * from US_TODO_MODEL t  where t.send_flag = 0 and t.work_flag = 1 and t.enabled =1】
2025-05-29 23:40:00.018 [pool-14-thread-2] WARN  com.simbest.boot.base.repository.CustomDynamicWhere.queryForList Line:50 - 自定义查询SQL输出为：【select * from US_TODO_MODEL t  where t.send_flag = 0 and t.work_flag = 0 and t.enabled =1】
2025-05-29 23:45:00.009 [pool-14-thread-6] WARN  com.simbest.boot.base.repository.CustomDynamicWhere.queryForList Line:50 - 自定义查询SQL输出为：【select * from US_TODO_MODEL t  where t.send_flag = 0 and t.work_flag = 0 and t.enabled =1】
2025-05-29 23:45:00.009 [pool-14-thread-8] WARN  com.simbest.boot.base.repository.CustomDynamicWhere.queryForList Line:50 - 自定义查询SQL输出为：【select * from US_TODO_MODEL t  where t.send_flag = 0 and t.work_flag = 1 and t.enabled =1】
2025-05-29 23:50:00.011 [pool-14-thread-10] WARN  com.simbest.boot.base.repository.CustomDynamicWhere.queryForList Line:50 - 自定义查询SQL输出为：【select * from US_TODO_MODEL t  where t.send_flag = 0 and t.work_flag = 1 and t.enabled =1】
2025-05-29 23:50:00.011 [pool-14-thread-1] WARN  com.simbest.boot.base.repository.CustomDynamicWhere.queryForList Line:50 - 自定义查询SQL输出为：【select * from US_TODO_MODEL t  where t.send_flag = 0 and t.work_flag = 0 and t.enabled =1】
2025-05-29 23:55:00.045 [pool-14-thread-6] WARN  com.simbest.boot.base.repository.CustomDynamicWhere.queryForList Line:50 - 自定义查询SQL输出为：【select * from US_TODO_MODEL t  where t.send_flag = 0 and t.work_flag = 1 and t.enabled =1】
2025-05-29 23:55:00.045 [pool-14-thread-3] WARN  com.simbest.boot.base.repository.CustomDynamicWhere.queryForList Line:50 - 自定义查询SQL输出为：【select * from US_TODO_MODEL t  where t.send_flag = 0 and t.work_flag = 0 and t.enabled =1】
