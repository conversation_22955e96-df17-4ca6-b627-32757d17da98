package com.simbest.boot.hnjjwz.backstage.indexMenu.repository;

import com.simbest.boot.base.repository.LogicRepository;
import com.simbest.boot.hnjjwz.backstage.indexMenu.model.IndexMenu;
import org.springframework.data.domain.Page;
import org.springframework.data.domain.Pageable;
import org.springframework.data.jpa.repository.Query;
import org.springframework.data.repository.query.Param;

import java.util.List;
import java.util.Set;

/**
 * <AUTHOR>
 * @Data 201/05/06
 * @Description 首页菜单
 */
public interface IndexMenuRepository extends LogicRepository<IndexMenu,String> {

    /**
     * 菜单名(模糊)、菜单类型(精确)
     * @param menuName
     * @param menuUrlType
     * @param pageable
     * @return
     */
    @Query (value =  " SELECT * " +
            " FROM us_index_menu im " +
            " WHERE  im.menu_name like concat( concat('%',:menuName),'%') " +
            " AND im.menu_url_type IN (:menuUrlType) " +
            " AND im.enabled = 1 AND im.removed_time IS NULL ",
            countQuery = " SELECT COUNT(*) " +
                    " FROM us_index_menu im " +
                    " WHERE  im.menu_name like concat( concat('%',:menuName),'%') " +
                    " AND im.menu_url_type IN (:menuUrlType) " +
                    " AND im.enabled = 1 AND im.removed_time IS NULL ",
            nativeQuery = true)
    Page<IndexMenu> findAllDim( @Param ( "menuName" ) String menuName, @Param ( "menuUrlType" ) List<String> menuUrlType, Pageable pageable );

    @Query(value = "select  t.* from us_index_menu t  where t.enabled=1 ORDER BY id asc",
            nativeQuery = true)
    List<IndexMenu> findIndexMenu();

    /**
     * 页面初始化时获取菜单
     * @return
     */
    @Query(value = "select  t.* from us_index_menu t  where t.enabled=1 ORDER BY id asc",
            nativeQuery = true)
    Set<IndexMenu> findRootAndNext();
}
