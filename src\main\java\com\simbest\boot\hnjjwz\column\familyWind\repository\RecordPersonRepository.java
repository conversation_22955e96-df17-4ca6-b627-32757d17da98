package com.simbest.boot.hnjjwz.column.familyWind.repository;/**
 * Created by KZH on 2019/7/30 18:12.
 */

import com.simbest.boot.base.repository.LogicRepository;
import com.simbest.boot.hnjjwz.column.familyWind.model.RecordPerson;
import org.springframework.data.jpa.repository.Modifying;
import org.springframework.data.jpa.repository.Query;
import org.springframework.data.repository.query.Param;

import java.util.List;

/**
 * <AUTHOR>
 * @create 2019-07-30 18:12
 * @desc 记录人Dao
 **/
public interface RecordPersonRepository extends LogicRepository<RecordPerson,String> {

    @Query(value = "select  t.* from us_record_person t where  t.user_name = :userName and t.enabled=1",
            nativeQuery = true)
    RecordPerson findUserName(@Param("userName") String userName );


    @Modifying
    @Query(
            value = "update us_record_person t set t.calligraphy_vote= 5,t.collectarticle_vote=5,t.painting_vote=5,t.photography_vote=5  where t.enabled=1 and t.removed_time is null",
            nativeQuery = true
    )
    int updateOnVoteQuantity();

}
