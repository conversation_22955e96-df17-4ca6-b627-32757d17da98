package com.simbest.boot.hnjjwz.process.wf.repository;


import com.simbest.boot.base.repository.LogicRepository;
import com.simbest.boot.hnjjwz.process.wf.model.ProgramaDataForm;
import org.springframework.data.domain.Page;
import org.springframework.data.domain.Pageable;
import org.springframework.data.jpa.repository.Modifying;
import org.springframework.data.jpa.repository.Query;
import org.springframework.data.repository.query.Param;
import org.springframework.transaction.annotation.Transactional;

import java.util.List;
import java.util.Map;
import java.util.Set;

/**
 * <AUTHOR>
 * @Data 2019/04/28
 * @Description 审批工单数据层
 */
public interface ProgramaDataFormRepository extends LogicRepository<ProgramaDataForm, String> {

    @Transactional
    @Modifying
    @Query(
            value = "update us_programa_data_form set enabled=0 where id =:id",
            nativeQuery = true
    )
    int deleteByFromId(@Param("id") String id);

    @Transactional
    @Query(
            value = "select a.* from us_programa_data_form a where a.enabled = 1 and a.id=:id",
            nativeQuery = true
    )
    ProgramaDataForm getApprovalFromDetail(@Param("id") String id);

    @Query(
            value = "select a.* from us_programa_data_form a where a.enabled = 1  and a.pm_ins_id=:pmInstId ",
            nativeQuery = true
    )
    ProgramaDataForm getApprovalFromDetailFromInstId(@Param("pmInstId") String pmInstId);

    /**
     * 再根据pm_ins_id字段获取ApprovalForm对象
     *
     * @param pmInsId
     * @return
     */
    @Query(
            value = "SELECT a.* FROM us_programa_data_form a WHERE a.removed_time IS NULL  AND a.enabled = 1 " +
                    " AND a.pm_ins_id = :pmInsId ",
            nativeQuery = true
    )
    ProgramaDataForm getFromDeceiptCodeByPm(@Param("pmInsId") String pmInsId);

    /**
     * 根据bussinessKey查询所有工单信息
     *
     * @param bussinessKey
     * @return
     */
    @Query(
            value = " SELECT * FROM ACT_BUSINESS_STATUS abs,US_PM_INSTENCE upi,us_programa_data_form apf " +
                    " WHERE abs.business_key = upi.id AND  upi.pm_ins_id = apf.pm_ins_id AND " +
                    " abs.enabled = 1 AND upi.removed_time IS NULL  AND upi.enabled = 1" +
                    " AND apf.removed_time IS NULL  AND apf.enabled = 1 " +
                    " AND abs.business_key = :bussinessKey ",
            nativeQuery = true
    )
    ProgramaDataForm getFromDeceiptCode(@Param("bussinessKey") String bussinessKey);


    /**
     * 根据工单标题模糊查询工单列表并分页
     *
     * @param title
     * @param pageable
     * @return
     */
    @Query(
            value = " SELECT * FROM us_programa_data_form uaf WHERE uaf.title LIKE CONCAT( CONCAT('%',:title),'%') " +
                    " ORDER BY uaf.create_time DESC ",
            countQuery = "SELECT count(*) " +
                    " FROM US_APPROVAL_FORM uaf WHERE uaf.title LIKE CONCAT( CONCAT('%',:title),'%') " +
                    "  ORDER BY uaf.create_time DESC ",
            nativeQuery = true
    )
    Page<ProgramaDataForm> findWorkOrderList(@Param("title") String title, Pageable pageable);

    /**
     * 查询栏目内容表单信息并分页,只能查询已经发布的栏目内容
     *
     * @param programaCode
     * @param title
     * @return
     */
    @Query(value = " select *" +
            "  from (select pdi.*, pi.programa_name programaName" +
            "          from us_programa_data_form pdi" +
            "          LEFT JOIN us_programa_info pi" +
            "            ON pdi.programa_code = pi.programa_code" +
            "         WHERE pdi.programa_code = :programaCode" +
            "           AND pdi.title LIKE concat(concat('%', :title), '%')" +
            "           AND pdi.enabled = 1" +
            "           AND pdi.removed_time IS NULL" +
            "           AND pdi.is_publish = 1" +
            "         order by pdi.stick_flag desc, pdi.creation_time desc)" +
            " where rownum <=:rownum",
            nativeQuery = true)
    List<ProgramaDataForm> findDataFromProgramaCode(@Param("programaCode") String programaCode, @Param("title") String title, @Param("rownum") String rownum);


    /**
     * 查询栏目内容表单信息并分页,只能查询已经发布的栏目内容  解决纪律与监督的查询只查011的  其实现在的几率与监督里面有三个模块  011001纪律审查  011003专项监督  011002违反中央八项精神及四风问题
     *
     * @param programaCode
     * @param title
     * @return
     */
    @Query(value = " select *" +
            "  from (select pdi.*, pi.programa_name programaName" +
            "          from us_programa_data_form pdi" +
            "          LEFT JOIN us_programa_info pi" +
            "            ON pdi.programa_code = pi.programa_code" +
            "         WHERE pdi.programa_code in :programaCode" +
            "           AND pdi.title LIKE concat(concat('%', :title), '%')" +
            "           AND pdi.enabled = 1" +
            "           AND pdi.removed_time IS NULL" +
            "           AND pdi.is_publish = 1" +
            "         order by pdi.stick_flag desc, pdi.creation_time desc)" +
            " where rownum <=:rownum",
            nativeQuery = true)
    List<ProgramaDataForm> findDataFromProgramaCodeByCustom(@Param("programaCode") String[] programaCode, @Param("title") String title, @Param("rownum") String rownum);


    /**
     * 根据栏目编码查询所有
     *
     * @param programaCode
     * @return
     */
    @Query(value = " select pdi.title,pdi.programa_display_name,pdi.truename,pdi.belong_company_name,pdi.creation_time,pdi.belong_department_name,pdi.pm_ins_id,pi.programa_name programaName " +
            " from us_programa_data_form pdi " +
            " LEFT JOIN us_programa_info pi ON pdi.programa_code=pi.programa_code " +
            " WHERE pdi.programa_code=:programaCode" +
            " AND pdi.enabled=1 AND pdi.removed_time IS NULL AND pdi.is_publish = 1 " +
            "             order by pdi.stick_flag ,pdi.creation_time desc",
            countQuery = "SELECT count(*) " +
                    " from us_programa_data_form pdi " +
                    " LEFT JOIN us_programa_info pi ON pdi.programa_code=pi.programa_code " +
                    " WHERE pdi.programa_code=:programaCode " +
                    " AND pdi.enabled=1 AND pdi.removed_time IS NULL AND pdi.is_publish = 1 " +
                    "             order by pdi.stick_flag desc ,pdi.creation_time desc",
            nativeQuery = true)
    Page<Map<String, Object>> findDataFromProgramaCodeList(@Param("programaCode") String programaCode, Pageable pageable);


    @Query(value = " select pdi.title,pdi.programa_display_name,pdi.truename,pdi.belong_company_name,pdi.creation_time,pdi.belong_department_name,pdi.pm_ins_id,pi.programa_name programaName " +
            " from us_programa_data_form pdi " +
            " LEFT JOIN us_programa_info pi ON pdi.programa_code=pi.programa_code " +
            " WHERE pdi.programa_code=:programaCode" +
            " AND pdi.enabled=1 AND pdi.removed_time IS NULL AND pdi.is_publish = 1 " +
            "             order by pdi.stick_flag ,pdi.creation_time desc",
            nativeQuery = true)
    List<Map<String, Object>> findDataFromProgramaCodeListNoPage(@Param("programaCode") String programaCode);

    @Query(value = " select t.*" +
            "          from us_programa_data_form t" +
            "         where t.programa_code = :programaCode" +
            "           and t.IS_PUBLISH = 1" +
            "           and t.enabled = 1" +
            "         order by t.stick_flag,t.creation_time desc",
            countQuery = "SELECT count(*) " +
                    " from us_programa_data_form t" +
                    "         where t.programa_code = :programaCode" +
                    "           and t.IS_PUBLISH = 1" +
                    "           and t.enabled = 1" +
                    "         order by t.stick_flag desc,t.creation_time desc",
            nativeQuery = true)
    Page<ProgramaDataForm> findProgramaDataForm(@Param("programaCode") String programaCode, Pageable pageable);


    @Query(value = " select t.*" +
            "          from us_programa_data_form t" +
            "         where t.programa_code in :programaCode" +
            "           and t.IS_PUBLISH = 1" +
            "           and t.enabled = 1" +
            "         order by t.stick_flag,t.creation_time desc",
            countQuery = "SELECT count(*) " +
                    " from us_programa_data_form t" +
                    "         where t.programa_code in :programaCode" +
                    "           and t.IS_PUBLISH = 1" +
                    "           and t.enabled = 1" +
                    "         order by t.stick_flag desc,t.creation_time desc",
            nativeQuery = true)
    Page<ProgramaDataForm> findDataByIportalList(@Param("programaCode") List<String> programaCode, Pageable pageable);


    /**
     * 查询当前登录人起草审批的的报送
     * @param trueName
     * @param pageable
     * @return
     *//*
    @Query(
            value = "select t.id,  t.title,t.programa_display_name,t.truename,t.belong_company_name,t.creation_time,t.pm_ins_id,t.Belong_Department_Name" +
                    "  from US_PROGRAMA_DATA_FORM t, uums.v_org vuoo" +
                    " WHERE t.username = :trueName" +
                    "   AND t.belong_department_code = vuoo.orgcode" +
                    "   AND t.enabled = 1 order by t.creation_time desc",
            countQuery = "select count(*)" +
                    "  from US_PROGRAMA_DATA_FORM t, uums.v_org vuoo" +
                    " WHERE t.username = :trueName" +
                    "   AND t.belong_department_code = vuoo.orgcode" +
                    "   AND t.enabled = 1 order by t.creation_time desc",
            nativeQuery = true
    )
    Page<Map<String,Object>> findUserNameDetailList(@Param ("trueName")String trueName,Pageable pageable);*/

    /*    *//**
     * 查询当前登录人审批过的的报送
     * @param trueName
     * @param pageable
     * @return
     *//*
    @Query(
            value = "select  t.title,t.programa_display_name,t.truename,t.belong_company_name,t.creation_time,t.pm_ins_id,t.Belong_Department_Name" +
                    "  from US_PROGRAMA_DATA_FORM t, uums.v_org vuoo, WF_WORKITEM_MODEL wf  " +
                    "  where  wf.assistant= :trueName" +
                    "  and t.pm_ins_id=wf.receipt_code"+
                    "  and t.belong_department_code = vuoo.orgcode" +
                    "  and t.enabled = 1 ",
            countQuery = "select count(*)" +
                    "  from US_PROGRAMA_DATA_FORM t, uums.v_org vuoo, WF_WORKITEM_MODEL wf  " +
                    "  where  wf.assistant= :trueName"+
                    "  and t.pm_ins_id=wf.receipt_code"+
                    "  and t.belong_department_code = vuoo.orgcode"+
                    "  and t.enabled = 1 ",
            nativeQuery = true
    )
    Page<Map<String,Object>> findUserNameApprovalDetailList(@Param ("trueName")String trueName,Pageable pageable);*/

    /*  */

    /**
     * 查看栏目列表，当没有选择栏目时
     *
     * @param title
     * @param pageable
     * @return
     *//*
    @Query (value =  " select pdi.title,pdi.programa_display_name,pdi.truename,pdi.belong_company_name,pdi.creation_time,pdi.pm_ins_id,pdi.Belong_Department_Name" +
            " from us_programa_data_form pdi,uums.v_org vuoo " +
            " WHERE pdi.title LIKE concat( concat('%',:title),'%') " +
            " AND pdi.belong_department_code = vuoo.orgcode " +
            " AND pdi.enabled=1 AND pdi.removed_time IS NULL AND pdi.is_publish = 1 order by pdi.creation_time desc",
            countQuery = "SELECT count(*) " +
                    " from us_programa_data_form pdi,uums.v_org vuoo " +
                    " WHERE pdi.title LIKE concat( concat('%',:title),'%') " +
                    " AND pdi.belong_department_code = vuoo.orgcode " +
                    " AND pdi.enabled=1 AND pdi.removed_time IS NULL AND pdi.is_publish = 1 order by pdi.creation_time desc",
            nativeQuery = true)
    Page<Map<String,Object>> findDataDetailListNoCode( @Param ( "title" ) String title,  Pageable pageable );*/
    @Query(value = " select pdi.id,  pdi.title,pdi.programa_display_name,pdi.truename,pdi.belong_company_name,pdi.creation_time,pdi.pm_ins_id,pdi.Belong_Department_Name" +
            " from us_programa_data_form pdi" +
            " WHERE pdi.enabled=1 " +
            " AND pdi.removed_time IS NULL " +
            " AND pdi.is_publish = 1 " +
            "order by pdi.creation_time desc",
            countQuery = "SELECT count(*) " +
                    " from us_programa_data_form pdi " +
                    " WHERE pdi.enabled=1 " +
                    " AND pdi.removed_time IS NULL " +
                    " AND pdi.is_publish = 1 " +
                    " order by pdi.creation_time desc",
            nativeQuery = true)
    Page<Map<String, Object>> findDataAll(Pageable pageable);

    /**
     * 查看栏目列表，选择了栏目时
     *
     * @param title
     * @param pageable
     * @return
     */
    @Query(value = " select pdi.id,  pdi.title,pdi.programa_display_name,pdi.truename,pdi.belong_company_name,pdi.creation_time,pdi.pm_ins_id,pdi.Belong_Department_Name " +
            " from us_programa_data_form pdi,uums.v_org vuoo " +
            " WHERE pdi.programa_code in(:programaCodeList) AND pdi.title LIKE concat( concat('%',:title),'%') " +
            " AND pdi.belong_department_code = vuoo.orgcode " +
            " AND pdi.enabled=1 AND pdi.removed_time IS NULL AND pdi.is_publish = 1 order by pdi.creation_time desc",
            countQuery = "SELECT count(*) " +
                    " from us_programa_data_form pdi,uums.v_org vuoo " +
                    " WHERE pdi.programa_code in(:programaCodeList) AND pdi.title LIKE concat( concat('%',:title),'%') " +
                    " AND pdi.belong_department_code = vuoo.orgcode " +
                    " AND pdi.enabled=1 AND pdi.removed_time IS NULL AND pdi.is_publish = 1 order by pdi.creation_time desc",
            nativeQuery = true)
    Page<Map<String, Object>> findDataDetailList(@Param("programaCodeList") List<String> programaCodeList, @Param("title") String title, Pageable pageable);

    /**
     * 查看栏目列表，没选栏目 选标题时
     *
     * @param title
     * @param pageable
     * @return
     */
    @Query(value = " select pdi.id, pdi.title,pdi.programa_display_name,pdi.truename,pdi.belong_company_name,pdi.creation_time,pdi.pm_ins_id,pdi.Belong_Department_Name " +
            " from us_programa_data_form pdi" +
            " WHERE pdi.title LIKE concat( concat('%',:title),'%') " +
            " AND pdi.enabled=1 AND pdi.removed_time IS NULL AND pdi.is_publish = 1 order by pdi.creation_time desc",
            countQuery = "SELECT count(*) " +
                    " from us_programa_data_form pdi" +
                    " WHERE  pdi.title LIKE concat( concat('%',:title),'%') " +
                    " AND pdi.enabled=1 AND pdi.removed_time IS NULL AND pdi.is_publish = 1 order by pdi.creation_time desc",
            nativeQuery = true)
    Page<Map<String, Object>> findDataTableList(@Param("title") String title, Pageable pageable);


    /**
     * 根据轮播图id查询栏目内容信息
     *
     * @param id
     * @return
     */
    @Query(value = " select pdi.* " +
            " from us_programa_data_form pdi,us_slide_show uss " +
            " WHERE uss.id = :id " +
            " AND pdi.id = uss.programa_data_id " +
            " AND pdi.enabled=1 AND pdi.removed_time IS NULL AND pdi.is_publish = 1" +
            " AND uss.enabled=1 AND uss.removed_time IS NULL ",
            nativeQuery = true)
    ProgramaDataForm findProgramaDataFromSlide(@Param("id") String id);

    /**
     * 根据模板编码以及模板部位id来出栏目内容列表
     *
     * @return
     */
    @Query(value = " SELECT updf.* " +
            " FROM us_template_programa utp,us_template ut,us_programa_info upi,us_programa_data_form updf " +
            " WHERE utp.template_code = :templateCode AND utp.template_layout_id = :templateLayoutId " +
            " AND utp.template_code = ut.template_code AND utp.programa_code = upi.programa_code " +
            " AND upi.programa_code = updf.programa_code " +
            " AND utp.enabled=1 AND utp.removed_time IS NULL " +
            " AND ut.enabled=1 AND ut.removed_time IS NULL " +
            " AND upi.enabled=1 AND upi.removed_time IS NULL " +
            " AND updf.enabled=1 AND updf.removed_time IS NULL AND updf.is_publish = 1 ",
            nativeQuery = true)
    Set<ProgramaDataForm> findProgramaDataList(@Param("templateCode") String templateCode, @Param("templateLayoutId") String templateLayoutId);

    /*@Query(value = "select  t.* from us_programa_data_form t where  t.programa_code=:programaCode and t.IS_PUBLISH=1  and t.enabled=1  and rownum <= 5 order by t.creation_time desc",
            nativeQuery = true)*/
    @Query(value = "         select t.*" +
            "          from (select t.*" +
            "          from us_programa_data_form t" +
            "         where t.programa_code = :programaCode" +
            "           and t.IS_PUBLISH = 1" +
            "           and t.enabled = 1" +
            "         order by t.stick_flag,t.creation_time desc) t where rownum<=8",
            nativeQuery = true)
    List<ProgramaDataForm> findProgramaDataForm(@Param("programaCode") String programaCode);

    @Query(value = "         select t.*" +
            "          from (select t.*" +
            "          from us_programa_data_form t" +
            "         where t.programa_code = :programaCode" +
            "           and t.IS_PUBLISH = 1" +
            "           and t.enabled = 1" +
            "         order by t.stick_flag,t.creation_time desc) t where rownum<=3",
            nativeQuery = true)
    List<ProgramaDataForm> findProgramaDataFormThree(@Param("programaCode") String programaCode);

    /**
     * 控制廉洁文化二级页面显示的数量
     *
     * @param
     * @return
     */
    @Query(value =
            "select t.* from (select t.*" +
                    "   from us_programa_data_form t" +
                    "   where t.programa_code =:programaCode" +
                    "   and t.IS_PUBLISH = 1" +
                    "   and t.enabled = 1" +
                    " order by t.creation_time desc, t.stick_flag desc) t  where rownum <= 4",
            nativeQuery = true)
    List<ProgramaDataForm> findProgramaDataFormFour(@Param("programaCode") String programaCode);

    /**
     * 根据公告关联的信息进行查询
     *
     * @param
     * @return
     */
    @Query(value = "select  t.* from us_programa_data_form t where  t.id=:id and t.IS_PUBLISH=1  and t.enabled=1",
            nativeQuery = true)
    ProgramaDataForm findProgramaDataFormRelation(@Param("id") String id);

    /**
     * 倒叙取第一个
     *
     * @param
     * @return
     */
    @Query(value = "select t.* from (select t.* from US_PROGRAMA_DATA_FORM t where  t.programa_code=:programaCode and t.IS_PUBLISH=1  and t.enabled=1 order by t.stick_flag  desc ) t  where ROWNUM <=1",
            nativeQuery = true)
    ProgramaDataForm findFlashback(@Param("programaCode") String programaCode);

    /**
     * 从视图中获取用户基本信息和组织，不包含职位信息
     *
     * @param username
     * @return
     */
    @Query(value = "select * from V_USER_ORG_ONLY t  where t.username=:username",
            nativeQuery = true)
    Map<String, Object> findViewUserOrg(@Param("username") String username);

    /**
     * 获取各个公司审批的数据
     *
     * @param
     * @return
     */
    @Query(value = "select t.* from US_PROGRAMA_DATA_FORM t where t.company=:company and t.enabled=1",
            nativeQuery = true)
    List<ProgramaDataForm> findCompany(@Param("company") String company);


    /**
     * 再根据pm_ins_id字段获取ProgramaDataForm对象
     *
     * @param
     * @return
     */
    @Query(value = "select t.* from US_PROGRAMA_DATA_FORM t where t.pm_ins_id=:pmInsId and t.enabled=1",
            nativeQuery = true)
    ProgramaDataForm getProgramaDataFormPmInsId(@Param("pmInsId") String pmInsId);

    @Transactional
    @Modifying
    @Query(value = "update us_programa_data_form set enabled=0 where   pm_ins_id=:pmInsId",
            nativeQuery = true)
    void deleteByPmInsId(@Param("pmInsId") String pmInsId);


    /**
     * 查询一般权限
     *
     * @param pageable
     * @return
     */
    @Query(value = " select t.id," +
            "       t.title," +
            "       t.programa_display_name," +
            "       t.truename," +
            "       t.company," +
            "       t.creation_time," +
            "       t.pm_ins_id," +
            "       t.Belong_Department_Name" +
            "  from HNJJWZ.US_PROGRAMA_DATA_FORM t" +
            " WHERE t.username = :username" +
            "   AND t.enabled = 1" +
            " order by t.creation_time desc",
            countQuery = "select count(*)" +
                    "  from HNJJWZ.US_PROGRAMA_DATA_FORM t" +
                    " WHERE t.username = :username" +
                    "   AND t.enabled = 1" +
                    " order by t.creation_time desc",
            nativeQuery = true)
    Page<Map<String, Object>> findDataNormal(Pageable pageable, @Param("username") String username);

    @Query(value = "   select  t.* from  us_programa_data_form t" +
            "  where" +
            "    t.ENABLED = 1" +
            "    and t.IS_PUBLISH = 1" +
            "    and t.PROGRAMA_CODE in(:columnIds)" +
            "  order by" +
            "    t.STICK_FLAG DESC," +
            "    t.CREATED_TIME DESC",
            countQuery = "select  count(*) from  us_programa_data_form t" +
                    " where" +
                    "  t.ENABLED = 1" +
                    "  and t.IS_PUBLISH = 1" +
                    "  and t.PROGRAMA_CODE in(:columnIds)",
            nativeQuery = true)
    Page<ProgramaDataForm> findArticlePageByColumnId(Pageable pageable, @Param("columnIds")List<String> columnIds);

    @Query(value = "            select  * from" +
            "            (" +
            "            select" +
            "            t.*" +
            "            from" +
            "            us_programa_data_form t," +
            "            sys_new_column_model m" +
            "            where" +
            "            t.ENABLED = 1" +
            "            and t.IS_PUBLISH = 1" +
            "            and m.enabled = 1" +
            "            and m.is_display = 1" +
            "            and m.column_id = t.PROGRAMA_CODE" +
            "            and t.PROGRAMA_CODE in(:columnIds)" +
            "            ORDER BY" +
            "            t.STICK_FLAG DESC," +
            "            t.CREATED_TIME DESC" +
            "            )  where  rownum <= 10",nativeQuery = true)
    List<ProgramaDataForm> getProgramaDataList(@Param("columnIds") List<String> columnIds);

    @Query(value = "            select  * from" +
            "            (" +
            "            select" +
            "            t.*" +
            "            from" +
            "            us_programa_data_form t," +
            "            sys_new_column_model m" +
            "            where" +
            "            t.ENABLED = 1" +
            "            and t.IS_PUBLISH = 1" +
            "            and m.enabled = 1" +
            "            and m.is_display = 1" +
            "            and t.CREATED_TIME >=(sysdate - 180)" +
            "            and m.column_id = t.PROGRAMA_CODE" +
            "            and t.PROGRAMA_CODE = :programaCode" +
            "            and t.id != :id" +
            "            ORDER BY t.views_Number asc" +
            "            )  where  rownum <= 5",nativeQuery = true)
    List<ProgramaDataForm> recommendedArticle(@Param("id") String id,@Param("programaCode") String programaCode);
}
