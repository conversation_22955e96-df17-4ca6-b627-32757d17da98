package com.simbest.boot.hnjjwz.backstage.programa.service.impl;/**
 * Created by GZJ on 2019/6/26 15:07.
 */

import com.google.common.collect.Lists;
import com.simbest.boot.base.service.impl.LogicService;
import com.simbest.boot.hnjjwz.backstage.programa.model.ProgramaPower;
import com.simbest.boot.hnjjwz.backstage.programa.repository.ProgramaPowerRepository;
import com.simbest.boot.hnjjwz.backstage.programa.service.IProgramaPowerImpl;
import com.simbest.boot.sys.model.SysDictValue;
import com.simbest.boot.sys.service.ISysDictValueService;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;

import java.util.List;

/**
 * <AUTHOR>
 * @create 2019-06-26 15:07
 * @desc 栏目权限Service实现
 **/
@Slf4j
@Service
public class ProgramaPowerServiceImpl extends LogicService<ProgramaPower,String>implements IProgramaPowerImpl {

    private ProgramaPowerRepository programaPowerRepository;

    @Autowired
    private ISysDictValueService iSysDictValueService;

    @Autowired
    public ProgramaPowerServiceImpl (ProgramaPowerRepository programaPowerRepository){
        super(programaPowerRepository);
        this.programaPowerRepository=programaPowerRepository;

    }

    /**
     * 查看人对应的栏目权限
     * @param truename
     * @return
     */
    @Override
    public List<ProgramaPower> findPower(String truename) {


        SysDictValue sysDictValue=new SysDictValue();
        sysDictValue.setDictType("modulePermission");

        List<SysDictValue> dictValue = iSysDictValueService.findDictValue(sysDictValue);

        boolean state=false;

        for(SysDictValue sysDictValue1:dictValue){
            if(truename.equals(sysDictValue1.getValue())){
                state=true;
                break;
            }

        }

        SysDictValue sysDictValue1=new SysDictValue();
        sysDictValue1.setDictType("modulePermission1");

        List<SysDictValue> dictValue1 = iSysDictValueService.findDictValue(sysDictValue1);

        boolean state1=false;

        for(SysDictValue sysDictValue11:dictValue1){
            if(truename.equals(sysDictValue11.getValue())){
                state1=true;
                break;
            }

        }

        SysDictValue sysDictValue2=new SysDictValue();
        sysDictValue2.setDictType("modulePermission2");

        List<SysDictValue> dictValue2 = iSysDictValueService.findDictValue(sysDictValue2);

        boolean state2=false;

        for(SysDictValue sysDictValue12:dictValue2){
            if(truename.equals(sysDictValue12.getValue())){
                state2=true;
                break;
            }

        }

        if(state||state1||state2){

            if(state1){
                return programaPowerRepository.findPower(Lists.newArrayList("巡查工作办公室",truename));
            }else if(state2){
                return programaPowerRepository.findPower(Lists.newArrayList("纪委办公室",truename));
            }else {
                return programaPowerRepository.findPower(truename);
            }


        }else {
            return programaPowerRepository.findPower("省公司全体人员");
        }


    }
}
