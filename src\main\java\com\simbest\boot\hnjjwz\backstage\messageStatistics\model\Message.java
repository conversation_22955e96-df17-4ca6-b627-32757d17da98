package com.simbest.boot.hnjjwz.backstage.messageStatistics.model;/**
 * Created by KZH on 2019/7/1 10:12.
 */

import com.simbest.boot.base.annotations.EntityIdPrefix;
import com.simbest.boot.base.annotations.ExcelVOAttribute;
import com.simbest.boot.base.model.LogicModel;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.*;
import org.hibernate.annotations.GenericGenerator;

import javax.persistence.Column;
import javax.persistence.Entity;
import javax.persistence.GeneratedValue;
import javax.persistence.Id;

/**
 * <AUTHOR>
 * @create 2019-07-01 10:12
 * @desc 信息统计实体
 **/
@Data
@EqualsAndHashCode(callSuper = true)
@NoArgsConstructor
@AllArgsConstructor
@Entity(name = "us_message")
@ApiModel(value = "信息统计")
public class Message extends LogicModel {

    @Id
    @Column(name = "id", length = 40)
    @GeneratedValue(generator = "snowFlakeId")
    @GenericGenerator(name = "snowFlakeId", strategy = "com.simbest.boot.util.distribution.id.SnowflakeId")
    @EntityIdPrefix(prefix = "M") //主键前缀，此为可选项注解
    private String id;

    @Setter
    @Getter
    @Column(length = 100)
    @ApiModelProperty(value = "单位")
    @ExcelVOAttribute(name = "单位", column = "A")
    private String department;

    @Setter
    @Getter
    @Column(length = 100)
    @ApiModelProperty(value = "上报信息量")
    @ExcelVOAttribute(name = "上报信息量", column = "B")
    private String appearMessage;

    @Setter
    @Getter
    @Column(length = 100)
    @ApiModelProperty(value = "采编量")
    @ExcelVOAttribute(name = "采编量", column = "C")
    private String adopt;

    @Setter
    @Getter
    @Column(length = 100)
    @ApiModelProperty(value = "未采编量")
    @ExcelVOAttribute(name = "未采编量", column = "D")
    private String noAdopt;

    @Setter
    @Getter
    @Column(length = 100)
    @ApiModelProperty(value = "采编率")
    @ExcelVOAttribute(name = "采编率", column = "E")
    private String adoptRatio;
}
