# \u914D\u7F6E\u53C2\u8003 https://docs.spring.io/spring-boot/docs/current/reference/html/common-application-properties.html

#spring.profiles.active=dev
spring.profiles.active=${profileActive}

spring.main.allow-bean-definition-overriding=true
server.servlet.context-path=${server.servlet.context-path}
server.servlet.session.timeout=${server.servlet.session.timeout}
server.servlet.session.cookie.max-age=${server.servlet.session.timeout}
server.servlet.session.cookie.http-only=true
spring.servlet.multipart.max-file-size=${spring.servlet.multipart.max-file-size}
spring.servlet.multipart.max-request-size=${spring.servlet.multipart.max-request-size}
spring.datasource.type=${spring.datasource.type}
spring.datasource.driver-class-name=${spring.datasource.driver-class-name}
spring.datasource.url=${spring.datasource.url}
spring.datasource.username=${spring.datasource.username}
spring.datasource.password=${spring.datasource.password}
spring.jpa.database-platform=${spring.jpa.database-platform}
spring.datasource.sql-script-encoding=${spring.datasource.sql-script-encoding}
#\u914D\u7F6E\u521D\u59CB\u5316\u5927\u5C0F\u3001\u6700\u5C0F\u3001\u6700\u5927
spring.datasource.druid.initial-size=${spring.datasource.druid.initial-size}
spring.datasource.druid.min-idle=${spring.datasource.druid.min-idle}
spring.datasource.druid.max-active=${spring.datasource.druid.max-active}
#\u914D\u7F6E\u83B7\u53D6\u8FDE\u63A5\u7B49\u5F85\u8D85\u65F6\u7684\u65F6\u95F4
spring.datasource.druid.max-wait=${spring.datasource.druid.max-wait}
#\u914D\u7F6E\u95F4\u9694\u591A\u4E45\u624D\u8FDB\u884C\u4E00\u6B21\u68C0\u6D4B\uFF0C\u68C0\u6D4B\u9700\u8981\u5173\u95ED\u7684\u7A7A\u95F2\u8FDE\u63A5\uFF0C\u5355\u4F4D\u662F\u6BEB\u79D2
spring.datasource.druid.time-between-eviction-runs-millis=${spring.datasource.druid.time-between-eviction-runs-millis}  
#\u914D\u7F6E\u4E00\u4E2A\u8FDE\u63A5\u5728\u6C60\u4E2D\u6700\u5C0F\u751F\u5B58\u7684\u65F6\u95F4\uFF0C\u5355\u4F4D\u662F\u6BEB\u79D2
spring.datasource.druid.min-evictable-idle-time-millis=${spring.datasource.druid.min-evictable-idle-time-millis}
spring.datasource.druid.validation-query=${spring.datasource.druid.validation-query}
spring.datasource.druid.test-while-idle=true
spring.datasource.druid.test-on-borrow=false
spring.datasource.druid.test-on-return=false
spring.datasource.druid.remove-abandoned=true
spring.datasource.druid.remove-abandoned-timeout=1800
spring.datasource.druid.log-abandoned=true
spring.datasource.pool-prepared-statements=true  
spring.datasource.max-pool-prepared-statement-per-connection-size=20
# \u914D\u7F6EStatFilter 
spring.datasource.druid.filter.stat.db-type=${spring.datasource.database.type}
spring.datasource.druid.filter.stat.log-slow-sql=true
spring.datasource.druid.filter.stat.slow-sql-millis=${spring.datasource.druid.filter.stat.slow-sql-millis}
# \u914D\u7F6EWallFilter 
spring.datasource.druid.filter.wall.enabled=${spring.datasource.druid.filter.wall.enabled}
spring.datasource.druid.filter.wall.db-type=${spring.datasource.database.type}
spring.datasource.druid.filter.wall.config.delete-allow=true
spring.datasource.druid.filter.wall.config.drop-table-allow=true
# \u914D\u7F6ESlf4jLogFilter(\u6253\u5F00\u65F6\uFF0C\u9996\u6B21\u542F\u52A8\u5EFA\u8868\u65F6\uFF0C\u5220\u9664\u552F\u4E00\u952E\u7D22\u5F15\u62A5\u9519)
#spring.datasource.druid.filter.slf4j.enabled=true
#spring.datasource.druid.filter.slf4j.statement-sql-pretty-format=true
#spring.datasource.druid.filter.slf4j.statement-log-enabled=true
#spring.datasource.druid.filter.slf4j.statement-executgetAllTodoByManagerPagee-query-after-log-enabled=false
# WebStatFilter\u914D\u7F6E\uFF0C\u8BF4\u660E\u8BF7\u53C2\u8003Druid Wiki\uFF0C\u914D\u7F6E_\u914D\u7F6EWebStatFilter
spring.datasource.druid.web-stat-filter.profile-enable=true
spring.datasource.druid.web-stat-filter.url-pattern=/druid/*
spring.datasource.druid.web-stat-filter.exclusions=*.js,*.gif,*.jpg,*.bmp,*.png,*.css,*.ico,/druid/*
spring.datasource.druid.web-stat-filter.session-stat-enable=true
spring.datasource.druid.web-stat-filter.session-stat-max-count=10
# StatViewServlet\u914D\u7F6E\uFF0C\u8BF4\u660E\u8BF7\u53C2\u8003Druid Wiki\uFF0C\u914D\u7F6E_StatViewServlet\u914D\u7F6E
spring.datasource.druid.stat-view-servlet.enabled=true
spring.datasource.druid.stat-view-servlet.url-pattern=/druid/*
spring.datasource.druid.stat-view-servlet.reset-enable=true
spring.datasource.druid.stat-view-servlet.login-username=${spring.datasource.druid.stat-view-servlet.login-username}
spring.datasource.druid.stat-view-servlet.login-password=${spring.datasource.druid.stat-view-servlet.login-password}

#spring.redis.cluster.nodes=${spring.redis.cluster.nodes}
spring.redis.config.type=${spring.redis.config.type}
spring.redis.config.type.redis=${spring.redis.config.type.redis}
spring.redis.cluster.password=${spring.redis.cluster.password}
spring.redis.cluster.max-redirects=${spring.redis.cluster.max-redirects}
spring.cache.redis.key-prefix=${spring.cache.redis.key-prefix}
spring.console.enabled=${spring.console.enabled}
spring.console.path=${spring.console.path}
spring.jpa.show-sql=${spring.jpa.show-sql}
spring.jpa.generate-ddl=${spring.jpa.generate-ddl}
spring.jpa.hibernate.ddl-auto=${spring.jpa.hibernate.ddl-auto}
spring.jpa.database=${spring.jpa.database}
spring.messages.encoding=${spring.messages.encoding}
spring.http.encoding.charset=${spring.http.encoding.charset}
spring.thymeleaf.cache=${spring.thymeleaf.cache}
spring.thymeleaf.enable-spring-el-compiler=${spring.thymeleaf.enable-spring-el-compiler}
spring.session.store-type=${spring.session.store-type}
spring.session.redis.cleanup-cron=${spring.session.redis.cleanup-cron}
spring.session.redis.namespace=${spring.session.redis.namespace}

management.endpoints.web.exposure.include=${management.endpoints.web.exposure.include}

logback.groupId=${logback.groupId}
logback.artifactId=${logback.artifactId}
app.file.upload.path=${app.file.upload.path}
app.file.upload.location=${app.file.upload.location}
app.oa.portal.token=${app.oa.portal.token}
app.uums.address=${app.uums.address}
app.swagger.address=${app.swagger.address}
app.druid.monitor.address=${app.druid.monitor.address}
app.actuator.monitor.address=${app.actuator.monitor.address}
app.task.heart.test.job=${app.task.heart.test.job}
app.captcha.enable=${app.captcha.enable}
app.host.port=${app.host.port}

app.bps.tenant=${tenant}
app.bps.tenant_id=${tenant_id}

#\u7EDF\u4E00\u5F85\u529E-\u63A5\u53E3\u5E73\u53F0WebService\u5730\u5740
cxf.openTodoUrl=${cxf.openTodoUrl}
cxf.closeTodoUrl=${cxf.closeTodoUrl}
cxf.cancelTodoUrl=${cxf.cancelTodoUrl}


app.task.time.vote=${app.task.time.vote}
#app.cas.server.address=https://localhost:9443/cas
#app.cas.app.address=http://localhost:10001/nma/caslogin/cas



#Csrf \u9632\u76D7\u94FE\u4FDD\u62A4\u4E3B\u673A\u767D\u540D\u5355
app.security.white.hosts=************,portalui.ha.cmcc,************,************,localhost,hrpamgt,portaltest.ha.cmcc,***********,portal.ha.cmcc,iportal.ha.cmcc,***********,***********,************,************,************,************,************,************
