<!DOCTYPE html>
<html xmlns="http://www.w3.org/1999/xhtml"
      xmlns:th="http://www.thymeleaf.org">
<head>
    <title>深入贯彻中央八项规定精神学习教育——警示教育专区-清廉移动</title>
    <meta http-equiv="Content-Type" content="text/html; charset=utf-8"/>
    <meta http-equiv="X-UA-Compatible" content="IE=edge"/>
    <meta name="viewport" content="width=device-width, initial-scale=1, shrink-to-fit=no"/>
    <link href="../../fonts/iconfont/iconfont.css?v=svn.revision"
          th:href="@{/fonts/iconfont/iconfont.css?v=svn.revision}" rel="stylesheet"/>
    <link href="https://cdnjs.cloudflare.com/ajax/libs/font-awesome/5.15.4/css/all.min.css" rel="stylesheet"/>
    <link href="http://************:8088/simbestui/js/themes/default/easyui.css?v=svn.revision"
          th:href="@{http://************:8088/simbestui/js/themes/default/easyui.css?v=svn.revision}" rel="stylesheet"/>
    <link href="http://************:8088/simbestui/js/themes/icon.css?v=svn.revision"
          th:href="@{http://************:8088/simbestui/js/themes/icon.css?v=svn.revision}" rel="stylesheet"/>
    <link href="http://************:8088/simbestui/css/public.css?v=svn.revision"
          th:href="@{http://************:8088/simbestui/css/public.css?v=svn.revision}" rel="stylesheet"/>
    <link href="http://************:8088/simbestui/css/pages.css?v=svn.revision"
          th:href="@{http://************:8088/simbestui/css/pages.css?v=svn.revision}" rel="stylesheet"/>
    <link href="../../css/webSite.css?v=svn.revision" th:href="@{/css/webSite.css?v=svn.revision}" rel="stylesheet"/>
    <script src="http://************:8088/simbestui/js/jquery.min.js?v=svn.revision"
            th:src="@{http://************:8088/simbestui/js/jquery.min.js?v=svn.revision}"
            type="text/javascript"></script>
    <script src="http://************:8088/simbestui/js/jquery.easyui.min.js?v=svn.revision"
            th:src="@{http://************:8088/simbestui/js/jquery.easyui.min.js?v=svn.revision}"
            type="text/javascript"></script>
    <script src="http://************:8088/simbestui/js/easyui-lang-zh_CN.js?v=svn.revision"
            th:src="@{http://************:8088/simbestui/js/easyui-lang-zh_CN.js?v=svn.revision}"
            type="text/javascript"></script>
    <script src="../../js/jquery.config.js?v=svn.revision" th:src="@{/js/jquery.config.js?v=svn.revision}"
            type="text/javascript"></script>
    <script src="http://************:8088/simbestui/js/jquery.zjsfile.js?v=svn.revision"
            th:src="@{http://************:8088/simbestui/js/jquery.zjsfile.js?v=svn.revision}"
            type="text/javascript"></script>
              <script src="http://************:8088/simbestui/js/jsencrypt.min.js?v=-1?v=svn.revision"
            th:src="@{http://************:8088/simbestui/js/jsencrypt.min.js?v=-1?v=svn.revision}"
            type="text/javascript"></script>
    <script src="http://************:8088/simbestui/js/jquery.aduq.js?v=svn.revision"
            th:src="@{http://************:8088/simbestui/js/jquery.aduq.js?v=svn.revision}"
            type="text/javascript"></script>
    <script src="http://************:8088/simbestui/js/jquery.process.js?v=svn.revision"
            th:src="@{http://************:8088/simbestui/js/jquery.process.js?v=svn.revision}"
            type="text/javascript"></script>
    <script src="../../js/webSite.js?v=svn.revision" th:src="@{/js/webSite.js?v=svn.revision}"
            type="text/javascript"></script>
    <script src="../../js/eightRegulations.js?v=svn.revision" th:src="@{/js/eightRegulations.js?v=svn.revision}"
            type="text/javascript"></script>
    <script type="text/javascript">
        $(function () {
            getCurrent();
            webSite();
            // 页面初始化
            // 默认加载上级精神分类的新闻
            loadNewsData('directive');
        });
    </script>
    <style>
        /* 整体布局样式 */
        body {
            font-family: "Microsoft YaHei", Arial, sans-serif;
            background-color: #f5f5f5;
            margin: 0;
            padding: 0;
        }

        /* 头部样式 */
        .header {
            height: 300px;
            background: url(images/banner.png) no-repeat top center;
            background-size: cover;
            padding: 0;
        }

        /* 标题样式 */
        #h6 {
            font-size: 24px;
            color: #fff;
            text-align: center;
            line-height: 340px;
            text-shadow: 1px 1px 3px rgba(0, 0, 0, 0.5);
        }


        /* 内容区样式 */
        .content-container {
            width: 1200px;
            margin: 20px auto;
            display: flex;
            flex-wrap: wrap;
            justify-content: space-between;
        }

        /* 简介区域样式 */
        .intro-section {
            width: 100%;
            background-color: #fff;
            padding: 30px;
            margin-bottom: 20px;
            box-shadow: 0 2px 5px rgba(0, 0, 0, 0.1);
            border-radius: 5px;
        }

        .slideBanner {
            width: 1200px;
            height: 100px;
            padding: 200px 0 0;
            margin: 0 auto;
            overflow: hidden;
        }

        .slideBanner img {
            width: 100%;
            padding: 0px;
        }

        .intro-section h2 {
            font-size: 28px;
            color: #333;
            margin-top: 0;
            margin-bottom: 15px;
            font-weight: bold;
            position: relative;
            padding-bottom: 15px;
        }

        .intro-section h2::after {
            content: "";
            position: absolute;
            bottom: 0;
            left: 0;
            width: 80px;
            height: 3px;
            background: linear-gradient(to right, #e60000, #ffcc00);
        }

        .intro-section p {
            font-size: 16px;
            color: #666;
            line-height: 1.8;
        }

        /* 右侧图片样式 */
        .intro-image {
            float: right;
            width: 360px;
            height: 108px;
            margin-left: 20px;
            margin-bottom: 10px;
            background: url('images/eight_regulations.jpg') no-repeat center;
            background-size: cover;
        }

        /* 功能入口样式 */
        .function-entries {
            width: 100%;
            display: flex;
            justify-content: space-between;
        }

        .function-entry {
            width: 32%;
            height: 90px;
            display: flex;
            align-items: center;
            justify-content: center;
            background-color: #fff;
            box-shadow: 0 2px 8px rgba(0, 0, 0, 0.1);
            cursor: pointer;
            transition: all 0.3s;
            border-radius: 8px;
            position: relative;
            overflow: hidden;
        }

        .function-entry::before {
            content: "";
            position: absolute;
            top: 0;
            left: 0;
            width: 100%;
            height: 4px;
            background: linear-gradient(to right, #e60000, #ffcc00);
            opacity: 0;
            transition: opacity 0.3s;
        }

        .function-entry:hover {
            transform: translateY(-5px);
            box-shadow: 0 8px 20px rgba(0, 0, 0, 0.15);
        }

        .function-entry:hover::before {
            opacity: 1;
        }

        .function-entry:hover i {
            color: #e60000;
            background-color: #fff0f0;
            transform: scale(1.05);
            box-shadow: 0 5px 20px rgba(230, 0, 0, 0.15);
        }

        .function-entry:hover span {
            color: #e60000;
        }

        .function-entry.active {
            background: linear-gradient(to right, #e60000, #ffcc00);
            transform: translateY(-5px);
            box-shadow: 0 8px 25px rgba(230, 0, 0, 0.2);
        }

        .function-entry.active::before {
            opacity: 0;
        }

        .function-entry.active i {
            color: #fff;
            background: rgba(255, 255, 255, 0.2);
            transform: scale(1.05);
            box-shadow: 0 5px 15px rgba(255, 255, 255, 0.3);
        }

        .function-entry.active i::after {
            opacity: 0.3;
        }

        .function-entry.active span {
            color: #fff;
            text-shadow: 0 1px 2px rgba(0, 0, 0, 0.1);
        }

        .function-entry i {
            font-size: 36px;
            color: #555;
            transition: all 0.3s;
            width: 60px;
            height: 60px;
            display: flex;
            align-items: center;
            justify-content: center;
            border-radius: 50%;
            background-color: #f8f8f8;
            box-shadow: 0 4px 15px rgba(0, 0, 0, 0.08);
            position: relative;
            overflow: hidden;
        }

        .function-entry i::after {
            content: "";
            position: absolute;
            top: 0;
            left: 0;
            right: 0;
            bottom: 0;
            background: radial-gradient(circle at center, rgba(255, 255, 255, 0.9) 0%, rgba(255, 255, 255, 0.4) 70%, rgba(255, 255, 255, 0) 100%);
            opacity: 0.7;
        }

        .function-entry span {
            font-size: 18px;
            color: #333;
            font-weight: bold;
            transition: color 0.3s;
            letter-spacing: 1px;
            margin-left: 20px;

        }

        /* 新闻列表样式 */
        .news-section {
            width: 100%;
            background-color: #fff;
            padding: 15px 25px 0;
            box-shadow: 0 3px 10px rgba(0, 0, 0, 0.1);
            border-radius: 8px;
            margin-top: 20px;
        }

        .news-section h2 {
            display: none;
            font-size: 24px;
            color: #333;
            margin-top: 0;
            margin-bottom: 20px;
            font-weight: bold;
            position: relative;
            padding-bottom: 15px;
            text-align: center;
        }

        .news-section h2::after {
            content: "";
            position: absolute;
            bottom: 0;
            left: 50%;
            transform: translateX(-50%);
            width: 100px;
            height: 3px;
            background: linear-gradient(to right, #e60000, #ffcc00);
        }

        .news-list {
            margin-bottom: 20px;
        }

        .news-item {
            padding: 10px 10px;
            border-bottom: 1px solid #eee;
            transition: all 0.3s;
            margin-bottom: 5px;
            border-radius: 4px;
            height: 24px;
        }

        .news-item:hover {
            background-color: #f9f9f9;
            box-shadow: 0 2px 8px rgba(0, 0, 0, 0.05);
            transform: translateY(-2px);
        }

        .news-title {
            font-size: 16px;
            margin-bottom: 10px;
            font-weight: 500;
            height: 24px;
            width: 900px;
            float: left;
        }

        .news-title a {
            color: #333;
            text-decoration: none;
            transition: color 0.3s;
            position: relative;
            padding-left: 15px;
            display: block;
            line-height: 1.5;
            height: 24px;
            width: 900px;
            overflow: hidden;
            text-overflow: ellipsis;
            white-space: nowrap;
        }

        .news-title a:before {
            content: "";
            position: absolute;
            left: 0;
            top: 8px;
            width: 6px;
            height: 6px;
            background-color: #e60000;
            border-radius: 50%;
        }

        .news-title a:hover {
            color: #e60000;
        }

        .news-info {
            font-size: 13px;
            color: #888;
            display: flex;
            justify-content: space-between;
            align-items: center;
            padding-left: 15px;
            padding-right: 5px;
            width: 200px;
            float: right;
            height: 24px;
        }

        .news-date {
            margin-right: 20px;
            min-width: 150px;
            display: inline-block;
            width: 200px;
            float: right;
            height: 24px;
            text-align: right;
            line-height: 24px;
        }

        .news-author {
            display: inline-block;
            color: #666;
            max-width: 60%;
            overflow: hidden;
            text-overflow: ellipsis;
            white-space: nowrap;
        }

        /* 已移除阅读量图标样式 */

        /* 分页样式 */
        .pagination {
            text-align: center;
            margin-top: 20px;
        }

        .pagination a, .pagination span {
            display: inline-block;
            padding: 5px 10px;
            margin: 0 5px;
            border: 1px solid #ddd;
            color: #333;
            text-decoration: none;
            transition: all 0.3s;
            border-radius: 3px;
        }

        .pagination .current {
            background: linear-gradient(to right, #e60000, #ffcc00);
            color: #fff;
            border-color: #e60000;
        }

        .pagination a:hover {
            background-color: #f5f5f5;
            color: #e60000;
            border-color: #e60000;
        }

        .pagination .disabled {
            color: #999;
            background-color: #f5f5f5;
            cursor: not-allowed;
        }

        /* 页脚样式 */
        .footer {
            background-color: #e60000;
            color: #fff;
            padding: 10px 0;
            text-align: center;
            font-size: 12px;
            line-height: 30px;
            position: fixed;
            width: 100%;
            bottom: 0;
        }

        .footer p {
            margin: 0;
        }

        /* 悬浮返回按钮样式 */
        .float-button {
            position: fixed;
            right: 30px;
            bottom: 100px;
            width: 60px;
            height: 60px;
            background: linear-gradient(to right, #e60000, #ffcc00);
            border-radius: 50%;
            display: flex;
            flex-direction: column;
            align-items: center;
            justify-content: center;
            color: white;
            box-shadow: 0 4px 10px rgba(0, 0, 0, 0.2);
            cursor: pointer;
            z-index: 999;
            transition: all 0.3s ease;
        }

        .float-button:hover {
            transform: translateY(-5px);
            box-shadow: 0 6px 15px rgba(0, 0, 0, 0.3);
        }

        .float-button i {
            font-size: 24px;
            margin-bottom: 2px;
        }

        .float-button span {
            font-size: 12px;
            text-align: center;
        }
    </style>
</head>
<body>
<!--top-->
<div class="header">
    <div class="slideBanner"><img src='images/banner2.jpg' alt=""></div>
</div>
<!-- 导航栏已隐藏 -->


<!-- 内容区 -->
<div class="content-container">
    <!-- 移除多余的图片标签 -->
    <!-- 简介区域 -->
    <!--<div class="intro-section">
        <div class="intro-image"></div>
        <h2>锲而不舍落实中央八项规定精神</h2>
        <p>知敬畏、受警醒、明底线，深刻认识中央八项规定及其实施细则精神是不容突破的铁规矩、硬杠杠，以如磐恒心将作风建设进行到底。</p>
    </div>-->


    <!-- 功能入口 -->
    <div class="function-entries">
        <div class="function-entry directive active" data-type="directive"
             onclick="handleFunctionEntryClick(this); return false;">
            <i class="fas fa-file-alt"></i>
            <span>上级精神</span>
        </div>
        <div class="function-entry regulation" data-type="regulation"
             onclick="handleFunctionEntryClick(this); return false;">
            <i class="fas fa-gavel"></i>
            <span>纪法知识</span>
        </div>
        <div class="function-entry warning" data-type="warning" onclick="handleFunctionEntryClick(this); return false;">
            <i class="fas fa-bell"></i>
            <span>警示曝光</span>
        </div>
    </div>

    <!-- 新闻列表 -->
    <div class="news-section">
        <h2>上级精神</h2>
        <div class="news-list">
            <!-- 新闻列表将由JavaScript动态生成 -->
        </div>

        <!-- 分页已取消 -->
        <div id="pages" style="display:none;"></div>
    </div>
</div>

<!-- 悬浮返回按钮 -->
<div class="float-button" onclick="gotohome(); return false;">
    <i class="fas fa-home"></i>
    <span>返回首页</span>
</div>

<!--copy-->
<div class="footer">
    <p>&copy;版权所有 中国移动通信集团河南有限公司</p>
</div>
</body>
</html>
