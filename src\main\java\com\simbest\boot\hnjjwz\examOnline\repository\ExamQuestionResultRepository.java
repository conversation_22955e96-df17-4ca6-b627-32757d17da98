package com.simbest.boot.hnjjwz.examOnline.repository;

import com.simbest.boot.base.repository.LogicRepository;
import com.simbest.boot.hnjjwz.examOnline.model.ExamInfo;
import com.simbest.boot.hnjjwz.examOnline.model.ExamQuestionResult;
import org.springframework.data.domain.Page;
import org.springframework.data.domain.Pageable;
import org.springframework.data.jpa.repository.Query;
import org.springframework.data.repository.query.Param;

/**
 * <AUTHOR>
 * @Data 201/05/08
 * @Description 试卷内容
 */
public interface ExamQuestionResultRepository extends LogicRepository<ExamQuestionResult,String> {

    @Query(value = "select * from US_EXAM_QUESTION_RESULT t where t.publish_name=:truename and t.enabled=1",
            nativeQuery = true)
    Page<ExamQuestionResult> findOneselfDetails(@Param( "truename" ) String truename, Pageable pageable);
}
