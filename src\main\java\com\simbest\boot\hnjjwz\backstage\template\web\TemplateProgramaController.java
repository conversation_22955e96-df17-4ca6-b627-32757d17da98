package com.simbest.boot.hnjjwz.backstage.template.web;

import com.simbest.boot.base.web.controller.LogicController;
import com.simbest.boot.base.web.response.JsonResponse;
import com.simbest.boot.hnjjwz.backstage.template.model.TemplatePrograma;
import com.simbest.boot.hnjjwz.backstage.template.service.ITemplateProgramaService;
import io.swagger.annotations.Api;
import io.swagger.annotations.ApiImplicitParam;
import io.swagger.annotations.ApiImplicitParams;
import io.swagger.annotations.ApiOperation;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.data.domain.Pageable;
import org.springframework.web.bind.annotation.*;

import java.util.Map;

/**
 * <AUTHOR>
 * @Data 2019/05/06
 * @Description 模板与栏目的关系
 */
@Api(description = "模板与栏目的关系")
@Slf4j
@RestController
@RequestMapping(value = "/action/templatePrograma")
public class TemplateProgramaController extends LogicController<TemplatePrograma, String> {

    @Autowired
    private ITemplateProgramaService templateProgramaService;

    @Autowired
    public TemplateProgramaController(ITemplateProgramaService templateProgramaService) {
        super(templateProgramaService);
        this.templateProgramaService = templateProgramaService;
    }

    /**
     * 根据模板名称(模糊)、栏目编码(精确)、查询模板与栏目的列表并分页
     * @param page
     * @param size
     * @param direction
     * @param properties
     * @return
     */
    @ApiOperation (value = "查询模板与栏目的列表并分页", notes = "查询模板与栏目的列表并分页")
    @ApiImplicitParams ({ //
            @ApiImplicitParam (name = "page", value = "当前页码", dataType = "int", paramType = "query", //
                    required = true, example = "1"), //
            @ApiImplicitParam(name = "size", value = "每页数量", dataType = "int", paramType = "query", //
                    required = true, example = "10"), //
            @ApiImplicitParam(name = "direction", value = "排序规则（asc/desc）", dataType = "String", //
                    paramType = "query"), //
            @ApiImplicitParam(name = "properties", value = "排序规则（属性名称）", dataType = "String", //
                    paramType = "query") //
    })
    @PostMapping ({"/findAllDim","/findAllDim/sso"})
    public JsonResponse findAllDim( @RequestParam (required = false, defaultValue = "1") int page, //
                                 @RequestParam(required = false, defaultValue = "10") int size, //
                                 @RequestParam(required = false) String direction, //
                                 @RequestParam(required = false) String properties,
                                 @RequestBody (required = false) Map<String,Object> mapObject
    ) {
        Pageable pageable = templateProgramaService.getPageable(page, size, direction, properties);
        return templateProgramaService.findAllDim(mapObject,pageable);
    }

    /**
     * 根据id查询模板与栏目的关联
     * @param id
     * @return
     */
    @ApiOperation (value = "根据id查询模板与栏目的关联", notes = "根据id查询模板与栏目的关联")
    @ApiImplicitParam (name = "id", value = "主键id", dataType = "String", paramType = "query")
    @PostMapping ({"/findByIdDim","/findByIdDim/sso"})
    public JsonResponse findByIdDim(@RequestParam(required = false) String id) {
        return templateProgramaService.findByIdDim(id);
    }

}
