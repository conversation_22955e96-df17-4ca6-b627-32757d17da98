package com.simbest.boot.hnjjwz.backstage.template.service.impl;

import com.simbest.boot.base.service.impl.LogicService;
import com.simbest.boot.base.web.response.JsonResponse;
import com.simbest.boot.constants.ApplicationConstants;
import com.simbest.boot.hnjjwz.backstage.template.model.Template;
import com.simbest.boot.hnjjwz.backstage.template.repository.TemplateRepository;
import com.simbest.boot.hnjjwz.backstage.template.service.ITemplateService;
import com.simbest.boot.hnjjwz.constants.Constants;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.data.domain.Page;
import org.springframework.data.domain.Pageable;
import org.springframework.stereotype.Service;
import org.springframework.util.StringUtils;

import java.util.ArrayList;
import java.util.List;
import java.util.Map;

/**
 * <AUTHOR>
 * @Data 2019/04/02
 * @Description
 */
@Slf4j
@Service
public class TemplateServiceImpl extends LogicService<Template,String> implements ITemplateService {

    private TemplateRepository templateRepository;

    @Autowired
    public TemplateServiceImpl ( TemplateRepository templateRepository) {
        super(templateRepository);
        this.templateRepository = templateRepository;
    }

    /**
     * 获取可以使用的模板，如果有一个则只取一个
     * @return
     */
    @Override
    public JsonResponse getTemplateUse ( ) {
        List<Template> templateSet = templateRepository.findByTemplateUse(true);
        if(templateSet != null){
            return JsonResponse.success( templateSet.get( 0 ) );
        }
        return JsonResponse.success( null );
    }

    /**
     * 根据模板名称(模糊)、是否使用此模板(精确)
     * @param mapObject
     * @param pageable
     * @return
     */
    @Override
    public JsonResponse findAllDim ( Map<String, Object> mapObject, Pageable pageable ) {
        String  templateName = (String)mapObject.get( Constants.TEMPLATE_NAME_KEY );
        String  templateUse = (String)mapObject.get( Constants.TEMPLATE_USE_KEY );
        List<Boolean> templateUseList = new ArrayList<>(  );
        if( StringUtils.isEmpty( templateName )){
            templateName = ApplicationConstants.EMPTY;
        }
        if( StringUtils.isEmpty( templateUse ) ){
            templateUseList.add( true );
            templateUseList.add( false );
        }else{
            if("true".equals( templateUse )){
                templateUseList.add( true );
            }
            if("false".equals( templateUse )){
                templateUseList.add( false );
            }
        }
        Page<Template> templatePage = templateRepository.findAllDim( templateName,templateUseList, pageable);
        return JsonResponse.success( templatePage );
    }
}
