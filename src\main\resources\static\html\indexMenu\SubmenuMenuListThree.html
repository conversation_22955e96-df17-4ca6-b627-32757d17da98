<!DOCTYPE html>
<html xmlns="http://www.w3.org/1999/xhtml"
      xmlns:th="http://www.thymeleaf.org">
<head>
    <title>菜单树</title>
    <meta http-equiv="Content-Type" content="text/html; charset=utf-8"/>
    <meta http-equiv="X-UA-Compatible" content="IE=edge" />
    <meta name="viewport" content="width=device-width, initial-scale=1, shrink-to-fit=no"/>
    <!--
        <link href="http://************:8088/simbestui/fonts/iconfont/iconfont.css?v=svn.revision" th:href="@{http://************:8088/simbestui/fonts/iconfont/iconfont.css?v=svn.revision}" rel="stylesheet"/>
        -->
    <link href="../../fonts/iconfont/iconfont.css?v=svn.revision" th:href="@{/fonts/iconfont/iconfont.css?v=svn.revision}" rel="stylesheet"/>
    <link href="http://************:8088/simbestui/js/themes/default/easyui.css?v=svn.revision" th:href="@{http://************:8088/simbestui/js/themes/default/easyui.css?v=svn.revision}" rel="stylesheet"/>
    <link href="http://************:8088/simbestui/js/themes/icon.css?v=svn.revision" th:href="@{http://************:8088/simbestui/js/themes/icon.css?v=svn.revision}" rel="stylesheet"/>
    <link href="http://************:8088/simbestui/css/public.css?v=svn.revision" th:href="@{http://************:8088/simbestui/css/public.css?v=svn.revision}" rel="stylesheet"/>
    <link href="http://************:8088/simbestui/css/pages.css?v=svn.revision" th:href="@{http://************:8088/simbestui/css/pages.css?v=svn.revision}" rel="stylesheet"/>
    <script src="http://************:8088/simbestui/js/jquery.min.js?v=svn.revision" th:src="@{http://************:8088/simbestui/js/jquery.min.js?v=svn.revision}" type="text/javascript"></script>
    <script src="http://************:8088/simbestui/js/jquery.easyui.min.js?v=svn.revision" th:src="@{http://************:8088/simbestui/js/jquery.easyui.min.js?v=svn.revision}" type="text/javascript"></script>
    <script src="http://************:8088/simbestui/js/easyui-lang-zh_CN.js?v=svn.revision" th:src="@{http://************:8088/simbestui/js/easyui-lang-zh_CN.js?v=svn.revision}" type="text/javascript"></script>
    <script src="../../js/jquery.config.js?v=svn.revision" th:src="@{/js/jquery.config.js?v=svn.revision}" type="text/javascript"></script>
    <script src="http://************:8088/simbestui/js/jquery.zjsfile.js?v=svn.revision" th:src="@{http://************:8088/simbestui/js/jquery.zjsfile.js?v=svn.revision}" type="text/javascript"></script>
    <script src="http://************:8088/simbestui/js/jquery.aduq.js?v=svn.revision" th:src="@{http://************:8088/simbestui/js/jquery.aduq.js?v=svn.revision}" type="text/javascript"></script>
    <script src="http://************:8088/simbestui/js/jquery.process.js?v=svn.revision" th:src="@{http://************:8088/simbestui/js/jquery.process.js?v=svn.revision}" type="text/javascript"></script>
</head>
<body class="page_body">
<ul id="programmaTree"></ul>
<div class="role orgC"></div>
<script type="text/javascript">
    var gps=getQueryString();
    $(function(){
        $("#programmaTree").tree({
            url:web.rootdir+"action/indexMenu/findRootAndNext",
            checkbox:true,//是否在每一个借点之前都显示复选框
            lines:true,//是否显示树控件上的虚线
            treeId:'id',
            treePid:'id',
            cascadeCheck:false,
            onlyone:gps.multi==0?true:false,//不要乱配
            fileds:'id|id,menuName|text,id,menuName',
            animate:true,//节点在展开或折叠的时候是否显示动画效果
            onClick:function(node){
                //console.log(node.data);
                if(node.children){
                    if(node.children.length==0) top.mesAlert("提示信息","该栏目无下级栏目！", 'info');
                }else{
                    /*ajaxgeneral({
                        url:"action/programaInfo/findSonByParentCode",
                        data:{"parentProgramaCode":node.id},
                        success:function(data){
                            if(data.data.length==0 ){
                                top.mesAlert("提示信息","该栏目无下级栏目！", 'info');
                            }else{
                                var datas=[];
                                for(var i in data.data){
                                    var datai={};
                                    datai.id=data.data[i].programaCode;
                                    datai.text=data.data[i].programaName;
                                    datai.parentProgramaCode=data.data[i].parentProgramaCode;
                                    datai.programaDisplayName=data.data[i].programaDisplayName;
                                    datas.push(datai);
                                }
                                $("#programmaTree").tree("append", {
                                    parent : node.target,
                                    data : datas
                                });
                                treeLoadSuccess();
                            }
                        }
                    });*/
                }
            },
            onLoadSuccess:function(node, data){
                treeLoadSuccess();
            },
            onBeforeCheck:function(node,checked,a){
                //console.log(gps.multi);
                if(gps.multi==0){
                    var nodes=$("#programmaTree").tree("getChecked");
                    for(var i in nodes){
                        //var nodei=$("#orgTree").tree("find",nodes[i].id);
                        $("#programmaTree").tree("uncheck",nodes[i].target);
                    }
                    $(".role").html("");
                }
            },
            onCheck:function(node,checked){
                if(checked){
                    if($(".role a#"+node.id).length==0) $(".role").append("<a id='"+node.id+"'><font>"+node.menuName+"</font><i class='iconfont fr'>&#xe6ef;</i></a>");
                }else{
                    $(".role a#"+node.id).remove();
                }
            }
        });
        //删除已选
        $(document).on("click",".role a i",function(){
            var id=$(this).parent().attr("id");
            $(this).parent().remove();
            var nodei=$("#programmaTree").tree("find",id);
            if(nodei) $("#programmaTree").tree("uncheck",nodei.target);
        });
    });
    //数据加载成功
    function treeLoadSuccess(){
        var chooseRow=top.chooseWeb[gps.name]?top.chooseWeb[gps.name].data:[];

        //console.log(chooseRow);

        for(var i in chooseRow){
            if(chooseRow[i].id) chooseRow[i].id=chooseRow[i].id;
            if(chooseRow[i].name) chooseRow[i].menuName=chooseRow[i].menuName;
            var nodei=$("#programmaTree").tree("find",chooseRow[i].id);
            if(nodei) $("#programmaTree").tree("check",nodei.target);
            else
            if($(".role a#"+chooseRow[i].orgCode).length==0) $(".role").append("<a id='"+chooseRow[i].id+"'><font>"+chooseRow[i].menuName+"</font><i class='iconfont fr'>&#xe6ef;</i></a>");
        }
    };
    window.getchoosedata=function(){
        var datas=[];
        $(".role a").each(function(i,v){
            var data={};
            data.id=$(v).attr("id");
            data.menuName=$(v).children("font").html();
            datas.push(data);
        });
        return {"data":datas,"state":1};//state一般用于是否必选(必选时state为１表示选择有内容，为０表示没有选择，可以return {"data":[],"state":0};之前加弹出提示)因为当前不是必选所以state为１
    };
</script>
</body>
</html>
