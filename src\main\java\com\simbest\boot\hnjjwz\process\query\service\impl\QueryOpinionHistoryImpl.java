package com.simbest.boot.hnjjwz.process.query.service.impl;

import com.google.common.collect.Maps;
import com.simbest.boot.base.exception.Exceptions;
import com.simbest.boot.base.repository.LogicRepository;
import com.simbest.boot.base.service.impl.LogicService;
import com.simbest.boot.base.web.response.JsonResponse;
import com.simbest.boot.bps.process.listener.model.WfOptMsgModel;
import com.simbest.boot.hnjjwz.process.query.service.IQueryOpinionHistoryService;
import com.simbest.boot.hnjjwz.util.OperateLogTool;
import com.simbest.boot.sys.model.SysOperateLog;
import com.simbest.boot.sys.service.ISysOperateLogService;
import com.simbest.boot.util.security.SecurityUtils;
import com.simbest.boot.wf.process.service.IWfOptMsgService;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;

import java.util.LinkedHashMap;
import java.util.Map;

/**
 * <AUTHOR>
 * @Data 2018/06/12
 * @Description 流程意见
 */
@Slf4j
@Service(value = "queryOpinionHistory")
public class QueryOpinionHistoryImpl extends LogicService<WfOptMsgModel,String> implements IQueryOpinionHistoryService {

    @Autowired
    private OperateLogTool operateLogTool;

    @Autowired
    private IWfOptMsgService wfOptMsgService;

    @Autowired
    private ISysOperateLogService operateLogService;

    private final String param1 = "/action/queryOpinionHistory";

    public QueryOpinionHistoryImpl(LogicRepository<WfOptMsgModel, String> logicRepository) {
        super(logicRepository);
    }

    /**
     * 查询工单审批意见
     * @param processInstId
     * @return
     */
    @Override
    public JsonResponse getWfOptMags( Long processInstId, String source, String currentUser) {
        if(source==null && !("PC".equals( source )||"MOBILE".equals( source ))){
            source = "PC";
        }
        /**准备操作数据**/
        SysOperateLog operateLog = new SysOperateLog();
        String param2 = "/getWfOptMags";
        String params = "processInstId="+processInstId+",source="+source+",userCode"+currentUser;
        operateLog.setInterfaceParam( params );
        Map<String,String> paramMap = Maps.newHashMap();
        Map map = new LinkedHashMap<>(  );
        try{
            /**判断是否是从手机端 true是，false否**/
            JsonResponse returnObj = operateLogTool.operationSource( source, currentUser, param1, param2, operateLog);
            if ( returnObj != null ){
                return returnObj;
            }
            map.put("processInsId",processInstId  );
            map.put( "currentUser", SecurityUtils.getCurrentUserName() );
        }catch ( Exception e ){
            operateLog.setErrorMsg( e.toString() );
            Exceptions.printException( e );
            return JsonResponse.success( null,"获取审批意见失败！" );
        }finally {
            operateLogService.saveLog( operateLog );
            return  JsonResponse.success(wfOptMsgService.queryProcessOptMsgDataMap( map ),"获取审批意见成功！"  );
        }

    }


}
