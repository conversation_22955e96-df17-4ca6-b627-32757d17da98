package com.simbest.boot.hnjjwz.process.wf.model;

import com.simbest.boot.base.annotations.EntityIdPrefix;
import com.simbest.boot.cmcc.wf.model.WfFormModel;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.*;
import org.hibernate.annotations.GenericGenerator;

import javax.persistence.*;

/**
 * <strong>Title : </strong><br>
 * <strong>Description : </strong><br>
 * <strong>Create on : 2018/6/19</strong><br>
 * <strong>Modify on : 2018/6/19</strong><br>
 * <strong>Copyright (C) Ltd.</strong><br>
 *
 * @<NAME_EMAIL>
 * @version <strong>V1.0.0</strong><br>
 * <strong>修改历史:</strong><br>
 * 修改人 修改日期 修改描述<br>
 * -------------------------------------------<br>
 */



@Data
@EqualsAndHashCode (callSuper = true)
@NoArgsConstructor
@AllArgsConstructor
@Entity(name = "us_pm_instence")
@ApiModel (value = "主单据")
public class PmInstence extends WfFormModel {

    @Id
    @Column(name = "id", length = 40)
    @GeneratedValue(generator = "snowFlakeId")
    @GenericGenerator (name = "snowFlakeId", strategy = "com.simbest.boot.util.distribution.id.SnowflakeId")
    @EntityIdPrefix (prefix = "U") //主键前缀，此为可选项注解
    private String id;

    @Setter
    @Getter
    @Column(name = "pmInsId",nullable = true, length = 100)
    @ApiModelProperty (value = "业务实例ID", required = true)
    protected String pmInsId;

    @Setter
    @Getter
    @Column(name = "pmInsType",nullable = false,  length = 100)
    @ApiModelProperty(value = "业务流程类型", required = true)
    private String pmInsType;

    @Setter
    @Getter
    @Column(name = "pmInsTitle",nullable = true,  length = 100)
    @ApiModelProperty (value = "标题", required = true)
    private String pmInsTitle;

    @Setter
    @Getter
    @Column(name = "pmInsCreatorCode",nullable = true,  length = 100)
    @ApiModelProperty (value = "创建人ID", required = true)
    private String pmInsCreatorCode;

    @Setter
    @Getter
    @Column(name = "pmInsCreatorName",nullable = true,  length = 100)
    @ApiModelProperty (value = "创建人名称", required = true)
    private String pmInsCreatorName;

    @Setter
    @Getter
    @Column(name = "pmInsCurrentActivity",nullable = true,  length = 100)
    @ApiModelProperty (value = "当前环节", required = true)
    private String pmInsCurrentActivity;

    @Setter
    @Getter
    @Column(name = "pmInsFinishFlag",nullable = true,  length = 100)
    @ApiModelProperty (value = "结束标记（0：未结束、1：正常结束、11：强制结束、-11：被废除）", required = true)
    private String pmInsFinishFlag;

    @Setter
    @Getter
    @Column(name = "pmInsState",nullable = true, length = 100)
    @ApiModelProperty (value = "实例状态", required = true)
    private String pmInsState;

    @Setter
    @Getter
    @Column(name = "processDefName",nullable = true,  length = 100)
    @ApiModelProperty (value = "流程定义id", required = true)
    private String processDefName;
}

