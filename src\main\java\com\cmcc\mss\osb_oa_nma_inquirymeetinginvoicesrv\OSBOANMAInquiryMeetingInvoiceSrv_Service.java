package com.cmcc.mss.osb_oa_nma_inquirymeetinginvoicesrv;

import java.net.URL;
import javax.xml.namespace.QName;
import javax.xml.ws.WebEndpoint;
import javax.xml.ws.WebServiceClient;
import javax.xml.ws.WebServiceFeature;
import javax.xml.ws.Service;

/**
 * This class was generated by Apache CXF 3.2.4
 * 2018-06-26T16:00:14.621+08:00
 * Generated source version: 3.2.4
 *
 */
@WebServiceClient(name = "OSB_OA_NMA_InquiryMeetingInvoiceSrv",
                  wsdlLocation = "classpath:wsdl/OSB_OA_NMA_InquiryMeetingInvoiceSrv/OSB_OA_NMA_InquiryMeetingInvoiceSrv.WSDL",
                  targetNamespace = "http://mss.cmcc.com/OSB_OA_NMA_InquiryMeetingInvoiceSrv")
public class OSBOANMAInquiryMeetingInvoiceSrv_Service extends Service {

    public final static URL WSDL_LOCATION;

    public final static QName SERVICE = new QName("http://mss.cmcc.com/OSB_OA_NMA_InquiryMeetingInvoiceSrv", "OSB_OA_NMA_InquiryMeetingInvoiceSrv");
    public final static QName OSBOANMAInquiryMeetingInvoiceSrvPort = new QName("http://mss.cmcc.com/OSB_OA_NMA_InquiryMeetingInvoiceSrv", "OSB_OA_NMA_InquiryMeetingInvoiceSrvPort");
    static {
        URL url = OSBOANMAInquiryMeetingInvoiceSrv_Service.class.getClassLoader().getResource("wsdl/OSB_OA_NMA_InquiryMeetingInvoiceSrv/OSB_OA_NMA_InquiryMeetingInvoiceSrv.WSDL");
        if (url == null) {
            java.util.logging.Logger.getLogger(OSBOANMAInquiryMeetingInvoiceSrv_Service.class.getName())
                .log(java.util.logging.Level.INFO,
                     "Can not initialize the default wsdl from {0}", "classpath:wsdl/OSB_OA_NMA_InquiryMeetingInvoiceSrv/OSB_OA_NMA_InquiryMeetingInvoiceSrv.WSDL");
        }
        WSDL_LOCATION = url;
    }

    public OSBOANMAInquiryMeetingInvoiceSrv_Service(URL wsdlLocation) {
        super(wsdlLocation, SERVICE);
    }

    public OSBOANMAInquiryMeetingInvoiceSrv_Service(URL wsdlLocation, QName serviceName) {
        super(wsdlLocation, serviceName);
    }

    public OSBOANMAInquiryMeetingInvoiceSrv_Service() {
        super(WSDL_LOCATION, SERVICE);
    }

    public OSBOANMAInquiryMeetingInvoiceSrv_Service(WebServiceFeature ... features) {
        super(WSDL_LOCATION, SERVICE, features);
    }

    public OSBOANMAInquiryMeetingInvoiceSrv_Service(URL wsdlLocation, WebServiceFeature ... features) {
        super(wsdlLocation, SERVICE, features);
    }

    public OSBOANMAInquiryMeetingInvoiceSrv_Service(URL wsdlLocation, QName serviceName, WebServiceFeature ... features) {
        super(wsdlLocation, serviceName, features);
    }




    /**
     *
     * @return
     *     returns OSBOANMAInquiryMeetingInvoiceSrv
     */
    @WebEndpoint(name = "OSB_OA_NMA_InquiryMeetingInvoiceSrvPort")
    public OSBOANMAInquiryMeetingInvoiceSrv getOSBOANMAInquiryMeetingInvoiceSrvPort() {
        return super.getPort(OSBOANMAInquiryMeetingInvoiceSrvPort, OSBOANMAInquiryMeetingInvoiceSrv.class);
    }

    /**
     *
     * @param features
     *     A list of {@link javax.xml.ws.WebServiceFeature} to configure on the proxy.  Supported features not in the <code>features</code> parameter will have their default values.
     * @return
     *     returns OSBOANMAInquiryMeetingInvoiceSrv
     */
    @WebEndpoint(name = "OSB_OA_NMA_InquiryMeetingInvoiceSrvPort")
    public OSBOANMAInquiryMeetingInvoiceSrv getOSBOANMAInquiryMeetingInvoiceSrvPort(WebServiceFeature... features) {
        return super.getPort(OSBOANMAInquiryMeetingInvoiceSrvPort, OSBOANMAInquiryMeetingInvoiceSrv.class, features);
    }

}
