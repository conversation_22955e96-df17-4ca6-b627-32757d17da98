package com.simbest.boot.hnjjwz.column.familyWind.model;/**
 * Created by KZH on 2019/7/30 17:16.
 */

import com.simbest.boot.base.annotations.EntityIdPrefix;
import com.simbest.boot.base.model.LogicModel;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.AllArgsConstructor;
import lombok.Data;
import lombok.EqualsAndHashCode;
import lombok.NoArgsConstructor;
import org.hibernate.annotations.GenericGenerator;

import javax.persistence.*;

/**
 * <AUTHOR>
 * @create 2019-07-30 17:16
 * @desc 家风栏目数据实体
 **/
@Data
@EqualsAndHashCode(callSuper = true)
@NoArgsConstructor
@AllArgsConstructor
@Entity(name = "us_family_wind")
@ApiModel(value = "家风栏目")
public class FamilyWind extends LogicModel {

    @Id
    @Column(name = "id", length = 40)
    @GeneratedValue(generator = "snowFlakeId")
    @GenericGenerator(name = "snowFlakeId", strategy = "com.simbest.boot.util.distribution.id.SnowflakeId")
    @EntityIdPrefix(prefix = "FW") //主键前缀，此为可选项注解
    private String id;

    @Column(length = 40)
    @ApiModelProperty(value = "数据类型")
    private String familyType;

    @Column(columnDefinition="int default 0")
    @ApiModelProperty(value = "数据点击量")
    private Integer clickQuantity;

    @Column(columnDefinition="int default 0")
    @ApiModelProperty(value = "数据投票量")
    private Integer voteQuantity;

    @Column(length = 250)
    @ApiModelProperty(value = "数据列表标题")
    private String familyTitle;

    @Column(length = 2000)
    @ApiModelProperty(value = "简介")
    private String about;

    @Lob
    @Basic(fetch = FetchType.LAZY)
    @ApiModelProperty(value = "数据正文")
    private String mainBody;

    @Column(length = 250)
    @ApiModelProperty(value = "数据加载地址")
    private String loadingUrl;

    @Column(length = 40)
    @ApiModelProperty(value = "公司")
    private String company;

    @Column(length = 40)
    @ApiModelProperty(value = "人")
    private String people;

    @Column(length = 250)
    @ApiModelProperty(value = "数据预览地址")
    private String previewUrl;

    @Transient
    private Boolean vote = true;//可投不可投状态


}
