package com.simbest.boot.hnjjwz.backstage.indexMenu.model;

import com.simbest.boot.base.annotations.EntityIdPrefix;
import com.simbest.boot.base.model.LogicModel;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.*;
import org.hibernate.annotations.GenericGenerator;

import javax.persistence.*;

/**
 * <AUTHOR>
 * @Data 2019/04/01
 * Description 首页菜单
 */
@Data
@EqualsAndHashCode(callSuper = true)
@NoArgsConstructor
@AllArgsConstructor
@Entity(name = "us_index_menu")
@ApiModel(value = "首页菜单")
public class IndexMenu extends LogicModel {
    @Id
    @Column(name = "id", length = 40)
    @GeneratedValue(generator = "snowFlakeId")
    @GenericGenerator (name = "snowFlakeId", strategy = "com.simbest.boot.util.distribution.id.SnowflakeId")
    @EntityIdPrefix (prefix = "IM") //主键前缀，此为可选项注解
    private String id;

    @Column(length = 250)
    @Setter
    @Getter
    @ApiModelProperty(value = "连接地址", required = true)
    private String menuUrl;

    @Column(length = 250)
    @Setter
    @Getter
    @ApiModelProperty(value = "地址类型，是外部类型(连接到外部网址)，内部类型(连接到内部网址)", required = true)
    private String menuUrlType;

    @Transient
    private String menuUrlTypeName;

    @Column(length = 250)
    @Setter
    @Getter
    @ApiModelProperty(value = "父菜单名", required = true)
    private String menuName;

    @Column(length = 250)
    @Setter
    @Getter
    @ApiModelProperty(value = "额外字段1", required = true)
    private String spare1;

    @Column(length = 250)
    @Setter
    @Getter
    @ApiModelProperty(value = "额外字段2", required = true)
    private String spare2;

    @Column(length = 250)
    @Setter
    @Getter
    @ApiModelProperty(value = "额外字段3", required = true)
    private String spare3;

}
