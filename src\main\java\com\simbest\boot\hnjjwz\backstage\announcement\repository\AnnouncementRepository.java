package com.simbest.boot.hnjjwz.backstage.announcement.repository;

import com.simbest.boot.base.repository.LogicRepository;
import com.simbest.boot.hnjjwz.backstage.announcement.model.Announcement;
import com.simbest.boot.hnjjwz.process.wf.model.ProgramaDataForm;
import org.springframework.data.domain.Page;
import org.springframework.data.domain.Pageable;
import org.springframework.data.jpa.repository.Modifying;
import org.springframework.data.jpa.repository.Query;
import org.springframework.data.repository.query.Param;
import org.springframework.transaction.annotation.Transactional;

import java.util.List;
import java.util.Map;

/**
 * <AUTHOR>
 * @Data 201/05/06
 * @Description 公告
 */
public interface AnnouncementRepository extends LogicRepository<Announcement,String> {

    /**
     * 根据公告标题(模糊)、发布人姓名(模糊)、是否显示在首页公告滚动处(精确)
     * @param announcementTitle
     * @param creator
     * @param isDisplayList
     * @return
     */
    @Query (value =  "select * " +
            " FROM us_announcement ua" +
            " WHERE ua.announcement_title like concat( concat('%',:announcementTitle),'%') " +
            " AND ua.creator like concat( concat('%',:creator),'%') " +
            " AND ua.is_display IN (:isDisplay) " +
            " AND ua.enabled = 1 AND ua.removed_time IS NULL",
            countQuery = "SELECT COUNT(*)" +
                    " FROM us_announcement ua" +
                    " WHERE ua.announcement_title like concat( concat('%',:announcementTitle),'%') " +
                    " AND ua.creator like concat( concat('%',:publishName),'%') " +
                    " AND ua.is_display IN (:isDisplay) " +
                    " AND ua.enabled = 1 AND ua.removed_time IS NULL",
            nativeQuery = true)
    Page<Announcement> findAllDim( @Param ( "announcementTitle" ) String announcementTitle, @Param ( "creator" ) String  creator, @Param ( "isDisplay" ) List<Boolean> isDisplayList, Pageable pageable );

/*    *//**
     * 根据用户名查询
     * @param username
     * @return
     *//*
    @Query (value =  "select vuoo.* " +
            " FROM us_announcement ua,uums.V_USER_ORG_ONLY vuoo " +
            " WHERE ua.creator = :username " +
            " AND ua.creator = vuoo.username " +
            " AND ua.enabled = 1 AND ua.removed_time IS NULL ",
            nativeQuery = true)
    Map<String,Object> findFromUsername( @Param ( "username" ) String username );*/


    @Query(value = "select t.*" +
            "  from (select t.*" +
            "  from us_announcement t" +
            " where t.IS_PUBLISH = 1" +
            "   and t.enabled = 1" +
            "   and rownum <= 5" +
            " order by t.creation_time desc) t  order by t.stick_flag desc",
            nativeQuery = true)
    List<Announcement> findAnnouncement();

    @Transactional
    @Query(
            value = "select a.* from us_announcement a where a.enabled = 1  and a.id=:id",//and a.pm_ins_id = p.pm_ins_id
            nativeQuery = true
    )
    Announcement getApprovalFromDetail(@Param("id") String id);


    @Query(
            value = "select a.* from us_announcement a where a.enabled = 1  and a.pm_ins_id=:pmInstId ",
            nativeQuery = true
    )
    Announcement getApprovalFromDetailFromInstId(@Param("pmInstId") String pmInstId);

/*    *//**
     * 查询当前登录人起草审批的的报送
     * @param trueName
     * @param pageable
     * @return
     *//*
    @Query(
            value = "select t.id,t,truename," +
                    " pdi.belong_department_name,pdi.programa_display_name, " +
                    " t.created_time,t.belong_company_code," +
                    " t.belong_company_type_dict_value,t.belong_department_code," +
                    " t.belong_company_name,t.announcement_accessory_id," +
                    " t.announcement_title,t.creation_time," +
                    " t.pm_ins_id,t.programa_data_relation," +
                    " t.programa_data_ttitle, vuoo.displayname as belongDepartmentDisplayName" +
                    "  from us_announcement t, uums.v_org vuoo" +
                    " WHERE t.username = :trueName" +
                    "   AND t.belong_department_code = vuoo.orgcode" +
                    "   AND t.enabled = 1 order by t.creation_time desc",
            countQuery = "select count(*)" +
                    "  from us_announcement t, uums.v_org vuoo" +
                    " WHERE t.username = :trueName" +
                    "   AND t.belong_department_code = vuoo.orgcode" +
                    "   AND t.enabled = 1 order by t.creation_time desc",
            nativeQuery = true
    )
    Page<Map<String,Object>> findUserNameDetailList(@Param ("trueName")String trueName,Pageable pageable);*/


    /**
     * 查看栏目列表，选择了栏目时
     * @param title
     * @param pageable
     * @return
     */
    @Query (value =  " select pdi.id,pdi.truename, " +
            " pdi.belong_department_name,pdi.programa_display_name, " +
            " pdi.created_time,pdi.belong_company_code," +
            " pdi.belong_company_type_dict_value,pdi.belong_department_code," +
            " pdi.belong_company_name,pdi.announcement_accessory_id," +
            " pdi.announcement_title,pdi.creation_time,pdi.pm_ins_id," +
            " pdi.programa_data_relation,pdi.programa_data_ttitle" +
            " from us_announcement pdi" +
            " WHERE pdi.announcement_coding IN(:programaCodeList) " +
            " AND pdi.announcement_title LIKE concat( concat('%',:title),'%') " +
            " AND pdi.enabled=1 AND pdi.removed_time IS NULL " +
            " AND pdi.is_publish = 1 order by pdi.creation_time desc",
            countQuery = "SELECT count(*) " +
                    " from us_announcement pdi " +
                    " WHERE pdi.announcement_coding IN(:programaCodeList) " +
                    " AND pdi.announcement_title LIKE concat( concat('%',:title),'%') " +
                    " AND pdi.enabled=1 AND pdi.removed_time IS NULL " +
                    " AND pdi.is_publish = 1 order by pdi.creation_time desc",
            nativeQuery = true)
    Page<Map<String,Object>> findDataDetailList( @Param( "programaCodeList")List<String> programaCodeList, @Param ( "title" ) String title, Pageable pageable );


    /**
     * 查看栏目列表
     * @param
     * @param pageable
     * @return
     */
    @Query (value =  " select pdi.id,pdi.truename," +
            " pdi.belong_department_name,pdi.programa_display_name, " +
            " pdi.created_time,pdi.belong_company_code, " +
            " pdi.belong_company_type_dict_value,pdi.belong_department_code, " +
            " pdi.belong_company_name,pdi.announcement_accessory_id, " +
            " pdi.announcement_title,pdi.creation_time," +
            " pdi.pm_ins_id,pdi.programa_data_relation," +
            " pdi.programa_data_ttitle" +
            " from us_announcement pdi" +
            " WHERE pdi.enabled=1 AND pdi.removed_time IS NULL " +
            " AND pdi.is_publish = 1 order by pdi.creation_time desc ",
            countQuery = "SELECT count(*) " +
                    " from us_announcement pdi" +
                    " WHERE pdi.enabled=1 AND pdi.removed_time IS NULL " +
                    "AND pdi.is_publish = 1 order by pdi.creation_time desc",
            nativeQuery = true)
    Page<Map<String,Object>> findDataDetailListNoCode( Pageable pageable );

/*    *//**
     * 查询当前登录人审批过的的报送
     * @param trueName
     * @param pageable
     * @return
     *//*
    @Query(
            value = "select t.id,t.truename," +
                    " t.belong_department_name,t.programa_display_name, " +
                    " t.created_time,t.belong_company_code," +
                    " t.belong_company_type_dict_value," +
                    " t.belong_department_code,t.belong_company_name," +
                    " t.announcement_accessory_id,t.announcement_title," +
                    " t.creation_time,t.pm_ins_id,t.programa_data_relation," +
                    " t.programa_data_ttitle, vuoo.displayname as belongDepartmentDisplayName" +
                    "  from us_announcement t, uums.v_org vuoo, WF_WORKITEM_MODEL wf  " +
                    "  where  wf.assistant= :trueName" +
                    "  and t.pm_ins_id=wf.receipt_code"+
                    "  and t.belong_department_code = vuoo.orgcode" +
                    "  and t.enabled = 1 ",
            countQuery = "select count(*)" +
                    "  from us_announcement t, uums.v_org vuoo, WF_WORKITEM_MODEL wf  " +
                    "  where  wf.assistant= :trueName"+
                    "  and t.pm_ins_id=wf.receipt_code"+
                    "  and t.belong_department_code = vuoo.orgcode"+
                    "  and t.enabled = 1 ",
            nativeQuery = true
    )
    Page<Map<String,Object>> findUserNameApprovalDetailList(@Param ("trueName")String trueName,Pageable pageable);*/


    /**
     * 根据Id获取实体
     * @param
     * @return
     */
    @Query(value = "select  t.* from us_announcement t where  t.id=:id and t.IS_PUBLISH=1  and t.enabled=1",
            nativeQuery = true)
    Announcement findAnnouncementId(@Param ( "id" )String id);

    /**
     * 倒叙取第一个
     * @param
     * @return
     */
    @Query(value = "select t.* from (select t.* from us_announcement t where t.IS_PUBLISH=1  and t.enabled=1 order by t.stick_flag  desc ) t  where ROWNUM <=1",
            nativeQuery = true)
    Announcement findFlashback();

    @Transactional
    @Modifying
    @Query(value = "update us_announcement set enabled=0 where   pm_ins_id=:pmInsId",
            nativeQuery = true)
    void deleteByPmInsId(@Param ( "pmInsId" )String pmInsId);

    @Query(value = "   SELECT" +
            "    *" +
            "  FROM" +
            "    (" +
            "      SELECT" +
            "        a.*" +
            "      FROM" +
            "        US_ANNOUNCEMENT a" +
            "      WHERE" +
            "        a.ENABLED = 1" +
            "        AND a.IS_PUBLISH = 1" +
            "        AND a.START_TIME <= TO_CHAR(SYSDATE, 'yyyy-MM-dd HH24:mi:ss')" +
            "        AND a.END_TIME > TO_CHAR(SYSDATE, 'yyyy-MM-dd HH24:mi:ss')" +
            "      ORDER BY" +
            "        STICK_FLAG DESC," +
            "        DISPLAY_ORDER ASC," +
            "        CREATED_TIME DESC" +
            "    )" +
            "  WHERE" +
            "    ROWNUM <= 8",nativeQuery = true)
    List<Announcement> getAnnouncementArticleList();

    @Query(value = "      SELECT" +
            "        a.*" +
            "      FROM" +
            "        US_ANNOUNCEMENT a" +
            "      WHERE" +
            "        a.ENABLED = 1" +
            "        AND a.IS_PUBLISH = 1" +
            "        AND a.START_TIME <= TO_CHAR(SYSDATE, 'yyyy-MM-dd HH24:mi:ss')" +
            "        AND a.END_TIME > TO_CHAR(SYSDATE, 'yyyy-MM-dd HH24:mi:ss')" +
            "      ORDER BY" +
            "        STICK_FLAG DESC," +
            "        DISPLAY_ORDER ASC," +
            "        CREATED_TIME DESC",
            countQuery = "      SELECT count(*) FROM" +
                    "        US_ANNOUNCEMENT a" +
                    "      WHERE" +
                    "        a.ENABLED = 1" +
                    "        AND a.IS_PUBLISH = 1" +
                    "        AND a.START_TIME <= TO_CHAR(SYSDATE, 'yyyy-MM-dd HH24:mi:ss')" +
                    "        AND a.END_TIME > TO_CHAR(SYSDATE, 'yyyy-MM-dd HH24:mi:ss')",
            nativeQuery = true)
    Page<Announcement> findArticlePageByColumnId(Pageable pageable);
}
