package com.simbest.boot.hnjjwz.util;

import com.simbest.boot.base.web.response.JsonResponse;
import com.simbest.boot.config.AppConfig;
import com.simbest.boot.hnjjwz.constants.Constants;
import com.simbest.boot.security.IUser;
import com.simbest.boot.util.encrypt.RsaEncryptor;
import com.simbest.boot.util.http.client.HttpClient;
import com.simbest.boot.util.json.JacksonUtils;
import com.simbest.boot.util.security.SecurityUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Component;

import java.util.List;
import java.util.Map;

@Component
public class UumsQuerySqlUtil {

    @Autowired
    private AppConfig config;

    @Autowired
    private RsaEncryptor encryptor;

    public List<Map<String, Object>> uumsSelectBySql(Map<String, String> map, String sqlId) {
        IUser user = SecurityUtils.getCurrentUser();//获取当前人员
        JsonResponse jsonResponse = HttpClient.textBody(config.getUumsAddress() + "/action/uumsSelect/selectBySqlSelectType/sso" + "?loginuser=" + encryptor.encrypt(user.getUsername()) +
                        "&appcode=" + Constants.APP_CODE + "&appCode=" + Constants.APP_CODE + "&sqlId="+sqlId+"&selectType=replacetype")
                .json(JacksonUtils.obj2json(map)).asBean(JsonResponse.class);
        List<Map<String, Object>> data = (List<Map<String, Object>>) jsonResponse.getData();
        return data;
    }

}
