<!DOCTYPE html>
<html xmlns="http://www.w3.org/1999/xhtml"
      xmlns:th="http://www.thymeleaf.org">
<head>
    <title>河南移动新闻中心</title>
    <meta http-equiv="Content-Type" content="text/html; charset=utf-8"/>
    <meta http-equiv="X-UA-Compatible" content="IE=edge"/>
    <meta name="viewport" content="width=device-width, initial-scale=1, shrink-to-fit=no"/>
    <script src="http://************:8088/simbestui/js/jquery.min.js?v=svn.revision"
            th:src="@{http://************:8088/simbestui/js/jquery.min.js?v=svn.revision}"
            type="text/javascript"></script>
     <script src="http://************:8088/simbestui/js/jquery.aduq.js?v=svn.revision"
        th:src="@{http://************:8088/simbestui/js/jquery.aduq.js?v=svn.revision}" type="text/javascript"></script>
    <style>
        * {
            padding: 0;
            margin: 0;
            box-sizing: border-box;
        }

        body {
            font-family: "宋体";
            text-rendering: optimizeLegibility;
            font-size: 14px;
            margin: 0 !important;
        }

        p {
            margin: 0;
            line-height: 25px;
        }

        .news .content .leftContent img {
            display: block;
            max-width: 100%;
            height: auto;
            margin: 0 auto;
        }

        .news .content .leftContent img::before {
            content: "";
            display: block;
            width: 1px;
            height: 1px;
        }

        .news {
            width: 100%;
        }

        .news .top {
            width: 1000px;
            margin: 0 auto;
            height: 150px;
            background: url('http://iportal.ha.cmcc/portalweb/images/images/header.jpg') no-repeat top center;
            background-size: auto 100%;
        }

        .news .content {
            width: 1000px;
            margin: 0 auto;
            display: flex;
            justify-content: space-between;
            margin-bottom: 30px;
            border-bottom: 1px solid #0080cb;
        }

        .news .content .leftContent {
            width: 100%;
        }

        .news .content .leftContent .newsTitle {
            border-bottom: 1px solid #bbb;
            padding: 5px 0 10px 0;
            font-size: 20px;
            color: #000;
            text-align: center;
            font-weight: bold;
            margin-bottom: 10px;
            font-family: "Microsoft Yahei", "微软雅黑", arial, "宋体", sans-serif;
        }

        .news .content .leftContent .newsTitle img {
            display: inline-block;
        }

        /*富文本*/
        .news .content .leftContent .newsContent {
            width: 100%;
            padding: 0 53px 0 52px;
            font-family: -apple-system, BlinkMacSystemFont, 'Segoe UI', Roboto, Oxygen, Ubuntu, Cantarell, 'Open Sans', 'Helvetica Neue', sans-serif;
            line-height: 1.4;
            margin: 1rem;
        }

        .newsContent * {
            margin: 0;
            padding: 0;
            box-sizing: border-box;
        }

        .newsContent img {
            max-width: 770px;
            height: auto;
        }

        .newsContent p {
            margin: 0;
            line-height: 25px;
            text-align: justify;
            text-justify: distribute;
        }

        .news .content .leftContent .newFooter {
            width: 100%;
            padding: 0 60px;
            margin-bottom: 10px;
            margin-top: 20px;
        }

        .news .content .leftContent .newFooter p {
            padding-top: 15px;
            border-top: 1px solid #bbb;
            font-size: 12px;
        }

        .news .content .leftContent .newFooter p span {
            margin-right: 10px;
        }

        .news .footer {
            background: #27292E;
            width: 100%;
            height: 50px;
            line-height: 50px;
            text-align: center;
            color: #fff;
        }

        .fontOther {
            font-family: "Microsoft Yahei", "微软雅黑";
        }
    </style>
    <script>
        $(function () {
              var formData = {}
            if(sessionStorage.getItem('formData')){
                formData = JSON.parse(sessionStorage.getItem('formData'));
            }
            if(formData.type=='silde'){
                $(".newsTitle span").html(formData.slideShowTitle);
                if(formData.slideShowFile.length>0){
                    $(".newsContent img").attr("src",formData.slideShowFile[0].mobileFilePath);
                    $(".newsContent img").show();
                }
            }else if(formData.type=='announcement'){
                $(".newsTitle span").html(formData.announcementTitle);
            }else if(formData.type=='programaData'){
                $(".newsTitle span").html(formData.title);
            }
            $(".newsContent .contentText").html(formData.mainBody);

        })
    </script>
</head>
<body>
<div class="news">
    <div class="top"></div>
    <div class="content">
        <div class="leftContent">
            <div class="newsTitle">
                <span></span>
            </div>
            <div class="newsContent">
                <img src="" style="display: none;" alt="">
                <div class="contentText"></div>
            </div>
            <div style="clear: both;"></div>
            <div class="newFooter fontOther">
                <p>信息发布员：<span>管理员（河南移动）</span>发布日期：<span>2000-01-01</span>阅读：<span>100人次</span></p>
            </div>
        </div>
    </div>
</div>
</body>
</html>
