package com.simbest.boot.hnjjwz.process.wf.service.impl;

import cn.hutool.core.collection.CollUtil;
import cn.hutool.core.collection.CollectionUtil;
import cn.hutool.core.util.ObjectUtil;
import cn.hutool.core.util.StrUtil;
import com.fasterxml.jackson.core.type.TypeReference;
import com.google.common.collect.Lists;
import com.google.common.collect.Maps;
import com.simbest.boot.base.exception.Exceptions;
import com.simbest.boot.base.service.impl.LogicService;
import com.simbest.boot.base.web.response.JsonResponse;
import com.simbest.boot.bps.process.bussiness.model.ActBusinessStatus;
import com.simbest.boot.bps.process.bussiness.service.IActBusinessStatusService;
import com.simbest.boot.bps.process.listener.model.WfWorkItemModel;
import com.simbest.boot.cmcc.nagent.NagentOperatorService;
import com.simbest.boot.config.AppConfig;
import com.simbest.boot.constants.ApplicationConstants;
import com.simbest.boot.hnjjwz.attachment.service.IFileExtendService;
import com.simbest.boot.hnjjwz.backstage.announcement.model.Announcement;
import com.simbest.boot.hnjjwz.backstage.programa.model.ProgramaInfo;
import com.simbest.boot.hnjjwz.backstage.programa.service.IProgramaInfoService;
import com.simbest.boot.hnjjwz.backstage.slideshow.model.SlideShow;
import com.simbest.boot.hnjjwz.backstage.slideshow.service.ISlideShowService;
import com.simbest.boot.hnjjwz.backstage.templateLayout.model.TemplateLayout;
import com.simbest.boot.hnjjwz.backstage.templateLayout.service.ITemplateLayoutService;
import com.simbest.boot.hnjjwz.constants.Constants;
import com.simbest.boot.hnjjwz.newcolumn.model.SysNewColumnModel;
import com.simbest.boot.hnjjwz.newcolumn.service.ISysNewColumnService;
import com.simbest.boot.hnjjwz.process.wf.model.PmInstence;
import com.simbest.boot.hnjjwz.process.wf.model.ProgramaDataForm;
import com.simbest.boot.hnjjwz.process.wf.repository.ProgramaDataFormRepository;
import com.simbest.boot.hnjjwz.process.wf.service.IPmInstenceService;
import com.simbest.boot.hnjjwz.process.wf.service.IProgramaDataFormService;
import com.simbest.boot.hnjjwz.util.GZIPUtils;
import com.simbest.boot.hnjjwz.util.ImgToBase64Tool;
import com.simbest.boot.hnjjwz.util.OperateLogTool;
import com.simbest.boot.hnjjwz.util.UumsQuerySqlUtil;
import com.simbest.boot.security.*;
import com.simbest.boot.sys.model.SysDictValue;
import com.simbest.boot.sys.model.SysFile;
import com.simbest.boot.sys.model.SysOperateLog;
import com.simbest.boot.sys.repository.SysDictValueRepository;
import com.simbest.boot.sys.service.ISysDictValueService;
import com.simbest.boot.sys.service.ISysOperateLogService;
import com.simbest.boot.templates.MessageEnum;
import com.simbest.boot.util.CustomBeanUtil;
import com.simbest.boot.util.DateUtil;
import com.simbest.boot.util.distribution.id.IdGenerator;
import com.simbest.boot.util.distribution.id.RedisIdGenerator;
import com.simbest.boot.util.encrypt.RsaEncryptor;
import com.simbest.boot.util.http.client.HttpClient;
import com.simbest.boot.util.json.JacksonUtils;
import com.simbest.boot.util.security.LoginUtils;
import com.simbest.boot.util.security.SecurityUtils;
import com.simbest.boot.uums.api.app.UumsSysAppDecisionApi;
import com.simbest.boot.uums.api.group.UumsSysGroupApi;
import com.simbest.boot.uums.api.org.UumsSysOrgApi;
import com.simbest.boot.uums.api.user.UumsSysUserinfoApi;
import com.simbest.boot.wf.process.service.IProcessInstanceService;
import com.simbest.boot.wf.process.service.IWfOptMsgService;
import com.simbest.boot.wf.process.service.IWorkItemService;
import com.simbest.boot.wf.unitfytodo.IProcessTodoDataService;
import jodd.util.StringUtil;
import lombok.extern.slf4j.Slf4j;
import org.apache.poi.poifs.crypt.Encryptor;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.data.domain.Page;
import org.springframework.data.domain.PageImpl;
import org.springframework.data.domain.Pageable;
import org.springframework.scheduling.annotation.Async;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;
import org.springframework.transaction.interceptor.TransactionAspectSupport;
import org.springframework.util.StringUtils;
import sun.security.krb5.internal.crypto.RsaMd5CksumType;

import java.time.LocalDate;
import java.time.LocalDateTime;
import java.time.format.DateTimeFormatter;
import java.util.*;
import java.util.stream.Collectors;

/**
 * <AUTHOR>
 * @Data 2019/04/17
 * @Description 审批表单服务层具体实现
 */
@Slf4j
@Service(value = "programaDataFormService")
@SuppressWarnings("ALL")
public class ProgramaDataFormServiceImpl extends LogicService<ProgramaDataForm, String> implements IProgramaDataFormService {

    private ProgramaDataFormRepository repository;

    @Autowired
    private AppConfig appConfig;


    @Autowired
    private IFileExtendService fileExtendService;

    @Autowired
    private IProcessInstanceService iProcessInstanceService;

    @Autowired
    private ITemplateLayoutService iTemplateLayoutService;

    @Autowired
    private ISlideShowService iSlideShowService;

    @Autowired
    private IProgramaInfoService iProgramaInfoService;

    @Autowired
    private IProcessTodoDataService iProcessTodoDataService;

    @Autowired
    private ImgToBase64Tool imgToBase64Tool;

    @Autowired
    private UumsQuerySqlUtil uumsQuerySqlUtil;

    @Autowired
    public ProgramaDataFormServiceImpl(ProgramaDataFormRepository repository) {
        super(repository);
        this.repository = repository;
    }

    @Autowired
    private IPmInstenceService pmInstenceService;

    @Autowired
    private IProcessInstanceService processInstanceService;

    @Autowired
    private IWorkItemService workItemService;

    @Autowired
    private UumsSysAppDecisionApi uumsSysAppDecisionApi;

    @Autowired
    private UumsSysUserinfoApi uumsSysUserinfoApi;

    @Autowired
    private IActBusinessStatusService statusService;

    @Autowired
    private IWfOptMsgService wfOptMsgService;

    @Autowired
    private IWorkItemService workItemManager;

    @Autowired
    private RedisIdGenerator idGenerator;

    @Autowired
    private SysDictValueRepository sysDictValueRepository;

    @Autowired
    private UumsSysGroupApi uumsSysGroupApi;

    @Autowired
    private UumsSysOrgApi uumsSysOrgApi;


    @Autowired
    private ISysOperateLogService operateLogService;

    @Autowired
    private LoginUtils loginUtils;

    @Autowired
    private OperateLogTool operateLogTool;

    @Value("${app.host.port}")
    private String appIp;

    private final String param1 = "/action/approvalForm";

    @Autowired
    private NagentOperatorService nagentOperatorService;

    @Autowired
    public ISysDictValueService sysDictValueService;

    @Autowired
    private RsaEncryptor encryptor;

    @Autowired
    private ISysNewColumnService newColumnService;


    /**
     * 流转下一步
     *
     * @param currentUserCode
     * @param workItemId
     * @param outcome
     * @param location
     * @param bodyParam       前台传来的下一步的数据
     * @return
     */
    @Override
    public JsonResponse nextStep(String currentUserCode, String workItemId, String outcome, String location, String formId,
                                 String source, Map<String, Object> bodyParam) {
        if (source == null && !("PC".equals(source) || "MOBILE".equals(source))) {
            source = "PC";
        }
        Map flowParam = (Map) bodyParam.get("flowParam");
        String message = (String) flowParam.get("message");


        //获取form表单
        Object formData = flowParam.get("formData");
        ProgramaDataForm approvalForm = new ProgramaDataForm();


        if (formData != null) {
            String formDataJson = JacksonUtils.obj2json(formData);
            approvalForm = JacksonUtils.json2Type(formDataJson, new TypeReference<ProgramaDataForm>() {
            });
            //避免手机端提交数据，导致数据被覆盖。
            if (approvalForm != null && StrUtil.equals(source, Constants.MOBILE) && StrUtil.isNotEmpty(approvalForm.getId())) {
                approvalForm = this.findById(approvalForm.getId());
            }
        } else {
            if (!StringUtils.isEmpty(formId) && "MOBILE".equals(source)) {
                approvalForm = this.findById(formId);
            }
        }
        Object nextUserNameObject = flowParam.get("nextUserName");
        String nextUsernameJson = JacksonUtils.obj2json(nextUserNameObject);
        List<Map<String, Object>> nextUsernameList = JacksonUtils.json2Type(nextUsernameJson, new
                TypeReference<List<Map<String, Object>>>() {
                });
        String nextUserName = new String();
        int resultNum = 0;
        /**
         *
         * 废除归档
         */
        if (Constants.ACTIVITY_REJECT_END.equals(flowParam.get("decisionId"))) {

            for (Map<String, Object> nextUsernameMap : nextUsernameList) {
                String display = (String) nextUsernameMap.get("display");
                if ("user".equals(display)) {
                    resultNum = saveSubmitTask(approvalForm, workItemId, outcome, message, nextUserName, location, source,
                            currentUserCode);//审批
                }
                if (resultNum == 0) {
                    return JsonResponse.fail(-1, "流转不成功！");
                }
            }
        } else if (Constants.ACTIVITY_END.equals(outcome)) {

            //如果是归档，更新发布时间以及是否发布

            resultNum = saveSubmitTask(approvalForm, workItemId, outcome, message, nextUserName, location, source,
                    currentUserCode);//审批
            if (resultNum == 0) {
                return JsonResponse.fail(-1, "流转不成功！");
            }

        } else {
            if (nextUsernameList == null || nextUsernameList.isEmpty()) {
                return JsonResponse.fail(-1, "流转时没有人！");
            }

            for (Map<String, Object> nextUsernameMap : nextUsernameList) {
                String display = (String) nextUsernameMap.get("display");
                if ("user".equals(display)) {
                    nextUserName = (String) nextUsernameMap.get("value");

                    //纪检网站没有归档，所以如果是省公司流程中的部门领导审批(不传人)或者是分公司流程中省公司部门领导审核(不传人)或者传人的时候才可以流转
                    if (Constants.ACTIVITY_PROVINCE_DEPART.equals(location) || Constants.ACTIVITY_FILIALE_DEPART.equals
                            (location) || (!StringUtils.isEmpty(nextUserName) && !"null".equals(nextUserName))) {

                        if (approvalForm.getId() != null && workItemId != null && !"".equals(workItemId)) {

                            resultNum = saveSubmitTask(approvalForm, workItemId, outcome, message, nextUserName, location,
                                    source, currentUserCode);//审批
                        } else {
                            resultNum = startProcess(approvalForm, nextUserName, outcome, message, source,
                                    currentUserCode);//创建提交
                        }
                    } else {
                        return JsonResponse.fail(null, "流转时的下一个人不能为空！");
                    }
                    if (resultNum == 0) {
                        return JsonResponse.fail(-1, "流转不成功！");
                    }
                } else {
                    //没有为组织的情况
                    log.debug("传来的人员组织信息不正确！不能为组织！");
                    return JsonResponse.fail(-1, "传来的人员组织信息不正确！不能为组织！");
                }
            }
        }
        String showMessage = this.getTemplate(nextUserName);
        return JsonResponse.success(1, showMessage);
    }

    /**
     * 起草发起流程
     *
     * @param approvalForm 会议活动表单
     * @param nextUserName 审批人
     * @param outcome      连线规则
     * @param message      审批意见
     */
    @Override
    public int startProcess(ProgramaDataForm approvalForm, String nextUserName, String outcome, String message, String
            source, String currentUserCode) {
        int ret = 1;
        //准备操作日志参数
        SysOperateLog operateLog = new SysOperateLog();
        String param2 = "/startSubmitProcess";
        String params = "plan=" + approvalForm.toString() + ",source=" + source + ",userCode=" + currentUserCode +
                ",outcome=" + outcome + ",message=" + message + ",nextUserName=" + nextUserName;
        operateLog.setInterfaceParam(params);
        //主单据
        PmInstence pmInstence = new PmInstence();
        try {
            //判断是否是从手机端还是PC端记录操作日志
            JsonResponse returnObj = operateLogTool.operationSource(source, currentUserCode, param1, param2, operateLog);
            if (returnObj != null) {
                log.debug("判断是手机端还是pc端操作日志有误！");
                ret = 0;
            }

            IUser iuser = SecurityUtils.getCurrentUser();
            String companyTypeDictDesc = iuser.getBelongCompanyTypeDictValue();
            /**校验表单和下一步审批人是否为空**/
            if (StrUtil.isNotEmpty(nextUserName)) {
                /**获取登录人所在公司应启动的流程**/
                String processUserType = "";
                if ("01".equals(companyTypeDictDesc)) {
                    processUserType = "A";
                } else {
                    processUserType = "B";
                }
                Map<String, String> map = this.getUserProcessMap(processUserType);
                String processDefId = map.get("processName");
                String processType = map.get("processType");
                if (StrUtil.isNotEmpty(processDefId) && StrUtil.isNotEmpty(processType)) {
                    boolean flag = false;
                    //草稿判断
                    PmInstence usPmInstence = new PmInstence();
                    if (approvalForm.getId() != null && approvalForm.getPmInsId() != null) {
                        super.update(approvalForm);
                        this.updateFileByPmInsId(approvalForm, approvalForm.getPmInsId(), usPmInstence.getPmInsType());
                        usPmInstence = pmInstenceService.findByPmInsId(approvalForm.getPmInsId());
                        if (!StrUtil.equals(approvalForm.getTitle(), usPmInstence.getPmInsTitle())) {
                            usPmInstence.setPmInsTitle(approvalForm.getTitle());
                            pmInstenceService.update(usPmInstence);
                        }
                        flag = true;
                    } else {
                        usPmInstence.setPmInsType(processType);
                        /**保存业务数据**/
                        flag = this.savePlanTask(approvalForm, usPmInstence);
                    }
                    /**启动发起流程**/
                    if (flag) {
                        Map<String, Object> variables = Maps.newHashMap();
                        String currentUserCode1 = iuser.getUsername();
                        String currentUserName = iuser.getTruename();
                        variables.put("inputUserId", currentUserCode1);
                        variables.put("receiptId", usPmInstence.getId());
                        variables.put("title", usPmInstence.getPmInsTitle());
                        variables.put("code", usPmInstence.getPmInsId());
                        variables.put("currentUserCode", currentUserCode1);
                        variables.put("activityDefID", Constants.ACTIVITY_START);
                        variables.put("appCode", Constants.APP_CODE);

                        //第一个参数为流程定义名称
                        Long workItemId = processInstanceService.startProcessAndSetRelativeData(processDefId,
                                usPmInstence.getPmInsTitle(), usPmInstence.getPmInsTitle(), false, variables);
                        if (workItemId != 0) {
                            /**提交表单审批处理**/
                            if (StrUtil.isNotEmpty(nextUserName)) {
                                ret = this.processApproval(workItemId, currentUserCode1, currentUserName, nextUserName,
                                        outcome, message, usPmInstence);
                            } else {
                                operateLog.setErrorMsg("获取审批人失败");
                                JsonResponse.fail(null, "获取审批人失败");
                            }
                        } else {
                            operateLog.setErrorMsg("启动流程失败");
                            JsonResponse.fail(null, "启动流程失败");
                        }
                    } else {
                        operateLog.setErrorMsg("保存失败");
                        JsonResponse.fail(null, "保存失败");
                    }
                } else {
                    operateLog.setErrorMsg("获取流程失败");
                    JsonResponse.fail(null, "操作失败，获取流程失败!");
                }
            } else {
                operateLog.setErrorMsg("表单为空或审批人为空");
                JsonResponse.fail(null, "操作失败，请确认申请表单和审批人!");
            }

        } catch (Exception e) {
            ret = 0;
            operateLog.setErrorMsg(e.toString());
            Exceptions.printException(e);
        } finally {
            /**保存操作记录**/
            operateLogService.saveLog(operateLog);
            return ret;
        }
    }

    /**
     * 更新附件sys_file的pm_ins_id字段
     *
     * @param approvalForm
     */
    private void updatePmInsId(ProgramaDataForm approvalForm) {
        List<SysFile> imageFileList = approvalForm.getCoverImageFileList();
        List<SysFile> authBookFileList = approvalForm.getAuthBookFileList();
        List<SysFile> accessoryFileList = approvalForm.getAccessoryFileList();
        List<SysFile> videoFileList = approvalForm.getVideoFileList();
        String pmInsId = approvalForm.getPmInsId();
        try {
            if (imageFileList != null && imageFileList.size() > 0) {
                for (SysFile imageFile : imageFileList) {
                    fileExtendService.updatePmInsIdPart(pmInsId, imageFile.getId(), Constants.IMAGE_FILE);
                }
            }
            if (authBookFileList != null && authBookFileList.size() > 0) {
                for (SysFile authBookFile : authBookFileList) {
                    fileExtendService.updatePmInsIdPart(pmInsId, authBookFile.getId(), Constants.AUTH_BOOK_FILE);
                }
            }
            if (accessoryFileList != null && accessoryFileList.size() > 0) {
                for (SysFile accessoryFile : accessoryFileList) {
                    fileExtendService.updatePmInsIdPart(pmInsId, accessoryFile.getId(), Constants.ACCESSORY_FILE);
                }
            }
            if (videoFileList != null && videoFileList.size() > 0) {
                for (SysFile videoFile : videoFileList) {
                    fileExtendService.updatePmInsIdPart(pmInsId, videoFile.getId(), Constants.VIDEO_FILE);
                }
            }
        } catch (Exception e) {
            e.printStackTrace();
        }
    }

    /**
     * 工单流转
     *
     * @param approvalForm    会议活动表单
     * @param workItemID      工作项ID
     * @param outcome         连线规则
     * @param message         审批意见
     * @param nextUserName    审批人
     * @param location        当前环节
     * @param source          来源
     * @param currentUserCode
     * @return
     */
    @Override
    public int saveSubmitTask(ProgramaDataForm approvalForm, String workItemID, String outcome, String message, String
            nextUserName, String location, String source, String currentUserCode) {
        int ret = 1;
        //获取用户，手机端和pc端是不一样的
        IUser user = null;
        //source默认为PC
        if (source == null && !("PC".equals(source) || "MOBILE".equals(source))) {
            source = "PC";
        }
        //准备操作参数，用于日志记录
        SysOperateLog operateLog = new SysOperateLog();
        String param2 = "/startSubmitProcess";
        String params = "plan=" + ",workItemId=" + workItemID + ",outcome=" + outcome + ",message=" + message +
                ",nextUserName=" + nextUserName + ",location=" + location + ",copyLocation=" + null + ",copyMessage"
                + null + ",copyNextUserNames=" + null + ",notificationId=" + null + ",source=" + source + ",userCode=" +
                currentUserCode;
        operateLog.setInterfaceParam(params);
        try {
            //表单数据不为空
            if (approvalForm != null) {

                PmInstence pmInstence = pmInstenceService.getByPmInsId(approvalForm);
                //如果是归档，更新发布时间以及是否发布
                if (Constants.ACTIVITY_END.equals(outcome)) {
                    approvalForm.setCreationTime(DateUtil.getCurrentTimestamp());
                    approvalForm.setIsPublish(true);
                    pmInstence.setPmInsFinishFlag("1");
                }

                String pmInsId = approvalForm.getPmInsId();
                //废除归档环节
                if (Constants.ACTIVITY_REJECT_END.equals(outcome)) {
                    if (pmInsId != null) {
                        // ActBusinessStatus actBusinessStatus = statusService.getByProcessInst(processInstId);
                      /*  ActBusinessStatus actBusinessStatus = (ActBusinessStatus)
                                iProcessTodoDataService.queryActBusinessStatusByPmInstId(pmInsId);
                        ret = this.terminateProcessInst(actBusinessStatus.getProcessInstId(), approvalForm);*/
                        approvalForm.setCreationTime(DateUtil.getCurrentTimestamp());
                        approvalForm.setIsPublish(false);
                        pmInstence.setPmInsFinishFlag("-11");
                        /*statusService.updateActBusDataByProInsId(processInstId);
                        //删除流程实例，审批意见，工作项
                        wfOptMsgService.deleteLocalDataByProInsId(processInstId);
                        workItemManager.deleteByProInsId(processInstId);
                        processInstanceService.deleteLocalDataByProInsId(processInstId);
                        //删除流程实例名称  BPS引擎操作
                        processInstanceService.deleteProcessInstance(processInstId);*/
                    } else {
                        operateLog.setErrorMsg("pmInstence为空！pmInsId = " + pmInsId);
                        ret = 0;
                    }
                }
                if (Constants.ACTIVITY_START.equals(location) && org.apache.commons.lang3.StringUtils.isNotEmpty(workItemID) || "hnjjwz.general_manager".equals(location)) {
                    approvalForm.setInMobile(false);//退回修改不能修改
                } else {

                }
                //String pmInsId = approvalForm.getPmInsId();

                if (pmInstence != null) {
                    //判断是否是从手机端还是PC端记录操作日志
                    JsonResponse returnObj = operateLogTool.operationSource(source, currentUserCode, param1, param2,
                            operateLog);
                    if (returnObj != null) {
                        log.debug("判断失败！");
                        operateLog.setErrorMsg("判断失败！！pmInsId = " + pmInsId);
                        ret = 0;
                    }
                    //获取用户
                    user = judgeUsername(source, currentUserCode, operateLog);
                    log.debug("user的值为" + user);
                    if (user == null) {
                        ret = 0;
                    }
//                        pmInstence.setPmInsCurrentActivity( location );
//                        //pmInstence.setPmInsTitle( approvalForm.getTitle() );
//                        pmInstence.setPmInsTitle( approvalForm.getTitle() );
//                        //更新US_PM_INSTENCE表
                    pmInstenceService.update(pmInstence);
                    //更新US_APPROVAL_FORM表
                    //用于修复可能存在的板块内容丢失问题
                    if (StringUtil.isEmpty(approvalForm.getProgramaDisplayName())
                            && StringUtil.isNotEmpty(approvalForm.getProgramaCode())) {
                        ProgramaInfo parentProgramaInfo = iProgramaInfoService.findByFromProCode(approvalForm.getProgramaCode());
                        if (null != parentProgramaInfo) {
                            if (StringUtil.isNotEmpty(parentProgramaInfo.getProgramaDisplayName())) {
                                approvalForm.setProgramaDisplayName(parentProgramaInfo.getProgramaDisplayName());
                            }
                        }
                    }
                    this.update(approvalForm);
//                        //更新ACT_BUSINESS_STATUS表的RECEIPT_TITLE字段，需要调用wf的接口
//                        int isSuccess = processInstanceService.updateTitleByBusinessKey( pmInstence.getId(),approvalForm.getTitle());
//                        if(isSuccess == 0){
//                            throw new Exception( "更新ACT_BUSINESS_STATUS表的RECEIPT_TITLE字段失败！" );
//                        }
//
                    this.updatePmInsId(approvalForm);//更新附件。
                    ret = processApproval(Long.parseLong(workItemID), user.getUsername(), user.getTruename(), nextUserName,
                            outcome, message, pmInstence);

                } else {
                    operateLog.setErrorMsg("pmInstence为空！pmInsId = " + pmInsId);
                    ret = 0;
                }

            } else {
                operateLog.setErrorMsg("approvalForm为空！plan = " + approvalForm.toString());
                ret = 0;
            }
        } catch (Exception e) {
            Exceptions.printException(e);
            operateLog.setErrorMsg(e.toString());
            ret = 0;
        } finally {
            operateLogService.saveLog(operateLog);
            log.debug("ret的值为：" + ret);
            return ret;
        }

    }

    /**
     * 获取用户
     *
     * @param source
     * @param currentUserCode
     * @param operateLog
     * @return
     */
    private IUser judgeUsername(String source, String currentUserCode, SysOperateLog operateLog) {
        IUser user = null;
        Map map = new LinkedHashMap();
        //获取用户
        user = SecurityUtils.getCurrentUser();
        if (user == null) {
            log.debug("用户为空！");
            operateLog.setErrorMsg("请联系管理员，用户信息为空！");
            return null;
        }
        return user;
    }

    /**
     * 从视图中获取用户基本信息和组织，不包含职位信息
     *
     * @param username
     * @return
     */
    @Override
    public Map<String, Object> findViewUserOrg(String username) {
        IUser user = uumsSysUserinfoApi.findByKey(username, IAuthService.KeyType.username, Constants.APP_CODE); //审批人
        return repository.findViewUserOrg(username);
    }

    /**
     * 获取各个公司审批的数据
     *
     * @param
     * @return
     */
    @Override
    public List<ProgramaDataForm> findCompany(String company) {
        return repository.findCompany(company);
    }


    /**
     * 流程审批
     *
     * @param workItemID      工作项id
     * @param currentUserCode 当前登录人code
     * @param currentUserName 当前登录人名称
     * @param nextUserName    审批人
     * @param outcome         连线规则
     * @param message         审批意见
     * @return
     */
    @Override
    public int processApproval(Long workItemID, String currentUserCode, String currentUserName, String nextUserName, String
            outcome, String message, PmInstence pmInstence) {
        int ret = 0;
        Map<String, Object> map = new HashMap<>();
        if (nextUserName != null) {
            map.put("inputUserId", nextUserName);//指定下一审批人
        }
        map.put("outcome", outcome);
        map.put("receiptId", String.valueOf(pmInstence.getId()));
        map.put("title", pmInstence.getPmInsTitle());
        map.put("code", pmInstence.getPmInsId());
        map.put("currentUserCode", currentUserCode);
        map.put("appCode", Constants.APP_CODE);
        try {
            //添加流程审批意见
            int status = workItemService.submitApprovalMsg(workItemID, message);
            if (status == 1) {
                //根据工作项ID完成工作项 如果第三个参数为true，则启用事务分割；如果第二个参数为false，则不启用事务分割
                long workItemIdNext = workItemService.finishWorkItemWithRelativeData(workItemID, map, false);
                if (workItemIdNext > 0) {
                    ret = 1;
                } else {
                    ret = 0;
                }
            } else {
                ret = 0;
            }
        } catch (Exception e) {
            e.printStackTrace();
            ret = 0;
        }
        return ret;
    }

    /**
     * 获取用户组织树
     *
     * @param processInstId
     * @param sysAppDecision
     * @return
     */
    @Override
    public JsonResponse getOrgAndUser(String processInstId, IAppDecision sysAppDecision, String source, String currentUser) {
        String currentUserName = SecurityUtils.getCurrentUserName();
        String currentUserNameEncr = null;
        JsonResponse response = new JsonResponse();
        if (source == null && !("PC".equals(source) || "MOBILE".equals(source))) {
            source = "PC";
            currentUserNameEncr = encryptor.encrypt(currentUserName);
        } else if ("PC".equals(source)) {
            currentUserNameEncr = encryptor.encrypt(currentUserName);
        } else {
            currentUserNameEncr = currentUser;
        }
        /**准备操作日志参数**/
        SysOperateLog operateLog = new SysOperateLog();
        String param1 = "/action/networkMigrationForm";
        String param2 = "/getOrgAndUser";
        String params = "processInstId=" + processInstId + ",source=" + source + ",userCode=" + currentUser +
                ",SimpleAppDecision=" + sysAppDecision.toString();
        operateLog.setInterfaceParam(params);
        operateLog.setBussinessKey("processInstId=" + processInstId);
        try {
            /**判断来源记录操作日志**/
            JsonResponse returnObj = operateLogTool.operationSource(source, currentUser, param1, param2, operateLog);
            if (returnObj != null) {
                return returnObj;
            }
            /**请起草人确认决策处理**/
           /* if ( Constants.DEC_CONFIRM.equals( sysAppDecision.getDecisionId() ) ){
                if (processInstId != null){
                    List<WfWorkItemModel> wfWorkItemModels =  (List<WfWorkItemModel>)
workItemManager.queryWorkTtemDataByProInsId(Long.parseLong(processInstId));
                    if (wfWorkItemModels != null && wfWorkItemModels.size() > 0){
                        String userName = wfWorkItemModels.get(0).getCreator();
                        response = uumsSysUserinfoApi.findUserByUsernameNoPage( Constants.APP_CODE,userName );
                    }
                }
            }else {*/
            /**根据flowType处理**/
            String defaultValue = sysAppDecision.getDecisionConfig();
            String newDefault = JacksonUtils.unescapeString(defaultValue).replace(("\'"), "\"");
            List<HashMap<String, String>> mapLists = JacksonUtils.json2Type(newDefault, new
                    TypeReference<List<HashMap<String, String>>>() {
                    });
            if (mapLists != null && mapLists.size() > 0) {
                Map<String, String> hashMap = mapLists.get(0);
                String typeValue = hashMap.get("typeValue");
                String includeValue = hashMap.get("includeValue");
                if ("normalStep".equals(typeValue)) {
                    /*!sysAppDecision.getActivityDefId().equals("hnjjwz.city_depart_manager")||
                    !sysAppDecision.getActivityDefId().equals("hnjjwz.general_manager")*/
                    if ((sysAppDecision.getActivityDefId().equals("hnjjwz.city_depart_filialesecretary") && (sysAppDecision.getDecisionId().equals("hnjjwz.province_admin_pass")))
                            || (sysAppDecision.getActivityDefId().equals("hnjjwz.general_manager") && (sysAppDecision.getDecisionId().equals("redeploy")))
                            || (sysAppDecision.getActivityDefId().equals("hnjjwz.province_depart_threemanager") && (sysAppDecision.getDecisionId().equals("return")))) {
                        ProgramaDataForm approvalForm = new ProgramaDataForm();
//                        ActBusinessStatus actBusinessStatus ;
                        List<ActBusinessStatus> actBusinessStatus;
                        String receiptCode;
                        if (StrUtil.isNotEmpty(processInstId)) {
                            actBusinessStatus = (List<com.simbest.boot.bps.process.bussiness.model.ActBusinessStatus>) iProcessTodoDataService.queryActBusinessStatusByProInsId(Long.valueOf(processInstId));
                            receiptCode = actBusinessStatus.get(0).getReceiptCode();
                            approvalForm = repository.getApprovalFromDetailFromInstId(receiptCode);

                        }
                        Map<String, Object> userGruop = CollUtil.newHashMap();
                        userGruop.put("groupId", "G0119");
                        JsonResponse jsonResponse = HttpClient.textBody(appConfig.getUumsAddress() + Constants.USER_GROUP +
                                "?loginuser=" + currentUserNameEncr + "&appcode=" + Constants.APP_CODE)
                                .json(JacksonUtils.obj2json(userGruop))
                                .asBean(JsonResponse.class);
                        Object dataUserGruop = jsonResponse.getData();
                        Map<String, Object> dataMap = (Map<String, Object>) dataUserGruop;
                        List<Map<String, Object>> userGruopList = (List<Map<String, Object>>) dataMap.get("content");
                        String adminName = null;
                        List<String> userNames = userGruopList.stream().filter(item -> null == item.get("spare1") || "".equals(item.get("spare1"))).map(map -> map.get("username").toString()).collect(Collectors.toList());
                        //如果当前单子US_PROGRAMA_DATA_FORM中的programa_code =群组中的人 这个决策项就返回此人
                        for (int i = 0; i < userGruopList.size(); i++) {
                            if (approvalForm.getProgramaCode().equals((String) userGruopList.get(i).get("spare1"))) {
                                adminName = (String) userGruopList.get(i).get("username");
                                break;
                            }
                        }

                        if (StrUtil.isEmpty(adminName)) {
                            return response = uumsSysUserinfoApi.findUserByUsernameNoPage(Constants.APP_CODE, String.join(ApplicationConstants.COMMA, userNames));
                        }

                        response = uumsSysUserinfoApi.findUserByUsernameNoPage(Constants.APP_CODE, adminName);
                    } else {
                        Map<String, String> map = new HashMap<>();
                        map.put("appCode", Constants.APP_CODE);
                        map.put("processDefId", sysAppDecision.getProcessDefId());
                        map.put("activityDefId", sysAppDecision.getActivityDefId());
                        map.put("decisionId", sysAppDecision.getDecisionId());
                        map.put("groupId", sysAppDecision.getGroupId());
                        map.put("decisionConfig", sysAppDecision.getDecisionConfig());
                        //老的返回因为G0119群组中只有一个人,所以用这种  新的根据人名来出选人
                        response = uumsSysUserinfoApi.findUserByDecisionNoPage(Constants.APP_CODE, map);
                    }
                }
                if ("previousStep".equals(typeValue) && includeValue != null) {//退回上一步处理,获取上一步审批人
                    if (processInstId != null) {
                        WfWorkItemModel wfWorkItemModel = (WfWorkItemModel) workItemManager.getByProInstIdAAndAInstId
                                (Long.parseLong(processInstId), includeValue);
                        if (wfWorkItemModel != null) {
                            String userName = wfWorkItemModel.getParticipant();
                            response = uumsSysUserinfoApi.findUserByUsernameNoPage(Constants.APP_CODE, userName);
                        }
                    }
                }
                if ("reject".equals(sysAppDecision.getDecisionId())) {//驳回处理,获取上一步审批人
                    if (processInstId != null) {
                        WfWorkItemModel wfWorkItemModel = (WfWorkItemModel) workItemManager.getByProInstIdAAndAInstId
                                (Long.parseLong(processInstId), Constants.ACTIVITY_START);
                        if (wfWorkItemModel != null) {
                            String userName = wfWorkItemModel.getParticipant();
                            response = uumsSysUserinfoApi.findUserByUsernameNoPage(Constants.APP_CODE, userName);
                        }
                        /*ProgramaDataForm fromDeceiptCode = this.getFromDeceiptCode(processInstId);
                        response = uumsSysUserinfoApi.findUserByUsernameNoPage(
Constants.APP_CODE,fromDeceiptCode.getUsername() );
*/
                    }
                }
            }
            //}
        } catch (Exception e) {
            operateLog.setErrorMsg(e.toString());
            Exceptions.printException(e);
        } finally {
            operateLogService.saveLog(operateLog);
            return response;
        }
    }

    /**
     * 获取决策项
     *
     * @param processInstId  流程实例id
     * @param processDefName 流程定义名称
     * @param location       当前环节
     * @param source         来源
     * @param currentUser    当前操作人账号
     * @param processType    多页面多流程中 流程类型
     * @return
     */
    @Override
    public List<SimpleAppDecision> getDecisions(String processInstId, String processDefName, String location, String
            source, String currentUser, String processType) {
        if (source == null && !("PC".equals(source) || "MOBILE".equals(source))) {
            source = "PC";
        }
        /**处理操作参数**/
        SysOperateLog operateLog = new SysOperateLog();
        String param1 = "/action/ProgramaDataForm";
        String param2 = "/getDecisions";
        String params = "processInstId=" + processInstId + ",processDefName=" + processDefName + ",location=" + location +
                ",source=" + source + ",userCode=" + currentUser;
        operateLog.setInterfaceParam(params);
        operateLog.setBussinessKey("processInstId=" + processInstId);
        List<SimpleAppDecision> decisions = new ArrayList<>();
        try {
            /**判断是否是从手机端还是PC端记录操作日志**/
            JsonResponse returnObj = operateLogTool.operationSource(source, currentUser, param1, param2, operateLog);
            if (returnObj != null) {
                log.debug("判断失败！");
                operateLog.setErrorMsg("判断失败！");
                return null;
            }
            /**选择流程处理**/
            if ((processDefName == null || "".equals(processDefName) || "undefined".equals(processDefName)) &&
                    Constants.ACTIVITY_START.equals(location)) {
                processDefName = this.getProcessName(processType);
            }
            /**当前环节下所有决策**/
            Map<String, String> map = new HashMap<>();
            map.put("appCode", Constants.APP_CODE);
            map.put("processDefId", processDefName);
            map.put("activityDefId", location);
            //根据是pc端还是手机来判断调用主数据哪个接口
            decisions = uumsSysAppDecisionApi.findDecisions(Constants.APP_CODE, map);
            // 获取当前登录人
            IUser iUser = SecurityUtils.getCurrentUser();

            if ("guolin7".equals(iUser.getUsername()) || "yangruinan".equals(iUser.getUsername())) {
                SimpleAppDecision simpleAppDecision = new SimpleAppDecision();
                simpleAppDecision.setId("hnjjwz001");
                simpleAppDecision.setActivityDefId("hnjjwz.general_manager");
                simpleAppDecision.setActivityDefName("省公司纪检信息管理员审核");
                simpleAppDecision.setAppCode("hnjjwz");
                simpleAppDecision.setDecisionConfig
                        ("[{'type':'flowType','typeValue':'normalStep','includeValue':'','excludeValue':''}, {'type':'pageType','typeValue':'tree','includeValue':'','excludeValue':'','groupType':'#treeAll#true#false#user','formula': ''}]");
                simpleAppDecision.setDecisionId("end");
                simpleAppDecision.setDecisionName("直接发布");
                simpleAppDecision.setOpinion("同意");
                simpleAppDecision.setProcessDefId("com.hnjjwz.flow.county_company");
                simpleAppDecision.setProcessDefName("分公司信息上报流程");
                decisions.add(simpleAppDecision);
            }

        } catch (Exception e) {
            operateLog.setErrorMsg(e.toString());
            Exceptions.printException(e);
        } finally {
            operateLogService.saveLog(operateLog);
            return decisions;
        }

    }

    /**
     * 保存草稿
     *
     * @param approvalForm
     * @return
     */
    @Override
    public int saveApprovalFrom(ProgramaDataForm approvalForm) {
        int ret = 0;
        IUser iuser = SecurityUtils.getCurrentUser();
        String processName = this.getProcessName();
        if (approvalForm.getId() == null) {
            String pmInstId = this.getPmInstId();
            PmInstence pmInstence = new PmInstence();
            pmInstence.setPmInsId(pmInstId);
            pmInstence.setPmInsType(pmInstId.substring(0, 1));
            pmInstence.setPmInsTitle(approvalForm.getTitle());
            pmInstence.setProcessDefName(processName);
            pmInstence.setPmInsCurrentActivity(Constants.ACTIVITY_START);
            pmInstence.setPmInsCreatorCode(iuser.getUsername());
            pmInstence.setPmInsCreatorName(iuser.getTruename());
            pmInstence.setBelongCompanyCode(iuser.getBelongCompanyCode());
            pmInstence.setBelongCompanyTypeDictValue(iuser.getBelongCompanyTypeDictValue());
            pmInstence.setBelongDepartmentCode(iuser.getBelongDepartmentCode());
            pmInstence.setBelongOrgCode(iuser.getBelongOrgCode());
            pmInstenceService.insert(pmInstence);

            /**保存表单数据**/
            if (pmInstence.getId() != null) {
                approvalForm.setBelongCompanyCode(iuser.getBelongCompanyCode());
                approvalForm.setBelongCompanyTypeDictValue(iuser.getBelongCompanyTypeDictValue());
                approvalForm.setBelongDepartmentCode(iuser.getBelongDepartmentCode());
                approvalForm.setBelongOrgCode(iuser.getBelongOrgCode());
                approvalForm.setPmInsId(pmInstence.getPmInsId());
                this.insert(approvalForm);
                /**启动发起流程**/
                if (approvalForm.getId() != null) {
                    Map<String, Object> variables = new HashMap<String, Object>();
                    String inputUserId = iuser.getUsername();
                    String username = iuser.getTruename();
                    variables.put("inputUserId", inputUserId);
                    variables.put("receiptId", pmInstence.getId());
                    variables.put("title", pmInstence.getPmInsTitle());
                    variables.put("code", pmInstence.getPmInsId());
                    variables.put("currentUserCode", inputUserId);
                    variables.put("activityDefID", Constants.ACTIVITY_START);
                    variables.put("appCode", Constants.APP_CODE);
                    try {
                        //第一个参数为流程定义名称
                        if (processName != null) {
                            Long workItemId = processInstanceService.startProcessAndSetRelativeData(processName,
                                    pmInstence.getPmInsTitle(), pmInstence.getPmInsTitle(), false, variables);
                            ret = workItemId.intValue();
                        }
                    } catch (Exception e) {
                        e.printStackTrace();
                        ret = 0;
                    }
                }
            }
        } else {
            PmInstence pmInstence = pmInstenceService.getByPmInsId(approvalForm);
            if (pmInstence != null) {
                pmInstence.setPmInsTitle(approvalForm.getTitle());
                pmInstenceService.update(pmInstence);
            }
            ProgramaDataForm resultApprovalForm = this.update(approvalForm);
            ret = 1;
        }
        return ret;
    }


    /**
     * 保存草稿
     *
     * @return
     */
    @Override
    @Transactional
    public JsonResponse saveDraft(String source, String currentUserCode, ProgramaDataForm newTopicFrom) {
        /**准备操作参数**/
        SysOperateLog operateLog = new SysOperateLog();
        String param2 = "/saveDraft";
        String params = "ProgramaDataForm=" + newTopicFrom.toString() + ",source=" + source + ",userCode=" +
                currentUserCode;
        operateLog.setInterfaceParam(params);
        try {
            /**判断是否是从手机端还是PC端记录操作日志**/
            JsonResponse returnObj = operateLogTool.operationSource(source, currentUserCode, param1, param2, operateLog);
            if (returnObj != null) {
                return returnObj;
            }

            // 保存草稿
            if (StrUtil.isEmpty(newTopicFrom.getId())) {
                newTopicFrom.setId(null);
                IUser iuser = SecurityUtils.getCurrentUser();
                String companyTypeDictDesc = iuser.getBelongCompanyTypeDictValue();
                /**校验表单和下一步审批人是否为空**/
                /**获取登录人所在公司应启动的流程**/
                String processUserType = "";
                if ("01".equals(companyTypeDictDesc)) {
                    processUserType = Constants.PROCESS_A;
                } else {
                    processUserType = Constants.PROCESS_B;
                }
                // 保存业务单据信息
                PmInstence usPmInstence = new PmInstence();
                usPmInstence.setPmInsType(processUserType);
                this.savePlanTask(newTopicFrom, usPmInstence);
            }
            // 更新草稿
            else {
                // 更新表单数据
                newTopicFrom.setModifiedTime(LocalDateTime.now());
                this.update(newTopicFrom);
                // 更新主单据
                PmInstence selectPm = pmInstenceService.findByPmInsId(newTopicFrom.getPmInsId());
                selectPm.setPmInsTitle(newTopicFrom.getTitle());
                selectPm.setModifiedTime(LocalDateTime.now());
                PmInstence newSelectPm = pmInstenceService.update(selectPm);
                //  更新附件
                this.updateFileByPmInsId(newTopicFrom, newTopicFrom.getPmInsId(), newSelectPm.getPmInsType());
            }
        } catch (Exception e) {
            operateLog.setErrorMsg(e.toString());
            Exceptions.printException(e);
            return JsonResponse.fail(null, Constants.MESSAGE_FAIL);
        } finally {
            operateLogService.saveLog(operateLog);
        }
        return JsonResponse.success(newTopicFrom, Constants.MESSAGE_SUCCESS);
    }

    /**
     * 废除草稿
     *
     * @return
     */
    @Override
    @Transactional
    public JsonResponse deleteDraft(String source, String currentUserCode, String pmInsId, ProgramaDataForm
            innovationTopicForm) {
        /**准备操作参数**/
        SysOperateLog operateLog = new SysOperateLog();
        String param2 = "/deleteDraft";
        String params = "pmInsId=" + pmInsId + ",source=" + source + ",userCode=" + currentUserCode;
        operateLog.setInterfaceParam(params);
        try {
            /**判断是否是从手机端还是PC端记录操作日志**/
            JsonResponse returnObj = operateLogTool.operationSource(source, currentUserCode, param1, param2, operateLog);
            if (returnObj != null) {
                return returnObj;
            }
            // 删除主单据数据
            PmInstence pmInstence = pmInstenceService.findByPmInsId(pmInsId);
            pmInstenceService.delete(pmInstence);

            repository.deleteByPmInsId(pmInsId);
            // 删除业务单据数据
//            this.delete(innovationTopicForm);
        } catch (Exception e) {
            operateLog.setErrorMsg(e.toString());
            Exceptions.printException(e);
            return JsonResponse.fail(null, Constants.MESSAGE_FAIL);
        } finally {
            operateLogService.saveLog(operateLog);
        }
        return JsonResponse.success(null, Constants.MESSAGE_SUCCESS);
    }


    /**
     * 根据登录人所在公司获取流程
     *
     * @return
     */
    private Map<String, String> getUserProcessMap(String processUserType) {
        Map<String, String> map = Maps.newHashMap();
        switch (processUserType) {
            case Constants.PROCESS_A:
                map.put("processName", Constants.FLOW_PROVINCIAL);
                map.put("processType", "A"); //省公司人员流程
                break;
            case Constants.PROCESS_B:
                map.put("processName", Constants.FLOW_BRANCH);
                map.put("processType", "B");//市公司人员流程
                break;
            default:
                throw new IllegalStateException("Unexpected value: " + processUserType);
        }
        return map;
    }

    /**
     * 更新附件
     *
     * @param approvalForm 表单
     */
    private void updateFileByPmInsId(ProgramaDataForm approvalForm, String pmInsId, String pmInsType) {

        List<SysFile> imageFileList = approvalForm.getCoverImageFileList();
        List<SysFile> authBookFileList = approvalForm.getAuthBookFileList();
        List<SysFile> accessoryFileList = approvalForm.getAccessoryFileList();
        List<SysFile> videoFileList = approvalForm.getVideoFileList();

        try {
            fileExtendService.deleteByPmInsId(pmInsId);
            if (imageFileList != null && !imageFileList.isEmpty()) {
                for (SysFile file : imageFileList) {
                    fileExtendService.updatePmInsId(pmInsId, pmInsType, file.getId(), Constants.IMAGE_FILE);
                }
            }
            if (authBookFileList != null && !authBookFileList.isEmpty()) {
                for (SysFile file : authBookFileList) {
                    fileExtendService.updatePmInsId(pmInsId, pmInsType, file.getId(), Constants.AUTH_BOOK_FILE);
                }
            }
            if (accessoryFileList != null && !accessoryFileList.isEmpty()) {
                for (SysFile file : accessoryFileList) {
                    fileExtendService.updatePmInsId(pmInsId, pmInsType, file.getId(), Constants.ACCESSORY_FILE);
                }
            }
            if (videoFileList != null && !videoFileList.isEmpty()) {
                for (SysFile file : videoFileList) {
                    fileExtendService.updatePmInsId(pmInsId, pmInsType, file.getId(), Constants.VIDEO_FILE);
                }
            }

        } catch (Exception e) {
            e.printStackTrace();
        }

    }

    /**
     * 保存业务数据
     *
     * @return
     * @throws Exception
     */
    boolean savePlanTask(ProgramaDataForm approvalForm, PmInstence pmInstence) {
        boolean flag = false;
        IUser iuser = SecurityUtils.getCurrentUser();
        /**保存申请表单任务**/
        try {
            /**保存主单据**/
            if (StrUtil.isEmpty(approvalForm.getId())) {
                String pmInsId = pmInstence.getPmInsType() + String.valueOf(IdGenerator.idWorker.nextId());//获取到pmInsId
                pmInstence.setPmInsId(pmInsId);
                pmInstence.setPmInsTitle(approvalForm.getTitle());
                pmInstence.setPmInsCurrentActivity(Constants.ACTIVITY_START);
                pmInstence.setPmInsCreatorCode(iuser.getUsername());
                pmInstence.setPmInsCreatorName(iuser.getTruename());
                pmInstence.setBelongCompanyCode(iuser.getBelongCompanyCode());
                pmInstence.setBelongCompanyTypeDictValue(iuser.getBelongCompanyTypeDictValue());
                pmInstence.setBelongDepartmentCode(iuser.getBelongDepartmentCode());
                pmInstence.setBelongOrgCode(iuser.getBelongOrgCode());
                pmInstenceService.insert(pmInstence);
            }
            /**保存表单数据**/
            if (pmInstence.getId() != null) {
//                Map<String, Object> viewUserOrg = this.findViewUserOrg(iuser.getUsername());
                IUser user = uumsSysUserinfoApi.findByKey(iuser.getUsername(), IAuthService.KeyType.username, Constants.APP_CODE); //审批人
                approvalForm.setUsername(iuser.getUsername());
                approvalForm.setTruename(iuser.getTruename());
                approvalForm.setBelongCompanyCode(iuser.getBelongCompanyCode());
                approvalForm.setBelongCompanyName(iuser.getBelongCompanyName());
                approvalForm.setBelongCompanyTypeDictValue(iuser.getBelongCompanyTypeDictValue());
                approvalForm.setBelongDepartmentCode(iuser.getBelongDepartmentCode());
                approvalForm.setBelongDepartmentName(iuser.getBelongDepartmentName());
                approvalForm.setBelongOrgCode(iuser.getBelongOrgCode());
                approvalForm.setBelongOrgName(iuser.getBelongOrgName());
                approvalForm.setPmInsId(pmInstence.getPmInsId());
                approvalForm.setIsPublish(false);
              /*  approvalForm.setCompany(String.valueOf(viewUserOrg.get("COMPANYNAME")));//起草人公司
                approvalForm.setDepartmentName(String.valueOf(viewUserOrg.get("DEPARTMENTNAME")));//起草人部门
                approvalForm.setDisplayName(String.valueOf(viewUserOrg.get("DISPLAYNAME")));//起草人组织路径*/

                approvalForm.setCompany(iuser.getBelongCompanyName());//起草人公司
                approvalForm.setDepartmentName(iuser.getBelongDepartmentName());//起草人部门
                approvalForm.setDisplayName(iuser.getBelongCompanyName() + "\\" + iuser.getBelongDepartmentName());//起草人组织路径
                //用于修复可能存在的板块内容丢失问题
                if (StringUtil.isEmpty(approvalForm.getProgramaDisplayName())
                        && StringUtil.isNotEmpty(approvalForm.getProgramaCode())) {
                    ProgramaInfo parentProgramaInfo = iProgramaInfoService.findByFromProCode(approvalForm.getProgramaCode());
                    if (null != parentProgramaInfo) {
                        if (StringUtil.isNotEmpty(parentProgramaInfo.getProgramaDisplayName())) {
                            approvalForm.setProgramaDisplayName(parentProgramaInfo.getProgramaDisplayName());
                        }
                    }
                }
                ProgramaDataForm approvalForm1 = this.insert(approvalForm);
                this.updatePmInsId(approvalForm);//更新附件
                if (approvalForm1 != null) {
                    flag = true;
                }
            }
        } catch (Exception e) {
            flag = false;
            TransactionAspectSupport.currentTransactionStatus().setRollbackOnly();
            log.debug(e.getMessage());
            return flag;
        }
        return flag;
    }

    /**
     * 注销流程
     *
     * @param approvalForm
     * @return
     */
    @Override
    public int deleteProcess(Long processInstId, ProgramaDataForm approvalForm) {
        if (approvalForm.getId() == null) {
            return 1;
        } else {
            try {
                //注销都是把相应表的enabled变为0
                //牵涉到
                // Sys_file、US_APPROVAL_FORM、US_PM_INSTENCE、WF_OPTMSG_MODEL、
                // WF_PROCESS_INST_MODEL、WF_WORKITEM_MODEL
                // 这六个表，现在对sys_file这个表没有进行处理，其他进行逻辑删除处理。
                PmInstence pmInstence = pmInstenceService.getByPmInsId(approvalForm);
                if (pmInstence != null) {
                    statusService.updateActBusDataByProInsId(processInstId);
                    //删除流程实例，审批意见，工作项
                    wfOptMsgService.deleteLocalDataByProInsId(processInstId);
                    workItemManager.deleteByProInsId(processInstId);
                    processInstanceService.deleteLocalDataByProInsId(processInstId);
                    //删除流程实例名称  BPS引擎操作
                    processInstanceService.deleteProcessInstance(processInstId);
                    pmInstenceService.deleteByPmId(pmInstence.getId());
                }
                repository.deleteByFromId(approvalForm.getId());
            } catch (Exception e) {
                log.error(e.getLocalizedMessage());
                return 0;
            }
        }
        return 0;
    }

    /**
     * 注销流程2
     *
     * @param processInstId
     * @return
     */
    @Override
    public int terminateProcessInst(Long processInstId, ProgramaDataForm approvalForm) {
        return processInstanceService.terminateProcessInst(processInstId);
    }

    /**
     * 获取详情
     *
     * @param processInstId 流程实例id
     * @return
     */
    @Override
    public JsonResponse getApprovalFromDetail(Long processInstId, String source, String currentUserCode, String pmInstId, String pmInsId, String location) {
        //解决草稿传值问题
        if (StrUtil.isNotEmpty(pmInsId)) {
            pmInstId = pmInsId;
        }
        if (source == null && !("PC".equals(source) || "MOBILE".equals(source))) {
            source = "PC";
        }
        /**操作日志记录参数**/
        SysOperateLog operateLog = new SysOperateLog();
        String param2 = "/getApprovalFromDetail";
        String params = "source=" + source + ",currentUserCode" + currentUserCode;
        operateLog.setInterfaceParam(params);
        ProgramaDataForm approvalForm = new ProgramaDataForm();
        try {
            /**判断是否是从手机端还是PC端记录操作日志**/
            JsonResponse returnObj = operateLogTool.operationSource(source, currentUserCode, param1, param2, operateLog);
            if (returnObj != null) {
                return returnObj;
            }
            if (processInstId != null) {
                ActBusinessStatus actBusinessStatus = statusService.getByProcessInst(processInstId);
                if (actBusinessStatus != null) {
                    Long start = System.currentTimeMillis();
                    String id = actBusinessStatus.getReceiptCode();
                    approvalForm = repository.getApprovalFromDetailFromInstId(id);
                    //测试用  验证url和加密base64    上限时要取消
//                    if (actBusinessStatus.getReceiptTitle().contains("url")){
//                        approvalForm.setCompressBase(approvalForm.getMainBody());
                    String compressBase = GZIPUtils.compress(approvalForm.getMainBody());
                    approvalForm.setCompressBase(compressBase);
        /*            }else{
                        String imgToBase64 = imgToBase64Tool.ImgToBase64(approvalForm.getMainBody());
                        String compressBase =  GZIPUtils.compress(imgToBase64);
                        approvalForm.setCompressBase(compressBase);
                    }*/

                    Long end = System.currentTimeMillis();
                    Date endDate = DateUtil.getCurrent();
                    log.info("根据文章ID获取公告详情耗时：{}ms", (end - start));
                 /*   if (!"PC".equals(source)){
                        approvalForm.setMainBody(null);
                    }*/
                }
            } else {
                approvalForm = repository.getApprovalFromDetailFromInstId(pmInstId);
            }
            //获取附件信息
            if (approvalForm != null && approvalForm.getPmInsId() != null) {
                List<SysFile> imageFileList = fileExtendService.getPartFile(approvalForm.getPmInsId(),
                        Constants.IMAGE_FILE);
                List<SysFile> authBookFileList = fileExtendService.getPartFile(approvalForm.getPmInsId(),
                        Constants.AUTH_BOOK_FILE);
                List<SysFile> accessroyFileList = fileExtendService.getPartFile(approvalForm.getPmInsId(),
                        Constants.ACCESSORY_FILE);
                List<SysFile> videoFileList = fileExtendService.getPartFile(approvalForm.getPmInsId(),
                        Constants.VIDEO_FILE);
                putFile(imageFileList, approvalForm, Constants.IMAGE_FILE);
                putFile(authBookFileList, approvalForm, Constants.AUTH_BOOK_FILE);
                putFile(accessroyFileList, approvalForm, Constants.ACCESSORY_FILE);
                putFile(videoFileList, approvalForm, Constants.VIDEO_FILE);
            }
            if (Constants.MOBILE.equals(source) && approvalForm != null) {
                if (Constants.ACTIVITY_START.equals(location)) {
                    approvalForm.setInMobile(false);//退回修改不能修改
                } else {
                    approvalForm.setInMobile(true);//退回
                }
            }
        } catch (Exception e) {
            operateLog.setErrorMsg(e.toString());
            Exceptions.printException(e);
        } finally {
            operateLogService.saveLog(operateLog);

            if (approvalForm != null) {
                List<TemplateLayout> programa_code = iTemplateLayoutService.findtemplateLayout
                        (approvalForm.getProgramaCode());

                if (programa_code.size() > 0) {
                    for (TemplateLayout templateLayout : programa_code) {

                        approvalForm.setPointUrl(templateLayout.getPointUrl());
                    }
                }
            }

            return JsonResponse.success(approvalForm);
        }
    }

    /**
     * 向获得的工单信息中放入附件信息
     *
     * @param fileList
     * @param approvalForm
     */
    private void putFile(List<SysFile> fileList, ProgramaDataForm approvalForm, String fileType) {
        if (fileList != null && fileList.size() > 0) {
            if (Constants.IMAGE_FILE.equals(fileType)) {
                approvalForm.setCoverImageFileList(fileList);
            }
            if (Constants.AUTH_BOOK_FILE.equals(fileType)) {
                approvalForm.setAuthBookFileList(fileList);
            }
            if (Constants.ACCESSORY_FILE.equals(fileType)) {
                approvalForm.setAccessoryFileList(fileList);
            }
            if (Constants.VIDEO_FILE.equals(fileType)) {
                approvalForm.setVideoFileList(fileList);
            }
        }
    }

    /**
     * 再根据pm_ins_id字段获取ApprovalForm对象
     *
     * @param pmInsId
     * @return
     */
    @Override
    public ProgramaDataForm getProgramaDataFormPmInsId(String pmInsId) {

        ProgramaDataForm approvalForm = repository.getProgramaDataFormPmInsId(pmInsId);
        if (approvalForm.getPmInsId() != null) {
            List<SysFile> imageFileList = fileExtendService.getPartFile(approvalForm.getPmInsId(), Constants.IMAGE_FILE);
            List<SysFile> authBookFileList = fileExtendService.getPartFile(approvalForm.getPmInsId(),
                    Constants.AUTH_BOOK_FILE);
            List<SysFile> accessroyFileList = fileExtendService.getPartFile(approvalForm.getPmInsId(),
                    Constants.ACCESSORY_FILE);
            List<SysFile> videoFileList = fileExtendService.getPartFile(approvalForm.getPmInsId(), Constants.VIDEO_FILE);
            putFile(imageFileList, approvalForm, Constants.IMAGE_FILE);
            putFile(authBookFileList, approvalForm, Constants.AUTH_BOOK_FILE);
            putFile(accessroyFileList, approvalForm, Constants.ACCESSORY_FILE);
            putFile(videoFileList, approvalForm, Constants.VIDEO_FILE);
        }
        return approvalForm;
    }

    /**
     * 起草阶段根据起草人所在公司起草相应的流程，针对pc端
     *
     * @return
     */
    private String getProcessName() {
        String processName;
        IUser iUser = SecurityUtils.getCurrentUser();
        /**根据起草人所在的公司起草相应的流程**/
        switch (iUser.getBelongCompanyTypeDictValue()) {
            case Constants.PROVINCIAL_CODE:
                processName = Constants.FLOW_PROVINCIAL;
                break;
            case Constants.BRANCH_CODE_03:
                processName = Constants.FLOW_BRANCH;
                break;
            case Constants.BRANCH_CODE:
                processName = Constants.FLOW_BRANCH;
                break;
            default:
                processName = Constants.FLOW_BRANCH;
                break;
        }
        return processName;
    }

    /**
     * 起草阶段根据起草页面类型
     *
     * @return
     */
    private String getProcessName(String processType) {
        String processName;
        switch (processType) {
            case Constants.PROCESS_A:
                processName = Constants.FLOW_PROVINCIAL;
                break;
            case Constants.PROCESS_B:
                processName = Constants.FLOW_BRANCH;
                break;
            case Constants.PROCESS_C:
                processName = Constants.FLOW_PROVINCIAL;
                break;
            case Constants.PROCESS_D:
                processName = Constants.FLOW_BRANCH;
                break;
            case Constants.PROCESS_E:
                processName = Constants.FLOW_PROVINCIAL;
                break;
            case Constants.PROCESS_F:
                processName = Constants.FLOW_BRANCH;
                break;
            case Constants.PROCESS_G:
                processName = Constants.FLOW_PROVINCIAL;
                break;
            case Constants.PROCESS_H:
                processName = Constants.FLOW_BRANCH;
                break;
            default:
                processName = Constants.ACTIVITY_PROVINCE_DEPART;
                break;
        }
        return processName;
    }

    /**
     * 起草阶段根据起草人所在公司起草相应的流程，针对手机端
     *
     * @param iUser 当前人
     * @return
     */
    private String getProcessNameMobile(IUser iUser) {
        String processName = null;
        /**根据起草人所在的公司起草相应的流程**/
        /**根据起草人所在的公司起草相应的流程**/
        switch (iUser.getBelongCompanyTypeDictValue()) {
            case Constants.PROVINCIAL_CODE:
                processName = Constants.FLOW_PROVINCIAL;
                break;
            case Constants.BRANCH_CODE_03:
                processName = Constants.FLOW_BRANCH;
                break;
            case Constants.BRANCH_CODE:
                processName = Constants.FLOW_BRANCH;
                break;
            default:
                processName = Constants.FLOW_BRANCH;
                break;
        }
        return processName;
    }

    /**
     * 起草阶段根据流程类型生成pmInstId，用于PC端
     *
     * @return
     */
    private String getPmInstId() {
        IUser iUser = SecurityUtils.getCurrentUser();
        return pmInstIdNormal(iUser);
    }

    /**
     * 起草阶段根据起草人所在公司生成相应的订单编号，用于手机端
     *
     * @param iUser
     * @return
     */
    private String getPmInstIdMobile(IUser iUser) {
        return pmInstIdNormal(iUser);
    }

    /**
     * 通用获取pmInstId
     *
     * @param iUser
     * @return
     */
    private String pmInstIdNormal(IUser iUser) {

        String pmInstId = idGenerator.getDateId().toString();
        switch (iUser.getBelongCompanyTypeDictValue()) {
            case Constants.PROVINCIAL_CODE:
                pmInstId = Constants.PROCESS_A + pmInstId;
                break;
            case Constants.BRANCH_CODE:
                pmInstId = Constants.PROCESS_B + pmInstId;
                break;
            default:
                break;
        }
        return pmInstId;
    }


    /**
     * 获取到流转到下一步提示信息
     *
     * @param nextUserName 审批人
     * @return
     */
    private String getTemplate(String nextUserName) {
        Map<String, String> paramMap = Maps.newHashMap();
        String showMessage = "";
        try {
            if (!StringUtils.isEmpty(nextUserName)) {
                // 有代理时才放开
                // String agentUser = getAgentUser( nextUserName );
                IUser user = uumsSysUserinfoApi.findByKey(nextUserName, IAuthService.KeyType.username, Constants.APP_CODE);
//审批人
                if (user != null) {
                    List<SimplePosition> simplePositionList = new ArrayList(user.getAuthPositions());
                    paramMap.put("companyName", user.getBelongCompanyName());
                    paramMap.put("departmentName", user.getBelongDepartmentName());
                    paramMap.put("trueName", user.getTruename());
                    paramMap.put("positionName", simplePositionList != null ? simplePositionList.get(0).getPositionName() :
                            "");
                    //有代理时才放开
                    /* if(!StringUtils.isEmpty( agentUser )){
                        IUser userAgent = uumsSysUserinfoApi.findByKey(agentUser, IAuthService.KeyType.username,
Constants.APP_CODE); //代理人
                        List<SimplePosition> simpleAgentPositionList = new ArrayList(userAgent.getAuthPositions());
                        paramMap.put("agentedCompanyName", userAgent.getBelongCompanyName());
                        paramMap.put("agentedDepartmentName", userAgent.getBelongDepartmentName());
                        paramMap.put("agentedTrueName", userAgent.getTruename());
                        paramMap.put("agentedPositionName", simpleAgentPositionList != null ? simpleAgentPositionList.get
(0).getPositionName() : "");
                        showMessage = MessageEnum.MW000005.getMessage((Map) paramMap);
                    }else{*/
                    showMessage = MessageEnum.MW000001.getMessage((Map) paramMap);
                    //}
                }
            } else {
                showMessage = Constants.SHOWMESSAGESUCCESS;
            }
        } catch (Exception e) {
            log.debug(e.toString());
        }
        return showMessage;
    }

    /**
     * 根据栏目编码获取栏目下的内容
     *
     * @param programaCode
     * @return
     */
    @Override
    public JsonResponse findDataFromProgramaCode(String programaCode, String title, String sourceType, Pageable pageable) {
        SysDictValue sysDictValue = new SysDictValue();
        sysDictValue.setDictType(Constants.SOURCE_TYPE);
        if (title == null) {
            title = ApplicationConstants.EMPTY;
        }
        String rownum = "5";//默认展示数量
        switch (programaCode) {
            case Constants.L_W_Y_L:
                rownum = "9";//廉闻要论展示数量
                break;
            case Constants.Y_A_S_J:
                rownum = "7";//以案示警展示数量
                break;
            case Constants.J_L_S_C:
                //   解决纪律与监督的查询只查011的  其实现在的几率与监督里面有三个模块  011001 纪律审查  011003 专项监督  011002 违反中央八项精神及四风问题
                String[] newCode = {"011", "011001", "011003", "011002"};
                rownum = "7";//纪律审查与监督展示数量
                List<ProgramaDataForm> programaDataInfoSet = repository.findDataFromProgramaCodeByCustom(newCode, title, rownum);
                return JsonResponse.success(programaDataInfoSet);
            case Constants.F_G_S:
                rownum = "7";//分公司动态展示数量
                break;

        }
        List<ProgramaDataForm> programaDataInfoSet = repository.findDataFromProgramaCode(programaCode, title, rownum);

        return JsonResponse.success(programaDataInfoSet);
    }

    @Override
    public JsonResponse findDataFromProgramaCodeByIportal(String programaCode, Pageable pageable) {
        Page<ProgramaDataForm> programaDataForm = repository.findProgramaDataForm(programaCode, pageable);


        List<ProgramaDataForm> content = programaDataForm.getContent();
        for (ProgramaDataForm dataForm : content) {
            StringBuilder stringBuilder = new StringBuilder();
            stringBuilder.append(appConfig.getAppHostPort() + "/hnjjwz/html/webSite/listDetails.html");
            //stringBuilder.append("?appcode=hnjjwz&loginuser="+ encrypt);
            stringBuilder.append("?id=" + dataForm.getId());
            stringBuilder.append("&pmInsId=" + dataForm.getPmInsId());
            dataForm.setIportalUrl(stringBuilder.toString());
            String creationTime = dataForm.getCreationTime();


            String formatDate = cn.hutool.core.date.DateUtil.format(cn.hutool.core.date.DateUtil.parse(creationTime), "MM- dd");
            dataForm.setCreationTime(formatDate);
            dataForm.setMainBody(null);
        }


        return JsonResponse.success(programaDataForm);
    }

    @Override
    public JsonResponse findDataByIportalList(String programaCode, Pageable pageable) {

        String[] list = programaCode.split(",");
        List<String> arrayList = CollUtil.newArrayList();
        for (String id : list) {
            arrayList.add(id);
        }
        Page<ProgramaDataForm> programaDataForm = repository.findDataByIportalList(arrayList, pageable);
        List<ProgramaDataForm> content = programaDataForm.getContent();
        for (ProgramaDataForm dataForm : content) {
            StringBuilder stringBuilder = new StringBuilder();
            stringBuilder.append(appConfig.getAppHostPort() + "/hnjjwz/html/webSite/listDetails.html");
            //stringBuilder.append("?appcode=hnjjwz&loginuser="+ encrypt);
            stringBuilder.append("?id=" + dataForm.getId());
            stringBuilder.append("&pmInsId=" + dataForm.getPmInsId());
            dataForm.setIportalUrl(stringBuilder.toString());
            String creationTime = dataForm.getCreationTime();
            String formatDate = cn.hutool.core.date.DateUtil.format(cn.hutool.core.date.DateUtil.parse(creationTime), "MM-dd");
            dataForm.setCreationTime(formatDate);
            dataForm.setMainBody(null);
        }
        return JsonResponse.success(programaDataForm);
    }


    /**
     * 查询栏目详情列表
     *
     * @param paramsMap
     * @param pageable
     * @return
     */
    @Override
    public JsonResponse findDataDetailList(Map<String, Object> paramsMap, Pageable pageable) {

        Page<Map<String, Object>> programaDataFormPage = null;
        String programaCodes = (String) paramsMap.get("programaCode");
        programaDataFormPage = repository.findDataFromProgramaCodeList(programaCodes, pageable);

        if (programaDataFormPage == null) {
            return JsonResponse.success(1, "该栏目下暂无数据");
        }


        List<Map<String, Object>> content = programaDataFormPage.getContent();
        List<Map<String, Object>> contentNew = new ArrayList<>();
        List<TemplateLayout> programa_code = iTemplateLayoutService.findtemplateLayout(programaCodes);
        ProgramaInfo byFromProCode = iProgramaInfoService.findByFromProCode(programaCodes);
        for (TemplateLayout templateLayout : programa_code) {
            Map<String, Object> map1 = new HashMap<>();
            map1.put("locationName", templateLayout.getLocationId());
            map1.put("pointUrl", templateLayout.getPointUrl());
            map1.put("programaDisplayName", byFromProCode.getProgramaDisplayName());
            contentNew.add(map1);
        }

        Pageable pageable1 = programaDataFormPage.getPageable();
        Map<String, Object> contentRes = Maps.newConcurrentMap();
        contentRes.put("contentDetial", content);
        contentRes.put("nav", contentNew);
        contentRes.put("pageable1", pageable1);
        contentRes.put("totalPages", programaDataFormPage.getTotalPages());
        contentRes.put("totalElements", programaDataFormPage.getTotalElements());
        contentRes.put("size", programaDataFormPage.getSize());
        contentRes.put("first", programaDataFormPage.isFirst());
        return JsonResponse.success(contentRes);
    }


    @Override
    public JsonResponse findDataDetailListNoPage(Map<String, Object> paramsMap) {

        List<Map<String, Object>> programaDataFormPage = null;
        String programaCodes = (String) paramsMap.get("programaCode");
        programaDataFormPage = repository.findDataFromProgramaCodeListNoPage(programaCodes);

        if (programaDataFormPage == null) {
            return JsonResponse.success(1, "该栏目下暂无数据");
        }
        return JsonResponse.success(programaDataFormPage);
    }

    @Override
    public JsonResponse findDataDetailList2(Map<String, Object> paramsMap, Pageable pageable) {
        //List<SimpleGroup> hnjjwz = uumsSysGroupApi.findGroupByUsernameNoPage("hnjjwz");
        String username = SecurityUtils.getCurrentUserName();
        IUser currentUser = SecurityUtils.getCurrentUser();
        Page<Map<String, Object>> programaDataFormPage;
        String title = (String) paramsMap.get("title");

        boolean Super_Control = false;
        String programaCodes = (String) paramsMap.get("programaCode");

        Set<SimpleRole> authRoles = (Set<SimpleRole>) currentUser.getAuthRoles();

        for (SimpleRole simpleRole : authRoles) {//遍历所有的角色
            if (Constants.SUPER_CONTROL.equals(simpleRole.getRoleCode()) || Constants.ROLE_S_CJGLY_APPLY.equals
                    (simpleRole.getRoleCode()) || Constants.ROLE_MANAGEMENT.equals(simpleRole.getRoleCode())) {
                Super_Control = true;// 如果所有角色中有纪检超级管理员就设置超级管理状态 进行管理查询
            }
        }

        if (Super_Control) {
            if (title == null) {
                title = "";
            }
            if (StringUtils.isEmpty(programaCodes) && StringUtils.isEmpty(title)) {
                programaDataFormPage = repository.findDataAll(pageable);//超级查询
            } else if (!StringUtils.isEmpty(title)) {
                programaDataFormPage = repository.findDataTableList(title, pageable);
            } else {
                List<String> programaCodeList = Arrays.asList(programaCodes.split(ApplicationConstants.COMMA));
                programaDataFormPage = repository.findDataDetailList(programaCodeList, title, pageable);
            }
        } else {//普通权限
            if (StringUtils.isEmpty(programaCodes) && StringUtils.isEmpty(title)) {
//                Map<String, String> sql1 = new HashMap<>();
//                sql1.put("firstParam", username);
//                List<Map<String, Object>> query = this.uumsQuerySqlUtil.uumsSelectBySql(sql1, "hnjjwz_007");
//                programaDataFormPage = new PageImpl(query, pageable, query.size());
                programaDataFormPage = repository.findDataNormal(pageable, username);//超级查询

            } else if (!StringUtils.isEmpty(title)) {
                programaDataFormPage = repository.findDataTableList(title, pageable);
            } else {
                List<String> programaCodeList = Arrays.asList(programaCodes.split(ApplicationConstants.COMMA));
                programaDataFormPage = repository.findDataDetailList(programaCodeList, title, pageable);
            }
        }

        if (programaDataFormPage == null) {//如果都是空
            //如果当前登录人 没有起草过 就去查审批过的起草进行显示
//            programaDataFormPage = repository.findUserNameApprovalDetailList(username, pageable);
            Map<String, String> sql1 = new HashMap<>();
            sql1.put("firstParam", username);
            List<Map<String, Object>> query = this.uumsQuerySqlUtil.uumsSelectBySql(sql1, "hnjjwz_008");
            programaDataFormPage = new PageImpl(query, pageable, query.size());
        }


        return JsonResponse.success(programaDataFormPage);
    }

    /**
     * 根据轮播图id查询栏目内容信息
     *
     * @param id
     * @return
     */
    @Override
    public JsonResponse findProgramaDataFromSlide(String id) {
        ProgramaDataForm programaDataForm = repository.findProgramaDataFromSlide(id);
        if (programaDataForm == null) {

            return JsonResponse.success(iSlideShowService.findById(id));
        }
        return JsonResponse.success(programaDataForm);
    }


    /**
     * 根据模板编码以及模板部位id来出栏目内容列表
     *
     * @return
     */
    @Override
    public JsonResponse findProgramaDataList(Map<String, Object> map) {
        String templateCode = (String) map.get("templateCode");
        String templateLayoutId = (String) map.get("templateLayoutId");
        Set<ProgramaDataForm> programaDataFormSet = repository.findProgramaDataList(templateCode, templateLayoutId);
        return JsonResponse.success(programaDataFormSet);
    }

    /**
     * 根据登录人查询栏目详情列表
     *
     * @return
     */
    @Override
    public JsonResponse findUserNameDetailList(String appCode, Pageable pageable) {


        return null;
    }

    @Override
    public List<ProgramaDataForm> findProgramaDataForm(String programaCode) {

        List<ProgramaDataForm> programaDataForm;
        if (programaCode.contains("012")) {//进行控制 廉洁文化 故事这些展示四个 其他的展示五个
            programaDataForm = repository.findProgramaDataFormFour(programaCode);
        } else if (programaCode.contains("018") || programaCode.contains("017") || programaCode.contains("019")) {
            programaDataForm = repository.findProgramaDataFormThree(programaCode);
        } else {
            programaDataForm = repository.findProgramaDataForm(programaCode);

        }
        for (ProgramaDataForm programaDataForm1 : programaDataForm) {
            String pmInsId = programaDataForm1.getPmInsId();
            List<SysFile> imageFileList = null;
            if (!StringUtils.isEmpty(pmInsId)) {
                imageFileList = fileExtendService.getPartFile(pmInsId, Constants.IMAGE_FILE);
                programaDataForm1.setCoverImageFileList(imageFileList);
            }
            if (imageFileList.size() == 0) {
                List<SysFile> imageFileList2 = fileExtendService.getPartFile(pmInsId, Constants.ACCESSORY_FILE);
                programaDataForm1.setAccessoryFileList(imageFileList2);
            }

        }

        return programaDataForm;
    }

    @Override
    public JsonResponse findProgramaDataFormRelation(String id) {
        return JsonResponse.success(repository.findProgramaDataFormRelation(id));
    }

    /**
     * 置顶功能
     *
     * @param id
     * @return
     */
    @Override
    public JsonResponse stick(String pmInsId, String id) {
        ProgramaDataForm programaDataFormRelation = repository.getProgramaDataFormPmInsId(pmInsId);

        int stickFlag = programaDataFormRelation.getStickFlag();
        int stickFlagBack = stickFlag + 1;

        ProgramaDataForm relation = repository.findProgramaDataFormRelation(id);
        relation.setStickFlag(stickFlagBack);
        ProgramaDataForm update = this.update(relation);
        if (update != null) {
            return JsonResponse.success(1, "置顶成功");
        } else {
            return JsonResponse.fail(-1, "置顶失败");

        }

    }

    @Override
    public Page<ProgramaDataForm> findArticlePageByColumnId(List<String> columnIds, int page, int size) {
        Pageable pageable = this.getPageable(page, size, null, null);
        Page<ProgramaDataForm> articlePageByColumnId = repository.findArticlePageByColumnId(pageable, columnIds);
        List<ProgramaDataForm> content = articlePageByColumnId.getContent();
        if (CollUtil.isNotEmpty(content)) {
            for (ProgramaDataForm programaDataForm : content) {
                List<SysFile> imageFileList = fileExtendService.getPartFile(programaDataForm.getPmInsId(),
                        Constants.IMAGE_FILE);
                List<SysFile> authBookFileList = fileExtendService.getPartFile(programaDataForm.getPmInsId(),
                        Constants.AUTH_BOOK_FILE);
                List<SysFile> accessroyFileList = fileExtendService.getPartFile(programaDataForm.getPmInsId(),
                        Constants.ACCESSORY_FILE);
                List<SysFile> videoFileList = fileExtendService.getPartFile(programaDataForm.getPmInsId(),
                        Constants.VIDEO_FILE);
                putFile(imageFileList, programaDataForm, Constants.IMAGE_FILE);
                putFile(authBookFileList, programaDataForm, Constants.AUTH_BOOK_FILE);
                putFile(accessroyFileList, programaDataForm, Constants.ACCESSORY_FILE);
                putFile(videoFileList, programaDataForm, Constants.VIDEO_FILE);
            }
        }
        return articlePageByColumnId;
    }

    @Override
    public List<ProgramaDataForm> getProgramaDataList(List<String> columnIds) {
        List<ProgramaDataForm> programaDataList = repository.getProgramaDataList(columnIds);
        if (CollUtil.isNotEmpty(programaDataList)) {
            for (ProgramaDataForm programaDataForm : programaDataList) {
                List<SysFile> imageFileList = fileExtendService.getPartFile(programaDataForm.getPmInsId(),
                        Constants.IMAGE_FILE);
                List<SysFile> authBookFileList = fileExtendService.getPartFile(programaDataForm.getPmInsId(),
                        Constants.AUTH_BOOK_FILE);
                List<SysFile> accessroyFileList = fileExtendService.getPartFile(programaDataForm.getPmInsId(),
                        Constants.ACCESSORY_FILE);
                List<SysFile> videoFileList = fileExtendService.getPartFile(programaDataForm.getPmInsId(),
                        Constants.VIDEO_FILE);
                putFile(imageFileList, programaDataForm, Constants.IMAGE_FILE);
                putFile(authBookFileList, programaDataForm, Constants.AUTH_BOOK_FILE);
                putFile(accessroyFileList, programaDataForm, Constants.ACCESSORY_FILE);
                putFile(videoFileList, programaDataForm, Constants.VIDEO_FILE);
            }
        }
        return programaDataList;
    }

    @Override
    public JsonResponse getDataArticleById(String articleId) {
        ProgramaDataForm byId = this.findById(articleId);
        if (ObjectUtil.isNotEmpty(byId)) {
            int viewsNumber = byId.getViewsNumber();
            byId.setViewsNumber(viewsNumber + 1);
            ProgramaDataForm update = this.update(byId);

            List<SysFile> imageFileList = fileExtendService.getPartFile(update.getPmInsId(), Constants.IMAGE_FILE);
            if (imageFileList.size() > 0) {
                putFile(imageFileList, update, Constants.IMAGE_FILE);
            }
            return JsonResponse.success(update);
        }
        return JsonResponse.defaultErrorResponse();
    }

    @Override
    public ProgramaDataForm saveSlideShow(SlideShow slideShow) {

        ProgramaDataForm programaDataForm = repository.getFromDeceiptCodeByPm(slideShow.getPmInsId());
        if (ObjectUtil.isNotEmpty(programaDataForm)){
            return programaDataForm;
        }

        loginUtils.manualLogin(encryptor.encrypt(slideShow.getCreator()), Constants.APP_CODE);
        IUser iUser = SecurityUtils.getCurrentUser();

        ProgramaDataForm form = new ProgramaDataForm();
        String programaDisplayName = slideShow.getProgramaDisplayName();
        if(StringUtil.isEmpty(programaDisplayName)){
            programaDisplayName = "首页轮播新闻";
        }
        SysNewColumnModel newColumnModel = newColumnService.findOneByAllColumnName(programaDisplayName);
        form.setCreatedTime(slideShow.getCreatedTime());
        form.setModifiedTime(slideShow.getModifiedTime());
        form.setCreator(slideShow.getCreator());
        form.setModifier(slideShow.getModifier());
        form.setEnabled(Boolean.TRUE);
        form.setBelongCompanyCode(iUser.getBelongCompanyCode());
        form.setBelongCompanyName(iUser.getBelongCompanyName());
        form.setBelongDepartmentCode(iUser.getBelongDepartmentCode());
        form.setBelongDepartmentName(iUser.getBelongDepartmentName());
        form.setBelongCompanyTypeDictValue(iUser.getBelongCompanyTypeDictValue());
        form.setBelongOrgCode(iUser.getBelongOrgCode());
        form.setBelongOrgName(iUser.getBelongOrgName());
        form.setCreationTime(slideShow.getCreationTime());
        form.setIsPublish(Boolean.TRUE);
        form.setPmInsId(slideShow.getPmInsId());
        form.setProgramaCode(newColumnModel.getColumnId());
        form.setProgramaDisplayName(newColumnModel.getColumnAllName());
        form.setTitle(slideShow.getSlideShowTitle());
        form.setTruename(slideShow.getTruename());
        form.setUsername(slideShow.getUsername());
        form.setMainBody(slideShow.getMainBody());
        form.setCompany(iUser.getBelongCompanyName());
        form.setDepartmentName(iUser.getBelongDepartmentName());
        form.setDisplayName(iUser.getBelongCompanyName() + "\\" + iUser.getBelongDepartmentName());
        form.setStickFlag(slideShow.getStickFlag());
        form.setImportantFlag(slideShow.getImportantFlag());
        form.setViewsNumber(slideShow.getViewsNumber());
        ProgramaDataForm insert = this.insert(form);
        return insert;
    }


    @Override
    public ProgramaDataForm saveAnnouncement(Announcement announcement) {
        ProgramaDataForm programaDataForm = repository.getFromDeceiptCodeByPm(announcement.getPmInsId());
        if (ObjectUtil.isNotEmpty(programaDataForm)){
            return programaDataForm;
        }

        loginUtils.manualLogin(encryptor.encrypt(announcement.getCreator()), Constants.APP_CODE);
        IUser iUser = SecurityUtils.getCurrentUser();

        ProgramaDataForm form = new ProgramaDataForm();
        String programaDisplayName = announcement.getProgramaDisplayName();
        if(StringUtil.isEmpty(programaDisplayName)){
            programaDisplayName = "公告";
        }
        SysNewColumnModel newColumnModel = newColumnService.findOneByAllColumnName(announcement.getProgramaDisplayName());
        form.setCreatedTime(announcement.getCreatedTime());
        form.setModifiedTime(announcement.getModifiedTime());
        form.setCreator(announcement.getCreator());
        form.setModifier(announcement.getModifier());
        form.setEnabled(Boolean.TRUE);
        form.setBelongCompanyCode(iUser.getBelongCompanyCode());
        form.setBelongCompanyName(iUser.getBelongCompanyName());
        form.setBelongDepartmentCode(iUser.getBelongDepartmentCode());
        form.setBelongDepartmentName(iUser.getBelongDepartmentName());
        form.setBelongCompanyTypeDictValue(iUser.getBelongCompanyTypeDictValue());
        form.setBelongOrgCode(iUser.getBelongOrgCode());
        form.setBelongOrgName(iUser.getBelongOrgName());
        form.setCreationTime(announcement.getCreationTime());
        form.setIsPublish(Boolean.TRUE);
        form.setPmInsId(announcement.getPmInsId());
        form.setProgramaCode(newColumnModel.getColumnId());
        form.setProgramaDisplayName(newColumnModel.getColumnAllName());
        form.setTitle(announcement.getAnnouncementTitle());
        form.setTruename(announcement.getTruename());
        form.setUsername(announcement.getUsername());
        form.setMainBody(announcement.getAnnouncementInfo());
        form.setCompany(iUser.getBelongCompanyName());
        form.setDepartmentName(iUser.getBelongDepartmentName());
        form.setDisplayName(iUser.getBelongCompanyName() + "\\" + iUser.getBelongDepartmentName());
        form.setStickFlag(announcement.getStickFlag());
        form.setImportantFlag(announcement.getImportantFlag());
        form.setViewsNumber(announcement.getViewsNumber());
        ProgramaDataForm insert = this.insert(form);
        return insert;
    }

    @Override
    public JsonResponse recommendedArticle(String pmInsId) {
        ProgramaDataForm form = repository.getFromDeceiptCodeByPm(pmInsId);
        String id = form.getId();
        String programaCode = form.getProgramaCode();
        List<ProgramaDataForm> programaDataForms = repository.recommendedArticle(id, programaCode);
        if (CollectionUtil.isNotEmpty(programaDataForms)) {
            for (ProgramaDataForm programaDataForm : programaDataForms) {
                List<SysFile> imageFileList = fileExtendService.getPartFile(programaDataForm.getPmInsId(),
                        Constants.IMAGE_FILE);
                List<SysFile> authBookFileList = fileExtendService.getPartFile(programaDataForm.getPmInsId(),
                        Constants.AUTH_BOOK_FILE);
                List<SysFile> accessroyFileList = fileExtendService.getPartFile(programaDataForm.getPmInsId(),
                        Constants.ACCESSORY_FILE);
                List<SysFile> videoFileList = fileExtendService.getPartFile(programaDataForm.getPmInsId(),
                        Constants.VIDEO_FILE);
                putFile(imageFileList, programaDataForm, Constants.IMAGE_FILE);
                putFile(authBookFileList, programaDataForm, Constants.AUTH_BOOK_FILE);
                putFile(accessroyFileList, programaDataForm, Constants.ACCESSORY_FILE);
                putFile(videoFileList, programaDataForm, Constants.VIDEO_FILE);
            }
        }
        return JsonResponse.success(programaDataForms);
    }
}
