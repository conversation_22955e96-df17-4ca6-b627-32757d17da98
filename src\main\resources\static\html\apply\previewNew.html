<!DOCTYPE html>
<html xmlns="http://www.w3.org/1999/xhtml" xmlns:th="http://www.thymeleaf.org">

<head>
    <title>河南移动新闻中心</title>
    <meta http-equiv="Content-Type" content="text/html; charset=utf-8" />
    <meta http-equiv="X-UA-Compatible" content="IE=edge" />
    <meta name="viewport" content="width=device-width, initial-scale=1, shrink-to-fit=no" />
    <script src="http://************:8088/simbestui/js/jquery.min.js?v=svn.revision"
        th:src="@{http://************:8088/simbestui/js/jquery.min.js?v=svn.revision}" type="text/javascript"></script>
    <script src="http://************:8088/simbestui/js/jquery.aduq.js?v=svn.revision"
        th:src="@{http://************:8088/simbestui/js/jquery.aduq.js?v=svn.revision}" type="text/javascript"></script>
    <style>
        * {
            padding: 0;
            margin: 0;
            box-sizing: border-box;
        }

        body {
            font-family: Microsoft Yahei, 微软雅黑, arial, 宋体, sans-serif;
            ;
            text-rendering: optimizeLegibility;
            font-size: 14px;
            margin: 0 !important;
        }

        .website-layout {
            min-height: 100vh;
            display: flex;
            flex-direction: column;
        }

        .main-content {
            flex: 1;
            min-height: calc(100vh - 200px);
            /* 减去头部和底部的高度 */
        }

        .website-header {
            width: 100%;
            position: relative;
            z-index: 1000;
        }

        /* 横幅区域 */
        .header-banner {
            width: 100%;
            height: 136px;
            position: relative;
            overflow: hidden;
        }

        .banner-content {
            width: 100%;
            height: 100%;
            position: relative;
        }

        .banner-bg {
            width: 100%;
            height: 100%;
            position: relative;
        }

        .banner-image {
            width: 100%;
            height: 100%;
            object-fit: cover;
            display: block;
        }

        .banner-overlay {
            position: absolute;
            top: 0;
            left: 0;
            right: 0;
            bottom: 0;
            background: linear-gradient(135deg, rgba(220, 20, 60, 0.6) 0%, rgba(255, 140, 0, 0.6) 100%);
            display: flex;
            align-items: center;
            justify-content: center;
        }

        .banner-title {
            color: white;
            font-size: 36px;
            font-weight: bold;
            text-shadow: 2px 2px 4px rgba(0, 0, 0, 0.5);
            margin: 0;
            text-align: center;
        }

        /* 导航菜单区域 */
        .header-nav {
            background-image: linear-gradient(90deg,
                    #e70800 0%,
                    #fdb900 100%);
            box-shadow: 0 2px 8px rgba(0, 0, 0, 0.1);
        }

        .nav-container {
            max-width: 1200px;
            margin: 0 auto;
            padding: 0 20px;
        }

        .nav-menu {
            display: flex;
            list-style: none;
            margin: 0;
            padding: 0;
            align-items: center;
            width: 100%;
            justify-content: space-around;
        }

        .nav-item {
            position: relative;
            cursor: pointer;
        }

        .nav-link {
            display: block;
            padding: 15px 25px;
            color: white;
            text-decoration: none;
            font-size: 22px;
            font-weight: 500;
            transition: all 0.3s ease;
            border-radius: 0;
            letter-spacing: 3px;
        }

        .nav-link:hover {
            background-color: rgba(255, 255, 255, 0.1);
            color: white;
        }

        .nav-item.active .nav-link {
            background-color: rgba(255, 255, 255, 0.2);
            color: white;
        }

        .nav-item.active::after {
            content: '';
            position: absolute;
            bottom: 0;
            left: 50%;
            transform: translateX(-50%);
            width: 0;
            height: 0;
            border-left: 8px solid transparent;
            border-right: 8px solid transparent;
            border-bottom: 8px solid white;
        }

        .website-footer {
            background-color: #e60000;
            color: #ffffff;
            padding: 20px 0;
            margin-top: auto;
            width: 100%;
        }

        .footer-content {
            max-width: 1200px;
            margin: 0 auto;
            padding: 0 20px;
            text-align: center;
        }

        .footer-info {
            display: flex;
            flex-direction: column;
            align-items: center;
            gap: 8px;
        }

        .copyright {
            margin: 0;
            font-size: 14px;
            color: #fff;
            font-weight: 500;
        }

        .contact-info {
            margin: 0;
            font-size: 13px;
            color: #999999;
            line-height: 1.5;
        }

        .detail-page {
            padding: 20px;
            background-color: #f5f5f5;
            min-height: 100vh;
        }

        .detail-container {
            max-width: 1200px;
            margin: 0 auto;
            background: #fff;
            padding: 30px;
            border-radius: 8px;
            box-shadow: 0 2px 8px rgba(0, 0, 0, 0.1);
        }

        .article-title {
            font-size: 24px;
            font-weight: bold;
            color: #333;
            text-align: center;
            margin-bottom: 20px;
        }

        .article-meta {
            text-align: center;
            color: #888;
            font-size: 14px;
            margin-bottom: 30px;
            border-bottom: 1px solid #eee;
            padding-bottom: 15px;
        }

        .meta-item {
            margin: 0 10px;
        }

        .article-content {
            line-height: 1.8;
            color: #555;
            font-size: 16px;
            margin-bottom: 40px;
            /* 与相关文章部分留出间距 */
        }

        /* 基本富文本样式覆盖，确保内部 HTML 标签正常显示 */
        .article-content>>>p {
            margin-bottom: 1em;
        }

        .article-content>>>strong,
        .article-content>>>b {
            font-weight: bold;
        }

        .article-content>>>em,
        .article-content>>>i {
            font-style: italic;
        }

        .article-content>>>span {
            /* 根据需要调整 span 的样式 */
        }

        .article-content>>>table {
            border-collapse: collapse;
            width: 100%;
            margin-bottom: 1em;
        }

        .article-content>>>th,
        .article-content>>>td {
            border: 1px solid #ddd;
            padding: 8px;
        }

        .article-content>>>th {
            background-color: #f2f2f2;
            text-align: left;
        }

        .article-content>>>img {
            max-width: 100%;
            height: auto;
        }

        /* 相关文章样式 */
        .related-articles {
            margin-top: 30px;
            padding-top: 20px;
            border-top: 1px solid #eee;
            /* 可选：顶部边框 */
        }

        .related-title {
            display: flex;
            align-items: center;
            margin-bottom: 20px;
        }

        .title-icon {
            display: inline-block;
            width: 4px;
            height: 18px;
            background-color: #f44336;
            /* 红色 */
            margin-right: 8px;
            border-radius: 2px;
        }

        .related-title h3 {
            font-size: 18px;
            font-weight: bold;
            color: #333;
            margin: 0;
        }

        .related-list {
            list-style: none;
            padding: 0;
            margin: 0;
        }

        .related-item {
            display: flex;
            align-items: center;
            padding: 12px 0;
            border-bottom: 1px dashed #ddd;
            /* 虚线 */
            cursor: pointer;
            transition: color 0.2s ease;
        }

        .related-item:last-child {
            border-bottom: none;
        }

        .related-item:hover .article-link {
            color: #f44336;
            /* 悬停变红 */
        }

        .bullet-point {
            display: inline-block;
            width: 6px;
            height: 6px;
            background-color: #f44336;
            /* 红色点 */
            border-radius: 50%;
            margin-right: 10px;
            flex-shrink: 0;
            /* 防止flex item缩小 */
        }

        .article-link {
            flex-grow: 1;
            /* 占据剩余空间 */
            margin-right: 10px;
            /* 与日期留出间距 */
            color: #555;
            font-size: 15px;
            overflow: hidden;
            text-overflow: ellipsis;
            white-space: nowrap;
        }

        .article-date {
            color: #888;
            font-size: 14px;
            flex-shrink: 0;
            /* 防止flex item缩小 */
        }
    </style>
    <script>
        $(function () {
            var formData = {}
            if (sessionStorage.getItem('formData')) {
                formData = JSON.parse(sessionStorage.getItem('formData'));
            }
            if (formData.type == 'silde') {
                $(".article-title").html(formData.slideShowTitle);
                if (formData.slideShowFile.length > 0) {
                    // $(".newsContent img").attr("src", formData.slideShowFile[0].mobileFilePath);
                    // $(".newsContent img").show();
                }
            } else if (formData.type == 'announcement') {
              $(".article-title").html(formData.announcementTitle);
            } else if (formData.type == 'programaData') {
               $(".article-title").html(formData.title);
            }
            if(formData.articleStartTime){
                $('.time').html('发布时间：'+formData.articleStartTime)
            }else{
                $('.time').html('发布时间：')
            }

            if(formData.articleFrom){
                $('.source').html('来源：'+formData.articleFrom)
            }else{
                $('.source').html('来源：')
            }
            if(formData.importantFlag == '1'){
                 $(".article-title").css({"color":"#c90100",'font-weight':'bold'})
            }
            $(".article-content").html(formData.mainBody);

        })
    </script>
</head>

<body>
    <div class="website-layout">
        <div class="website-header">
            <!-- 顶部横幅区域 -->
            <div class="header-banner">
                <div class="banner-content">
                    <div class="banner-bg">
                        <img src="../../images/index_top.png" alt="河南移动党风廉政视窗" class="banner-image" />
                    </div>
                </div>
            </div>
            <div class="header-nav">
                <div class="nav-container">
                    <ul class="nav-menu">
                        <li class="nav-item">
                            <div class="nav-link">首页</div>
                        </li>
                        <li class="nav-item">
                            <div class="nav-link">信息公开</div>
                        </li>
                        <li class="nav-item">
                            <div class="nav-link">新闻动态</div>
                        </li>
                        <li class="nav-item">
                            <div class="nav-link">法规制度</div>
                        </li>
                        <li class="nav-item">
                            <div class="nav-link">警示曝光</div>
                        </li>
                        <li class="nav-item">
                            <div class="nav-link">廉洁文化</div>

                        </li>
                        <li class="nav-item">
                            <div class="nav-link">信息报送</div>
                        </li>
                    </ul>
                </div>
            </div>
        </div>

        <div class="main-content">
            <div class="detail-page">
                <div class="detail-container">
                    <h1 class="article-title"></h1>
                    <div class="article-meta">
                        <span class="meta-item time"></span>
                        <span class="meta-item source"></span>
                        <span class="meta-item"></span>浏览量：0</span>
                    </div>
                    <div class="article-content"></div>

                    <!-- 相关文章 -->
                    <!-- <div class="related-articles">
                        <div class="related-title">
                            <span class="title-icon"></span>
                            <h3>相关文章</h3>
                        </div>
                        <ul class="related-list">
                            <li
                        class="related-item"
                        v-for="item in recommendedArticles"
                        :key="item.id"
                        @click="goToRelatedDetail(item)"
                    >
                        <span class="bullet-point"></span>
                        <span class="article-link">{{ util.htmlDecode(item.title) }}</span>
                        <span class="article-date">{{ formatDate(item.createdTime) }}</span>
                    </li>
                        </ul>
                    </div> -->
                </div>
            </div>

        </div>


        <div class="website-footer">
            <div class="footer-content">
                <div class="footer-info">
                    <p class="copyright">©版权所有 中国移动通信集团河南有限公司</p>
                    <p class="contact-info"></p>
                </div>
            </div>
        </div>

    </div>
</body>

</html>