package com.simbest.boot.hnjjwz.sharingPlatform.model;

import com.simbest.boot.base.annotations.EntityIdPrefix;
import com.simbest.boot.base.model.LogicModel;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.*;
import org.hibernate.annotations.GenericGenerator;

import javax.persistence.*;

/**
 * <AUTHOR>
 * @Data 2019/05/06
 * Description 菜单配置
 */
@Data
@EqualsAndHashCode(callSuper = true)
@NoArgsConstructor
@AllArgsConstructor
@Entity(name = "us_menu_deploy")
@ApiModel(value = "菜单配置")
public class MenuDeploy extends LogicModel {
    @Id
    @Column(name = "id", length = 40)
    @GeneratedValue(generator = "snowFlakeId")
    @GenericGenerator (name = "snowFlakeId", strategy = "com.simbest.boot.util.distribution.id.SnowflakeId")
    @EntityIdPrefix (prefix = "MD") //主键前缀，此为可选项注解
    private String id;

    @Column(length = 250)
    @Setter
    @Getter
    @ApiModelProperty(value = "菜单名", required = true)
    private String menuName;

    @Column(length = 100)
    @Setter
    @Getter
    @ApiModelProperty(value = "父菜单id", required = true)
    private String parentMenuId;

    @Column(length = 100)
    @Setter
    @Getter
    @ApiModelProperty(value = "同一个父菜单下的排序", required = true)
    private String displayOrder;//菜单排序

    @Column(length = 100)
    @Setter
    @Getter
    @ApiModelProperty(value = "菜单url", required = true)
    private String url;//点击菜单弹出的页面

    @Column(length = 500)
    @Setter
    @Getter
    @ApiModelProperty(value = "菜单全路径", required = true)
    private String displayName;//菜单全路径

    @Column(length = 500)
    @Setter
    @Getter
    @ApiModelProperty(value = "父菜单名", required = true)
    private String parentMenuName;//父菜单名

    @Column(length = 500)
    @Setter
    @Getter
    @ApiModelProperty(value = "父菜单全路径", required = true)
    private String parentMenuDisplayName;//父菜单全路径

    @Column(length = 250)
    @Setter
    @Getter
    @ApiModelProperty(value = "额外字段1", required = true)
    private String spare1;

    @Column(length = 250)
    @Setter
    @Getter
    @ApiModelProperty(value = "额外字段2", required = true)
    private String spare2;

    @Column(length = 250)
    @Setter
    @Getter
    @ApiModelProperty(value = "额外字段3", required = true)
    private String spare3;

}
