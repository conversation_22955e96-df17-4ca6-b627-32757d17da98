
package com.cmcc.mss.osb_oa_nma_inquirymeetinginvoicesrv;

import java.util.ArrayList;
import java.util.List;
import javax.xml.bind.annotation.XmlAccessType;
import javax.xml.bind.annotation.XmlAccessorType;
import javax.xml.bind.annotation.XmlElement;
import javax.xml.bind.annotation.XmlType;


/**
 * <p>OSB_OA_NMA_InquiryMeetingInvoiceSrvOutputCollection complex type的 Java 类。
 * 
 * <p>以下模式片段指定包含在此类中的预期内容。
 * 
 * <pre>
 * &lt;complexType name="OSB_OA_NMA_InquiryMeetingInvoiceSrvOutputCollection"&gt;
 *   &lt;complexContent&gt;
 *     &lt;restriction base="{http://www.w3.org/2001/XMLSchema}anyType"&gt;
 *       &lt;sequence&gt;
 *         &lt;element name="OSB_OA_NMA_InquiryMeetingInvoiceSrvOutputItem" type="{http://mss.cmcc.com/OSB_OA_NMA_InquiryMeetingInvoiceSrv}OSB_OA_NMA_InquiryMeetingInvoiceSrvOutputItem" maxOccurs="unbounded" minOccurs="0"/&gt;
 *       &lt;/sequence&gt;
 *     &lt;/restriction&gt;
 *   &lt;/complexContent&gt;
 * &lt;/complexType&gt;
 * </pre>
 * 
 * 
 */
@XmlAccessorType(XmlAccessType.FIELD)
@XmlType(name = "OSB_OA_NMA_InquiryMeetingInvoiceSrvOutputCollection", propOrder = {
    "osboanmaInquiryMeetingInvoiceSrvOutputItem"
})
public class OSBOANMAInquiryMeetingInvoiceSrvOutputCollection {

    @XmlElement(name = "OSB_OA_NMA_InquiryMeetingInvoiceSrvOutputItem")
    protected List<OSBOANMAInquiryMeetingInvoiceSrvOutputItem> osboanmaInquiryMeetingInvoiceSrvOutputItem;

    /**
     * Gets the value of the osboanmaInquiryMeetingInvoiceSrvOutputItem property.
     * 
     * <p>
     * This accessor method returns a reference to the live list,
     * not a snapshot. Therefore any modification you make to the
     * returned list will be present inside the JAXB object.
     * This is why there is not a <CODE>set</CODE> method for the osboanmaInquiryMeetingInvoiceSrvOutputItem property.
     * 
     * <p>
     * For example, to add a new item, do as follows:
     * <pre>
     *    getOSBOANMAInquiryMeetingInvoiceSrvOutputItem().add(newItem);
     * </pre>
     * 
     * 
     * <p>
     * Objects of the following type(s) are allowed in the list
     * {@link OSBOANMAInquiryMeetingInvoiceSrvOutputItem }
     * 
     * 
     */
    public List<OSBOANMAInquiryMeetingInvoiceSrvOutputItem> getOSBOANMAInquiryMeetingInvoiceSrvOutputItem() {
        if (osboanmaInquiryMeetingInvoiceSrvOutputItem == null) {
            osboanmaInquiryMeetingInvoiceSrvOutputItem = new ArrayList<OSBOANMAInquiryMeetingInvoiceSrvOutputItem>();
        }
        return this.osboanmaInquiryMeetingInvoiceSrvOutputItem;
    }

    public void setOsboanmaInquiryMeetingInvoiceSrvOutputItem(List<OSBOANMAInquiryMeetingInvoiceSrvOutputItem> osboanmaInquiryMeetingInvoiceSrvOutputItem) {
        this.osboanmaInquiryMeetingInvoiceSrvOutputItem = osboanmaInquiryMeetingInvoiceSrvOutputItem;
    }
}
