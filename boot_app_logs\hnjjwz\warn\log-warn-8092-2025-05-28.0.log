2025-05-28 08:44:20.403 [restartedMain] WARN  com.alibaba.cloud.nacos.client.NacosPropertySourceBuilder.loadNacosData Line:87 - Ignore the empty nacos configuration and get it based on dataId[hnjjwz-custom-obuat.properties] & group[DEFAULT_GROUP]
2025-05-28 08:44:20.415 [restartedMain] WARN  com.alibaba.cloud.nacos.client.NacosPropertySourceBuilder.loadNacosData Line:87 - Ignore the empty nacos configuration and get it based on dataId[hnjjwz] & group[DEFAULT_GROUP]
2025-05-28 08:44:20.424 [restartedMain] WARN  com.alibaba.cloud.nacos.client.NacosPropertySourceBuilder.loadNacosData Line:87 - Ignore the empty nacos configuration and get it based on dataId[hnjjwz.properties] & group[DEFAULT_GROUP]
2025-05-28 08:44:22.783 [restartedMain] WARN  org.springframework.boot.actuate.endpoint.EndpointId.logWarning Line:155 - Endpoint ID 'service-registry' contains invalid characters, please migrate to a valid format.
2025-05-28 08:44:23.632 [restartedMain] WARN  com.alibaba.druid.pool.DruidDataSource.initCheck Line:1314 - removeAbandoned is true, not use in production.
2025-05-28 08:45:27.737 [restartedMain] WARN  com.simbest.boot.SimbestApplication.onApplicationEvent Line:60 - 加载环境信息为: 【obuat】
2025-05-28 08:45:27.738 [restartedMain] WARN  com.simbest.boot.SimbestApplication.onApplicationEvent Line:61 - Application started successfully, lets go and have fun......
2025-05-28 08:45:27.738 [restartedMain] WARN  com.simbest.boot.SimbestApplication.onApplicationEvent Line:71 - 
---------------------------------------------------------
	应用已成功启动，运行地址如下：:
	Local:		http://localhost:8092/hnjjwz
	External:	http://***********:8092/hnjjwz
Aplication started successfully, lets go and have fun......
---------------------------------------------------------

2025-05-28 08:50:00.026 [pool-14-thread-7] WARN  com.simbest.boot.base.repository.CustomDynamicWhere.queryForList Line:50 - 自定义查询SQL输出为：【select * from US_TODO_MODEL t  where t.send_flag = 0 and t.work_flag = 1 and t.enabled =1】
2025-05-28 08:50:00.027 [pool-14-thread-6] WARN  com.simbest.boot.base.repository.CustomDynamicWhere.queryForList Line:50 - 自定义查询SQL输出为：【select * from US_TODO_MODEL t  where t.send_flag = 0 and t.work_flag = 0 and t.enabled =1】
2025-05-28 08:55:00.015 [pool-14-thread-9] WARN  com.simbest.boot.base.repository.CustomDynamicWhere.queryForList Line:50 - 自定义查询SQL输出为：【select * from US_TODO_MODEL t  where t.send_flag = 0 and t.work_flag = 1 and t.enabled =1】
2025-05-28 08:55:00.015 [pool-14-thread-8] WARN  com.simbest.boot.base.repository.CustomDynamicWhere.queryForList Line:50 - 自定义查询SQL输出为：【select * from US_TODO_MODEL t  where t.send_flag = 0 and t.work_flag = 0 and t.enabled =1】
2025-05-28 08:58:22.549 [http-nio-8092-exec-13] WARN  com.simbest.boot.security.auth.service.impl.AuthUserCacheServiceImpl.loadCacheUser Line:118 - 无法通过关键字【auth:user:chenhong】读取用户主键ID
2025-05-28 08:58:22.809 [http-nio-8092-exec-13] WARN  com.simbest.boot.security.auth.service.impl.AuthUserCacheServiceImpl.saveOrUpdateCacheUser Line:64 - 即将在缓存中将用户权限置为空----SET AuthPermissions EMPTY----【12704】
2025-05-28 09:00:00.024 [pool-14-thread-3] WARN  com.simbest.boot.base.repository.CustomDynamicWhere.queryForList Line:50 - 自定义查询SQL输出为：【select * from US_TODO_MODEL t  where t.send_flag = 0 and t.work_flag = 0 and t.enabled =1】
2025-05-28 09:00:00.028 [pool-14-thread-8] WARN  com.simbest.boot.cmcc.a4.task.A4TaskCreateXmlFile.execute Line:76 - >>>>>>>>>>>>>>>>>>>>>>>>>>>>>>>>>>>>>>>>>>>>>>>>>>>>>A4TaskCreateXmlFile:执行定时任务但不是Prd环境【2025-05-28T09:00:00.028】
2025-05-28 09:00:00.035 [pool-14-thread-10] WARN  com.simbest.boot.base.repository.CustomDynamicWhere.queryForList Line:50 - 自定义查询SQL输出为：【select * from US_TODO_MODEL t  where t.send_flag = 0 and t.work_flag = 1 and t.enabled =1】
2025-05-28 09:05:00.008 [pool-14-thread-3] WARN  com.simbest.boot.base.repository.CustomDynamicWhere.queryForList Line:50 - 自定义查询SQL输出为：【select * from US_TODO_MODEL t  where t.send_flag = 0 and t.work_flag = 1 and t.enabled =1】
2025-05-28 09:05:00.011 [pool-14-thread-4] WARN  com.simbest.boot.base.repository.CustomDynamicWhere.queryForList Line:50 - 自定义查询SQL输出为：【select * from US_TODO_MODEL t  where t.send_flag = 0 and t.work_flag = 0 and t.enabled =1】
2025-05-28 09:10:00.021 [pool-14-thread-2] WARN  com.simbest.boot.base.repository.CustomDynamicWhere.queryForList Line:50 - 自定义查询SQL输出为：【select * from US_TODO_MODEL t  where t.send_flag = 0 and t.work_flag = 1 and t.enabled =1】
2025-05-28 09:10:00.256 [pool-14-thread-5] WARN  com.simbest.boot.base.repository.CustomDynamicWhere.queryForList Line:50 - 自定义查询SQL输出为：【select * from US_TODO_MODEL t  where t.send_flag = 0 and t.work_flag = 0 and t.enabled =1】
2025-05-28 09:10:20.052 [http-nio-8092-exec-9] WARN  com.simbest.exclude.web.GlobalErrorController.logErrorInformation Line:160 - 请注意所访问URL【/hnjjwz/hnjjwz_form.vue】发生404错误，资源地址不存在！
2025-05-28 09:14:07.134 [Thread-37] WARN  com.simbest.boot.component.GracefulShutdown.onApplicationEvent Line:161 - executor非ThreadPoolExecutor，具体类型为【org.apache.tomcat.util.threads.ThreadPoolExecutor】
2025-05-28 09:14:08.312 [Thread-5] WARN  com.alibaba.nacos.common.http.HttpClientBeanHolder.shutdown Line:108 - [HttpClientBeanHolder] Start destroying common HttpClient
2025-05-28 09:14:08.316 [Thread-5] WARN  com.alibaba.nacos.common.http.HttpClientBeanHolder.shutdown Line:114 - [HttpClientBeanHolder] Destruction of the end
2025-05-28 09:14:08.320 [Thread-11] WARN  com.alibaba.nacos.common.notify.NotifyCenter.shutdown Line:136 - [NotifyCenter] Start destroying Publisher
2025-05-28 09:14:08.324 [Thread-11] WARN  com.alibaba.nacos.common.notify.NotifyCenter.shutdown Line:153 - [NotifyCenter] Destruction of the end
2025-05-28 09:14:22.535 [restartedMain] WARN  com.alibaba.cloud.nacos.client.NacosPropertySourceBuilder.loadNacosData Line:87 - Ignore the empty nacos configuration and get it based on dataId[hnjjwz-custom-obuat.properties] & group[DEFAULT_GROUP]
2025-05-28 09:14:22.543 [restartedMain] WARN  com.alibaba.cloud.nacos.client.NacosPropertySourceBuilder.loadNacosData Line:87 - Ignore the empty nacos configuration and get it based on dataId[hnjjwz] & group[DEFAULT_GROUP]
2025-05-28 09:14:22.553 [restartedMain] WARN  com.alibaba.cloud.nacos.client.NacosPropertySourceBuilder.loadNacosData Line:87 - Ignore the empty nacos configuration and get it based on dataId[hnjjwz.properties] & group[DEFAULT_GROUP]
2025-05-28 09:14:25.114 [restartedMain] WARN  org.springframework.boot.actuate.endpoint.EndpointId.logWarning Line:155 - Endpoint ID 'service-registry' contains invalid characters, please migrate to a valid format.
2025-05-28 09:14:26.111 [restartedMain] WARN  com.alibaba.druid.pool.DruidDataSource.initCheck Line:1314 - removeAbandoned is true, not use in production.
2025-05-28 09:14:27.091 [Thread-5] WARN  com.alibaba.nacos.common.http.HttpClientBeanHolder.shutdown Line:108 - [HttpClientBeanHolder] Start destroying common HttpClient
2025-05-28 09:14:27.091 [Thread-11] WARN  com.alibaba.nacos.common.notify.NotifyCenter.shutdown Line:136 - [NotifyCenter] Start destroying Publisher
2025-05-28 09:14:27.091 [Thread-11] WARN  com.alibaba.nacos.common.notify.NotifyCenter.shutdown Line:153 - [NotifyCenter] Destruction of the end
2025-05-28 09:14:27.092 [Thread-5] WARN  com.alibaba.nacos.common.http.HttpClientBeanHolder.shutdown Line:114 - [HttpClientBeanHolder] Destruction of the end
2025-05-28 09:15:31.051 [restartedMain] WARN  com.alibaba.cloud.nacos.client.NacosPropertySourceBuilder.loadNacosData Line:87 - Ignore the empty nacos configuration and get it based on dataId[hnjjwz-custom-obuat.properties] & group[DEFAULT_GROUP]
2025-05-28 09:15:31.066 [restartedMain] WARN  com.alibaba.cloud.nacos.client.NacosPropertySourceBuilder.loadNacosData Line:87 - Ignore the empty nacos configuration and get it based on dataId[hnjjwz] & group[DEFAULT_GROUP]
2025-05-28 09:15:31.079 [restartedMain] WARN  com.alibaba.cloud.nacos.client.NacosPropertySourceBuilder.loadNacosData Line:87 - Ignore the empty nacos configuration and get it based on dataId[hnjjwz.properties] & group[DEFAULT_GROUP]
2025-05-28 09:15:35.410 [restartedMain] WARN  org.springframework.boot.actuate.endpoint.EndpointId.logWarning Line:155 - Endpoint ID 'service-registry' contains invalid characters, please migrate to a valid format.
2025-05-28 09:15:38.314 [restartedMain] WARN  com.alibaba.druid.pool.DruidDataSource.initCheck Line:1314 - removeAbandoned is true, not use in production.
2025-05-28 09:16:45.221 [restartedMain] WARN  com.simbest.boot.SimbestApplication.onApplicationEvent Line:60 - 加载环境信息为: 【obuat】
2025-05-28 09:16:45.222 [restartedMain] WARN  com.simbest.boot.SimbestApplication.onApplicationEvent Line:61 - Application started successfully, lets go and have fun......
2025-05-28 09:16:45.223 [restartedMain] WARN  com.simbest.boot.SimbestApplication.onApplicationEvent Line:71 - 
---------------------------------------------------------
	应用已成功启动，运行地址如下：:
	Local:		http://localhost:8092/hnjjwz
	External:	http://***********:8092/hnjjwz
Aplication started successfully, lets go and have fun......
---------------------------------------------------------

2025-05-28 09:19:26.594 [http-nio-8092-exec-11] WARN  com.simbest.exclude.web.GlobalErrorController.logErrorInformation Line:160 - 请注意所访问URL【/hnjjwz/action/process/findMyTodo】发生404错误，资源地址不存在！
2025-05-28 09:20:00.043 [pool-14-thread-3] WARN  com.simbest.boot.base.repository.CustomDynamicWhere.queryForList Line:50 - 自定义查询SQL输出为：【select * from US_TODO_MODEL t  where t.send_flag = 0 and t.work_flag = 0 and t.enabled =1】
2025-05-28 09:20:00.043 [pool-14-thread-4] WARN  com.simbest.boot.base.repository.CustomDynamicWhere.queryForList Line:50 - 自定义查询SQL输出为：【select * from US_TODO_MODEL t  where t.send_flag = 0 and t.work_flag = 1 and t.enabled =1】
2025-05-28 09:25:00.019 [pool-14-thread-10] WARN  com.simbest.boot.base.repository.CustomDynamicWhere.queryForList Line:50 - 自定义查询SQL输出为：【select * from US_TODO_MODEL t  where t.send_flag = 0 and t.work_flag = 1 and t.enabled =1】
2025-05-28 09:25:00.230 [pool-14-thread-1] WARN  com.simbest.boot.base.repository.CustomDynamicWhere.queryForList Line:50 - 自定义查询SQL输出为：【select * from US_TODO_MODEL t  where t.send_flag = 0 and t.work_flag = 0 and t.enabled =1】
2025-05-28 09:30:00.017 [pool-14-thread-8] WARN  com.simbest.boot.base.repository.CustomDynamicWhere.queryForList Line:50 - 自定义查询SQL输出为：【select * from US_TODO_MODEL t  where t.send_flag = 0 and t.work_flag = 1 and t.enabled =1】
2025-05-28 09:30:00.020 [pool-14-thread-7] WARN  com.simbest.boot.base.repository.CustomDynamicWhere.queryForList Line:50 - 自定义查询SQL输出为：【select * from US_TODO_MODEL t  where t.send_flag = 0 and t.work_flag = 0 and t.enabled =1】
2025-05-28 09:34:17.643 [http-nio-8092-exec-1] WARN  com.simbest.exclude.web.GlobalErrorController.logErrorInformation Line:160 - 请注意所访问URL【/hnjjwz/action/process/findMyTodo】发生404错误，资源地址不存在！
2025-05-28 09:35:00.019 [pool-14-thread-2] WARN  com.simbest.boot.base.repository.CustomDynamicWhere.queryForList Line:50 - 自定义查询SQL输出为：【select * from US_TODO_MODEL t  where t.send_flag = 0 and t.work_flag = 1 and t.enabled =1】
2025-05-28 09:35:00.235 [pool-14-thread-10] WARN  com.simbest.boot.base.repository.CustomDynamicWhere.queryForList Line:50 - 自定义查询SQL输出为：【select * from US_TODO_MODEL t  where t.send_flag = 0 and t.work_flag = 0 and t.enabled =1】
2025-05-28 09:40:00.006 [pool-14-thread-3] WARN  com.simbest.boot.base.repository.CustomDynamicWhere.queryForList Line:50 - 自定义查询SQL输出为：【select * from US_TODO_MODEL t  where t.send_flag = 0 and t.work_flag = 1 and t.enabled =1】
2025-05-28 09:40:00.006 [pool-14-thread-6] WARN  com.simbest.boot.base.repository.CustomDynamicWhere.queryForList Line:50 - 自定义查询SQL输出为：【select * from US_TODO_MODEL t  where t.send_flag = 0 and t.work_flag = 0 and t.enabled =1】
2025-05-28 09:45:00.010 [pool-14-thread-7] WARN  com.simbest.boot.base.repository.CustomDynamicWhere.queryForList Line:50 - 自定义查询SQL输出为：【select * from US_TODO_MODEL t  where t.send_flag = 0 and t.work_flag = 1 and t.enabled =1】
2025-05-28 09:45:00.013 [pool-14-thread-10] WARN  com.simbest.boot.base.repository.CustomDynamicWhere.queryForList Line:50 - 自定义查询SQL输出为：【select * from US_TODO_MODEL t  where t.send_flag = 0 and t.work_flag = 0 and t.enabled =1】
2025-05-28 09:50:00.016 [pool-14-thread-3] WARN  com.simbest.boot.base.repository.CustomDynamicWhere.queryForList Line:50 - 自定义查询SQL输出为：【select * from US_TODO_MODEL t  where t.send_flag = 0 and t.work_flag = 0 and t.enabled =1】
2025-05-28 09:50:00.017 [pool-14-thread-8] WARN  com.simbest.boot.base.repository.CustomDynamicWhere.queryForList Line:50 - 自定义查询SQL输出为：【select * from US_TODO_MODEL t  where t.send_flag = 0 and t.work_flag = 1 and t.enabled =1】
2025-05-28 09:55:00.013 [pool-14-thread-2] WARN  com.simbest.boot.base.repository.CustomDynamicWhere.queryForList Line:50 - 自定义查询SQL输出为：【select * from US_TODO_MODEL t  where t.send_flag = 0 and t.work_flag = 1 and t.enabled =1】
2025-05-28 09:55:00.013 [pool-14-thread-3] WARN  com.simbest.boot.base.repository.CustomDynamicWhere.queryForList Line:50 - 自定义查询SQL输出为：【select * from US_TODO_MODEL t  where t.send_flag = 0 and t.work_flag = 0 and t.enabled =1】
2025-05-28 10:00:00.013 [pool-14-thread-10] WARN  com.simbest.boot.base.repository.CustomDynamicWhere.queryForList Line:50 - 自定义查询SQL输出为：【select * from US_TODO_MODEL t  where t.send_flag = 0 and t.work_flag = 1 and t.enabled =1】
2025-05-28 10:00:00.017 [pool-14-thread-6] WARN  com.simbest.boot.cmcc.a4.task.A4TaskCreateXmlFile.execute Line:76 - >>>>>>>>>>>>>>>>>>>>>>>>>>>>>>>>>>>>>>>>>>>>>>>>>>>>>A4TaskCreateXmlFile:执行定时任务但不是Prd环境【2025-05-28T10:00:00.017】
2025-05-28 10:00:00.017 [pool-14-thread-8] WARN  com.simbest.boot.base.repository.CustomDynamicWhere.queryForList Line:50 - 自定义查询SQL输出为：【select * from US_TODO_MODEL t  where t.send_flag = 0 and t.work_flag = 0 and t.enabled =1】
2025-05-28 10:05:00.007 [pool-14-thread-3] WARN  com.simbest.boot.base.repository.CustomDynamicWhere.queryForList Line:50 - 自定义查询SQL输出为：【select * from US_TODO_MODEL t  where t.send_flag = 0 and t.work_flag = 1 and t.enabled =1】
2025-05-28 10:05:00.009 [pool-14-thread-4] WARN  com.simbest.boot.base.repository.CustomDynamicWhere.queryForList Line:50 - 自定义查询SQL输出为：【select * from US_TODO_MODEL t  where t.send_flag = 0 and t.work_flag = 0 and t.enabled =1】
2025-05-28 10:10:00.016 [pool-14-thread-9] WARN  com.simbest.boot.base.repository.CustomDynamicWhere.queryForList Line:50 - 自定义查询SQL输出为：【select * from US_TODO_MODEL t  where t.send_flag = 0 and t.work_flag = 1 and t.enabled =1】
2025-05-28 10:10:00.017 [pool-14-thread-6] WARN  com.simbest.boot.base.repository.CustomDynamicWhere.queryForList Line:50 - 自定义查询SQL输出为：【select * from US_TODO_MODEL t  where t.send_flag = 0 and t.work_flag = 0 and t.enabled =1】
2025-05-28 10:15:00.019 [pool-14-thread-6] WARN  com.simbest.boot.base.repository.CustomDynamicWhere.queryForList Line:50 - 自定义查询SQL输出为：【select * from US_TODO_MODEL t  where t.send_flag = 0 and t.work_flag = 1 and t.enabled =1】
2025-05-28 10:15:00.019 [pool-14-thread-2] WARN  com.simbest.boot.base.repository.CustomDynamicWhere.queryForList Line:50 - 自定义查询SQL输出为：【select * from US_TODO_MODEL t  where t.send_flag = 0 and t.work_flag = 0 and t.enabled =1】
2025-05-28 10:20:00.019 [pool-14-thread-3] WARN  com.simbest.boot.base.repository.CustomDynamicWhere.queryForList Line:50 - 自定义查询SQL输出为：【select * from US_TODO_MODEL t  where t.send_flag = 0 and t.work_flag = 1 and t.enabled =1】
2025-05-28 10:20:00.022 [pool-14-thread-10] WARN  com.simbest.boot.base.repository.CustomDynamicWhere.queryForList Line:50 - 自定义查询SQL输出为：【select * from US_TODO_MODEL t  where t.send_flag = 0 and t.work_flag = 0 and t.enabled =1】
2025-05-28 10:25:00.018 [pool-14-thread-1] WARN  com.simbest.boot.base.repository.CustomDynamicWhere.queryForList Line:50 - 自定义查询SQL输出为：【select * from US_TODO_MODEL t  where t.send_flag = 0 and t.work_flag = 0 and t.enabled =1】
2025-05-28 10:25:00.018 [pool-14-thread-7] WARN  com.simbest.boot.base.repository.CustomDynamicWhere.queryForList Line:50 - 自定义查询SQL输出为：【select * from US_TODO_MODEL t  where t.send_flag = 0 and t.work_flag = 1 and t.enabled =1】
2025-05-28 10:30:00.233 [pool-14-thread-2] WARN  com.simbest.boot.base.repository.CustomDynamicWhere.queryForList Line:50 - 自定义查询SQL输出为：【select * from US_TODO_MODEL t  where t.send_flag = 0 and t.work_flag = 1 and t.enabled =1】
2025-05-28 10:30:00.263 [pool-14-thread-5] WARN  com.simbest.boot.base.repository.CustomDynamicWhere.queryForList Line:50 - 自定义查询SQL输出为：【select * from US_TODO_MODEL t  where t.send_flag = 0 and t.work_flag = 0 and t.enabled =1】
2025-05-28 10:35:00.017 [pool-14-thread-9] WARN  com.simbest.boot.base.repository.CustomDynamicWhere.queryForList Line:50 - 自定义查询SQL输出为：【select * from US_TODO_MODEL t  where t.send_flag = 0 and t.work_flag = 1 and t.enabled =1】
2025-05-28 10:35:00.027 [pool-14-thread-6] WARN  com.simbest.boot.base.repository.CustomDynamicWhere.queryForList Line:50 - 自定义查询SQL输出为：【select * from US_TODO_MODEL t  where t.send_flag = 0 and t.work_flag = 0 and t.enabled =1】
2025-05-28 10:40:00.013 [pool-14-thread-7] WARN  com.simbest.boot.base.repository.CustomDynamicWhere.queryForList Line:50 - 自定义查询SQL输出为：【select * from US_TODO_MODEL t  where t.send_flag = 0 and t.work_flag = 0 and t.enabled =1】
2025-05-28 10:40:00.014 [pool-14-thread-3] WARN  com.simbest.boot.base.repository.CustomDynamicWhere.queryForList Line:50 - 自定义查询SQL输出为：【select * from US_TODO_MODEL t  where t.send_flag = 0 and t.work_flag = 1 and t.enabled =1】
2025-05-28 10:45:00.024 [pool-14-thread-1] WARN  com.simbest.boot.base.repository.CustomDynamicWhere.queryForList Line:50 - 自定义查询SQL输出为：【select * from US_TODO_MODEL t  where t.send_flag = 0 and t.work_flag = 0 and t.enabled =1】
2025-05-28 10:45:00.241 [pool-14-thread-2] WARN  com.simbest.boot.base.repository.CustomDynamicWhere.queryForList Line:50 - 自定义查询SQL输出为：【select * from US_TODO_MODEL t  where t.send_flag = 0 and t.work_flag = 1 and t.enabled =1】
2025-05-28 10:50:00.014 [pool-14-thread-8] WARN  com.simbest.boot.base.repository.CustomDynamicWhere.queryForList Line:50 - 自定义查询SQL输出为：【select * from US_TODO_MODEL t  where t.send_flag = 0 and t.work_flag = 0 and t.enabled =1】
2025-05-28 10:50:00.016 [pool-14-thread-5] WARN  com.simbest.boot.base.repository.CustomDynamicWhere.queryForList Line:50 - 自定义查询SQL输出为：【select * from US_TODO_MODEL t  where t.send_flag = 0 and t.work_flag = 1 and t.enabled =1】
2025-05-28 10:55:00.016 [pool-14-thread-8] WARN  com.simbest.boot.base.repository.CustomDynamicWhere.queryForList Line:50 - 自定义查询SQL输出为：【select * from US_TODO_MODEL t  where t.send_flag = 0 and t.work_flag = 1 and t.enabled =1】
2025-05-28 10:55:00.027 [pool-14-thread-2] WARN  com.simbest.boot.base.repository.CustomDynamicWhere.queryForList Line:50 - 自定义查询SQL输出为：【select * from US_TODO_MODEL t  where t.send_flag = 0 and t.work_flag = 0 and t.enabled =1】
2025-05-28 11:00:00.018 [pool-14-thread-10] WARN  com.simbest.boot.base.repository.CustomDynamicWhere.queryForList Line:50 - 自定义查询SQL输出为：【select * from US_TODO_MODEL t  where t.send_flag = 0 and t.work_flag = 0 and t.enabled =1】
2025-05-28 11:00:00.019 [pool-14-thread-1] WARN  com.simbest.boot.base.repository.CustomDynamicWhere.queryForList Line:50 - 自定义查询SQL输出为：【select * from US_TODO_MODEL t  where t.send_flag = 0 and t.work_flag = 1 and t.enabled =1】
2025-05-28 11:00:00.019 [pool-14-thread-9] WARN  com.simbest.boot.cmcc.a4.task.A4TaskCreateXmlFile.execute Line:76 - >>>>>>>>>>>>>>>>>>>>>>>>>>>>>>>>>>>>>>>>>>>>>>>>>>>>>A4TaskCreateXmlFile:执行定时任务但不是Prd环境【2025-05-28T11:00:00.019】
2025-05-28 11:05:00.010 [pool-14-thread-9] WARN  com.simbest.boot.base.repository.CustomDynamicWhere.queryForList Line:50 - 自定义查询SQL输出为：【select * from US_TODO_MODEL t  where t.send_flag = 0 and t.work_flag = 0 and t.enabled =1】
2025-05-28 11:05:00.010 [pool-14-thread-4] WARN  com.simbest.boot.base.repository.CustomDynamicWhere.queryForList Line:50 - 自定义查询SQL输出为：【select * from US_TODO_MODEL t  where t.send_flag = 0 and t.work_flag = 1 and t.enabled =1】
2025-05-28 11:10:00.008 [pool-14-thread-7] WARN  com.simbest.boot.base.repository.CustomDynamicWhere.queryForList Line:50 - 自定义查询SQL输出为：【select * from US_TODO_MODEL t  where t.send_flag = 0 and t.work_flag = 1 and t.enabled =1】
2025-05-28 11:10:00.009 [pool-14-thread-2] WARN  com.simbest.boot.base.repository.CustomDynamicWhere.queryForList Line:50 - 自定义查询SQL输出为：【select * from US_TODO_MODEL t  where t.send_flag = 0 and t.work_flag = 0 and t.enabled =1】
2025-05-28 11:15:00.009 [pool-14-thread-10] WARN  com.simbest.boot.base.repository.CustomDynamicWhere.queryForList Line:50 - 自定义查询SQL输出为：【select * from US_TODO_MODEL t  where t.send_flag = 0 and t.work_flag = 1 and t.enabled =1】
2025-05-28 11:15:00.009 [pool-14-thread-8] WARN  com.simbest.boot.base.repository.CustomDynamicWhere.queryForList Line:50 - 自定义查询SQL输出为：【select * from US_TODO_MODEL t  where t.send_flag = 0 and t.work_flag = 0 and t.enabled =1】
2025-05-28 11:20:00.011 [pool-14-thread-1] WARN  com.simbest.boot.base.repository.CustomDynamicWhere.queryForList Line:50 - 自定义查询SQL输出为：【select * from US_TODO_MODEL t  where t.send_flag = 0 and t.work_flag = 1 and t.enabled =1】
2025-05-28 11:20:00.011 [pool-14-thread-7] WARN  com.simbest.boot.base.repository.CustomDynamicWhere.queryForList Line:50 - 自定义查询SQL输出为：【select * from US_TODO_MODEL t  where t.send_flag = 0 and t.work_flag = 0 and t.enabled =1】
2025-05-28 11:25:00.019 [pool-14-thread-6] WARN  com.simbest.boot.base.repository.CustomDynamicWhere.queryForList Line:50 - 自定义查询SQL输出为：【select * from US_TODO_MODEL t  where t.send_flag = 0 and t.work_flag = 1 and t.enabled =1】
2025-05-28 11:25:00.019 [pool-14-thread-9] WARN  com.simbest.boot.base.repository.CustomDynamicWhere.queryForList Line:50 - 自定义查询SQL输出为：【select * from US_TODO_MODEL t  where t.send_flag = 0 and t.work_flag = 0 and t.enabled =1】
2025-05-28 11:30:00.252 [pool-14-thread-3] WARN  com.simbest.boot.base.repository.CustomDynamicWhere.queryForList Line:50 - 自定义查询SQL输出为：【select * from US_TODO_MODEL t  where t.send_flag = 0 and t.work_flag = 1 and t.enabled =1】
2025-05-28 11:30:00.270 [pool-14-thread-7] WARN  com.simbest.boot.base.repository.CustomDynamicWhere.queryForList Line:50 - 自定义查询SQL输出为：【select * from US_TODO_MODEL t  where t.send_flag = 0 and t.work_flag = 0 and t.enabled =1】
2025-05-28 11:35:00.010 [pool-14-thread-5] WARN  com.simbest.boot.base.repository.CustomDynamicWhere.queryForList Line:50 - 自定义查询SQL输出为：【select * from US_TODO_MODEL t  where t.send_flag = 0 and t.work_flag = 0 and t.enabled =1】
2025-05-28 11:35:00.011 [pool-14-thread-3] WARN  com.simbest.boot.base.repository.CustomDynamicWhere.queryForList Line:50 - 自定义查询SQL输出为：【select * from US_TODO_MODEL t  where t.send_flag = 0 and t.work_flag = 1 and t.enabled =1】
2025-05-28 11:40:00.025 [pool-14-thread-5] WARN  com.simbest.boot.base.repository.CustomDynamicWhere.queryForList Line:50 - 自定义查询SQL输出为：【select * from US_TODO_MODEL t  where t.send_flag = 0 and t.work_flag = 1 and t.enabled =1】
2025-05-28 11:40:00.028 [pool-14-thread-1] WARN  com.simbest.boot.base.repository.CustomDynamicWhere.queryForList Line:50 - 自定义查询SQL输出为：【select * from US_TODO_MODEL t  where t.send_flag = 0 and t.work_flag = 0 and t.enabled =1】
2025-05-28 11:45:00.009 [pool-14-thread-5] WARN  com.simbest.boot.base.repository.CustomDynamicWhere.queryForList Line:50 - 自定义查询SQL输出为：【select * from US_TODO_MODEL t  where t.send_flag = 0 and t.work_flag = 0 and t.enabled =1】
2025-05-28 11:45:00.009 [pool-14-thread-1] WARN  com.simbest.boot.base.repository.CustomDynamicWhere.queryForList Line:50 - 自定义查询SQL输出为：【select * from US_TODO_MODEL t  where t.send_flag = 0 and t.work_flag = 1 and t.enabled =1】
2025-05-28 11:50:00.015 [pool-14-thread-1] WARN  com.simbest.boot.base.repository.CustomDynamicWhere.queryForList Line:50 - 自定义查询SQL输出为：【select * from US_TODO_MODEL t  where t.send_flag = 0 and t.work_flag = 0 and t.enabled =1】
2025-05-28 11:50:00.018 [pool-14-thread-2] WARN  com.simbest.boot.base.repository.CustomDynamicWhere.queryForList Line:50 - 自定义查询SQL输出为：【select * from US_TODO_MODEL t  where t.send_flag = 0 and t.work_flag = 1 and t.enabled =1】
2025-05-28 11:55:00.008 [pool-14-thread-10] WARN  com.simbest.boot.base.repository.CustomDynamicWhere.queryForList Line:50 - 自定义查询SQL输出为：【select * from US_TODO_MODEL t  where t.send_flag = 0 and t.work_flag = 0 and t.enabled =1】
2025-05-28 11:55:00.011 [pool-14-thread-2] WARN  com.simbest.boot.base.repository.CustomDynamicWhere.queryForList Line:50 - 自定义查询SQL输出为：【select * from US_TODO_MODEL t  where t.send_flag = 0 and t.work_flag = 1 and t.enabled =1】
2025-05-28 12:00:00.020 [pool-14-thread-7] WARN  com.simbest.boot.base.repository.CustomDynamicWhere.queryForList Line:50 - 自定义查询SQL输出为：【select * from US_TODO_MODEL t  where t.send_flag = 0 and t.work_flag = 0 and t.enabled =1】
2025-05-28 12:00:00.025 [pool-14-thread-5] WARN  com.simbest.boot.cmcc.a4.task.A4TaskCreateXmlFile.execute Line:76 - >>>>>>>>>>>>>>>>>>>>>>>>>>>>>>>>>>>>>>>>>>>>>>>>>>>>>A4TaskCreateXmlFile:执行定时任务但不是Prd环境【2025-05-28T12:00:00.025】
2025-05-28 12:05:00.015 [pool-14-thread-2] WARN  com.simbest.boot.base.repository.CustomDynamicWhere.queryForList Line:50 - 自定义查询SQL输出为：【select * from US_TODO_MODEL t  where t.send_flag = 0 and t.work_flag = 0 and t.enabled =1】
2025-05-28 12:05:00.015 [pool-14-thread-10] WARN  com.simbest.boot.base.repository.CustomDynamicWhere.queryForList Line:50 - 自定义查询SQL输出为：【select * from US_TODO_MODEL t  where t.send_flag = 0 and t.work_flag = 1 and t.enabled =1】
2025-05-28 12:10:00.013 [pool-14-thread-4] WARN  com.simbest.boot.base.repository.CustomDynamicWhere.queryForList Line:50 - 自定义查询SQL输出为：【select * from US_TODO_MODEL t  where t.send_flag = 0 and t.work_flag = 1 and t.enabled =1】
2025-05-28 12:10:00.013 [pool-14-thread-8] WARN  com.simbest.boot.base.repository.CustomDynamicWhere.queryForList Line:50 - 自定义查询SQL输出为：【select * from US_TODO_MODEL t  where t.send_flag = 0 and t.work_flag = 0 and t.enabled =1】
2025-05-28 12:15:00.018 [pool-14-thread-2] WARN  com.simbest.boot.base.repository.CustomDynamicWhere.queryForList Line:50 - 自定义查询SQL输出为：【select * from US_TODO_MODEL t  where t.send_flag = 0 and t.work_flag = 1 and t.enabled =1】
2025-05-28 12:15:00.023 [pool-14-thread-4] WARN  com.simbest.boot.base.repository.CustomDynamicWhere.queryForList Line:50 - 自定义查询SQL输出为：【select * from US_TODO_MODEL t  where t.send_flag = 0 and t.work_flag = 0 and t.enabled =1】
2025-05-28 12:20:00.010 [pool-14-thread-6] WARN  com.simbest.boot.base.repository.CustomDynamicWhere.queryForList Line:50 - 自定义查询SQL输出为：【select * from US_TODO_MODEL t  where t.send_flag = 0 and t.work_flag = 0 and t.enabled =1】
2025-05-28 12:20:00.010 [pool-14-thread-9] WARN  com.simbest.boot.base.repository.CustomDynamicWhere.queryForList Line:50 - 自定义查询SQL输出为：【select * from US_TODO_MODEL t  where t.send_flag = 0 and t.work_flag = 1 and t.enabled =1】
2025-05-28 12:25:00.023 [pool-14-thread-3] WARN  com.simbest.boot.base.repository.CustomDynamicWhere.queryForList Line:50 - 自定义查询SQL输出为：【select * from US_TODO_MODEL t  where t.send_flag = 0 and t.work_flag = 0 and t.enabled =1】
2025-05-28 12:25:00.022 [pool-14-thread-5] WARN  com.simbest.boot.base.repository.CustomDynamicWhere.queryForList Line:50 - 自定义查询SQL输出为：【select * from US_TODO_MODEL t  where t.send_flag = 0 and t.work_flag = 1 and t.enabled =1】
2025-05-28 12:50:35.143 [AMQP Connection 10.92.82.172:5672] WARN  com.rabbitmq.client.impl.ForgivingExceptionHandler.log Line:115 - An unexpected connection driver error occured (Exception message: Connection reset)
2025-05-28 12:50:36.946 [AMQP Connection 10.92.82.172:5672] WARN  com.rabbitmq.client.impl.ForgivingExceptionHandler.log Line:115 - An unexpected connection driver error occured (Exception message: Connection reset)
2025-05-28 12:50:37.023 [AMQP Connection 10.92.82.172:5672] WARN  com.rabbitmq.client.impl.ForgivingExceptionHandler.log Line:115 - An unexpected connection driver error occured (Exception message: Connection reset)
2025-05-28 12:50:37.053 [AMQP Connection 10.92.82.172:5672] WARN  com.rabbitmq.client.impl.ForgivingExceptionHandler.log Line:115 - An unexpected connection driver error occured (Exception message: Connection reset)
2025-05-28 13:00:00.010 [pool-14-thread-5] WARN  com.simbest.boot.base.repository.CustomDynamicWhere.queryForList Line:50 - 自定义查询SQL输出为：【select * from US_TODO_MODEL t  where t.send_flag = 0 and t.work_flag = 0 and t.enabled =1】
2025-05-28 13:05:00.014 [pool-14-thread-7] WARN  com.simbest.boot.base.repository.CustomDynamicWhere.queryForList Line:50 - 自定义查询SQL输出为：【select * from US_TODO_MODEL t  where t.send_flag = 0 and t.work_flag = 0 and t.enabled =1】
2025-05-28 13:05:09.304 [pool-14-thread-10] WARN  com.simbest.boot.base.repository.CustomDynamicWhere.queryForList Line:50 - 自定义查询SQL输出为：【select * from US_TODO_MODEL t  where t.send_flag = 0 and t.work_flag = 1 and t.enabled =1】
2025-05-28 13:10:00.010 [pool-14-thread-9] WARN  com.simbest.boot.base.repository.CustomDynamicWhere.queryForList Line:50 - 自定义查询SQL输出为：【select * from US_TODO_MODEL t  where t.send_flag = 0 and t.work_flag = 0 and t.enabled =1】
2025-05-28 13:10:00.013 [pool-14-thread-5] WARN  com.simbest.boot.base.repository.CustomDynamicWhere.queryForList Line:50 - 自定义查询SQL输出为：【select * from US_TODO_MODEL t  where t.send_flag = 0 and t.work_flag = 1 and t.enabled =1】
2025-05-28 13:31:35.650 [AMQP Connection 10.92.82.172:5672] WARN  com.rabbitmq.client.impl.ForgivingExceptionHandler.log Line:115 - An unexpected connection driver error occured (Exception message: Connection reset)
2025-05-28 13:31:37.615 [AMQP Connection 10.92.82.172:5672] WARN  com.rabbitmq.client.impl.ForgivingExceptionHandler.log Line:115 - An unexpected connection driver error occured (Exception message: Connection reset)
2025-05-28 13:31:37.709 [AMQP Connection 10.92.82.172:5672] WARN  com.rabbitmq.client.impl.ForgivingExceptionHandler.log Line:115 - An unexpected connection driver error occured (Exception message: Connection reset)
2025-05-28 13:31:38.020 [AMQP Connection 10.92.82.172:5672] WARN  com.rabbitmq.client.impl.ForgivingExceptionHandler.log Line:115 - An unexpected connection driver error occured (Exception message: Connection reset)
2025-05-28 13:40:00.009 [pool-14-thread-9] WARN  com.simbest.boot.base.repository.CustomDynamicWhere.queryForList Line:50 - 自定义查询SQL输出为：【select * from US_TODO_MODEL t  where t.send_flag = 0 and t.work_flag = 0 and t.enabled =1】
2025-05-28 13:45:00.017 [pool-14-thread-1] WARN  com.simbest.boot.base.repository.CustomDynamicWhere.queryForList Line:50 - 自定义查询SQL输出为：【select * from US_TODO_MODEL t  where t.send_flag = 0 and t.work_flag = 0 and t.enabled =1】
2025-05-28 13:45:06.178 [pool-14-thread-2] WARN  com.simbest.boot.base.repository.CustomDynamicWhere.queryForList Line:50 - 自定义查询SQL输出为：【select * from US_TODO_MODEL t  where t.send_flag = 0 and t.work_flag = 1 and t.enabled =1】
2025-05-28 13:50:00.004 [pool-14-thread-5] WARN  com.simbest.boot.base.repository.CustomDynamicWhere.queryForList Line:50 - 自定义查询SQL输出为：【select * from US_TODO_MODEL t  where t.send_flag = 0 and t.work_flag = 0 and t.enabled =1】
2025-05-28 13:50:00.004 [pool-14-thread-1] WARN  com.simbest.boot.base.repository.CustomDynamicWhere.queryForList Line:50 - 自定义查询SQL输出为：【select * from US_TODO_MODEL t  where t.send_flag = 0 and t.work_flag = 1 and t.enabled =1】
2025-05-28 13:55:00.015 [pool-14-thread-9] WARN  com.simbest.boot.base.repository.CustomDynamicWhere.queryForList Line:50 - 自定义查询SQL输出为：【select * from US_TODO_MODEL t  where t.send_flag = 0 and t.work_flag = 0 and t.enabled =1】
2025-05-28 13:55:00.017 [pool-14-thread-3] WARN  com.simbest.boot.base.repository.CustomDynamicWhere.queryForList Line:50 - 自定义查询SQL输出为：【select * from US_TODO_MODEL t  where t.send_flag = 0 and t.work_flag = 1 and t.enabled =1】
2025-05-28 14:00:55.847 [AMQP Connection 10.92.82.172:5672] WARN  com.rabbitmq.client.impl.ForgivingExceptionHandler.log Line:115 - An unexpected connection driver error occured (Exception message: Connection reset)
2025-05-28 14:00:57.800 [AMQP Connection 10.92.82.172:5672] WARN  com.rabbitmq.client.impl.ForgivingExceptionHandler.log Line:115 - An unexpected connection driver error occured (Exception message: Connection reset)
2025-05-28 14:00:57.893 [AMQP Connection 10.92.82.172:5672] WARN  com.rabbitmq.client.impl.ForgivingExceptionHandler.log Line:115 - An unexpected connection driver error occured (Exception message: Connection reset)
2025-05-28 14:00:58.246 [AMQP Connection 10.92.82.172:5672] WARN  com.rabbitmq.client.impl.ForgivingExceptionHandler.log Line:115 - An unexpected connection driver error occured (Exception message: Connection reset)
2025-05-28 14:01:00.203 [pool-14-thread-3] WARN  com.simbest.boot.cmcc.a4.task.A4TaskCreateXmlFile.execute Line:76 - >>>>>>>>>>>>>>>>>>>>>>>>>>>>>>>>>>>>>>>>>>>>>>>>>>>>>A4TaskCreateXmlFile:执行定时任务但不是Prd环境【2025-05-28T14:01:00.203】
2025-05-28 14:01:00.407 [pool-14-thread-10] WARN  com.simbest.boot.base.repository.CustomDynamicWhere.queryForList Line:50 - 自定义查询SQL输出为：【select * from US_TODO_MODEL t  where t.send_flag = 0 and t.work_flag = 1 and t.enabled =1】
2025-05-28 14:05:00.016 [pool-14-thread-10] WARN  com.simbest.boot.base.repository.CustomDynamicWhere.queryForList Line:50 - 自定义查询SQL输出为：【select * from US_TODO_MODEL t  where t.send_flag = 0 and t.work_flag = 1 and t.enabled =1】
2025-05-28 14:10:00.006 [pool-14-thread-1] WARN  com.simbest.boot.base.repository.CustomDynamicWhere.queryForList Line:50 - 自定义查询SQL输出为：【select * from US_TODO_MODEL t  where t.send_flag = 0 and t.work_flag = 1 and t.enabled =1】
2025-05-28 14:15:00.026 [pool-14-thread-1] WARN  com.simbest.boot.base.repository.CustomDynamicWhere.queryForList Line:50 - 自定义查询SQL输出为：【select * from US_TODO_MODEL t  where t.send_flag = 0 and t.work_flag = 1 and t.enabled =1】
2025-05-28 14:20:00.006 [pool-14-thread-9] WARN  com.simbest.boot.base.repository.CustomDynamicWhere.queryForList Line:50 - 自定义查询SQL输出为：【select * from US_TODO_MODEL t  where t.send_flag = 0 and t.work_flag = 1 and t.enabled =1】
2025-05-28 14:20:00.006 [pool-14-thread-3] WARN  com.simbest.boot.base.repository.CustomDynamicWhere.queryForList Line:50 - 自定义查询SQL输出为：【select * from US_TODO_MODEL t  where t.send_flag = 0 and t.work_flag = 0 and t.enabled =1】
2025-05-28 14:25:00.018 [pool-14-thread-2] WARN  com.simbest.boot.base.repository.CustomDynamicWhere.queryForList Line:50 - 自定义查询SQL输出为：【select * from US_TODO_MODEL t  where t.send_flag = 0 and t.work_flag = 0 and t.enabled =1】
2025-05-28 14:25:00.019 [pool-14-thread-1] WARN  com.simbest.boot.base.repository.CustomDynamicWhere.queryForList Line:50 - 自定义查询SQL输出为：【select * from US_TODO_MODEL t  where t.send_flag = 0 and t.work_flag = 1 and t.enabled =1】
2025-05-28 14:30:00.013 [pool-14-thread-9] WARN  com.simbest.boot.base.repository.CustomDynamicWhere.queryForList Line:50 - 自定义查询SQL输出为：【select * from US_TODO_MODEL t  where t.send_flag = 0 and t.work_flag = 1 and t.enabled =1】
2025-05-28 14:30:00.021 [pool-14-thread-6] WARN  com.simbest.boot.base.repository.CustomDynamicWhere.queryForList Line:50 - 自定义查询SQL输出为：【select * from US_TODO_MODEL t  where t.send_flag = 0 and t.work_flag = 0 and t.enabled =1】
2025-05-28 14:35:00.013 [pool-14-thread-3] WARN  com.simbest.boot.base.repository.CustomDynamicWhere.queryForList Line:50 - 自定义查询SQL输出为：【select * from US_TODO_MODEL t  where t.send_flag = 0 and t.work_flag = 0 and t.enabled =1】
2025-05-28 14:35:00.014 [pool-14-thread-1] WARN  com.simbest.boot.base.repository.CustomDynamicWhere.queryForList Line:50 - 自定义查询SQL输出为：【select * from US_TODO_MODEL t  where t.send_flag = 0 and t.work_flag = 1 and t.enabled =1】
2025-05-28 14:40:00.013 [pool-14-thread-9] WARN  com.simbest.boot.base.repository.CustomDynamicWhere.queryForList Line:50 - 自定义查询SQL输出为：【select * from US_TODO_MODEL t  where t.send_flag = 0 and t.work_flag = 0 and t.enabled =1】
2025-05-28 14:40:00.013 [pool-14-thread-3] WARN  com.simbest.boot.base.repository.CustomDynamicWhere.queryForList Line:50 - 自定义查询SQL输出为：【select * from US_TODO_MODEL t  where t.send_flag = 0 and t.work_flag = 1 and t.enabled =1】
2025-05-28 14:45:00.022 [pool-14-thread-4] WARN  com.simbest.boot.base.repository.CustomDynamicWhere.queryForList Line:50 - 自定义查询SQL输出为：【select * from US_TODO_MODEL t  where t.send_flag = 0 and t.work_flag = 0 and t.enabled =1】
2025-05-28 14:45:00.022 [pool-14-thread-7] WARN  com.simbest.boot.base.repository.CustomDynamicWhere.queryForList Line:50 - 自定义查询SQL输出为：【select * from US_TODO_MODEL t  where t.send_flag = 0 and t.work_flag = 1 and t.enabled =1】
2025-05-28 14:47:52.131 [http-nio-8092-exec-10] WARN  com.simbest.boot.security.auth.service.impl.AuthUserCacheServiceImpl.loadCacheUser Line:118 - 无法通过关键字【auth:user:zhanglu16】读取用户主键ID
2025-05-28 14:47:52.409 [http-nio-8092-exec-10] WARN  com.simbest.boot.security.auth.service.impl.AuthUserCacheServiceImpl.saveOrUpdateCacheUser Line:64 - 即将在缓存中将用户权限置为空----SET AuthPermissions EMPTY----【38348】
2025-05-28 14:50:00.018 [pool-14-thread-3] WARN  com.simbest.boot.base.repository.CustomDynamicWhere.queryForList Line:50 - 自定义查询SQL输出为：【select * from US_TODO_MODEL t  where t.send_flag = 0 and t.work_flag = 1 and t.enabled =1】
2025-05-28 14:50:00.018 [pool-14-thread-8] WARN  com.simbest.boot.base.repository.CustomDynamicWhere.queryForList Line:50 - 自定义查询SQL输出为：【select * from US_TODO_MODEL t  where t.send_flag = 0 and t.work_flag = 0 and t.enabled =1】
2025-05-28 14:55:00.014 [pool-14-thread-7] WARN  com.simbest.boot.base.repository.CustomDynamicWhere.queryForList Line:50 - 自定义查询SQL输出为：【select * from US_TODO_MODEL t  where t.send_flag = 0 and t.work_flag = 0 and t.enabled =1】
2025-05-28 14:55:00.014 [pool-14-thread-2] WARN  com.simbest.boot.base.repository.CustomDynamicWhere.queryForList Line:50 - 自定义查询SQL输出为：【select * from US_TODO_MODEL t  where t.send_flag = 0 and t.work_flag = 1 and t.enabled =1】
2025-05-28 15:00:00.012 [pool-14-thread-8] WARN  com.simbest.boot.base.repository.CustomDynamicWhere.queryForList Line:50 - 自定义查询SQL输出为：【select * from US_TODO_MODEL t  where t.send_flag = 0 and t.work_flag = 0 and t.enabled =1】
2025-05-28 15:00:00.012 [pool-14-thread-2] WARN  com.simbest.boot.base.repository.CustomDynamicWhere.queryForList Line:50 - 自定义查询SQL输出为：【select * from US_TODO_MODEL t  where t.send_flag = 0 and t.work_flag = 1 and t.enabled =1】
2025-05-28 15:00:00.013 [pool-14-thread-1] WARN  com.simbest.boot.cmcc.a4.task.A4TaskCreateXmlFile.execute Line:76 - >>>>>>>>>>>>>>>>>>>>>>>>>>>>>>>>>>>>>>>>>>>>>>>>>>>>>A4TaskCreateXmlFile:执行定时任务但不是Prd环境【2025-05-28T15:00:00.013】
2025-05-28 15:05:00.016 [pool-14-thread-1] WARN  com.simbest.boot.base.repository.CustomDynamicWhere.queryForList Line:50 - 自定义查询SQL输出为：【select * from US_TODO_MODEL t  where t.send_flag = 0 and t.work_flag = 0 and t.enabled =1】
2025-05-28 15:05:00.019 [pool-14-thread-9] WARN  com.simbest.boot.base.repository.CustomDynamicWhere.queryForList Line:50 - 自定义查询SQL输出为：【select * from US_TODO_MODEL t  where t.send_flag = 0 and t.work_flag = 1 and t.enabled =1】
2025-05-28 15:10:00.009 [pool-14-thread-2] WARN  com.simbest.boot.base.repository.CustomDynamicWhere.queryForList Line:50 - 自定义查询SQL输出为：【select * from US_TODO_MODEL t  where t.send_flag = 0 and t.work_flag = 1 and t.enabled =1】
2025-05-28 15:10:00.013 [pool-14-thread-7] WARN  com.simbest.boot.base.repository.CustomDynamicWhere.queryForList Line:50 - 自定义查询SQL输出为：【select * from US_TODO_MODEL t  where t.send_flag = 0 and t.work_flag = 0 and t.enabled =1】
2025-05-28 15:15:00.022 [pool-14-thread-3] WARN  com.simbest.boot.base.repository.CustomDynamicWhere.queryForList Line:50 - 自定义查询SQL输出为：【select * from US_TODO_MODEL t  where t.send_flag = 0 and t.work_flag = 1 and t.enabled =1】
2025-05-28 15:15:00.024 [pool-14-thread-10] WARN  com.simbest.boot.base.repository.CustomDynamicWhere.queryForList Line:50 - 自定义查询SQL输出为：【select * from US_TODO_MODEL t  where t.send_flag = 0 and t.work_flag = 0 and t.enabled =1】
2025-05-28 15:20:00.020 [pool-14-thread-6] WARN  com.simbest.boot.base.repository.CustomDynamicWhere.queryForList Line:50 - 自定义查询SQL输出为：【select * from US_TODO_MODEL t  where t.send_flag = 0 and t.work_flag = 1 and t.enabled =1】
2025-05-28 15:20:00.021 [pool-14-thread-1] WARN  com.simbest.boot.base.repository.CustomDynamicWhere.queryForList Line:50 - 自定义查询SQL输出为：【select * from US_TODO_MODEL t  where t.send_flag = 0 and t.work_flag = 0 and t.enabled =1】
2025-05-28 15:25:00.010 [pool-14-thread-7] WARN  com.simbest.boot.base.repository.CustomDynamicWhere.queryForList Line:50 - 自定义查询SQL输出为：【select * from US_TODO_MODEL t  where t.send_flag = 0 and t.work_flag = 1 and t.enabled =1】
2025-05-28 15:25:00.023 [pool-14-thread-8] WARN  com.simbest.boot.base.repository.CustomDynamicWhere.queryForList Line:50 - 自定义查询SQL输出为：【select * from US_TODO_MODEL t  where t.send_flag = 0 and t.work_flag = 0 and t.enabled =1】
2025-05-28 15:30:00.009 [pool-14-thread-1] WARN  com.simbest.boot.base.repository.CustomDynamicWhere.queryForList Line:50 - 自定义查询SQL输出为：【select * from US_TODO_MODEL t  where t.send_flag = 0 and t.work_flag = 0 and t.enabled =1】
2025-05-28 15:30:00.009 [pool-14-thread-10] WARN  com.simbest.boot.base.repository.CustomDynamicWhere.queryForList Line:50 - 自定义查询SQL输出为：【select * from US_TODO_MODEL t  where t.send_flag = 0 and t.work_flag = 1 and t.enabled =1】
2025-05-28 15:35:00.007 [pool-14-thread-8] WARN  com.simbest.boot.base.repository.CustomDynamicWhere.queryForList Line:50 - 自定义查询SQL输出为：【select * from US_TODO_MODEL t  where t.send_flag = 0 and t.work_flag = 1 and t.enabled =1】
2025-05-28 15:35:00.009 [pool-14-thread-3] WARN  com.simbest.boot.base.repository.CustomDynamicWhere.queryForList Line:50 - 自定义查询SQL输出为：【select * from US_TODO_MODEL t  where t.send_flag = 0 and t.work_flag = 0 and t.enabled =1】
2025-05-28 15:40:00.011 [pool-14-thread-6] WARN  com.simbest.boot.base.repository.CustomDynamicWhere.queryForList Line:50 - 自定义查询SQL输出为：【select * from US_TODO_MODEL t  where t.send_flag = 0 and t.work_flag = 1 and t.enabled =1】
2025-05-28 15:40:00.015 [pool-14-thread-8] WARN  com.simbest.boot.base.repository.CustomDynamicWhere.queryForList Line:50 - 自定义查询SQL输出为：【select * from US_TODO_MODEL t  where t.send_flag = 0 and t.work_flag = 0 and t.enabled =1】
2025-05-28 15:45:00.006 [pool-14-thread-9] WARN  com.simbest.boot.base.repository.CustomDynamicWhere.queryForList Line:50 - 自定义查询SQL输出为：【select * from US_TODO_MODEL t  where t.send_flag = 0 and t.work_flag = 1 and t.enabled =1】
2025-05-28 15:45:00.008 [pool-14-thread-10] WARN  com.simbest.boot.base.repository.CustomDynamicWhere.queryForList Line:50 - 自定义查询SQL输出为：【select * from US_TODO_MODEL t  where t.send_flag = 0 and t.work_flag = 0 and t.enabled =1】
2025-05-28 15:50:00.016 [pool-14-thread-4] WARN  com.simbest.boot.base.repository.CustomDynamicWhere.queryForList Line:50 - 自定义查询SQL输出为：【select * from US_TODO_MODEL t  where t.send_flag = 0 and t.work_flag = 0 and t.enabled =1】
2025-05-28 15:50:00.017 [pool-14-thread-3] WARN  com.simbest.boot.base.repository.CustomDynamicWhere.queryForList Line:50 - 自定义查询SQL输出为：【select * from US_TODO_MODEL t  where t.send_flag = 0 and t.work_flag = 1 and t.enabled =1】
2025-05-28 15:55:00.017 [pool-14-thread-5] WARN  com.simbest.boot.base.repository.CustomDynamicWhere.queryForList Line:50 - 自定义查询SQL输出为：【select * from US_TODO_MODEL t  where t.send_flag = 0 and t.work_flag = 0 and t.enabled =1】
2025-05-28 15:55:00.017 [pool-14-thread-8] WARN  com.simbest.boot.base.repository.CustomDynamicWhere.queryForList Line:50 - 自定义查询SQL输出为：【select * from US_TODO_MODEL t  where t.send_flag = 0 and t.work_flag = 1 and t.enabled =1】
2025-05-28 16:00:00.018 [pool-14-thread-3] WARN  com.simbest.boot.base.repository.CustomDynamicWhere.queryForList Line:50 - 自定义查询SQL输出为：【select * from US_TODO_MODEL t  where t.send_flag = 0 and t.work_flag = 0 and t.enabled =1】
2025-05-28 16:00:00.018 [pool-14-thread-7] WARN  com.simbest.boot.base.repository.CustomDynamicWhere.queryForList Line:50 - 自定义查询SQL输出为：【select * from US_TODO_MODEL t  where t.send_flag = 0 and t.work_flag = 1 and t.enabled =1】
2025-05-28 16:00:00.021 [pool-14-thread-6] WARN  com.simbest.boot.cmcc.a4.task.A4TaskCreateXmlFile.execute Line:76 - >>>>>>>>>>>>>>>>>>>>>>>>>>>>>>>>>>>>>>>>>>>>>>>>>>>>>A4TaskCreateXmlFile:执行定时任务但不是Prd环境【2025-05-28T16:00:00.021】
2025-05-28 16:05:00.015 [pool-14-thread-1] WARN  com.simbest.boot.base.repository.CustomDynamicWhere.queryForList Line:50 - 自定义查询SQL输出为：【select * from US_TODO_MODEL t  where t.send_flag = 0 and t.work_flag = 0 and t.enabled =1】
2025-05-28 16:05:00.015 [pool-14-thread-6] WARN  com.simbest.boot.base.repository.CustomDynamicWhere.queryForList Line:50 - 自定义查询SQL输出为：【select * from US_TODO_MODEL t  where t.send_flag = 0 and t.work_flag = 1 and t.enabled =1】
2025-05-28 16:10:00.017 [pool-14-thread-5] WARN  com.simbest.boot.base.repository.CustomDynamicWhere.queryForList Line:50 - 自定义查询SQL输出为：【select * from US_TODO_MODEL t  where t.send_flag = 0 and t.work_flag = 1 and t.enabled =1】
2025-05-28 16:10:00.019 [pool-14-thread-1] WARN  com.simbest.boot.base.repository.CustomDynamicWhere.queryForList Line:50 - 自定义查询SQL输出为：【select * from US_TODO_MODEL t  where t.send_flag = 0 and t.work_flag = 0 and t.enabled =1】
2025-05-28 16:15:00.006 [pool-14-thread-9] WARN  com.simbest.boot.base.repository.CustomDynamicWhere.queryForList Line:50 - 自定义查询SQL输出为：【select * from US_TODO_MODEL t  where t.send_flag = 0 and t.work_flag = 1 and t.enabled =1】
2025-05-28 16:15:00.013 [pool-14-thread-6] WARN  com.simbest.boot.base.repository.CustomDynamicWhere.queryForList Line:50 - 自定义查询SQL输出为：【select * from US_TODO_MODEL t  where t.send_flag = 0 and t.work_flag = 0 and t.enabled =1】
2025-05-28 16:20:00.012 [pool-14-thread-10] WARN  com.simbest.boot.base.repository.CustomDynamicWhere.queryForList Line:50 - 自定义查询SQL输出为：【select * from US_TODO_MODEL t  where t.send_flag = 0 and t.work_flag = 1 and t.enabled =1】
2025-05-28 16:20:00.014 [pool-14-thread-1] WARN  com.simbest.boot.base.repository.CustomDynamicWhere.queryForList Line:50 - 自定义查询SQL输出为：【select * from US_TODO_MODEL t  where t.send_flag = 0 and t.work_flag = 0 and t.enabled =1】
2025-05-28 16:25:00.020 [pool-14-thread-6] WARN  com.simbest.boot.base.repository.CustomDynamicWhere.queryForList Line:50 - 自定义查询SQL输出为：【select * from US_TODO_MODEL t  where t.send_flag = 0 and t.work_flag = 1 and t.enabled =1】
2025-05-28 16:25:00.020 [pool-14-thread-3] WARN  com.simbest.boot.base.repository.CustomDynamicWhere.queryForList Line:50 - 自定义查询SQL输出为：【select * from US_TODO_MODEL t  where t.send_flag = 0 and t.work_flag = 0 and t.enabled =1】
2025-05-28 16:30:00.017 [pool-14-thread-6] WARN  com.simbest.boot.base.repository.CustomDynamicWhere.queryForList Line:50 - 自定义查询SQL输出为：【select * from US_TODO_MODEL t  where t.send_flag = 0 and t.work_flag = 1 and t.enabled =1】
2025-05-28 16:30:00.260 [pool-14-thread-7] WARN  com.simbest.boot.base.repository.CustomDynamicWhere.queryForList Line:50 - 自定义查询SQL输出为：【select * from US_TODO_MODEL t  where t.send_flag = 0 and t.work_flag = 0 and t.enabled =1】
2025-05-28 16:35:00.013 [pool-14-thread-7] WARN  com.simbest.boot.base.repository.CustomDynamicWhere.queryForList Line:50 - 自定义查询SQL输出为：【select * from US_TODO_MODEL t  where t.send_flag = 0 and t.work_flag = 1 and t.enabled =1】
2025-05-28 16:35:00.014 [pool-14-thread-8] WARN  com.simbest.boot.base.repository.CustomDynamicWhere.queryForList Line:50 - 自定义查询SQL输出为：【select * from US_TODO_MODEL t  where t.send_flag = 0 and t.work_flag = 0 and t.enabled =1】
2025-05-28 16:40:00.009 [pool-14-thread-8] WARN  com.simbest.boot.base.repository.CustomDynamicWhere.queryForList Line:50 - 自定义查询SQL输出为：【select * from US_TODO_MODEL t  where t.send_flag = 0 and t.work_flag = 1 and t.enabled =1】
2025-05-28 16:40:00.009 [pool-14-thread-6] WARN  com.simbest.boot.base.repository.CustomDynamicWhere.queryForList Line:50 - 自定义查询SQL输出为：【select * from US_TODO_MODEL t  where t.send_flag = 0 and t.work_flag = 0 and t.enabled =1】
2025-05-28 16:45:00.015 [pool-14-thread-7] WARN  com.simbest.boot.base.repository.CustomDynamicWhere.queryForList Line:50 - 自定义查询SQL输出为：【select * from US_TODO_MODEL t  where t.send_flag = 0 and t.work_flag = 0 and t.enabled =1】
2025-05-28 16:45:00.015 [pool-14-thread-3] WARN  com.simbest.boot.base.repository.CustomDynamicWhere.queryForList Line:50 - 自定义查询SQL输出为：【select * from US_TODO_MODEL t  where t.send_flag = 0 and t.work_flag = 1 and t.enabled =1】
2025-05-28 16:50:00.010 [pool-14-thread-8] WARN  com.simbest.boot.base.repository.CustomDynamicWhere.queryForList Line:50 - 自定义查询SQL输出为：【select * from US_TODO_MODEL t  where t.send_flag = 0 and t.work_flag = 1 and t.enabled =1】
2025-05-28 16:50:00.010 [pool-14-thread-5] WARN  com.simbest.boot.base.repository.CustomDynamicWhere.queryForList Line:50 - 自定义查询SQL输出为：【select * from US_TODO_MODEL t  where t.send_flag = 0 and t.work_flag = 0 and t.enabled =1】
2025-05-28 16:55:00.005 [pool-14-thread-7] WARN  com.simbest.boot.base.repository.CustomDynamicWhere.queryForList Line:50 - 自定义查询SQL输出为：【select * from US_TODO_MODEL t  where t.send_flag = 0 and t.work_flag = 0 and t.enabled =1】
2025-05-28 16:55:00.007 [pool-14-thread-4] WARN  com.simbest.boot.base.repository.CustomDynamicWhere.queryForList Line:50 - 自定义查询SQL输出为：【select * from US_TODO_MODEL t  where t.send_flag = 0 and t.work_flag = 1 and t.enabled =1】
2025-05-28 17:00:00.011 [pool-14-thread-2] WARN  com.simbest.boot.base.repository.CustomDynamicWhere.queryForList Line:50 - 自定义查询SQL输出为：【select * from US_TODO_MODEL t  where t.send_flag = 0 and t.work_flag = 0 and t.enabled =1】
2025-05-28 17:00:00.012 [pool-14-thread-9] WARN  com.simbest.boot.cmcc.a4.task.A4TaskCreateXmlFile.execute Line:76 - >>>>>>>>>>>>>>>>>>>>>>>>>>>>>>>>>>>>>>>>>>>>>>>>>>>>>A4TaskCreateXmlFile:执行定时任务但不是Prd环境【2025-05-28T17:00:00.012】
2025-05-28 17:00:00.019 [pool-14-thread-6] WARN  com.simbest.boot.base.repository.CustomDynamicWhere.queryForList Line:50 - 自定义查询SQL输出为：【select * from US_TODO_MODEL t  where t.send_flag = 0 and t.work_flag = 1 and t.enabled =1】
2025-05-28 17:05:00.008 [pool-14-thread-10] WARN  com.simbest.boot.base.repository.CustomDynamicWhere.queryForList Line:50 - 自定义查询SQL输出为：【select * from US_TODO_MODEL t  where t.send_flag = 0 and t.work_flag = 0 and t.enabled =1】
2025-05-28 17:05:00.009 [pool-14-thread-1] WARN  com.simbest.boot.base.repository.CustomDynamicWhere.queryForList Line:50 - 自定义查询SQL输出为：【select * from US_TODO_MODEL t  where t.send_flag = 0 and t.work_flag = 1 and t.enabled =1】
2025-05-28 17:10:00.012 [pool-14-thread-7] WARN  com.simbest.boot.base.repository.CustomDynamicWhere.queryForList Line:50 - 自定义查询SQL输出为：【select * from US_TODO_MODEL t  where t.send_flag = 0 and t.work_flag = 1 and t.enabled =1】
2025-05-28 17:10:00.013 [pool-14-thread-10] WARN  com.simbest.boot.base.repository.CustomDynamicWhere.queryForList Line:50 - 自定义查询SQL输出为：【select * from US_TODO_MODEL t  where t.send_flag = 0 and t.work_flag = 0 and t.enabled =1】
2025-05-28 17:15:00.004 [pool-14-thread-6] WARN  com.simbest.boot.base.repository.CustomDynamicWhere.queryForList Line:50 - 自定义查询SQL输出为：【select * from US_TODO_MODEL t  where t.send_flag = 0 and t.work_flag = 1 and t.enabled =1】
2025-05-28 17:15:00.004 [pool-14-thread-9] WARN  com.simbest.boot.base.repository.CustomDynamicWhere.queryForList Line:50 - 自定义查询SQL输出为：【select * from US_TODO_MODEL t  where t.send_flag = 0 and t.work_flag = 0 and t.enabled =1】
2025-05-28 17:20:00.026 [pool-14-thread-1] WARN  com.simbest.boot.base.repository.CustomDynamicWhere.queryForList Line:50 - 自定义查询SQL输出为：【select * from US_TODO_MODEL t  where t.send_flag = 0 and t.work_flag = 0 and t.enabled =1】
2025-05-28 17:20:00.026 [pool-14-thread-2] WARN  com.simbest.boot.base.repository.CustomDynamicWhere.queryForList Line:50 - 自定义查询SQL输出为：【select * from US_TODO_MODEL t  where t.send_flag = 0 and t.work_flag = 1 and t.enabled =1】
2025-05-28 17:25:00.016 [pool-14-thread-1] WARN  com.simbest.boot.base.repository.CustomDynamicWhere.queryForList Line:50 - 自定义查询SQL输出为：【select * from US_TODO_MODEL t  where t.send_flag = 0 and t.work_flag = 1 and t.enabled =1】
2025-05-28 17:25:00.017 [pool-14-thread-10] WARN  com.simbest.boot.base.repository.CustomDynamicWhere.queryForList Line:50 - 自定义查询SQL输出为：【select * from US_TODO_MODEL t  where t.send_flag = 0 and t.work_flag = 0 and t.enabled =1】
2025-05-28 17:30:00.007 [pool-14-thread-2] WARN  com.simbest.boot.base.repository.CustomDynamicWhere.queryForList Line:50 - 自定义查询SQL输出为：【select * from US_TODO_MODEL t  where t.send_flag = 0 and t.work_flag = 0 and t.enabled =1】
2025-05-28 17:30:00.009 [pool-14-thread-10] WARN  com.simbest.boot.base.repository.CustomDynamicWhere.queryForList Line:50 - 自定义查询SQL输出为：【select * from US_TODO_MODEL t  where t.send_flag = 0 and t.work_flag = 1 and t.enabled =1】
2025-05-28 17:35:00.017 [pool-14-thread-7] WARN  com.simbest.boot.base.repository.CustomDynamicWhere.queryForList Line:50 - 自定义查询SQL输出为：【select * from US_TODO_MODEL t  where t.send_flag = 0 and t.work_flag = 1 and t.enabled =1】
2025-05-28 17:35:00.017 [pool-14-thread-5] WARN  com.simbest.boot.base.repository.CustomDynamicWhere.queryForList Line:50 - 自定义查询SQL输出为：【select * from US_TODO_MODEL t  where t.send_flag = 0 and t.work_flag = 0 and t.enabled =1】
2025-05-28 17:40:00.006 [pool-14-thread-7] WARN  com.simbest.boot.base.repository.CustomDynamicWhere.queryForList Line:50 - 自定义查询SQL输出为：【select * from US_TODO_MODEL t  where t.send_flag = 0 and t.work_flag = 1 and t.enabled =1】
2025-05-28 17:40:00.009 [pool-14-thread-9] WARN  com.simbest.boot.base.repository.CustomDynamicWhere.queryForList Line:50 - 自定义查询SQL输出为：【select * from US_TODO_MODEL t  where t.send_flag = 0 and t.work_flag = 0 and t.enabled =1】
2025-05-28 17:45:00.008 [pool-14-thread-2] WARN  com.simbest.boot.base.repository.CustomDynamicWhere.queryForList Line:50 - 自定义查询SQL输出为：【select * from US_TODO_MODEL t  where t.send_flag = 0 and t.work_flag = 0 and t.enabled =1】
2025-05-28 17:45:00.009 [pool-14-thread-7] WARN  com.simbest.boot.base.repository.CustomDynamicWhere.queryForList Line:50 - 自定义查询SQL输出为：【select * from US_TODO_MODEL t  where t.send_flag = 0 and t.work_flag = 1 and t.enabled =1】
2025-05-28 17:50:00.006 [pool-14-thread-8] WARN  com.simbest.boot.base.repository.CustomDynamicWhere.queryForList Line:50 - 自定义查询SQL输出为：【select * from US_TODO_MODEL t  where t.send_flag = 0 and t.work_flag = 0 and t.enabled =1】
2025-05-28 17:50:00.007 [pool-14-thread-9] WARN  com.simbest.boot.base.repository.CustomDynamicWhere.queryForList Line:50 - 自定义查询SQL输出为：【select * from US_TODO_MODEL t  where t.send_flag = 0 and t.work_flag = 1 and t.enabled =1】
2025-05-28 17:55:00.029 [pool-14-thread-10] WARN  com.simbest.boot.base.repository.CustomDynamicWhere.queryForList Line:50 - 自定义查询SQL输出为：【select * from US_TODO_MODEL t  where t.send_flag = 0 and t.work_flag = 1 and t.enabled =1】
2025-05-28 17:55:00.030 [pool-14-thread-7] WARN  com.simbest.boot.base.repository.CustomDynamicWhere.queryForList Line:50 - 自定义查询SQL输出为：【select * from US_TODO_MODEL t  where t.send_flag = 0 and t.work_flag = 0 and t.enabled =1】
2025-05-28 17:56:29.802 [http-nio-8092-exec-15] WARN  org.springframework.web.servlet.mvc.method.annotation.ExceptionHandlerExceptionResolver.doResolveHandlerMethodException Line:414 - Failure in @ExceptionHandler com.simbest.boot.base.exception.RestControllerExceptionHandler#handleErrorException(HttpServletRequest, Exception)
org.apache.catalina.connector.ClientAbortException: java.io.IOException: 你的主机中的软件中止了一个已建立的连接。
	at org.apache.catalina.connector.OutputBuffer.doFlush(OutputBuffer.java:310)
	at org.apache.catalina.connector.OutputBuffer.flush(OutputBuffer.java:273)
	at org.apache.catalina.connector.CoyoteOutputStream.flush(CoyoteOutputStream.java:118)
	at org.springframework.session.web.http.OnCommittedResponseWrapper$SaveContextServletOutputStream.flush(OnCommittedResponseWrapper.java:499)
	at org.springframework.security.web.util.OnCommittedResponseWrapper$SaveContextServletOutputStream.flush(OnCommittedResponseWrapper.java:520)
	at org.springframework.security.web.util.OnCommittedResponseWrapper$SaveContextServletOutputStream.flush(OnCommittedResponseWrapper.java:520)
	at net.bull.javamelody.internal.web.FilterServletOutputStream.flush(FilterServletOutputStream.java:69)
	at com.fasterxml.jackson.core.json.UTF8JsonGenerator.flush(UTF8JsonGenerator.java:1153)
	at com.fasterxml.jackson.databind.ObjectWriter.writeValue(ObjectWriter.java:923)
	at org.springframework.http.converter.json.AbstractJackson2HttpMessageConverter.writeInternal(AbstractJackson2HttpMessageConverter.java:287)
	at org.springframework.http.converter.AbstractGenericHttpMessageConverter.write(AbstractGenericHttpMessageConverter.java:104)
	at org.springframework.web.servlet.mvc.method.annotation.AbstractMessageConverterMethodProcessor.writeWithMessageConverters(AbstractMessageConverterMethodProcessor.java:295)
	at org.springframework.web.servlet.mvc.method.annotation.RequestResponseBodyMethodProcessor.handleReturnValue(RequestResponseBodyMethodProcessor.java:181)
	at org.springframework.web.method.support.HandlerMethodReturnValueHandlerComposite.handleReturnValue(HandlerMethodReturnValueHandlerComposite.java:82)
	at org.springframework.web.servlet.mvc.method.annotation.ServletInvocableHandlerMethod.invokeAndHandle(ServletInvocableHandlerMethod.java:124)
	at org.springframework.web.servlet.mvc.method.annotation.ExceptionHandlerExceptionResolver.doResolveHandlerMethodException(ExceptionHandlerExceptionResolver.java:403)
	at org.springframework.web.servlet.handler.AbstractHandlerMethodExceptionResolver.doResolveException(AbstractHandlerMethodExceptionResolver.java:61)
	at org.springframework.web.servlet.handler.AbstractHandlerExceptionResolver.resolveException(AbstractHandlerExceptionResolver.java:141)
	at org.springframework.web.servlet.handler.HandlerExceptionResolverComposite.resolveException(HandlerExceptionResolverComposite.java:80)
	at org.springframework.web.servlet.DispatcherServlet.processHandlerException(DispatcherServlet.java:1300)
	at org.springframework.web.servlet.DispatcherServlet.processDispatchResult(DispatcherServlet.java:1111)
	at org.springframework.web.servlet.DispatcherServlet.doDispatch(DispatcherServlet.java:1057)
	at org.springframework.web.servlet.DispatcherServlet.doService(DispatcherServlet.java:943)
	at org.springframework.web.servlet.FrameworkServlet.processRequest(FrameworkServlet.java:1006)
	at org.springframework.web.servlet.FrameworkServlet.doPost(FrameworkServlet.java:909)
	at javax.servlet.http.HttpServlet.service(HttpServlet.java:681)
	at org.springframework.web.servlet.FrameworkServlet.service(FrameworkServlet.java:883)
	at javax.servlet.http.HttpServlet.service(HttpServlet.java:764)
	at org.apache.catalina.core.ApplicationFilterChain.internalDoFilter(ApplicationFilterChain.java:227)
	at org.apache.catalina.core.ApplicationFilterChain.doFilter(ApplicationFilterChain.java:162)
	at org.apache.tomcat.websocket.server.WsFilter.doFilter(WsFilter.java:53)
	at org.apache.catalina.core.ApplicationFilterChain.internalDoFilter(ApplicationFilterChain.java:189)
	at org.apache.catalina.core.ApplicationFilterChain.doFilter(ApplicationFilterChain.java:162)
	at com.simbest.boot.security.auth.filter.CaptchaAuthenticationFilter.doFilter(CaptchaAuthenticationFilter.java:46)
	at org.apache.catalina.core.ApplicationFilterChain.internalDoFilter(ApplicationFilterChain.java:189)
	at org.apache.catalina.core.ApplicationFilterChain.doFilter(ApplicationFilterChain.java:162)
	at com.simbest.boot.security.auth.filter.CaptchaAuthenticationFilter.doFilter(CaptchaAuthenticationFilter.java:46)
	at org.apache.catalina.core.ApplicationFilterChain.internalDoFilter(ApplicationFilterChain.java:189)
	at org.apache.catalina.core.ApplicationFilterChain.doFilter(ApplicationFilterChain.java:162)
	at org.springframework.security.web.authentication.AbstractAuthenticationProcessingFilter.doFilter(AbstractAuthenticationProcessingFilter.java:200)
	at org.apache.catalina.core.ApplicationFilterChain.internalDoFilter(ApplicationFilterChain.java:189)
	at org.apache.catalina.core.ApplicationFilterChain.doFilter(ApplicationFilterChain.java:162)
	at org.springframework.security.web.authentication.logout.LogoutFilter.doFilter(LogoutFilter.java:116)
	at org.apache.catalina.core.ApplicationFilterChain.internalDoFilter(ApplicationFilterChain.java:189)
	at org.apache.catalina.core.ApplicationFilterChain.doFilter(ApplicationFilterChain.java:162)
	at org.springframework.security.web.authentication.AbstractAuthenticationProcessingFilter.doFilter(AbstractAuthenticationProcessingFilter.java:200)
	at org.apache.catalina.core.ApplicationFilterChain.internalDoFilter(ApplicationFilterChain.java:189)
	at org.apache.catalina.core.ApplicationFilterChain.doFilter(ApplicationFilterChain.java:162)
	at org.springframework.security.web.authentication.AbstractAuthenticationProcessingFilter.doFilter(AbstractAuthenticationProcessingFilter.java:200)
	at org.apache.catalina.core.ApplicationFilterChain.internalDoFilter(ApplicationFilterChain.java:189)
	at org.apache.catalina.core.ApplicationFilterChain.doFilter(ApplicationFilterChain.java:162)
	at org.springframework.security.web.authentication.AbstractAuthenticationProcessingFilter.doFilter(AbstractAuthenticationProcessingFilter.java:200)
	at org.apache.catalina.core.ApplicationFilterChain.internalDoFilter(ApplicationFilterChain.java:189)
	at org.apache.catalina.core.ApplicationFilterChain.doFilter(ApplicationFilterChain.java:162)
	at org.springframework.security.web.authentication.AbstractAuthenticationProcessingFilter.doFilter(AbstractAuthenticationProcessingFilter.java:200)
	at org.apache.catalina.core.ApplicationFilterChain.internalDoFilter(ApplicationFilterChain.java:189)
	at org.apache.catalina.core.ApplicationFilterChain.doFilter(ApplicationFilterChain.java:162)
	at org.springframework.security.web.authentication.AbstractAuthenticationProcessingFilter.doFilter(AbstractAuthenticationProcessingFilter.java:200)
	at org.apache.catalina.core.ApplicationFilterChain.internalDoFilter(ApplicationFilterChain.java:189)
	at org.apache.catalina.core.ApplicationFilterChain.doFilter(ApplicationFilterChain.java:162)
	at com.simbest.boot.base.web.filter.CsrfProtectFilter.doFilter(CsrfProtectFilter.java:164)
	at org.apache.catalina.core.ApplicationFilterChain.internalDoFilter(ApplicationFilterChain.java:189)
	at org.apache.catalina.core.ApplicationFilterChain.doFilter(ApplicationFilterChain.java:162)
	at org.springframework.web.servlet.resource.ResourceUrlEncodingFilter.doFilter(ResourceUrlEncodingFilter.java:64)
	at org.apache.catalina.core.ApplicationFilterChain.internalDoFilter(ApplicationFilterChain.java:189)
	at org.apache.catalina.core.ApplicationFilterChain.doFilter(ApplicationFilterChain.java:162)
	at net.bull.javamelody.MonitoringFilter.doFilter(MonitoringFilter.java:199)
	at org.apache.catalina.core.ApplicationFilterChain.internalDoFilter(ApplicationFilterChain.java:189)
	at org.apache.catalina.core.ApplicationFilterChain.doFilter(ApplicationFilterChain.java:162)
	at net.bull.javamelody.MonitoringFilter.doFilter(MonitoringFilter.java:239)
	at net.bull.javamelody.MonitoringFilter.doFilter(MonitoringFilter.java:215)
	at org.apache.catalina.core.ApplicationFilterChain.internalDoFilter(ApplicationFilterChain.java:189)
	at org.apache.catalina.core.ApplicationFilterChain.doFilter(ApplicationFilterChain.java:162)
	at org.springframework.security.web.FilterChainProxy$VirtualFilterChain.doFilter(FilterChainProxy.java:320)
	at org.springframework.security.web.access.intercept.FilterSecurityInterceptor.invoke(FilterSecurityInterceptor.java:126)
	at org.springframework.security.web.access.intercept.FilterSecurityInterceptor.doFilter(FilterSecurityInterceptor.java:90)
	at org.springframework.security.web.FilterChainProxy$VirtualFilterChain.doFilter(FilterChainProxy.java:334)
	at org.springframework.security.web.access.ExceptionTranslationFilter.doFilter(ExceptionTranslationFilter.java:118)
	at org.springframework.security.web.FilterChainProxy$VirtualFilterChain.doFilter(FilterChainProxy.java:334)
	at org.springframework.security.web.session.SessionManagementFilter.doFilter(SessionManagementFilter.java:137)
	at org.springframework.security.web.FilterChainProxy$VirtualFilterChain.doFilter(FilterChainProxy.java:334)
	at org.springframework.security.web.authentication.AnonymousAuthenticationFilter.doFilter(AnonymousAuthenticationFilter.java:111)
	at org.springframework.security.web.FilterChainProxy$VirtualFilterChain.doFilter(FilterChainProxy.java:334)
	at org.springframework.security.web.servletapi.SecurityContextHolderAwareRequestFilter.doFilter(SecurityContextHolderAwareRequestFilter.java:158)
	at org.springframework.security.web.FilterChainProxy$VirtualFilterChain.doFilter(FilterChainProxy.java:334)
	at org.springframework.security.web.savedrequest.RequestCacheAwareFilter.doFilter(RequestCacheAwareFilter.java:63)
	at org.springframework.security.web.FilterChainProxy$VirtualFilterChain.doFilter(FilterChainProxy.java:334)
	at org.springframework.security.web.session.ConcurrentSessionFilter.doFilter(ConcurrentSessionFilter.java:152)
	at org.springframework.security.web.FilterChainProxy$VirtualFilterChain.doFilter(FilterChainProxy.java:334)
	at org.springframework.security.web.authentication.AbstractAuthenticationProcessingFilter.doFilter(AbstractAuthenticationProcessingFilter.java:200)
	at org.springframework.security.web.FilterChainProxy$VirtualFilterChain.doFilter(FilterChainProxy.java:334)
	at org.springframework.security.web.authentication.AbstractAuthenticationProcessingFilter.doFilter(AbstractAuthenticationProcessingFilter.java:200)
	at org.springframework.security.web.FilterChainProxy$VirtualFilterChain.doFilter(FilterChainProxy.java:334)
	at org.springframework.security.web.authentication.AbstractAuthenticationProcessingFilter.doFilter(AbstractAuthenticationProcessingFilter.java:200)
	at org.springframework.security.web.FilterChainProxy$VirtualFilterChain.doFilter(FilterChainProxy.java:334)
	at org.springframework.security.web.authentication.AbstractAuthenticationProcessingFilter.doFilter(AbstractAuthenticationProcessingFilter.java:200)
	at org.springframework.security.web.FilterChainProxy$VirtualFilterChain.doFilter(FilterChainProxy.java:334)
	at org.springframework.security.web.authentication.AbstractAuthenticationProcessingFilter.doFilter(AbstractAuthenticationProcessingFilter.java:200)
	at org.springframework.security.web.FilterChainProxy$VirtualFilterChain.doFilter(FilterChainProxy.java:334)
	at org.springframework.security.web.authentication.AbstractAuthenticationProcessingFilter.doFilter(AbstractAuthenticationProcessingFilter.java:200)
	at org.springframework.security.web.FilterChainProxy$VirtualFilterChain.doFilter(FilterChainProxy.java:334)
	at org.springframework.security.web.authentication.AbstractAuthenticationProcessingFilter.doFilter(AbstractAuthenticationProcessingFilter.java:200)
	at org.springframework.security.web.FilterChainProxy$VirtualFilterChain.doFilter(FilterChainProxy.java:334)
	at com.simbest.boot.security.auth.filter.CaptchaAuthenticationFilter.doFilter(CaptchaAuthenticationFilter.java:46)
	at org.springframework.security.web.FilterChainProxy$VirtualFilterChain.doFilter(FilterChainProxy.java:334)
	at com.simbest.boot.security.auth.filter.CaptchaAuthenticationFilter.doFilter(CaptchaAuthenticationFilter.java:46)
	at org.springframework.security.web.FilterChainProxy$VirtualFilterChain.doFilter(FilterChainProxy.java:334)
	at org.springframework.security.web.authentication.logout.LogoutFilter.doFilter(LogoutFilter.java:116)
	at org.springframework.security.web.FilterChainProxy$VirtualFilterChain.doFilter(FilterChainProxy.java:334)
	at org.springframework.web.filter.CorsFilter.doFilterInternal(CorsFilter.java:92)
	at org.springframework.web.filter.OncePerRequestFilter.doFilter(OncePerRequestFilter.java:119)
	at org.springframework.security.web.FilterChainProxy$VirtualFilterChain.doFilter(FilterChainProxy.java:334)
	at org.springframework.security.web.header.HeaderWriterFilter.doHeadersAfter(HeaderWriterFilter.java:92)
	at org.springframework.security.web.header.HeaderWriterFilter.doFilterInternal(HeaderWriterFilter.java:77)
	at org.springframework.web.filter.OncePerRequestFilter.doFilter(OncePerRequestFilter.java:119)
	at org.springframework.security.web.FilterChainProxy$VirtualFilterChain.doFilter(FilterChainProxy.java:334)
	at com.simbest.boot.security.auth.filter.CloudHeaderTokenAuthenticationFilter.doFilterInternal(CloudHeaderTokenAuthenticationFilter.java:171)
	at org.springframework.web.filter.OncePerRequestFilter.doFilter(OncePerRequestFilter.java:119)
	at org.springframework.security.web.FilterChainProxy$VirtualFilterChain.doFilter(FilterChainProxy.java:334)
	at org.springframework.security.web.context.SecurityContextPersistenceFilter.doFilter(SecurityContextPersistenceFilter.java:105)
	at org.springframework.security.web.FilterChainProxy$VirtualFilterChain.doFilter(FilterChainProxy.java:334)
	at org.springframework.security.web.context.request.async.WebAsyncManagerIntegrationFilter.doFilterInternal(WebAsyncManagerIntegrationFilter.java:56)
	at org.springframework.web.filter.OncePerRequestFilter.doFilter(OncePerRequestFilter.java:119)
	at org.springframework.security.web.FilterChainProxy$VirtualFilterChain.doFilter(FilterChainProxy.java:334)
	at org.springframework.security.web.FilterChainProxy.doFilterInternal(FilterChainProxy.java:215)
	at org.springframework.security.web.FilterChainProxy.doFilter(FilterChainProxy.java:178)
	at org.springframework.web.filter.DelegatingFilterProxy.invokeDelegate(DelegatingFilterProxy.java:358)
	at org.springframework.web.filter.DelegatingFilterProxy.doFilter(DelegatingFilterProxy.java:271)
	at org.apache.catalina.core.ApplicationFilterChain.internalDoFilter(ApplicationFilterChain.java:189)
	at org.apache.catalina.core.ApplicationFilterChain.doFilter(ApplicationFilterChain.java:162)
	at org.springframework.session.web.http.SessionRepositoryFilter.doFilterInternal(SessionRepositoryFilter.java:141)
	at org.springframework.session.web.http.OncePerRequestFilter.doFilter(OncePerRequestFilter.java:82)
	at org.apache.catalina.core.ApplicationFilterChain.internalDoFilter(ApplicationFilterChain.java:189)
	at org.apache.catalina.core.ApplicationFilterChain.doFilter(ApplicationFilterChain.java:162)
	at org.springframework.boot.actuate.metrics.web.servlet.WebMvcMetricsFilter.doFilterInternal(WebMvcMetricsFilter.java:108)
	at org.springframework.web.filter.OncePerRequestFilter.doFilter(OncePerRequestFilter.java:119)
	at org.apache.catalina.core.ApplicationFilterChain.internalDoFilter(ApplicationFilterChain.java:189)
	at org.apache.catalina.core.ApplicationFilterChain.doFilter(ApplicationFilterChain.java:162)
	at org.springframework.web.filter.CharacterEncodingFilter.doFilterInternal(CharacterEncodingFilter.java:201)
	at org.springframework.web.filter.OncePerRequestFilter.doFilter(OncePerRequestFilter.java:119)
	at org.apache.catalina.core.ApplicationFilterChain.internalDoFilter(ApplicationFilterChain.java:189)
	at org.apache.catalina.core.ApplicationFilterChain.doFilter(ApplicationFilterChain.java:162)
	at org.apache.catalina.core.StandardWrapperValve.invoke(StandardWrapperValve.java:197)
	at org.apache.catalina.core.StandardContextValve.invoke(StandardContextValve.java:97)
	at org.apache.catalina.authenticator.AuthenticatorBase.invoke(AuthenticatorBase.java:541)
	at org.apache.catalina.core.StandardHostValve.invoke(StandardHostValve.java:135)
	at org.apache.catalina.valves.ErrorReportValve.invoke(ErrorReportValve.java:92)
	at org.apache.catalina.core.StandardEngineValve.invoke(StandardEngineValve.java:78)
	at org.apache.catalina.connector.CoyoteAdapter.service(CoyoteAdapter.java:360)
	at org.apache.coyote.http11.Http11Processor.service(Http11Processor.java:399)
	at org.apache.coyote.AbstractProcessorLight.process(AbstractProcessorLight.java:65)
	at org.apache.coyote.AbstractProtocol$ConnectionHandler.process(AbstractProtocol.java:890)
	at org.apache.tomcat.util.net.NioEndpoint$SocketProcessor.doRun(NioEndpoint.java:1789)
	at org.apache.tomcat.util.net.SocketProcessorBase.run(SocketProcessorBase.java:49)
	at org.apache.tomcat.util.threads.ThreadPoolExecutor.runWorker(ThreadPoolExecutor.java:1191)
	at org.apache.tomcat.util.threads.ThreadPoolExecutor$Worker.run(ThreadPoolExecutor.java:659)
	at org.apache.tomcat.util.threads.TaskThread$WrappingRunnable.run(TaskThread.java:61)
	at java.lang.Thread.run(Thread.java:748)
Caused by: java.io.IOException: 你的主机中的软件中止了一个已建立的连接。
	at sun.nio.ch.SocketDispatcher.write0(Native Method)
	at sun.nio.ch.SocketDispatcher.write(SocketDispatcher.java:51)
	at sun.nio.ch.IOUtil.writeFromNativeBuffer(IOUtil.java:93)
	at sun.nio.ch.IOUtil.write(IOUtil.java:65)
	at sun.nio.ch.SocketChannelImpl.write(SocketChannelImpl.java:471)
	at org.apache.tomcat.util.net.NioChannel.write(NioChannel.java:135)
	at org.apache.tomcat.util.net.NioEndpoint$NioSocketWrapper.doWrite(NioEndpoint.java:1424)
	at org.apache.tomcat.util.net.SocketWrapperBase.doWrite(SocketWrapperBase.java:768)
	at org.apache.tomcat.util.net.SocketWrapperBase.flushBlocking(SocketWrapperBase.java:732)
	at org.apache.tomcat.util.net.SocketWrapperBase.flush(SocketWrapperBase.java:716)
	at org.apache.coyote.http11.Http11OutputBuffer$SocketOutputBuffer.flush(Http11OutputBuffer.java:573)
	at org.apache.coyote.http11.filters.ChunkedOutputFilter.flush(ChunkedOutputFilter.java:157)
	at org.apache.coyote.http11.Http11OutputBuffer.flush(Http11OutputBuffer.java:221)
	at org.apache.coyote.http11.Http11Processor.flush(Http11Processor.java:1255)
	at org.apache.coyote.AbstractProcessor.action(AbstractProcessor.java:402)
	at org.apache.coyote.Response.action(Response.java:209)
	at org.apache.catalina.connector.OutputBuffer.doFlush(OutputBuffer.java:306)
	... 157 common frames omitted
2025-05-28 18:00:00.011 [pool-14-thread-6] WARN  com.simbest.boot.base.repository.CustomDynamicWhere.queryForList Line:50 - 自定义查询SQL输出为：【select * from US_TODO_MODEL t  where t.send_flag = 0 and t.work_flag = 0 and t.enabled =1】
2025-05-28 18:00:00.011 [pool-14-thread-4] WARN  com.simbest.boot.base.repository.CustomDynamicWhere.queryForList Line:50 - 自定义查询SQL输出为：【select * from US_TODO_MODEL t  where t.send_flag = 0 and t.work_flag = 1 and t.enabled =1】
2025-05-28 18:00:00.012 [pool-14-thread-3] WARN  com.simbest.boot.cmcc.a4.task.A4TaskCreateXmlFile.execute Line:76 - >>>>>>>>>>>>>>>>>>>>>>>>>>>>>>>>>>>>>>>>>>>>>>>>>>>>>A4TaskCreateXmlFile:执行定时任务但不是Prd环境【2025-05-28T18:00:00.011】
2025-05-28 18:05:00.021 [pool-14-thread-10] WARN  com.simbest.boot.base.repository.CustomDynamicWhere.queryForList Line:50 - 自定义查询SQL输出为：【select * from US_TODO_MODEL t  where t.send_flag = 0 and t.work_flag = 1 and t.enabled =1】
2025-05-28 18:05:00.022 [pool-14-thread-1] WARN  com.simbest.boot.base.repository.CustomDynamicWhere.queryForList Line:50 - 自定义查询SQL输出为：【select * from US_TODO_MODEL t  where t.send_flag = 0 and t.work_flag = 0 and t.enabled =1】
2025-05-28 18:10:00.008 [pool-14-thread-3] WARN  com.simbest.boot.base.repository.CustomDynamicWhere.queryForList Line:50 - 自定义查询SQL输出为：【select * from US_TODO_MODEL t  where t.send_flag = 0 and t.work_flag = 0 and t.enabled =1】
2025-05-28 18:10:00.011 [pool-14-thread-10] WARN  com.simbest.boot.base.repository.CustomDynamicWhere.queryForList Line:50 - 自定义查询SQL输出为：【select * from US_TODO_MODEL t  where t.send_flag = 0 and t.work_flag = 1 and t.enabled =1】
2025-05-28 18:15:00.014 [pool-14-thread-9] WARN  com.simbest.boot.base.repository.CustomDynamicWhere.queryForList Line:50 - 自定义查询SQL输出为：【select * from US_TODO_MODEL t  where t.send_flag = 0 and t.work_flag = 1 and t.enabled =1】
2025-05-28 18:15:00.014 [pool-14-thread-5] WARN  com.simbest.boot.base.repository.CustomDynamicWhere.queryForList Line:50 - 自定义查询SQL输出为：【select * from US_TODO_MODEL t  where t.send_flag = 0 and t.work_flag = 0 and t.enabled =1】
2025-05-28 18:20:00.017 [pool-14-thread-4] WARN  com.simbest.boot.base.repository.CustomDynamicWhere.queryForList Line:50 - 自定义查询SQL输出为：【select * from US_TODO_MODEL t  where t.send_flag = 0 and t.work_flag = 0 and t.enabled =1】
2025-05-28 18:20:00.018 [pool-14-thread-10] WARN  com.simbest.boot.base.repository.CustomDynamicWhere.queryForList Line:50 - 自定义查询SQL输出为：【select * from US_TODO_MODEL t  where t.send_flag = 0 and t.work_flag = 1 and t.enabled =1】
2025-05-28 18:25:00.017 [pool-14-thread-10] WARN  com.simbest.boot.base.repository.CustomDynamicWhere.queryForList Line:50 - 自定义查询SQL输出为：【select * from US_TODO_MODEL t  where t.send_flag = 0 and t.work_flag = 0 and t.enabled =1】
2025-05-28 18:25:00.019 [pool-14-thread-7] WARN  com.simbest.boot.base.repository.CustomDynamicWhere.queryForList Line:50 - 自定义查询SQL输出为：【select * from US_TODO_MODEL t  where t.send_flag = 0 and t.work_flag = 1 and t.enabled =1】
2025-05-28 18:30:00.006 [pool-14-thread-1] WARN  com.simbest.boot.base.repository.CustomDynamicWhere.queryForList Line:50 - 自定义查询SQL输出为：【select * from US_TODO_MODEL t  where t.send_flag = 0 and t.work_flag = 0 and t.enabled =1】
2025-05-28 18:30:00.007 [pool-14-thread-7] WARN  com.simbest.boot.base.repository.CustomDynamicWhere.queryForList Line:50 - 自定义查询SQL输出为：【select * from US_TODO_MODEL t  where t.send_flag = 0 and t.work_flag = 1 and t.enabled =1】
2025-05-28 18:35:00.012 [pool-14-thread-2] WARN  com.simbest.boot.base.repository.CustomDynamicWhere.queryForList Line:50 - 自定义查询SQL输出为：【select * from US_TODO_MODEL t  where t.send_flag = 0 and t.work_flag = 0 and t.enabled =1】
2025-05-28 18:35:00.013 [pool-14-thread-5] WARN  com.simbest.boot.base.repository.CustomDynamicWhere.queryForList Line:50 - 自定义查询SQL输出为：【select * from US_TODO_MODEL t  where t.send_flag = 0 and t.work_flag = 1 and t.enabled =1】
2025-05-28 18:40:00.015 [pool-14-thread-9] WARN  com.simbest.boot.base.repository.CustomDynamicWhere.queryForList Line:50 - 自定义查询SQL输出为：【select * from US_TODO_MODEL t  where t.send_flag = 0 and t.work_flag = 0 and t.enabled =1】
2025-05-28 18:40:00.015 [pool-14-thread-10] WARN  com.simbest.boot.base.repository.CustomDynamicWhere.queryForList Line:50 - 自定义查询SQL输出为：【select * from US_TODO_MODEL t  where t.send_flag = 0 and t.work_flag = 1 and t.enabled =1】
2025-05-28 18:45:00.019 [pool-14-thread-3] WARN  com.simbest.boot.base.repository.CustomDynamicWhere.queryForList Line:50 - 自定义查询SQL输出为：【select * from US_TODO_MODEL t  where t.send_flag = 0 and t.work_flag = 0 and t.enabled =1】
2025-05-28 18:45:00.019 [pool-14-thread-10] WARN  com.simbest.boot.base.repository.CustomDynamicWhere.queryForList Line:50 - 自定义查询SQL输出为：【select * from US_TODO_MODEL t  where t.send_flag = 0 and t.work_flag = 1 and t.enabled =1】
2025-05-28 18:50:00.022 [pool-14-thread-3] WARN  com.simbest.boot.base.repository.CustomDynamicWhere.queryForList Line:50 - 自定义查询SQL输出为：【select * from US_TODO_MODEL t  where t.send_flag = 0 and t.work_flag = 1 and t.enabled =1】
2025-05-28 18:50:00.021 [pool-14-thread-5] WARN  com.simbest.boot.base.repository.CustomDynamicWhere.queryForList Line:50 - 自定义查询SQL输出为：【select * from US_TODO_MODEL t  where t.send_flag = 0 and t.work_flag = 0 and t.enabled =1】
2025-05-28 18:55:00.030 [pool-14-thread-1] WARN  com.simbest.boot.base.repository.CustomDynamicWhere.queryForList Line:50 - 自定义查询SQL输出为：【select * from US_TODO_MODEL t  where t.send_flag = 0 and t.work_flag = 0 and t.enabled =1】
2025-05-28 18:55:00.030 [pool-14-thread-3] WARN  com.simbest.boot.base.repository.CustomDynamicWhere.queryForList Line:50 - 自定义查询SQL输出为：【select * from US_TODO_MODEL t  where t.send_flag = 0 and t.work_flag = 1 and t.enabled =1】
2025-05-28 19:00:00.017 [pool-14-thread-3] WARN  com.simbest.boot.base.repository.CustomDynamicWhere.queryForList Line:50 - 自定义查询SQL输出为：【select * from US_TODO_MODEL t  where t.send_flag = 0 and t.work_flag = 0 and t.enabled =1】
2025-05-28 19:00:00.018 [pool-14-thread-4] WARN  com.simbest.boot.base.repository.CustomDynamicWhere.queryForList Line:50 - 自定义查询SQL输出为：【select * from US_TODO_MODEL t  where t.send_flag = 0 and t.work_flag = 1 and t.enabled =1】
2025-05-28 19:00:00.029 [pool-14-thread-10] WARN  com.simbest.boot.cmcc.a4.task.A4TaskCreateXmlFile.execute Line:76 - >>>>>>>>>>>>>>>>>>>>>>>>>>>>>>>>>>>>>>>>>>>>>>>>>>>>>A4TaskCreateXmlFile:执行定时任务但不是Prd环境【2025-05-28T19:00:00.028】
2025-05-28 19:05:00.010 [pool-14-thread-2] WARN  com.simbest.boot.base.repository.CustomDynamicWhere.queryForList Line:50 - 自定义查询SQL输出为：【select * from US_TODO_MODEL t  where t.send_flag = 0 and t.work_flag = 1 and t.enabled =1】
2025-05-28 19:05:00.010 [pool-14-thread-5] WARN  com.simbest.boot.base.repository.CustomDynamicWhere.queryForList Line:50 - 自定义查询SQL输出为：【select * from US_TODO_MODEL t  where t.send_flag = 0 and t.work_flag = 0 and t.enabled =1】
2025-05-28 19:10:00.005 [pool-14-thread-2] WARN  com.simbest.boot.base.repository.CustomDynamicWhere.queryForList Line:50 - 自定义查询SQL输出为：【select * from US_TODO_MODEL t  where t.send_flag = 0 and t.work_flag = 0 and t.enabled =1】
2025-05-28 19:10:00.006 [pool-14-thread-1] WARN  com.simbest.boot.base.repository.CustomDynamicWhere.queryForList Line:50 - 自定义查询SQL输出为：【select * from US_TODO_MODEL t  where t.send_flag = 0 and t.work_flag = 1 and t.enabled =1】
2025-05-28 19:15:00.019 [pool-14-thread-9] WARN  com.simbest.boot.base.repository.CustomDynamicWhere.queryForList Line:50 - 自定义查询SQL输出为：【select * from US_TODO_MODEL t  where t.send_flag = 0 and t.work_flag = 1 and t.enabled =1】
2025-05-28 19:15:00.019 [pool-14-thread-7] WARN  com.simbest.boot.base.repository.CustomDynamicWhere.queryForList Line:50 - 自定义查询SQL输出为：【select * from US_TODO_MODEL t  where t.send_flag = 0 and t.work_flag = 0 and t.enabled =1】
2025-05-28 19:20:00.020 [pool-14-thread-7] WARN  com.simbest.boot.base.repository.CustomDynamicWhere.queryForList Line:50 - 自定义查询SQL输出为：【select * from US_TODO_MODEL t  where t.send_flag = 0 and t.work_flag = 1 and t.enabled =1】
2025-05-28 19:20:00.021 [pool-14-thread-10] WARN  com.simbest.boot.base.repository.CustomDynamicWhere.queryForList Line:50 - 自定义查询SQL输出为：【select * from US_TODO_MODEL t  where t.send_flag = 0 and t.work_flag = 0 and t.enabled =1】
2025-05-28 19:25:00.012 [pool-14-thread-2] WARN  com.simbest.boot.base.repository.CustomDynamicWhere.queryForList Line:50 - 自定义查询SQL输出为：【select * from US_TODO_MODEL t  where t.send_flag = 0 and t.work_flag = 0 and t.enabled =1】
2025-05-28 19:25:00.012 [pool-14-thread-5] WARN  com.simbest.boot.base.repository.CustomDynamicWhere.queryForList Line:50 - 自定义查询SQL输出为：【select * from US_TODO_MODEL t  where t.send_flag = 0 and t.work_flag = 1 and t.enabled =1】
2025-05-28 19:30:00.011 [pool-14-thread-6] WARN  com.simbest.boot.base.repository.CustomDynamicWhere.queryForList Line:50 - 自定义查询SQL输出为：【select * from US_TODO_MODEL t  where t.send_flag = 0 and t.work_flag = 1 and t.enabled =1】
2025-05-28 19:30:00.014 [pool-14-thread-1] WARN  com.simbest.boot.base.repository.CustomDynamicWhere.queryForList Line:50 - 自定义查询SQL输出为：【select * from US_TODO_MODEL t  where t.send_flag = 0 and t.work_flag = 0 and t.enabled =1】
2025-05-28 19:35:00.010 [pool-14-thread-1] WARN  com.simbest.boot.base.repository.CustomDynamicWhere.queryForList Line:50 - 自定义查询SQL输出为：【select * from US_TODO_MODEL t  where t.send_flag = 0 and t.work_flag = 0 and t.enabled =1】
2025-05-28 19:35:00.016 [pool-14-thread-7] WARN  com.simbest.boot.base.repository.CustomDynamicWhere.queryForList Line:50 - 自定义查询SQL输出为：【select * from US_TODO_MODEL t  where t.send_flag = 0 and t.work_flag = 1 and t.enabled =1】
2025-05-28 19:40:00.010 [pool-14-thread-8] WARN  com.simbest.boot.base.repository.CustomDynamicWhere.queryForList Line:50 - 自定义查询SQL输出为：【select * from US_TODO_MODEL t  where t.send_flag = 0 and t.work_flag = 1 and t.enabled =1】
2025-05-28 19:40:00.009 [pool-14-thread-10] WARN  com.simbest.boot.base.repository.CustomDynamicWhere.queryForList Line:50 - 自定义查询SQL输出为：【select * from US_TODO_MODEL t  where t.send_flag = 0 and t.work_flag = 0 and t.enabled =1】
2025-05-28 19:45:00.011 [pool-14-thread-4] WARN  com.simbest.boot.base.repository.CustomDynamicWhere.queryForList Line:50 - 自定义查询SQL输出为：【select * from US_TODO_MODEL t  where t.send_flag = 0 and t.work_flag = 0 and t.enabled =1】
2025-05-28 19:45:00.012 [pool-14-thread-9] WARN  com.simbest.boot.base.repository.CustomDynamicWhere.queryForList Line:50 - 自定义查询SQL输出为：【select * from US_TODO_MODEL t  where t.send_flag = 0 and t.work_flag = 1 and t.enabled =1】
2025-05-28 19:50:00.017 [pool-14-thread-4] WARN  com.simbest.boot.base.repository.CustomDynamicWhere.queryForList Line:50 - 自定义查询SQL输出为：【select * from US_TODO_MODEL t  where t.send_flag = 0 and t.work_flag = 0 and t.enabled =1】
2025-05-28 19:50:00.019 [pool-14-thread-10] WARN  com.simbest.boot.base.repository.CustomDynamicWhere.queryForList Line:50 - 自定义查询SQL输出为：【select * from US_TODO_MODEL t  where t.send_flag = 0 and t.work_flag = 1 and t.enabled =1】
2025-05-28 19:55:00.004 [pool-14-thread-4] WARN  com.simbest.boot.base.repository.CustomDynamicWhere.queryForList Line:50 - 自定义查询SQL输出为：【select * from US_TODO_MODEL t  where t.send_flag = 0 and t.work_flag = 0 and t.enabled =1】
2025-05-28 19:55:00.004 [pool-14-thread-5] WARN  com.simbest.boot.base.repository.CustomDynamicWhere.queryForList Line:50 - 自定义查询SQL输出为：【select * from US_TODO_MODEL t  where t.send_flag = 0 and t.work_flag = 1 and t.enabled =1】
2025-05-28 20:00:44.893 [pool-14-thread-1] WARN  com.simbest.boot.base.repository.CustomDynamicWhere.queryForList Line:50 - 自定义查询SQL输出为：【select * from US_TODO_MODEL t  where t.send_flag = 0 and t.work_flag = 1 and t.enabled =1】
2025-05-28 20:00:44.894 [pool-14-thread-4] WARN  com.simbest.boot.base.repository.CustomDynamicWhere.queryForList Line:50 - 自定义查询SQL输出为：【select * from US_TODO_MODEL t  where t.send_flag = 0 and t.work_flag = 0 and t.enabled =1】
2025-05-28 20:00:44.898 [pool-14-thread-2] WARN  com.simbest.boot.cmcc.a4.task.A4TaskCreateXmlFile.execute Line:76 - >>>>>>>>>>>>>>>>>>>>>>>>>>>>>>>>>>>>>>>>>>>>>>>>>>>>>A4TaskCreateXmlFile:执行定时任务但不是Prd环境【2025-05-28T20:00:44.898】
2025-05-28 20:05:00.010 [pool-14-thread-5] WARN  com.simbest.boot.base.repository.CustomDynamicWhere.queryForList Line:50 - 自定义查询SQL输出为：【select * from US_TODO_MODEL t  where t.send_flag = 0 and t.work_flag = 0 and t.enabled =1】
2025-05-28 20:05:00.012 [pool-14-thread-9] WARN  com.simbest.boot.base.repository.CustomDynamicWhere.queryForList Line:50 - 自定义查询SQL输出为：【select * from US_TODO_MODEL t  where t.send_flag = 0 and t.work_flag = 1 and t.enabled =1】
2025-05-28 20:10:00.006 [pool-14-thread-7] WARN  com.simbest.boot.base.repository.CustomDynamicWhere.queryForList Line:50 - 自定义查询SQL输出为：【select * from US_TODO_MODEL t  where t.send_flag = 0 and t.work_flag = 1 and t.enabled =1】
2025-05-28 20:10:00.007 [pool-14-thread-2] WARN  com.simbest.boot.base.repository.CustomDynamicWhere.queryForList Line:50 - 自定义查询SQL输出为：【select * from US_TODO_MODEL t  where t.send_flag = 0 and t.work_flag = 0 and t.enabled =1】
2025-05-28 20:15:00.012 [pool-14-thread-2] WARN  com.simbest.boot.base.repository.CustomDynamicWhere.queryForList Line:50 - 自定义查询SQL输出为：【select * from US_TODO_MODEL t  where t.send_flag = 0 and t.work_flag = 0 and t.enabled =1】
2025-05-28 20:15:00.012 [pool-14-thread-3] WARN  com.simbest.boot.base.repository.CustomDynamicWhere.queryForList Line:50 - 自定义查询SQL输出为：【select * from US_TODO_MODEL t  where t.send_flag = 0 and t.work_flag = 1 and t.enabled =1】
2025-05-28 20:20:00.005 [pool-14-thread-5] WARN  com.simbest.boot.base.repository.CustomDynamicWhere.queryForList Line:50 - 自定义查询SQL输出为：【select * from US_TODO_MODEL t  where t.send_flag = 0 and t.work_flag = 0 and t.enabled =1】
2025-05-28 20:20:00.005 [pool-14-thread-10] WARN  com.simbest.boot.base.repository.CustomDynamicWhere.queryForList Line:50 - 自定义查询SQL输出为：【select * from US_TODO_MODEL t  where t.send_flag = 0 and t.work_flag = 1 and t.enabled =1】
2025-05-28 20:41:26.604 [AMQP Connection 10.92.82.172:5672] WARN  com.rabbitmq.client.impl.ForgivingExceptionHandler.log Line:115 - An unexpected connection driver error occured (Exception message: Connection reset)
2025-05-28 20:41:28.422 [AMQP Connection 10.92.82.172:5672] WARN  com.rabbitmq.client.impl.ForgivingExceptionHandler.log Line:115 - An unexpected connection driver error occured (Exception message: Connection reset)
2025-05-28 20:41:28.529 [AMQP Connection 10.92.82.172:5672] WARN  com.rabbitmq.client.impl.ForgivingExceptionHandler.log Line:115 - An unexpected connection driver error occured (Exception message: Connection reset)
2025-05-28 20:41:28.958 [AMQP Connection 10.92.82.172:5672] WARN  com.rabbitmq.client.impl.ForgivingExceptionHandler.log Line:115 - An unexpected connection driver error occured (Exception message: Connection reset)
2025-05-28 20:50:00.009 [pool-14-thread-2] WARN  com.simbest.boot.base.repository.CustomDynamicWhere.queryForList Line:50 - 自定义查询SQL输出为：【select * from US_TODO_MODEL t  where t.send_flag = 0 and t.work_flag = 0 and t.enabled =1】
2025-05-28 20:55:00.009 [pool-14-thread-2] WARN  com.simbest.boot.base.repository.CustomDynamicWhere.queryForList Line:50 - 自定义查询SQL输出为：【select * from US_TODO_MODEL t  where t.send_flag = 0 and t.work_flag = 0 and t.enabled =1】
2025-05-28 20:55:09.283 [pool-14-thread-5] WARN  com.simbest.boot.base.repository.CustomDynamicWhere.queryForList Line:50 - 自定义查询SQL输出为：【select * from US_TODO_MODEL t  where t.send_flag = 0 and t.work_flag = 1 and t.enabled =1】
2025-05-28 21:00:00.017 [pool-14-thread-9] WARN  com.simbest.boot.base.repository.CustomDynamicWhere.queryForList Line:50 - 自定义查询SQL输出为：【select * from US_TODO_MODEL t  where t.send_flag = 0 and t.work_flag = 1 and t.enabled =1】
2025-05-28 21:00:03.074 [pool-14-thread-10] WARN  com.simbest.boot.cmcc.a4.task.A4TaskCreateXmlFile.execute Line:76 - >>>>>>>>>>>>>>>>>>>>>>>>>>>>>>>>>>>>>>>>>>>>>>>>>>>>>A4TaskCreateXmlFile:执行定时任务但不是Prd环境【2025-05-28T21:00:03.074】
2025-05-28 21:21:58.087 [AMQP Connection 10.92.82.172:5672] WARN  com.rabbitmq.client.impl.ForgivingExceptionHandler.log Line:115 - An unexpected connection driver error occured (Exception message: Connection reset)
2025-05-28 21:22:00.039 [AMQP Connection 10.92.82.172:5672] WARN  com.rabbitmq.client.impl.ForgivingExceptionHandler.log Line:115 - An unexpected connection driver error occured (Exception message: Connection reset)
2025-05-28 21:22:00.271 [AMQP Connection 10.92.82.172:5672] WARN  com.rabbitmq.client.impl.ForgivingExceptionHandler.log Line:115 - An unexpected connection driver error occured (Exception message: Connection reset)
2025-05-28 21:22:00.271 [AMQP Connection 10.92.82.172:5672] WARN  com.rabbitmq.client.impl.ForgivingExceptionHandler.log Line:115 - An unexpected connection driver error occured (Exception message: Connection reset)
2025-05-28 21:25:03.041 [pool-14-thread-7] WARN  com.simbest.boot.base.repository.CustomDynamicWhere.queryForList Line:50 - 自定义查询SQL输出为：【select * from US_TODO_MODEL t  where t.send_flag = 0 and t.work_flag = 0 and t.enabled =1】
2025-05-28 22:02:29.205 [AMQP Connection 10.92.82.172:5672] WARN  com.rabbitmq.client.impl.ForgivingExceptionHandler.log Line:115 - An unexpected connection driver error occured (Exception message: Connection reset)
2025-05-28 22:02:30.218 [AMQP Connection 10.92.82.172:5672] WARN  com.rabbitmq.client.impl.ForgivingExceptionHandler.log Line:115 - An unexpected connection driver error occured (Exception message: Connection reset)
2025-05-28 22:02:30.403 [AMQP Connection 10.92.82.172:5672] WARN  com.rabbitmq.client.impl.ForgivingExceptionHandler.log Line:115 - An unexpected connection driver error occured (Exception message: Connection reset)
2025-05-28 22:02:30.404 [AMQP Connection 10.92.82.172:5672] WARN  com.rabbitmq.client.impl.ForgivingExceptionHandler.log Line:115 - An unexpected connection driver error occured (Exception message: Connection reset)
2025-05-28 22:15:00.014 [pool-14-thread-10] WARN  com.simbest.boot.base.repository.CustomDynamicWhere.queryForList Line:50 - 自定义查询SQL输出为：【select * from US_TODO_MODEL t  where t.send_flag = 0 and t.work_flag = 0 and t.enabled =1】
2025-05-28 22:20:00.006 [pool-14-thread-3] WARN  com.simbest.boot.base.repository.CustomDynamicWhere.queryForList Line:50 - 自定义查询SQL输出为：【select * from US_TODO_MODEL t  where t.send_flag = 0 and t.work_flag = 0 and t.enabled =1】
2025-05-28 22:43:31.172 [AMQP Connection 10.92.82.172:5672] WARN  com.rabbitmq.client.impl.ForgivingExceptionHandler.log Line:115 - An unexpected connection driver error occured (Exception message: Connection reset)
2025-05-28 22:43:32.092 [AMQP Connection 10.92.82.172:5672] WARN  com.rabbitmq.client.impl.ForgivingExceptionHandler.log Line:115 - An unexpected connection driver error occured (Exception message: Connection reset)
2025-05-28 22:43:32.345 [AMQP Connection 10.92.82.172:5672] WARN  com.rabbitmq.client.impl.ForgivingExceptionHandler.log Line:115 - An unexpected connection driver error occured (Exception message: Connection reset)
2025-05-28 22:43:32.407 [AMQP Connection 10.92.82.172:5672] WARN  com.rabbitmq.client.impl.ForgivingExceptionHandler.log Line:115 - An unexpected connection driver error occured (Exception message: Connection reset)
2025-05-28 23:24:03.124 [AMQP Connection 10.92.82.172:5672] WARN  com.rabbitmq.client.impl.ForgivingExceptionHandler.log Line:115 - An unexpected connection driver error occured (Exception message: Connection reset)
2025-05-28 23:24:04.038 [AMQP Connection 10.92.82.172:5672] WARN  com.rabbitmq.client.impl.ForgivingExceptionHandler.log Line:115 - An unexpected connection driver error occured (Exception message: Connection reset)
2025-05-28 23:24:04.350 [AMQP Connection 10.92.82.172:5672] WARN  com.rabbitmq.client.impl.ForgivingExceptionHandler.log Line:115 - An unexpected connection driver error occured (Exception message: Connection reset)
2025-05-28 23:24:04.366 [AMQP Connection 10.92.82.172:5672] WARN  com.rabbitmq.client.impl.ForgivingExceptionHandler.log Line:115 - An unexpected connection driver error occured (Exception message: Connection reset)
