package com.simbest.boot.hnjjwz.examOnline.repository;/**
 * Created by KZH on 2019/6/17 16:10.
 */

import com.simbest.boot.base.repository.LogicRepository;
import com.simbest.boot.hnjjwz.examOnline.model.ExamAttribute;
import org.springframework.data.jpa.repository.Query;
import org.springframework.data.repository.query.Param;

/**
 * <AUTHOR>
 * @create 2019-06-17 16:10
 * @desc
 **/
public interface ExamAttributeRepository extends LogicRepository<ExamAttribute,String> {


    @Query(value = "select t.*, t.rowid from US_EXAM_ATTRIBUTE t where t.question_bank_code=:questionBankCode and t.enabled=1",
            nativeQuery = true)
    ExamAttribute customfindOne(@Param( "questionBankCode" )String questionBankCode);
}
