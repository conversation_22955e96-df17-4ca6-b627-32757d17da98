2025-05-26 08:18:19.965 [AMQP Connection ************:5672] WARN  com.rabbitmq.client.impl.ForgivingExceptionHandler.log Line:115 - An unexpected connection driver error occured (Exception message: Connection reset)
2025-05-26 08:18:20.652 [AMQP Connection ************:5672] WARN  com.rabbitmq.client.impl.ForgivingExceptionHandler.log Line:115 - An unexpected connection driver error occured (Exception message: Connection reset)
2025-05-26 08:18:21.739 [AMQP Connection ************:5672] WARN  com.rabbitmq.client.impl.ForgivingExceptionHandler.log Line:115 - An unexpected connection driver error occured (Exception message: Connection reset)
2025-05-26 08:18:22.121 [AMQP Connection ************:5672] WARN  com.rabbitmq.client.impl.ForgivingExceptionHandler.log Line:115 - An unexpected connection driver error occured (Exception message: Connection reset)
2025-05-26 08:18:22.882 [lettuce-nioEventLoop-4-3] WARN  io.lettuce.core.protocol.ConnectionWatchdog.log Line:153 - Cannot reconnect to [************:7003]: connection timed out: /************:7003
2025-05-26 08:18:22.882 [lettuce-nioEventLoop-4-2] WARN  io.lettuce.core.protocol.ConnectionWatchdog.log Line:153 - Cannot reconnect to [************:7005]: connection timed out: /************:7005
2025-05-26 08:18:22.882 [lettuce-nioEventLoop-4-12] WARN  io.lettuce.core.protocol.ConnectionWatchdog.log Line:153 - Cannot reconnect to [************:7005]: connection timed out: /************:7005
2025-05-26 08:18:22.882 [lettuce-nioEventLoop-4-1] WARN  io.lettuce.core.protocol.ConnectionWatchdog.log Line:153 - Cannot reconnect to [************:7004]: connection timed out: /************:7004
2025-05-26 08:18:23.292 [lettuce-nioEventLoop-4-4] WARN  io.lettuce.core.protocol.ConnectionWatchdog.log Line:153 - Cannot reconnect to [************:7003]: connection timed out: /************:7003
2025-05-26 08:18:23.292 [lettuce-nioEventLoop-4-5] WARN  io.lettuce.core.protocol.ConnectionWatchdog.log Line:153 - Cannot reconnect to [************:7004]: connection timed out: /************:7004
2025-05-26 08:18:23.292 [lettuce-nioEventLoop-4-6] WARN  io.lettuce.core.protocol.ConnectionWatchdog.log Line:153 - Cannot reconnect to [************:7003]: connection timed out: /************:7003
2025-05-26 08:18:23.292 [lettuce-nioEventLoop-4-7] WARN  io.lettuce.core.protocol.ConnectionWatchdog.log Line:153 - Cannot reconnect to [************:7004]: connection timed out: /************:7004
2025-05-26 08:18:23.307 [lettuce-nioEventLoop-4-8] WARN  io.lettuce.core.protocol.ConnectionWatchdog.log Line:153 - Cannot reconnect to [************:7003]: connection timed out: /************:7003
2025-05-26 08:20:09.225 [pool-14-thread-10] WARN  com.simbest.boot.base.repository.CustomDynamicWhere.queryForList Line:50 - 自定义查询SQL输出为：【select * from US_TODO_MODEL t  where t.send_flag = 0 and t.work_flag = 0 and t.enabled =1】
2025-05-26 08:30:49.289 [http-nio-8092-exec-11] WARN  com.simbest.boot.security.auth.service.impl.AuthUserCacheServiceImpl.loadCacheUser Line:118 - 无法通过关键字【auth:user:chenhong】读取用户主键ID
2025-05-26 08:30:49.535 [http-nio-8092-exec-11] WARN  com.simbest.boot.security.auth.service.impl.AuthUserCacheServiceImpl.saveOrUpdateCacheUser Line:64 - 即将在缓存中将用户权限置为空----SET AuthPermissions EMPTY----【12704】
2025-05-26 09:00:52.596 [Thread-11] WARN  com.alibaba.nacos.common.notify.NotifyCenter.shutdown Line:136 - [NotifyCenter] Start destroying Publisher
2025-05-26 09:00:52.596 [Thread-5] WARN  com.alibaba.nacos.common.http.HttpClientBeanHolder.shutdown Line:108 - [HttpClientBeanHolder] Start destroying common HttpClient
2025-05-26 09:00:52.596 [Thread-11] WARN  com.alibaba.nacos.common.notify.NotifyCenter.shutdown Line:153 - [NotifyCenter] Destruction of the end
2025-05-26 09:00:52.597 [Thread-5] WARN  com.alibaba.nacos.common.http.HttpClientBeanHolder.shutdown Line:114 - [HttpClientBeanHolder] Destruction of the end
2025-05-26 09:00:52.744 [SpringContextShutdownHook] WARN  com.simbest.boot.component.GracefulShutdown.onApplicationEvent Line:161 - executor非ThreadPoolExecutor，具体类型为【org.apache.tomcat.util.threads.ThreadPoolExecutor】
2025-05-26 09:01:14.375 [restartedMain] WARN  com.alibaba.cloud.nacos.client.NacosPropertySourceBuilder.loadNacosData Line:87 - Ignore the empty nacos configuration and get it based on dataId[hnjjwz-custom-uat.properties] & group[DEFAULT_GROUP]
2025-05-26 09:01:14.386 [restartedMain] WARN  com.alibaba.cloud.nacos.client.NacosPropertySourceBuilder.loadNacosData Line:87 - Ignore the empty nacos configuration and get it based on dataId[hnjjwz] & group[DEFAULT_GROUP]
2025-05-26 09:01:14.397 [restartedMain] WARN  com.alibaba.cloud.nacos.client.NacosPropertySourceBuilder.loadNacosData Line:87 - Ignore the empty nacos configuration and get it based on dataId[hnjjwz.properties] & group[DEFAULT_GROUP]
2025-05-26 09:01:16.942 [restartedMain] WARN  org.springframework.boot.actuate.endpoint.EndpointId.logWarning Line:155 - Endpoint ID 'service-registry' contains invalid characters, please migrate to a valid format.
2025-05-26 09:01:17.949 [restartedMain] WARN  com.alibaba.druid.pool.DruidDataSource.initCheck Line:1314 - removeAbandoned is true, not use in production.
2025-05-26 09:02:17.004 [restartedMain] WARN  com.simbest.boot.SimbestApplication.onApplicationEvent Line:60 - 加载环境信息为: 【uat】
2025-05-26 09:02:17.005 [restartedMain] WARN  com.simbest.boot.SimbestApplication.onApplicationEvent Line:61 - Application started successfully, lets go and have fun......
2025-05-26 09:02:17.006 [restartedMain] WARN  com.simbest.boot.SimbestApplication.onApplicationEvent Line:71 - 
---------------------------------------------------------
	应用已成功启动，运行地址如下：:
	Local:		http://localhost:8092/hnjjwz
	External:	http://***********:8092/hnjjwz
Aplication started successfully, lets go and have fun......
---------------------------------------------------------

2025-05-26 09:03:15.880 [Thread-5] WARN  com.alibaba.nacos.common.http.HttpClientBeanHolder.shutdown Line:108 - [HttpClientBeanHolder] Start destroying common HttpClient
2025-05-26 09:03:15.880 [Thread-11] WARN  com.alibaba.nacos.common.notify.NotifyCenter.shutdown Line:136 - [NotifyCenter] Start destroying Publisher
2025-05-26 09:03:15.881 [Thread-11] WARN  com.alibaba.nacos.common.notify.NotifyCenter.shutdown Line:153 - [NotifyCenter] Destruction of the end
2025-05-26 09:03:15.882 [Thread-5] WARN  com.alibaba.nacos.common.http.HttpClientBeanHolder.shutdown Line:114 - [HttpClientBeanHolder] Destruction of the end
2025-05-26 09:03:15.963 [SpringContextShutdownHook] WARN  com.simbest.boot.component.GracefulShutdown.onApplicationEvent Line:161 - executor非ThreadPoolExecutor，具体类型为【org.apache.tomcat.util.threads.ThreadPoolExecutor】
2025-05-26 09:03:36.694 [restartedMain] WARN  com.alibaba.cloud.nacos.client.NacosPropertySourceBuilder.loadNacosData Line:87 - Ignore the empty nacos configuration and get it based on dataId[hnjjwz-custom-uat.properties] & group[DEFAULT_GROUP]
2025-05-26 09:03:36.704 [restartedMain] WARN  com.alibaba.cloud.nacos.client.NacosPropertySourceBuilder.loadNacosData Line:87 - Ignore the empty nacos configuration and get it based on dataId[hnjjwz] & group[DEFAULT_GROUP]
2025-05-26 09:03:36.712 [restartedMain] WARN  com.alibaba.cloud.nacos.client.NacosPropertySourceBuilder.loadNacosData Line:87 - Ignore the empty nacos configuration and get it based on dataId[hnjjwz.properties] & group[DEFAULT_GROUP]
2025-05-26 09:03:39.175 [restartedMain] WARN  org.springframework.boot.actuate.endpoint.EndpointId.logWarning Line:155 - Endpoint ID 'service-registry' contains invalid characters, please migrate to a valid format.
2025-05-26 09:03:40.152 [restartedMain] WARN  com.alibaba.druid.pool.DruidDataSource.initCheck Line:1314 - removeAbandoned is true, not use in production.
2025-05-26 09:04:25.330 [restartedMain] WARN  com.simbest.boot.SimbestApplication.onApplicationEvent Line:60 - 加载环境信息为: 【uat】
2025-05-26 09:04:25.331 [restartedMain] WARN  com.simbest.boot.SimbestApplication.onApplicationEvent Line:61 - Application started successfully, lets go and have fun......
2025-05-26 09:04:25.332 [restartedMain] WARN  com.simbest.boot.SimbestApplication.onApplicationEvent Line:71 - 
---------------------------------------------------------
	应用已成功启动，运行地址如下：:
	Local:		http://localhost:8092/hnjjwz
	External:	http://***********:8092/hnjjwz
Aplication started successfully, lets go and have fun......
---------------------------------------------------------

2025-05-26 09:15:29.099 [AMQP Connection ************:5672] WARN  com.rabbitmq.client.impl.ForgivingExceptionHandler.log Line:115 - An unexpected connection driver error occured (Exception message: Connection reset)
2025-05-26 09:15:29.099 [AMQP Connection ************:5672] WARN  com.rabbitmq.client.impl.ForgivingExceptionHandler.log Line:115 - An unexpected connection driver error occured (Exception message: Connection reset)
2025-05-26 09:15:29.099 [AMQP Connection ************:5672] WARN  com.rabbitmq.client.impl.ForgivingExceptionHandler.log Line:115 - An unexpected connection driver error occured (Exception message: Connection reset)
2025-05-26 09:15:29.099 [AMQP Connection ************:5672] WARN  com.rabbitmq.client.impl.ForgivingExceptionHandler.log Line:115 - An unexpected connection driver error occured (Exception message: Connection reset)
2025-05-26 09:36:20.699 [Thread-31] WARN  com.simbest.boot.component.GracefulShutdown.onApplicationEvent Line:161 - executor非ThreadPoolExecutor，具体类型为【org.apache.tomcat.util.threads.ThreadPoolExecutor】
2025-05-26 09:36:32.808 [Thread-31] WARN  org.springframework.data.redis.connection.lettuce.LettucePoolingConnectionProvider.destroy Line:229 - LettucePoolingConnectionProvider contains unreleased connections
2025-05-26 09:36:32.932 [Thread-31] WARN  org.springframework.data.redis.connection.lettuce.LettuceConnectionFactory.destroy Line:323 - Cannot properly close cluster command executor
org.springframework.data.redis.connection.PoolException: Returned connection io.lettuce.core.cluster.StatefulRedisClusterConnectionImpl@2b212264 was either previously returned or does not belong to this connection provider
	at org.springframework.data.redis.connection.lettuce.LettucePoolingConnectionProvider.release(LettucePoolingConnectionProvider.java:170)
	at org.springframework.data.redis.connection.lettuce.LettuceClusterConnection$LettuceClusterNodeResourceProvider.destroy(LettuceClusterConnection.java:703)
	at org.springframework.data.redis.connection.ClusterCommandExecutor.destroy(ClusterCommandExecutor.java:361)
	at org.springframework.data.redis.connection.lettuce.LettuceConnectionFactory.destroy(LettuceConnectionFactory.java:321)
	at org.springframework.beans.factory.support.DisposableBeanAdapter.destroy(DisposableBeanAdapter.java:258)
	at org.springframework.beans.factory.support.DefaultSingletonBeanRegistry.destroyBean(DefaultSingletonBeanRegistry.java:571)
	at org.springframework.beans.factory.support.DefaultSingletonBeanRegistry.destroySingleton(DefaultSingletonBeanRegistry.java:543)
	at org.springframework.beans.factory.support.DefaultListableBeanFactory.destroySingleton(DefaultListableBeanFactory.java:1072)
	at org.springframework.beans.factory.support.DefaultSingletonBeanRegistry.destroySingletons(DefaultSingletonBeanRegistry.java:504)
	at org.springframework.beans.factory.support.DefaultListableBeanFactory.destroySingletons(DefaultListableBeanFactory.java:1065)
	at org.springframework.context.support.AbstractApplicationContext.destroyBeans(AbstractApplicationContext.java:1060)
	at org.springframework.context.support.AbstractApplicationContext.doClose(AbstractApplicationContext.java:1029)
	at org.springframework.context.support.AbstractApplicationContext.close(AbstractApplicationContext.java:978)
	at org.springframework.boot.builder.ParentContextCloserApplicationListener$ContextCloserListener.onApplicationEvent(ParentContextCloserApplicationListener.java:94)
	at org.springframework.boot.builder.ParentContextCloserApplicationListener$ContextCloserListener.onApplicationEvent(ParentContextCloserApplicationListener.java:82)
	at org.springframework.context.event.SimpleApplicationEventMulticaster.doInvokeListener(SimpleApplicationEventMulticaster.java:172)
	at org.springframework.context.event.SimpleApplicationEventMulticaster.invokeListener(SimpleApplicationEventMulticaster.java:165)
	at org.springframework.context.event.SimpleApplicationEventMulticaster.multicastEvent(SimpleApplicationEventMulticaster.java:139)
	at org.springframework.context.support.AbstractApplicationContext.publishEvent(AbstractApplicationContext.java:403)
	at org.springframework.context.support.AbstractApplicationContext.publishEvent(AbstractApplicationContext.java:360)
	at org.springframework.context.support.AbstractApplicationContext.doClose(AbstractApplicationContext.java:1012)
	at org.springframework.context.support.AbstractApplicationContext.close(AbstractApplicationContext.java:978)
	at org.springframework.boot.devtools.restart.Restarter.stop(Restarter.java:309)
	at org.springframework.boot.devtools.restart.Restarter.lambda$restart$1(Restarter.java:251)
	at org.springframework.boot.devtools.restart.Restarter$LeakSafeThread.run(Restarter.java:629)
2025-05-26 09:36:33.798 [restartedMain] WARN  com.alibaba.cloud.nacos.client.NacosPropertySourceBuilder.loadNacosData Line:87 - Ignore the empty nacos configuration and get it based on dataId[hnjjwz-custom-uat.properties] & group[DEFAULT_GROUP]
2025-05-26 09:36:33.806 [restartedMain] WARN  com.alibaba.cloud.nacos.client.NacosPropertySourceBuilder.loadNacosData Line:87 - Ignore the empty nacos configuration and get it based on dataId[hnjjwz] & group[DEFAULT_GROUP]
2025-05-26 09:36:33.816 [restartedMain] WARN  com.alibaba.cloud.nacos.client.NacosPropertySourceBuilder.loadNacosData Line:87 - Ignore the empty nacos configuration and get it based on dataId[hnjjwz.properties] & group[DEFAULT_GROUP]
2025-05-26 09:36:36.094 [restartedMain] WARN  com.alibaba.druid.pool.DruidDataSource.initCheck Line:1314 - removeAbandoned is true, not use in production.
2025-05-26 09:36:46.720 [Thread-11] WARN  com.alibaba.nacos.common.notify.NotifyCenter.shutdown Line:136 - [NotifyCenter] Start destroying Publisher
2025-05-26 09:36:46.720 [Thread-5] WARN  com.alibaba.nacos.common.http.HttpClientBeanHolder.shutdown Line:108 - [HttpClientBeanHolder] Start destroying common HttpClient
2025-05-26 09:36:46.723 [Thread-11] WARN  com.alibaba.nacos.common.notify.NotifyCenter.shutdown Line:153 - [NotifyCenter] Destruction of the end
2025-05-26 09:36:46.723 [Thread-5] WARN  com.alibaba.nacos.common.http.HttpClientBeanHolder.shutdown Line:114 - [HttpClientBeanHolder] Destruction of the end
2025-05-26 09:37:05.347 [restartedMain] WARN  com.alibaba.cloud.nacos.client.NacosPropertySourceBuilder.loadNacosData Line:87 - Ignore the empty nacos configuration and get it based on dataId[hnjjwz-custom-uat.properties] & group[DEFAULT_GROUP]
2025-05-26 09:37:05.356 [restartedMain] WARN  com.alibaba.cloud.nacos.client.NacosPropertySourceBuilder.loadNacosData Line:87 - Ignore the empty nacos configuration and get it based on dataId[hnjjwz] & group[DEFAULT_GROUP]
2025-05-26 09:37:05.364 [restartedMain] WARN  com.alibaba.cloud.nacos.client.NacosPropertySourceBuilder.loadNacosData Line:87 - Ignore the empty nacos configuration and get it based on dataId[hnjjwz.properties] & group[DEFAULT_GROUP]
2025-05-26 09:37:07.855 [restartedMain] WARN  org.springframework.boot.actuate.endpoint.EndpointId.logWarning Line:155 - Endpoint ID 'service-registry' contains invalid characters, please migrate to a valid format.
2025-05-26 09:37:08.870 [restartedMain] WARN  com.alibaba.druid.pool.DruidDataSource.initCheck Line:1314 - removeAbandoned is true, not use in production.
2025-05-26 09:37:53.913 [restartedMain] WARN  com.simbest.boot.SimbestApplication.onApplicationEvent Line:60 - 加载环境信息为: 【uat】
2025-05-26 09:37:53.913 [restartedMain] WARN  com.simbest.boot.SimbestApplication.onApplicationEvent Line:61 - Application started successfully, lets go and have fun......
2025-05-26 09:37:53.914 [restartedMain] WARN  com.simbest.boot.SimbestApplication.onApplicationEvent Line:71 - 
---------------------------------------------------------
	应用已成功启动，运行地址如下：:
	Local:		http://localhost:8092/hnjjwz
	External:	http://***********:8092/hnjjwz
Aplication started successfully, lets go and have fun......
---------------------------------------------------------

2025-05-26 09:40:08.474 [Thread-5] WARN  com.alibaba.nacos.common.http.HttpClientBeanHolder.shutdown Line:108 - [HttpClientBeanHolder] Start destroying common HttpClient
2025-05-26 09:40:08.474 [Thread-11] WARN  com.alibaba.nacos.common.notify.NotifyCenter.shutdown Line:136 - [NotifyCenter] Start destroying Publisher
2025-05-26 09:40:08.475 [Thread-11] WARN  com.alibaba.nacos.common.notify.NotifyCenter.shutdown Line:153 - [NotifyCenter] Destruction of the end
2025-05-26 09:40:08.476 [Thread-5] WARN  com.alibaba.nacos.common.http.HttpClientBeanHolder.shutdown Line:114 - [HttpClientBeanHolder] Destruction of the end
2025-05-26 09:40:08.608 [SpringContextShutdownHook] WARN  com.simbest.boot.component.GracefulShutdown.onApplicationEvent Line:161 - executor非ThreadPoolExecutor，具体类型为【org.apache.tomcat.util.threads.ThreadPoolExecutor】
2025-05-26 09:40:23.519 [SpringContextShutdownHook] WARN  org.springframework.data.redis.connection.lettuce.LettucePoolingConnectionProvider.destroy Line:229 - LettucePoolingConnectionProvider contains unreleased connections
2025-05-26 09:40:23.647 [SpringContextShutdownHook] WARN  org.springframework.data.redis.connection.lettuce.LettuceConnectionFactory.destroy Line:323 - Cannot properly close cluster command executor
org.springframework.data.redis.connection.PoolException: Returned connection io.lettuce.core.cluster.StatefulRedisClusterConnectionImpl@3c0c9499 was either previously returned or does not belong to this connection provider
	at org.springframework.data.redis.connection.lettuce.LettucePoolingConnectionProvider.release(LettucePoolingConnectionProvider.java:170)
	at org.springframework.data.redis.connection.lettuce.LettuceClusterConnection$LettuceClusterNodeResourceProvider.destroy(LettuceClusterConnection.java:703)
	at org.springframework.data.redis.connection.ClusterCommandExecutor.destroy(ClusterCommandExecutor.java:361)
	at org.springframework.data.redis.connection.lettuce.LettuceConnectionFactory.destroy(LettuceConnectionFactory.java:321)
	at org.springframework.beans.factory.support.DisposableBeanAdapter.destroy(DisposableBeanAdapter.java:258)
	at org.springframework.beans.factory.support.DefaultSingletonBeanRegistry.destroyBean(DefaultSingletonBeanRegistry.java:571)
	at org.springframework.beans.factory.support.DefaultSingletonBeanRegistry.destroySingleton(DefaultSingletonBeanRegistry.java:543)
	at org.springframework.beans.factory.support.DefaultListableBeanFactory.destroySingleton(DefaultListableBeanFactory.java:1072)
	at org.springframework.beans.factory.support.DefaultSingletonBeanRegistry.destroySingletons(DefaultSingletonBeanRegistry.java:504)
	at org.springframework.beans.factory.support.DefaultListableBeanFactory.destroySingletons(DefaultListableBeanFactory.java:1065)
	at org.springframework.context.support.AbstractApplicationContext.destroyBeans(AbstractApplicationContext.java:1060)
	at org.springframework.context.support.AbstractApplicationContext.doClose(AbstractApplicationContext.java:1029)
	at org.springframework.context.support.AbstractApplicationContext$1.run(AbstractApplicationContext.java:948)
2025-05-26 09:44:42.027 [restartedMain] WARN  com.alibaba.cloud.nacos.client.NacosPropertySourceBuilder.loadNacosData Line:87 - Ignore the empty nacos configuration and get it based on dataId[hnjjwz-custom-obuat.properties] & group[DEFAULT_GROUP]
2025-05-26 09:44:42.036 [restartedMain] WARN  com.alibaba.cloud.nacos.client.NacosPropertySourceBuilder.loadNacosData Line:87 - Ignore the empty nacos configuration and get it based on dataId[hnjjwz] & group[DEFAULT_GROUP]
2025-05-26 09:44:42.043 [restartedMain] WARN  com.alibaba.cloud.nacos.client.NacosPropertySourceBuilder.loadNacosData Line:87 - Ignore the empty nacos configuration and get it based on dataId[hnjjwz.properties] & group[DEFAULT_GROUP]
2025-05-26 09:44:44.249 [restartedMain] WARN  org.springframework.boot.actuate.endpoint.EndpointId.logWarning Line:155 - Endpoint ID 'service-registry' contains invalid characters, please migrate to a valid format.
2025-05-26 09:44:45.125 [restartedMain] WARN  com.alibaba.druid.pool.DruidDataSource.initCheck Line:1314 - removeAbandoned is true, not use in production.
2025-05-26 09:45:56.618 [restartedMain] WARN  com.simbest.boot.SimbestApplication.onApplicationEvent Line:60 - 加载环境信息为: 【obuat】
2025-05-26 09:45:56.618 [restartedMain] WARN  com.simbest.boot.SimbestApplication.onApplicationEvent Line:61 - Application started successfully, lets go and have fun......
2025-05-26 09:45:56.619 [restartedMain] WARN  com.simbest.boot.SimbestApplication.onApplicationEvent Line:71 - 
---------------------------------------------------------
	应用已成功启动，运行地址如下：:
	Local:		http://localhost:8092/hnjjwz
	External:	http://***********:8092/hnjjwz
Aplication started successfully, lets go and have fun......
---------------------------------------------------------

2025-05-26 09:46:06.987 [http-nio-8092-exec-12] WARN  com.simbest.boot.security.auth.provider.UumsHttpValidationAuthenticationProvider.authenticate Line:153 - UUMS登录认证器UUMS认证用户 【chenghong】 通过密码【111.com】访问 【hnjjwz】 失败, 错误信息: 【账号密码校验错误】
2025-05-26 09:46:06.988 [http-nio-8092-exec-12] WARN  com.simbest.boot.security.auth.provider.UumsHttpValidationAuthenticationProvider.authenticate Line:173 - UUMS登录认证器UUMS认证 【chenghong】 失败， 捕获【账号密码校验错误】异常!
2025-05-26 09:46:07.301 [http-nio-8092-exec-12] WARN  com.simbest.boot.security.auth.service.impl.AuthUserCacheServiceImpl.loadCacheUser Line:118 - 无法通过关键字【auth:user:chenghong】读取用户主键ID
2025-05-26 09:46:07.683 [http-nio-8092-exec-12] WARN  com.simbest.boot.security.auth.handle.FailedLoginHandler.onAuthenticationFailure Line:42 - 用户【chenghong】尝试登录失败
2025-05-26 09:54:49.729 [http-nio-8092-exec-13] WARN  com.simbest.boot.util.AppFileUtil.getFileName Line:218 - 文件URL路径【壁纸.jpg】无法通过URL获取路径，直接通过工具类提取文件全称为【壁纸.jpg】
2025-05-26 09:54:49.729 [http-nio-8092-exec-13] WARN  com.simbest.boot.util.AppFileUtil.getFileSuffix Line:274 - 文件URL路径【壁纸.jpg】无法通过URL获取路径，直接通过工具类提取文件后缀为【jpg】
2025-05-26 09:54:49.732 [http-nio-8092-exec-13] WARN  com.simbest.boot.util.AppFileUtil.getFileBaseName Line:251 - 文件URL路径【壁纸.jpg】无法通过URL获取路径，直接通过工具类提取文件名称为【壁纸】
2025-05-26 09:54:49.732 [http-nio-8092-exec-13] WARN  com.simbest.boot.util.AppFileUtil.getFileSuffix Line:274 - 文件URL路径【壁纸.jpg】无法通过URL获取路径，直接通过工具类提取文件后缀为【jpg】
2025-05-26 09:54:49.744 [http-nio-8092-exec-13] WARN  com.simbest.boot.util.AppFileUtil.getFileSuffix Line:274 - 文件URL路径【壁纸.jpg】无法通过URL获取路径，直接通过工具类提取文件后缀为【jpg】
2025-05-26 09:56:12.380 [http-nio-8092-exec-20] WARN  com.simbest.boot.util.AppFileUtil.getFileName Line:218 - 文件URL路径【壁纸.jpg】无法通过URL获取路径，直接通过工具类提取文件全称为【壁纸.jpg】
2025-05-26 09:56:12.380 [http-nio-8092-exec-20] WARN  com.simbest.boot.util.AppFileUtil.getFileSuffix Line:274 - 文件URL路径【壁纸.jpg】无法通过URL获取路径，直接通过工具类提取文件后缀为【jpg】
2025-05-26 09:56:12.385 [http-nio-8092-exec-20] WARN  com.simbest.boot.util.AppFileUtil.getFileBaseName Line:251 - 文件URL路径【壁纸.jpg】无法通过URL获取路径，直接通过工具类提取文件名称为【壁纸】
2025-05-26 09:56:12.385 [http-nio-8092-exec-20] WARN  com.simbest.boot.util.AppFileUtil.getFileSuffix Line:274 - 文件URL路径【壁纸.jpg】无法通过URL获取路径，直接通过工具类提取文件后缀为【jpg】
2025-05-26 09:56:12.398 [http-nio-8092-exec-20] WARN  com.simbest.boot.util.AppFileUtil.getFileSuffix Line:274 - 文件URL路径【壁纸.jpg】无法通过URL获取路径，直接通过工具类提取文件后缀为【jpg】
2025-05-26 09:56:29.702 [http-nio-8092-exec-8] WARN  com.simbest.boot.bps.process.operate.WfProcessManager.startProcessAndSetRelativeData Line:87 - 流程启动调用BPS流程启动接口用时：4117ms
2025-05-26 09:56:30.050 [http-nio-8092-exec-8] WARN  com.simbest.boot.bps.process.operate.WfProcessManager.startProcessAndSetRelativeData Line:107 - 【BPS Driver】流程启动第一个工作项的状态：【10】
2025-05-26 09:56:30.051 [http-nio-8092-exec-8] WARN  com.simbest.boot.bps.process.operate.WfProcessManager.startProcessAndSetRelativeData Line:118 - 流程启动调用BPS流程工作项查询接口用时：336ms
2025-05-26 09:56:30.240 [http-nio-8092-exec-8] WARN  com.simbest.boot.bps.process.operate.WorkItemManager.finishWorkItemWithRelativeData Line:169 - 流程下一步传递的参数为：{"code":"E1444392847574282240","currentUserCode":"chenhong","inputUserId":"kongzhijie","appCode":"hnjjwz","title":"qweqwe","receiptId":"U869156195585781760","outcome":"hnjjwz.depart_manager"}
2025-05-26 09:56:31.028 [http-nio-8092-exec-8] WARN  com.simbest.boot.bps.unifiedtodo.service.impl.CloseUnifiedTodoServiceImpl.closeTodo Line:68 - 统一待办核销状态：Y
2025-05-26 09:56:31.031 [http-nio-8092-exec-8] WARN  com.simbest.boot.bps.common.HttpNagentDataUtil.queryAgent Line:76 - 获取接口配置对象1：SimpleConfig(id=AC486600246105735169, address=http://************:8088/ntydl, interfaceStyle=ntydl, isOpen=true, isInner=true, provider=北京晟壁)
2025-05-26 09:56:31.031 [http-nio-8092-exec-8] WARN  com.simbest.boot.bps.common.HttpNagentDataUtil.queryAgent Line:80 - 获取接口配置对象2：SimpleConfig(id=AC486600246105735169, address=http://************:8088/ntydl, interfaceStyle=ntydl, isOpen=true, isInner=true, provider=北京晟壁)
2025-05-26 09:56:31.593 [http-nio-8092-exec-8] WARN  com.simbest.boot.bps.process.operate.WorkItemManager.finishWorkItemWithRelativeData Line:264 - 下一个审批人是：kongzhijie，代理人为：[]
2025-05-26 09:56:31.594 [http-nio-8092-exec-8] WARN  com.simbest.boot.bps.process.operate.WorkItemManager.finishWorkItemWithRelativeData Line:275 - 流程提交下一步处理代理人用时：1332ms
2025-05-26 09:56:37.637 [http-nio-8092-exec-8] WARN  com.simbest.boot.bps.process.operate.WorkItemManager.finishWorkItemWithRelativeData Line:293 - 流程提交下一步调用BPS流程引擎提交下一步接口用时：6043ms
2025-05-26 10:12:52.869 [http-nio-8092-exec-13] WARN  com.simbest.boot.bps.process.operate.WorkItemManager.finishWorkItemWithRelativeData Line:169 - 流程下一步传递的参数为：{"code":"E1444392847574282240","currentUserCode":"kongzhijie","inputUserId":"chenhong","appCode":"hnjjwz","title":"qweqwe","receiptId":"U869156195585781760","outcome":"return"}
2025-05-26 10:12:53.161 [http-nio-8092-exec-13] WARN  com.simbest.boot.bps.unifiedtodo.service.impl.CloseUnifiedTodoServiceImpl.closeTodo Line:68 - 统一待办核销状态：Y
2025-05-26 10:12:53.161 [http-nio-8092-exec-13] WARN  com.simbest.boot.bps.common.HttpNagentDataUtil.queryAgent Line:76 - 获取接口配置对象1：SimpleConfig(id=AC486600246105735169, address=http://************:8088/ntydl, interfaceStyle=ntydl, isOpen=true, isInner=true, provider=北京晟壁)
2025-05-26 10:12:53.162 [http-nio-8092-exec-13] WARN  com.simbest.boot.bps.common.HttpNagentDataUtil.queryAgent Line:80 - 获取接口配置对象2：SimpleConfig(id=AC486600246105735169, address=http://************:8088/ntydl, interfaceStyle=ntydl, isOpen=true, isInner=true, provider=北京晟壁)
2025-05-26 10:12:53.668 [http-nio-8092-exec-13] WARN  com.simbest.boot.bps.process.operate.WorkItemManager.finishWorkItemWithRelativeData Line:264 - 下一个审批人是：chenhong，代理人为：[]
2025-05-26 10:12:53.668 [http-nio-8092-exec-13] WARN  com.simbest.boot.bps.process.operate.WorkItemManager.finishWorkItemWithRelativeData Line:275 - 流程提交下一步处理代理人用时：799ms
2025-05-26 10:12:58.875 [http-nio-8092-exec-13] WARN  com.simbest.boot.bps.process.operate.WorkItemManager.finishWorkItemWithRelativeData Line:293 - 流程提交下一步调用BPS流程引擎提交下一步接口用时：5206ms
2025-05-26 10:46:20.103 [http-nio-8092-exec-2] WARN  com.simbest.boot.bps.process.operate.WfProcessManager.startProcessAndSetRelativeData Line:87 - 流程启动调用BPS流程启动接口用时：3633ms
2025-05-26 10:46:20.376 [http-nio-8092-exec-2] WARN  com.simbest.boot.bps.process.operate.WfProcessManager.startProcessAndSetRelativeData Line:107 - 【BPS Driver】流程启动第一个工作项的状态：【10】
2025-05-26 10:46:20.378 [http-nio-8092-exec-2] WARN  com.simbest.boot.bps.process.operate.WfProcessManager.startProcessAndSetRelativeData Line:118 - 流程启动调用BPS流程工作项查询接口用时：272ms
2025-05-26 10:46:20.791 [http-nio-8092-exec-2] WARN  com.simbest.boot.bps.process.operate.WorkItemManager.finishWorkItemWithRelativeData Line:169 - 流程下一步传递的参数为：{"code":"A1444405392683020288","currentUserCode":"chenhong","inputUserId":"kongzhijie","appCode":"hnjjwz","title":"we4534545","receiptId":"U869168740631605248","outcome":"hnjjwz.depart_manager"}
2025-05-26 10:46:21.060 [http-nio-8092-exec-2] WARN  com.simbest.boot.bps.unifiedtodo.service.impl.CloseUnifiedTodoServiceImpl.closeTodo Line:68 - 统一待办核销状态：Y
2025-05-26 10:46:21.061 [http-nio-8092-exec-2] WARN  com.simbest.boot.bps.common.HttpNagentDataUtil.queryAgent Line:76 - 获取接口配置对象1：SimpleConfig(id=AC486600246105735169, address=http://************:8088/ntydl, interfaceStyle=ntydl, isOpen=true, isInner=true, provider=北京晟壁)
2025-05-26 10:46:21.062 [http-nio-8092-exec-2] WARN  com.simbest.boot.bps.common.HttpNagentDataUtil.queryAgent Line:80 - 获取接口配置对象2：SimpleConfig(id=AC486600246105735169, address=http://************:8088/ntydl, interfaceStyle=ntydl, isOpen=true, isInner=true, provider=北京晟壁)
2025-05-26 10:46:21.558 [http-nio-8092-exec-2] WARN  com.simbest.boot.bps.process.operate.WorkItemManager.finishWorkItemWithRelativeData Line:264 - 下一个审批人是：kongzhijie，代理人为：[]
2025-05-26 10:46:21.559 [http-nio-8092-exec-2] WARN  com.simbest.boot.bps.process.operate.WorkItemManager.finishWorkItemWithRelativeData Line:275 - 流程提交下一步处理代理人用时：767ms
2025-05-26 10:46:26.911 [http-nio-8092-exec-2] WARN  com.simbest.boot.bps.process.operate.WorkItemManager.finishWorkItemWithRelativeData Line:293 - 流程提交下一步调用BPS流程引擎提交下一步接口用时：5351ms
2025-05-26 10:55:09.983 [http-nio-8092-exec-5] WARN  com.simbest.boot.bps.process.operate.WfProcessManager.startProcessAndSetRelativeData Line:87 - 流程启动调用BPS流程启动接口用时：4042ms
2025-05-26 10:55:10.460 [http-nio-8092-exec-5] WARN  com.simbest.boot.bps.process.operate.WfProcessManager.startProcessAndSetRelativeData Line:107 - 【BPS Driver】流程启动第一个工作项的状态：【10】
2025-05-26 10:55:10.461 [http-nio-8092-exec-5] WARN  com.simbest.boot.bps.process.operate.WfProcessManager.startProcessAndSetRelativeData Line:118 - 流程启动调用BPS流程工作项查询接口用时：476ms
2025-05-26 10:55:10.586 [http-nio-8092-exec-5] WARN  com.simbest.boot.bps.process.operate.WorkItemManager.finishWorkItemWithRelativeData Line:169 - 流程下一步传递的参数为：{"code":"C1444407613395021824","currentUserCode":"chenhong","inputUserId":"kongzhijie","appCode":"hnjjwz","title":"34234","receiptId":"U869170961318440960","outcome":"hnjjwz.depart_manager"}
2025-05-26 10:55:11.043 [http-nio-8092-exec-5] WARN  com.simbest.boot.bps.unifiedtodo.service.impl.CloseUnifiedTodoServiceImpl.closeTodo Line:68 - 统一待办核销状态：Y
2025-05-26 10:55:11.046 [http-nio-8092-exec-5] WARN  com.simbest.boot.bps.common.HttpNagentDataUtil.queryAgent Line:76 - 获取接口配置对象1：SimpleConfig(id=AC486600246105735169, address=http://************:8088/ntydl, interfaceStyle=ntydl, isOpen=true, isInner=true, provider=北京晟壁)
2025-05-26 10:55:11.047 [http-nio-8092-exec-5] WARN  com.simbest.boot.bps.common.HttpNagentDataUtil.queryAgent Line:80 - 获取接口配置对象2：SimpleConfig(id=AC486600246105735169, address=http://************:8088/ntydl, interfaceStyle=ntydl, isOpen=true, isInner=true, provider=北京晟壁)
2025-05-26 10:55:11.500 [http-nio-8092-exec-5] WARN  com.simbest.boot.bps.process.operate.WorkItemManager.finishWorkItemWithRelativeData Line:264 - 下一个审批人是：kongzhijie，代理人为：[]
2025-05-26 10:55:11.501 [http-nio-8092-exec-5] WARN  com.simbest.boot.bps.process.operate.WorkItemManager.finishWorkItemWithRelativeData Line:275 - 流程提交下一步处理代理人用时：914ms
2025-05-26 10:55:17.261 [http-nio-8092-exec-5] WARN  com.simbest.boot.bps.process.operate.WorkItemManager.finishWorkItemWithRelativeData Line:293 - 流程提交下一步调用BPS流程引擎提交下一步接口用时：5759ms
2025-05-26 10:55:38.062 [http-nio-8092-exec-20] WARN  com.simbest.exclude.web.GlobalErrorController.logErrorInformation Line:160 - 请注意所访问URL【/hnjjwz/iconfont/iconfont.css】发生404错误，资源地址不存在！
2025-05-26 10:59:48.466 [http-nio-8092-exec-12] WARN  com.simbest.boot.util.AppFileUtil.getFileName Line:218 - 文件URL路径【壁纸.jpg】无法通过URL获取路径，直接通过工具类提取文件全称为【壁纸.jpg】
2025-05-26 10:59:48.468 [http-nio-8092-exec-12] WARN  com.simbest.boot.util.AppFileUtil.getFileSuffix Line:274 - 文件URL路径【壁纸.jpg】无法通过URL获取路径，直接通过工具类提取文件后缀为【jpg】
2025-05-26 10:59:48.470 [http-nio-8092-exec-12] WARN  com.simbest.boot.util.AppFileUtil.getFileBaseName Line:251 - 文件URL路径【壁纸.jpg】无法通过URL获取路径，直接通过工具类提取文件名称为【壁纸】
2025-05-26 10:59:48.470 [http-nio-8092-exec-12] WARN  com.simbest.boot.util.AppFileUtil.getFileSuffix Line:274 - 文件URL路径【壁纸.jpg】无法通过URL获取路径，直接通过工具类提取文件后缀为【jpg】
2025-05-26 10:59:48.478 [http-nio-8092-exec-12] WARN  com.simbest.boot.util.AppFileUtil.getFileSuffix Line:274 - 文件URL路径【壁纸.jpg】无法通过URL获取路径，直接通过工具类提取文件后缀为【jpg】
2025-05-26 11:00:01.414 [http-nio-8092-exec-14] WARN  com.simbest.boot.bps.process.operate.WfProcessManager.startProcessAndSetRelativeData Line:87 - 流程启动调用BPS流程启动接口用时：3686ms
2025-05-26 11:00:01.857 [http-nio-8092-exec-14] WARN  com.simbest.boot.bps.process.operate.WfProcessManager.startProcessAndSetRelativeData Line:107 - 【BPS Driver】流程启动第一个工作项的状态：【10】
2025-05-26 11:00:01.858 [http-nio-8092-exec-14] WARN  com.simbest.boot.bps.process.operate.WfProcessManager.startProcessAndSetRelativeData Line:118 - 流程启动调用BPS流程工作项查询接口用时：443ms
2025-05-26 11:00:02.215 [http-nio-8092-exec-14] WARN  com.simbest.boot.bps.process.operate.WorkItemManager.finishWorkItemWithRelativeData Line:169 - 流程下一步传递的参数为：{"code":"E1444408834772152320","currentUserCode":"chenhong","inputUserId":"kongzhijie","appCode":"hnjjwz","title":"23423","receiptId":"U869172182712348672","outcome":"hnjjwz.depart_manager"}
2025-05-26 11:00:02.574 [http-nio-8092-exec-14] WARN  com.simbest.boot.bps.unifiedtodo.service.impl.CloseUnifiedTodoServiceImpl.closeTodo Line:68 - 统一待办核销状态：Y
2025-05-26 11:00:02.575 [http-nio-8092-exec-14] WARN  com.simbest.boot.bps.common.HttpNagentDataUtil.queryAgent Line:76 - 获取接口配置对象1：SimpleConfig(id=AC486600246105735169, address=http://************:8088/ntydl, interfaceStyle=ntydl, isOpen=true, isInner=true, provider=北京晟壁)
2025-05-26 11:00:02.575 [http-nio-8092-exec-14] WARN  com.simbest.boot.bps.common.HttpNagentDataUtil.queryAgent Line:80 - 获取接口配置对象2：SimpleConfig(id=AC486600246105735169, address=http://************:8088/ntydl, interfaceStyle=ntydl, isOpen=true, isInner=true, provider=北京晟壁)
2025-05-26 11:00:03.037 [http-nio-8092-exec-14] WARN  com.simbest.boot.bps.process.operate.WorkItemManager.finishWorkItemWithRelativeData Line:264 - 下一个审批人是：kongzhijie，代理人为：[]
2025-05-26 11:00:03.038 [http-nio-8092-exec-14] WARN  com.simbest.boot.bps.process.operate.WorkItemManager.finishWorkItemWithRelativeData Line:275 - 流程提交下一步处理代理人用时：823ms
2025-05-26 11:00:08.562 [http-nio-8092-exec-14] WARN  com.simbest.boot.bps.process.operate.WorkItemManager.finishWorkItemWithRelativeData Line:293 - 流程提交下一步调用BPS流程引擎提交下一步接口用时：5524ms
2025-05-26 11:01:42.333 [http-nio-8092-exec-13] WARN  com.simbest.boot.bps.process.operate.WfProcessManager.startProcessAndSetRelativeData Line:87 - 流程启动调用BPS流程启动接口用时：4004ms
2025-05-26 11:01:42.490 [http-nio-8092-exec-13] WARN  com.simbest.boot.bps.process.operate.WfProcessManager.startProcessAndSetRelativeData Line:107 - 【BPS Driver】流程启动第一个工作项的状态：【10】
2025-05-26 11:01:42.492 [http-nio-8092-exec-13] WARN  com.simbest.boot.bps.process.operate.WfProcessManager.startProcessAndSetRelativeData Line:118 - 流程启动调用BPS流程工作项查询接口用时：156ms
2025-05-26 11:01:42.536 [http-nio-8092-exec-13] WARN  com.simbest.boot.bps.process.operate.WorkItemManager.finishWorkItemWithRelativeData Line:169 - 流程下一步传递的参数为：{"code":"A1444409259118276608","currentUserCode":"chenhong","inputUserId":"kongzhijie","appCode":"hnjjwz","title":"234234","receiptId":"U869172607138164736","outcome":"hnjjwz.depart_manager"}
2025-05-26 11:01:42.855 [http-nio-8092-exec-13] WARN  com.simbest.boot.bps.unifiedtodo.service.impl.CloseUnifiedTodoServiceImpl.closeTodo Line:68 - 统一待办核销状态：Y
2025-05-26 11:01:42.855 [http-nio-8092-exec-13] WARN  com.simbest.boot.bps.common.HttpNagentDataUtil.queryAgent Line:76 - 获取接口配置对象1：SimpleConfig(id=AC486600246105735169, address=http://************:8088/ntydl, interfaceStyle=ntydl, isOpen=true, isInner=true, provider=北京晟壁)
2025-05-26 11:01:42.856 [http-nio-8092-exec-13] WARN  com.simbest.boot.bps.common.HttpNagentDataUtil.queryAgent Line:80 - 获取接口配置对象2：SimpleConfig(id=AC486600246105735169, address=http://************:8088/ntydl, interfaceStyle=ntydl, isOpen=true, isInner=true, provider=北京晟壁)
2025-05-26 11:01:43.298 [http-nio-8092-exec-13] WARN  com.simbest.boot.bps.process.operate.WorkItemManager.finishWorkItemWithRelativeData Line:264 - 下一个审批人是：kongzhijie，代理人为：[]
2025-05-26 11:01:43.300 [http-nio-8092-exec-13] WARN  com.simbest.boot.bps.process.operate.WorkItemManager.finishWorkItemWithRelativeData Line:275 - 流程提交下一步处理代理人用时：764ms
2025-05-26 11:01:48.500 [http-nio-8092-exec-13] WARN  com.simbest.boot.bps.process.operate.WorkItemManager.finishWorkItemWithRelativeData Line:293 - 流程提交下一步调用BPS流程引擎提交下一步接口用时：5200ms
2025-05-26 11:05:08.873 [http-nio-8092-exec-17] WARN  com.simbest.boot.util.AppFileUtil.getFileName Line:218 - 文件URL路径【壁纸.jpg】无法通过URL获取路径，直接通过工具类提取文件全称为【壁纸.jpg】
2025-05-26 11:05:08.873 [http-nio-8092-exec-17] WARN  com.simbest.boot.util.AppFileUtil.getFileSuffix Line:274 - 文件URL路径【壁纸.jpg】无法通过URL获取路径，直接通过工具类提取文件后缀为【jpg】
2025-05-26 11:05:08.876 [http-nio-8092-exec-17] WARN  com.simbest.boot.util.AppFileUtil.getFileBaseName Line:251 - 文件URL路径【壁纸.jpg】无法通过URL获取路径，直接通过工具类提取文件名称为【壁纸】
2025-05-26 11:05:08.876 [http-nio-8092-exec-17] WARN  com.simbest.boot.util.AppFileUtil.getFileSuffix Line:274 - 文件URL路径【壁纸.jpg】无法通过URL获取路径，直接通过工具类提取文件后缀为【jpg】
2025-05-26 11:05:08.894 [http-nio-8092-exec-17] WARN  com.simbest.boot.util.AppFileUtil.getFileSuffix Line:274 - 文件URL路径【壁纸.jpg】无法通过URL获取路径，直接通过工具类提取文件后缀为【jpg】
2025-05-26 11:05:18.928 [http-nio-8092-exec-6] WARN  com.simbest.boot.bps.process.operate.WfProcessManager.startProcessAndSetRelativeData Line:87 - 流程启动调用BPS流程启动接口用时：4299ms
2025-05-26 11:05:19.340 [http-nio-8092-exec-6] WARN  com.simbest.boot.bps.process.operate.WfProcessManager.startProcessAndSetRelativeData Line:107 - 【BPS Driver】流程启动第一个工作项的状态：【10】
2025-05-26 11:05:19.342 [http-nio-8092-exec-6] WARN  com.simbest.boot.bps.process.operate.WfProcessManager.startProcessAndSetRelativeData Line:118 - 流程启动调用BPS流程工作项查询接口用时：411ms
2025-05-26 11:05:19.537 [http-nio-8092-exec-6] WARN  com.simbest.boot.bps.process.operate.WorkItemManager.finishWorkItemWithRelativeData Line:169 - 流程下一步传递的参数为：{"code":"E1444410165863886848","currentUserCode":"chenhong","inputUserId":"kongzhijie","appCode":"hnjjwz","title":"234234","receiptId":"U869173513812471808","outcome":"hnjjwz.depart_manager"}
2025-05-26 11:05:20.025 [http-nio-8092-exec-6] WARN  com.simbest.boot.bps.unifiedtodo.service.impl.CloseUnifiedTodoServiceImpl.closeTodo Line:68 - 统一待办核销状态：Y
2025-05-26 11:05:20.026 [http-nio-8092-exec-6] WARN  com.simbest.boot.bps.common.HttpNagentDataUtil.queryAgent Line:76 - 获取接口配置对象1：SimpleConfig(id=AC486600246105735169, address=http://************:8088/ntydl, interfaceStyle=ntydl, isOpen=true, isInner=true, provider=北京晟壁)
2025-05-26 11:05:20.027 [http-nio-8092-exec-6] WARN  com.simbest.boot.bps.common.HttpNagentDataUtil.queryAgent Line:80 - 获取接口配置对象2：SimpleConfig(id=AC486600246105735169, address=http://************:8088/ntydl, interfaceStyle=ntydl, isOpen=true, isInner=true, provider=北京晟壁)
2025-05-26 11:05:20.501 [http-nio-8092-exec-6] WARN  com.simbest.boot.bps.process.operate.WorkItemManager.finishWorkItemWithRelativeData Line:264 - 下一个审批人是：kongzhijie，代理人为：[]
2025-05-26 11:05:20.502 [http-nio-8092-exec-6] WARN  com.simbest.boot.bps.process.operate.WorkItemManager.finishWorkItemWithRelativeData Line:275 - 流程提交下一步处理代理人用时：964ms
2025-05-26 11:05:25.382 [http-nio-8092-exec-6] WARN  com.simbest.boot.bps.process.operate.WorkItemManager.finishWorkItemWithRelativeData Line:293 - 流程提交下一步调用BPS流程引擎提交下一步接口用时：4879ms
2025-05-26 11:08:29.798 [http-nio-8092-exec-1] WARN  com.simbest.boot.util.AppFileUtil.getFileName Line:218 - 文件URL路径【壁纸.jpg】无法通过URL获取路径，直接通过工具类提取文件全称为【壁纸.jpg】
2025-05-26 11:08:29.798 [http-nio-8092-exec-1] WARN  com.simbest.boot.util.AppFileUtil.getFileSuffix Line:274 - 文件URL路径【壁纸.jpg】无法通过URL获取路径，直接通过工具类提取文件后缀为【jpg】
2025-05-26 11:08:29.801 [http-nio-8092-exec-1] WARN  com.simbest.boot.util.AppFileUtil.getFileBaseName Line:251 - 文件URL路径【壁纸.jpg】无法通过URL获取路径，直接通过工具类提取文件名称为【壁纸】
2025-05-26 11:08:29.801 [http-nio-8092-exec-1] WARN  com.simbest.boot.util.AppFileUtil.getFileSuffix Line:274 - 文件URL路径【壁纸.jpg】无法通过URL获取路径，直接通过工具类提取文件后缀为【jpg】
2025-05-26 11:08:29.811 [http-nio-8092-exec-1] WARN  com.simbest.boot.util.AppFileUtil.getFileSuffix Line:274 - 文件URL路径【壁纸.jpg】无法通过URL获取路径，直接通过工具类提取文件后缀为【jpg】
2025-05-26 11:10:56.103 [http-nio-8092-exec-18] WARN  com.simbest.exclude.web.GlobalErrorController.logErrorInformation Line:160 - 请注意所访问URL【/hnjjwz/action/slideShow/deleteProcess】发生404错误，资源地址不存在！
2025-05-26 11:11:57.922 [http-nio-8092-exec-14] WARN  com.simbest.boot.security.auth.entryPoint.RestAccessDeniedEntryPoint.commence Line:28 - 无权限访问【/hnjjwz/getCurrentUser】，即将返回HttpStatus.UNAUTHORIZED，状态码【401】
2025-05-26 11:11:57.923 [http-nio-8092-exec-14] WARN  com.simbest.boot.base.web.response.JsonResponse.unauthorized Line:272 - 无权限访问，即将返回【{"errcode":401,"timestamp":"2025-05-26 11:11:57","status":401,"error":"Full authentication is required to access this resource","message":"权限不足，禁止访问!","path":null,"data":null}】
2025-05-26 11:16:10.441 [Thread-11] WARN  com.alibaba.nacos.common.notify.NotifyCenter.shutdown Line:136 - [NotifyCenter] Start destroying Publisher
2025-05-26 11:16:10.441 [Thread-5] WARN  com.alibaba.nacos.common.http.HttpClientBeanHolder.shutdown Line:108 - [HttpClientBeanHolder] Start destroying common HttpClient
2025-05-26 11:16:10.441 [Thread-11] WARN  com.alibaba.nacos.common.notify.NotifyCenter.shutdown Line:153 - [NotifyCenter] Destruction of the end
2025-05-26 11:16:10.442 [Thread-5] WARN  com.alibaba.nacos.common.http.HttpClientBeanHolder.shutdown Line:114 - [HttpClientBeanHolder] Destruction of the end
2025-05-26 11:16:10.539 [SpringContextShutdownHook] WARN  com.simbest.boot.component.GracefulShutdown.onApplicationEvent Line:161 - executor非ThreadPoolExecutor，具体类型为【org.apache.tomcat.util.threads.ThreadPoolExecutor】
2025-05-26 11:16:19.718 [restartedMain] WARN  com.alibaba.cloud.nacos.client.NacosPropertySourceBuilder.loadNacosData Line:87 - Ignore the empty nacos configuration and get it based on dataId[hnjjwz-custom-obuat.properties] & group[DEFAULT_GROUP]
2025-05-26 11:16:19.728 [restartedMain] WARN  com.alibaba.cloud.nacos.client.NacosPropertySourceBuilder.loadNacosData Line:87 - Ignore the empty nacos configuration and get it based on dataId[hnjjwz] & group[DEFAULT_GROUP]
2025-05-26 11:16:19.738 [restartedMain] WARN  com.alibaba.cloud.nacos.client.NacosPropertySourceBuilder.loadNacosData Line:87 - Ignore the empty nacos configuration and get it based on dataId[hnjjwz.properties] & group[DEFAULT_GROUP]
2025-05-26 11:16:21.934 [restartedMain] WARN  org.springframework.boot.actuate.endpoint.EndpointId.logWarning Line:155 - Endpoint ID 'service-registry' contains invalid characters, please migrate to a valid format.
2025-05-26 11:16:22.792 [restartedMain] WARN  com.alibaba.druid.pool.DruidDataSource.initCheck Line:1314 - removeAbandoned is true, not use in production.
2025-05-26 11:17:21.214 [restartedMain] WARN  com.simbest.boot.SimbestApplication.onApplicationEvent Line:60 - 加载环境信息为: 【obuat】
2025-05-26 11:17:21.215 [restartedMain] WARN  com.simbest.boot.SimbestApplication.onApplicationEvent Line:61 - Application started successfully, lets go and have fun......
2025-05-26 11:17:21.215 [restartedMain] WARN  com.simbest.boot.SimbestApplication.onApplicationEvent Line:71 - 
---------------------------------------------------------
	应用已成功启动，运行地址如下：:
	Local:		http://localhost:8092/hnjjwz
	External:	http://***********:8092/hnjjwz
Aplication started successfully, lets go and have fun......
---------------------------------------------------------

2025-05-26 11:17:29.451 [http-nio-8092-exec-7] WARN  com.simbest.exclude.web.GlobalErrorController.logErrorInformation Line:160 - 请注意所访问URL【/hnjjwz/iconfont/iconfont.css】发生404错误，资源地址不存在！
2025-05-26 11:45:30.830 [http-nio-8092-exec-14] WARN  com.simbest.boot.util.AppFileUtil.getFileName Line:218 - 文件URL路径【壁纸.jpg】无法通过URL获取路径，直接通过工具类提取文件全称为【壁纸.jpg】
2025-05-26 11:45:30.831 [http-nio-8092-exec-14] WARN  com.simbest.boot.util.AppFileUtil.getFileSuffix Line:274 - 文件URL路径【壁纸.jpg】无法通过URL获取路径，直接通过工具类提取文件后缀为【jpg】
2025-05-26 11:45:30.833 [http-nio-8092-exec-14] WARN  com.simbest.boot.util.AppFileUtil.getFileBaseName Line:251 - 文件URL路径【壁纸.jpg】无法通过URL获取路径，直接通过工具类提取文件名称为【壁纸】
2025-05-26 11:45:30.834 [http-nio-8092-exec-14] WARN  com.simbest.boot.util.AppFileUtil.getFileSuffix Line:274 - 文件URL路径【壁纸.jpg】无法通过URL获取路径，直接通过工具类提取文件后缀为【jpg】
2025-05-26 11:45:30.847 [http-nio-8092-exec-14] WARN  com.simbest.boot.util.AppFileUtil.getFileSuffix Line:274 - 文件URL路径【壁纸.jpg】无法通过URL获取路径，直接通过工具类提取文件后缀为【jpg】
2025-05-26 12:32:43.638 [AMQP Connection ************:5672] WARN  com.rabbitmq.client.impl.ForgivingExceptionHandler.log Line:115 - An unexpected connection driver error occured (Exception message: Connection reset)
2025-05-26 12:32:43.714 [AMQP Connection ************:5672] WARN  com.rabbitmq.client.impl.ForgivingExceptionHandler.log Line:115 - An unexpected connection driver error occured (Exception message: Connection reset)
2025-05-26 12:32:43.714 [AMQP Connection ************:5672] WARN  com.rabbitmq.client.impl.ForgivingExceptionHandler.log Line:115 - An unexpected connection driver error occured (Exception message: Connection reset)
2025-05-26 12:33:07.483 [AMQP Connection ************:5672] WARN  com.rabbitmq.client.impl.ForgivingExceptionHandler.log Line:115 - An unexpected connection driver error occured (Exception message: Connection reset)
2025-05-26 13:13:05.617 [lettuce-nioEventLoop-4-9] WARN  io.lettuce.core.cluster.topology.ClusterTopologyRefresh.lambda$getConnections$0 Line:284 - Unable to connect to [************:7003]: No route to host: no further information: /************:7003
2025-05-26 13:13:05.617 [lettuce-nioEventLoop-4-11] WARN  io.lettuce.core.cluster.topology.ClusterTopologyRefresh.lambda$getConnections$0 Line:284 - Unable to connect to [************:7005]: No route to host: no further information: /************:7005
2025-05-26 13:13:05.617 [lettuce-nioEventLoop-4-8] WARN  io.lettuce.core.cluster.topology.ClusterTopologyRefresh.lambda$getConnections$0 Line:284 - Unable to connect to [************:7002]: No route to host: no further information: /************:7002
2025-05-26 13:13:05.617 [lettuce-nioEventLoop-4-7] WARN  io.lettuce.core.cluster.topology.ClusterTopologyRefresh.lambda$getConnections$0 Line:284 - Unable to connect to [************:7001]: No route to host: no further information: /************:7001
2025-05-26 13:13:05.617 [lettuce-nioEventLoop-4-10] WARN  io.lettuce.core.cluster.topology.ClusterTopologyRefresh.lambda$getConnections$0 Line:284 - Unable to connect to [************:7004]: No route to host: no further information: /************:7004
2025-05-26 13:13:05.617 [lettuce-nioEventLoop-4-12] WARN  io.lettuce.core.cluster.topology.ClusterTopologyRefresh.lambda$getConnections$0 Line:284 - Unable to connect to [************:7006]: No route to host: no further information: /************:7006
2025-05-26 13:13:05.620 [lettuce-nioEventLoop-4-1] WARN  io.lettuce.core.cluster.topology.ClusterTopologyRefresh.lambda$getConnections$0 Line:284 - Unable to connect to [************:7006]: No route to host: no further information: /************:7006
2025-05-26 13:13:05.620 [lettuce-nioEventLoop-4-2] WARN  io.lettuce.core.cluster.topology.ClusterTopologyRefresh.lambda$getConnections$0 Line:284 - Unable to connect to [************:7004]: No route to host: no further information: /************:7004
2025-05-26 13:13:05.620 [lettuce-nioEventLoop-4-3] WARN  io.lettuce.core.cluster.topology.ClusterTopologyRefresh.lambda$getConnections$0 Line:284 - Unable to connect to [************:7005]: No route to host: no further information: /************:7005
2025-05-26 13:13:05.621 [lettuce-nioEventLoop-4-4] WARN  io.lettuce.core.cluster.topology.ClusterTopologyRefresh.lambda$getConnections$0 Line:284 - Unable to connect to [************:7002]: No route to host: no further information: /************:7002
2025-05-26 13:13:05.621 [lettuce-nioEventLoop-4-5] WARN  io.lettuce.core.cluster.topology.ClusterTopologyRefresh.lambda$getConnections$0 Line:284 - Unable to connect to [************:7003]: No route to host: no further information: /************:7003
2025-05-26 13:13:05.621 [lettuce-nioEventLoop-4-6] WARN  io.lettuce.core.cluster.topology.ClusterTopologyRefresh.lambda$getConnections$0 Line:284 - Unable to connect to [************:7001]: No route to host: no further information: /************:7001
2025-05-26 13:13:05.630 [lettuce-eventExecutorLoop-1-6] WARN  io.lettuce.core.cluster.ClusterTopologyRefreshScheduler.doRun Line:258 - Cannot refresh Redis Cluster topology
io.lettuce.core.RedisException: Cannot retrieve initial cluster partitions from initial URIs [RedisURI [host='************', port=7006], RedisURI [host='************', port=7004], RedisURI [host='************', port=7005], RedisURI [host='************', port=7002], RedisURI [host='************', port=7003], RedisURI [host='************', port=7001]]
	at io.lettuce.core.cluster.RedisClusterClient.loadPartitions(RedisClusterClient.java:848)
	at io.lettuce.core.cluster.RedisClusterClient.reloadPartitions(RedisClusterClient.java:784)
	at io.lettuce.core.cluster.ClusterTopologyRefreshScheduler$ClusterTopologyRefreshTask.doRun(ClusterTopologyRefreshScheduler.java:256)
	at io.lettuce.core.cluster.ClusterTopologyRefreshScheduler$ClusterTopologyRefreshTask.run(ClusterTopologyRefreshScheduler.java:236)
	at io.netty.util.concurrent.PromiseTask.runTask(PromiseTask.java:98)
	at io.netty.util.concurrent.PromiseTask.run(PromiseTask.java:106)
	at io.netty.util.concurrent.DefaultEventExecutor.run(DefaultEventExecutor.java:66)
	at io.netty.util.concurrent.SingleThreadEventExecutor$6.run(SingleThreadEventExecutor.java:1050)
	at io.netty.util.internal.ThreadExecutorMap$2.run(ThreadExecutorMap.java:74)
	at io.netty.util.concurrent.FastThreadLocalRunnable.run(FastThreadLocalRunnable.java:30)
	at java.lang.Thread.run(Thread.java:748)
	Suppressed: io.lettuce.core.RedisConnectionException: Unable to establish a connection to Redis Cluster at [RedisURI [host='************', port=7001], RedisURI [host='************', port=7002], RedisURI [host='************', port=7003], RedisURI [host='************', port=7004], RedisURI [host='************', port=7005], RedisURI [host='************', port=7006]]
		at io.lettuce.core.cluster.topology.AsyncConnections.get(AsyncConnections.java:89)
		at io.lettuce.core.cluster.topology.ClusterTopologyRefresh.loadViews(ClusterTopologyRefresh.java:76)
		at io.lettuce.core.cluster.RedisClusterClient.doLoadPartitions(RedisClusterClient.java:865)
		at io.lettuce.core.cluster.RedisClusterClient.loadPartitions(RedisClusterClient.java:838)
		... 10 common frames omitted
		Suppressed: io.lettuce.core.RedisConnectionException: Unable to connect to [************:7002]: No route to host: no further information: /************:7002
			at io.lettuce.core.cluster.topology.ClusterTopologyRefresh.lambda$getConnections$0(ClusterTopologyRefresh.java:290)
			at java.util.concurrent.CompletableFuture.uniWhenComplete(CompletableFuture.java:760)
			at java.util.concurrent.CompletableFuture$UniWhenComplete.tryFire(CompletableFuture.java:736)
			at java.util.concurrent.CompletableFuture.postComplete(CompletableFuture.java:474)
			at java.util.concurrent.CompletableFuture.completeExceptionally(CompletableFuture.java:1977)
			at io.lettuce.core.AbstractRedisClient.lambda$initializeChannelAsync0$4(AbstractRedisClient.java:329)
			at io.netty.util.concurrent.DefaultPromise.notifyListener0(DefaultPromise.java:577)
			at io.netty.util.concurrent.DefaultPromise.notifyListeners0(DefaultPromise.java:570)
			at io.netty.util.concurrent.DefaultPromise.notifyListenersNow(DefaultPromise.java:549)
			at io.netty.util.concurrent.DefaultPromise.notifyListeners(DefaultPromise.java:490)
			at io.netty.util.concurrent.DefaultPromise.setValue0(DefaultPromise.java:615)
			at io.netty.util.concurrent.DefaultPromise.setFailure0(DefaultPromise.java:608)
			at io.netty.util.concurrent.DefaultPromise.tryFailure(DefaultPromise.java:117)
			at io.netty.channel.nio.AbstractNioChannel$AbstractNioUnsafe.fulfillConnectPromise(AbstractNioChannel.java:321)
			at io.netty.channel.nio.AbstractNioChannel$AbstractNioUnsafe.finishConnect(AbstractNioChannel.java:337)
			at io.netty.channel.nio.NioEventLoop.processSelectedKey(NioEventLoop.java:688)
			at io.netty.channel.nio.NioEventLoop.processSelectedKeysOptimized(NioEventLoop.java:635)
			at io.netty.channel.nio.NioEventLoop.processSelectedKeys(NioEventLoop.java:552)
			at io.netty.channel.nio.NioEventLoop.run(NioEventLoop.java:514)
			... 4 common frames omitted
		Caused by: io.netty.channel.AbstractChannel$AnnotatedNoRouteToHostException: No route to host: no further information: /************:7002
		Caused by: java.net.NoRouteToHostException: No route to host: no further information
			at sun.nio.ch.SocketChannelImpl.checkConnect(Native Method)
			at sun.nio.ch.SocketChannelImpl.finishConnect(SocketChannelImpl.java:717)
			at io.netty.channel.socket.nio.NioSocketChannel.doFinishConnect(NioSocketChannel.java:330)
			at io.netty.channel.nio.AbstractNioChannel$AbstractNioUnsafe.finishConnect(AbstractNioChannel.java:334)
			at io.netty.channel.nio.NioEventLoop.processSelectedKey(NioEventLoop.java:688)
			at io.netty.channel.nio.NioEventLoop.processSelectedKeysOptimized(NioEventLoop.java:635)
			at io.netty.channel.nio.NioEventLoop.processSelectedKeys(NioEventLoop.java:552)
			at io.netty.channel.nio.NioEventLoop.run(NioEventLoop.java:514)
			at io.netty.util.concurrent.SingleThreadEventExecutor$6.run(SingleThreadEventExecutor.java:1050)
			at io.netty.util.internal.ThreadExecutorMap$2.run(ThreadExecutorMap.java:74)
			at io.netty.util.concurrent.FastThreadLocalRunnable.run(FastThreadLocalRunnable.java:30)
			at java.lang.Thread.run(Thread.java:748)
		Suppressed: io.lettuce.core.RedisConnectionException: Unable to connect to [************:7006]: No route to host: no further information: /************:7006
			at io.lettuce.core.cluster.topology.ClusterTopologyRefresh.lambda$getConnections$0(ClusterTopologyRefresh.java:290)
			at java.util.concurrent.CompletableFuture.uniWhenComplete(CompletableFuture.java:760)
			at java.util.concurrent.CompletableFuture$UniWhenComplete.tryFire(CompletableFuture.java:736)
			at java.util.concurrent.CompletableFuture.postComplete(CompletableFuture.java:474)
			at java.util.concurrent.CompletableFuture.completeExceptionally(CompletableFuture.java:1977)
			at io.lettuce.core.AbstractRedisClient.lambda$initializeChannelAsync0$4(AbstractRedisClient.java:329)
			at io.netty.util.concurrent.DefaultPromise.notifyListener0(DefaultPromise.java:577)
			at io.netty.util.concurrent.DefaultPromise.notifyListeners0(DefaultPromise.java:570)
			at io.netty.util.concurrent.DefaultPromise.notifyListenersNow(DefaultPromise.java:549)
			at io.netty.util.concurrent.DefaultPromise.notifyListeners(DefaultPromise.java:490)
			at io.netty.util.concurrent.DefaultPromise.setValue0(DefaultPromise.java:615)
			at io.netty.util.concurrent.DefaultPromise.setFailure0(DefaultPromise.java:608)
			at io.netty.util.concurrent.DefaultPromise.tryFailure(DefaultPromise.java:117)
			at io.netty.channel.nio.AbstractNioChannel$AbstractNioUnsafe.fulfillConnectPromise(AbstractNioChannel.java:321)
			at io.netty.channel.nio.AbstractNioChannel$AbstractNioUnsafe.finishConnect(AbstractNioChannel.java:337)
			at io.netty.channel.nio.NioEventLoop.processSelectedKey(NioEventLoop.java:688)
			at io.netty.channel.nio.NioEventLoop.processSelectedKeysOptimized(NioEventLoop.java:635)
			at io.netty.channel.nio.NioEventLoop.processSelectedKeys(NioEventLoop.java:552)
			at io.netty.channel.nio.NioEventLoop.run(NioEventLoop.java:514)
			... 4 common frames omitted
		Caused by: io.netty.channel.AbstractChannel$AnnotatedNoRouteToHostException: No route to host: no further information: /************:7006
		Caused by: java.net.NoRouteToHostException: No route to host: no further information
			at sun.nio.ch.SocketChannelImpl.checkConnect(Native Method)
			at sun.nio.ch.SocketChannelImpl.finishConnect(SocketChannelImpl.java:717)
			at io.netty.channel.socket.nio.NioSocketChannel.doFinishConnect(NioSocketChannel.java:330)
			at io.netty.channel.nio.AbstractNioChannel$AbstractNioUnsafe.finishConnect(AbstractNioChannel.java:334)
			at io.netty.channel.nio.NioEventLoop.processSelectedKey(NioEventLoop.java:688)
			at io.netty.channel.nio.NioEventLoop.processSelectedKeysOptimized(NioEventLoop.java:635)
			at io.netty.channel.nio.NioEventLoop.processSelectedKeys(NioEventLoop.java:552)
			at io.netty.channel.nio.NioEventLoop.run(NioEventLoop.java:514)
			at io.netty.util.concurrent.SingleThreadEventExecutor$6.run(SingleThreadEventExecutor.java:1050)
			at io.netty.util.internal.ThreadExecutorMap$2.run(ThreadExecutorMap.java:74)
			at io.netty.util.concurrent.FastThreadLocalRunnable.run(FastThreadLocalRunnable.java:30)
			at java.lang.Thread.run(Thread.java:748)
		Suppressed: io.lettuce.core.RedisConnectionException: Unable to connect to [************:7001]: No route to host: no further information: /************:7001
			at io.lettuce.core.cluster.topology.ClusterTopologyRefresh.lambda$getConnections$0(ClusterTopologyRefresh.java:290)
			at java.util.concurrent.CompletableFuture.uniWhenComplete(CompletableFuture.java:760)
			at java.util.concurrent.CompletableFuture$UniWhenComplete.tryFire(CompletableFuture.java:736)
			at java.util.concurrent.CompletableFuture.postComplete(CompletableFuture.java:474)
			at java.util.concurrent.CompletableFuture.completeExceptionally(CompletableFuture.java:1977)
			at io.lettuce.core.AbstractRedisClient.lambda$initializeChannelAsync0$4(AbstractRedisClient.java:329)
			at io.netty.util.concurrent.DefaultPromise.notifyListener0(DefaultPromise.java:577)
			at io.netty.util.concurrent.DefaultPromise.notifyListeners0(DefaultPromise.java:570)
			at io.netty.util.concurrent.DefaultPromise.notifyListenersNow(DefaultPromise.java:549)
			at io.netty.util.concurrent.DefaultPromise.notifyListeners(DefaultPromise.java:490)
			at io.netty.util.concurrent.DefaultPromise.setValue0(DefaultPromise.java:615)
			at io.netty.util.concurrent.DefaultPromise.setFailure0(DefaultPromise.java:608)
			at io.netty.util.concurrent.DefaultPromise.tryFailure(DefaultPromise.java:117)
			at io.netty.channel.nio.AbstractNioChannel$AbstractNioUnsafe.fulfillConnectPromise(AbstractNioChannel.java:321)
			at io.netty.channel.nio.AbstractNioChannel$AbstractNioUnsafe.finishConnect(AbstractNioChannel.java:337)
			at io.netty.channel.nio.NioEventLoop.processSelectedKey(NioEventLoop.java:688)
			at io.netty.channel.nio.NioEventLoop.processSelectedKeysOptimized(NioEventLoop.java:635)
			at io.netty.channel.nio.NioEventLoop.processSelectedKeys(NioEventLoop.java:552)
			at io.netty.channel.nio.NioEventLoop.run(NioEventLoop.java:514)
			... 4 common frames omitted
		Caused by: io.netty.channel.AbstractChannel$AnnotatedNoRouteToHostException: No route to host: no further information: /************:7001
		Caused by: java.net.NoRouteToHostException: No route to host: no further information
			at sun.nio.ch.SocketChannelImpl.checkConnect(Native Method)
			at sun.nio.ch.SocketChannelImpl.finishConnect(SocketChannelImpl.java:717)
			at io.netty.channel.socket.nio.NioSocketChannel.doFinishConnect(NioSocketChannel.java:330)
			at io.netty.channel.nio.AbstractNioChannel$AbstractNioUnsafe.finishConnect(AbstractNioChannel.java:334)
			at io.netty.channel.nio.NioEventLoop.processSelectedKey(NioEventLoop.java:688)
			at io.netty.channel.nio.NioEventLoop.processSelectedKeysOptimized(NioEventLoop.java:635)
			at io.netty.channel.nio.NioEventLoop.processSelectedKeys(NioEventLoop.java:552)
			at io.netty.channel.nio.NioEventLoop.run(NioEventLoop.java:514)
			at io.netty.util.concurrent.SingleThreadEventExecutor$6.run(SingleThreadEventExecutor.java:1050)
			at io.netty.util.internal.ThreadExecutorMap$2.run(ThreadExecutorMap.java:74)
			at io.netty.util.concurrent.FastThreadLocalRunnable.run(FastThreadLocalRunnable.java:30)
			at java.lang.Thread.run(Thread.java:748)
		Suppressed: io.lettuce.core.RedisConnectionException: Unable to connect to [************:7005]: No route to host: no further information: /************:7005
			at io.lettuce.core.cluster.topology.ClusterTopologyRefresh.lambda$getConnections$0(ClusterTopologyRefresh.java:290)
			at java.util.concurrent.CompletableFuture.uniWhenComplete(CompletableFuture.java:760)
			at java.util.concurrent.CompletableFuture$UniWhenComplete.tryFire(CompletableFuture.java:736)
			at java.util.concurrent.CompletableFuture.postComplete(CompletableFuture.java:474)
			at java.util.concurrent.CompletableFuture.completeExceptionally(CompletableFuture.java:1977)
			at io.lettuce.core.AbstractRedisClient.lambda$initializeChannelAsync0$4(AbstractRedisClient.java:329)
			at io.netty.util.concurrent.DefaultPromise.notifyListener0(DefaultPromise.java:577)
			at io.netty.util.concurrent.DefaultPromise.notifyListeners0(DefaultPromise.java:570)
			at io.netty.util.concurrent.DefaultPromise.notifyListenersNow(DefaultPromise.java:549)
			at io.netty.util.concurrent.DefaultPromise.notifyListeners(DefaultPromise.java:490)
			at io.netty.util.concurrent.DefaultPromise.setValue0(DefaultPromise.java:615)
			at io.netty.util.concurrent.DefaultPromise.setFailure0(DefaultPromise.java:608)
			at io.netty.util.concurrent.DefaultPromise.tryFailure(DefaultPromise.java:117)
			at io.netty.channel.nio.AbstractNioChannel$AbstractNioUnsafe.fulfillConnectPromise(AbstractNioChannel.java:321)
			at io.netty.channel.nio.AbstractNioChannel$AbstractNioUnsafe.finishConnect(AbstractNioChannel.java:337)
			at io.netty.channel.nio.NioEventLoop.processSelectedKey(NioEventLoop.java:688)
			at io.netty.channel.nio.NioEventLoop.processSelectedKeysOptimized(NioEventLoop.java:635)
			at io.netty.channel.nio.NioEventLoop.processSelectedKeys(NioEventLoop.java:552)
			at io.netty.channel.nio.NioEventLoop.run(NioEventLoop.java:514)
			... 4 common frames omitted
		Caused by: io.netty.channel.AbstractChannel$AnnotatedNoRouteToHostException: No route to host: no further information: /************:7005
		Caused by: java.net.NoRouteToHostException: No route to host: no further information
			at sun.nio.ch.SocketChannelImpl.checkConnect(Native Method)
			at sun.nio.ch.SocketChannelImpl.finishConnect(SocketChannelImpl.java:717)
			at io.netty.channel.socket.nio.NioSocketChannel.doFinishConnect(NioSocketChannel.java:330)
			at io.netty.channel.nio.AbstractNioChannel$AbstractNioUnsafe.finishConnect(AbstractNioChannel.java:334)
			at io.netty.channel.nio.NioEventLoop.processSelectedKey(NioEventLoop.java:688)
			at io.netty.channel.nio.NioEventLoop.processSelectedKeysOptimized(NioEventLoop.java:635)
			at io.netty.channel.nio.NioEventLoop.processSelectedKeys(NioEventLoop.java:552)
			at io.netty.channel.nio.NioEventLoop.run(NioEventLoop.java:514)
			at io.netty.util.concurrent.SingleThreadEventExecutor$6.run(SingleThreadEventExecutor.java:1050)
			at io.netty.util.internal.ThreadExecutorMap$2.run(ThreadExecutorMap.java:74)
			at io.netty.util.concurrent.FastThreadLocalRunnable.run(FastThreadLocalRunnable.java:30)
			at java.lang.Thread.run(Thread.java:748)
		Suppressed: io.lettuce.core.RedisConnectionException: Unable to connect to [************:7003]: No route to host: no further information: /************:7003
			at io.lettuce.core.cluster.topology.ClusterTopologyRefresh.lambda$getConnections$0(ClusterTopologyRefresh.java:290)
			at java.util.concurrent.CompletableFuture.uniWhenComplete(CompletableFuture.java:760)
			at java.util.concurrent.CompletableFuture$UniWhenComplete.tryFire(CompletableFuture.java:736)
			at java.util.concurrent.CompletableFuture.postComplete(CompletableFuture.java:474)
			at java.util.concurrent.CompletableFuture.completeExceptionally(CompletableFuture.java:1977)
			at io.lettuce.core.AbstractRedisClient.lambda$initializeChannelAsync0$4(AbstractRedisClient.java:329)
			at io.netty.util.concurrent.DefaultPromise.notifyListener0(DefaultPromise.java:577)
			at io.netty.util.concurrent.DefaultPromise.notifyListeners0(DefaultPromise.java:570)
			at io.netty.util.concurrent.DefaultPromise.notifyListenersNow(DefaultPromise.java:549)
			at io.netty.util.concurrent.DefaultPromise.notifyListeners(DefaultPromise.java:490)
			at io.netty.util.concurrent.DefaultPromise.setValue0(DefaultPromise.java:615)
			at io.netty.util.concurrent.DefaultPromise.setFailure0(DefaultPromise.java:608)
			at io.netty.util.concurrent.DefaultPromise.tryFailure(DefaultPromise.java:117)
			at io.netty.channel.nio.AbstractNioChannel$AbstractNioUnsafe.fulfillConnectPromise(AbstractNioChannel.java:321)
			at io.netty.channel.nio.AbstractNioChannel$AbstractNioUnsafe.finishConnect(AbstractNioChannel.java:337)
			at io.netty.channel.nio.NioEventLoop.processSelectedKey(NioEventLoop.java:688)
			at io.netty.channel.nio.NioEventLoop.processSelectedKeysOptimized(NioEventLoop.java:635)
			at io.netty.channel.nio.NioEventLoop.processSelectedKeys(NioEventLoop.java:552)
			at io.netty.channel.nio.NioEventLoop.run(NioEventLoop.java:514)
			... 4 common frames omitted
		Caused by: io.netty.channel.AbstractChannel$AnnotatedNoRouteToHostException: No route to host: no further information: /************:7003
		Caused by: java.net.NoRouteToHostException: No route to host: no further information
			at sun.nio.ch.SocketChannelImpl.checkConnect(Native Method)
			at sun.nio.ch.SocketChannelImpl.finishConnect(SocketChannelImpl.java:717)
			at io.netty.channel.socket.nio.NioSocketChannel.doFinishConnect(NioSocketChannel.java:330)
			at io.netty.channel.nio.AbstractNioChannel$AbstractNioUnsafe.finishConnect(AbstractNioChannel.java:334)
			at io.netty.channel.nio.NioEventLoop.processSelectedKey(NioEventLoop.java:688)
			at io.netty.channel.nio.NioEventLoop.processSelectedKeysOptimized(NioEventLoop.java:635)
			at io.netty.channel.nio.NioEventLoop.processSelectedKeys(NioEventLoop.java:552)
			at io.netty.channel.nio.NioEventLoop.run(NioEventLoop.java:514)
			at io.netty.util.concurrent.SingleThreadEventExecutor$6.run(SingleThreadEventExecutor.java:1050)
			at io.netty.util.internal.ThreadExecutorMap$2.run(ThreadExecutorMap.java:74)
			at io.netty.util.concurrent.FastThreadLocalRunnable.run(FastThreadLocalRunnable.java:30)
			at java.lang.Thread.run(Thread.java:748)
	Caused by: io.lettuce.core.RedisConnectionException: Unable to connect to [************:7004]: No route to host: no further information: /************:7004
		at io.lettuce.core.cluster.topology.ClusterTopologyRefresh.lambda$getConnections$0(ClusterTopologyRefresh.java:290)
		at java.util.concurrent.CompletableFuture.uniWhenComplete(CompletableFuture.java:760)
		at java.util.concurrent.CompletableFuture$UniWhenComplete.tryFire(CompletableFuture.java:736)
		at java.util.concurrent.CompletableFuture.postComplete(CompletableFuture.java:474)
		at java.util.concurrent.CompletableFuture.completeExceptionally(CompletableFuture.java:1977)
		at io.lettuce.core.AbstractRedisClient.lambda$initializeChannelAsync0$4(AbstractRedisClient.java:329)
		at io.netty.util.concurrent.DefaultPromise.notifyListener0(DefaultPromise.java:577)
		at io.netty.util.concurrent.DefaultPromise.notifyListeners0(DefaultPromise.java:570)
		at io.netty.util.concurrent.DefaultPromise.notifyListenersNow(DefaultPromise.java:549)
		at io.netty.util.concurrent.DefaultPromise.notifyListeners(DefaultPromise.java:490)
		at io.netty.util.concurrent.DefaultPromise.setValue0(DefaultPromise.java:615)
		at io.netty.util.concurrent.DefaultPromise.setFailure0(DefaultPromise.java:608)
		at io.netty.util.concurrent.DefaultPromise.tryFailure(DefaultPromise.java:117)
		at io.netty.channel.nio.AbstractNioChannel$AbstractNioUnsafe.fulfillConnectPromise(AbstractNioChannel.java:321)
		at io.netty.channel.nio.AbstractNioChannel$AbstractNioUnsafe.finishConnect(AbstractNioChannel.java:337)
		at io.netty.channel.nio.NioEventLoop.processSelectedKey(NioEventLoop.java:688)
		at io.netty.channel.nio.NioEventLoop.processSelectedKeysOptimized(NioEventLoop.java:635)
		at io.netty.channel.nio.NioEventLoop.processSelectedKeys(NioEventLoop.java:552)
		at io.netty.channel.nio.NioEventLoop.run(NioEventLoop.java:514)
		... 4 common frames omitted
	Caused by: io.netty.channel.AbstractChannel$AnnotatedNoRouteToHostException: No route to host: no further information: /************:7004
	Caused by: java.net.NoRouteToHostException: No route to host: no further information
		at sun.nio.ch.SocketChannelImpl.checkConnect(Native Method)
		at sun.nio.ch.SocketChannelImpl.finishConnect(SocketChannelImpl.java:717)
		at io.netty.channel.socket.nio.NioSocketChannel.doFinishConnect(NioSocketChannel.java:330)
		at io.netty.channel.nio.AbstractNioChannel$AbstractNioUnsafe.finishConnect(AbstractNioChannel.java:334)
		at io.netty.channel.nio.NioEventLoop.processSelectedKey(NioEventLoop.java:688)
		at io.netty.channel.nio.NioEventLoop.processSelectedKeysOptimized(NioEventLoop.java:635)
		at io.netty.channel.nio.NioEventLoop.processSelectedKeys(NioEventLoop.java:552)
		at io.netty.channel.nio.NioEventLoop.run(NioEventLoop.java:514)
		at io.netty.util.concurrent.SingleThreadEventExecutor$6.run(SingleThreadEventExecutor.java:1050)
		at io.netty.util.internal.ThreadExecutorMap$2.run(ThreadExecutorMap.java:74)
		at io.netty.util.concurrent.FastThreadLocalRunnable.run(FastThreadLocalRunnable.java:30)
		at java.lang.Thread.run(Thread.java:748)
Caused by: io.lettuce.core.RedisConnectionException: Unable to establish a connection to Redis Cluster at [RedisURI [host='************', port=7001], RedisURI [host='************', port=7002], RedisURI [host='************', port=7003], RedisURI [host='************', port=7004], RedisURI [host='************', port=7005], RedisURI [host='************', port=7006]]
	at io.lettuce.core.cluster.topology.AsyncConnections.get(AsyncConnections.java:89)
	at io.lettuce.core.cluster.topology.ClusterTopologyRefresh.loadViews(ClusterTopologyRefresh.java:76)
	at io.lettuce.core.cluster.RedisClusterClient.doLoadPartitions(RedisClusterClient.java:865)
	at io.lettuce.core.cluster.RedisClusterClient.loadPartitions(RedisClusterClient.java:845)
	... 10 common frames omitted
	Suppressed: io.lettuce.core.RedisConnectionException: Unable to connect to [************:7006]: No route to host: no further information: /************:7006
		at io.lettuce.core.cluster.topology.ClusterTopologyRefresh.lambda$getConnections$0(ClusterTopologyRefresh.java:290)
		at java.util.concurrent.CompletableFuture.uniWhenComplete(CompletableFuture.java:760)
		at java.util.concurrent.CompletableFuture$UniWhenComplete.tryFire(CompletableFuture.java:736)
		at java.util.concurrent.CompletableFuture.postComplete(CompletableFuture.java:474)
		at java.util.concurrent.CompletableFuture.completeExceptionally(CompletableFuture.java:1977)
		at io.lettuce.core.AbstractRedisClient.lambda$initializeChannelAsync0$4(AbstractRedisClient.java:329)
		at io.netty.util.concurrent.DefaultPromise.notifyListener0(DefaultPromise.java:577)
		at io.netty.util.concurrent.DefaultPromise.notifyListeners0(DefaultPromise.java:570)
		at io.netty.util.concurrent.DefaultPromise.notifyListenersNow(DefaultPromise.java:549)
		at io.netty.util.concurrent.DefaultPromise.notifyListeners(DefaultPromise.java:490)
		at io.netty.util.concurrent.DefaultPromise.setValue0(DefaultPromise.java:615)
		at io.netty.util.concurrent.DefaultPromise.setFailure0(DefaultPromise.java:608)
		at io.netty.util.concurrent.DefaultPromise.tryFailure(DefaultPromise.java:117)
		at io.netty.channel.nio.AbstractNioChannel$AbstractNioUnsafe.fulfillConnectPromise(AbstractNioChannel.java:321)
		at io.netty.channel.nio.AbstractNioChannel$AbstractNioUnsafe.finishConnect(AbstractNioChannel.java:337)
		at io.netty.channel.nio.NioEventLoop.processSelectedKey(NioEventLoop.java:688)
		at io.netty.channel.nio.NioEventLoop.processSelectedKeysOptimized(NioEventLoop.java:635)
		at io.netty.channel.nio.NioEventLoop.processSelectedKeys(NioEventLoop.java:552)
		at io.netty.channel.nio.NioEventLoop.run(NioEventLoop.java:514)
		... 4 common frames omitted
	Caused by: io.netty.channel.AbstractChannel$AnnotatedNoRouteToHostException: No route to host: no further information: /************:7006
	Caused by: java.net.NoRouteToHostException: No route to host: no further information
		at sun.nio.ch.SocketChannelImpl.checkConnect(Native Method)
		at sun.nio.ch.SocketChannelImpl.finishConnect(SocketChannelImpl.java:717)
		at io.netty.channel.socket.nio.NioSocketChannel.doFinishConnect(NioSocketChannel.java:330)
		at io.netty.channel.nio.AbstractNioChannel$AbstractNioUnsafe.finishConnect(AbstractNioChannel.java:334)
		at io.netty.channel.nio.NioEventLoop.processSelectedKey(NioEventLoop.java:688)
		at io.netty.channel.nio.NioEventLoop.processSelectedKeysOptimized(NioEventLoop.java:635)
		at io.netty.channel.nio.NioEventLoop.processSelectedKeys(NioEventLoop.java:552)
		at io.netty.channel.nio.NioEventLoop.run(NioEventLoop.java:514)
		at io.netty.util.concurrent.SingleThreadEventExecutor$6.run(SingleThreadEventExecutor.java:1050)
		at io.netty.util.internal.ThreadExecutorMap$2.run(ThreadExecutorMap.java:74)
		at io.netty.util.concurrent.FastThreadLocalRunnable.run(FastThreadLocalRunnable.java:30)
		at java.lang.Thread.run(Thread.java:748)
	Suppressed: io.lettuce.core.RedisConnectionException: Unable to connect to [************:7005]: No route to host: no further information: /************:7005
		at io.lettuce.core.cluster.topology.ClusterTopologyRefresh.lambda$getConnections$0(ClusterTopologyRefresh.java:290)
		at java.util.concurrent.CompletableFuture.uniWhenComplete(CompletableFuture.java:760)
		at java.util.concurrent.CompletableFuture$UniWhenComplete.tryFire(CompletableFuture.java:736)
		at java.util.concurrent.CompletableFuture.postComplete(CompletableFuture.java:474)
		at java.util.concurrent.CompletableFuture.completeExceptionally(CompletableFuture.java:1977)
		at io.lettuce.core.AbstractRedisClient.lambda$initializeChannelAsync0$4(AbstractRedisClient.java:329)
		at io.netty.util.concurrent.DefaultPromise.notifyListener0(DefaultPromise.java:577)
		at io.netty.util.concurrent.DefaultPromise.notifyListeners0(DefaultPromise.java:570)
		at io.netty.util.concurrent.DefaultPromise.notifyListenersNow(DefaultPromise.java:549)
		at io.netty.util.concurrent.DefaultPromise.notifyListeners(DefaultPromise.java:490)
		at io.netty.util.concurrent.DefaultPromise.setValue0(DefaultPromise.java:615)
		at io.netty.util.concurrent.DefaultPromise.setFailure0(DefaultPromise.java:608)
		at io.netty.util.concurrent.DefaultPromise.tryFailure(DefaultPromise.java:117)
		at io.netty.channel.nio.AbstractNioChannel$AbstractNioUnsafe.fulfillConnectPromise(AbstractNioChannel.java:321)
		at io.netty.channel.nio.AbstractNioChannel$AbstractNioUnsafe.finishConnect(AbstractNioChannel.java:337)
		at io.netty.channel.nio.NioEventLoop.processSelectedKey(NioEventLoop.java:688)
		at io.netty.channel.nio.NioEventLoop.processSelectedKeysOptimized(NioEventLoop.java:635)
		at io.netty.channel.nio.NioEventLoop.processSelectedKeys(NioEventLoop.java:552)
		at io.netty.channel.nio.NioEventLoop.run(NioEventLoop.java:514)
		... 4 common frames omitted
	Caused by: io.netty.channel.AbstractChannel$AnnotatedNoRouteToHostException: No route to host: no further information: /************:7005
	Caused by: java.net.NoRouteToHostException: No route to host: no further information
		at sun.nio.ch.SocketChannelImpl.checkConnect(Native Method)
		at sun.nio.ch.SocketChannelImpl.finishConnect(SocketChannelImpl.java:717)
		at io.netty.channel.socket.nio.NioSocketChannel.doFinishConnect(NioSocketChannel.java:330)
		at io.netty.channel.nio.AbstractNioChannel$AbstractNioUnsafe.finishConnect(AbstractNioChannel.java:334)
		at io.netty.channel.nio.NioEventLoop.processSelectedKey(NioEventLoop.java:688)
		at io.netty.channel.nio.NioEventLoop.processSelectedKeysOptimized(NioEventLoop.java:635)
		at io.netty.channel.nio.NioEventLoop.processSelectedKeys(NioEventLoop.java:552)
		at io.netty.channel.nio.NioEventLoop.run(NioEventLoop.java:514)
		at io.netty.util.concurrent.SingleThreadEventExecutor$6.run(SingleThreadEventExecutor.java:1050)
		at io.netty.util.internal.ThreadExecutorMap$2.run(ThreadExecutorMap.java:74)
		at io.netty.util.concurrent.FastThreadLocalRunnable.run(FastThreadLocalRunnable.java:30)
		at java.lang.Thread.run(Thread.java:748)
	Suppressed: io.lettuce.core.RedisConnectionException: Unable to connect to [************:7002]: No route to host: no further information: /************:7002
		at io.lettuce.core.cluster.topology.ClusterTopologyRefresh.lambda$getConnections$0(ClusterTopologyRefresh.java:290)
		at java.util.concurrent.CompletableFuture.uniWhenComplete(CompletableFuture.java:760)
		at java.util.concurrent.CompletableFuture$UniWhenComplete.tryFire(CompletableFuture.java:736)
		at java.util.concurrent.CompletableFuture.postComplete(CompletableFuture.java:474)
		at java.util.concurrent.CompletableFuture.completeExceptionally(CompletableFuture.java:1977)
		at io.lettuce.core.AbstractRedisClient.lambda$initializeChannelAsync0$4(AbstractRedisClient.java:329)
		at io.netty.util.concurrent.DefaultPromise.notifyListener0(DefaultPromise.java:577)
		at io.netty.util.concurrent.DefaultPromise.notifyListeners0(DefaultPromise.java:570)
		at io.netty.util.concurrent.DefaultPromise.notifyListenersNow(DefaultPromise.java:549)
		at io.netty.util.concurrent.DefaultPromise.notifyListeners(DefaultPromise.java:490)
		at io.netty.util.concurrent.DefaultPromise.setValue0(DefaultPromise.java:615)
		at io.netty.util.concurrent.DefaultPromise.setFailure0(DefaultPromise.java:608)
		at io.netty.util.concurrent.DefaultPromise.tryFailure(DefaultPromise.java:117)
		at io.netty.channel.nio.AbstractNioChannel$AbstractNioUnsafe.fulfillConnectPromise(AbstractNioChannel.java:321)
		at io.netty.channel.nio.AbstractNioChannel$AbstractNioUnsafe.finishConnect(AbstractNioChannel.java:337)
		at io.netty.channel.nio.NioEventLoop.processSelectedKey(NioEventLoop.java:688)
		at io.netty.channel.nio.NioEventLoop.processSelectedKeysOptimized(NioEventLoop.java:635)
		at io.netty.channel.nio.NioEventLoop.processSelectedKeys(NioEventLoop.java:552)
		at io.netty.channel.nio.NioEventLoop.run(NioEventLoop.java:514)
		... 4 common frames omitted
	Caused by: io.netty.channel.AbstractChannel$AnnotatedNoRouteToHostException: No route to host: no further information: /************:7002
	Caused by: java.net.NoRouteToHostException: No route to host: no further information
		at sun.nio.ch.SocketChannelImpl.checkConnect(Native Method)
		at sun.nio.ch.SocketChannelImpl.finishConnect(SocketChannelImpl.java:717)
		at io.netty.channel.socket.nio.NioSocketChannel.doFinishConnect(NioSocketChannel.java:330)
		at io.netty.channel.nio.AbstractNioChannel$AbstractNioUnsafe.finishConnect(AbstractNioChannel.java:334)
		at io.netty.channel.nio.NioEventLoop.processSelectedKey(NioEventLoop.java:688)
		at io.netty.channel.nio.NioEventLoop.processSelectedKeysOptimized(NioEventLoop.java:635)
		at io.netty.channel.nio.NioEventLoop.processSelectedKeys(NioEventLoop.java:552)
		at io.netty.channel.nio.NioEventLoop.run(NioEventLoop.java:514)
		at io.netty.util.concurrent.SingleThreadEventExecutor$6.run(SingleThreadEventExecutor.java:1050)
		at io.netty.util.internal.ThreadExecutorMap$2.run(ThreadExecutorMap.java:74)
		at io.netty.util.concurrent.FastThreadLocalRunnable.run(FastThreadLocalRunnable.java:30)
		at java.lang.Thread.run(Thread.java:748)
	Suppressed: io.lettuce.core.RedisConnectionException: Unable to connect to [************:7003]: No route to host: no further information: /************:7003
		at io.lettuce.core.cluster.topology.ClusterTopologyRefresh.lambda$getConnections$0(ClusterTopologyRefresh.java:290)
		at java.util.concurrent.CompletableFuture.uniWhenComplete(CompletableFuture.java:760)
		at java.util.concurrent.CompletableFuture$UniWhenComplete.tryFire(CompletableFuture.java:736)
		at java.util.concurrent.CompletableFuture.postComplete(CompletableFuture.java:474)
		at java.util.concurrent.CompletableFuture.completeExceptionally(CompletableFuture.java:1977)
		at io.lettuce.core.AbstractRedisClient.lambda$initializeChannelAsync0$4(AbstractRedisClient.java:329)
		at io.netty.util.concurrent.DefaultPromise.notifyListener0(DefaultPromise.java:577)
		at io.netty.util.concurrent.DefaultPromise.notifyListeners0(DefaultPromise.java:570)
		at io.netty.util.concurrent.DefaultPromise.notifyListenersNow(DefaultPromise.java:549)
		at io.netty.util.concurrent.DefaultPromise.notifyListeners(DefaultPromise.java:490)
		at io.netty.util.concurrent.DefaultPromise.setValue0(DefaultPromise.java:615)
		at io.netty.util.concurrent.DefaultPromise.setFailure0(DefaultPromise.java:608)
		at io.netty.util.concurrent.DefaultPromise.tryFailure(DefaultPromise.java:117)
		at io.netty.channel.nio.AbstractNioChannel$AbstractNioUnsafe.fulfillConnectPromise(AbstractNioChannel.java:321)
		at io.netty.channel.nio.AbstractNioChannel$AbstractNioUnsafe.finishConnect(AbstractNioChannel.java:337)
		at io.netty.channel.nio.NioEventLoop.processSelectedKey(NioEventLoop.java:688)
		at io.netty.channel.nio.NioEventLoop.processSelectedKeysOptimized(NioEventLoop.java:635)
		at io.netty.channel.nio.NioEventLoop.processSelectedKeys(NioEventLoop.java:552)
		at io.netty.channel.nio.NioEventLoop.run(NioEventLoop.java:514)
		... 4 common frames omitted
	Caused by: io.netty.channel.AbstractChannel$AnnotatedNoRouteToHostException: No route to host: no further information: /************:7003
	Caused by: java.net.NoRouteToHostException: No route to host: no further information
		at sun.nio.ch.SocketChannelImpl.checkConnect(Native Method)
		at sun.nio.ch.SocketChannelImpl.finishConnect(SocketChannelImpl.java:717)
		at io.netty.channel.socket.nio.NioSocketChannel.doFinishConnect(NioSocketChannel.java:330)
		at io.netty.channel.nio.AbstractNioChannel$AbstractNioUnsafe.finishConnect(AbstractNioChannel.java:334)
		at io.netty.channel.nio.NioEventLoop.processSelectedKey(NioEventLoop.java:688)
		at io.netty.channel.nio.NioEventLoop.processSelectedKeysOptimized(NioEventLoop.java:635)
		at io.netty.channel.nio.NioEventLoop.processSelectedKeys(NioEventLoop.java:552)
		at io.netty.channel.nio.NioEventLoop.run(NioEventLoop.java:514)
		at io.netty.util.concurrent.SingleThreadEventExecutor$6.run(SingleThreadEventExecutor.java:1050)
		at io.netty.util.internal.ThreadExecutorMap$2.run(ThreadExecutorMap.java:74)
		at io.netty.util.concurrent.FastThreadLocalRunnable.run(FastThreadLocalRunnable.java:30)
		at java.lang.Thread.run(Thread.java:748)
	Suppressed: io.lettuce.core.RedisConnectionException: Unable to connect to [************:7001]: No route to host: no further information: /************:7001
		at io.lettuce.core.cluster.topology.ClusterTopologyRefresh.lambda$getConnections$0(ClusterTopologyRefresh.java:290)
		at java.util.concurrent.CompletableFuture.uniWhenComplete(CompletableFuture.java:760)
		at java.util.concurrent.CompletableFuture$UniWhenComplete.tryFire(CompletableFuture.java:736)
		at java.util.concurrent.CompletableFuture.postComplete(CompletableFuture.java:474)
		at java.util.concurrent.CompletableFuture.completeExceptionally(CompletableFuture.java:1977)
		at io.lettuce.core.AbstractRedisClient.lambda$initializeChannelAsync0$4(AbstractRedisClient.java:329)
		at io.netty.util.concurrent.DefaultPromise.notifyListener0(DefaultPromise.java:577)
		at io.netty.util.concurrent.DefaultPromise.notifyListeners0(DefaultPromise.java:570)
		at io.netty.util.concurrent.DefaultPromise.notifyListenersNow(DefaultPromise.java:549)
		at io.netty.util.concurrent.DefaultPromise.notifyListeners(DefaultPromise.java:490)
		at io.netty.util.concurrent.DefaultPromise.setValue0(DefaultPromise.java:615)
		at io.netty.util.concurrent.DefaultPromise.setFailure0(DefaultPromise.java:608)
		at io.netty.util.concurrent.DefaultPromise.tryFailure(DefaultPromise.java:117)
		at io.netty.channel.nio.AbstractNioChannel$AbstractNioUnsafe.fulfillConnectPromise(AbstractNioChannel.java:321)
		at io.netty.channel.nio.AbstractNioChannel$AbstractNioUnsafe.finishConnect(AbstractNioChannel.java:337)
		at io.netty.channel.nio.NioEventLoop.processSelectedKey(NioEventLoop.java:688)
		at io.netty.channel.nio.NioEventLoop.processSelectedKeysOptimized(NioEventLoop.java:635)
		at io.netty.channel.nio.NioEventLoop.processSelectedKeys(NioEventLoop.java:552)
		at io.netty.channel.nio.NioEventLoop.run(NioEventLoop.java:514)
		... 4 common frames omitted
	Caused by: io.netty.channel.AbstractChannel$AnnotatedNoRouteToHostException: No route to host: no further information: /************:7001
	Caused by: java.net.NoRouteToHostException: No route to host: no further information
		at sun.nio.ch.SocketChannelImpl.checkConnect(Native Method)
		at sun.nio.ch.SocketChannelImpl.finishConnect(SocketChannelImpl.java:717)
		at io.netty.channel.socket.nio.NioSocketChannel.doFinishConnect(NioSocketChannel.java:330)
		at io.netty.channel.nio.AbstractNioChannel$AbstractNioUnsafe.finishConnect(AbstractNioChannel.java:334)
		at io.netty.channel.nio.NioEventLoop.processSelectedKey(NioEventLoop.java:688)
		at io.netty.channel.nio.NioEventLoop.processSelectedKeysOptimized(NioEventLoop.java:635)
		at io.netty.channel.nio.NioEventLoop.processSelectedKeys(NioEventLoop.java:552)
		at io.netty.channel.nio.NioEventLoop.run(NioEventLoop.java:514)
		at io.netty.util.concurrent.SingleThreadEventExecutor$6.run(SingleThreadEventExecutor.java:1050)
		at io.netty.util.internal.ThreadExecutorMap$2.run(ThreadExecutorMap.java:74)
		at io.netty.util.concurrent.FastThreadLocalRunnable.run(FastThreadLocalRunnable.java:30)
		at java.lang.Thread.run(Thread.java:748)
Caused by: io.lettuce.core.RedisConnectionException: Unable to connect to [************:7004]: No route to host: no further information: /************:7004
	at io.lettuce.core.cluster.topology.ClusterTopologyRefresh.lambda$getConnections$0(ClusterTopologyRefresh.java:290)
	at java.util.concurrent.CompletableFuture.uniWhenComplete(CompletableFuture.java:760)
	at java.util.concurrent.CompletableFuture$UniWhenComplete.tryFire(CompletableFuture.java:736)
	at java.util.concurrent.CompletableFuture.postComplete(CompletableFuture.java:474)
	at java.util.concurrent.CompletableFuture.completeExceptionally(CompletableFuture.java:1977)
	at io.lettuce.core.AbstractRedisClient.lambda$initializeChannelAsync0$4(AbstractRedisClient.java:329)
	at io.netty.util.concurrent.DefaultPromise.notifyListener0(DefaultPromise.java:577)
	at io.netty.util.concurrent.DefaultPromise.notifyListeners0(DefaultPromise.java:570)
	at io.netty.util.concurrent.DefaultPromise.notifyListenersNow(DefaultPromise.java:549)
	at io.netty.util.concurrent.DefaultPromise.notifyListeners(DefaultPromise.java:490)
	at io.netty.util.concurrent.DefaultPromise.setValue0(DefaultPromise.java:615)
	at io.netty.util.concurrent.DefaultPromise.setFailure0(DefaultPromise.java:608)
	at io.netty.util.concurrent.DefaultPromise.tryFailure(DefaultPromise.java:117)
	at io.netty.channel.nio.AbstractNioChannel$AbstractNioUnsafe.fulfillConnectPromise(AbstractNioChannel.java:321)
	at io.netty.channel.nio.AbstractNioChannel$AbstractNioUnsafe.finishConnect(AbstractNioChannel.java:337)
	at io.netty.channel.nio.NioEventLoop.processSelectedKey(NioEventLoop.java:688)
	at io.netty.channel.nio.NioEventLoop.processSelectedKeysOptimized(NioEventLoop.java:635)
	at io.netty.channel.nio.NioEventLoop.processSelectedKeys(NioEventLoop.java:552)
	at io.netty.channel.nio.NioEventLoop.run(NioEventLoop.java:514)
	... 4 common frames omitted
Caused by: io.netty.channel.AbstractChannel$AnnotatedNoRouteToHostException: No route to host: no further information: /************:7004
Caused by: java.net.NoRouteToHostException: No route to host: no further information
	at sun.nio.ch.SocketChannelImpl.checkConnect(Native Method)
	at sun.nio.ch.SocketChannelImpl.finishConnect(SocketChannelImpl.java:717)
	at io.netty.channel.socket.nio.NioSocketChannel.doFinishConnect(NioSocketChannel.java:330)
	at io.netty.channel.nio.AbstractNioChannel$AbstractNioUnsafe.finishConnect(AbstractNioChannel.java:334)
	at io.netty.channel.nio.NioEventLoop.processSelectedKey(NioEventLoop.java:688)
	at io.netty.channel.nio.NioEventLoop.processSelectedKeysOptimized(NioEventLoop.java:635)
	at io.netty.channel.nio.NioEventLoop.processSelectedKeys(NioEventLoop.java:552)
	at io.netty.channel.nio.NioEventLoop.run(NioEventLoop.java:514)
	at io.netty.util.concurrent.SingleThreadEventExecutor$6.run(SingleThreadEventExecutor.java:1050)
	at io.netty.util.internal.ThreadExecutorMap$2.run(ThreadExecutorMap.java:74)
	at io.netty.util.concurrent.FastThreadLocalRunnable.run(FastThreadLocalRunnable.java:30)
	at java.lang.Thread.run(Thread.java:748)
2025-05-26 13:13:05.633 [lettuce-nioEventLoop-4-7] WARN  io.lettuce.core.cluster.topology.ClusterTopologyRefresh.lambda$getConnections$0 Line:284 - Unable to connect to [************:7001]: No route to host: no further information: /************:7001
2025-05-26 13:13:05.633 [lettuce-nioEventLoop-4-9] WARN  io.lettuce.core.cluster.topology.ClusterTopologyRefresh.lambda$getConnections$0 Line:284 - Unable to connect to [************:7003]: No route to host: no further information: /************:7003
2025-05-26 13:13:05.633 [lettuce-nioEventLoop-4-8] WARN  io.lettuce.core.cluster.topology.ClusterTopologyRefresh.lambda$getConnections$0 Line:284 - Unable to connect to [************:7002]: No route to host: no further information: /************:7002
2025-05-26 13:13:05.633 [lettuce-nioEventLoop-4-10] WARN  io.lettuce.core.cluster.topology.ClusterTopologyRefresh.lambda$getConnections$0 Line:284 - Unable to connect to [************:7004]: No route to host: no further information: /************:7004
2025-05-26 13:13:05.633 [lettuce-nioEventLoop-4-11] WARN  io.lettuce.core.cluster.topology.ClusterTopologyRefresh.lambda$getConnections$0 Line:284 - Unable to connect to [************:7005]: No route to host: no further information: /************:7005
2025-05-26 13:13:05.634 [lettuce-nioEventLoop-4-12] WARN  io.lettuce.core.cluster.topology.ClusterTopologyRefresh.lambda$getConnections$0 Line:284 - Unable to connect to [************:7006]: No route to host: no further information: /************:7006
2025-05-26 13:13:05.635 [lettuce-nioEventLoop-4-1] WARN  io.lettuce.core.cluster.topology.ClusterTopologyRefresh.lambda$getConnections$0 Line:284 - Unable to connect to [************:7006]: No route to host: no further information: /************:7006
2025-05-26 13:13:05.635 [lettuce-nioEventLoop-4-2] WARN  io.lettuce.core.cluster.topology.ClusterTopologyRefresh.lambda$getConnections$0 Line:284 - Unable to connect to [************:7004]: No route to host: no further information: /************:7004
2025-05-26 13:13:05.635 [lettuce-nioEventLoop-4-3] WARN  io.lettuce.core.cluster.topology.ClusterTopologyRefresh.lambda$getConnections$0 Line:284 - Unable to connect to [************:7005]: No route to host: no further information: /************:7005
2025-05-26 13:13:05.636 [lettuce-nioEventLoop-4-4] WARN  io.lettuce.core.cluster.topology.ClusterTopologyRefresh.lambda$getConnections$0 Line:284 - Unable to connect to [************:7002]: No route to host: no further information: /************:7002
2025-05-26 13:13:05.636 [lettuce-nioEventLoop-4-5] WARN  io.lettuce.core.cluster.topology.ClusterTopologyRefresh.lambda$getConnections$0 Line:284 - Unable to connect to [************:7003]: No route to host: no further information: /************:7003
2025-05-26 13:13:05.636 [lettuce-nioEventLoop-4-6] WARN  io.lettuce.core.cluster.topology.ClusterTopologyRefresh.lambda$getConnections$0 Line:284 - Unable to connect to [************:7001]: No route to host: no further information: /************:7001
2025-05-26 13:13:05.646 [lettuce-eventExecutorLoop-1-6] WARN  io.lettuce.core.cluster.ClusterTopologyRefreshScheduler.doRun Line:258 - Cannot refresh Redis Cluster topology
io.lettuce.core.RedisException: Cannot retrieve initial cluster partitions from initial URIs [RedisURI [host='************', port=7006], RedisURI [host='************', port=7004], RedisURI [host='************', port=7005], RedisURI [host='************', port=7002], RedisURI [host='************', port=7003], RedisURI [host='************', port=7001]]
	at io.lettuce.core.cluster.RedisClusterClient.loadPartitions(RedisClusterClient.java:848)
	at io.lettuce.core.cluster.RedisClusterClient.reloadPartitions(RedisClusterClient.java:784)
	at io.lettuce.core.cluster.ClusterTopologyRefreshScheduler$ClusterTopologyRefreshTask.doRun(ClusterTopologyRefreshScheduler.java:256)
	at io.lettuce.core.cluster.ClusterTopologyRefreshScheduler$ClusterTopologyRefreshTask.run(ClusterTopologyRefreshScheduler.java:236)
	at io.netty.util.concurrent.PromiseTask.runTask(PromiseTask.java:98)
	at io.netty.util.concurrent.PromiseTask.run(PromiseTask.java:106)
	at io.netty.util.concurrent.DefaultEventExecutor.run(DefaultEventExecutor.java:66)
	at io.netty.util.concurrent.SingleThreadEventExecutor$6.run(SingleThreadEventExecutor.java:1050)
	at io.netty.util.internal.ThreadExecutorMap$2.run(ThreadExecutorMap.java:74)
	at io.netty.util.concurrent.FastThreadLocalRunnable.run(FastThreadLocalRunnable.java:30)
	at java.lang.Thread.run(Thread.java:748)
	Suppressed: io.lettuce.core.RedisConnectionException: Unable to establish a connection to Redis Cluster at [RedisURI [host='************', port=7001], RedisURI [host='************', port=7002], RedisURI [host='************', port=7003], RedisURI [host='************', port=7004], RedisURI [host='************', port=7005], RedisURI [host='************', port=7006]]
		at io.lettuce.core.cluster.topology.AsyncConnections.get(AsyncConnections.java:89)
		at io.lettuce.core.cluster.topology.ClusterTopologyRefresh.loadViews(ClusterTopologyRefresh.java:76)
		at io.lettuce.core.cluster.RedisClusterClient.doLoadPartitions(RedisClusterClient.java:865)
		at io.lettuce.core.cluster.RedisClusterClient.loadPartitions(RedisClusterClient.java:838)
		... 10 common frames omitted
		Suppressed: io.lettuce.core.RedisConnectionException: Unable to connect to [************:7003]: No route to host: no further information: /************:7003
			at io.lettuce.core.cluster.topology.ClusterTopologyRefresh.lambda$getConnections$0(ClusterTopologyRefresh.java:290)
			at java.util.concurrent.CompletableFuture.uniWhenComplete(CompletableFuture.java:760)
			at java.util.concurrent.CompletableFuture$UniWhenComplete.tryFire(CompletableFuture.java:736)
			at java.util.concurrent.CompletableFuture.postComplete(CompletableFuture.java:474)
			at java.util.concurrent.CompletableFuture.completeExceptionally(CompletableFuture.java:1977)
			at io.lettuce.core.AbstractRedisClient.lambda$initializeChannelAsync0$4(AbstractRedisClient.java:329)
			at io.netty.util.concurrent.DefaultPromise.notifyListener0(DefaultPromise.java:577)
			at io.netty.util.concurrent.DefaultPromise.notifyListeners0(DefaultPromise.java:570)
			at io.netty.util.concurrent.DefaultPromise.notifyListenersNow(DefaultPromise.java:549)
			at io.netty.util.concurrent.DefaultPromise.notifyListeners(DefaultPromise.java:490)
			at io.netty.util.concurrent.DefaultPromise.setValue0(DefaultPromise.java:615)
			at io.netty.util.concurrent.DefaultPromise.setFailure0(DefaultPromise.java:608)
			at io.netty.util.concurrent.DefaultPromise.tryFailure(DefaultPromise.java:117)
			at io.netty.channel.nio.AbstractNioChannel$AbstractNioUnsafe.fulfillConnectPromise(AbstractNioChannel.java:321)
			at io.netty.channel.nio.AbstractNioChannel$AbstractNioUnsafe.finishConnect(AbstractNioChannel.java:337)
			at io.netty.channel.nio.NioEventLoop.processSelectedKey(NioEventLoop.java:688)
			at io.netty.channel.nio.NioEventLoop.processSelectedKeysOptimized(NioEventLoop.java:635)
			at io.netty.channel.nio.NioEventLoop.processSelectedKeys(NioEventLoop.java:552)
			at io.netty.channel.nio.NioEventLoop.run(NioEventLoop.java:514)
			... 4 common frames omitted
		Caused by: io.netty.channel.AbstractChannel$AnnotatedNoRouteToHostException: No route to host: no further information: /************:7003
		Caused by: java.net.NoRouteToHostException: No route to host: no further information
			at sun.nio.ch.SocketChannelImpl.checkConnect(Native Method)
			at sun.nio.ch.SocketChannelImpl.finishConnect(SocketChannelImpl.java:717)
			at io.netty.channel.socket.nio.NioSocketChannel.doFinishConnect(NioSocketChannel.java:330)
			at io.netty.channel.nio.AbstractNioChannel$AbstractNioUnsafe.finishConnect(AbstractNioChannel.java:334)
			at io.netty.channel.nio.NioEventLoop.processSelectedKey(NioEventLoop.java:688)
			at io.netty.channel.nio.NioEventLoop.processSelectedKeysOptimized(NioEventLoop.java:635)
			at io.netty.channel.nio.NioEventLoop.processSelectedKeys(NioEventLoop.java:552)
			at io.netty.channel.nio.NioEventLoop.run(NioEventLoop.java:514)
			at io.netty.util.concurrent.SingleThreadEventExecutor$6.run(SingleThreadEventExecutor.java:1050)
			at io.netty.util.internal.ThreadExecutorMap$2.run(ThreadExecutorMap.java:74)
			at io.netty.util.concurrent.FastThreadLocalRunnable.run(FastThreadLocalRunnable.java:30)
			at java.lang.Thread.run(Thread.java:748)
		Suppressed: io.lettuce.core.RedisConnectionException: Unable to connect to [************:7002]: No route to host: no further information: /************:7002
			at io.lettuce.core.cluster.topology.ClusterTopologyRefresh.lambda$getConnections$0(ClusterTopologyRefresh.java:290)
			at java.util.concurrent.CompletableFuture.uniWhenComplete(CompletableFuture.java:760)
			at java.util.concurrent.CompletableFuture$UniWhenComplete.tryFire(CompletableFuture.java:736)
			at java.util.concurrent.CompletableFuture.postComplete(CompletableFuture.java:474)
			at java.util.concurrent.CompletableFuture.completeExceptionally(CompletableFuture.java:1977)
			at io.lettuce.core.AbstractRedisClient.lambda$initializeChannelAsync0$4(AbstractRedisClient.java:329)
			at io.netty.util.concurrent.DefaultPromise.notifyListener0(DefaultPromise.java:577)
			at io.netty.util.concurrent.DefaultPromise.notifyListeners0(DefaultPromise.java:570)
			at io.netty.util.concurrent.DefaultPromise.notifyListenersNow(DefaultPromise.java:549)
			at io.netty.util.concurrent.DefaultPromise.notifyListeners(DefaultPromise.java:490)
			at io.netty.util.concurrent.DefaultPromise.setValue0(DefaultPromise.java:615)
			at io.netty.util.concurrent.DefaultPromise.setFailure0(DefaultPromise.java:608)
			at io.netty.util.concurrent.DefaultPromise.tryFailure(DefaultPromise.java:117)
			at io.netty.channel.nio.AbstractNioChannel$AbstractNioUnsafe.fulfillConnectPromise(AbstractNioChannel.java:321)
			at io.netty.channel.nio.AbstractNioChannel$AbstractNioUnsafe.finishConnect(AbstractNioChannel.java:337)
			at io.netty.channel.nio.NioEventLoop.processSelectedKey(NioEventLoop.java:688)
			at io.netty.channel.nio.NioEventLoop.processSelectedKeysOptimized(NioEventLoop.java:635)
			at io.netty.channel.nio.NioEventLoop.processSelectedKeys(NioEventLoop.java:552)
			at io.netty.channel.nio.NioEventLoop.run(NioEventLoop.java:514)
			... 4 common frames omitted
		Caused by: io.netty.channel.AbstractChannel$AnnotatedNoRouteToHostException: No route to host: no further information: /************:7002
		Caused by: java.net.NoRouteToHostException: No route to host: no further information
			at sun.nio.ch.SocketChannelImpl.checkConnect(Native Method)
			at sun.nio.ch.SocketChannelImpl.finishConnect(SocketChannelImpl.java:717)
			at io.netty.channel.socket.nio.NioSocketChannel.doFinishConnect(NioSocketChannel.java:330)
			at io.netty.channel.nio.AbstractNioChannel$AbstractNioUnsafe.finishConnect(AbstractNioChannel.java:334)
			at io.netty.channel.nio.NioEventLoop.processSelectedKey(NioEventLoop.java:688)
			at io.netty.channel.nio.NioEventLoop.processSelectedKeysOptimized(NioEventLoop.java:635)
			at io.netty.channel.nio.NioEventLoop.processSelectedKeys(NioEventLoop.java:552)
			at io.netty.channel.nio.NioEventLoop.run(NioEventLoop.java:514)
			at io.netty.util.concurrent.SingleThreadEventExecutor$6.run(SingleThreadEventExecutor.java:1050)
			at io.netty.util.internal.ThreadExecutorMap$2.run(ThreadExecutorMap.java:74)
			at io.netty.util.concurrent.FastThreadLocalRunnable.run(FastThreadLocalRunnable.java:30)
			at java.lang.Thread.run(Thread.java:748)
		Suppressed: io.lettuce.core.RedisConnectionException: Unable to connect to [************:7004]: No route to host: no further information: /************:7004
			at io.lettuce.core.cluster.topology.ClusterTopologyRefresh.lambda$getConnections$0(ClusterTopologyRefresh.java:290)
			at java.util.concurrent.CompletableFuture.uniWhenComplete(CompletableFuture.java:760)
			at java.util.concurrent.CompletableFuture$UniWhenComplete.tryFire(CompletableFuture.java:736)
			at java.util.concurrent.CompletableFuture.postComplete(CompletableFuture.java:474)
			at java.util.concurrent.CompletableFuture.completeExceptionally(CompletableFuture.java:1977)
			at io.lettuce.core.AbstractRedisClient.lambda$initializeChannelAsync0$4(AbstractRedisClient.java:329)
			at io.netty.util.concurrent.DefaultPromise.notifyListener0(DefaultPromise.java:577)
			at io.netty.util.concurrent.DefaultPromise.notifyListeners0(DefaultPromise.java:570)
			at io.netty.util.concurrent.DefaultPromise.notifyListenersNow(DefaultPromise.java:549)
			at io.netty.util.concurrent.DefaultPromise.notifyListeners(DefaultPromise.java:490)
			at io.netty.util.concurrent.DefaultPromise.setValue0(DefaultPromise.java:615)
			at io.netty.util.concurrent.DefaultPromise.setFailure0(DefaultPromise.java:608)
			at io.netty.util.concurrent.DefaultPromise.tryFailure(DefaultPromise.java:117)
			at io.netty.channel.nio.AbstractNioChannel$AbstractNioUnsafe.fulfillConnectPromise(AbstractNioChannel.java:321)
			at io.netty.channel.nio.AbstractNioChannel$AbstractNioUnsafe.finishConnect(AbstractNioChannel.java:337)
			at io.netty.channel.nio.NioEventLoop.processSelectedKey(NioEventLoop.java:688)
			at io.netty.channel.nio.NioEventLoop.processSelectedKeysOptimized(NioEventLoop.java:635)
			at io.netty.channel.nio.NioEventLoop.processSelectedKeys(NioEventLoop.java:552)
			at io.netty.channel.nio.NioEventLoop.run(NioEventLoop.java:514)
			... 4 common frames omitted
		Caused by: io.netty.channel.AbstractChannel$AnnotatedNoRouteToHostException: No route to host: no further information: /************:7004
		Caused by: java.net.NoRouteToHostException: No route to host: no further information
			at sun.nio.ch.SocketChannelImpl.checkConnect(Native Method)
			at sun.nio.ch.SocketChannelImpl.finishConnect(SocketChannelImpl.java:717)
			at io.netty.channel.socket.nio.NioSocketChannel.doFinishConnect(NioSocketChannel.java:330)
			at io.netty.channel.nio.AbstractNioChannel$AbstractNioUnsafe.finishConnect(AbstractNioChannel.java:334)
			at io.netty.channel.nio.NioEventLoop.processSelectedKey(NioEventLoop.java:688)
			at io.netty.channel.nio.NioEventLoop.processSelectedKeysOptimized(NioEventLoop.java:635)
			at io.netty.channel.nio.NioEventLoop.processSelectedKeys(NioEventLoop.java:552)
			at io.netty.channel.nio.NioEventLoop.run(NioEventLoop.java:514)
			at io.netty.util.concurrent.SingleThreadEventExecutor$6.run(SingleThreadEventExecutor.java:1050)
			at io.netty.util.internal.ThreadExecutorMap$2.run(ThreadExecutorMap.java:74)
			at io.netty.util.concurrent.FastThreadLocalRunnable.run(FastThreadLocalRunnable.java:30)
			at java.lang.Thread.run(Thread.java:748)
		Suppressed: io.lettuce.core.RedisConnectionException: Unable to connect to [************:7005]: No route to host: no further information: /************:7005
			at io.lettuce.core.cluster.topology.ClusterTopologyRefresh.lambda$getConnections$0(ClusterTopologyRefresh.java:290)
			at java.util.concurrent.CompletableFuture.uniWhenComplete(CompletableFuture.java:760)
			at java.util.concurrent.CompletableFuture$UniWhenComplete.tryFire(CompletableFuture.java:736)
			at java.util.concurrent.CompletableFuture.postComplete(CompletableFuture.java:474)
			at java.util.concurrent.CompletableFuture.completeExceptionally(CompletableFuture.java:1977)
			at io.lettuce.core.AbstractRedisClient.lambda$initializeChannelAsync0$4(AbstractRedisClient.java:329)
			at io.netty.util.concurrent.DefaultPromise.notifyListener0(DefaultPromise.java:577)
			at io.netty.util.concurrent.DefaultPromise.notifyListeners0(DefaultPromise.java:570)
			at io.netty.util.concurrent.DefaultPromise.notifyListenersNow(DefaultPromise.java:549)
			at io.netty.util.concurrent.DefaultPromise.notifyListeners(DefaultPromise.java:490)
			at io.netty.util.concurrent.DefaultPromise.setValue0(DefaultPromise.java:615)
			at io.netty.util.concurrent.DefaultPromise.setFailure0(DefaultPromise.java:608)
			at io.netty.util.concurrent.DefaultPromise.tryFailure(DefaultPromise.java:117)
			at io.netty.channel.nio.AbstractNioChannel$AbstractNioUnsafe.fulfillConnectPromise(AbstractNioChannel.java:321)
			at io.netty.channel.nio.AbstractNioChannel$AbstractNioUnsafe.finishConnect(AbstractNioChannel.java:337)
			at io.netty.channel.nio.NioEventLoop.processSelectedKey(NioEventLoop.java:688)
			at io.netty.channel.nio.NioEventLoop.processSelectedKeysOptimized(NioEventLoop.java:635)
			at io.netty.channel.nio.NioEventLoop.processSelectedKeys(NioEventLoop.java:552)
			at io.netty.channel.nio.NioEventLoop.run(NioEventLoop.java:514)
			... 4 common frames omitted
		Caused by: io.netty.channel.AbstractChannel$AnnotatedNoRouteToHostException: No route to host: no further information: /************:7005
		Caused by: java.net.NoRouteToHostException: No route to host: no further information
			at sun.nio.ch.SocketChannelImpl.checkConnect(Native Method)
			at sun.nio.ch.SocketChannelImpl.finishConnect(SocketChannelImpl.java:717)
			at io.netty.channel.socket.nio.NioSocketChannel.doFinishConnect(NioSocketChannel.java:330)
			at io.netty.channel.nio.AbstractNioChannel$AbstractNioUnsafe.finishConnect(AbstractNioChannel.java:334)
			at io.netty.channel.nio.NioEventLoop.processSelectedKey(NioEventLoop.java:688)
			at io.netty.channel.nio.NioEventLoop.processSelectedKeysOptimized(NioEventLoop.java:635)
			at io.netty.channel.nio.NioEventLoop.processSelectedKeys(NioEventLoop.java:552)
			at io.netty.channel.nio.NioEventLoop.run(NioEventLoop.java:514)
			at io.netty.util.concurrent.SingleThreadEventExecutor$6.run(SingleThreadEventExecutor.java:1050)
			at io.netty.util.internal.ThreadExecutorMap$2.run(ThreadExecutorMap.java:74)
			at io.netty.util.concurrent.FastThreadLocalRunnable.run(FastThreadLocalRunnable.java:30)
			at java.lang.Thread.run(Thread.java:748)
		Suppressed: io.lettuce.core.RedisConnectionException: Unable to connect to [************:7006]: No route to host: no further information: /************:7006
			at io.lettuce.core.cluster.topology.ClusterTopologyRefresh.lambda$getConnections$0(ClusterTopologyRefresh.java:290)
			at java.util.concurrent.CompletableFuture.uniWhenComplete(CompletableFuture.java:760)
			at java.util.concurrent.CompletableFuture$UniWhenComplete.tryFire(CompletableFuture.java:736)
			at java.util.concurrent.CompletableFuture.postComplete(CompletableFuture.java:474)
			at java.util.concurrent.CompletableFuture.completeExceptionally(CompletableFuture.java:1977)
			at io.lettuce.core.AbstractRedisClient.lambda$initializeChannelAsync0$4(AbstractRedisClient.java:329)
			at io.netty.util.concurrent.DefaultPromise.notifyListener0(DefaultPromise.java:577)
			at io.netty.util.concurrent.DefaultPromise.notifyListeners0(DefaultPromise.java:570)
			at io.netty.util.concurrent.DefaultPromise.notifyListenersNow(DefaultPromise.java:549)
			at io.netty.util.concurrent.DefaultPromise.notifyListeners(DefaultPromise.java:490)
			at io.netty.util.concurrent.DefaultPromise.setValue0(DefaultPromise.java:615)
			at io.netty.util.concurrent.DefaultPromise.setFailure0(DefaultPromise.java:608)
			at io.netty.util.concurrent.DefaultPromise.tryFailure(DefaultPromise.java:117)
			at io.netty.channel.nio.AbstractNioChannel$AbstractNioUnsafe.fulfillConnectPromise(AbstractNioChannel.java:321)
			at io.netty.channel.nio.AbstractNioChannel$AbstractNioUnsafe.finishConnect(AbstractNioChannel.java:337)
			at io.netty.channel.nio.NioEventLoop.processSelectedKey(NioEventLoop.java:688)
			at io.netty.channel.nio.NioEventLoop.processSelectedKeysOptimized(NioEventLoop.java:635)
			at io.netty.channel.nio.NioEventLoop.processSelectedKeys(NioEventLoop.java:552)
			at io.netty.channel.nio.NioEventLoop.run(NioEventLoop.java:514)
			... 4 common frames omitted
		Caused by: io.netty.channel.AbstractChannel$AnnotatedNoRouteToHostException: No route to host: no further information: /************:7006
		Caused by: java.net.NoRouteToHostException: No route to host: no further information
			at sun.nio.ch.SocketChannelImpl.checkConnect(Native Method)
			at sun.nio.ch.SocketChannelImpl.finishConnect(SocketChannelImpl.java:717)
			at io.netty.channel.socket.nio.NioSocketChannel.doFinishConnect(NioSocketChannel.java:330)
			at io.netty.channel.nio.AbstractNioChannel$AbstractNioUnsafe.finishConnect(AbstractNioChannel.java:334)
			at io.netty.channel.nio.NioEventLoop.processSelectedKey(NioEventLoop.java:688)
			at io.netty.channel.nio.NioEventLoop.processSelectedKeysOptimized(NioEventLoop.java:635)
			at io.netty.channel.nio.NioEventLoop.processSelectedKeys(NioEventLoop.java:552)
			at io.netty.channel.nio.NioEventLoop.run(NioEventLoop.java:514)
			at io.netty.util.concurrent.SingleThreadEventExecutor$6.run(SingleThreadEventExecutor.java:1050)
			at io.netty.util.internal.ThreadExecutorMap$2.run(ThreadExecutorMap.java:74)
			at io.netty.util.concurrent.FastThreadLocalRunnable.run(FastThreadLocalRunnable.java:30)
			at java.lang.Thread.run(Thread.java:748)
	Caused by: io.lettuce.core.RedisConnectionException: Unable to connect to [************:7001]: No route to host: no further information: /************:7001
		at io.lettuce.core.cluster.topology.ClusterTopologyRefresh.lambda$getConnections$0(ClusterTopologyRefresh.java:290)
		at java.util.concurrent.CompletableFuture.uniWhenComplete(CompletableFuture.java:760)
		at java.util.concurrent.CompletableFuture$UniWhenComplete.tryFire(CompletableFuture.java:736)
		at java.util.concurrent.CompletableFuture.postComplete(CompletableFuture.java:474)
		at java.util.concurrent.CompletableFuture.completeExceptionally(CompletableFuture.java:1977)
		at io.lettuce.core.AbstractRedisClient.lambda$initializeChannelAsync0$4(AbstractRedisClient.java:329)
		at io.netty.util.concurrent.DefaultPromise.notifyListener0(DefaultPromise.java:577)
		at io.netty.util.concurrent.DefaultPromise.notifyListeners0(DefaultPromise.java:570)
		at io.netty.util.concurrent.DefaultPromise.notifyListenersNow(DefaultPromise.java:549)
		at io.netty.util.concurrent.DefaultPromise.notifyListeners(DefaultPromise.java:490)
		at io.netty.util.concurrent.DefaultPromise.setValue0(DefaultPromise.java:615)
		at io.netty.util.concurrent.DefaultPromise.setFailure0(DefaultPromise.java:608)
		at io.netty.util.concurrent.DefaultPromise.tryFailure(DefaultPromise.java:117)
		at io.netty.channel.nio.AbstractNioChannel$AbstractNioUnsafe.fulfillConnectPromise(AbstractNioChannel.java:321)
		at io.netty.channel.nio.AbstractNioChannel$AbstractNioUnsafe.finishConnect(AbstractNioChannel.java:337)
		at io.netty.channel.nio.NioEventLoop.processSelectedKey(NioEventLoop.java:688)
		at io.netty.channel.nio.NioEventLoop.processSelectedKeysOptimized(NioEventLoop.java:635)
		at io.netty.channel.nio.NioEventLoop.processSelectedKeys(NioEventLoop.java:552)
		at io.netty.channel.nio.NioEventLoop.run(NioEventLoop.java:514)
		... 4 common frames omitted
	Caused by: io.netty.channel.AbstractChannel$AnnotatedNoRouteToHostException: No route to host: no further information: /************:7001
	Caused by: java.net.NoRouteToHostException: No route to host: no further information
		at sun.nio.ch.SocketChannelImpl.checkConnect(Native Method)
		at sun.nio.ch.SocketChannelImpl.finishConnect(SocketChannelImpl.java:717)
		at io.netty.channel.socket.nio.NioSocketChannel.doFinishConnect(NioSocketChannel.java:330)
		at io.netty.channel.nio.AbstractNioChannel$AbstractNioUnsafe.finishConnect(AbstractNioChannel.java:334)
		at io.netty.channel.nio.NioEventLoop.processSelectedKey(NioEventLoop.java:688)
		at io.netty.channel.nio.NioEventLoop.processSelectedKeysOptimized(NioEventLoop.java:635)
		at io.netty.channel.nio.NioEventLoop.processSelectedKeys(NioEventLoop.java:552)
		at io.netty.channel.nio.NioEventLoop.run(NioEventLoop.java:514)
		at io.netty.util.concurrent.SingleThreadEventExecutor$6.run(SingleThreadEventExecutor.java:1050)
		at io.netty.util.internal.ThreadExecutorMap$2.run(ThreadExecutorMap.java:74)
		at io.netty.util.concurrent.FastThreadLocalRunnable.run(FastThreadLocalRunnable.java:30)
		at java.lang.Thread.run(Thread.java:748)
Caused by: io.lettuce.core.RedisConnectionException: Unable to establish a connection to Redis Cluster at [RedisURI [host='************', port=7001], RedisURI [host='************', port=7002], RedisURI [host='************', port=7003], RedisURI [host='************', port=7004], RedisURI [host='************', port=7005], RedisURI [host='************', port=7006]]
	at io.lettuce.core.cluster.topology.AsyncConnections.get(AsyncConnections.java:89)
	at io.lettuce.core.cluster.topology.ClusterTopologyRefresh.loadViews(ClusterTopologyRefresh.java:76)
	at io.lettuce.core.cluster.RedisClusterClient.doLoadPartitions(RedisClusterClient.java:865)
	at io.lettuce.core.cluster.RedisClusterClient.loadPartitions(RedisClusterClient.java:845)
	... 10 common frames omitted
	Suppressed: io.lettuce.core.RedisConnectionException: Unable to connect to [************:7006]: No route to host: no further information: /************:7006
		at io.lettuce.core.cluster.topology.ClusterTopologyRefresh.lambda$getConnections$0(ClusterTopologyRefresh.java:290)
		at java.util.concurrent.CompletableFuture.uniWhenComplete(CompletableFuture.java:760)
		at java.util.concurrent.CompletableFuture$UniWhenComplete.tryFire(CompletableFuture.java:736)
		at java.util.concurrent.CompletableFuture.postComplete(CompletableFuture.java:474)
		at java.util.concurrent.CompletableFuture.completeExceptionally(CompletableFuture.java:1977)
		at io.lettuce.core.AbstractRedisClient.lambda$initializeChannelAsync0$4(AbstractRedisClient.java:329)
		at io.netty.util.concurrent.DefaultPromise.notifyListener0(DefaultPromise.java:577)
		at io.netty.util.concurrent.DefaultPromise.notifyListeners0(DefaultPromise.java:570)
		at io.netty.util.concurrent.DefaultPromise.notifyListenersNow(DefaultPromise.java:549)
		at io.netty.util.concurrent.DefaultPromise.notifyListeners(DefaultPromise.java:490)
		at io.netty.util.concurrent.DefaultPromise.setValue0(DefaultPromise.java:615)
		at io.netty.util.concurrent.DefaultPromise.setFailure0(DefaultPromise.java:608)
		at io.netty.util.concurrent.DefaultPromise.tryFailure(DefaultPromise.java:117)
		at io.netty.channel.nio.AbstractNioChannel$AbstractNioUnsafe.fulfillConnectPromise(AbstractNioChannel.java:321)
		at io.netty.channel.nio.AbstractNioChannel$AbstractNioUnsafe.finishConnect(AbstractNioChannel.java:337)
		at io.netty.channel.nio.NioEventLoop.processSelectedKey(NioEventLoop.java:688)
		at io.netty.channel.nio.NioEventLoop.processSelectedKeysOptimized(NioEventLoop.java:635)
		at io.netty.channel.nio.NioEventLoop.processSelectedKeys(NioEventLoop.java:552)
		at io.netty.channel.nio.NioEventLoop.run(NioEventLoop.java:514)
		... 4 common frames omitted
	Caused by: io.netty.channel.AbstractChannel$AnnotatedNoRouteToHostException: No route to host: no further information: /************:7006
	Caused by: java.net.NoRouteToHostException: No route to host: no further information
		at sun.nio.ch.SocketChannelImpl.checkConnect(Native Method)
		at sun.nio.ch.SocketChannelImpl.finishConnect(SocketChannelImpl.java:717)
		at io.netty.channel.socket.nio.NioSocketChannel.doFinishConnect(NioSocketChannel.java:330)
		at io.netty.channel.nio.AbstractNioChannel$AbstractNioUnsafe.finishConnect(AbstractNioChannel.java:334)
		at io.netty.channel.nio.NioEventLoop.processSelectedKey(NioEventLoop.java:688)
		at io.netty.channel.nio.NioEventLoop.processSelectedKeysOptimized(NioEventLoop.java:635)
		at io.netty.channel.nio.NioEventLoop.processSelectedKeys(NioEventLoop.java:552)
		at io.netty.channel.nio.NioEventLoop.run(NioEventLoop.java:514)
		at io.netty.util.concurrent.SingleThreadEventExecutor$6.run(SingleThreadEventExecutor.java:1050)
		at io.netty.util.internal.ThreadExecutorMap$2.run(ThreadExecutorMap.java:74)
		at io.netty.util.concurrent.FastThreadLocalRunnable.run(FastThreadLocalRunnable.java:30)
		at java.lang.Thread.run(Thread.java:748)
	Suppressed: io.lettuce.core.RedisConnectionException: Unable to connect to [************:7005]: No route to host: no further information: /************:7005
		at io.lettuce.core.cluster.topology.ClusterTopologyRefresh.lambda$getConnections$0(ClusterTopologyRefresh.java:290)
		at java.util.concurrent.CompletableFuture.uniWhenComplete(CompletableFuture.java:760)
		at java.util.concurrent.CompletableFuture$UniWhenComplete.tryFire(CompletableFuture.java:736)
		at java.util.concurrent.CompletableFuture.postComplete(CompletableFuture.java:474)
		at java.util.concurrent.CompletableFuture.completeExceptionally(CompletableFuture.java:1977)
		at io.lettuce.core.AbstractRedisClient.lambda$initializeChannelAsync0$4(AbstractRedisClient.java:329)
		at io.netty.util.concurrent.DefaultPromise.notifyListener0(DefaultPromise.java:577)
		at io.netty.util.concurrent.DefaultPromise.notifyListeners0(DefaultPromise.java:570)
		at io.netty.util.concurrent.DefaultPromise.notifyListenersNow(DefaultPromise.java:549)
		at io.netty.util.concurrent.DefaultPromise.notifyListeners(DefaultPromise.java:490)
		at io.netty.util.concurrent.DefaultPromise.setValue0(DefaultPromise.java:615)
		at io.netty.util.concurrent.DefaultPromise.setFailure0(DefaultPromise.java:608)
		at io.netty.util.concurrent.DefaultPromise.tryFailure(DefaultPromise.java:117)
		at io.netty.channel.nio.AbstractNioChannel$AbstractNioUnsafe.fulfillConnectPromise(AbstractNioChannel.java:321)
		at io.netty.channel.nio.AbstractNioChannel$AbstractNioUnsafe.finishConnect(AbstractNioChannel.java:337)
		at io.netty.channel.nio.NioEventLoop.processSelectedKey(NioEventLoop.java:688)
		at io.netty.channel.nio.NioEventLoop.processSelectedKeysOptimized(NioEventLoop.java:635)
		at io.netty.channel.nio.NioEventLoop.processSelectedKeys(NioEventLoop.java:552)
		at io.netty.channel.nio.NioEventLoop.run(NioEventLoop.java:514)
		... 4 common frames omitted
	Caused by: io.netty.channel.AbstractChannel$AnnotatedNoRouteToHostException: No route to host: no further information: /************:7005
	Caused by: java.net.NoRouteToHostException: No route to host: no further information
		at sun.nio.ch.SocketChannelImpl.checkConnect(Native Method)
		at sun.nio.ch.SocketChannelImpl.finishConnect(SocketChannelImpl.java:717)
		at io.netty.channel.socket.nio.NioSocketChannel.doFinishConnect(NioSocketChannel.java:330)
		at io.netty.channel.nio.AbstractNioChannel$AbstractNioUnsafe.finishConnect(AbstractNioChannel.java:334)
		at io.netty.channel.nio.NioEventLoop.processSelectedKey(NioEventLoop.java:688)
		at io.netty.channel.nio.NioEventLoop.processSelectedKeysOptimized(NioEventLoop.java:635)
		at io.netty.channel.nio.NioEventLoop.processSelectedKeys(NioEventLoop.java:552)
		at io.netty.channel.nio.NioEventLoop.run(NioEventLoop.java:514)
		at io.netty.util.concurrent.SingleThreadEventExecutor$6.run(SingleThreadEventExecutor.java:1050)
		at io.netty.util.internal.ThreadExecutorMap$2.run(ThreadExecutorMap.java:74)
		at io.netty.util.concurrent.FastThreadLocalRunnable.run(FastThreadLocalRunnable.java:30)
		at java.lang.Thread.run(Thread.java:748)
	Suppressed: io.lettuce.core.RedisConnectionException: Unable to connect to [************:7002]: No route to host: no further information: /************:7002
		at io.lettuce.core.cluster.topology.ClusterTopologyRefresh.lambda$getConnections$0(ClusterTopologyRefresh.java:290)
		at java.util.concurrent.CompletableFuture.uniWhenComplete(CompletableFuture.java:760)
		at java.util.concurrent.CompletableFuture$UniWhenComplete.tryFire(CompletableFuture.java:736)
		at java.util.concurrent.CompletableFuture.postComplete(CompletableFuture.java:474)
		at java.util.concurrent.CompletableFuture.completeExceptionally(CompletableFuture.java:1977)
		at io.lettuce.core.AbstractRedisClient.lambda$initializeChannelAsync0$4(AbstractRedisClient.java:329)
		at io.netty.util.concurrent.DefaultPromise.notifyListener0(DefaultPromise.java:577)
		at io.netty.util.concurrent.DefaultPromise.notifyListeners0(DefaultPromise.java:570)
		at io.netty.util.concurrent.DefaultPromise.notifyListenersNow(DefaultPromise.java:549)
		at io.netty.util.concurrent.DefaultPromise.notifyListeners(DefaultPromise.java:490)
		at io.netty.util.concurrent.DefaultPromise.setValue0(DefaultPromise.java:615)
		at io.netty.util.concurrent.DefaultPromise.setFailure0(DefaultPromise.java:608)
		at io.netty.util.concurrent.DefaultPromise.tryFailure(DefaultPromise.java:117)
		at io.netty.channel.nio.AbstractNioChannel$AbstractNioUnsafe.fulfillConnectPromise(AbstractNioChannel.java:321)
		at io.netty.channel.nio.AbstractNioChannel$AbstractNioUnsafe.finishConnect(AbstractNioChannel.java:337)
		at io.netty.channel.nio.NioEventLoop.processSelectedKey(NioEventLoop.java:688)
		at io.netty.channel.nio.NioEventLoop.processSelectedKeysOptimized(NioEventLoop.java:635)
		at io.netty.channel.nio.NioEventLoop.processSelectedKeys(NioEventLoop.java:552)
		at io.netty.channel.nio.NioEventLoop.run(NioEventLoop.java:514)
		... 4 common frames omitted
	Caused by: io.netty.channel.AbstractChannel$AnnotatedNoRouteToHostException: No route to host: no further information: /************:7002
	Caused by: java.net.NoRouteToHostException: No route to host: no further information
		at sun.nio.ch.SocketChannelImpl.checkConnect(Native Method)
		at sun.nio.ch.SocketChannelImpl.finishConnect(SocketChannelImpl.java:717)
		at io.netty.channel.socket.nio.NioSocketChannel.doFinishConnect(NioSocketChannel.java:330)
		at io.netty.channel.nio.AbstractNioChannel$AbstractNioUnsafe.finishConnect(AbstractNioChannel.java:334)
		at io.netty.channel.nio.NioEventLoop.processSelectedKey(NioEventLoop.java:688)
		at io.netty.channel.nio.NioEventLoop.processSelectedKeysOptimized(NioEventLoop.java:635)
		at io.netty.channel.nio.NioEventLoop.processSelectedKeys(NioEventLoop.java:552)
		at io.netty.channel.nio.NioEventLoop.run(NioEventLoop.java:514)
		at io.netty.util.concurrent.SingleThreadEventExecutor$6.run(SingleThreadEventExecutor.java:1050)
		at io.netty.util.internal.ThreadExecutorMap$2.run(ThreadExecutorMap.java:74)
		at io.netty.util.concurrent.FastThreadLocalRunnable.run(FastThreadLocalRunnable.java:30)
		at java.lang.Thread.run(Thread.java:748)
	Suppressed: io.lettuce.core.RedisConnectionException: Unable to connect to [************:7003]: No route to host: no further information: /************:7003
		at io.lettuce.core.cluster.topology.ClusterTopologyRefresh.lambda$getConnections$0(ClusterTopologyRefresh.java:290)
		at java.util.concurrent.CompletableFuture.uniWhenComplete(CompletableFuture.java:760)
		at java.util.concurrent.CompletableFuture$UniWhenComplete.tryFire(CompletableFuture.java:736)
		at java.util.concurrent.CompletableFuture.postComplete(CompletableFuture.java:474)
		at java.util.concurrent.CompletableFuture.completeExceptionally(CompletableFuture.java:1977)
		at io.lettuce.core.AbstractRedisClient.lambda$initializeChannelAsync0$4(AbstractRedisClient.java:329)
		at io.netty.util.concurrent.DefaultPromise.notifyListener0(DefaultPromise.java:577)
		at io.netty.util.concurrent.DefaultPromise.notifyListeners0(DefaultPromise.java:570)
		at io.netty.util.concurrent.DefaultPromise.notifyListenersNow(DefaultPromise.java:549)
		at io.netty.util.concurrent.DefaultPromise.notifyListeners(DefaultPromise.java:490)
		at io.netty.util.concurrent.DefaultPromise.setValue0(DefaultPromise.java:615)
		at io.netty.util.concurrent.DefaultPromise.setFailure0(DefaultPromise.java:608)
		at io.netty.util.concurrent.DefaultPromise.tryFailure(DefaultPromise.java:117)
		at io.netty.channel.nio.AbstractNioChannel$AbstractNioUnsafe.fulfillConnectPromise(AbstractNioChannel.java:321)
		at io.netty.channel.nio.AbstractNioChannel$AbstractNioUnsafe.finishConnect(AbstractNioChannel.java:337)
		at io.netty.channel.nio.NioEventLoop.processSelectedKey(NioEventLoop.java:688)
		at io.netty.channel.nio.NioEventLoop.processSelectedKeysOptimized(NioEventLoop.java:635)
		at io.netty.channel.nio.NioEventLoop.processSelectedKeys(NioEventLoop.java:552)
		at io.netty.channel.nio.NioEventLoop.run(NioEventLoop.java:514)
		... 4 common frames omitted
	Caused by: io.netty.channel.AbstractChannel$AnnotatedNoRouteToHostException: No route to host: no further information: /************:7003
	Caused by: java.net.NoRouteToHostException: No route to host: no further information
		at sun.nio.ch.SocketChannelImpl.checkConnect(Native Method)
		at sun.nio.ch.SocketChannelImpl.finishConnect(SocketChannelImpl.java:717)
		at io.netty.channel.socket.nio.NioSocketChannel.doFinishConnect(NioSocketChannel.java:330)
		at io.netty.channel.nio.AbstractNioChannel$AbstractNioUnsafe.finishConnect(AbstractNioChannel.java:334)
		at io.netty.channel.nio.NioEventLoop.processSelectedKey(NioEventLoop.java:688)
		at io.netty.channel.nio.NioEventLoop.processSelectedKeysOptimized(NioEventLoop.java:635)
		at io.netty.channel.nio.NioEventLoop.processSelectedKeys(NioEventLoop.java:552)
		at io.netty.channel.nio.NioEventLoop.run(NioEventLoop.java:514)
		at io.netty.util.concurrent.SingleThreadEventExecutor$6.run(SingleThreadEventExecutor.java:1050)
		at io.netty.util.internal.ThreadExecutorMap$2.run(ThreadExecutorMap.java:74)
		at io.netty.util.concurrent.FastThreadLocalRunnable.run(FastThreadLocalRunnable.java:30)
		at java.lang.Thread.run(Thread.java:748)
	Suppressed: io.lettuce.core.RedisConnectionException: Unable to connect to [************:7001]: No route to host: no further information: /************:7001
		at io.lettuce.core.cluster.topology.ClusterTopologyRefresh.lambda$getConnections$0(ClusterTopologyRefresh.java:290)
		at java.util.concurrent.CompletableFuture.uniWhenComplete(CompletableFuture.java:760)
		at java.util.concurrent.CompletableFuture$UniWhenComplete.tryFire(CompletableFuture.java:736)
		at java.util.concurrent.CompletableFuture.postComplete(CompletableFuture.java:474)
		at java.util.concurrent.CompletableFuture.completeExceptionally(CompletableFuture.java:1977)
		at io.lettuce.core.AbstractRedisClient.lambda$initializeChannelAsync0$4(AbstractRedisClient.java:329)
		at io.netty.util.concurrent.DefaultPromise.notifyListener0(DefaultPromise.java:577)
		at io.netty.util.concurrent.DefaultPromise.notifyListeners0(DefaultPromise.java:570)
		at io.netty.util.concurrent.DefaultPromise.notifyListenersNow(DefaultPromise.java:549)
		at io.netty.util.concurrent.DefaultPromise.notifyListeners(DefaultPromise.java:490)
		at io.netty.util.concurrent.DefaultPromise.setValue0(DefaultPromise.java:615)
		at io.netty.util.concurrent.DefaultPromise.setFailure0(DefaultPromise.java:608)
		at io.netty.util.concurrent.DefaultPromise.tryFailure(DefaultPromise.java:117)
		at io.netty.channel.nio.AbstractNioChannel$AbstractNioUnsafe.fulfillConnectPromise(AbstractNioChannel.java:321)
		at io.netty.channel.nio.AbstractNioChannel$AbstractNioUnsafe.finishConnect(AbstractNioChannel.java:337)
		at io.netty.channel.nio.NioEventLoop.processSelectedKey(NioEventLoop.java:688)
		at io.netty.channel.nio.NioEventLoop.processSelectedKeysOptimized(NioEventLoop.java:635)
		at io.netty.channel.nio.NioEventLoop.processSelectedKeys(NioEventLoop.java:552)
		at io.netty.channel.nio.NioEventLoop.run(NioEventLoop.java:514)
		... 4 common frames omitted
	Caused by: io.netty.channel.AbstractChannel$AnnotatedNoRouteToHostException: No route to host: no further information: /************:7001
	Caused by: java.net.NoRouteToHostException: No route to host: no further information
		at sun.nio.ch.SocketChannelImpl.checkConnect(Native Method)
		at sun.nio.ch.SocketChannelImpl.finishConnect(SocketChannelImpl.java:717)
		at io.netty.channel.socket.nio.NioSocketChannel.doFinishConnect(NioSocketChannel.java:330)
		at io.netty.channel.nio.AbstractNioChannel$AbstractNioUnsafe.finishConnect(AbstractNioChannel.java:334)
		at io.netty.channel.nio.NioEventLoop.processSelectedKey(NioEventLoop.java:688)
		at io.netty.channel.nio.NioEventLoop.processSelectedKeysOptimized(NioEventLoop.java:635)
		at io.netty.channel.nio.NioEventLoop.processSelectedKeys(NioEventLoop.java:552)
		at io.netty.channel.nio.NioEventLoop.run(NioEventLoop.java:514)
		at io.netty.util.concurrent.SingleThreadEventExecutor$6.run(SingleThreadEventExecutor.java:1050)
		at io.netty.util.internal.ThreadExecutorMap$2.run(ThreadExecutorMap.java:74)
		at io.netty.util.concurrent.FastThreadLocalRunnable.run(FastThreadLocalRunnable.java:30)
		at java.lang.Thread.run(Thread.java:748)
Caused by: io.lettuce.core.RedisConnectionException: Unable to connect to [************:7004]: No route to host: no further information: /************:7004
	at io.lettuce.core.cluster.topology.ClusterTopologyRefresh.lambda$getConnections$0(ClusterTopologyRefresh.java:290)
	at java.util.concurrent.CompletableFuture.uniWhenComplete(CompletableFuture.java:760)
	at java.util.concurrent.CompletableFuture$UniWhenComplete.tryFire(CompletableFuture.java:736)
	at java.util.concurrent.CompletableFuture.postComplete(CompletableFuture.java:474)
	at java.util.concurrent.CompletableFuture.completeExceptionally(CompletableFuture.java:1977)
	at io.lettuce.core.AbstractRedisClient.lambda$initializeChannelAsync0$4(AbstractRedisClient.java:329)
	at io.netty.util.concurrent.DefaultPromise.notifyListener0(DefaultPromise.java:577)
	at io.netty.util.concurrent.DefaultPromise.notifyListeners0(DefaultPromise.java:570)
	at io.netty.util.concurrent.DefaultPromise.notifyListenersNow(DefaultPromise.java:549)
	at io.netty.util.concurrent.DefaultPromise.notifyListeners(DefaultPromise.java:490)
	at io.netty.util.concurrent.DefaultPromise.setValue0(DefaultPromise.java:615)
	at io.netty.util.concurrent.DefaultPromise.setFailure0(DefaultPromise.java:608)
	at io.netty.util.concurrent.DefaultPromise.tryFailure(DefaultPromise.java:117)
	at io.netty.channel.nio.AbstractNioChannel$AbstractNioUnsafe.fulfillConnectPromise(AbstractNioChannel.java:321)
	at io.netty.channel.nio.AbstractNioChannel$AbstractNioUnsafe.finishConnect(AbstractNioChannel.java:337)
	at io.netty.channel.nio.NioEventLoop.processSelectedKey(NioEventLoop.java:688)
	at io.netty.channel.nio.NioEventLoop.processSelectedKeysOptimized(NioEventLoop.java:635)
	at io.netty.channel.nio.NioEventLoop.processSelectedKeys(NioEventLoop.java:552)
	at io.netty.channel.nio.NioEventLoop.run(NioEventLoop.java:514)
	... 4 common frames omitted
Caused by: io.netty.channel.AbstractChannel$AnnotatedNoRouteToHostException: No route to host: no further information: /************:7004
Caused by: java.net.NoRouteToHostException: No route to host: no further information
	at sun.nio.ch.SocketChannelImpl.checkConnect(Native Method)
	at sun.nio.ch.SocketChannelImpl.finishConnect(SocketChannelImpl.java:717)
	at io.netty.channel.socket.nio.NioSocketChannel.doFinishConnect(NioSocketChannel.java:330)
	at io.netty.channel.nio.AbstractNioChannel$AbstractNioUnsafe.finishConnect(AbstractNioChannel.java:334)
	at io.netty.channel.nio.NioEventLoop.processSelectedKey(NioEventLoop.java:688)
	at io.netty.channel.nio.NioEventLoop.processSelectedKeysOptimized(NioEventLoop.java:635)
	at io.netty.channel.nio.NioEventLoop.processSelectedKeys(NioEventLoop.java:552)
	at io.netty.channel.nio.NioEventLoop.run(NioEventLoop.java:514)
	at io.netty.util.concurrent.SingleThreadEventExecutor$6.run(SingleThreadEventExecutor.java:1050)
	at io.netty.util.internal.ThreadExecutorMap$2.run(ThreadExecutorMap.java:74)
	at io.netty.util.concurrent.FastThreadLocalRunnable.run(FastThreadLocalRunnable.java:30)
	at java.lang.Thread.run(Thread.java:748)
2025-05-26 13:13:39.154 [AMQP Connection ************:5672] WARN  com.rabbitmq.client.impl.ForgivingExceptionHandler.log Line:115 - An unexpected connection driver error occured (Exception message: Connection reset)
2025-05-26 13:13:45.936 [AMQP Connection ************:5672] WARN  com.rabbitmq.client.impl.ForgivingExceptionHandler.log Line:115 - An unexpected connection driver error occured (Exception message: Connection reset)
2025-05-26 13:13:45.951 [AMQP Connection ************:5672] WARN  com.rabbitmq.client.impl.ForgivingExceptionHandler.log Line:115 - An unexpected connection driver error occured (Exception message: Connection reset)
2025-05-26 13:13:45.997 [AMQP Connection ************:5672] WARN  com.rabbitmq.client.impl.ForgivingExceptionHandler.log Line:115 - An unexpected connection driver error occured (Exception message: Connection reset)
2025-05-26 13:54:09.491 [AMQP Connection ************:5672] WARN  com.rabbitmq.client.impl.ForgivingExceptionHandler.log Line:115 - An unexpected connection driver error occured (Exception message: Connection reset)
2025-05-26 13:54:16.560 [AMQP Connection ************:5672] WARN  com.rabbitmq.client.impl.ForgivingExceptionHandler.log Line:115 - An unexpected connection driver error occured (Exception message: Connection reset)
2025-05-26 13:54:16.575 [AMQP Connection ************:5672] WARN  com.rabbitmq.client.impl.ForgivingExceptionHandler.log Line:115 - An unexpected connection driver error occured (Exception message: Connection reset)
2025-05-26 13:54:16.591 [AMQP Connection ************:5672] WARN  com.rabbitmq.client.impl.ForgivingExceptionHandler.log Line:115 - An unexpected connection driver error occured (Exception message: Connection reset)
2025-05-26 14:09:24.813 [http-nio-8092-exec-7] WARN  com.simbest.boot.util.AppFileUtil.getFileName Line:218 - 文件URL路径【壁纸.jpg】无法通过URL获取路径，直接通过工具类提取文件全称为【壁纸.jpg】
2025-05-26 14:09:24.814 [http-nio-8092-exec-7] WARN  com.simbest.boot.util.AppFileUtil.getFileSuffix Line:274 - 文件URL路径【壁纸.jpg】无法通过URL获取路径，直接通过工具类提取文件后缀为【jpg】
2025-05-26 14:09:24.818 [http-nio-8092-exec-7] WARN  com.simbest.boot.util.AppFileUtil.getFileBaseName Line:251 - 文件URL路径【壁纸.jpg】无法通过URL获取路径，直接通过工具类提取文件名称为【壁纸】
2025-05-26 14:09:24.818 [http-nio-8092-exec-7] WARN  com.simbest.boot.util.AppFileUtil.getFileSuffix Line:274 - 文件URL路径【壁纸.jpg】无法通过URL获取路径，直接通过工具类提取文件后缀为【jpg】
2025-05-26 14:09:24.832 [http-nio-8092-exec-7] WARN  com.simbest.boot.util.AppFileUtil.getFileSuffix Line:274 - 文件URL路径【壁纸.jpg】无法通过URL获取路径，直接通过工具类提取文件后缀为【jpg】
2025-05-26 14:10:25.175 [http-nio-8092-exec-18] WARN  com.simbest.boot.util.AppFileUtil.getFileName Line:218 - 文件URL路径【壁纸.jpg】无法通过URL获取路径，直接通过工具类提取文件全称为【壁纸.jpg】
2025-05-26 14:10:25.175 [http-nio-8092-exec-18] WARN  com.simbest.boot.util.AppFileUtil.getFileSuffix Line:274 - 文件URL路径【壁纸.jpg】无法通过URL获取路径，直接通过工具类提取文件后缀为【jpg】
2025-05-26 14:10:25.177 [http-nio-8092-exec-18] WARN  com.simbest.boot.util.AppFileUtil.getFileBaseName Line:251 - 文件URL路径【壁纸.jpg】无法通过URL获取路径，直接通过工具类提取文件名称为【壁纸】
2025-05-26 14:10:25.178 [http-nio-8092-exec-18] WARN  com.simbest.boot.util.AppFileUtil.getFileSuffix Line:274 - 文件URL路径【壁纸.jpg】无法通过URL获取路径，直接通过工具类提取文件后缀为【jpg】
2025-05-26 14:10:25.190 [http-nio-8092-exec-18] WARN  com.simbest.boot.util.AppFileUtil.getFileSuffix Line:274 - 文件URL路径【壁纸.jpg】无法通过URL获取路径，直接通过工具类提取文件后缀为【jpg】
2025-05-26 14:10:35.023 [http-nio-8092-exec-9] WARN  com.simbest.boot.bps.process.operate.WfProcessManager.startProcessAndSetRelativeData Line:87 - 流程启动调用BPS流程启动接口用时：3805ms
2025-05-26 14:10:35.527 [http-nio-8092-exec-9] WARN  com.simbest.boot.bps.process.operate.WfProcessManager.startProcessAndSetRelativeData Line:107 - 【BPS Driver】流程启动第一个工作项的状态：【10】
2025-05-26 14:10:35.527 [http-nio-8092-exec-9] WARN  com.simbest.boot.bps.process.operate.WfProcessManager.startProcessAndSetRelativeData Line:118 - 流程启动调用BPS流程工作项查询接口用时：493ms
2025-05-26 14:10:35.592 [http-nio-8092-exec-9] WARN  com.simbest.boot.bps.process.operate.WorkItemManager.finishWorkItemWithRelativeData Line:169 - 流程下一步传递的参数为：{"code":"E1444456792337649664","currentUserCode":"chenhong","inputUserId":"kongzhijie","appCode":"hnjjwz","title":"23434","receiptId":"U869220140342370304","outcome":"hnjjwz.depart_manager"}
2025-05-26 14:10:36.260 [http-nio-8092-exec-9] WARN  com.simbest.boot.bps.unifiedtodo.service.impl.CloseUnifiedTodoServiceImpl.closeTodo Line:68 - 统一待办核销状态：Y
2025-05-26 14:10:36.263 [http-nio-8092-exec-9] WARN  com.simbest.boot.bps.common.HttpNagentDataUtil.queryAgent Line:76 - 获取接口配置对象1：SimpleConfig(id=AC486600246105735169, address=http://************:8088/ntydl, interfaceStyle=ntydl, isOpen=true, isInner=true, provider=北京晟壁)
2025-05-26 14:10:36.264 [http-nio-8092-exec-9] WARN  com.simbest.boot.bps.common.HttpNagentDataUtil.queryAgent Line:80 - 获取接口配置对象2：SimpleConfig(id=AC486600246105735169, address=http://************:8088/ntydl, interfaceStyle=ntydl, isOpen=true, isInner=true, provider=北京晟壁)
2025-05-26 14:10:36.730 [http-nio-8092-exec-9] WARN  com.simbest.boot.bps.process.operate.WorkItemManager.finishWorkItemWithRelativeData Line:264 - 下一个审批人是：kongzhijie，代理人为：[]
2025-05-26 14:10:36.730 [http-nio-8092-exec-9] WARN  com.simbest.boot.bps.process.operate.WorkItemManager.finishWorkItemWithRelativeData Line:275 - 流程提交下一步处理代理人用时：1116ms
2025-05-26 14:10:41.822 [http-nio-8092-exec-9] WARN  com.simbest.boot.bps.process.operate.WorkItemManager.finishWorkItemWithRelativeData Line:293 - 流程提交下一步调用BPS流程引擎提交下一步接口用时：5091ms
2025-05-26 14:19:20.573 [http-nio-8092-exec-3] WARN  com.simbest.exclude.web.GlobalErrorController.logErrorInformation Line:160 - 请注意所访问URL【/hnjjwz/iconfont/iconfont.css】发生404错误，资源地址不存在！
2025-05-26 14:28:17.820 [http-nio-8092-exec-1] WARN  com.simbest.boot.bps.process.operate.WfProcessManager.startProcessAndSetRelativeData Line:87 - 流程启动调用BPS流程启动接口用时：3847ms
2025-05-26 14:28:18.214 [http-nio-8092-exec-1] WARN  com.simbest.boot.bps.process.operate.WfProcessManager.startProcessAndSetRelativeData Line:107 - 【BPS Driver】流程启动第一个工作项的状态：【10】
2025-05-26 14:28:18.215 [http-nio-8092-exec-1] WARN  com.simbest.boot.bps.process.operate.WfProcessManager.startProcessAndSetRelativeData Line:118 - 流程启动调用BPS流程工作项查询接口用时：392ms
2025-05-26 14:28:18.439 [http-nio-8092-exec-1] WARN  com.simbest.boot.bps.process.operate.WorkItemManager.finishWorkItemWithRelativeData Line:169 - 流程下一步传递的参数为：{"code":"A1444461250559840256","currentUserCode":"chenhong","inputUserId":"kongzhijie","appCode":"hnjjwz","title":"栏目起草","receiptId":"U869224598501646336","outcome":"hnjjwz.depart_manager"}
2025-05-26 14:28:18.660 [http-nio-8092-exec-1] WARN  com.simbest.boot.bps.unifiedtodo.service.impl.CloseUnifiedTodoServiceImpl.closeTodo Line:68 - 统一待办核销状态：Y
2025-05-26 14:28:18.661 [http-nio-8092-exec-1] WARN  com.simbest.boot.bps.common.HttpNagentDataUtil.queryAgent Line:76 - 获取接口配置对象1：SimpleConfig(id=AC486600246105735169, address=http://************:8088/ntydl, interfaceStyle=ntydl, isOpen=true, isInner=true, provider=北京晟壁)
2025-05-26 14:28:18.663 [http-nio-8092-exec-1] WARN  com.simbest.boot.bps.common.HttpNagentDataUtil.queryAgent Line:80 - 获取接口配置对象2：SimpleConfig(id=AC486600246105735169, address=http://************:8088/ntydl, interfaceStyle=ntydl, isOpen=true, isInner=true, provider=北京晟壁)
2025-05-26 14:28:19.147 [http-nio-8092-exec-1] WARN  com.simbest.boot.bps.process.operate.WorkItemManager.finishWorkItemWithRelativeData Line:264 - 下一个审批人是：kongzhijie，代理人为：[]
2025-05-26 14:28:19.148 [http-nio-8092-exec-1] WARN  com.simbest.boot.bps.process.operate.WorkItemManager.finishWorkItemWithRelativeData Line:275 - 流程提交下一步处理代理人用时：707ms
2025-05-26 14:28:24.325 [http-nio-8092-exec-1] WARN  com.simbest.boot.bps.process.operate.WorkItemManager.finishWorkItemWithRelativeData Line:293 - 流程提交下一步调用BPS流程引擎提交下一步接口用时：5177ms
2025-05-26 14:28:53.816 [http-nio-8092-exec-20] WARN  com.simbest.boot.bps.process.operate.WorkItemManager.finishWorkItemWithRelativeData Line:169 - 流程下一步传递的参数为：{"code":"A1444461250559840256","currentUserCode":"kongzhijie","inputUserId":"chenhong","appCode":"hnjjwz","title":"栏目起草","receiptId":"U869224598501646336","outcome":"return"}
2025-05-26 14:28:54.114 [http-nio-8092-exec-20] WARN  com.simbest.boot.bps.unifiedtodo.service.impl.CloseUnifiedTodoServiceImpl.closeTodo Line:68 - 统一待办核销状态：Y
2025-05-26 14:28:54.115 [http-nio-8092-exec-20] WARN  com.simbest.boot.bps.common.HttpNagentDataUtil.queryAgent Line:76 - 获取接口配置对象1：SimpleConfig(id=AC486600246105735169, address=http://************:8088/ntydl, interfaceStyle=ntydl, isOpen=true, isInner=true, provider=北京晟壁)
2025-05-26 14:28:54.115 [http-nio-8092-exec-20] WARN  com.simbest.boot.bps.common.HttpNagentDataUtil.queryAgent Line:80 - 获取接口配置对象2：SimpleConfig(id=AC486600246105735169, address=http://************:8088/ntydl, interfaceStyle=ntydl, isOpen=true, isInner=true, provider=北京晟壁)
2025-05-26 14:28:54.567 [http-nio-8092-exec-20] WARN  com.simbest.boot.bps.process.operate.WorkItemManager.finishWorkItemWithRelativeData Line:264 - 下一个审批人是：chenhong，代理人为：[]
2025-05-26 14:28:54.569 [http-nio-8092-exec-20] WARN  com.simbest.boot.bps.process.operate.WorkItemManager.finishWorkItemWithRelativeData Line:275 - 流程提交下一步处理代理人用时：752ms
2025-05-26 14:29:00.620 [http-nio-8092-exec-20] WARN  com.simbest.boot.bps.process.operate.WorkItemManager.finishWorkItemWithRelativeData Line:293 - 流程提交下一步调用BPS流程引擎提交下一步接口用时：6050ms
2025-05-26 14:29:05.669 [http-nio-8092-exec-10] WARN  com.simbest.exclude.web.GlobalErrorController.logErrorInformation Line:160 - 请注意所访问URL【/hnjjwz/iconfont/iconfont.css】发生404错误，资源地址不存在！
2025-05-26 14:30:34.150 [http-nio-8092-exec-10] WARN  com.simbest.boot.util.AppFileUtil.getFileName Line:218 - 文件URL路径【壁纸.jpg】无法通过URL获取路径，直接通过工具类提取文件全称为【壁纸.jpg】
2025-05-26 14:30:34.151 [http-nio-8092-exec-10] WARN  com.simbest.boot.util.AppFileUtil.getFileSuffix Line:274 - 文件URL路径【壁纸.jpg】无法通过URL获取路径，直接通过工具类提取文件后缀为【jpg】
2025-05-26 14:30:34.153 [http-nio-8092-exec-10] WARN  com.simbest.boot.util.AppFileUtil.getFileBaseName Line:251 - 文件URL路径【壁纸.jpg】无法通过URL获取路径，直接通过工具类提取文件名称为【壁纸】
2025-05-26 14:30:34.154 [http-nio-8092-exec-10] WARN  com.simbest.boot.util.AppFileUtil.getFileSuffix Line:274 - 文件URL路径【壁纸.jpg】无法通过URL获取路径，直接通过工具类提取文件后缀为【jpg】
2025-05-26 14:30:34.168 [http-nio-8092-exec-10] WARN  com.simbest.boot.util.AppFileUtil.getFileSuffix Line:274 - 文件URL路径【壁纸.jpg】无法通过URL获取路径，直接通过工具类提取文件后缀为【jpg】
2025-05-26 14:30:43.069 [http-nio-8092-exec-13] WARN  com.simbest.boot.bps.process.operate.WfProcessManager.startProcessAndSetRelativeData Line:87 - 流程启动调用BPS流程启动接口用时：3338ms
2025-05-26 14:30:43.344 [http-nio-8092-exec-13] WARN  com.simbest.boot.bps.process.operate.WfProcessManager.startProcessAndSetRelativeData Line:107 - 【BPS Driver】流程启动第一个工作项的状态：【10】
2025-05-26 14:30:43.346 [http-nio-8092-exec-13] WARN  com.simbest.boot.bps.process.operate.WfProcessManager.startProcessAndSetRelativeData Line:118 - 流程启动调用BPS流程工作项查询接口用时：273ms
2025-05-26 14:30:43.513 [http-nio-8092-exec-13] WARN  com.simbest.boot.bps.process.operate.WorkItemManager.finishWorkItemWithRelativeData Line:169 - 流程下一步传递的参数为：{"code":"E1444461861535715328","currentUserCode":"chenhong","inputUserId":"kongzhijie","appCode":"hnjjwz","title":"轮播图测试","receiptId":"U869225209498492928","outcome":"hnjjwz.depart_manager"}
2025-05-26 14:30:43.695 [http-nio-8092-exec-13] WARN  com.simbest.boot.bps.unifiedtodo.service.impl.CloseUnifiedTodoServiceImpl.closeTodo Line:68 - 统一待办核销状态：Y
2025-05-26 14:30:43.696 [http-nio-8092-exec-13] WARN  com.simbest.boot.bps.common.HttpNagentDataUtil.queryAgent Line:76 - 获取接口配置对象1：SimpleConfig(id=AC486600246105735169, address=http://************:8088/ntydl, interfaceStyle=ntydl, isOpen=true, isInner=true, provider=北京晟壁)
2025-05-26 14:30:43.696 [http-nio-8092-exec-13] WARN  com.simbest.boot.bps.common.HttpNagentDataUtil.queryAgent Line:80 - 获取接口配置对象2：SimpleConfig(id=AC486600246105735169, address=http://************:8088/ntydl, interfaceStyle=ntydl, isOpen=true, isInner=true, provider=北京晟壁)
2025-05-26 14:30:44.161 [http-nio-8092-exec-13] WARN  com.simbest.boot.bps.process.operate.WorkItemManager.finishWorkItemWithRelativeData Line:264 - 下一个审批人是：kongzhijie，代理人为：[]
2025-05-26 14:30:44.162 [http-nio-8092-exec-13] WARN  com.simbest.boot.bps.process.operate.WorkItemManager.finishWorkItemWithRelativeData Line:275 - 流程提交下一步处理代理人用时：649ms
2025-05-26 14:30:49.068 [http-nio-8092-exec-13] WARN  com.simbest.boot.bps.process.operate.WorkItemManager.finishWorkItemWithRelativeData Line:293 - 流程提交下一步调用BPS流程引擎提交下一步接口用时：4906ms
2025-05-26 14:31:26.155 [http-nio-8092-exec-20] WARN  com.simbest.boot.bps.process.operate.WfProcessManager.startProcessAndSetRelativeData Line:87 - 流程启动调用BPS流程启动接口用时：4079ms
2025-05-26 14:31:26.458 [http-nio-8092-exec-20] WARN  com.simbest.boot.bps.process.operate.WfProcessManager.startProcessAndSetRelativeData Line:107 - 【BPS Driver】流程启动第一个工作项的状态：【10】
2025-05-26 14:31:26.458 [http-nio-8092-exec-20] WARN  com.simbest.boot.bps.process.operate.WfProcessManager.startProcessAndSetRelativeData Line:118 - 流程启动调用BPS流程工作项查询接口用时：301ms
2025-05-26 14:31:26.603 [http-nio-8092-exec-20] WARN  com.simbest.boot.bps.process.operate.WorkItemManager.finishWorkItemWithRelativeData Line:169 - 流程下一步传递的参数为：{"code":"C1444462039424536576","currentUserCode":"chenhong","inputUserId":"kongzhijie","appCode":"hnjjwz","title":"公告起草","receiptId":"U869225387362148352","outcome":"hnjjwz.depart_manager"}
2025-05-26 14:31:26.927 [http-nio-8092-exec-20] WARN  com.simbest.boot.bps.unifiedtodo.service.impl.CloseUnifiedTodoServiceImpl.closeTodo Line:68 - 统一待办核销状态：Y
2025-05-26 14:31:26.928 [http-nio-8092-exec-20] WARN  com.simbest.boot.bps.common.HttpNagentDataUtil.queryAgent Line:76 - 获取接口配置对象1：SimpleConfig(id=AC486600246105735169, address=http://************:8088/ntydl, interfaceStyle=ntydl, isOpen=true, isInner=true, provider=北京晟壁)
2025-05-26 14:31:26.929 [http-nio-8092-exec-20] WARN  com.simbest.boot.bps.common.HttpNagentDataUtil.queryAgent Line:80 - 获取接口配置对象2：SimpleConfig(id=AC486600246105735169, address=http://************:8088/ntydl, interfaceStyle=ntydl, isOpen=true, isInner=true, provider=北京晟壁)
2025-05-26 14:31:27.372 [http-nio-8092-exec-20] WARN  com.simbest.boot.bps.process.operate.WorkItemManager.finishWorkItemWithRelativeData Line:264 - 下一个审批人是：kongzhijie，代理人为：[]
2025-05-26 14:31:27.373 [http-nio-8092-exec-20] WARN  com.simbest.boot.bps.process.operate.WorkItemManager.finishWorkItemWithRelativeData Line:275 - 流程提交下一步处理代理人用时：769ms
2025-05-26 14:31:33.417 [http-nio-8092-exec-20] WARN  com.simbest.boot.bps.process.operate.WorkItemManager.finishWorkItemWithRelativeData Line:293 - 流程提交下一步调用BPS流程引擎提交下一步接口用时：6043ms
2025-05-26 14:33:32.126 [http-nio-8092-exec-3] WARN  com.simbest.boot.bps.process.operate.WorkItemManager.finishWorkItemWithRelativeData Line:169 - 流程下一步传递的参数为：{"code":"C1444462039424536576","currentUserCode":"kongzhijie","inputUserId":"chenhong","appCode":"hnjjwz","title":"公告起草","receiptId":"U869225387362148352","outcome":"return"}
2025-05-26 14:33:32.624 [http-nio-8092-exec-3] WARN  com.simbest.boot.bps.unifiedtodo.service.impl.CloseUnifiedTodoServiceImpl.closeTodo Line:68 - 统一待办核销状态：Y
2025-05-26 14:33:32.624 [http-nio-8092-exec-3] WARN  com.simbest.boot.bps.common.HttpNagentDataUtil.queryAgent Line:76 - 获取接口配置对象1：SimpleConfig(id=AC486600246105735169, address=http://************:8088/ntydl, interfaceStyle=ntydl, isOpen=true, isInner=true, provider=北京晟壁)
2025-05-26 14:33:32.625 [http-nio-8092-exec-3] WARN  com.simbest.boot.bps.common.HttpNagentDataUtil.queryAgent Line:80 - 获取接口配置对象2：SimpleConfig(id=AC486600246105735169, address=http://************:8088/ntydl, interfaceStyle=ntydl, isOpen=true, isInner=true, provider=北京晟壁)
2025-05-26 14:33:33.091 [http-nio-8092-exec-3] WARN  com.simbest.boot.bps.process.operate.WorkItemManager.finishWorkItemWithRelativeData Line:264 - 下一个审批人是：chenhong，代理人为：[]
2025-05-26 14:33:33.091 [http-nio-8092-exec-3] WARN  com.simbest.boot.bps.process.operate.WorkItemManager.finishWorkItemWithRelativeData Line:275 - 流程提交下一步处理代理人用时：964ms
2025-05-26 14:33:38.156 [http-nio-8092-exec-3] WARN  com.simbest.boot.bps.process.operate.WorkItemManager.finishWorkItemWithRelativeData Line:293 - 流程提交下一步调用BPS流程引擎提交下一步接口用时：5064ms
2025-05-26 14:33:50.802 [http-nio-8092-exec-6] WARN  com.simbest.boot.bps.process.operate.WorkItemManager.finishWorkItemWithRelativeData Line:169 - 流程下一步传递的参数为：{"code":"E1444461861535715328","currentUserCode":"kongzhijie","inputUserId":"chenhong","appCode":"hnjjwz","title":"轮播图测试","receiptId":"U869225209498492928","outcome":"return"}
2025-05-26 14:33:51.093 [http-nio-8092-exec-6] WARN  com.simbest.boot.bps.unifiedtodo.service.impl.CloseUnifiedTodoServiceImpl.closeTodo Line:68 - 统一待办核销状态：Y
2025-05-26 14:33:51.094 [http-nio-8092-exec-6] WARN  com.simbest.boot.bps.common.HttpNagentDataUtil.queryAgent Line:76 - 获取接口配置对象1：SimpleConfig(id=AC486600246105735169, address=http://************:8088/ntydl, interfaceStyle=ntydl, isOpen=true, isInner=true, provider=北京晟壁)
2025-05-26 14:33:51.094 [http-nio-8092-exec-6] WARN  com.simbest.boot.bps.common.HttpNagentDataUtil.queryAgent Line:80 - 获取接口配置对象2：SimpleConfig(id=AC486600246105735169, address=http://************:8088/ntydl, interfaceStyle=ntydl, isOpen=true, isInner=true, provider=北京晟壁)
2025-05-26 14:33:51.546 [http-nio-8092-exec-6] WARN  com.simbest.boot.bps.process.operate.WorkItemManager.finishWorkItemWithRelativeData Line:264 - 下一个审批人是：chenhong，代理人为：[]
2025-05-26 14:33:51.547 [http-nio-8092-exec-6] WARN  com.simbest.boot.bps.process.operate.WorkItemManager.finishWorkItemWithRelativeData Line:275 - 流程提交下一步处理代理人用时：744ms
2025-05-26 14:33:56.803 [http-nio-8092-exec-6] WARN  com.simbest.boot.bps.process.operate.WorkItemManager.finishWorkItemWithRelativeData Line:293 - 流程提交下一步调用BPS流程引擎提交下一步接口用时：5256ms
2025-05-26 14:33:57.937 [http-nio-8092-exec-13] WARN  com.simbest.exclude.web.GlobalErrorController.logErrorInformation Line:160 - 请注意所访问URL【/hnjjwz/html/updatepassword.html】发生404错误，资源地址不存在！
2025-05-26 14:34:49.423 [http-nio-8092-exec-17] WARN  com.simbest.boot.bps.process.operate.WorkItemManager.finishWorkItemWithRelativeData Line:169 - 流程下一步传递的参数为：{"code":"C1444462039424536576","currentUserCode":"chenhong","inputUserId":"kongzhijie","appCode":"hnjjwz","title":"公告起草","receiptId":"U869225387362148352","outcome":"hnjjwz.depart_manager"}
2025-05-26 14:34:49.730 [http-nio-8092-exec-17] WARN  com.simbest.boot.bps.unifiedtodo.service.impl.CloseUnifiedTodoServiceImpl.closeTodo Line:68 - 统一待办核销状态：Y
2025-05-26 14:34:49.731 [http-nio-8092-exec-17] WARN  com.simbest.boot.bps.common.HttpNagentDataUtil.queryAgent Line:76 - 获取接口配置对象1：SimpleConfig(id=AC486600246105735169, address=http://************:8088/ntydl, interfaceStyle=ntydl, isOpen=true, isInner=true, provider=北京晟壁)
2025-05-26 14:34:49.732 [http-nio-8092-exec-17] WARN  com.simbest.boot.bps.common.HttpNagentDataUtil.queryAgent Line:80 - 获取接口配置对象2：SimpleConfig(id=AC486600246105735169, address=http://************:8088/ntydl, interfaceStyle=ntydl, isOpen=true, isInner=true, provider=北京晟壁)
2025-05-26 14:34:50.190 [http-nio-8092-exec-17] WARN  com.simbest.boot.bps.process.operate.WorkItemManager.finishWorkItemWithRelativeData Line:264 - 下一个审批人是：kongzhijie，代理人为：[]
2025-05-26 14:34:50.191 [http-nio-8092-exec-17] WARN  com.simbest.boot.bps.process.operate.WorkItemManager.finishWorkItemWithRelativeData Line:275 - 流程提交下一步处理代理人用时：767ms
2025-05-26 14:34:54.643 [http-nio-8092-exec-17] WARN  com.simbest.boot.bps.process.operate.WorkItemManager.finishWorkItemWithRelativeData Line:293 - 流程提交下一步调用BPS流程引擎提交下一步接口用时：4452ms
2025-05-26 15:02:58.594 [http-nio-8092-exec-3] WARN  com.simbest.boot.util.AppFileUtil.getFileName Line:218 - 文件URL路径【壁纸.jpg】无法通过URL获取路径，直接通过工具类提取文件全称为【壁纸.jpg】
2025-05-26 15:02:58.595 [http-nio-8092-exec-3] WARN  com.simbest.boot.util.AppFileUtil.getFileSuffix Line:274 - 文件URL路径【壁纸.jpg】无法通过URL获取路径，直接通过工具类提取文件后缀为【jpg】
2025-05-26 15:02:58.600 [http-nio-8092-exec-3] WARN  com.simbest.boot.util.AppFileUtil.getFileBaseName Line:251 - 文件URL路径【壁纸.jpg】无法通过URL获取路径，直接通过工具类提取文件名称为【壁纸】
2025-05-26 15:02:58.600 [http-nio-8092-exec-3] WARN  com.simbest.boot.util.AppFileUtil.getFileSuffix Line:274 - 文件URL路径【壁纸.jpg】无法通过URL获取路径，直接通过工具类提取文件后缀为【jpg】
2025-05-26 15:02:58.617 [http-nio-8092-exec-3] WARN  com.simbest.boot.util.AppFileUtil.getFileSuffix Line:274 - 文件URL路径【壁纸.jpg】无法通过URL获取路径，直接通过工具类提取文件后缀为【jpg】
2025-05-26 15:16:12.255 [http-nio-8092-exec-3] WARN  com.simbest.boot.util.AppFileUtil.getFileName Line:218 - 文件URL路径【壁纸.jpg】无法通过URL获取路径，直接通过工具类提取文件全称为【壁纸.jpg】
2025-05-26 15:16:12.256 [http-nio-8092-exec-3] WARN  com.simbest.boot.util.AppFileUtil.getFileSuffix Line:274 - 文件URL路径【壁纸.jpg】无法通过URL获取路径，直接通过工具类提取文件后缀为【jpg】
2025-05-26 15:16:12.258 [http-nio-8092-exec-3] WARN  com.simbest.boot.util.AppFileUtil.getFileBaseName Line:251 - 文件URL路径【壁纸.jpg】无法通过URL获取路径，直接通过工具类提取文件名称为【壁纸】
2025-05-26 15:16:12.259 [http-nio-8092-exec-3] WARN  com.simbest.boot.util.AppFileUtil.getFileSuffix Line:274 - 文件URL路径【壁纸.jpg】无法通过URL获取路径，直接通过工具类提取文件后缀为【jpg】
2025-05-26 15:16:12.271 [http-nio-8092-exec-3] WARN  com.simbest.boot.util.AppFileUtil.getFileSuffix Line:274 - 文件URL路径【壁纸.jpg】无法通过URL获取路径，直接通过工具类提取文件后缀为【jpg】
2025-05-26 15:17:25.156 [http-nio-8092-exec-2] WARN  com.simbest.boot.util.AppFileUtil.getFileName Line:218 - 文件URL路径【壁纸.jpg】无法通过URL获取路径，直接通过工具类提取文件全称为【壁纸.jpg】
2025-05-26 15:17:25.157 [http-nio-8092-exec-2] WARN  com.simbest.boot.util.AppFileUtil.getFileSuffix Line:274 - 文件URL路径【壁纸.jpg】无法通过URL获取路径，直接通过工具类提取文件后缀为【jpg】
2025-05-26 15:17:25.160 [http-nio-8092-exec-2] WARN  com.simbest.boot.util.AppFileUtil.getFileBaseName Line:251 - 文件URL路径【壁纸.jpg】无法通过URL获取路径，直接通过工具类提取文件名称为【壁纸】
2025-05-26 15:17:25.160 [http-nio-8092-exec-2] WARN  com.simbest.boot.util.AppFileUtil.getFileSuffix Line:274 - 文件URL路径【壁纸.jpg】无法通过URL获取路径，直接通过工具类提取文件后缀为【jpg】
2025-05-26 15:17:25.173 [http-nio-8092-exec-2] WARN  com.simbest.boot.util.AppFileUtil.getFileSuffix Line:274 - 文件URL路径【壁纸.jpg】无法通过URL获取路径，直接通过工具类提取文件后缀为【jpg】
2025-05-26 15:23:48.786 [http-nio-8092-exec-11] WARN  com.simbest.boot.util.AppFileUtil.getFileName Line:218 - 文件URL路径【壁纸.jpg】无法通过URL获取路径，直接通过工具类提取文件全称为【壁纸.jpg】
2025-05-26 15:23:48.787 [http-nio-8092-exec-11] WARN  com.simbest.boot.util.AppFileUtil.getFileSuffix Line:274 - 文件URL路径【壁纸.jpg】无法通过URL获取路径，直接通过工具类提取文件后缀为【jpg】
2025-05-26 15:23:48.789 [http-nio-8092-exec-11] WARN  com.simbest.boot.util.AppFileUtil.getFileBaseName Line:251 - 文件URL路径【壁纸.jpg】无法通过URL获取路径，直接通过工具类提取文件名称为【壁纸】
2025-05-26 15:23:48.790 [http-nio-8092-exec-11] WARN  com.simbest.boot.util.AppFileUtil.getFileSuffix Line:274 - 文件URL路径【壁纸.jpg】无法通过URL获取路径，直接通过工具类提取文件后缀为【jpg】
2025-05-26 15:23:48.800 [http-nio-8092-exec-11] WARN  com.simbest.boot.util.AppFileUtil.getFileSuffix Line:274 - 文件URL路径【壁纸.jpg】无法通过URL获取路径，直接通过工具类提取文件后缀为【jpg】
2025-05-26 15:23:54.331 [http-nio-8092-exec-6] WARN  com.simbest.exclude.web.GlobalErrorController.logErrorInformation Line:160 - 请注意所访问URL【/hnjjwz/html/slideShow/html/apply/preview.html】发生404错误，资源地址不存在！
2025-05-26 15:25:23.123 [http-nio-8092-exec-6] WARN  com.simbest.exclude.web.GlobalErrorController.logErrorInformation Line:160 - 请注意所访问URL【/hnjjwz/html/slideShow/html/apply/preview.html】发生404错误，资源地址不存在！
2025-05-26 15:28:25.331 [http-nio-8092-exec-14] WARN  com.simbest.boot.util.AppFileUtil.getFileName Line:218 - 文件URL路径【壁纸.jpg】无法通过URL获取路径，直接通过工具类提取文件全称为【壁纸.jpg】
2025-05-26 15:28:25.331 [http-nio-8092-exec-14] WARN  com.simbest.boot.util.AppFileUtil.getFileSuffix Line:274 - 文件URL路径【壁纸.jpg】无法通过URL获取路径，直接通过工具类提取文件后缀为【jpg】
2025-05-26 15:28:25.334 [http-nio-8092-exec-14] WARN  com.simbest.boot.util.AppFileUtil.getFileBaseName Line:251 - 文件URL路径【壁纸.jpg】无法通过URL获取路径，直接通过工具类提取文件名称为【壁纸】
2025-05-26 15:28:25.335 [http-nio-8092-exec-14] WARN  com.simbest.boot.util.AppFileUtil.getFileSuffix Line:274 - 文件URL路径【壁纸.jpg】无法通过URL获取路径，直接通过工具类提取文件后缀为【jpg】
2025-05-26 15:28:25.347 [http-nio-8092-exec-14] WARN  com.simbest.boot.util.AppFileUtil.getFileSuffix Line:274 - 文件URL路径【壁纸.jpg】无法通过URL获取路径，直接通过工具类提取文件后缀为【jpg】
2025-05-26 15:32:37.238 [http-nio-8092-exec-2] WARN  com.simbest.boot.util.AppFileUtil.getFileName Line:218 - 文件URL路径【壁纸.jpg】无法通过URL获取路径，直接通过工具类提取文件全称为【壁纸.jpg】
2025-05-26 15:32:37.239 [http-nio-8092-exec-2] WARN  com.simbest.boot.util.AppFileUtil.getFileSuffix Line:274 - 文件URL路径【壁纸.jpg】无法通过URL获取路径，直接通过工具类提取文件后缀为【jpg】
2025-05-26 15:32:37.241 [http-nio-8092-exec-2] WARN  com.simbest.boot.util.AppFileUtil.getFileBaseName Line:251 - 文件URL路径【壁纸.jpg】无法通过URL获取路径，直接通过工具类提取文件名称为【壁纸】
2025-05-26 15:32:37.242 [http-nio-8092-exec-2] WARN  com.simbest.boot.util.AppFileUtil.getFileSuffix Line:274 - 文件URL路径【壁纸.jpg】无法通过URL获取路径，直接通过工具类提取文件后缀为【jpg】
2025-05-26 15:32:37.253 [http-nio-8092-exec-2] WARN  com.simbest.boot.util.AppFileUtil.getFileSuffix Line:274 - 文件URL路径【壁纸.jpg】无法通过URL获取路径，直接通过工具类提取文件后缀为【jpg】
2025-05-26 15:33:21.282 [http-nio-8092-exec-10] WARN  com.simbest.boot.util.AppFileUtil.getFileName Line:218 - 文件URL路径【壁纸.jpg】无法通过URL获取路径，直接通过工具类提取文件全称为【壁纸.jpg】
2025-05-26 15:33:21.282 [http-nio-8092-exec-10] WARN  com.simbest.boot.util.AppFileUtil.getFileSuffix Line:274 - 文件URL路径【壁纸.jpg】无法通过URL获取路径，直接通过工具类提取文件后缀为【jpg】
2025-05-26 15:33:21.286 [http-nio-8092-exec-10] WARN  com.simbest.boot.util.AppFileUtil.getFileBaseName Line:251 - 文件URL路径【壁纸.jpg】无法通过URL获取路径，直接通过工具类提取文件名称为【壁纸】
2025-05-26 15:33:21.286 [http-nio-8092-exec-10] WARN  com.simbest.boot.util.AppFileUtil.getFileSuffix Line:274 - 文件URL路径【壁纸.jpg】无法通过URL获取路径，直接通过工具类提取文件后缀为【jpg】
2025-05-26 15:33:21.299 [http-nio-8092-exec-10] WARN  com.simbest.boot.util.AppFileUtil.getFileSuffix Line:274 - 文件URL路径【壁纸.jpg】无法通过URL获取路径，直接通过工具类提取文件后缀为【jpg】
2025-05-26 16:21:46.236 [http-nio-8092-exec-10] WARN  com.simbest.exclude.web.GlobalErrorController.logErrorInformation Line:160 - 请注意所访问URL【/hnjjwz/iconfont/iconfont.css】发生404错误，资源地址不存在！
2025-05-26 16:21:57.383 [http-nio-8092-exec-20] WARN  com.simbest.boot.util.AppFileUtil.getFileName Line:218 - 文件URL路径【组 2.png】无法通过URL获取路径，直接通过工具类提取文件全称为【组 2.png】
2025-05-26 16:21:57.383 [http-nio-8092-exec-20] WARN  com.simbest.boot.util.AppFileUtil.getFileSuffix Line:274 - 文件URL路径【组 2.png】无法通过URL获取路径，直接通过工具类提取文件后缀为【png】
2025-05-26 16:21:57.386 [http-nio-8092-exec-20] WARN  com.simbest.boot.util.AppFileUtil.getFileBaseName Line:251 - 文件URL路径【组 2.png】无法通过URL获取路径，直接通过工具类提取文件名称为【组 2】
2025-05-26 16:21:57.386 [http-nio-8092-exec-20] WARN  com.simbest.boot.util.AppFileUtil.getFileSuffix Line:274 - 文件URL路径【组 2.png】无法通过URL获取路径，直接通过工具类提取文件后缀为【png】
2025-05-26 16:21:57.394 [http-nio-8092-exec-20] WARN  com.simbest.boot.util.AppFileUtil.getFileSuffix Line:274 - 文件URL路径【组 2.png】无法通过URL获取路径，直接通过工具类提取文件后缀为【png】
2025-05-26 16:23:01.755 [http-nio-8092-exec-2] WARN  com.simbest.boot.util.AppFileUtil.getFileName Line:218 - 文件URL路径【组 2.png】无法通过URL获取路径，直接通过工具类提取文件全称为【组 2.png】
2025-05-26 16:23:01.755 [http-nio-8092-exec-2] WARN  com.simbest.boot.util.AppFileUtil.getFileSuffix Line:274 - 文件URL路径【组 2.png】无法通过URL获取路径，直接通过工具类提取文件后缀为【png】
2025-05-26 16:23:01.758 [http-nio-8092-exec-2] WARN  com.simbest.boot.util.AppFileUtil.getFileBaseName Line:251 - 文件URL路径【组 2.png】无法通过URL获取路径，直接通过工具类提取文件名称为【组 2】
2025-05-26 16:23:01.758 [http-nio-8092-exec-2] WARN  com.simbest.boot.util.AppFileUtil.getFileSuffix Line:274 - 文件URL路径【组 2.png】无法通过URL获取路径，直接通过工具类提取文件后缀为【png】
2025-05-26 16:23:01.768 [http-nio-8092-exec-2] WARN  com.simbest.boot.util.AppFileUtil.getFileSuffix Line:274 - 文件URL路径【组 2.png】无法通过URL获取路径，直接通过工具类提取文件后缀为【png】
2025-05-26 16:24:38.164 [http-nio-8092-exec-20] WARN  com.simbest.boot.util.AppFileUtil.getFileName Line:218 - 文件URL路径【组 2.png】无法通过URL获取路径，直接通过工具类提取文件全称为【组 2.png】
2025-05-26 16:24:38.165 [http-nio-8092-exec-20] WARN  com.simbest.boot.util.AppFileUtil.getFileSuffix Line:274 - 文件URL路径【组 2.png】无法通过URL获取路径，直接通过工具类提取文件后缀为【png】
2025-05-26 16:24:38.168 [http-nio-8092-exec-20] WARN  com.simbest.boot.util.AppFileUtil.getFileBaseName Line:251 - 文件URL路径【组 2.png】无法通过URL获取路径，直接通过工具类提取文件名称为【组 2】
2025-05-26 16:24:38.169 [http-nio-8092-exec-20] WARN  com.simbest.boot.util.AppFileUtil.getFileSuffix Line:274 - 文件URL路径【组 2.png】无法通过URL获取路径，直接通过工具类提取文件后缀为【png】
2025-05-26 16:24:38.180 [http-nio-8092-exec-20] WARN  com.simbest.boot.util.AppFileUtil.getFileSuffix Line:274 - 文件URL路径【组 2.png】无法通过URL获取路径，直接通过工具类提取文件后缀为【png】
2025-05-26 16:26:01.431 [http-nio-8092-exec-8] WARN  com.simbest.boot.bps.process.operate.WfProcessManager.startProcessAndSetRelativeData Line:87 - 流程启动调用BPS流程启动接口用时：3371ms
2025-05-26 16:26:01.878 [http-nio-8092-exec-8] WARN  com.simbest.boot.bps.process.operate.WfProcessManager.startProcessAndSetRelativeData Line:107 - 【BPS Driver】流程启动第一个工作项的状态：【10】
2025-05-26 16:26:01.878 [http-nio-8092-exec-8] WARN  com.simbest.boot.bps.process.operate.WfProcessManager.startProcessAndSetRelativeData Line:118 - 流程启动调用BPS流程工作项查询接口用时：445ms
2025-05-26 16:26:01.976 [http-nio-8092-exec-8] WARN  com.simbest.boot.bps.process.operate.WorkItemManager.finishWorkItemWithRelativeData Line:169 - 流程下一步传递的参数为：{"code":"E1444490879140073472","currentUserCode":"chenhong","inputUserId":"kongzhijie","appCode":"hnjjwz","title":"234234","receiptId":"U869254227073490944","outcome":"hnjjwz.depart_manager"}
2025-05-26 16:26:02.317 [http-nio-8092-exec-8] WARN  com.simbest.boot.bps.unifiedtodo.service.impl.CloseUnifiedTodoServiceImpl.closeTodo Line:68 - 统一待办核销状态：Y
2025-05-26 16:26:02.317 [http-nio-8092-exec-8] WARN  com.simbest.boot.bps.common.HttpNagentDataUtil.queryAgent Line:76 - 获取接口配置对象1：SimpleConfig(id=AC486600246105735169, address=http://************:8088/ntydl, interfaceStyle=ntydl, isOpen=true, isInner=true, provider=北京晟壁)
2025-05-26 16:26:02.318 [http-nio-8092-exec-8] WARN  com.simbest.boot.bps.common.HttpNagentDataUtil.queryAgent Line:80 - 获取接口配置对象2：SimpleConfig(id=AC486600246105735169, address=http://************:8088/ntydl, interfaceStyle=ntydl, isOpen=true, isInner=true, provider=北京晟壁)
2025-05-26 16:26:02.794 [http-nio-8092-exec-8] WARN  com.simbest.boot.bps.process.operate.WorkItemManager.finishWorkItemWithRelativeData Line:264 - 下一个审批人是：kongzhijie，代理人为：[]
2025-05-26 16:26:02.795 [http-nio-8092-exec-8] WARN  com.simbest.boot.bps.process.operate.WorkItemManager.finishWorkItemWithRelativeData Line:275 - 流程提交下一步处理代理人用时：818ms
2025-05-26 16:26:08.276 [http-nio-8092-exec-8] WARN  com.simbest.boot.bps.process.operate.WorkItemManager.finishWorkItemWithRelativeData Line:293 - 流程提交下一步调用BPS流程引擎提交下一步接口用时：5480ms
2025-05-26 16:28:34.001 [http-nio-8092-exec-19] WARN  com.simbest.boot.util.AppFileUtil.getFileName Line:218 - 文件URL路径【组 3.png】无法通过URL获取路径，直接通过工具类提取文件全称为【组 3.png】
2025-05-26 16:28:34.002 [http-nio-8092-exec-19] WARN  com.simbest.boot.util.AppFileUtil.getFileSuffix Line:274 - 文件URL路径【组 3.png】无法通过URL获取路径，直接通过工具类提取文件后缀为【png】
2025-05-26 16:28:34.004 [http-nio-8092-exec-19] WARN  com.simbest.boot.util.AppFileUtil.getFileBaseName Line:251 - 文件URL路径【组 3.png】无法通过URL获取路径，直接通过工具类提取文件名称为【组 3】
2025-05-26 16:28:34.004 [http-nio-8092-exec-19] WARN  com.simbest.boot.util.AppFileUtil.getFileSuffix Line:274 - 文件URL路径【组 3.png】无法通过URL获取路径，直接通过工具类提取文件后缀为【png】
2025-05-26 16:28:34.013 [http-nio-8092-exec-19] WARN  com.simbest.boot.util.AppFileUtil.getFileSuffix Line:274 - 文件URL路径【组 3.png】无法通过URL获取路径，直接通过工具类提取文件后缀为【png】
2025-05-26 16:35:27.472 [http-nio-8092-exec-2] WARN  com.simbest.boot.util.AppFileUtil.getFileName Line:218 - 文件URL路径【组 2.png】无法通过URL获取路径，直接通过工具类提取文件全称为【组 2.png】
2025-05-26 16:35:27.473 [http-nio-8092-exec-2] WARN  com.simbest.boot.util.AppFileUtil.getFileSuffix Line:274 - 文件URL路径【组 2.png】无法通过URL获取路径，直接通过工具类提取文件后缀为【png】
2025-05-26 16:35:27.479 [http-nio-8092-exec-2] WARN  com.simbest.boot.util.AppFileUtil.getFileBaseName Line:251 - 文件URL路径【组 2.png】无法通过URL获取路径，直接通过工具类提取文件名称为【组 2】
2025-05-26 16:35:27.479 [http-nio-8092-exec-2] WARN  com.simbest.boot.util.AppFileUtil.getFileSuffix Line:274 - 文件URL路径【组 2.png】无法通过URL获取路径，直接通过工具类提取文件后缀为【png】
2025-05-26 16:35:27.486 [http-nio-8092-exec-2] WARN  com.simbest.boot.util.AppFileUtil.getFileSuffix Line:274 - 文件URL路径【组 2.png】无法通过URL获取路径，直接通过工具类提取文件后缀为【png】
2025-05-26 16:44:40.808 [http-nio-8092-exec-5] WARN  com.simbest.boot.util.AppFileUtil.getFileName Line:218 - 文件URL路径【组 2.png】无法通过URL获取路径，直接通过工具类提取文件全称为【组 2.png】
2025-05-26 16:44:40.809 [http-nio-8092-exec-5] WARN  com.simbest.boot.util.AppFileUtil.getFileSuffix Line:274 - 文件URL路径【组 2.png】无法通过URL获取路径，直接通过工具类提取文件后缀为【png】
2025-05-26 16:44:40.811 [http-nio-8092-exec-5] WARN  com.simbest.boot.util.AppFileUtil.getFileBaseName Line:251 - 文件URL路径【组 2.png】无法通过URL获取路径，直接通过工具类提取文件名称为【组 2】
2025-05-26 16:44:40.811 [http-nio-8092-exec-5] WARN  com.simbest.boot.util.AppFileUtil.getFileSuffix Line:274 - 文件URL路径【组 2.png】无法通过URL获取路径，直接通过工具类提取文件后缀为【png】
2025-05-26 16:44:40.819 [http-nio-8092-exec-5] WARN  com.simbest.boot.util.AppFileUtil.getFileSuffix Line:274 - 文件URL路径【组 2.png】无法通过URL获取路径，直接通过工具类提取文件后缀为【png】
2025-05-26 16:47:34.702 [http-nio-8092-exec-16] WARN  com.simbest.boot.util.AppFileUtil.getFileName Line:218 - 文件URL路径【组 2.png】无法通过URL获取路径，直接通过工具类提取文件全称为【组 2.png】
2025-05-26 16:47:34.702 [http-nio-8092-exec-16] WARN  com.simbest.boot.util.AppFileUtil.getFileSuffix Line:274 - 文件URL路径【组 2.png】无法通过URL获取路径，直接通过工具类提取文件后缀为【png】
2025-05-26 16:47:34.704 [http-nio-8092-exec-16] WARN  com.simbest.boot.util.AppFileUtil.getFileBaseName Line:251 - 文件URL路径【组 2.png】无法通过URL获取路径，直接通过工具类提取文件名称为【组 2】
2025-05-26 16:47:34.706 [http-nio-8092-exec-16] WARN  com.simbest.boot.util.AppFileUtil.getFileSuffix Line:274 - 文件URL路径【组 2.png】无法通过URL获取路径，直接通过工具类提取文件后缀为【png】
2025-05-26 16:47:34.718 [http-nio-8092-exec-16] WARN  com.simbest.boot.util.AppFileUtil.getFileSuffix Line:274 - 文件URL路径【组 2.png】无法通过URL获取路径，直接通过工具类提取文件后缀为【png】
2025-05-26 16:48:28.966 [http-nio-8092-exec-13] WARN  com.simbest.boot.util.AppFileUtil.getFileName Line:218 - 文件URL路径【组 2.png】无法通过URL获取路径，直接通过工具类提取文件全称为【组 2.png】
2025-05-26 16:48:28.966 [http-nio-8092-exec-13] WARN  com.simbest.boot.util.AppFileUtil.getFileSuffix Line:274 - 文件URL路径【组 2.png】无法通过URL获取路径，直接通过工具类提取文件后缀为【png】
2025-05-26 16:48:28.969 [http-nio-8092-exec-13] WARN  com.simbest.boot.util.AppFileUtil.getFileBaseName Line:251 - 文件URL路径【组 2.png】无法通过URL获取路径，直接通过工具类提取文件名称为【组 2】
2025-05-26 16:48:28.970 [http-nio-8092-exec-13] WARN  com.simbest.boot.util.AppFileUtil.getFileSuffix Line:274 - 文件URL路径【组 2.png】无法通过URL获取路径，直接通过工具类提取文件后缀为【png】
2025-05-26 16:48:28.982 [http-nio-8092-exec-13] WARN  com.simbest.boot.util.AppFileUtil.getFileSuffix Line:274 - 文件URL路径【组 2.png】无法通过URL获取路径，直接通过工具类提取文件后缀为【png】
2025-05-26 16:51:55.472 [http-nio-8092-exec-18] WARN  com.simbest.boot.util.AppFileUtil.getFileName Line:218 - 文件URL路径【组 2.png】无法通过URL获取路径，直接通过工具类提取文件全称为【组 2.png】
2025-05-26 16:51:55.473 [http-nio-8092-exec-18] WARN  com.simbest.boot.util.AppFileUtil.getFileSuffix Line:274 - 文件URL路径【组 2.png】无法通过URL获取路径，直接通过工具类提取文件后缀为【png】
2025-05-26 16:51:55.475 [http-nio-8092-exec-18] WARN  com.simbest.boot.util.AppFileUtil.getFileBaseName Line:251 - 文件URL路径【组 2.png】无法通过URL获取路径，直接通过工具类提取文件名称为【组 2】
2025-05-26 16:51:55.476 [http-nio-8092-exec-18] WARN  com.simbest.boot.util.AppFileUtil.getFileSuffix Line:274 - 文件URL路径【组 2.png】无法通过URL获取路径，直接通过工具类提取文件后缀为【png】
2025-05-26 16:51:55.486 [http-nio-8092-exec-18] WARN  com.simbest.boot.util.AppFileUtil.getFileSuffix Line:274 - 文件URL路径【组 2.png】无法通过URL获取路径，直接通过工具类提取文件后缀为【png】
2025-05-26 16:54:15.456 [http-nio-8092-exec-9] WARN  com.simbest.boot.util.AppFileUtil.getFileName Line:218 - 文件URL路径【组 2.png】无法通过URL获取路径，直接通过工具类提取文件全称为【组 2.png】
2025-05-26 16:54:15.457 [http-nio-8092-exec-9] WARN  com.simbest.boot.util.AppFileUtil.getFileSuffix Line:274 - 文件URL路径【组 2.png】无法通过URL获取路径，直接通过工具类提取文件后缀为【png】
2025-05-26 16:54:15.461 [http-nio-8092-exec-9] WARN  com.simbest.boot.util.AppFileUtil.getFileBaseName Line:251 - 文件URL路径【组 2.png】无法通过URL获取路径，直接通过工具类提取文件名称为【组 2】
2025-05-26 16:54:15.461 [http-nio-8092-exec-9] WARN  com.simbest.boot.util.AppFileUtil.getFileSuffix Line:274 - 文件URL路径【组 2.png】无法通过URL获取路径，直接通过工具类提取文件后缀为【png】
2025-05-26 16:54:15.468 [http-nio-8092-exec-9] WARN  com.simbest.boot.util.AppFileUtil.getFileSuffix Line:274 - 文件URL路径【组 2.png】无法通过URL获取路径，直接通过工具类提取文件后缀为【png】
2025-05-26 17:17:31.119 [http-nio-8092-exec-15] WARN  com.simbest.exclude.web.GlobalErrorController.logErrorInformation Line:160 - 请注意所访问URL【/hnjjwz/iconfont/iconfont.css】发生404错误，资源地址不存在！
2025-05-26 17:18:43.214 [http-nio-8092-exec-9] WARN  com.simbest.exclude.web.GlobalErrorController.logErrorInformation Line:160 - 请注意所访问URL【/hnjjwz/iconfont/iconfont.css】发生404错误，资源地址不存在！
2025-05-26 18:40:11.640 [redisson-netty-5-10] WARN  org.redisson.client.handler.CommandDecoder.completeResponse Line:445 - response has been skipped due to timeout! channel: [id: 0x6e2701b6, L:/***********:58538 - R:************/************:7003], command: (CLUSTER NODES), params: [], result: [ClusterNodeInfo [nodeId=4dcfd30d1191cb5e65ef69e88dc76592f8b3d14d, address=redis://************:7005, flags=[MASTER], slaveOf=null, slotRanges=[[10923-16383]]], ClusterNodeInfo [nodeId=248f8c5f1a3d07d891f1bcf924c02097c5509e0d, address=redis://************:7001, flags=[SLAVE], slaveOf=64f45f937e6f8c36d0fb7cf09195403c4cccdf1d, slotRanges=[]], ClusterNodeInfo [nodeId=02aff3b78f8c09c43cbfc2346eac68a16a942097, address=redis://************:7003, flags=[MASTER, MYSELF], slaveOf=null, slotRanges=[[5461-10922]]], ClusterNodeInfo [nodeId=85e61682488d3a355264d212607a63832da60e8c, address=redis://************:7006, flags=[SLAVE], slaveOf=02aff3b78f8c09c43cbfc2346eac68a16a942097, slotRanges=[]], ClusterNodeInfo [nodeId=64f45f937e6f8c36d0fb7cf09195403c4cccdf1d, address=redis://************:7004, flags=[MASTER], slaveOf=null, slotRanges=[[0-5460]]], ClusterNodeInfo [nodeId=5514edc6d1aaab0526b0093a424e9f352e9914ed, address=redis://************:7002, flags=[SLAVE], slaveOf=4dcfd30d1191cb5e65ef69e88dc76592f8b3d14d, slotRanges=[]]]
2025-05-26 19:21:09.175 [lettuce-nioEventLoop-4-2] WARN  io.lettuce.core.protocol.ConnectionWatchdog.log Line:153 - Cannot reconnect to [************:7003]: connection timed out: /************:7003
2025-05-26 19:21:09.175 [lettuce-nioEventLoop-4-12] WARN  io.lettuce.core.protocol.ConnectionWatchdog.log Line:153 - Cannot reconnect to [************:7004]: connection timed out: /************:7004
2025-05-26 19:21:09.175 [lettuce-nioEventLoop-4-3] WARN  io.lettuce.core.protocol.ConnectionWatchdog.log Line:153 - Cannot reconnect to [************:7003]: connection timed out: /************:7003
2025-05-26 19:21:09.175 [lettuce-nioEventLoop-4-1] WARN  io.lettuce.core.protocol.ConnectionWatchdog.log Line:153 - Cannot reconnect to [************:7005]: connection timed out: /************:7005
2025-05-26 19:21:09.360 [lettuce-nioEventLoop-4-4] WARN  io.lettuce.core.protocol.ConnectionWatchdog.log Line:153 - Cannot reconnect to [************:7003]: connection timed out: /************:7003
2025-05-26 19:21:11.241 [AMQP Connection ************:5672] WARN  com.rabbitmq.client.impl.ForgivingExceptionHandler.log Line:115 - An unexpected connection driver error occured (Exception message: Connection reset)
2025-05-26 19:21:15.165 [lettuce-nioEventLoop-4-6] WARN  io.lettuce.core.cluster.topology.ClusterTopologyRefresh.lambda$getConnections$0 Line:284 - Unable to connect to [************:7002]: connection timed out: /************:7002
2025-05-26 19:21:15.165 [lettuce-nioEventLoop-4-8] WARN  io.lettuce.core.cluster.topology.ClusterTopologyRefresh.lambda$getConnections$0 Line:284 - Unable to connect to [************:7004]: connection timed out: /************:7004
2025-05-26 19:21:15.166 [lettuce-nioEventLoop-4-9] WARN  io.lettuce.core.cluster.topology.ClusterTopologyRefresh.lambda$getConnections$0 Line:284 - Unable to connect to [************:7005]: connection timed out: /************:7005
2025-05-26 19:21:15.165 [lettuce-nioEventLoop-4-5] WARN  io.lettuce.core.cluster.topology.ClusterTopologyRefresh.lambda$getConnections$0 Line:284 - Unable to connect to [************:7001]: connection timed out: /************:7001
2025-05-26 19:21:15.165 [lettuce-nioEventLoop-4-7] WARN  io.lettuce.core.cluster.topology.ClusterTopologyRefresh.lambda$getConnections$0 Line:284 - Unable to connect to [************:7003]: connection timed out: /************:7003
2025-05-26 19:21:15.181 [lettuce-nioEventLoop-4-10] WARN  io.lettuce.core.cluster.topology.ClusterTopologyRefresh.lambda$getConnections$0 Line:284 - Unable to connect to [************:7006]: connection timed out: /************:7006
2025-05-26 19:21:18.278 [lettuce-nioEventLoop-4-11] WARN  io.lettuce.core.protocol.ConnectionWatchdog.log Line:153 - Cannot reconnect to [************:7005]: connection timed out: /************:7005
2025-05-26 19:21:18.326 [AMQP Connection ************:5672] WARN  com.rabbitmq.client.impl.ForgivingExceptionHandler.log Line:115 - An unexpected connection driver error occured (Exception message: Connection reset)
2025-05-26 19:21:18.435 [AMQP Connection ************:5672] WARN  com.rabbitmq.client.impl.ForgivingExceptionHandler.log Line:115 - An unexpected connection driver error occured (Exception message: Connection reset)
2025-05-26 19:21:18.435 [AMQP Connection ************:5672] WARN  com.rabbitmq.client.impl.ForgivingExceptionHandler.log Line:115 - An unexpected connection driver error occured (Exception message: Connection reset)
2025-05-26 19:21:19.267 [lettuce-nioEventLoop-4-12] WARN  io.lettuce.core.protocol.ConnectionWatchdog.log Line:153 - Cannot reconnect to [************:7004]: connection timed out: /************:7004
2025-05-26 19:21:19.267 [lettuce-nioEventLoop-4-2] WARN  io.lettuce.core.protocol.ConnectionWatchdog.log Line:153 - Cannot reconnect to [************:7005]: connection timed out: /************:7005
2025-05-26 19:21:19.267 [lettuce-nioEventLoop-4-1] WARN  io.lettuce.core.protocol.ConnectionWatchdog.log Line:153 - Cannot reconnect to [************:7005]: connection timed out: /************:7005
2025-05-26 19:21:19.469 [lettuce-nioEventLoop-4-3] WARN  io.lettuce.core.protocol.ConnectionWatchdog.log Line:153 - Cannot reconnect to [************:7003]: connection timed out: /************:7003
2025-05-26 19:21:25.197 [lettuce-nioEventLoop-4-7] WARN  io.lettuce.core.cluster.topology.ClusterTopologyRefresh.lambda$getConnections$0 Line:284 - Unable to connect to [************:7002]: connection timed out: /************:7002
2025-05-26 19:21:25.197 [lettuce-nioEventLoop-4-9] WARN  io.lettuce.core.cluster.topology.ClusterTopologyRefresh.lambda$getConnections$0 Line:284 - Unable to connect to [************:7001]: connection timed out: /************:7001
2025-05-26 19:21:25.197 [lettuce-nioEventLoop-4-4] WARN  io.lettuce.core.cluster.topology.ClusterTopologyRefresh.lambda$getConnections$0 Line:284 - Unable to connect to [************:7006]: connection timed out: /************:7006
2025-05-26 19:21:25.197 [lettuce-nioEventLoop-4-8] WARN  io.lettuce.core.cluster.topology.ClusterTopologyRefresh.lambda$getConnections$0 Line:284 - Unable to connect to [************:7003]: connection timed out: /************:7003
2025-05-26 19:21:25.197 [lettuce-nioEventLoop-4-6] WARN  io.lettuce.core.cluster.topology.ClusterTopologyRefresh.lambda$getConnections$0 Line:284 - Unable to connect to [************:7005]: connection timed out: /************:7005
2025-05-26 19:21:25.197 [lettuce-nioEventLoop-4-5] WARN  io.lettuce.core.cluster.topology.ClusterTopologyRefresh.lambda$getConnections$0 Line:284 - Unable to connect to [************:7004]: connection timed out: /************:7004
2025-05-26 19:21:25.220 [lettuce-eventExecutorLoop-1-11] WARN  io.lettuce.core.cluster.ClusterTopologyRefreshScheduler.doRun Line:258 - Cannot refresh Redis Cluster topology
io.lettuce.core.RedisException: Cannot retrieve initial cluster partitions from initial URIs [RedisURI [host='************', port=7006], RedisURI [host='************', port=7004], RedisURI [host='************', port=7005], RedisURI [host='************', port=7002], RedisURI [host='************', port=7003], RedisURI [host='************', port=7001]]
	at io.lettuce.core.cluster.RedisClusterClient.loadPartitions(RedisClusterClient.java:848)
	at io.lettuce.core.cluster.RedisClusterClient.reloadPartitions(RedisClusterClient.java:784)
	at io.lettuce.core.cluster.ClusterTopologyRefreshScheduler$ClusterTopologyRefreshTask.doRun(ClusterTopologyRefreshScheduler.java:256)
	at io.lettuce.core.cluster.ClusterTopologyRefreshScheduler$ClusterTopologyRefreshTask.run(ClusterTopologyRefreshScheduler.java:236)
	at io.netty.util.concurrent.PromiseTask.runTask(PromiseTask.java:98)
	at io.netty.util.concurrent.PromiseTask.run(PromiseTask.java:106)
	at io.netty.util.concurrent.DefaultEventExecutor.run(DefaultEventExecutor.java:66)
	at io.netty.util.concurrent.SingleThreadEventExecutor$6.run(SingleThreadEventExecutor.java:1050)
	at io.netty.util.internal.ThreadExecutorMap$2.run(ThreadExecutorMap.java:74)
	at io.netty.util.concurrent.FastThreadLocalRunnable.run(FastThreadLocalRunnable.java:30)
	at java.lang.Thread.run(Thread.java:748)
	Suppressed: io.lettuce.core.RedisConnectionException: Unable to establish a connection to Redis Cluster at [RedisURI [host='************', port=7001], RedisURI [host='************', port=7002], RedisURI [host='************', port=7003], RedisURI [host='************', port=7004], RedisURI [host='************', port=7005], RedisURI [host='************', port=7006]]
		at io.lettuce.core.cluster.topology.AsyncConnections.get(AsyncConnections.java:89)
		at io.lettuce.core.cluster.topology.ClusterTopologyRefresh.loadViews(ClusterTopologyRefresh.java:76)
		at io.lettuce.core.cluster.RedisClusterClient.doLoadPartitions(RedisClusterClient.java:865)
		at io.lettuce.core.cluster.RedisClusterClient.loadPartitions(RedisClusterClient.java:838)
		... 10 common frames omitted
		Suppressed: io.lettuce.core.RedisConnectionException: Unable to connect to [************:7004]: connection timed out: /************:7004
			at io.lettuce.core.cluster.topology.ClusterTopologyRefresh.lambda$getConnections$0(ClusterTopologyRefresh.java:290)
			at java.util.concurrent.CompletableFuture.uniWhenComplete(CompletableFuture.java:760)
			at java.util.concurrent.CompletableFuture$UniWhenComplete.tryFire(CompletableFuture.java:736)
			at java.util.concurrent.CompletableFuture.postComplete(CompletableFuture.java:474)
			at java.util.concurrent.CompletableFuture.completeExceptionally(CompletableFuture.java:1977)
			at io.lettuce.core.AbstractRedisClient.lambda$initializeChannelAsync0$4(AbstractRedisClient.java:329)
			at io.netty.util.concurrent.DefaultPromise.notifyListener0(DefaultPromise.java:577)
			at io.netty.util.concurrent.DefaultPromise.notifyListeners0(DefaultPromise.java:570)
			at io.netty.util.concurrent.DefaultPromise.notifyListenersNow(DefaultPromise.java:549)
			at io.netty.util.concurrent.DefaultPromise.notifyListeners(DefaultPromise.java:490)
			at io.netty.util.concurrent.DefaultPromise.setValue0(DefaultPromise.java:615)
			at io.netty.util.concurrent.DefaultPromise.setFailure0(DefaultPromise.java:608)
			at io.netty.util.concurrent.DefaultPromise.tryFailure(DefaultPromise.java:117)
			at io.netty.channel.nio.AbstractNioChannel$AbstractNioUnsafe$1.run(AbstractNioChannel.java:263)
			at io.netty.util.concurrent.PromiseTask.runTask(PromiseTask.java:98)
			at io.netty.util.concurrent.ScheduledFutureTask.run(ScheduledFutureTask.java:150)
			at io.netty.util.concurrent.AbstractEventExecutor.safeExecute$$$capture(AbstractEventExecutor.java:163)
			at io.netty.util.concurrent.AbstractEventExecutor.safeExecute(AbstractEventExecutor.java)
			at io.netty.util.concurrent.SingleThreadEventExecutor.runAllTasks(SingleThreadEventExecutor.java:510)
			at io.netty.channel.nio.NioEventLoop.run(NioEventLoop.java:518)
			... 4 common frames omitted
		Caused by: io.netty.channel.ConnectTimeoutException: connection timed out: /************:7004
			at io.netty.channel.nio.AbstractNioChannel$AbstractNioUnsafe$1.run(AbstractNioChannel.java:261)
			... 10 common frames omitted
		Suppressed: io.lettuce.core.RedisConnectionException: Unable to connect to [************:7005]: connection timed out: /************:7005
			at io.lettuce.core.cluster.topology.ClusterTopologyRefresh.lambda$getConnections$0(ClusterTopologyRefresh.java:290)
			at java.util.concurrent.CompletableFuture.uniWhenComplete(CompletableFuture.java:760)
			at java.util.concurrent.CompletableFuture$UniWhenComplete.tryFire(CompletableFuture.java:736)
			at java.util.concurrent.CompletableFuture.postComplete(CompletableFuture.java:474)
			at java.util.concurrent.CompletableFuture.completeExceptionally(CompletableFuture.java:1977)
			at io.lettuce.core.AbstractRedisClient.lambda$initializeChannelAsync0$4(AbstractRedisClient.java:329)
			at io.netty.util.concurrent.DefaultPromise.notifyListener0(DefaultPromise.java:577)
			at io.netty.util.concurrent.DefaultPromise.notifyListeners0(DefaultPromise.java:570)
			at io.netty.util.concurrent.DefaultPromise.notifyListenersNow(DefaultPromise.java:549)
			at io.netty.util.concurrent.DefaultPromise.notifyListeners(DefaultPromise.java:490)
			at io.netty.util.concurrent.DefaultPromise.setValue0(DefaultPromise.java:615)
			at io.netty.util.concurrent.DefaultPromise.setFailure0(DefaultPromise.java:608)
			at io.netty.util.concurrent.DefaultPromise.tryFailure(DefaultPromise.java:117)
			at io.netty.channel.nio.AbstractNioChannel$AbstractNioUnsafe$1.run(AbstractNioChannel.java:263)
			at io.netty.util.concurrent.PromiseTask.runTask(PromiseTask.java:98)
			at io.netty.util.concurrent.ScheduledFutureTask.run(ScheduledFutureTask.java:150)
			at io.netty.util.concurrent.AbstractEventExecutor.safeExecute$$$capture(AbstractEventExecutor.java:163)
			at io.netty.util.concurrent.AbstractEventExecutor.safeExecute(AbstractEventExecutor.java)
			at io.netty.util.concurrent.SingleThreadEventExecutor.runAllTasks(SingleThreadEventExecutor.java:510)
			at io.netty.channel.nio.NioEventLoop.run(NioEventLoop.java:518)
			... 4 common frames omitted
		Caused by: io.netty.channel.ConnectTimeoutException: connection timed out: /************:7005
			at io.netty.channel.nio.AbstractNioChannel$AbstractNioUnsafe$1.run(AbstractNioChannel.java:261)
			... 10 common frames omitted
		Suppressed: io.lettuce.core.RedisConnectionException: Unable to connect to [************:7001]: connection timed out: /************:7001
			at io.lettuce.core.cluster.topology.ClusterTopologyRefresh.lambda$getConnections$0(ClusterTopologyRefresh.java:290)
			at java.util.concurrent.CompletableFuture.uniWhenComplete(CompletableFuture.java:760)
			at java.util.concurrent.CompletableFuture$UniWhenComplete.tryFire(CompletableFuture.java:736)
			at java.util.concurrent.CompletableFuture.postComplete(CompletableFuture.java:474)
			at java.util.concurrent.CompletableFuture.completeExceptionally(CompletableFuture.java:1977)
			at io.lettuce.core.AbstractRedisClient.lambda$initializeChannelAsync0$4(AbstractRedisClient.java:329)
			at io.netty.util.concurrent.DefaultPromise.notifyListener0(DefaultPromise.java:577)
			at io.netty.util.concurrent.DefaultPromise.notifyListeners0(DefaultPromise.java:570)
			at io.netty.util.concurrent.DefaultPromise.notifyListenersNow(DefaultPromise.java:549)
			at io.netty.util.concurrent.DefaultPromise.notifyListeners(DefaultPromise.java:490)
			at io.netty.util.concurrent.DefaultPromise.setValue0(DefaultPromise.java:615)
			at io.netty.util.concurrent.DefaultPromise.setFailure0(DefaultPromise.java:608)
			at io.netty.util.concurrent.DefaultPromise.tryFailure(DefaultPromise.java:117)
			at io.netty.channel.nio.AbstractNioChannel$AbstractNioUnsafe$1.run(AbstractNioChannel.java:263)
			at io.netty.util.concurrent.PromiseTask.runTask(PromiseTask.java:98)
			at io.netty.util.concurrent.ScheduledFutureTask.run(ScheduledFutureTask.java:150)
			at io.netty.util.concurrent.AbstractEventExecutor.safeExecute$$$capture(AbstractEventExecutor.java:163)
			at io.netty.util.concurrent.AbstractEventExecutor.safeExecute(AbstractEventExecutor.java)
			at io.netty.util.concurrent.SingleThreadEventExecutor.runAllTasks(SingleThreadEventExecutor.java:510)
			at io.netty.channel.nio.NioEventLoop.run(NioEventLoop.java:518)
			... 4 common frames omitted
		Caused by: io.netty.channel.ConnectTimeoutException: connection timed out: /************:7001
			at io.netty.channel.nio.AbstractNioChannel$AbstractNioUnsafe$1.run(AbstractNioChannel.java:261)
			... 10 common frames omitted
		Suppressed: io.lettuce.core.RedisConnectionException: Unable to connect to [************:7003]: connection timed out: /************:7003
			at io.lettuce.core.cluster.topology.ClusterTopologyRefresh.lambda$getConnections$0(ClusterTopologyRefresh.java:290)
			at java.util.concurrent.CompletableFuture.uniWhenComplete(CompletableFuture.java:760)
			at java.util.concurrent.CompletableFuture$UniWhenComplete.tryFire(CompletableFuture.java:736)
			at java.util.concurrent.CompletableFuture.postComplete(CompletableFuture.java:474)
			at java.util.concurrent.CompletableFuture.completeExceptionally(CompletableFuture.java:1977)
			at io.lettuce.core.AbstractRedisClient.lambda$initializeChannelAsync0$4(AbstractRedisClient.java:329)
			at io.netty.util.concurrent.DefaultPromise.notifyListener0(DefaultPromise.java:577)
			at io.netty.util.concurrent.DefaultPromise.notifyListeners0(DefaultPromise.java:570)
			at io.netty.util.concurrent.DefaultPromise.notifyListenersNow(DefaultPromise.java:549)
			at io.netty.util.concurrent.DefaultPromise.notifyListeners(DefaultPromise.java:490)
			at io.netty.util.concurrent.DefaultPromise.setValue0(DefaultPromise.java:615)
			at io.netty.util.concurrent.DefaultPromise.setFailure0(DefaultPromise.java:608)
			at io.netty.util.concurrent.DefaultPromise.tryFailure(DefaultPromise.java:117)
			at io.netty.channel.nio.AbstractNioChannel$AbstractNioUnsafe$1.run(AbstractNioChannel.java:263)
			at io.netty.util.concurrent.PromiseTask.runTask(PromiseTask.java:98)
			at io.netty.util.concurrent.ScheduledFutureTask.run(ScheduledFutureTask.java:150)
			at io.netty.util.concurrent.AbstractEventExecutor.safeExecute$$$capture(AbstractEventExecutor.java:163)
			at io.netty.util.concurrent.AbstractEventExecutor.safeExecute(AbstractEventExecutor.java)
			at io.netty.util.concurrent.SingleThreadEventExecutor.runAllTasks(SingleThreadEventExecutor.java:510)
			at io.netty.channel.nio.NioEventLoop.run(NioEventLoop.java:518)
			... 4 common frames omitted
		Caused by: io.netty.channel.ConnectTimeoutException: connection timed out: /************:7003
			at io.netty.channel.nio.AbstractNioChannel$AbstractNioUnsafe$1.run(AbstractNioChannel.java:261)
			... 10 common frames omitted
		Suppressed: io.lettuce.core.RedisConnectionException: Unable to connect to [************:7006]: connection timed out: /************:7006
			at io.lettuce.core.cluster.topology.ClusterTopologyRefresh.lambda$getConnections$0(ClusterTopologyRefresh.java:290)
			at java.util.concurrent.CompletableFuture.uniWhenComplete(CompletableFuture.java:760)
			at java.util.concurrent.CompletableFuture$UniWhenComplete.tryFire(CompletableFuture.java:736)
			at java.util.concurrent.CompletableFuture.postComplete(CompletableFuture.java:474)
			at java.util.concurrent.CompletableFuture.completeExceptionally(CompletableFuture.java:1977)
			at io.lettuce.core.AbstractRedisClient.lambda$initializeChannelAsync0$4(AbstractRedisClient.java:329)
			at io.netty.util.concurrent.DefaultPromise.notifyListener0(DefaultPromise.java:577)
			at io.netty.util.concurrent.DefaultPromise.notifyListeners0(DefaultPromise.java:570)
			at io.netty.util.concurrent.DefaultPromise.notifyListenersNow(DefaultPromise.java:549)
			at io.netty.util.concurrent.DefaultPromise.notifyListeners(DefaultPromise.java:490)
			at io.netty.util.concurrent.DefaultPromise.setValue0(DefaultPromise.java:615)
			at io.netty.util.concurrent.DefaultPromise.setFailure0(DefaultPromise.java:608)
			at io.netty.util.concurrent.DefaultPromise.tryFailure(DefaultPromise.java:117)
			at io.netty.channel.nio.AbstractNioChannel$AbstractNioUnsafe$1.run(AbstractNioChannel.java:263)
			at io.netty.util.concurrent.PromiseTask.runTask(PromiseTask.java:98)
			at io.netty.util.concurrent.ScheduledFutureTask.run(ScheduledFutureTask.java:150)
			at io.netty.util.concurrent.AbstractEventExecutor.safeExecute$$$capture(AbstractEventExecutor.java:163)
			at io.netty.util.concurrent.AbstractEventExecutor.safeExecute(AbstractEventExecutor.java)
			at io.netty.util.concurrent.SingleThreadEventExecutor.runAllTasks(SingleThreadEventExecutor.java:510)
			at io.netty.channel.nio.NioEventLoop.run(NioEventLoop.java:518)
			... 4 common frames omitted
		Caused by: io.netty.channel.ConnectTimeoutException: connection timed out: /************:7006
			at io.netty.channel.nio.AbstractNioChannel$AbstractNioUnsafe$1.run(AbstractNioChannel.java:261)
			... 10 common frames omitted
	Caused by: io.lettuce.core.RedisConnectionException: Unable to connect to [************:7002]: connection timed out: /************:7002
		at io.lettuce.core.cluster.topology.ClusterTopologyRefresh.lambda$getConnections$0(ClusterTopologyRefresh.java:290)
		at java.util.concurrent.CompletableFuture.uniWhenComplete(CompletableFuture.java:760)
		at java.util.concurrent.CompletableFuture$UniWhenComplete.tryFire(CompletableFuture.java:736)
		at java.util.concurrent.CompletableFuture.postComplete(CompletableFuture.java:474)
		at java.util.concurrent.CompletableFuture.completeExceptionally(CompletableFuture.java:1977)
		at io.lettuce.core.AbstractRedisClient.lambda$initializeChannelAsync0$4(AbstractRedisClient.java:329)
		at io.netty.util.concurrent.DefaultPromise.notifyListener0(DefaultPromise.java:577)
		at io.netty.util.concurrent.DefaultPromise.notifyListeners0(DefaultPromise.java:570)
		at io.netty.util.concurrent.DefaultPromise.notifyListenersNow(DefaultPromise.java:549)
		at io.netty.util.concurrent.DefaultPromise.notifyListeners(DefaultPromise.java:490)
		at io.netty.util.concurrent.DefaultPromise.setValue0(DefaultPromise.java:615)
		at io.netty.util.concurrent.DefaultPromise.setFailure0(DefaultPromise.java:608)
		at io.netty.util.concurrent.DefaultPromise.tryFailure(DefaultPromise.java:117)
		at io.netty.channel.nio.AbstractNioChannel$AbstractNioUnsafe$1.run(AbstractNioChannel.java:263)
		at io.netty.util.concurrent.PromiseTask.runTask(PromiseTask.java:98)
		at io.netty.util.concurrent.ScheduledFutureTask.run(ScheduledFutureTask.java:150)
		at io.netty.util.concurrent.AbstractEventExecutor.safeExecute$$$capture(AbstractEventExecutor.java:163)
		at io.netty.util.concurrent.AbstractEventExecutor.safeExecute(AbstractEventExecutor.java)
		at io.netty.util.concurrent.SingleThreadEventExecutor.runAllTasks(SingleThreadEventExecutor.java:510)
		at io.netty.channel.nio.NioEventLoop.run(NioEventLoop.java:518)
		... 4 common frames omitted
	Caused by: io.netty.channel.ConnectTimeoutException: connection timed out: /************:7002
		at io.netty.channel.nio.AbstractNioChannel$AbstractNioUnsafe$1.run(AbstractNioChannel.java:261)
		... 10 common frames omitted
Caused by: io.lettuce.core.RedisConnectionException: Unable to establish a connection to Redis Cluster at [RedisURI [host='************', port=7001], RedisURI [host='************', port=7002], RedisURI [host='************', port=7003], RedisURI [host='************', port=7004], RedisURI [host='************', port=7005], RedisURI [host='************', port=7006]]
	at io.lettuce.core.cluster.topology.AsyncConnections.get(AsyncConnections.java:89)
	at io.lettuce.core.cluster.topology.ClusterTopologyRefresh.loadViews(ClusterTopologyRefresh.java:76)
	at io.lettuce.core.cluster.RedisClusterClient.doLoadPartitions(RedisClusterClient.java:865)
	at io.lettuce.core.cluster.RedisClusterClient.loadPartitions(RedisClusterClient.java:845)
	... 10 common frames omitted
	Suppressed: io.lettuce.core.RedisConnectionException: Unable to connect to [************:7001]: connection timed out: /************:7001
		at io.lettuce.core.cluster.topology.ClusterTopologyRefresh.lambda$getConnections$0(ClusterTopologyRefresh.java:290)
		at java.util.concurrent.CompletableFuture.uniWhenComplete(CompletableFuture.java:760)
		at java.util.concurrent.CompletableFuture$UniWhenComplete.tryFire(CompletableFuture.java:736)
		at java.util.concurrent.CompletableFuture.postComplete(CompletableFuture.java:474)
		at java.util.concurrent.CompletableFuture.completeExceptionally(CompletableFuture.java:1977)
		at io.lettuce.core.AbstractRedisClient.lambda$initializeChannelAsync0$4(AbstractRedisClient.java:329)
		at io.netty.util.concurrent.DefaultPromise.notifyListener0(DefaultPromise.java:577)
		at io.netty.util.concurrent.DefaultPromise.notifyListeners0(DefaultPromise.java:570)
		at io.netty.util.concurrent.DefaultPromise.notifyListenersNow(DefaultPromise.java:549)
		at io.netty.util.concurrent.DefaultPromise.notifyListeners(DefaultPromise.java:490)
		at io.netty.util.concurrent.DefaultPromise.setValue0(DefaultPromise.java:615)
		at io.netty.util.concurrent.DefaultPromise.setFailure0(DefaultPromise.java:608)
		at io.netty.util.concurrent.DefaultPromise.tryFailure(DefaultPromise.java:117)
		at io.netty.channel.nio.AbstractNioChannel$AbstractNioUnsafe$1.run(AbstractNioChannel.java:263)
		at io.netty.util.concurrent.PromiseTask.runTask(PromiseTask.java:98)
		at io.netty.util.concurrent.ScheduledFutureTask.run(ScheduledFutureTask.java:150)
		at io.netty.util.concurrent.AbstractEventExecutor.safeExecute$$$capture(AbstractEventExecutor.java:163)
		at io.netty.util.concurrent.AbstractEventExecutor.safeExecute(AbstractEventExecutor.java)
		at io.netty.util.concurrent.SingleThreadEventExecutor.runAllTasks(SingleThreadEventExecutor.java:510)
		at io.netty.channel.nio.NioEventLoop.run(NioEventLoop.java:518)
		... 4 common frames omitted
	Caused by: io.netty.channel.ConnectTimeoutException: connection timed out: /************:7001
		at io.netty.channel.nio.AbstractNioChannel$AbstractNioUnsafe$1.run(AbstractNioChannel.java:261)
		... 10 common frames omitted
	Suppressed: io.lettuce.core.RedisConnectionException: Unable to connect to [************:7006]: connection timed out: /************:7006
		at io.lettuce.core.cluster.topology.ClusterTopologyRefresh.lambda$getConnections$0(ClusterTopologyRefresh.java:290)
		at java.util.concurrent.CompletableFuture.uniWhenComplete(CompletableFuture.java:760)
		at java.util.concurrent.CompletableFuture$UniWhenComplete.tryFire(CompletableFuture.java:736)
		at java.util.concurrent.CompletableFuture.postComplete(CompletableFuture.java:474)
		at java.util.concurrent.CompletableFuture.completeExceptionally(CompletableFuture.java:1977)
		at io.lettuce.core.AbstractRedisClient.lambda$initializeChannelAsync0$4(AbstractRedisClient.java:329)
		at io.netty.util.concurrent.DefaultPromise.notifyListener0(DefaultPromise.java:577)
		at io.netty.util.concurrent.DefaultPromise.notifyListeners0(DefaultPromise.java:570)
		at io.netty.util.concurrent.DefaultPromise.notifyListenersNow(DefaultPromise.java:549)
		at io.netty.util.concurrent.DefaultPromise.notifyListeners(DefaultPromise.java:490)
		at io.netty.util.concurrent.DefaultPromise.setValue0(DefaultPromise.java:615)
		at io.netty.util.concurrent.DefaultPromise.setFailure0(DefaultPromise.java:608)
		at io.netty.util.concurrent.DefaultPromise.tryFailure(DefaultPromise.java:117)
		at io.netty.channel.nio.AbstractNioChannel$AbstractNioUnsafe$1.run(AbstractNioChannel.java:263)
		at io.netty.util.concurrent.PromiseTask.runTask(PromiseTask.java:98)
		at io.netty.util.concurrent.ScheduledFutureTask.run(ScheduledFutureTask.java:150)
		at io.netty.util.concurrent.AbstractEventExecutor.safeExecute$$$capture(AbstractEventExecutor.java:163)
		at io.netty.util.concurrent.AbstractEventExecutor.safeExecute(AbstractEventExecutor.java)
		at io.netty.util.concurrent.SingleThreadEventExecutor.runAllTasks(SingleThreadEventExecutor.java:510)
		at io.netty.channel.nio.NioEventLoop.run(NioEventLoop.java:518)
		... 4 common frames omitted
	Caused by: io.netty.channel.ConnectTimeoutException: connection timed out: /************:7006
		at io.netty.channel.nio.AbstractNioChannel$AbstractNioUnsafe$1.run(AbstractNioChannel.java:261)
		... 10 common frames omitted
	Suppressed: io.lettuce.core.RedisConnectionException: Unable to connect to [************:7003]: connection timed out: /************:7003
		at io.lettuce.core.cluster.topology.ClusterTopologyRefresh.lambda$getConnections$0(ClusterTopologyRefresh.java:290)
		at java.util.concurrent.CompletableFuture.uniWhenComplete(CompletableFuture.java:760)
		at java.util.concurrent.CompletableFuture$UniWhenComplete.tryFire(CompletableFuture.java:736)
		at java.util.concurrent.CompletableFuture.postComplete(CompletableFuture.java:474)
		at java.util.concurrent.CompletableFuture.completeExceptionally(CompletableFuture.java:1977)
		at io.lettuce.core.AbstractRedisClient.lambda$initializeChannelAsync0$4(AbstractRedisClient.java:329)
		at io.netty.util.concurrent.DefaultPromise.notifyListener0(DefaultPromise.java:577)
		at io.netty.util.concurrent.DefaultPromise.notifyListeners0(DefaultPromise.java:570)
		at io.netty.util.concurrent.DefaultPromise.notifyListenersNow(DefaultPromise.java:549)
		at io.netty.util.concurrent.DefaultPromise.notifyListeners(DefaultPromise.java:490)
		at io.netty.util.concurrent.DefaultPromise.setValue0(DefaultPromise.java:615)
		at io.netty.util.concurrent.DefaultPromise.setFailure0(DefaultPromise.java:608)
		at io.netty.util.concurrent.DefaultPromise.tryFailure(DefaultPromise.java:117)
		at io.netty.channel.nio.AbstractNioChannel$AbstractNioUnsafe$1.run(AbstractNioChannel.java:263)
		at io.netty.util.concurrent.PromiseTask.runTask(PromiseTask.java:98)
		at io.netty.util.concurrent.ScheduledFutureTask.run(ScheduledFutureTask.java:150)
		at io.netty.util.concurrent.AbstractEventExecutor.safeExecute$$$capture(AbstractEventExecutor.java:163)
		at io.netty.util.concurrent.AbstractEventExecutor.safeExecute(AbstractEventExecutor.java)
		at io.netty.util.concurrent.SingleThreadEventExecutor.runAllTasks(SingleThreadEventExecutor.java:510)
		at io.netty.channel.nio.NioEventLoop.run(NioEventLoop.java:518)
		... 4 common frames omitted
	Caused by: io.netty.channel.ConnectTimeoutException: connection timed out: /************:7003
		at io.netty.channel.nio.AbstractNioChannel$AbstractNioUnsafe$1.run(AbstractNioChannel.java:261)
		... 10 common frames omitted
	Suppressed: io.lettuce.core.RedisConnectionException: Unable to connect to [************:7004]: connection timed out: /************:7004
		at io.lettuce.core.cluster.topology.ClusterTopologyRefresh.lambda$getConnections$0(ClusterTopologyRefresh.java:290)
		at java.util.concurrent.CompletableFuture.uniWhenComplete(CompletableFuture.java:760)
		at java.util.concurrent.CompletableFuture$UniWhenComplete.tryFire(CompletableFuture.java:736)
		at java.util.concurrent.CompletableFuture.postComplete(CompletableFuture.java:474)
		at java.util.concurrent.CompletableFuture.completeExceptionally(CompletableFuture.java:1977)
		at io.lettuce.core.AbstractRedisClient.lambda$initializeChannelAsync0$4(AbstractRedisClient.java:329)
		at io.netty.util.concurrent.DefaultPromise.notifyListener0(DefaultPromise.java:577)
		at io.netty.util.concurrent.DefaultPromise.notifyListeners0(DefaultPromise.java:570)
		at io.netty.util.concurrent.DefaultPromise.notifyListenersNow(DefaultPromise.java:549)
		at io.netty.util.concurrent.DefaultPromise.notifyListeners(DefaultPromise.java:490)
		at io.netty.util.concurrent.DefaultPromise.setValue0(DefaultPromise.java:615)
		at io.netty.util.concurrent.DefaultPromise.setFailure0(DefaultPromise.java:608)
		at io.netty.util.concurrent.DefaultPromise.tryFailure(DefaultPromise.java:117)
		at io.netty.channel.nio.AbstractNioChannel$AbstractNioUnsafe$1.run(AbstractNioChannel.java:263)
		at io.netty.util.concurrent.PromiseTask.runTask(PromiseTask.java:98)
		at io.netty.util.concurrent.ScheduledFutureTask.run(ScheduledFutureTask.java:150)
		at io.netty.util.concurrent.AbstractEventExecutor.safeExecute$$$capture(AbstractEventExecutor.java:163)
		at io.netty.util.concurrent.AbstractEventExecutor.safeExecute(AbstractEventExecutor.java)
		at io.netty.util.concurrent.SingleThreadEventExecutor.runAllTasks(SingleThreadEventExecutor.java:510)
		at io.netty.channel.nio.NioEventLoop.run(NioEventLoop.java:518)
		... 4 common frames omitted
	Caused by: io.netty.channel.ConnectTimeoutException: connection timed out: /************:7004
		at io.netty.channel.nio.AbstractNioChannel$AbstractNioUnsafe$1.run(AbstractNioChannel.java:261)
		... 10 common frames omitted
	Suppressed: io.lettuce.core.RedisConnectionException: Unable to connect to [************:7005]: connection timed out: /************:7005
		at io.lettuce.core.cluster.topology.ClusterTopologyRefresh.lambda$getConnections$0(ClusterTopologyRefresh.java:290)
		at java.util.concurrent.CompletableFuture.uniWhenComplete(CompletableFuture.java:760)
		at java.util.concurrent.CompletableFuture$UniWhenComplete.tryFire(CompletableFuture.java:736)
		at java.util.concurrent.CompletableFuture.postComplete(CompletableFuture.java:474)
		at java.util.concurrent.CompletableFuture.completeExceptionally(CompletableFuture.java:1977)
		at io.lettuce.core.AbstractRedisClient.lambda$initializeChannelAsync0$4(AbstractRedisClient.java:329)
		at io.netty.util.concurrent.DefaultPromise.notifyListener0(DefaultPromise.java:577)
		at io.netty.util.concurrent.DefaultPromise.notifyListeners0(DefaultPromise.java:570)
		at io.netty.util.concurrent.DefaultPromise.notifyListenersNow(DefaultPromise.java:549)
		at io.netty.util.concurrent.DefaultPromise.notifyListeners(DefaultPromise.java:490)
		at io.netty.util.concurrent.DefaultPromise.setValue0(DefaultPromise.java:615)
		at io.netty.util.concurrent.DefaultPromise.setFailure0(DefaultPromise.java:608)
		at io.netty.util.concurrent.DefaultPromise.tryFailure(DefaultPromise.java:117)
		at io.netty.channel.nio.AbstractNioChannel$AbstractNioUnsafe$1.run(AbstractNioChannel.java:263)
		at io.netty.util.concurrent.PromiseTask.runTask(PromiseTask.java:98)
		at io.netty.util.concurrent.ScheduledFutureTask.run(ScheduledFutureTask.java:150)
		at io.netty.util.concurrent.AbstractEventExecutor.safeExecute$$$capture(AbstractEventExecutor.java:163)
		at io.netty.util.concurrent.AbstractEventExecutor.safeExecute(AbstractEventExecutor.java)
		at io.netty.util.concurrent.SingleThreadEventExecutor.runAllTasks(SingleThreadEventExecutor.java:510)
		at io.netty.channel.nio.NioEventLoop.run(NioEventLoop.java:518)
		... 4 common frames omitted
	Caused by: io.netty.channel.ConnectTimeoutException: connection timed out: /************:7005
		at io.netty.channel.nio.AbstractNioChannel$AbstractNioUnsafe$1.run(AbstractNioChannel.java:261)
		... 10 common frames omitted
Caused by: io.lettuce.core.RedisConnectionException: Unable to connect to [************:7002]: connection timed out: /************:7002
	at io.lettuce.core.cluster.topology.ClusterTopologyRefresh.lambda$getConnections$0(ClusterTopologyRefresh.java:290)
	at java.util.concurrent.CompletableFuture.uniWhenComplete(CompletableFuture.java:760)
	at java.util.concurrent.CompletableFuture$UniWhenComplete.tryFire(CompletableFuture.java:736)
	at java.util.concurrent.CompletableFuture.postComplete(CompletableFuture.java:474)
	at java.util.concurrent.CompletableFuture.completeExceptionally(CompletableFuture.java:1977)
	at io.lettuce.core.AbstractRedisClient.lambda$initializeChannelAsync0$4(AbstractRedisClient.java:329)
	at io.netty.util.concurrent.DefaultPromise.notifyListener0(DefaultPromise.java:577)
	at io.netty.util.concurrent.DefaultPromise.notifyListeners0(DefaultPromise.java:570)
	at io.netty.util.concurrent.DefaultPromise.notifyListenersNow(DefaultPromise.java:549)
	at io.netty.util.concurrent.DefaultPromise.notifyListeners(DefaultPromise.java:490)
	at io.netty.util.concurrent.DefaultPromise.setValue0(DefaultPromise.java:615)
	at io.netty.util.concurrent.DefaultPromise.setFailure0(DefaultPromise.java:608)
	at io.netty.util.concurrent.DefaultPromise.tryFailure(DefaultPromise.java:117)
	at io.netty.channel.nio.AbstractNioChannel$AbstractNioUnsafe$1.run(AbstractNioChannel.java:263)
	at io.netty.util.concurrent.PromiseTask.runTask(PromiseTask.java:98)
	at io.netty.util.concurrent.ScheduledFutureTask.run(ScheduledFutureTask.java:150)
	at io.netty.util.concurrent.AbstractEventExecutor.safeExecute$$$capture(AbstractEventExecutor.java:163)
	at io.netty.util.concurrent.AbstractEventExecutor.safeExecute(AbstractEventExecutor.java)
	at io.netty.util.concurrent.SingleThreadEventExecutor.runAllTasks(SingleThreadEventExecutor.java:510)
	at io.netty.channel.nio.NioEventLoop.run(NioEventLoop.java:518)
	... 4 common frames omitted
Caused by: io.netty.channel.ConnectTimeoutException: connection timed out: /************:7002
	at io.netty.channel.nio.AbstractNioChannel$AbstractNioUnsafe$1.run(AbstractNioChannel.java:261)
	... 10 common frames omitted
2025-05-26 19:21:28.359 [lettuce-nioEventLoop-4-10] WARN  io.lettuce.core.protocol.ConnectionWatchdog.log Line:153 - Cannot reconnect to [************:7005]: connection timed out: /************:7005
2025-05-26 19:21:29.364 [lettuce-nioEventLoop-4-12] WARN  io.lettuce.core.protocol.ConnectionWatchdog.log Line:153 - Cannot reconnect to [************:7002]: connection timed out: /************:7002
2025-05-26 19:21:29.364 [lettuce-nioEventLoop-4-1] WARN  io.lettuce.core.protocol.ConnectionWatchdog.log Line:153 - Cannot reconnect to [************:7002]: connection timed out: /************:7002
2025-05-26 19:21:29.364 [lettuce-nioEventLoop-4-11] WARN  io.lettuce.core.protocol.ConnectionWatchdog.log Line:153 - Cannot reconnect to [************:7004]: connection timed out: /************:7004
2025-05-26 19:21:29.566 [lettuce-nioEventLoop-4-2] WARN  io.lettuce.core.protocol.ConnectionWatchdog.log Line:153 - Cannot reconnect to [************:7003]: connection timed out: /************:7003
2025-05-26 19:21:35.228 [lettuce-nioEventLoop-4-8] WARN  io.lettuce.core.cluster.topology.ClusterTopologyRefresh.lambda$getConnections$0 Line:284 - Unable to connect to [************:7006]: connection timed out: /************:7006
2025-05-26 19:21:35.228 [lettuce-nioEventLoop-4-3] WARN  io.lettuce.core.cluster.topology.ClusterTopologyRefresh.lambda$getConnections$0 Line:284 - Unable to connect to [************:7001]: connection timed out: /************:7001
2025-05-26 19:21:35.228 [lettuce-nioEventLoop-4-5] WARN  io.lettuce.core.cluster.topology.ClusterTopologyRefresh.lambda$getConnections$0 Line:284 - Unable to connect to [************:7003]: connection timed out: /************:7003
2025-05-26 19:21:35.228 [lettuce-nioEventLoop-4-4] WARN  io.lettuce.core.cluster.topology.ClusterTopologyRefresh.lambda$getConnections$0 Line:284 - Unable to connect to [************:7002]: connection timed out: /************:7002
2025-05-26 19:21:35.228 [lettuce-nioEventLoop-4-7] WARN  io.lettuce.core.cluster.topology.ClusterTopologyRefresh.lambda$getConnections$0 Line:284 - Unable to connect to [************:7005]: connection timed out: /************:7005
2025-05-26 19:21:35.228 [lettuce-nioEventLoop-4-6] WARN  io.lettuce.core.cluster.topology.ClusterTopologyRefresh.lambda$getConnections$0 Line:284 - Unable to connect to [************:7004]: connection timed out: /************:7004
2025-05-26 19:21:38.079 [org.springframework.amqp.rabbit.RabbitListenerEndpointContainer#0-5] WARN  org.springframework.amqp.rabbit.listener.SimpleMessageListenerContainer.logConsumerException Line:1440 - Consumer raised exception, processing can restart if the connection factory supports it. Exception summary: org.springframework.amqp.AmqpConnectException: java.net.ConnectException: Connection timed out: connect
2025-05-26 20:02:04.440 [AMQP Connection ************:5672] WARN  com.rabbitmq.client.impl.ForgivingExceptionHandler.log Line:115 - An unexpected connection driver error occured (Exception message: Connection reset)
2025-05-26 20:02:04.441 [AMQP Connection ************:5672] WARN  com.rabbitmq.client.impl.ForgivingExceptionHandler.log Line:115 - An unexpected connection driver error occured (Exception message: Connection reset)
2025-05-26 20:02:04.516 [AMQP Connection ************:5672] WARN  com.rabbitmq.client.impl.ForgivingExceptionHandler.log Line:115 - An unexpected connection driver error occured (Exception message: Connection reset)
2025-05-26 20:02:08.512 [AMQP Connection ************:5672] WARN  com.rabbitmq.client.impl.ForgivingExceptionHandler.log Line:115 - An unexpected connection driver error occured (Exception message: Connection reset)
2025-05-26 20:42:35.794 [AMQP Connection ************:5672] WARN  com.rabbitmq.client.impl.ForgivingExceptionHandler.log Line:115 - An unexpected connection driver error occured (Exception message: Connection reset)
2025-05-26 20:42:35.794 [AMQP Connection ************:5672] WARN  com.rabbitmq.client.impl.ForgivingExceptionHandler.log Line:115 - An unexpected connection driver error occured (Exception message: Connection reset)
2025-05-26 20:42:35.842 [AMQP Connection ************:5672] WARN  com.rabbitmq.client.impl.ForgivingExceptionHandler.log Line:115 - An unexpected connection driver error occured (Exception message: Connection reset)
2025-05-26 20:42:39.877 [AMQP Connection ************:5672] WARN  com.rabbitmq.client.impl.ForgivingExceptionHandler.log Line:115 - An unexpected connection driver error occured (Exception message: Connection reset)
2025-05-26 21:23:37.248 [AMQP Connection ************:5672] WARN  com.rabbitmq.client.impl.ForgivingExceptionHandler.log Line:115 - An unexpected connection driver error occured (Exception message: Connection reset)
2025-05-26 21:23:37.249 [AMQP Connection ************:5672] WARN  com.rabbitmq.client.impl.ForgivingExceptionHandler.log Line:115 - An unexpected connection driver error occured (Exception message: Connection reset)
2025-05-26 21:23:37.263 [AMQP Connection ************:5672] WARN  com.rabbitmq.client.impl.ForgivingExceptionHandler.log Line:115 - An unexpected connection driver error occured (Exception message: Connection reset)
2025-05-26 21:23:41.306 [AMQP Connection ************:5672] WARN  com.rabbitmq.client.impl.ForgivingExceptionHandler.log Line:115 - An unexpected connection driver error occured (Exception message: Connection reset)
2025-05-26 22:04:08.559 [AMQP Connection ************:5672] WARN  com.rabbitmq.client.impl.ForgivingExceptionHandler.log Line:115 - An unexpected connection driver error occured (Exception message: Connection reset)
2025-05-26 22:04:08.623 [AMQP Connection ************:5672] WARN  com.rabbitmq.client.impl.ForgivingExceptionHandler.log Line:115 - An unexpected connection driver error occured (Exception message: Connection reset)
2025-05-26 22:04:08.637 [AMQP Connection ************:5672] WARN  com.rabbitmq.client.impl.ForgivingExceptionHandler.log Line:115 - An unexpected connection driver error occured (Exception message: Connection reset)
2025-05-26 22:04:12.612 [AMQP Connection ************:5672] WARN  com.rabbitmq.client.impl.ForgivingExceptionHandler.log Line:115 - An unexpected connection driver error occured (Exception message: Connection reset)
2025-05-26 22:44:39.728 [AMQP Connection ************:5672] WARN  com.rabbitmq.client.impl.ForgivingExceptionHandler.log Line:115 - An unexpected connection driver error occured (Exception message: Connection reset)
2025-05-26 22:44:39.728 [AMQP Connection ************:5672] WARN  com.rabbitmq.client.impl.ForgivingExceptionHandler.log Line:115 - An unexpected connection driver error occured (Exception message: Connection reset)
2025-05-26 22:44:39.773 [AMQP Connection ************:5672] WARN  com.rabbitmq.client.impl.ForgivingExceptionHandler.log Line:115 - An unexpected connection driver error occured (Exception message: Connection reset)
2025-05-26 22:44:43.716 [AMQP Connection ************:5672] WARN  com.rabbitmq.client.impl.ForgivingExceptionHandler.log Line:115 - An unexpected connection driver error occured (Exception message: Connection reset)
2025-05-26 23:25:41.286 [AMQP Connection ************:5672] WARN  com.rabbitmq.client.impl.ForgivingExceptionHandler.log Line:115 - An unexpected connection driver error occured (Exception message: Connection reset)
2025-05-26 23:25:41.285 [AMQP Connection ************:5672] WARN  com.rabbitmq.client.impl.ForgivingExceptionHandler.log Line:115 - An unexpected connection driver error occured (Exception message: Connection reset)
2025-05-26 23:25:41.286 [AMQP Connection ************:5672] WARN  com.rabbitmq.client.impl.ForgivingExceptionHandler.log Line:115 - An unexpected connection driver error occured (Exception message: Connection reset)
2025-05-26 23:25:45.337 [AMQP Connection ************:5672] WARN  com.rabbitmq.client.impl.ForgivingExceptionHandler.log Line:115 - An unexpected connection driver error occured (Exception message: Connection reset)
