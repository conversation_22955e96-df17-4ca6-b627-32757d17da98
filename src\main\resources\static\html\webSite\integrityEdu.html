<!DOCTYPE html>
<html xmlns="http://www.w3.org/1999/xhtml"
      xmlns:th="http://www.thymeleaf.org">
<head>
    <title>廉洁教育-清廉移动</title>
    <meta http-equiv="Content-Type" content="text/html; charset=utf-8"/>
    <meta http-equiv="X-UA-Compatible" content="IE=edge" />
    <meta name="viewport" content="width=device-width, initial-scale=1, shrink-to-fit=no"/>
    <!--
       <link href="http://************:8088/simbestui/fonts/iconfont/iconfont.css?v=svn.revision" th:href="@{http://************:8088/simbestui/fonts/iconfont/iconfont.css?v=svn.revision}" rel="stylesheet"/>
       -->
    <link href="../../fonts/iconfont/iconfont.css?v=svn.revision" th:href="@{/fonts/iconfont/iconfont.css?v=svn.revision}" rel="stylesheet"/>
    <link href="http://************:8088/simbestui/js/themes/default/easyui.css?v=svn.revision" th:href="@{http://************:8088/simbestui/js/themes/default/easyui.css?v=svn.revision}" rel="stylesheet"/>
    <link href="http://************:8088/simbestui/js/themes/icon.css?v=svn.revision" th:href="@{http://************:8088/simbestui/js/themes/icon.css?v=svn.revision}" rel="stylesheet"/>
    <link href="http://************:8088/simbestui/css/public.css?v=svn.revision" th:href="@{http://************:8088/simbestui/css/public.css?v=svn.revision}" rel="stylesheet"/>
    <link href="http://************:8088/simbestui/css/pages.css?v=svn.revision" th:href="@{http://************:8088/simbestui/css/pages.css?v=svn.revision}" rel="stylesheet"/>
    <link href="../../css/webSite.css?v=svn.revision" th:href="@{/css/webSite.css?v=svn.revision}" rel="stylesheet"/>
    <script src="http://************:8088/simbestui/js/jquery.min.js?v=svn.revision" th:src="@{http://************:8088/simbestui/js/jquery.min.js?v=svn.revision}" type="text/javascript"></script>
    <script src="http://************:8088/simbestui/js/jquery.easyui.min.js?v=svn.revision" th:src="@{http://************:8088/simbestui/js/jquery.easyui.min.js?v=svn.revision}" type="text/javascript"></script>
    <script src="http://************:8088/simbestui/js/easyui-lang-zh_CN.js?v=svn.revision" th:src="@{http://************:8088/simbestui/js/easyui-lang-zh_CN.js?v=svn.revision}" type="text/javascript"></script>
    <script src="../../js/jquery.config.js?v=svn.revision" th:src="@{/js/jquery.config.js?v=svn.revision}" type="text/javascript"></script>
    <script src="http://************:8088/simbestui/js/jquery.zjsfile.js?v=svn.revision" th:src="@{http://************:8088/simbestui/js/jquery.zjsfile.js?v=svn.revision}" type="text/javascript"></script>
    <script src="http://************:8088/simbestui/js/jquery.aduq.js?v=svn.revision" th:src="@{http://************:8088/simbestui/js/jquery.aduq.js?v=svn.revision}" type="text/javascript"></script>
    <script src="http://************:8088/simbestui/js/jquery.process.js?v=svn.revision" th:src="@{http://************:8088/simbestui/js/jquery.process.js?v=svn.revision}" type="text/javascript"></script>
    <script src="../../js/webSite.js?v=svn.revision" th:src="@{/js/webSite.js?v=svn.revision}" type="text/javascript"></script>
    <script type="text/javascript">
        $(function(){

            //获取当前登录人
            var gps=getQueryString();
            var url="action/templateLayout/constructEducationLayout/sso?appcode=hnjjwz&loginuser=" + gps.loginuser;
            if(gps.from=="oa"){
                ajaxgeneral({
                    url: "getCurrentUser/sso?appcode=hnjjwz&loginuser=" + gps.loginuser,
                    async:false,
                    success: function (ress) {
                    }
                });
            }else {
                url="action/templateLayout/constructEducationLayout";
            }
            webSite();

            ajaxgeneral({
                url:url,
                async: false,
                success:function(res) {
                    //渲染模块
                    var dataD = res.data[0];
                    //if (dataD.templateData[j].programaCoverFile) dataD.templateData[j].downLoadUrl = dataD.templateData[j].programaCoverFile[0].downLoadUrl;
                    var dH=["<div class=\"ptb15 of_hidden projectImg\">\n" +
                    "        <div class=\"modTit2 modTit3\">"+
                    fastrenderRow(dataD, "<strong class='fl' style='font-weight: 600'>{{locationName}}</strong><a target='_blank' href='list.html?id={{locationId}}' class='fr'>更多>></a></div>\n" +
                        "        <img class=\"fl\" src=\"/"+web.appCode+"{{templateUrl}}\"/>")
                    +"<ul class=\"list list_proImg\">"];
                    for (var j in dataD.templateData) {
                        dataD.templateData[j].creationTime = getdateformat(dataD.templateData[j].creationTime, "yyyy-MM-dd");
                    }
                    dH.push(fastrender(dataD.templateData, "<li><a target='_blank' href='listDetails.html?id={{id}}&pmInsId={{pmInsId}}'>·&nbsp;{{title}}<font class=\"fr\">{{creationTime}}</font></a></li>"));//[{{belongDepartmentName}}]
                    dH.push("</ul></div>");
                    $(".center").append(dH.join("")+"<div class=\"ptb15 of_hidden modelDiv\"></div>");

                    for(var i=1; i<res.data.length-1;i++) {
                        var dataD = res.data[i];
                        var dH=["<div class=\"modelD"+(i%3==0?" modelD3":"")+"\"><div class=\"modTit2 modTit4\">"+
                        fastrenderRow(dataD, "<strong class='fl'>{{locationName}}</strong><a target='_blank' href='list.html?id={{locationId}}' class='fr'>更多>></a>")
                        +"</div><ul class=\"list listT listRDL\">"];
                        for (var j in dataD.templateData) {
                            dataD.templateData[j].creationTime = getdateformat(dataD.templateData[j].creationTime, "yyyy-MM-dd");
                            //if (dataD.templateData[j].programaCoverFile) dataD.templateData[j].downLoadUrl = dataD.templateData[j].programaCoverFile[0].downLoadUrl;
                        }
                        dH.push(fastrender(dataD.templateData, "<li><a target='_blank' href='listDetails.html?id={{id}}&pmInsId={{pmInsId}}'><b>◆</b>{{title}}</a></li>"));
                        dH.push("</ul></div>");
                        $(".center .modelDiv").append(dH.join(""));
                    }


                    var dataD = res.data[res.data.length-1];
                    //if (dataD.templateData[j].programaCoverFile) dataD.templateData[j].downLoadUrl = dataD.templateData[j].programaCoverFile[0].downLoadUrl;
                    var dH=["<div class='link of_hidden'><div class='modTit2 modTit5'>"+
                    fastrenderRow(dataD, "<strong class='fl' style='font-weight: 600'><a target='_blank' href='"+(res.data[i].pointUrl || "list.html")+"?id={{locationId}}' class='fr'>{{locationName}}</a></strong></div>")
                    +"<ul class=\"listRB linkU2\"></ul></div>"];
                    $(".center").append(dH.join(""));
                    moduleF(dataD.templateUrl, {"index":12,"className":"link","htmlClass":"ul.linkU2","template":"<li><a target='_blank' href='list.html?id={{programaCode}}'><img src='/"+web.appCode+"{{downLoadUrl}}'/><font>{{programaName}}</font></a></li>"});

                    //9.29新增需求
                    $(".center").append("<div class='doubleImg'><a href='http://portal.ha.cmcc/was2/HaPortalSso/inner_sso/bps_hnjjwz2.jsp' target='_blank'><img src='../../images/whzpzs.jpg' alt=''></a>" +
                        "<a href='http://oa.ha.cmcc:8000/honest_clean/index.aspx' target='_blank'><img src='../../images/yljf.jpg' alt=''></a> </div>")

                    $(".doubleImg").find("a").eq(0).css("float","left");
                    $(".doubleImg").find("a").eq(1).css("float","right");
                }
            });
            /*var menuDataJson =sessionStorage.getItem("menuDataJson");
            var menuData =JSON.parse(menuDataJson);
            $("#h6").html(menuData[4].menuName+"-清廉移动");*/


        });
    </script>
</head>
<body>
<!--top-->

<div class="nav">
    <ul class="auto1024">
        <!--<li><a>首页</a></li>
        <li><a>信息公开</a></li>
        <li class="li_hover"><a>规章制度</a></li>
        <li><a>巡察工作</a></li>
        <li><a>嵌入式防控监督</a></li>
        <li><a>课题研究</a></li>
        <li><a>共享平台</a></li>
        <li>
            <a>数据报送</a>
            <ul>
                <li><a>领导人员廉洁情况活页夹</a></li>
                <li><a>信息报送</a></li>
            </ul>
        </li>
        <li class="nobor"><a>信访举报</a></li>-->
    </ul>
</div>
<!--center-->
<div class="auto1024 center">
    <!--<div class="ptb15 of_hidden projectImg">
        <div class="modTit2 modTit3"><strong class="fl">课题部署</strong><a class="fr">更多>></a></div>
        <img class="fl" src="http://photocdn.sohu.com/20111207/Img328215666.jpg"/>
        <ul class="list list_proImg">
            <li><a>·中共中央办公厅印发《中国共产党纪律检查机关监督执纪工作规则》 [纪检监察室（巡察工作办公室）]<font class="fr">2019-01-01</font></a></li>
            <li><a>·中共中央办公厅印发《中国共产党纪律检查机关监督执纪工作规则》 [纪检监察室（巡察工作办公室）]<font class="fr">2019-01-01</font></a></li>
            <li><a>·中共中央办公厅印发《中国共产党纪律检查机关监督执纪工作规则》 [纪检监察室（巡察工作办公室）]<font class="fr">2019-01-01</font></a></li>
            <li><a>·中共中央办公厅印发《中国共产党纪律检查机关监督执纪工作规则》 [纪检监察室（巡察工作办公室）]<font class="fr">2019-01-01</font></a></li>
            <li><a>·中共中央办公厅印发《中国共产党纪律检查机关监督执纪工作规则》 [纪检监察室（巡察工作办公室）]<font class="fr">2019-01-01</font></a></li>
        </ul>
    </div>
    <div class="ptb15 of_hidden">
        <div class="modelD">
            <div class="modTit2 modTit4"><strong class="fl">心系廉洁</strong><a class="fr">更多>></a></div>
            <ul class="list listT listRDL">
                <li><a>·杨晓渡：提高政治站位 深化专项治理 为打赢脱贫攻坚战提供坚强纪律保障</a></li>
                <li><a>中共中央印发《中国共产党纪律处分条例》</a></li>
                <li><a>·从通报看形势增定力之四：严惩不收敛不收手者</a></li>
            </ul>
        </div>
        <div class="modelD">
            <div class="modTit2 modTit4"><strong class="fl">心系廉洁</strong><a class="fr">更多>></a></div>
            <ul class="list listT listRDL">
                <li><a>·杨晓渡：提高政治站位 深化专项治理 为打赢脱贫攻坚战提供坚强纪律保障</a></li>
                <li><a>中共中央印发《中国共产党纪律处分条例》</a></li>
                <li><a>·从通报看形势增定力之四：严惩不收敛不收手者</a></li>
            </ul>
        </div>
        <div class="modelD modelD3">
            <div class="modTit2 modTit4"><strong class="fl">心系廉洁</strong><a class="fr">更多>></a></div>
            <ul class="list listT listRDL">
                <li><a>·杨晓渡：提高政治站位 深化专项治理 为打赢脱贫攻坚战提供坚强纪律保障</a></li>
                <li><a>中共中央印发《中国共产党纪律处分条例》</a></li>
                <li><a>·从通报看形势增定力之四：严惩不收敛不收手者</a></li>
            </ul>
        </div>
    </div>
    <div class="link of_hidden">
        <div class="modTit2 modTit5"><strong class="fl">廉洁文化</strong></div>
        <ul class="listRB linkU2">
            <li><a><img src="http://photocdn.sohu.com/20111207/Img328215666.jpg"/><p>故事</p></a></li>
            <li><a><img src="http://k.zol-img.com.cn/sjbbs/7692/a7691515_s.jpg"/><p>书画</p></a></li>
            <li><a><img src="http://photocdn.sohu.com/20120213/Img334502641.jpg"/><p>海报</p></a></li>
            <li><a><img src="http://photocdn.sohu.com/20111207/Img328215666.jpg"/><p>视频</p></a></li>
            <li><a><img src="http://photocdn.sohu.com/20111207/Img328215666.jpg"/><p>漫画</p></a></li>
        </ul>
    </div>-->
</div>
<!--copy-->
<div class="copy">©版权所有 中国移动通信集团河南有限公司</div>
</body>
</html>
