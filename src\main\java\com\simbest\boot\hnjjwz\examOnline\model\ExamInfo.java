package com.simbest.boot.hnjjwz.examOnline.model;

import com.simbest.boot.base.annotations.EntityIdPrefix;
import com.simbest.boot.base.model.LogicModel;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.*;
import org.hibernate.annotations.GenericGenerator;

import javax.persistence.*;
import java.util.List;

/**
 * <AUTHOR>
 * @Data 2019/05/08
 * Description 试卷得分表
 */
@Data
@EqualsAndHashCode(callSuper = true)
@NoArgsConstructor
@AllArgsConstructor
@Entity(name = "us_exam_info")
@ApiModel(value = "试卷表")
public class ExamInfo extends LogicModel {
    @Id
    @Column(name = "id", length = 40)
    @GeneratedValue(generator = "snowFlakeId")
    @GenericGenerator (name = "snowFlakeId", strategy = "com.simbest.boot.util.distribution.id.SnowflakeId")
    @EntityIdPrefix (prefix = "QS") //主键前缀，此为可选项注解
    private String id;

    /*@Column(length = 100)
    @Setter
    @Getter
    @ApiModelProperty(value = "试卷的唯一标识", required = true)
    private Long onlyRecord;*/

    @Column(length = 20)
    @Setter
    @Getter
    @ApiModelProperty(value = "得分", required = true)
    private Integer examScore;

    @Column(length = 10)
    @Setter
    @Getter
    @ApiModelProperty(value = "是否完成", required = true)
    private Boolean isComplete;//用于保存试卷，因为可能答题时间比较长。保存试卷时就是没有完成

    //试卷的可用时间放在数据字典表里维护

    @Column(length = 100)
    @Setter
    @Getter
    @ApiModelProperty(value = "试卷剩余时间(单位秒)", required = true)
    private long leftTime;//试卷剩余时间，试卷有时间限制，保存时记录还剩余多少时间

    @Column(length = 100)
    @Setter
    @Getter
    @ApiModelProperty(value = "答题人名", required = true)
    private String publishName;

    @Transient
    private String publishOrgCode;//发布人所在组织code

    @Transient
    private String publishOrgName;//发布人所在组织名

    @Transient
    private String publishDisplayName;//发布人所在组织全路径

    @Column(length = 250)
    @Setter
    @Getter
    @ApiModelProperty(value = "额外字段1", required = true)
    private String spare1;

    @Column(length = 250)
    @Setter
    @Getter
    @ApiModelProperty(value = "额外字段2", required = true)
    private String spare2;

    @Column(length = 250)
    @Setter
    @Getter
    @ApiModelProperty(value = "额外字段3", required = true)
    private String spare3;

    /**
     * 试卷问题答案
     */
    @Transient
    List<ExamQuestionResult> examQuestionResultList;

}
