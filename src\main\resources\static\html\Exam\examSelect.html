<!DOCTYPE html>
<html xmlns="http://www.w3.org/1999/xhtml"
      xmlns:th="http://www.thymeleaf.org">
<head>
    <title>成绩查询</title>
    <meta http-equiv="Content-Type" content="text/html; charset=utf-8"/>
    <meta http-equiv="X-UA-Compatible" content="IE=edge" />
    <meta name="viewport" content="width=device-width, initial-scale=1, shrink-to-fit=no"/>
    <!--
    <link href="http://************:8088/simbestui/fonts/iconfont/iconfont.css?v={svn.revision" th:href="@{http://************:8088/simbestui/fonts/iconfont/iconfont.css?v=svn.revision}" rel="stylesheet"/>
    -->
    <link rel="stylesheet" href="../../iconfont/iconfont.css">
    <link href="http://************:8088/simbestui/js/themes/default/easyui.css?v=svn.revision"  rel="stylesheet"/>
    <link href="http://************:8088/simbestui/js/themes/icon.css?v=svn.revision"  rel="stylesheet"/>
    <link href="http://************:8088/simbestui/css/public.css?v=svn.revision"  rel="stylesheet"/>
    <link href="http://************:8088/simbestui/css/pages.css?v=svn.revision"  rel="stylesheet"/>
    <script src="http://************:8088/simbestui/js/jquery.min.js?v=svn.revision"  type="text/javascript"></script>
    <script src="http://************:8088/simbestui/js/jquery.easyui.min.js?v=svn.revision"  type="text/javascript"></script>
    <script src="http://************:8088/simbestui/js/easyui-lang-zh_CN.js?v=svn.revision"  type="text/javascript"></script>
    <script src="http://************:8088/simbestui/js/kindeditor/kindeditor.min.js?v=svn.revision"  type="text/javascript"></script>
    <script src="../../js/jquery.config.js"></script>
    <script src="http://************:8088/simbestui/js/jquery.zjsfile.js?v=svn.revision"  type="text/javascript"></script>
    <script src="http://************:8088/simbestui/js/jquery.aduq.js?v=svn.revision"  type="text/javascript"></script>
    <script src="http://************:8088/simbestui/js/jquery.process.js?v=svn.revision"  type="text/javascript"></script>
    <script type="text/javascript">
        $(function(){

            var url;

            getCurrent();
            var authRoles=web.currentUser.authRoles;

            url="action/exam/findByExamInfoByUsername?username="+web.currentUser.username;
            for(var i in authRoles){
                if("125"==authRoles[i].id){
                    url="action/exam/findByExamInfoByUsername";
                }
            }
            var pageparam={
                "listtable":{
                    "listname":"#examSelectTable",//table列表的id名称，需加#
                    "querycmd":url,//table列表的查询命令
                    "contentType":"application/json; charset=utf-8",//table列表的传参方式，如果是json就写，不写默认kv
                    "nowrap": true,//把数据显示在一行里,默认true
                    "styleClass": "noScroll",
                    "loadFilter":pagerFilter,
                    "checkboxall":true,
                    "frozenColumns":[[
                        { field: "ck",checkbox:true}
                    ]],//固定在左侧的列
                    "columns":[[//列
                        { title: "OA账号", field: "publishUsername", width: 100,tooltip:true},
                        { title: "姓名", field: "publishTruename", width: 100,tooltip:true},
                        { title: "部门", field: "departmentName", width: 110,tooltip:true},
                        { title: "得分", field: "score", width: 110 ,tooltip:true},
                        { title: "证书", field: "sysFile", width: 300,//align：对齐此列的数据，可以用left、right、center
                            formatter: function (value, row,index) {//单元格的格式化函数，需要三个参数：value：字段的值；rowData：行的记录数据；rowIndex：行的索引

                                var g="<a href='#' class='yl' anonymousFilePath='" + value.anonymousFilePath + "'>"+value.fileName+"</a>";
                                return g;
                            }}

                    ]]
                },
                "readDialog":{//查看
                    "dialogid": "#readDag",
                    "dialogedit": true,//查看对话框底部要不要编辑按钮
                    "formname":"#examSelectTableReadForm"
                }
            };

            loadGrid(pageparam);


            //图片预览查看操作事件
            $(document).on("click",".examSelectTable a.yl",function(){
                var anonymousFilePath=$(this).attr("anonymousFilePath");
                window.open(anonymousFilePath,"_blank");
                // top.dialogP('html/Exam/preview.html?previewSkinUrl='+anonymousFilePath,window.name,'证书预览','chooseCallback',true,'950','750');
            });

        });

    </script>
</head>
<body class="body_page">

<!--searchform-->
<form id="examSelectTableQueryForm">
    <table border="0" cellpadding="0" cellspacing="4" width="100%">
        <tr>
            <td width="150" align="right">OA账户：</td>
            <td width="150">
                <input  name="username" type="text" value=""  style="width: 200px;height: 32px"/>
            </td>
            <td>
                <div class="w100">
                    <a class="btn fr searchtable"><font>查询</font></a>
                </div>
            </td>
        </tr>
    </table>
</form>
<!--table-->
<div class="examSelectTable"><table id="examSelectTable"></table></div>

<div id="readDag" title="详情" class="easyui-dialog" style="width:850px;height:600px;">
    <form id="examSelectTableReadForm" method="post" contentType="application/json; charset=utf-8">
        <table border="0" cellpadding="0" cellspacing="10" width="100%" class="tabForm">
            <tr>
                <td width="200" align="right" class="tit">OA账号：</td>
                <td >
                    <input  type="text" class="publishUsername" style="width: 100%;height: 32px"/>
                </td>
            </tr>

            <tr>
                <td width="200" align="right" class="tit">姓名：</td>
                <td >
                    <input type="text" class="publishTruename" style="width: 100%;height: 32px"/>
                </td>
            </tr>

            <tr>
                <td width="200" align="right" class="tit">部门：</td>
                <td >
                    <input type="text" class="departmentName" style="width: 100%;height: 32px"/>
                </td>
            </tr>

            <tr>
                <td width="200" align="right" class="tit">得分：</td>
                <td >
                    <input type="text" class="score" style="width: 100%;height: 32px"/>
                </td>
            </tr>

            <tr>
                <td width="200" align="right" class="tit">证书：</td>
                <td >
                    <input type="text" class="anonymousFilePath" style="width: 100%;height: 32px"/>
                </td>
            </tr>



        </table>

    </form>
</div>

</body>
</html>
