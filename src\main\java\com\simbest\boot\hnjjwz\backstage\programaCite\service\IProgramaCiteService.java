package com.simbest.boot.hnjjwz.backstage.programaCite.service;


import com.simbest.boot.base.service.ILogicService;
import com.simbest.boot.base.web.response.JsonResponse;
import com.simbest.boot.hnjjwz.backstage.programaCite.model.ProgramaCite;
import org.springframework.data.domain.Pageable;


/**
 * <strong>Title : </strong><br>
 * <strong>Description : </strong><br>
 * <strong>Create on : 2018/6/19</strong><br>
 * <strong>Modify on : 2018/6/19</strong><br>
 * <strong>Copyright (C) Ltd.</strong><br>
 *
 * <AUTHOR> 刘萌@simbest.com.cn
 * @version <strong>V1.0.0</strong><br>
 * <strong>修改历史:</strong><br>
 * 修改人 修改日期 修改描述<br>
 * -------------------------------------------<br>
 */
public interface IProgramaCiteService extends ILogicService<ProgramaCite,String> {

    /**
     * 根据栏目code获取栏目的中间信息
     * @param programaOneCode
     * @return
     */
    JsonResponse findRelationPrograma(String programaOneCode);

    /**
     * 根据栏目关系类型(精确)以及栏目名(模糊)查询
     * @param programaName
     * @param programaCiteType
     * @param pageable
     * @return
     */
    JsonResponse findDimProgramaCite( String programaName, String programaCiteType, Pageable pageable );
}
