package com.simbest.boot.hnjjwz.examOnline.service.impl;

import com.simbest.boot.base.service.impl.LogicService;
import com.simbest.boot.hnjjwz.examOnline.model.ExamQuestionBankQuestion;
import com.simbest.boot.hnjjwz.examOnline.repository.ExamQuestionBankQuestionRepository;
import com.simbest.boot.hnjjwz.examOnline.service.IExamQuestionBankQuestionService;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;

/**
 * <AUTHOR>
 * @Data 2019/05/08
 * @Description 题库问题的相关信息
 */
@Slf4j
@Service
public class ExamQuestionBankQuestionServiceImpl extends LogicService<ExamQuestionBankQuestion,String> implements IExamQuestionBankQuestionService {

    private ExamQuestionBankQuestionRepository examQuestionBankQuestionRepository;

    @Autowired
    public ExamQuestionBankQuestionServiceImpl ( ExamQuestionBankQuestionRepository examQuestionBankQuestionRepository) {
        super(examQuestionBankQuestionRepository);
        this.examQuestionBankQuestionRepository = examQuestionBankQuestionRepository;
    }

}
