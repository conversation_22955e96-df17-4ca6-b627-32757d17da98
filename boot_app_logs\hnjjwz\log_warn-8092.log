2025-06-18 14:18:31.945 [restartedMain] WARN  com.alibaba.cloud.nacos.client.NacosPropertySourceBuilder.loadNacosData Line:87 - Ignore the empty nacos configuration and get it based on dataId[hnjjwz-custom-obuat.properties] & group[DEFAULT_GROUP]
2025-06-18 14:18:31.963 [restartedMain] WARN  com.alibaba.cloud.nacos.client.NacosPropertySourceBuilder.loadNacosData Line:87 - Ignore the empty nacos configuration and get it based on dataId[hnjjwz] & group[DEFAULT_GROUP]
2025-06-18 14:18:31.978 [restartedMain] WARN  com.alibaba.cloud.nacos.client.NacosPropertySourceBuilder.loadNacosData Line:87 - Ignore the empty nacos configuration and get it based on dataId[hnjjwz.properties] & group[DEFAULT_GROUP]
2025-06-18 14:18:36.171 [restartedMain] WARN  org.springframework.boot.actuate.endpoint.EndpointId.logWarning Line:155 - Endpoint ID 'service-registry' contains invalid characters, please migrate to a valid format.
2025-06-18 14:18:37.410 [restartedMain] WARN  com.alibaba.druid.pool.DruidDataSource.initCheck Line:1314 - removeAbandoned is true, not use in production.
2025-06-18 14:19:42.785 [restartedMain] WARN  com.simbest.boot.SimbestApplication.onApplicationEvent Line:60 - 加载环境信息为: 【obuat】
2025-06-18 14:19:42.786 [restartedMain] WARN  com.simbest.boot.SimbestApplication.onApplicationEvent Line:61 - Application started successfully, lets go and have fun......
2025-06-18 14:19:42.787 [restartedMain] WARN  com.simbest.boot.SimbestApplication.onApplicationEvent Line:71 - 
---------------------------------------------------------
	应用已成功启动，运行地址如下：:
	Local:		http://localhost:8092/hnjjwz
	External:	http://***********:8092/hnjjwz
Aplication started successfully, lets go and have fun......
---------------------------------------------------------

2025-06-18 14:19:45.578 [RMI TCP Connection(4)-***********] WARN  org.springframework.boot.actuate.ldap.LdapHealthIndicator.health Line:87 - LDAP health check failed
org.springframework.ldap.CommunicationException: localhost:389; nested exception is javax.naming.CommunicationException: localhost:389 [Root exception is java.net.ConnectException: Connection refused: connect]
	at org.springframework.ldap.support.LdapUtils.convertLdapException(LdapUtils.java:108)
	at org.springframework.ldap.core.support.AbstractContextSource.createContext(AbstractContextSource.java:355)
	at org.springframework.ldap.core.support.AbstractContextSource.doGetContext(AbstractContextSource.java:139)
	at org.springframework.ldap.core.support.AbstractContextSource.getReadOnlyContext(AbstractContextSource.java:158)
	at org.springframework.ldap.core.LdapTemplate.executeReadOnly(LdapTemplate.java:802)
	at org.springframework.boot.actuate.ldap.LdapHealthIndicator.doHealthCheck(LdapHealthIndicator.java:50)
	at org.springframework.boot.actuate.health.AbstractHealthIndicator.health(AbstractHealthIndicator.java:82)
	at org.springframework.boot.actuate.health.HealthIndicator.getHealth(HealthIndicator.java:37)
	at org.springframework.boot.actuate.health.HealthEndpoint.getHealth(HealthEndpoint.java:81)
	at org.springframework.boot.actuate.health.HealthEndpoint.getHealth(HealthEndpoint.java:38)
	at org.springframework.boot.actuate.health.HealthEndpointSupport.getContribution(HealthEndpointSupport.java:108)
	at org.springframework.boot.actuate.health.HealthEndpointSupport.getAggregateHealth(HealthEndpointSupport.java:119)
	at org.springframework.boot.actuate.health.HealthEndpointSupport.getContribution(HealthEndpointSupport.java:105)
	at org.springframework.boot.actuate.health.HealthEndpointSupport.getHealth(HealthEndpointSupport.java:83)
	at org.springframework.boot.actuate.health.HealthEndpointSupport.getHealth(HealthEndpointSupport.java:70)
	at org.springframework.boot.actuate.health.HealthEndpoint.health(HealthEndpoint.java:75)
	at org.springframework.boot.actuate.health.HealthEndpoint.health(HealthEndpoint.java:65)
	at sun.reflect.NativeMethodAccessorImpl.invoke0(Native Method)
	at sun.reflect.NativeMethodAccessorImpl.invoke(NativeMethodAccessorImpl.java:62)
	at sun.reflect.DelegatingMethodAccessorImpl.invoke(DelegatingMethodAccessorImpl.java:43)
	at java.lang.reflect.Method.invoke(Method.java:498)
	at org.springframework.util.ReflectionUtils.invokeMethod(ReflectionUtils.java:282)
	at org.springframework.boot.actuate.endpoint.invoke.reflect.ReflectiveOperationInvoker.invoke(ReflectiveOperationInvoker.java:77)
	at org.springframework.boot.actuate.endpoint.annotation.AbstractDiscoveredOperation.invoke(AbstractDiscoveredOperation.java:60)
	at org.springframework.boot.actuate.endpoint.jmx.EndpointMBean.invoke(EndpointMBean.java:121)
	at org.springframework.boot.actuate.endpoint.jmx.EndpointMBean.invoke(EndpointMBean.java:96)
	at com.sun.jmx.interceptor.DefaultMBeanServerInterceptor.invoke(DefaultMBeanServerInterceptor.java:819)
	at com.sun.jmx.mbeanserver.JmxMBeanServer.invoke(JmxMBeanServer.java:801)
	at javax.management.remote.rmi.RMIConnectionImpl.doOperation(RMIConnectionImpl.java:1468)
	at javax.management.remote.rmi.RMIConnectionImpl.access$300(RMIConnectionImpl.java:76)
	at javax.management.remote.rmi.RMIConnectionImpl$PrivilegedOperation.run(RMIConnectionImpl.java:1309)
	at javax.management.remote.rmi.RMIConnectionImpl.doPrivilegedOperation(RMIConnectionImpl.java:1401)
	at javax.management.remote.rmi.RMIConnectionImpl.invoke(RMIConnectionImpl.java:829)
	at sun.reflect.GeneratedMethodAccessor330.invoke(Unknown Source)
	at sun.reflect.DelegatingMethodAccessorImpl.invoke(DelegatingMethodAccessorImpl.java:43)
	at java.lang.reflect.Method.invoke(Method.java:498)
	at sun.rmi.server.UnicastServerRef.dispatch(UnicastServerRef.java:357)
	at sun.rmi.transport.Transport$1.run(Transport.java:200)
	at sun.rmi.transport.Transport$1.run(Transport.java:197)
	at java.security.AccessController.doPrivileged(Native Method)
	at sun.rmi.transport.Transport.serviceCall(Transport.java:196)
	at sun.rmi.transport.tcp.TCPTransport.handleMessages(TCPTransport.java:573)
	at sun.rmi.transport.tcp.TCPTransport$ConnectionHandler.run0(TCPTransport.java:834)
	at sun.rmi.transport.tcp.TCPTransport$ConnectionHandler.lambda$run$0(TCPTransport.java:688)
	at java.security.AccessController.doPrivileged(Native Method)
	at sun.rmi.transport.tcp.TCPTransport$ConnectionHandler.run(TCPTransport.java:687)
	at java.util.concurrent.ThreadPoolExecutor.runWorker(ThreadPoolExecutor.java:1149)
	at java.util.concurrent.ThreadPoolExecutor$Worker.run(ThreadPoolExecutor.java:624)
	at java.lang.Thread.run(Thread.java:748)
Caused by: javax.naming.CommunicationException: localhost:389
	at com.sun.jndi.ldap.Connection.<init>(Connection.java:238)
	at com.sun.jndi.ldap.LdapClient.<init>(LdapClient.java:137)
	at com.sun.jndi.ldap.LdapClient.getInstance(LdapClient.java:1609)
	at com.sun.jndi.ldap.LdapCtx.connect(LdapCtx.java:2749)
	at com.sun.jndi.ldap.LdapCtx.<init>(LdapCtx.java:319)
	at com.sun.jndi.ldap.LdapCtxFactory.getUsingURL(LdapCtxFactory.java:192)
	at com.sun.jndi.ldap.LdapCtxFactory.getUsingURLs(LdapCtxFactory.java:210)
	at com.sun.jndi.ldap.LdapCtxFactory.getLdapCtxInstance(LdapCtxFactory.java:153)
	at com.sun.jndi.ldap.LdapCtxFactory.getInitialContext(LdapCtxFactory.java:83)
	at javax.naming.spi.NamingManager.getInitialContext(NamingManager.java:684)
	at javax.naming.InitialContext.getDefaultInitCtx(InitialContext.java:313)
	at javax.naming.InitialContext.init(InitialContext.java:244)
	at javax.naming.ldap.InitialLdapContext.<init>(InitialLdapContext.java:154)
	at org.springframework.ldap.core.support.LdapContextSource.getDirContextInstance(LdapContextSource.java:42)
	at org.springframework.ldap.core.support.AbstractContextSource.createContext(AbstractContextSource.java:343)
	... 47 common frames omitted
Caused by: java.net.ConnectException: Connection refused: connect
	at java.net.DualStackPlainSocketImpl.connect0(Native Method)
	at java.net.DualStackPlainSocketImpl.socketConnect(DualStackPlainSocketImpl.java:79)
	at java.net.AbstractPlainSocketImpl.doConnect(AbstractPlainSocketImpl.java:350)
	at java.net.AbstractPlainSocketImpl.connectToAddress(AbstractPlainSocketImpl.java:206)
	at java.net.AbstractPlainSocketImpl.connect(AbstractPlainSocketImpl.java:188)
	at java.net.PlainSocketImpl.connect(PlainSocketImpl.java:172)
	at java.net.SocksSocketImpl.connect(SocksSocketImpl.java:392)
	at java.net.Socket.connect(Socket.java:589)
	at java.net.Socket.connect(Socket.java:538)
	at java.net.Socket.<init>(Socket.java:434)
	at java.net.Socket.<init>(Socket.java:211)
	at com.sun.jndi.ldap.Connection.createSocket(Connection.java:375)
	at com.sun.jndi.ldap.Connection.<init>(Connection.java:215)
	... 61 common frames omitted
