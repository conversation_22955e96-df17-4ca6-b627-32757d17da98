package com.simbest.boot.hnjjwz.backstage.taskStudy.service;/**
 * Created by KZH on 2019/6/13 9:35.
 */

import com.simbest.boot.base.service.ILogicService;
import com.simbest.boot.base.web.response.JsonResponse;
import com.simbest.boot.hnjjwz.backstage.slideshow.model.SlideShow;
import com.simbest.boot.hnjjwz.backstage.taskStudy.model.TaskStudy;
import com.simbest.boot.hnjjwz.process.wf.model.PmInstence;
import com.simbest.boot.security.SimpleAppDecision;
import org.springframework.data.domain.Pageable;

import java.util.List;
import java.util.Map;

/**
 * <AUTHOR>
 * @create 2019-06-13 9:35
 * @desc
 **/
public interface ITaskStudyService extends ILogicService<TaskStudy,String> {

    List<TaskStudy> findTaskStudy(String programaDisplayName);
    /**
     * 提交下一步
     * @param currentUserCode
     * @param workItemId
     * @param outcome
     * @param location
     * @param bodyParam
     * @return
     */
    JsonResponse nextStep(String currentUserCode, String workItemId, String outcome, String location, String formId, String source, Map<String,Object> bodyParam);

    /**
     * 起草发起流程
     * @param taskStudy
     * @param userId
     * @param outcome
     * @param message
     */
    int startProcess(TaskStudy taskStudy, String userId, String outcome, String message, String source, String currentUserCode);

    /**
     *
     * @param taskStudy
     * @param workItemID
     * @param outcome
     * @param message
     * @param userId
     * @param location
     */
    int saveSubmitTask(TaskStudy taskStudy, String workItemID, String outcome, String message,  String userId, String location,String source,String currentUserCode);

    /**
     * 流程审批
     * @param workItemID
     * @param currentUserCode
     * @param currentUserName
     * @param userId
     * @param outcome
     * @param message
     * @param taskStudy
     * @return
     */
    int processApproval( Long workItemID, String currentUserCode , String currentUserName, String userId, String outcome, String message, PmInstence pmInstence );

    /**
     * 查询决策
     * @param processInstId
     * @param processDefName
     * @param location
     * @return

    List<SimpleAppDecision> getDecisions(String processInstId, String processDefName, String location, String source, String currentUser);
     */
    /**
     * 注销流程2
     * @param pmInstId
     * @return
     */
    int terminateProcessInst(Long pmInstId);

    /**
     * 详情办理
     * @param processInstId
     * @return
     */
    JsonResponse getApprovalFromDetail(Long processInstId,String source,String currentUserCode,String pmInstId,String location);

    JsonResponse findDataDetailList2(Map<String,Object> paramsMap, Pageable pageable);

    /**
     * 再根据pm_ins_id字段获取ApprovalForm对象
     * @param pmInsId
     * @return
     */
    TaskStudy getTaskStudyPmInsId(String pmInsId);

    /**
     * 保存草稿
     *
     * @return
     */
    JsonResponse saveDraft(String source, String currentUserCode, TaskStudy innovationTopicForm);


    /**
     * 废除草稿
     *
     * @return
     */
    JsonResponse deleteDraft(String source, String currentUserCode, String pmInsId, TaskStudy innovationTopicForm);

}
