package com.simbest.boot.hnjjwz.backstage.messageStatistics.web;/**
 * Created by KZH on 2019/7/1 10:15.
 */

import com.simbest.boot.base.web.controller.LogicController;
import com.simbest.boot.base.web.response.JsonResponse;
import com.simbest.boot.hnjjwz.backstage.messageStatistics.model.Message;
import com.simbest.boot.hnjjwz.backstage.messageStatistics.service.IMessageService;
import io.swagger.annotations.Api;
import io.swagger.annotations.ApiImplicitParam;
import io.swagger.annotations.ApiImplicitParams;
import io.swagger.annotations.ApiOperation;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.data.domain.Page;
import org.springframework.data.domain.PageImpl;
import org.springframework.data.domain.Pageable;
import org.springframework.web.bind.annotation.*;

import javax.servlet.http.HttpServletRequest;
import javax.servlet.http.HttpServletResponse;
import java.util.List;
import java.util.Map;

/**
 * <AUTHOR>
 * @create 2019-07-01 10:15
 * @desc 信息统计相关接口
 **/
@Api(description = "信息统计")
@Slf4j
@RestController
@RequestMapping(value = "/action/Message")
public class MessageController extends LogicController<Message,String> {
    @Autowired
    private IMessageService iMessageService;

    public MessageController(IMessageService iMessageService){
        super(iMessageService);
        this.iMessageService=iMessageService;

    }

    /**
     * 根据dictType获取数据字典值
     * @param dictType
     * @return
     */
    @ApiOperation(value = "根据dictType获取数据字典值", notes = "根据dictType获取数据字典值")
    @PostMapping(value = {"/findDictValue","/findDictValue/sso"})
    public JsonResponse findDictValue(@RequestParam(required = false) String dictType ) {

        return iMessageService.findDictValue(dictType);
    }

    /**
     * 信息统计
     * @return
     */
    @ApiOperation (value = "信息统计", notes = "信息统计")
    @ApiImplicitParams({
            @ApiImplicitParam(name = "page", value = "当前页码", dataType = "int", paramType = "query",
                    required = true, example = "1"),
            @ApiImplicitParam(name = "size", value = "每页数量", dataType = "int", paramType = "query",
                    required = true, example = "20"),
            @ApiImplicitParam(name = "direction", value = "排序规则（asc/desc）", dataType = "String",
                    paramType = "query"),
            @ApiImplicitParam(name = "properties", value = "排序规则（属性名称）", dataType = "String",
                    paramType = "query")
    })
    @PostMapping (value = {"/findMessageStatistics","/findMessageStatistics/sso"})
    public JsonResponse findMessageStatistics( @RequestParam (required = false, defaultValue = "1") int page,
                                    @RequestParam(required = false, defaultValue = "20") int size,
                                    @RequestParam(required = false) String direction,
                                    @RequestParam(required = false) String properties,
                                    @RequestBody (required = false) Map<String,String> mapObject ) {
        Pageable pageable = iMessageService.getPageable(page, 20, direction, properties);
        Page<List<Message>> page2 = null;
        List<Map<String, Object>> messageStatistics = iMessageService.findMessageStatistics(mapObject);
        if(messageStatistics.size() > 0){
            long size2 = messageStatistics.size();
            page2 = new PageImpl(messageStatistics,pageable,size2);
        }else {
            JsonResponse.success(1,"暂无数据");
        }
        return JsonResponse.success(page2);
    }

    @ApiOperation(value = "导出", notes = "导出")
    @PostMapping(value = "/exportNDExcel")
    @ResponseBody
    public void exportNDExcel(HttpServletRequest request, HttpServletResponse response, @RequestParam (required = false) Map<String,String> mapObject )throws Exception{

        iMessageService.exportNDExcel(request,response,mapObject);
    }

}
