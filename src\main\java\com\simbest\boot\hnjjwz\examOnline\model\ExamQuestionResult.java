package com.simbest.boot.hnjjwz.examOnline.model;

import com.simbest.boot.base.annotations.EntityIdPrefix;
import com.simbest.boot.base.model.LogicModel;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.*;
import org.hibernate.annotations.GenericGenerator;

import javax.persistence.*;

/**
 * <AUTHOR>
 * @Data 2019/05/08
 * Description
 */
@Data
@EqualsAndHashCode(callSuper = true)
@NoArgsConstructor
@AllArgsConstructor
@Entity(name = "us_exam_question_result")
@ApiModel(value = "试卷所做题目答案表")
public class ExamQuestionResult extends LogicModel {
    @Id
    @Column(name = "id", length = 40)
    @GeneratedValue(generator = "snowFlakeId")
    @GenericGenerator (name = "snowFlakeId", strategy = "com.simbest.boot.util.distribution.id.SnowflakeId")
    @EntityIdPrefix (prefix = "QR") //主键前缀，此为可选项注解
    private String id;

    @Column(length = 250)
    @Setter
    @Getter
    @ApiModelProperty(value = "唯一试卷编码", required = true)
    private Long onlyRecord;

    @Column(length = 250)
    @Setter
    @Getter
    @ApiModelProperty(value = "所做题库编码", required = true)
    private String questionBankCode;

    @Column(length = 4000)
    @Setter
    @Getter
    @ApiModelProperty(value = "所做题目编码", required = true)
    private String questionCode;

    @Column(length = 4000)
    @Setter
    @Getter
    @ApiModelProperty(value = "所做答案编码", required = true)
    private String answerCode;

    @Column(length = 250)
    @Setter
    @Getter
    @ApiModelProperty(value = "答题人姓名", required = true)
    private String publishName;

    @Column(length = 250)
    @Setter
    @Getter
    @ApiModelProperty(value = "答题人所在组织名", required = true)
    private String publishOrgName;//答题人所在组织名

    @Transient
    private Boolean isCorrect;

    @Transient
    private Integer questionValue;

    @Transient
    private String publishOrgCode;//答题人所在组织code

    @Transient
    private String publishDisplayName;//答题人所在组织全路径

    @Column(length = 250)
    @Setter
    @Getter
    @ApiModelProperty(value = "额外字段1", required = true)
    private String spare1;

    @Column(length = 250)
    @Setter
    @Getter
    @ApiModelProperty(value = "额外字段2", required = true)
    private String spare2;

    @Column(length = 250)
    @Setter
    @Getter
    @ApiModelProperty(value = "额外字段3", required = true)
    private String spare3;

}
