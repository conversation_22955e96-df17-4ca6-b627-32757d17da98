package com.simbest.boot.hnjjwz.backstage.messageStatistics.service.impl;/**
 * Created by KZH on 2019/7/1 10:16.
 */

import cn.hutool.core.collection.CollUtil;
import com.google.common.collect.Lists;
import com.google.common.collect.Maps;
import com.simbest.boot.base.repository.CustomDynamicWhere;
import com.simbest.boot.base.service.impl.LogicService;
import com.simbest.boot.base.web.response.JsonResponse;
import com.simbest.boot.hnjjwz.backstage.messageStatistics.model.Message;
import com.simbest.boot.hnjjwz.backstage.messageStatistics.repository.MessageRepository;
import com.simbest.boot.hnjjwz.backstage.messageStatistics.service.IMessageService;
import com.simbest.boot.hnjjwz.process.wf.service.IProgramaDataFormService;
import com.simbest.boot.hnjjwz.util.FileTool;
import com.simbest.boot.sys.model.SysDictValue;
import com.simbest.boot.sys.service.ISysDictValueService;
import com.simbest.boot.util.office.ExcelUtil;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.io.FileUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;

import javax.persistence.EntityManager;
import javax.persistence.PersistenceContext;
import javax.servlet.http.HttpServletRequest;
import javax.servlet.http.HttpServletResponse;
import java.io.File;
import java.io.FileOutputStream;
import java.net.URLEncoder;
import java.util.*;

/**
 * <AUTHOR>
 * @create 2019-07-01 10:16
 * @desc
 **/
@Slf4j
@Service
public class MessageServiceImpl extends LogicService<Message,String> implements IMessageService {

    @Autowired
    private MessageRepository messageRepository;

    @Autowired
    private ISysDictValueService iSysDictValueService;

    @Autowired
    private CustomDynamicWhere customDynamicWhere;

    @Autowired
    private IProgramaDataFormService iProgramaDataFormService;

    @PersistenceContext
    private EntityManager entityManager;

    public MessageServiceImpl(MessageRepository messageRepository) {
        super(messageRepository);
        this.messageRepository=messageRepository;

    }

    /**
     * 根据dictType获取数据字典值
     * @param dictType
     * @return
     */
    @Override
    public JsonResponse findDictValue(String dictType) {
        SysDictValue sysDictValue=new SysDictValue();
        sysDictValue.setDictType(dictType);

        List<SysDictValue> dictValue = iSysDictValueService.findDictValue(sysDictValue);
        return JsonResponse.success(dictValue);
    }

    @Override
    public List<Map<String, Object>> findMessageStatistics(Map<String, String> mapObject) {

        //初始化返回参数
        //List<Message> messageList=new ArrayList<>();

        //结果集映射的名称
        //List<Map<String,Object>> programaDataFormList;

        Map<String,Object> map = Maps.newHashMap();

        //单位
        String department= mapObject.get("department");
        //年份
        String year=mapObject.get("year");
        //月度
        String month=mapObject.get("month");
        //季度
        String quarter=mapObject.get("quarter");
        //半年
        String semiyearly=mapObject.get("semiyearly");
        //信息类型
        String messageType=mapObject.get("messageType");

        String selectSQL = "select t.company,count(1) sbl,count(decode(t.is_publish,1,1)) as sptg,count(decode(t.is_publish,0,1)) as wcbl,ROUND( (count(decode(t.is_publish,1,1))/count(1))*100,2) cbl from US_PROGRAMA_DATA_FORM t ";
        String whereSQL = " where  t.enabled=1 ";
        String groupBySQL = " group by t.company ";

        if(year!=null&&year!=""){
            whereSQL = whereSQL + " and to_char(t.modified_time,'yyyy') in (:year) ";
            map.put("year",year);
        }
        if(month!=null&&month!=""){
            whereSQL = whereSQL + " and to_char(t.modified_time,'mm') in (:month) ";
            map.put("month",month);
        }
        if(quarter!=null&&year!=""){
            whereSQL = whereSQL + " and to_char(t.modified_time,'mm') in (:quarter) ";
            map.put("quarter", Arrays.asList(quarter.split(",")));
        }
        if(semiyearly!=null&&semiyearly!=""){
            whereSQL = whereSQL + " and to_char(t.modified_time,'mm') in (:semiyearly) ";
            //String replace1=semiyearly.replace("&#39;","");
            map.put("semiyearly",Arrays.asList(semiyearly.split(",")));
        }
        if(department!=null&&department!=""){
            whereSQL = whereSQL + " and t.company=(:department) ";
            map.put("department",department);
        }
        if(messageType!=null&&messageType!=""){
            //013为原来的分公司动态,013001是纪检信息动态 013002是党廉信息动态
            if ("013".equals(messageType)){
                List<String> typeList = CollUtil.newArrayList();
                typeList.add("013");
                typeList.add("013001");
                typeList.add("013002");
                whereSQL = whereSQL + " and t.programa_code in(:typeList) ";
                map.put("typeList",typeList);
            }else if("010".equals(messageType)){
                //010为原来的以案示警,010001是以案示警 010002是四风八规曝光台
                List<String> typeList = CollUtil.newArrayList();
                typeList.add("010");
                typeList.add("010001");
                typeList.add("010002");
                whereSQL = whereSQL + " and t.programa_code in(:typeList) ";
                map.put("typeList",typeList);
            }else{
                whereSQL = whereSQL + " and t.programa_code=(:messageType) ";
                map.put("messageType",messageType);
            }
        }
        String querySQL = selectSQL + whereSQL + groupBySQL;

        List<Map<String, Object>> maps = customDynamicWhere.queryNamedParameterForList(querySQL,map);

        return maps;
    }

    @Override
    public void exportNDExcel(HttpServletRequest request, HttpServletResponse response, Map<String, String> mapObject) throws Exception {

        String targetFileName = "上报信息统计.xls";//设置导出Excel的名称
        String userAgent = request.getHeader("user-agent");
        if (userAgent != null && userAgent.indexOf("Firefox") >= 0 || userAgent.indexOf("Chrome") >= 0
                || userAgent.indexOf("Safari") >= 0) {
            targetFileName= new String((targetFileName).getBytes(), "UTF8");
        } else {
            targetFileName= URLEncoder.encode(targetFileName,"UTF8"); //其他浏览器
        }

        File targetFile = new File( targetFileName);//获取File对象
        //覆盖文件
        FileUtils.touch(targetFile);
        Message message=null;
        List<Message> messageList= Lists.newArrayList();//Lists工具类 自动推导尖括号里面的数据类型

        List<Map<String, Object>> messageStatistics = this.findMessageStatistics(mapObject);
        for(Map<String,Object> temp :messageStatistics){
            message=new Message();
            if(temp.get("COMPANY")==null){
                message.setDepartment("无");
            }else{
                message.setDepartment(temp.get("COMPANY").toString());
            }
            if(temp.get("SBL")==null){
                message.setAppearMessage("无");
            }else{
                message.setAppearMessage(temp.get("SBL").toString());
            }
            if(temp.get("SPTG")==null){
                message.setAdopt("无");
            }else{
                message.setAdopt(temp.get("SPTG").toString());
            }
            if(temp.get("WCBL")==null){
                message.setNoAdopt("无");
            }else{
                message.setNoAdopt(temp.get("WCBL").toString());
            }
            if(temp.get("CBL")==null){
                message.setAdoptRatio("无");
            }else{
                message.setAdoptRatio(temp.get("CBL").toString());
            }
            messageList.add(message);
        }
        ExcelUtil<Message> exportUtil = new ExcelUtil<Message>(Message.class);//获取ExcelUtile工具类
        exportUtil.exportExcel(messageList, "汇总结果", new FileOutputStream(targetFile), null);
        FileTool.download(targetFile.getPath(),response);//download

    }

}
