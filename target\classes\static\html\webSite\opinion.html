﻿<!DOCTYPE html>
<html lang="en">

<head>
    <meta charset="UTF-8">
    <meta http-equiv="X-UA-Compatible" content="IE=edge">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>意见征集</title>
    <script>
        (function () {
            // 在标准 375px 适配下，100px = 1rem;
            var baseFontSize = 100;
            var baseWidth = 375;
            var set = function () {
                var clientWidth = document.documentElement.clientWidth || window.innerWidth;
                var rem = 100;
                if (clientWidth != baseWidth) {
                    rem = Math.floor(clientWidth / baseWidth * baseFontSize);
                }
                document.querySelector('html').style.fontSize = rem + 'px';
            }
            set();
            window.addEventListener('resize', set);
        }());
    </script>
    <style>
        .main {
            width: 1000px;
            margin: 0px auto;
            border: 1px solid #CCCCCC;
        }

        .ppt {
            font-size: 14px;
            color: #558ED5;
            padding: 4px;
            font-weight: bold;
            margin-left: 68px;

        }

        .ppt p {
            line-height: 23px;
            white-space: nowrap;
            padding: 0px;
            margin: 0px;
        }

        .content1 img {
            width: 50px;
            margin: 0px 26px;
            float: left;
            margin-top: 39px;
            line-height: 66px;
        }

        .content2 img {
            width: 50px;
            margin: 0px 26px;
            float: left;
            line-height: 66px;
            margin-top: 25px;
        }

        .content1,
        .content2 {
            width: 482px;
            float: left;
            margin-top: 25px;
            margin-bottom: 18px;
        }

        .content1 {
            margin-left: 8px;
        }

        .content2 {
            margin-left: 18px;
        }

        .content2 li {
            border-radius: 8px;
            background: #E2EFFF;
            height: 96px;
            margin: 16px 0px;

        }

        .content1 li {
            border-radius: 8px;
            background: #E2EFFF;
            height: 132px;
            margin: 18px 0px;
        }

        .content2 .ppt {
            margin-top: 28px;
            padding: 8px 4px;
        }

        .content1 .ppt {
            margin-top: 39px;
        }

        ul {
            margin: 0px;
            padding: 0px;
        }

        ul li {
            margin: 0px;
            padding: 0px;
            list-style: none;
            overflow: hidden;
        }

        .head {
            text-indent: 2rem;
            font-size: 16px;
            color: #000;
            padding: 0px 8px;
            margin-top: 12px;
        }

        .doubleLine {
            height: 3px;
            width: 100%;
            border: 1px solid #558ED5;
            border-left: none;
            border-right: none;
            margin-bottom: 10px;
        }

        .title {
            font-size: 32px;
            font-weight: bolder;
            color: #0070C0;
            margin: 0px;
        }

        .boot {
            padding: 7px 7px;
            font-size: 14px;
            font-weight: normal;
            /* display: inline; */
            width: auto;
        }

        .bottom {
            background: rgb(222, 222, 222);
            height: 41px;
            color: #000;
            font-size: 14px;
            line-height: 41px;
            text-align: center;
        }

        @media screen and (max-width:1200px) {

            .main {
                width: 3.75rem;
                margin: 0px auto;
                 border: none;
                display: flex;
                flex-direction: column;
                justify-content: center;
            }

            .main .ppt {
                font-size: .14rem;
                color: #558ED5;
                padding: 0.04rem;
                font-weight: bold;
                margin-top: 0px!important;
                margin-left: 0px!important;
            }

            .main .ppt p {
                line-height: .23rem;
                white-space: nowrap;
                padding: 0px;
                margin: 0px;
                font-size: 0.14rem;
            }

            .main .content img {
                width: .40rem;
                margin: 0rem .04rem;
                float: left;
                line-height: .66rem;
                /* margin-top: .35rem; */
            }

            .main .content {
                width: 3.5rem;
                /* float: left; */
                margin: 0px auto;
                margin-top: .25rem;
                /* margin-bottom: 18px; */
            }
            .main .content li {
                border-radius: 0.08rem;
                background: #E2EFFF;
                height: 1.22rem;
                margin: .18rem 0px;
                display: flex;
                justify-content: center;
                align-items: center;
            }

            .main .content .ppt {
                /* margin-top: .28rem; */
                padding: 0.08rem 0.04rem;
            }

            ul {
                margin: 0px;
                padding: 0px;
            }

            ul li {
                margin: 0px;
                padding: 0px;
                list-style: none;
                overflow: hidden;
            }

            .head {
                text-indent: 0.2rem;
                font-size: 0.17rem;
                color: #000;
                padding: 0px 0.08rem;
                margin-top: .12rem;
            }
            .title {
                font-size: 0.32rem;
                font-weight: bolder;
                color: #0070C0;
                margin: 0px;
            }

            .boot {
                padding: 0.07rem 0.07rem;
                font-size: .04rem;
                margin: 0px;
                font-weight: normal;
                /* display: inline; */
                width: auto;
            }

            .bottom {
                /* position: relative; */
                /* bottom: 0px; */
                /* margin-top: 45px; */
                /* left: 50%; */
                /* margin-left: -500px; */
                font-size: .03rem;
                width: 3.75rem;
                background: rgb(222, 222, 222);
                height: 0.3rem;
                color: #000;
                line-height: 0.3rem;
                text-align: center;
            }

        }

        /* //当屏幕最大为1200px时，即屏幕小于1200px时 =>运用移动端的css方案 */

        @media screen and (min-width:1200px) {
            .main {
                width: 1000px;
                margin: 0px auto;
                border: 1px solid #CCCCCC;
            }
            .ppt {
                font-size: 14px;
                color: #558ED5;
                padding: 4px;
                font-weight: bold;
                margin-left: 68px;

            }
            .ppt p {
                line-height: 23px;
                white-space: nowrap;
                padding: 0px;
                margin: 0px;
            }
            .content1 img {
                width: 50px;
                margin: 0px 26px;
                float: left;
                margin-top: 39px;
                line-height: 66px;
            }
            .content2 img {
                width: 50px;
                margin: 0px 26px;
                float: left;
                line-height: 66px;
                margin-top: 25px;
            }

            .content1,
            .content2 {
                width: 482px;
                float: left;
                margin-top: 25px;
                margin-bottom: 18px;
            }

            .content1 {
                margin-left: 8px;
            }

            .content2 {
                margin-left: 18px;
            }

            .content2 li {
                border-radius: 8px;
                background: #E2EFFF;
                height: 96px;
                margin: 16px 0px;

            }

            .content1 li {
                border-radius: 8px;
                background: #E2EFFF;
                height: 132px;
                margin: 18px 0px;
            }

            .content2 .ppt {
                margin-top: 28px;
                padding: 8px 4px;
            }

            .content1 .ppt {
                margin-top: 39px;
            }

            ul {
                margin: 0px;
                padding: 0px;
            }

            ul li {
                margin: 0px;
                padding: 0px;
                list-style: none;
                overflow: hidden;
            }

            .head {
                text-indent: 0.07rem;
                font-size: 16px;
                color: #000;
                padding: 0px 8px;
                margin-top: 12px;
            }

            .doubleLine {
                height: 3px;
                width: 100%;
                border: 1px solid #558ED5;
                border-left: none;
                border-right: none;
                margin-bottom: 10px;
            }

            .title {
                font-size: 32px;
                font-weight: bolder;
                color: #0070C0;
                margin: 0px;
            }

            .boot {
                padding: 7px 7px;
                font-size: 14px;
                font-weight: normal;
                /* display: inline; */
                width: auto;
            }

            .bottom {
                width: 1000px;
                background: rgb(222, 222, 222);
                height: 41px;
                color: #000;
                font-size: 14px;
                line-height: 41px;
                text-align: center;
            }
        }
        /* //当屏幕最小为1200px时，即屏幕大于1200px时 =>运用pc端的css方案 */
    </style>
</head>

<body style="margin: 0px;padding: 0px;">
    <div class="main" style="overflow: hidden;">
        <h5 class="title"><img style="width: 100%;" src="../../images/征集员工意见专题图.jpg" alt=""></h5>
        <div class="head">
            为落实好“我为群众办实事”实践活动，进一步牢固树立以人民为中心和高质量发展的理念，公司集中开通员工意见征集渠道，听心声、解难题、办实事。诚挚欢迎广大干部员工提出意见、反映问题，各相关部门将安排专人回复处理。
        </div>
        <ul class="content1 content" >
            <li style="margin: 0px;">
                <img src="../../images/1.png" alt="">
                <div class="ppt">
                    <p>人力资源部沟通邮箱：<EMAIL> </p>
                    <p>干部考察监督电话：18237129779</p>
                </div>
            </li>
            <li>
                <img src="../../images/0.png" alt="">
                <div class="ppt">
                    <p>采购物流部沟通邮箱：<EMAIL></p>
                    <p>招投标监督电话：0371-67260889</p>
                </div>
            </li>
            <li style="margin: 0px;">
                <img src="../../images/5.png" alt="">
                <div class="ppt" style=" margin-top:48px">
                    <p>市场经营部沟通邮箱：<EMAIL>
                    </p>
                </div>
            </li>
        </ul>
        <ul class="content2 content">
            <li style="margin: 0px;">
                <img src="../../images/2.png" alt="">
                <div class="ppt">
                    <p>政企客户部沟通邮箱：<EMAIL>
                    </p>
                </div>
            </li>
            <li>
                <img src="../../images/3.png" alt="">
                <div class="ppt">
                    <p>计划建设部沟通邮箱：<EMAIL>
                    </p>
                </div>
            </li>
            <li>
                <img src="../../images/6.png" alt="">
                <div class="ppt">
                    <p>网络部沟通邮箱：<EMAIL>
                    </p>
                </div>
            </li>
            <li>
                <img src="../../images/4.png" alt="">
                <div class="ppt">
                    <p>党廉办沟通邮箱 ：<EMAIL>
                    </p>
                </div>
            </li>
        </ul>
        
        <h6 class="boot">注释：各部门沟通邮箱设置原则为“部门简称+沟通”拼音首写字母
        </h6>
        <div class="bottom">
            版权所有 中国移动通信集团河南有限公司
        </div>
    </div>


</body>

</html>