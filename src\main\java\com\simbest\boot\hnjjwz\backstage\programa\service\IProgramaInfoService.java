package com.simbest.boot.hnjjwz.backstage.programa.service;


import com.simbest.boot.base.service.ILogicService;
import com.simbest.boot.base.web.response.JsonResponse;
import com.simbest.boot.hnjjwz.backstage.programa.model.ProgramaInfo;
import com.simbest.boot.hnjjwz.backstage.programa.model.TabsData;
import org.springframework.data.domain.Pageable;

import java.util.List;
import java.util.Set;

/**
 * <strong>Title : </strong><br>
 * <strong>Description : </strong><br>
 * <strong>Create on : 2018/6/19</strong><br>
 * <strong>Modify on : 2018/6/19</strong><br>
 * <strong>Copyright (C) Ltd.</strong><br>
 *
 * <AUTHOR> 刘萌@simbest.com.cn
 * @version <strong>V1.0.0</strong><br>
 * <strong>修改历史:</strong><br>
 * 修改人 修改日期 修改描述<br>
 * -------------------------------------------<br>
 */
public interface IProgramaInfoService extends ILogicService<ProgramaInfo,String> {

    /**
     * 自动生成栏目编码
     * @param parentProgramaCode
     * @return
     */
    String autoGenerationCode(String parentProgramaCode);

    /**
     * 根据所属栏目分类id、结点类型、父栏目名（模糊）、栏目名称（模糊）并分页
     * @param programaClassifyIds
     * @param nodeTypes
     * @param parentProgramaName
     * @param programaName
     * @param pageable
     * @return
     */
    JsonResponse findDimProgramaInfo( String programaClassifyIds, String nodeTypes, String parentProgramaName, String programaName, Pageable pageable );

    /**
     * 页面初始化时获取根栏目以及下一级栏目
     * @return
     */
    Set<ProgramaInfo>  findRootAndNext();

    /**
     * 根据登录人进行页面初始化时获取根栏目以及下一级栏目
     * @return
     */
    List<TabsData>  findUserNameRootAndNext(String CompanyName);

    /**
     * 获取栏目的下一级栏目
     * @param parentProgramaCode
     * @return
     */
    JsonResponse findSonByParentCode(String parentProgramaCode);

    /**
     * 根据栏目code获得某个栏目的信息
     * @param ProgramaCode
     * @return
     */
    ProgramaInfo findByFromProCode(String ProgramaCode);


    /**
     * 获取栏目的下一级栏目2
     * @param locationId
     * @return
     */
    List<ProgramaInfo> findSonByProgramaInfo(String locationId);


}
