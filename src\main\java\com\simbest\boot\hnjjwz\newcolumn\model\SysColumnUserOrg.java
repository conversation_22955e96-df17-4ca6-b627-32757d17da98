package com.simbest.boot.hnjjwz.newcolumn.model;

import com.simbest.boot.base.annotations.EntityIdPrefix;
import com.simbest.boot.base.model.LogicModel;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.AllArgsConstructor;
import lombok.Data;
import lombok.EqualsAndHashCode;
import lombok.NoArgsConstructor;
import org.hibernate.annotations.GenericGenerator;

import javax.persistence.Column;
import javax.persistence.Entity;
import javax.persistence.GeneratedValue;
import javax.persistence.Id;

@Data
@EqualsAndHashCode(callSuper = true)
@NoArgsConstructor
@AllArgsConstructor
@Entity(name = "SYS_COLUMN_USER_ORG")
@ApiModel(value = "新版栏目分类")
public class SysColumnUserOrg  extends LogicModel {
    @Id
    @Column(name = "id", length = 40)
    @GeneratedValue(generator = "snowFlakeId")
    @GenericGenerator(name = "snowFlakeId", strategy = "com.simbest.boot.util.distribution.id.SnowflakeId")
    @EntityIdPrefix(prefix = "SCUO") //主键前缀，此为可选项注解
    private String id;

    @ApiModelProperty(value = "栏目ID")
    @Column(length = 50)
    private String columnId;

    @ApiModelProperty(value = "对应的人员或组织编码")
    @Column(length = 500)
    private String value;
}
