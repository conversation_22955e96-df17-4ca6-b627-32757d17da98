package com.simbest.boot.hnjjwz.backstage.templateLayout.web;/**
 * Created by KZH on 2019/5/30 17:23.
 */

import com.simbest.boot.base.web.controller.LogicController;
import com.simbest.boot.base.web.response.JsonResponse;
import com.simbest.boot.hnjjwz.backstage.announcement.service.IAnnouncementService;
import com.simbest.boot.hnjjwz.backstage.indexMenu.model.IndexMenu;
import com.simbest.boot.hnjjwz.backstage.indexMenu.model.SubmenuMenu;
import com.simbest.boot.hnjjwz.backstage.indexMenu.service.IIndexMenuService;
import com.simbest.boot.hnjjwz.backstage.indexMenu.service.ISubmenuMenuService;
import com.simbest.boot.hnjjwz.backstage.programa.service.IProgramaInfoService;
import com.simbest.boot.hnjjwz.backstage.slideshow.service.ISlideShowService;
import com.simbest.boot.hnjjwz.backstage.templateLayout.model.TemplateLayout;
import com.simbest.boot.hnjjwz.backstage.templateLayout.model.Menu;
import com.simbest.boot.hnjjwz.backstage.templateLayout.service.ITemplateLayoutService;
import com.simbest.boot.hnjjwz.constants.Constants;
import com.simbest.boot.hnjjwz.process.wf.service.IProgramaDataFormService;
import com.simbest.boot.util.security.LoginUtils;
import io.swagger.annotations.Api;
import io.swagger.annotations.ApiOperation;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.data.domain.Pageable;
import org.springframework.web.bind.annotation.*;

import javax.servlet.http.HttpServletRequest;
import java.util.*;

/**
 * <AUTHOR>
 * @create 2019-05-30 17:23
 * @desc
 **/
@Api(description = "模板布局")
@Slf4j
@RestController
@RequestMapping(value = "/action/templateLayout")
public class TemplateLayoutController extends LogicController<TemplateLayout,String> {

    @Autowired
    private ITemplateLayoutService iTemplateLayoutService;

    @Autowired
    public TemplateLayoutController(ITemplateLayoutService iTemplateLayoutService){
        super(iTemplateLayoutService);
        this.iTemplateLayoutService=iTemplateLayoutService;
    }

    @ApiOperation(value = "构建首页模板布局", notes = "构建首页模板布局")
    @PostMapping (value = {"/constructTemplateLayout","/constructTemplateLayout/sso"})
    public JsonResponse constructTemplateLayout() {

        return  iTemplateLayoutService.findTemplateLayout();
    }

    @ApiOperation(value = "构建规章制度模板布局", notes = "构建规章制度模板布局")
    @PostMapping (value = {"/constructRulesGaugeLayout","/constructRulesGaugeLayout/sso"})
    public JsonResponse constructRulesGaugeLayout() {

        return  iTemplateLayoutService.findRulesGaugeLayout();
    }

    @ApiOperation(value = "构建巡察工作模板布局", notes = "构建巡察工作模板布局")
    @PostMapping (value = {"/constructInspectionWorkLayout","/constructInspectionWorkLayout/sso"})
    public JsonResponse constructInspectionWorkLayout() {

        return  iTemplateLayoutService.findInspectionWorkLayout();
    }

    @ApiOperation(value = "构建嵌入式防控监督模板布局", notes = "构建嵌入式防控监督模板布局")
    @PostMapping (value = {"/constructEmbeddedLayout","/constructEmbeddedLayout/sso"})
    public JsonResponse constructEmbeddedLayout() {

        return  iTemplateLayoutService.findEmbeddedLayout();
    }

    @ApiOperation(value = "构建课题研究模板布局", notes = "构建课题研究模板布局")
    @PostMapping (value = {"/constructResearchLayout","/constructResearchLayout/sso"})
    public JsonResponse constructResearchLayout() {

        return  iTemplateLayoutService.findResearchLayout();
    }

    @ApiOperation(value = "构建廉洁教育模板布局", notes = "构建廉洁教育模板布局")
    @PostMapping (value = {"/constructEducationLayout","/constructEducationLayout/sso"})
    public JsonResponse constructEducationLayout() {

        return  iTemplateLayoutService.findEducationLayout();
    }

    @ApiOperation(value = "构建廉洁文化模板布局", notes = "构建廉洁文化模板布局")
    @PostMapping (value = {"/constructCultureLayout","/constructCultureLayout/sso"})
    public JsonResponse constructCultureLayout() {

        return  iTemplateLayoutService.findCultureLayout();
    }

    @ApiOperation(value = "构建信访举报模板布局", notes = "构建信访举报模板布局")
    @PostMapping (value = {"/constructPetitionLayout","/constructPetitionLayout/sso"})
    public JsonResponse constructPetitionLayout() {

        return  iTemplateLayoutService.findPetitionLetter();
    }

    @ApiOperation(value = "构建纪律审查与监督模板布局", notes = "构建纪律审查与监督模板布局")
    @PostMapping (value = {"/constructSuperviSionLayout","/constructSuperviSionLayout/sso"})
    public JsonResponse constructSuperviSionLayout() {

        return  iTemplateLayoutService.findSupervisionLayout();
    }

    @ApiOperation(value = "构建家风栏目布局", notes = "构建家风栏目布局")
    @PostMapping (value = {"/constructDanColumnsLayout","/constructDanColumnsLayout/sso"})
    public JsonResponse constructDanColumnsLayout(@RequestParam(required = false) String locationType) {

        return  iTemplateLayoutService.findDanColumnsLayout(locationType);
    }



    @ApiOperation(value = "模拟登陆", notes = "模拟登陆")
    @PostMapping (value = {"/simulatedLanding","/simulatedLanding/sso"})
    public JsonResponse simulatedLanding(@RequestParam(required = false) String loginuser, @RequestParam(required = false) String appcode) {
          iTemplateLayoutService.simulatedLanding(loginuser,appcode);
          return JsonResponse.defaultSuccessResponse();
    }

    @ApiOperation(value = "手动记录日志", notes = "手动记录日志")
    @PostMapping (value = {"/recordSuccessLogin","/recordSuccessLogin/sso"})
    public JsonResponse recordSuccessLogin(@RequestBody Map<String,Object> paramMap, HttpServletRequest request) {
        iTemplateLayoutService.recordSuccessLogin(paramMap,request);
        return JsonResponse.defaultSuccessResponse();
    }

    @ApiOperation(value = "构建党廉信息和纪检信息布局", notes = "构建党廉信息和纪检信息布局")
    @PostMapping (value = {"/constructDownCompany","/constructDownCompany/sso"})
    public JsonResponse constructDownCompany() {

        return  iTemplateLayoutService.findIndexTemplateDownCompany();
    }

    @ApiOperation(value = "构建以案示警和四风八规曝光台布局", notes = "构建以案示警和四风八规曝光台布局")
    @PostMapping (value = {"/constructWarnCompany","/constructWarnCompany/sso"})
    public JsonResponse constructWarnCompany() {

        return  iTemplateLayoutService.findIndexTemplateWarnCompany();
    }
}
