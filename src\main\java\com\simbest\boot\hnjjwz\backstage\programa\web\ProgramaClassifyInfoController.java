package com.simbest.boot.hnjjwz.backstage.programa.web;

import com.simbest.boot.base.web.controller.LogicController;
import com.simbest.boot.base.web.response.JsonResponse;
import com.simbest.boot.hnjjwz.backstage.programa.model.ProgramaClassifyInfo;
import com.simbest.boot.hnjjwz.backstage.programa.service.IProgramaClassifyInfoService;
import com.simbest.boot.hnjjwz.backstage.programa.service.IProgramaInfoService;
import io.swagger.annotations.Api;
import io.swagger.annotations.ApiImplicitParam;
import io.swagger.annotations.ApiImplicitParams;
import io.swagger.annotations.ApiOperation;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.data.domain.Pageable;
import org.springframework.web.bind.annotation.*;

import java.util.Map;

/**
 * <AUTHOR>
 * @Data 2019/03/29
 * @Description 栏目分类信息相关接口
 */
@Api(description = "栏目分类信息相关接口")
@Slf4j
@RestController
@RequestMapping(value = "/action/programaClassifyInfo")
public class ProgramaClassifyInfoController extends LogicController<ProgramaClassifyInfo, String> {

    private IProgramaClassifyInfoService programaClassifyInfoService;

    @Autowired
    private IProgramaInfoService programaInfoService;

    @Autowired
    public ProgramaClassifyInfoController ( IProgramaClassifyInfoService programaClassifyInfoService) {
        super(programaClassifyInfoService);
        this.programaClassifyInfoService = programaClassifyInfoService;
    }

    /**
     * 新建栏目类型信息,因为每一个分类就相当于是栏目详细信息表中的一个详细信息，父栏目为空。所以在新增栏目分类时，也要在栏目详细信息中新增。
     * @param programaClassifyInfo
     * @return
     */
    @ApiOperation (value = "新建栏目分类信息", notes = "新建栏目分类信息")
    public JsonResponse create( @RequestBody (required = false) ProgramaClassifyInfo programaClassifyInfo) {
        //先插入栏目分类表
        return super.create( programaClassifyInfo );
    }

    /**
     *修改栏目分类信息
     * @param programaClassifyInfo
     * @return
     */
    @ApiOperation(value = "修改栏目分类信息", notes = "修改栏目分类信息")
    public JsonResponse update( @RequestBody(required = false) ProgramaClassifyInfo programaClassifyInfo) {
        return super.update(programaClassifyInfo );
    }

    /**
     *条件组合查询获取栏目分类信息不分页
     * @param programaClassifyInfo
     * @return
     */
    @ApiOperation(value = "条件组合查询获取栏目分类信息不分页", notes = "条件组合查询获取栏目分类信息不分页")
    @PostMapping({"/findAllNoPage","/findAllNoPage/sso"})
    public JsonResponse findAllNoPage( @RequestBody(required = false)  ProgramaClassifyInfo programaClassifyInfo ) {
        return super.findAllNoPage( programaClassifyInfo);
    }

    /**
     *根据id查询栏目类别
     * @param id
     * @return
     */
    @ApiOperation(value = "根据id查询栏目类别", notes = "根据id查询栏目类别")
    @ApiImplicitParam(name = "id", value = "栏目类别id", dataType = "String", paramType = "query")
    @PostMapping({"/findById","/findById/sso"})
    public JsonResponse findById(@RequestParam String id) {
        return super.findById( id );
    }

    /**
     * 根据id逻辑删除
     * @param id
     * @return
     */
    @ApiOperation(value = "删除栏目分类信息", notes = "删除栏目分类信息")
    @ApiImplicitParam (name = "id", value = "主键ID",  dataType = "String", paramType = "query")
    public JsonResponse deleteById(@RequestParam(required = false) String id) {
        return super.deleteById( id );
    }

    /**
     * 批量逻辑删除栏目分类信息
     * @param ids
     * @return JsonResponse
     */
    @ApiOperation(value = "批量逻辑删除栏目分类信息", notes = "批量逻辑删除栏目分类信息")
    public JsonResponse deleteAllByIds(@RequestBody(required = false) String[] ids) {
        return  super.deleteAllByIds(ids);
    }

    /**
     * 根据栏目类型(精确)以及栏目分类名称(模糊)查询
     * @return
     */
    @ApiOperation(value = "根据栏目类型(精确)以及栏目分类名称(模糊)查询", notes = "根据栏目类型(精确)以及栏目分类名称(模糊)查询")
    @ApiImplicitParams ({ //
            @ApiImplicitParam(name = "page", value = "当前页码", dataType = "int", paramType = "query", //
                    required = true, example = "1"), //
            @ApiImplicitParam(name = "size", value = "每页数量", dataType = "int", paramType = "query", //
                    required = true, example = "10"), //
            @ApiImplicitParam(name = "direction", value = "排序规则（asc/desc）", dataType = "String", //
                    paramType = "query"), //
            @ApiImplicitParam(name = "properties", value = "排序规则（属性名称）", dataType = "String", //
                    paramType = "query")
    })
    @PostMapping (value = {"/findDimProgramaClassify","/findDimProgramaClassify/sso"})
    public JsonResponse findDimProgramaClassify( @RequestParam(required = false, defaultValue = "1") int page, //
                                                 @RequestParam(required = false, defaultValue = "10") int size, //
                                                 @RequestParam(required = false) String direction, //
                                                 @RequestParam(required = false) String properties,
                                                 @RequestBody(required = false) Map<String,Object> mapObject ) {
        Pageable pageable = programaClassifyInfoService.getPageable(page, size, direction, properties);
        String  programaClassifyName = (String)mapObject.get( "programaClassifyName" );
        String  programaType = (String)mapObject.get( "programaType" );
        return programaClassifyInfoService.findDimProgramaClassify(programaClassifyName,programaType,pageable);
    }
}
