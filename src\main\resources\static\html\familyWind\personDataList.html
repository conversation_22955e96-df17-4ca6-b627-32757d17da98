<!DOCTYPE html>
<html xmlns="http://www.w3.org/1999/xhtml"
      xmlns:th="http://www.thymeleaf.org">
<head>
    <title>家风栏目</title>
    <meta http-equiv="Content-Type" content="text/html; charset=utf-8"/>
    <meta http-equiv="X-UA-Compatible" content="IE=edge"/>
    <meta name="viewport" content="width=device-width, initial-scale=1, shrink-to-fit=no"/>
    <!--
    <link href="http://************:8088/simbestui/fonts/iconfont/iconfont.css?v=svn.revision" th:href="@{http://************:8088/simbestui/fonts/iconfont/iconfont.css?v=svn.revision}" rel="stylesheet"/>
    -->
    <link href="../../fonts/iconfont/iconfont.css?v=svn.revision"
          th:href="@{/fonts/iconfont/iconfont.css?v=svn.revision}" rel="stylesheet"/>
    <link href="http://************:8088/simbestui/js/themes/default/easyui.css?v=svn.revision"
          th:href="@{http://************:8088/simbestui/js/themes/default/easyui.css?v=svn.revision}" rel="stylesheet"/>
    <link href="http://************:8088/simbestui/js/themes/icon.css?v=svn.revision"
          th:href="@{http://************:8088/simbestui/js/themes/icon.css?v=svn.revision}" rel="stylesheet"/>
    <link href="http://************:8088/simbestui/css/public.css?v=svn.revision"
          th:href="@{http://************:8088/simbestui/css/public.css?v=svn.revision}" rel="stylesheet"/>
    <link href="http://************:8088/simbestui/css/pages.css?v=svn.revision"
          th:href="@{http://************:8088/simbestui/css/pages.css?v=svn.revision}" rel="stylesheet"/>
    <script src="http://************:8088/simbestui/js/jquery.min.js?v=svn.revision"
            th:src="@{http://************:8088/simbestui/js/jquery.min.js?v=svn.revision}"
            type="text/javascript"></script>
    <script src="http://************:8088/simbestui/js/jquery.easyui.min.js?v=svn.revision"randomExam
            th:src="@{http://************:8088/simbestui/js/jquery.easyui.min.js?v=svn.revision}"
            type="text/javascript"></script>
    <script src="http://************:8088/simbestui/js/easyui-lang-zh_CN.js?v=svn.revision"
            th:src="@{http://************:8088/simbestui/js/easyui-lang-zh_CN.js?v=svn.revision}"
            type="text/javascript"></script>
    <script src="http://************:8088/simbestui/js/kindeditor/kindeditor.min.js?v=svn.revision"
            th:src="@{http://************:8088/simbestui/js/kindeditor.min.js?v=svn.revision}"
            type="text/javascript"></script>
    <script src="../../js/jquery.config.js?v=svn.revision" th:src="@{/js/jquery.config.js?v=svn.revision}"
            type="text/javascript"></script>
    <script src="http://************:8088/simbestui/js/jquery.zjsfile.js?v=svn.revision"
            th:src="@{http://************:8088/simbestui/js/jquery.zjsfile.js?v=svn.revision}"
            type="text/javascript"></script>
    <script src="http://************:8088/simbestui/js/jquery.aduq.js?v=svn.revision"
            th:src="@{http://************:8088/simbestui/js/jquery.aduq.js?v=svn.revision}"
            type="text/javascript"></script>
    <script src="http://************:8088/simbestui/js/jquery.process.js?v=svn.revision"
            th:src="@{http://************:8088/simbestui/js/jquery.process.js?v=svn.revision}"
            type="text/javascript"></script>
    <script type="text/javascript">
        $(function () {

            //showDialog做为打开对话框的公共class名称   showDialogindex做为打开修改对话框传行的索引   delete做为删除按钮配置命令   deleteid做为删除传值  searchtable做为条件查询的按钮公共class名称
            var pageparam = {
                "listtable": {
                    "listname": "#recordPersonTable",//table列表的id名称，需加#
                    "querycmd": "action/recordPerson/findAll",//table列表的查询命令
                    "contentType": "application/json; charset=utf-8",//table列表的传参方式，如果是json就写，不写默认kv
                    "nowrap": true,//把数据显示在一行里,默认true
                    "styleClass": "noScroll",
                    "sortOrder": 'desc',
                    "columns": [[//列
                        {title: "OA账号", field: "userName", width: 150,sortable:true},
                        {title: "书法投票次数", field: "calligraphyVote", width: 150},
                        {title: "绘画投票次数", field: "paintingVote", width: 150,sortable:true},
                        {title: "征文投票次数", field: "collectarticleVote", width: 150,sortable:true},
                        {title: "摄影投票次数", field: "photographyVote", width: 150,sortable:true},
                        {
                            field: "opt", title: "操作", width: 250, rowspan: 1,//align：对齐此列的数据，可以用left、right、center
                            formatter: function (value, row, index) {//单元格的格式化函数，需要三个参数：value：字段的值；rowData：行的记录数据；rowIndex：行的索引
                                var g = "<a href='#' class='readDialog' readDialogindex='" + index + "'>【查看】</a>"
                                    + "<a href='#' class='showDialog' showDialogindex='" + index + "'>【修改】</a>"
                                    + "<a href='#' delete='action/recordPerson/deleteById' deleteid='" + row.id + "'>【删除】</a>";
                                //g=g+" <a class='resolveNeed' ptitle='分解' path='html/resolve/resolveNeed.html?needTitle="+row.needTitle+"'>【分解】</a> ";
                                //g=g+" <a class='resolveNeedfind' ptitle='分解查看' path='html/resolve/resolveNeed(find).html?needTitle="+row.needTitle+"'>【分解查看】</a> "
                                return g;
                            }
                        }

                    ]]
                },
                "dialogform": {
                    "dialogid": "#buttons",//对话框的id
                    "ctable": "maUserOrg",
                    "formname": "#recordPersonTableAddForm",//新增或修改对话框的formid需加#
                    "insertcmd": "action/recordPerson/create",//新增命令
                    //"getcmd":"action/projectData/selectProjectDataName",//查询单条命令
                    "updatacmd": "action/recordPerson/update",//修改命令
                    "onSubmit": function (param) {//在请求加载数据之前触发。返回false可以停止该动作
                        //param.sysOrg={"id":$("#oid").combogrid("getValue"),"orgName":$("#orgName").val()};
                        return true;
                    }

                },
                "readDialog":{//查看
                    "dialogid": "#readDag",
                    "dialogedit": true,//查看对话框底部要不要编辑按钮
                    "formname":"#recordPersonTableReadForm"
                }
            };
            loadGrid(pageparam);


        });

        //表单校验
        window.fvalidate = function () {
            return $("#familyWindTableAddForm").form("validate");
        };

        //form表单填充数据之前做什么,data为填充数据的集合,isupdate表示是新增还是修改,若为新增则data为{}
        function beforerender(data, isupdate) {
            if (isupdate) {
                $('.update-readonly').hide();
            } else {
                $('.update-readonly').show();
            }
        };
    </script>
</head>
<style>
    .formTable { width: 100%; margin-top: 30px; border-spacing: 0; border-top: 1px solid #dedede; border-left: 1px solid #dedede; }
    .formTable>tbody>tr:not(:first-child)>td { border-right: 1px solid #dedede; border-bottom: 1px solid #dedede; font-size: 14px; height: 36px; }
    .formTable>tbody>tr>td input,.formTable>tbody>tr>td span,.formTable>tbody>tr>td textarea,.formTable>tbody>tr>td .textbox .textbox-text{ border: none; font-size: 14px; }
    .formTable td.lable{ background-color: #ddf1fe; padding: 5px; text-align: left; }
    .formTable td .textAndInput_readonly, .formTable td .textAndInput_readonly .validatebox-readonly{ background-color: #fff; }
    .cselectorImageUL .btn{ right: 3px; top: 3px; }
    .cselectorImageUL input[type='file']{ right: 25px; top: 3px; }
    textarea{ line-height: 20px; letter-spacing: 1px; }
    table.tabForm{ border-color: #dedede; }
</style>
</style>
<body class="body_page">
<form id="recordPersonTableQueryForm">
    <table border="0" cellpadding="0" cellspacing="4" width="100%">
        <tr>
            <td width="120" align="right">应用编码：</td>
            <td width="150">
                <input name="appName" type="text" value=""/></td>
            <td>
            <td>
                <div class="w100">
                    <a class="btn a_primary fr searchtable"><span>查询</span></a>   
					<a class="btn a_success showDialog fr mr10"><span>新增</span></a>
                </div>
            </td>
        </tr>
    </table>
</form>
<!--searchform-->

<!--table-->
<div class="recordPersonTable">
    <table id="recordPersonTable"></table>
</div>

<!--dialog-->
<div id="buttons" title="新增或修改" class="easyui-dialog" style="width:650px;height:300px;">
    <form id="recordPersonTableAddForm" method="post" contentType="application/json; charset=utf-8">
        <input id="id" name="id" type="hidden"/>
        <table border="0" style="width: 100%;" class="tabForm formTable" cellpadding="0" cellspacing="10">
            <tr>
                <td width="10%"></td>
                <td width="20%"></td>
            </tr>
            <tr>
                <td width="10%" class="lable">OA账号<font class="col_r">*</font></td>
                <td width="20%" length="100">
                    <input id="userName" name="userName" type="text" value="" class="easyui-validatebox" 
                           required='required'/>
                </td>
            </tr>

            <tr>
                <td width="10%" class="lable">书法次数</td>
                <td width="20%" length="100">
                    <input id="calligraphyVote" name="calligraphyVote" type="text" value="" class="easyui-validatebox" />
                </td>
            </tr>

            <tr>
                <td width="10%" class="lable">绘画次数</td>
                <td width="20%" length="100">
                    <input id="paintingVote" name="paintingVote" type="text" value="" class="easyui-validatebox" />
                </td>
            </tr>

            <tr>
                <td width="10%" class="lable">征文投票次数</td>
                <td width="20%" length="100">
                    <input id="collectarticleVote" name="collectarticleVote" type="text" value="" class="easyui-validatebox" />
                </td>
            </tr>

            <tr>
                <td width="10%" class="lable">摄影投票次数</td>
                <td width="20%" length="100">
                    <input id="photographyVote" name="photographyVote" type="text" value="" class="easyui-validatebox" />
                </td>
            </tr>



        </table>

    </form>
</div>

<div id="readDag" title="详情" class="easyui-dialog" style="width:650px;height:300px;">
    <form id="recordPersonTableReadForm" method="post" contentType="application/json; charset=utf-8">
        <table border="0" style="width: 100%;" class="tabForm formTable" cellpadding="0" cellspacing="10">
            <tr>
                <td width="15%"></td>
                <td width="20%"></td>
            </tr>
            <tr>
                <td width="15%" class="lable">OA账号</td>
                <td width="20%" length="100">
                    <input  type="text"  class="userName" readonly="readonly"/>
                </td>
            </tr>

            <tr>
                <td width="15%" class="lable">书法次数</td>
                <td width="20%" length="100">
                    <input  type="text"  class="onVoteQuantity"  readonly="readonly"/>
                </td>
            </tr>

            <tr>
                <td width="15%" class="lable">绘画次数</td>
                <td width="20%" length="100">
                    <input  type="text"  class="calligraphyVote" readonly="readonly"/>
                </td>
            </tr>

            <tr>
                <td width="15%" class="lable">征文投票次数</td>
                <td width="20%" length="100">
                    <input  type="text"  class="collectarticleVote" readonly="readonly"/>
                </td>
            </tr>

            <tr>
                <td width="15%" class="lable">摄影投票次数</td>
                <td width="20%" length="100">
                    <input  type="text"  class="photographyVote" readonly="readonly"/>
                </td>
            </tr>


        </table>

    </form>
</div>


</body>
</html>
