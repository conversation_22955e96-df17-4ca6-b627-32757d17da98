package com.cmcc.mss.osb_oa_nma_inquirymeetinginvoicesrv;

import javax.jws.WebMethod;
import javax.jws.WebParam;
import javax.jws.WebResult;
import javax.jws.WebService;
import javax.jws.soap.SOAPBinding;
import javax.xml.bind.annotation.XmlSeeAlso;

/**
 * This class was generated by Apache CXF 3.2.4
 * 2018-06-26T16:00:14.584+08:00
 * Generated source version: 3.2.4
 *
 */
@WebService(targetNamespace = "http://mss.cmcc.com/OSB_OA_NMA_InquiryMeetingInvoiceSrv", name = "OSB_OA_NMA_InquiryMeetingInvoiceSrv")
@XmlSeeAlso({ObjectFactory.class, com.cmcc.mss.msgheader.ObjectFactory.class})
@SOAPBinding(parameterStyle = SOAPBinding.ParameterStyle.BARE)
public interface OSBOANMAInquiryMeetingInvoiceSrv {

    @WebMethod(action = "process")
    @WebResult(name = "OSB_OA_NMA_InquiryMeetingInvoiceSrvResponse", targetNamespace = "http://mss.cmcc.com/OSB_OA_NMA_InquiryMeetingInvoiceSrv", partName = "payload")
    public OSBOANMAInquiryMeetingInvoiceSrvResponse process(
        @WebParam(partName = "payload", name = "OSB_OA_NMA_InquiryMeetingInvoiceSrvRequest", targetNamespace = "http://mss.cmcc.com/OSB_OA_NMA_InquiryMeetingInvoiceSrv")
        OSBOANMAInquiryMeetingInvoiceSrvRequest payload
    );
}
