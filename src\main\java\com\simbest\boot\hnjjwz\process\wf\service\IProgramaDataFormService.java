package com.simbest.boot.hnjjwz.process.wf.service;

import com.simbest.boot.base.service.ILogicService;
import com.simbest.boot.base.web.response.JsonResponse;
import com.simbest.boot.hnjjwz.backstage.announcement.model.Announcement;
import com.simbest.boot.hnjjwz.backstage.slideshow.model.SlideShow;
import com.simbest.boot.hnjjwz.process.wf.model.PmInstence;
import com.simbest.boot.hnjjwz.process.wf.model.ProgramaDataForm;
import com.simbest.boot.security.IAppDecision;
import com.simbest.boot.security.SimpleAppDecision;
import org.springframework.data.domain.Page;
import org.springframework.data.domain.Pageable;

import java.util.List;
import java.util.Map;

/**
 * <AUTHOR>
 * @Data 2019/04/17
 * @Description 审批表service层
 */
public interface IProgramaDataFormService extends ILogicService<ProgramaDataForm,String> {

    /**
     * 提交下一步
     * @param currentUserCode
     * @param workItemId
     * @param outcome
     * @param location
     * @param bodyParam
     * @return
     */
    JsonResponse nextStep(String currentUserCode,String workItemId,String outcome, String location,String formId,String source,Map<String,Object> bodyParam);

    /**
     * 起草发起流程
     * @param approvalForm
     * @param userId
     * @param outcome
     * @param message
     */
    int startProcess(ProgramaDataForm approvalForm,String userId, String outcome,String message,String source,String currentUserCode);

    /**
     *
     * @param approvalForm
     * @param workItemID
     * @param outcome
     * @param message
     * @param userId
     * @param location
     */
    int saveSubmitTask(ProgramaDataForm approvalForm, String workItemID, String outcome, String message,  String userId, String location,String source,String currentUserCode);

    /**
     * 流程审批
     * @param workItemID
     * @param currentUserCode
     * @param currentUserName
     * @param userId
     * @param outcome
     * @param message
     * @return
     */
    int processApproval( Long workItemID, String currentUserCode , String currentUserName, String userId, String outcome, String message,PmInstence pmInstence );

    /**
     * 根据决策查找人员
     * @param sysAppDecision
     * @return
     */
    JsonResponse  getOrgAndUser(String processInstId, IAppDecision sysAppDecision,String source,String currentUser);

    /**
     * 查询决策
     * @param processInstId
     * @param processDefName
     * @param location
     * @return
     */
    List<SimpleAppDecision> getDecisions(String processInstId, String processDefName, String location,String source,String currentUser,String processType);

    /**
     *  保存表单
     * @param approvalForm
     * @return
     */
    int saveApprovalFrom(ProgramaDataForm approvalForm);

    /**
     * 注销流程
     * @param approvalForm
     * @return
     */
    int deleteProcess(Long processInstId,ProgramaDataForm approvalForm);

    /**
     * 注销流程2
     * @param processInstId
     * @return
     */
    int terminateProcessInst(Long processInstId,ProgramaDataForm approvalForm);

    /**
     * 详情办理
     * @param processInstId
     * @return
     */
    JsonResponse getApprovalFromDetail(Long processInstId,String source,String currentUserCode,String pmInstId,String pmInsId ,String location);

    /**
     * 再根据pm_ins_id字段获取ApprovalForm对象
     * @param pmInsId
     * @return
     */
    ProgramaDataForm getProgramaDataFormPmInsId(String pmInsId);

    /**
     * 根据栏目编码获取栏目下的内容
     * @param programaCode
     * @return
     */
    JsonResponse findDataFromProgramaCode(String programaCode,String title,String sourceType, Pageable pageable);

    /**
     *
     * @param programaCode
     * @param pageable
     * @param programaCode
     * @return
     */
    JsonResponse findDataFromProgramaCodeByIportal(String programaCode, Pageable pageable);
    /**
     *
     * @param programaCode
     * @param pageable
     * @param programaCode
     * @return
     */
    JsonResponse  findDataByIportalList(String programaCode, Pageable pageable);


    /**
     * 查询栏目详情列表
     * @param paramsMap
     * @param pageable
     * @return
     */
    JsonResponse findDataDetailList(Map<String,Object> paramsMap,Pageable pageable);

    JsonResponse findDataDetailListNoPage(Map<String,Object> paramsMap);

    JsonResponse findDataDetailList2(Map<String,Object> paramsMap,Pageable pageable);

    /**
     * 根据轮播图id查询栏目内容信息
     * @param id
     * @return
     */
    JsonResponse findProgramaDataFromSlide(String id);

    /**
     * 根据模板编码以及模板部位id来出栏目内容列表
     * @return
     */
    JsonResponse findProgramaDataList(Map<String,Object> map);

    /**
     * 根据登录人查询栏目详情列表
     * @param  appCode
     * @return
     */
    JsonResponse findUserNameDetailList(String appCode,Pageable pageable);


    List<ProgramaDataForm> findProgramaDataForm(String programaCode);

    /**
     * 根据公告关联的信息进行查询
     * @param
     * @return
     */
    JsonResponse findProgramaDataFormRelation(String id);

    /**
     * 置顶功能
     * @param id
     * @return
     */
    JsonResponse stick( String programaCode,String id);

    /**
     * 从视图中获取用户基本信息和组织，不包含职位信息
     * @param username
     * @return
     */
    Map<String,Object> findViewUserOrg(String username);

    /**
     * 获取各个公司审批的数据
     * @param
     * @return
     */
    List<ProgramaDataForm> findCompany(String company);

    /**
     * 保存草稿
     *
     * @return
     */
    JsonResponse saveDraft(String source, String currentUserCode, ProgramaDataForm innovationTopicForm);


    /**
     * 废除草稿
     *
     * @return
     */
    JsonResponse deleteDraft(String source, String currentUserCode, String pmInsId, ProgramaDataForm innovationTopicForm);

    Page<ProgramaDataForm> findArticlePageByColumnId(List<String> columnIds, int page, int size);

    List<ProgramaDataForm> getProgramaDataList(List<String> columnIds);

    JsonResponse getDataArticleById(String articleId);

    ProgramaDataForm saveSlideShow(SlideShow resultApprovalForm);

    ProgramaDataForm saveAnnouncement(Announcement announcement);

    JsonResponse recommendedArticle(String pmInsId);
}
