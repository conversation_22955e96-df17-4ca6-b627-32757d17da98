package com.simbest.boot.hnjjwz.backstage.template.repository;

import com.simbest.boot.base.repository.LogicRepository;
import com.simbest.boot.hnjjwz.backstage.template.model.TemplatePrograma;
import org.springframework.data.domain.Page;
import org.springframework.data.domain.Pageable;
import org.springframework.data.jpa.repository.Query;
import org.springframework.data.repository.query.Param;

import java.util.Map;

/**
 * <AUTHOR>
 * @Data 201/05/06
 * @Description 模板与栏目关联
 */
public interface TemplateProgramaRepository extends LogicRepository<TemplatePrograma,String> {

    /**
     * 根据模板名称(模糊)、栏目编码(精确)、查询模板与栏目的列表并分页
     * @param templateName
     * @param programaCode
     * @param pageable
     * @return
     */
    @Query (value =  " SELECT utp.*,ut.template_name,upi.programa_display_name " +
            " FROM us_template_programa utp,us_template ut,us_programa_info upi " +
            " WHERE ut.template_name LIKE concat( concat('%',:templateName),'%')" +
            " AND upi.programa_code = :programaCode " +
            " AND ut.template_code = utp.template_code AND upi.programa_code = utp.template_code " +
            " AND utp.enabled = 1 AND utp.removed_time IS NULL " +
            " AND ut.enabled = 1 AND ut.removed_time IS NULL " +
            " AND upi.enabled = 1 AND upi.removed_time IS NULL ",
            countQuery =  " SELECT COUNT(*) " +
                    " FROM us_template_programa utp,us_template ut,us_programa_info upi " +
                    " WHERE ut.template_name LIKE concat( concat('%',:templateName),'%') " +
                    " AND upi.programa_code = :programaCode " +
                    " AND ut.template_code = utp.template_code AND upi.programa_code = utp.template_code " +
                    " AND utp.enabled = 1 AND utp.removed_time IS NULL " +
                    " AND ut.enabled = 1 AND ut.removed_time IS NULL " +
                    " AND upi.enabled = 1 AND upi.removed_time IS NULL ",
            nativeQuery = true)
    Page<Map<String,Object>> findAllDim( @Param ( "templateName" ) String templateName, @Param ( "programaCode" ) String programaCode, Pageable pageable );

    /**
     * 根据id查询模板与栏目的关联
     * @param id
     * @return
     */
    @Query (value =  " SELECT utp.*,ut.template_name,upi.programa_display_name " +
            " FROM us_template_programa utp,us_template ut,us_programa_info upi " +
            " WHERE utp.id = :id " +
            " AND ut.template_code = utp.template_code AND upi.programa_code = utp.template_code " +
            " AND utp.enabled = 1 AND utp.removed_time IS NULL " +
            " AND ut.enabled = 1 AND ut.removed_time IS NULL " +
            " AND upi.enabled = 1 AND upi.removed_time IS NULL ",
            nativeQuery = true)
    Map<String,Object> findByIdDim( @Param ( "id" ) String id);
}
