package com.simbest.boot.hnjjwz.sharingPlatform.model;

import com.simbest.boot.base.annotations.EntityIdPrefix;
import com.simbest.boot.base.model.LogicModel;
import com.simbest.boot.sys.model.SysFile;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.*;
import org.hibernate.annotations.GenericGenerator;

import javax.persistence.*;
import java.util.List;

/**
 * <AUTHOR>
 * @Data 2019/05/05
 * Description 共享平台
 */
@Data
@EqualsAndHashCode(callSuper = true)
@NoArgsConstructor
@AllArgsConstructor
@Entity(name = "us_sharing_platform")
@ApiModel(value = "共享平台")
public class SharingPlatform extends LogicModel {
    @Id
    @Column(name = "id", length = 40)
    @GeneratedValue(generator = "snowFlakeId")
    @GenericGenerator (name = "snowFlakeId", strategy = "com.simbest.boot.util.distribution.id.SnowflakeId")
    @EntityIdPrefix (prefix = "SP") //主键前缀，此为可选项注解
    private String id;

    @Column(length = 250)
    @Setter
    @Getter
    @ApiModelProperty(value = "标题", required = true)
    private String title;

    @Column(length = 4000)
    @Setter
    @Getter
    @ApiModelProperty(value = "正文", required = true)
    private String info;

    @Column(length = 100)
    @Setter
    @Getter
    @ApiModelProperty(value = "附件id", required = true)
    private String accessoryId;//上传的附件id

    @Column(length = 250)
    @Setter
    @Getter
    @ApiModelProperty(value = "下载地址", required = true)
    private String downLoadUrl;//上传的附件id

    @Column(length = 250)
    @Setter
    @Getter
    @ApiModelProperty(value = "查看地址", required = true)
    private String mobileFilePath;//上传的附件id

    @Column(length = 100)
    @Setter
    @Getter
    @ApiModelProperty(value = "创建人中文名", required = true)
    private String creatorName;

    @Column(length = 100)
    @Setter
    @Getter
    @ApiModelProperty(value = "类型", required = true)
    private String menuType;//因为页面均一样，根据左侧的菜单的不同页面显示的内容不同，此标识为唯一的区分标识。此类型与菜单表关联，不同的菜单出不同的内容。此字段的值与对应的取消萘胺

    @Column(length = 250)
    @Setter
    @Getter
    @ApiModelProperty(value = "额外字段1", required = true)
    private String spare1;

    @Column(length = 250)
    @Setter
    @Getter
    @ApiModelProperty(value = "额外字段2", required = true)
    private String spare2;

    @Column(length = 250)
    @Setter
    @Getter
    @ApiModelProperty(value = "额外字段3", required = true)
    private String spare3;

    @Transient
    private List<SysFile> infoFileList;//存放查询平台信息中的文件信息
}
