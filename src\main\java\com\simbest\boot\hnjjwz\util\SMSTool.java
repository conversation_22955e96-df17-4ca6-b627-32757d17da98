package com.simbest.boot.hnjjwz.util;

import cn.hutool.core.util.StrUtil;
import com.google.common.collect.Maps;
import com.google.common.collect.Sets;
import com.mzlion.easyokhttp.HttpClient;
import com.simbest.boot.base.exception.Exceptions;
import com.simbest.boot.base.web.response.JsonResponse;
import com.simbest.boot.cmcc.constants.CmccConstants;
import com.simbest.boot.cmcc.nmsg.MsgPostOperatorService;
import com.simbest.boot.cmcc.nmsg.model.Content;
import com.simbest.boot.cmcc.nmsg.model.ShrotMsg;
import com.simbest.boot.hnjjwz.constants.Constants;
import com.simbest.boot.security.SimpleConfig;
import com.simbest.boot.security.SimpleUser;
import com.simbest.boot.templates.MessageEnum;
import com.simbest.boot.util.SpringContextUtil;
import com.simbest.boot.util.encrypt.Des3Encryptor;
import com.simbest.boot.util.encrypt.RsaEncryptor;
import com.simbest.boot.util.json.JacksonUtils;
import com.simbest.boot.uums.api.app.UumsSysAppConfigApi;
import com.simbest.boot.uums.api.user.UumsSysUserinfoApi;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.lang3.StringUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Component;

import java.util.HashSet;
import java.util.Map;
import java.util.Set;

/**
 * @用途: 发送短信
 * @作者：zsf
 * @时间: 2018/12/7
 */
@SuppressWarnings("ALL")
@Slf4j
@Component
public class SMSTool {
    private final String[] msgSendUsers = new String[]{"oalijianwu","oazhangqianfeng","oasunhaoran","oawuxingyu","sjbg"};

    @Autowired
    private MsgPostOperatorService msgPostOperatorService;

    @Autowired
    private Des3Encryptor des3Encryptor;

    @Autowired
    private SpringContextUtil contextUtil;

    @Autowired
    private UumsSysAppConfigApi uumsSysAppConfigApi;

    @Autowired
    private RsaEncryptor rsaEncryptor;

    @Autowired
    private UumsSysUserinfoApi uumsSysUserinfoApi;

    /**
     * 发送短信
     * @param map 准备发送参数
     * @return
     */
    public Boolean sendSMS(Map<String,Object> map ){
        Boolean isPostMsgOK = false; //是否发送成功标志
        /**发送短信操作**/
        try {
            /**参数不为空情况下**/
            if ( map != null && map.size() > 0 ){
                String sendUser = map.get("sendUser") != null ? map.get("sendUser").toString(): "";
                /**发送人不为空**/
                if ( StringUtils.isNotEmpty( sendUser) ){
                    /**准备审批短信模板数据**/
                    Map<String, String> paramMap = Maps.newHashMap();
                    paramMap.put("appName", Constants.APP_NAME);
                    paramMap.put("fromUser", map.get("fromUser") != null ?  map.get("fromUser").toString() : "");
                    paramMap.put("itemSubject", map.get("itemSubject") != null ? map.get("itemSubject").toString() : "" );
                    String msg = MessageEnum.MT000001.getMessage(paramMap);
                    /**准备参数**/
                    Map<String,Object> mapParam = Maps.newHashMap();
                    mapParam.put("sendUser", sendUser);
                    mapParam.put("msg", msg);
                    isPostMsgOK = msgPostOperatorService.postMsg( readyParams( mapParam ));
                }
            }
        }catch ( Exception e ){
            Exceptions.printException( e );
        }
        return isPostMsgOK;
    }

    /**
     * 发送短信(待阅用)
     * @param map 准备发送参数
     * @return
     */
    public Boolean sendSMSdoRead(Map<String,Object> map ){
        Boolean isPostMsgOK = false; //是否发送成功标志
        /**发送短信操作**/
        try {
            /**参数不为空情况下**/
            if ( map != null && map.size() > 0 ){
                String sendUser = map.get("sendUser") != null ? map.get("sendUser").toString(): "";
                /**发送人不为空**/
                if ( StringUtils.isNotEmpty( sendUser) ){
                    /**准备审批短信模板数据**/
                /*    Map<String, String> paramMap = Maps.newHashMap();
                    paramMap.put("appName", Constants.APP_NAME);
                    paramMap.put("fromUser", map.get("fromUser") != null ?  map.get("fromUser").toString() : "");
                    paramMap.put("itemSubject", map.get("itemSubject") != null ? map.get("itemSubject").toString() : "" );*/
                    String fromUser=map.get("fromUser") != null ?  map.get("fromUser").toString() : "";
                    String itemSubject=map.get("itemSubject") != null ? map.get("itemSubject").toString() : "";
               /*     *//**
                     * appName	            : 系统名称。
                     * fromUser		        : 发送人。
                     * itemSubject			: 事项主题。
                     * 风险合规管理平台:您收到sunxin1向您发送的[测试工单请忽略:2023-01-12-003]的待阅工单，请及时处理。
                     *//*
                    MT000001("${appName}:您收到${fromUser}向您发送的[${itemSubject}]的工单待办，请及时处理。"),*/
//                    String msg = MessageEnum.MT000001.getMessage(paramMap);
                    String msg =Constants.APP_NAME +":您收到"+fromUser +"向您发送的["+itemSubject+"]的待阅工单，请及时处理。" ;
                    /**准备参数**/
                    Map<String,Object> mapParam = Maps.newHashMap();
                    mapParam.put("sendUser", sendUser);
                    mapParam.put("msg", msg);
                    isPostMsgOK = msgPostOperatorService.postMsg( readyParams( mapParam ));
                }
            }
        }catch ( Exception e ){
            Exceptions.printException( e );
        }
        return isPostMsgOK;
    }

    /**
     *
     * @param phones
     * @param msg
     * @return
     */
    public Boolean sendMsgNoShort(String sendUser,String msg) {
        if (StrUtil.equals("prd", contextUtil.getActiveProfile())) {
            /**准备参数**/
            Map<String,Object> mapParam = Maps.newHashMap();
            mapParam.put("sendUser", sendUser);
            mapParam.put("msg", msg);
            Boolean isPostMsgOK = this.postMsgWithCommon( readyParams( mapParam ));
            return isPostMsgOK;
        } else {
            return Boolean.FALSE;
        }
    }

    /**
     * 发送短信
     * @param shrotMsg      短信发送实体
     * @return
     */
    public Boolean postMsgWithCommon( ShrotMsg shrotMsg ){
        Boolean postFlag = false;
        try {
            for ( Content content : shrotMsg.getContents( ) ) {
                Set<String> msgPhones = content.getPhoneNums( );
                Set<String> resMsgPhones = Sets.newHashSet( );
                if ( msgPhones != null && !msgPhones.isEmpty( ) ) {
                    break;
                }
                SimpleUser simpleUser = uumsSysUserinfoApi.findByUsername( content.getUsername(),shrotMsg.getAppCode() );
                resMsgPhones.add( simpleUser.getPreferredMobile( ) );
                content.setPhoneNums( resMsgPhones );
            }
            String msgJson = JacksonUtils.obj2json( shrotMsg );
            String msgEncyptorJson = des3Encryptor.encrypt( msgJson );
            log.warn( "SMSTool>>>>>>>postMsgWithAnddoc>>>>>短信发送加密串>>>" + msgEncyptorJson );
            SimpleConfig simpleConfig = uumsSysAppConfigApi.findAppConfigByStyle(CmccConstants.MSG_APP_CODE,"hadmin", CmccConstants.COMMON_APP_CODE);
            JsonResponse response = HttpClient.post( simpleConfig.getAddress() + CmccConstants.MSG_POST_INTERFACE )
                    .param( "loginuser", rsaEncryptor.encrypt( "hadmin" ) )
                    .param( "appcode", CmccConstants.COMMON_APP_CODE )
                    .param( "sendMtMsgJson", msgEncyptorJson )
                    .asBean( JsonResponse.class );
            log.warn( "SMSTool>>>>>>>response>>>>>结束>>>" );
            int ret = response.getErrcode( ).intValue( );
            if ( ret == 0 ) {
                postFlag = true;
                log.warn( "SMSTool>>>>>>>response>>>>>结束>>>发送成功 ret=" + ret );
            }else {
                log.warn( "SMSTool>>>>>>>response>>>>>结束>>>发送失败" );
            }
        }catch ( Exception e ){
            Exceptions.printException(e);
            throw e;
        }
        return postFlag;
    }



    /**
     * 准备短信对象
     * @param map 参数 （endUser待发人，短信模板）
     * @return
     */
    private  ShrotMsg readyParams(Map<String,Object> map){
        ShrotMsg shrotMsg = new ShrotMsg();//短信对象
        Content content = new Content();
        Set<Content> contentSet = new HashSet<Content>();
        shrotMsg.setAppCode( Constants.APP_CODE );
        content.setUsername( map.get("sendUser") != null ? map.get("sendUser").toString():"" );
        content.setMsgContent( map.get("msg") != null ? map.get("msg").toString():""  );
        content.setImmediately( true );
        content.setSmsPriority( 1 );
        contentSet.add( content );
        shrotMsg.setContents( contentSet );
        return shrotMsg;
    }

    public void sendMsgUtil(String msgContent) {
        try {
            ShrotMsg shrotMsg = new ShrotMsg();
            Set<Content> contentSet = new HashSet<Content>();
            shrotMsg.setAppCode( Constants.APP_CODE );

            for (int i = 0,count = msgSendUsers.length; i < count; i++) {
                Content content = new Content();
                content.setUsername( msgSendUsers[i] );
                content.setMsgContent( msgContent );
                content.setImmediately( true );
                content.setSmsPriority( 1 );
                contentSet.add( content );
            }
            shrotMsg.setContents( contentSet );
            Boolean isPostMsgOK = msgPostOperatorService.postMsg( shrotMsg );
            log.error( "调用统一待办接口服务发短信状态:【{}】", JacksonUtils.obj2json(isPostMsgOK));
        } catch (Exception e) {
            Exceptions.printException( e );
        }
    }
}
