package com.simbest.boot.hnjjwz.sharingPlatform.repository;

import com.simbest.boot.base.repository.LogicRepository;
import com.simbest.boot.hnjjwz.sharingPlatform.model.SharingPlatform;
import org.springframework.data.domain.Page;
import org.springframework.data.domain.Pageable;
import org.springframework.data.jpa.repository.Query;
import org.springframework.data.repository.query.Param;
import org.springframework.stereotype.Repository;

@Repository
public interface SharingPlatformRepository  extends LogicRepository<SharingPlatform,String> {

    /**
     *根据应用id和用户名查询用户权限
     * @param title
     * @param menuType
     * @return
     */
    @Query (value = " SELECT * FROM us_sharing_platform usp " +
            " WHERE usp.title LIKE CONCAT( CONCAT('%',:title),'%') " +
            " AND usp.menu_type = :menuType " +
            " AND usp.enabled =1 AND usp.removed_time IS NULL ",
            countQuery = "SELECT COUNT (*)" +
                    " FROM us_sharing_platform usp" +
                    " WHERE usp.title LIKE CONCAT( CONCAT('%',:title),'%')" +
                    " AND usp.menu_type = :menuType " +
                    " AND usp.enabled =1 AND usp.removed_time IS NULL ",nativeQuery = true)
    Page<SharingPlatform> findAllDim( @Param ("title") String title, @Param("menuType")String menuType, Pageable pageable );
}
