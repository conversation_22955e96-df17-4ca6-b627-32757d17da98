<!DOCTYPE html>
<html xmlns="http://www.w3.org/1999/xhtml"
      xmlns:th="http://www.thymeleaf.org">
<head>
    <title>详情-清廉移动</title>
    <meta http-equiv="Content-Type" content="text/html; charset=utf-8"/>
    <meta http-equiv="X-UA-Compatible" content="IE=edge" />
    <meta name="viewport" content="width=device-width, initial-scale=1, shrink-to-fit=no"/>
    <!--
       <link href="http://************:8088/simbestui/fonts/iconfont/iconfont.css?v=svn.revision" th:href="@{http://************:8088/simbestui/fonts/iconfont/iconfont.css?v=svn.revision}" rel="stylesheet"/>
       -->
    <link href="../../fonts/iconfont/iconfont.css?v=svn.revision" th:href="@{/fonts/iconfont/iconfont.css?v=svn.revision}" rel="stylesheet"/>
    <link href="http://************:8088/simbestui/js/themes/default/easyui.css?v=svn.revision" th:href="@{http://************:8088/simbestui/js/themes/default/easyui.css?v=svn.revision}" rel="stylesheet"/>
    <link href="http://************:8088/simbestui/js/themes/icon.css?v=svn.revision" th:href="@{http://************:8088/simbestui/js/themes/icon.css?v=svn.revision}" rel="stylesheet"/>
    <link href="http://************:8088/simbestui/css/public.css?v=svn.revision" th:href="@{http://************:8088/simbestui/css/public.css?v=svn.revision}" rel="stylesheet"/>
    <link href="http://************:8088/simbestui/css/pages.css?v=svn.revision" th:href="@{http://************:8088/simbestui/css/pages.css?v=svn.revision}" rel="stylesheet"/>
    <link href="../../css/webSite.css?v=svn.revision" th:href="@{/css/webSite.css?v=svn.revision}" rel="stylesheet"/>
    <script src="http://************:8088/simbestui/js/jquery.min.js?v=svn.revision" th:src="@{http://************:8088/simbestui/js/jquery.min.js?v=svn.revision}" type="text/javascript"></script>
    <script src="http://************:8088/simbestui/js/jquery.easyui.min.js?v=svn.revision" th:src="@{http://************:8088/simbestui/js/jquery.easyui.min.js?v=svn.revision}" type="text/javascript"></script>
    <script src="http://************:8088/simbestui/js/easyui-lang-zh_CN.js?v=svn.revision" th:src="@{http://************:8088/simbestui/js/easyui-lang-zh_CN.js?v=svn.revision}" type="text/javascript"></script>
    <script src="../../js/jquery.config.js?v=svn.revision" th:src="@{/js/jquery.config.js?v=svn.revision}" type="text/javascript"></script>
    <script src="http://************:8088/simbestui/js/jquery.zjsfile.js?v=svn.revision" th:src="@{http://************:8088/simbestui/js/jquery.zjsfile.js?v=svn.revision}" type="text/javascript"></script>
    <script src="http://************:8088/simbestui/js/jquery.aduq.js?v=svn.revision" th:src="@{http://************:8088/simbestui/js/jquery.aduq.js?v=svn.revision}" type="text/javascript"></script>
    <script src="http://************:8088/simbestui/js/jquery.process.js?v=svn.revision" th:src="@{http://************:8088/simbestui/js/jquery.process.js?v=svn.revision}" type="text/javascript"></script>
    <script src="../../js/webSite.js?v=svn.revision" th:src="@{/js/webSite.js?v=svn.revision}" type="text/javascript"></script>
    <script type="text/javascript">
        $(function(){
            var type='listDetails';
            if(undefined == gps.loginuser){
                indexAjax(type);
            }else {
                ajaxgeneral({
                    //url: "action/templateLayout/simulatedLanding/sso?appcode=" + gps.appcode + "&loginuser=" + gps.loginuser,
                    url: "getCurrentUser/sso?appcode=" + gps.appcode + "&loginuser=" + gps.loginuser,
                    async: false,
                    success: function (ress) {
                        indexAjax(type);
                    }
                });
            }


            //webSite();
            var url="action/approvalForm/getApprovalFromDetail?pmInstId="+gps.pmInsId;
            if(gps.notice) url="action/announcement/getApprovalFromDetail?source=PC&pmInstId="+gps.pmInsId;
            if(gps.programaDataRelation) url="action/approvalForm/findProgramaDataFormRelation?id="+gps.programaDataRelation;
            if(gps.focus) url="action/approvalForm/findProgramaDataFromSlide?id="+gps.id;
            ajaxgeneral({
                url: url,
                success: function (res) {
                    var titD=[];
                    if((!gps.programaDataRelation) && gps.notice){
                        res.data.title=res.data.announcementTitle;
                        res.data.mainBody=res.data.announcementInfo;
                        res.data.accessoryFileList=res.data.announcementFileList;
                    }
                    var titN=res.data.programaDisplayName?res.data.programaDisplayName.split("/"):[];
                    var titU=res.data.pointUrl?res.data.pointUrl.split("/"):[];
                    //console.log(titU);
                    if(titU.length==0){
                        if(res.data.programaCode) {
                            for (var i = 0; i < res.data.programaCode.length; i += 3) {
                                titD.push({"id": res.data.programaCode.substr(0, i+3), "name": titN[i / 3],"url":(titU[i / 3]==""?"list.html":titU[i / 3])});//
                            }
                        }
                        //console.log(titD);
                        $(".navTit").html("<img class='mr5' src='../../images/er1.jpg'/><a target='_blank' href='index.html'>首页</a>"+fastrender(titD,"><a target='_blank'  href='{{url}}?id={{id}}'>{{name}}</a>"));

                    }else {
                        if(res.data.programaCode) {
                            for (var i = 0; i < res.data.programaCode.length; i += 3) {
                                titD.push({"id": res.data.programaCode.substr(0, i+3), "name": titN[i / 3],"url":(titU[i / 3]==""?"list.html":titU[i / 3])});//[i / 3]
                                //console.log(titU[i/3])
                            }
                        }
                        $(".navTit").html("<img class='mr5' src='../../images/er1.jpg'/><a target='_blank' href='index.html'>首页</a>"+fastrender(titD,"><a target='_blank' href='{{url}}?id={{id}}'>{{name}}</a>"));
                    }

                    if(res.data.title==undefined){
                        document.title=res.data.slideShowTitle+"-清廉移动";
                        $(".details_tit").html(fastrenderRow(res.data,"<h1>{{slideShowTitle}}</h1><p><font class='mr30'>来源：{{departmentName}}</font><font>发布时间：{{creationTime}}</font></p>"));


                    }else {
                        document.title=res.data.title+"-清廉移动";
                        $(".details_tit").html(fastrenderRow(res.data,"<h1>{{title}}</h1><p><font class='mr30'>来源：{{belongDepartmentName}}</font><font>发布时间：{{creationTime}}</font></p>"));


                    }
                    //$("#h6").html(res.data.programaDisplayName+"-清廉移动");


                    //$(".details_tit").html(fastrenderRow(res.data,"<h1>{{title}}</h1><p><font class='mr30'>来源：{{belongDepartmentName}}</font><font>发布时间：{{creationTime}}</font></p>"));
                    if(res.data.videoFileList && res.data.videoFileList.length>0){
                        //var voH="<video width=\"800\" height=\"600\" controls=\"controls\">\n" +
                        //    "                <source src=\"{{mobileFilePath}}\" type=\"video/mp4\" />\n" +
                        //    "                <source src=\"{{mobileFilePath}}\" type=\"video/ogg\" />\n" +
                        //    "                <source src=\"{{mobileFilePath}}\" type=\"video/webm\" />\n" +
                        //    "                <object data=\"{{mobileFilePath}}\" width=\"800\" height=\"600\">\n" +
                        //    "                    <embed src=\"{{mobileFilePath}}\" width=\"800\" height=\"600\" />\n" +
                        //    "                </object>\n" +
                        //    "            </video>";
                        //var voH="<object width=\"800\" height=\"600\" data=\"{{mobileFilePath}}\"></object>";
                        var voH="<video width=\"800\" height=\"600\" controls=\"controls\">" +
                            "<source src=\"{{mobileFilePath}}\" type=\"video/mp4\" />" +
                            "</video>";
                        $(".details_video").html(fastrender(res.data.videoFileList,voH));
                    }else{
                        $(".details_video").remove();
                    }
                    $(".details_center").html(htmlDecode(res.data.mainBody));
                    if(res.data.accessoryFileList && res.data.accessoryFileList.length>0) {
                        document.getElementById("listFile").style.display = "inline";
                        for (var i in res.data.accessoryFileList) {
                            res.data.accessoryFileList[i].openUrl = res.data.accessoryFileList[i].downLoadUrl.replace("download", "open");
                        }
                        $("ul.listFile").html(fastrender(res.data.accessoryFileList, "<li><a target='_blank' href='/" + web.appCode + "{{openUrl}}'>{{fileName}}</a><a class='ml15' target='_blank' href='/" + web.appCode + "{{downLoadUrl}}'>下载</a></li>"));
                    }


                }
            });
        });
    </script>
</head>
<style>
    .details_tit h1,
    .details_tit h2,
    .details_tit h3,
    .details_tit h4,
    .details_tit h5,
    .details_tit h6
    {
        font-size: 20px!important;
        color: #333!important;
        font-weight: 600!important;
        font-family:仿宋_GB2312!important;
        /* font-family:'Microsoft Yahei','微软雅黑',arial,'宋体',sans-serif*/
    }
    .details_center{
        padding: 10px 0;
    }
    .details_center p,
    .details_center p span{
        font-size: 16pt!important;
        color: #333!important;
        line-height: 32pt;
        /*font-family:'Microsoft Yahei','微软雅黑',arial,'宋体',sans-serif!important;*/
        font-family:仿宋_GB2312!important;
    }
    .details_center img{
        display: block;
        max-width: 100%;
        height: auto;
        margin: 10px auto;
    }
    .details_center img::before{
        content: "";
        display: block;
        width: 1px;
        height: 1px;
    }
    .details_center p{
        text-align: justify;
        text-justify: inter-ideograph;
    }
    .navTit{
        width: 770px;
        margin: 0 auto;
    }
    .details{
        width: 770px;
        margin: 0 auto;
    }
</style>
<body style="">
<!--top-->

<div class="nav">
    <ul class="auto1024">
        <!--<li><a>首页</a></li>
        <li><a>信息公开</a></li>
        <li class="li_hover"><a>规章制度</a></li>
        <li><a>巡察工作</a></li>
        <li><a>嵌入式防控监督</a></li>
        <li><a>课题研究</a></li>
        <li><a>共享平台</a></li>
        <li>
            <a>数据报送</a>
            <ul>
                <li><a>领导人员廉洁情况活页夹</a></li>
                <li><a>信息报送</a></li>
            </ul>
        </li>
        <li class="nobor"><a>信访举报</a></li>-->
    </ul>
</div>
<!--center-->
<div class="auto1024 center">
    <div class="navTit"><!--<strong></strong><a>首页</a>><a>规章制度</a>><a>文章详情</a>--></div>
    <div class="details p15">
        <div class="details_tit">
            <!--<h1>中共中央办公厅印发《中国共产党纪律检查机关监督执纪工作规则》 [纪检监察室（巡察工作办公室）]</h1>
            <p><font class="mr30">来源：综合部</font><font>发布时间：2019-01-08 11:22:12</font></p>-->
        </div>
        <div class="details_video p10 txtc">
            <!--<video width="800" height="600" controls="controls">
                <source src="movie.mp4" type="video/mp4" />
                <source src="movie.ogg" type="video/ogg" />
                <source src="movie.webm" type="video/webm" />
                <object data="movie.mp4" width="800" height="600">
                    <embed src="movie.swf" width="800" height="600" />
                </object>
            </video>-->
        </div>
        <div class="details_center" >
            <!--<p>中共中央办公厅印发《中国共产党纪律检查机关监督执纪工作规则》 [纪检监察室（巡察工作办公室）]中共中央办公厅印发《中国共产党纪律检查机关监督执纪工作规则》 [纪检监察室（巡察工作办公室）]中共中央办公厅印发《中国共产党纪律检查机关监督执纪工作规则》 [纪检监察室（巡察工作办公室）]中共中央办公厅印发《中国共产党纪律检查机关监督执纪工作规则》 [纪检监察室（巡察工作办公室）]</p>
            <p>中共中央办公厅印发《中国共产党纪律检查机关监督执纪工作规则》 [纪检监察室（巡察工作办公室）]</p>
            <p>中共中央办公厅印发《中国共产党纪律检查机关监督执纪工作规则》 [纪检监察室（巡察工作办公室）]</p>-->
        </div>
        <table id="listFile" border="0" cellpadding="0" cellspacing="0" width="100%" style="display:none">
            <tr>
                <td width="50" valign="top"><b class="lh32 f14">附件：</b></td>
                <td>
                    <ul class="list listFile">
                        <!--<li><a>中共中央办公厅印发中共中央办公厅印发中共中央办公厅印发.doc</a></li>
                        <li><a>中共中央办公厅印发中共中央办公厅印发中共中央.doc</a></li>
                        <li><a>中共中央办公厅印发中共中央办公厅印发中共中央办公.doc</a></li>
                        <li><a>中共中央办公厅印发.doc</a></li>
                        <li><a>中共中央办公厅印发中共中央办公厅印印发.doc</a></li>-->
                    </ul>
                </td>
            </tr>
        </table>
    </div>
</div>
<!--copy-->
<div class="copy">©版权所有 中国移动通信集团河南有限公司</div>
</body>
</html>
