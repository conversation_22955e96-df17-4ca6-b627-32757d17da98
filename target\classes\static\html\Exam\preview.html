<!DOCTYPE html>
<html xmlns="http://www.w3.org/1999/xhtml"
      xmlns:th="http://www.thymeleaf.org">
<head>
    <title>预览皮肤</title>
    <meta http-equiv="Content-Type" content="text/html; charset=utf-8"/>
    <meta http-equiv="X-UA-Compatible" content="IE=edge" />
    <meta name="viewport" content="width=device-width, initial-scale=1, shrink-to-fit=no"/>
    <!--
    <link href="http://iportal.ha.cmcc/portalweb/simbestui/fonts/iconfont/iconfont.css?v=svn.revision" th:href="@{http://iportal.ha.cmcc/portalweb/simbestui/fonts/iconfont/iconfont.css?v=svn.revision}" rel="stylesheet"/>
    -->
    <link rel="stylesheet" href="../../iconfont/iconfont.css">
    <link href="http://************:8088/simbestui/js/themes/default/easyui.css?v=svn.revision"  rel="stylesheet"/>
    <link href="http://************:8088/simbestui/js/themes/icon.css?v=svn.revision"  rel="stylesheet"/>
    <link href="http://************:8088/simbestui/css/public.css?v=svn.revision"  rel="stylesheet"/>
    <link href="http://************:8088/simbestui/css/pages.css?v=svn.revision"  rel="stylesheet"/>
    <script src="http://************:8088/simbestui/js/jquery.min.js?v=svn.revision"  type="text/javascript"></script>
    <script src="http://************:8088/simbestui/js/jquery.easyui.min.js?v=svn.revision"  type="text/javascript"></script>
    <script src="http://************:8088/simbestui/js/easyui-lang-zh_CN.js?v=svn.revision"  type="text/javascript"></script>
    <script src="http://************:8088/simbestui/js/kindeditor/kindeditor.min.js?v=svn.revision"  type="text/javascript"></script>
    <script src="../../js/jquery.config.js"></script>
    <script src="http://************:8088/simbestui/js/jquery.zjsfile.js?v=svn.revision"  type="text/javascript"></script>
    <script src="http://************:8088/simbestui/js/jquery.aduq.js?v=svn.revision"  type="text/javascript"></script>
    <script src="http://************:8088/simbestui/js/jquery.process.js?v=svn.revision"  type="text/javascript"></script>
    <script type="text/javascript">
        $(function(){
            var gps=getQueryString();
            $("#preview").attr('src',gps.previewSkinUrl);
        });
    </script>
</head>
<body>

<div>
    <img id="preview" alt="Failed to get picture" width="100%" height="100%">
</div>

</body>
</html>
