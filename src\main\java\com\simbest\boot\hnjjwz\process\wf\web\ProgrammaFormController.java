package com.simbest.boot.hnjjwz.process.wf.web;

import com.simbest.boot.base.web.controller.LogicController;
import com.simbest.boot.base.web.response.JsonResponse;
import com.simbest.boot.hnjjwz.backstage.slideshow.model.SlideShow;
import com.simbest.boot.hnjjwz.process.wf.model.ProgramaDataForm;
import com.simbest.boot.hnjjwz.process.wf.service.IProgramaDataFormService;
import com.simbest.boot.security.SimpleAppDecision;
import com.simbest.boot.security.SimpleGroup;
import com.simbest.boot.security.SimplePosition;
import com.simbest.boot.uums.api.group.UumsSysGroupApi;
import com.simbest.boot.uums.api.position.UumsSysPositionApi;
import io.swagger.annotations.Api;
import io.swagger.annotations.ApiImplicitParam;
import io.swagger.annotations.ApiImplicitParams;
import io.swagger.annotations.ApiOperation;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.data.domain.Pageable;
import org.springframework.web.bind.annotation.*;

import java.util.List;
import java.util.Map;

/**
 * <AUTHOR>
 * @Data 2019/04/17
 * @Description 信息报送相关操作
 */
@Api(description = "信息报送相关操作")
@Slf4j
@RestController
@RequestMapping(value = "/action/approvalForm")
public class ProgrammaFormController extends LogicController<ProgramaDataForm, String> {

    @Autowired
    private IProgramaDataFormService approvalFormService;

    @Autowired
    private UumsSysGroupApi uumsSysGroupApi;

    @Autowired
    public ProgrammaFormController(IProgramaDataFormService approvalFormService) {
        super(approvalFormService);
        this.approvalFormService = approvalFormService;
    }

    /**
     * 提交下一步
     * @param currentUserCode
     * @param workItemId
     * @param outcome
     * @param location
     * @param bodyParam
     * @return
     * @throws Exception
     */
    @ApiOperation(value = "提交起草流程", notes = "通过此接口启动流转审批表单")
    @ApiImplicitParams({
            @ApiImplicitParam(name = "currentUserCode", value = "当前登录人", dataType = "String", paramType = "query",required = true),
            @ApiImplicitParam(name = "workItemId", value = "工作项ID", dataType = "String", paramType = "query"),
            @ApiImplicitParam(name = "outcome", value = "连线规则", dataType = "String", paramType = "query", required = true),
            @ApiImplicitParam(name = "message", value = "审批意见", dataType = "String", paramType = "query",required = true),
            @ApiImplicitParam(name = "location", value = "当前状态", dataType = "String", paramType = "query"),
            @ApiImplicitParam(name = "formId", value = "表单id", dataType = "String", paramType = "query", required = false),
            @ApiImplicitParam(name = "source", value = "来源", dataType = "String", paramType = "query", required = false)
    })
    @PostMapping(value = {"/startSubmitProcess","/startSubmitProcess/sso","/api/startSubmitProcess"})
    public JsonResponse startSubmitProcessMS(@RequestParam String currentUserCode,
                                             @RequestParam(required = false) String workItemId,
                                             @RequestParam String outcome,
                                             @RequestParam(required = false) String location,
                                             @RequestParam(required = false) String source,
                                             @RequestParam(required = false) String formId,
                                             @RequestBody(required = false) Map<String,Object> bodyParam
    ) throws Exception {
        return approvalFormService.nextStep(currentUserCode, workItemId, outcome, location,formId,source,bodyParam);
    }

    /**
     * 根据决策显示组织人员
     * @param processInstId
     * @param appDecision
     * @return
     */
    @ApiOperation(value = "根据决策显示组织人员", notes = "根据决策查询人员组织")
    @ApiImplicitParams({
            @ApiImplicitParam(name = "processInstId", value = "流程实例ID", dataType = "String", paramType = "query"),
            @ApiImplicitParam(name = "source", value = "来源", dataType = "String", paramType = "query"),
            @ApiImplicitParam(name = "currentUserCode", value = "当前人", dataType = "String", paramType = "query")
    })
    @PostMapping(value = {"/getOrgAndUser","/getOrgAndUser/sso","/api/getOrgAndUser"})
    public JsonResponse getOrgAndUser(@RequestParam(required = false) String processInstId,
                                      @RequestParam(required = false) String source,
                                      @RequestParam(required = false) String currentUserCode,
                                      @RequestBody SimpleAppDecision appDecision ){
        return approvalFormService.getOrgAndUser(processInstId,appDecision,source,currentUserCode  );
    }

    /**
     * 查询决策
     * @param processInstId 流程实例id
     * @param processDefName 流程定义名称
     * @param location       当前环节
     * @return
     */
    @ApiOperation( value = "查询决策" , notes = "根据当前环节提供相应的决策")
    @ApiImplicitParams({
            @ApiImplicitParam(name = "processInstId", value = "流程实例ID", dataType = "String", paramType = "query"),
            @ApiImplicitParam(name = "processDefName", value = "流程定义名称", dataType = "String" ,paramType = "query"),
            @ApiImplicitParam(name = "location", value = "当前环节", dataType = "String", paramType = "query", required = true),
            @ApiImplicitParam(name = "source", value = "来源", dataType = "String", paramType = "query"),
            @ApiImplicitParam(name = "currentUserCode", value = "当前人", dataType = "String", paramType = "query"),
            @ApiImplicitParam(name = "processType", value = "流程类型", dataType = "String", paramType = "query")
    })
    @PostMapping(value = {"/getDecisions","/getDecisions/sso","/api/getDecisions"})
    public JsonResponse getDecisions(@RequestParam(required = false) String processInstId,
                                     @RequestParam(required = false) String processDefName ,
                                     @RequestParam(required = false ) String location,
                                     @RequestParam(required = false ) String source,
                                     @RequestParam(required = false ) String currentUserCode,
                                     @RequestParam(required = false) String processType){
        return JsonResponse.success(approvalFormService.getDecisions( processInstId,processDefName,location,source,currentUserCode,processType ));
    }

    /**
     * 注销该流程
     * @param currentUserCode 当前登录人
     * @param processInstId 流程实例id
     * @param approvalForm 表单
     * @return
     */
    @ApiOperation(value = "注销该流程", notes = "注销起草的流程")
    @ApiImplicitParams({
            @ApiImplicitParam(name = "currentUserCode", value = "当前登录人", dataType = "String", paramType = "query",required = true),
            @ApiImplicitParam(name = "processInstId", value = "流程实例id", dataType = "Long", paramType = "query",required = true),
    })
    @PostMapping(value = {"/deleteProcess","/api/deleteProcess"})
    public JsonResponse deleteProcess(@RequestParam(required = false) String currentUserCode,
                                      @RequestParam(required = false) Long processInstId,
                                      @RequestParam(required = false) String source,
                                      @RequestBody(required = false) ProgramaDataForm approvalForm){
        return JsonResponse.success(approvalFormService.deleteProcess(processInstId,approvalForm),"注销成功！");
    }

    /**
     * 注销该流程
     * @param currentUserCode 当前登录人
     * @param processInstId 流程实例id
     * @param
     * @return
     */
    @ApiOperation(value = "注销该流程2", notes = "注销起草的流程2")
    @ApiImplicitParams({
            @ApiImplicitParam(name = "currentUserCode", value = "当前登录人", dataType = "String", paramType = "query",required = true),
            @ApiImplicitParam(name = "processInstId", value = "流程实例id", dataType = "Long", paramType = "query",required = true),
    })
    @PostMapping(value = {"/terminateProcessInst","/api/terminateProcessInst"})
    public JsonResponse terminateProcessInst  (@RequestParam(required = false) String currentUserCode,
                                               @RequestParam(required = false) Long processInstId,
                                               @RequestParam(required = false) String source,
                                               @RequestBody(required = false) ProgramaDataForm approvalForm){
        return JsonResponse.success(approvalFormService.terminateProcessInst(processInstId,approvalForm),"注销成功！");
    }


    /**
     * 详情办理
     * @param processInstId 流程实例idgetApprovalFromDetail
     * @return
     */
    @ApiOperation(value = "打开详情", notes = "办理")
    @ApiImplicitParams({
            @ApiImplicitParam(name = "processInstId", value = "流程实例id，用于流程中获取详情", dataType = "String", paramType = "query"),
            @ApiImplicitParam(name = "source", value = "来源", dataType = "String", paramType = "query"),
            @ApiImplicitParam(name = "currentUserCode", value = "当前用户", dataType = "String", paramType = "query"),
            @ApiImplicitParam(name = "pmInstId", value = "表单中的流程id", dataType = "String", paramType = "query"),
    })
    @PostMapping(value = {"/getApprovalFromDetail", "/getApprovalFromDetail/sso","/api/getApprovalFromDetail"})
    public JsonResponse getApprovalFromDetail(@RequestParam(required = false) Long processInstId,
                                              @RequestParam(required = false) String source,
                                              @RequestParam(required = false) String currentUserCode,
                                              @RequestParam(required = false) String pmInstId,
                                              @RequestParam(required = false) String pmInsId,
                                              @RequestParam(required = false) String location){

        return approvalFormService.getApprovalFromDetail(processInstId,source,currentUserCode,pmInstId,pmInsId,location);
    }

    /**
     * 新增栏目的数据信息
     * @param programaDataInfo
     * @return
     */
    @ApiOperation (value = "新增栏目的数据信息", notes = "新增栏目的数据信息")
    public JsonResponse create( @RequestBody (required = false) ProgramaDataForm programaDataInfo) {
        return super.create( programaDataInfo );
    }

    /**
     * 修改栏目的数据信息
     * @param programaDataInfo
     * @return
     */
    @ApiOperation(value = "修改栏目的数据信息", notes = "修改栏目的数据信息")
    public JsonResponse update( @RequestBody (required = false) ProgramaDataForm programaDataInfo) {
        return super.update(programaDataInfo );
    }

    /**
     * 根据id逻辑删除
     * @param id
     * @return
     */
    @ApiOperation (value = "删除栏目的数据信息", notes = "删除栏目的数据信息")
    @ApiImplicitParam (name = "id", value = "主键ID",  dataType = "String", paramType = "query")
    public JsonResponse deleteById(@RequestParam (required = false) String id) {
        return super.deleteById( id );
    }

    /**
     * 批量逻辑删除栏目的数据信息
     * @param ids
     * @return JsonResponse
     */
    @ApiOperation(value = "批量逻辑删除栏目的数据信息", notes = "批量逻辑删除栏目的数据信息")
    public JsonResponse deleteAllByIds(@RequestBody(required = false) String[] ids) {
        return  super.deleteAllByIds(ids);
    }

    /**
     * 根据栏目获取栏目下的内容并分页
     * @param programaCode
     * @return
     */
    @ApiOperation(value = "根据栏目编码获取栏目下的内容", notes = "根据栏目获取栏目下的内容")
    @ApiImplicitParams( {
            @ApiImplicitParam(name = "page", value = "当前页码", dataType = "int", paramType = "query", example = "1"), //
            @ApiImplicitParam(name = "size", value = "每页数量", dataType = "int", paramType = "query", example = "10"), //
            @ApiImplicitParam(name = "direction", value = "排序规则（asc/desc）", dataType = "String", //
                    paramType = "query"), //
            @ApiImplicitParam(name = "properties", value = "排序规则（属性名称）", dataType = "String", //
                    paramType = "query") ,
            @ApiImplicitParam(name = "programaCode", value = "栏目编码", dataType = "string", paramType = "query")
    } )
    @PostMapping (value = {"/findDataFromProgramaCode","/findDataFromProgramaCode/sso"})
    public JsonResponse findDataFromProgramaCode(@RequestParam(required = false, defaultValue = "1") int page, //
                                                 @RequestParam(required = false, defaultValue = "10") int size, //
                                                 @RequestParam(required = false) String direction, //
                                                 @RequestParam(required = false) String properties,
                                                 @RequestParam(required = false) String programaCode,
                                                 @RequestParam(required = false) String title,
                                                 @RequestParam(required = false) String sourceType) {
        Pageable pageable = approvalFormService.getPageable(page, size, "desc", "creation_time");
        return  approvalFormService.findDataFromProgramaCode(programaCode,title,sourceType, pageable);
    }

    /**
     * 查询栏目详情列表
     * @param page
     * @param size
     * @param direction
     * @param properties
     * @param paramsMap
     * @return
     */
    @ApiOperation(value = "查询栏目详情列表", notes = "查询栏目详情列表")
    @ApiImplicitParams( {
            @ApiImplicitParam(name = "page", value = "当前页码", dataType = "int", paramType = "query", example = "1"), //
            @ApiImplicitParam(name = "size", value = "每页数量", dataType = "int", paramType = "query", example = "10"), //
            @ApiImplicitParam(name = "direction", value = "排序规则（asc/desc）", dataType = "String", //
                    paramType = "query"), //
            @ApiImplicitParam(name = "properties", value = "排序规则（属性名称）", dataType = "String", //
                    paramType = "query")
    } )
    @PostMapping (value = {"/findDataDetailList","/findDataDetailList/sso"})
    public JsonResponse findDataDetailList(@RequestParam(required = false, defaultValue = "1") int page, //
                                           @RequestParam(required = false, defaultValue = "10") int size, //
                                           @RequestParam(required = false) String direction, //
                                           @RequestParam(required = false) String properties,
                                           @RequestBody(required = false) Map<String,Object> paramsMap) {
        Pageable pageable = approvalFormService.getPageable(page, size, "desc", "creation_time");
        return  approvalFormService.findDataDetailList(paramsMap, pageable);
    }

    @PostMapping (value = {"/findDataDetailListNoPage","/findDataDetailListNoPage/sso","/findDataDetailListNoPage/anonymous"})
    public JsonResponse findDataDetailListNoPage(@RequestParam(required = false, defaultValue = "1") int page, //
                                           @RequestParam(required = false, defaultValue = "10") int size, //
                                           @RequestParam(required = false) String direction, //
                                           @RequestParam(required = false) String properties,
                                           @RequestBody(required = false) Map<String,Object> paramsMap) {
        return  approvalFormService.findDataDetailListNoPage(paramsMap);
    }

    /**
     * 查询栏目详情列表2
     * @param page
     * @param size
     * @param direction
     * @param properties
     * @param paramsMap
     * @return
     */
    @ApiOperation(value = "查询栏目详情列表", notes = "查询栏目详情列表")
    @ApiImplicitParams( {
            @ApiImplicitParam(name = "page", value = "当前页码", dataType = "int", paramType = "query", example = "1"), //
            @ApiImplicitParam(name = "size", value = "每页数量", dataType = "int", paramType = "query", example = "10"), //
            @ApiImplicitParam(name = "direction", value = "排序规则（asc/desc）", dataType = "String", //
                    paramType = "query"), //
            @ApiImplicitParam(name = "properties", value = "排序规则（属性名称）", dataType = "String", //
                    paramType = "query")
    } )
    @PostMapping (value = {"/findDataDetailList2","/findDataDetailList2/sso"})
    public JsonResponse findDataDetailList2(@RequestParam(required = false, defaultValue = "1") int page, //
                                           @RequestParam(required = false, defaultValue = "10") int size, //
                                           @RequestParam(required = false) String direction, //
                                           @RequestParam(required = false) String properties,
                                           @RequestBody(required = false) Map<String,Object> paramsMap) {
        Pageable pageable = approvalFormService.getPageable(page, size, "desc", "creation_Time");
        return  approvalFormService.findDataDetailList2(paramsMap, pageable);
    }

    @ApiOperation(value = "根据登录人查询栏目详情列表", notes = "根据登录人查询栏目详情列表")
    @ApiImplicitParams( {
            @ApiImplicitParam(name = "page", value = "当前页码", dataType = "int", paramType = "query", example = "1"), //
            @ApiImplicitParam(name = "size", value = "每页数量", dataType = "int", paramType = "query", example = "10"), //
            @ApiImplicitParam(name = "direction", value = "排序规则（asc/desc）", dataType = "String", //
                    paramType = "query"), //
            @ApiImplicitParam(name = "properties", value = "排序规则（属性名称）", dataType = "String", //
                    paramType = "query")
    } )
    @PostMapping (value = {"/findUserNameDetailList","/findUserNameDetailList/sso"})
    public JsonResponse findUserNameDetailList(@RequestParam(required = false, defaultValue = "1") int page, //
                                               @RequestParam(required = false, defaultValue = "10") int size, //
                                               @RequestParam(required = false) String direction, //
                                               @RequestParam(required = false) String properties,
                                               @RequestParam(required = false) String appCode) {

        Pageable pageable = approvalFormService.getPageable(page, size, direction, properties);
        return  approvalFormService.findUserNameDetailList(appCode,pageable);
    }

    /**
     * 根据轮播图id查询栏目内容信息
     * @param id
     * @return
     */
    @ApiOperation(value = "根据轮播图id查询栏目内容信息", notes = "根据轮播图id查询栏目内容信息")
    @ApiImplicitParam(name = "id", value = "轮播图id", dataType = "String", paramType = "query")
    @PostMapping (value = {"/findProgramaDataFromSlide","/findProgramaDataFromSlide/sso"})
    public JsonResponse findProgramaDataFromSlide(@RequestParam(required = false) String id) {
        return  approvalFormService.findProgramaDataFromSlide(id);
    }

    /**
     * 根据模板编码以及模板部位id来出栏目内容列表
     * @param map
     * @return
     */
    @ApiOperation(value = "根据模板编码以及模板部位id来出栏目内容列表", notes = "根据模板编码以及模板部位id来出栏目内容列表")
    @PostMapping (value = {"/findProgramaDataList","/findProgramaDataList/sso"})
    public JsonResponse findProgramaDataList( @RequestBody(required = false) Map<String,Object> map) {
        return approvalFormService.findProgramaDataList(map);
    }

    /**
     * 根据公告关联的信息进行查询
     * @param
     * @return
     */
    @ApiOperation(value = "根据公告关联的信息进行查询", notes = "根据公告关联的信息进行查询")
    @PostMapping (value = {"/findProgramaDataFormRelation","/findProgramaDataFormRelation/sso"})
    public JsonResponse findProgramaDataFormRelation( @RequestParam String id) {
        return approvalFormService.findProgramaDataFormRelation(id);
    }

    /**
     * 置顶
     * @param
     * @return
     */
    @ApiOperation(value = "置顶", notes = "置顶")
    @PostMapping (value = {"/stick","/stick/sso"})
    public JsonResponse stick( @RequestParam String pmInsId,@RequestParam String id) {
        return approvalFormService.stick(pmInsId,id);
    }

    /**
     * 从视图中获取用户基本信息和组织，不包含职位信息
     * @param username
     * @return
     */
    @ApiOperation(value = "从视图中获取用户基本信息和组织", notes = "从视图中获取用户基本信息和组织")
    @PostMapping (value = {"/findViewUserOrg","/findViewUserOrg/sso"})
    public JsonResponse findViewUserOrg( @RequestParam String username) {
        return JsonResponse.success(approvalFormService.findViewUserOrg(username));
    }



    /**
     * 再根据pm_ins_id字段获取ApprovalForm对象
     * @param map
     * @return
     */
    @ApiOperation(value = "再根据pm_ins_id字段获取ApprovalForm对象", notes = "再根据pm_ins_id字段获取ApprovalForm对象")
    @PostMapping (value = {"/getProgramaDataFormPmInsId","/getProgramaDataFormPmInsId/sso"})
    public JsonResponse getProgramaDataFormPmInsId( @RequestBody Map<String, Object> map) {
        String pmInsId=String.valueOf(map.get("pmInstId"));
        return JsonResponse.success(approvalFormService.getProgramaDataFormPmInsId(pmInsId));
    }


    /**
     * 新门户获取栏目下内容
     * @param programaCode
     * @return
     */
    @ApiOperation(value = "新门户获取栏目下内容", notes = "新门户获取栏目下内容")
    @ApiImplicitParams( {
            @ApiImplicitParam(name = "page", value = "当前页码", dataType = "int", paramType = "query", example = "1"), //
            @ApiImplicitParam(name = "size", value = "每页数量", dataType = "int", paramType = "query", example = "10"), //
            @ApiImplicitParam(name = "programaCode", value = "栏目编码", dataType = "string", paramType = "query")
    } )
    @PostMapping (value = {"/findDataFromProgramaCodeByIportal","sso/findDataFromProgramaCodeByIportal"})
    public JsonResponse findDataFromProgramaCodeByIportal(@RequestParam(required = false, defaultValue = "1") int page, //
                                                 @RequestParam(required = false, defaultValue = "10") int size, //
                                                 @RequestParam(required = false) String programaCode) {
        Pageable pageable = approvalFormService.getPageable(page, size, "desc", "creation_time");
        return  approvalFormService.findDataFromProgramaCodeByIportal(programaCode, pageable);
    }



    /**
     * 2022.07根据所传栏目codeList 新门户获取栏目下内容
     * @param programaCode
     * @return
     */
    @ApiOperation(value = "门户获取栏目下内容", notes = "门户获取栏目下内容")
    @ApiImplicitParams( {
            @ApiImplicitParam(name = "page", value = "当前页码", dataType = "int", paramType = "query", example = "1"), //
            @ApiImplicitParam(name = "size", value = "每页数量", dataType = "int", paramType = "query", example = "10"), //
            @ApiImplicitParam(name = "programaCode", value = "栏目编码", dataType = "string", paramType = "query")
    } )
    @PostMapping (value = {"/findDataByIportalList","sso/findDataByIportalList"})
    public JsonResponse findDataByIportalList(@RequestParam(required = false, defaultValue = "1") int page, //
                                                          @RequestParam(required = false, defaultValue = "10") int size, //
                                                          @RequestParam(required = false) String programaCode) {
        Pageable pageable = approvalFormService.getPageable(page, size, "desc", "creation_time");
        return  approvalFormService.findDataByIportalList(programaCode, pageable);
    }


    @ApiOperation(value = "保存草稿", notes = "保存草稿")
    @PostMapping(value = {"/saveDraft", "/saveDraft/api"})
    public JsonResponse saveDraft(@RequestParam(required = false) String source,
                                  @RequestParam(required = false) String currentUserCode,
                                  @RequestBody ProgramaDataForm innovationTopicForm) {
        return approvalFormService.saveDraft(source, currentUserCode, innovationTopicForm);
    }

    @ApiOperation(value = "废除草稿", notes = "废除草稿")
    @PostMapping(value = {"/deleteDraft", "/deleteDraft/api"})
    public JsonResponse deleteDraft(@RequestParam(required = false) String source,
                                    @RequestParam(required = false) String currentUserCode,
                                    @RequestParam(required = false) String pmInsId,
                                    @RequestBody ProgramaDataForm innovationTopicForm) {
        return approvalFormService.deleteDraft(source, currentUserCode, pmInsId, innovationTopicForm);
    }

}
