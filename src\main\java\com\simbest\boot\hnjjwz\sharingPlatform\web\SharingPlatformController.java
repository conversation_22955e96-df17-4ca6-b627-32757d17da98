package com.simbest.boot.hnjjwz.sharingPlatform.web;

import com.simbest.boot.base.web.controller.LogicController;
import com.simbest.boot.base.web.response.JsonResponse;
import com.simbest.boot.hnjjwz.sharingPlatform.model.SharingPlatform;
import com.simbest.boot.hnjjwz.sharingPlatform.service.ISharingPlatformService;
import com.simbest.boot.hnjjwz.util.FileTool;
import com.simbest.boot.sys.model.SysFile;
import com.simbest.boot.sys.service.ISysFileService;
import io.swagger.annotations.Api;
import io.swagger.annotations.ApiImplicitParam;
import io.swagger.annotations.ApiImplicitParams;
import io.swagger.annotations.ApiOperation;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.lang3.StringUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.data.domain.Pageable;
import org.springframework.web.bind.annotation.*;

import java.util.ArrayList;
import java.util.Arrays;
import java.util.List;
import java.util.Map;

/**
 * <AUTHOR>
 * @Data 2019/05/05
 * @Description 共享平台相关接口
 */
@Api(description = "共享平台相关接口")
@Slf4j
@RestController
@RequestMapping(value = "/action/sharingPlatform")
public class SharingPlatformController extends LogicController<SharingPlatform, String> {

    private ISharingPlatformService sharingPlatformService;


    @Autowired
    public SharingPlatformController ( ISharingPlatformService sharingPlatformService) {
        super(sharingPlatformService);
        this.sharingPlatformService = sharingPlatformService;
    }

    @Autowired
    private FileTool fileTool;

    @Autowired
    private ISysFileService sysFileService;

    /**
     * 根据标题(模糊)以及类型(精确)查询不分页
     * @param page
     * @param size
     * @param direction
     * @param properties
     * @param sharingPlatformMap
     * @return
     */
    @ApiOperation (value = "根据标题(模糊)以及类型(精确)查询不分页", notes = "根据标题(模糊)以及类型(精确)查询不分页")
    @ApiImplicitParams ({ //
            @ApiImplicitParam (name = "page", value = "当前页码", dataType = "int", paramType = "query", //
                    required = true, example = "1"), //
            @ApiImplicitParam(name = "size", value = "每页数量", dataType = "int", paramType = "query", //
                    required = true, example = "10"), //
            @ApiImplicitParam(name = "direction", value = "排序规则（asc/desc）", dataType = "String", //
                    paramType = "query"), //
            @ApiImplicitParam(name = "properties", value = "排序规则（属性名称）", dataType = "String", //
                    paramType = "query") //
    })
    @PostMapping (value = {"/findAllDim","/findAllDim/sso"})
    public JsonResponse findAllDim( @RequestParam (required = false, defaultValue = "1") int page, //
                                 @RequestParam(required = false, defaultValue = "10") int size, //
                                 @RequestParam(required = false) String direction, //
                                 @RequestParam(required = false) String properties,
                                 @RequestBody(required = false) Map<String,Object> sharingPlatformMap //
    ) {
        Pageable pageable = sharingPlatformService.getPageable(page, size, direction, properties);
        return sharingPlatformService.findAllDim(sharingPlatformMap,pageable);
    }

    /**
     * 新增共享平台一个菜单下的信息
     * @return
     */
    @ApiOperation (value = "新增共享平台一个栏目下的信息", notes = "新增共享平台一个栏目下的信息")
    @PostMapping (value = {"/createDim","/createDim/sso"})
    public JsonResponse createDim( @RequestBody(required = false) SharingPlatform sharingPlatform ) {
        boolean state = changeFile(sharingPlatform);
        if(state){
            return super.create( sharingPlatform );
        }else {
            return JsonResponse.success(-1,"获取文件失败");
        }

    }

    /**
     * 修改共享平台一个菜单下的信息
     * @return
     */
    @ApiOperation (value = "修改共享平台一个菜单下的信息", notes = "修改共享平台一个菜单下的信息")
    @PostMapping (value = {"/updateDim","/updateDim/sso"})
    public JsonResponse updateDim( @RequestBody(required = false) SharingPlatform sharingPlatform ) {
        boolean state = changeFile(sharingPlatform);
        if(state){
            return super.update( sharingPlatform );
        }else {
            return JsonResponse.success(-1,"获取文件失败");
        }

    }

    /**
     * 放入附件的id
     * @param sharingPlatform
     */
    private boolean changeFile(SharingPlatform sharingPlatform){
        //todo sharingPlatform中必须有一个字段menuType有值
        //把图片的id取出来，存到数据库中
        List<SysFile> sysFileList = sharingPlatform.getInfoFileList();

        if(sysFileList.size()>0){
            for(SysFile sysFile:sysFileList){
                sharingPlatform.setTitle(sysFile.getFileName());//改后
                sharingPlatform.setAccessoryId(sysFile.getId() );
                sharingPlatform.setDownLoadUrl(sysFile.getDownLoadUrl());
                sharingPlatform.setMobileFilePath(sysFile.getMobileFilePath());
            }
            return  true;
        }else {
            return  false;
        }

    }

    /**
     * 根据id查询菜单下的信息
     * @param id
     * @return
     */
    @ApiOperation (value = "根据id查询菜单下的信息", notes = "根据id查询菜单下的信息")
    @ApiImplicitParam(name = "id", value = "栏目信息ID", dataType = "String", paramType = "query")
    @PostMapping (value = {"/findByIdDim","/findByIdDim/sso"})
    public JsonResponse findByIdDim(@RequestParam(required = false) String id,@RequestParam(required = false) String menuType) {
        SharingPlatform sharingPlatform = sharingPlatformService.findById( id );
        if(sharingPlatform==null){
            return JsonResponse.success( null );
        }
        String fileId = sharingPlatform.getAccessoryId();
        List<SysFile> sysFileList = new ArrayList<>(  );
        if( !StringUtils.isEmpty( fileId )){
            List<String> fileList = Arrays.asList( fileId.split( "," ) );
            for(String fileNewId:fileList){
                SysFile sysfile = sysFileService.findById( fileId );
                sysFileList.add( sysfile );
            }
        }
        sharingPlatform.setInfoFileList( sysFileList );
        return JsonResponse.success( sharingPlatform );
    }


}
