package com.simbest.boot.hnjjwz.backstage.taskStudy.model;/**
 * Created by KZH on 2019/6/13 9:30.
 */

import com.simbest.boot.base.annotations.EntityIdPrefix;
import com.simbest.boot.cmcc.wf.model.WfFormModel;
import com.simbest.boot.sys.model.SysFile;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.*;
import org.hibernate.annotations.GenericGenerator;

import javax.persistence.*;
import java.io.File;
import java.util.List;

/**
 * <AUTHOR>
 * @create 2019-06-13 9:30
 * @desc 课题研究
 **/
@Data
@EqualsAndHashCode(callSuper = true)
@NoArgsConstructor
@AllArgsConstructor
@Entity(name = "us_task_study")
@ApiModel(value = "课题研究")
public class TaskStudy extends WfFormModel {
    @Id
    @Column(name = "id", length = 40)
    @GeneratedValue(generator = "snowFlakeId")
    @GenericGenerator(name = "snowFlakeId", strategy = "com.simbest.boot.util.distribution.id.SnowflakeId")
    @EntityIdPrefix(prefix = "TS") //主键前缀，此为可选项注解
    private String id;

    @Setter
    @Getter
    @Column(name = "pmInsId",nullable = false,length = 100)
    @ApiModelProperty(value = "业务实例id")
    private String pmInsId;

    @Setter
    @Getter
    @Column(length = 100)
    @ApiModelProperty(value = "所属栏目全路径名")
    private String programaDisplayName;//所属栏目全路径名

    @Setter
    @Getter
    @Column(length = 100)
    @ApiModelProperty(value = "发布时间")
    private String creationTime;//审核发布后的时间

    @Setter
    @Getter
    @Column(length = 10)
    @ApiModelProperty(value = "是否发布")
    private Boolean isPublish;//是否发布

    @Setter
    @Getter
    @Column( length = 40)
    @ApiModelProperty(value = "起草人oa账号")
    @NonNull
    private String username;

    @Setter
    @Getter
    @Column( length = 40)
    @ApiModelProperty(value = "起草人姓名")
    @NonNull
    private String truename;

    @Setter
    @Getter
    @Column( length = 240)
    @ApiModelProperty(value = "起草人公司")
    @NonNull
    private String company;

    @Setter
    @Getter
    @Column( length = 240)
    @ApiModelProperty(value = "起草人部门")
    @NonNull
    private String departmentName;

    @Setter
    @Getter
    @Column( length = 240)
    @ApiModelProperty(value = "起草人组织路径")
    @NonNull
    private String displayName;

    @Setter
    @Getter
    @ApiModelProperty(value = "课题研究标题", required = true)
    private String taskStudyTitle;

    @Setter
    @Getter
    @ApiModelProperty(value = "课题研究所在文件的id", required = true)
    private String taskStudyId;

    @Setter
    @Getter
    @ApiModelProperty(value = "课题研究所在文件的Url", required = true)
    private String taskStudyUrl;

    @Setter
    @Getter
    @Column(columnDefinition="int default 0")
    @ApiModelProperty(value = "置顶标识")
    private int stickFlag;

    @Setter
    @Getter
    @ApiModelProperty(value = "额外字段1", required = true)
    private String spare1;

    @Setter
    @Getter
    @ApiModelProperty(value = "额外字段2", required = true)
    private String spare2;

    @Setter
    @Getter
    @ApiModelProperty(value = "额外字段3", required = true)
    private String spare3;

    @Transient
    private List<SysFile> taskStudyFile;//存放文件信息

    @Transient
    @ApiModelProperty(value = "手机端判断 true和1可以在手机端处理",required = true)
    private boolean inMobile;

    @Transient
    @ApiModelProperty(value = "富文本压缩base64手机端用")
    private String compressBase;
}
