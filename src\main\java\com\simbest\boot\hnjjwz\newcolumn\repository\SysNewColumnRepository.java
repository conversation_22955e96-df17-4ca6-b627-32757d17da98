package com.simbest.boot.hnjjwz.newcolumn.repository;

import com.simbest.boot.base.repository.LogicRepository;
import com.simbest.boot.hnjjwz.backstage.announcement.model.Announcement;
import com.simbest.boot.hnjjwz.backstage.slideshow.model.SlideShow;
import com.simbest.boot.hnjjwz.newcolumn.model.SysNewColumnModel;
import com.simbest.boot.hnjjwz.process.wf.model.ProgramaDataForm;
import com.simbest.boot.hnjjwz.sharingPlatform.model.MenuDeploy;
import org.springframework.data.jpa.repository.Query;
import org.springframework.data.repository.query.Param;
import org.springframework.stereotype.Repository;

import java.util.List;
import java.util.Map;

@Repository
public interface SysNewColumnRepository extends LogicRepository<SysNewColumnModel, String> {
    @Query(value = "select * from sys_new_column_model  where enabled = 1 and is_display = 1 and column_id in(select column_id from sys_column_user_org  where enabled = 1  and value in(:values)) order by column_level,column_order", nativeQuery = true)
    List<SysNewColumnModel> findColumnByValues(@Param("values") List<String> values);




    @Query(
            value = "select  COLUMN_ID from sys_new_column_model where ENABLED = 1  and (ROOT_COLUMN_ID = :columnId or PARENT_COLUMN_ID = :columnId or column_Id = :columnId)",
            nativeQuery = true
    )
    List<String> findColumnIdsByRootId(@Param("columnId")String columnId);

    @Query(
            value = "select  COLUMN_ID from sys_new_column_model where ENABLED = 1  and (PARENT_COLUMN_ID = :columnId or column_Id = :columnId)",
            nativeQuery = true
    )
    List<String> findColumnIdsByParentId(@Param("columnId")String columnId);

    @Query(
            value = "select * from sys_new_column_model  where enabled = 1   order by column_level,column_order",
            nativeQuery = true
    )
    List<SysNewColumnModel> findAllColumn();


    @Query(
            value = "select * from sys_new_column_model  where ENABLED = 1  and COLUMN_ID = :programaCode  order by column_level,column_order",
            nativeQuery = true
    )
    SysNewColumnModel findOneByColumnId(@Param("programaCode")String programaCode);

    @Query(
            value = "select * from sys_new_column_model  where ENABLED = 1  and COLUMN_ID in :columnIdsByRootId  order by column_level,column_order",
            nativeQuery = true
    )
    List<SysNewColumnModel> findAllColumnByColumnIds(@Param("columnIdsByRootId") List<String> columnIdsByRootId);

    @Query(value = "select * from sys_new_column_model  where enabled = 1 and column_All_Name = :programaDisplayName", nativeQuery = true)
    SysNewColumnModel findOneByAllColumnName(@Param("programaDisplayName") String programaDisplayName);
}
