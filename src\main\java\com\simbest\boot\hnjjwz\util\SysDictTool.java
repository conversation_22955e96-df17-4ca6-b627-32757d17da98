package com.simbest.boot.hnjjwz.util;

import com.simbest.boot.sys.model.SysDictValue;
import com.simbest.boot.sys.service.ISysDictValueService;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Component;

import java.util.ArrayList;
import java.util.List;

@Component
public class SysDictTool {

    @Autowired
    private ISysDictValueService sysDictValueService;

    /**
     * 获取所有的数据字典值，用于查询时，不选择数据字典的类型而需要查出所有类型的情况
     * @param dictType
     * @return
     */
    public List getSysDictValueList(String dictType){
        List dictValueList = new ArrayList<>(  );
        //从数据字典中获取类型
        SysDictValue sysDictValue = new SysDictValue(  );
        sysDictValue.setDictType( dictType );
        List<SysDictValue> sysDictValueList = sysDictValueService.findDictValue( sysDictValue );
        for(SysDictValue sysDictValueNew:sysDictValueList){
            dictValueList.add( sysDictValueNew.getValue() );
        }
        return dictValueList;
    }

    /**
     * 由数据字典的值获取数据字典的name值
     * @param value
     * @return
     */
    public String firstSysDictName(String value){
        SysDictValue sysDictValue = new SysDictValue(  );
        sysDictValue.setValue( value );
        List<SysDictValue> sysDictValueList = sysDictValueService.findDictValue( sysDictValue );
        if(sysDictValueList!=null && !sysDictValueList.isEmpty()){
            return sysDictValueList.get( 0 ).getName();
        }
        return null;
    }
}
