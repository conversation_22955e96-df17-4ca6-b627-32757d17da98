/**
 * Copyright (c) Tiny Technologies, Inc. All rights reserved.
 * Licensed under the LGPL or a commercial license.
 * For LGPL see License.txt in the project root for license information.
 * For commercial licenses see https://www.tiny.cloud/
 *
 * Version: 5.6.2 (2020-12-08)
 */
!function(){"use strict";var y=function(){},b=function(n){return function(){return n}},d=function(n){return n};function w(r){for(var o=[],n=1;n<arguments.length;n++)o[n-1]=arguments[n];return function(){for(var n=[],e=0;e<arguments.length;e++)n[e]=arguments[e];var t=o.concat(n);return r.apply(null,t)}}var n,e,t,r,g=function(e){return function(n){return!e(n)}},f=b(!1),C=b(!0),o=function(){return u},u=(n=function(n){return n.isNone()},{fold:function(n,e){return n()},is:f,isSome:f,isNone:C,getOr:t=function(n){return n},getOrThunk:e=function(n){return n()},getOrDie:function(n){throw new Error(n||"error: getOrDie called on none.")},getOrNull:b(null),getOrUndefined:b(undefined),or:t,orThunk:e,map:o,each:y,bind:o,exists:f,forall:C,filter:o,equals:n,equals_:n,toArray:function(){return[]},toString:b("none()")}),i=function(t){var n=b(t),e=function(){return o},r=function(n){return n(t)},o={fold:function(n,e){return e(t)},is:function(n){return t===n},isSome:C,isNone:f,getOr:n,getOrThunk:n,getOrDie:n,getOrNull:n,getOrUndefined:n,or:e,orThunk:e,map:function(n){return i(n(t))},each:function(n){n(t)},bind:r,exists:r,forall:r,filter:function(n){return n(t)?o:u},toArray:function(){return[t]},toString:function(){return"some("+t+")"},equals:function(n){return n.is(t)},equals_:function(n,e){return n.fold(f,function(n){return e(t,n)})}};return o},x={some:i,none:o,from:function(n){return null===n||n===undefined?u:i(n)}},c=function(r){return function(n){return t=typeof(e=n),(null===e?"null":"object"==t&&(Array.prototype.isPrototypeOf(e)||e.constructor&&"Array"===e.constructor.name)?"array":"object"==t&&(String.prototype.isPrototypeOf(e)||e.constructor&&"String"===e.constructor.name)?"string":t)===r;var e,t}},a=function(e){return function(n){return typeof n===e}},p=c("string"),h=c("object"),l=c("array"),s=a("boolean"),m=(r=undefined,function(n){return r===n}),v=function(n){return!(null===(e=n)||e===undefined);var e},S=a("function"),T=a("number"),R=Array.prototype.slice,O=Array.prototype.indexOf,D=Array.prototype.push,A=function(n,e){return t=n,r=e,-1<O.call(t,r);var t,r},I=function(n,e){for(var t=0,r=n.length;t<r;t++){if(e(n[t],t))return!0}return!1},B=function(n,e){for(var t=[],r=0;r<n;r++)t.push(e(r));return t},E=function(n,e){for(var t=n.length,r=new Array(t),o=0;o<t;o++){var u=n[o];r[o]=e(u,o)}return r},P=function(n,e){for(var t=0,r=n.length;t<r;t++){e(n[t],t)}},k=function(n,e){for(var t=[],r=0,o=n.length;r<o;r++){var u=n[r];e(u,r)&&t.push(u)}return t},M=function(n,e,t){return function(n,e){for(var t=n.length-1;0<=t;t--){e(n[t],t)}}(n,function(n){t=e(t,n)}),t},N=function(n,e,t){return P(n,function(n){t=e(t,n)}),t},j=function(n,e){return function(n,e,t){for(var r=0,o=n.length;r<o;r++){var u=n[r];if(e(u,r))return x.some(u);if(t(u,r))break}return x.none()}(n,e,f)},_=function(n,e){for(var t=0,r=n.length;t<r;t++){if(e(n[t],t))return x.some(t)}return x.none()},z=function(n){for(var e=[],t=0,r=n.length;t<r;++t){if(!l(n[t]))throw new Error("Arr.flatten item "+t+" was not an array, input: "+n);D.apply(e,n[t])}return e},W=function(n,e){return z(E(n,e))},F=function(n,e){for(var t=0,r=n.length;t<r;++t){if(!0!==e(n[t],t))return!1}return!0},L=function(n){return[n]},H=function(n,e){return 0<=e&&e<n.length?x.some(n[e]):x.none()},q=function(n){return H(n,n.length-1)},V=function(n,e){for(var t=0;t<n.length;t++){var r=e(n[t],t);if(r.isSome())return r}return x.none()},U=function(){return(U=Object.assign||function(n){for(var e,t=1,r=arguments.length;t<r;t++)for(var o in e=arguments[t])Object.prototype.hasOwnProperty.call(e,o)&&(n[o]=e[o]);return n}).apply(this,arguments)};function $(){for(var n=0,e=0,t=arguments.length;e<t;e++)n+=arguments[e].length;for(var r=Array(n),o=0,e=0;e<t;e++)for(var u=arguments[e],i=0,c=u.length;i<c;i++,o++)r[o]=u[i];return r}var G,K=function(t){var r,o=!1;return function(){for(var n=[],e=0;e<arguments.length;e++)n[e]=arguments[e];return o||(o=!0,r=t.apply(null,n)),r}},X=function(n,e){var t=function(n,e){for(var t=0;t<n.length;t++){var r=n[t];if(r.test(e))return r}return undefined}(n,e);if(!t)return{major:0,minor:0};var r=function(n){return Number(e.replace(t,"$"+n))};return J(r(1),r(2))},Y=function(){return J(0,0)},J=function(n,e){return{major:n,minor:e}},Q={nu:J,detect:function(n,e){var t=String(e).toLowerCase();return 0===n.length?Y():X(n,t)},unknown:Y},Z=function(n,e){var t=String(e).toLowerCase();return j(n,function(n){return n.search(t)})},nn=function(n,t){return Z(n,t).map(function(n){var e=Q.detect(n.versionRegexes,t);return{current:n.name,version:e}})},en=function(n,t){return Z(n,t).map(function(n){var e=Q.detect(n.versionRegexes,t);return{current:n.name,version:e}})},tn=function(n,e,t){return""===e||n.length>=e.length&&n.substr(t,t+e.length)===e},rn=function(n,e){return-1!==n.indexOf(e)},on=function(n,e){return tn(n,e,0)},un=function(n,e){return tn(n,e,n.length-e.length)},cn=(G=/^\s+|\s+$/g,function(n){return n.replace(G,"")}),an=function(n){return 0<n.length},ln=/.*?version\/\ ?([0-9]+)\.([0-9]+).*/,fn=function(e){return function(n){return rn(n,e)}},sn=[{name:"Edge",versionRegexes:[/.*?edge\/ ?([0-9]+)\.([0-9]+)$/],search:function(n){return rn(n,"edge/")&&rn(n,"chrome")&&rn(n,"safari")&&rn(n,"applewebkit")}},{name:"Chrome",versionRegexes:[/.*?chrome\/([0-9]+)\.([0-9]+).*/,ln],search:function(n){return rn(n,"chrome")&&!rn(n,"chromeframe")}},{name:"IE",versionRegexes:[/.*?msie\ ?([0-9]+)\.([0-9]+).*/,/.*?rv:([0-9]+)\.([0-9]+).*/],search:function(n){return rn(n,"msie")||rn(n,"trident")}},{name:"Opera",versionRegexes:[ln,/.*?opera\/([0-9]+)\.([0-9]+).*/],search:fn("opera")},{name:"Firefox",versionRegexes:[/.*?firefox\/\ ?([0-9]+)\.([0-9]+).*/],search:fn("firefox")},{name:"Safari",versionRegexes:[ln,/.*?cpu os ([0-9]+)_([0-9]+).*/],search:function(n){return(rn(n,"safari")||rn(n,"mobile/"))&&rn(n,"applewebkit")}}],dn=[{name:"Windows",search:fn("win"),versionRegexes:[/.*?windows\ nt\ ?([0-9]+)\.([0-9]+).*/]},{name:"iOS",search:function(n){return rn(n,"iphone")||rn(n,"ipad")},versionRegexes:[/.*?version\/\ ?([0-9]+)\.([0-9]+).*/,/.*cpu os ([0-9]+)_([0-9]+).*/,/.*cpu iphone os ([0-9]+)_([0-9]+).*/]},{name:"Android",search:fn("android"),versionRegexes:[/.*?android\ ?([0-9]+)\.([0-9]+).*/]},{name:"OSX",search:fn("mac os x"),versionRegexes:[/.*?mac\ os\ x\ ?([0-9]+)_([0-9]+).*/]},{name:"Linux",search:fn("linux"),versionRegexes:[]},{name:"Solaris",search:fn("sunos"),versionRegexes:[]},{name:"FreeBSD",search:fn("freebsd"),versionRegexes:[]},{name:"ChromeOS",search:fn("cros"),versionRegexes:[/.*?chrome\/([0-9]+)\.([0-9]+).*/]}],mn={browsers:b(sn),oses:b(dn)},gn="Firefox",pn=function(n){var e=n.current,t=n.version,r=function(n){return function(){return e===n}};return{current:e,version:t,isEdge:r("Edge"),isChrome:r("Chrome"),isIE:r("IE"),isOpera:r("Opera"),isFirefox:r(gn),isSafari:r("Safari")}},hn={unknown:function(){return pn({current:undefined,version:Q.unknown()})},nu:pn,edge:b("Edge"),chrome:b("Chrome"),ie:b("IE"),opera:b("Opera"),firefox:b(gn),safari:b("Safari")},vn="Windows",bn="Android",wn="Solaris",yn="FreeBSD",Cn="ChromeOS",xn=function(n){var e=n.current,t=n.version,r=function(n){return function(){return e===n}};return{current:e,version:t,isWindows:r(vn),isiOS:r("iOS"),isAndroid:r(bn),isOSX:r("OSX"),isLinux:r("Linux"),isSolaris:r(wn),isFreeBSD:r(yn),isChromeOS:r(Cn)}},Sn={unknown:function(){return xn({current:undefined,version:Q.unknown()})},nu:xn,windows:b(vn),ios:b("iOS"),android:b(bn),linux:b("Linux"),osx:b("OSX"),solaris:b(wn),freebsd:b(yn),chromeos:b(Cn)},Tn=function(n,e){var t,r,o,u,i,c,a,l,f,s,d,m,g=mn.browsers(),p=mn.oses(),h=nn(g,n).fold(hn.unknown,hn.nu),v=en(p,n).fold(Sn.unknown,Sn.nu);return{browser:h,os:v,deviceType:(r=h,o=n,u=e,i=(t=v).isiOS()&&!0===/ipad/i.test(o),c=t.isiOS()&&!i,a=t.isiOS()||t.isAndroid(),l=a||u("(pointer:coarse)"),f=i||!c&&a&&u("(min-device-width:768px)"),s=c||a&&!f,d=r.isSafari()&&t.isiOS()&&!1===/safari/i.test(o),m=!s&&!f&&!d,{isiPad:b(i),isiPhone:b(c),isTablet:b(f),isPhone:b(s),isTouch:b(l),isAndroid:t.isAndroid,isiOS:t.isiOS,isWebView:b(d),isDesktop:b(m)})}},Rn=function(n){return window.matchMedia(n).matches},On=K(function(){return Tn(navigator.userAgent,Rn)}),Dn=function(){return On()},An=function(n){if(null===n||n===undefined)throw new Error("Node cannot be null or undefined");return{dom:n}},In={fromHtml:function(n,e){var t=(e||document).createElement("div");if(t.innerHTML=n,!t.hasChildNodes()||1<t.childNodes.length)throw console.error("HTML does not have a single root node",n),new Error("HTML must have a single root node");return An(t.childNodes[0])},fromTag:function(n,e){var t=(e||document).createElement(n);return An(t)},fromText:function(n,e){var t=(e||document).createTextNode(n);return An(t)},fromDom:An,fromPoint:function(n,e,t){return x.from(n.dom.elementFromPoint(e,t)).map(An)}},Bn=function(n,e){var t=n.dom;if(1!==t.nodeType)return!1;var r=t;if(r.matches!==undefined)return r.matches(e);if(r.msMatchesSelector!==undefined)return r.msMatchesSelector(e);if(r.webkitMatchesSelector!==undefined)return r.webkitMatchesSelector(e);if(r.mozMatchesSelector!==undefined)return r.mozMatchesSelector(e);throw new Error("Browser lacks native selectors")},En=function(n){return 1!==n.nodeType&&9!==n.nodeType&&11!==n.nodeType||0===n.childElementCount},Pn=function(n,e){return n.dom===e.dom},kn=function(n,e){return t=n.dom,r=e.dom,o=t,u=r,i=Node.DOCUMENT_POSITION_CONTAINED_BY,0!=(o.compareDocumentPosition(u)&i);var t,r,o,u,i},Mn=function(n,e){return Dn().browser.isIE()?kn(n,e):(t=e,r=n.dom,o=t.dom,r!==o&&r.contains(o));var t,r,o},Nn=Bn,jn=Object.keys,_n=Object.hasOwnProperty,zn=function(n,e){for(var t=jn(n),r=0,o=t.length;r<o;r++){var u=t[r];e(n[u],u)}},Wn=function(n,t){return Fn(n,function(n,e){return{k:e,v:t(n,e)}})},Fn=function(n,r){var o={};return zn(n,function(n,e){var t=r(n,e);o[t.k]=t.v}),o},Ln=function(n,e){var t,r,o,u,i={};return t=e,u=i,r=function(n,e){u[e]=n},o=y,zn(n,function(n,e){(t(n,e)?r:o)(n,e)}),i},Hn=function(n){return t=function(n){return n},r=[],zn(n,function(n,e){r.push(t(n,e))}),r;var t,r},qn=function(n,e){return Vn(n,e)?x.from(n[e]):x.none()},Vn=function(n,e){return _n.call(n,e)},Un=["tfoot","thead","tbody","colgroup"],$n=function(n,e,t){return{element:n,rowspan:e,colspan:t}},Gn=function(n,e,t){return{element:n,cells:e,section:t}},Kn=function(n,e){return{element:n,isNew:e}},Xn=function(n,e){return{cells:n,section:e}},Yn=("undefined"!=typeof window||Function("return this;")(),function(n){return n.dom.nodeName.toLowerCase()}),Jn=function(n){return n.dom.nodeType},Qn=function(e){return function(n){return Jn(n)===e}},Zn=function(n){return 8===Jn(n)||"#comment"===Yn(n)},ne=Qn(1),ee=Qn(3),te=Qn(9),re=Qn(11),oe=function(n){return In.fromDom(n.dom.ownerDocument)},ue=function(n){return te(n)?n:oe(n)},ie=function(n){return x.from(n.dom.parentNode).map(In.fromDom)},ce=function(n,e){for(var t=S(e)?e:f,r=n.dom,o=[];null!==r.parentNode&&r.parentNode!==undefined;){var u=r.parentNode,i=In.fromDom(u);if(o.push(i),!0===t(i))break;r=u}return o},ae=function(n){return x.from(n.dom.previousSibling).map(In.fromDom)},le=function(n){return x.from(n.dom.nextSibling).map(In.fromDom)},fe=function(n){return E(n.dom.childNodes,In.fromDom)},se=function(n,e){var t=n.dom.childNodes;return x.from(t[e]).map(In.fromDom)},de=S(Element.prototype.attachShadow)&&S(Node.prototype.getRootNode),me=b(de),ge=de?function(n){return In.fromDom(n.dom.getRootNode())}:ue,pe=function(n){var e=ge(n);return re(e)?x.some(e):x.none()},he=function(n){return In.fromDom(n.dom.host)},ve=function(n){if(me()&&v(n.target)){var e=In.fromDom(n.target);if(ne(e)&&be(e)&&n.composed&&n.composedPath){var t=n.composedPath();if(t)return H(t,0)}}return x.from(n.target)},be=function(n){return v(n.dom.shadowRoot)},we=function(n){var e=ee(n)?n.dom.parentNode:n.dom;if(e===undefined||null===e||null===e.ownerDocument)return!1;var t,r,o=e.ownerDocument;return pe(In.fromDom(e)).fold(function(){return o.body.contains(e)},(t=we,r=he,function(n){return t(r(n))}))},ye=function(n){var e=n.dom.body;if(null===e||e===undefined)throw new Error("Body is not available yet");return In.fromDom(e)},Ce=function(n,e){var t=[];return P(fe(n),function(n){e(n)&&(t=t.concat([n])),t=t.concat(Ce(n,e))}),t},xe=function(n,e,t){return r=function(n){return Bn(n,e)},k(ce(n,t),r);var r},Se=function(n,e){return t=function(n){return Bn(n,e)},k(fe(n),t);var t},Te=function(n,e){return t=e,o=(r=n)===undefined?document:r.dom,En(o)?[]:E(o.querySelectorAll(t),In.fromDom);var t,r,o};function Re(n,e,t,r,o){return n(t,r)?x.some(t):S(o)&&o(t)?x.none():e(t,r,o)}var Oe=function(n,e,t){for(var r=n.dom,o=S(t)?t:f;r.parentNode;){r=r.parentNode;var u=In.fromDom(r);if(e(u))return x.some(u);if(o(u))break}return x.none()},De=function(n,e,t){return Oe(n,function(n){return Bn(n,e)},t)},Ae=function(n,e){return t=function(n){return Bn(n,e)},j(n.dom.childNodes,function(n){return t(In.fromDom(n))}).map(In.fromDom);var t},Ie=function(n,e){return t=e,o=(r=n)===undefined?document:r.dom,En(o)?x.none():x.from(o.querySelector(t)).map(In.fromDom);var t,r,o},Be=function(n,e,t){return Re(Bn,De,n,e,t)},Ee=function(n,e,t){if(!(p(t)||s(t)||T(t)))throw console.error("Invalid call to Attribute.set. Key ",e,":: Value ",t,":: Element ",n),new Error("Attribute value was not simple");n.setAttribute(e,t+"")},Pe=function(n,e,t){Ee(n.dom,e,t)},ke=function(n,e){var t=n.dom;zn(e,function(n,e){Ee(t,e,n)})},Me=function(n,e){var t=n.dom.getAttribute(e);return null===t?undefined:t},Ne=function(n,e){return x.from(Me(n,e))},je=function(n,e){n.dom.removeAttribute(e)},_e=function(n){return N(n.dom.attributes,function(n,e){return n[e.name]=e.value,n},{})},ze=function(n){return n.style!==undefined&&S(n.style.getPropertyValue)},We=function(n,e,t){if(!p(t))throw console.error("Invalid call to CSS.set. Property ",e,":: Value ",t,":: Element ",n),new Error("CSS value must be a string: "+t);ze(n)&&n.style.setProperty(e,t)},Fe=function(n,e,t){var r=n.dom;We(r,e,t)},Le=function(n,e){var t=n.dom;zn(e,function(n,e){We(t,e,n)})},He=function(n,e){var t=n.dom,r=window.getComputedStyle(t).getPropertyValue(e);return""!==r||we(n)?r:qe(t,e)},qe=function(n,e){return ze(n)?n.style.getPropertyValue(e):""},Ve=function(n,e){var t=n.dom,r=qe(t,e);return x.from(r).filter(function(n){return 0<n.length})},Ue=function(n,e){var t,r,o=n.dom;r=e,ze(t=o)&&t.style.removeProperty(r),Ne(n,"style").map(cn).is("")&&je(n,"style")},$e=function(n,e,t){return void 0===t&&(t=0),Ne(n,e).map(function(n){return parseInt(n,10)}).getOr(t)},Ge=function(n,e){return $e(n,e,1)},Ke=function(n){return 1<Ge(n,"colspan")},Xe=function(n){return 1<Ge(n,"rowspan")},Ye=function(n,e){return parseInt(He(n,e),10)},Je=b(10),Qe=b(10),Ze=function(n,e){return nt(n,e,C)},nt=function(n,e,t){return W(fe(n),function(n){return Bn(n,e)?t(n)?[n]:[]:nt(n,e,t)})},et=function(n,e){return function(n,e,t){if(void 0===t&&(t=f),t(e))return x.none();if(A(n,Yn(e)))return x.some(e);return De(e,n.join(","),function(n){return Bn(n,"table")||t(n)})}(["td","th"],n,e)},tt=function(n){return Ze(n,"th,td")},rt=function(n){return Bn(n,"colgroup")?Se(n,"col"):W(it(n),function(n){return Se(n,"col")})},ot=function(n,e){return Be(n,"table",e)},ut=function(n){return Ze(n,"tr")},it=function(n){return ot(n).fold(b([]),function(n){return Se(n,"colgroup")})},ct=function(n,t){return E(n,function(n){if("colgroup"===Yn(n)){var e=E(rt(n),function(n){var e=$e(n,"span",1);return $n(n,1,e)});return Gn(n,e,"colgroup")}e=E(tt(n),function(n){var e=$e(n,"rowspan",1),t=$e(n,"colspan",1);return $n(n,e,t)});return Gn(n,e,t(n))})},at=function(n){return ie(n).map(function(n){var e=Yn(n);return A(Un,e)?e:"tbody"}).getOr("tbody")},lt=function(n){var e=ut(n),t=$(it(n),e);return ct(t,at)},ft=function(n,e){return n+","+e},st=function(n,e){var t=W(n.all,function(n){return n.cells});return k(t,e)},dt=function(n){var a={},e=[],t={},r=0,l=0,f=0;return P(n,function(n){var c,o,u;"colgroup"===n.section?(o={},u=0,P(n.cells,function(t){var r=t.colspan;B(r,function(n){var e=u+n;o[e]={element:t.element,colspan:r,column:e}}),u+=r}),t=o):(c=[],P(n.cells,function(n){for(var e=0;a[ft(f,e)]!==undefined;)e++;for(var t={element:n.element,rowspan:n.rowspan,colspan:n.colspan,row:f,column:e},r=0;r<n.colspan;r++)for(var o=0;o<n.rowspan;o++){var u=e+r,i=ft(f+o,u);a[i]=t,l=Math.max(l,u+1)}c.push(t)}),r++,e.push(Gn(n.element,c,n.section)),f++)}),{grid:{rows:r,columns:l},access:a,all:e,columns:t}},mt={fromTable:function(n){var e=lt(n);return dt(e)},generate:dt,getAt:function(n,e,t){var r=n.access[ft(e,t)];return r!==undefined?x.some(r):x.none()},findItem:function(n,e,t){var r=st(n,function(n){return t(e,n.element)});return 0<r.length?x.some(r[0]):x.none()},filterItems:st,justCells:function(n){return W(n.all,function(n){return n.cells})},justColumns:function(n){return Hn(n.columns)},hasColumns:function(n){return 0<jn(n.columns).length},getColumnAt:function(n,e){return x.from(n.columns[e])}},gt=function(n,e){var t=e.column,r=e.column+e.colspan-1,o=e.row,u=e.row+e.rowspan-1;return t<=n.finishCol&&r>=n.startCol&&o<=n.finishRow&&u>=n.startRow},pt=function(n,e){return e.column>=n.startCol&&e.column+e.colspan-1<=n.finishCol&&e.row>=n.startRow&&e.row+e.rowspan-1<=n.finishRow},ht=function(n,e,t){var r=mt.findItem(n,e,Pn),o=mt.findItem(n,t,Pn);return r.bind(function(r){return o.map(function(n){return e=r,t=n,{startRow:Math.min(e.row,t.row),startCol:Math.min(e.column,t.column),finishRow:Math.max(e.row+e.rowspan-1,t.row+t.rowspan-1),finishCol:Math.max(e.column+e.colspan-1,t.column+t.colspan-1)};var e,t})})},vt=function(e,n,t){return ht(e,n,t).bind(function(n){return function(n,e){for(var t=!0,r=w(pt,e),o=e.startRow;o<=e.finishRow;o++)for(var u=e.startCol;u<=e.finishCol;u++)t=t&&mt.getAt(n,o,u).exists(r);return t?x.some(e):x.none()}(e,n)})},bt=function(t,n,e){return ht(t,n,e).map(function(n){var e=mt.filterItems(t,w(gt,n));return E(e,function(n){return n.element})})},wt=function(n,e){return mt.findItem(n,e,function(n,e){return Mn(e,n)}).map(function(n){return n.element})},yt=function(i,c,a){return ot(i).bind(function(n){var r,e,o,u,t=xt(n);return r=t,e=i,o=c,u=a,mt.findItem(r,e,Pn).bind(function(n){var e=0<o?n.row+n.rowspan-1:n.row,t=0<u?n.column+n.colspan-1:n.column;return mt.getAt(r,e+o,t+u).map(function(n){return n.element})})})},Ct=function(n,e,t,r,o){var u=xt(n),i=Pn(n,t)?x.some(e):wt(u,e),c=Pn(n,o)?x.some(r):wt(u,r);return i.bind(function(e){return c.bind(function(n){return bt(u,e,n)})})},xt=mt.fromTable,St=function(e,t){ie(e).each(function(n){n.dom.insertBefore(t.dom,e.dom)})},Tt=function(n,e){le(n).fold(function(){ie(n).each(function(n){Ot(n,e)})},function(n){St(n,e)})},Rt=function(e,t){se(e,0).fold(function(){Ot(e,t)},function(n){e.dom.insertBefore(t.dom,n.dom)})},Ot=function(n,e){n.dom.appendChild(e.dom)},Dt=function(n,e){St(n,e),Ot(e,n)},At=function(r,o){P(o,function(n,e){var t=0===e?r:o[e-1];Tt(t,n)})},It=function(e,n){P(n,function(n){Ot(e,n)})},Bt=function(n){n.dom.textContent="",P(fe(n),function(n){Et(n)})},Et=function(n){var e=n.dom;null!==e.parentNode&&e.parentNode.removeChild(e)},Pt=function(n){var e,t=fe(n);0<t.length&&(e=n,P(t,function(n){St(e,n)})),Et(n)};var kt,Mt,Nt,jt=(kt=ee,Mt="text",{get:function(n){if(!kt(n))throw new Error("Can only get "+Mt+" value of a "+Mt+" node");return Nt(n).getOr("")},getOption:Nt=function(n){return kt(n)?x.from(n.dom.nodeValue):x.none()},set:function(n,e){if(!kt(n))throw new Error("Can only set raw "+Mt+" value of a "+Mt+" node");n.dom.nodeValue=e}}),_t=function(n){return jt.get(n)},zt=function(n){return jt.getOption(n)},Wt=function(n,e){return jt.set(n,e)},Ft=["body","p","div","article","aside","figcaption","figure","footer","header","nav","section","ol","ul","li","table","thead","tbody","tfoot","caption","tr","td","th","h1","h2","h3","h4","h5","h6","blockquote","pre","address"];function Lt(){return{up:b({selector:De,closest:Be,predicate:Oe,all:ce}),down:b({selector:Te,predicate:Ce}),styles:b({get:He,getRaw:Ve,set:Fe,remove:Ue}),attrs:b({get:Me,set:Pe,remove:je,copyTo:function(n,e){var t=_e(n);ke(e,t)}}),insert:b({before:St,after:Tt,afterAll:At,append:Ot,appendAll:It,prepend:Rt,wrap:Dt}),remove:b({unwrap:Pt,remove:Et}),create:b({nu:In.fromTag,clone:function(n){return In.fromDom(n.dom.cloneNode(!1))},text:In.fromText}),query:b({comparePosition:function(n,e){return n.dom.compareDocumentPosition(e.dom)},prevSibling:ae,nextSibling:le}),property:b({children:fe,name:Yn,parent:ie,document:function(n){return ue(n).dom},isText:ee,isComment:Zn,isElement:ne,getText:_t,setText:Wt,isBoundary:function(n){return!!ne(n)&&("body"===Yn(n)||A(Ft,Yn(n)))},isEmptyTag:function(n){return!!ne(n)&&A(["br","img","hr","input"],Yn(n))},isNonEditable:function(n){return ne(n)&&"false"===Me(n,"contenteditable")}}),eq:Pn,is:Nn}}var Ht=function(r,o,n,e){var t=o(r,n);return M(e,function(n,e){var t=o(r,e);return qt(r,n,t)},t)},qt=function(e,n,t){return n.bind(function(n){return t.filter(w(e.eq,n))})},Vt=function(n,e,t){return 0<t.length?Ht(n,e,(r=t)[0],r.slice(1)):x.none();var r},Ut=function(t,n,e,r){void 0===r&&(r=f);var o=[n].concat(t.up().all(n)),u=[e].concat(t.up().all(e)),i=function(e){return _(e,r).fold(function(){return e},function(n){return e.slice(0,n+1)})},c=i(o),a=i(u),l=j(c,function(n){return I(a,(e=n,w(t.eq,e)));var e});return{firstpath:c,secondpath:a,shared:l}},$t=Lt(),Gt=function(t,n){return Vt($t,function(n,e){return t(e)},n)},Kt=function(n){return De(n,"table")},Xt=function(l,f,s){var d=function(e){return function(n){return s!==undefined&&s(n)||Pn(n,e)}};return Pn(l,f)?x.some({boxes:x.some([l]),start:l,finish:f}):Kt(l).bind(function(a){return Kt(f).bind(function(u){if(Pn(a,u))return x.some({boxes:(o=l,i=f,c=xt(a),bt(c,o,i)),start:l,finish:f});if(Mn(a,u)){var n=0<(e=xe(f,"td,th",d(a))).length?e[e.length-1]:f;return x.some({boxes:Ct(a,l,a,f,u),start:l,finish:n})}if(Mn(u,a)){var e,t=0<(e=xe(l,"td,th",d(u))).length?e[e.length-1]:l;return x.some({boxes:Ct(u,l,a,f,u),start:l,finish:t})}return Ut($t,l,f,r).shared.bind(function(n){return Be(n,"table",s).bind(function(n){var e=xe(f,"td,th",d(n)),t=0<e.length?e[e.length-1]:f,r=xe(l,"td,th",d(n)),o=0<r.length?r[r.length-1]:l;return x.some({boxes:Ct(n,l,a,f,u),start:o,finish:t})})});var r,o,i,c})})},Yt=function(n,e){var t=Te(n,e);return 0<t.length?x.some(t):x.none()},Jt=function(n,e,r){return Ie(n,e).bind(function(t){return Ie(n,r).bind(function(e){return Gt(Kt,[t,e]).map(function(n){return{first:t,last:e,table:n}})})})},Qt=function(n,e,t,r,o){return u=o,j(n,function(n){return Bn(n,u)}).bind(function(n){return yt(n,e,t).bind(function(n){return t=r,De(e=n,"table").bind(function(n){return Ie(n,t).bind(function(n){return Xt(n,e).bind(function(e){return e.boxes.map(function(n){return{boxes:n,start:e.start,finish:e.finish}})})})});var e,t})});var u},Zt=Yt,nr=function(o,n,e){return Jt(o,n,e).bind(function(i){var n=function(n){return Pn(o,n)},e="thead,tfoot,tbody,table",t=De(i.first,e,n),r=De(i.last,e,n);return t.bind(function(u){return r.bind(function(n){return Pn(u,n)?(e=i.table,t=i.first,r=i.last,o=xt(e),vt(o,t,r)):x.none();var e,t,r,o})})})},er=function(i){if(!l(i))throw new Error("cases must be an array");if(0===i.length)throw new Error("there must be at least one case");var c=[],t={};return P(i,function(n,r){var e=jn(n);if(1!==e.length)throw new Error("one and only one name per case");var o=e[0],u=n[o];if(t[o]!==undefined)throw new Error("duplicate key detected:"+o);if("cata"===o)throw new Error("cannot have a case named cata (sorry)");if(!l(u))throw new Error("case arguments must be an array");c.push(o),t[o]=function(){var n=arguments.length;if(n!==u.length)throw new Error("Wrong number of arguments to case "+o+". Expected "+u.length+" ("+u+"), got "+n);for(var t=new Array(n),e=0;e<t.length;e++)t[e]=arguments[e];return{fold:function(){if(arguments.length!==i.length)throw new Error("Wrong number of arguments to fold. Expected "+i.length+", got "+arguments.length);return arguments[r].apply(null,t)},match:function(n){var e=jn(n);if(c.length!==e.length)throw new Error("Wrong number of arguments to match. Expected: "+c.join(",")+"\nActual: "+e.join(","));if(!F(c,function(n){return A(e,n)}))throw new Error("Not all branches were specified when using match. Specified: "+e.join(", ")+"\nRequired: "+c.join(", "));return n[o].apply(null,t)},log:function(n){console.log(n,{constructors:c,constructor:o,params:t})}}}}),t},tr=er([{none:[]},{multiple:["elements"]},{single:["element"]}]),rr=function(n,e,t,r){return n.fold(e,t,r)},or=tr.none,ur=tr.multiple,ir=tr.single,cr=function(n,e,t){return{get:function(){return Zt(n(),t).fold(function(){return e().map(ir).getOrThunk(or)},function(n){return ur(n)})}}},ar=tinymce.util.Tools.resolve("tinymce.PluginManager"),lr=function(n,e,t,r){for(var o,u,i,c=e.grid.columns,a=e.grid.rows,l=0;l<a;l++)for(var f=!1,s=0;s<c;s++){l<t.minRow||l>t.maxRow||s<t.minCol||s>t.maxCol||(mt.getAt(e,l,s).filter(r).isNone()?(o=f,0,u=n[l].element,i=In.fromTag("td"),Ot(i,In.fromTag("br")),(o?Ot:Rt)(u,i)):f=!0)}},fr=function(n,e){var t,u,r,i,c,a,l,o,f,s,d=function(n){return Bn(n.element,e)},m=lt(n),g=mt.generate(m),p=(u=d,r=(t=g).grid.columns,i=t.grid.rows,c=r,l=a=0,zn(t.access,function(n){var e,t,r,o;u(n)&&(t=(e=n.row)+n.rowspan-1,o=(r=n.column)+n.colspan-1,e<i?i=e:a<t&&(a=t),r<c?c=r:l<o&&(l=o))}),{minRow:i,minCol:c,maxRow:a,maxCol:l}),h="th:not("+e+"),td:not("+e+")",v=nt(n,"th,td",function(n){return Bn(n,h)});return P(v,Et),lr(m,g,p,d),f=p,s=k(Ze(o=n,"tr"),function(n){return 0===n.dom.childElementCount}),P(s,Et),f.minCol!==f.maxCol&&f.minRow!==f.maxRow||P(Ze(o,"th,td"),function(n){je(n,"rowspan"),je(n,"colspan")}),je(o,"width"),je(o,"height"),Ue(o,"width"),Ue(o,"height"),n},sr=function(n){return"img"===Yn(n)?1:zt(n).fold(function(){return fe(n).length},function(n){return n.length})},dr=["img","br"],mr=function(n){return zt(n).filter(function(n){return 0!==n.trim().length||-1<n.indexOf("\xa0")}).isSome()||A(dr,Yn(n))},gr=function(n){return o=mr,(u=function(n){for(var e=0;e<n.childNodes.length;e++){var t=In.fromDom(n.childNodes[e]);if(o(t))return x.some(t);var r=u(n.childNodes[e]);if(r.isSome())return r}return x.none()})(n.dom);var o,u},pr=function(n){return hr(n,mr)},hr=function(n,u){var i=function(n){for(var e=fe(n),t=e.length-1;0<=t;t--){var r=e[t];if(u(r))return x.some(r);var o=i(r);if(o.isSome())return o}return x.none()};return i(n)},vr=function(n,e){return In.fromDom(n.dom.cloneNode(e))},br=function(n){return vr(n,!1)},wr=function(n){return vr(n,!0)},yr=function(n,e){var t,r,o,u,i=(t=n,r=e,o=In.fromTag(r),u=_e(t),ke(o,u),o),c=fe(wr(n));return It(i,c),i},Cr={scope:["row","col"]},xr=function(){var n=In.fromTag("td");return Ot(n,In.fromTag("br")),n},Sr=function(){return In.fromTag("col")},Tr=function(){return In.fromTag("colgroup")},Rr=function(n,e,t){var r=yr(n,e);return zn(t,function(n,e){null===n?je(r,e):Pe(r,e,n)}),r},Or=function(n){return n},Dr=function(n){return function(){return In.fromTag("tr",n.dom)}},Ar=function(f,n,s){var d=function(n,e){var t,r,o,u;t=n.element,r=e,o=t.dom,u=r.dom,ze(o)&&ze(u)&&(u.style.cssText=o.style.cssText),Ue(e,"height"),1!==n.colspan&&Ue(n.element,"width")};return{col:function(n){var e=oe(n.element),t=In.fromTag(Yn(n.element),e.dom);return d(n,t),f(n.element,t),t},colgroup:Tr,row:Dr(n),cell:function(n){var r,o,u,i,c,e=oe(n.element),t=In.fromTag(Yn(n.element),e.dom),a=s.getOr(["strong","em","b","i","span","font","h1","h2","h3","h4","h5","h6","p","div"]),l=0<a.length?(r=n.element,o=t,u=a,gr(r).map(function(n){var e=u.join(","),t=xe(n,e,function(n){return Pn(n,r)});return M(t,function(n,e){var t=br(e);return je(t,"contenteditable"),Ot(n,t),t},o)}).getOr(o)):t;return Ot(l,In.fromTag("br")),d(n,t),i=n.element,c=t,zn(Cr,function(e,t){return Ne(i,t).filter(function(n){return A(e,n)}).each(function(n){return Pe(c,t,n)})}),f(n.element,t),t},replace:Rr,gap:xr}},Ir=function(n){return{col:Sr,colgroup:Tr,row:Dr(n),cell:xr,replace:Or,gap:xr}},Br=function(n){return E(n,In.fromDom)},Er=function(n){return rr(n.get(),b([]),d,L)},Pr="data-mce-selected",kr="data-mce-first-selected",Mr="data-mce-last-selected",Nr={selected:Pr,selectedSelector:"td[data-mce-selected],th[data-mce-selected]",firstSelected:kr,firstSelectedSelector:"td[data-mce-first-selected],th[data-mce-first-selected]",lastSelected:Mr,lastSelectedSelector:"td[data-mce-last-selected],th[data-mce-last-selected]"},jr=function(n){return{element:n,mergable:x.none(),unmergable:x.none(),selection:[n]}},_r=function(n,e,t){return{element:t,mergable:(u=e,i=Nr,rr(n.get(),x.none,function(e){return e.length<=1?x.none():nr(u,i.firstSelectedSelector,i.lastSelectedSelector).map(function(n){return{bounds:n,cells:e}})},x.none)),unmergable:(r=function(n,e){return Ne(n,e).exists(function(n){return 1<parseInt(n,10)})},0<(o=Er(n)).length&&F(o,function(n){return r(n,"rowspan")||r(n,"colspan")})?x.some(o):x.none()),selection:Er(n)};var r,o,u,i},zr=function(s,n,d,m){s.on("BeforeGetContent",function(t){!0===t.selection&&rr(n.get(),y,function(n){t.preventDefault(),ot(n[0]).map(wr).map(function(n){return[fr(n,"[data-mce-selected]")]}).each(function(n){var e;t.content="text"===t.format?E(n,function(n){return n.dom.innerText}).join(""):(e=s,E(n,function(n){return e.selection.serializer.serialize(n.dom,{})}).join(""))})},y)}),s.on("BeforeSetContent",function(f){!0===f.selection&&!0===f.paste&&x.from(s.dom.getParent(s.selection.getStart(),"th,td")).each(function(n){var l=In.fromDom(n);ot(l).each(function(e){var n,t,r,o,u,i,c,a=k((n=f.content,(r=(t||document).createElement("div")).innerHTML=n,fe(In.fromDom(r))),function(n){return"meta"!==Yn(n)});1===a.length&&(c=a[0],"table"===Yn(c))&&(f.preventDefault(),o=In.fromDom(s.getDoc()),u=Ir(o),i={element:l,clipboard:a[0],generators:u},d.pasteCells(e,i).each(function(n){s.selection.setRng(n),s.focus(),m.clear(e)}))})})})},Wr=er([{none:[]},{only:["index"]},{left:["index","next"]},{middle:["prev","index","next"]},{right:["prev","index"]}]),Fr=U({},Wr),Lr=function(n,e,r,o,u){var t,i,c=n.slice(0),a=(i=e,0===(t=n).length?Fr.none():1===t.length?Fr.only(0):0===i?Fr.left(0,1):i===t.length-1?Fr.right(i-1,i):0<i&&i<t.length-1?Fr.middle(i-1,i,i+1):Fr.none()),l=b(E(c,b(0)));return a.fold(l,function(n){return o.singleColumnWidth(c[n],r)},function(n,e){return u.calcLeftEdgeDeltas(c,n,e,r,o.minCellWidth(),o.isRelative)},function(n,e,t){return u.calcMiddleDeltas(c,n,e,t,r,o.minCellWidth(),o.isRelative)},function(n,e){return u.calcRightEdgeDeltas(c,n,e,r,o.minCellWidth(),o.isRelative)})},Hr=function(n){for(var e=[],t=function(n){e.push(n)},r=0;r<n.length;r++)n[r].each(t);return e};function qr(r,o){var n=function(n){var e=o(n);if(e<=0||null===e){var t=He(n,r);return parseFloat(t)||0}return e},u=function(o,n){return N(n,function(n,e){var t=He(o,e),r=t===undefined?0:parseInt(t,10);return isNaN(r)?n:n+r},0)};return{set:function(n,e){if(!T(e)&&!e.match(/^[0-9]+$/))throw new Error(r+".set accepts only positive integer values. Value was "+e);var t=n.dom;ze(t)&&(t.style[r]=e+"px")},get:n,getOuter:n,aggregate:u,max:function(n,e,t){var r=u(n,t);return r<e?e-r:0}}}var Vr,Ur,$r,Gr,Kr,Xr=qr("width",function(n){return n.dom.offsetWidth}),Yr=function(n){return Xr.get(n)},Jr=function(n){return Xr.getOuter(n)},Qr=function(t){var n=t.grid,e=B(n.columns,d),r=B(n.rows,d);return E(e,function(e){return Zr(function(){return W(r,function(n){return mt.getAt(t,n,e).filter(function(n){return n.column===e}).toArray()})},function(n){return 1===n.colspan},function(){return mt.getAt(t,0,e)})})},Zr=function(n,e,t){var r=n();return j(r,e).orThunk(function(){return x.from(r[0]).orThunk(t)}).map(function(n){return n.element})},no=function(t){var n=t.grid,e=B(n.rows,d),r=B(n.columns,d);return E(e,function(e){return Zr(function(){return W(r,function(n){return mt.getAt(t,e,n).filter(function(n){return n.row===e}).fold(b([]),function(n){return[n]})})},function(n){return 1===n.rowspan},function(){return mt.getAt(t,e,0)})})},eo=function(r,o){if(o<0||o>=r.length-1)return x.none();var n=r[o].fold(function(){var n,e,t=(n=r.slice(0,o),(e=R.call(n,0)).reverse(),e);return V(t,function(n,e){return n.map(function(n){return{value:n,delta:e+1}})})},function(n){return x.some({value:n,delta:0})}),e=r[o+1].fold(function(){var n=r.slice(o+1);return V(n,function(n,e){return n.map(function(n){return{value:n,delta:e+1}})})},function(n){return x.some({value:n,delta:1})});return n.bind(function(t){return e.map(function(n){var e=n.delta+t.delta;return Math.abs(n.value-t.value)/e})})},to=function(e,t){return function(n){return"rtl"===ro(n)?t:e}},ro=function(n){return"rtl"===He(n,"direction")?"rtl":"ltr"},oo=qr("height",function(n){var e=n.dom;return we(n)?e.getBoundingClientRect().height:e.offsetHeight}),uo=function(n){return oo.get(n)},io=function(n){return oo.getOuter(n)},co=function(t,r){return{left:t,top:r,translate:function(n,e){return co(t+n,r+e)}}},ao=co,lo=function(n,e){return n!==undefined?n:e!==undefined?e:0},fo=function(n){var e=n.dom.ownerDocument,t=e.body,r=e.defaultView,o=e.documentElement;if(t===n.dom)return ao(t.offsetLeft,t.offsetTop);var u=lo(null==r?void 0:r.pageYOffset,o.scrollTop),i=lo(null==r?void 0:r.pageXOffset,o.scrollLeft),c=lo(o.clientTop,t.clientTop),a=lo(o.clientLeft,t.clientLeft);return so(n).translate(i-a,u-c)},so=function(n){var e,t=n.dom,r=t.ownerDocument.body;return r===t?ao(r.offsetLeft,r.offsetTop):we(n)?(e=t.getBoundingClientRect(),ao(e.left,e.top)):ao(0,0)},mo=function(n,e){return{row:n,y:e}},go=function(n,e){return{col:n,x:e}},po=function(n){return fo(n).left+Jr(n)},ho=function(n){return fo(n).left},vo=function(n,e){return go(n,ho(e))},bo=function(n,e){return go(n,po(e))},wo=function(n){return fo(n).top},yo=function(n,e){return mo(n,wo(e))},Co=function(n,e){return mo(n,wo(e)+io(e))},xo=function(t,e,r){if(0===r.length)return[];var n=E(r.slice(1),function(n,e){return n.map(function(n){return t(e,n)})}),o=r[r.length-1].map(function(n){return e(r.length-1,n)});return n.concat([o])},So={delta:d,positions:function(n){return xo(yo,Co,n)},edge:wo},To=to({delta:d,edge:ho,positions:function(n){return xo(vo,bo,n)}},{delta:function(n){return-n},edge:po,positions:function(n){return xo(bo,vo,n)}}),Ro={delta:function(n,e){return To(e).delta(n,e)},positions:function(n,e){return To(e).positions(n,e)},edge:function(n){return To(n).edge(n)}},Oo={unsupportedLength:["em","ex","cap","ch","ic","rem","lh","rlh","vw","vh","vi","vb","vmin","vmax","cm","mm","Q","in","pc","pt","px"],fixed:["px","pt"],relative:["%"],empty:[""]},Do=(Ur="[eE][+-]?[0-9]+",Gr=["Infinity",(Vr="[0-9]+")+"\\."+($r=function(n){return"(?:"+n+")?"})(Vr)+$r(Ur),"\\."+Vr+$r(Ur),Vr+$r(Ur)].join("|"),new RegExp("^([+-]?(?:"+Gr+"))(.*)$")),Ao=function(n,o){return x.from(Do.exec(n)).bind(function(n){var e,t=Number(n[1]),r=n[2];return e=r,I(o,function(n){return I(Oo[n],function(n){return e===n})})?x.some({value:t,unit:r}):x.none()})},Io=function(){var n=Dn().browser;return n.isIE()||n.isEdge()},Bo=function(n,e,t){return r=He(n,e),o=t,u=parseFloat(r),isNaN(u)?o:u;var r,o,u},Eo=function(n){return Io()?(t=(e=n).dom.getBoundingClientRect().height,"border-box"===He(e,"box-sizing")?t:t-Bo(e,"padding-top",0)-Bo(e,"padding-bottom",0)-(Bo(e,"border-top-width",0)+Bo(e,"border-bottom-width",0))):Bo(n,"height",uo(n));var e,t},Po=function(n){return Io()?(t=(e=n).dom.getBoundingClientRect().width,"border-box"===He(e,"box-sizing")?t:t-Bo(e,"padding-left",0)-Bo(e,"padding-right",0)-(Bo(e,"border-left-width",0)+Bo(e,"border-right-width",0))):Bo(n,"width",Yr(n));var e,t},ko=/(\d+(\.\d+)?)%/,Mo=/(\d+(\.\d+)?)px|em/,No=function(n,e){var t,r=(t=n,x.from(t.dom.offsetParent).map(In.fromDom).getOr(ye(oe(n))));return e(n)/e(r)*100},jo=function(n,e){Fe(n,"width",e+"px")},_o=function(n,e){Fe(n,"width",e+"%")},zo=function(n,e){Fe(n,"height",e+"px")},Wo=function(n,e,t,r){var o,u,i,c,a,l=parseInt(n,10);return un(n,"%")&&"table"!==Yn(e)?(u=l,i=t,c=r,a=ot(o=e).map(function(n){var e=i(n);return Math.floor(u/100*e)}).getOr(u),c(o,a),a):l},Fo=function(n){var e,t=Ve(e=n,"height").getOrThunk(function(){return Eo(e)+"px"});return t?Wo(t,n,uo,zo):uo(n)},Lo=function(n){return Ve(n,"width").fold(function(){return x.from(Me(n,"width"))},function(n){return x.some(n)})},Ho=function(n,e){return n/e.pixelWidth()*100},qo=function(e,t){return Lo(e).fold(function(){var n=Yr(e);return Ho(n,t)},function(n){return function(n,e,t){var r=ko.exec(e);if(null!==r)return parseFloat(r[1]);var o=Po(n);return Ho(o,t)}(e,n,t)})},Vo=function(e,t){return Lo(e).fold(function(){return Po(e)},function(n){return function(n,e,t){var r=Mo.exec(e);if(null!==r)return parseInt(r[1],10);var o=ko.exec(e);if(null===o)return Po(n);var u=parseFloat(o[1]);return u/100*t.pixelWidth()}(e,n,t)})},Uo=function(n){return t="rowspan",Fo(e=n)/Ge(e,t);var e,t},$o=function(n,e,t){Fe(n,"width",e+t)},Go=function(n){return No(n,Yr)+"%"},Ko=b(ko),Xo=b(Mo),Yo=(Kr="col",function(n){return ne(n)&&Yn(n)===Kr}),Jo=function(n,e,t){return Ve(n,e).fold(function(){return t(n)+"px"},function(n){return n})},Qo=function(n,e){return Jo(n,"width",function(n){return Yo(n)?Yr(n):Vo(n,e)})},Zo=function(n){return Jo(n,"height",Uo)},nu=function(n,e,t,r,o,u){return n.filter(r).fold(function(){return u(eo(t,e))},function(n){return o(n)})},eu=function(n,e,i,c,a){var t,l=Qr(n),r=mt.hasColumns(n)?(t=n,E(mt.justColumns(t),function(n){return x.from(n.element)})):l,f=[x.some(Ro.edge(e))].concat(E(Ro.positions(l,e),function(n){return n.map(function(n){return n.x})})),s=g(Ke);return E(r,function(n,u){return nu(n,u,f,s,function(n){if(!Yo(r=n)||Ve(r,"width").isSome())return i(n,a);var e,t,r,o=(e=l[u],t=d,e!==undefined&&null!==e?t(e):x.none());return nu(o,u,f,s,function(n){return c(x.some(Yr(n)))},c)},c)})},tu=function(n){return n.map(function(n){return n+"px"}).getOr("")},ru=function(n,e,t){return eu(n,e,Vo,function(n){return n.getOrThunk(t.minCellWidth)},t)},ou=function(n,e,t,r,o){var u=no(n),i=[x.some(t.edge(e))].concat(E(t.positions(u,e),function(n){return n.map(function(n){return n.y})}));return E(u,function(n,e){return nu(n,e,i,g(Xe),r,o)})},uu=function(n,e,t){for(var r=0,o=n;o<e;o++)r+=t[o]!==undefined?t[o]:0;return r},iu=function(n,e){return mt.hasColumns(n)?(u=n,i=e,c=mt.justColumns(u),E(c,function(n,e){return{element:n.element,width:i[e],colspan:n.colspan}})):(t=n,r=e,o=mt.justCells(t),E(o,function(n){var e=uu(n.column,n.column+n.colspan,r);return{element:n.element,width:e,colspan:n.colspan}}));var t,r,o,u,i,c},cu=function(n,e,t){var r=iu(n,e);P(r,function(n){t.setElementWidth(n.element,n.width)})},au=function(n,e,t,r,o){var u=mt.fromTable(n),i=o.getCellDelta(e),c=o.getWidths(u,o),a=t===u.grid.columns-1,l=r.clampTableDelta(c,t,i,o.minCellWidth(),a),f=Lr(c,t,l,o,r),s=E(f,function(n,e){return n+c[e]});cu(u,s,o),r.resizeTable(o.adjustTableWidth,l,a)},lu=function(n,t,r,e){var o,u,i,c,a=mt.fromTable(n),l=ou(a,n,e,Uo,function(n){return n.getOrThunk(Qe)}),f=E(l,function(n,e){return r===e?Math.max(t+n,Qe()):n}),s=(o=a,u=f,i=mt.justCells(o),E(i,function(n){var e=uu(n.row,n.row+n.rowspan,u);return{element:n.element,height:e,rowspan:n.rowspan}})),d=(c=f,E(a.all,function(n,e){return{element:n.element,height:c[e]}}));P(d,function(n){zo(n.element,n.height)}),P(s,function(n){zo(n.element,n.height)});var m=M(f,function(n,e){return n+e},0);zo(n,m)},fu=function(n){return E(n,b(0))},su=function(n,e,t,r,o){return o(n.slice(0,e)).concat(r).concat(o(n.slice(t)))},du=function(i){return function(n,e,t,r){if(i(t)){var o=Math.max(r,n[e]-Math.abs(t)),u=Math.abs(o-n[e]);return 0<=t?u:-u}return t}},mu=du(function(n){return n<0}),gu=du(C),pu=function(){var f=function(n,t,e,r){var o=(100+e)/100,u=Math.max(r,(n[t]+e)/o);return E(n,function(n,e){return(e===t?u:n/o)-n})},c=function(n,e,t,r,o,u){return u?f(n,e,r,o):(a=t,l=mu(i=n,c=e,r,o),su(i,c,a+1,[l,0],fu));var i,c,a,l};return{resizeTable:function(n,e){return n(e)},clampTableDelta:mu,calcLeftEdgeDeltas:c,calcMiddleDeltas:function(n,e,t,r,o,u,i){return c(n,t,r,o,u,i)},calcRightEdgeDeltas:function(n,e,t,r,o,u){if(u)return f(n,t,r,o);var i=mu(n,t,r,o);return fu(n.slice(0,t)).concat([i])}}},hu=function(n,e){var t=x.from(n.dom.documentElement).map(In.fromDom).getOr(n);return{parent:b(t),view:b(n),origin:b(ao(0,0)),isResizable:e}},vu=function(n,e,t){return{parent:b(e),view:b(n),origin:b(ao(0,0)),isResizable:t}},bu=er([{invalid:["raw"]},{pixels:["value"]},{percent:["value"]}]),wu=function(n,e,t){var r=t.substring(0,t.length-n.length),o=parseFloat(r);return r===o.toString()?e(o):bu.invalid(t)},yu=U(U({},bu),{from:function(n){return un(n,"%")?wu("%",bu.percent,n):un(n,"px")?wu("px",bu.pixels,n):bu.invalid(n)}}),Cu=function(n,r,o){return n.fold(function(){return r},function(n){return t=(e=n)/o,E(r,function(n){return yu.from(n).fold(function(){return n},function(n){return n*t+"px"},function(n){return n/100*e+"px"})});var e,t},function(n){return e=o,E(r,function(n){return yu.from(n).fold(function(){return n},function(n){return n/e*100+"%"},function(n){return n+"%"})});var e})},xu=function(n,e,t){var r,o,u,i=yu.from(t),c=F(n,function(n){return"0px"===n})?(r=i,o=n.length,u=r.fold(function(){return b("")},function(n){return b(n/o+"px")},function(){return b(100/o+"%")}),B(o,u)):Cu(i,n,e);return Tu(c)},Su=function(n,e){return 0===n.length?e:M(n,function(n,e){return yu.from(e).fold(b(0),d,d)+n},0)},Tu=function(n){if(0===n.length)return n;var e,t,r=M(n,function(n,e){var t=yu.from(e).fold(function(){return{value:e,remainder:0}},function(n){return e=n,t="px",{value:(r=Math.floor(e))+t,remainder:e-r};var e,t,r},function(n){return{value:n+"%",remainder:0}});return{output:[t.value].concat(n.output),remainder:n.remainder+t.remainder}},{output:[],remainder:0}),o=r.output;return o.slice(0,o.length-1).concat([(e=o[o.length-1],t=Math.round(r.remainder),yu.from(e).fold(b(e),function(n){return n+t+"px"},function(n){return n+t+"%"}))])},Ru=yu.from,Ou=function(n){return Ru(n).fold(b("px"),b("px"),b("%"))},Du=function(l,n,e,f){var s=mt.fromTable(l),a=s.all,d=mt.justCells(s),m=mt.justColumns(s);n.each(function(n){var r,o,u,i,e=Ou(n),t=Yr(l),c=eu(s,l,Qo,tu,f),a=xu(c,t,n);mt.hasColumns(s)?(u=a,i=e,P(m,function(n,e){var t=Su([u[e]],Je());Fe(n.element,"width",t+i)})):(r=a,o=e,P(d,function(n){var e=r.slice(n.column,n.colspan+n.column),t=Su(e,Je());Fe(n.element,"width",t+o)})),Fe(l,"width",n)}),e.each(function(n){var r,e,o,t=Ou(n),u=uo(l),i=ou(s,l,So,Zo,tu),c=xu(i,u,n);r=c,e=a,o=t,P(d,function(n){var e=r.slice(n.row,n.rowspan+n.row),t=Su(e,Qe());Fe(n.element,"height",t+o)}),P(e,function(n,e){Fe(n.element,"height",r[e])}),Fe(l,"height",n)})},Au=function(n){return Lo(n).exists(function(n){return ko.test(n)})},Iu=function(n){return Lo(n).exists(function(n){return Mo.test(n)})},Bu=function(n){return Lo(n).isNone()},Eu=Go,Pu=function(n){return mt.fromTable(n).grid},ku=function(e){var o=[];return{bind:function(n){if(n===undefined)throw new Error("Event bind error: undefined handler");o.push(n)},unbind:function(e){o=k(o,function(n){return n!==e})},trigger:function(){for(var t=[],n=0;n<arguments.length;n++)t[n]=arguments[n];var r={};P(e,function(n,e){r[n]=t[e]}),P(o,function(n){n(r)})}}},Mu=function(n){return{registry:Wn(n,function(n){return{bind:n.bind,unbind:n.unbind}}),trigger:Wn(n,function(n){return n.trigger})}},Nu=function(n){return n.slice(0).sort()},ju=function(r,o,u){if(0===o.length)throw new Error("You must specify at least one required field.");var t;return function(e,n){if(!l(n))throw new Error("The "+e+" fields must be an array. Was: "+n+".");P(n,function(n){if(!p(n))throw new Error("The value "+n+" in the "+e+" fields was not a string.")})}("required",o),t=Nu(o),j(t,function(n,e){return e<t.length-1&&n===t[e+1]}).each(function(n){throw new Error("The field: "+n+" occurs more than once in the combined fields: ["+t.join(", ")+"].")}),function(e){var t=jn(e);F(o,function(n){return A(t,n)})||function(n,e){throw new Error("All required keys ("+Nu(n).join(", ")+") were not specified. Specified keys were: "+Nu(e).join(", ")+".")}(o,t),r(o,t);var n=k(o,function(n){return!u.validate(e[n],n)});return 0<n.length&&function(n,e){throw new Error("All values need to be of type: "+e+". Keys ("+Nu(n).join(", ")+") were not.")}(n,u.label),e}},_u=function(e,n){var t=k(n,function(n){return!A(e,n)});0<t.length&&function(n){throw new Error("Unsupported keys for object: "+Nu(n).join(", "))}(t)},zu=function(n){return ju(_u,n,{validate:S,label:"function"})},Wu=zu(["compare","extract","mutate","sink"]),Fu=zu(["element","start","stop","destroy"]),Lu=zu(["forceDrop","drop","move","delayDrop"]);function Hu(){var u=x.none(),i=Mu({move:ku(["info"])});return{onEvent:function(n,o){o.extract(n).each(function(n){var e,t,r;(e=o,t=n,r=u.map(function(n){return e.compare(n,t)}),u=x.some(t),r).each(function(n){i.trigger.move(n)})})},reset:function(){u=x.none()},events:i.registry}}function qu(){var n={onEvent:y,reset:y},e=Hu(),t=n;return{on:function(){t.reset(),t=e},off:function(){t.reset(),t=n},isOn:function(){return t===e},onEvent:function(n,e){t.onEvent(n,e)},events:e.events}}var Vu=function(e,t,n){var r,o,u,i=!1,c=Mu({start:ku([]),stop:ku([])}),a=qu(),l=function(){d.stop(),a.isOn()&&(a.off(),c.trigger.stop())},f=(r=l,o=200,u=null,{cancel:function(){null!==u&&(clearTimeout(u),u=null)},throttle:function(){for(var n=[],e=0;e<arguments.length;e++)n[e]=arguments[e];null!==u&&clearTimeout(u),u=setTimeout(function(){r.apply(null,n),u=null},o)}});a.events.move.bind(function(n){t.mutate(e,n.info)});var s=function(t){return function(){for(var n=[],e=0;e<arguments.length;e++)n[e]=arguments[e];i&&t.apply(null,n)}},d=t.sink(Lu({forceDrop:l,drop:s(l),move:s(function(n){f.cancel(),a.onEvent(n,t)}),delayDrop:s(f.throttle)}),n);return{element:d.element,go:function(n){d.start(n),a.on(),c.trigger.start()},on:function(){i=!0},off:function(){i=!1},destroy:function(){d.destroy()},events:c.registry}},Uu=function(n){var t,r,e=In.fromDom(ve(n).getOr(n.target)),o=function(){return n.stopPropagation()},u=function(){return n.preventDefault()},i=(t=u,r=o,function(){for(var n=[],e=0;e<arguments.length;e++)n[e]=arguments[e];return t(r.apply(null,n))});return{target:e,x:n.clientX,y:n.clientY,stop:o,prevent:u,kill:i,raw:n}},$u=function(n,e,t,r,o){var u,i,c=(u=t,i=r,function(n){u(n)&&i(Uu(n))});return n.dom.addEventListener(e,c,o),{unbind:w(Gu,n,e,c,o)}},Gu=function(n,e,t,r){n.dom.removeEventListener(e,t,r)},Ku=C,Xu=function(n,e,t){return $u(n,e,Ku,t,!1)},Yu=Uu,Ju=function(n,e){var t=Me(n,e);return t===undefined||""===t?[]:t.split(" ")},Qu=function(n){return n.dom.classList!==undefined},Zu=function(n,e){return o=e,u=Ju(t=n,r="class").concat([o]),Pe(t,r,u.join(" ")),!0;var t,r,o,u},ni=function(n,e){return o=e,0<(u=k(Ju(t=n,r="class"),function(n){return n!==o})).length?Pe(t,r,u.join(" ")):je(t,r),!1;var t,r,o,u},ei=function(n,e){Qu(n)?n.dom.classList.add(e):Zu(n,e)},ti=function(n){0===(Qu(n)?n.dom.classList:Ju(n,"class")).length&&je(n,"class")},ri=function(n,e){return Qu(n)&&n.dom.classList.contains(e)},oi=function(n){var e=n.replace(/\./g,"-");return{resolve:function(n){return e+"-"+n}}},ui=oi("ephox-dragster").resolve,ii=Wu({compare:function(n,e){return ao(e.left-n.left,e.top-n.top)},extract:function(n){return x.some(ao(n.x,n.y))},sink:function(n,e){var t=function(n){var e=U({layerClass:ui("blocker")},n),t=In.fromTag("div");Pe(t,"role","presentation"),Le(t,{position:"fixed",left:"0px",top:"0px",width:"100%",height:"100%"}),ei(t,ui("blocker")),ei(t,e.layerClass);return{element:function(){return t},destroy:function(){Et(t)}}}(e),r=Xu(t.element(),"mousedown",n.forceDrop),o=Xu(t.element(),"mouseup",n.drop),u=Xu(t.element(),"mousemove",n.move),i=Xu(t.element(),"mouseout",n.delayDrop);return Fu({element:t.element,start:function(n){Ot(n,t.element())},stop:function(){Et(t.element())},destroy:function(){t.destroy(),o.unbind(),u.unbind(),i.unbind(),r.unbind()}})},mutate:function(n,e){n.mutate(e.left,e.top)}}),ci=function(n){return"true"===Me(n,"contenteditable")},ai=oi("ephox-snooker").resolve,li=function(){var t,r=Mu({drag:ku(["xDelta","yDelta","target"])}),o=x.none(),n={mutate:function(n,e){t.trigger.drag(n,e)},events:(t=Mu({drag:ku(["xDelta","yDelta"])})).registry};n.events.drag.bind(function(e){o.each(function(n){r.trigger.drag(e.xDelta,e.yDelta,n)})});return{assign:function(n){o=x.some(n)},get:function(){return o},mutate:n.mutate,events:r.registry}},fi=ai("resizer-bar"),si=ai("resizer-rows"),di=ai("resizer-cols"),mi=function(n){var e=Te(n.parent(),"."+fi);P(e,Et)},gi=function(t,n,r){var o=t.origin();P(n,function(n){n.each(function(n){var e=r(o,n);ei(e,fi),Ot(t.parent(),e)})})},pi=function(n,e,l,f){gi(n,e,function(n,e){var t,r,o,u,i,c,a=(t=e.col,r=e.x-n.left,o=l.top-n.top,u=7,i=f,c=In.fromTag("div"),Le(c,{position:"absolute",left:r-u/2+"px",top:o+"px",height:i+"px",width:u+"px"}),ke(c,{"data-column":t,role:"presentation"}),c);return ei(a,di),a})},hi=function(n,e,l,f){gi(n,e,function(n,e){var t,r,o,u,i,c,a=(t=e.row,r=l.left-n.left,o=e.y-n.top,u=f,i=7,c=In.fromTag("div"),Le(c,{position:"absolute",left:r+"px",top:o-i/2+"px",height:i+"px",width:u+"px"}),ke(c,{"data-row":t,role:"presentation"}),c);return ei(a,si),a})},vi=function(n,e,t,r,o){var u,i=fo(t),c=e.isResizable,a=0<r.length?So.positions(r,t):[],l=0<a.length?(u=c,W(n.all,function(n,e){return u(n.element)?[e]:[]})):[],f=k(a,function(n,e){return I(l,function(n){return e===n})});hi(e,f,i,Jr(t));var s,d,m,g=0<o.length?Ro.positions(o,t):[],p=0<g.length?(d=c,m=[],B((s=n).grid.columns,function(n){mt.getColumnAt(s,n).map(function(n){return n.element}).forall(d)&&m.push(n)}),k(m,function(e){var n=mt.filterItems(s,function(n){return n.column===e});return F(n,function(n){return d(n.element)})})):[],h=k(g,function(n,e){return I(p,function(n){return e===n})});pi(e,h,i,io(t))},bi=function(n,e){var t,r,o;mi(n),n.isResizable(e)&&(t=mt.fromTable(e),r=no(t),o=Qr(t),vi(t,n,e,r,o))},wi=function(n,e){var t=Te(n.parent(),"."+fi);P(t,e)},yi=function(n){wi(n,function(n){Fe(n,"display","none")})},Ci=function(n){wi(n,function(n){Fe(n,"display","block")})},xi=ai("resizer-bar-dragging"),Si=function(o){var t=li(),r=function(n,e){void 0===e&&(e={});var t=e.mode!==undefined?e.mode:ii;return Vu(n,t,e)}(t,{}),e=x.none(),n=function(n,e){return x.from(Me(n,e))};t.events.drag.bind(function(t){n(t.target,"data-row").each(function(n){var e=Ye(t.target,"top");Fe(t.target,"top",e+t.yDelta+"px")}),n(t.target,"data-column").each(function(n){var e=Ye(t.target,"left");Fe(t.target,"left",e+t.xDelta+"px")})});var u=function(n,e){return Ye(n,e)-$e(n,"data-initial-"+e,0)};r.events.stop.bind(function(){t.get().each(function(r){e.each(function(t){n(r,"data-row").each(function(n){var e=u(r,"top");je(r,"data-initial-top"),s.trigger.adjustHeight(t,e,parseInt(n,10))}),n(r,"data-column").each(function(n){var e=u(r,"left");je(r,"data-initial-left"),s.trigger.adjustWidth(t,e,parseInt(n,10))}),bi(o,t)})})});var i=function(n,e){s.trigger.startAdjust(),t.assign(n),Pe(n,"data-initial-"+e,Ye(n,e)),ei(n,xi),Fe(n,"opacity","0.2"),r.go(o.parent())},c=Xu(o.parent(),"mousedown",function(n){var e,t;e=n.target,ri(e,si)&&i(n.target,"top"),t=n.target,ri(t,di)&&i(n.target,"left")}),a=function(n){return Pn(n,o.view())},l=function(n){return Be(n,"table",a).filter(function(n){return Be(n,"[contenteditable]",a).exists(ci)})},f=Xu(o.view(),"mouseover",function(n){l(n.target).fold(function(){we(n.target)&&mi(o)},function(n){e=x.some(n),bi(o,n)})}),s=Mu({adjustHeight:ku(["table","delta","row"]),adjustWidth:ku(["table","delta","column"]),startAdjust:ku([])});return{destroy:function(){c.unbind(),f.unbind(),r.destroy(),mi(o)},refresh:function(n){bi(o,n)},on:r.on,off:r.off,hideBars:w(yi,o),showBars:w(Ci,o),events:s.registry}},Ti=function(n,o,u){var r=So,i=Ro,e=Si(n),c=Mu({beforeResize:ku(["table","type"]),afterResize:ku(["table","type"]),startDrag:ku([])});return e.events.adjustHeight.bind(function(n){var e=n.table;c.trigger.beforeResize(e,"row");var t=r.delta(n.delta,e);lu(e,t,n.row,r),c.trigger.afterResize(e,"row")}),e.events.startAdjust.bind(function(n){c.trigger.startDrag()}),e.events.adjustWidth.bind(function(n){var e=n.table;c.trigger.beforeResize(e,"col");var t=i.delta(n.delta,e),r=u(e);au(e,t,n.column,o,r),c.trigger.afterResize(e,"col")}),{on:e.on,off:e.off,hideBars:e.hideBars,showBars:e.showBars,destroy:e.destroy,events:c.registry}},Ri=function(n,e){return n.fire("newrow",{node:e})},Oi=function(n,e){return n.fire("newcell",{node:e})},Di=function(n,e,t,r,o){n.fire("TableSelectionChange",{cells:e,start:t,finish:r,otherCells:o})},Ai=function(n){n.fire("TableSelectionClear")},Ii=function(n,e,t){n.fire("TableModified",U(U({},t),{table:e}))},Bi={"border-collapse":"collapse",width:"100%"},Ei={border:"1"},Pi="preservetable",ki=function(n){return n.getParam("table_sizing_mode","auto")},Mi=function(n){return n.getParam("table_responsive_width")},Ni=function(n){return n.getParam("table_default_attributes",Ei,"object")},ji=function(n){return n.getParam("table_default_styles",function(n){if(qi(n)){var e=n.getBody().offsetWidth;return U(U({},Bi),{width:e+"px"})}return Vi(n)?Ln(Bi,function(n,e){return"width"!==e}):Bi}(n),"object")},_i=function(n){return n.getParam("table_tab_navigation",!0,"boolean")},zi=function(n){return n.getParam("table_cell_advtab",!0,"boolean")},Wi=function(n){return n.getParam("table_row_advtab",!0,"boolean")},Fi=function(n){return n.getParam("table_advtab",!0,"boolean")},Li=function(n){return n.getParam("table_style_by_css",!1,"boolean")},Hi=function(n){return"relative"===ki(n)||!0===Mi(n)},qi=function(n){return"fixed"===ki(n)||!1===Mi(n)},Vi=function(n){return"responsive"===ki(n)},Ui=function(n){var e="section",t=n.getParam("table_header_type",e,"string");return A(["section","cells","sectionCells","auto"],t)?t:e},$i=function(n){var e=n.getParam("table_column_resizing",Pi,"string");return j(["preservetable","resizetable"],function(n){return n===e}).getOr(Pi)},Gi=function(n){return"preservetable"===$i(n)},Ki=function(n){var e=n.getParam("table_clone_elements");return p(e)?x.some(e.split(/[ ,]/)):Array.isArray(e)?x.some(e):x.none()},Xi=function(n){return n.nodeName.toLowerCase()},Yi=function(n){return In.fromDom(n.getBody())},Ji=function(n){return n.getBoundingClientRect().width},Qi=function(n){return n.getBoundingClientRect().height},Zi=function(e){return function(n){return Pn(n,Yi(e))}},nc=function(n){return/^\d+(\.\d+)?$/.test(n)?n+"px":n},ec=function(n){je(n,"data-mce-style");var e=function(n){return je(n,"data-mce-style")};P(tt(n),e),P(rt(n),e)},tc=function(n,e){var t=n.dom.getStyle(e,"width")||n.dom.getAttrib(e,"width");return x.from(t).filter(an)},rc=function(n){return/^(\d+(\.\d+)?)%$/.test(n)},oc=function(n){return In.fromDom(n.selection.getStart())},uc=function(n){var e=n;return{get:function(){return e},set:function(n){e=n}}},ic=function(t){var n=function(){return Yr(t)},e=b(0);return{width:n,pixelWidth:n,getWidths:function(n,e){return ru(n,t,e)},getCellDelta:e,singleColumnWidth:b([0]),minCellWidth:e,setElementWidth:y,adjustTableWidth:y,isRelative:!0,label:"none"}},cc=function(n,r){var o=uc(parseFloat(n)),u=uc(Yr(r));return{width:o.get,pixelWidth:u.get,getWidths:function(n,e){return eu(n,r,qo,function(n){return n.fold(function(){return t.minCellWidth()},function(n){return n/t.pixelWidth()*100})},t=e);var t},getCellDelta:function(n){return n/u.get()*100},singleColumnWidth:function(n,e){return[100-n]},minCellWidth:function(){return Je()/u.get()*100},setElementWidth:_o,adjustTableWidth:function(n){var e=o.get(),t=e+n/100*e;_o(r,t),o.set(t),u.set(Yr(r))},isRelative:!0,label:"percent"}},ac=function(n,t){var r=uc(n),o=r.get;return{width:o,pixelWidth:o,getWidths:function(n,e){return ru(n,t,e)},getCellDelta:d,singleColumnWidth:function(n,e){return[Math.max(Je(),n+e)-n]},minCellWidth:Je,setElementWidth:jo,adjustTableWidth:function(n){var e=o()+n;jo(t,e),r.set(e)},isRelative:!1,label:"pixel"}},lc=function(e){return Lo(e).fold(function(){return ic(e)},function(n){return function(n,e){var t=Ko().exec(e);if(null!==t)return cc(t[1],n);var r=Xo().exec(e);if(null!==r){var o=parseInt(r[1],10);return ac(o,n)}var u=Yr(n);return ac(u,n)}(e,n)})},fc=ac,sc=cc,dc=function(n,e){if(Hi(n)){var t=tc(n,e.dom).filter(rc).getOrThunk(function(){return Eu(e)});return sc(t,e)}return qi(n)?fc(Yr(e),e):lc(e)},mc=function(n){je(n,"width")},gc=function(n,e){var t=Yr(n)+"px";Du(n,x.some(t),x.none(),e),mc(n)},pc=function(n,e){var t,r,o,u=dc(n,e);r=u,o=Go(t=e),Du(t,x.some(o),x.none(),r),mc(t)},hc=function(n,e){var t=dc(n,e);gc(e,t)},vc=function(n){Ue(n,"width");var e=rt(n),t=0<e.length?e:tt(n);P(t,function(n){Ue(n,"width"),mc(n)}),mc(n)},bc=function(){var n=In.fromTag("div");return Le(n,{position:"static",height:"0",width:"0",padding:"0",margin:"0",border:"0"}),Ot(ye(In.fromDom(document)),n),n},wc="bar-",yc=function(n){return"false"!==Me(n,"data-mce-resize")},Cc=function(f){var s,d,a=x.none(),i=x.none(),c=x.none(),o=function(n){return"TABLE"===n.nodeName},n=function(){return i},m=function(n){return dc(f,n)},g=function(){return Gi(f)?{resizeTable:function(n,e,t){t&&n(e)},clampTableDelta:function(n,e,t,r,o){if(o){if(0<=t)return t;var u=N(n,function(n,e){return n+e-r},0);return Math.max(-u,t)}return mu(n,e,t,r)},calcLeftEdgeDeltas:i=function(n,e,t,r,o){var u=gu(n,0<=r?t:e,r,o);return su(n,e,t+1,[u,-u],fu)},calcMiddleDeltas:function(n,e,t,r,o,u){return i(n,t,r,o,u)},calcRightEdgeDeltas:function(n,e,t,r,o,u){if(u)return fu(n);var i=r/n.length;return E(n,b(i))}}:pu();var i},u=function(n,e,t){var r,o,u,i,c,a,l=un(e,"e");""===d&&pc(f,n),t!==s&&""!==d?(Fe(n,"width",d),r=g(),o=m(n),u=Gi(f)||l?Pu(n).columns-1:0,au(n,t-s,u,r,o)):rc(d)&&(i=parseFloat(d.replace("%","")),Fe(n,"width",t*i/s+"%")),/^(\d+(\.\d+)?)px$/.test(d)&&(c=n,a=mt.fromTable(c),mt.hasColumns(a)||P(tt(c),function(n){var e=He(n,"width");Fe(n,"width",e),je(n,"width")}))};return f.on("init",function(){var n,e,t,r,o,u=(e=yc,(n=f).inline?vu(Yi(n),bc(),e):hu(In.fromDom(n.getDoc()),e));c=x.some(u),o=f.getParam("object_resizing",!0),(p(o)?"table"===o:o)&&f.getParam("table_resize_bars",!0,"boolean")&&(t=g(),(r=Ti(u,t,m)).on(),r.events.startDrag.bind(function(n){a=x.some(f.selection.getRng())}),r.events.beforeResize.bind(function(n){var e,t,r,o,u,i=n.table.dom;e=f,r=Ji(t=i),o=Qi(i),u=wc+n.type,e.fire("ObjectResizeStart",{target:t,width:r,height:o,origin:u})}),r.events.afterResize.bind(function(n){var e,t,r,o,u,i=n.table,c=i.dom;ec(i),a.each(function(n){f.selection.setRng(n),f.focus()}),e=f,r=Ji(t=c),o=Qi(c),u=wc+n.type,e.fire("ObjectResized",{target:t,width:r,height:o,origin:u}),f.undoManager.add()}),i=x.some(r))}),f.on("ObjectResizeStart",function(n){var e,t=n.target;o(t)&&(e=In.fromDom(t),P(f.dom.select(".mce-clonedresizable"),function(n){f.dom.addClass(n,"mce-"+$i(f)+"-columns")}),!Iu(e)&&qi(f)?hc(f,e):!Au(e)&&Hi(f)&&pc(f,e),Bu(e)&&on(n.origin,wc)&&pc(f,e),s=n.width,d=Vi(f)?"":tc(f,t).getOr(""))}),f.on("ObjectResized",function(n){var e,t,r=n.target;o(r)&&(e=In.fromDom(r),t=n.origin,on(t,"corner-")&&u(e,t,n.width),ec(e),Ii(f,e.dom))}),f.on("SwitchMode",function(){i.each(function(n){f.mode.isReadOnly()?n.hideBars():n.showBars()})}),{lazyResize:n,lazyWire:function(){return c.getOr(hu(In.fromDom(f.getBody()),yc))},destroy:function(){i.each(function(n){n.destroy()}),c.each(function(n){var e;e=n,f.inline&&Et(e.parent())})}}},xc=function(n,e){return{element:n,offset:e}},Sc=function(e,n,t){return e.property().isText(n)&&0===e.property().getText(n).trim().length||e.property().isComment(n)?t(n).bind(function(n){return Sc(e,n,t).orThunk(function(){return x.some(n)})}):x.none()},Tc=function(n,e){return n.property().isText(e)?n.property().getText(e).length:n.property().children(e).length},Rc=function(n,e){var t=Sc(n,e,n.query().prevSibling).getOr(e);if(n.property().isText(t))return xc(t,Tc(n,t));var r=n.property().children(t);return 0<r.length?Rc(n,r[r.length-1]):xc(t,Tc(n,t))},Oc=Rc,Dc=Lt(),Ac=function(t,r){Lo(t).bind(function(n){return Ao(n,["fixed","relative","empty"])}).each(function(n){var e=n.value/2;$o(t,e,n.unit),$o(r,e,n.unit)})},Ic=function(n,e,t){n.cells[e]=t},Bc=function(n,e){return Xn(e,n.section)},Ec=function(n,e){var t=n.cells,r=E(t,e);return Xn(r,n.section)},Pc=function(n,e){return n.cells[e]},kc=function(n,e){return Pc(n,e).element},Mc=function(n){return n.cells.length},Nc=function(n){var e=function(n,e){for(var t=[],r=[],o=0,u=n.length;o<u;o++){var i=n[o];(e(i,o)?t:r).push(i)}return{pass:t,fail:r}}(n,function(n){return"colgroup"===n.section});return{rows:e.fail,cols:e.pass}},jc=function(n,e,t,r){t===r?je(n,e):Pe(n,e,t)},_c=function(n,e,t){q(Se(n,e)).fold(function(){return Rt(n,t)},function(n){return Tt(n,t)})},zc=function(c,n){var t=[],r=[],a=function(n){return E(n,function(n){n.isNew&&t.push(n.element);var e=n.element;return Bt(e),P(n.cells,function(n){n.isNew&&r.push(n.element),jc(n.element,"colspan",n.colspan,1),jc(n.element,"rowspan",n.rowspan,1),Ot(e,n.element)}),e})},l=function(n){return W(n,function(n){return E(n.cells,function(n){return jc(n.element,"span",n.colspan,1),n.element})})},o=function(n,e){var t,r,o,u=(o=Ae(t=c,r=e).getOrThunk(function(){var n=In.fromTag(r,oe(t).dom);return"thead"===r?_c(t,"caption,colgroup",n):"colgroup"===r?_c(t,"caption",n):Ot(t,n),n}),Bt(o),o),i=("colgroup"===e?l:a)(n);It(u,i)},e=function(n,e){0<n.length?o(n,e):Ae(c,e).each(Et)},u=[],i=[],f=[],s=[];return P(n,function(n){switch(n.section){case"thead":u.push(n);break;case"tbody":i.push(n);break;case"tfoot":f.push(n);break;case"colgroup":s.push(n)}}),e(s,"colgroup"),e(u,"thead"),e(i,"tbody"),e(f,"tfoot"),{newRows:t,newCells:r}},Wc=function(n,e){if(0===n.length)return 0;var t=n[0];return _(n,function(n){return!e(t.element,n.element)}).fold(function(){return n.length},function(n){return n})},Fc=function(n,e,t,r){var o,u=n[e].cells.slice(t),i=Wc(u,r),c=(o=t,E(n,function(n){return Pc(n,o)}).slice(e));return{colspan:i,rowspan:Wc(c,r)}},Lc=function(o,u){var i=E(o,function(n){return E(n.cells,f)});return E(o,function(n,r){return{details:W(n.cells,function(n,e){if(!1!==i[r][e])return[];var t=Fc(o,r,e,u);return function(n,e,t,r){for(var o=n;o<n+t;o++)for(var u=e;u<e+r;u++)i[o][u]=!0}(r,e,t.rowspan,t.colspan),[{element:n.element,rowspan:t.rowspan,colspan:t.colspan,isNew:n.isNew}]}),section:n.section}})},Hc=function(n,e,t){var r,o=[];mt.hasColumns(n)&&(r=E(mt.justColumns(n),function(n){return Kn(n.element,t)}),o.push(Xn(r,"colgroup")));for(var u=0;u<n.grid.rows;u++){for(var i=[],c=0;c<n.grid.columns;c++){var a=mt.getAt(n,u,c).map(function(n){return Kn(n.element,t)}).getOrThunk(function(){return Kn(e.gap(),!0)});i.push(a)}var l=Xn(i,n.all[u].section);o.push(l)}return o},qc=function(n,r){return E(n,function(n){var e,t=(e=n.details,V(e,function(n){return ie(n.element).map(function(n){var e=ie(n).isNone();return Kn(n,e)})}).getOrThunk(function(){return Kn(r.row(),!0)}));return{element:t.element,cells:n.details,section:n.section,isNew:t.isNew}})},Vc=function(n,e){var t=Lc(n,Pn);return qc(t,e)},Uc=function(n,e){return V(n.all,function(n){return j(n.cells,function(n){return Pn(e,n.element)})})},$c=function(a,e,l,f,s){return function(r,o,n,u,i){var c=mt.fromTable(o);return e(c,n).map(function(n){var e=Hc(c,u,!1),t=a(e,n,Pn,s(u));return{grid:Vc(t.grid,u),cursor:t.cursor}}).fold(function(){return x.none()},function(n){var e=zc(o,n.grid),t=x.from(i).getOrThunk(function(){return lc(o)});return l(o,n.grid,t),f(o),bi(r,o),x.some({cursor:n.cursor,newRows:e.newRows,newCells:e.newCells})})}},Gc=function(e,n){return et(n.element).bind(function(n){return Uc(e,n)})},Kc=function(e,n){var t=E(n.selection,function(n){return et(n).bind(function(n){return Uc(e,n)})}),r=Hr(t);return 0<r.length?x.some({cells:r,generators:n.generators,clipboard:n.clipboard}):x.none()},Xc=function(e,n){var t=E(n.selection,function(n){return et(n).bind(function(n){return Uc(e,n)})}),r=Hr(t);return 0<r.length?x.some(r):x.none()},Yc=function(n,e,t,r){for(var o=Nc(n).rows,u=!0,i=0;i<o.length;i++)for(var c=0;c<Mc(o[0]);c++){var a=t(kc(o[i],c),e);!0===a&&!1===u?Ic(o[i],c,Kn(r(),!0)):!0===a&&(u=!1)}return n},Jc=function(n,e,u,i){var t,r,o,c=Nc(n).rows;return 0<e&&e<c.length&&(t=c[e-1].cells,o=u,r=N(t,function(n,e){return I(n,function(n){return o(n.element,e.element)})?n:n.concat([e])},[]),P(r,function(r){for(var o=x.none(),n=e;n<c.length;n++)!function(t){for(var n=0;n<Mc(c[0]);n++)!function(e){var n=c[t].cells[e];u(n.element,r.element)&&(o.isNone()&&(o=x.some(i())),o.each(function(n){Ic(c[t],e,Kn(n,!0))}))}(n)}(n)})),n},Qc=function(t){return{is:function(n){return t===n},isValue:C,isError:f,getOr:b(t),getOrThunk:b(t),getOrDie:b(t),or:function(n){return Qc(t)},orThunk:function(n){return Qc(t)},fold:function(n,e){return e(t)},map:function(n){return Qc(n(t))},mapError:function(n){return Qc(t)},each:function(n){n(t)},bind:function(n){return n(t)},exists:function(n){return n(t)},forall:function(n){return n(t)},toOptional:function(){return x.some(t)}}},Zc=function(t){return{is:f,isValue:f,isError:C,getOr:d,getOrThunk:function(n){return n()},getOrDie:function(){return n=String(t),function(){throw new Error(n)}();var n},or:function(n){return n},orThunk:function(n){return n()},fold:function(n,e){return n(t)},map:function(n){return Zc(t)},mapError:function(n){return Zc(n(t))},each:y,bind:function(n){return Zc(t)},exists:f,forall:C,toOptional:x.none}},na={value:Qc,error:Zc,fromOption:function(n,e){return n.fold(function(){return Zc(e)},Qc)}},ea=function(n,e){return{rowDelta:0,colDelta:Mc(n[0])-Mc(e[0])}},ta=function(n,e){return{rowDelta:n.length-e.length,colDelta:0}},ra=function(n,e,t){var r="colgroup"===e.section?t.col:t.cell;return E(n,function(){return Kn(r(),!0)})},oa=function(e,n,t){return e.concat(B(n,function(){var n=e[e.length-1];return Bc(n,ra(n.cells,n,t))}))},ua=function(n,t,r){return E(n,function(n){var e=ra(B(t,d),n,r);return Bc(n,n.cells.concat(e))})},ia=function(n,e,t){var r=e.colDelta<0?ua:d;return(e.rowDelta<0?oa:d)(r(n,Math.abs(e.colDelta),t),Math.abs(e.rowDelta),t)},ca=function(n,e,t,r,o){for(var u,i,c,a,l,f=n.row,s=n.column,d=f+t.length,m=s+Mc(t[0]),g=f;g<d;g++)for(var p=s;p<m;p++){c=p,a=void 0,a=w(o,Pc((u=e)[i=g],c).element),l=u[i],1<u.length&&1<Mc(l)&&(0<c&&a(kc(l,c-1))||c<l.cells.length-1&&a(kc(l,c+1))||0<i&&a(kc(u[i-1],c))||i<u.length-1&&a(kc(u[i+1],c)))&&Yc(e,kc(e[g],p),o,r.cell);var h=kc(t[g-f],p-s),v=r.replace(h);Ic(e[g],p,Kn(v,!0))}return e},aa=function(t,r,o,u,i){return function(n,e,t){if(n.row>=e.length||n.column>Mc(e[0]))return na.error("invalid start address out of table bounds, row: "+n.row+", column: "+n.column);var r=e.slice(n.row),o=r[0].cells.slice(n.column),u=Mc(t[0]),i=t.length;return na.value({rowDelta:r.length-i,colDelta:o.length-u})}(t,r,o).map(function(n){var e=ia(r,n,u);return ca(t,e,o,u,i)})},la=function(r,n,e,t,o){var u,i,c,a;u=n,i=r,c=o,a=t.cell,0<i&&i<u[0].cells.length&&P(u,function(n){var e=n.cells[i-1],t=n.cells[i];c(t.element,e.element)&&Ic(n,i,Kn(a(),!0))});var l=ta(e,n),f=ia(e,l,t),s=ta(n,f),d=ia(n,s,t);return E(d,function(n,e){var t=n.cells.slice(0,r).concat(f[e].cells).concat(n.cells.slice(r,n.cells.length));return Bc(n,t)})},fa=function(n,e,t,r,o){Jc(e,n,o,r.cell);var u=ea(t,e),i=ia(t,u,r),c=ea(e,i),a=ia(e,c,r),l=Nc(a),f=l.cols,s=l.rows;return f.concat(s.slice(0,n)).concat(i).concat(s.slice(n,s.length))},sa=function(n,t,e,r,o){var u=Nc(n),i=u.rows,c=u.cols,a=i.slice(0,t),l=i.slice(t),f=Ec(i[e],function(n,e){return 0<t&&t<i.length&&r(kc(i[t-1],e),kc(i[t],e))?Pc(i[t],e):Kn(o(n.element,r),!0)});return c.concat(a).concat([f]).concat(l)},da=function(n,v,b,w,y){return E(n,function(n){var e,t,r,o,u,i,c,a,l,f,s,d,m,g,p=0<v&&v<Mc(n)&&w(kc(n,v-1),kc(n,v)),h=(t=v,r=(e=n).section,o=p,u=b,i=w,c=y,"colgroup"!==r&&o?Pc(e,t):Kn(c(kc(e,u),i),!0));return l=v,f=h,s=(a=n).cells,d=s.slice(0,l),m=s.slice(l),g=d.concat([f]).concat(m),Bc(a,g)})},ma=function(n,t,r,o){return E(n,function(n){return Ec(n,function(n){return e=n,I(t,function(n){return r(e.element,n.element)})?Kn(o(n.element,r),!0):n;var e})})},ga=function(n,e,t,r){return kc(n[e],t)!==undefined&&0<e&&r(kc(n[e-1],t),kc(n[e],t))},pa=function(n,e,t){return 0<e&&t(kc(n,e-1),kc(n,e))},ha=function(t,r,o,n){var e=W(t,function(n,e){return ga(t,e,r,o)||pa(n,r,o)?[]:[Pc(n,r)]});return ma(t,e,o,n)},va=function(n,t,r,e){var o=Nc(n).rows,u=o[t],i=W(u.cells,function(n,e){return ga(o,t,e,r)||pa(u,e,r)?[]:[n]});return ma(n,i,r,e)},ba=zu(["cell","row","replace","gap","col","colgroup"]),wa=function(n){return{element:n,colspan:$e(n,"colspan",1),rowspan:$e(n,"rowspan",1)}},ya=function(e,t){void 0===t&&(t=wa),ba(e);var r=uc(x.none()),o=function(n){return function(n){switch(Yn(n.element)){case"col":return e.col(n);default:return e.cell(n)}}(t(n))},u=function(n){var e=o(n);return r.get().isNone()&&r.set(x.some(e)),i=x.some({item:n,replacement:e}),e},i=x.none();return{getOrInit:function(e,t){return i.fold(function(){return u(e)},function(n){return t(e,n.item)?n.replacement:u(e)})},cursor:r.get}},Ca=function(c,a){return function(r){var o=uc(x.none());ba(r);var u=[],i=function(n){var e={scope:c},t=r.replace(n,a,e);return u.push({item:n,sub:t}),o.get().isNone()&&o.set(x.some(t)),t};return{replaceOrInit:function(e,t){return r=e,o=t,j(u,function(n){return o(n.item,r)}).fold(function(){return i(e)},function(n){return t(e,n.item)?n.sub:i(e)});var r,o},cursor:o.get}}},xa=function(t){ba(t);var n=uc(x.none());return{combine:function(e){return n.get().isNone()&&n.set(x.some(e)),function(){var n=t.cell({element:e,colspan:1,rowspan:1});return Ue(n,"width"),Ue(e,"width"),n}},cursor:n.get}},Sa=["body","p","div","article","aside","figcaption","figure","footer","header","nav","section","ol","ul","table","thead","tfoot","tbody","caption","tr","td","th","h1","h2","h3","h4","h5","h6","blockquote","pre","address"],Ta=Lt(),Ra=function(n){return e=n,t=Ta.property().name(e),A(Sa,t);var e,t},Oa=function(n){return e=n,t=Ta.property().name(e),A(["ol","ul"],t);var e,t},Da=function(n){return e=n,A(["br","img","hr","input"],Ta.property().name(e));var e},Aa=function(n){var e,u=function(n){return"br"===Yn(n)},t=function(o){return pr(o).bind(function(t){var r=le(t).map(function(n){return!!Ra(n)||!!Da(n)&&"img"!==Yn(n)}).getOr(!1);return ie(t).map(function(n){return!0===r||("li"===Yn(e=n)||Oe(e,Oa).isSome())||u(t)||Ra(n)&&!Pn(o,n)?[]:[In.fromTag("br")];var e})}).getOr([])},r=0===(e=W(n,function(n){var e=fe(n);return F(e,function(n){return u(n)||ee(n)&&0===_t(n).trim().length})?[]:e.concat(t(n))})).length?[In.fromTag("br")]:e;Bt(n[0]),It(n[0],r)},Ia=function(n){0===tt(n).length&&Et(n)},Ba=function(n,e){return{grid:n,cursor:e}},Ea=function(n,e,t){var r=Nc(n).rows;return Pa(r,e,t).orThunk(function(){return Pa(r,0,0)})},Pa=function(n,e,t){return x.from(n[e]).bind(function(n){return x.from(n.cells[t]).bind(function(n){return x.from(n.element)})})},ka=function(n,e,t){var r=Nc(n).rows;return Ba(n,Pa(r,e,t))},Ma=function(n){return N(n,function(n,e){return I(n,function(n){return n.row===e.row})?n:n.concat([e])},[]).sort(function(n,e){return n.row-e.row})},Na=function(n){return N(n,function(n,e){return I(n,function(n){return n.column===e.column})?n:n.concat([e])},[]).sort(function(n,e){return n.column-e.column})},ja=function(n,e,t){var r,o,u=(r=n,o=t.section,ct(r,function(){return o})),i=mt.generate(u);return Hc(i,e,!0)},_a=function(n,e){var t=k(n,e);return 0===t.length?x.some("td"):t.length===n.length?x.some("th"):x.none()},za=function(n,e,t){var r=mt.generate(e),o=t.getWidths(r,t);cu(r,o,t)},Wa=$c(function(n,e,t,r){var o=e[0].row,u=e[0].row,i=Ma(e),c=N(i,function(n,e){return sa(n,u,o,t,r.getOrInit)},n);return ka(c,u,e[0].column)},Xc,y,y,ya),Fa=$c(function(n,e,t,r){var o=Ma(e),u=o[o.length-1].row,i=o[o.length-1].row+o[o.length-1].rowspan,c=N(o,function(n,e){return sa(n,i,u,t,r.getOrInit)},n);return ka(c,i,e[0].column)},Xc,y,y,ya),La=$c(function(n,e,t,r){var o=Na(e),u=o[0].column,i=o[0].column,c=N(o,function(n,e){return da(n,i,u,t,r.getOrInit)},n);return ka(c,e[0].row,i)},Xc,za,y,ya),Ha=$c(function(n,e,t,r){var o=e[e.length-1].column,u=e[e.length-1].column+e[e.length-1].colspan,i=Na(e),c=N(i,function(n,e){return da(n,u,o,t,r.getOrInit)},n);return ka(c,e[0].row,u)},Xc,za,y,ya),qa=$c(function(n,e,t,r){var o,u,i,c,a=Na(e),l=(o=n,u=a[0].column,i=a[a.length-1].column,c=E(o,function(n){var e=n.cells.slice(0,u).concat(n.cells.slice(i+1));return Xn(e,n.section)}),k(c,function(n){return 0<n.cells.length})),f=Ea(l,e[0].row,e[0].column);return Ba(l,f)},Xc,za,Ia,ya),Va=$c(function(n,e,t,r){var o,u,i,c,a,l=Ma(e),f=(o=n,u=l[0].row,i=l[l.length-1].row,c=Nc(o),a=c.rows,c.cols.concat(a.slice(0,u)).concat(a.slice(i+1))),s=Ea(f,e[0].row,e[0].column);return Ba(f,s)},Xc,y,Ia,ya),Ua=($c(function(n,e,t,r){var o=ha(n,e.column,t,r.replaceOrInit);return ka(o,e.row,e.column)},Gc,y,y,Ca("row","th")),$c(function(n,e,t,r){var o=Na(e),u=N(o,function(n,e){return ha(n,e.column,t,r.replaceOrInit)},n);return ka(u,e[0].row,e[0].column)},Xc,y,y,Ca("row","th"))),$a=($c(function(n,e,t,r){var o=ha(n,e.column,t,r.replaceOrInit);return ka(o,e.row,e.column)},Gc,y,y,Ca(null,"td")),$c(function(n,e,t,r){var o=Na(e),u=N(o,function(n,e){return ha(n,e.column,t,r.replaceOrInit)},n);return ka(u,e[0].row,e[0].column)},Xc,y,y,Ca(null,"td"))),Ga=($c(function(n,e,t,r){var o=va(n,e.row,t,r.replaceOrInit);return ka(o,e.row,e.column)},Gc,y,y,Ca("col","th")),$c(function(n,e,t,r){var o=Ma(e),u=N(o,function(n,e){return va(n,e.row,t,r.replaceOrInit)},n);return ka(u,e[0].row,e[0].column)},Xc,y,y,Ca("col","th")),$c(function(n,e,t,r){var o=va(n,e.row,t,r.replaceOrInit);return ka(o,e.row,e.column)},Gc,y,y,Ca(null,"td")),$c(function(n,e,t,r){var o=Ma(e),u=N(o,function(n,e){return va(n,e.row,t,r.replaceOrInit)},n);return ka(u,e[0].row,e[0].column)},Xc,y,y,Ca(null,"td")),$c(function(n,e,t,r){var o=e.cells;Aa(o);var u=function(n,e,t){var r=Nc(n).rows;if(0===r.length)return n;for(var o=e.startRow;o<=e.finishRow;o++)for(var u=e.startCol;u<=e.finishCol;u++)Ic(r[o],u,Kn(t(),!1));return n}(n,e.bounds,b(o[0]));return Ba(u,x.from(o[0]))},function(n,e){return e.mergable},y,y,xa)),Ka=$c(function(n,e,t,r){var o=M(e,function(n,e){return Yc(n,e,t,r.combine(e))},n);return Ba(o,x.from(e[0]))},function(n,e){return e.unmergable},za,y,xa),Xa=$c(function(n,t,e,r){var o,u,i,c=(o=t.clipboard,u=t.generators,i=mt.fromTable(o),Hc(i,u,!0)),a={row:t.row,column:t.column};return aa(a,n,c,t.generators,e).fold(function(){return Ba(n,x.some(t.element))},function(n){var e=Ea(n,t.row,t.column);return Ba(n,e)})},function(e,t){return et(t.element).bind(function(n){return Uc(e,n).map(function(n){return U(U({},n),{generators:t.generators,clipboard:t.clipboard})})})},za,y,ya),Ya=$c(function(n,e,t,r){var o=Nc(n).rows,u=e.cells[0].column,i=o[e.cells[0].row],c=ja(e.clipboard,e.generators,i),a=la(u,n,c,e.generators,t),l=Ea(a,e.cells[0].row,e.cells[0].column);return Ba(a,l)},Kc,y,y,ya),Ja=$c(function(n,e,t,r){var o=Nc(n).rows,u=e.cells[e.cells.length-1].column+e.cells[e.cells.length-1].colspan,i=o[e.cells[0].row],c=ja(e.clipboard,e.generators,i),a=la(u,n,c,e.generators,t),l=Ea(a,e.cells[0].row,e.cells[0].column);return Ba(a,l)},Kc,y,y,ya),Qa=$c(function(n,e,t,r){var o=Nc(n).rows,u=e.cells[0].row,i=o[u],c=ja(e.clipboard,e.generators,i),a=fa(u,n,c,e.generators,t),l=Ea(a,e.cells[0].row,e.cells[0].column);return Ba(a,l)},Kc,y,y,ya),Za=$c(function(n,e,t,r){var o=Nc(n).rows,u=e.cells[e.cells.length-1].row+e.cells[e.cells.length-1].rowspan,i=o[e.cells[0].row],c=ja(e.clipboard,e.generators,i),a=fa(u,n,c,e.generators,t),l=Ea(a,e.cells[0].row,e.cells[0].column);return Ba(a,l)},Kc,y,y,ya),nl=function(n,e){var u=mt.fromTable(n);return Xc(u,e).bind(function(n){var e=n[n.length-1],t=n[0].column,r=e.column+e.colspan,o=z(E(u.all,function(n){return k(n.cells,function(n){return n.column>=t&&n.column<r})}));return _a(o,function(n){return"th"===Yn(n.element)})}).getOr("")},el=function(n){return Xi(n.parentNode)},tl=function(n,e){var t="thead"===el(e),r=!I(e.cells,function(n){return"th"!==Xi(n)});return t||r?x.some({thead:t,ths:r}):x.none()},rl=function(n,e){return"thead"===(t=tl(0,e).fold(function(){return el(e)},function(n){return"thead"}))?"header":"tfoot"===t?"footer":"body";var t},ol=function(e,n,t){var r,o,u=e.getParent(n,"table"),i=n.parentNode,c=Xi(i);t!==c&&((r=e.select(t,u)[0])||(r=e.create(t),o=u.firstChild,"thead"===t?q(Se(In.fromDom(u),"caption,colgroup")).fold(function(){return u.insertBefore(r,o)},function(n){return e.insertAfter(r,n.dom)}):u.appendChild(r)),"tbody"===t&&"thead"===c&&r.firstChild?r.insertBefore(n,r.firstChild):r.appendChild(n),i.hasChildNodes()||e.remove(i))},ul=function(n,e,t,r){var o=n.dom,u=function(n,e,t){if(v(t)&&Xi(e)!==t){var r=n.dom.rename(e,t);return Oi(n,r),r}return e}(n,e,t);return m(r)||o.setAttrib(u,"scope",r),u},il=function(e,n,t,r){return P(n,function(n){return ul(e,n,t,r)})},cl=function(n,e,t){var r,o,u,i=n.dom;"header"===t?(o="auto"===(r=Ui(n))?(u=ot(In.fromDom(e.cells[0])).map(ut).getOr([]),V(u,function(n){return tl(0,n.dom)}).map(function(n){return n.thead&&n.ths?"sectionCells":n.thead?"section":"cells"}).getOr("section")):r,il(n,e.cells,"section"===o?"td":"th","col"),ol(i,e,"cells"===o?"tbody":"thead")):(il(n,e.cells,"td",null),ol(i,e,"footer"===t?"tfoot":"tbody"))},al=function(o){return function(n){var e,t=Yn(n),r="col"===t||"colgroup"===t?ot(e=n).bind(function(n){return Zt(n,Nr.firstSelectedSelector)}).fold(function(){return e},function(n){return n[0]}):n;return Be(r,o)}},ll=al("th,td"),fl=al("th,td,caption"),sl=function(n,e){return ll(n).map(function(n){return Er(e)}).getOr([])},dl=function(n,t){var e,r,o,u=ll(n),i=u.bind(function(n){return ot(n)}).map(ut);return r=i,o=function(e,n){return k(n,function(n){return I(Br(n.dom.cells),function(n){return"1"===Me(n,t)||Pn(n,e)})})},((e=u).isSome()&&r.isSome()?x.some(o(e.getOrDie(),r.getOrDie())):x.none()).getOr([])},ml=function(f,n,r){var e=function(n){return"table"===Yn(Yi(n))},s=Ki(f),t=function(i,c,a,l){return function(n,e){ec(n);var t=l(),r=In.fromDom(f.getDoc()),o=Ar(a,r,s),u=dc(f,n);return c(n)?i(t,n,e,o,u).bind(function(n){return P(n.newRows,function(n){Ri(f,n.dom)}),P(n.newCells,function(n){Oi(f,n.dom)}),n.cursor.map(function(n){var e=Oc(Dc,n),t=f.dom.createRng();return t.setStart(e.element.dom,e.offset),t.setEnd(e.element.dom,e.offset),t})}):x.none()}},o=t(Va,function(n){return!1===e(f)||1<Pu(n).rows},y,n),u=t(qa,function(n){return!1===e(f)||1<Pu(n).columns},y,n),i=t(Wa,C,y,n),c=t(Fa,C,y,n),a=t(La,C,Ac,n),l=t(Ha,C,Ac,n),d=t(Ga,C,y,n),m=t(Ka,C,y,n),g=t(Ya,C,y,n),p=t(Ja,C,y,n),h=t(Qa,C,y,n),v=t(Za,C,y,n),b=t(Xa,C,y,n),w=function(n,e){return qn(n,"type").filter(function(n){return A(e,n)})};return{deleteRow:o,deleteColumn:u,insertRowsBefore:i,insertRowsAfter:c,insertColumnsBefore:a,insertColumnsAfter:l,mergeCells:d,unmergeCells:m,pasteColsBefore:g,pasteColsAfter:p,pasteRowsBefore:h,pasteRowsAfter:v,pasteCells:b,setTableCellType:function(t,n){return w(n,["td","th"]).each(function(n){var e=E(sl(oc(t),r),function(n){return n.dom});il(t,e,n,null)})},setTableRowType:function(t,n){return w(n,["header","body","footer"]).each(function(e){E(dl(oc(t),Nr.selected),function(n){return cl(t,n.dom,e)})})},makeColumnsHeader:t(Ua,C,y,n),unmakeColumnsHeader:t($a,C,y,n),getTableRowType:function(n){var e=dl(oc(n),Nr.selected);if(0<e.length){var t=E(e,function(n){return rl(0,n.dom)}),r=A(t,"header"),o=A(t,"footer");if(r||o){var u=A(t,"body");return!r||u||o?r||u||!o?"":"footer":"header"}return"body"}},getTableCellType:function(n){return _a(sl(oc(n),r),function(n){return"th"===Yn(n)}).getOr("")},getTableColType:nl}},gl={styles:{"border-collapse":"collapse",width:"100%"},attributes:{border:"1"},colGroups:!1},pl=function(n,e,t,r){for(var o=In.fromTag("tr"),u=0;u<n;u++){var i=r<e||u<t?In.fromTag("th"):In.fromTag("td");u<t&&Pe(i,"scope","row"),r<e&&Pe(i,"scope","col"),Ot(i,In.fromTag("br")),Ot(o,i)}return o},hl=function(n){var e=In.fromTag("colgroup");return B(n,function(){return Ot(e,In.fromTag("col"))}),e},vl=function(n,e,t,r){return B(n,function(n){return pl(e,t,r,n)})},bl=function(n,e){n.selection.select(e.dom,!0),n.selection.collapse(!0)},wl=function(u,i,c,a,l){var o=ji(u),f={styles:o,attributes:Ni(u),colGroups:u.getParam("table_use_colgroups",!1,"boolean")};return u.undoManager.ignore(function(){var n=function(n,e,t,r,o,u){void 0===u&&(u=gl);var i=In.fromTag("table"),c="cells"!==o;Le(i,u.styles),ke(i,u.attributes),u.colGroups&&Ot(i,hl(e));var a,l,f=Math.min(n,t);c&&0<t&&(a=In.fromTag("thead"),Ot(i,a),l=vl(t,e,"sectionCells"===o?f:0,r),It(a,l));var s=In.fromTag("tbody");Ot(i,s);var d=vl(c?n-f:n,e,c?0:t,r);return It(s,d),i}(c,i,l,a,Ui(u),f);Pe(n,"data-mce-id","__mce");var e,t,r,o=(e=n,t=In.fromTag("div"),r=In.fromDom(e.dom.cloneNode(!0)),Ot(t,r),t.dom.innerHTML);u.insertContent(o),u.addVisual()}),Ie(Yi(u),'table[data-mce-id="__mce"]').map(function(n){var e,t,r;return qi(u)?hc(u,n):Vi(u)?vc(n):(Hi(u)||(e=o.width,p(e)&&-1!==e.indexOf("%")))&&pc(u,n),ec(n),je(n,"data-mce-id"),t=u,P(Te(n,"tr"),function(n){Ri(t,n.dom),P(Te(n,"th,td"),function(n){Oi(t,n.dom)})}),r=u,Ie(n,"td,th").each(w(bl,r)),n.dom}).getOr(null)},yl=function(n,e,t,r,o){void 0===r&&(r={});var u=function(n){return T(n)&&0<n};if(u(e)&&u(t)){var i=r.headerRows||0,c=r.headerColumns||0;return wl(n,t,e,c,i)}return console.error(o),null},Cl=function(n){return function(){return n().fold(function(){return[]},function(n){return E(n,function(n){return n.dom})})}},xl=function(t){return function(n){var e=0<n.length?x.some(Br(n)):x.none();t(e)}},Sl=function(n,e,t,r){return{insertTable:(o=n,function(n,e,t){void 0===t&&(t={});var r=yl(o,e,n,t,"Invalid values for insertTable - rows and columns values are required to insert a table.");return o.undoManager.add(),r}),setClipboardRows:xl(e.setRows),getClipboardRows:Cl(e.getRows),setClipboardCols:xl(e.setColumns),getClipboardCols:Cl(e.getColumns),resizeHandler:t,selectionTargets:r};var o},Tl=function(n,e,t){var r=$e(n,e,1);1===t||r<=1?je(n,e):Pe(n,e,Math.min(t,r))},Rl=function(n,e){var i=mt.fromTable(n);return Xc(i,e).map(function(n){var o,u,e=n[n.length-1],t=n[0].column,r=e.column+e.colspan;return $(function(n,t,r){if(mt.hasColumns(n)){var e=k(mt.justColumns(n),function(n){return n.column>=t&&n.column<r}),o=E(e,function(n){var e=wr(n.element);return Tl(e,"span",r-t),e}),u=In.fromTag("colgroup");return It(u,o),[u]}return[]}(i,t,r),(o=t,u=r,E(i.all,function(n){var e=k(n.cells,function(n){return n.column>=o&&n.column<u}),t=E(e,function(n){var e=wr(n.element);return Tl(e,"colspan",u-o),e}),r=In.fromTag("tr");return It(r,t),r})))})},Ol=function(n,e,o){var u=mt.fromTable(n);return Xc(u,e).map(function(n){var e=Hc(u,o,!1),t=Nc(e).rows.slice(n[0].row,n[n.length-1].row+n[n.length-1].rowspan),r=Vc(t,o);return E(r,function(n){var t=br(n.element);return P(n.cells,function(n){var e=wr(n.element);jc(e,"colspan",n.colspan,1),jc(e,"rowspan",n.rowspan,1),Ot(t,e)}),t})})},Dl=tinymce.util.Tools.resolve("tinymce.util.Tools"),Al=function(o,n,u){return function(n,e){for(var t=0;t<e.length;t++){var r=o.getStyle(e[t],u);if(void 0===n&&(n=r),n!==r)return""}return n}(void 0,o.select("td,th",n))},Il=function(n,e,t){t&&n.formatter.apply("align"+t,{},e)},Bl=function(e,t){Dl.each("left center right".split(" "),function(n){e.formatter.remove("align"+n,{},t)})},El=function(n){return Vn(e=n,t="menu")&&e[t]!==undefined&&null!==e[t];var e,t},Pl=function(n,e){return(e||[]).concat(E(n,function(n){var e=n.text||n.title;return El(n)?{text:e,items:Pl(n.menu)}:{text:e,value:n.value}}))},kl=function(e){return function(n){return on(n,"rgb")?e.toHex(n):n}},Ml=function(n,e){var t=In.fromDom(e);return{borderwidth:Ve(t,"border-width").getOr(""),borderstyle:Ve(t,"border-style").getOr(""),bordercolor:Ve(t,"border-color").map(kl(n)).getOr(""),backgroundcolor:Ve(t,"background-color").map(kl(n)).getOr("")}},Nl=function(n){var o=n[0],e=n.slice(1);return P(e,function(n){P(jn(o),function(r){zn(n,function(n,e){var t=o[r];""!==t&&r===e&&t!==n&&(o[r]="")})})}),o},jl=function(n){var e=[{name:"borderstyle",type:"listbox",label:"Border style",items:[{text:"Select...",value:""},{text:"Solid",value:"solid"},{text:"Dotted",value:"dotted"},{text:"Dashed",value:"dashed"},{text:"Double",value:"double"},{text:"Groove",value:"groove"},{text:"Ridge",value:"ridge"},{text:"Inset",value:"inset"},{text:"Outset",value:"outset"},{text:"None",value:"none"},{text:"Hidden",value:"hidden"}]},{name:"bordercolor",type:"colorinput",label:"Border color"},{name:"backgroundcolor",type:"colorinput",label:"Background color"}];return{title:"Advanced",name:"advanced",items:"cell"===n?[{name:"borderwidth",type:"input",label:"Border width"}].concat(e):e}},_l=function(n,e,t,r){return j(n,function(n){return t.formatter.matchNode(r,e+n)}).getOr("")},zl=w(_l,["left","center","right"],"align"),Wl=w(_l,["top","middle","bottom"],"valign"),Fl=function(n,e){var t,r,o,u,i=ji(n),c=Ni(n),a=e?(t=n.dom,{borderstyle:qn(i,"border-style").getOr(""),bordercolor:kl(t)(qn(i,"border-color").getOr("")),backgroundcolor:kl(t)(qn(i,"background-color").getOr(""))}):{};return U(U(U(U(U(U({},{height:"",width:"100%",cellspacing:"",cellpadding:"",caption:!1,"class":"",align:"",border:""}),i),c),a),(u=i["border-width"],Li(n)&&u?{border:u}:qn(c,"border").fold(function(){return{}},function(n){return{border:n}}))),(r=qn(i,"border-spacing").or(qn(c,"cellspacing")).fold(function(){return{}},function(n){return{cellspacing:n}}),o=qn(i,"border-padding").or(qn(c,"cellpadding")).fold(function(){return{}},function(n){return{cellpadding:n}}),U(U({},r),o)))},Ll=[{name:"width",type:"input",label:"Width"},{name:"height",type:"input",label:"Height"},{name:"celltype",type:"listbox",label:"Cell type",items:[{text:"Cell",value:"td"},{text:"Header cell",value:"th"}]},{name:"scope",type:"listbox",label:"Scope",items:[{text:"None",value:""},{text:"Row",value:"row"},{text:"Column",value:"col"},{text:"Row group",value:"rowgroup"},{text:"Column group",value:"colgroup"}]},{name:"halign",type:"listbox",label:"H Align",items:[{text:"None",value:""},{text:"Left",value:"left"},{text:"Center",value:"center"},{text:"Right",value:"right"}]},{name:"valign",type:"listbox",label:"V Align",items:[{text:"None",value:""},{text:"Top",value:"top"},{text:"Middle",value:"middle"},{text:"Bottom",value:"bottom"}]}],Hl=function(n){return Ll.concat((0<(e=Pl(n.getParam("table_cell_class_list",[],"array"))).length?x.some({name:"class",type:"listbox",label:"Class",items:e}):x.none()).toArray());var e},ql=function(u){return function(t,r){var o=t.dom;return{setAttrib:function(n,e){u&&!e||o.setAttrib(r,n,e)},setStyle:function(n,e){u&&!e||o.setStyle(r,n,e)},setFormat:function(n,e){u&&!e||(""===e?t.formatter.remove(n,{value:null},r,!0):t.formatter.apply(n,{value:e},r))}}}},Vl={normal:ql(!1),ifTruthy:ql(!0)},Ul=function(o){return ot(o[0]).map(function(n){var e=mt.fromTable(n),t=mt.justCells(e),r=k(t,function(e){return I(o,function(n){return Pn(e.element,n)})});return E(r,function(n){return{element:n.element.dom,column:mt.getColumnAt(e,n.column).map(function(n){return n.element.dom})}})})},$l=function(g,n,p){var e,h=1===n.length;1<=n.length&&(e=ot(n[0]),Ul(n).each(function(n){P(n,function(n){var e,t,r,o,u,i,c,a,l,f,s=ul(g,n.element,p.celltype),d=h?Vl.normal(g,s):Vl.ifTruthy(g,s),m=n.column.map(function(n){return h?Vl.normal(g,n):Vl.ifTruthy(g,n)}).getOr(d);t=m,r=p,(e=d).setAttrib("scope",r.scope),e.setAttrib("class",r["class"]),e.setStyle("height",nc(r.height)),t.setStyle("width",nc(r.width)),zi(g)&&(u=p,(o=d).setFormat("tablecellbackgroundcolor",u.backgroundcolor),o.setFormat("tablecellbordercolor",u.bordercolor),o.setFormat("tablecellborderstyle",u.borderstyle),o.setFormat("tablecellborderwidth",nc(u.borderwidth))),h&&(Bl(g,s),i=g,c=s,Dl.each("top middle bottom".split(" "),function(n){i.formatter.remove("valign"+n,{},c)})),p.halign&&Il(g,s,p.halign),p.valign&&(a=g,l=s,(f=p.valign)&&a.formatter.apply("valign"+f,{},l))})}),e.each(function(n){return Ii(g,n.dom)}))},Gl=function(n,e,t){var r=t.getData();t.close(),n.undoManager.transact(function(){$l(n,e,r),n.focus()})},Kl=function(a,n){var e=Ul(n).map(function(n){return E(n,function(n){return e=a,t=n.element,r=zi(a),o=n.column,u=e.dom,i=o.getOr(t),U({width:(c=function(n,e){return u.getStyle(n,e)||u.getAttrib(n,e)})(i,"width"),height:c(t,"height"),scope:u.getAttrib(t,"scope"),celltype:Xi(t),"class":u.getAttrib(t,"class",""),halign:zl(e,t),valign:Wl(e,t)},r?Ml(u,t):{});var e,t,r,o,u,i,c})});return Nl(e.getOrDie())},Xl=function(n,e){var t,r,o,u=sl(oc(n),e);0!==u.length&&(t=Kl(n,u),r={type:"tabpanel",tabs:[{title:"General",name:"general",items:Hl(n)},jl("cell")]},o={type:"panel",items:[{type:"grid",columns:2,items:Hl(n)}]},n.windowManager.open({title:"Cell Properties",size:"normal",body:zi(n)?r:o,buttons:[{type:"cancel",name:"cancel",text:"Cancel"},{type:"submit",name:"save",text:"Save",primary:!0}],initialData:t,onSubmit:w(Gl,n,u)}))},Yl=[{type:"listbox",name:"type",label:"Row type",items:[{text:"Header",value:"header"},{text:"Body",value:"body"},{text:"Footer",value:"footer"}]},{type:"listbox",name:"align",label:"Alignment",items:[{text:"None",value:""},{text:"Left",value:"left"},{text:"Center",value:"center"},{text:"Right",value:"right"}]},{label:"Height",name:"height",type:"input"}],Jl=function(n){return Yl.concat((0<(e=Pl(n.getParam("table_row_class_list",[],"array"))).length?x.some({name:"class",type:"listbox",label:"Class",items:e}):x.none()).toArray());var e},Ql=function(i,n,c,a){var l=1===n.length;P(n,function(n){a.type!==Xi(n.parentNode)&&cl(i,n,a.type);var e,t,r,o,u=l?Vl.normal(i,n):Vl.ifTruthy(i,n);t=a,(e=u).setAttrib("scope",t.scope),e.setAttrib("class",t["class"]),e.setStyle("height",nc(t.height)),Wi(i)&&(o=a,(r=u).setStyle("background-color",o.backgroundcolor),r.setStyle("border-color",o.bordercolor),r.setStyle("border-style",o.borderstyle)),a.align!==c.align&&(Bl(i,n),Il(i,n,a.align))}),ot(In.fromDom(n[0])).each(function(n){return Ii(i,n.dom)})},Zl=function(n,e,t,r){var o=r.getData();r.close(),n.undoManager.transact(function(){Ql(n,e,t,o),n.focus()})},nf=function(u){var n,e,t,r,o=dl(oc(u),Nr.selected);0!==o.length&&(n=E(o,function(n){return e=u,t=n.dom,r=Wi(u),o=e.dom,U({height:o.getStyle(t,"height")||o.getAttrib(t,"height"),scope:o.getAttrib(t,"scope"),"class":o.getAttrib(t,"class",""),type:rl(0,t),align:zl(e,t)},r?Ml(o,t):{});var e,t,r,o}),e=Nl(n),t={type:"tabpanel",tabs:[{title:"General",name:"general",items:Jl(u)},jl("row")]},r={type:"panel",items:[{type:"grid",columns:2,items:Jl(u)}]},u.windowManager.open({title:"Row Properties",size:"normal",body:Wi(u)?t:r,buttons:[{type:"cancel",name:"cancel",text:"Cancel"},{type:"submit",name:"save",text:"Save",primary:!0}],initialData:e,onSubmit:w(Zl,u,E(o,function(n){return n.dom}),e)}))},ef=tinymce.util.Tools.resolve("tinymce.Env"),tf=function(n,e,t,r){if("TD"===e.tagName||"TH"===e.tagName)p(t)?n.setStyle(e,t,r):n.setStyle(e,t);else if(e.children)for(var o=0;o<e.children.length;o++)tf(n,e.children[o],t,r)},rf=function(t,r,n){var o,u=t.dom,i=n.getData();n.close(),""===i["class"]&&delete i["class"],t.undoManager.transact(function(){var n,e;r||(n=parseInt(i.cols,10)||1,e=parseInt(i.rows,10)||1,r=wl(t,n,e,0,0)),function(n,e,t){var r,o=n.dom,u={},i={};if(u["class"]=t["class"],i.height=nc(t.height),o.getAttrib(e,"width")&&!Li(n)?u.width=(r=t.width)?r.replace(/px$/,""):"":i.width=nc(t.width),Li(n)?(i["border-width"]=nc(t.border),i["border-spacing"]=nc(t.cellspacing)):(u.border=t.border,u.cellpadding=t.cellpadding,u.cellspacing=t.cellspacing),Li(n)&&e.children)for(var c=0;c<e.children.length;c++)tf(o,e.children[c],{"border-width":nc(t.border),padding:nc(t.cellpadding)}),Fi(n)&&tf(o,e.children[c],{"border-color":t.bordercolor});Fi(n)&&(i["background-color"]=t.backgroundcolor,i["border-color"]=t.bordercolor,i["border-style"]=t.borderstyle),u.style=o.serializeStyle(U(U({},ji(n)),i)),o.setAttribs(e,U(U({},Ni(n)),u)),Ii(n,e)}(t,r,i),(o=u.select("caption",r)[0])&&!i.caption&&u.remove(o),!o&&i.caption&&((o=u.create("caption")).innerHTML=ef.ie?"\xa0":'<br data-mce-bogus="1"/>',r.insertBefore(o,r.firstChild)),""===i.align?Bl(t,r):Il(t,r,i.align),t.focus(),t.addVisual()})},of=function(n,e){var t,r,o,u,i,c,a,l,f=n.dom,s=Fl(n,Fi(n));!1===e?(t=f.getParent(n.selection.getStart(),"table"))?(o=t,u=Fi(r=n),l=r.dom,s=U({width:l.getStyle(o,"width")||l.getAttrib(o,"width"),height:l.getStyle(o,"height")||l.getAttrib(o,"height"),cellspacing:l.getStyle(o,"border-spacing")||l.getAttrib(o,"cellspacing"),cellpadding:l.getAttrib(o,"cellpadding")||Al(r.dom,o,"padding"),border:(i=l,c=o,a=Ve(In.fromDom(c),"border-width"),Li(r)&&a.isSome()?a.getOr(""):i.getAttrib(c,"border")||Al(r.dom,c,"border-width")||Al(r.dom,c,"border")),caption:!!l.select("caption",o)[0],"class":l.getAttrib(o,"class",""),align:zl(r,o)},u?Ml(l,o):{})):Fi(n)&&(s.borderstyle="",s.bordercolor="",s.backgroundcolor=""):(s.cols="1",s.rows="1",Fi(n)&&(s.borderstyle="",s.bordercolor="",s.backgroundcolor=""));var d=Pl(n.getParam("table_class_list",[],"array"));0<d.length&&s["class"]&&(s["class"]=s["class"].replace(/\s*mce\-item\-table\s*/g,""));var m,g,p,h,v={type:"grid",columns:2,items:(m=d,g=e?[{type:"input",name:"cols",label:"Cols",inputMode:"numeric"},{type:"input",name:"rows",label:"Rows",inputMode:"numeric"}]:[],p=n.getParam("table_appearance_options",!0,"boolean")?[{type:"input",name:"cellspacing",label:"Cell spacing",inputMode:"numeric"},{type:"input",name:"cellpadding",label:"Cell padding",inputMode:"numeric"},{type:"input",name:"border",label:"Border width"},{type:"label",label:"Caption",items:[{type:"checkbox",name:"caption",label:"Show caption"}]}]:[],h=0<m.length?[{type:"listbox",name:"class",label:"Class",items:m}]:[],g.concat([{type:"input",name:"width",label:"Width"},{type:"input",name:"height",label:"Height"}]).concat(p).concat([{type:"listbox",name:"align",label:"Alignment",items:[{text:"None",value:""},{text:"Left",value:"left"},{text:"Center",value:"center"},{text:"Right",value:"right"}]}]).concat(h))},b=Fi(n)?{type:"tabpanel",tabs:[{title:"General",name:"general",items:[v]},jl("table")]}:{type:"panel",items:[v]};n.windowManager.open({title:"Table Properties",size:"normal",body:b,onSubmit:w(rf,n,t),buttons:[{type:"cancel",name:"cancel",text:"Cancel"},{type:"submit",name:"save",text:"Save",primary:!0}],initialData:s})},uf=function(n){return fl(oc(n))},cf=function(n){return ll(oc(n))},af=function(c,r,t,a,e){var o=Zi(c),l=function(n){return ot(n,o)},f=function(e){return function(n){c.selection.setRng(n),c.focus(),t.clear(e),ec(e),Ii(c,e.dom)}},u=function(r){return cf(c).each(function(t){l(t).each(function(n){var e=_r(a,n,t);r(n,e).each(f(n))})})},i=function(){return cf(c).map(function(r){return l(r).bind(function(n){var e=_r(a,n,r),t=Ar(y,In.fromDom(c.getDoc()),x.none());return Ol(n,e,t)})})},s=function(){return cf(c).map(function(t){return l(t).bind(function(n){var e=_r(a,n,t);return Rl(n,e)})})},d=function(i,n){return n().each(function(n){var u=E(n,wr);cf(c).each(function(n){return l(n).each(function(n){var e,t,r=Ir(In.fromDom(c.getDoc())),o=(e=u,t=r,{selection:Er(a),clipboard:e,generators:t});i(n,o).each(f(n))})})})};zn({mceTableSplitCells:function(){return u(r.unmergeCells)},mceTableMergeCells:function(){return u(r.mergeCells)},mceTableInsertRowBefore:function(){return u(r.insertRowsBefore)},mceTableInsertRowAfter:function(){return u(r.insertRowsAfter)},mceTableInsertColBefore:function(){return u(r.insertColumnsBefore)},mceTableInsertColAfter:function(){return u(r.insertColumnsAfter)},mceTableDeleteCol:function(){return u(r.deleteColumn)},mceTableDeleteRow:function(){return u(r.deleteRow)},mceTableCutCol:function(n){return s().each(function(n){e.setColumns(n),u(r.deleteColumn)})},mceTableCutRow:function(n){return i().each(function(n){e.setRows(n),u(r.deleteRow)})},mceTableCopyCol:function(n){return s().each(function(n){return e.setColumns(n)})},mceTableCopyRow:function(n){return i().each(function(n){return e.setRows(n)})},mceTablePasteColBefore:function(n){return d(r.pasteColsBefore,e.getColumns)},mceTablePasteColAfter:function(n){return d(r.pasteColsAfter,e.getColumns)},mceTablePasteRowBefore:function(n){return d(r.pasteRowsBefore,e.getRows)},mceTablePasteRowAfter:function(n){return d(r.pasteRowsAfter,e.getRows)},mceTableDelete:function(){return uf(c).each(function(n){ot(n,o).filter(g(o)).each(function(n){var e,t=In.fromText("");Tt(n,t),Et(n),c.dom.isEmpty(c.getBody())?(c.setContent(""),c.selection.setCursorLocation()):((e=c.dom.createRng()).setStart(t.dom,0),e.setEnd(t.dom,0),c.selection.setRng(e),c.nodeChanged())})})},mceTableSizingMode:function(n,e){return t=e,uf(c).each(function(n){Vi(c)||qi(c)||Hi(c)||ot(n,o).each(function(n){"relative"!==t||Au(n)?"fixed"!==t||Iu(n)?"responsive"!==t||Bu(n)||vc(n):hc(c,n):pc(c,n),ec(n),Ii(c,n.dom)})});var t}},function(n,e){return c.addCommand(e,n)});var m=function(e,n){n.each(function(n){Ii(e,n.dom)})};zn({mceTableCellType:function(n,e){var t=ot(oc(c),o);r.setTableCellType(c,e),m(c,t)},mceTableRowType:function(n,e){var t=ot(oc(c),o);r.setTableRowType(c,e),m(c,t)}},function(n,e){return c.addCommand(e,n)}),c.addCommand("mceTableColType",function(n,e){return qn(e,"type").each(function(n){return u("th"===n?r.makeColumnsHeader:r.unmakeColumnsHeader)})}),zn({mceTableProps:w(of,c,!1),mceTableRowProps:w(nf,c),mceTableCellProps:w(Xl,c,a)},function(n,e){return c.addCommand(e,function(){return n()})}),c.addCommand("mceInsertTable",function(n,e){h(e)&&0<jn(e).length?yl(c,e.rows,e.columns,e.options,"Invalid values for mceInsertTable - rows and columns values are required to insert a table."):of(c,!0)}),c.addCommand("mceTableApplyCellStyle",function(n,e){var r,t,o=function(n){return"tablecell"+n.toLowerCase().replace("-","")};!h(e)||0!==(r=sl(oc(c),a)).length&&(function(n){for(var e in n)if(_n.call(n,e))return!1;return!0}(t=Ln(e,function(n,e){return c.formatter.has(o(e))&&p(n)}))||(zn(t,function(e,t){P(r,function(n){Vl.normal(c,n.dom).setFormat(o(t),e)})}),l(r[0]).each(function(n){return Ii(c,n.dom,{structure:!1,style:!0})})))})},lf=function(t,r,o){var n=Zi(t);zn({mceTableRowType:function(){return r.getTableRowType(t)},mceTableCellType:function(){return r.getTableCellType(t)},mceTableColType:function(){return ll(oc(t)).bind(function(t){return ot(t,n).map(function(n){var e=_r(o,n,t);return r.getTableColType(n,e)})}).getOr("")}},function(n,e){return t.addQueryValueHandler(e,n)})},ff=function(){var e=uc(x.none()),t=uc(x.none()),r=function(n){n.set(x.none())};return{getRows:e.get,setRows:function(n){e.set(n),r(t)},clearRows:function(){return r(e)},getColumns:t.get,setColumns:function(n){t.set(n),r(e)},clearColumns:function(){return r(t)}}},sf={tablecellbackgroundcolor:{selector:"td,th",styles:{backgroundColor:"%value"},remove_similar:!0},tablecellbordercolor:{selector:"td,th",styles:{borderColor:"%value"},remove_similar:!0},tablecellborderstyle:{selector:"td,th",styles:{borderStyle:"%value"},remove_similar:!0},tablecellborderwidth:{selector:"td,th",styles:{borderWidth:"%value"},remove_similar:!0}},df=function(n){n.formatter.register(sf)},mf=er([{none:["current"]},{first:["current"]},{middle:["current","target"]},{last:["current"]}]),gf=U(U({},mf),{none:function(n){return void 0===n&&(n=undefined),mf.none(n)}}),pf=function(t,n){return ot(t,n).bind(function(n){var e=tt(n);return _(e,function(n){return Pn(t,n)}).map(function(n){return{index:n,all:e}})})},hf=function(n,e,t,r){return{start:n,soffset:e,finish:t,foffset:r}},vf=er([{before:["element"]},{on:["element","offset"]},{after:["element"]}]),bf={before:vf.before,on:vf.on,after:vf.after,cata:function(n,e,t,r){return n.fold(e,t,r)},getStart:function(n){return n.fold(d,d,d)}},wf=er([{domRange:["rng"]},{relative:["startSitu","finishSitu"]},{exact:["start","soffset","finish","foffset"]}]),yf={domRange:wf.domRange,relative:wf.relative,exact:wf.exact,exactFromRange:function(n){return wf.exact(n.start,n.soffset,n.finish,n.foffset)},getWin:function(n){var e,t=n.match({domRange:function(n){return In.fromDom(n.startContainer)},relative:function(n,e){return bf.getStart(n)},exact:function(n,e,t,r){return n}});return e=t,In.fromDom(ue(e).dom.defaultView)},range:hf},Cf=function(n,e){return n.selectNodeContents(e.dom)},xf=function(n,e,t){var r,o,u=n.document.createRange();return r=u,e.fold(function(n){r.setStartBefore(n.dom)},function(n,e){r.setStart(n.dom,e)},function(n){r.setStartAfter(n.dom)}),o=u,t.fold(function(n){o.setEndBefore(n.dom)},function(n,e){o.setEnd(n.dom,e)},function(n){o.setEndAfter(n.dom)}),u},Sf=function(n,e,t,r,o){var u=n.document.createRange();return u.setStart(e.dom,t),u.setEnd(r.dom,o),u},Tf=function(n){return{left:n.left,top:n.top,right:n.right,bottom:n.bottom,width:n.width,height:n.height}},Rf=er([{ltr:["start","soffset","finish","foffset"]},{rtl:["start","soffset","finish","foffset"]}]),Of=function(n,e,t){return e(In.fromDom(t.startContainer),t.startOffset,In.fromDom(t.endContainer),t.endOffset)},Df=function(n,e){var o,t,r,u=(o=n,e.match({domRange:function(n){return{ltr:b(n),rtl:x.none}},relative:function(n,e){return{ltr:K(function(){return xf(o,n,e)}),rtl:K(function(){return x.some(xf(o,e,n))})}},exact:function(n,e,t,r){return{ltr:K(function(){return Sf(o,n,e,t,r)}),rtl:K(function(){return x.some(Sf(o,t,r,n,e))})}}}));return(r=(t=u).ltr()).collapsed?t.rtl().filter(function(n){return!1===n.collapsed}).map(function(n){return Rf.rtl(In.fromDom(n.endContainer),n.endOffset,In.fromDom(n.startContainer),n.startOffset)}).getOrThunk(function(){return Of(0,Rf.ltr,r)}):Of(0,Rf.ltr,r)},Af=function(u,n){return Df(u,n).match({ltr:function(n,e,t,r){var o=u.document.createRange();return o.setStart(n.dom,e),o.setEnd(t.dom,r),o},rtl:function(n,e,t,r){var o=u.document.createRange();return o.setStart(t.dom,r),o.setEnd(n.dom,e),o}})},If=(Rf.ltr,Rf.rtl,function(n,e,t){return e>=n.left&&e<=n.right&&t>=n.top&&t<=n.bottom}),Bf=function(t,r,n,e,o){var u=function(n){var e=t.dom.createRange();return e.setStart(r.dom,n),e.collapse(!0),e},i=_t(r).length,c=function(n,e,t,r,o){if(0===o)return 0;if(e===r)return o-1;for(var u=r,i=1;i<o;i++){var c=n(i),a=Math.abs(e-c.left);if(t<=c.bottom){if(t<c.top||u<a)return i-1;u=a}}return 0}(function(n){return u(n).getBoundingClientRect()},n,e,o.right,i);return u(c)},Ef=function(n,e,t,r){return ee(e)?function(e,t,r,o){var n=e.dom.createRange();n.selectNode(t.dom);var u=n.getClientRects();return V(u,function(n){return If(n,r,o)?x.some(n):x.none()}).map(function(n){return Bf(e,t,r,o,n)})}(n,e,t,r):(u=e,i=t,c=r,a=(o=n).dom.createRange(),l=fe(u),V(l,function(n){return a.selectNode(n.dom),If(a.getBoundingClientRect(),i,c)?Ef(o,n,i,c):x.none()}));var o,u,i,c,a,l},Pf=function(n,e){return e-n.left<n.right-e},kf=function(n,e,t){var r=n.dom.createRange();return r.selectNode(e.dom),r.collapse(t),r},Mf=function(e,n,t){var r=e.dom.createRange();r.selectNode(n.dom);var o=r.getBoundingClientRect(),u=Pf(o,t);return(!0===u?gr:pr)(n).map(function(n){return kf(e,n,u)})},Nf=function(n,e,t){var r=e.dom.getBoundingClientRect(),o=Pf(r,t);return x.some(kf(n,e,o))},jf=function(t,n,e){return x.from(t.dom.caretPositionFromPoint(n,e)).bind(function(n){if(null===n.offsetNode)return x.none();var e=t.dom.createRange();return e.setStart(n.offsetNode,n.offset),e.collapse(),x.some(e)})},_f=function(n,e,t){return x.from(n.dom.caretRangeFromPoint(e,t))},zf=function(n,e,t,r){var o=n.dom.createRange();o.selectNode(e.dom);var u=o.getBoundingClientRect();return function(n,e,t,r){var o=n.dom.createRange();o.selectNode(e.dom);var u=o.getBoundingClientRect(),i=Math.max(u.left,Math.min(u.right,t)),c=Math.max(u.top,Math.min(u.bottom,r));return Ef(n,e,i,c)}(n,e,Math.max(u.left,Math.min(u.right,t)),Math.max(u.top,Math.min(u.bottom,r)))},Wf=function(o,u,e){return In.fromPoint(o,u,e).bind(function(r){var n=function(){return n=o,t=u,(0===fe(e=r).length?Nf:Mf)(n,e,t);var n,e,t};return 0===fe(r).length?n():zf(o,r,u,e).orThunk(n)})},Ff=document.caretPositionFromPoint?jf:document.caretRangeFromPoint?_f:Wf,Lf=function(n,e){var t=Yn(n);return"input"===t?bf.after(n):A(["br","img"],t)?0===e?bf.before(n):bf.after(n):bf.on(n,e)},Hf=function(n,e){var t=n.fold(bf.before,Lf,bf.after),r=e.fold(bf.before,Lf,bf.after);return yf.relative(t,r)},qf=function(n,e,t,r){var o=Lf(n,e),u=Lf(t,r);return yf.relative(o,u)},Vf=function(n,e,t,r){var o,u,i,c,a,l=(u=e,i=t,c=r,(a=oe(o=n).dom.createRange()).setStart(o.dom,u),a.setEnd(i.dom,c),a),f=Pn(n,t)&&e===r;return l.collapsed&&!f},Uf=function(n){return x.from(n.getSelection())},$f=function(n,e){Uf(n).each(function(n){n.removeAllRanges(),n.addRange(e)})},Gf=function(n,e,t,r,o){var u=Sf(n,e,t,r,o);$f(n,u)},Kf=function(s,n){return Df(s,n).match({ltr:function(n,e,t,r){Gf(s,n,e,t,r)},rtl:function(c,a,l,f){Uf(s).each(function(n){if(n.setBaseAndExtent)n.setBaseAndExtent(c.dom,a,l.dom,f);else if(n.extend)try{t=c,r=a,o=l,u=f,(e=n).collapse(t.dom,r),e.extend(o.dom,u)}catch(i){Gf(s,l,f,c,a)}else Gf(s,l,f,c,a);var e,t,r,o,u})}})},Xf=function(n,e,t,r,o){var u=qf(e,t,r,o);Kf(n,u)},Yf=function(n,e,t){var r=Hf(e,t);Kf(n,r)},Jf=function(n){var o=yf.getWin(n).dom,e=function(n,e,t,r){return Sf(o,n,e,t,r)},t=n.match({domRange:function(n){var e=In.fromDom(n.startContainer),t=In.fromDom(n.endContainer);return qf(e,n.startOffset,t,n.endOffset)},relative:Hf,exact:qf});return Df(o,t).match({ltr:e,rtl:e})},Qf=function(n){if(0<n.rangeCount){var e=n.getRangeAt(0),t=n.getRangeAt(n.rangeCount-1);return x.some(hf(In.fromDom(e.startContainer),e.startOffset,In.fromDom(t.endContainer),t.endOffset))}return x.none()},Zf=function(n){if(null===n.anchorNode||null===n.focusNode)return Qf(n);var e=In.fromDom(n.anchorNode),t=In.fromDom(n.focusNode);return Vf(e,n.anchorOffset,t,n.focusOffset)?x.some(hf(e,n.anchorOffset,t,n.focusOffset)):Qf(n)},ns=function(n,e){var t,r,o=(t=e,r=n.document.createRange(),Cf(r,t),r);$f(n,o)},es=function(n){return Uf(n).filter(function(n){return 0<n.rangeCount}).bind(Zf).map(function(n){return yf.exact(n.start,n.soffset,n.finish,n.foffset)})},ts=function(n,e){var t,r,o,u=Af(n,e);return r=(t=u).getClientRects(),0<(o=0<r.length?r[0]:t.getBoundingClientRect()).width||0<o.height?x.some(o).map(Tf):x.none()},rs=function(n,e,t){return r=n,o=e,u=t,i=In.fromDom(r.document),Ff(i,o,u).map(function(n){return hf(In.fromDom(n.startContainer),n.startOffset,In.fromDom(n.endContainer),n.endOffset)});var r,o,u,i},os=tinymce.util.Tools.resolve("tinymce.util.VK"),us=function(n,e,t,r){return as(n,e,pf(o=t,u).fold(function(){return gf.none(o)},function(n){return n.index+1<n.all.length?gf.middle(o,n.all[n.index+1]):gf.last(o)}),r);var o,u},is=function(n,e,t,r){return as(n,e,pf(o=t,u).fold(function(){return gf.none()},function(n){return 0<=n.index-1?gf.middle(o,n.all[n.index-1]):gf.first(o)}),r);var o,u},cs=function(n,e){var t=yf.exact(e,0,e,0);return Jf(t)},as=function(o,n,e,u){return e.fold(x.none,x.none,function(n,e){return gr(e).map(function(n){return cs(0,n)})},function(r){return ot(r,n).bind(function(n){var e,t=jr(r);return o.undoManager.transact(function(){u.insertRowsAfter(n,t)}),e=Te(n,"tr"),q(e).bind(function(n){return Ie(n,"td,th").map(function(n){return cs(0,n)})})})})},ls=["table","li","dl"],fs=function(e,t,r){var o,u,n,i;e.keyCode===os.TAB&&(o=Yi(t),u=function(n){var e=Yn(n);return Pn(n,o)||A(ls,e)},(n=t.selection.getRng()).collapsed&&(i=In.fromDom(n.startContainer),et(i,u).each(function(n){e.preventDefault(),(e.shiftKey?is:us)(t,u,n,r).each(function(n){t.selection.setRng(n)})})))},ss=function(n,e){return{selection:n,kill:e}},ds=function(n,e,t,r){return{start:bf.on(n,e),finish:bf.on(t,r)}},ms=function(n,e){var t=Af(n,e);return hf(In.fromDom(t.startContainer),t.startOffset,In.fromDom(t.endContainer),t.endOffset)},gs=ds,ps=function(t,n,r,e,o){return Pn(r,e)?x.none():Xt(r,e,n).bind(function(n){var e=n.boxes.getOr([]);return 0<e.length?(o(t,e,n.start,n.finish),x.some(ss(x.some(gs(r,0,r,sr(r))),!0))):x.none()})},hs=function(n,e){return{item:n,mode:e}},vs=function(n,e,t,r){return void 0===r&&(r=bs),n.property().parent(e).map(function(n){return hs(n,r)})},bs=function(n,e,t,r){return void 0===r&&(r=ws),t.sibling(n,e).map(function(n){return hs(n,r)})},ws=function(n,e,t,r){void 0===r&&(r=ws);var o=n.property().children(e);return t.first(o).map(function(n){return hs(n,r)})},ys=[{current:vs,next:bs,fallback:x.none()},{current:bs,next:ws,fallback:x.some(vs)},{current:ws,next:ws,fallback:x.some(bs)}],Cs=function(e,t,r,o,n){return void 0===n&&(n=ys),j(n,function(n){return n.current===r}).bind(function(n){return n.current(e,t,o,n.next).orThunk(function(){return n.fallback.bind(function(n){return Cs(e,t,n,o)})})})},xs=function(){return{sibling:function(n,e){return n.query().prevSibling(e)},first:function(n){return 0<n.length?x.some(n[n.length-1]):x.none()}}},Ss=function(){return{sibling:function(n,e){return n.query().nextSibling(e)},first:function(n){return 0<n.length?x.some(n[0]):x.none()}}},Ts=function(e,n,t,r,o,u){return Cs(e,n,r,o).bind(function(n){return u(n.item)?x.none():t(n.item)?x.some(n.item):Ts(e,n.item,t,n.mode,o,u)})},Rs=function(e){return function(n){return 0===e.property().children(n).length}},Os=function(n,e,t,r){return Ts(n,e,t,bs,xs(),r)},Ds=function(n,e,t,r){return Ts(n,e,t,bs,Ss(),r)},As=Lt(),Is=function(n,e){return Os(t=As,n,Rs(t),e);var t},Bs=function(n,e){return Ds(t=As,n,Rs(t),e);var t},Es=er([{none:["message"]},{success:[]},{failedUp:["cell"]},{failedDown:["cell"]}]),Ps=function(n){return Be(n,"tr")},ks=U(U({},Es),{verify:function(c,n,e,t,r,a,o){return Be(t,"td,th",o).bind(function(i){return Be(n,"td,th",o).map(function(u){return Pn(i,u)?Pn(t,i)&&sr(i)===r?a(u):Es.none("in same cell"):Gt(Ps,[i,u]).fold(function(){return e=u,t=i,r=(n=c).getRect(e),(o=n.getRect(t)).right>r.left&&o.left<r.right?Es.success():a(u);var n,e,t,r,o},function(n){return a(u)})})}).getOr(Es.none("default"))},cata:function(n,e,t,r,o){return n.fold(e,t,r,o)}}),Ms=function(n,e){return _(n,w(Pn,e))},Ns=function(n){return"br"===Yn(n)},js=function(n,e,t){return e(n,t).bind(function(n){return ee(n)&&0===_t(n).trim().length?js(n,e,t):x.some(n)})},_s=function(e,n,t,r){return se(o=n,u=t).filter(Ns).orThunk(function(){return se(o,u-1).filter(Ns)}).bind(function(n){return r.traverse(n).fold(function(){return js(n,r.gather,e).map(r.relative)},function(n){return ie(r=n).bind(function(e){var t=fe(e);return Ms(t,r).map(function(n){return{parent:e,children:t,element:r,index:n}})}).map(function(n){return bf.on(n.parent,n.index)});var r})});var o,u},zs=function(n,e,t,r){var o,u,i;return(Ns(e)?(o=n,u=e,(i=r).traverse(u).orThunk(function(){return js(u,i.gather,o)}).map(i.relative)):_s(n,e,t,r)).map(function(n){return{start:n,finish:n}})},Ws=function(n,e){return{left:n.left,top:n.top+e,right:n.right,bottom:n.bottom+e}},Fs=function(n,e){return{left:n.left,top:n.top-e,right:n.right,bottom:n.bottom-e}},Ls=function(n,e,t){return{left:n.left+e,top:n.top+t,right:n.right+e,bottom:n.bottom+t}},Hs=function(n){return{left:n.left,top:n.top,right:n.right,bottom:n.bottom}},qs=function(n,e){return x.some(n.getRect(e))},Vs=function(n,e,t){return ne(e)?qs(n,e).map(Hs):ee(e)?(r=n,o=e,(0<=(u=t)&&u<sr(o)?r.getRangedRect(o,u,o,u+1):0<u?r.getRangedRect(o,u-1,o,u):x.none()).map(Hs)):x.none();var r,o,u},Us=function(n,e){return ne(e)?qs(n,e).map(Hs):ee(e)?n.getRangedRect(e,0,e,sr(e)).map(Hs):x.none()},$s=er([{none:[]},{retry:["caret"]}]),Gs=function(e,n,r){return Re(function(n,e){return e(n)},Oe,n,Ra,t).fold(f,function(n){return Us(e,n).exists(function(n){return t=n,(e=r).left<t.left||Math.abs(t.right-e.left)<1||e.left>t.right;var e,t})});var t},Ks={point:function(n){return n.bottom},adjuster:function(n,e,t,r,o){var u=Ws(o,5);return Math.abs(t.bottom-r.bottom)<1||t.top>o.bottom?$s.retry(u):t.top===o.bottom?$s.retry(Ws(o,1)):Gs(n,e,o)?$s.retry(Ls(u,5,0)):$s.none()},move:Ws,gather:Bs},Xs=function(t,r,o,u,i){return 0===i?x.some(u):(a=t,l=u.left,f=r.point(u),a.elementFromPoint(l,f).filter(function(n){return"table"===Yn(n)}).isSome()?(e=u,c=i-1,Xs(t,n=r,o,n.move(e,5),c)):t.situsFromPoint(u.left,r.point(u)).bind(function(n){return n.start.fold(x.none,function(e){return Us(t,e).bind(function(n){return r.adjuster(t,e,n,o,u).fold(x.none,function(n){return Xs(t,r,o,n,i-1)})}).orThunk(function(){return x.some(u)})},x.none)}));var n,e,c,a,l,f},Ys=function(e,t,n){var r,o,u,i=e.move(n,5),c=Xs(t,e,n,i,100).getOr(i);return o=c,u=t,((r=e).point(o)>u.getInnerHeight()?x.some(r.point(o)-u.getInnerHeight()):r.point(o)<0?x.some(-r.point(o)):x.none()).fold(function(){return t.situsFromPoint(c.left,e.point(c))},function(n){return t.scrollBy(0,n),t.situsFromPoint(c.left,e.point(c)-n)})},Js={tryUp:w(Ys,{point:function(n){return n.top},adjuster:function(n,e,t,r,o){var u=Fs(o,5);return Math.abs(t.top-r.top)<1||t.bottom<o.top?$s.retry(u):t.bottom===o.top?$s.retry(Fs(o,1)):Gs(n,e,o)?$s.retry(Ls(u,5,0)):$s.none()},move:Fs,gather:Is}),tryDown:w(Ys,Ks),ieTryUp:function(n,e){return n.situsFromPoint(e.left,e.top-5)},ieTryDown:function(n,e){return n.situsFromPoint(e.left,e.bottom+5)},getJumpSize:b(5)},Qs=function(u,i,c){return u.getSelection().bind(function(o){return zs(i,o.finish,o.foffset,c).fold(function(){return x.some(xc(o.finish,o.foffset))},function(n){var e,t=u.fromSitus(n),r=ks.verify(u,o.finish,o.foffset,t.finish,t.foffset,c.failure,i);return e=r,ks.cata(e,function(n){return x.none()},function(){return x.none()},function(n){return x.some(xc(n,0))},function(n){return x.some(xc(n,sr(n)))})})})},Zs=function(r,o,u,i,c,a){return 0===a?x.none():td(r,o,u,i,c).bind(function(n){var e=r.fromSitus(n),t=ks.verify(r,u,i,e.finish,e.foffset,c.failure,o);return ks.cata(t,function(){return x.none()},function(){return x.some(n)},function(n){return Pn(u,n)&&0===i?nd(r,u,i,Fs,c):Zs(r,o,n,0,c,a-1)},function(n){return Pn(u,n)&&i===sr(n)?nd(r,u,i,Ws,c):Zs(r,o,n,sr(n),c,a-1)})})},nd=function(e,n,t,r,o){return Vs(e,n,t).bind(function(n){return ed(e,o,r(n,Js.getJumpSize()))})},ed=function(n,e,t){var r=Dn().browser;return r.isChrome()||r.isSafari()||r.isFirefox()||r.isEdge()?e.otherRetry(n,t):r.isIE()?e.ieRetry(n,t):x.none()},td=function(e,n,t,r,o){return Vs(e,t,r).bind(function(n){return ed(e,o,n)})},rd=function(n,e){return Oe(n,function(n){return ie(n).exists(function(n){return Pn(n,e)})},t).isSome();var t},od=function(u,i,c,n,a){return Be(n,"td,th",i).bind(function(o){return Be(o,"table",i).bind(function(n){return rd(a,n)?Qs(e=u,t=i,r=c).bind(function(n){return Zs(e,t,n.element,n.offset,r,20).map(e.fromSitus)}).bind(function(e){return Be(e.finish,"td,th",i).map(function(n){return{start:o,finish:n,range:e}})}):x.none();var e,t,r})})},ud=function(n,e,t,r,o,u){return Dn().browser.isIE()?x.none():u(r,e).orThunk(function(){return od(n,e,t,r,o).map(function(n){var e=n.range;return ss(x.some(gs(e.start,e.soffset,e.finish,e.foffset)),!0)})})},id=function(n,r){return Be(n,"tr",r).bind(function(t){return Be(t,"table",r).bind(function(n){var e=Te(n,"tr");return Pn(t,e[0])?Os(As,n,function(n){return pr(n).isSome()},r).map(function(n){var e=sr(n);return ss(x.some(gs(n,e,n,e)),!0)}):x.none()})})},cd=function(n,r){return Be(n,"tr",r).bind(function(t){return Be(t,"table",r).bind(function(n){var e=Te(n,"tr");return Pn(t,e[e.length-1])?Ds(As,n,function(n){return gr(n).isSome()},r).map(function(n){return ss(x.some(gs(n,0,n,0)),!0)}):x.none()})})},ad=function(n,e,t,r,o,u,i){return od(n,t,r,o,u).bind(function(n){return ps(e,t,n.start,n.finish,i)})},ld=function(n,e){return Be(n,"td,th",e)};function fd(o,u,e,i){var t,r=(t=uc(x.none()),{clear:function(){return t.set(x.none())},set:function(n){return t.set(x.some(n))},isSet:function(){return t.get().isSome()},on:function(n){return t.get().each(n)}}),c=r.clear;return{clearstate:c,mousedown:function(n){i.clear(u),ld(n.target,e).each(r.set)},mouseover:function(n){r.on(function(r){i.clearBeforeUpdate(u),ld(n.target,e).each(function(t){Xt(r,t,e).each(function(n){var e=n.boxes.getOr([]);(1<e.length||1===e.length&&!Pn(r,t))&&(i.selectRange(u,e,n.start,n.finish),o.selectContents(t))})})})},mouseup:function(n){c()}}}var sd={traverse:le,gather:Bs,relative:bf.before,otherRetry:Js.tryDown,ieRetry:Js.ieTryDown,failure:ks.failedDown},dd={traverse:ae,gather:Is,relative:bf.before,otherRetry:Js.tryUp,ieRetry:Js.ieTryUp,failure:ks.failedUp},md=function(e){return function(n){return n===e}},gd=md(38),pd=md(40),hd=function(n){return 37<=n&&n<=40},vd={isBackward:md(37),isForward:md(39)},bd={isBackward:md(39),isForward:md(37)},wd=function(c){return{elementFromPoint:function(n,e){return In.fromPoint(In.fromDom(c.document),n,e)},getRect:function(n){return n.dom.getBoundingClientRect()},getRangedRect:function(n,e,t,r){var o=yf.exact(n,e,t,r);return ts(c,o)},getSelection:function(){return es(c).map(function(n){return ms(c,n)})},fromSitus:function(n){var e=yf.relative(n.start,n.finish);return ms(c,e)},situsFromPoint:function(n,e){return rs(c,n,e).map(function(n){return ds(n.start,n.soffset,n.finish,n.foffset)})},clearSelection:function(){Uf(c).each(function(n){return n.removeAllRanges()})},collapseSelection:function(i){void 0===i&&(i=!1),es(c).each(function(n){return n.fold(function(n){return n.collapse(i)},function(n,e){var t=i?n:e;Yf(c,t,t)},function(n,e,t,r){var o=i?n:t,u=i?e:r;Xf(c,o,u,o,u)})})},setSelection:function(n){Xf(c,n.start,n.soffset,n.finish,n.foffset)},setRelativeSelection:function(n,e){Yf(c,n,e)},selectContents:function(n){ns(c,n)},getInnerHeight:function(){return c.innerHeight},getScrollY:function(){var n,e,t,r;return(n=In.fromDom(c.document),e=n!==undefined?n.dom:document,t=e.body.scrollLeft||e.documentElement.scrollLeft,r=e.body.scrollTop||e.documentElement.scrollTop,ao(t,r)).top},scrollBy:function(n,e){var t,r,o,u;t=n,r=e,o=In.fromDom(c.document),(u=(o!==undefined?o.dom:document).defaultView)&&u.scrollBy(t,r)}}},yd=function(n,e){return{rows:n,cols:e}},Cd=function(n,e,t,r){var o=fd(wd(n),e,t,r);return{clearstate:o.clearstate,mousedown:o.mousedown,mouseover:o.mouseover,mouseup:o.mouseup}},xd=function(n,g,p,h){var l=wd(n),f=function(){return h.clear(g),x.none()};return{keydown:function(n,e,t,r,o,i){var u=n.raw,c=u.which,a=!0===u.shiftKey;return Yt(g,h.selectedSelector).fold(function(){return pd(c)&&a?w(ad,l,g,p,sd,r,e,h.selectRange):gd(c)&&a?w(ad,l,g,p,dd,r,e,h.selectRange):pd(c)?w(ud,l,p,sd,r,e,cd):gd(c)?w(ud,l,p,dd,r,e,id):x.none},function(u){var n=function(n){return function(){return V(n,function(n){return e=n.rows,t=n.cols,r=g,Qt(u,e,t,(o=h).firstSelectedSelector,o.lastSelectedSelector).map(function(n){return o.clearBeforeUpdate(r),o.selectRange(r,n.boxes,n.start,n.finish),n.boxes});var e,t,r,o}).fold(function(){return Jt(g,h.firstSelectedSelector,h.lastSelectedSelector).map(function(n){var e=pd(c)||i.isForward(c)?bf.after:bf.before;return l.setRelativeSelection(bf.on(n.first,0),e(n.table)),h.clear(g),ss(x.none(),!0)})},function(n){return x.some(ss(x.none(),!0))})}};return pd(c)&&a?n([yd(1,0)]):gd(c)&&a?n([yd(-1,0)]):i.isBackward(c)&&a?n([yd(0,-1),yd(-1,0)]):i.isForward(c)&&a?n([yd(0,1),yd(1,0)]):hd(c)&&!1==a?f:x.none})()},keyup:function(l,f,s,d,m){return Yt(g,h.selectedSelector).fold(function(){var t,r,n,e,o,u,i,c=l.raw,a=c.which;return!1!=(!0===c.shiftKey)&&hd(a)?(t=g,r=p,n=f,e=s,o=d,u=m,i=h.selectRange,Pn(n,o)&&e===u?x.none():Be(n,"td,th",r).bind(function(e){return Be(o,"td,th",r).bind(function(n){return ps(t,r,e,n,i)})})):x.none()},x.none)}}},Sd=function(n,r,e,o){var u=wd(n);return function(n,t){o.clearBeforeUpdate(r),Xt(n,t,e).each(function(n){var e=n.boxes.getOr([]);o.selectRange(r,e,n.start,n.finish),u.selectContents(t),u.collapseSelection()})}},Td=function(r,n){P(n,function(n){var e,t;t=n,Qu(e=r)?e.dom.classList.remove(t):ni(e,t),ti(e)})},Rd={byClass:function(o){var e,t,u=(e=o.selected,function(n){ei(n,e)}),r=(t=[o.selected,o.lastSelected,o.firstSelected],function(n){Td(n,t)}),i=function(n){var e=Te(n,o.selectedSelector);P(e,r)};return{clearBeforeUpdate:i,clear:i,selectRange:function(n,e,t,r){i(n),P(e,u),ei(t,o.firstSelected),ei(r,o.lastSelected)},selectedSelector:o.selectedSelector,firstSelectedSelector:o.firstSelectedSelector,lastSelectedSelector:o.lastSelectedSelector}},byAttr:function(o,u,e){var t=function(n){je(n,o.selected),je(n,o.firstSelected),je(n,o.lastSelected)},i=function(n){Pe(n,o.selected,"1")},c=function(n){r(n),e()},r=function(n){var e=Te(n,o.selectedSelector);P(e,t)};return{clearBeforeUpdate:r,clear:c,selectRange:function(n,e,t,r){c(n),P(e,i),Pe(t,o.firstSelected,"1"),Pe(r,o.lastSelected,"1"),u(e,t,r)},selectedSelector:o.selectedSelector,firstSelectedSelector:o.firstSelectedSelector,lastSelectedSelector:o.lastSelectedSelector}}},Od=function(n,e,s){var d=mt.fromTable(n);return Xc(d,e).map(function(n){var t,e,r,o,u,i,c,a,l,f=Hc(d,s,!1);return{upOrLeftCells:(t=n,e=s,r=f.slice(0,t[t.length-1].row+1),o=Vc(r,e),W(o,function(n){var e=n.cells.slice(0,t[t.length-1].column+1);return E(e,function(n){return n.element})})),downOrRightCells:(i=n,c=s,a=(u=f).slice(i[0].row+i[0].rowspan-1,u.length),l=Vc(a,c),W(l,function(n){var e=n.cells.slice(i[0].column+i[0].colspan-1,n.cells.length);return E(e,function(n){return n.element})}))}})},Dd=function(n){return!1===ri(In.fromDom(n.target),"ephox-snooker-resizer-bar")};function Ad(d,m,n){var g=Rd.byAttr(Nr,function(u,i,c){n.targets().each(function(o){ot(i).each(function(n){var e=Ki(d),t=Ar(y,In.fromDom(d.getDoc()),e),r=Od(n,o,t);Di(d,u,i,c,r)})})},function(){return Ai(d)});return d.on("init",function(n){var r=d.getWin(),o=Yi(d),e=Zi(d),t=Cd(r,o,e,g),i=xd(r,o,e,g),u=Sd(r,o,e,g);d.on("TableSelectorChange",function(n){return u(n.start,n.finish)});var c,a,l=function(n,e){!0===n.raw.shiftKey&&(e.kill&&n.kill(),e.selection.each(function(n){var e=yf.relative(n.start,n.finish),t=Af(r,e);d.selection.setRng(t)}))},f=function(n){return 0===n.button},s=(c=uc(In.fromDom(o)),a=uc(0),{touchEnd:function(n){var e,t,r=In.fromDom(n.target);"td"!==Yn(r)&&"th"!==Yn(r)||(e=c.get(),t=a.get(),Pn(e,r)&&n.timeStamp-t<300&&(n.preventDefault(),u(r,r))),c.set(r),a.set(n.timeStamp)}});d.on("dragstart",function(n){t.clearstate()}),d.on("mousedown",function(n){f(n)&&Dd(n)&&t.mousedown(Yu(n))}),d.on("mouseover",function(n){var e;((e=n).buttons===undefined||ef.browser.isEdge()&&0===e.buttons||0!=(1&e.buttons))&&Dd(n)&&t.mouseover(Yu(n))}),d.on("mouseup",function(n){f(n)&&Dd(n)&&t.mouseup(Yu(n))}),d.on("touchend",s.touchEnd),d.on("keyup",function(n){var e,t,r,o=Yu(n);o.raw.shiftKey&&hd(o.raw.which)&&(e=d.selection.getRng(),t=In.fromDom(e.startContainer),r=In.fromDom(e.endContainer),i.keyup(o,t,e.startOffset,r,e.endOffset).each(function(n){l(o,n)}))}),d.on("keydown",function(n){var e=Yu(n);m().each(function(n){return n.hideBars()});var t=d.selection.getRng(),r=In.fromDom(t.startContainer),o=In.fromDom(t.endContainer),u=to(vd,bd)(In.fromDom(d.selection.getStart()));i.keydown(e,r,t.startOffset,o,t.endOffset,u).each(function(n){l(e,n)}),m().each(function(n){return n.showBars()})}),d.on("NodeChange",function(){var n=d.selection,e=In.fromDom(n.getStart()),t=In.fromDom(n.getEnd());Gt(ot,[e,t]).fold(function(){return g.clear(o)},y)})}),{clear:g.clear}}var Id=function(n,t){var o=uc(x.none()),u=uc([]),e=function(){return fl(oc(n)).bind(function(e){var n=ot(e);return n.map(function(n){return"caption"===Yn(e)?jr(e):_r(t,n,e)})})},r=function(){o.set(K(e)()),P(u.get(),function(n){return n()})},i=function(e,t){var r=function(){return o.get().fold(function(){e.setDisabled(!0)},function(n){e.setDisabled(t(n))})};return r(),u.set(u.get().concat([r])),function(){u.set(k(u.get(),function(n){return n!==r}))}};return n.on("NodeChange ExecCommand TableSelectorChange",r),{onSetupTable:function(n){return i(n,function(n){return!1})},onSetupCellOrRow:function(n){return i(n,function(n){return"caption"===Yn(n.element)})},onSetupPasteable:function(e){return function(n){return i(n,function(n){return"caption"===Yn(n.element)||e().isNone()})}},onSetupMergeable:function(n){return i(n,function(n){return n.mergable.isNone()})},onSetupUnmergeable:function(n){return i(n,function(n){return n.unmergable.isNone()})},resetTargets:r,targets:function(){return o.get()}}},Bd=function(e,n,t){e.ui.registry.addMenuButton("table",{tooltip:"Table",icon:"table",fetch:function(n){return n("inserttable | cell row column | advtablesort | tableprops deletetable")}});var r=function(n){return function(){return e.execCommand(n)}};e.ui.registry.addButton("tableprops",{tooltip:"Table properties",onAction:r("mceTableProps"),icon:"table",onSetup:n.onSetupTable}),e.ui.registry.addButton("tabledelete",{tooltip:"Delete table",onAction:r("mceTableDelete"),icon:"table-delete-table",onSetup:n.onSetupTable}),e.ui.registry.addButton("tablecellprops",{tooltip:"Cell properties",onAction:r("mceTableCellProps"),icon:"table-cell-properties",onSetup:n.onSetupCellOrRow}),e.ui.registry.addButton("tablemergecells",{tooltip:"Merge cells",onAction:r("mceTableMergeCells"),icon:"table-merge-cells",onSetup:n.onSetupMergeable}),e.ui.registry.addButton("tablesplitcells",{tooltip:"Split cell",onAction:r("mceTableSplitCells"),icon:"table-split-cells",onSetup:n.onSetupUnmergeable}),e.ui.registry.addButton("tableinsertrowbefore",{tooltip:"Insert row before",onAction:r("mceTableInsertRowBefore"),icon:"table-insert-row-above",onSetup:n.onSetupCellOrRow}),e.ui.registry.addButton("tableinsertrowafter",{tooltip:"Insert row after",onAction:r("mceTableInsertRowAfter"),icon:"table-insert-row-after",onSetup:n.onSetupCellOrRow}),e.ui.registry.addButton("tabledeleterow",{tooltip:"Delete row",onAction:r("mceTableDeleteRow"),icon:"table-delete-row",onSetup:n.onSetupCellOrRow}),e.ui.registry.addButton("tablerowprops",{tooltip:"Row properties",onAction:r("mceTableRowProps"),icon:"table-row-properties",onSetup:n.onSetupCellOrRow}),e.ui.registry.addButton("tableinsertcolbefore",{tooltip:"Insert column before",onAction:r("mceTableInsertColBefore"),icon:"table-insert-column-before",onSetup:n.onSetupCellOrRow}),e.ui.registry.addButton("tableinsertcolafter",{tooltip:"Insert column after",onAction:r("mceTableInsertColAfter"),icon:"table-insert-column-after",onSetup:n.onSetupCellOrRow}),e.ui.registry.addButton("tabledeletecol",{tooltip:"Delete column",onAction:r("mceTableDeleteCol"),icon:"table-delete-column",onSetup:n.onSetupCellOrRow}),e.ui.registry.addButton("tablecutrow",{tooltip:"Cut row",icon:"cut-row",onAction:r("mceTableCutRow"),onSetup:n.onSetupCellOrRow}),e.ui.registry.addButton("tablecopyrow",{tooltip:"Copy row",icon:"duplicate-row",onAction:r("mceTableCopyRow"),onSetup:n.onSetupCellOrRow}),e.ui.registry.addButton("tablepasterowbefore",{tooltip:"Paste row before",icon:"paste-row-before",onAction:r("mceTablePasteRowBefore"),onSetup:n.onSetupPasteable(t.getRows)}),e.ui.registry.addButton("tablepasterowafter",{tooltip:"Paste row after",icon:"paste-row-after",onAction:r("mceTablePasteRowAfter"),onSetup:n.onSetupPasteable(t.getRows)}),e.ui.registry.addButton("tablecutcol",{tooltip:"Cut column",icon:"cut-column",onAction:r("mceTableCutCol"),onSetup:n.onSetupCellOrRow}),e.ui.registry.addButton("tablecopycol",{tooltip:"Copy column",icon:"duplicate-column",onAction:r("mceTableCopyCol"),onSetup:n.onSetupCellOrRow}),e.ui.registry.addButton("tablepastecolbefore",{tooltip:"Paste column before",icon:"paste-column-before",onAction:r("mceTablePasteColBefore"),onSetup:n.onSetupPasteable(t.getColumns)}),e.ui.registry.addButton("tablepastecolafter",{tooltip:"Paste column after",icon:"paste-column-after",onAction:r("mceTablePasteColAfter"),onSetup:n.onSetupPasteable(t.getColumns)}),e.ui.registry.addButton("tableinsertdialog",{tooltip:"Insert table",onAction:r("mceInsertTable"),icon:"table"})},Ed=function(e){var n=e.getParam("table_toolbar","tableprops tabledelete | tableinsertrowbefore tableinsertrowafter tabledeleterow | tableinsertcolbefore tableinsertcolafter tabledeletecol");0<n.length&&e.ui.registry.addContextToolbar("table",{predicate:function(n){return e.dom.is(n,"table")&&e.getBody().contains(n)},items:n,scope:"node",position:"node"})},Pd=function(e,n,t){var r=function(n){return function(){return e.execCommand(n)}},o=function(n){e.execCommand("mceInsertTable",!1,{rows:n.numRows,columns:n.numColumns})},u={text:"Table properties",onSetup:n.onSetupTable,onAction:r("mceTableProps")},i={text:"Delete table",icon:"table-delete-table",onSetup:n.onSetupTable,onAction:r("mceTableDelete")};e.ui.registry.addMenuItem("tableinsertrowbefore",{text:"Insert row before",icon:"table-insert-row-above",onAction:r("mceTableInsertRowBefore"),onSetup:n.onSetupCellOrRow}),e.ui.registry.addMenuItem("tableinsertrowafter",{text:"Insert row after",icon:"table-insert-row-after",onAction:r("mceTableInsertRowAfter"),onSetup:n.onSetupCellOrRow}),e.ui.registry.addMenuItem("tabledeleterow",{text:"Delete row",icon:"table-delete-row",onAction:r("mceTableDeleteRow"),onSetup:n.onSetupCellOrRow}),e.ui.registry.addMenuItem("tablerowprops",{text:"Row properties",icon:"table-row-properties",onAction:r("mceTableRowProps"),onSetup:n.onSetupCellOrRow}),e.ui.registry.addMenuItem("tablecutrow",{text:"Cut row",icon:"cut-row",onAction:r("mceTableCutRow"),onSetup:n.onSetupCellOrRow}),e.ui.registry.addMenuItem("tablecopyrow",{text:"Copy row",icon:"duplicate-row",onAction:r("mceTableCopyRow"),onSetup:n.onSetupCellOrRow}),e.ui.registry.addMenuItem("tablepasterowbefore",{text:"Paste row before",icon:"paste-row-before",onAction:r("mceTablePasteRowBefore"),onSetup:n.onSetupPasteable(t.getRows)}),e.ui.registry.addMenuItem("tablepasterowafter",{text:"Paste row after",icon:"paste-row-after",onAction:r("mceTablePasteRowAfter"),onSetup:n.onSetupPasteable(t.getRows)});e.ui.registry.addMenuItem("tableinsertcolumnbefore",{text:"Insert column before",icon:"table-insert-column-before",onAction:r("mceTableInsertColBefore"),onSetup:n.onSetupCellOrRow}),e.ui.registry.addMenuItem("tableinsertcolumnafter",{text:"Insert column after",icon:"table-insert-column-after",onAction:r("mceTableInsertColAfter"),onSetup:n.onSetupCellOrRow}),e.ui.registry.addMenuItem("tabledeletecolumn",{text:"Delete column",icon:"table-delete-column",onAction:r("mceTableDeleteCol"),onSetup:n.onSetupCellOrRow}),e.ui.registry.addMenuItem("tablecutcolumn",{text:"Cut column",icon:"cut-column",onAction:r("mceTableCutCol"),onSetup:n.onSetupCellOrRow}),e.ui.registry.addMenuItem("tablecopycolumn",{text:"Copy column",icon:"duplicate-column",onAction:r("mceTableCopyCol"),onSetup:n.onSetupCellOrRow}),e.ui.registry.addMenuItem("tablepastecolumnbefore",{text:"Paste column before",icon:"paste-column-before",onAction:r("mceTablePasteColBefore"),onSetup:n.onSetupPasteable(t.getColumns)}),e.ui.registry.addMenuItem("tablepastecolumnafter",{text:"Paste column after",icon:"paste-column-after",onAction:r("mceTablePasteColAfter"),onSetup:n.onSetupPasteable(t.getColumns)});e.ui.registry.addMenuItem("tablecellprops",{text:"Cell properties",icon:"table-cell-properties",onAction:r("mceTableCellProps"),onSetup:n.onSetupCellOrRow}),e.ui.registry.addMenuItem("tablemergecells",{text:"Merge cells",icon:"table-merge-cells",onAction:r("mceTableMergeCells"),onSetup:n.onSetupMergeable}),e.ui.registry.addMenuItem("tablesplitcells",{text:"Split cell",icon:"table-split-cells",onAction:r("mceTableSplitCells"),onSetup:n.onSetupUnmergeable});!1===e.getParam("table_grid",!0,"boolean")?e.ui.registry.addMenuItem("inserttable",{text:"Table",icon:"table",onAction:r("mceInsertTable")}):e.ui.registry.addNestedMenuItem("inserttable",{text:"Table",icon:"table",getSubmenuItems:function(){return[{type:"fancymenuitem",fancytype:"inserttable",onAction:o}]}}),e.ui.registry.addMenuItem("inserttabledialog",{text:"Insert table",icon:"table",onAction:r("mceInsertTable")}),e.ui.registry.addMenuItem("tableprops",u),e.ui.registry.addMenuItem("deletetable",i),e.ui.registry.addNestedMenuItem("row",{type:"nestedmenuitem",text:"Row",getSubmenuItems:function(){return"tableinsertrowbefore tableinsertrowafter tabledeleterow tablerowprops | tablecutrow tablecopyrow tablepasterowbefore tablepasterowafter"}}),e.ui.registry.addNestedMenuItem("column",{type:"nestedmenuitem",text:"Column",getSubmenuItems:function(){return"tableinsertcolumnbefore tableinsertcolumnafter tabledeletecolumn | tablecutcolumn tablecopycolumn tablepastecolumnbefore tablepastecolumnafter"}}),e.ui.registry.addNestedMenuItem("cell",{type:"nestedmenuitem",text:"Cell",getSubmenuItems:function(){return"tablecellprops tablemergecells tablesplitcells"}}),e.ui.registry.addContextMenu("table",{update:function(){return n.resetTargets(),n.targets().fold(function(){return""},function(n){return"caption"===Yn(n.element)?"tableprops deletetable":"cell row column | advtablesort | tableprops deletetable"})}})};function kd(e){var n=cr(function(){return Yi(e)},function(){return fl(oc(e))},Nr.selectedSelector),t=Id(e,n),r=Cc(e),o=Ad(e,r.lazyResize,t),u=ml(e,r.lazyWire,n),i=ff();return af(e,u,o,n,i),lf(e,u,n),zr(e,n,u,o),Pd(e,t,i),Bd(e,t,i),Ed(e),e.on("PreInit",function(){e.serializer.addTempAttr(Nr.firstSelected),e.serializer.addTempAttr(Nr.lastSelected),df(e)}),_i(e)&&e.on("keydown",function(n){fs(n,e,u)}),e.on("remove",function(){r.destroy()}),Sl(e,i,r,t)}ar.add("table",kd)}();