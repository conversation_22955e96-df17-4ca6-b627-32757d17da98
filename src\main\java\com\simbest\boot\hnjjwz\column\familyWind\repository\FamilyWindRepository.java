package com.simbest.boot.hnjjwz.column.familyWind.repository;/**
 * Created by KZH on 2019/7/30 17:18.
 */

import com.simbest.boot.base.repository.LogicRepository;
import com.simbest.boot.hnjjwz.column.familyWind.model.FamilyWind;
import org.springframework.data.jpa.repository.Query;
import org.springframework.data.repository.query.Param;

import java.util.List;

/**
 * <AUTHOR>
 * @create 2019-07-30 17:18
 * @desc 家风栏目数据Dao
 **/
public interface FamilyWindRepository extends LogicRepository<FamilyWind,String> {

    /**
     * 根据familyType获取到数据
     * @param familyType
     * @return
     */
    @Query(
            value = "select * from us_family_wind t where t.family_type=:familyType and t.enabled=1 and t.removed_time is null order by t.vote_Quantity desc",
            nativeQuery = true
    )
    List<FamilyWind> findFamilyType(@Param("familyType")String familyType);
}
