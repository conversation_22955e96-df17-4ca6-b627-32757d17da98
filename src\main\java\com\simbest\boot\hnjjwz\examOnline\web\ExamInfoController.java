package com.simbest.boot.hnjjwz.examOnline.web;

import com.simbest.boot.base.web.controller.LogicController;
import com.simbest.boot.base.web.response.JsonResponse;
import com.simbest.boot.hnjjwz.examOnline.model.ExamInfo;
import com.simbest.boot.hnjjwz.examOnline.service.IExamInfoService;
import com.simbest.boot.util.json.JacksonUtils;
import io.swagger.annotations.Api;
import io.swagger.annotations.ApiImplicitParam;
import io.swagger.annotations.ApiImplicitParams;
import io.swagger.annotations.ApiOperation;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.data.domain.Pageable;
import org.springframework.web.bind.annotation.*;

import java.util.List;
import java.util.Map;

/**
 * <AUTHOR>
 * @Data 2019/05/09
 * @Description
 */
@Api(description = "试卷")
@Slf4j
@RestController
@RequestMapping(value = "/action/examInfo")
public class ExamInfoController extends LogicController<ExamInfo, String> {

    @Autowired
    private IExamInfoService examInfoService;

    @Autowired
    public ExamInfoController(IExamInfoService examInfoService) {
        super(examInfoService);
        this.examInfoService = examInfoService;
    }

    /**
     * 查询保存后的试卷内容
     * @param onlyRecord
     * @return
     */
    @ApiOperation (value = "查询保存后的试卷内容", notes = "查询保存后的试卷内容")
    @ApiImplicitParam (name = "onlyRecord", value = "试卷唯一编码", dataType = "String", paramType = "query")
    @PostMapping (value = {"/findSaveQuestion","/findSaveQuestion/sso"})
    public JsonResponse findSaveQuestion( @RequestParam (required = false) String onlyRecord ) {
        return examInfoService.findSaveQuestion(onlyRecord);
    }

    /**
     * 保存试卷
     * @return
     */
    @ApiOperation (value = "保存试卷", notes = "保存试卷")
    @PostMapping (value = {"/saveExamInfo","/saveExamInfo/sso"})
    public JsonResponse saveExamInfo( ExamInfo examInfo ) {
        return examInfoService.saveExamInfo( examInfo );
    }

    /**
     * 进行评分
     * @param examInfo
     * @return
     */
    @ApiOperation (value = "进行评分", notes = "进行评分")
    @PostMapping (value = {"/gradExam","/gradExam/sso"})
    public JsonResponse gradExam( @RequestBody(required = false) ExamInfo examInfo ) {
        return examInfoService.gradExam(examInfo);
    }


    /**
     * 评测试卷
     * @param
     * @return
     */
    @ApiOperation (value = "评测试卷", notes = "评测试卷")
    @PostMapping (value = {"/evaluatingExam","/evaluatingExam/sso"})
    public JsonResponse evaluatingExam(@RequestParam  Map<Object,List<Object>> map) {

        return examInfoService.evaluatingExam(map);
    }

    /**
     * 随机试卷
     * @param
     * @return
     */
    @ApiOperation (value = "随机试卷", notes = "随机试卷")
    @ApiImplicitParam (name = "questionBankCode", value = "题库编码", dataType = "String", paramType = "query")
    @PostMapping (value = {"/randomExam","/randomExam/sso"})
    public JsonResponse randomExam(@RequestParam (required = false) String questionBankCode) {
        return  examInfoService.randomExam(questionBankCode);
    }

    /**
     * 获取当前登录人的成绩
     * @return
     */
    @ApiOperation(value = "获取当前登录人的成绩", notes = "获取当前登录人的成绩")
    @ApiImplicitParams( {
            @ApiImplicitParam(name = "page", value = "当前页码", dataType = "int", paramType = "query", example = "1"), //
            @ApiImplicitParam(name = "size", value = "每页数量", dataType = "int", paramType = "query", example = "10"), //
            @ApiImplicitParam(name = "direction", value = "排序规则（asc/desc）", dataType = "String", //
                    paramType = "query"), //
            @ApiImplicitParam(name = "properties", value = "排序规则（属性名称）", dataType = "String", //
                    paramType = "query")
    } )
    @PostMapping (value = {"/findOneself","/findOneself/sso"})
    public JsonResponse findOneself(@RequestParam(required = false, defaultValue = "1") int page,
                                    @RequestParam(required = false, defaultValue = "10") int size,
                                    @RequestParam(required = false) String direction,
                                    @RequestParam(required = false) String properties) {
        Pageable pageable = examInfoService.getPageable(page, size, direction, properties);

        return  examInfoService.findOneself(pageable);
    }

}
