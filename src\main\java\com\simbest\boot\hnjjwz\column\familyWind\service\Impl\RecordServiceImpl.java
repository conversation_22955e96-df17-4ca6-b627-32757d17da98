package com.simbest.boot.hnjjwz.column.familyWind.service.Impl;/**
 * Created by KZH on 2019/8/5 17:43.
 */

import com.simbest.boot.base.service.impl.LogicService;
import com.simbest.boot.base.web.response.JsonResponse;
import com.simbest.boot.hnjjwz.column.familyWind.model.Record;
import com.simbest.boot.hnjjwz.column.familyWind.repository.RecordRepository;
import com.simbest.boot.hnjjwz.column.familyWind.service.IRecordService;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;

import java.util.List;

/**
 * <AUTHOR>
 * @create 2019-08-05 17:43
 * @desc
 **/
@Slf4j
@Service
public class RecordServiceImpl extends LogicService<Record,String> implements IRecordService {

    private RecordRepository  recordRepository;
    @Autowired
    public RecordServiceImpl(RecordRepository repository){
        super(repository);
        this.recordRepository=repository;

    }

    @Override
    public JsonResponse findRecord(String username) {
        return JsonResponse.success(recordRepository.findRecord(username));
    }

    @Override
    public List<Record> findRecordList(String username) {
        return recordRepository.findRecord(username);
    }
}
