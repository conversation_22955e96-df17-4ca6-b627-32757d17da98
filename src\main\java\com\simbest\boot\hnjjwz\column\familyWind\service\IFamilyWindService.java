package com.simbest.boot.hnjjwz.column.familyWind.service;/**
 * Created by KZH on 2019/7/30 17:20.
 */

import com.simbest.boot.base.service.ILogicService;
import com.simbest.boot.hnjjwz.column.familyWind.model.FamilyWind;

import java.util.List;

/**
 * <AUTHOR>
 * @create 2019-07-30 17:20
 * @desc 家风栏目Service
 **/
public interface IFamilyWindService extends ILogicService<FamilyWind,String> {

    /**
     * 根据familyType获取到数据
     * @param familyType
     * @return
     */
    List<FamilyWind> findFamilyType(String familyType);

    /**
     * 根据Id更新投票数量
     * @param Id
     * @return
     */
    Boolean updateVoteQuantity(String Id);
}
