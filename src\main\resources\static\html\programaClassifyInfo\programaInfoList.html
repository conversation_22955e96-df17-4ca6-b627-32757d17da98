<!DOCTYPE html>
<html xmlns="http://www.w3.org/1999/xhtml"
      xmlns:th="http://www.thymeleaf.org">
<head>
    <title>栏目信息管理</title>
    <meta http-equiv="Content-Type" content="text/html; charset=utf-8"/>
    <meta http-equiv="X-UA-Compatible" content="IE=edge" />
    <meta name="viewport" content="width=device-width, initial-scale=1, shrink-to-fit=no"/>
    <!--
    <link href="http://************:8088/simbestui/fonts/iconfont/iconfont.css?v=svn.revision" th:href="@{http://************:8088/simbestui/fonts/iconfont/iconfont.css?v=svn.revision}" rel="stylesheet"/>
    -->
    <link href="../../fonts/iconfont/iconfont.css?v=svn.revision" th:href="@{/fonts/iconfont/iconfont.css?v=svn.revision}" rel="stylesheet"/>
    <link href="http://************:8088/simbestui/js/themes/default/easyui.css?v=svn.revision" th:href="@{http://************:8088/simbestui/js/themes/default/easyui.css?v=svn.revision}" rel="stylesheet"/>
    <link href="http://************:8088/simbestui/js/themes/icon.css?v=svn.revision" th:href="@{http://************:8088/simbestui/js/themes/icon.css?v=svn.revision}" rel="stylesheet"/>
    <link href="http://************:8088/simbestui/css/public.css?v=svn.revision" th:href="@{http://************:8088/simbestui/css/public.css?v=svn.revision}" rel="stylesheet"/>
    <link href="http://************:8088/simbestui/css/pages.css?v=svn.revision" th:href="@{http://************:8088/simbestui/css/pages.css?v=svn.revision}" rel="stylesheet"/>
    <script src="http://************:8088/simbestui/js/jquery.min.js?v=svn.revision" th:src="@{http://************:8088/simbestui/js/jquery.min.js?v=svn.revision}" type="text/javascript"></script>
    <script src="http://************:8088/simbestui/js/jquery.easyui.min.js?v=svn.revision" th:src="@{http://************:8088/simbestui/js/jquery.easyui.min.js?v=svn.revision}" type="text/javascript"></script>
    <script src="http://************:8088/simbestui/js/easyui-lang-zh_CN.js?v=svn.revision" th:src="@{http://************:8088/simbestui/js/easyui-lang-zh_CN.js?v=svn.revision}" type="text/javascript"></script>
    <script src="../../js/jquery.config.js?v=svn.revision" th:src="@{/js/jquery.config.js?v=svn.revision}" type="text/javascript"></script>
    <script src="http://************:8088/simbestui/js/jquery.zjsfile.js?v=svn.revision" th:src="@{http://************:8088/simbestui/js/jquery.zjsfile.js?v=svn.revision}" type="text/javascript"></script>
    <script src="http://************:8088/simbestui/js/jquery.aduq.js?v=svn.revision" th:src="@{http://************:8088/simbestui/js/jquery.aduq.js?v=svn.revision}" type="text/javascript"></script>
    <script src="http://************:8088/simbestui/js/jquery.process.js?v=svn.revision" th:src="@{http://************:8088/simbestui/js/jquery.process.js?v=svn.revision}" type="text/javascript"></script>
    <script type="text/javascript">
        $(function(){
            //showDialog做为打开对话框的公共class名称   showDialogindex做为打开修改对话框传行的索引   delete做为删除按钮配置命令   deleteid做为删除传值  searchtable做为条件查询的按钮公共class名称
            var pageparam={
                "listtable":{
                    "listname":"#programaInfoTable",//table列表的id名称，需加#
                    "querycmd":"action/programaInfo/findDimProgramaInfo",//table列表的查询命令
                    "checkboxall":true,
                    "contentType":"application/json; charset=utf-8",//table列表的传参方式，如果是json就写，不写默认kv
                    "nowrap": true,//把数据显示在一行里,默认true
                    "styleClass": "noScroll",
                    "frozenColumns":[[
                        { field: "ck",checkbox:true}
                    ]],//固定在左侧的列
                    "columns":[[//列
                        { title: "栏目编码", field: "programaCode", width: 120},
                        { title: "栏目名", field: "programaName", width: 120},
                        { title: "栏目名全路径", field: "programaDisplayName", width: 120},
                        { title: "所属栏目分类", field: "programaClassifyName", width: 120},
                        { title: "父栏目", field: "parentProgramaName", width: 120},
                        { title: "结点类型", field: "nodeStyleName", width: 120},
                        { title: "同一个父栏目下的排序", field: "displayOrder", width: 120},
                        {
                            field: "opt", title: "操作", width: 110, rowspan: 1,//align：对齐此列的数据，可以用left、right、center
                            formatter: function (value, row,index) {//单元格的格式化函数，需要三个参数：value：字段的值；rowData：行的记录数据；rowIndex：行的索引
                                var g = "<a href='#' class='showDialog'  showDialogindex='" + index + "'>【修改】</a>"
                                    +"<a href='#' delete='action/programaInfo/deleteById' deleteid='"+row.id+"'>【删除】</a>";
                                return g;
                            }
                        }
                    ] ],
                    "pagerbar": [{
                        id:"deleteall",
                        iconCls: 'icon-remove',
                        text:"批量删除&nbsp;"
                    }],
                    "deleteall":{//批量删除deleteall.id要与pagerbar.id相同
                        "id":"deleteall",
                        "url":"action/programaInfo/deleteAllByIds",
                        "contentType":"application/json; charset=utf-8"
                    }
                },
                "dialogform":{
                    "dialogid":"#buttons",//对话框的id
                    "formname":"#programaInfoTableAddForm",//新增或修改对话框的formid需加#
                    "insertcmd":"action/programaInfo/create",//新增命令
                    "updatacmd":"action/programaInfo/update",//修改命令
                    "onSubmit":function(data){
                        return true;
                    }
                }
            };
            //点击栏目关系设置后的按钮
            $(document).on("click",".programaCite",function(){
                var gps=getQueryString();
                var $t=$(this);
                //从应用配置按钮处获取地址以及参数
                var url=$t.attr("path");
                top.dialogP(url,gps.form?"programaInfoList":"rightiframe",'栏目关系设置','programaCiteConfig',false,'800','450');
            });
            //点击打开栏目树
            $(".chooseparentPrograma").on("click",function(){
                var gps=getQueryString();
                //第一个参数是页面的url(multi为0表示单选，为1表示多选),第二个参数是当前对话框对应iframe的name（若是有好多次iframe请按从内到外的顺序以|分隔）,第三个参数选择人界面的标题,
                //第四个参数是选择人完成之后的回调函数名称,第五个参数对话框是否有底部按钮来设置表示默认有(若需要设置宽高,若需要就写false 不需要写true) 第6是宽默认1000，第7是高默认550
                //第二个参数必须是唯一的
                var href={"multi":"0","name":"chooseParentProgramaVal"};
                var chooseRow=top.chooseWeb.chooseParentProgramaVal?top.chooseWeb.chooseParentProgramaVal.data:[];
                if($("#parentProgramaCode").val()!=""){
                    var datas=[];
                    var names=$("#parentProgramaName").val().split(",");
                    var codes=$("#parentProgramaCode").val().split(",");
                    for(var i in codes){
                        var datai={};
                        datai.id=codes[i];
                        datai.name=names[i];
                        datas.push(datai);
                    }
                    top.chooseWeb.chooseParentProgramaVal={"data":datas};
                }else{//表示新增
                    top.chooseWeb.chooseParentProgramaVal={"data":[]};
                }
                var url=tourl((gps.form?"hnjjwz/":"")+'html/programaClassifyInfo/programaInfoTree.html?',href);
                top.dialogP(url,gps.form?"programaInfoList":'programaInfoList','选择栏目','chooseParentPrograma',false,'800');
                //记住decisionOptF这个参数是打开的页面里的名字
            });
            loadGrid(pageparam);
        });
        //此处的回调函数
        window.programaCiteConfig=function(data){
        };
        //form表单填充数据之前做什么,data为填充数据的集合,isupdate表示是新增还是修改,若为新增则data为{}
        function beforerender(data,isupdate){
            if(isupdate){
                $('.update-readonly').hide();
            }else{
                $('.update-readonly').show();
            }
        };
        //此处在提交之前对下拉框进行处理。若没有值，就把所有值取出来拼成逗号的形式。
        /*function querySubmit(params){
            if(params.programaClassifyIds==""){
                var types=$("#programaClassifyIds").combobox("getData");
                var ts=[];
                for(var i in types){
                    if(types[i].id!="" && types[i].id!=undefined) ts.push(types[i].id);
                }
                params.programaClassifyIds=ts.join(",");
            }
            if(params.nodeStyles==""){
                var types=$("#nodeStyles").combobox("getData");
                var ts=[];
                for(var i in types){
                    if(types[i].value!="" && types[i].value!=undefined) ts.push(types[i].value);
                }
                params.nodeStyles=ts.join(",");
            }
            return true;
        };*/
        //选择栏目树后用于回显
        window.chooseParentPrograma=function(data){
            var programaCode = "";
            var programaName = "";
            var dataLength = data.data.length;
            for(var i=0;i<dataLength;i++){
                programaCode = programaCode + data.data[i].orgCode;
                programaName =  programaName + data.data[i].displayName;
                if(i<dataLength-1){
                    programaCode = programaCode + ",";
                    programaName = programaName + ",";
                }
            }
            $("#parentProgramaCode").val(programaCode);
            $("#parentProgramaName").val(programaName);
        };
        //对表单中的元素进行校验
        window.getchoosedata=function(){
            if($("#programaInfoTableQueryForm").form("validate")){
                var datas=getFormValue("programaInfoTableQueryForm");
                return {"data":datas,"state":1,"mod":gps.mod?gps.mod:-1};
            }else{
                top.mesAlert("提示信息","校验不成功","error");
                return {"data":{},"state":0};
            }
        };
    </script>
    <style>

        .datagrid-body{
            height: auto!important;
        }
        .formTable { width: 100%; margin-top: 30px; border-spacing: 0; border-top: 1px solid #dedede; border-left: 1px solid #dedede; }
        .formTable>tbody>tr:not(:first-child)>td { border-right: 1px solid #dedede; border-bottom: 1px solid #dedede; font-size: 14px; height: 36px; }
        .formTable>tbody>tr>td input,.formTable>tbody>tr>td span,.formTable>tbody>tr>td textarea,.formTable>tbody>tr>td .textbox .textbox-text{ border: none; font-size: 14px; }
        .formTable td.lable{ background-color: #ddf1fe; padding: 5px; text-align: left; }
        .formTable td .textAndInput_readonly, .formTable td .textAndInput_readonly .validatebox-readonly{ background-color: #fff; }
        .cselectorImageUL .btn{ right: 3px; top: 3px; }
        .cselectorImageUL input[type='file']{ right: 25px; top: 3px; }
        textarea{ line-height: 20px; letter-spacing: 1px; }
        table.tabForm{ border-color: #dedede; }
    </style>
</head>
<body class="body_page">
<!--searchform-->
<form id="programaInfoTableQueryForm"  >
    <table border="0" cellpadding="0" cellspacing="6" width="100%">
        <tr>
            <td width="80" align="right">栏目名称：</td><td width="150"><input name="programaName" type="text" value="" /></td>
            <td width="100" align="right">所属栏目分类：</td><td width="150">
            <input id="programaClassifyIds" name="programaClassifyIds" class="easyui-combobox"  style="width: 100%; height: 32px;" data-options="
                          valueField: 'id',
                           panelHeight:'auto',
                           ischooseall:true,
                           ischooseallTxt:'全部',
                           queryParams:{},
                           textField: 'programaClassifyName',
                           contentType:'application/json; charset=utf-8',
                           url: web.rootdir+'action/programaClassifyInfo/findAllNoPage'"/>
        </td>
            <td width="60" align="right">父栏目：</td><td width="150"><input name="parentProgramaName" type="text" value="" /></td>
            <td width="70" align="right">结点类型：</td><td width="150">
                <input id="nodeStyles" name="nodeStyles" class="easyui-combobox"  style="width: 100%; height: 32px;" data-options="
                           valueField: 'value',
                           panelHeight:'auto',
                           ischooseall:true,
                           ischooseallTxt:'请选择',
                           textField: 'name',
                           queryParams:{'dictType':'nodeStyle'},
                           contentType:'application/json; charset=utf-8',
                           url: web.rootdir+'sys/dictValue/findDictValue'"/>
             </td>
            <td>
                <div class="w100">
                    <a class="btn a_primary fr searchtable"><span>查询</span></a>   
					<a class="btn a_success showDialog fr mr10"><span>新增</span></a>
                </div>
            </td>
        </tr>
    </table>
</form>
<!--table-->
<div class="programaInfoTable"><table id="programaInfoTable"></table></div>
<!--dialog-->
<div id="buttons" title="新增或修改" class="easyui-dialog" style="width:800px;height:500px;">
    <form id="programaInfoTableAddForm" method="post" contentType="application/json; charset=utf-8" cmd-select="action/programaInfo/findById" beforerender="beforerender()">
        <input id="id" name="id" type="hidden" />
        <table border="0" style="width: 100%;" class="tabForm formTable" cellpadding="0" cellspacing="10" >
            <tr>
                <td width="16.6%"></td>
                <td width="32.2%"></td>
                <td width="16.6%"></td>
                <td width="32.2%"></td>
            </tr>
            <tr>
                <td align="right" class="lable">
                    <font class="col_r">*</font>所属栏目分类
                </td>
                <td>
                    <input id="programaClassifyId" name="programaClassifyId" class="easyui-combobox"
                        style="width: 100%; height: 32px;" data-options="
                                   valueField: 'id',
                                   panelHeight:'200',
                                   ischooseall:true,
                                   ischooseallTxt:'请选择',
                                   queryParams:{},
                                   textField: 'programaClassifyName',
                                   contentType:'application/json; charset=utf-8',
                                   url: web.rootdir+'action/programaClassifyInfo/findAllNoPage'" />
                </td>
                <td align="right" class="lable">
                    <font class="col_r">*</font>结点类型
                </td>
                <td>
                    <input id="nodeStyle" name="nodeStyle" class="easyui-combobox" style="width: 99%; height: 32px;" data-options="
            					valueField: 'value',
            					ischooseall:true,
            					textField: 'name',
            					queryParams:{'dictType':'nodeStyle'},
            					contentType:'application/json; charset=utf-8',
            					url: web.rootdir+'sys/dictValue/findDictValue' " />
                </td>
            </tr>
            <tr style="display: none">
                <td align="right" class="lable">父栏目编码</td>
                <td colspan="3"><input id="parentProgramaCode" name="parentProgramaCode" type="text" class="easyui-validatebox" />
                </td>
            </tr>
            <tr>
                <td align="right" class="lable">
                    <font class="col_r">*</font>栏目名称
                </td>
                <td>
                    <input id="programaName" name="programaName" type="text" class="easyui-validatebox" required='required' />
                </td>
                <td align="right" class="lable">
                    <font class="col_r">*</font>同一个父栏目下的排序
                </td>
                <td>
                    <input id="displayOrder" name="displayOrder" type="text" class="easyui-validatebox" required='required' />
                </td>
            </tr>
            <tr>
                <td align="right" class="lable">选择父栏目</td>
                <td colspan="3"><input id="parentProgramaName" name="parentProgramaName" type="text" class="easyui-validatebox" style="width: calc(100% - 60px);" />
                <a class="btn a_warning chooseparentPrograma fr" ><i class="iconfont">&#xe634;</i></a></td>
            </tr>
            <tr>
                <td align="right" valign="top" class="lable">栏目封面</td>
                <td colspan="3" valign="top">
                    <input id="programaCoverFile" name="programaCoverFile" type="text" mulaccept="image/*"
                        class="cselectorImageUpload" sizelength="1" extension="gif,jpg,jpeg,png"
                        btnmsg="<i class='iconfont' title='添加'>&#xe641;</i>"
                        href="sys/file/uploadProcessFiles?pmInsType=programaCoverType&pmInsTypePart=1" />
                </td>
            </tr>
        </table>
    </form>
</div>
</body>
</html>
