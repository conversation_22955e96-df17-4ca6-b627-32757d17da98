2025-06-06 00:13:44.903 [redisson-netty-5-24] ERROR org.redisson.cluster.ClusterConnectionManager.lambda$updateClusterState$5 Line:363 - Can't execute CLUSTER_NODES with 10.92.82.152/10.92.82.152:7003
org.redisson.client.RedisTimeoutException: Command execution timeout for command: (CLUSTER NODES), command params: [], Redis client: [addr=redis://10.92.82.152:7003]
	at org.redisson.client.RedisConnection$1.run(RedisConnection.java:209)
	at io.netty.util.concurrent.PromiseTask.runTask(PromiseTask.java:98)
	at io.netty.util.concurrent.ScheduledFutureTask.run(ScheduledFutureTask.java:150)
	at io.netty.util.concurrent.AbstractEventExecutor.safeExecute(AbstractEventExecutor.java:163)
	at io.netty.util.concurrent.SingleThreadEventExecutor.runAllTasks(SingleThreadEventExecutor.java:510)
	at io.netty.channel.nio.NioEventLoop.run(NioEventLoop.java:518)
	at io.netty.util.concurrent.SingleThreadEventExecutor$6.run(SingleThreadEventExecutor.java:1050)
	at io.netty.util.internal.ThreadExecutorMap$2.run(ThreadExecutorMap.java:74)
	at io.netty.util.concurrent.FastThreadLocalRunnable.run(FastThreadLocalRunnable.java:30)
	at java.lang.Thread.run(Thread.java:748)
2025-06-06 00:34:24.563 [redisson-netty-5-26] ERROR org.redisson.cluster.ClusterConnectionManager.lambda$updateClusterState$5 Line:363 - Can't execute CLUSTER_NODES with 10.92.82.152/10.92.82.152:7003
org.redisson.client.RedisTimeoutException: Command execution timeout for command: (CLUSTER NODES), command params: [], Redis client: [addr=redis://10.92.82.152:7003]
	at org.redisson.client.RedisConnection$1.run(RedisConnection.java:209)
	at io.netty.util.concurrent.PromiseTask.runTask(PromiseTask.java:98)
	at io.netty.util.concurrent.ScheduledFutureTask.run(ScheduledFutureTask.java:150)
	at io.netty.util.concurrent.AbstractEventExecutor.safeExecute(AbstractEventExecutor.java:163)
	at io.netty.util.concurrent.SingleThreadEventExecutor.runAllTasks(SingleThreadEventExecutor.java:510)
	at io.netty.channel.nio.NioEventLoop.run(NioEventLoop.java:518)
	at io.netty.util.concurrent.SingleThreadEventExecutor$6.run(SingleThreadEventExecutor.java:1050)
	at io.netty.util.internal.ThreadExecutorMap$2.run(ThreadExecutorMap.java:74)
	at io.netty.util.concurrent.FastThreadLocalRunnable.run(FastThreadLocalRunnable.java:30)
	at java.lang.Thread.run(Thread.java:748)
2025-06-06 00:34:27.572 [redisson-netty-5-8] ERROR org.redisson.cluster.ClusterConnectionManager.lambda$updateClusterState$5 Line:363 - Can't execute CLUSTER_NODES with 10.92.82.153/10.92.82.153:7005
org.redisson.client.RedisTimeoutException: Command execution timeout for command: (CLUSTER NODES), command params: [], Redis client: [addr=redis://10.92.82.153:7005]
	at org.redisson.client.RedisConnection$1.run(RedisConnection.java:209)
	at io.netty.util.concurrent.PromiseTask.runTask(PromiseTask.java:98)
	at io.netty.util.concurrent.ScheduledFutureTask.run(ScheduledFutureTask.java:150)
	at io.netty.util.concurrent.AbstractEventExecutor.safeExecute(AbstractEventExecutor.java:163)
	at io.netty.util.concurrent.SingleThreadEventExecutor.runAllTasks(SingleThreadEventExecutor.java:510)
	at io.netty.channel.nio.NioEventLoop.run(NioEventLoop.java:518)
	at io.netty.util.concurrent.SingleThreadEventExecutor$6.run(SingleThreadEventExecutor.java:1050)
	at io.netty.util.internal.ThreadExecutorMap$2.run(ThreadExecutorMap.java:74)
	at io.netty.util.concurrent.FastThreadLocalRunnable.run(FastThreadLocalRunnable.java:30)
	at java.lang.Thread.run(Thread.java:748)
2025-06-06 00:55:13.926 [redisson-netty-5-12] ERROR org.redisson.cluster.ClusterConnectionManager.lambda$updateClusterState$5 Line:363 - Can't execute CLUSTER_NODES with 10.92.82.152/10.92.82.152:7003
org.redisson.client.RedisTimeoutException: Command execution timeout for command: (CLUSTER NODES), command params: [], Redis client: [addr=redis://10.92.82.152:7003]
	at org.redisson.client.RedisConnection$1.run(RedisConnection.java:209)
	at io.netty.util.concurrent.PromiseTask.runTask(PromiseTask.java:98)
	at io.netty.util.concurrent.ScheduledFutureTask.run(ScheduledFutureTask.java:150)
	at io.netty.util.concurrent.AbstractEventExecutor.safeExecute(AbstractEventExecutor.java:163)
	at io.netty.util.concurrent.SingleThreadEventExecutor.runAllTasks(SingleThreadEventExecutor.java:510)
	at io.netty.channel.nio.NioEventLoop.run(NioEventLoop.java:518)
	at io.netty.util.concurrent.SingleThreadEventExecutor$6.run(SingleThreadEventExecutor.java:1050)
	at io.netty.util.internal.ThreadExecutorMap$2.run(ThreadExecutorMap.java:74)
	at io.netty.util.concurrent.FastThreadLocalRunnable.run(FastThreadLocalRunnable.java:30)
	at java.lang.Thread.run(Thread.java:748)
2025-06-06 01:15:57.495 [redisson-netty-5-31] ERROR org.redisson.cluster.ClusterConnectionManager.lambda$updateClusterState$5 Line:363 - Can't execute CLUSTER_NODES with 10.92.82.152/10.92.82.152:7003
org.redisson.client.RedisTimeoutException: Command execution timeout for command: (CLUSTER NODES), command params: [], Redis client: [addr=redis://10.92.82.152:7003]
	at org.redisson.client.RedisConnection$1.run(RedisConnection.java:209)
	at io.netty.util.concurrent.PromiseTask.runTask(PromiseTask.java:98)
	at io.netty.util.concurrent.ScheduledFutureTask.run(ScheduledFutureTask.java:150)
	at io.netty.util.concurrent.AbstractEventExecutor.safeExecute(AbstractEventExecutor.java:163)
	at io.netty.util.concurrent.SingleThreadEventExecutor.runAllTasks(SingleThreadEventExecutor.java:510)
	at io.netty.channel.nio.NioEventLoop.run(NioEventLoop.java:518)
	at io.netty.util.concurrent.SingleThreadEventExecutor$6.run(SingleThreadEventExecutor.java:1050)
	at io.netty.util.internal.ThreadExecutorMap$2.run(ThreadExecutorMap.java:74)
	at io.netty.util.concurrent.FastThreadLocalRunnable.run(FastThreadLocalRunnable.java:30)
	at java.lang.Thread.run(Thread.java:748)
2025-06-06 01:16:09.305 [redisson-netty-5-25] ERROR org.redisson.cluster.ClusterConnectionManager.lambda$updateClusterState$5 Line:363 - Can't execute CLUSTER_NODES with 10.92.82.152/10.92.82.152:7003
org.redisson.client.RedisTimeoutException: Command execution timeout for command: (CLUSTER NODES), command params: [], Redis client: [addr=redis://10.92.82.152:7003]
	at org.redisson.client.RedisConnection$1.run(RedisConnection.java:209)
	at io.netty.util.concurrent.PromiseTask.runTask(PromiseTask.java:98)
	at io.netty.util.concurrent.ScheduledFutureTask.run(ScheduledFutureTask.java:150)
	at io.netty.util.concurrent.AbstractEventExecutor.safeExecute(AbstractEventExecutor.java:163)
	at io.netty.util.concurrent.SingleThreadEventExecutor.runAllTasks(SingleThreadEventExecutor.java:510)
	at io.netty.channel.nio.NioEventLoop.run(NioEventLoop.java:518)
	at io.netty.util.concurrent.SingleThreadEventExecutor$6.run(SingleThreadEventExecutor.java:1050)
	at io.netty.util.internal.ThreadExecutorMap$2.run(ThreadExecutorMap.java:74)
	at io.netty.util.concurrent.FastThreadLocalRunnable.run(FastThreadLocalRunnable.java:30)
	at java.lang.Thread.run(Thread.java:748)
2025-06-06 01:36:39.933 [redisson-netty-5-23] ERROR org.redisson.cluster.ClusterConnectionManager.lambda$updateClusterState$5 Line:363 - Can't execute CLUSTER_NODES with 10.92.82.152/10.92.82.152:7003
org.redisson.client.RedisTimeoutException: Command execution timeout for command: (CLUSTER NODES), command params: [], Redis client: [addr=redis://10.92.82.152:7003]
	at org.redisson.client.RedisConnection$1.run(RedisConnection.java:209)
	at io.netty.util.concurrent.PromiseTask.runTask(PromiseTask.java:98)
	at io.netty.util.concurrent.ScheduledFutureTask.run(ScheduledFutureTask.java:150)
	at io.netty.util.concurrent.AbstractEventExecutor.safeExecute(AbstractEventExecutor.java:163)
	at io.netty.util.concurrent.SingleThreadEventExecutor.runAllTasks(SingleThreadEventExecutor.java:510)
	at io.netty.channel.nio.NioEventLoop.run(NioEventLoop.java:518)
	at io.netty.util.concurrent.SingleThreadEventExecutor$6.run(SingleThreadEventExecutor.java:1050)
	at io.netty.util.internal.ThreadExecutorMap$2.run(ThreadExecutorMap.java:74)
	at io.netty.util.concurrent.FastThreadLocalRunnable.run(FastThreadLocalRunnable.java:30)
	at java.lang.Thread.run(Thread.java:748)
2025-06-06 01:57:25.918 [redisson-netty-5-25] ERROR org.redisson.cluster.ClusterConnectionManager.lambda$updateClusterState$5 Line:363 - Can't execute CLUSTER_NODES with 10.92.82.152/10.92.82.152:7003
org.redisson.client.RedisTimeoutException: Command execution timeout for command: (CLUSTER NODES), command params: [], Redis client: [addr=redis://10.92.82.152:7003]
	at org.redisson.client.RedisConnection$1.run(RedisConnection.java:209)
	at io.netty.util.concurrent.PromiseTask.runTask(PromiseTask.java:98)
	at io.netty.util.concurrent.ScheduledFutureTask.run(ScheduledFutureTask.java:150)
	at io.netty.util.concurrent.AbstractEventExecutor.safeExecute(AbstractEventExecutor.java:163)
	at io.netty.util.concurrent.SingleThreadEventExecutor.runAllTasks(SingleThreadEventExecutor.java:510)
	at io.netty.channel.nio.NioEventLoop.run(NioEventLoop.java:518)
	at io.netty.util.concurrent.SingleThreadEventExecutor$6.run(SingleThreadEventExecutor.java:1050)
	at io.netty.util.internal.ThreadExecutorMap$2.run(ThreadExecutorMap.java:74)
	at io.netty.util.concurrent.FastThreadLocalRunnable.run(FastThreadLocalRunnable.java:30)
	at java.lang.Thread.run(Thread.java:748)
2025-06-06 02:18:06.765 [redisson-netty-5-8] ERROR org.redisson.cluster.ClusterConnectionManager.lambda$updateClusterState$5 Line:363 - Can't execute CLUSTER_NODES with 10.92.82.152/10.92.82.152:7003
org.redisson.client.RedisTimeoutException: Command execution timeout for command: (CLUSTER NODES), command params: [], Redis client: [addr=redis://10.92.82.152:7003]
	at org.redisson.client.RedisConnection$1.run(RedisConnection.java:209)
	at io.netty.util.concurrent.PromiseTask.runTask(PromiseTask.java:98)
	at io.netty.util.concurrent.ScheduledFutureTask.run(ScheduledFutureTask.java:150)
	at io.netty.util.concurrent.AbstractEventExecutor.safeExecute(AbstractEventExecutor.java:163)
	at io.netty.util.concurrent.SingleThreadEventExecutor.runAllTasks(SingleThreadEventExecutor.java:510)
	at io.netty.channel.nio.NioEventLoop.run(NioEventLoop.java:518)
	at io.netty.util.concurrent.SingleThreadEventExecutor$6.run(SingleThreadEventExecutor.java:1050)
	at io.netty.util.internal.ThreadExecutorMap$2.run(ThreadExecutorMap.java:74)
	at io.netty.util.concurrent.FastThreadLocalRunnable.run(FastThreadLocalRunnable.java:30)
	at java.lang.Thread.run(Thread.java:748)
2025-06-06 02:18:09.769 [redisson-netty-5-10] ERROR org.redisson.cluster.ClusterConnectionManager.lambda$updateClusterState$5 Line:363 - Can't execute CLUSTER_NODES with 10.92.82.153/10.92.82.153:7005
org.redisson.client.RedisTimeoutException: Command execution timeout for command: (CLUSTER NODES), command params: [], Redis client: [addr=redis://10.92.82.153:7005]
	at org.redisson.client.RedisConnection$1.run(RedisConnection.java:209)
	at io.netty.util.concurrent.PromiseTask.runTask(PromiseTask.java:98)
	at io.netty.util.concurrent.ScheduledFutureTask.run(ScheduledFutureTask.java:150)
	at io.netty.util.concurrent.AbstractEventExecutor.safeExecute(AbstractEventExecutor.java:163)
	at io.netty.util.concurrent.SingleThreadEventExecutor.runAllTasks(SingleThreadEventExecutor.java:510)
	at io.netty.channel.nio.NioEventLoop.run(NioEventLoop.java:518)
	at io.netty.util.concurrent.SingleThreadEventExecutor$6.run(SingleThreadEventExecutor.java:1050)
	at io.netty.util.internal.ThreadExecutorMap$2.run(ThreadExecutorMap.java:74)
	at io.netty.util.concurrent.FastThreadLocalRunnable.run(FastThreadLocalRunnable.java:30)
	at java.lang.Thread.run(Thread.java:748)
2025-06-06 02:18:21.988 [redisson-netty-5-19] ERROR org.redisson.cluster.ClusterConnectionManager.lambda$updateClusterState$5 Line:363 - Can't execute CLUSTER_NODES with 10.92.82.152/10.92.82.152:7003
org.redisson.client.RedisTimeoutException: Command execution timeout for command: (CLUSTER NODES), command params: [], Redis client: [addr=redis://10.92.82.152:7003]
	at org.redisson.client.RedisConnection$1.run(RedisConnection.java:209)
	at io.netty.util.concurrent.PromiseTask.runTask(PromiseTask.java:98)
	at io.netty.util.concurrent.ScheduledFutureTask.run(ScheduledFutureTask.java:150)
	at io.netty.util.concurrent.AbstractEventExecutor.safeExecute(AbstractEventExecutor.java:163)
	at io.netty.util.concurrent.SingleThreadEventExecutor.runAllTasks(SingleThreadEventExecutor.java:510)
	at io.netty.channel.nio.NioEventLoop.run(NioEventLoop.java:518)
	at io.netty.util.concurrent.SingleThreadEventExecutor$6.run(SingleThreadEventExecutor.java:1050)
	at io.netty.util.internal.ThreadExecutorMap$2.run(ThreadExecutorMap.java:74)
	at io.netty.util.concurrent.FastThreadLocalRunnable.run(FastThreadLocalRunnable.java:30)
	at java.lang.Thread.run(Thread.java:748)
2025-06-06 02:38:54.538 [redisson-netty-5-31] ERROR org.redisson.cluster.ClusterConnectionManager.lambda$updateClusterState$5 Line:363 - Can't execute CLUSTER_NODES with 10.92.82.152/10.92.82.152:7003
org.redisson.client.RedisTimeoutException: Command execution timeout for command: (CLUSTER NODES), command params: [], Redis client: [addr=redis://10.92.82.152:7003]
	at org.redisson.client.RedisConnection$1.run(RedisConnection.java:209)
	at io.netty.util.concurrent.PromiseTask.runTask(PromiseTask.java:98)
	at io.netty.util.concurrent.ScheduledFutureTask.run(ScheduledFutureTask.java:150)
	at io.netty.util.concurrent.AbstractEventExecutor.safeExecute(AbstractEventExecutor.java:163)
	at io.netty.util.concurrent.SingleThreadEventExecutor.runAllTasks(SingleThreadEventExecutor.java:510)
	at io.netty.channel.nio.NioEventLoop.run(NioEventLoop.java:518)
	at io.netty.util.concurrent.SingleThreadEventExecutor$6.run(SingleThreadEventExecutor.java:1050)
	at io.netty.util.internal.ThreadExecutorMap$2.run(ThreadExecutorMap.java:74)
	at io.netty.util.concurrent.FastThreadLocalRunnable.run(FastThreadLocalRunnable.java:30)
	at java.lang.Thread.run(Thread.java:748)
2025-06-06 02:59:38.021 [redisson-netty-5-22] ERROR org.redisson.cluster.ClusterConnectionManager.lambda$updateClusterState$5 Line:363 - Can't execute CLUSTER_NODES with 10.92.82.152/10.92.82.152:7003
org.redisson.client.RedisTimeoutException: Command execution timeout for command: (CLUSTER NODES), command params: [], Redis client: [addr=redis://10.92.82.152:7003]
	at org.redisson.client.RedisConnection$1.run(RedisConnection.java:209)
	at io.netty.util.concurrent.PromiseTask.runTask(PromiseTask.java:98)
	at io.netty.util.concurrent.ScheduledFutureTask.run(ScheduledFutureTask.java:150)
	at io.netty.util.concurrent.AbstractEventExecutor.safeExecute(AbstractEventExecutor.java:163)
	at io.netty.util.concurrent.SingleThreadEventExecutor.runAllTasks(SingleThreadEventExecutor.java:510)
	at io.netty.channel.nio.NioEventLoop.run(NioEventLoop.java:518)
	at io.netty.util.concurrent.SingleThreadEventExecutor$6.run(SingleThreadEventExecutor.java:1050)
	at io.netty.util.internal.ThreadExecutorMap$2.run(ThreadExecutorMap.java:74)
	at io.netty.util.concurrent.FastThreadLocalRunnable.run(FastThreadLocalRunnable.java:30)
	at java.lang.Thread.run(Thread.java:748)
2025-06-06 02:59:41.030 [redisson-netty-5-23] ERROR org.redisson.cluster.ClusterConnectionManager.lambda$updateClusterState$5 Line:363 - Can't execute CLUSTER_NODES with 10.92.82.153/10.92.82.153:7005
org.redisson.client.RedisTimeoutException: Command execution timeout for command: (CLUSTER NODES), command params: [], Redis client: [addr=redis://10.92.82.153:7005]
	at org.redisson.client.RedisConnection$1.run(RedisConnection.java:209)
	at io.netty.util.concurrent.PromiseTask.runTask(PromiseTask.java:98)
	at io.netty.util.concurrent.ScheduledFutureTask.run(ScheduledFutureTask.java:150)
	at io.netty.util.concurrent.AbstractEventExecutor.safeExecute(AbstractEventExecutor.java:163)
	at io.netty.util.concurrent.SingleThreadEventExecutor.runAllTasks(SingleThreadEventExecutor.java:510)
	at io.netty.channel.nio.NioEventLoop.run(NioEventLoop.java:518)
	at io.netty.util.concurrent.SingleThreadEventExecutor$6.run(SingleThreadEventExecutor.java:1050)
	at io.netty.util.internal.ThreadExecutorMap$2.run(ThreadExecutorMap.java:74)
	at io.netty.util.concurrent.FastThreadLocalRunnable.run(FastThreadLocalRunnable.java:30)
	at java.lang.Thread.run(Thread.java:748)
2025-06-06 02:59:48.619 [redisson-netty-5-8] ERROR org.redisson.cluster.ClusterConnectionManager.lambda$updateClusterState$5 Line:363 - Can't execute CLUSTER_NODES with 10.92.82.152/10.92.82.152:7003
org.redisson.client.RedisTimeoutException: Command execution timeout for command: (CLUSTER NODES), command params: [], Redis client: [addr=redis://10.92.82.152:7003]
	at org.redisson.client.RedisConnection$1.run(RedisConnection.java:209)
	at io.netty.util.concurrent.PromiseTask.runTask(PromiseTask.java:98)
	at io.netty.util.concurrent.ScheduledFutureTask.run(ScheduledFutureTask.java:150)
	at io.netty.util.concurrent.AbstractEventExecutor.safeExecute(AbstractEventExecutor.java:163)
	at io.netty.util.concurrent.SingleThreadEventExecutor.runAllTasks(SingleThreadEventExecutor.java:510)
	at io.netty.channel.nio.NioEventLoop.run(NioEventLoop.java:518)
	at io.netty.util.concurrent.SingleThreadEventExecutor$6.run(SingleThreadEventExecutor.java:1050)
	at io.netty.util.internal.ThreadExecutorMap$2.run(ThreadExecutorMap.java:74)
	at io.netty.util.concurrent.FastThreadLocalRunnable.run(FastThreadLocalRunnable.java:30)
	at java.lang.Thread.run(Thread.java:748)
2025-06-06 03:20:21.052 [redisson-netty-5-10] ERROR org.redisson.cluster.ClusterConnectionManager.lambda$updateClusterState$5 Line:363 - Can't execute CLUSTER_NODES with 10.92.82.152/10.92.82.152:7003
org.redisson.client.RedisTimeoutException: Command execution timeout for command: (CLUSTER NODES), command params: [], Redis client: [addr=redis://10.92.82.152:7003]
	at org.redisson.client.RedisConnection$1.run(RedisConnection.java:209)
	at io.netty.util.concurrent.PromiseTask.runTask(PromiseTask.java:98)
	at io.netty.util.concurrent.ScheduledFutureTask.run(ScheduledFutureTask.java:150)
	at io.netty.util.concurrent.AbstractEventExecutor.safeExecute(AbstractEventExecutor.java:163)
	at io.netty.util.concurrent.SingleThreadEventExecutor.runAllTasks(SingleThreadEventExecutor.java:510)
	at io.netty.channel.nio.NioEventLoop.run(NioEventLoop.java:518)
	at io.netty.util.concurrent.SingleThreadEventExecutor$6.run(SingleThreadEventExecutor.java:1050)
	at io.netty.util.internal.ThreadExecutorMap$2.run(ThreadExecutorMap.java:74)
	at io.netty.util.concurrent.FastThreadLocalRunnable.run(FastThreadLocalRunnable.java:30)
	at java.lang.Thread.run(Thread.java:748)
2025-06-06 03:20:28.104 [redisson-netty-5-30] ERROR org.redisson.cluster.ClusterConnectionManager.lambda$updateClusterState$5 Line:363 - Can't execute CLUSTER_NODES with 10.92.82.152/10.92.82.152:7003
org.redisson.client.RedisTimeoutException: Command execution timeout for command: (CLUSTER NODES), command params: [], Redis client: [addr=redis://10.92.82.152:7003]
	at org.redisson.client.RedisConnection$1.run(RedisConnection.java:209)
	at io.netty.util.concurrent.PromiseTask.runTask(PromiseTask.java:98)
	at io.netty.util.concurrent.ScheduledFutureTask.run(ScheduledFutureTask.java:150)
	at io.netty.util.concurrent.AbstractEventExecutor.safeExecute(AbstractEventExecutor.java:163)
	at io.netty.util.concurrent.SingleThreadEventExecutor.runAllTasks(SingleThreadEventExecutor.java:510)
	at io.netty.channel.nio.NioEventLoop.run(NioEventLoop.java:518)
	at io.netty.util.concurrent.SingleThreadEventExecutor$6.run(SingleThreadEventExecutor.java:1050)
	at io.netty.util.internal.ThreadExecutorMap$2.run(ThreadExecutorMap.java:74)
	at io.netty.util.concurrent.FastThreadLocalRunnable.run(FastThreadLocalRunnable.java:30)
	at java.lang.Thread.run(Thread.java:748)
2025-06-06 03:20:31.119 [redisson-netty-5-31] ERROR org.redisson.cluster.ClusterConnectionManager.lambda$updateClusterState$5 Line:363 - Can't execute CLUSTER_NODES with 10.92.82.153/10.92.82.153:7005
org.redisson.client.RedisTimeoutException: Command execution timeout for command: (CLUSTER NODES), command params: [], Redis client: [addr=redis://10.92.82.153:7005]
	at org.redisson.client.RedisConnection$1.run(RedisConnection.java:209)
	at io.netty.util.concurrent.PromiseTask.runTask(PromiseTask.java:98)
	at io.netty.util.concurrent.ScheduledFutureTask.run(ScheduledFutureTask.java:150)
	at io.netty.util.concurrent.AbstractEventExecutor.safeExecute(AbstractEventExecutor.java:163)
	at io.netty.util.concurrent.SingleThreadEventExecutor.runAllTasks(SingleThreadEventExecutor.java:510)
	at io.netty.channel.nio.NioEventLoop.run(NioEventLoop.java:518)
	at io.netty.util.concurrent.SingleThreadEventExecutor$6.run(SingleThreadEventExecutor.java:1050)
	at io.netty.util.internal.ThreadExecutorMap$2.run(ThreadExecutorMap.java:74)
	at io.netty.util.concurrent.FastThreadLocalRunnable.run(FastThreadLocalRunnable.java:30)
	at java.lang.Thread.run(Thread.java:748)
2025-06-06 03:20:34.129 [redisson-netty-5-19] ERROR org.redisson.cluster.ClusterConnectionManager.lambda$updateClusterState$5 Line:363 - Can't execute CLUSTER_NODES with 10.92.82.152/10.92.82.152:7004
org.redisson.client.RedisTimeoutException: Command execution timeout for command: (CLUSTER NODES), command params: [], Redis client: [addr=redis://10.92.82.152:7004]
	at org.redisson.client.RedisConnection$1.run(RedisConnection.java:209)
	at io.netty.util.concurrent.PromiseTask.runTask(PromiseTask.java:98)
	at io.netty.util.concurrent.ScheduledFutureTask.run(ScheduledFutureTask.java:150)
	at io.netty.util.concurrent.AbstractEventExecutor.safeExecute(AbstractEventExecutor.java:163)
	at io.netty.util.concurrent.SingleThreadEventExecutor.runAllTasks(SingleThreadEventExecutor.java:510)
	at io.netty.channel.nio.NioEventLoop.run(NioEventLoop.java:518)
	at io.netty.util.concurrent.SingleThreadEventExecutor$6.run(SingleThreadEventExecutor.java:1050)
	at io.netty.util.internal.ThreadExecutorMap$2.run(ThreadExecutorMap.java:74)
	at io.netty.util.concurrent.FastThreadLocalRunnable.run(FastThreadLocalRunnable.java:30)
	at java.lang.Thread.run(Thread.java:748)
2025-06-06 03:41:10.505 [redisson-netty-5-24] ERROR org.redisson.cluster.ClusterConnectionManager.lambda$updateClusterState$5 Line:363 - Can't execute CLUSTER_NODES with 10.92.82.152/10.92.82.152:7003
org.redisson.client.RedisTimeoutException: Command execution timeout for command: (CLUSTER NODES), command params: [], Redis client: [addr=redis://10.92.82.152:7003]
	at org.redisson.client.RedisConnection$1.run(RedisConnection.java:209)
	at io.netty.util.concurrent.PromiseTask.runTask(PromiseTask.java:98)
	at io.netty.util.concurrent.ScheduledFutureTask.run(ScheduledFutureTask.java:150)
	at io.netty.util.concurrent.AbstractEventExecutor.safeExecute(AbstractEventExecutor.java:163)
	at io.netty.util.concurrent.SingleThreadEventExecutor.runAllTasks(SingleThreadEventExecutor.java:510)
	at io.netty.channel.nio.NioEventLoop.run(NioEventLoop.java:518)
	at io.netty.util.concurrent.SingleThreadEventExecutor$6.run(SingleThreadEventExecutor.java:1050)
	at io.netty.util.internal.ThreadExecutorMap$2.run(ThreadExecutorMap.java:74)
	at io.netty.util.concurrent.FastThreadLocalRunnable.run(FastThreadLocalRunnable.java:30)
	at java.lang.Thread.run(Thread.java:748)
2025-06-06 03:41:18.125 [redisson-netty-5-19] ERROR org.redisson.cluster.ClusterConnectionManager.lambda$updateClusterState$5 Line:363 - Can't execute CLUSTER_NODES with 10.92.82.152/10.92.82.152:7003
org.redisson.client.RedisTimeoutException: Command execution timeout for command: (CLUSTER NODES), command params: [], Redis client: [addr=redis://10.92.82.152:7003]
	at org.redisson.client.RedisConnection$1.run(RedisConnection.java:209)
	at io.netty.util.concurrent.PromiseTask.runTask(PromiseTask.java:98)
	at io.netty.util.concurrent.ScheduledFutureTask.run(ScheduledFutureTask.java:150)
	at io.netty.util.concurrent.AbstractEventExecutor.safeExecute(AbstractEventExecutor.java:163)
	at io.netty.util.concurrent.SingleThreadEventExecutor.runAllTasks(SingleThreadEventExecutor.java:510)
	at io.netty.channel.nio.NioEventLoop.run(NioEventLoop.java:518)
	at io.netty.util.concurrent.SingleThreadEventExecutor$6.run(SingleThreadEventExecutor.java:1050)
	at io.netty.util.internal.ThreadExecutorMap$2.run(ThreadExecutorMap.java:74)
	at io.netty.util.concurrent.FastThreadLocalRunnable.run(FastThreadLocalRunnable.java:30)
	at java.lang.Thread.run(Thread.java:748)
2025-06-06 04:01:44.516 [redisson-netty-5-32] ERROR org.redisson.cluster.ClusterConnectionManager.lambda$updateClusterState$5 Line:363 - Can't execute CLUSTER_NODES with 10.92.82.152/10.92.82.152:7003
org.redisson.client.RedisTimeoutException: Command execution timeout for command: (CLUSTER NODES), command params: [], Redis client: [addr=redis://10.92.82.152:7003]
	at org.redisson.client.RedisConnection$1.run(RedisConnection.java:209)
	at io.netty.util.concurrent.PromiseTask.runTask(PromiseTask.java:98)
	at io.netty.util.concurrent.ScheduledFutureTask.run(ScheduledFutureTask.java:150)
	at io.netty.util.concurrent.AbstractEventExecutor.safeExecute(AbstractEventExecutor.java:163)
	at io.netty.util.concurrent.SingleThreadEventExecutor.runAllTasks(SingleThreadEventExecutor.java:510)
	at io.netty.channel.nio.NioEventLoop.run(NioEventLoop.java:518)
	at io.netty.util.concurrent.SingleThreadEventExecutor$6.run(SingleThreadEventExecutor.java:1050)
	at io.netty.util.internal.ThreadExecutorMap$2.run(ThreadExecutorMap.java:74)
	at io.netty.util.concurrent.FastThreadLocalRunnable.run(FastThreadLocalRunnable.java:30)
	at java.lang.Thread.run(Thread.java:748)
2025-06-06 04:01:47.522 [redisson-netty-5-17] ERROR org.redisson.cluster.ClusterConnectionManager.lambda$updateClusterState$5 Line:363 - Can't execute CLUSTER_NODES with 10.92.82.153/10.92.82.153:7005
org.redisson.client.RedisTimeoutException: Command execution timeout for command: (CLUSTER NODES), command params: [], Redis client: [addr=redis://10.92.82.153:7005]
	at org.redisson.client.RedisConnection$1.run(RedisConnection.java:209)
	at io.netty.util.concurrent.PromiseTask.runTask(PromiseTask.java:98)
	at io.netty.util.concurrent.ScheduledFutureTask.run(ScheduledFutureTask.java:150)
	at io.netty.util.concurrent.AbstractEventExecutor.safeExecute(AbstractEventExecutor.java:163)
	at io.netty.util.concurrent.SingleThreadEventExecutor.runAllTasks(SingleThreadEventExecutor.java:510)
	at io.netty.channel.nio.NioEventLoop.run(NioEventLoop.java:518)
	at io.netty.util.concurrent.SingleThreadEventExecutor$6.run(SingleThreadEventExecutor.java:1050)
	at io.netty.util.internal.ThreadExecutorMap$2.run(ThreadExecutorMap.java:74)
	at io.netty.util.concurrent.FastThreadLocalRunnable.run(FastThreadLocalRunnable.java:30)
	at java.lang.Thread.run(Thread.java:748)
2025-06-06 04:22:31.545 [redisson-netty-5-25] ERROR org.redisson.cluster.ClusterConnectionManager.lambda$updateClusterState$5 Line:363 - Can't execute CLUSTER_NODES with 10.92.82.152/10.92.82.152:7003
org.redisson.client.RedisTimeoutException: Command execution timeout for command: (CLUSTER NODES), command params: [], Redis client: [addr=redis://10.92.82.152:7003]
	at org.redisson.client.RedisConnection$1.run(RedisConnection.java:209)
	at io.netty.util.concurrent.PromiseTask.runTask(PromiseTask.java:98)
	at io.netty.util.concurrent.ScheduledFutureTask.run(ScheduledFutureTask.java:150)
	at io.netty.util.concurrent.AbstractEventExecutor.safeExecute(AbstractEventExecutor.java:163)
	at io.netty.util.concurrent.SingleThreadEventExecutor.runAllTasks(SingleThreadEventExecutor.java:510)
	at io.netty.channel.nio.NioEventLoop.run(NioEventLoop.java:518)
	at io.netty.util.concurrent.SingleThreadEventExecutor$6.run(SingleThreadEventExecutor.java:1050)
	at io.netty.util.internal.ThreadExecutorMap$2.run(ThreadExecutorMap.java:74)
	at io.netty.util.concurrent.FastThreadLocalRunnable.run(FastThreadLocalRunnable.java:30)
	at java.lang.Thread.run(Thread.java:748)
2025-06-06 04:22:43.491 [redisson-netty-5-22] ERROR org.redisson.cluster.ClusterConnectionManager.lambda$updateClusterState$5 Line:363 - Can't execute CLUSTER_NODES with 10.92.82.152/10.92.82.152:7003
org.redisson.client.RedisTimeoutException: Command execution timeout for command: (CLUSTER NODES), command params: [], Redis client: [addr=redis://10.92.82.152:7003]
	at org.redisson.client.RedisConnection$1.run(RedisConnection.java:209)
	at io.netty.util.concurrent.PromiseTask.runTask(PromiseTask.java:98)
	at io.netty.util.concurrent.ScheduledFutureTask.run(ScheduledFutureTask.java:150)
	at io.netty.util.concurrent.AbstractEventExecutor.safeExecute(AbstractEventExecutor.java:163)
	at io.netty.util.concurrent.SingleThreadEventExecutor.runAllTasks(SingleThreadEventExecutor.java:510)
	at io.netty.channel.nio.NioEventLoop.run(NioEventLoop.java:518)
	at io.netty.util.concurrent.SingleThreadEventExecutor$6.run(SingleThreadEventExecutor.java:1050)
	at io.netty.util.internal.ThreadExecutorMap$2.run(ThreadExecutorMap.java:74)
	at io.netty.util.concurrent.FastThreadLocalRunnable.run(FastThreadLocalRunnable.java:30)
	at java.lang.Thread.run(Thread.java:748)
2025-06-06 04:43:12.386 [redisson-netty-5-19] ERROR org.redisson.cluster.ClusterConnectionManager.lambda$updateClusterState$5 Line:363 - Can't execute CLUSTER_NODES with 10.92.82.152/10.92.82.152:7003
org.redisson.client.RedisTimeoutException: Command execution timeout for command: (CLUSTER NODES), command params: [], Redis client: [addr=redis://10.92.82.152:7003]
	at org.redisson.client.RedisConnection$1.run(RedisConnection.java:209)
	at io.netty.util.concurrent.PromiseTask.runTask(PromiseTask.java:98)
	at io.netty.util.concurrent.ScheduledFutureTask.run(ScheduledFutureTask.java:150)
	at io.netty.util.concurrent.AbstractEventExecutor.safeExecute(AbstractEventExecutor.java:163)
	at io.netty.util.concurrent.SingleThreadEventExecutor.runAllTasks(SingleThreadEventExecutor.java:510)
	at io.netty.channel.nio.NioEventLoop.run(NioEventLoop.java:518)
	at io.netty.util.concurrent.SingleThreadEventExecutor$6.run(SingleThreadEventExecutor.java:1050)
	at io.netty.util.internal.ThreadExecutorMap$2.run(ThreadExecutorMap.java:74)
	at io.netty.util.concurrent.FastThreadLocalRunnable.run(FastThreadLocalRunnable.java:30)
	at java.lang.Thread.run(Thread.java:748)
2025-06-06 04:43:15.404 [redisson-netty-5-20] ERROR org.redisson.cluster.ClusterConnectionManager.lambda$updateClusterState$5 Line:363 - Can't execute CLUSTER_NODES with 10.92.82.153/10.92.82.153:7005
org.redisson.client.RedisTimeoutException: Command execution timeout for command: (CLUSTER NODES), command params: [], Redis client: [addr=redis://10.92.82.153:7005]
	at org.redisson.client.RedisConnection$1.run(RedisConnection.java:209)
	at io.netty.util.concurrent.PromiseTask.runTask(PromiseTask.java:98)
	at io.netty.util.concurrent.ScheduledFutureTask.run(ScheduledFutureTask.java:150)
	at io.netty.util.concurrent.AbstractEventExecutor.safeExecute(AbstractEventExecutor.java:163)
	at io.netty.util.concurrent.SingleThreadEventExecutor.runAllTasks(SingleThreadEventExecutor.java:510)
	at io.netty.channel.nio.NioEventLoop.run(NioEventLoop.java:518)
	at io.netty.util.concurrent.SingleThreadEventExecutor$6.run(SingleThreadEventExecutor.java:1050)
	at io.netty.util.internal.ThreadExecutorMap$2.run(ThreadExecutorMap.java:74)
	at io.netty.util.concurrent.FastThreadLocalRunnable.run(FastThreadLocalRunnable.java:30)
	at java.lang.Thread.run(Thread.java:748)
2025-06-06 05:03:55.532 [redisson-netty-5-9] ERROR org.redisson.cluster.ClusterConnectionManager.lambda$updateClusterState$5 Line:363 - Can't execute CLUSTER_NODES with 10.92.82.152/10.92.82.152:7003
org.redisson.client.RedisTimeoutException: Command execution timeout for command: (CLUSTER NODES), command params: [], Redis client: [addr=redis://10.92.82.152:7003]
	at org.redisson.client.RedisConnection$1.run(RedisConnection.java:209)
	at io.netty.util.concurrent.PromiseTask.runTask(PromiseTask.java:98)
	at io.netty.util.concurrent.ScheduledFutureTask.run(ScheduledFutureTask.java:150)
	at io.netty.util.concurrent.AbstractEventExecutor.safeExecute(AbstractEventExecutor.java:163)
	at io.netty.util.concurrent.SingleThreadEventExecutor.runAllTasks(SingleThreadEventExecutor.java:510)
	at io.netty.channel.nio.NioEventLoop.run(NioEventLoop.java:518)
	at io.netty.util.concurrent.SingleThreadEventExecutor$6.run(SingleThreadEventExecutor.java:1050)
	at io.netty.util.internal.ThreadExecutorMap$2.run(ThreadExecutorMap.java:74)
	at io.netty.util.concurrent.FastThreadLocalRunnable.run(FastThreadLocalRunnable.java:30)
	at java.lang.Thread.run(Thread.java:748)
2025-06-06 05:03:58.541 [redisson-netty-5-17] ERROR org.redisson.cluster.ClusterConnectionManager.lambda$updateClusterState$5 Line:363 - Can't execute CLUSTER_NODES with ************/************:7001
org.redisson.client.RedisTimeoutException: Command execution timeout for command: (CLUSTER NODES), command params: [], Redis client: [addr=redis://************:7001]
	at org.redisson.client.RedisConnection$1.run(RedisConnection.java:209)
	at io.netty.util.concurrent.PromiseTask.runTask(PromiseTask.java:98)
	at io.netty.util.concurrent.ScheduledFutureTask.run(ScheduledFutureTask.java:150)
	at io.netty.util.concurrent.AbstractEventExecutor.safeExecute(AbstractEventExecutor.java:163)
	at io.netty.util.concurrent.SingleThreadEventExecutor.runAllTasks(SingleThreadEventExecutor.java:510)
	at io.netty.channel.nio.NioEventLoop.run(NioEventLoop.java:518)
	at io.netty.util.concurrent.SingleThreadEventExecutor$6.run(SingleThreadEventExecutor.java:1050)
	at io.netty.util.internal.ThreadExecutorMap$2.run(ThreadExecutorMap.java:74)
	at io.netty.util.concurrent.FastThreadLocalRunnable.run(FastThreadLocalRunnable.java:30)
	at java.lang.Thread.run(Thread.java:748)
2025-06-06 05:03:58.543 [redisson-netty-5-17] ERROR org.redisson.cluster.ClusterConnectionManager.checkClusterState Line:336 - Can't update cluster state
org.redisson.client.RedisTimeoutException: Command execution timeout for command: (CLUSTER NODES), command params: [], Redis client: [addr=redis://************:7001]
	at org.redisson.client.RedisConnection$1.run(RedisConnection.java:209)
	at io.netty.util.concurrent.PromiseTask.runTask(PromiseTask.java:98)
	at io.netty.util.concurrent.ScheduledFutureTask.run(ScheduledFutureTask.java:150)
	at io.netty.util.concurrent.AbstractEventExecutor.safeExecute(AbstractEventExecutor.java:163)
	at io.netty.util.concurrent.SingleThreadEventExecutor.runAllTasks(SingleThreadEventExecutor.java:510)
	at io.netty.channel.nio.NioEventLoop.run(NioEventLoop.java:518)
	at io.netty.util.concurrent.SingleThreadEventExecutor$6.run(SingleThreadEventExecutor.java:1050)
	at io.netty.util.internal.ThreadExecutorMap$2.run(ThreadExecutorMap.java:74)
	at io.netty.util.concurrent.FastThreadLocalRunnable.run(FastThreadLocalRunnable.java:30)
	at java.lang.Thread.run(Thread.java:748)
2025-06-06 05:04:11.961 [redisson-netty-5-25] ERROR org.redisson.cluster.ClusterConnectionManager.lambda$updateClusterState$5 Line:363 - Can't execute CLUSTER_NODES with 10.92.82.152/10.92.82.152:7003
org.redisson.client.RedisTimeoutException: Command execution timeout for command: (CLUSTER NODES), command params: [], Redis client: [addr=redis://10.92.82.152:7003]
	at org.redisson.client.RedisConnection$1.run(RedisConnection.java:209)
	at io.netty.util.concurrent.PromiseTask.runTask(PromiseTask.java:98)
	at io.netty.util.concurrent.ScheduledFutureTask.run(ScheduledFutureTask.java:150)
	at io.netty.util.concurrent.AbstractEventExecutor.safeExecute(AbstractEventExecutor.java:163)
	at io.netty.util.concurrent.SingleThreadEventExecutor.runAllTasks(SingleThreadEventExecutor.java:510)
	at io.netty.channel.nio.NioEventLoop.run(NioEventLoop.java:518)
	at io.netty.util.concurrent.SingleThreadEventExecutor$6.run(SingleThreadEventExecutor.java:1050)
	at io.netty.util.internal.ThreadExecutorMap$2.run(ThreadExecutorMap.java:74)
	at io.netty.util.concurrent.FastThreadLocalRunnable.run(FastThreadLocalRunnable.java:30)
	at java.lang.Thread.run(Thread.java:748)
2025-06-06 05:24:43.952 [redisson-netty-5-24] ERROR org.redisson.cluster.ClusterConnectionManager.lambda$updateClusterState$5 Line:363 - Can't execute CLUSTER_NODES with 10.92.82.152/10.92.82.152:7003
org.redisson.client.RedisTimeoutException: Command execution timeout for command: (CLUSTER NODES), command params: [], Redis client: [addr=redis://10.92.82.152:7003]
	at org.redisson.client.RedisConnection$1.run(RedisConnection.java:209)
	at io.netty.util.concurrent.PromiseTask.runTask(PromiseTask.java:98)
	at io.netty.util.concurrent.ScheduledFutureTask.run(ScheduledFutureTask.java:150)
	at io.netty.util.concurrent.AbstractEventExecutor.safeExecute(AbstractEventExecutor.java:163)
	at io.netty.util.concurrent.SingleThreadEventExecutor.runAllTasks(SingleThreadEventExecutor.java:510)
	at io.netty.channel.nio.NioEventLoop.run(NioEventLoop.java:518)
	at io.netty.util.concurrent.SingleThreadEventExecutor$6.run(SingleThreadEventExecutor.java:1050)
	at io.netty.util.internal.ThreadExecutorMap$2.run(ThreadExecutorMap.java:74)
	at io.netty.util.concurrent.FastThreadLocalRunnable.run(FastThreadLocalRunnable.java:30)
	at java.lang.Thread.run(Thread.java:748)
2025-06-06 05:45:27.404 [redisson-netty-5-8] ERROR org.redisson.cluster.ClusterConnectionManager.lambda$updateClusterState$5 Line:363 - Can't execute CLUSTER_NODES with 10.92.82.152/10.92.82.152:7003
org.redisson.client.RedisTimeoutException: Command execution timeout for command: (CLUSTER NODES), command params: [], Redis client: [addr=redis://10.92.82.152:7003]
	at org.redisson.client.RedisConnection$1.run(RedisConnection.java:209)
	at io.netty.util.concurrent.PromiseTask.runTask(PromiseTask.java:98)
	at io.netty.util.concurrent.ScheduledFutureTask.run(ScheduledFutureTask.java:150)
	at io.netty.util.concurrent.AbstractEventExecutor.safeExecute(AbstractEventExecutor.java:163)
	at io.netty.util.concurrent.SingleThreadEventExecutor.runAllTasks(SingleThreadEventExecutor.java:510)
	at io.netty.channel.nio.NioEventLoop.run(NioEventLoop.java:518)
	at io.netty.util.concurrent.SingleThreadEventExecutor$6.run(SingleThreadEventExecutor.java:1050)
	at io.netty.util.internal.ThreadExecutorMap$2.run(ThreadExecutorMap.java:74)
	at io.netty.util.concurrent.FastThreadLocalRunnable.run(FastThreadLocalRunnable.java:30)
	at java.lang.Thread.run(Thread.java:748)
2025-06-06 05:45:40.119 [redisson-netty-5-31] ERROR org.redisson.cluster.ClusterConnectionManager.lambda$updateClusterState$5 Line:363 - Can't execute CLUSTER_NODES with 10.92.82.152/10.92.82.152:7003
org.redisson.client.RedisTimeoutException: Command execution timeout for command: (CLUSTER NODES), command params: [], Redis client: [addr=redis://10.92.82.152:7003]
	at org.redisson.client.RedisConnection$1.run(RedisConnection.java:209)
	at io.netty.util.concurrent.PromiseTask.runTask(PromiseTask.java:98)
	at io.netty.util.concurrent.ScheduledFutureTask.run(ScheduledFutureTask.java:150)
	at io.netty.util.concurrent.AbstractEventExecutor.safeExecute(AbstractEventExecutor.java:163)
	at io.netty.util.concurrent.SingleThreadEventExecutor.runAllTasks(SingleThreadEventExecutor.java:510)
	at io.netty.channel.nio.NioEventLoop.run(NioEventLoop.java:518)
	at io.netty.util.concurrent.SingleThreadEventExecutor$6.run(SingleThreadEventExecutor.java:1050)
	at io.netty.util.internal.ThreadExecutorMap$2.run(ThreadExecutorMap.java:74)
	at io.netty.util.concurrent.FastThreadLocalRunnable.run(FastThreadLocalRunnable.java:30)
	at java.lang.Thread.run(Thread.java:748)
2025-06-06 06:06:10.776 [redisson-netty-5-16] ERROR org.redisson.cluster.ClusterConnectionManager.lambda$updateClusterState$5 Line:363 - Can't execute CLUSTER_NODES with 10.92.82.152/10.92.82.152:7003
org.redisson.client.RedisTimeoutException: Command execution timeout for command: (CLUSTER NODES), command params: [], Redis client: [addr=redis://10.92.82.152:7003]
	at org.redisson.client.RedisConnection$1.run(RedisConnection.java:209)
	at io.netty.util.concurrent.PromiseTask.runTask(PromiseTask.java:98)
	at io.netty.util.concurrent.ScheduledFutureTask.run(ScheduledFutureTask.java:150)
	at io.netty.util.concurrent.AbstractEventExecutor.safeExecute(AbstractEventExecutor.java:163)
	at io.netty.util.concurrent.SingleThreadEventExecutor.runAllTasks(SingleThreadEventExecutor.java:510)
	at io.netty.channel.nio.NioEventLoop.run(NioEventLoop.java:518)
	at io.netty.util.concurrent.SingleThreadEventExecutor$6.run(SingleThreadEventExecutor.java:1050)
	at io.netty.util.internal.ThreadExecutorMap$2.run(ThreadExecutorMap.java:74)
	at io.netty.util.concurrent.FastThreadLocalRunnable.run(FastThreadLocalRunnable.java:30)
	at java.lang.Thread.run(Thread.java:748)
2025-06-06 06:06:21.955 [redisson-netty-5-20] ERROR org.redisson.cluster.ClusterConnectionManager.lambda$updateClusterState$5 Line:363 - Can't execute CLUSTER_NODES with 10.92.82.152/10.92.82.152:7003
org.redisson.client.RedisTimeoutException: Command execution timeout for command: (CLUSTER NODES), command params: [], Redis client: [addr=redis://10.92.82.152:7003]
	at org.redisson.client.RedisConnection$1.run(RedisConnection.java:209)
	at io.netty.util.concurrent.PromiseTask.runTask(PromiseTask.java:98)
	at io.netty.util.concurrent.ScheduledFutureTask.run(ScheduledFutureTask.java:150)
	at io.netty.util.concurrent.AbstractEventExecutor.safeExecute(AbstractEventExecutor.java:163)
	at io.netty.util.concurrent.SingleThreadEventExecutor.runAllTasks(SingleThreadEventExecutor.java:510)
	at io.netty.channel.nio.NioEventLoop.run(NioEventLoop.java:518)
	at io.netty.util.concurrent.SingleThreadEventExecutor$6.run(SingleThreadEventExecutor.java:1050)
	at io.netty.util.internal.ThreadExecutorMap$2.run(ThreadExecutorMap.java:74)
	at io.netty.util.concurrent.FastThreadLocalRunnable.run(FastThreadLocalRunnable.java:30)
	at java.lang.Thread.run(Thread.java:748)
2025-06-06 06:26:52.941 [redisson-netty-5-31] ERROR org.redisson.cluster.ClusterConnectionManager.lambda$updateClusterState$5 Line:363 - Can't execute CLUSTER_NODES with 10.92.82.152/10.92.82.152:7003
org.redisson.client.RedisTimeoutException: Command execution timeout for command: (CLUSTER NODES), command params: [], Redis client: [addr=redis://10.92.82.152:7003]
	at org.redisson.client.RedisConnection$1.run(RedisConnection.java:209)
	at io.netty.util.concurrent.PromiseTask.runTask(PromiseTask.java:98)
	at io.netty.util.concurrent.ScheduledFutureTask.run(ScheduledFutureTask.java:150)
	at io.netty.util.concurrent.AbstractEventExecutor.safeExecute(AbstractEventExecutor.java:163)
	at io.netty.util.concurrent.SingleThreadEventExecutor.runAllTasks(SingleThreadEventExecutor.java:510)
	at io.netty.channel.nio.NioEventLoop.run(NioEventLoop.java:518)
	at io.netty.util.concurrent.SingleThreadEventExecutor$6.run(SingleThreadEventExecutor.java:1050)
	at io.netty.util.internal.ThreadExecutorMap$2.run(ThreadExecutorMap.java:74)
	at io.netty.util.concurrent.FastThreadLocalRunnable.run(FastThreadLocalRunnable.java:30)
	at java.lang.Thread.run(Thread.java:748)
2025-06-06 06:47:36.858 [redisson-netty-5-23] ERROR org.redisson.cluster.ClusterConnectionManager.lambda$updateClusterState$5 Line:363 - Can't execute CLUSTER_NODES with 10.92.82.152/10.92.82.152:7003
org.redisson.client.RedisTimeoutException: Command execution timeout for command: (CLUSTER NODES), command params: [], Redis client: [addr=redis://10.92.82.152:7003]
	at org.redisson.client.RedisConnection$1.run(RedisConnection.java:209)
	at io.netty.util.concurrent.PromiseTask.runTask(PromiseTask.java:98)
	at io.netty.util.concurrent.ScheduledFutureTask.run(ScheduledFutureTask.java:150)
	at io.netty.util.concurrent.AbstractEventExecutor.safeExecute(AbstractEventExecutor.java:163)
	at io.netty.util.concurrent.SingleThreadEventExecutor.runAllTasks(SingleThreadEventExecutor.java:510)
	at io.netty.channel.nio.NioEventLoop.run(NioEventLoop.java:518)
	at io.netty.util.concurrent.SingleThreadEventExecutor$6.run(SingleThreadEventExecutor.java:1050)
	at io.netty.util.internal.ThreadExecutorMap$2.run(ThreadExecutorMap.java:74)
	at io.netty.util.concurrent.FastThreadLocalRunnable.run(FastThreadLocalRunnable.java:30)
	at java.lang.Thread.run(Thread.java:748)
2025-06-06 06:47:47.926 [redisson-netty-5-12] ERROR org.redisson.cluster.ClusterConnectionManager.lambda$updateClusterState$5 Line:363 - Can't execute CLUSTER_NODES with 10.92.82.152/10.92.82.152:7003
org.redisson.client.RedisTimeoutException: Command execution timeout for command: (CLUSTER NODES), command params: [], Redis client: [addr=redis://10.92.82.152:7003]
	at org.redisson.client.RedisConnection$1.run(RedisConnection.java:209)
	at io.netty.util.concurrent.PromiseTask.runTask(PromiseTask.java:98)
	at io.netty.util.concurrent.ScheduledFutureTask.run(ScheduledFutureTask.java:150)
	at io.netty.util.concurrent.AbstractEventExecutor.safeExecute(AbstractEventExecutor.java:163)
	at io.netty.util.concurrent.SingleThreadEventExecutor.runAllTasks(SingleThreadEventExecutor.java:510)
	at io.netty.channel.nio.NioEventLoop.run(NioEventLoop.java:518)
	at io.netty.util.concurrent.SingleThreadEventExecutor$6.run(SingleThreadEventExecutor.java:1050)
	at io.netty.util.internal.ThreadExecutorMap$2.run(ThreadExecutorMap.java:74)
	at io.netty.util.concurrent.FastThreadLocalRunnable.run(FastThreadLocalRunnable.java:30)
	at java.lang.Thread.run(Thread.java:748)
2025-06-06 07:08:20.558 [redisson-netty-5-26] ERROR org.redisson.cluster.ClusterConnectionManager.lambda$updateClusterState$5 Line:363 - Can't execute CLUSTER_NODES with 10.92.82.152/10.92.82.152:7003
org.redisson.client.RedisTimeoutException: Command execution timeout for command: (CLUSTER NODES), command params: [], Redis client: [addr=redis://10.92.82.152:7003]
	at org.redisson.client.RedisConnection$1.run(RedisConnection.java:209)
	at io.netty.util.concurrent.PromiseTask.runTask(PromiseTask.java:98)
	at io.netty.util.concurrent.ScheduledFutureTask.run(ScheduledFutureTask.java:150)
	at io.netty.util.concurrent.AbstractEventExecutor.safeExecute(AbstractEventExecutor.java:163)
	at io.netty.util.concurrent.SingleThreadEventExecutor.runAllTasks(SingleThreadEventExecutor.java:510)
	at io.netty.channel.nio.NioEventLoop.run(NioEventLoop.java:518)
	at io.netty.util.concurrent.SingleThreadEventExecutor$6.run(SingleThreadEventExecutor.java:1050)
	at io.netty.util.internal.ThreadExecutorMap$2.run(ThreadExecutorMap.java:74)
	at io.netty.util.concurrent.FastThreadLocalRunnable.run(FastThreadLocalRunnable.java:30)
	at java.lang.Thread.run(Thread.java:748)
2025-06-06 07:08:23.565 [redisson-netty-5-8] ERROR org.redisson.cluster.ClusterConnectionManager.lambda$updateClusterState$5 Line:363 - Can't execute CLUSTER_NODES with 10.92.82.153/10.92.82.153:7005
org.redisson.client.RedisTimeoutException: Command execution timeout for command: (CLUSTER NODES), command params: [], Redis client: [addr=redis://10.92.82.153:7005]
	at org.redisson.client.RedisConnection$1.run(RedisConnection.java:209)
	at io.netty.util.concurrent.PromiseTask.runTask(PromiseTask.java:98)
	at io.netty.util.concurrent.ScheduledFutureTask.run(ScheduledFutureTask.java:150)
	at io.netty.util.concurrent.AbstractEventExecutor.safeExecute(AbstractEventExecutor.java:163)
	at io.netty.util.concurrent.SingleThreadEventExecutor.runAllTasks(SingleThreadEventExecutor.java:510)
	at io.netty.channel.nio.NioEventLoop.run(NioEventLoop.java:518)
	at io.netty.util.concurrent.SingleThreadEventExecutor$6.run(SingleThreadEventExecutor.java:1050)
	at io.netty.util.internal.ThreadExecutorMap$2.run(ThreadExecutorMap.java:74)
	at io.netty.util.concurrent.FastThreadLocalRunnable.run(FastThreadLocalRunnable.java:30)
	at java.lang.Thread.run(Thread.java:748)
2025-06-06 07:29:05.305 [redisson-netty-5-16] ERROR org.redisson.cluster.ClusterConnectionManager.lambda$updateClusterState$5 Line:363 - Can't execute CLUSTER_NODES with 10.92.82.152/10.92.82.152:7003
org.redisson.client.RedisTimeoutException: Command execution timeout for command: (CLUSTER NODES), command params: [], Redis client: [addr=redis://10.92.82.152:7003]
	at org.redisson.client.RedisConnection$1.run(RedisConnection.java:209)
	at io.netty.util.concurrent.PromiseTask.runTask(PromiseTask.java:98)
	at io.netty.util.concurrent.ScheduledFutureTask.run(ScheduledFutureTask.java:150)
	at io.netty.util.concurrent.AbstractEventExecutor.safeExecute(AbstractEventExecutor.java:163)
	at io.netty.util.concurrent.SingleThreadEventExecutor.runAllTasks(SingleThreadEventExecutor.java:510)
	at io.netty.channel.nio.NioEventLoop.run(NioEventLoop.java:518)
	at io.netty.util.concurrent.SingleThreadEventExecutor$6.run(SingleThreadEventExecutor.java:1050)
	at io.netty.util.internal.ThreadExecutorMap$2.run(ThreadExecutorMap.java:74)
	at io.netty.util.concurrent.FastThreadLocalRunnable.run(FastThreadLocalRunnable.java:30)
	at java.lang.Thread.run(Thread.java:748)
2025-06-06 07:29:20.347 [redisson-netty-5-21] ERROR org.redisson.cluster.ClusterConnectionManager.lambda$updateClusterState$5 Line:363 - Can't execute CLUSTER_NODES with 10.92.82.153/10.92.82.153:7005
org.redisson.client.RedisTimeoutException: Command execution timeout for command: (CLUSTER NODES), command params: [], Redis client: [addr=redis://10.92.82.153:7005]
	at org.redisson.client.RedisConnection$1.run(RedisConnection.java:209)
	at io.netty.util.concurrent.PromiseTask.runTask(PromiseTask.java:98)
	at io.netty.util.concurrent.ScheduledFutureTask.run(ScheduledFutureTask.java:150)
	at io.netty.util.concurrent.AbstractEventExecutor.safeExecute(AbstractEventExecutor.java:163)
	at io.netty.util.concurrent.SingleThreadEventExecutor.runAllTasks(SingleThreadEventExecutor.java:510)
	at io.netty.channel.nio.NioEventLoop.run(NioEventLoop.java:518)
	at io.netty.util.concurrent.SingleThreadEventExecutor$6.run(SingleThreadEventExecutor.java:1050)
	at io.netty.util.internal.ThreadExecutorMap$2.run(ThreadExecutorMap.java:74)
	at io.netty.util.concurrent.FastThreadLocalRunnable.run(FastThreadLocalRunnable.java:30)
	at java.lang.Thread.run(Thread.java:748)
2025-06-06 07:29:23.359 [redisson-netty-5-1] ERROR org.redisson.cluster.ClusterConnectionManager.lambda$updateClusterState$5 Line:363 - Can't execute CLUSTER_NODES with 10.92.82.152/10.92.82.152:7004
org.redisson.client.RedisTimeoutException: Command execution timeout for command: (CLUSTER NODES), command params: [], Redis client: [addr=redis://10.92.82.152:7004]
	at org.redisson.client.RedisConnection$1.run(RedisConnection.java:209)
	at io.netty.util.concurrent.PromiseTask.runTask(PromiseTask.java:98)
	at io.netty.util.concurrent.ScheduledFutureTask.run(ScheduledFutureTask.java:150)
	at io.netty.util.concurrent.AbstractEventExecutor.safeExecute(AbstractEventExecutor.java:163)
	at io.netty.util.concurrent.SingleThreadEventExecutor.runAllTasks(SingleThreadEventExecutor.java:510)
	at io.netty.channel.nio.NioEventLoop.run(NioEventLoop.java:518)
	at io.netty.util.concurrent.SingleThreadEventExecutor$6.run(SingleThreadEventExecutor.java:1050)
	at io.netty.util.internal.ThreadExecutorMap$2.run(ThreadExecutorMap.java:74)
	at io.netty.util.concurrent.FastThreadLocalRunnable.run(FastThreadLocalRunnable.java:30)
	at java.lang.Thread.run(Thread.java:748)
2025-06-06 07:29:25.660 [pool-14-thread-8] ERROR org.springframework.scheduling.support.TaskUtils$LoggingErrorHandler.handleError Line:95 - Unexpected error occurred in scheduled task
org.redisson.client.RedisResponseTimeoutException: Redis server response timeout (3000 ms) occured after 3 retry attempts. Command: (EVAL), params: [while true do local firstThreadId2 = redis.call('lindex', KEYS[2], 0);if firstThreadId2 == false the..., 3, redisson_lock_key_prefix_cache:key:hnjjwz:master_lock, redisson_lock_queue:{redisson_lock_key_prefix_cache:key:hnjjwz:master_lock}, redisson_lock_timeout:{redisson_lock_key_prefix_cache:key:hnjjwz:master_lock}, 5000, c4ae4f96-ca70-480c-a479-a1d886a4b15d:638, 1749166158302, 1749166153302], channel: [id: 0xd3c5d11c, L:/10.87.57.73:58131 - R:10.92.82.152/10.92.82.152:7003]
	at org.redisson.command.CommandAsyncService$8.run(CommandAsyncService.java:935)
	at io.netty.util.HashedWheelTimer$HashedWheelTimeout.expire(HashedWheelTimer.java:680)
	at io.netty.util.HashedWheelTimer$HashedWheelBucket.expireTimeouts(HashedWheelTimer.java:755)
	at io.netty.util.HashedWheelTimer$Worker.run(HashedWheelTimer.java:483)
	at java.lang.Thread.run(Thread.java:748)
2025-06-06 07:29:32.253 [redisson-netty-5-7] ERROR org.redisson.client.handler.CommandsQueue.exceptionCaught Line:153 - Exception occured. Channel: [id: 0xad820ef7, L:/10.87.57.73:58354 - R:10.92.82.152/10.92.82.152:7003]
java.io.IOException: 远程主机强迫关闭了一个现有的连接。
	at sun.nio.ch.SocketDispatcher.read0(Native Method)
	at sun.nio.ch.SocketDispatcher.read(SocketDispatcher.java:43)
	at sun.nio.ch.IOUtil.readIntoNativeBuffer(IOUtil.java:223)
	at sun.nio.ch.IOUtil.read(IOUtil.java:192)
	at sun.nio.ch.SocketChannelImpl.read(SocketChannelImpl.java:380)
	at io.netty.buffer.PooledByteBuf.setBytes(PooledByteBuf.java:247)
	at io.netty.buffer.AbstractByteBuf.writeBytes(AbstractByteBuf.java:1147)
	at io.netty.channel.socket.nio.NioSocketChannel.doReadBytes(NioSocketChannel.java:350)
	at io.netty.channel.nio.AbstractNioByteChannel$NioByteUnsafe.read(AbstractNioByteChannel.java:148)
	at io.netty.channel.nio.NioEventLoop.processSelectedKey(NioEventLoop.java:700)
	at io.netty.channel.nio.NioEventLoop.processSelectedKeysOptimized(NioEventLoop.java:635)
	at io.netty.channel.nio.NioEventLoop.processSelectedKeys(NioEventLoop.java:552)
	at io.netty.channel.nio.NioEventLoop.run(NioEventLoop.java:514)
	at io.netty.util.concurrent.SingleThreadEventExecutor$6.run(SingleThreadEventExecutor.java:1050)
	at io.netty.util.internal.ThreadExecutorMap$2.run(ThreadExecutorMap.java:74)
	at io.netty.util.concurrent.FastThreadLocalRunnable.run(FastThreadLocalRunnable.java:30)
	at java.lang.Thread.run(Thread.java:748)
2025-06-06 07:29:32.452 [pool-14-thread-3] ERROR org.springframework.scheduling.support.TaskUtils$LoggingErrorHandler.handleError Line:95 - Unexpected error occurred in scheduled task
org.springframework.data.redis.RedisSystemException: Redis exception; nested exception is io.lettuce.core.RedisException: java.io.IOException: 远程主机强迫关闭了一个现有的连接。
	at org.springframework.data.redis.connection.lettuce.LettuceExceptionConverter.convert(LettuceExceptionConverter.java:74)
	at org.springframework.data.redis.connection.lettuce.LettuceExceptionConverter.convert(LettuceExceptionConverter.java:41)
	at org.springframework.data.redis.PassThroughExceptionTranslationStrategy.translate(PassThroughExceptionTranslationStrategy.java:44)
	at org.springframework.data.redis.FallbackExceptionTranslationStrategy.translate(FallbackExceptionTranslationStrategy.java:42)
	at org.springframework.data.redis.connection.lettuce.LettuceConnection.convertLettuceAccessException(LettuceConnection.java:270)
	at org.springframework.data.redis.connection.lettuce.LettuceSetCommands.convertLettuceAccessException(LettuceSetCommands.java:520)
	at org.springframework.data.redis.connection.lettuce.LettuceSetCommands.sMembers(LettuceSetCommands.java:245)
	at org.springframework.data.redis.connection.DefaultedRedisConnection.sMembers(DefaultedRedisConnection.java:729)
	at org.springframework.data.redis.core.DefaultSetOperations.lambda$members$10(DefaultSetOperations.java:214)
	at org.springframework.data.redis.core.RedisTemplate.execute(RedisTemplate.java:228)
	at org.springframework.data.redis.core.RedisTemplate.execute(RedisTemplate.java:188)
	at org.springframework.data.redis.core.AbstractOperations.execute(AbstractOperations.java:96)
	at org.springframework.data.redis.core.DefaultSetOperations.members(DefaultSetOperations.java:214)
	at org.springframework.data.redis.core.DefaultBoundSetOperations.members(DefaultBoundSetOperations.java:152)
	at org.springframework.session.data.redis.RedisSessionExpirationPolicy.cleanExpiredSessions(RedisSessionExpirationPolicy.java:129)
	at org.springframework.session.data.redis.RedisIndexedSessionRepository.cleanupExpiredSessions(RedisIndexedSessionRepository.java:407)
	at org.springframework.scheduling.support.DelegatingErrorHandlingRunnable.run(DelegatingErrorHandlingRunnable.java:54)
	at org.springframework.scheduling.concurrent.ReschedulingRunnable.run(ReschedulingRunnable.java:93)
	at java.util.concurrent.Executors$RunnableAdapter.call(Executors.java:511)
	at java.util.concurrent.FutureTask.run(FutureTask.java:266)
	at java.util.concurrent.ScheduledThreadPoolExecutor$ScheduledFutureTask.access$201(ScheduledThreadPoolExecutor.java:180)
	at java.util.concurrent.ScheduledThreadPoolExecutor$ScheduledFutureTask.run(ScheduledThreadPoolExecutor.java:293)
	at java.util.concurrent.ThreadPoolExecutor.runWorker(ThreadPoolExecutor.java:1149)
	at java.util.concurrent.ThreadPoolExecutor$Worker.run(ThreadPoolExecutor.java:624)
	at java.lang.Thread.run(Thread.java:748)
Caused by: io.lettuce.core.RedisException: java.io.IOException: 远程主机强迫关闭了一个现有的连接。
	at io.lettuce.core.LettuceFutures.awaitOrCancel(LettuceFutures.java:129)
	at io.lettuce.core.cluster.ClusterFutureSyncInvocationHandler.handleInvocation(ClusterFutureSyncInvocationHandler.java:123)
	at io.lettuce.core.internal.AbstractInvocationHandler.invoke(AbstractInvocationHandler.java:80)
	at com.sun.proxy.$Proxy247.smembers(Unknown Source)
	at org.springframework.data.redis.connection.lettuce.LettuceSetCommands.sMembers(LettuceSetCommands.java:243)
	... 18 common frames omitted
Caused by: java.io.IOException: 远程主机强迫关闭了一个现有的连接。
	at sun.nio.ch.SocketDispatcher.read0(Native Method)
	at sun.nio.ch.SocketDispatcher.read(SocketDispatcher.java:43)
	at sun.nio.ch.IOUtil.readIntoNativeBuffer(IOUtil.java:223)
	at sun.nio.ch.IOUtil.read(IOUtil.java:192)
	at sun.nio.ch.SocketChannelImpl.read(SocketChannelImpl.java:380)
	at io.netty.buffer.PooledByteBuf.setBytes(PooledByteBuf.java:247)
	at io.netty.buffer.AbstractByteBuf.writeBytes(AbstractByteBuf.java:1147)
	at io.netty.channel.socket.nio.NioSocketChannel.doReadBytes(NioSocketChannel.java:350)
	at io.netty.channel.nio.AbstractNioByteChannel$NioByteUnsafe.read(AbstractNioByteChannel.java:148)
	at io.netty.channel.nio.NioEventLoop.processSelectedKey(NioEventLoop.java:700)
	at io.netty.channel.nio.NioEventLoop.processSelectedKeysOptimized(NioEventLoop.java:635)
	at io.netty.channel.nio.NioEventLoop.processSelectedKeys(NioEventLoop.java:552)
	at io.netty.channel.nio.NioEventLoop.run(NioEventLoop.java:514)
	at io.netty.util.concurrent.SingleThreadEventExecutor$6.run(SingleThreadEventExecutor.java:1050)
	at io.netty.util.internal.ThreadExecutorMap$2.run(ThreadExecutorMap.java:74)
	at io.netty.util.concurrent.FastThreadLocalRunnable.run(FastThreadLocalRunnable.java:30)
	... 1 common frames omitted
2025-06-06 07:29:35.315 [redisson-netty-5-16] ERROR org.redisson.client.handler.CommandsQueue.exceptionCaught Line:153 - Exception occured. Channel: [id: 0x489ffeab, L:/10.87.57.73:60063 - R:10.92.82.152/10.92.82.152:7003]
java.io.IOException: 远程主机强迫关闭了一个现有的连接。
	at sun.nio.ch.SocketDispatcher.read0(Native Method)
	at sun.nio.ch.SocketDispatcher.read(SocketDispatcher.java:43)
	at sun.nio.ch.IOUtil.readIntoNativeBuffer(IOUtil.java:223)
	at sun.nio.ch.IOUtil.read(IOUtil.java:192)
	at sun.nio.ch.SocketChannelImpl.read(SocketChannelImpl.java:380)
	at io.netty.buffer.PooledByteBuf.setBytes(PooledByteBuf.java:247)
	at io.netty.buffer.AbstractByteBuf.writeBytes(AbstractByteBuf.java:1147)
	at io.netty.channel.socket.nio.NioSocketChannel.doReadBytes(NioSocketChannel.java:350)
	at io.netty.channel.nio.AbstractNioByteChannel$NioByteUnsafe.read(AbstractNioByteChannel.java:148)
	at io.netty.channel.nio.NioEventLoop.processSelectedKey(NioEventLoop.java:700)
	at io.netty.channel.nio.NioEventLoop.processSelectedKeysOptimized(NioEventLoop.java:635)
	at io.netty.channel.nio.NioEventLoop.processSelectedKeys(NioEventLoop.java:552)
	at io.netty.channel.nio.NioEventLoop.run(NioEventLoop.java:514)
	at io.netty.util.concurrent.SingleThreadEventExecutor$6.run(SingleThreadEventExecutor.java:1050)
	at io.netty.util.internal.ThreadExecutorMap$2.run(ThreadExecutorMap.java:74)
	at io.netty.util.concurrent.FastThreadLocalRunnable.run(FastThreadLocalRunnable.java:30)
	at java.lang.Thread.run(Thread.java:748)
2025-06-06 07:29:38.399 [redisson-netty-5-17] ERROR org.redisson.client.handler.CommandsQueue.exceptionCaught Line:153 - Exception occured. Channel: [id: 0xea43b81e, L:/10.87.57.73:58143 - R:10.92.82.152/10.92.82.152:7003]
java.io.IOException: 远程主机强迫关闭了一个现有的连接。
	at sun.nio.ch.SocketDispatcher.read0(Native Method)
	at sun.nio.ch.SocketDispatcher.read(SocketDispatcher.java:43)
	at sun.nio.ch.IOUtil.readIntoNativeBuffer(IOUtil.java:223)
	at sun.nio.ch.IOUtil.read(IOUtil.java:192)
	at sun.nio.ch.SocketChannelImpl.read(SocketChannelImpl.java:380)
	at io.netty.buffer.PooledByteBuf.setBytes(PooledByteBuf.java:247)
	at io.netty.buffer.AbstractByteBuf.writeBytes(AbstractByteBuf.java:1147)
	at io.netty.channel.socket.nio.NioSocketChannel.doReadBytes(NioSocketChannel.java:350)
	at io.netty.channel.nio.AbstractNioByteChannel$NioByteUnsafe.read(AbstractNioByteChannel.java:148)
	at io.netty.channel.nio.NioEventLoop.processSelectedKey(NioEventLoop.java:700)
	at io.netty.channel.nio.NioEventLoop.processSelectedKeysOptimized(NioEventLoop.java:635)
	at io.netty.channel.nio.NioEventLoop.processSelectedKeys(NioEventLoop.java:552)
	at io.netty.channel.nio.NioEventLoop.run(NioEventLoop.java:514)
	at io.netty.util.concurrent.SingleThreadEventExecutor$6.run(SingleThreadEventExecutor.java:1050)
	at io.netty.util.internal.ThreadExecutorMap$2.run(ThreadExecutorMap.java:74)
	at io.netty.util.concurrent.FastThreadLocalRunnable.run(FastThreadLocalRunnable.java:30)
	at java.lang.Thread.run(Thread.java:748)
2025-06-06 07:29:41.501 [redisson-netty-5-13] ERROR org.redisson.client.handler.CommandsQueue.exceptionCaught Line:153 - Exception occured. Channel: [id: 0xd3c5d11c, L:/10.87.57.73:58131 - R:10.92.82.152/10.92.82.152:7003]
java.io.IOException: 远程主机强迫关闭了一个现有的连接。
	at sun.nio.ch.SocketDispatcher.read0(Native Method)
	at sun.nio.ch.SocketDispatcher.read(SocketDispatcher.java:43)
	at sun.nio.ch.IOUtil.readIntoNativeBuffer(IOUtil.java:223)
	at sun.nio.ch.IOUtil.read(IOUtil.java:192)
	at sun.nio.ch.SocketChannelImpl.read(SocketChannelImpl.java:380)
	at io.netty.buffer.PooledByteBuf.setBytes(PooledByteBuf.java:247)
	at io.netty.buffer.AbstractByteBuf.writeBytes(AbstractByteBuf.java:1147)
	at io.netty.channel.socket.nio.NioSocketChannel.doReadBytes(NioSocketChannel.java:350)
	at io.netty.channel.nio.AbstractNioByteChannel$NioByteUnsafe.read(AbstractNioByteChannel.java:148)
	at io.netty.channel.nio.NioEventLoop.processSelectedKey(NioEventLoop.java:700)
	at io.netty.channel.nio.NioEventLoop.processSelectedKeysOptimized(NioEventLoop.java:635)
	at io.netty.channel.nio.NioEventLoop.processSelectedKeys(NioEventLoop.java:552)
	at io.netty.channel.nio.NioEventLoop.run(NioEventLoop.java:514)
	at io.netty.util.concurrent.SingleThreadEventExecutor$6.run(SingleThreadEventExecutor.java:1050)
	at io.netty.util.internal.ThreadExecutorMap$2.run(ThreadExecutorMap.java:74)
	at io.netty.util.concurrent.FastThreadLocalRunnable.run(FastThreadLocalRunnable.java:30)
	at java.lang.Thread.run(Thread.java:748)
2025-06-06 07:49:44.386 [redisson-netty-5-15] ERROR org.redisson.cluster.ClusterConnectionManager.lambda$updateClusterState$5 Line:363 - Can't execute CLUSTER_NODES with 10.92.82.152/10.92.82.152:7003
org.redisson.client.RedisTimeoutException: Command execution timeout for command: (CLUSTER NODES), command params: [], Redis client: [addr=redis://10.92.82.152:7003]
	at org.redisson.client.RedisConnection$1.run(RedisConnection.java:209)
	at io.netty.util.concurrent.PromiseTask.runTask(PromiseTask.java:98)
	at io.netty.util.concurrent.ScheduledFutureTask.run(ScheduledFutureTask.java:150)
	at io.netty.util.concurrent.AbstractEventExecutor.safeExecute(AbstractEventExecutor.java:163)
	at io.netty.util.concurrent.SingleThreadEventExecutor.runAllTasks(SingleThreadEventExecutor.java:510)
	at io.netty.channel.nio.NioEventLoop.run(NioEventLoop.java:518)
	at io.netty.util.concurrent.SingleThreadEventExecutor$6.run(SingleThreadEventExecutor.java:1050)
	at io.netty.util.internal.ThreadExecutorMap$2.run(ThreadExecutorMap.java:74)
	at io.netty.util.concurrent.FastThreadLocalRunnable.run(FastThreadLocalRunnable.java:30)
	at java.lang.Thread.run(Thread.java:748)
2025-06-06 07:49:47.611 [redisson-netty-5-1] ERROR org.redisson.cluster.ClusterConnectionManager.lambda$updateClusterState$5 Line:363 - Can't execute CLUSTER_NODES with ************/************:7001
org.redisson.client.RedisTimeoutException: Command execution timeout for command: (CLUSTER NODES), command params: [], Redis client: [addr=redis://************:7001]
	at org.redisson.client.RedisConnection$1.run(RedisConnection.java:209)
	at io.netty.util.concurrent.PromiseTask.runTask(PromiseTask.java:98)
	at io.netty.util.concurrent.ScheduledFutureTask.run(ScheduledFutureTask.java:150)
	at io.netty.util.concurrent.AbstractEventExecutor.safeExecute(AbstractEventExecutor.java:163)
	at io.netty.util.concurrent.SingleThreadEventExecutor.runAllTasks(SingleThreadEventExecutor.java:510)
	at io.netty.channel.nio.NioEventLoop.run(NioEventLoop.java:518)
	at io.netty.util.concurrent.SingleThreadEventExecutor$6.run(SingleThreadEventExecutor.java:1050)
	at io.netty.util.internal.ThreadExecutorMap$2.run(ThreadExecutorMap.java:74)
	at io.netty.util.concurrent.FastThreadLocalRunnable.run(FastThreadLocalRunnable.java:30)
	at java.lang.Thread.run(Thread.java:748)
2025-06-06 07:49:47.613 [redisson-netty-5-1] ERROR org.redisson.cluster.ClusterConnectionManager.checkClusterState Line:336 - Can't update cluster state
org.redisson.client.RedisTimeoutException: Command execution timeout for command: (CLUSTER NODES), command params: [], Redis client: [addr=redis://************:7001]
	at org.redisson.client.RedisConnection$1.run(RedisConnection.java:209)
	at io.netty.util.concurrent.PromiseTask.runTask(PromiseTask.java:98)
	at io.netty.util.concurrent.ScheduledFutureTask.run(ScheduledFutureTask.java:150)
	at io.netty.util.concurrent.AbstractEventExecutor.safeExecute(AbstractEventExecutor.java:163)
	at io.netty.util.concurrent.SingleThreadEventExecutor.runAllTasks(SingleThreadEventExecutor.java:510)
	at io.netty.channel.nio.NioEventLoop.run(NioEventLoop.java:518)
	at io.netty.util.concurrent.SingleThreadEventExecutor$6.run(SingleThreadEventExecutor.java:1050)
	at io.netty.util.internal.ThreadExecutorMap$2.run(ThreadExecutorMap.java:74)
	at io.netty.util.concurrent.FastThreadLocalRunnable.run(FastThreadLocalRunnable.java:30)
	at java.lang.Thread.run(Thread.java:748)
2025-06-06 07:49:54.644 [redisson-netty-5-26] ERROR org.redisson.cluster.ClusterConnectionManager.lambda$updateClusterState$5 Line:363 - Can't execute CLUSTER_NODES with 10.92.82.152/10.92.82.152:7003
org.redisson.client.RedisTimeoutException: Command execution timeout for command: (CLUSTER NODES), command params: [], Redis client: [addr=redis://10.92.82.152:7003]
	at org.redisson.client.RedisConnection$1.run(RedisConnection.java:209)
	at io.netty.util.concurrent.PromiseTask.runTask(PromiseTask.java:98)
	at io.netty.util.concurrent.ScheduledFutureTask.run(ScheduledFutureTask.java:150)
	at io.netty.util.concurrent.AbstractEventExecutor.safeExecute(AbstractEventExecutor.java:163)
	at io.netty.util.concurrent.SingleThreadEventExecutor.runAllTasks(SingleThreadEventExecutor.java:510)
	at io.netty.channel.nio.NioEventLoop.run(NioEventLoop.java:518)
	at io.netty.util.concurrent.SingleThreadEventExecutor$6.run(SingleThreadEventExecutor.java:1050)
	at io.netty.util.internal.ThreadExecutorMap$2.run(ThreadExecutorMap.java:74)
	at io.netty.util.concurrent.FastThreadLocalRunnable.run(FastThreadLocalRunnable.java:30)
	at java.lang.Thread.run(Thread.java:748)
2025-06-06 08:10:33.439 [redisson-netty-5-1] ERROR org.redisson.cluster.ClusterConnectionManager.lambda$updateClusterState$5 Line:363 - Can't execute CLUSTER_NODES with 10.92.82.152/10.92.82.152:7003
org.redisson.client.RedisTimeoutException: Command execution timeout for command: (CLUSTER NODES), command params: [], Redis client: [addr=redis://10.92.82.152:7003]
	at org.redisson.client.RedisConnection$1.run(RedisConnection.java:209)
	at io.netty.util.concurrent.PromiseTask.runTask(PromiseTask.java:98)
	at io.netty.util.concurrent.ScheduledFutureTask.run(ScheduledFutureTask.java:150)
	at io.netty.util.concurrent.AbstractEventExecutor.safeExecute(AbstractEventExecutor.java:163)
	at io.netty.util.concurrent.SingleThreadEventExecutor.runAllTasks(SingleThreadEventExecutor.java:510)
	at io.netty.channel.nio.NioEventLoop.run(NioEventLoop.java:518)
	at io.netty.util.concurrent.SingleThreadEventExecutor$6.run(SingleThreadEventExecutor.java:1050)
	at io.netty.util.internal.ThreadExecutorMap$2.run(ThreadExecutorMap.java:74)
	at io.netty.util.concurrent.FastThreadLocalRunnable.run(FastThreadLocalRunnable.java:30)
	at java.lang.Thread.run(Thread.java:748)
2025-06-06 12:28:07.602 [redisson-netty-5-6] ERROR org.redisson.cluster.ClusterConnectionManager.lambda$updateClusterState$5 Line:363 - Can't execute CLUSTER_NODES with 10.92.82.152/10.92.82.152:7003
org.redisson.client.RedisTimeoutException: Command execution timeout for command: (CLUSTER NODES), command params: [], Redis client: [addr=redis://10.92.82.152:7003]
	at org.redisson.client.RedisConnection$1.run(RedisConnection.java:209)
	at io.netty.util.concurrent.PromiseTask.runTask(PromiseTask.java:98)
	at io.netty.util.concurrent.ScheduledFutureTask.run(ScheduledFutureTask.java:150)
	at io.netty.util.concurrent.AbstractEventExecutor.safeExecute(AbstractEventExecutor.java:163)
	at io.netty.util.concurrent.SingleThreadEventExecutor.runAllTasks(SingleThreadEventExecutor.java:510)
	at io.netty.channel.nio.NioEventLoop.run(NioEventLoop.java:518)
	at io.netty.util.concurrent.SingleThreadEventExecutor$6.run(SingleThreadEventExecutor.java:1050)
	at io.netty.util.internal.ThreadExecutorMap$2.run(ThreadExecutorMap.java:74)
	at io.netty.util.concurrent.FastThreadLocalRunnable.run(FastThreadLocalRunnable.java:30)
	at java.lang.Thread.run(Thread.java:748)
2025-06-06 12:48:52.140 [redisson-netty-5-4] ERROR org.redisson.cluster.ClusterConnectionManager.lambda$updateClusterState$5 Line:363 - Can't execute CLUSTER_NODES with 10.92.82.152/10.92.82.152:7003
org.redisson.client.RedisTimeoutException: Command execution timeout for command: (CLUSTER NODES), command params: [], Redis client: [addr=redis://10.92.82.152:7003]
	at org.redisson.client.RedisConnection$1.run(RedisConnection.java:209)
	at io.netty.util.concurrent.PromiseTask.runTask(PromiseTask.java:98)
	at io.netty.util.concurrent.ScheduledFutureTask.run(ScheduledFutureTask.java:150)
	at io.netty.util.concurrent.AbstractEventExecutor.safeExecute(AbstractEventExecutor.java:163)
	at io.netty.util.concurrent.SingleThreadEventExecutor.runAllTasks(SingleThreadEventExecutor.java:510)
	at io.netty.channel.nio.NioEventLoop.run(NioEventLoop.java:518)
	at io.netty.util.concurrent.SingleThreadEventExecutor$6.run(SingleThreadEventExecutor.java:1050)
	at io.netty.util.internal.ThreadExecutorMap$2.run(ThreadExecutorMap.java:74)
	at io.netty.util.concurrent.FastThreadLocalRunnable.run(FastThreadLocalRunnable.java:30)
	at java.lang.Thread.run(Thread.java:748)
2025-06-06 12:48:55.146 [redisson-netty-5-5] ERROR org.redisson.cluster.ClusterConnectionManager.lambda$updateClusterState$5 Line:363 - Can't execute CLUSTER_NODES with 10.92.82.153/10.92.82.153:7005
org.redisson.client.RedisTimeoutException: Command execution timeout for command: (CLUSTER NODES), command params: [], Redis client: [addr=redis://10.92.82.153:7005]
	at org.redisson.client.RedisConnection$1.run(RedisConnection.java:209)
	at io.netty.util.concurrent.PromiseTask.runTask(PromiseTask.java:98)
	at io.netty.util.concurrent.ScheduledFutureTask.run(ScheduledFutureTask.java:150)
	at io.netty.util.concurrent.AbstractEventExecutor.safeExecute(AbstractEventExecutor.java:163)
	at io.netty.util.concurrent.SingleThreadEventExecutor.runAllTasks(SingleThreadEventExecutor.java:510)
	at io.netty.channel.nio.NioEventLoop.run(NioEventLoop.java:518)
	at io.netty.util.concurrent.SingleThreadEventExecutor$6.run(SingleThreadEventExecutor.java:1050)
	at io.netty.util.internal.ThreadExecutorMap$2.run(ThreadExecutorMap.java:74)
	at io.netty.util.concurrent.FastThreadLocalRunnable.run(FastThreadLocalRunnable.java:30)
	at java.lang.Thread.run(Thread.java:748)
2025-06-06 12:48:58.149 [redisson-netty-5-6] ERROR org.redisson.cluster.ClusterConnectionManager.lambda$updateClusterState$5 Line:363 - Can't execute CLUSTER_NODES with 10.92.82.152/10.92.82.152:7004
org.redisson.client.RedisTimeoutException: Command execution timeout for command: (CLUSTER NODES), command params: [], Redis client: [addr=redis://10.92.82.152:7004]
	at org.redisson.client.RedisConnection$1.run(RedisConnection.java:209)
	at io.netty.util.concurrent.PromiseTask.runTask(PromiseTask.java:98)
	at io.netty.util.concurrent.ScheduledFutureTask.run(ScheduledFutureTask.java:150)
	at io.netty.util.concurrent.AbstractEventExecutor.safeExecute(AbstractEventExecutor.java:163)
	at io.netty.util.concurrent.SingleThreadEventExecutor.runAllTasks(SingleThreadEventExecutor.java:510)
	at io.netty.channel.nio.NioEventLoop.run(NioEventLoop.java:518)
	at io.netty.util.concurrent.SingleThreadEventExecutor$6.run(SingleThreadEventExecutor.java:1050)
	at io.netty.util.internal.ThreadExecutorMap$2.run(ThreadExecutorMap.java:74)
	at io.netty.util.concurrent.FastThreadLocalRunnable.run(FastThreadLocalRunnable.java:30)
	at java.lang.Thread.run(Thread.java:748)
2025-06-06 13:09:35.847 [redisson-netty-5-6] ERROR org.redisson.cluster.ClusterConnectionManager.lambda$updateClusterState$5 Line:363 - Can't execute CLUSTER_NODES with 10.92.82.152/10.92.82.152:7003
org.redisson.client.RedisTimeoutException: Command execution timeout for command: (CLUSTER NODES), command params: [], Redis client: [addr=redis://10.92.82.152:7003]
	at org.redisson.client.RedisConnection$1.run(RedisConnection.java:209)
	at io.netty.util.concurrent.PromiseTask.runTask(PromiseTask.java:98)
	at io.netty.util.concurrent.ScheduledFutureTask.run(ScheduledFutureTask.java:150)
	at io.netty.util.concurrent.AbstractEventExecutor.safeExecute(AbstractEventExecutor.java:163)
	at io.netty.util.concurrent.SingleThreadEventExecutor.runAllTasks(SingleThreadEventExecutor.java:510)
	at io.netty.channel.nio.NioEventLoop.run(NioEventLoop.java:518)
	at io.netty.util.concurrent.SingleThreadEventExecutor$6.run(SingleThreadEventExecutor.java:1050)
	at io.netty.util.internal.ThreadExecutorMap$2.run(ThreadExecutorMap.java:74)
	at io.netty.util.concurrent.FastThreadLocalRunnable.run(FastThreadLocalRunnable.java:30)
	at java.lang.Thread.run(Thread.java:748)
2025-06-06 13:30:20.330 [redisson-netty-5-15] ERROR org.redisson.cluster.ClusterConnectionManager.lambda$updateClusterState$5 Line:363 - Can't execute CLUSTER_NODES with 10.92.82.152/10.92.82.152:7003
org.redisson.client.RedisTimeoutException: Command execution timeout for command: (CLUSTER NODES), command params: [], Redis client: [addr=redis://10.92.82.152:7003]
	at org.redisson.client.RedisConnection$1.run(RedisConnection.java:209)
	at io.netty.util.concurrent.PromiseTask.runTask(PromiseTask.java:98)
	at io.netty.util.concurrent.ScheduledFutureTask.run(ScheduledFutureTask.java:150)
	at io.netty.util.concurrent.AbstractEventExecutor.safeExecute(AbstractEventExecutor.java:163)
	at io.netty.util.concurrent.SingleThreadEventExecutor.runAllTasks(SingleThreadEventExecutor.java:510)
	at io.netty.channel.nio.NioEventLoop.run(NioEventLoop.java:518)
	at io.netty.util.concurrent.SingleThreadEventExecutor$6.run(SingleThreadEventExecutor.java:1050)
	at io.netty.util.internal.ThreadExecutorMap$2.run(ThreadExecutorMap.java:74)
	at io.netty.util.concurrent.FastThreadLocalRunnable.run(FastThreadLocalRunnable.java:30)
	at java.lang.Thread.run(Thread.java:748)
2025-06-06 13:51:01.030 [redisson-netty-5-32] ERROR org.redisson.cluster.ClusterConnectionManager.lambda$updateClusterState$5 Line:363 - Can't execute CLUSTER_NODES with 10.92.82.152/10.92.82.152:7003
org.redisson.client.RedisTimeoutException: Command execution timeout for command: (CLUSTER NODES), command params: [], Redis client: [addr=redis://10.92.82.152:7003]
	at org.redisson.client.RedisConnection$1.run(RedisConnection.java:209)
	at io.netty.util.concurrent.PromiseTask.runTask(PromiseTask.java:98)
	at io.netty.util.concurrent.ScheduledFutureTask.run(ScheduledFutureTask.java:150)
	at io.netty.util.concurrent.AbstractEventExecutor.safeExecute(AbstractEventExecutor.java:163)
	at io.netty.util.concurrent.SingleThreadEventExecutor.runAllTasks(SingleThreadEventExecutor.java:510)
	at io.netty.channel.nio.NioEventLoop.run(NioEventLoop.java:518)
	at io.netty.util.concurrent.SingleThreadEventExecutor$6.run(SingleThreadEventExecutor.java:1050)
	at io.netty.util.internal.ThreadExecutorMap$2.run(ThreadExecutorMap.java:74)
	at io.netty.util.concurrent.FastThreadLocalRunnable.run(FastThreadLocalRunnable.java:30)
	at java.lang.Thread.run(Thread.java:748)
2025-06-06 13:51:04.041 [redisson-netty-5-1] ERROR org.redisson.cluster.ClusterConnectionManager.lambda$updateClusterState$5 Line:363 - Can't execute CLUSTER_NODES with 10.92.82.153/10.92.82.153:7006
org.redisson.client.RedisTimeoutException: Command execution timeout for command: (CLUSTER NODES), command params: [], Redis client: [addr=redis://10.92.82.153:7006]
	at org.redisson.client.RedisConnection$1.run(RedisConnection.java:209)
	at io.netty.util.concurrent.PromiseTask.runTask(PromiseTask.java:98)
	at io.netty.util.concurrent.ScheduledFutureTask.run(ScheduledFutureTask.java:150)
	at io.netty.util.concurrent.AbstractEventExecutor.safeExecute(AbstractEventExecutor.java:163)
	at io.netty.util.concurrent.SingleThreadEventExecutor.runAllTasks(SingleThreadEventExecutor.java:510)
	at io.netty.channel.nio.NioEventLoop.run(NioEventLoop.java:518)
	at io.netty.util.concurrent.SingleThreadEventExecutor$6.run(SingleThreadEventExecutor.java:1050)
	at io.netty.util.internal.ThreadExecutorMap$2.run(ThreadExecutorMap.java:74)
	at io.netty.util.concurrent.FastThreadLocalRunnable.run(FastThreadLocalRunnable.java:30)
	at java.lang.Thread.run(Thread.java:748)
2025-06-06 13:51:04.052 [redisson-netty-5-25] ERROR org.redisson.cluster.ClusterConnectionManager.checkClusterState Line:336 - Can't update cluster state
io.netty.channel.AbstractChannel$AnnotatedNoRouteToHostException: No route to host: no further information: ************/************:7001
Caused by: java.net.NoRouteToHostException: No route to host: no further information
	at sun.nio.ch.SocketChannelImpl.checkConnect(Native Method)
	at sun.nio.ch.SocketChannelImpl.finishConnect(SocketChannelImpl.java:717)
	at io.netty.channel.socket.nio.NioSocketChannel.doFinishConnect(NioSocketChannel.java:330)
	at io.netty.channel.nio.AbstractNioChannel$AbstractNioUnsafe.finishConnect(AbstractNioChannel.java:334)
	at io.netty.channel.nio.NioEventLoop.processSelectedKey(NioEventLoop.java:688)
	at io.netty.channel.nio.NioEventLoop.processSelectedKeysOptimized(NioEventLoop.java:635)
	at io.netty.channel.nio.NioEventLoop.processSelectedKeys(NioEventLoop.java:552)
	at io.netty.channel.nio.NioEventLoop.run(NioEventLoop.java:514)
	at io.netty.util.concurrent.SingleThreadEventExecutor$6.run(SingleThreadEventExecutor.java:1050)
	at io.netty.util.internal.ThreadExecutorMap$2.run(ThreadExecutorMap.java:74)
	at io.netty.util.concurrent.FastThreadLocalRunnable.run(FastThreadLocalRunnable.java:30)
	at java.lang.Thread.run(Thread.java:748)
