package com.simbest.boot.hnjjwz.examOnline.service.impl;

import com.simbest.boot.base.exception.Exceptions;
import com.simbest.boot.base.service.impl.LogicService;
import com.simbest.boot.base.web.response.JsonResponse;
import com.simbest.boot.hnjjwz.examOnline.model.*;
import com.simbest.boot.hnjjwz.examOnline.repository.ExamQuestionBankRepository;
import com.simbest.boot.hnjjwz.examOnline.service.*;
import com.simbest.boot.util.json.JacksonUtils;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;

import java.util.ArrayList;
import java.util.List;
import java.util.Map;

/**
 * <AUTHOR>
 * @Data 2019/05/08
 * @Description 题库
 */
@Slf4j
@Service
public class ExamQuestionBankServiceImpl extends LogicService<ExamQuestionBank,String> implements IExamQuestionBankService {

    private ExamQuestionBankRepository examQuestionBankRepository;

    @Autowired
    public ExamQuestionBankServiceImpl ( ExamQuestionBankRepository examQuestionBankRepository) {
        super(examQuestionBankRepository);
        this.examQuestionBankRepository = examQuestionBankRepository;
    }

    @Autowired
    private IExamAnswerService examAnswerService;

    @Autowired
    private IExamQuestionAnswerService examQuestionAnswerService;

    @Autowired
    private IExamQuestionService examQuestionService;


    /**
     * 导入一套题
     * @param examQuestionBank
     * @return
     */
    @Override
    public JsonResponse importQuestionBank ( ExamQuestionBank examQuestionBank ) {
        try{
        //先判断是否存在题库，不存在就新增题库 否则更新某个题库的题
        int whetherDistinctBy = examQuestionBankRepository.findWhetherDistinctBy(examQuestionBank.getQuestionBankCode());
            if(whetherDistinctBy==0) {
            this.insert(examQuestionBank);
        }else{

            Map<Object,List<Object>> map = examQuestionBank.getExamQuestionList();
            String questionBankCode = examQuestionBank.getQuestionBankCode();


                ExamQuestionBank sizeExamQuestionBank = examQuestionBankRepository.findSizeExamQuestionBank(examQuestionBank.getQuestionBankCode());
                int number=Integer.parseInt(sizeExamQuestionBank.getQuestionBankSize());
                for(Map.Entry i:map.entrySet()){
                number++;
                ExamQuestion examQuestion=new ExamQuestion();//题目
                Object key= i.getKey();
                ArrayList<String> lsit=(ArrayList<String>)i.getValue();
                examQuestion.setQuestionCode(questionBankCode+"-"+number);
                examQuestion.setQuestionName(String.valueOf(key));


                //for(String str:lsit){
                int both=0;
                for(int j=0;j<lsit.size();j++){
                    String str =lsit.get(j);
                    ExamAnswer examAnswer=new ExamAnswer(); //答案

                    ExamQuestionAnswer examQuestionAnswer=new ExamQuestionAnswer(); //题目正确答案
                    if("A".equals(str)| "B".equals(str)| "C".equals(str)| "D".equals(str)){//做判断是因为把答案和正确选项一起传过来要分隔
                        both++;
                        switch (str){
                            case  "A": examQuestionAnswer.setAnswerContent(lsit.get(0));
                                break;
                            case  "B": examQuestionAnswer.setAnswerContent(lsit.get(1));
                                break;
                            case  "C": examQuestionAnswer.setAnswerContent(lsit.get(2));
                                break;
                            case  "D": examQuestionAnswer.setAnswerContent(lsit.get(3));
                        }

                        examQuestionAnswer.setQuestionCode(examQuestion.getQuestionCode());
                        //examQuestionAnswer.setAnswerContent(examAnswer.getAnswerName());
                        examQuestionAnswer.setAnswerCode(str);
                        examQuestionAnswer.setIsCorrect(true);
                        examQuestionAnswerService.insert(examQuestionAnswer);
                    }else{
                        switch (j){
                            case  0: examAnswer.setAnswerOption("A");
                                break;
                            case  1: examAnswer.setAnswerOption("B");
                                break;
                            case  2: examAnswer.setAnswerOption("C");
                                break;
                            case  3: examAnswer.setAnswerOption("D");
                        }
                        examAnswer.setAnswerCode(examQuestion.getQuestionCode());
                        examAnswer.setAnswerName(str);
                        examAnswerService.insert(examAnswer);
                    }

                }
                if(both>1){
                    examQuestion.setQuestionType("多选题");
                    examQuestionService.insert(examQuestion);
                }else {
                    examQuestion.setQuestionType("单选题");
                    examQuestionService.insert(examQuestion);
                }

            }

                examQuestionBankRepository.updateQuestionBankSize(String.valueOf(number),examQuestionBank.getQuestionBankCode());

        }

        }catch ( Exception e ){
            Exceptions.printException( e );
            return JsonResponse.fail( null,"导入题目失败！" );
        }
        return JsonResponse.success( null,"导入题目成功！" );
    }
}
