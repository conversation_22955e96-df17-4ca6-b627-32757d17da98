package com.simbest.boot.hnjjwz.examOnline.model;/**
 * Created by KZH on 2019/6/17 16:04.
 */

import com.simbest.boot.base.annotations.EntityIdPrefix;
import com.simbest.boot.base.model.LogicModel;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.*;
import org.hibernate.annotations.GenericGenerator;

import javax.persistence.*;

/**
 * <AUTHOR>
 * @create 2019-06-17 16:04
 * @desc 试卷属性
 **/
@Data
@EqualsAndHashCode(callSuper = true)
@NoArgsConstructor
@AllArgsConstructor
@Entity(name = "us_exam_attribute")
@ApiModel(value = "试卷属性")
public class ExamAttribute  extends LogicModel {

    @Id
    @Column(name = "id", length = 40)
    @GeneratedValue(generator = "snowFlakeId")
    @GenericGenerator(name = "snowFlakeId", strategy = "com.simbest.boot.util.distribution.id.SnowflakeId")
    @EntityIdPrefix(prefix = "EB") //主键前缀，此为可选项注解
    private String id;

    @Column(length = 250)
    @Setter
    @Getter
    @ApiModelProperty(value = "总题目数", required = true)
    private String topicSum;

    @Column(length = 250)
    @Setter
    @Getter
    @ApiModelProperty(value = "题库编码", required = true)
    private String questionBankCode;

    @Column(length = 250)
    @Setter
    @Getter
    @ApiModelProperty(value = "单选题比例", required = true)
    private String singleChoice;

    @Column(length = 250)
    @Setter
    @Getter
    @ApiModelProperty(value = "多选题比例", required = true)
    private String moreChoice;

    @Column(length = 250)
    @Setter
    @Getter
    @ApiModelProperty(value = "试卷规定时间限制", required = true)
    private String setTime;

    @Transient
    private String examName;

    @Transient
    private Object examMoreData=new Object();

    @Transient
    private Object examSingleData=new Object();
}
