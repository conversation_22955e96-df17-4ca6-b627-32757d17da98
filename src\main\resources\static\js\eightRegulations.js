// 定义程序编码映射
const PROGRAMA_CODE_MAP = {
    "directive": "025001", // 上级精神
    "regulation": "025002", // 纪法知识
    "warning": "025003"     // 警示曝光
};

// 等待页面加载完成
document.addEventListener('DOMContentLoaded', function() {
    console.log('eightRegulations.js loaded');
// 返回首页
    

    // 获取所有功能入口元素
    var functionEntries = document.querySelectorAll('.function-entry');
    console.log('Found ' + functionEntries.length + ' function entries');

    // 为每个功能入口添加点击事件
    functionEntries.forEach(function(entry) {
        entry.addEventListener('click', function(e) {
            console.log('Function entry clicked');

            // 获取当前点击的功能入口的类型和文字
            var type = this.getAttribute('data-type');
            var title = this.querySelector('span').innerText;
            console.log('Type: ' + type + ', Title: ' + title);

            // 移除所有功能入口的active类
            functionEntries.forEach(function(el) {
                el.classList.remove('active');
            });

            // 给当前点击的功能入口添加active类
            this.classList.add('active');
            console.log('Added active class to: ' + type);

            // 设置新闻列表标题
            var newsTitle = document.querySelector('.news-section h2');
            if (newsTitle) {
                newsTitle.innerText = title;
                console.log('Updated news title to: ' + title);
            } else {
                console.error('News title element not found');
            }

            // 加载相应的数据
            loadNewsData(1, type);

            // 阻止事件冒泡和默认行为
            e.preventDefault();
            e.stopPropagation();
            return false;
        });
    });

    // 确保默认选中上级精神
    var directiveEntry = document.querySelector('.function-entry.directive');
    if (directiveEntry) {
        directiveEntry.classList.add('active');
        console.log('Set directive entry as active by default');

        // 默认加载上级精神数据
        loadNewsData(1, "directive");
    }
    gotohome();
});
function gotohome() {
        var  baseUrl = window.location.href.indexOf('10.88.178.111:8088') !=-1 ? 'http://10.88.178.111:8088/oaWeb/hnjjwzWeb/#/' : 'http://10.92.82.161:8088/oaWeb/hnjjwzWeb/#/'
        var url = baseUrl + 'home?appCode=hnjjwz&username='+web.currentUser.username+'&loginuser='+getRsa(web.currentUser.username)+'&from=oa';
        window.open(url, '_blank');
    }

// 直接添加内联点击处理函数
function handleFunctionEntryClick(element) {
    console.log('handleFunctionEntryClick called');

    // 获取当前点击的功能入口的类型和文字
    var type = element.getAttribute('data-type');
    var title = element.querySelector('span').innerText;
    console.log('Type: ' + type + ', Title: ' + title);

    // 移除所有功能入口的active类
    /*document.querySelectorAll('.function-entry').forEach(function(el) {
        el.classList.remove('active');
    });*/
    var elements = document.querySelectorAll('.function-entry');
    for (var i = 0; i < elements.length; i++) {
        elements[i].classList.remove('active');
    }

    // 给当前点击的功能入口添加active类
    element.classList.add('active');
    console.log('Added active class to: ' + type);

    // 设置新闻列表标题
    var newsTitle = document.querySelector('.news-section h2');
    if (newsTitle) {
        newsTitle.innerText = title;
        console.log('Updated news title to: ' + title);
    }

    // 保存当前标题，以便在AJAX请求完成后恢复
    window.currentNewsTitle = title;

    // 加载相应的数据
    loadNewsData(type);

    return false;
}



// 加载新闻数据
// 将函数添加到全局作用域
window.loadNewsData = function(category) {
    console.log('Loading news data for category: ' + category);

    // 如果没有指定分类，使用当前选中的分类
    if (!category) {
        category = $('.function-entry.active').data('category') || 'upperDirective';
        console.log('Using current active category: ' + category);
    }

    // 获取对应的程序编码
    var programaCode = PROGRAMA_CODE_MAP[category];
    if (!programaCode) {
        console.error('Invalid category: ' + category);
        return;
    }

    // 构建请求参数 - 使用不分页接口
    var requestData = {
        programaCode: programaCode
        // 不再需要分页参数
    };

    console.log('Request data:', requestData);

    // 调用后端接口 - 使用新的不分页接口
    var url = "action/approvalForm/findDataDetailListNoPage";
    ajaxgeneral({
        url: url,
        data: requestData,
        type: "POST",
        contentType: "application/json;charset=UTF-8",
        success: function(res) {
            console.log('API response:', res);

            // 保存当前标题
            var currentTitle = document.querySelector('.news-section h2').innerText;

            // 设置页面标题
            $("#h6").html("深入贯彻中央八项规定精神学习教育——警示教育专区");

            // 导航路径
            var html = ["<img class='mr5' src='images/er1.jpg'/><a target='_blank' href='/../../webSite/index.html'>首页</a>"];
            html.push("><a href='javascript:void(0);'>深入贯彻中央八项规定精神学习教育——警示教育专区</a>");
            $(".navTit").html(html.join(""));

            // 恢复标题
            document.querySelector('.news-section h2').innerText = currentTitle;

            // 渲染新闻列表 - 适应新的返回格式
            if (res.data && res.data.length > 0) {
                var listHtml = "";
                for (var i = 0; i < res.data.length; i++) {
                    var item = res.data[i];
                    // 获取部门和发布人信息
                    var departmentName = item.BELONG_DEPARTMENT_NAME || '';
                    var authorName = item.TRUENAME || '系统管理员';
                    var authorInfo = departmentName ? (departmentName + ' ' + authorName) : authorName;

                    listHtml += '<div class="news-item">';
                    listHtml += '<div class="news-title"><a  target="_blank"  href="/hnjjwz/html/webSite/listDetails.html?pmInsId=' + item.PM_INS_ID + '">' + item.TITLE + '</a></div>';
                    listHtml += '<div class="news-info">';
                    listHtml += '<span class="news-date">' + item.CREATION_TIME + '</span>';
                    /*listHtml += '<span class="news-author">' + authorInfo + '</span>';*/
                    listHtml += '</div>';
                    listHtml += '</div>';
                }
                $(".news-list").html(listHtml);

                // 已取消分页显示
                $("#pages").empty();
            } else {
                // 显示无数据提示
                $(".news-list").html('<div class="no-data">暂无数据</div>');
                $("#pages").html("<p>暂无数据！</p>");
            }
        },
        error: function(xhr, status, error) {
            console.error('API error:', error);

            // 显示错误提示
            $(".news-list").html('<div class="error-data">加载数据失败，请稍后重试</div>');
            $("#pages").html("");
        }
    });
}

// 监听DOM变化，确保标题不会被覆盖
var observer = new MutationObserver(function(mutations) {
    mutations.forEach(function(mutation) {
        if (mutation.target.classList.contains('news-section') &&
            mutation.type === 'childList' &&
            window.currentNewsTitle) {

            var newsTitle = document.querySelector('.news-section h2');
            if (newsTitle && newsTitle.innerText !== window.currentNewsTitle) {
                newsTitle.innerText = window.currentNewsTitle;
                console.log('Restored title to: ' + window.currentNewsTitle + ' after DOM mutation');
            }
        }
    });
});

// 在页面加载完成后开始监听
document.addEventListener('DOMContentLoaded', function() {
    var newsSection = document.querySelector('.news-section');
    if (newsSection) {
        observer.observe(newsSection, { childList: true, subtree: true });
        console.log('Started observing news-section for DOM mutations');
    }
});
