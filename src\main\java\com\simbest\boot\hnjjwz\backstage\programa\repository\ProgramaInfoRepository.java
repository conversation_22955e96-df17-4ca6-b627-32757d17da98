package com.simbest.boot.hnjjwz.backstage.programa.repository;

import com.simbest.boot.base.repository.LogicRepository;
import com.simbest.boot.hnjjwz.backstage.programa.model.ProgramaInfo;
import org.springframework.data.domain.Page;
import org.springframework.data.domain.Pageable;
import org.springframework.data.jpa.repository.Query;
import org.springframework.data.repository.query.Param;

import java.util.List;
import java.util.Map;
import java.util.Set;


/**
 * <AUTHOR>
 * @Data 2019/04/02
 * @Description 栏目信息相关sql
 */
public interface ProgramaInfoRepository extends LogicRepository<ProgramaInfo,String> {

    /**
     * 根据programaCode获取栏目信息
     * @param programaCode
     * @return
     */
    @Query (value =  " select * from us_programa_info pi " +
            " where pi.programa_code = :programaCode " +
            " AND pi.enabled =1 AND pi.removed_time IS NULL ",
            nativeQuery = true)
    ProgramaInfo findFromProgramaCode( @Param ( "programaCode" ) String programaCode);

    /**
     * 查询父栏目下一级编码最大的子栏目
     * @param programaCode
     * @return
     */
    @Query (value =  " select MAX(spi.programa_code) from us_programa_info ppi,us_programa_info spi " +
            " where ppi.programa_code = spi.parent_programa_code " +
            " AND ppi.programa_code = :programaCode " +
            " AND ppi.enabled =1 AND ppi.removed_time IS NULL " +
            " AND spi.enabled =1 AND spi.removed_time IS NULL ",
            nativeQuery = true)
    String findMaxSon( @Param ( "programaCode" ) String programaCode);

    /**
     * 查询父栏目的下一级子栏目
     * @param programaCode
     * @return
     */
    @Query (value =  " select spi.* from us_programa_info ppi,us_programa_info spi " +
            " where ppi.programa_code = spi.parent_programa_code " +
            " AND ppi.programa_code = :programaCode " +
            " AND ppi.enabled =1 AND ppi.removed_time IS NULL " +
            " AND spi.enabled =1 AND spi.removed_time IS NULL "+
            "order by spi.display_order  asc",
            nativeQuery = true)
    Set<ProgramaInfo> findSons( @Param ( "programaCode" ) String programaCode);

    /**
     * 查看最大的顶级组织
     * @return
     */
    @Query (value =  " select MAX(pi.programa_code) from us_programa_info pi " +
            " where pi.parent_programa_code IS NULL " +
            " AND pi.enabled=1 AND pi.removed_time IS NULL ",
            nativeQuery = true)
    String findMaxRoot();


    /**
     * 查看顶级组织
     * @return
     */
    @Query (value =  " select * from us_programa_info pi " +
            " where pi.parent_programa_code IS NULL " +
            " AND pi.enabled=1 AND pi.removed_time IS NULL ",
            nativeQuery = true)
    Set<ProgramaInfo> findRoot();

    /**
     * 查看分公司组织
     * @return
     */
    @Query (value =  "select *" +
            "  from us_programa_info pi" +
            " where pi.parent_programa_code IS NULL" +
            "    AND pi.programa_code =:programa_code" +
            "    AND pi.enabled = 1" +
            "    AND pi.removed_time IS NULL",
            nativeQuery = true)
    ProgramaInfo findFilialeRoot(@Param ( "programa_code" )String programa_code);


    /**
     * 查看分公司组织
     * @return
     */
    @Query (value =  "select *" +
            "  from us_programa_info pi" +
            " where pi.parent_programa_code IS NULL" +
            "    AND pi.programa_code in (:programa_code)" +
            "    AND pi.enabled = 1" +
            "    AND pi.removed_time IS NULL",
            nativeQuery = true)
    Set<ProgramaInfo> findTotalRoot(@Param ( "programa_code" )List<String> programa_code);

    /**
     * 当传来的父栏目名为空时
     * 根据所属栏目分类id、结点类型、父栏目名（模糊）、栏目名称（模糊）
     * @param programaClassifyIds
     * @param nodeStyles
     * @param programaName
     * @return
     */
    @Query (value =  "select spi.enabled as enabled,spi.id id,spi.programa_classify_id programaClassifyId,spi.parent_programa_code parentProgramaCode,spi.programa_code programaCode," +
            " spi.programa_name programaName,spi.programa_display_name programaDisplayName,spi.node_style nodeStyle,spi.display_order displayOrder,ppi.programa_name parentProgramaName,sdv.name nodeStyleName,pci.programa_classify_name programaClassifyName " +
            " from us_programa_info spi LEFT JOIN us_programa_info ppi ON ppi.programa_code = spi.parent_programa_code" +
            " LEFT JOIN sys_dict_value sdv ON spi.node_style = sdv.value LEFT JOIN US_PROGRAMA_CLASSIFY_INFO pci ON spi.programa_classify_id = pci.id " +
            " where spi.programa_classify_id in (:programaClassifyIds) " +
            " AND spi.node_style in (:nodeStyles) " +
            " AND spi.programa_name like concat( concat('%',:programaName),'%')" +
            " AND sdv.dict_type='nodeStyle' "+
            " AND spi.enabled = 1 AND spi.removed_time IS NULL",
            countQuery = "SELECT COUNT(*)" +
                    " from us_programa_info spi LEFT JOIN us_programa_info ppi ON ppi.programa_code = spi.parent_programa_code" +
                    " LEFT JOIN sys_dict_value sdv ON spi.node_style = sdv.value LEFT JOIN US_PROGRAMA_CLASSIFY_INFO pci ON spi.programa_classify_id = pci.id " +
                    " where spi.programa_classify_id in (:programaClassifyIds) " +
                    " AND spi.node_style in (:nodeStyles) " +
                    " AND spi.programa_name like concat( concat('%',:programaName),'%')" +
                    " AND sdv.dict_type='nodeStyle' "+
                    " AND spi.enabled = 1 AND spi.removed_time IS NULL" ,
            nativeQuery = true)
    Page<Map<String,Object>> findDimProgramaInfoNoParent( @Param ( "programaClassifyIds" ) List<String> programaClassifyIds, @Param ( "nodeStyles" ) List<String>  nodeStyles, @Param ( "programaName" ) String programaName, Pageable pageable );

    /**
     * 当传来的父栏目名不为空时
     * 根据所属栏目分类id、结点类型、父栏目名（模糊）、栏目名称（模糊）
     * @param programaClassifyIds
     * @param nodeStyles
     * @param parentProgramaName
     * @param programaName
     * @return
     */
    @Query (value =  " select spi.enabled as enabled,spi.id id,spi.programa_classify_id programaClassifyId,spi.parent_programa_code parentProgramaCode,spi.programa_code programaCode, " +
            " spi.programa_name programaName,spi.programa_display_name programaDisplayName,spi.node_style nodeStyle,spi.display_order displayOrder,ppi.programa_name parentProgramaName,sdv.name nodeStyleName,pci.programa_classify_name programaClassifyName " +
            " from us_programa_info spi LEFT JOIN us_programa_info ppi ON ppi.programa_code = spi.parent_programa_code " +
            " LEFT JOIN sys_dict_value sdv ON spi.node_style = sdv.value LEFT JOIN US_PROGRAMA_CLASSIFY_INFO pci ON spi.programa_classify_id = pci.id  " +
            " where spi.programa_classify_id in (:programaClassifyIds) " +
            " AND spi.node_style in (:nodeStyles) " +
            " AND spi.programa_name like concat( concat('%',:programaName),'%')" +
            " AND ppi.programa_name like concat(concat('%', :parentProgramaName), '%')" +
            " AND sdv.dict_type='nodeStyle' "+
            " AND spi.enabled = 1 AND spi.removed_time IS NULL" ,
            countQuery = "SELECT COUNT(*)" +
                    " from us_programa_info spi LEFT JOIN us_programa_info ppi ON ppi.programa_code = spi.parent_programa_code " +
                    " LEFT JOIN sys_dict_value sdv ON spi.node_style = sdv.value LEFT JOIN US_PROGRAMA_CLASSIFY_INFO pci ON spi.programa_classify_id = pci.id  " +
                    " where spi.programa_classify_id in (:programaClassifyIds) " +
                    " AND spi.node_style in (:nodeStyles) " +
                    " AND spi.programa_name like concat( concat('%',:programaName),'%')" +
                    " AND ppi.programa_name like concat(concat('%', :parentProgramaName), '%')" +
                    " AND sdv.dict_type='nodeStyle' "+
                    " AND spi.enabled = 1 AND spi.removed_time IS NULL ",
            nativeQuery = true)
    Page<Map<String,Object>> findDimProgramaInfo( @Param ( "programaClassifyIds" ) List<String> programaClassifyIds, @Param ( "nodeStyles" ) List<String>  nodeStyles, @Param ( "parentProgramaName" ) String parentProgramaName, @Param ( "programaName" ) String programaName, Pageable pageable );

    @Query(value = "select * from US_PROGRAMA_INFO t where t.parent_programa_code=:locationId and t.enabled=1 and t.removed_time is null",
            nativeQuery = true)
    List<ProgramaInfo> findSonByProgramaInfo(@Param ( "locationId" )String locationId);
}
