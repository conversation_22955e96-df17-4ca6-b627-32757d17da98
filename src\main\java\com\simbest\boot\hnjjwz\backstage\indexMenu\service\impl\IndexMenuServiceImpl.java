package com.simbest.boot.hnjjwz.backstage.indexMenu.service.impl;

import com.simbest.boot.base.service.impl.LogicService;
import com.simbest.boot.base.web.response.JsonResponse;
import com.simbest.boot.constants.ApplicationConstants;
import com.simbest.boot.hnjjwz.backstage.indexMenu.model.IndexMenu;
import com.simbest.boot.hnjjwz.backstage.indexMenu.repository.IndexMenuRepository;
import com.simbest.boot.hnjjwz.backstage.indexMenu.service.IIndexMenuService;
import com.simbest.boot.hnjjwz.constants.Constants;
import com.simbest.boot.hnjjwz.util.SysDictTool;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.data.domain.Page;
import org.springframework.data.domain.PageImpl;
import org.springframework.data.domain.Pageable;
import org.springframework.stereotype.Service;
import org.springframework.util.StringUtils;

import java.util.ArrayList;
import java.util.List;
import java.util.Map;
import java.util.Set;

/**
 * <AUTHOR>
 * @Data 2019/05/06
 * @Description 首页菜单
 */
@Slf4j
@Service
public class IndexMenuServiceImpl extends LogicService<IndexMenu,String> implements IIndexMenuService {

    private IndexMenuRepository indexMenuRepository;

    @Autowired
    public IndexMenuServiceImpl ( IndexMenuRepository indexMenuRepository) {
        super(indexMenuRepository);
        this.indexMenuRepository = indexMenuRepository;
    }

    @Autowired
    private SysDictTool sysDictTool;

    /**
     * 菜单名(模糊)、菜单类型(精确)
     * @param mapObject
     * @param pageable
     * @return
     */
    @Override
    public JsonResponse findAllDim ( Map<String, Object> mapObject, Pageable pageable ) {
        String menuName = (String)mapObject.get( Constants.MENU_NAME_KEY );
        String menuUrlType = (String)mapObject.get( Constants.MENU_URL_TYPE_KEY );
        List<String> menuUrlTypeList = new ArrayList<>(  );
        if( StringUtils.isEmpty( menuName ) ){
            menuName = ApplicationConstants.EMPTY;
        }
        if( StringUtils.isEmpty( menuUrlType ) ){
            //从数据字典中获取类型
            menuUrlTypeList = sysDictTool.getSysDictValueList(Constants.INDEX_MENU_URL_TYPE);
        }else{
            menuUrlTypeList.add( menuUrlType );
        }
        Page<IndexMenu> indexMenuPage = indexMenuRepository.findAllDim(menuName,menuUrlTypeList,pageable);
        List<IndexMenu> indexMenuList = indexMenuPage.getContent();
        List<IndexMenu> indexMenuListNew = new ArrayList<>(  );
        for(IndexMenu indexMenu:indexMenuList){
            if(indexMenu!=null){
                String sysDictName = sysDictTool.firstSysDictName(indexMenu.getMenuUrlType());
                indexMenu.setMenuUrlTypeName( sysDictName );
                indexMenuListNew.add( indexMenu );
            }
        }
        Page<IndexMenu> indexMenuPageNew = new PageImpl<IndexMenu>(indexMenuListNew,pageable,indexMenuPage.getTotalElements() );
        return JsonResponse.success( indexMenuPageNew );
    }

    @Override
    public List<IndexMenu> findIndexMenu() {
        return indexMenuRepository.findIndexMenu();
    }

    /**
     * 页面初始化时获取菜单
     * @return
     */
    @Override
    public Set<IndexMenu> findRootAndNext() {
        return indexMenuRepository.findRootAndNext();
    }
}
