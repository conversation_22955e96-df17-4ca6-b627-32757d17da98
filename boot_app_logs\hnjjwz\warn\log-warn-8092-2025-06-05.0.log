2025-06-05 00:24:52.232 [AMQP Connection ************:5672] WARN  com.rabbitmq.client.impl.ForgivingExceptionHandler.log Line:115 - An unexpected connection driver error occured (Exception message: Connection reset)
2025-06-05 00:24:55.451 [AMQP Connection ************:5672] WARN  com.rabbitmq.client.impl.ForgivingExceptionHandler.log Line:115 - An unexpected connection driver error occured (Exception message: Connection reset)
2025-06-05 00:24:55.926 [AMQP Connection ************:5672] WARN  com.rabbitmq.client.impl.ForgivingExceptionHandler.log Line:115 - An unexpected connection driver error occured (Exception message: Connection reset)
2025-06-05 00:24:56.078 [AMQP Connection ************:5672] WARN  com.rabbitmq.client.impl.ForgivingExceptionHandler.log Line:115 - An unexpected connection driver error occured (Exception message: Connection reset)
2025-06-05 00:45:01.290 [redisson-netty-5-19] WARN  org.redisson.client.handler.CommandDecoder.completeResponse Line:445 - response has been skipped due to timeout! channel: [id: 0xbd673f1b, L:/***********:57948 - R:************/************:7003], command: (CLUSTER NODES), params: [], result: [ClusterNodeInfo [nodeId=4dcfd30d1191cb5e65ef69e88dc76592f8b3d14d, address=redis://************:7005, flags=[MASTER], slaveOf=null, slotRanges=[[10923-16383]]], ClusterNodeInfo [nodeId=248f8c5f1a3d07d891f1bcf924c02097c5509e0d, address=redis://************:7001, flags=[SLAVE], slaveOf=64f45f937e6f8c36d0fb7cf09195403c4cccdf1d, slotRanges=[]], ClusterNodeInfo [nodeId=02aff3b78f8c09c43cbfc2346eac68a16a942097, address=redis://************:7003, flags=[MASTER, MYSELF], slaveOf=null, slotRanges=[[5461-10922]]], ClusterNodeInfo [nodeId=85e61682488d3a355264d212607a63832da60e8c, address=redis://************:7006, flags=[SLAVE], slaveOf=02aff3b78f8c09c43cbfc2346eac68a16a942097, slotRanges=[]], ClusterNodeInfo [nodeId=64f45f937e6f8c36d0fb7cf09195403c4cccdf1d, address=redis://************:7004, flags=[MASTER], slaveOf=null, slotRanges=[[0-5460]]], ClusterNodeInfo [nodeId=5514edc6d1aaab0526b0093a424e9f352e9914ed, address=redis://************:7002, flags=[SLAVE], slaveOf=4dcfd30d1191cb5e65ef69e88dc76592f8b3d14d, slotRanges=[]]]
2025-06-05 01:05:45.969 [redisson-netty-5-24] WARN  org.redisson.client.handler.CommandDecoder.completeResponse Line:445 - response has been skipped due to timeout! channel: [id: 0xdfb04861, L:/***********:53510 - R:************/************:7003], command: (CLUSTER NODES), params: [], result: [ClusterNodeInfo [nodeId=4dcfd30d1191cb5e65ef69e88dc76592f8b3d14d, address=redis://************:7005, flags=[MASTER], slaveOf=null, slotRanges=[[10923-16383]]], ClusterNodeInfo [nodeId=248f8c5f1a3d07d891f1bcf924c02097c5509e0d, address=redis://************:7001, flags=[SLAVE], slaveOf=64f45f937e6f8c36d0fb7cf09195403c4cccdf1d, slotRanges=[]], ClusterNodeInfo [nodeId=02aff3b78f8c09c43cbfc2346eac68a16a942097, address=redis://************:7003, flags=[MASTER, MYSELF], slaveOf=null, slotRanges=[[5461-10922]]], ClusterNodeInfo [nodeId=85e61682488d3a355264d212607a63832da60e8c, address=redis://************:7006, flags=[SLAVE], slaveOf=02aff3b78f8c09c43cbfc2346eac68a16a942097, slotRanges=[]], ClusterNodeInfo [nodeId=64f45f937e6f8c36d0fb7cf09195403c4cccdf1d, address=redis://************:7004, flags=[MASTER], slaveOf=null, slotRanges=[[0-5460]]], ClusterNodeInfo [nodeId=5514edc6d1aaab0526b0093a424e9f352e9914ed, address=redis://************:7002, flags=[SLAVE], slaveOf=4dcfd30d1191cb5e65ef69e88dc76592f8b3d14d, slotRanges=[]]]
2025-06-05 01:26:30.573 [redisson-netty-5-6] WARN  org.redisson.client.handler.CommandDecoder.completeResponse Line:445 - response has been skipped due to timeout! channel: [id: 0x91022903, L:/***********:54739 - R:************/************:7003], command: (CLUSTER NODES), params: [], result: [ClusterNodeInfo [nodeId=4dcfd30d1191cb5e65ef69e88dc76592f8b3d14d, address=redis://************:7005, flags=[MASTER], slaveOf=null, slotRanges=[[10923-16383]]], ClusterNodeInfo [nodeId=248f8c5f1a3d07d891f1bcf924c02097c5509e0d, address=redis://************:7001, flags=[SLAVE], slaveOf=64f45f937e6f8c36d0fb7cf09195403c4cccdf1d, slotRanges=[]], ClusterNodeInfo [nodeId=02aff3b78f8c09c43cbfc2346eac68a16a942097, address=redis://************:7003, flags=[MASTER, MYSELF], slaveOf=null, slotRanges=[[5461-10922]]], ClusterNodeInfo [nodeId=85e61682488d3a355264d212607a63832da60e8c, address=redis://************:7006, flags=[SLAVE], slaveOf=02aff3b78f8c09c43cbfc2346eac68a16a942097, slotRanges=[]], ClusterNodeInfo [nodeId=64f45f937e6f8c36d0fb7cf09195403c4cccdf1d, address=redis://************:7004, flags=[MASTER], slaveOf=null, slotRanges=[[0-5460]]], ClusterNodeInfo [nodeId=5514edc6d1aaab0526b0093a424e9f352e9914ed, address=redis://************:7002, flags=[SLAVE], slaveOf=4dcfd30d1191cb5e65ef69e88dc76592f8b3d14d, slotRanges=[]]]
2025-06-05 01:26:43.864 [redisson-netty-5-1] WARN  org.redisson.client.handler.CommandDecoder.completeResponse Line:445 - response has been skipped due to timeout! channel: [id: 0x76eaa866, L:/***********:63431 - R:************/************:7003], command: (CLUSTER NODES), params: [], result: [ClusterNodeInfo [nodeId=4dcfd30d1191cb5e65ef69e88dc76592f8b3d14d, address=redis://************:7005, flags=[MASTER], slaveOf=null, slotRanges=[[10923-16383]]], ClusterNodeInfo [nodeId=248f8c5f1a3d07d891f1bcf924c02097c5509e0d, address=redis://************:7001, flags=[SLAVE], slaveOf=64f45f937e6f8c36d0fb7cf09195403c4cccdf1d, slotRanges=[]], ClusterNodeInfo [nodeId=02aff3b78f8c09c43cbfc2346eac68a16a942097, address=redis://************:7003, flags=[MASTER, MYSELF], slaveOf=null, slotRanges=[[5461-10922]]], ClusterNodeInfo [nodeId=85e61682488d3a355264d212607a63832da60e8c, address=redis://************:7006, flags=[SLAVE], slaveOf=02aff3b78f8c09c43cbfc2346eac68a16a942097, slotRanges=[]], ClusterNodeInfo [nodeId=64f45f937e6f8c36d0fb7cf09195403c4cccdf1d, address=redis://************:7004, flags=[MASTER], slaveOf=null, slotRanges=[[0-5460]]], ClusterNodeInfo [nodeId=5514edc6d1aaab0526b0093a424e9f352e9914ed, address=redis://************:7002, flags=[SLAVE], slaveOf=4dcfd30d1191cb5e65ef69e88dc76592f8b3d14d, slotRanges=[]]]
2025-06-05 01:47:15.622 [redisson-netty-5-11] WARN  org.redisson.client.handler.CommandDecoder.completeResponse Line:445 - response has been skipped due to timeout! channel: [id: 0xd9886ea1, L:/***********:63487 - R:************/************:7003], command: (CLUSTER NODES), params: [], result: [ClusterNodeInfo [nodeId=4dcfd30d1191cb5e65ef69e88dc76592f8b3d14d, address=redis://************:7005, flags=[MASTER], slaveOf=null, slotRanges=[[10923-16383]]], ClusterNodeInfo [nodeId=248f8c5f1a3d07d891f1bcf924c02097c5509e0d, address=redis://************:7001, flags=[SLAVE], slaveOf=64f45f937e6f8c36d0fb7cf09195403c4cccdf1d, slotRanges=[]], ClusterNodeInfo [nodeId=02aff3b78f8c09c43cbfc2346eac68a16a942097, address=redis://************:7003, flags=[MASTER, MYSELF], slaveOf=null, slotRanges=[[5461-10922]]], ClusterNodeInfo [nodeId=85e61682488d3a355264d212607a63832da60e8c, address=redis://************:7006, flags=[SLAVE], slaveOf=02aff3b78f8c09c43cbfc2346eac68a16a942097, slotRanges=[]], ClusterNodeInfo [nodeId=64f45f937e6f8c36d0fb7cf09195403c4cccdf1d, address=redis://************:7004, flags=[MASTER], slaveOf=null, slotRanges=[[0-5460]]], ClusterNodeInfo [nodeId=5514edc6d1aaab0526b0093a424e9f352e9914ed, address=redis://************:7002, flags=[SLAVE], slaveOf=4dcfd30d1191cb5e65ef69e88dc76592f8b3d14d, slotRanges=[]]]
2025-06-05 02:07:58.514 [redisson-netty-5-5] WARN  org.redisson.client.handler.CommandDecoder.completeResponse Line:445 - response has been skipped due to timeout! channel: [id: 0x9bfa4385, L:/***********:56049 - R:************/************:7003], command: (CLUSTER NODES), params: [], result: [ClusterNodeInfo [nodeId=4dcfd30d1191cb5e65ef69e88dc76592f8b3d14d, address=redis://************:7005, flags=[MASTER], slaveOf=null, slotRanges=[[10923-16383]]], ClusterNodeInfo [nodeId=248f8c5f1a3d07d891f1bcf924c02097c5509e0d, address=redis://************:7001, flags=[SLAVE], slaveOf=64f45f937e6f8c36d0fb7cf09195403c4cccdf1d, slotRanges=[]], ClusterNodeInfo [nodeId=02aff3b78f8c09c43cbfc2346eac68a16a942097, address=redis://************:7003, flags=[MASTER, MYSELF], slaveOf=null, slotRanges=[[5461-10922]]], ClusterNodeInfo [nodeId=85e61682488d3a355264d212607a63832da60e8c, address=redis://************:7006, flags=[SLAVE], slaveOf=02aff3b78f8c09c43cbfc2346eac68a16a942097, slotRanges=[]], ClusterNodeInfo [nodeId=64f45f937e6f8c36d0fb7cf09195403c4cccdf1d, address=redis://************:7004, flags=[MASTER], slaveOf=null, slotRanges=[[0-5460]]], ClusterNodeInfo [nodeId=5514edc6d1aaab0526b0093a424e9f352e9914ed, address=redis://************:7002, flags=[SLAVE], slaveOf=4dcfd30d1191cb5e65ef69e88dc76592f8b3d14d, slotRanges=[]]]
2025-06-05 02:28:41.306 [redisson-netty-5-22] WARN  org.redisson.client.handler.CommandDecoder.completeResponse Line:445 - response has been skipped due to timeout! channel: [id: 0x4309a3fe, L:/***********:53492 - R:************/************:7005], command: (CLUSTER NODES), params: [], result: [ClusterNodeInfo [nodeId=02aff3b78f8c09c43cbfc2346eac68a16a942097, address=redis://************:7003, flags=[MASTER], slaveOf=null, slotRanges=[[5461-10922]]], ClusterNodeInfo [nodeId=85e61682488d3a355264d212607a63832da60e8c, address=redis://************:7006, flags=[SLAVE], slaveOf=02aff3b78f8c09c43cbfc2346eac68a16a942097, slotRanges=[]], ClusterNodeInfo [nodeId=64f45f937e6f8c36d0fb7cf09195403c4cccdf1d, address=redis://************:7004, flags=[MASTER], slaveOf=null, slotRanges=[[0-5460]]], ClusterNodeInfo [nodeId=4dcfd30d1191cb5e65ef69e88dc76592f8b3d14d, address=redis://************:7005, flags=[MASTER, MYSELF], slaveOf=null, slotRanges=[[10923-16383]]], ClusterNodeInfo [nodeId=248f8c5f1a3d07d891f1bcf924c02097c5509e0d, address=redis://************:7001, flags=[SLAVE], slaveOf=64f45f937e6f8c36d0fb7cf09195403c4cccdf1d, slotRanges=[]], ClusterNodeInfo [nodeId=5514edc6d1aaab0526b0093a424e9f352e9914ed, address=redis://************:7002, flags=[SLAVE], slaveOf=4dcfd30d1191cb5e65ef69e88dc76592f8b3d14d, slotRanges=[]]]
2025-06-05 02:49:26.220 [redisson-netty-5-25] WARN  org.redisson.client.handler.CommandDecoder.completeResponse Line:445 - response has been skipped due to timeout! channel: [id: 0xd777ec74, L:/***********:57983 - R:************/************:7003], command: (CLUSTER NODES), params: [], result: [ClusterNodeInfo [nodeId=4dcfd30d1191cb5e65ef69e88dc76592f8b3d14d, address=redis://************:7005, flags=[MASTER], slaveOf=null, slotRanges=[[10923-16383]]], ClusterNodeInfo [nodeId=248f8c5f1a3d07d891f1bcf924c02097c5509e0d, address=redis://************:7001, flags=[SLAVE], slaveOf=64f45f937e6f8c36d0fb7cf09195403c4cccdf1d, slotRanges=[]], ClusterNodeInfo [nodeId=02aff3b78f8c09c43cbfc2346eac68a16a942097, address=redis://************:7003, flags=[MASTER, MYSELF], slaveOf=null, slotRanges=[[5461-10922]]], ClusterNodeInfo [nodeId=85e61682488d3a355264d212607a63832da60e8c, address=redis://************:7006, flags=[SLAVE], slaveOf=02aff3b78f8c09c43cbfc2346eac68a16a942097, slotRanges=[]], ClusterNodeInfo [nodeId=64f45f937e6f8c36d0fb7cf09195403c4cccdf1d, address=redis://************:7004, flags=[MASTER], slaveOf=null, slotRanges=[[0-5460]]], ClusterNodeInfo [nodeId=5514edc6d1aaab0526b0093a424e9f352e9914ed, address=redis://************:7002, flags=[SLAVE], slaveOf=4dcfd30d1191cb5e65ef69e88dc76592f8b3d14d, slotRanges=[]]]
2025-06-05 03:10:09.061 [redisson-netty-5-26] WARN  org.redisson.client.handler.CommandDecoder.completeResponse Line:445 - response has been skipped due to timeout! channel: [id: 0x07d702a8, L:/***********:51237 - R:************/************:7003], command: (CLUSTER NODES), params: [], result: [ClusterNodeInfo [nodeId=4dcfd30d1191cb5e65ef69e88dc76592f8b3d14d, address=redis://************:7005, flags=[MASTER], slaveOf=null, slotRanges=[[10923-16383]]], ClusterNodeInfo [nodeId=248f8c5f1a3d07d891f1bcf924c02097c5509e0d, address=redis://************:7001, flags=[SLAVE], slaveOf=64f45f937e6f8c36d0fb7cf09195403c4cccdf1d, slotRanges=[]], ClusterNodeInfo [nodeId=02aff3b78f8c09c43cbfc2346eac68a16a942097, address=redis://************:7003, flags=[MASTER, MYSELF], slaveOf=null, slotRanges=[[5461-10922]]], ClusterNodeInfo [nodeId=85e61682488d3a355264d212607a63832da60e8c, address=redis://************:7006, flags=[SLAVE], slaveOf=02aff3b78f8c09c43cbfc2346eac68a16a942097, slotRanges=[]], ClusterNodeInfo [nodeId=64f45f937e6f8c36d0fb7cf09195403c4cccdf1d, address=redis://************:7004, flags=[MASTER], slaveOf=null, slotRanges=[[0-5460]]], ClusterNodeInfo [nodeId=5514edc6d1aaab0526b0093a424e9f352e9914ed, address=redis://************:7002, flags=[SLAVE], slaveOf=4dcfd30d1191cb5e65ef69e88dc76592f8b3d14d, slotRanges=[]]]
2025-06-05 03:30:34.091 [lettuce-nioEventLoop-4-11] WARN  io.lettuce.core.cluster.topology.ClusterTopologyRefresh.lambda$getConnections$0 Line:284 - Unable to connect to [************:7005]: No route to host: no further information: /************:7005
2025-06-05 03:30:34.091 [lettuce-nioEventLoop-4-10] WARN  io.lettuce.core.cluster.topology.ClusterTopologyRefresh.lambda$getConnections$0 Line:284 - Unable to connect to [************:7004]: No route to host: no further information: /************:7004
2025-06-05 03:30:34.091 [lettuce-nioEventLoop-4-8] WARN  io.lettuce.core.cluster.topology.ClusterTopologyRefresh.lambda$getConnections$0 Line:284 - Unable to connect to [************:7002]: No route to host: no further information: /************:7002
2025-06-05 03:30:34.091 [lettuce-nioEventLoop-4-7] WARN  io.lettuce.core.cluster.topology.ClusterTopologyRefresh.lambda$getConnections$0 Line:284 - Unable to connect to [************:7001]: No route to host: no further information: /************:7001
2025-06-05 03:30:34.091 [lettuce-nioEventLoop-4-9] WARN  io.lettuce.core.cluster.topology.ClusterTopologyRefresh.lambda$getConnections$0 Line:284 - Unable to connect to [************:7003]: No route to host: no further information: /************:7003
2025-06-05 03:30:34.091 [lettuce-nioEventLoop-4-12] WARN  io.lettuce.core.cluster.topology.ClusterTopologyRefresh.lambda$getConnections$0 Line:284 - Unable to connect to [************:7006]: No route to host: no further information: /************:7006
2025-06-05 03:30:34.095 [lettuce-nioEventLoop-4-2] WARN  io.lettuce.core.cluster.topology.ClusterTopologyRefresh.lambda$getConnections$0 Line:284 - Unable to connect to [************:7004]: No route to host: no further information: /************:7004
2025-06-05 03:30:34.096 [lettuce-nioEventLoop-4-3] WARN  io.lettuce.core.cluster.topology.ClusterTopologyRefresh.lambda$getConnections$0 Line:284 - Unable to connect to [************:7005]: No route to host: no further information: /************:7005
2025-06-05 03:30:34.095 [lettuce-nioEventLoop-4-1] WARN  io.lettuce.core.cluster.topology.ClusterTopologyRefresh.lambda$getConnections$0 Line:284 - Unable to connect to [************:7006]: No route to host: no further information: /************:7006
2025-06-05 03:30:34.096 [lettuce-nioEventLoop-4-4] WARN  io.lettuce.core.cluster.topology.ClusterTopologyRefresh.lambda$getConnections$0 Line:284 - Unable to connect to [************:7002]: No route to host: no further information: /************:7002
2025-06-05 03:30:34.096 [lettuce-nioEventLoop-4-6] WARN  io.lettuce.core.cluster.topology.ClusterTopologyRefresh.lambda$getConnections$0 Line:284 - Unable to connect to [************:7001]: No route to host: no further information: /************:7001
2025-06-05 03:30:34.119 [lettuce-nioEventLoop-4-5] WARN  io.lettuce.core.cluster.topology.ClusterTopologyRefresh.lambda$getConnections$0 Line:284 - Unable to connect to [************:7003]: No route to host: no further information: /************:7003
2025-06-05 03:30:34.136 [lettuce-eventExecutorLoop-1-8] WARN  io.lettuce.core.cluster.ClusterTopologyRefreshScheduler.doRun Line:258 - Cannot refresh Redis Cluster topology
io.lettuce.core.RedisException: Cannot retrieve initial cluster partitions from initial URIs [RedisURI [host='************', port=7006], RedisURI [host='************', port=7004], RedisURI [host='************', port=7005], RedisURI [host='************', port=7002], RedisURI [host='************', port=7003], RedisURI [host='************', port=7001]]
	at io.lettuce.core.cluster.RedisClusterClient.loadPartitions(RedisClusterClient.java:848)
	at io.lettuce.core.cluster.RedisClusterClient.reloadPartitions(RedisClusterClient.java:784)
	at io.lettuce.core.cluster.ClusterTopologyRefreshScheduler$ClusterTopologyRefreshTask.doRun(ClusterTopologyRefreshScheduler.java:256)
	at io.lettuce.core.cluster.ClusterTopologyRefreshScheduler$ClusterTopologyRefreshTask.run(ClusterTopologyRefreshScheduler.java:236)
	at io.netty.util.concurrent.PromiseTask.runTask(PromiseTask.java:98)
	at io.netty.util.concurrent.PromiseTask.run(PromiseTask.java:106)
	at io.netty.util.concurrent.DefaultEventExecutor.run(DefaultEventExecutor.java:66)
	at io.netty.util.concurrent.SingleThreadEventExecutor$6.run(SingleThreadEventExecutor.java:1050)
	at io.netty.util.internal.ThreadExecutorMap$2.run(ThreadExecutorMap.java:74)
	at io.netty.util.concurrent.FastThreadLocalRunnable.run(FastThreadLocalRunnable.java:30)
	at java.lang.Thread.run(Thread.java:748)
	Suppressed: io.lettuce.core.RedisConnectionException: Unable to establish a connection to Redis Cluster at [RedisURI [host='************', port=7001], RedisURI [host='************', port=7002], RedisURI [host='************', port=7003], RedisURI [host='************', port=7004], RedisURI [host='************', port=7005], RedisURI [host='************', port=7006]]
		at io.lettuce.core.cluster.topology.AsyncConnections.get(AsyncConnections.java:89)
		at io.lettuce.core.cluster.topology.ClusterTopologyRefresh.loadViews(ClusterTopologyRefresh.java:76)
		at io.lettuce.core.cluster.RedisClusterClient.doLoadPartitions(RedisClusterClient.java:865)
		at io.lettuce.core.cluster.RedisClusterClient.loadPartitions(RedisClusterClient.java:838)
		... 10 common frames omitted
		Suppressed: io.lettuce.core.RedisConnectionException: Unable to connect to [************:7002]: No route to host: no further information: /************:7002
			at io.lettuce.core.cluster.topology.ClusterTopologyRefresh.lambda$getConnections$0(ClusterTopologyRefresh.java:290)
			at java.util.concurrent.CompletableFuture.uniWhenComplete(CompletableFuture.java:760)
			at java.util.concurrent.CompletableFuture$UniWhenComplete.tryFire(CompletableFuture.java:736)
			at java.util.concurrent.CompletableFuture.postComplete(CompletableFuture.java:474)
			at java.util.concurrent.CompletableFuture.completeExceptionally(CompletableFuture.java:1977)
			at io.lettuce.core.AbstractRedisClient.lambda$initializeChannelAsync0$4(AbstractRedisClient.java:329)
			at io.netty.util.concurrent.DefaultPromise.notifyListener0(DefaultPromise.java:577)
			at io.netty.util.concurrent.DefaultPromise.notifyListeners0(DefaultPromise.java:570)
			at io.netty.util.concurrent.DefaultPromise.notifyListenersNow(DefaultPromise.java:549)
			at io.netty.util.concurrent.DefaultPromise.notifyListeners(DefaultPromise.java:490)
			at io.netty.util.concurrent.DefaultPromise.setValue0(DefaultPromise.java:615)
			at io.netty.util.concurrent.DefaultPromise.setFailure0(DefaultPromise.java:608)
			at io.netty.util.concurrent.DefaultPromise.tryFailure(DefaultPromise.java:117)
			at io.netty.channel.nio.AbstractNioChannel$AbstractNioUnsafe.fulfillConnectPromise(AbstractNioChannel.java:321)
			at io.netty.channel.nio.AbstractNioChannel$AbstractNioUnsafe.finishConnect(AbstractNioChannel.java:337)
			at io.netty.channel.nio.NioEventLoop.processSelectedKey(NioEventLoop.java:688)
			at io.netty.channel.nio.NioEventLoop.processSelectedKeysOptimized(NioEventLoop.java:635)
			at io.netty.channel.nio.NioEventLoop.processSelectedKeys(NioEventLoop.java:552)
			at io.netty.channel.nio.NioEventLoop.run(NioEventLoop.java:514)
			... 4 common frames omitted
		Caused by: io.netty.channel.AbstractChannel$AnnotatedNoRouteToHostException: No route to host: no further information: /************:7002
		Caused by: java.net.NoRouteToHostException: No route to host: no further information
			at sun.nio.ch.SocketChannelImpl.checkConnect(Native Method)
			at sun.nio.ch.SocketChannelImpl.finishConnect(SocketChannelImpl.java:717)
			at io.netty.channel.socket.nio.NioSocketChannel.doFinishConnect(NioSocketChannel.java:330)
			at io.netty.channel.nio.AbstractNioChannel$AbstractNioUnsafe.finishConnect(AbstractNioChannel.java:334)
			at io.netty.channel.nio.NioEventLoop.processSelectedKey(NioEventLoop.java:688)
			at io.netty.channel.nio.NioEventLoop.processSelectedKeysOptimized(NioEventLoop.java:635)
			at io.netty.channel.nio.NioEventLoop.processSelectedKeys(NioEventLoop.java:552)
			at io.netty.channel.nio.NioEventLoop.run(NioEventLoop.java:514)
			at io.netty.util.concurrent.SingleThreadEventExecutor$6.run(SingleThreadEventExecutor.java:1050)
			at io.netty.util.internal.ThreadExecutorMap$2.run(ThreadExecutorMap.java:74)
			at io.netty.util.concurrent.FastThreadLocalRunnable.run(FastThreadLocalRunnable.java:30)
			at java.lang.Thread.run(Thread.java:748)
		Suppressed: io.lettuce.core.RedisConnectionException: Unable to connect to [************:7005]: No route to host: no further information: /************:7005
			at io.lettuce.core.cluster.topology.ClusterTopologyRefresh.lambda$getConnections$0(ClusterTopologyRefresh.java:290)
			at java.util.concurrent.CompletableFuture.uniWhenComplete(CompletableFuture.java:760)
			at java.util.concurrent.CompletableFuture$UniWhenComplete.tryFire(CompletableFuture.java:736)
			at java.util.concurrent.CompletableFuture.postComplete(CompletableFuture.java:474)
			at java.util.concurrent.CompletableFuture.completeExceptionally(CompletableFuture.java:1977)
			at io.lettuce.core.AbstractRedisClient.lambda$initializeChannelAsync0$4(AbstractRedisClient.java:329)
			at io.netty.util.concurrent.DefaultPromise.notifyListener0(DefaultPromise.java:577)
			at io.netty.util.concurrent.DefaultPromise.notifyListeners0(DefaultPromise.java:570)
			at io.netty.util.concurrent.DefaultPromise.notifyListenersNow(DefaultPromise.java:549)
			at io.netty.util.concurrent.DefaultPromise.notifyListeners(DefaultPromise.java:490)
			at io.netty.util.concurrent.DefaultPromise.setValue0(DefaultPromise.java:615)
			at io.netty.util.concurrent.DefaultPromise.setFailure0(DefaultPromise.java:608)
			at io.netty.util.concurrent.DefaultPromise.tryFailure(DefaultPromise.java:117)
			at io.netty.channel.nio.AbstractNioChannel$AbstractNioUnsafe.fulfillConnectPromise(AbstractNioChannel.java:321)
			at io.netty.channel.nio.AbstractNioChannel$AbstractNioUnsafe.finishConnect(AbstractNioChannel.java:337)
			at io.netty.channel.nio.NioEventLoop.processSelectedKey(NioEventLoop.java:688)
			at io.netty.channel.nio.NioEventLoop.processSelectedKeysOptimized(NioEventLoop.java:635)
			at io.netty.channel.nio.NioEventLoop.processSelectedKeys(NioEventLoop.java:552)
			at io.netty.channel.nio.NioEventLoop.run(NioEventLoop.java:514)
			... 4 common frames omitted
		Caused by: io.netty.channel.AbstractChannel$AnnotatedNoRouteToHostException: No route to host: no further information: /************:7005
		Caused by: java.net.NoRouteToHostException: No route to host: no further information
			at sun.nio.ch.SocketChannelImpl.checkConnect(Native Method)
			at sun.nio.ch.SocketChannelImpl.finishConnect(SocketChannelImpl.java:717)
			at io.netty.channel.socket.nio.NioSocketChannel.doFinishConnect(NioSocketChannel.java:330)
			at io.netty.channel.nio.AbstractNioChannel$AbstractNioUnsafe.finishConnect(AbstractNioChannel.java:334)
			at io.netty.channel.nio.NioEventLoop.processSelectedKey(NioEventLoop.java:688)
			at io.netty.channel.nio.NioEventLoop.processSelectedKeysOptimized(NioEventLoop.java:635)
			at io.netty.channel.nio.NioEventLoop.processSelectedKeys(NioEventLoop.java:552)
			at io.netty.channel.nio.NioEventLoop.run(NioEventLoop.java:514)
			at io.netty.util.concurrent.SingleThreadEventExecutor$6.run(SingleThreadEventExecutor.java:1050)
			at io.netty.util.internal.ThreadExecutorMap$2.run(ThreadExecutorMap.java:74)
			at io.netty.util.concurrent.FastThreadLocalRunnable.run(FastThreadLocalRunnable.java:30)
			at java.lang.Thread.run(Thread.java:748)
		Suppressed: io.lettuce.core.RedisConnectionException: Unable to connect to [************:7001]: No route to host: no further information: /************:7001
			at io.lettuce.core.cluster.topology.ClusterTopologyRefresh.lambda$getConnections$0(ClusterTopologyRefresh.java:290)
			at java.util.concurrent.CompletableFuture.uniWhenComplete(CompletableFuture.java:760)
			at java.util.concurrent.CompletableFuture$UniWhenComplete.tryFire(CompletableFuture.java:736)
			at java.util.concurrent.CompletableFuture.postComplete(CompletableFuture.java:474)
			at java.util.concurrent.CompletableFuture.completeExceptionally(CompletableFuture.java:1977)
			at io.lettuce.core.AbstractRedisClient.lambda$initializeChannelAsync0$4(AbstractRedisClient.java:329)
			at io.netty.util.concurrent.DefaultPromise.notifyListener0(DefaultPromise.java:577)
			at io.netty.util.concurrent.DefaultPromise.notifyListeners0(DefaultPromise.java:570)
			at io.netty.util.concurrent.DefaultPromise.notifyListenersNow(DefaultPromise.java:549)
			at io.netty.util.concurrent.DefaultPromise.notifyListeners(DefaultPromise.java:490)
			at io.netty.util.concurrent.DefaultPromise.setValue0(DefaultPromise.java:615)
			at io.netty.util.concurrent.DefaultPromise.setFailure0(DefaultPromise.java:608)
			at io.netty.util.concurrent.DefaultPromise.tryFailure(DefaultPromise.java:117)
			at io.netty.channel.nio.AbstractNioChannel$AbstractNioUnsafe.fulfillConnectPromise(AbstractNioChannel.java:321)
			at io.netty.channel.nio.AbstractNioChannel$AbstractNioUnsafe.finishConnect(AbstractNioChannel.java:337)
			at io.netty.channel.nio.NioEventLoop.processSelectedKey(NioEventLoop.java:688)
			at io.netty.channel.nio.NioEventLoop.processSelectedKeysOptimized(NioEventLoop.java:635)
			at io.netty.channel.nio.NioEventLoop.processSelectedKeys(NioEventLoop.java:552)
			at io.netty.channel.nio.NioEventLoop.run(NioEventLoop.java:514)
			... 4 common frames omitted
		Caused by: io.netty.channel.AbstractChannel$AnnotatedNoRouteToHostException: No route to host: no further information: /************:7001
		Caused by: java.net.NoRouteToHostException: No route to host: no further information
			at sun.nio.ch.SocketChannelImpl.checkConnect(Native Method)
			at sun.nio.ch.SocketChannelImpl.finishConnect(SocketChannelImpl.java:717)
			at io.netty.channel.socket.nio.NioSocketChannel.doFinishConnect(NioSocketChannel.java:330)
			at io.netty.channel.nio.AbstractNioChannel$AbstractNioUnsafe.finishConnect(AbstractNioChannel.java:334)
			at io.netty.channel.nio.NioEventLoop.processSelectedKey(NioEventLoop.java:688)
			at io.netty.channel.nio.NioEventLoop.processSelectedKeysOptimized(NioEventLoop.java:635)
			at io.netty.channel.nio.NioEventLoop.processSelectedKeys(NioEventLoop.java:552)
			at io.netty.channel.nio.NioEventLoop.run(NioEventLoop.java:514)
			at io.netty.util.concurrent.SingleThreadEventExecutor$6.run(SingleThreadEventExecutor.java:1050)
			at io.netty.util.internal.ThreadExecutorMap$2.run(ThreadExecutorMap.java:74)
			at io.netty.util.concurrent.FastThreadLocalRunnable.run(FastThreadLocalRunnable.java:30)
			at java.lang.Thread.run(Thread.java:748)
		Suppressed: io.lettuce.core.RedisConnectionException: Unable to connect to [************:7003]: No route to host: no further information: /************:7003
			at io.lettuce.core.cluster.topology.ClusterTopologyRefresh.lambda$getConnections$0(ClusterTopologyRefresh.java:290)
			at java.util.concurrent.CompletableFuture.uniWhenComplete(CompletableFuture.java:760)
			at java.util.concurrent.CompletableFuture$UniWhenComplete.tryFire(CompletableFuture.java:736)
			at java.util.concurrent.CompletableFuture.postComplete(CompletableFuture.java:474)
			at java.util.concurrent.CompletableFuture.completeExceptionally(CompletableFuture.java:1977)
			at io.lettuce.core.AbstractRedisClient.lambda$initializeChannelAsync0$4(AbstractRedisClient.java:329)
			at io.netty.util.concurrent.DefaultPromise.notifyListener0(DefaultPromise.java:577)
			at io.netty.util.concurrent.DefaultPromise.notifyListeners0(DefaultPromise.java:570)
			at io.netty.util.concurrent.DefaultPromise.notifyListenersNow(DefaultPromise.java:549)
			at io.netty.util.concurrent.DefaultPromise.notifyListeners(DefaultPromise.java:490)
			at io.netty.util.concurrent.DefaultPromise.setValue0(DefaultPromise.java:615)
			at io.netty.util.concurrent.DefaultPromise.setFailure0(DefaultPromise.java:608)
			at io.netty.util.concurrent.DefaultPromise.tryFailure(DefaultPromise.java:117)
			at io.netty.channel.nio.AbstractNioChannel$AbstractNioUnsafe.fulfillConnectPromise(AbstractNioChannel.java:321)
			at io.netty.channel.nio.AbstractNioChannel$AbstractNioUnsafe.finishConnect(AbstractNioChannel.java:337)
			at io.netty.channel.nio.NioEventLoop.processSelectedKey(NioEventLoop.java:688)
			at io.netty.channel.nio.NioEventLoop.processSelectedKeysOptimized(NioEventLoop.java:635)
			at io.netty.channel.nio.NioEventLoop.processSelectedKeys(NioEventLoop.java:552)
			at io.netty.channel.nio.NioEventLoop.run(NioEventLoop.java:514)
			... 4 common frames omitted
		Caused by: io.netty.channel.AbstractChannel$AnnotatedNoRouteToHostException: No route to host: no further information: /************:7003
		Caused by: java.net.NoRouteToHostException: No route to host: no further information
			at sun.nio.ch.SocketChannelImpl.checkConnect(Native Method)
			at sun.nio.ch.SocketChannelImpl.finishConnect(SocketChannelImpl.java:717)
			at io.netty.channel.socket.nio.NioSocketChannel.doFinishConnect(NioSocketChannel.java:330)
			at io.netty.channel.nio.AbstractNioChannel$AbstractNioUnsafe.finishConnect(AbstractNioChannel.java:334)
			at io.netty.channel.nio.NioEventLoop.processSelectedKey(NioEventLoop.java:688)
			at io.netty.channel.nio.NioEventLoop.processSelectedKeysOptimized(NioEventLoop.java:635)
			at io.netty.channel.nio.NioEventLoop.processSelectedKeys(NioEventLoop.java:552)
			at io.netty.channel.nio.NioEventLoop.run(NioEventLoop.java:514)
			at io.netty.util.concurrent.SingleThreadEventExecutor$6.run(SingleThreadEventExecutor.java:1050)
			at io.netty.util.internal.ThreadExecutorMap$2.run(ThreadExecutorMap.java:74)
			at io.netty.util.concurrent.FastThreadLocalRunnable.run(FastThreadLocalRunnable.java:30)
			at java.lang.Thread.run(Thread.java:748)
		Suppressed: io.lettuce.core.RedisConnectionException: Unable to connect to [************:7006]: No route to host: no further information: /************:7006
			at io.lettuce.core.cluster.topology.ClusterTopologyRefresh.lambda$getConnections$0(ClusterTopologyRefresh.java:290)
			at java.util.concurrent.CompletableFuture.uniWhenComplete(CompletableFuture.java:760)
			at java.util.concurrent.CompletableFuture$UniWhenComplete.tryFire(CompletableFuture.java:736)
			at java.util.concurrent.CompletableFuture.postComplete(CompletableFuture.java:474)
			at java.util.concurrent.CompletableFuture.completeExceptionally(CompletableFuture.java:1977)
			at io.lettuce.core.AbstractRedisClient.lambda$initializeChannelAsync0$4(AbstractRedisClient.java:329)
			at io.netty.util.concurrent.DefaultPromise.notifyListener0(DefaultPromise.java:577)
			at io.netty.util.concurrent.DefaultPromise.notifyListeners0(DefaultPromise.java:570)
			at io.netty.util.concurrent.DefaultPromise.notifyListenersNow(DefaultPromise.java:549)
			at io.netty.util.concurrent.DefaultPromise.notifyListeners(DefaultPromise.java:490)
			at io.netty.util.concurrent.DefaultPromise.setValue0(DefaultPromise.java:615)
			at io.netty.util.concurrent.DefaultPromise.setFailure0(DefaultPromise.java:608)
			at io.netty.util.concurrent.DefaultPromise.tryFailure(DefaultPromise.java:117)
			at io.netty.channel.nio.AbstractNioChannel$AbstractNioUnsafe.fulfillConnectPromise(AbstractNioChannel.java:321)
			at io.netty.channel.nio.AbstractNioChannel$AbstractNioUnsafe.finishConnect(AbstractNioChannel.java:337)
			at io.netty.channel.nio.NioEventLoop.processSelectedKey(NioEventLoop.java:688)
			at io.netty.channel.nio.NioEventLoop.processSelectedKeysOptimized(NioEventLoop.java:635)
			at io.netty.channel.nio.NioEventLoop.processSelectedKeys(NioEventLoop.java:552)
			at io.netty.channel.nio.NioEventLoop.run(NioEventLoop.java:514)
			... 4 common frames omitted
		Caused by: io.netty.channel.AbstractChannel$AnnotatedNoRouteToHostException: No route to host: no further information: /************:7006
		Caused by: java.net.NoRouteToHostException: No route to host: no further information
			at sun.nio.ch.SocketChannelImpl.checkConnect(Native Method)
			at sun.nio.ch.SocketChannelImpl.finishConnect(SocketChannelImpl.java:717)
			at io.netty.channel.socket.nio.NioSocketChannel.doFinishConnect(NioSocketChannel.java:330)
			at io.netty.channel.nio.AbstractNioChannel$AbstractNioUnsafe.finishConnect(AbstractNioChannel.java:334)
			at io.netty.channel.nio.NioEventLoop.processSelectedKey(NioEventLoop.java:688)
			at io.netty.channel.nio.NioEventLoop.processSelectedKeysOptimized(NioEventLoop.java:635)
			at io.netty.channel.nio.NioEventLoop.processSelectedKeys(NioEventLoop.java:552)
			at io.netty.channel.nio.NioEventLoop.run(NioEventLoop.java:514)
			at io.netty.util.concurrent.SingleThreadEventExecutor$6.run(SingleThreadEventExecutor.java:1050)
			at io.netty.util.internal.ThreadExecutorMap$2.run(ThreadExecutorMap.java:74)
			at io.netty.util.concurrent.FastThreadLocalRunnable.run(FastThreadLocalRunnable.java:30)
			at java.lang.Thread.run(Thread.java:748)
	Caused by: io.lettuce.core.RedisConnectionException: Unable to connect to [************:7004]: No route to host: no further information: /************:7004
		at io.lettuce.core.cluster.topology.ClusterTopologyRefresh.lambda$getConnections$0(ClusterTopologyRefresh.java:290)
		at java.util.concurrent.CompletableFuture.uniWhenComplete(CompletableFuture.java:760)
		at java.util.concurrent.CompletableFuture$UniWhenComplete.tryFire(CompletableFuture.java:736)
		at java.util.concurrent.CompletableFuture.postComplete(CompletableFuture.java:474)
		at java.util.concurrent.CompletableFuture.completeExceptionally(CompletableFuture.java:1977)
		at io.lettuce.core.AbstractRedisClient.lambda$initializeChannelAsync0$4(AbstractRedisClient.java:329)
		at io.netty.util.concurrent.DefaultPromise.notifyListener0(DefaultPromise.java:577)
		at io.netty.util.concurrent.DefaultPromise.notifyListeners0(DefaultPromise.java:570)
		at io.netty.util.concurrent.DefaultPromise.notifyListenersNow(DefaultPromise.java:549)
		at io.netty.util.concurrent.DefaultPromise.notifyListeners(DefaultPromise.java:490)
		at io.netty.util.concurrent.DefaultPromise.setValue0(DefaultPromise.java:615)
		at io.netty.util.concurrent.DefaultPromise.setFailure0(DefaultPromise.java:608)
		at io.netty.util.concurrent.DefaultPromise.tryFailure(DefaultPromise.java:117)
		at io.netty.channel.nio.AbstractNioChannel$AbstractNioUnsafe.fulfillConnectPromise(AbstractNioChannel.java:321)
		at io.netty.channel.nio.AbstractNioChannel$AbstractNioUnsafe.finishConnect(AbstractNioChannel.java:337)
		at io.netty.channel.nio.NioEventLoop.processSelectedKey(NioEventLoop.java:688)
		at io.netty.channel.nio.NioEventLoop.processSelectedKeysOptimized(NioEventLoop.java:635)
		at io.netty.channel.nio.NioEventLoop.processSelectedKeys(NioEventLoop.java:552)
		at io.netty.channel.nio.NioEventLoop.run(NioEventLoop.java:514)
		... 4 common frames omitted
	Caused by: io.netty.channel.AbstractChannel$AnnotatedNoRouteToHostException: No route to host: no further information: /************:7004
	Caused by: java.net.NoRouteToHostException: No route to host: no further information
		at sun.nio.ch.SocketChannelImpl.checkConnect(Native Method)
		at sun.nio.ch.SocketChannelImpl.finishConnect(SocketChannelImpl.java:717)
		at io.netty.channel.socket.nio.NioSocketChannel.doFinishConnect(NioSocketChannel.java:330)
		at io.netty.channel.nio.AbstractNioChannel$AbstractNioUnsafe.finishConnect(AbstractNioChannel.java:334)
		at io.netty.channel.nio.NioEventLoop.processSelectedKey(NioEventLoop.java:688)
		at io.netty.channel.nio.NioEventLoop.processSelectedKeysOptimized(NioEventLoop.java:635)
		at io.netty.channel.nio.NioEventLoop.processSelectedKeys(NioEventLoop.java:552)
		at io.netty.channel.nio.NioEventLoop.run(NioEventLoop.java:514)
		at io.netty.util.concurrent.SingleThreadEventExecutor$6.run(SingleThreadEventExecutor.java:1050)
		at io.netty.util.internal.ThreadExecutorMap$2.run(ThreadExecutorMap.java:74)
		at io.netty.util.concurrent.FastThreadLocalRunnable.run(FastThreadLocalRunnable.java:30)
		at java.lang.Thread.run(Thread.java:748)
Caused by: io.lettuce.core.RedisConnectionException: Unable to establish a connection to Redis Cluster at [RedisURI [host='************', port=7001], RedisURI [host='************', port=7002], RedisURI [host='************', port=7003], RedisURI [host='************', port=7004], RedisURI [host='************', port=7005], RedisURI [host='************', port=7006]]
	at io.lettuce.core.cluster.topology.AsyncConnections.get(AsyncConnections.java:89)
	at io.lettuce.core.cluster.topology.ClusterTopologyRefresh.loadViews(ClusterTopologyRefresh.java:76)
	at io.lettuce.core.cluster.RedisClusterClient.doLoadPartitions(RedisClusterClient.java:865)
	at io.lettuce.core.cluster.RedisClusterClient.loadPartitions(RedisClusterClient.java:845)
	... 10 common frames omitted
	Suppressed: io.lettuce.core.RedisConnectionException: Unable to connect to [************:7005]: No route to host: no further information: /************:7005
		at io.lettuce.core.cluster.topology.ClusterTopologyRefresh.lambda$getConnections$0(ClusterTopologyRefresh.java:290)
		at java.util.concurrent.CompletableFuture.uniWhenComplete(CompletableFuture.java:760)
		at java.util.concurrent.CompletableFuture$UniWhenComplete.tryFire(CompletableFuture.java:736)
		at java.util.concurrent.CompletableFuture.postComplete(CompletableFuture.java:474)
		at java.util.concurrent.CompletableFuture.completeExceptionally(CompletableFuture.java:1977)
		at io.lettuce.core.AbstractRedisClient.lambda$initializeChannelAsync0$4(AbstractRedisClient.java:329)
		at io.netty.util.concurrent.DefaultPromise.notifyListener0(DefaultPromise.java:577)
		at io.netty.util.concurrent.DefaultPromise.notifyListeners0(DefaultPromise.java:570)
		at io.netty.util.concurrent.DefaultPromise.notifyListenersNow(DefaultPromise.java:549)
		at io.netty.util.concurrent.DefaultPromise.notifyListeners(DefaultPromise.java:490)
		at io.netty.util.concurrent.DefaultPromise.setValue0(DefaultPromise.java:615)
		at io.netty.util.concurrent.DefaultPromise.setFailure0(DefaultPromise.java:608)
		at io.netty.util.concurrent.DefaultPromise.tryFailure(DefaultPromise.java:117)
		at io.netty.channel.nio.AbstractNioChannel$AbstractNioUnsafe.fulfillConnectPromise(AbstractNioChannel.java:321)
		at io.netty.channel.nio.AbstractNioChannel$AbstractNioUnsafe.finishConnect(AbstractNioChannel.java:337)
		at io.netty.channel.nio.NioEventLoop.processSelectedKey(NioEventLoop.java:688)
		at io.netty.channel.nio.NioEventLoop.processSelectedKeysOptimized(NioEventLoop.java:635)
		at io.netty.channel.nio.NioEventLoop.processSelectedKeys(NioEventLoop.java:552)
		at io.netty.channel.nio.NioEventLoop.run(NioEventLoop.java:514)
		... 4 common frames omitted
	Caused by: io.netty.channel.AbstractChannel$AnnotatedNoRouteToHostException: No route to host: no further information: /************:7005
	Caused by: java.net.NoRouteToHostException: No route to host: no further information
		at sun.nio.ch.SocketChannelImpl.checkConnect(Native Method)
		at sun.nio.ch.SocketChannelImpl.finishConnect(SocketChannelImpl.java:717)
		at io.netty.channel.socket.nio.NioSocketChannel.doFinishConnect(NioSocketChannel.java:330)
		at io.netty.channel.nio.AbstractNioChannel$AbstractNioUnsafe.finishConnect(AbstractNioChannel.java:334)
		at io.netty.channel.nio.NioEventLoop.processSelectedKey(NioEventLoop.java:688)
		at io.netty.channel.nio.NioEventLoop.processSelectedKeysOptimized(NioEventLoop.java:635)
		at io.netty.channel.nio.NioEventLoop.processSelectedKeys(NioEventLoop.java:552)
		at io.netty.channel.nio.NioEventLoop.run(NioEventLoop.java:514)
		at io.netty.util.concurrent.SingleThreadEventExecutor$6.run(SingleThreadEventExecutor.java:1050)
		at io.netty.util.internal.ThreadExecutorMap$2.run(ThreadExecutorMap.java:74)
		at io.netty.util.concurrent.FastThreadLocalRunnable.run(FastThreadLocalRunnable.java:30)
		at java.lang.Thread.run(Thread.java:748)
	Suppressed: io.lettuce.core.RedisConnectionException: Unable to connect to [************:7006]: No route to host: no further information: /************:7006
		at io.lettuce.core.cluster.topology.ClusterTopologyRefresh.lambda$getConnections$0(ClusterTopologyRefresh.java:290)
		at java.util.concurrent.CompletableFuture.uniWhenComplete(CompletableFuture.java:760)
		at java.util.concurrent.CompletableFuture$UniWhenComplete.tryFire(CompletableFuture.java:736)
		at java.util.concurrent.CompletableFuture.postComplete(CompletableFuture.java:474)
		at java.util.concurrent.CompletableFuture.completeExceptionally(CompletableFuture.java:1977)
		at io.lettuce.core.AbstractRedisClient.lambda$initializeChannelAsync0$4(AbstractRedisClient.java:329)
		at io.netty.util.concurrent.DefaultPromise.notifyListener0(DefaultPromise.java:577)
		at io.netty.util.concurrent.DefaultPromise.notifyListeners0(DefaultPromise.java:570)
		at io.netty.util.concurrent.DefaultPromise.notifyListenersNow(DefaultPromise.java:549)
		at io.netty.util.concurrent.DefaultPromise.notifyListeners(DefaultPromise.java:490)
		at io.netty.util.concurrent.DefaultPromise.setValue0(DefaultPromise.java:615)
		at io.netty.util.concurrent.DefaultPromise.setFailure0(DefaultPromise.java:608)
		at io.netty.util.concurrent.DefaultPromise.tryFailure(DefaultPromise.java:117)
		at io.netty.channel.nio.AbstractNioChannel$AbstractNioUnsafe.fulfillConnectPromise(AbstractNioChannel.java:321)
		at io.netty.channel.nio.AbstractNioChannel$AbstractNioUnsafe.finishConnect(AbstractNioChannel.java:337)
		at io.netty.channel.nio.NioEventLoop.processSelectedKey(NioEventLoop.java:688)
		at io.netty.channel.nio.NioEventLoop.processSelectedKeysOptimized(NioEventLoop.java:635)
		at io.netty.channel.nio.NioEventLoop.processSelectedKeys(NioEventLoop.java:552)
		at io.netty.channel.nio.NioEventLoop.run(NioEventLoop.java:514)
		... 4 common frames omitted
	Caused by: io.netty.channel.AbstractChannel$AnnotatedNoRouteToHostException: No route to host: no further information: /************:7006
	Caused by: java.net.NoRouteToHostException: No route to host: no further information
		at sun.nio.ch.SocketChannelImpl.checkConnect(Native Method)
		at sun.nio.ch.SocketChannelImpl.finishConnect(SocketChannelImpl.java:717)
		at io.netty.channel.socket.nio.NioSocketChannel.doFinishConnect(NioSocketChannel.java:330)
		at io.netty.channel.nio.AbstractNioChannel$AbstractNioUnsafe.finishConnect(AbstractNioChannel.java:334)
		at io.netty.channel.nio.NioEventLoop.processSelectedKey(NioEventLoop.java:688)
		at io.netty.channel.nio.NioEventLoop.processSelectedKeysOptimized(NioEventLoop.java:635)
		at io.netty.channel.nio.NioEventLoop.processSelectedKeys(NioEventLoop.java:552)
		at io.netty.channel.nio.NioEventLoop.run(NioEventLoop.java:514)
		at io.netty.util.concurrent.SingleThreadEventExecutor$6.run(SingleThreadEventExecutor.java:1050)
		at io.netty.util.internal.ThreadExecutorMap$2.run(ThreadExecutorMap.java:74)
		at io.netty.util.concurrent.FastThreadLocalRunnable.run(FastThreadLocalRunnable.java:30)
		at java.lang.Thread.run(Thread.java:748)
	Suppressed: io.lettuce.core.RedisConnectionException: Unable to connect to [************:7002]: No route to host: no further information: /************:7002
		at io.lettuce.core.cluster.topology.ClusterTopologyRefresh.lambda$getConnections$0(ClusterTopologyRefresh.java:290)
		at java.util.concurrent.CompletableFuture.uniWhenComplete(CompletableFuture.java:760)
		at java.util.concurrent.CompletableFuture$UniWhenComplete.tryFire(CompletableFuture.java:736)
		at java.util.concurrent.CompletableFuture.postComplete(CompletableFuture.java:474)
		at java.util.concurrent.CompletableFuture.completeExceptionally(CompletableFuture.java:1977)
		at io.lettuce.core.AbstractRedisClient.lambda$initializeChannelAsync0$4(AbstractRedisClient.java:329)
		at io.netty.util.concurrent.DefaultPromise.notifyListener0(DefaultPromise.java:577)
		at io.netty.util.concurrent.DefaultPromise.notifyListeners0(DefaultPromise.java:570)
		at io.netty.util.concurrent.DefaultPromise.notifyListenersNow(DefaultPromise.java:549)
		at io.netty.util.concurrent.DefaultPromise.notifyListeners(DefaultPromise.java:490)
		at io.netty.util.concurrent.DefaultPromise.setValue0(DefaultPromise.java:615)
		at io.netty.util.concurrent.DefaultPromise.setFailure0(DefaultPromise.java:608)
		at io.netty.util.concurrent.DefaultPromise.tryFailure(DefaultPromise.java:117)
		at io.netty.channel.nio.AbstractNioChannel$AbstractNioUnsafe.fulfillConnectPromise(AbstractNioChannel.java:321)
		at io.netty.channel.nio.AbstractNioChannel$AbstractNioUnsafe.finishConnect(AbstractNioChannel.java:337)
		at io.netty.channel.nio.NioEventLoop.processSelectedKey(NioEventLoop.java:688)
		at io.netty.channel.nio.NioEventLoop.processSelectedKeysOptimized(NioEventLoop.java:635)
		at io.netty.channel.nio.NioEventLoop.processSelectedKeys(NioEventLoop.java:552)
		at io.netty.channel.nio.NioEventLoop.run(NioEventLoop.java:514)
		... 4 common frames omitted
	Caused by: io.netty.channel.AbstractChannel$AnnotatedNoRouteToHostException: No route to host: no further information: /************:7002
	Caused by: java.net.NoRouteToHostException: No route to host: no further information
		at sun.nio.ch.SocketChannelImpl.checkConnect(Native Method)
		at sun.nio.ch.SocketChannelImpl.finishConnect(SocketChannelImpl.java:717)
		at io.netty.channel.socket.nio.NioSocketChannel.doFinishConnect(NioSocketChannel.java:330)
		at io.netty.channel.nio.AbstractNioChannel$AbstractNioUnsafe.finishConnect(AbstractNioChannel.java:334)
		at io.netty.channel.nio.NioEventLoop.processSelectedKey(NioEventLoop.java:688)
		at io.netty.channel.nio.NioEventLoop.processSelectedKeysOptimized(NioEventLoop.java:635)
		at io.netty.channel.nio.NioEventLoop.processSelectedKeys(NioEventLoop.java:552)
		at io.netty.channel.nio.NioEventLoop.run(NioEventLoop.java:514)
		at io.netty.util.concurrent.SingleThreadEventExecutor$6.run(SingleThreadEventExecutor.java:1050)
		at io.netty.util.internal.ThreadExecutorMap$2.run(ThreadExecutorMap.java:74)
		at io.netty.util.concurrent.FastThreadLocalRunnable.run(FastThreadLocalRunnable.java:30)
		at java.lang.Thread.run(Thread.java:748)
	Suppressed: io.lettuce.core.RedisConnectionException: Unable to connect to [************:7001]: No route to host: no further information: /************:7001
		at io.lettuce.core.cluster.topology.ClusterTopologyRefresh.lambda$getConnections$0(ClusterTopologyRefresh.java:290)
		at java.util.concurrent.CompletableFuture.uniWhenComplete(CompletableFuture.java:760)
		at java.util.concurrent.CompletableFuture$UniWhenComplete.tryFire(CompletableFuture.java:736)
		at java.util.concurrent.CompletableFuture.postComplete(CompletableFuture.java:474)
		at java.util.concurrent.CompletableFuture.completeExceptionally(CompletableFuture.java:1977)
		at io.lettuce.core.AbstractRedisClient.lambda$initializeChannelAsync0$4(AbstractRedisClient.java:329)
		at io.netty.util.concurrent.DefaultPromise.notifyListener0(DefaultPromise.java:577)
		at io.netty.util.concurrent.DefaultPromise.notifyListeners0(DefaultPromise.java:570)
		at io.netty.util.concurrent.DefaultPromise.notifyListenersNow(DefaultPromise.java:549)
		at io.netty.util.concurrent.DefaultPromise.notifyListeners(DefaultPromise.java:490)
		at io.netty.util.concurrent.DefaultPromise.setValue0(DefaultPromise.java:615)
		at io.netty.util.concurrent.DefaultPromise.setFailure0(DefaultPromise.java:608)
		at io.netty.util.concurrent.DefaultPromise.tryFailure(DefaultPromise.java:117)
		at io.netty.channel.nio.AbstractNioChannel$AbstractNioUnsafe.fulfillConnectPromise(AbstractNioChannel.java:321)
		at io.netty.channel.nio.AbstractNioChannel$AbstractNioUnsafe.finishConnect(AbstractNioChannel.java:337)
		at io.netty.channel.nio.NioEventLoop.processSelectedKey(NioEventLoop.java:688)
		at io.netty.channel.nio.NioEventLoop.processSelectedKeysOptimized(NioEventLoop.java:635)
		at io.netty.channel.nio.NioEventLoop.processSelectedKeys(NioEventLoop.java:552)
		at io.netty.channel.nio.NioEventLoop.run(NioEventLoop.java:514)
		... 4 common frames omitted
	Caused by: io.netty.channel.AbstractChannel$AnnotatedNoRouteToHostException: No route to host: no further information: /************:7001
	Caused by: java.net.NoRouteToHostException: No route to host: no further information
		at sun.nio.ch.SocketChannelImpl.checkConnect(Native Method)
		at sun.nio.ch.SocketChannelImpl.finishConnect(SocketChannelImpl.java:717)
		at io.netty.channel.socket.nio.NioSocketChannel.doFinishConnect(NioSocketChannel.java:330)
		at io.netty.channel.nio.AbstractNioChannel$AbstractNioUnsafe.finishConnect(AbstractNioChannel.java:334)
		at io.netty.channel.nio.NioEventLoop.processSelectedKey(NioEventLoop.java:688)
		at io.netty.channel.nio.NioEventLoop.processSelectedKeysOptimized(NioEventLoop.java:635)
		at io.netty.channel.nio.NioEventLoop.processSelectedKeys(NioEventLoop.java:552)
		at io.netty.channel.nio.NioEventLoop.run(NioEventLoop.java:514)
		at io.netty.util.concurrent.SingleThreadEventExecutor$6.run(SingleThreadEventExecutor.java:1050)
		at io.netty.util.internal.ThreadExecutorMap$2.run(ThreadExecutorMap.java:74)
		at io.netty.util.concurrent.FastThreadLocalRunnable.run(FastThreadLocalRunnable.java:30)
		at java.lang.Thread.run(Thread.java:748)
	Suppressed: io.lettuce.core.RedisConnectionException: Unable to connect to [************:7003]: No route to host: no further information: /************:7003
		at io.lettuce.core.cluster.topology.ClusterTopologyRefresh.lambda$getConnections$0(ClusterTopologyRefresh.java:290)
		at java.util.concurrent.CompletableFuture.uniWhenComplete(CompletableFuture.java:760)
		at java.util.concurrent.CompletableFuture$UniWhenComplete.tryFire(CompletableFuture.java:736)
		at java.util.concurrent.CompletableFuture.postComplete(CompletableFuture.java:474)
		at java.util.concurrent.CompletableFuture.completeExceptionally(CompletableFuture.java:1977)
		at io.lettuce.core.AbstractRedisClient.lambda$initializeChannelAsync0$4(AbstractRedisClient.java:329)
		at io.netty.util.concurrent.DefaultPromise.notifyListener0(DefaultPromise.java:577)
		at io.netty.util.concurrent.DefaultPromise.notifyListeners0(DefaultPromise.java:570)
		at io.netty.util.concurrent.DefaultPromise.notifyListenersNow(DefaultPromise.java:549)
		at io.netty.util.concurrent.DefaultPromise.notifyListeners(DefaultPromise.java:490)
		at io.netty.util.concurrent.DefaultPromise.setValue0(DefaultPromise.java:615)
		at io.netty.util.concurrent.DefaultPromise.setFailure0(DefaultPromise.java:608)
		at io.netty.util.concurrent.DefaultPromise.tryFailure(DefaultPromise.java:117)
		at io.netty.channel.nio.AbstractNioChannel$AbstractNioUnsafe.fulfillConnectPromise(AbstractNioChannel.java:321)
		at io.netty.channel.nio.AbstractNioChannel$AbstractNioUnsafe.finishConnect(AbstractNioChannel.java:337)
		at io.netty.channel.nio.NioEventLoop.processSelectedKey(NioEventLoop.java:688)
		at io.netty.channel.nio.NioEventLoop.processSelectedKeysOptimized(NioEventLoop.java:635)
		at io.netty.channel.nio.NioEventLoop.processSelectedKeys(NioEventLoop.java:552)
		at io.netty.channel.nio.NioEventLoop.run(NioEventLoop.java:514)
		... 4 common frames omitted
	Caused by: io.netty.channel.AbstractChannel$AnnotatedNoRouteToHostException: No route to host: no further information: /************:7003
	Caused by: java.net.NoRouteToHostException: No route to host: no further information
		at sun.nio.ch.SocketChannelImpl.checkConnect(Native Method)
		at sun.nio.ch.SocketChannelImpl.finishConnect(SocketChannelImpl.java:717)
		at io.netty.channel.socket.nio.NioSocketChannel.doFinishConnect(NioSocketChannel.java:330)
		at io.netty.channel.nio.AbstractNioChannel$AbstractNioUnsafe.finishConnect(AbstractNioChannel.java:334)
		at io.netty.channel.nio.NioEventLoop.processSelectedKey(NioEventLoop.java:688)
		at io.netty.channel.nio.NioEventLoop.processSelectedKeysOptimized(NioEventLoop.java:635)
		at io.netty.channel.nio.NioEventLoop.processSelectedKeys(NioEventLoop.java:552)
		at io.netty.channel.nio.NioEventLoop.run(NioEventLoop.java:514)
		at io.netty.util.concurrent.SingleThreadEventExecutor$6.run(SingleThreadEventExecutor.java:1050)
		at io.netty.util.internal.ThreadExecutorMap$2.run(ThreadExecutorMap.java:74)
		at io.netty.util.concurrent.FastThreadLocalRunnable.run(FastThreadLocalRunnable.java:30)
		at java.lang.Thread.run(Thread.java:748)
Caused by: io.lettuce.core.RedisConnectionException: Unable to connect to [************:7004]: No route to host: no further information: /************:7004
	at io.lettuce.core.cluster.topology.ClusterTopologyRefresh.lambda$getConnections$0(ClusterTopologyRefresh.java:290)
	at java.util.concurrent.CompletableFuture.uniWhenComplete(CompletableFuture.java:760)
	at java.util.concurrent.CompletableFuture$UniWhenComplete.tryFire(CompletableFuture.java:736)
	at java.util.concurrent.CompletableFuture.postComplete(CompletableFuture.java:474)
	at java.util.concurrent.CompletableFuture.completeExceptionally(CompletableFuture.java:1977)
	at io.lettuce.core.AbstractRedisClient.lambda$initializeChannelAsync0$4(AbstractRedisClient.java:329)
	at io.netty.util.concurrent.DefaultPromise.notifyListener0(DefaultPromise.java:577)
	at io.netty.util.concurrent.DefaultPromise.notifyListeners0(DefaultPromise.java:570)
	at io.netty.util.concurrent.DefaultPromise.notifyListenersNow(DefaultPromise.java:549)
	at io.netty.util.concurrent.DefaultPromise.notifyListeners(DefaultPromise.java:490)
	at io.netty.util.concurrent.DefaultPromise.setValue0(DefaultPromise.java:615)
	at io.netty.util.concurrent.DefaultPromise.setFailure0(DefaultPromise.java:608)
	at io.netty.util.concurrent.DefaultPromise.tryFailure(DefaultPromise.java:117)
	at io.netty.channel.nio.AbstractNioChannel$AbstractNioUnsafe.fulfillConnectPromise(AbstractNioChannel.java:321)
	at io.netty.channel.nio.AbstractNioChannel$AbstractNioUnsafe.finishConnect(AbstractNioChannel.java:337)
	at io.netty.channel.nio.NioEventLoop.processSelectedKey(NioEventLoop.java:688)
	at io.netty.channel.nio.NioEventLoop.processSelectedKeysOptimized(NioEventLoop.java:635)
	at io.netty.channel.nio.NioEventLoop.processSelectedKeys(NioEventLoop.java:552)
	at io.netty.channel.nio.NioEventLoop.run(NioEventLoop.java:514)
	... 4 common frames omitted
Caused by: io.netty.channel.AbstractChannel$AnnotatedNoRouteToHostException: No route to host: no further information: /************:7004
Caused by: java.net.NoRouteToHostException: No route to host: no further information
	at sun.nio.ch.SocketChannelImpl.checkConnect(Native Method)
	at sun.nio.ch.SocketChannelImpl.finishConnect(SocketChannelImpl.java:717)
	at io.netty.channel.socket.nio.NioSocketChannel.doFinishConnect(NioSocketChannel.java:330)
	at io.netty.channel.nio.AbstractNioChannel$AbstractNioUnsafe.finishConnect(AbstractNioChannel.java:334)
	at io.netty.channel.nio.NioEventLoop.processSelectedKey(NioEventLoop.java:688)
	at io.netty.channel.nio.NioEventLoop.processSelectedKeysOptimized(NioEventLoop.java:635)
	at io.netty.channel.nio.NioEventLoop.processSelectedKeys(NioEventLoop.java:552)
	at io.netty.channel.nio.NioEventLoop.run(NioEventLoop.java:514)
	at io.netty.util.concurrent.SingleThreadEventExecutor$6.run(SingleThreadEventExecutor.java:1050)
	at io.netty.util.internal.ThreadExecutorMap$2.run(ThreadExecutorMap.java:74)
	at io.netty.util.concurrent.FastThreadLocalRunnable.run(FastThreadLocalRunnable.java:30)
	at java.lang.Thread.run(Thread.java:748)
2025-06-05 03:30:53.837 [redisson-netty-5-18] WARN  org.redisson.client.handler.CommandDecoder.completeResponse Line:445 - response has been skipped due to timeout! channel: [id: 0xd1d39432, L:/***********:49752 - R:************/************:7003], command: (CLUSTER NODES), params: [], result: [ClusterNodeInfo [nodeId=4dcfd30d1191cb5e65ef69e88dc76592f8b3d14d, address=redis://************:7005, flags=[MASTER], slaveOf=null, slotRanges=[[10923-16383]]], ClusterNodeInfo [nodeId=248f8c5f1a3d07d891f1bcf924c02097c5509e0d, address=redis://************:7001, flags=[SLAVE], slaveOf=64f45f937e6f8c36d0fb7cf09195403c4cccdf1d, slotRanges=[]], ClusterNodeInfo [nodeId=02aff3b78f8c09c43cbfc2346eac68a16a942097, address=redis://************:7003, flags=[MASTER, MYSELF], slaveOf=null, slotRanges=[[5461-10922]]], ClusterNodeInfo [nodeId=85e61682488d3a355264d212607a63832da60e8c, address=redis://************:7006, flags=[SLAVE], slaveOf=02aff3b78f8c09c43cbfc2346eac68a16a942097, slotRanges=[]], ClusterNodeInfo [nodeId=64f45f937e6f8c36d0fb7cf09195403c4cccdf1d, address=redis://************:7004, flags=[MASTER], slaveOf=null, slotRanges=[[0-5460]]], ClusterNodeInfo [nodeId=5514edc6d1aaab0526b0093a424e9f352e9914ed, address=redis://************:7002, flags=[SLAVE], slaveOf=4dcfd30d1191cb5e65ef69e88dc76592f8b3d14d, slotRanges=[]]]
2025-06-05 03:31:29.197 [lettuce-nioEventLoop-4-7] WARN  io.lettuce.core.protocol.ConnectionWatchdog.log Line:153 - Cannot reconnect to [************:7003]: connection timed out: /************:7003
2025-06-05 04:12:21.979 [redisson-netty-5-5] WARN  org.redisson.client.handler.CommandDecoder.completeResponse Line:445 - response has been skipped due to timeout! channel: [id: 0xc8303cfb, L:/***********:64050 - R:************/************:7003], command: (CLUSTER NODES), params: [], result: [ClusterNodeInfo [nodeId=4dcfd30d1191cb5e65ef69e88dc76592f8b3d14d, address=redis://************:7005, flags=[MASTER], slaveOf=null, slotRanges=[[10923-16383]]], ClusterNodeInfo [nodeId=248f8c5f1a3d07d891f1bcf924c02097c5509e0d, address=redis://************:7001, flags=[SLAVE], slaveOf=64f45f937e6f8c36d0fb7cf09195403c4cccdf1d, slotRanges=[]], ClusterNodeInfo [nodeId=02aff3b78f8c09c43cbfc2346eac68a16a942097, address=redis://************:7003, flags=[MASTER, MYSELF], slaveOf=null, slotRanges=[[5461-10922]]], ClusterNodeInfo [nodeId=85e61682488d3a355264d212607a63832da60e8c, address=redis://************:7006, flags=[SLAVE], slaveOf=02aff3b78f8c09c43cbfc2346eac68a16a942097, slotRanges=[]], ClusterNodeInfo [nodeId=64f45f937e6f8c36d0fb7cf09195403c4cccdf1d, address=redis://************:7004, flags=[MASTER], slaveOf=null, slotRanges=[[0-5460]]], ClusterNodeInfo [nodeId=5514edc6d1aaab0526b0093a424e9f352e9914ed, address=redis://************:7002, flags=[SLAVE], slaveOf=4dcfd30d1191cb5e65ef69e88dc76592f8b3d14d, slotRanges=[]]]
2025-06-05 04:33:05.913 [redisson-netty-5-13] WARN  org.redisson.client.handler.CommandDecoder.completeResponse Line:445 - response has been skipped due to timeout! channel: [id: 0x20e863b3, L:/***********:57734 - R:************/************:7003], command: (CLUSTER NODES), params: [], result: [ClusterNodeInfo [nodeId=4dcfd30d1191cb5e65ef69e88dc76592f8b3d14d, address=redis://************:7005, flags=[MASTER], slaveOf=null, slotRanges=[[10923-16383]]], ClusterNodeInfo [nodeId=248f8c5f1a3d07d891f1bcf924c02097c5509e0d, address=redis://************:7001, flags=[SLAVE], slaveOf=64f45f937e6f8c36d0fb7cf09195403c4cccdf1d, slotRanges=[]], ClusterNodeInfo [nodeId=02aff3b78f8c09c43cbfc2346eac68a16a942097, address=redis://************:7003, flags=[MASTER, MYSELF], slaveOf=null, slotRanges=[[5461-10922]]], ClusterNodeInfo [nodeId=85e61682488d3a355264d212607a63832da60e8c, address=redis://************:7006, flags=[SLAVE], slaveOf=02aff3b78f8c09c43cbfc2346eac68a16a942097, slotRanges=[]], ClusterNodeInfo [nodeId=64f45f937e6f8c36d0fb7cf09195403c4cccdf1d, address=redis://************:7004, flags=[MASTER], slaveOf=null, slotRanges=[[0-5460]]], ClusterNodeInfo [nodeId=5514edc6d1aaab0526b0093a424e9f352e9914ed, address=redis://************:7002, flags=[SLAVE], slaveOf=4dcfd30d1191cb5e65ef69e88dc76592f8b3d14d, slotRanges=[]]]
2025-06-05 04:53:48.051 [lettuce-nioEventLoop-4-10] WARN  io.lettuce.core.cluster.topology.ClusterTopologyRefresh.lambda$getConnections$0 Line:284 - Unable to connect to [************:7002]: No route to host: no further information: /************:7002
2025-06-05 04:53:48.051 [lettuce-nioEventLoop-4-9] WARN  io.lettuce.core.cluster.topology.ClusterTopologyRefresh.lambda$getConnections$0 Line:284 - Unable to connect to [************:7001]: No route to host: no further information: /************:7001
2025-06-05 04:53:48.052 [lettuce-nioEventLoop-4-12] WARN  io.lettuce.core.cluster.topology.ClusterTopologyRefresh.lambda$getConnections$0 Line:284 - Unable to connect to [************:7004]: No route to host: no further information: /************:7004
2025-06-05 04:53:48.052 [lettuce-nioEventLoop-4-1] WARN  io.lettuce.core.cluster.topology.ClusterTopologyRefresh.lambda$getConnections$0 Line:284 - Unable to connect to [************:7005]: No route to host: no further information: /************:7005
2025-06-05 04:53:48.053 [lettuce-nioEventLoop-4-2] WARN  io.lettuce.core.cluster.topology.ClusterTopologyRefresh.lambda$getConnections$0 Line:284 - Unable to connect to [************:7006]: No route to host: no further information: /************:7006
2025-06-05 04:53:48.065 [lettuce-nioEventLoop-4-11] WARN  io.lettuce.core.cluster.topology.ClusterTopologyRefresh.lambda$getConnections$0 Line:284 - Unable to connect to [************:7003]: No route to host: no further information: /************:7003
2025-06-05 04:53:48.069 [lettuce-nioEventLoop-4-3] WARN  io.lettuce.core.cluster.topology.ClusterTopologyRefresh.lambda$getConnections$0 Line:284 - Unable to connect to [************:7006]: No route to host: no further information: /************:7006
2025-06-05 04:53:48.070 [lettuce-nioEventLoop-4-4] WARN  io.lettuce.core.cluster.topology.ClusterTopologyRefresh.lambda$getConnections$0 Line:284 - Unable to connect to [************:7004]: No route to host: no further information: /************:7004
2025-06-05 04:53:48.070 [lettuce-nioEventLoop-4-5] WARN  io.lettuce.core.cluster.topology.ClusterTopologyRefresh.lambda$getConnections$0 Line:284 - Unable to connect to [************:7005]: No route to host: no further information: /************:7005
2025-06-05 04:53:48.071 [lettuce-nioEventLoop-4-7] WARN  io.lettuce.core.cluster.topology.ClusterTopologyRefresh.lambda$getConnections$0 Line:284 - Unable to connect to [************:7003]: No route to host: no further information: /************:7003
2025-06-05 04:53:48.071 [lettuce-nioEventLoop-4-8] WARN  io.lettuce.core.cluster.topology.ClusterTopologyRefresh.lambda$getConnections$0 Line:284 - Unable to connect to [************:7001]: No route to host: no further information: /************:7001
2025-06-05 04:53:48.080 [lettuce-nioEventLoop-4-6] WARN  io.lettuce.core.cluster.topology.ClusterTopologyRefresh.lambda$getConnections$0 Line:284 - Unable to connect to [************:7002]: No route to host: no further information: /************:7002
2025-06-05 04:53:48.095 [lettuce-eventExecutorLoop-1-9] WARN  io.lettuce.core.cluster.ClusterTopologyRefreshScheduler.doRun Line:258 - Cannot refresh Redis Cluster topology
io.lettuce.core.RedisException: Cannot retrieve initial cluster partitions from initial URIs [RedisURI [host='************', port=7006], RedisURI [host='************', port=7004], RedisURI [host='************', port=7005], RedisURI [host='************', port=7002], RedisURI [host='************', port=7003], RedisURI [host='************', port=7001]]
	at io.lettuce.core.cluster.RedisClusterClient.loadPartitions(RedisClusterClient.java:848)
	at io.lettuce.core.cluster.RedisClusterClient.reloadPartitions(RedisClusterClient.java:784)
	at io.lettuce.core.cluster.ClusterTopologyRefreshScheduler$ClusterTopologyRefreshTask.doRun(ClusterTopologyRefreshScheduler.java:256)
	at io.lettuce.core.cluster.ClusterTopologyRefreshScheduler$ClusterTopologyRefreshTask.run(ClusterTopologyRefreshScheduler.java:236)
	at io.netty.util.concurrent.PromiseTask.runTask(PromiseTask.java:98)
	at io.netty.util.concurrent.PromiseTask.run(PromiseTask.java:106)
	at io.netty.util.concurrent.DefaultEventExecutor.run(DefaultEventExecutor.java:66)
	at io.netty.util.concurrent.SingleThreadEventExecutor$6.run(SingleThreadEventExecutor.java:1050)
	at io.netty.util.internal.ThreadExecutorMap$2.run(ThreadExecutorMap.java:74)
	at io.netty.util.concurrent.FastThreadLocalRunnable.run(FastThreadLocalRunnable.java:30)
	at java.lang.Thread.run(Thread.java:748)
	Suppressed: io.lettuce.core.RedisConnectionException: Unable to establish a connection to Redis Cluster at [RedisURI [host='************', port=7001], RedisURI [host='************', port=7002], RedisURI [host='************', port=7003], RedisURI [host='************', port=7004], RedisURI [host='************', port=7005], RedisURI [host='************', port=7006]]
		at io.lettuce.core.cluster.topology.AsyncConnections.get(AsyncConnections.java:89)
		at io.lettuce.core.cluster.topology.ClusterTopologyRefresh.loadViews(ClusterTopologyRefresh.java:76)
		at io.lettuce.core.cluster.RedisClusterClient.doLoadPartitions(RedisClusterClient.java:865)
		at io.lettuce.core.cluster.RedisClusterClient.loadPartitions(RedisClusterClient.java:838)
		... 10 common frames omitted
		Suppressed: io.lettuce.core.RedisConnectionException: Unable to connect to [************:7001]: No route to host: no further information: /************:7001
			at io.lettuce.core.cluster.topology.ClusterTopologyRefresh.lambda$getConnections$0(ClusterTopologyRefresh.java:290)
			at java.util.concurrent.CompletableFuture.uniWhenComplete(CompletableFuture.java:760)
			at java.util.concurrent.CompletableFuture$UniWhenComplete.tryFire(CompletableFuture.java:736)
			at java.util.concurrent.CompletableFuture.postComplete(CompletableFuture.java:474)
			at java.util.concurrent.CompletableFuture.completeExceptionally(CompletableFuture.java:1977)
			at io.lettuce.core.AbstractRedisClient.lambda$initializeChannelAsync0$4(AbstractRedisClient.java:329)
			at io.netty.util.concurrent.DefaultPromise.notifyListener0(DefaultPromise.java:577)
			at io.netty.util.concurrent.DefaultPromise.notifyListeners0(DefaultPromise.java:570)
			at io.netty.util.concurrent.DefaultPromise.notifyListenersNow(DefaultPromise.java:549)
			at io.netty.util.concurrent.DefaultPromise.notifyListeners(DefaultPromise.java:490)
			at io.netty.util.concurrent.DefaultPromise.setValue0(DefaultPromise.java:615)
			at io.netty.util.concurrent.DefaultPromise.setFailure0(DefaultPromise.java:608)
			at io.netty.util.concurrent.DefaultPromise.tryFailure(DefaultPromise.java:117)
			at io.netty.channel.nio.AbstractNioChannel$AbstractNioUnsafe.fulfillConnectPromise(AbstractNioChannel.java:321)
			at io.netty.channel.nio.AbstractNioChannel$AbstractNioUnsafe.finishConnect(AbstractNioChannel.java:337)
			at io.netty.channel.nio.NioEventLoop.processSelectedKey(NioEventLoop.java:688)
			at io.netty.channel.nio.NioEventLoop.processSelectedKeysOptimized(NioEventLoop.java:635)
			at io.netty.channel.nio.NioEventLoop.processSelectedKeys(NioEventLoop.java:552)
			at io.netty.channel.nio.NioEventLoop.run(NioEventLoop.java:514)
			... 4 common frames omitted
		Caused by: io.netty.channel.AbstractChannel$AnnotatedNoRouteToHostException: No route to host: no further information: /************:7001
		Caused by: java.net.NoRouteToHostException: No route to host: no further information
			at sun.nio.ch.SocketChannelImpl.checkConnect(Native Method)
			at sun.nio.ch.SocketChannelImpl.finishConnect(SocketChannelImpl.java:717)
			at io.netty.channel.socket.nio.NioSocketChannel.doFinishConnect(NioSocketChannel.java:330)
			at io.netty.channel.nio.AbstractNioChannel$AbstractNioUnsafe.finishConnect(AbstractNioChannel.java:334)
			at io.netty.channel.nio.NioEventLoop.processSelectedKey(NioEventLoop.java:688)
			at io.netty.channel.nio.NioEventLoop.processSelectedKeysOptimized(NioEventLoop.java:635)
			at io.netty.channel.nio.NioEventLoop.processSelectedKeys(NioEventLoop.java:552)
			at io.netty.channel.nio.NioEventLoop.run(NioEventLoop.java:514)
			at io.netty.util.concurrent.SingleThreadEventExecutor$6.run(SingleThreadEventExecutor.java:1050)
			at io.netty.util.internal.ThreadExecutorMap$2.run(ThreadExecutorMap.java:74)
			at io.netty.util.concurrent.FastThreadLocalRunnable.run(FastThreadLocalRunnable.java:30)
			at java.lang.Thread.run(Thread.java:748)
		Suppressed: io.lettuce.core.RedisConnectionException: Unable to connect to [************:7004]: No route to host: no further information: /************:7004
			at io.lettuce.core.cluster.topology.ClusterTopologyRefresh.lambda$getConnections$0(ClusterTopologyRefresh.java:290)
			at java.util.concurrent.CompletableFuture.uniWhenComplete(CompletableFuture.java:760)
			at java.util.concurrent.CompletableFuture$UniWhenComplete.tryFire(CompletableFuture.java:736)
			at java.util.concurrent.CompletableFuture.postComplete(CompletableFuture.java:474)
			at java.util.concurrent.CompletableFuture.completeExceptionally(CompletableFuture.java:1977)
			at io.lettuce.core.AbstractRedisClient.lambda$initializeChannelAsync0$4(AbstractRedisClient.java:329)
			at io.netty.util.concurrent.DefaultPromise.notifyListener0(DefaultPromise.java:577)
			at io.netty.util.concurrent.DefaultPromise.notifyListeners0(DefaultPromise.java:570)
			at io.netty.util.concurrent.DefaultPromise.notifyListenersNow(DefaultPromise.java:549)
			at io.netty.util.concurrent.DefaultPromise.notifyListeners(DefaultPromise.java:490)
			at io.netty.util.concurrent.DefaultPromise.setValue0(DefaultPromise.java:615)
			at io.netty.util.concurrent.DefaultPromise.setFailure0(DefaultPromise.java:608)
			at io.netty.util.concurrent.DefaultPromise.tryFailure(DefaultPromise.java:117)
			at io.netty.channel.nio.AbstractNioChannel$AbstractNioUnsafe.fulfillConnectPromise(AbstractNioChannel.java:321)
			at io.netty.channel.nio.AbstractNioChannel$AbstractNioUnsafe.finishConnect(AbstractNioChannel.java:337)
			at io.netty.channel.nio.NioEventLoop.processSelectedKey(NioEventLoop.java:688)
			at io.netty.channel.nio.NioEventLoop.processSelectedKeysOptimized(NioEventLoop.java:635)
			at io.netty.channel.nio.NioEventLoop.processSelectedKeys(NioEventLoop.java:552)
			at io.netty.channel.nio.NioEventLoop.run(NioEventLoop.java:514)
			... 4 common frames omitted
		Caused by: io.netty.channel.AbstractChannel$AnnotatedNoRouteToHostException: No route to host: no further information: /************:7004
		Caused by: java.net.NoRouteToHostException: No route to host: no further information
			at sun.nio.ch.SocketChannelImpl.checkConnect(Native Method)
			at sun.nio.ch.SocketChannelImpl.finishConnect(SocketChannelImpl.java:717)
			at io.netty.channel.socket.nio.NioSocketChannel.doFinishConnect(NioSocketChannel.java:330)
			at io.netty.channel.nio.AbstractNioChannel$AbstractNioUnsafe.finishConnect(AbstractNioChannel.java:334)
			at io.netty.channel.nio.NioEventLoop.processSelectedKey(NioEventLoop.java:688)
			at io.netty.channel.nio.NioEventLoop.processSelectedKeysOptimized(NioEventLoop.java:635)
			at io.netty.channel.nio.NioEventLoop.processSelectedKeys(NioEventLoop.java:552)
			at io.netty.channel.nio.NioEventLoop.run(NioEventLoop.java:514)
			at io.netty.util.concurrent.SingleThreadEventExecutor$6.run(SingleThreadEventExecutor.java:1050)
			at io.netty.util.internal.ThreadExecutorMap$2.run(ThreadExecutorMap.java:74)
			at io.netty.util.concurrent.FastThreadLocalRunnable.run(FastThreadLocalRunnable.java:30)
			at java.lang.Thread.run(Thread.java:748)
		Suppressed: io.lettuce.core.RedisConnectionException: Unable to connect to [************:7005]: No route to host: no further information: /************:7005
			at io.lettuce.core.cluster.topology.ClusterTopologyRefresh.lambda$getConnections$0(ClusterTopologyRefresh.java:290)
			at java.util.concurrent.CompletableFuture.uniWhenComplete(CompletableFuture.java:760)
			at java.util.concurrent.CompletableFuture$UniWhenComplete.tryFire(CompletableFuture.java:736)
			at java.util.concurrent.CompletableFuture.postComplete(CompletableFuture.java:474)
			at java.util.concurrent.CompletableFuture.completeExceptionally(CompletableFuture.java:1977)
			at io.lettuce.core.AbstractRedisClient.lambda$initializeChannelAsync0$4(AbstractRedisClient.java:329)
			at io.netty.util.concurrent.DefaultPromise.notifyListener0(DefaultPromise.java:577)
			at io.netty.util.concurrent.DefaultPromise.notifyListeners0(DefaultPromise.java:570)
			at io.netty.util.concurrent.DefaultPromise.notifyListenersNow(DefaultPromise.java:549)
			at io.netty.util.concurrent.DefaultPromise.notifyListeners(DefaultPromise.java:490)
			at io.netty.util.concurrent.DefaultPromise.setValue0(DefaultPromise.java:615)
			at io.netty.util.concurrent.DefaultPromise.setFailure0(DefaultPromise.java:608)
			at io.netty.util.concurrent.DefaultPromise.tryFailure(DefaultPromise.java:117)
			at io.netty.channel.nio.AbstractNioChannel$AbstractNioUnsafe.fulfillConnectPromise(AbstractNioChannel.java:321)
			at io.netty.channel.nio.AbstractNioChannel$AbstractNioUnsafe.finishConnect(AbstractNioChannel.java:337)
			at io.netty.channel.nio.NioEventLoop.processSelectedKey(NioEventLoop.java:688)
			at io.netty.channel.nio.NioEventLoop.processSelectedKeysOptimized(NioEventLoop.java:635)
			at io.netty.channel.nio.NioEventLoop.processSelectedKeys(NioEventLoop.java:552)
			at io.netty.channel.nio.NioEventLoop.run(NioEventLoop.java:514)
			... 4 common frames omitted
		Caused by: io.netty.channel.AbstractChannel$AnnotatedNoRouteToHostException: No route to host: no further information: /************:7005
		Caused by: java.net.NoRouteToHostException: No route to host: no further information
			at sun.nio.ch.SocketChannelImpl.checkConnect(Native Method)
			at sun.nio.ch.SocketChannelImpl.finishConnect(SocketChannelImpl.java:717)
			at io.netty.channel.socket.nio.NioSocketChannel.doFinishConnect(NioSocketChannel.java:330)
			at io.netty.channel.nio.AbstractNioChannel$AbstractNioUnsafe.finishConnect(AbstractNioChannel.java:334)
			at io.netty.channel.nio.NioEventLoop.processSelectedKey(NioEventLoop.java:688)
			at io.netty.channel.nio.NioEventLoop.processSelectedKeysOptimized(NioEventLoop.java:635)
			at io.netty.channel.nio.NioEventLoop.processSelectedKeys(NioEventLoop.java:552)
			at io.netty.channel.nio.NioEventLoop.run(NioEventLoop.java:514)
			at io.netty.util.concurrent.SingleThreadEventExecutor$6.run(SingleThreadEventExecutor.java:1050)
			at io.netty.util.internal.ThreadExecutorMap$2.run(ThreadExecutorMap.java:74)
			at io.netty.util.concurrent.FastThreadLocalRunnable.run(FastThreadLocalRunnable.java:30)
			at java.lang.Thread.run(Thread.java:748)
		Suppressed: io.lettuce.core.RedisConnectionException: Unable to connect to [************:7006]: No route to host: no further information: /************:7006
			at io.lettuce.core.cluster.topology.ClusterTopologyRefresh.lambda$getConnections$0(ClusterTopologyRefresh.java:290)
			at java.util.concurrent.CompletableFuture.uniWhenComplete(CompletableFuture.java:760)
			at java.util.concurrent.CompletableFuture$UniWhenComplete.tryFire(CompletableFuture.java:736)
			at java.util.concurrent.CompletableFuture.postComplete(CompletableFuture.java:474)
			at java.util.concurrent.CompletableFuture.completeExceptionally(CompletableFuture.java:1977)
			at io.lettuce.core.AbstractRedisClient.lambda$initializeChannelAsync0$4(AbstractRedisClient.java:329)
			at io.netty.util.concurrent.DefaultPromise.notifyListener0(DefaultPromise.java:577)
			at io.netty.util.concurrent.DefaultPromise.notifyListeners0(DefaultPromise.java:570)
			at io.netty.util.concurrent.DefaultPromise.notifyListenersNow(DefaultPromise.java:549)
			at io.netty.util.concurrent.DefaultPromise.notifyListeners(DefaultPromise.java:490)
			at io.netty.util.concurrent.DefaultPromise.setValue0(DefaultPromise.java:615)
			at io.netty.util.concurrent.DefaultPromise.setFailure0(DefaultPromise.java:608)
			at io.netty.util.concurrent.DefaultPromise.tryFailure(DefaultPromise.java:117)
			at io.netty.channel.nio.AbstractNioChannel$AbstractNioUnsafe.fulfillConnectPromise(AbstractNioChannel.java:321)
			at io.netty.channel.nio.AbstractNioChannel$AbstractNioUnsafe.finishConnect(AbstractNioChannel.java:337)
			at io.netty.channel.nio.NioEventLoop.processSelectedKey(NioEventLoop.java:688)
			at io.netty.channel.nio.NioEventLoop.processSelectedKeysOptimized(NioEventLoop.java:635)
			at io.netty.channel.nio.NioEventLoop.processSelectedKeys(NioEventLoop.java:552)
			at io.netty.channel.nio.NioEventLoop.run(NioEventLoop.java:514)
			... 4 common frames omitted
		Caused by: io.netty.channel.AbstractChannel$AnnotatedNoRouteToHostException: No route to host: no further information: /************:7006
		Caused by: java.net.NoRouteToHostException: No route to host: no further information
			at sun.nio.ch.SocketChannelImpl.checkConnect(Native Method)
			at sun.nio.ch.SocketChannelImpl.finishConnect(SocketChannelImpl.java:717)
			at io.netty.channel.socket.nio.NioSocketChannel.doFinishConnect(NioSocketChannel.java:330)
			at io.netty.channel.nio.AbstractNioChannel$AbstractNioUnsafe.finishConnect(AbstractNioChannel.java:334)
			at io.netty.channel.nio.NioEventLoop.processSelectedKey(NioEventLoop.java:688)
			at io.netty.channel.nio.NioEventLoop.processSelectedKeysOptimized(NioEventLoop.java:635)
			at io.netty.channel.nio.NioEventLoop.processSelectedKeys(NioEventLoop.java:552)
			at io.netty.channel.nio.NioEventLoop.run(NioEventLoop.java:514)
			at io.netty.util.concurrent.SingleThreadEventExecutor$6.run(SingleThreadEventExecutor.java:1050)
			at io.netty.util.internal.ThreadExecutorMap$2.run(ThreadExecutorMap.java:74)
			at io.netty.util.concurrent.FastThreadLocalRunnable.run(FastThreadLocalRunnable.java:30)
			at java.lang.Thread.run(Thread.java:748)
		Suppressed: io.lettuce.core.RedisConnectionException: Unable to connect to [************:7003]: No route to host: no further information: /************:7003
			at io.lettuce.core.cluster.topology.ClusterTopologyRefresh.lambda$getConnections$0(ClusterTopologyRefresh.java:290)
			at java.util.concurrent.CompletableFuture.uniWhenComplete(CompletableFuture.java:760)
			at java.util.concurrent.CompletableFuture$UniWhenComplete.tryFire(CompletableFuture.java:736)
			at java.util.concurrent.CompletableFuture.postComplete(CompletableFuture.java:474)
			at java.util.concurrent.CompletableFuture.completeExceptionally(CompletableFuture.java:1977)
			at io.lettuce.core.AbstractRedisClient.lambda$initializeChannelAsync0$4(AbstractRedisClient.java:329)
			at io.netty.util.concurrent.DefaultPromise.notifyListener0(DefaultPromise.java:577)
			at io.netty.util.concurrent.DefaultPromise.notifyListeners0(DefaultPromise.java:570)
			at io.netty.util.concurrent.DefaultPromise.notifyListenersNow(DefaultPromise.java:549)
			at io.netty.util.concurrent.DefaultPromise.notifyListeners(DefaultPromise.java:490)
			at io.netty.util.concurrent.DefaultPromise.setValue0(DefaultPromise.java:615)
			at io.netty.util.concurrent.DefaultPromise.setFailure0(DefaultPromise.java:608)
			at io.netty.util.concurrent.DefaultPromise.tryFailure(DefaultPromise.java:117)
			at io.netty.channel.nio.AbstractNioChannel$AbstractNioUnsafe.fulfillConnectPromise(AbstractNioChannel.java:321)
			at io.netty.channel.nio.AbstractNioChannel$AbstractNioUnsafe.finishConnect(AbstractNioChannel.java:337)
			at io.netty.channel.nio.NioEventLoop.processSelectedKey(NioEventLoop.java:688)
			at io.netty.channel.nio.NioEventLoop.processSelectedKeysOptimized(NioEventLoop.java:635)
			at io.netty.channel.nio.NioEventLoop.processSelectedKeys(NioEventLoop.java:552)
			at io.netty.channel.nio.NioEventLoop.run(NioEventLoop.java:514)
			... 4 common frames omitted
		Caused by: io.netty.channel.AbstractChannel$AnnotatedNoRouteToHostException: No route to host: no further information: /************:7003
		Caused by: java.net.NoRouteToHostException: No route to host: no further information
			at sun.nio.ch.SocketChannelImpl.checkConnect(Native Method)
			at sun.nio.ch.SocketChannelImpl.finishConnect(SocketChannelImpl.java:717)
			at io.netty.channel.socket.nio.NioSocketChannel.doFinishConnect(NioSocketChannel.java:330)
			at io.netty.channel.nio.AbstractNioChannel$AbstractNioUnsafe.finishConnect(AbstractNioChannel.java:334)
			at io.netty.channel.nio.NioEventLoop.processSelectedKey(NioEventLoop.java:688)
			at io.netty.channel.nio.NioEventLoop.processSelectedKeysOptimized(NioEventLoop.java:635)
			at io.netty.channel.nio.NioEventLoop.processSelectedKeys(NioEventLoop.java:552)
			at io.netty.channel.nio.NioEventLoop.run(NioEventLoop.java:514)
			at io.netty.util.concurrent.SingleThreadEventExecutor$6.run(SingleThreadEventExecutor.java:1050)
			at io.netty.util.internal.ThreadExecutorMap$2.run(ThreadExecutorMap.java:74)
			at io.netty.util.concurrent.FastThreadLocalRunnable.run(FastThreadLocalRunnable.java:30)
			at java.lang.Thread.run(Thread.java:748)
	Caused by: io.lettuce.core.RedisConnectionException: Unable to connect to [************:7002]: No route to host: no further information: /************:7002
		at io.lettuce.core.cluster.topology.ClusterTopologyRefresh.lambda$getConnections$0(ClusterTopologyRefresh.java:290)
		at java.util.concurrent.CompletableFuture.uniWhenComplete(CompletableFuture.java:760)
		at java.util.concurrent.CompletableFuture$UniWhenComplete.tryFire(CompletableFuture.java:736)
		at java.util.concurrent.CompletableFuture.postComplete(CompletableFuture.java:474)
		at java.util.concurrent.CompletableFuture.completeExceptionally(CompletableFuture.java:1977)
		at io.lettuce.core.AbstractRedisClient.lambda$initializeChannelAsync0$4(AbstractRedisClient.java:329)
		at io.netty.util.concurrent.DefaultPromise.notifyListener0(DefaultPromise.java:577)
		at io.netty.util.concurrent.DefaultPromise.notifyListeners0(DefaultPromise.java:570)
		at io.netty.util.concurrent.DefaultPromise.notifyListenersNow(DefaultPromise.java:549)
		at io.netty.util.concurrent.DefaultPromise.notifyListeners(DefaultPromise.java:490)
		at io.netty.util.concurrent.DefaultPromise.setValue0(DefaultPromise.java:615)
		at io.netty.util.concurrent.DefaultPromise.setFailure0(DefaultPromise.java:608)
		at io.netty.util.concurrent.DefaultPromise.tryFailure(DefaultPromise.java:117)
		at io.netty.channel.nio.AbstractNioChannel$AbstractNioUnsafe.fulfillConnectPromise(AbstractNioChannel.java:321)
		at io.netty.channel.nio.AbstractNioChannel$AbstractNioUnsafe.finishConnect(AbstractNioChannel.java:337)
		at io.netty.channel.nio.NioEventLoop.processSelectedKey(NioEventLoop.java:688)
		at io.netty.channel.nio.NioEventLoop.processSelectedKeysOptimized(NioEventLoop.java:635)
		at io.netty.channel.nio.NioEventLoop.processSelectedKeys(NioEventLoop.java:552)
		at io.netty.channel.nio.NioEventLoop.run(NioEventLoop.java:514)
		... 4 common frames omitted
	Caused by: io.netty.channel.AbstractChannel$AnnotatedNoRouteToHostException: No route to host: no further information: /************:7002
	Caused by: java.net.NoRouteToHostException: No route to host: no further information
		at sun.nio.ch.SocketChannelImpl.checkConnect(Native Method)
		at sun.nio.ch.SocketChannelImpl.finishConnect(SocketChannelImpl.java:717)
		at io.netty.channel.socket.nio.NioSocketChannel.doFinishConnect(NioSocketChannel.java:330)
		at io.netty.channel.nio.AbstractNioChannel$AbstractNioUnsafe.finishConnect(AbstractNioChannel.java:334)
		at io.netty.channel.nio.NioEventLoop.processSelectedKey(NioEventLoop.java:688)
		at io.netty.channel.nio.NioEventLoop.processSelectedKeysOptimized(NioEventLoop.java:635)
		at io.netty.channel.nio.NioEventLoop.processSelectedKeys(NioEventLoop.java:552)
		at io.netty.channel.nio.NioEventLoop.run(NioEventLoop.java:514)
		at io.netty.util.concurrent.SingleThreadEventExecutor$6.run(SingleThreadEventExecutor.java:1050)
		at io.netty.util.internal.ThreadExecutorMap$2.run(ThreadExecutorMap.java:74)
		at io.netty.util.concurrent.FastThreadLocalRunnable.run(FastThreadLocalRunnable.java:30)
		at java.lang.Thread.run(Thread.java:748)
Caused by: io.lettuce.core.RedisConnectionException: Unable to establish a connection to Redis Cluster at [RedisURI [host='************', port=7001], RedisURI [host='************', port=7002], RedisURI [host='************', port=7003], RedisURI [host='************', port=7004], RedisURI [host='************', port=7005], RedisURI [host='************', port=7006]]
	at io.lettuce.core.cluster.topology.AsyncConnections.get(AsyncConnections.java:89)
	at io.lettuce.core.cluster.topology.ClusterTopologyRefresh.loadViews(ClusterTopologyRefresh.java:76)
	at io.lettuce.core.cluster.RedisClusterClient.doLoadPartitions(RedisClusterClient.java:865)
	at io.lettuce.core.cluster.RedisClusterClient.loadPartitions(RedisClusterClient.java:845)
	... 10 common frames omitted
	Suppressed: io.lettuce.core.RedisConnectionException: Unable to connect to [************:7004]: No route to host: no further information: /************:7004
		at io.lettuce.core.cluster.topology.ClusterTopologyRefresh.lambda$getConnections$0(ClusterTopologyRefresh.java:290)
		at java.util.concurrent.CompletableFuture.uniWhenComplete(CompletableFuture.java:760)
		at java.util.concurrent.CompletableFuture$UniWhenComplete.tryFire(CompletableFuture.java:736)
		at java.util.concurrent.CompletableFuture.postComplete(CompletableFuture.java:474)
		at java.util.concurrent.CompletableFuture.completeExceptionally(CompletableFuture.java:1977)
		at io.lettuce.core.AbstractRedisClient.lambda$initializeChannelAsync0$4(AbstractRedisClient.java:329)
		at io.netty.util.concurrent.DefaultPromise.notifyListener0(DefaultPromise.java:577)
		at io.netty.util.concurrent.DefaultPromise.notifyListeners0(DefaultPromise.java:570)
		at io.netty.util.concurrent.DefaultPromise.notifyListenersNow(DefaultPromise.java:549)
		at io.netty.util.concurrent.DefaultPromise.notifyListeners(DefaultPromise.java:490)
		at io.netty.util.concurrent.DefaultPromise.setValue0(DefaultPromise.java:615)
		at io.netty.util.concurrent.DefaultPromise.setFailure0(DefaultPromise.java:608)
		at io.netty.util.concurrent.DefaultPromise.tryFailure(DefaultPromise.java:117)
		at io.netty.channel.nio.AbstractNioChannel$AbstractNioUnsafe.fulfillConnectPromise(AbstractNioChannel.java:321)
		at io.netty.channel.nio.AbstractNioChannel$AbstractNioUnsafe.finishConnect(AbstractNioChannel.java:337)
		at io.netty.channel.nio.NioEventLoop.processSelectedKey(NioEventLoop.java:688)
		at io.netty.channel.nio.NioEventLoop.processSelectedKeysOptimized(NioEventLoop.java:635)
		at io.netty.channel.nio.NioEventLoop.processSelectedKeys(NioEventLoop.java:552)
		at io.netty.channel.nio.NioEventLoop.run(NioEventLoop.java:514)
		... 4 common frames omitted
	Caused by: io.netty.channel.AbstractChannel$AnnotatedNoRouteToHostException: No route to host: no further information: /************:7004
	Caused by: java.net.NoRouteToHostException: No route to host: no further information
		at sun.nio.ch.SocketChannelImpl.checkConnect(Native Method)
		at sun.nio.ch.SocketChannelImpl.finishConnect(SocketChannelImpl.java:717)
		at io.netty.channel.socket.nio.NioSocketChannel.doFinishConnect(NioSocketChannel.java:330)
		at io.netty.channel.nio.AbstractNioChannel$AbstractNioUnsafe.finishConnect(AbstractNioChannel.java:334)
		at io.netty.channel.nio.NioEventLoop.processSelectedKey(NioEventLoop.java:688)
		at io.netty.channel.nio.NioEventLoop.processSelectedKeysOptimized(NioEventLoop.java:635)
		at io.netty.channel.nio.NioEventLoop.processSelectedKeys(NioEventLoop.java:552)
		at io.netty.channel.nio.NioEventLoop.run(NioEventLoop.java:514)
		at io.netty.util.concurrent.SingleThreadEventExecutor$6.run(SingleThreadEventExecutor.java:1050)
		at io.netty.util.internal.ThreadExecutorMap$2.run(ThreadExecutorMap.java:74)
		at io.netty.util.concurrent.FastThreadLocalRunnable.run(FastThreadLocalRunnable.java:30)
		at java.lang.Thread.run(Thread.java:748)
	Suppressed: io.lettuce.core.RedisConnectionException: Unable to connect to [************:7005]: No route to host: no further information: /************:7005
		at io.lettuce.core.cluster.topology.ClusterTopologyRefresh.lambda$getConnections$0(ClusterTopologyRefresh.java:290)
		at java.util.concurrent.CompletableFuture.uniWhenComplete(CompletableFuture.java:760)
		at java.util.concurrent.CompletableFuture$UniWhenComplete.tryFire(CompletableFuture.java:736)
		at java.util.concurrent.CompletableFuture.postComplete(CompletableFuture.java:474)
		at java.util.concurrent.CompletableFuture.completeExceptionally(CompletableFuture.java:1977)
		at io.lettuce.core.AbstractRedisClient.lambda$initializeChannelAsync0$4(AbstractRedisClient.java:329)
		at io.netty.util.concurrent.DefaultPromise.notifyListener0(DefaultPromise.java:577)
		at io.netty.util.concurrent.DefaultPromise.notifyListeners0(DefaultPromise.java:570)
		at io.netty.util.concurrent.DefaultPromise.notifyListenersNow(DefaultPromise.java:549)
		at io.netty.util.concurrent.DefaultPromise.notifyListeners(DefaultPromise.java:490)
		at io.netty.util.concurrent.DefaultPromise.setValue0(DefaultPromise.java:615)
		at io.netty.util.concurrent.DefaultPromise.setFailure0(DefaultPromise.java:608)
		at io.netty.util.concurrent.DefaultPromise.tryFailure(DefaultPromise.java:117)
		at io.netty.channel.nio.AbstractNioChannel$AbstractNioUnsafe.fulfillConnectPromise(AbstractNioChannel.java:321)
		at io.netty.channel.nio.AbstractNioChannel$AbstractNioUnsafe.finishConnect(AbstractNioChannel.java:337)
		at io.netty.channel.nio.NioEventLoop.processSelectedKey(NioEventLoop.java:688)
		at io.netty.channel.nio.NioEventLoop.processSelectedKeysOptimized(NioEventLoop.java:635)
		at io.netty.channel.nio.NioEventLoop.processSelectedKeys(NioEventLoop.java:552)
		at io.netty.channel.nio.NioEventLoop.run(NioEventLoop.java:514)
		... 4 common frames omitted
	Caused by: io.netty.channel.AbstractChannel$AnnotatedNoRouteToHostException: No route to host: no further information: /************:7005
	Caused by: java.net.NoRouteToHostException: No route to host: no further information
		at sun.nio.ch.SocketChannelImpl.checkConnect(Native Method)
		at sun.nio.ch.SocketChannelImpl.finishConnect(SocketChannelImpl.java:717)
		at io.netty.channel.socket.nio.NioSocketChannel.doFinishConnect(NioSocketChannel.java:330)
		at io.netty.channel.nio.AbstractNioChannel$AbstractNioUnsafe.finishConnect(AbstractNioChannel.java:334)
		at io.netty.channel.nio.NioEventLoop.processSelectedKey(NioEventLoop.java:688)
		at io.netty.channel.nio.NioEventLoop.processSelectedKeysOptimized(NioEventLoop.java:635)
		at io.netty.channel.nio.NioEventLoop.processSelectedKeys(NioEventLoop.java:552)
		at io.netty.channel.nio.NioEventLoop.run(NioEventLoop.java:514)
		at io.netty.util.concurrent.SingleThreadEventExecutor$6.run(SingleThreadEventExecutor.java:1050)
		at io.netty.util.internal.ThreadExecutorMap$2.run(ThreadExecutorMap.java:74)
		at io.netty.util.concurrent.FastThreadLocalRunnable.run(FastThreadLocalRunnable.java:30)
		at java.lang.Thread.run(Thread.java:748)
	Suppressed: io.lettuce.core.RedisConnectionException: Unable to connect to [************:7003]: No route to host: no further information: /************:7003
		at io.lettuce.core.cluster.topology.ClusterTopologyRefresh.lambda$getConnections$0(ClusterTopologyRefresh.java:290)
		at java.util.concurrent.CompletableFuture.uniWhenComplete(CompletableFuture.java:760)
		at java.util.concurrent.CompletableFuture$UniWhenComplete.tryFire(CompletableFuture.java:736)
		at java.util.concurrent.CompletableFuture.postComplete(CompletableFuture.java:474)
		at java.util.concurrent.CompletableFuture.completeExceptionally(CompletableFuture.java:1977)
		at io.lettuce.core.AbstractRedisClient.lambda$initializeChannelAsync0$4(AbstractRedisClient.java:329)
		at io.netty.util.concurrent.DefaultPromise.notifyListener0(DefaultPromise.java:577)
		at io.netty.util.concurrent.DefaultPromise.notifyListeners0(DefaultPromise.java:570)
		at io.netty.util.concurrent.DefaultPromise.notifyListenersNow(DefaultPromise.java:549)
		at io.netty.util.concurrent.DefaultPromise.notifyListeners(DefaultPromise.java:490)
		at io.netty.util.concurrent.DefaultPromise.setValue0(DefaultPromise.java:615)
		at io.netty.util.concurrent.DefaultPromise.setFailure0(DefaultPromise.java:608)
		at io.netty.util.concurrent.DefaultPromise.tryFailure(DefaultPromise.java:117)
		at io.netty.channel.nio.AbstractNioChannel$AbstractNioUnsafe.fulfillConnectPromise(AbstractNioChannel.java:321)
		at io.netty.channel.nio.AbstractNioChannel$AbstractNioUnsafe.finishConnect(AbstractNioChannel.java:337)
		at io.netty.channel.nio.NioEventLoop.processSelectedKey(NioEventLoop.java:688)
		at io.netty.channel.nio.NioEventLoop.processSelectedKeysOptimized(NioEventLoop.java:635)
		at io.netty.channel.nio.NioEventLoop.processSelectedKeys(NioEventLoop.java:552)
		at io.netty.channel.nio.NioEventLoop.run(NioEventLoop.java:514)
		... 4 common frames omitted
	Caused by: io.netty.channel.AbstractChannel$AnnotatedNoRouteToHostException: No route to host: no further information: /************:7003
	Caused by: java.net.NoRouteToHostException: No route to host: no further information
		at sun.nio.ch.SocketChannelImpl.checkConnect(Native Method)
		at sun.nio.ch.SocketChannelImpl.finishConnect(SocketChannelImpl.java:717)
		at io.netty.channel.socket.nio.NioSocketChannel.doFinishConnect(NioSocketChannel.java:330)
		at io.netty.channel.nio.AbstractNioChannel$AbstractNioUnsafe.finishConnect(AbstractNioChannel.java:334)
		at io.netty.channel.nio.NioEventLoop.processSelectedKey(NioEventLoop.java:688)
		at io.netty.channel.nio.NioEventLoop.processSelectedKeysOptimized(NioEventLoop.java:635)
		at io.netty.channel.nio.NioEventLoop.processSelectedKeys(NioEventLoop.java:552)
		at io.netty.channel.nio.NioEventLoop.run(NioEventLoop.java:514)
		at io.netty.util.concurrent.SingleThreadEventExecutor$6.run(SingleThreadEventExecutor.java:1050)
		at io.netty.util.internal.ThreadExecutorMap$2.run(ThreadExecutorMap.java:74)
		at io.netty.util.concurrent.FastThreadLocalRunnable.run(FastThreadLocalRunnable.java:30)
		at java.lang.Thread.run(Thread.java:748)
	Suppressed: io.lettuce.core.RedisConnectionException: Unable to connect to [************:7001]: No route to host: no further information: /************:7001
		at io.lettuce.core.cluster.topology.ClusterTopologyRefresh.lambda$getConnections$0(ClusterTopologyRefresh.java:290)
		at java.util.concurrent.CompletableFuture.uniWhenComplete(CompletableFuture.java:760)
		at java.util.concurrent.CompletableFuture$UniWhenComplete.tryFire(CompletableFuture.java:736)
		at java.util.concurrent.CompletableFuture.postComplete(CompletableFuture.java:474)
		at java.util.concurrent.CompletableFuture.completeExceptionally(CompletableFuture.java:1977)
		at io.lettuce.core.AbstractRedisClient.lambda$initializeChannelAsync0$4(AbstractRedisClient.java:329)
		at io.netty.util.concurrent.DefaultPromise.notifyListener0(DefaultPromise.java:577)
		at io.netty.util.concurrent.DefaultPromise.notifyListeners0(DefaultPromise.java:570)
		at io.netty.util.concurrent.DefaultPromise.notifyListenersNow(DefaultPromise.java:549)
		at io.netty.util.concurrent.DefaultPromise.notifyListeners(DefaultPromise.java:490)
		at io.netty.util.concurrent.DefaultPromise.setValue0(DefaultPromise.java:615)
		at io.netty.util.concurrent.DefaultPromise.setFailure0(DefaultPromise.java:608)
		at io.netty.util.concurrent.DefaultPromise.tryFailure(DefaultPromise.java:117)
		at io.netty.channel.nio.AbstractNioChannel$AbstractNioUnsafe.fulfillConnectPromise(AbstractNioChannel.java:321)
		at io.netty.channel.nio.AbstractNioChannel$AbstractNioUnsafe.finishConnect(AbstractNioChannel.java:337)
		at io.netty.channel.nio.NioEventLoop.processSelectedKey(NioEventLoop.java:688)
		at io.netty.channel.nio.NioEventLoop.processSelectedKeysOptimized(NioEventLoop.java:635)
		at io.netty.channel.nio.NioEventLoop.processSelectedKeys(NioEventLoop.java:552)
		at io.netty.channel.nio.NioEventLoop.run(NioEventLoop.java:514)
		... 4 common frames omitted
	Caused by: io.netty.channel.AbstractChannel$AnnotatedNoRouteToHostException: No route to host: no further information: /************:7001
	Caused by: java.net.NoRouteToHostException: No route to host: no further information
		at sun.nio.ch.SocketChannelImpl.checkConnect(Native Method)
		at sun.nio.ch.SocketChannelImpl.finishConnect(SocketChannelImpl.java:717)
		at io.netty.channel.socket.nio.NioSocketChannel.doFinishConnect(NioSocketChannel.java:330)
		at io.netty.channel.nio.AbstractNioChannel$AbstractNioUnsafe.finishConnect(AbstractNioChannel.java:334)
		at io.netty.channel.nio.NioEventLoop.processSelectedKey(NioEventLoop.java:688)
		at io.netty.channel.nio.NioEventLoop.processSelectedKeysOptimized(NioEventLoop.java:635)
		at io.netty.channel.nio.NioEventLoop.processSelectedKeys(NioEventLoop.java:552)
		at io.netty.channel.nio.NioEventLoop.run(NioEventLoop.java:514)
		at io.netty.util.concurrent.SingleThreadEventExecutor$6.run(SingleThreadEventExecutor.java:1050)
		at io.netty.util.internal.ThreadExecutorMap$2.run(ThreadExecutorMap.java:74)
		at io.netty.util.concurrent.FastThreadLocalRunnable.run(FastThreadLocalRunnable.java:30)
		at java.lang.Thread.run(Thread.java:748)
	Suppressed: io.lettuce.core.RedisConnectionException: Unable to connect to [************:7002]: No route to host: no further information: /************:7002
		at io.lettuce.core.cluster.topology.ClusterTopologyRefresh.lambda$getConnections$0(ClusterTopologyRefresh.java:290)
		at java.util.concurrent.CompletableFuture.uniWhenComplete(CompletableFuture.java:760)
		at java.util.concurrent.CompletableFuture$UniWhenComplete.tryFire(CompletableFuture.java:736)
		at java.util.concurrent.CompletableFuture.postComplete(CompletableFuture.java:474)
		at java.util.concurrent.CompletableFuture.completeExceptionally(CompletableFuture.java:1977)
		at io.lettuce.core.AbstractRedisClient.lambda$initializeChannelAsync0$4(AbstractRedisClient.java:329)
		at io.netty.util.concurrent.DefaultPromise.notifyListener0(DefaultPromise.java:577)
		at io.netty.util.concurrent.DefaultPromise.notifyListeners0(DefaultPromise.java:570)
		at io.netty.util.concurrent.DefaultPromise.notifyListenersNow(DefaultPromise.java:549)
		at io.netty.util.concurrent.DefaultPromise.notifyListeners(DefaultPromise.java:490)
		at io.netty.util.concurrent.DefaultPromise.setValue0(DefaultPromise.java:615)
		at io.netty.util.concurrent.DefaultPromise.setFailure0(DefaultPromise.java:608)
		at io.netty.util.concurrent.DefaultPromise.tryFailure(DefaultPromise.java:117)
		at io.netty.channel.nio.AbstractNioChannel$AbstractNioUnsafe.fulfillConnectPromise(AbstractNioChannel.java:321)
		at io.netty.channel.nio.AbstractNioChannel$AbstractNioUnsafe.finishConnect(AbstractNioChannel.java:337)
		at io.netty.channel.nio.NioEventLoop.processSelectedKey(NioEventLoop.java:688)
		at io.netty.channel.nio.NioEventLoop.processSelectedKeysOptimized(NioEventLoop.java:635)
		at io.netty.channel.nio.NioEventLoop.processSelectedKeys(NioEventLoop.java:552)
		at io.netty.channel.nio.NioEventLoop.run(NioEventLoop.java:514)
		... 4 common frames omitted
	Caused by: io.netty.channel.AbstractChannel$AnnotatedNoRouteToHostException: No route to host: no further information: /************:7002
	Caused by: java.net.NoRouteToHostException: No route to host: no further information
		at sun.nio.ch.SocketChannelImpl.checkConnect(Native Method)
		at sun.nio.ch.SocketChannelImpl.finishConnect(SocketChannelImpl.java:717)
		at io.netty.channel.socket.nio.NioSocketChannel.doFinishConnect(NioSocketChannel.java:330)
		at io.netty.channel.nio.AbstractNioChannel$AbstractNioUnsafe.finishConnect(AbstractNioChannel.java:334)
		at io.netty.channel.nio.NioEventLoop.processSelectedKey(NioEventLoop.java:688)
		at io.netty.channel.nio.NioEventLoop.processSelectedKeysOptimized(NioEventLoop.java:635)
		at io.netty.channel.nio.NioEventLoop.processSelectedKeys(NioEventLoop.java:552)
		at io.netty.channel.nio.NioEventLoop.run(NioEventLoop.java:514)
		at io.netty.util.concurrent.SingleThreadEventExecutor$6.run(SingleThreadEventExecutor.java:1050)
		at io.netty.util.internal.ThreadExecutorMap$2.run(ThreadExecutorMap.java:74)
		at io.netty.util.concurrent.FastThreadLocalRunnable.run(FastThreadLocalRunnable.java:30)
		at java.lang.Thread.run(Thread.java:748)
Caused by: io.lettuce.core.RedisConnectionException: Unable to connect to [************:7006]: No route to host: no further information: /************:7006
	at io.lettuce.core.cluster.topology.ClusterTopologyRefresh.lambda$getConnections$0(ClusterTopologyRefresh.java:290)
	at java.util.concurrent.CompletableFuture.uniWhenComplete(CompletableFuture.java:760)
	at java.util.concurrent.CompletableFuture$UniWhenComplete.tryFire(CompletableFuture.java:736)
	at java.util.concurrent.CompletableFuture.postComplete(CompletableFuture.java:474)
	at java.util.concurrent.CompletableFuture.completeExceptionally(CompletableFuture.java:1977)
	at io.lettuce.core.AbstractRedisClient.lambda$initializeChannelAsync0$4(AbstractRedisClient.java:329)
	at io.netty.util.concurrent.DefaultPromise.notifyListener0(DefaultPromise.java:577)
	at io.netty.util.concurrent.DefaultPromise.notifyListeners0(DefaultPromise.java:570)
	at io.netty.util.concurrent.DefaultPromise.notifyListenersNow(DefaultPromise.java:549)
	at io.netty.util.concurrent.DefaultPromise.notifyListeners(DefaultPromise.java:490)
	at io.netty.util.concurrent.DefaultPromise.setValue0(DefaultPromise.java:615)
	at io.netty.util.concurrent.DefaultPromise.setFailure0(DefaultPromise.java:608)
	at io.netty.util.concurrent.DefaultPromise.tryFailure(DefaultPromise.java:117)
	at io.netty.channel.nio.AbstractNioChannel$AbstractNioUnsafe.fulfillConnectPromise(AbstractNioChannel.java:321)
	at io.netty.channel.nio.AbstractNioChannel$AbstractNioUnsafe.finishConnect(AbstractNioChannel.java:337)
	at io.netty.channel.nio.NioEventLoop.processSelectedKey(NioEventLoop.java:688)
	at io.netty.channel.nio.NioEventLoop.processSelectedKeysOptimized(NioEventLoop.java:635)
	at io.netty.channel.nio.NioEventLoop.processSelectedKeys(NioEventLoop.java:552)
	at io.netty.channel.nio.NioEventLoop.run(NioEventLoop.java:514)
	... 4 common frames omitted
Caused by: io.netty.channel.AbstractChannel$AnnotatedNoRouteToHostException: No route to host: no further information: /************:7006
Caused by: java.net.NoRouteToHostException: No route to host: no further information
	at sun.nio.ch.SocketChannelImpl.checkConnect(Native Method)
	at sun.nio.ch.SocketChannelImpl.finishConnect(SocketChannelImpl.java:717)
	at io.netty.channel.socket.nio.NioSocketChannel.doFinishConnect(NioSocketChannel.java:330)
	at io.netty.channel.nio.AbstractNioChannel$AbstractNioUnsafe.finishConnect(AbstractNioChannel.java:334)
	at io.netty.channel.nio.NioEventLoop.processSelectedKey(NioEventLoop.java:688)
	at io.netty.channel.nio.NioEventLoop.processSelectedKeysOptimized(NioEventLoop.java:635)
	at io.netty.channel.nio.NioEventLoop.processSelectedKeys(NioEventLoop.java:552)
	at io.netty.channel.nio.NioEventLoop.run(NioEventLoop.java:514)
	at io.netty.util.concurrent.SingleThreadEventExecutor$6.run(SingleThreadEventExecutor.java:1050)
	at io.netty.util.internal.ThreadExecutorMap$2.run(ThreadExecutorMap.java:74)
	at io.netty.util.concurrent.FastThreadLocalRunnable.run(FastThreadLocalRunnable.java:30)
	at java.lang.Thread.run(Thread.java:748)
2025-06-05 04:53:50.138 [redisson-netty-5-15] WARN  org.redisson.client.handler.CommandDecoder.completeResponse Line:445 - response has been skipped due to timeout! channel: [id: 0x0f7b3cd5, L:/***********:51887 - R:************/************:7003], command: (CLUSTER NODES), params: [], result: [ClusterNodeInfo [nodeId=4dcfd30d1191cb5e65ef69e88dc76592f8b3d14d, address=redis://************:7005, flags=[MASTER], slaveOf=null, slotRanges=[[10923-16383]]], ClusterNodeInfo [nodeId=248f8c5f1a3d07d891f1bcf924c02097c5509e0d, address=redis://************:7001, flags=[SLAVE], slaveOf=64f45f937e6f8c36d0fb7cf09195403c4cccdf1d, slotRanges=[]], ClusterNodeInfo [nodeId=02aff3b78f8c09c43cbfc2346eac68a16a942097, address=redis://************:7003, flags=[MASTER, MYSELF], slaveOf=null, slotRanges=[[5461-10922]]], ClusterNodeInfo [nodeId=85e61682488d3a355264d212607a63832da60e8c, address=redis://************:7006, flags=[SLAVE], slaveOf=02aff3b78f8c09c43cbfc2346eac68a16a942097, slotRanges=[]], ClusterNodeInfo [nodeId=64f45f937e6f8c36d0fb7cf09195403c4cccdf1d, address=redis://************:7004, flags=[MASTER], slaveOf=null, slotRanges=[[0-5460]]], ClusterNodeInfo [nodeId=5514edc6d1aaab0526b0093a424e9f352e9914ed, address=redis://************:7002, flags=[SLAVE], slaveOf=4dcfd30d1191cb5e65ef69e88dc76592f8b3d14d, slotRanges=[]]]
2025-06-05 05:14:32.990 [redisson-netty-5-9] WARN  org.redisson.client.handler.CommandDecoder.completeResponse Line:445 - response has been skipped due to timeout! channel: [id: 0x8e44eef3, L:/***********:51854 - R:************/************:7005], command: (CLUSTER NODES), params: [], result: [ClusterNodeInfo [nodeId=02aff3b78f8c09c43cbfc2346eac68a16a942097, address=redis://************:7003, flags=[MASTER], slaveOf=null, slotRanges=[[5461-10922]]], ClusterNodeInfo [nodeId=85e61682488d3a355264d212607a63832da60e8c, address=redis://************:7006, flags=[SLAVE], slaveOf=02aff3b78f8c09c43cbfc2346eac68a16a942097, slotRanges=[]], ClusterNodeInfo [nodeId=64f45f937e6f8c36d0fb7cf09195403c4cccdf1d, address=redis://************:7004, flags=[MASTER], slaveOf=null, slotRanges=[[0-5460]]], ClusterNodeInfo [nodeId=4dcfd30d1191cb5e65ef69e88dc76592f8b3d14d, address=redis://************:7005, flags=[MASTER, MYSELF], slaveOf=null, slotRanges=[[10923-16383]]], ClusterNodeInfo [nodeId=248f8c5f1a3d07d891f1bcf924c02097c5509e0d, address=redis://************:7001, flags=[SLAVE], slaveOf=64f45f937e6f8c36d0fb7cf09195403c4cccdf1d, slotRanges=[]], ClusterNodeInfo [nodeId=5514edc6d1aaab0526b0093a424e9f352e9914ed, address=redis://************:7002, flags=[SLAVE], slaveOf=4dcfd30d1191cb5e65ef69e88dc76592f8b3d14d, slotRanges=[]]]
2025-06-05 05:14:40.050 [redisson-netty-5-31] WARN  org.redisson.client.handler.CommandDecoder.completeResponse Line:445 - response has been skipped due to timeout! channel: [id: 0xdf3fca2d, L:/***********:63382 - R:************/************:7003], command: (CLUSTER NODES), params: [], result: [ClusterNodeInfo [nodeId=4dcfd30d1191cb5e65ef69e88dc76592f8b3d14d, address=redis://************:7005, flags=[MASTER], slaveOf=null, slotRanges=[[10923-16383]]], ClusterNodeInfo [nodeId=248f8c5f1a3d07d891f1bcf924c02097c5509e0d, address=redis://************:7001, flags=[SLAVE], slaveOf=64f45f937e6f8c36d0fb7cf09195403c4cccdf1d, slotRanges=[]], ClusterNodeInfo [nodeId=02aff3b78f8c09c43cbfc2346eac68a16a942097, address=redis://************:7003, flags=[MASTER, MYSELF], slaveOf=null, slotRanges=[[5461-10922]]], ClusterNodeInfo [nodeId=85e61682488d3a355264d212607a63832da60e8c, address=redis://************:7006, flags=[SLAVE], slaveOf=02aff3b78f8c09c43cbfc2346eac68a16a942097, slotRanges=[]], ClusterNodeInfo [nodeId=64f45f937e6f8c36d0fb7cf09195403c4cccdf1d, address=redis://************:7004, flags=[MASTER], slaveOf=null, slotRanges=[[0-5460]]], ClusterNodeInfo [nodeId=5514edc6d1aaab0526b0093a424e9f352e9914ed, address=redis://************:7002, flags=[SLAVE], slaveOf=4dcfd30d1191cb5e65ef69e88dc76592f8b3d14d, slotRanges=[]]]
2025-06-05 05:35:16.061 [redisson-netty-5-25] WARN  org.redisson.client.handler.CommandDecoder.completeResponse Line:445 - response has been skipped due to timeout! channel: [id: 0xccedd45e, L:/***********:63439 - R:************/************:7003], command: (CLUSTER NODES), params: [], result: [ClusterNodeInfo [nodeId=4dcfd30d1191cb5e65ef69e88dc76592f8b3d14d, address=redis://************:7005, flags=[MASTER], slaveOf=null, slotRanges=[[10923-16383]]], ClusterNodeInfo [nodeId=248f8c5f1a3d07d891f1bcf924c02097c5509e0d, address=redis://************:7001, flags=[SLAVE], slaveOf=64f45f937e6f8c36d0fb7cf09195403c4cccdf1d, slotRanges=[]], ClusterNodeInfo [nodeId=02aff3b78f8c09c43cbfc2346eac68a16a942097, address=redis://************:7003, flags=[MASTER, MYSELF], slaveOf=null, slotRanges=[[5461-10922]]], ClusterNodeInfo [nodeId=85e61682488d3a355264d212607a63832da60e8c, address=redis://************:7006, flags=[SLAVE], slaveOf=02aff3b78f8c09c43cbfc2346eac68a16a942097, slotRanges=[]], ClusterNodeInfo [nodeId=64f45f937e6f8c36d0fb7cf09195403c4cccdf1d, address=redis://************:7004, flags=[MASTER], slaveOf=null, slotRanges=[[0-5460]]], ClusterNodeInfo [nodeId=5514edc6d1aaab0526b0093a424e9f352e9914ed, address=redis://************:7002, flags=[SLAVE], slaveOf=4dcfd30d1191cb5e65ef69e88dc76592f8b3d14d, slotRanges=[]]]
2025-06-05 05:55:59.990 [redisson-netty-5-24] WARN  org.redisson.client.handler.CommandDecoder.completeResponse Line:445 - response has been skipped due to timeout! channel: [id: 0x5e52a09c, L:/***********:57756 - R:************/************:7003], command: (CLUSTER NODES), params: [], result: [ClusterNodeInfo [nodeId=4dcfd30d1191cb5e65ef69e88dc76592f8b3d14d, address=redis://************:7005, flags=[MASTER], slaveOf=null, slotRanges=[[10923-16383]]], ClusterNodeInfo [nodeId=248f8c5f1a3d07d891f1bcf924c02097c5509e0d, address=redis://************:7001, flags=[SLAVE], slaveOf=64f45f937e6f8c36d0fb7cf09195403c4cccdf1d, slotRanges=[]], ClusterNodeInfo [nodeId=02aff3b78f8c09c43cbfc2346eac68a16a942097, address=redis://************:7003, flags=[MASTER, MYSELF], slaveOf=null, slotRanges=[[5461-10922]]], ClusterNodeInfo [nodeId=85e61682488d3a355264d212607a63832da60e8c, address=redis://************:7006, flags=[SLAVE], slaveOf=02aff3b78f8c09c43cbfc2346eac68a16a942097, slotRanges=[]], ClusterNodeInfo [nodeId=64f45f937e6f8c36d0fb7cf09195403c4cccdf1d, address=redis://************:7004, flags=[MASTER], slaveOf=null, slotRanges=[[0-5460]]], ClusterNodeInfo [nodeId=5514edc6d1aaab0526b0093a424e9f352e9914ed, address=redis://************:7002, flags=[SLAVE], slaveOf=4dcfd30d1191cb5e65ef69e88dc76592f8b3d14d, slotRanges=[]]]
2025-06-05 06:16:45.064 [redisson-netty-5-28] WARN  org.redisson.client.handler.CommandDecoder.completeResponse Line:445 - response has been skipped due to timeout! channel: [id: 0xe3df1106, L:/***********:52733 - R:************/************:7003], command: (CLUSTER NODES), params: [], result: [ClusterNodeInfo [nodeId=4dcfd30d1191cb5e65ef69e88dc76592f8b3d14d, address=redis://************:7005, flags=[MASTER], slaveOf=null, slotRanges=[[10923-16383]]], ClusterNodeInfo [nodeId=248f8c5f1a3d07d891f1bcf924c02097c5509e0d, address=redis://************:7001, flags=[SLAVE], slaveOf=64f45f937e6f8c36d0fb7cf09195403c4cccdf1d, slotRanges=[]], ClusterNodeInfo [nodeId=02aff3b78f8c09c43cbfc2346eac68a16a942097, address=redis://************:7003, flags=[MASTER, MYSELF], slaveOf=null, slotRanges=[[5461-10922]]], ClusterNodeInfo [nodeId=85e61682488d3a355264d212607a63832da60e8c, address=redis://************:7006, flags=[SLAVE], slaveOf=02aff3b78f8c09c43cbfc2346eac68a16a942097, slotRanges=[]], ClusterNodeInfo [nodeId=64f45f937e6f8c36d0fb7cf09195403c4cccdf1d, address=redis://************:7004, flags=[MASTER], slaveOf=null, slotRanges=[[0-5460]]], ClusterNodeInfo [nodeId=5514edc6d1aaab0526b0093a424e9f352e9914ed, address=redis://************:7002, flags=[SLAVE], slaveOf=4dcfd30d1191cb5e65ef69e88dc76592f8b3d14d, slotRanges=[]]]
2025-06-05 06:37:28.622 [redisson-netty-5-6] WARN  org.redisson.client.handler.CommandDecoder.completeResponse Line:445 - response has been skipped due to timeout! channel: [id: 0x8b64945a, L:/***********:63608 - R:************/************:7003], command: (CLUSTER NODES), params: [], result: [ClusterNodeInfo [nodeId=4dcfd30d1191cb5e65ef69e88dc76592f8b3d14d, address=redis://************:7005, flags=[MASTER], slaveOf=null, slotRanges=[[10923-16383]]], ClusterNodeInfo [nodeId=248f8c5f1a3d07d891f1bcf924c02097c5509e0d, address=redis://************:7001, flags=[SLAVE], slaveOf=64f45f937e6f8c36d0fb7cf09195403c4cccdf1d, slotRanges=[]], ClusterNodeInfo [nodeId=02aff3b78f8c09c43cbfc2346eac68a16a942097, address=redis://************:7003, flags=[MASTER, MYSELF], slaveOf=null, slotRanges=[[5461-10922]]], ClusterNodeInfo [nodeId=85e61682488d3a355264d212607a63832da60e8c, address=redis://************:7006, flags=[SLAVE], slaveOf=02aff3b78f8c09c43cbfc2346eac68a16a942097, slotRanges=[]], ClusterNodeInfo [nodeId=64f45f937e6f8c36d0fb7cf09195403c4cccdf1d, address=redis://************:7004, flags=[MASTER], slaveOf=null, slotRanges=[[0-5460]]], ClusterNodeInfo [nodeId=5514edc6d1aaab0526b0093a424e9f352e9914ed, address=redis://************:7002, flags=[SLAVE], slaveOf=4dcfd30d1191cb5e65ef69e88dc76592f8b3d14d, slotRanges=[]]]
2025-06-05 06:58:10.420 [redisson-netty-5-10] WARN  org.redisson.client.handler.CommandDecoder.completeResponse Line:445 - response has been skipped due to timeout! channel: [id: 0xd17f72b9, L:/***********:56628 - R:************/************:7003], command: (CLUSTER NODES), params: [], result: [ClusterNodeInfo [nodeId=4dcfd30d1191cb5e65ef69e88dc76592f8b3d14d, address=redis://************:7005, flags=[MASTER], slaveOf=null, slotRanges=[[10923-16383]]], ClusterNodeInfo [nodeId=248f8c5f1a3d07d891f1bcf924c02097c5509e0d, address=redis://************:7001, flags=[SLAVE], slaveOf=64f45f937e6f8c36d0fb7cf09195403c4cccdf1d, slotRanges=[]], ClusterNodeInfo [nodeId=02aff3b78f8c09c43cbfc2346eac68a16a942097, address=redis://************:7003, flags=[MASTER, MYSELF], slaveOf=null, slotRanges=[[5461-10922]]], ClusterNodeInfo [nodeId=85e61682488d3a355264d212607a63832da60e8c, address=redis://************:7006, flags=[SLAVE], slaveOf=02aff3b78f8c09c43cbfc2346eac68a16a942097, slotRanges=[]], ClusterNodeInfo [nodeId=64f45f937e6f8c36d0fb7cf09195403c4cccdf1d, address=redis://************:7004, flags=[MASTER], slaveOf=null, slotRanges=[[0-5460]]], ClusterNodeInfo [nodeId=5514edc6d1aaab0526b0093a424e9f352e9914ed, address=redis://************:7002, flags=[SLAVE], slaveOf=4dcfd30d1191cb5e65ef69e88dc76592f8b3d14d, slotRanges=[]]]
2025-06-05 07:18:55.690 [redisson-netty-5-27] WARN  org.redisson.client.handler.CommandDecoder.completeResponse Line:445 - response has been skipped due to timeout! channel: [id: 0x30a8b291, L:/***********:54564 - R:************/************:7003], command: (CLUSTER NODES), params: [], result: [ClusterNodeInfo [nodeId=4dcfd30d1191cb5e65ef69e88dc76592f8b3d14d, address=redis://************:7005, flags=[MASTER], slaveOf=null, slotRanges=[[10923-16383]]], ClusterNodeInfo [nodeId=248f8c5f1a3d07d891f1bcf924c02097c5509e0d, address=redis://************:7001, flags=[SLAVE], slaveOf=64f45f937e6f8c36d0fb7cf09195403c4cccdf1d, slotRanges=[]], ClusterNodeInfo [nodeId=02aff3b78f8c09c43cbfc2346eac68a16a942097, address=redis://************:7003, flags=[MASTER, MYSELF], slaveOf=null, slotRanges=[[5461-10922]]], ClusterNodeInfo [nodeId=85e61682488d3a355264d212607a63832da60e8c, address=redis://************:7006, flags=[SLAVE], slaveOf=02aff3b78f8c09c43cbfc2346eac68a16a942097, slotRanges=[]], ClusterNodeInfo [nodeId=64f45f937e6f8c36d0fb7cf09195403c4cccdf1d, address=redis://************:7004, flags=[MASTER], slaveOf=null, slotRanges=[[0-5460]]], ClusterNodeInfo [nodeId=5514edc6d1aaab0526b0093a424e9f352e9914ed, address=redis://************:7002, flags=[SLAVE], slaveOf=4dcfd30d1191cb5e65ef69e88dc76592f8b3d14d, slotRanges=[]]]
2025-06-05 07:39:37.513 [redisson-netty-5-7] WARN  org.redisson.client.handler.CommandDecoder.completeResponse Line:445 - response has been skipped due to timeout! channel: [id: 0xa501834b, L:/***********:57407 - R:************/************:7003], command: (CLUSTER NODES), params: [], result: [ClusterNodeInfo [nodeId=4dcfd30d1191cb5e65ef69e88dc76592f8b3d14d, address=redis://************:7005, flags=[MASTER], slaveOf=null, slotRanges=[[10923-16383]]], ClusterNodeInfo [nodeId=248f8c5f1a3d07d891f1bcf924c02097c5509e0d, address=redis://************:7001, flags=[SLAVE], slaveOf=64f45f937e6f8c36d0fb7cf09195403c4cccdf1d, slotRanges=[]], ClusterNodeInfo [nodeId=02aff3b78f8c09c43cbfc2346eac68a16a942097, address=redis://************:7003, flags=[MASTER, MYSELF], slaveOf=null, slotRanges=[[5461-10922]]], ClusterNodeInfo [nodeId=85e61682488d3a355264d212607a63832da60e8c, address=redis://************:7006, flags=[SLAVE], slaveOf=02aff3b78f8c09c43cbfc2346eac68a16a942097, slotRanges=[]], ClusterNodeInfo [nodeId=64f45f937e6f8c36d0fb7cf09195403c4cccdf1d, address=redis://************:7004, flags=[MASTER], slaveOf=null, slotRanges=[[0-5460]]], ClusterNodeInfo [nodeId=5514edc6d1aaab0526b0093a424e9f352e9914ed, address=redis://************:7002, flags=[SLAVE], slaveOf=4dcfd30d1191cb5e65ef69e88dc76592f8b3d14d, slotRanges=[]]]
2025-06-05 07:39:55.157 [redisson-netty-5-12] WARN  org.redisson.client.handler.CommandDecoder.completeResponse Line:445 - response has been skipped due to timeout! channel: [id: 0x9a0d1b8b, L:/***********:52669 - R:************/************:7004], command: (CLUSTER NODES), params: [], result: [ClusterNodeInfo [nodeId=64f45f937e6f8c36d0fb7cf09195403c4cccdf1d, address=redis://************:7004, flags=[MASTER, MYSELF], slaveOf=null, slotRanges=[[0-5460]]], ClusterNodeInfo [nodeId=02aff3b78f8c09c43cbfc2346eac68a16a942097, address=redis://************:7003, flags=[MASTER], slaveOf=null, slotRanges=[[5461-10922]]], ClusterNodeInfo [nodeId=248f8c5f1a3d07d891f1bcf924c02097c5509e0d, address=redis://************:7001, flags=[SLAVE], slaveOf=64f45f937e6f8c36d0fb7cf09195403c4cccdf1d, slotRanges=[]], ClusterNodeInfo [nodeId=85e61682488d3a355264d212607a63832da60e8c, address=redis://************:7006, flags=[SLAVE], slaveOf=02aff3b78f8c09c43cbfc2346eac68a16a942097, slotRanges=[]], ClusterNodeInfo [nodeId=4dcfd30d1191cb5e65ef69e88dc76592f8b3d14d, address=redis://************:7005, flags=[MASTER], slaveOf=null, slotRanges=[[10923-16383]]], ClusterNodeInfo [nodeId=5514edc6d1aaab0526b0093a424e9f352e9914ed, address=redis://************:7002, flags=[SLAVE], slaveOf=4dcfd30d1191cb5e65ef69e88dc76592f8b3d14d, slotRanges=[]]]
2025-06-05 07:39:56.713 [lettuce-nioEventLoop-4-4] WARN  io.lettuce.core.cluster.topology.ClusterTopologyRefresh.lambda$getConnections$0 Line:284 - Unable to connect to [************:7002]: connection timed out: /************:7002
2025-06-05 07:39:56.713 [lettuce-nioEventLoop-4-7] WARN  io.lettuce.core.cluster.topology.ClusterTopologyRefresh.lambda$getConnections$0 Line:284 - Unable to connect to [************:7005]: connection timed out: /************:7005
2025-06-05 07:39:56.713 [lettuce-nioEventLoop-4-8] WARN  io.lettuce.core.cluster.topology.ClusterTopologyRefresh.lambda$getConnections$0 Line:284 - Unable to connect to [************:7006]: connection timed out: /************:7006
2025-06-05 07:39:56.713 [lettuce-nioEventLoop-4-6] WARN  io.lettuce.core.cluster.topology.ClusterTopologyRefresh.lambda$getConnections$0 Line:284 - Unable to connect to [************:7004]: connection timed out: /************:7004
2025-06-05 07:39:56.713 [lettuce-nioEventLoop-4-3] WARN  io.lettuce.core.cluster.topology.ClusterTopologyRefresh.lambda$getConnections$0 Line:284 - Unable to connect to [************:7001]: connection timed out: /************:7001
2025-06-05 07:39:56.713 [lettuce-nioEventLoop-4-5] WARN  io.lettuce.core.cluster.topology.ClusterTopologyRefresh.lambda$getConnections$0 Line:284 - Unable to connect to [************:7003]: connection timed out: /************:7003
2025-06-05 10:05:00.036 [pool-14-thread-4] WARN  com.simbest.boot.base.repository.CustomDynamicWhere.queryForList Line:50 - 自定义查询SQL输出为：【select * from US_TODO_MODEL t  where t.send_flag = 0 and t.work_flag = 0 and t.enabled =1】
2025-06-05 11:35:00.018 [pool-14-thread-6] WARN  com.simbest.boot.base.repository.CustomDynamicWhere.queryForList Line:50 - 自定义查询SQL输出为：【select * from US_TODO_MODEL t  where t.send_flag = 0 and t.work_flag = 0 and t.enabled =1】
2025-06-05 12:50:00.050 [redisson-netty-5-11] WARN  org.redisson.client.handler.CommandDecoder.completeResponse Line:445 - response has been skipped due to timeout! channel: [id: 0x5f94e264, L:/***********:64988 - R:************/************:7003], command: (CLUSTER NODES), params: [], result: [ClusterNodeInfo [nodeId=4dcfd30d1191cb5e65ef69e88dc76592f8b3d14d, address=redis://************:7005, flags=[MASTER], slaveOf=null, slotRanges=[[10923-16383]]], ClusterNodeInfo [nodeId=248f8c5f1a3d07d891f1bcf924c02097c5509e0d, address=redis://************:7001, flags=[SLAVE], slaveOf=64f45f937e6f8c36d0fb7cf09195403c4cccdf1d, slotRanges=[]], ClusterNodeInfo [nodeId=02aff3b78f8c09c43cbfc2346eac68a16a942097, address=redis://************:7003, flags=[MASTER, MYSELF], slaveOf=null, slotRanges=[[5461-10922]]], ClusterNodeInfo [nodeId=85e61682488d3a355264d212607a63832da60e8c, address=redis://************:7006, flags=[SLAVE], slaveOf=02aff3b78f8c09c43cbfc2346eac68a16a942097, slotRanges=[]], ClusterNodeInfo [nodeId=64f45f937e6f8c36d0fb7cf09195403c4cccdf1d, address=redis://************:7004, flags=[MASTER], slaveOf=null, slotRanges=[[0-5460]]], ClusterNodeInfo [nodeId=5514edc6d1aaab0526b0093a424e9f352e9914ed, address=redis://************:7002, flags=[SLAVE], slaveOf=4dcfd30d1191cb5e65ef69e88dc76592f8b3d14d, slotRanges=[]]]
2025-06-05 12:50:37.153 [lettuce-nioEventLoop-4-3] WARN  io.lettuce.core.protocol.ConnectionWatchdog.log Line:153 - Cannot reconnect to [************:7005]: connection timed out: /************:7005
2025-06-05 13:10:37.456 [redisson-netty-5-23] WARN  org.redisson.client.handler.CommandDecoder.completeResponse Line:445 - response has been skipped due to timeout! channel: [id: 0x4b4e2604, L:/***********:60240 - R:************/************:7003], command: (CLUSTER NODES), params: [], result: [ClusterNodeInfo [nodeId=4dcfd30d1191cb5e65ef69e88dc76592f8b3d14d, address=redis://************:7005, flags=[MASTER], slaveOf=null, slotRanges=[[10923-16383]]], ClusterNodeInfo [nodeId=248f8c5f1a3d07d891f1bcf924c02097c5509e0d, address=redis://************:7001, flags=[SLAVE], slaveOf=64f45f937e6f8c36d0fb7cf09195403c4cccdf1d, slotRanges=[]], ClusterNodeInfo [nodeId=02aff3b78f8c09c43cbfc2346eac68a16a942097, address=redis://************:7003, flags=[MASTER, MYSELF], slaveOf=null, slotRanges=[[5461-10922]]], ClusterNodeInfo [nodeId=85e61682488d3a355264d212607a63832da60e8c, address=redis://************:7006, flags=[SLAVE], slaveOf=02aff3b78f8c09c43cbfc2346eac68a16a942097, slotRanges=[]], ClusterNodeInfo [nodeId=64f45f937e6f8c36d0fb7cf09195403c4cccdf1d, address=redis://************:7004, flags=[MASTER], slaveOf=null, slotRanges=[[0-5460]]], ClusterNodeInfo [nodeId=5514edc6d1aaab0526b0093a424e9f352e9914ed, address=redis://************:7002, flags=[SLAVE], slaveOf=4dcfd30d1191cb5e65ef69e88dc76592f8b3d14d, slotRanges=[]]]
2025-06-05 13:10:44.723 [redisson-netty-5-10] WARN  org.redisson.client.handler.CommandDecoder.completeResponse Line:445 - response has been skipped due to timeout! channel: [id: 0x72da55da, L:/***********:61957 - R:************/************:7003], command: (CLUSTER NODES), params: [], result: [ClusterNodeInfo [nodeId=4dcfd30d1191cb5e65ef69e88dc76592f8b3d14d, address=redis://************:7005, flags=[MASTER], slaveOf=null, slotRanges=[[10923-16383]]], ClusterNodeInfo [nodeId=248f8c5f1a3d07d891f1bcf924c02097c5509e0d, address=redis://************:7001, flags=[SLAVE], slaveOf=64f45f937e6f8c36d0fb7cf09195403c4cccdf1d, slotRanges=[]], ClusterNodeInfo [nodeId=02aff3b78f8c09c43cbfc2346eac68a16a942097, address=redis://************:7003, flags=[MASTER, MYSELF], slaveOf=null, slotRanges=[[5461-10922]]], ClusterNodeInfo [nodeId=85e61682488d3a355264d212607a63832da60e8c, address=redis://************:7006, flags=[SLAVE], slaveOf=02aff3b78f8c09c43cbfc2346eac68a16a942097, slotRanges=[]], ClusterNodeInfo [nodeId=64f45f937e6f8c36d0fb7cf09195403c4cccdf1d, address=redis://************:7004, flags=[MASTER], slaveOf=null, slotRanges=[[0-5460]]], ClusterNodeInfo [nodeId=5514edc6d1aaab0526b0093a424e9f352e9914ed, address=redis://************:7002, flags=[SLAVE], slaveOf=4dcfd30d1191cb5e65ef69e88dc76592f8b3d14d, slotRanges=[]]]
2025-06-05 13:31:20.888 [redisson-netty-5-19] WARN  org.redisson.client.handler.CommandDecoder.completeResponse Line:445 - response has been skipped due to timeout! channel: [id: 0x9e812dc5, L:/***********:60418 - R:************/************:7003], command: (CLUSTER NODES), params: [], result: [ClusterNodeInfo [nodeId=4dcfd30d1191cb5e65ef69e88dc76592f8b3d14d, address=redis://************:7005, flags=[MASTER], slaveOf=null, slotRanges=[[10923-16383]]], ClusterNodeInfo [nodeId=248f8c5f1a3d07d891f1bcf924c02097c5509e0d, address=redis://************:7001, flags=[SLAVE], slaveOf=64f45f937e6f8c36d0fb7cf09195403c4cccdf1d, slotRanges=[]], ClusterNodeInfo [nodeId=02aff3b78f8c09c43cbfc2346eac68a16a942097, address=redis://************:7003, flags=[MASTER, MYSELF], slaveOf=null, slotRanges=[[5461-10922]]], ClusterNodeInfo [nodeId=85e61682488d3a355264d212607a63832da60e8c, address=redis://************:7006, flags=[SLAVE], slaveOf=02aff3b78f8c09c43cbfc2346eac68a16a942097, slotRanges=[]], ClusterNodeInfo [nodeId=64f45f937e6f8c36d0fb7cf09195403c4cccdf1d, address=redis://************:7004, flags=[MASTER], slaveOf=null, slotRanges=[[0-5460]]], ClusterNodeInfo [nodeId=5514edc6d1aaab0526b0093a424e9f352e9914ed, address=redis://************:7002, flags=[SLAVE], slaveOf=4dcfd30d1191cb5e65ef69e88dc76592f8b3d14d, slotRanges=[]]]
2025-06-05 14:45:00.015 [pool-14-thread-6] WARN  com.simbest.boot.base.repository.CustomDynamicWhere.queryForList Line:50 - 自定义查询SQL输出为：【select * from US_TODO_MODEL t  where t.send_flag = 0 and t.work_flag = 0 and t.enabled =1】
2025-06-05 14:45:09.227 [pool-14-thread-9] WARN  com.simbest.boot.base.repository.CustomDynamicWhere.queryForList Line:50 - 自定义查询SQL输出为：【select * from US_TODO_MODEL t  where t.send_flag = 0 and t.work_flag = 1 and t.enabled =1】
2025-06-05 17:35:41.479 [http-nio-8092-exec-7] WARN  com.simbest.boot.security.auth.service.impl.AuthUserCacheServiceImpl.loadCacheUser Line:118 - 无法通过关键字【auth:user:chenhong】读取用户主键ID
2025-06-05 17:35:41.732 [http-nio-8092-exec-7] WARN  com.simbest.boot.security.auth.service.impl.AuthUserCacheServiceImpl.saveOrUpdateCacheUser Line:64 - 即将在缓存中将用户权限置为空----SET AuthPermissions EMPTY----【12704】
2025-06-05 17:35:58.052 [http-nio-8092-exec-17] WARN  com.simbest.boot.util.AppFileUtil.getFileName Line:218 - 文件URL路径【组 2.png】无法通过URL获取路径，直接通过工具类提取文件全称为【组 2.png】
2025-06-05 17:35:58.054 [http-nio-8092-exec-17] WARN  com.simbest.boot.util.AppFileUtil.getFileSuffix Line:274 - 文件URL路径【组 2.png】无法通过URL获取路径，直接通过工具类提取文件后缀为【png】
2025-06-05 17:35:58.059 [http-nio-8092-exec-17] WARN  com.simbest.boot.util.AppFileUtil.getFileBaseName Line:251 - 文件URL路径【组 2.png】无法通过URL获取路径，直接通过工具类提取文件名称为【组 2】
2025-06-05 17:35:58.059 [http-nio-8092-exec-17] WARN  com.simbest.boot.util.AppFileUtil.getFileSuffix Line:274 - 文件URL路径【组 2.png】无法通过URL获取路径，直接通过工具类提取文件后缀为【png】
2025-06-05 17:35:58.063 [http-nio-8092-exec-17] WARN  com.simbest.boot.util.AppFileUtil.getFileSuffix Line:274 - 文件URL路径【组 2.png】无法通过URL获取路径，直接通过工具类提取文件后缀为【png】
2025-06-05 17:36:03.489 [http-nio-8092-exec-3] WARN  com.simbest.boot.util.AppFileUtil.getFileName Line:218 - 文件URL路径【fd6471cac5ffe4aaffcc488007d75c44.mp4】无法通过URL获取路径，直接通过工具类提取文件全称为【fd6471cac5ffe4aaffcc488007d75c44.mp4】
2025-06-05 17:36:03.489 [http-nio-8092-exec-3] WARN  com.simbest.boot.util.AppFileUtil.getFileSuffix Line:274 - 文件URL路径【fd6471cac5ffe4aaffcc488007d75c44.mp4】无法通过URL获取路径，直接通过工具类提取文件后缀为【mp4】
2025-06-05 17:36:03.493 [http-nio-8092-exec-3] WARN  com.simbest.boot.util.AppFileUtil.getFileBaseName Line:251 - 文件URL路径【fd6471cac5ffe4aaffcc488007d75c44.mp4】无法通过URL获取路径，直接通过工具类提取文件名称为【fd6471cac5ffe4aaffcc488007d75c44】
2025-06-05 17:36:03.493 [http-nio-8092-exec-3] WARN  com.simbest.boot.util.AppFileUtil.getFileSuffix Line:274 - 文件URL路径【fd6471cac5ffe4aaffcc488007d75c44.mp4】无法通过URL获取路径，直接通过工具类提取文件后缀为【mp4】
2025-06-05 17:36:03.497 [http-nio-8092-exec-3] WARN  com.simbest.boot.util.AppFileUtil.getFileSuffix Line:274 - 文件URL路径【fd6471cac5ffe4aaffcc488007d75c44.mp4】无法通过URL获取路径，直接通过工具类提取文件后缀为【mp4】
2025-06-05 17:40:30.634 [http-nio-8092-exec-9] WARN  com.simbest.boot.util.AppFileUtil.getFileName Line:218 - 文件URL路径【组 2.png】无法通过URL获取路径，直接通过工具类提取文件全称为【组 2.png】
2025-06-05 17:40:30.635 [http-nio-8092-exec-9] WARN  com.simbest.boot.util.AppFileUtil.getFileSuffix Line:274 - 文件URL路径【组 2.png】无法通过URL获取路径，直接通过工具类提取文件后缀为【png】
2025-06-05 17:40:30.639 [http-nio-8092-exec-9] WARN  com.simbest.boot.util.AppFileUtil.getFileBaseName Line:251 - 文件URL路径【组 2.png】无法通过URL获取路径，直接通过工具类提取文件名称为【组 2】
2025-06-05 17:40:30.639 [http-nio-8092-exec-9] WARN  com.simbest.boot.util.AppFileUtil.getFileSuffix Line:274 - 文件URL路径【组 2.png】无法通过URL获取路径，直接通过工具类提取文件后缀为【png】
2025-06-05 17:40:30.642 [http-nio-8092-exec-9] WARN  com.simbest.boot.util.AppFileUtil.getFileSuffix Line:274 - 文件URL路径【组 2.png】无法通过URL获取路径，直接通过工具类提取文件后缀为【png】
2025-06-05 17:40:36.611 [http-nio-8092-exec-8] WARN  com.simbest.boot.util.AppFileUtil.getFileName Line:218 - 文件URL路径【fd6471cac5ffe4aaffcc488007d75c44.mp4】无法通过URL获取路径，直接通过工具类提取文件全称为【fd6471cac5ffe4aaffcc488007d75c44.mp4】
2025-06-05 17:40:36.611 [http-nio-8092-exec-8] WARN  com.simbest.boot.util.AppFileUtil.getFileSuffix Line:274 - 文件URL路径【fd6471cac5ffe4aaffcc488007d75c44.mp4】无法通过URL获取路径，直接通过工具类提取文件后缀为【mp4】
2025-06-05 17:40:36.613 [http-nio-8092-exec-8] WARN  com.simbest.boot.util.AppFileUtil.getFileBaseName Line:251 - 文件URL路径【fd6471cac5ffe4aaffcc488007d75c44.mp4】无法通过URL获取路径，直接通过工具类提取文件名称为【fd6471cac5ffe4aaffcc488007d75c44】
2025-06-05 17:40:36.614 [http-nio-8092-exec-8] WARN  com.simbest.boot.util.AppFileUtil.getFileSuffix Line:274 - 文件URL路径【fd6471cac5ffe4aaffcc488007d75c44.mp4】无法通过URL获取路径，直接通过工具类提取文件后缀为【mp4】
2025-06-05 17:40:36.619 [http-nio-8092-exec-8] WARN  com.simbest.boot.util.AppFileUtil.getFileSuffix Line:274 - 文件URL路径【fd6471cac5ffe4aaffcc488007d75c44.mp4】无法通过URL获取路径，直接通过工具类提取文件后缀为【mp4】
2025-06-05 17:46:49.423 [http-nio-8092-exec-17] WARN  com.simbest.boot.util.AppFileUtil.getFileName Line:218 - 文件URL路径【组 2.png】无法通过URL获取路径，直接通过工具类提取文件全称为【组 2.png】
2025-06-05 17:46:49.424 [http-nio-8092-exec-17] WARN  com.simbest.boot.util.AppFileUtil.getFileSuffix Line:274 - 文件URL路径【组 2.png】无法通过URL获取路径，直接通过工具类提取文件后缀为【png】
2025-06-05 17:46:49.427 [http-nio-8092-exec-17] WARN  com.simbest.boot.util.AppFileUtil.getFileBaseName Line:251 - 文件URL路径【组 2.png】无法通过URL获取路径，直接通过工具类提取文件名称为【组 2】
2025-06-05 17:46:49.427 [http-nio-8092-exec-17] WARN  com.simbest.boot.util.AppFileUtil.getFileSuffix Line:274 - 文件URL路径【组 2.png】无法通过URL获取路径，直接通过工具类提取文件后缀为【png】
2025-06-05 17:46:49.431 [http-nio-8092-exec-17] WARN  com.simbest.boot.util.AppFileUtil.getFileSuffix Line:274 - 文件URL路径【组 2.png】无法通过URL获取路径，直接通过工具类提取文件后缀为【png】
2025-06-05 17:46:55.270 [http-nio-8092-exec-6] WARN  com.simbest.boot.util.AppFileUtil.getFileName Line:218 - 文件URL路径【fd6471cac5ffe4aaffcc488007d75c44.mp4】无法通过URL获取路径，直接通过工具类提取文件全称为【fd6471cac5ffe4aaffcc488007d75c44.mp4】
2025-06-05 17:46:55.270 [http-nio-8092-exec-6] WARN  com.simbest.boot.util.AppFileUtil.getFileSuffix Line:274 - 文件URL路径【fd6471cac5ffe4aaffcc488007d75c44.mp4】无法通过URL获取路径，直接通过工具类提取文件后缀为【mp4】
2025-06-05 17:46:55.284 [http-nio-8092-exec-6] WARN  com.simbest.boot.util.AppFileUtil.getFileBaseName Line:251 - 文件URL路径【fd6471cac5ffe4aaffcc488007d75c44.mp4】无法通过URL获取路径，直接通过工具类提取文件名称为【fd6471cac5ffe4aaffcc488007d75c44】
2025-06-05 17:46:55.286 [http-nio-8092-exec-6] WARN  com.simbest.boot.util.AppFileUtil.getFileSuffix Line:274 - 文件URL路径【fd6471cac5ffe4aaffcc488007d75c44.mp4】无法通过URL获取路径，直接通过工具类提取文件后缀为【mp4】
2025-06-05 17:46:55.317 [http-nio-8092-exec-6] WARN  com.simbest.boot.util.AppFileUtil.getFileSuffix Line:274 - 文件URL路径【fd6471cac5ffe4aaffcc488007d75c44.mp4】无法通过URL获取路径，直接通过工具类提取文件后缀为【mp4】
2025-06-05 17:58:10.761 [http-nio-8092-exec-13] WARN  com.simbest.boot.util.AppFileUtil.getFileName Line:218 - 文件URL路径【组 2.png】无法通过URL获取路径，直接通过工具类提取文件全称为【组 2.png】
2025-06-05 17:58:10.761 [http-nio-8092-exec-13] WARN  com.simbest.boot.util.AppFileUtil.getFileSuffix Line:274 - 文件URL路径【组 2.png】无法通过URL获取路径，直接通过工具类提取文件后缀为【png】
2025-06-05 17:58:10.765 [http-nio-8092-exec-13] WARN  com.simbest.boot.util.AppFileUtil.getFileBaseName Line:251 - 文件URL路径【组 2.png】无法通过URL获取路径，直接通过工具类提取文件名称为【组 2】
2025-06-05 17:58:10.766 [http-nio-8092-exec-13] WARN  com.simbest.boot.util.AppFileUtil.getFileSuffix Line:274 - 文件URL路径【组 2.png】无法通过URL获取路径，直接通过工具类提取文件后缀为【png】
2025-06-05 17:58:10.770 [http-nio-8092-exec-13] WARN  com.simbest.boot.util.AppFileUtil.getFileSuffix Line:274 - 文件URL路径【组 2.png】无法通过URL获取路径，直接通过工具类提取文件后缀为【png】
2025-06-05 17:58:16.275 [http-nio-8092-exec-4] WARN  com.simbest.boot.util.AppFileUtil.getFileName Line:218 - 文件URL路径【fd6471cac5ffe4aaffcc488007d75c44.mp4】无法通过URL获取路径，直接通过工具类提取文件全称为【fd6471cac5ffe4aaffcc488007d75c44.mp4】
2025-06-05 17:58:16.275 [http-nio-8092-exec-4] WARN  com.simbest.boot.util.AppFileUtil.getFileSuffix Line:274 - 文件URL路径【fd6471cac5ffe4aaffcc488007d75c44.mp4】无法通过URL获取路径，直接通过工具类提取文件后缀为【mp4】
2025-06-05 17:58:16.278 [http-nio-8092-exec-4] WARN  com.simbest.boot.util.AppFileUtil.getFileBaseName Line:251 - 文件URL路径【fd6471cac5ffe4aaffcc488007d75c44.mp4】无法通过URL获取路径，直接通过工具类提取文件名称为【fd6471cac5ffe4aaffcc488007d75c44】
2025-06-05 17:58:16.278 [http-nio-8092-exec-4] WARN  com.simbest.boot.util.AppFileUtil.getFileSuffix Line:274 - 文件URL路径【fd6471cac5ffe4aaffcc488007d75c44.mp4】无法通过URL获取路径，直接通过工具类提取文件后缀为【mp4】
2025-06-05 17:58:16.287 [http-nio-8092-exec-4] WARN  com.simbest.boot.util.AppFileUtil.getFileSuffix Line:274 - 文件URL路径【fd6471cac5ffe4aaffcc488007d75c44.mp4】无法通过URL获取路径，直接通过工具类提取文件后缀为【mp4】
2025-06-05 18:01:25.058 [http-nio-8092-exec-19] WARN  com.simbest.boot.util.AppFileUtil.getFileName Line:218 - 文件URL路径【组 2.png】无法通过URL获取路径，直接通过工具类提取文件全称为【组 2.png】
2025-06-05 18:01:25.058 [http-nio-8092-exec-19] WARN  com.simbest.boot.util.AppFileUtil.getFileSuffix Line:274 - 文件URL路径【组 2.png】无法通过URL获取路径，直接通过工具类提取文件后缀为【png】
2025-06-05 18:01:25.061 [http-nio-8092-exec-19] WARN  com.simbest.boot.util.AppFileUtil.getFileBaseName Line:251 - 文件URL路径【组 2.png】无法通过URL获取路径，直接通过工具类提取文件名称为【组 2】
2025-06-05 18:01:25.061 [http-nio-8092-exec-19] WARN  com.simbest.boot.util.AppFileUtil.getFileSuffix Line:274 - 文件URL路径【组 2.png】无法通过URL获取路径，直接通过工具类提取文件后缀为【png】
2025-06-05 18:01:25.063 [http-nio-8092-exec-19] WARN  com.simbest.boot.util.AppFileUtil.getFileSuffix Line:274 - 文件URL路径【组 2.png】无法通过URL获取路径，直接通过工具类提取文件后缀为【png】
2025-06-05 18:01:30.238 [http-nio-8092-exec-17] WARN  com.simbest.boot.util.AppFileUtil.getFileName Line:218 - 文件URL路径【fd6471cac5ffe4aaffcc488007d75c44.mp4】无法通过URL获取路径，直接通过工具类提取文件全称为【fd6471cac5ffe4aaffcc488007d75c44.mp4】
2025-06-05 18:01:30.239 [http-nio-8092-exec-17] WARN  com.simbest.boot.util.AppFileUtil.getFileSuffix Line:274 - 文件URL路径【fd6471cac5ffe4aaffcc488007d75c44.mp4】无法通过URL获取路径，直接通过工具类提取文件后缀为【mp4】
2025-06-05 18:01:30.240 [http-nio-8092-exec-17] WARN  com.simbest.boot.util.AppFileUtil.getFileBaseName Line:251 - 文件URL路径【fd6471cac5ffe4aaffcc488007d75c44.mp4】无法通过URL获取路径，直接通过工具类提取文件名称为【fd6471cac5ffe4aaffcc488007d75c44】
2025-06-05 18:01:30.241 [http-nio-8092-exec-17] WARN  com.simbest.boot.util.AppFileUtil.getFileSuffix Line:274 - 文件URL路径【fd6471cac5ffe4aaffcc488007d75c44.mp4】无法通过URL获取路径，直接通过工具类提取文件后缀为【mp4】
2025-06-05 18:01:30.245 [http-nio-8092-exec-17] WARN  com.simbest.boot.util.AppFileUtil.getFileSuffix Line:274 - 文件URL路径【fd6471cac5ffe4aaffcc488007d75c44.mp4】无法通过URL获取路径，直接通过工具类提取文件后缀为【mp4】
2025-06-05 18:50:00.008 [pool-14-thread-6] WARN  com.simbest.boot.base.repository.CustomDynamicWhere.queryForList Line:50 - 自定义查询SQL输出为：【select * from US_TODO_MODEL t  where t.send_flag = 0 and t.work_flag = 0 and t.enabled =1】
2025-06-05 18:50:00.008 [pool-14-thread-5] WARN  com.simbest.boot.base.repository.CustomDynamicWhere.queryForList Line:50 - 自定义查询SQL输出为：【select * from US_TODO_MODEL t  where t.send_flag = 0 and t.work_flag = 1 and t.enabled =1】
2025-06-05 18:55:00.013 [pool-14-thread-5] WARN  com.simbest.boot.base.repository.CustomDynamicWhere.queryForList Line:50 - 自定义查询SQL输出为：【select * from US_TODO_MODEL t  where t.send_flag = 0 and t.work_flag = 0 and t.enabled =1】
2025-06-05 18:55:00.014 [pool-14-thread-10] WARN  com.simbest.boot.base.repository.CustomDynamicWhere.queryForList Line:50 - 自定义查询SQL输出为：【select * from US_TODO_MODEL t  where t.send_flag = 0 and t.work_flag = 1 and t.enabled =1】
2025-06-05 19:00:00.016 [pool-14-thread-4] WARN  com.simbest.boot.base.repository.CustomDynamicWhere.queryForList Line:50 - 自定义查询SQL输出为：【select * from US_TODO_MODEL t  where t.send_flag = 0 and t.work_flag = 1 and t.enabled =1】
2025-06-05 19:00:00.017 [pool-14-thread-8] WARN  com.simbest.boot.cmcc.a4.task.A4TaskCreateXmlFile.execute Line:76 - >>>>>>>>>>>>>>>>>>>>>>>>>>>>>>>>>>>>>>>>>>>>>>>>>>>>>A4TaskCreateXmlFile:执行定时任务但不是Prd环境【2025-06-05T19:00:00.017】
2025-06-05 19:00:00.035 [pool-14-thread-9] WARN  com.simbest.boot.base.repository.CustomDynamicWhere.queryForList Line:50 - 自定义查询SQL输出为：【select * from US_TODO_MODEL t  where t.send_flag = 0 and t.work_flag = 0 and t.enabled =1】
2025-06-05 19:05:00.009 [pool-14-thread-4] WARN  com.simbest.boot.base.repository.CustomDynamicWhere.queryForList Line:50 - 自定义查询SQL输出为：【select * from US_TODO_MODEL t  where t.send_flag = 0 and t.work_flag = 1 and t.enabled =1】
2025-06-05 19:05:00.009 [pool-14-thread-5] WARN  com.simbest.boot.base.repository.CustomDynamicWhere.queryForList Line:50 - 自定义查询SQL输出为：【select * from US_TODO_MODEL t  where t.send_flag = 0 and t.work_flag = 0 and t.enabled =1】
2025-06-05 19:10:00.014 [pool-14-thread-3] WARN  com.simbest.boot.base.repository.CustomDynamicWhere.queryForList Line:50 - 自定义查询SQL输出为：【select * from US_TODO_MODEL t  where t.send_flag = 0 and t.work_flag = 0 and t.enabled =1】
2025-06-05 19:10:00.014 [pool-14-thread-2] WARN  com.simbest.boot.base.repository.CustomDynamicWhere.queryForList Line:50 - 自定义查询SQL输出为：【select * from US_TODO_MODEL t  where t.send_flag = 0 and t.work_flag = 1 and t.enabled =1】
2025-06-05 19:15:00.014 [pool-14-thread-6] WARN  com.simbest.boot.base.repository.CustomDynamicWhere.queryForList Line:50 - 自定义查询SQL输出为：【select * from US_TODO_MODEL t  where t.send_flag = 0 and t.work_flag = 1 and t.enabled =1】
2025-06-05 19:15:00.014 [pool-14-thread-5] WARN  com.simbest.boot.base.repository.CustomDynamicWhere.queryForList Line:50 - 自定义查询SQL输出为：【select * from US_TODO_MODEL t  where t.send_flag = 0 and t.work_flag = 0 and t.enabled =1】
2025-06-05 19:20:00.014 [pool-14-thread-3] WARN  com.simbest.boot.base.repository.CustomDynamicWhere.queryForList Line:50 - 自定义查询SQL输出为：【select * from US_TODO_MODEL t  where t.send_flag = 0 and t.work_flag = 0 and t.enabled =1】
2025-06-05 19:20:00.014 [pool-14-thread-10] WARN  com.simbest.boot.base.repository.CustomDynamicWhere.queryForList Line:50 - 自定义查询SQL输出为：【select * from US_TODO_MODEL t  where t.send_flag = 0 and t.work_flag = 1 and t.enabled =1】
2025-06-05 19:25:00.011 [pool-14-thread-10] WARN  com.simbest.boot.base.repository.CustomDynamicWhere.queryForList Line:50 - 自定义查询SQL输出为：【select * from US_TODO_MODEL t  where t.send_flag = 0 and t.work_flag = 1 and t.enabled =1】
2025-06-05 19:25:00.012 [pool-14-thread-8] WARN  com.simbest.boot.base.repository.CustomDynamicWhere.queryForList Line:50 - 自定义查询SQL输出为：【select * from US_TODO_MODEL t  where t.send_flag = 0 and t.work_flag = 0 and t.enabled =1】
2025-06-05 19:30:00.018 [pool-14-thread-4] WARN  com.simbest.boot.base.repository.CustomDynamicWhere.queryForList Line:50 - 自定义查询SQL输出为：【select * from US_TODO_MODEL t  where t.send_flag = 0 and t.work_flag = 0 and t.enabled =1】
2025-06-05 19:30:00.025 [pool-14-thread-6] WARN  com.simbest.boot.base.repository.CustomDynamicWhere.queryForList Line:50 - 自定义查询SQL输出为：【select * from US_TODO_MODEL t  where t.send_flag = 0 and t.work_flag = 1 and t.enabled =1】
2025-06-05 19:35:00.012 [pool-14-thread-1] WARN  com.simbest.boot.base.repository.CustomDynamicWhere.queryForList Line:50 - 自定义查询SQL输出为：【select * from US_TODO_MODEL t  where t.send_flag = 0 and t.work_flag = 0 and t.enabled =1】
2025-06-05 19:35:00.012 [pool-14-thread-4] WARN  com.simbest.boot.base.repository.CustomDynamicWhere.queryForList Line:50 - 自定义查询SQL输出为：【select * from US_TODO_MODEL t  where t.send_flag = 0 and t.work_flag = 1 and t.enabled =1】
2025-06-05 19:40:00.018 [pool-14-thread-7] WARN  com.simbest.boot.base.repository.CustomDynamicWhere.queryForList Line:50 - 自定义查询SQL输出为：【select * from US_TODO_MODEL t  where t.send_flag = 0 and t.work_flag = 0 and t.enabled =1】
2025-06-05 19:40:00.020 [pool-14-thread-3] WARN  com.simbest.boot.base.repository.CustomDynamicWhere.queryForList Line:50 - 自定义查询SQL输出为：【select * from US_TODO_MODEL t  where t.send_flag = 0 and t.work_flag = 1 and t.enabled =1】
2025-06-05 19:45:00.005 [pool-14-thread-3] WARN  com.simbest.boot.base.repository.CustomDynamicWhere.queryForList Line:50 - 自定义查询SQL输出为：【select * from US_TODO_MODEL t  where t.send_flag = 0 and t.work_flag = 1 and t.enabled =1】
2025-06-05 19:45:00.011 [pool-14-thread-1] WARN  com.simbest.boot.base.repository.CustomDynamicWhere.queryForList Line:50 - 自定义查询SQL输出为：【select * from US_TODO_MODEL t  where t.send_flag = 0 and t.work_flag = 0 and t.enabled =1】
2025-06-05 19:50:00.007 [pool-14-thread-5] WARN  com.simbest.boot.base.repository.CustomDynamicWhere.queryForList Line:50 - 自定义查询SQL输出为：【select * from US_TODO_MODEL t  where t.send_flag = 0 and t.work_flag = 0 and t.enabled =1】
2025-06-05 19:50:00.009 [pool-14-thread-6] WARN  com.simbest.boot.base.repository.CustomDynamicWhere.queryForList Line:50 - 自定义查询SQL输出为：【select * from US_TODO_MODEL t  where t.send_flag = 0 and t.work_flag = 1 and t.enabled =1】
2025-06-05 19:55:00.012 [pool-14-thread-1] WARN  com.simbest.boot.base.repository.CustomDynamicWhere.queryForList Line:50 - 自定义查询SQL输出为：【select * from US_TODO_MODEL t  where t.send_flag = 0 and t.work_flag = 0 and t.enabled =1】
2025-06-05 19:55:00.012 [pool-14-thread-2] WARN  com.simbest.boot.base.repository.CustomDynamicWhere.queryForList Line:50 - 自定义查询SQL输出为：【select * from US_TODO_MODEL t  where t.send_flag = 0 and t.work_flag = 1 and t.enabled =1】
2025-06-05 20:00:00.018 [pool-14-thread-3] WARN  com.simbest.boot.base.repository.CustomDynamicWhere.queryForList Line:50 - 自定义查询SQL输出为：【select * from US_TODO_MODEL t  where t.send_flag = 0 and t.work_flag = 0 and t.enabled =1】
2025-06-05 20:00:00.018 [pool-14-thread-2] WARN  com.simbest.boot.base.repository.CustomDynamicWhere.queryForList Line:50 - 自定义查询SQL输出为：【select * from US_TODO_MODEL t  where t.send_flag = 0 and t.work_flag = 1 and t.enabled =1】
2025-06-05 20:00:00.020 [pool-14-thread-6] WARN  com.simbest.boot.cmcc.a4.task.A4TaskCreateXmlFile.execute Line:76 - >>>>>>>>>>>>>>>>>>>>>>>>>>>>>>>>>>>>>>>>>>>>>>>>>>>>>A4TaskCreateXmlFile:执行定时任务但不是Prd环境【2025-06-05T20:00:00.020】
2025-06-05 20:05:00.009 [pool-14-thread-4] WARN  com.simbest.boot.base.repository.CustomDynamicWhere.queryForList Line:50 - 自定义查询SQL输出为：【select * from US_TODO_MODEL t  where t.send_flag = 0 and t.work_flag = 1 and t.enabled =1】
2025-06-05 20:05:00.011 [pool-14-thread-3] WARN  com.simbest.boot.base.repository.CustomDynamicWhere.queryForList Line:50 - 自定义查询SQL输出为：【select * from US_TODO_MODEL t  where t.send_flag = 0 and t.work_flag = 0 and t.enabled =1】
2025-06-05 20:10:00.013 [pool-14-thread-4] WARN  com.simbest.boot.base.repository.CustomDynamicWhere.queryForList Line:50 - 自定义查询SQL输出为：【select * from US_TODO_MODEL t  where t.send_flag = 0 and t.work_flag = 0 and t.enabled =1】
2025-06-05 20:10:00.013 [pool-14-thread-5] WARN  com.simbest.boot.base.repository.CustomDynamicWhere.queryForList Line:50 - 自定义查询SQL输出为：【select * from US_TODO_MODEL t  where t.send_flag = 0 and t.work_flag = 1 and t.enabled =1】
2025-06-05 20:15:00.017 [pool-14-thread-1] WARN  com.simbest.boot.base.repository.CustomDynamicWhere.queryForList Line:50 - 自定义查询SQL输出为：【select * from US_TODO_MODEL t  where t.send_flag = 0 and t.work_flag = 0 and t.enabled =1】
2025-06-05 20:15:00.017 [pool-14-thread-4] WARN  com.simbest.boot.base.repository.CustomDynamicWhere.queryForList Line:50 - 自定义查询SQL输出为：【select * from US_TODO_MODEL t  where t.send_flag = 0 and t.work_flag = 1 and t.enabled =1】
2025-06-05 20:20:00.020 [pool-14-thread-4] WARN  com.simbest.boot.base.repository.CustomDynamicWhere.queryForList Line:50 - 自定义查询SQL输出为：【select * from US_TODO_MODEL t  where t.send_flag = 0 and t.work_flag = 0 and t.enabled =1】
2025-06-05 20:20:00.020 [pool-14-thread-7] WARN  com.simbest.boot.base.repository.CustomDynamicWhere.queryForList Line:50 - 自定义查询SQL输出为：【select * from US_TODO_MODEL t  where t.send_flag = 0 and t.work_flag = 1 and t.enabled =1】
2025-06-05 20:25:00.011 [pool-14-thread-5] WARN  com.simbest.boot.base.repository.CustomDynamicWhere.queryForList Line:50 - 自定义查询SQL输出为：【select * from US_TODO_MODEL t  where t.send_flag = 0 and t.work_flag = 1 and t.enabled =1】
2025-06-05 20:25:00.011 [pool-14-thread-7] WARN  com.simbest.boot.base.repository.CustomDynamicWhere.queryForList Line:50 - 自定义查询SQL输出为：【select * from US_TODO_MODEL t  where t.send_flag = 0 and t.work_flag = 0 and t.enabled =1】
2025-06-05 20:30:00.011 [pool-14-thread-3] WARN  com.simbest.boot.base.repository.CustomDynamicWhere.queryForList Line:50 - 自定义查询SQL输出为：【select * from US_TODO_MODEL t  where t.send_flag = 0 and t.work_flag = 0 and t.enabled =1】
2025-06-05 20:30:00.021 [pool-14-thread-9] WARN  com.simbest.boot.base.repository.CustomDynamicWhere.queryForList Line:50 - 自定义查询SQL输出为：【select * from US_TODO_MODEL t  where t.send_flag = 0 and t.work_flag = 1 and t.enabled =1】
2025-06-05 20:35:00.015 [pool-14-thread-2] WARN  com.simbest.boot.base.repository.CustomDynamicWhere.queryForList Line:50 - 自定义查询SQL输出为：【select * from US_TODO_MODEL t  where t.send_flag = 0 and t.work_flag = 0 and t.enabled =1】
2025-06-05 20:35:00.015 [pool-14-thread-8] WARN  com.simbest.boot.base.repository.CustomDynamicWhere.queryForList Line:50 - 自定义查询SQL输出为：【select * from US_TODO_MODEL t  where t.send_flag = 0 and t.work_flag = 1 and t.enabled =1】
2025-06-05 20:40:00.006 [pool-14-thread-6] WARN  com.simbest.boot.base.repository.CustomDynamicWhere.queryForList Line:50 - 自定义查询SQL输出为：【select * from US_TODO_MODEL t  where t.send_flag = 0 and t.work_flag = 1 and t.enabled =1】
2025-06-05 20:40:00.007 [pool-14-thread-8] WARN  com.simbest.boot.base.repository.CustomDynamicWhere.queryForList Line:50 - 自定义查询SQL输出为：【select * from US_TODO_MODEL t  where t.send_flag = 0 and t.work_flag = 0 and t.enabled =1】
2025-06-05 20:45:00.011 [pool-14-thread-6] WARN  com.simbest.boot.base.repository.CustomDynamicWhere.queryForList Line:50 - 自定义查询SQL输出为：【select * from US_TODO_MODEL t  where t.send_flag = 0 and t.work_flag = 0 and t.enabled =1】
2025-06-05 20:45:00.015 [pool-14-thread-8] WARN  com.simbest.boot.base.repository.CustomDynamicWhere.queryForList Line:50 - 自定义查询SQL输出为：【select * from US_TODO_MODEL t  where t.send_flag = 0 and t.work_flag = 1 and t.enabled =1】
2025-06-05 20:50:00.008 [pool-14-thread-1] WARN  com.simbest.boot.base.repository.CustomDynamicWhere.queryForList Line:50 - 自定义查询SQL输出为：【select * from US_TODO_MODEL t  where t.send_flag = 0 and t.work_flag = 0 and t.enabled =1】
2025-06-05 20:50:00.011 [pool-14-thread-2] WARN  com.simbest.boot.base.repository.CustomDynamicWhere.queryForList Line:50 - 自定义查询SQL输出为：【select * from US_TODO_MODEL t  where t.send_flag = 0 and t.work_flag = 1 and t.enabled =1】
2025-06-05 20:55:00.007 [pool-14-thread-8] WARN  com.simbest.boot.base.repository.CustomDynamicWhere.queryForList Line:50 - 自定义查询SQL输出为：【select * from US_TODO_MODEL t  where t.send_flag = 0 and t.work_flag = 0 and t.enabled =1】
2025-06-05 20:55:00.008 [pool-14-thread-2] WARN  com.simbest.boot.base.repository.CustomDynamicWhere.queryForList Line:50 - 自定义查询SQL输出为：【select * from US_TODO_MODEL t  where t.send_flag = 0 and t.work_flag = 1 and t.enabled =1】
2025-06-05 21:00:00.011 [pool-14-thread-2] WARN  com.simbest.boot.base.repository.CustomDynamicWhere.queryForList Line:50 - 自定义查询SQL输出为：【select * from US_TODO_MODEL t  where t.send_flag = 0 and t.work_flag = 0 and t.enabled =1】
2025-06-05 21:00:00.011 [pool-14-thread-7] WARN  com.simbest.boot.base.repository.CustomDynamicWhere.queryForList Line:50 - 自定义查询SQL输出为：【select * from US_TODO_MODEL t  where t.send_flag = 0 and t.work_flag = 1 and t.enabled =1】
2025-06-05 21:00:00.011 [pool-14-thread-5] WARN  com.simbest.boot.cmcc.a4.task.A4TaskCreateXmlFile.execute Line:76 - >>>>>>>>>>>>>>>>>>>>>>>>>>>>>>>>>>>>>>>>>>>>>>>>>>>>>A4TaskCreateXmlFile:执行定时任务但不是Prd环境【2025-06-05T21:00:00.011】
2025-06-05 21:05:00.008 [pool-14-thread-6] WARN  com.simbest.boot.base.repository.CustomDynamicWhere.queryForList Line:50 - 自定义查询SQL输出为：【select * from US_TODO_MODEL t  where t.send_flag = 0 and t.work_flag = 0 and t.enabled =1】
2025-06-05 21:05:00.009 [pool-14-thread-8] WARN  com.simbest.boot.base.repository.CustomDynamicWhere.queryForList Line:50 - 自定义查询SQL输出为：【select * from US_TODO_MODEL t  where t.send_flag = 0 and t.work_flag = 1 and t.enabled =1】
2025-06-05 21:10:00.015 [pool-14-thread-6] WARN  com.simbest.boot.base.repository.CustomDynamicWhere.queryForList Line:50 - 自定义查询SQL输出为：【select * from US_TODO_MODEL t  where t.send_flag = 0 and t.work_flag = 1 and t.enabled =1】
2025-06-05 21:10:00.015 [pool-14-thread-4] WARN  com.simbest.boot.base.repository.CustomDynamicWhere.queryForList Line:50 - 自定义查询SQL输出为：【select * from US_TODO_MODEL t  where t.send_flag = 0 and t.work_flag = 0 and t.enabled =1】
2025-06-05 21:15:00.021 [pool-14-thread-9] WARN  com.simbest.boot.base.repository.CustomDynamicWhere.queryForList Line:50 - 自定义查询SQL输出为：【select * from US_TODO_MODEL t  where t.send_flag = 0 and t.work_flag = 0 and t.enabled =1】
2025-06-05 21:15:00.021 [pool-14-thread-5] WARN  com.simbest.boot.base.repository.CustomDynamicWhere.queryForList Line:50 - 自定义查询SQL输出为：【select * from US_TODO_MODEL t  where t.send_flag = 0 and t.work_flag = 1 and t.enabled =1】
2025-06-05 21:20:00.008 [pool-14-thread-2] WARN  com.simbest.boot.base.repository.CustomDynamicWhere.queryForList Line:50 - 自定义查询SQL输出为：【select * from US_TODO_MODEL t  where t.send_flag = 0 and t.work_flag = 0 and t.enabled =1】
2025-06-05 21:20:00.008 [pool-14-thread-4] WARN  com.simbest.boot.base.repository.CustomDynamicWhere.queryForList Line:50 - 自定义查询SQL输出为：【select * from US_TODO_MODEL t  where t.send_flag = 0 and t.work_flag = 1 and t.enabled =1】
2025-06-05 21:25:00.009 [pool-14-thread-7] WARN  com.simbest.boot.base.repository.CustomDynamicWhere.queryForList Line:50 - 自定义查询SQL输出为：【select * from US_TODO_MODEL t  where t.send_flag = 0 and t.work_flag = 1 and t.enabled =1】
2025-06-05 21:25:00.009 [pool-14-thread-2] WARN  com.simbest.boot.base.repository.CustomDynamicWhere.queryForList Line:50 - 自定义查询SQL输出为：【select * from US_TODO_MODEL t  where t.send_flag = 0 and t.work_flag = 0 and t.enabled =1】
2025-06-05 21:27:58.186 [lettuce-nioEventLoop-4-1] WARN  io.lettuce.core.cluster.topology.ClusterTopologyRefresh.lambda$getConnections$0 Line:284 - Unable to connect to [************:7002]: No route to host: no further information: /************:7002
2025-06-05 21:27:58.186 [lettuce-nioEventLoop-4-5] WARN  io.lettuce.core.cluster.topology.ClusterTopologyRefresh.lambda$getConnections$0 Line:284 - Unable to connect to [************:7006]: No route to host: no further information: /************:7006
2025-06-05 21:27:58.186 [lettuce-nioEventLoop-4-12] WARN  io.lettuce.core.cluster.topology.ClusterTopologyRefresh.lambda$getConnections$0 Line:284 - Unable to connect to [************:7001]: No route to host: no further information: /************:7001
2025-06-05 21:27:58.186 [lettuce-nioEventLoop-4-4] WARN  io.lettuce.core.cluster.topology.ClusterTopologyRefresh.lambda$getConnections$0 Line:284 - Unable to connect to [************:7005]: No route to host: no further information: /************:7005
2025-06-05 21:27:58.186 [lettuce-nioEventLoop-4-3] WARN  io.lettuce.core.cluster.topology.ClusterTopologyRefresh.lambda$getConnections$0 Line:284 - Unable to connect to [************:7004]: No route to host: no further information: /************:7004
2025-06-05 21:27:58.186 [lettuce-nioEventLoop-4-2] WARN  io.lettuce.core.cluster.topology.ClusterTopologyRefresh.lambda$getConnections$0 Line:284 - Unable to connect to [************:7003]: No route to host: no further information: /************:7003
2025-06-05 21:27:58.192 [lettuce-nioEventLoop-4-6] WARN  io.lettuce.core.cluster.topology.ClusterTopologyRefresh.lambda$getConnections$0 Line:284 - Unable to connect to [************:7006]: No route to host: no further information: /************:7006
2025-06-05 21:27:58.192 [lettuce-nioEventLoop-4-7] WARN  io.lettuce.core.cluster.topology.ClusterTopologyRefresh.lambda$getConnections$0 Line:284 - Unable to connect to [************:7004]: No route to host: no further information: /************:7004
2025-06-05 21:27:58.192 [lettuce-nioEventLoop-4-8] WARN  io.lettuce.core.cluster.topology.ClusterTopologyRefresh.lambda$getConnections$0 Line:284 - Unable to connect to [************:7005]: No route to host: no further information: /************:7005
2025-06-05 21:27:58.193 [lettuce-nioEventLoop-4-9] WARN  io.lettuce.core.cluster.topology.ClusterTopologyRefresh.lambda$getConnections$0 Line:284 - Unable to connect to [************:7002]: No route to host: no further information: /************:7002
2025-06-05 21:27:58.193 [lettuce-nioEventLoop-4-10] WARN  io.lettuce.core.cluster.topology.ClusterTopologyRefresh.lambda$getConnections$0 Line:284 - Unable to connect to [************:7003]: No route to host: no further information: /************:7003
2025-06-05 21:27:58.193 [lettuce-nioEventLoop-4-11] WARN  io.lettuce.core.cluster.topology.ClusterTopologyRefresh.lambda$getConnections$0 Line:284 - Unable to connect to [************:7001]: No route to host: no further information: /************:7001
2025-06-05 21:27:58.207 [lettuce-eventExecutorLoop-1-10] WARN  io.lettuce.core.cluster.ClusterTopologyRefreshScheduler.doRun Line:258 - Cannot refresh Redis Cluster topology
io.lettuce.core.RedisException: Cannot retrieve initial cluster partitions from initial URIs [RedisURI [host='************', port=7006], RedisURI [host='************', port=7004], RedisURI [host='************', port=7005], RedisURI [host='************', port=7002], RedisURI [host='************', port=7003], RedisURI [host='************', port=7001]]
	at io.lettuce.core.cluster.RedisClusterClient.loadPartitions(RedisClusterClient.java:848)
	at io.lettuce.core.cluster.RedisClusterClient.reloadPartitions(RedisClusterClient.java:784)
	at io.lettuce.core.cluster.ClusterTopologyRefreshScheduler$ClusterTopologyRefreshTask.doRun(ClusterTopologyRefreshScheduler.java:256)
	at io.lettuce.core.cluster.ClusterTopologyRefreshScheduler$ClusterTopologyRefreshTask.run(ClusterTopologyRefreshScheduler.java:236)
	at io.netty.util.concurrent.PromiseTask.runTask(PromiseTask.java:98)
	at io.netty.util.concurrent.PromiseTask.run(PromiseTask.java:106)
	at io.netty.util.concurrent.DefaultEventExecutor.run(DefaultEventExecutor.java:66)
	at io.netty.util.concurrent.SingleThreadEventExecutor$6.run(SingleThreadEventExecutor.java:1050)
	at io.netty.util.internal.ThreadExecutorMap$2.run(ThreadExecutorMap.java:74)
	at io.netty.util.concurrent.FastThreadLocalRunnable.run(FastThreadLocalRunnable.java:30)
	at java.lang.Thread.run(Thread.java:748)
	Suppressed: io.lettuce.core.RedisConnectionException: Unable to establish a connection to Redis Cluster at [RedisURI [host='************', port=7001], RedisURI [host='************', port=7002], RedisURI [host='************', port=7003], RedisURI [host='************', port=7004], RedisURI [host='************', port=7005], RedisURI [host='************', port=7006]]
		at io.lettuce.core.cluster.topology.AsyncConnections.get(AsyncConnections.java:89)
		at io.lettuce.core.cluster.topology.ClusterTopologyRefresh.loadViews(ClusterTopologyRefresh.java:76)
		at io.lettuce.core.cluster.RedisClusterClient.doLoadPartitions(RedisClusterClient.java:865)
		at io.lettuce.core.cluster.RedisClusterClient.loadPartitions(RedisClusterClient.java:838)
		... 10 common frames omitted
		Suppressed: io.lettuce.core.RedisConnectionException: Unable to connect to [************:7006]: No route to host: no further information: /************:7006
			at io.lettuce.core.cluster.topology.ClusterTopologyRefresh.lambda$getConnections$0(ClusterTopologyRefresh.java:290)
			at java.util.concurrent.CompletableFuture.uniWhenComplete(CompletableFuture.java:760)
			at java.util.concurrent.CompletableFuture$UniWhenComplete.tryFire(CompletableFuture.java:736)
			at java.util.concurrent.CompletableFuture.postComplete(CompletableFuture.java:474)
			at java.util.concurrent.CompletableFuture.completeExceptionally(CompletableFuture.java:1977)
			at io.lettuce.core.AbstractRedisClient.lambda$initializeChannelAsync0$4(AbstractRedisClient.java:329)
			at io.netty.util.concurrent.DefaultPromise.notifyListener0(DefaultPromise.java:577)
			at io.netty.util.concurrent.DefaultPromise.notifyListeners0(DefaultPromise.java:570)
			at io.netty.util.concurrent.DefaultPromise.notifyListenersNow(DefaultPromise.java:549)
			at io.netty.util.concurrent.DefaultPromise.notifyListeners(DefaultPromise.java:490)
			at io.netty.util.concurrent.DefaultPromise.setValue0(DefaultPromise.java:615)
			at io.netty.util.concurrent.DefaultPromise.setFailure0(DefaultPromise.java:608)
			at io.netty.util.concurrent.DefaultPromise.tryFailure(DefaultPromise.java:117)
			at io.netty.channel.nio.AbstractNioChannel$AbstractNioUnsafe.fulfillConnectPromise(AbstractNioChannel.java:321)
			at io.netty.channel.nio.AbstractNioChannel$AbstractNioUnsafe.finishConnect(AbstractNioChannel.java:337)
			at io.netty.channel.nio.NioEventLoop.processSelectedKey(NioEventLoop.java:688)
			at io.netty.channel.nio.NioEventLoop.processSelectedKeysOptimized(NioEventLoop.java:635)
			at io.netty.channel.nio.NioEventLoop.processSelectedKeys(NioEventLoop.java:552)
			at io.netty.channel.nio.NioEventLoop.run(NioEventLoop.java:514)
			... 4 common frames omitted
		Caused by: io.netty.channel.AbstractChannel$AnnotatedNoRouteToHostException: No route to host: no further information: /************:7006
		Caused by: java.net.NoRouteToHostException: No route to host: no further information
			at sun.nio.ch.SocketChannelImpl.checkConnect(Native Method)
			at sun.nio.ch.SocketChannelImpl.finishConnect(SocketChannelImpl.java:717)
			at io.netty.channel.socket.nio.NioSocketChannel.doFinishConnect(NioSocketChannel.java:330)
			at io.netty.channel.nio.AbstractNioChannel$AbstractNioUnsafe.finishConnect(AbstractNioChannel.java:334)
			at io.netty.channel.nio.NioEventLoop.processSelectedKey(NioEventLoop.java:688)
			at io.netty.channel.nio.NioEventLoop.processSelectedKeysOptimized(NioEventLoop.java:635)
			at io.netty.channel.nio.NioEventLoop.processSelectedKeys(NioEventLoop.java:552)
			at io.netty.channel.nio.NioEventLoop.run(NioEventLoop.java:514)
			at io.netty.util.concurrent.SingleThreadEventExecutor$6.run(SingleThreadEventExecutor.java:1050)
			at io.netty.util.internal.ThreadExecutorMap$2.run(ThreadExecutorMap.java:74)
			at io.netty.util.concurrent.FastThreadLocalRunnable.run(FastThreadLocalRunnable.java:30)
			at java.lang.Thread.run(Thread.java:748)
		Suppressed: io.lettuce.core.RedisConnectionException: Unable to connect to [************:7001]: No route to host: no further information: /************:7001
			at io.lettuce.core.cluster.topology.ClusterTopologyRefresh.lambda$getConnections$0(ClusterTopologyRefresh.java:290)
			at java.util.concurrent.CompletableFuture.uniWhenComplete(CompletableFuture.java:760)
			at java.util.concurrent.CompletableFuture$UniWhenComplete.tryFire(CompletableFuture.java:736)
			at java.util.concurrent.CompletableFuture.postComplete(CompletableFuture.java:474)
			at java.util.concurrent.CompletableFuture.completeExceptionally(CompletableFuture.java:1977)
			at io.lettuce.core.AbstractRedisClient.lambda$initializeChannelAsync0$4(AbstractRedisClient.java:329)
			at io.netty.util.concurrent.DefaultPromise.notifyListener0(DefaultPromise.java:577)
			at io.netty.util.concurrent.DefaultPromise.notifyListeners0(DefaultPromise.java:570)
			at io.netty.util.concurrent.DefaultPromise.notifyListenersNow(DefaultPromise.java:549)
			at io.netty.util.concurrent.DefaultPromise.notifyListeners(DefaultPromise.java:490)
			at io.netty.util.concurrent.DefaultPromise.setValue0(DefaultPromise.java:615)
			at io.netty.util.concurrent.DefaultPromise.setFailure0(DefaultPromise.java:608)
			at io.netty.util.concurrent.DefaultPromise.tryFailure(DefaultPromise.java:117)
			at io.netty.channel.nio.AbstractNioChannel$AbstractNioUnsafe.fulfillConnectPromise(AbstractNioChannel.java:321)
			at io.netty.channel.nio.AbstractNioChannel$AbstractNioUnsafe.finishConnect(AbstractNioChannel.java:337)
			at io.netty.channel.nio.NioEventLoop.processSelectedKey(NioEventLoop.java:688)
			at io.netty.channel.nio.NioEventLoop.processSelectedKeysOptimized(NioEventLoop.java:635)
			at io.netty.channel.nio.NioEventLoop.processSelectedKeys(NioEventLoop.java:552)
			at io.netty.channel.nio.NioEventLoop.run(NioEventLoop.java:514)
			... 4 common frames omitted
		Caused by: io.netty.channel.AbstractChannel$AnnotatedNoRouteToHostException: No route to host: no further information: /************:7001
		Caused by: java.net.NoRouteToHostException: No route to host: no further information
			at sun.nio.ch.SocketChannelImpl.checkConnect(Native Method)
			at sun.nio.ch.SocketChannelImpl.finishConnect(SocketChannelImpl.java:717)
			at io.netty.channel.socket.nio.NioSocketChannel.doFinishConnect(NioSocketChannel.java:330)
			at io.netty.channel.nio.AbstractNioChannel$AbstractNioUnsafe.finishConnect(AbstractNioChannel.java:334)
			at io.netty.channel.nio.NioEventLoop.processSelectedKey(NioEventLoop.java:688)
			at io.netty.channel.nio.NioEventLoop.processSelectedKeysOptimized(NioEventLoop.java:635)
			at io.netty.channel.nio.NioEventLoop.processSelectedKeys(NioEventLoop.java:552)
			at io.netty.channel.nio.NioEventLoop.run(NioEventLoop.java:514)
			at io.netty.util.concurrent.SingleThreadEventExecutor$6.run(SingleThreadEventExecutor.java:1050)
			at io.netty.util.internal.ThreadExecutorMap$2.run(ThreadExecutorMap.java:74)
			at io.netty.util.concurrent.FastThreadLocalRunnable.run(FastThreadLocalRunnable.java:30)
			at java.lang.Thread.run(Thread.java:748)
		Suppressed: io.lettuce.core.RedisConnectionException: Unable to connect to [************:7005]: No route to host: no further information: /************:7005
			at io.lettuce.core.cluster.topology.ClusterTopologyRefresh.lambda$getConnections$0(ClusterTopologyRefresh.java:290)
			at java.util.concurrent.CompletableFuture.uniWhenComplete(CompletableFuture.java:760)
			at java.util.concurrent.CompletableFuture$UniWhenComplete.tryFire(CompletableFuture.java:736)
			at java.util.concurrent.CompletableFuture.postComplete(CompletableFuture.java:474)
			at java.util.concurrent.CompletableFuture.completeExceptionally(CompletableFuture.java:1977)
			at io.lettuce.core.AbstractRedisClient.lambda$initializeChannelAsync0$4(AbstractRedisClient.java:329)
			at io.netty.util.concurrent.DefaultPromise.notifyListener0(DefaultPromise.java:577)
			at io.netty.util.concurrent.DefaultPromise.notifyListeners0(DefaultPromise.java:570)
			at io.netty.util.concurrent.DefaultPromise.notifyListenersNow(DefaultPromise.java:549)
			at io.netty.util.concurrent.DefaultPromise.notifyListeners(DefaultPromise.java:490)
			at io.netty.util.concurrent.DefaultPromise.setValue0(DefaultPromise.java:615)
			at io.netty.util.concurrent.DefaultPromise.setFailure0(DefaultPromise.java:608)
			at io.netty.util.concurrent.DefaultPromise.tryFailure(DefaultPromise.java:117)
			at io.netty.channel.nio.AbstractNioChannel$AbstractNioUnsafe.fulfillConnectPromise(AbstractNioChannel.java:321)
			at io.netty.channel.nio.AbstractNioChannel$AbstractNioUnsafe.finishConnect(AbstractNioChannel.java:337)
			at io.netty.channel.nio.NioEventLoop.processSelectedKey(NioEventLoop.java:688)
			at io.netty.channel.nio.NioEventLoop.processSelectedKeysOptimized(NioEventLoop.java:635)
			at io.netty.channel.nio.NioEventLoop.processSelectedKeys(NioEventLoop.java:552)
			at io.netty.channel.nio.NioEventLoop.run(NioEventLoop.java:514)
			... 4 common frames omitted
		Caused by: io.netty.channel.AbstractChannel$AnnotatedNoRouteToHostException: No route to host: no further information: /************:7005
		Caused by: java.net.NoRouteToHostException: No route to host: no further information
			at sun.nio.ch.SocketChannelImpl.checkConnect(Native Method)
			at sun.nio.ch.SocketChannelImpl.finishConnect(SocketChannelImpl.java:717)
			at io.netty.channel.socket.nio.NioSocketChannel.doFinishConnect(NioSocketChannel.java:330)
			at io.netty.channel.nio.AbstractNioChannel$AbstractNioUnsafe.finishConnect(AbstractNioChannel.java:334)
			at io.netty.channel.nio.NioEventLoop.processSelectedKey(NioEventLoop.java:688)
			at io.netty.channel.nio.NioEventLoop.processSelectedKeysOptimized(NioEventLoop.java:635)
			at io.netty.channel.nio.NioEventLoop.processSelectedKeys(NioEventLoop.java:552)
			at io.netty.channel.nio.NioEventLoop.run(NioEventLoop.java:514)
			at io.netty.util.concurrent.SingleThreadEventExecutor$6.run(SingleThreadEventExecutor.java:1050)
			at io.netty.util.internal.ThreadExecutorMap$2.run(ThreadExecutorMap.java:74)
			at io.netty.util.concurrent.FastThreadLocalRunnable.run(FastThreadLocalRunnable.java:30)
			at java.lang.Thread.run(Thread.java:748)
		Suppressed: io.lettuce.core.RedisConnectionException: Unable to connect to [************:7003]: No route to host: no further information: /************:7003
			at io.lettuce.core.cluster.topology.ClusterTopologyRefresh.lambda$getConnections$0(ClusterTopologyRefresh.java:290)
			at java.util.concurrent.CompletableFuture.uniWhenComplete(CompletableFuture.java:760)
			at java.util.concurrent.CompletableFuture$UniWhenComplete.tryFire(CompletableFuture.java:736)
			at java.util.concurrent.CompletableFuture.postComplete(CompletableFuture.java:474)
			at java.util.concurrent.CompletableFuture.completeExceptionally(CompletableFuture.java:1977)
			at io.lettuce.core.AbstractRedisClient.lambda$initializeChannelAsync0$4(AbstractRedisClient.java:329)
			at io.netty.util.concurrent.DefaultPromise.notifyListener0(DefaultPromise.java:577)
			at io.netty.util.concurrent.DefaultPromise.notifyListeners0(DefaultPromise.java:570)
			at io.netty.util.concurrent.DefaultPromise.notifyListenersNow(DefaultPromise.java:549)
			at io.netty.util.concurrent.DefaultPromise.notifyListeners(DefaultPromise.java:490)
			at io.netty.util.concurrent.DefaultPromise.setValue0(DefaultPromise.java:615)
			at io.netty.util.concurrent.DefaultPromise.setFailure0(DefaultPromise.java:608)
			at io.netty.util.concurrent.DefaultPromise.tryFailure(DefaultPromise.java:117)
			at io.netty.channel.nio.AbstractNioChannel$AbstractNioUnsafe.fulfillConnectPromise(AbstractNioChannel.java:321)
			at io.netty.channel.nio.AbstractNioChannel$AbstractNioUnsafe.finishConnect(AbstractNioChannel.java:337)
			at io.netty.channel.nio.NioEventLoop.processSelectedKey(NioEventLoop.java:688)
			at io.netty.channel.nio.NioEventLoop.processSelectedKeysOptimized(NioEventLoop.java:635)
			at io.netty.channel.nio.NioEventLoop.processSelectedKeys(NioEventLoop.java:552)
			at io.netty.channel.nio.NioEventLoop.run(NioEventLoop.java:514)
			... 4 common frames omitted
		Caused by: io.netty.channel.AbstractChannel$AnnotatedNoRouteToHostException: No route to host: no further information: /************:7003
		Caused by: java.net.NoRouteToHostException: No route to host: no further information
			at sun.nio.ch.SocketChannelImpl.checkConnect(Native Method)
			at sun.nio.ch.SocketChannelImpl.finishConnect(SocketChannelImpl.java:717)
			at io.netty.channel.socket.nio.NioSocketChannel.doFinishConnect(NioSocketChannel.java:330)
			at io.netty.channel.nio.AbstractNioChannel$AbstractNioUnsafe.finishConnect(AbstractNioChannel.java:334)
			at io.netty.channel.nio.NioEventLoop.processSelectedKey(NioEventLoop.java:688)
			at io.netty.channel.nio.NioEventLoop.processSelectedKeysOptimized(NioEventLoop.java:635)
			at io.netty.channel.nio.NioEventLoop.processSelectedKeys(NioEventLoop.java:552)
			at io.netty.channel.nio.NioEventLoop.run(NioEventLoop.java:514)
			at io.netty.util.concurrent.SingleThreadEventExecutor$6.run(SingleThreadEventExecutor.java:1050)
			at io.netty.util.internal.ThreadExecutorMap$2.run(ThreadExecutorMap.java:74)
			at io.netty.util.concurrent.FastThreadLocalRunnable.run(FastThreadLocalRunnable.java:30)
			at java.lang.Thread.run(Thread.java:748)
		Suppressed: io.lettuce.core.RedisConnectionException: Unable to connect to [************:7004]: No route to host: no further information: /************:7004
			at io.lettuce.core.cluster.topology.ClusterTopologyRefresh.lambda$getConnections$0(ClusterTopologyRefresh.java:290)
			at java.util.concurrent.CompletableFuture.uniWhenComplete(CompletableFuture.java:760)
			at java.util.concurrent.CompletableFuture$UniWhenComplete.tryFire(CompletableFuture.java:736)
			at java.util.concurrent.CompletableFuture.postComplete(CompletableFuture.java:474)
			at java.util.concurrent.CompletableFuture.completeExceptionally(CompletableFuture.java:1977)
			at io.lettuce.core.AbstractRedisClient.lambda$initializeChannelAsync0$4(AbstractRedisClient.java:329)
			at io.netty.util.concurrent.DefaultPromise.notifyListener0(DefaultPromise.java:577)
			at io.netty.util.concurrent.DefaultPromise.notifyListeners0(DefaultPromise.java:570)
			at io.netty.util.concurrent.DefaultPromise.notifyListenersNow(DefaultPromise.java:549)
			at io.netty.util.concurrent.DefaultPromise.notifyListeners(DefaultPromise.java:490)
			at io.netty.util.concurrent.DefaultPromise.setValue0(DefaultPromise.java:615)
			at io.netty.util.concurrent.DefaultPromise.setFailure0(DefaultPromise.java:608)
			at io.netty.util.concurrent.DefaultPromise.tryFailure(DefaultPromise.java:117)
			at io.netty.channel.nio.AbstractNioChannel$AbstractNioUnsafe.fulfillConnectPromise(AbstractNioChannel.java:321)
			at io.netty.channel.nio.AbstractNioChannel$AbstractNioUnsafe.finishConnect(AbstractNioChannel.java:337)
			at io.netty.channel.nio.NioEventLoop.processSelectedKey(NioEventLoop.java:688)
			at io.netty.channel.nio.NioEventLoop.processSelectedKeysOptimized(NioEventLoop.java:635)
			at io.netty.channel.nio.NioEventLoop.processSelectedKeys(NioEventLoop.java:552)
			at io.netty.channel.nio.NioEventLoop.run(NioEventLoop.java:514)
			... 4 common frames omitted
		Caused by: io.netty.channel.AbstractChannel$AnnotatedNoRouteToHostException: No route to host: no further information: /************:7004
		Caused by: java.net.NoRouteToHostException: No route to host: no further information
			at sun.nio.ch.SocketChannelImpl.checkConnect(Native Method)
			at sun.nio.ch.SocketChannelImpl.finishConnect(SocketChannelImpl.java:717)
			at io.netty.channel.socket.nio.NioSocketChannel.doFinishConnect(NioSocketChannel.java:330)
			at io.netty.channel.nio.AbstractNioChannel$AbstractNioUnsafe.finishConnect(AbstractNioChannel.java:334)
			at io.netty.channel.nio.NioEventLoop.processSelectedKey(NioEventLoop.java:688)
			at io.netty.channel.nio.NioEventLoop.processSelectedKeysOptimized(NioEventLoop.java:635)
			at io.netty.channel.nio.NioEventLoop.processSelectedKeys(NioEventLoop.java:552)
			at io.netty.channel.nio.NioEventLoop.run(NioEventLoop.java:514)
			at io.netty.util.concurrent.SingleThreadEventExecutor$6.run(SingleThreadEventExecutor.java:1050)
			at io.netty.util.internal.ThreadExecutorMap$2.run(ThreadExecutorMap.java:74)
			at io.netty.util.concurrent.FastThreadLocalRunnable.run(FastThreadLocalRunnable.java:30)
			at java.lang.Thread.run(Thread.java:748)
	Caused by: io.lettuce.core.RedisConnectionException: Unable to connect to [************:7002]: No route to host: no further information: /************:7002
		at io.lettuce.core.cluster.topology.ClusterTopologyRefresh.lambda$getConnections$0(ClusterTopologyRefresh.java:290)
		at java.util.concurrent.CompletableFuture.uniWhenComplete(CompletableFuture.java:760)
		at java.util.concurrent.CompletableFuture$UniWhenComplete.tryFire(CompletableFuture.java:736)
		at java.util.concurrent.CompletableFuture.postComplete(CompletableFuture.java:474)
		at java.util.concurrent.CompletableFuture.completeExceptionally(CompletableFuture.java:1977)
		at io.lettuce.core.AbstractRedisClient.lambda$initializeChannelAsync0$4(AbstractRedisClient.java:329)
		at io.netty.util.concurrent.DefaultPromise.notifyListener0(DefaultPromise.java:577)
		at io.netty.util.concurrent.DefaultPromise.notifyListeners0(DefaultPromise.java:570)
		at io.netty.util.concurrent.DefaultPromise.notifyListenersNow(DefaultPromise.java:549)
		at io.netty.util.concurrent.DefaultPromise.notifyListeners(DefaultPromise.java:490)
		at io.netty.util.concurrent.DefaultPromise.setValue0(DefaultPromise.java:615)
		at io.netty.util.concurrent.DefaultPromise.setFailure0(DefaultPromise.java:608)
		at io.netty.util.concurrent.DefaultPromise.tryFailure(DefaultPromise.java:117)
		at io.netty.channel.nio.AbstractNioChannel$AbstractNioUnsafe.fulfillConnectPromise(AbstractNioChannel.java:321)
		at io.netty.channel.nio.AbstractNioChannel$AbstractNioUnsafe.finishConnect(AbstractNioChannel.java:337)
		at io.netty.channel.nio.NioEventLoop.processSelectedKey(NioEventLoop.java:688)
		at io.netty.channel.nio.NioEventLoop.processSelectedKeysOptimized(NioEventLoop.java:635)
		at io.netty.channel.nio.NioEventLoop.processSelectedKeys(NioEventLoop.java:552)
		at io.netty.channel.nio.NioEventLoop.run(NioEventLoop.java:514)
		... 4 common frames omitted
	Caused by: io.netty.channel.AbstractChannel$AnnotatedNoRouteToHostException: No route to host: no further information: /************:7002
	Caused by: java.net.NoRouteToHostException: No route to host: no further information
		at sun.nio.ch.SocketChannelImpl.checkConnect(Native Method)
		at sun.nio.ch.SocketChannelImpl.finishConnect(SocketChannelImpl.java:717)
		at io.netty.channel.socket.nio.NioSocketChannel.doFinishConnect(NioSocketChannel.java:330)
		at io.netty.channel.nio.AbstractNioChannel$AbstractNioUnsafe.finishConnect(AbstractNioChannel.java:334)
		at io.netty.channel.nio.NioEventLoop.processSelectedKey(NioEventLoop.java:688)
		at io.netty.channel.nio.NioEventLoop.processSelectedKeysOptimized(NioEventLoop.java:635)
		at io.netty.channel.nio.NioEventLoop.processSelectedKeys(NioEventLoop.java:552)
		at io.netty.channel.nio.NioEventLoop.run(NioEventLoop.java:514)
		at io.netty.util.concurrent.SingleThreadEventExecutor$6.run(SingleThreadEventExecutor.java:1050)
		at io.netty.util.internal.ThreadExecutorMap$2.run(ThreadExecutorMap.java:74)
		at io.netty.util.concurrent.FastThreadLocalRunnable.run(FastThreadLocalRunnable.java:30)
		at java.lang.Thread.run(Thread.java:748)
Caused by: io.lettuce.core.RedisConnectionException: Unable to establish a connection to Redis Cluster at [RedisURI [host='************', port=7001], RedisURI [host='************', port=7002], RedisURI [host='************', port=7003], RedisURI [host='************', port=7004], RedisURI [host='************', port=7005], RedisURI [host='************', port=7006]]
	at io.lettuce.core.cluster.topology.AsyncConnections.get(AsyncConnections.java:89)
	at io.lettuce.core.cluster.topology.ClusterTopologyRefresh.loadViews(ClusterTopologyRefresh.java:76)
	at io.lettuce.core.cluster.RedisClusterClient.doLoadPartitions(RedisClusterClient.java:865)
	at io.lettuce.core.cluster.RedisClusterClient.loadPartitions(RedisClusterClient.java:845)
	... 10 common frames omitted
	Suppressed: io.lettuce.core.RedisConnectionException: Unable to connect to [************:7004]: No route to host: no further information: /************:7004
		at io.lettuce.core.cluster.topology.ClusterTopologyRefresh.lambda$getConnections$0(ClusterTopologyRefresh.java:290)
		at java.util.concurrent.CompletableFuture.uniWhenComplete(CompletableFuture.java:760)
		at java.util.concurrent.CompletableFuture$UniWhenComplete.tryFire(CompletableFuture.java:736)
		at java.util.concurrent.CompletableFuture.postComplete(CompletableFuture.java:474)
		at java.util.concurrent.CompletableFuture.completeExceptionally(CompletableFuture.java:1977)
		at io.lettuce.core.AbstractRedisClient.lambda$initializeChannelAsync0$4(AbstractRedisClient.java:329)
		at io.netty.util.concurrent.DefaultPromise.notifyListener0(DefaultPromise.java:577)
		at io.netty.util.concurrent.DefaultPromise.notifyListeners0(DefaultPromise.java:570)
		at io.netty.util.concurrent.DefaultPromise.notifyListenersNow(DefaultPromise.java:549)
		at io.netty.util.concurrent.DefaultPromise.notifyListeners(DefaultPromise.java:490)
		at io.netty.util.concurrent.DefaultPromise.setValue0(DefaultPromise.java:615)
		at io.netty.util.concurrent.DefaultPromise.setFailure0(DefaultPromise.java:608)
		at io.netty.util.concurrent.DefaultPromise.tryFailure(DefaultPromise.java:117)
		at io.netty.channel.nio.AbstractNioChannel$AbstractNioUnsafe.fulfillConnectPromise(AbstractNioChannel.java:321)
		at io.netty.channel.nio.AbstractNioChannel$AbstractNioUnsafe.finishConnect(AbstractNioChannel.java:337)
		at io.netty.channel.nio.NioEventLoop.processSelectedKey(NioEventLoop.java:688)
		at io.netty.channel.nio.NioEventLoop.processSelectedKeysOptimized(NioEventLoop.java:635)
		at io.netty.channel.nio.NioEventLoop.processSelectedKeys(NioEventLoop.java:552)
		at io.netty.channel.nio.NioEventLoop.run(NioEventLoop.java:514)
		... 4 common frames omitted
	Caused by: io.netty.channel.AbstractChannel$AnnotatedNoRouteToHostException: No route to host: no further information: /************:7004
	Caused by: java.net.NoRouteToHostException: No route to host: no further information
		at sun.nio.ch.SocketChannelImpl.checkConnect(Native Method)
		at sun.nio.ch.SocketChannelImpl.finishConnect(SocketChannelImpl.java:717)
		at io.netty.channel.socket.nio.NioSocketChannel.doFinishConnect(NioSocketChannel.java:330)
		at io.netty.channel.nio.AbstractNioChannel$AbstractNioUnsafe.finishConnect(AbstractNioChannel.java:334)
		at io.netty.channel.nio.NioEventLoop.processSelectedKey(NioEventLoop.java:688)
		at io.netty.channel.nio.NioEventLoop.processSelectedKeysOptimized(NioEventLoop.java:635)
		at io.netty.channel.nio.NioEventLoop.processSelectedKeys(NioEventLoop.java:552)
		at io.netty.channel.nio.NioEventLoop.run(NioEventLoop.java:514)
		at io.netty.util.concurrent.SingleThreadEventExecutor$6.run(SingleThreadEventExecutor.java:1050)
		at io.netty.util.internal.ThreadExecutorMap$2.run(ThreadExecutorMap.java:74)
		at io.netty.util.concurrent.FastThreadLocalRunnable.run(FastThreadLocalRunnable.java:30)
		at java.lang.Thread.run(Thread.java:748)
	Suppressed: io.lettuce.core.RedisConnectionException: Unable to connect to [************:7005]: No route to host: no further information: /************:7005
		at io.lettuce.core.cluster.topology.ClusterTopologyRefresh.lambda$getConnections$0(ClusterTopologyRefresh.java:290)
		at java.util.concurrent.CompletableFuture.uniWhenComplete(CompletableFuture.java:760)
		at java.util.concurrent.CompletableFuture$UniWhenComplete.tryFire(CompletableFuture.java:736)
		at java.util.concurrent.CompletableFuture.postComplete(CompletableFuture.java:474)
		at java.util.concurrent.CompletableFuture.completeExceptionally(CompletableFuture.java:1977)
		at io.lettuce.core.AbstractRedisClient.lambda$initializeChannelAsync0$4(AbstractRedisClient.java:329)
		at io.netty.util.concurrent.DefaultPromise.notifyListener0(DefaultPromise.java:577)
		at io.netty.util.concurrent.DefaultPromise.notifyListeners0(DefaultPromise.java:570)
		at io.netty.util.concurrent.DefaultPromise.notifyListenersNow(DefaultPromise.java:549)
		at io.netty.util.concurrent.DefaultPromise.notifyListeners(DefaultPromise.java:490)
		at io.netty.util.concurrent.DefaultPromise.setValue0(DefaultPromise.java:615)
		at io.netty.util.concurrent.DefaultPromise.setFailure0(DefaultPromise.java:608)
		at io.netty.util.concurrent.DefaultPromise.tryFailure(DefaultPromise.java:117)
		at io.netty.channel.nio.AbstractNioChannel$AbstractNioUnsafe.fulfillConnectPromise(AbstractNioChannel.java:321)
		at io.netty.channel.nio.AbstractNioChannel$AbstractNioUnsafe.finishConnect(AbstractNioChannel.java:337)
		at io.netty.channel.nio.NioEventLoop.processSelectedKey(NioEventLoop.java:688)
		at io.netty.channel.nio.NioEventLoop.processSelectedKeysOptimized(NioEventLoop.java:635)
		at io.netty.channel.nio.NioEventLoop.processSelectedKeys(NioEventLoop.java:552)
		at io.netty.channel.nio.NioEventLoop.run(NioEventLoop.java:514)
		... 4 common frames omitted
	Caused by: io.netty.channel.AbstractChannel$AnnotatedNoRouteToHostException: No route to host: no further information: /************:7005
	Caused by: java.net.NoRouteToHostException: No route to host: no further information
		at sun.nio.ch.SocketChannelImpl.checkConnect(Native Method)
		at sun.nio.ch.SocketChannelImpl.finishConnect(SocketChannelImpl.java:717)
		at io.netty.channel.socket.nio.NioSocketChannel.doFinishConnect(NioSocketChannel.java:330)
		at io.netty.channel.nio.AbstractNioChannel$AbstractNioUnsafe.finishConnect(AbstractNioChannel.java:334)
		at io.netty.channel.nio.NioEventLoop.processSelectedKey(NioEventLoop.java:688)
		at io.netty.channel.nio.NioEventLoop.processSelectedKeysOptimized(NioEventLoop.java:635)
		at io.netty.channel.nio.NioEventLoop.processSelectedKeys(NioEventLoop.java:552)
		at io.netty.channel.nio.NioEventLoop.run(NioEventLoop.java:514)
		at io.netty.util.concurrent.SingleThreadEventExecutor$6.run(SingleThreadEventExecutor.java:1050)
		at io.netty.util.internal.ThreadExecutorMap$2.run(ThreadExecutorMap.java:74)
		at io.netty.util.concurrent.FastThreadLocalRunnable.run(FastThreadLocalRunnable.java:30)
		at java.lang.Thread.run(Thread.java:748)
	Suppressed: io.lettuce.core.RedisConnectionException: Unable to connect to [************:7002]: No route to host: no further information: /************:7002
		at io.lettuce.core.cluster.topology.ClusterTopologyRefresh.lambda$getConnections$0(ClusterTopologyRefresh.java:290)
		at java.util.concurrent.CompletableFuture.uniWhenComplete(CompletableFuture.java:760)
		at java.util.concurrent.CompletableFuture$UniWhenComplete.tryFire(CompletableFuture.java:736)
		at java.util.concurrent.CompletableFuture.postComplete(CompletableFuture.java:474)
		at java.util.concurrent.CompletableFuture.completeExceptionally(CompletableFuture.java:1977)
		at io.lettuce.core.AbstractRedisClient.lambda$initializeChannelAsync0$4(AbstractRedisClient.java:329)
		at io.netty.util.concurrent.DefaultPromise.notifyListener0(DefaultPromise.java:577)
		at io.netty.util.concurrent.DefaultPromise.notifyListeners0(DefaultPromise.java:570)
		at io.netty.util.concurrent.DefaultPromise.notifyListenersNow(DefaultPromise.java:549)
		at io.netty.util.concurrent.DefaultPromise.notifyListeners(DefaultPromise.java:490)
		at io.netty.util.concurrent.DefaultPromise.setValue0(DefaultPromise.java:615)
		at io.netty.util.concurrent.DefaultPromise.setFailure0(DefaultPromise.java:608)
		at io.netty.util.concurrent.DefaultPromise.tryFailure(DefaultPromise.java:117)
		at io.netty.channel.nio.AbstractNioChannel$AbstractNioUnsafe.fulfillConnectPromise(AbstractNioChannel.java:321)
		at io.netty.channel.nio.AbstractNioChannel$AbstractNioUnsafe.finishConnect(AbstractNioChannel.java:337)
		at io.netty.channel.nio.NioEventLoop.processSelectedKey(NioEventLoop.java:688)
		at io.netty.channel.nio.NioEventLoop.processSelectedKeysOptimized(NioEventLoop.java:635)
		at io.netty.channel.nio.NioEventLoop.processSelectedKeys(NioEventLoop.java:552)
		at io.netty.channel.nio.NioEventLoop.run(NioEventLoop.java:514)
		... 4 common frames omitted
	Caused by: io.netty.channel.AbstractChannel$AnnotatedNoRouteToHostException: No route to host: no further information: /************:7002
	Caused by: java.net.NoRouteToHostException: No route to host: no further information
		at sun.nio.ch.SocketChannelImpl.checkConnect(Native Method)
		at sun.nio.ch.SocketChannelImpl.finishConnect(SocketChannelImpl.java:717)
		at io.netty.channel.socket.nio.NioSocketChannel.doFinishConnect(NioSocketChannel.java:330)
		at io.netty.channel.nio.AbstractNioChannel$AbstractNioUnsafe.finishConnect(AbstractNioChannel.java:334)
		at io.netty.channel.nio.NioEventLoop.processSelectedKey(NioEventLoop.java:688)
		at io.netty.channel.nio.NioEventLoop.processSelectedKeysOptimized(NioEventLoop.java:635)
		at io.netty.channel.nio.NioEventLoop.processSelectedKeys(NioEventLoop.java:552)
		at io.netty.channel.nio.NioEventLoop.run(NioEventLoop.java:514)
		at io.netty.util.concurrent.SingleThreadEventExecutor$6.run(SingleThreadEventExecutor.java:1050)
		at io.netty.util.internal.ThreadExecutorMap$2.run(ThreadExecutorMap.java:74)
		at io.netty.util.concurrent.FastThreadLocalRunnable.run(FastThreadLocalRunnable.java:30)
		at java.lang.Thread.run(Thread.java:748)
	Suppressed: io.lettuce.core.RedisConnectionException: Unable to connect to [************:7003]: No route to host: no further information: /************:7003
		at io.lettuce.core.cluster.topology.ClusterTopologyRefresh.lambda$getConnections$0(ClusterTopologyRefresh.java:290)
		at java.util.concurrent.CompletableFuture.uniWhenComplete(CompletableFuture.java:760)
		at java.util.concurrent.CompletableFuture$UniWhenComplete.tryFire(CompletableFuture.java:736)
		at java.util.concurrent.CompletableFuture.postComplete(CompletableFuture.java:474)
		at java.util.concurrent.CompletableFuture.completeExceptionally(CompletableFuture.java:1977)
		at io.lettuce.core.AbstractRedisClient.lambda$initializeChannelAsync0$4(AbstractRedisClient.java:329)
		at io.netty.util.concurrent.DefaultPromise.notifyListener0(DefaultPromise.java:577)
		at io.netty.util.concurrent.DefaultPromise.notifyListeners0(DefaultPromise.java:570)
		at io.netty.util.concurrent.DefaultPromise.notifyListenersNow(DefaultPromise.java:549)
		at io.netty.util.concurrent.DefaultPromise.notifyListeners(DefaultPromise.java:490)
		at io.netty.util.concurrent.DefaultPromise.setValue0(DefaultPromise.java:615)
		at io.netty.util.concurrent.DefaultPromise.setFailure0(DefaultPromise.java:608)
		at io.netty.util.concurrent.DefaultPromise.tryFailure(DefaultPromise.java:117)
		at io.netty.channel.nio.AbstractNioChannel$AbstractNioUnsafe.fulfillConnectPromise(AbstractNioChannel.java:321)
		at io.netty.channel.nio.AbstractNioChannel$AbstractNioUnsafe.finishConnect(AbstractNioChannel.java:337)
		at io.netty.channel.nio.NioEventLoop.processSelectedKey(NioEventLoop.java:688)
		at io.netty.channel.nio.NioEventLoop.processSelectedKeysOptimized(NioEventLoop.java:635)
		at io.netty.channel.nio.NioEventLoop.processSelectedKeys(NioEventLoop.java:552)
		at io.netty.channel.nio.NioEventLoop.run(NioEventLoop.java:514)
		... 4 common frames omitted
	Caused by: io.netty.channel.AbstractChannel$AnnotatedNoRouteToHostException: No route to host: no further information: /************:7003
	Caused by: java.net.NoRouteToHostException: No route to host: no further information
		at sun.nio.ch.SocketChannelImpl.checkConnect(Native Method)
		at sun.nio.ch.SocketChannelImpl.finishConnect(SocketChannelImpl.java:717)
		at io.netty.channel.socket.nio.NioSocketChannel.doFinishConnect(NioSocketChannel.java:330)
		at io.netty.channel.nio.AbstractNioChannel$AbstractNioUnsafe.finishConnect(AbstractNioChannel.java:334)
		at io.netty.channel.nio.NioEventLoop.processSelectedKey(NioEventLoop.java:688)
		at io.netty.channel.nio.NioEventLoop.processSelectedKeysOptimized(NioEventLoop.java:635)
		at io.netty.channel.nio.NioEventLoop.processSelectedKeys(NioEventLoop.java:552)
		at io.netty.channel.nio.NioEventLoop.run(NioEventLoop.java:514)
		at io.netty.util.concurrent.SingleThreadEventExecutor$6.run(SingleThreadEventExecutor.java:1050)
		at io.netty.util.internal.ThreadExecutorMap$2.run(ThreadExecutorMap.java:74)
		at io.netty.util.concurrent.FastThreadLocalRunnable.run(FastThreadLocalRunnable.java:30)
		at java.lang.Thread.run(Thread.java:748)
	Suppressed: io.lettuce.core.RedisConnectionException: Unable to connect to [************:7001]: No route to host: no further information: /************:7001
		at io.lettuce.core.cluster.topology.ClusterTopologyRefresh.lambda$getConnections$0(ClusterTopologyRefresh.java:290)
		at java.util.concurrent.CompletableFuture.uniWhenComplete(CompletableFuture.java:760)
		at java.util.concurrent.CompletableFuture$UniWhenComplete.tryFire(CompletableFuture.java:736)
		at java.util.concurrent.CompletableFuture.postComplete(CompletableFuture.java:474)
		at java.util.concurrent.CompletableFuture.completeExceptionally(CompletableFuture.java:1977)
		at io.lettuce.core.AbstractRedisClient.lambda$initializeChannelAsync0$4(AbstractRedisClient.java:329)
		at io.netty.util.concurrent.DefaultPromise.notifyListener0(DefaultPromise.java:577)
		at io.netty.util.concurrent.DefaultPromise.notifyListeners0(DefaultPromise.java:570)
		at io.netty.util.concurrent.DefaultPromise.notifyListenersNow(DefaultPromise.java:549)
		at io.netty.util.concurrent.DefaultPromise.notifyListeners(DefaultPromise.java:490)
		at io.netty.util.concurrent.DefaultPromise.setValue0(DefaultPromise.java:615)
		at io.netty.util.concurrent.DefaultPromise.setFailure0(DefaultPromise.java:608)
		at io.netty.util.concurrent.DefaultPromise.tryFailure(DefaultPromise.java:117)
		at io.netty.channel.nio.AbstractNioChannel$AbstractNioUnsafe.fulfillConnectPromise(AbstractNioChannel.java:321)
		at io.netty.channel.nio.AbstractNioChannel$AbstractNioUnsafe.finishConnect(AbstractNioChannel.java:337)
		at io.netty.channel.nio.NioEventLoop.processSelectedKey(NioEventLoop.java:688)
		at io.netty.channel.nio.NioEventLoop.processSelectedKeysOptimized(NioEventLoop.java:635)
		at io.netty.channel.nio.NioEventLoop.processSelectedKeys(NioEventLoop.java:552)
		at io.netty.channel.nio.NioEventLoop.run(NioEventLoop.java:514)
		... 4 common frames omitted
	Caused by: io.netty.channel.AbstractChannel$AnnotatedNoRouteToHostException: No route to host: no further information: /************:7001
	Caused by: java.net.NoRouteToHostException: No route to host: no further information
		at sun.nio.ch.SocketChannelImpl.checkConnect(Native Method)
		at sun.nio.ch.SocketChannelImpl.finishConnect(SocketChannelImpl.java:717)
		at io.netty.channel.socket.nio.NioSocketChannel.doFinishConnect(NioSocketChannel.java:330)
		at io.netty.channel.nio.AbstractNioChannel$AbstractNioUnsafe.finishConnect(AbstractNioChannel.java:334)
		at io.netty.channel.nio.NioEventLoop.processSelectedKey(NioEventLoop.java:688)
		at io.netty.channel.nio.NioEventLoop.processSelectedKeysOptimized(NioEventLoop.java:635)
		at io.netty.channel.nio.NioEventLoop.processSelectedKeys(NioEventLoop.java:552)
		at io.netty.channel.nio.NioEventLoop.run(NioEventLoop.java:514)
		at io.netty.util.concurrent.SingleThreadEventExecutor$6.run(SingleThreadEventExecutor.java:1050)
		at io.netty.util.internal.ThreadExecutorMap$2.run(ThreadExecutorMap.java:74)
		at io.netty.util.concurrent.FastThreadLocalRunnable.run(FastThreadLocalRunnable.java:30)
		at java.lang.Thread.run(Thread.java:748)
Caused by: io.lettuce.core.RedisConnectionException: Unable to connect to [************:7006]: No route to host: no further information: /************:7006
	at io.lettuce.core.cluster.topology.ClusterTopologyRefresh.lambda$getConnections$0(ClusterTopologyRefresh.java:290)
	at java.util.concurrent.CompletableFuture.uniWhenComplete(CompletableFuture.java:760)
	at java.util.concurrent.CompletableFuture$UniWhenComplete.tryFire(CompletableFuture.java:736)
	at java.util.concurrent.CompletableFuture.postComplete(CompletableFuture.java:474)
	at java.util.concurrent.CompletableFuture.completeExceptionally(CompletableFuture.java:1977)
	at io.lettuce.core.AbstractRedisClient.lambda$initializeChannelAsync0$4(AbstractRedisClient.java:329)
	at io.netty.util.concurrent.DefaultPromise.notifyListener0(DefaultPromise.java:577)
	at io.netty.util.concurrent.DefaultPromise.notifyListeners0(DefaultPromise.java:570)
	at io.netty.util.concurrent.DefaultPromise.notifyListenersNow(DefaultPromise.java:549)
	at io.netty.util.concurrent.DefaultPromise.notifyListeners(DefaultPromise.java:490)
	at io.netty.util.concurrent.DefaultPromise.setValue0(DefaultPromise.java:615)
	at io.netty.util.concurrent.DefaultPromise.setFailure0(DefaultPromise.java:608)
	at io.netty.util.concurrent.DefaultPromise.tryFailure(DefaultPromise.java:117)
	at io.netty.channel.nio.AbstractNioChannel$AbstractNioUnsafe.fulfillConnectPromise(AbstractNioChannel.java:321)
	at io.netty.channel.nio.AbstractNioChannel$AbstractNioUnsafe.finishConnect(AbstractNioChannel.java:337)
	at io.netty.channel.nio.NioEventLoop.processSelectedKey(NioEventLoop.java:688)
	at io.netty.channel.nio.NioEventLoop.processSelectedKeysOptimized(NioEventLoop.java:635)
	at io.netty.channel.nio.NioEventLoop.processSelectedKeys(NioEventLoop.java:552)
	at io.netty.channel.nio.NioEventLoop.run(NioEventLoop.java:514)
	... 4 common frames omitted
Caused by: io.netty.channel.AbstractChannel$AnnotatedNoRouteToHostException: No route to host: no further information: /************:7006
Caused by: java.net.NoRouteToHostException: No route to host: no further information
	at sun.nio.ch.SocketChannelImpl.checkConnect(Native Method)
	at sun.nio.ch.SocketChannelImpl.finishConnect(SocketChannelImpl.java:717)
	at io.netty.channel.socket.nio.NioSocketChannel.doFinishConnect(NioSocketChannel.java:330)
	at io.netty.channel.nio.AbstractNioChannel$AbstractNioUnsafe.finishConnect(AbstractNioChannel.java:334)
	at io.netty.channel.nio.NioEventLoop.processSelectedKey(NioEventLoop.java:688)
	at io.netty.channel.nio.NioEventLoop.processSelectedKeysOptimized(NioEventLoop.java:635)
	at io.netty.channel.nio.NioEventLoop.processSelectedKeys(NioEventLoop.java:552)
	at io.netty.channel.nio.NioEventLoop.run(NioEventLoop.java:514)
	at io.netty.util.concurrent.SingleThreadEventExecutor$6.run(SingleThreadEventExecutor.java:1050)
	at io.netty.util.internal.ThreadExecutorMap$2.run(ThreadExecutorMap.java:74)
	at io.netty.util.concurrent.FastThreadLocalRunnable.run(FastThreadLocalRunnable.java:30)
	at java.lang.Thread.run(Thread.java:748)
2025-06-05 21:28:00.158 [redisson-netty-5-19] WARN  org.redisson.client.handler.CommandDecoder.completeResponse Line:445 - response has been skipped due to timeout! channel: [id: 0x2ce2d3c2, L:/***********:59543 - R:************/************:7003], command: (CLUSTER NODES), params: [], result: [ClusterNodeInfo [nodeId=4dcfd30d1191cb5e65ef69e88dc76592f8b3d14d, address=redis://************:7005, flags=[MASTER], slaveOf=null, slotRanges=[[10923-16383]]], ClusterNodeInfo [nodeId=248f8c5f1a3d07d891f1bcf924c02097c5509e0d, address=redis://************:7001, flags=[SLAVE], slaveOf=64f45f937e6f8c36d0fb7cf09195403c4cccdf1d, slotRanges=[]], ClusterNodeInfo [nodeId=02aff3b78f8c09c43cbfc2346eac68a16a942097, address=redis://************:7003, flags=[MASTER, MYSELF], slaveOf=null, slotRanges=[[5461-10922]]], ClusterNodeInfo [nodeId=85e61682488d3a355264d212607a63832da60e8c, address=redis://************:7006, flags=[SLAVE], slaveOf=02aff3b78f8c09c43cbfc2346eac68a16a942097, slotRanges=[]], ClusterNodeInfo [nodeId=64f45f937e6f8c36d0fb7cf09195403c4cccdf1d, address=redis://************:7004, flags=[MASTER], slaveOf=null, slotRanges=[[0-5460]]], ClusterNodeInfo [nodeId=5514edc6d1aaab0526b0093a424e9f352e9914ed, address=redis://************:7002, flags=[SLAVE], slaveOf=4dcfd30d1191cb5e65ef69e88dc76592f8b3d14d, slotRanges=[]]]
2025-06-05 21:48:42.093 [redisson-netty-5-19] WARN  org.redisson.client.handler.CommandDecoder.completeResponse Line:445 - response has been skipped due to timeout! channel: [id: 0xa00d2c1b, L:/***********:49233 - R:************/************:7003], command: (CLUSTER NODES), params: [], result: [ClusterNodeInfo [nodeId=4dcfd30d1191cb5e65ef69e88dc76592f8b3d14d, address=redis://************:7005, flags=[MASTER], slaveOf=null, slotRanges=[[10923-16383]]], ClusterNodeInfo [nodeId=248f8c5f1a3d07d891f1bcf924c02097c5509e0d, address=redis://************:7001, flags=[SLAVE], slaveOf=64f45f937e6f8c36d0fb7cf09195403c4cccdf1d, slotRanges=[]], ClusterNodeInfo [nodeId=02aff3b78f8c09c43cbfc2346eac68a16a942097, address=redis://************:7003, flags=[MASTER, MYSELF], slaveOf=null, slotRanges=[[5461-10922]]], ClusterNodeInfo [nodeId=85e61682488d3a355264d212607a63832da60e8c, address=redis://************:7006, flags=[SLAVE], slaveOf=02aff3b78f8c09c43cbfc2346eac68a16a942097, slotRanges=[]], ClusterNodeInfo [nodeId=64f45f937e6f8c36d0fb7cf09195403c4cccdf1d, address=redis://************:7004, flags=[MASTER], slaveOf=null, slotRanges=[[0-5460]]], ClusterNodeInfo [nodeId=5514edc6d1aaab0526b0093a424e9f352e9914ed, address=redis://************:7002, flags=[SLAVE], slaveOf=4dcfd30d1191cb5e65ef69e88dc76592f8b3d14d, slotRanges=[]]]
2025-06-05 22:09:27.622 [redisson-netty-5-23] WARN  org.redisson.client.handler.CommandDecoder.completeResponse Line:445 - response has been skipped due to timeout! channel: [id: 0x9cffa8ea, L:/***********:62798 - R:************/************:7003], command: (CLUSTER NODES), params: [], result: [ClusterNodeInfo [nodeId=4dcfd30d1191cb5e65ef69e88dc76592f8b3d14d, address=redis://************:7005, flags=[MASTER], slaveOf=null, slotRanges=[[10923-16383]]], ClusterNodeInfo [nodeId=248f8c5f1a3d07d891f1bcf924c02097c5509e0d, address=redis://************:7001, flags=[SLAVE], slaveOf=64f45f937e6f8c36d0fb7cf09195403c4cccdf1d, slotRanges=[]], ClusterNodeInfo [nodeId=02aff3b78f8c09c43cbfc2346eac68a16a942097, address=redis://************:7003, flags=[MASTER, MYSELF], slaveOf=null, slotRanges=[[5461-10922]]], ClusterNodeInfo [nodeId=85e61682488d3a355264d212607a63832da60e8c, address=redis://************:7006, flags=[SLAVE], slaveOf=02aff3b78f8c09c43cbfc2346eac68a16a942097, slotRanges=[]], ClusterNodeInfo [nodeId=64f45f937e6f8c36d0fb7cf09195403c4cccdf1d, address=redis://************:7004, flags=[MASTER], slaveOf=null, slotRanges=[[0-5460]]], ClusterNodeInfo [nodeId=5514edc6d1aaab0526b0093a424e9f352e9914ed, address=redis://************:7002, flags=[SLAVE], slaveOf=4dcfd30d1191cb5e65ef69e88dc76592f8b3d14d, slotRanges=[]]]
2025-06-05 22:30:21.763 [redisson-netty-5-12] WARN  org.redisson.client.handler.CommandDecoder.completeResponse Line:445 - response has been skipped due to timeout! channel: [id: 0x503a4285, L:/***********:61593 - R:************/************:7003], command: (CLUSTER NODES), params: [], result: [ClusterNodeInfo [nodeId=4dcfd30d1191cb5e65ef69e88dc76592f8b3d14d, address=redis://************:7005, flags=[MASTER], slaveOf=null, slotRanges=[[10923-16383]]], ClusterNodeInfo [nodeId=248f8c5f1a3d07d891f1bcf924c02097c5509e0d, address=redis://************:7001, flags=[SLAVE], slaveOf=64f45f937e6f8c36d0fb7cf09195403c4cccdf1d, slotRanges=[]], ClusterNodeInfo [nodeId=02aff3b78f8c09c43cbfc2346eac68a16a942097, address=redis://************:7003, flags=[MASTER, MYSELF], slaveOf=null, slotRanges=[[5461-10922]]], ClusterNodeInfo [nodeId=85e61682488d3a355264d212607a63832da60e8c, address=redis://************:7006, flags=[SLAVE], slaveOf=02aff3b78f8c09c43cbfc2346eac68a16a942097, slotRanges=[]], ClusterNodeInfo [nodeId=64f45f937e6f8c36d0fb7cf09195403c4cccdf1d, address=redis://************:7004, flags=[MASTER], slaveOf=null, slotRanges=[[0-5460]]], ClusterNodeInfo [nodeId=5514edc6d1aaab0526b0093a424e9f352e9914ed, address=redis://************:7002, flags=[SLAVE], slaveOf=4dcfd30d1191cb5e65ef69e88dc76592f8b3d14d, slotRanges=[]]]
2025-06-05 23:11:56.290 [lettuce-nioEventLoop-4-6] WARN  io.lettuce.core.cluster.topology.ClusterTopologyRefresh.lambda$getConnections$0 Line:284 - Unable to connect to [************:7001]: connection timed out: /************:7001
2025-06-05 23:11:56.305 [lettuce-nioEventLoop-4-11] WARN  io.lettuce.core.cluster.topology.ClusterTopologyRefresh.lambda$getConnections$0 Line:284 - Unable to connect to [************:7006]: connection timed out: /************:7006
2025-06-05 23:11:56.305 [lettuce-nioEventLoop-4-8] WARN  io.lettuce.core.cluster.topology.ClusterTopologyRefresh.lambda$getConnections$0 Line:284 - Unable to connect to [************:7003]: connection timed out: /************:7003
2025-06-05 23:11:56.305 [lettuce-nioEventLoop-4-7] WARN  io.lettuce.core.cluster.topology.ClusterTopologyRefresh.lambda$getConnections$0 Line:284 - Unable to connect to [************:7002]: connection timed out: /************:7002
2025-06-05 23:11:56.305 [lettuce-nioEventLoop-4-9] WARN  io.lettuce.core.cluster.topology.ClusterTopologyRefresh.lambda$getConnections$0 Line:284 - Unable to connect to [************:7004]: connection timed out: /************:7004
2025-06-05 23:11:56.305 [lettuce-nioEventLoop-4-10] WARN  io.lettuce.core.cluster.topology.ClusterTopologyRefresh.lambda$getConnections$0 Line:284 - Unable to connect to [************:7005]: connection timed out: /************:7005
2025-06-05 23:12:06.323 [lettuce-nioEventLoop-4-3] WARN  io.lettuce.core.cluster.topology.ClusterTopologyRefresh.lambda$getConnections$0 Line:284 - Unable to connect to [************:7002]: connection timed out: /************:7002
2025-06-05 23:12:06.323 [lettuce-nioEventLoop-4-12] WARN  io.lettuce.core.cluster.topology.ClusterTopologyRefresh.lambda$getConnections$0 Line:284 - Unable to connect to [************:7006]: connection timed out: /************:7006
2025-06-05 23:12:06.323 [lettuce-nioEventLoop-4-2] WARN  io.lettuce.core.cluster.topology.ClusterTopologyRefresh.lambda$getConnections$0 Line:284 - Unable to connect to [************:7005]: connection timed out: /************:7005
2025-06-05 23:12:06.323 [lettuce-nioEventLoop-4-5] WARN  io.lettuce.core.cluster.topology.ClusterTopologyRefresh.lambda$getConnections$0 Line:284 - Unable to connect to [************:7001]: connection timed out: /************:7001
2025-06-05 23:12:06.323 [lettuce-nioEventLoop-4-4] WARN  io.lettuce.core.cluster.topology.ClusterTopologyRefresh.lambda$getConnections$0 Line:284 - Unable to connect to [************:7003]: connection timed out: /************:7003
2025-06-05 23:12:06.323 [lettuce-nioEventLoop-4-1] WARN  io.lettuce.core.cluster.topology.ClusterTopologyRefresh.lambda$getConnections$0 Line:284 - Unable to connect to [************:7004]: connection timed out: /************:7004
2025-06-05 23:12:06.330 [lettuce-eventExecutorLoop-1-6] WARN  io.lettuce.core.cluster.ClusterTopologyRefreshScheduler.doRun Line:258 - Cannot refresh Redis Cluster topology
io.lettuce.core.RedisException: Cannot retrieve initial cluster partitions from initial URIs [RedisURI [host='************', port=7006], RedisURI [host='************', port=7004], RedisURI [host='************', port=7005], RedisURI [host='************', port=7002], RedisURI [host='************', port=7003], RedisURI [host='************', port=7001]]
	at io.lettuce.core.cluster.RedisClusterClient.loadPartitions(RedisClusterClient.java:848)
	at io.lettuce.core.cluster.RedisClusterClient.reloadPartitions(RedisClusterClient.java:784)
	at io.lettuce.core.cluster.ClusterTopologyRefreshScheduler$ClusterTopologyRefreshTask.doRun(ClusterTopologyRefreshScheduler.java:256)
	at io.lettuce.core.cluster.ClusterTopologyRefreshScheduler$ClusterTopologyRefreshTask.run(ClusterTopologyRefreshScheduler.java:236)
	at io.netty.util.concurrent.PromiseTask.runTask(PromiseTask.java:98)
	at io.netty.util.concurrent.PromiseTask.run(PromiseTask.java:106)
	at io.netty.util.concurrent.DefaultEventExecutor.run(DefaultEventExecutor.java:66)
	at io.netty.util.concurrent.SingleThreadEventExecutor$6.run(SingleThreadEventExecutor.java:1050)
	at io.netty.util.internal.ThreadExecutorMap$2.run(ThreadExecutorMap.java:74)
	at io.netty.util.concurrent.FastThreadLocalRunnable.run(FastThreadLocalRunnable.java:30)
	at java.lang.Thread.run(Thread.java:748)
	Suppressed: io.lettuce.core.RedisConnectionException: Unable to establish a connection to Redis Cluster at [RedisURI [host='************', port=7001], RedisURI [host='************', port=7002], RedisURI [host='************', port=7003], RedisURI [host='************', port=7004], RedisURI [host='************', port=7005], RedisURI [host='************', port=7006]]
		at io.lettuce.core.cluster.topology.AsyncConnections.get(AsyncConnections.java:89)
		at io.lettuce.core.cluster.topology.ClusterTopologyRefresh.loadViews(ClusterTopologyRefresh.java:76)
		at io.lettuce.core.cluster.RedisClusterClient.doLoadPartitions(RedisClusterClient.java:865)
		at io.lettuce.core.cluster.RedisClusterClient.loadPartitions(RedisClusterClient.java:838)
		... 10 common frames omitted
		Suppressed: io.lettuce.core.RedisConnectionException: Unable to connect to [************:7006]: connection timed out: /************:7006
			at io.lettuce.core.cluster.topology.ClusterTopologyRefresh.lambda$getConnections$0(ClusterTopologyRefresh.java:290)
			at java.util.concurrent.CompletableFuture.uniWhenComplete(CompletableFuture.java:760)
			at java.util.concurrent.CompletableFuture$UniWhenComplete.tryFire(CompletableFuture.java:736)
			at java.util.concurrent.CompletableFuture.postComplete(CompletableFuture.java:474)
			at java.util.concurrent.CompletableFuture.completeExceptionally(CompletableFuture.java:1977)
			at io.lettuce.core.AbstractRedisClient.lambda$initializeChannelAsync0$4(AbstractRedisClient.java:329)
			at io.netty.util.concurrent.DefaultPromise.notifyListener0(DefaultPromise.java:577)
			at io.netty.util.concurrent.DefaultPromise.notifyListeners0(DefaultPromise.java:570)
			at io.netty.util.concurrent.DefaultPromise.notifyListenersNow(DefaultPromise.java:549)
			at io.netty.util.concurrent.DefaultPromise.notifyListeners(DefaultPromise.java:490)
			at io.netty.util.concurrent.DefaultPromise.setValue0(DefaultPromise.java:615)
			at io.netty.util.concurrent.DefaultPromise.setFailure0(DefaultPromise.java:608)
			at io.netty.util.concurrent.DefaultPromise.tryFailure(DefaultPromise.java:117)
			at io.netty.channel.nio.AbstractNioChannel$AbstractNioUnsafe$1.run(AbstractNioChannel.java:263)
			at io.netty.util.concurrent.PromiseTask.runTask(PromiseTask.java:98)
			at io.netty.util.concurrent.ScheduledFutureTask.run(ScheduledFutureTask.java:150)
			at io.netty.util.concurrent.AbstractEventExecutor.safeExecute(AbstractEventExecutor.java:163)
			at io.netty.util.concurrent.SingleThreadEventExecutor.runAllTasks(SingleThreadEventExecutor.java:510)
			at io.netty.channel.nio.NioEventLoop.run(NioEventLoop.java:518)
			... 4 common frames omitted
		Caused by: io.netty.channel.ConnectTimeoutException: connection timed out: /************:7006
			at io.netty.channel.nio.AbstractNioChannel$AbstractNioUnsafe$1.run(AbstractNioChannel.java:261)
			... 9 common frames omitted
		Suppressed: io.lettuce.core.RedisConnectionException: Unable to connect to [************:7003]: connection timed out: /************:7003
			at io.lettuce.core.cluster.topology.ClusterTopologyRefresh.lambda$getConnections$0(ClusterTopologyRefresh.java:290)
			at java.util.concurrent.CompletableFuture.uniWhenComplete(CompletableFuture.java:760)
			at java.util.concurrent.CompletableFuture$UniWhenComplete.tryFire(CompletableFuture.java:736)
			at java.util.concurrent.CompletableFuture.postComplete(CompletableFuture.java:474)
			at java.util.concurrent.CompletableFuture.completeExceptionally(CompletableFuture.java:1977)
			at io.lettuce.core.AbstractRedisClient.lambda$initializeChannelAsync0$4(AbstractRedisClient.java:329)
			at io.netty.util.concurrent.DefaultPromise.notifyListener0(DefaultPromise.java:577)
			at io.netty.util.concurrent.DefaultPromise.notifyListeners0(DefaultPromise.java:570)
			at io.netty.util.concurrent.DefaultPromise.notifyListenersNow(DefaultPromise.java:549)
			at io.netty.util.concurrent.DefaultPromise.notifyListeners(DefaultPromise.java:490)
			at io.netty.util.concurrent.DefaultPromise.setValue0(DefaultPromise.java:615)
			at io.netty.util.concurrent.DefaultPromise.setFailure0(DefaultPromise.java:608)
			at io.netty.util.concurrent.DefaultPromise.tryFailure(DefaultPromise.java:117)
			at io.netty.channel.nio.AbstractNioChannel$AbstractNioUnsafe$1.run(AbstractNioChannel.java:263)
			at io.netty.util.concurrent.PromiseTask.runTask(PromiseTask.java:98)
			at io.netty.util.concurrent.ScheduledFutureTask.run(ScheduledFutureTask.java:150)
			at io.netty.util.concurrent.AbstractEventExecutor.safeExecute(AbstractEventExecutor.java:163)
			at io.netty.util.concurrent.SingleThreadEventExecutor.runAllTasks(SingleThreadEventExecutor.java:510)
			at io.netty.channel.nio.NioEventLoop.run(NioEventLoop.java:518)
			... 4 common frames omitted
		Caused by: io.netty.channel.ConnectTimeoutException: connection timed out: /************:7003
			at io.netty.channel.nio.AbstractNioChannel$AbstractNioUnsafe$1.run(AbstractNioChannel.java:261)
			... 9 common frames omitted
		Suppressed: io.lettuce.core.RedisConnectionException: Unable to connect to [************:7002]: connection timed out: /************:7002
			at io.lettuce.core.cluster.topology.ClusterTopologyRefresh.lambda$getConnections$0(ClusterTopologyRefresh.java:290)
			at java.util.concurrent.CompletableFuture.uniWhenComplete(CompletableFuture.java:760)
			at java.util.concurrent.CompletableFuture$UniWhenComplete.tryFire(CompletableFuture.java:736)
			at java.util.concurrent.CompletableFuture.postComplete(CompletableFuture.java:474)
			at java.util.concurrent.CompletableFuture.completeExceptionally(CompletableFuture.java:1977)
			at io.lettuce.core.AbstractRedisClient.lambda$initializeChannelAsync0$4(AbstractRedisClient.java:329)
			at io.netty.util.concurrent.DefaultPromise.notifyListener0(DefaultPromise.java:577)
			at io.netty.util.concurrent.DefaultPromise.notifyListeners0(DefaultPromise.java:570)
			at io.netty.util.concurrent.DefaultPromise.notifyListenersNow(DefaultPromise.java:549)
			at io.netty.util.concurrent.DefaultPromise.notifyListeners(DefaultPromise.java:490)
			at io.netty.util.concurrent.DefaultPromise.setValue0(DefaultPromise.java:615)
			at io.netty.util.concurrent.DefaultPromise.setFailure0(DefaultPromise.java:608)
			at io.netty.util.concurrent.DefaultPromise.tryFailure(DefaultPromise.java:117)
			at io.netty.channel.nio.AbstractNioChannel$AbstractNioUnsafe$1.run(AbstractNioChannel.java:263)
			at io.netty.util.concurrent.PromiseTask.runTask(PromiseTask.java:98)
			at io.netty.util.concurrent.ScheduledFutureTask.run(ScheduledFutureTask.java:150)
			at io.netty.util.concurrent.AbstractEventExecutor.safeExecute(AbstractEventExecutor.java:163)
			at io.netty.util.concurrent.SingleThreadEventExecutor.runAllTasks(SingleThreadEventExecutor.java:510)
			at io.netty.channel.nio.NioEventLoop.run(NioEventLoop.java:518)
			... 4 common frames omitted
		Caused by: io.netty.channel.ConnectTimeoutException: connection timed out: /************:7002
			at io.netty.channel.nio.AbstractNioChannel$AbstractNioUnsafe$1.run(AbstractNioChannel.java:261)
			... 9 common frames omitted
		Suppressed: io.lettuce.core.RedisConnectionException: Unable to connect to [************:7004]: connection timed out: /************:7004
			at io.lettuce.core.cluster.topology.ClusterTopologyRefresh.lambda$getConnections$0(ClusterTopologyRefresh.java:290)
			at java.util.concurrent.CompletableFuture.uniWhenComplete(CompletableFuture.java:760)
			at java.util.concurrent.CompletableFuture$UniWhenComplete.tryFire(CompletableFuture.java:736)
			at java.util.concurrent.CompletableFuture.postComplete(CompletableFuture.java:474)
			at java.util.concurrent.CompletableFuture.completeExceptionally(CompletableFuture.java:1977)
			at io.lettuce.core.AbstractRedisClient.lambda$initializeChannelAsync0$4(AbstractRedisClient.java:329)
			at io.netty.util.concurrent.DefaultPromise.notifyListener0(DefaultPromise.java:577)
			at io.netty.util.concurrent.DefaultPromise.notifyListeners0(DefaultPromise.java:570)
			at io.netty.util.concurrent.DefaultPromise.notifyListenersNow(DefaultPromise.java:549)
			at io.netty.util.concurrent.DefaultPromise.notifyListeners(DefaultPromise.java:490)
			at io.netty.util.concurrent.DefaultPromise.setValue0(DefaultPromise.java:615)
			at io.netty.util.concurrent.DefaultPromise.setFailure0(DefaultPromise.java:608)
			at io.netty.util.concurrent.DefaultPromise.tryFailure(DefaultPromise.java:117)
			at io.netty.channel.nio.AbstractNioChannel$AbstractNioUnsafe$1.run(AbstractNioChannel.java:263)
			at io.netty.util.concurrent.PromiseTask.runTask(PromiseTask.java:98)
			at io.netty.util.concurrent.ScheduledFutureTask.run(ScheduledFutureTask.java:150)
			at io.netty.util.concurrent.AbstractEventExecutor.safeExecute(AbstractEventExecutor.java:163)
			at io.netty.util.concurrent.SingleThreadEventExecutor.runAllTasks(SingleThreadEventExecutor.java:510)
			at io.netty.channel.nio.NioEventLoop.run(NioEventLoop.java:518)
			... 4 common frames omitted
		Caused by: io.netty.channel.ConnectTimeoutException: connection timed out: /************:7004
			at io.netty.channel.nio.AbstractNioChannel$AbstractNioUnsafe$1.run(AbstractNioChannel.java:261)
			... 9 common frames omitted
		Suppressed: io.lettuce.core.RedisConnectionException: Unable to connect to [************:7005]: connection timed out: /************:7005
			at io.lettuce.core.cluster.topology.ClusterTopologyRefresh.lambda$getConnections$0(ClusterTopologyRefresh.java:290)
			at java.util.concurrent.CompletableFuture.uniWhenComplete(CompletableFuture.java:760)
			at java.util.concurrent.CompletableFuture$UniWhenComplete.tryFire(CompletableFuture.java:736)
			at java.util.concurrent.CompletableFuture.postComplete(CompletableFuture.java:474)
			at java.util.concurrent.CompletableFuture.completeExceptionally(CompletableFuture.java:1977)
			at io.lettuce.core.AbstractRedisClient.lambda$initializeChannelAsync0$4(AbstractRedisClient.java:329)
			at io.netty.util.concurrent.DefaultPromise.notifyListener0(DefaultPromise.java:577)
			at io.netty.util.concurrent.DefaultPromise.notifyListeners0(DefaultPromise.java:570)
			at io.netty.util.concurrent.DefaultPromise.notifyListenersNow(DefaultPromise.java:549)
			at io.netty.util.concurrent.DefaultPromise.notifyListeners(DefaultPromise.java:490)
			at io.netty.util.concurrent.DefaultPromise.setValue0(DefaultPromise.java:615)
			at io.netty.util.concurrent.DefaultPromise.setFailure0(DefaultPromise.java:608)
			at io.netty.util.concurrent.DefaultPromise.tryFailure(DefaultPromise.java:117)
			at io.netty.channel.nio.AbstractNioChannel$AbstractNioUnsafe$1.run(AbstractNioChannel.java:263)
			at io.netty.util.concurrent.PromiseTask.runTask(PromiseTask.java:98)
			at io.netty.util.concurrent.ScheduledFutureTask.run(ScheduledFutureTask.java:150)
			at io.netty.util.concurrent.AbstractEventExecutor.safeExecute(AbstractEventExecutor.java:163)
			at io.netty.util.concurrent.SingleThreadEventExecutor.runAllTasks(SingleThreadEventExecutor.java:510)
			at io.netty.channel.nio.NioEventLoop.run(NioEventLoop.java:518)
			... 4 common frames omitted
		Caused by: io.netty.channel.ConnectTimeoutException: connection timed out: /************:7005
			at io.netty.channel.nio.AbstractNioChannel$AbstractNioUnsafe$1.run(AbstractNioChannel.java:261)
			... 9 common frames omitted
	Caused by: io.lettuce.core.RedisConnectionException: Unable to connect to [************:7001]: connection timed out: /************:7001
		at io.lettuce.core.cluster.topology.ClusterTopologyRefresh.lambda$getConnections$0(ClusterTopologyRefresh.java:290)
		at java.util.concurrent.CompletableFuture.uniWhenComplete(CompletableFuture.java:760)
		at java.util.concurrent.CompletableFuture$UniWhenComplete.tryFire(CompletableFuture.java:736)
		at java.util.concurrent.CompletableFuture.postComplete(CompletableFuture.java:474)
		at java.util.concurrent.CompletableFuture.completeExceptionally(CompletableFuture.java:1977)
		at io.lettuce.core.AbstractRedisClient.lambda$initializeChannelAsync0$4(AbstractRedisClient.java:329)
		at io.netty.util.concurrent.DefaultPromise.notifyListener0(DefaultPromise.java:577)
		at io.netty.util.concurrent.DefaultPromise.notifyListeners0(DefaultPromise.java:570)
		at io.netty.util.concurrent.DefaultPromise.notifyListenersNow(DefaultPromise.java:549)
		at io.netty.util.concurrent.DefaultPromise.notifyListeners(DefaultPromise.java:490)
		at io.netty.util.concurrent.DefaultPromise.setValue0(DefaultPromise.java:615)
		at io.netty.util.concurrent.DefaultPromise.setFailure0(DefaultPromise.java:608)
		at io.netty.util.concurrent.DefaultPromise.tryFailure(DefaultPromise.java:117)
		at io.netty.channel.nio.AbstractNioChannel$AbstractNioUnsafe$1.run(AbstractNioChannel.java:263)
		at io.netty.util.concurrent.PromiseTask.runTask(PromiseTask.java:98)
		at io.netty.util.concurrent.ScheduledFutureTask.run(ScheduledFutureTask.java:150)
		at io.netty.util.concurrent.AbstractEventExecutor.safeExecute(AbstractEventExecutor.java:163)
		at io.netty.util.concurrent.SingleThreadEventExecutor.runAllTasks(SingleThreadEventExecutor.java:510)
		at io.netty.channel.nio.NioEventLoop.run(NioEventLoop.java:518)
		... 4 common frames omitted
	Caused by: io.netty.channel.ConnectTimeoutException: connection timed out: /************:7001
		at io.netty.channel.nio.AbstractNioChannel$AbstractNioUnsafe$1.run(AbstractNioChannel.java:261)
		... 9 common frames omitted
Caused by: io.lettuce.core.RedisConnectionException: Unable to establish a connection to Redis Cluster at [RedisURI [host='************', port=7001], RedisURI [host='************', port=7002], RedisURI [host='************', port=7003], RedisURI [host='************', port=7004], RedisURI [host='************', port=7005], RedisURI [host='************', port=7006]]
	at io.lettuce.core.cluster.topology.AsyncConnections.get(AsyncConnections.java:89)
	at io.lettuce.core.cluster.topology.ClusterTopologyRefresh.loadViews(ClusterTopologyRefresh.java:76)
	at io.lettuce.core.cluster.RedisClusterClient.doLoadPartitions(RedisClusterClient.java:865)
	at io.lettuce.core.cluster.RedisClusterClient.loadPartitions(RedisClusterClient.java:845)
	... 10 common frames omitted
	Suppressed: io.lettuce.core.RedisConnectionException: Unable to connect to [************:7006]: connection timed out: /************:7006
		at io.lettuce.core.cluster.topology.ClusterTopologyRefresh.lambda$getConnections$0(ClusterTopologyRefresh.java:290)
		at java.util.concurrent.CompletableFuture.uniWhenComplete(CompletableFuture.java:760)
		at java.util.concurrent.CompletableFuture$UniWhenComplete.tryFire(CompletableFuture.java:736)
		at java.util.concurrent.CompletableFuture.postComplete(CompletableFuture.java:474)
		at java.util.concurrent.CompletableFuture.completeExceptionally(CompletableFuture.java:1977)
		at io.lettuce.core.AbstractRedisClient.lambda$initializeChannelAsync0$4(AbstractRedisClient.java:329)
		at io.netty.util.concurrent.DefaultPromise.notifyListener0(DefaultPromise.java:577)
		at io.netty.util.concurrent.DefaultPromise.notifyListeners0(DefaultPromise.java:570)
		at io.netty.util.concurrent.DefaultPromise.notifyListenersNow(DefaultPromise.java:549)
		at io.netty.util.concurrent.DefaultPromise.notifyListeners(DefaultPromise.java:490)
		at io.netty.util.concurrent.DefaultPromise.setValue0(DefaultPromise.java:615)
		at io.netty.util.concurrent.DefaultPromise.setFailure0(DefaultPromise.java:608)
		at io.netty.util.concurrent.DefaultPromise.tryFailure(DefaultPromise.java:117)
		at io.netty.channel.nio.AbstractNioChannel$AbstractNioUnsafe$1.run(AbstractNioChannel.java:263)
		at io.netty.util.concurrent.PromiseTask.runTask(PromiseTask.java:98)
		at io.netty.util.concurrent.ScheduledFutureTask.run(ScheduledFutureTask.java:150)
		at io.netty.util.concurrent.AbstractEventExecutor.safeExecute(AbstractEventExecutor.java:163)
		at io.netty.util.concurrent.SingleThreadEventExecutor.runAllTasks(SingleThreadEventExecutor.java:510)
		at io.netty.channel.nio.NioEventLoop.run(NioEventLoop.java:518)
		... 4 common frames omitted
	Caused by: io.netty.channel.ConnectTimeoutException: connection timed out: /************:7006
		at io.netty.channel.nio.AbstractNioChannel$AbstractNioUnsafe$1.run(AbstractNioChannel.java:261)
		... 9 common frames omitted
	Suppressed: io.lettuce.core.RedisConnectionException: Unable to connect to [************:7005]: connection timed out: /************:7005
		at io.lettuce.core.cluster.topology.ClusterTopologyRefresh.lambda$getConnections$0(ClusterTopologyRefresh.java:290)
		at java.util.concurrent.CompletableFuture.uniWhenComplete(CompletableFuture.java:760)
		at java.util.concurrent.CompletableFuture$UniWhenComplete.tryFire(CompletableFuture.java:736)
		at java.util.concurrent.CompletableFuture.postComplete(CompletableFuture.java:474)
		at java.util.concurrent.CompletableFuture.completeExceptionally(CompletableFuture.java:1977)
		at io.lettuce.core.AbstractRedisClient.lambda$initializeChannelAsync0$4(AbstractRedisClient.java:329)
		at io.netty.util.concurrent.DefaultPromise.notifyListener0(DefaultPromise.java:577)
		at io.netty.util.concurrent.DefaultPromise.notifyListeners0(DefaultPromise.java:570)
		at io.netty.util.concurrent.DefaultPromise.notifyListenersNow(DefaultPromise.java:549)
		at io.netty.util.concurrent.DefaultPromise.notifyListeners(DefaultPromise.java:490)
		at io.netty.util.concurrent.DefaultPromise.setValue0(DefaultPromise.java:615)
		at io.netty.util.concurrent.DefaultPromise.setFailure0(DefaultPromise.java:608)
		at io.netty.util.concurrent.DefaultPromise.tryFailure(DefaultPromise.java:117)
		at io.netty.channel.nio.AbstractNioChannel$AbstractNioUnsafe$1.run(AbstractNioChannel.java:263)
		at io.netty.util.concurrent.PromiseTask.runTask(PromiseTask.java:98)
		at io.netty.util.concurrent.ScheduledFutureTask.run(ScheduledFutureTask.java:150)
		at io.netty.util.concurrent.AbstractEventExecutor.safeExecute(AbstractEventExecutor.java:163)
		at io.netty.util.concurrent.SingleThreadEventExecutor.runAllTasks(SingleThreadEventExecutor.java:510)
		at io.netty.channel.nio.NioEventLoop.run(NioEventLoop.java:518)
		... 4 common frames omitted
	Caused by: io.netty.channel.ConnectTimeoutException: connection timed out: /************:7005
		at io.netty.channel.nio.AbstractNioChannel$AbstractNioUnsafe$1.run(AbstractNioChannel.java:261)
		... 9 common frames omitted
	Suppressed: io.lettuce.core.RedisConnectionException: Unable to connect to [************:7001]: connection timed out: /************:7001
		at io.lettuce.core.cluster.topology.ClusterTopologyRefresh.lambda$getConnections$0(ClusterTopologyRefresh.java:290)
		at java.util.concurrent.CompletableFuture.uniWhenComplete(CompletableFuture.java:760)
		at java.util.concurrent.CompletableFuture$UniWhenComplete.tryFire(CompletableFuture.java:736)
		at java.util.concurrent.CompletableFuture.postComplete(CompletableFuture.java:474)
		at java.util.concurrent.CompletableFuture.completeExceptionally(CompletableFuture.java:1977)
		at io.lettuce.core.AbstractRedisClient.lambda$initializeChannelAsync0$4(AbstractRedisClient.java:329)
		at io.netty.util.concurrent.DefaultPromise.notifyListener0(DefaultPromise.java:577)
		at io.netty.util.concurrent.DefaultPromise.notifyListeners0(DefaultPromise.java:570)
		at io.netty.util.concurrent.DefaultPromise.notifyListenersNow(DefaultPromise.java:549)
		at io.netty.util.concurrent.DefaultPromise.notifyListeners(DefaultPromise.java:490)
		at io.netty.util.concurrent.DefaultPromise.setValue0(DefaultPromise.java:615)
		at io.netty.util.concurrent.DefaultPromise.setFailure0(DefaultPromise.java:608)
		at io.netty.util.concurrent.DefaultPromise.tryFailure(DefaultPromise.java:117)
		at io.netty.channel.nio.AbstractNioChannel$AbstractNioUnsafe$1.run(AbstractNioChannel.java:263)
		at io.netty.util.concurrent.PromiseTask.runTask(PromiseTask.java:98)
		at io.netty.util.concurrent.ScheduledFutureTask.run(ScheduledFutureTask.java:150)
		at io.netty.util.concurrent.AbstractEventExecutor.safeExecute(AbstractEventExecutor.java:163)
		at io.netty.util.concurrent.SingleThreadEventExecutor.runAllTasks(SingleThreadEventExecutor.java:510)
		at io.netty.channel.nio.NioEventLoop.run(NioEventLoop.java:518)
		... 4 common frames omitted
	Caused by: io.netty.channel.ConnectTimeoutException: connection timed out: /************:7001
		at io.netty.channel.nio.AbstractNioChannel$AbstractNioUnsafe$1.run(AbstractNioChannel.java:261)
		... 9 common frames omitted
	Suppressed: io.lettuce.core.RedisConnectionException: Unable to connect to [************:7003]: connection timed out: /************:7003
		at io.lettuce.core.cluster.topology.ClusterTopologyRefresh.lambda$getConnections$0(ClusterTopologyRefresh.java:290)
		at java.util.concurrent.CompletableFuture.uniWhenComplete(CompletableFuture.java:760)
		at java.util.concurrent.CompletableFuture$UniWhenComplete.tryFire(CompletableFuture.java:736)
		at java.util.concurrent.CompletableFuture.postComplete(CompletableFuture.java:474)
		at java.util.concurrent.CompletableFuture.completeExceptionally(CompletableFuture.java:1977)
		at io.lettuce.core.AbstractRedisClient.lambda$initializeChannelAsync0$4(AbstractRedisClient.java:329)
		at io.netty.util.concurrent.DefaultPromise.notifyListener0(DefaultPromise.java:577)
		at io.netty.util.concurrent.DefaultPromise.notifyListeners0(DefaultPromise.java:570)
		at io.netty.util.concurrent.DefaultPromise.notifyListenersNow(DefaultPromise.java:549)
		at io.netty.util.concurrent.DefaultPromise.notifyListeners(DefaultPromise.java:490)
		at io.netty.util.concurrent.DefaultPromise.setValue0(DefaultPromise.java:615)
		at io.netty.util.concurrent.DefaultPromise.setFailure0(DefaultPromise.java:608)
		at io.netty.util.concurrent.DefaultPromise.tryFailure(DefaultPromise.java:117)
		at io.netty.channel.nio.AbstractNioChannel$AbstractNioUnsafe$1.run(AbstractNioChannel.java:263)
		at io.netty.util.concurrent.PromiseTask.runTask(PromiseTask.java:98)
		at io.netty.util.concurrent.ScheduledFutureTask.run(ScheduledFutureTask.java:150)
		at io.netty.util.concurrent.AbstractEventExecutor.safeExecute(AbstractEventExecutor.java:163)
		at io.netty.util.concurrent.SingleThreadEventExecutor.runAllTasks(SingleThreadEventExecutor.java:510)
		at io.netty.channel.nio.NioEventLoop.run(NioEventLoop.java:518)
		... 4 common frames omitted
	Caused by: io.netty.channel.ConnectTimeoutException: connection timed out: /************:7003
		at io.netty.channel.nio.AbstractNioChannel$AbstractNioUnsafe$1.run(AbstractNioChannel.java:261)
		... 9 common frames omitted
	Suppressed: io.lettuce.core.RedisConnectionException: Unable to connect to [************:7004]: connection timed out: /************:7004
		at io.lettuce.core.cluster.topology.ClusterTopologyRefresh.lambda$getConnections$0(ClusterTopologyRefresh.java:290)
		at java.util.concurrent.CompletableFuture.uniWhenComplete(CompletableFuture.java:760)
		at java.util.concurrent.CompletableFuture$UniWhenComplete.tryFire(CompletableFuture.java:736)
		at java.util.concurrent.CompletableFuture.postComplete(CompletableFuture.java:474)
		at java.util.concurrent.CompletableFuture.completeExceptionally(CompletableFuture.java:1977)
		at io.lettuce.core.AbstractRedisClient.lambda$initializeChannelAsync0$4(AbstractRedisClient.java:329)
		at io.netty.util.concurrent.DefaultPromise.notifyListener0(DefaultPromise.java:577)
		at io.netty.util.concurrent.DefaultPromise.notifyListeners0(DefaultPromise.java:570)
		at io.netty.util.concurrent.DefaultPromise.notifyListenersNow(DefaultPromise.java:549)
		at io.netty.util.concurrent.DefaultPromise.notifyListeners(DefaultPromise.java:490)
		at io.netty.util.concurrent.DefaultPromise.setValue0(DefaultPromise.java:615)
		at io.netty.util.concurrent.DefaultPromise.setFailure0(DefaultPromise.java:608)
		at io.netty.util.concurrent.DefaultPromise.tryFailure(DefaultPromise.java:117)
		at io.netty.channel.nio.AbstractNioChannel$AbstractNioUnsafe$1.run(AbstractNioChannel.java:263)
		at io.netty.util.concurrent.PromiseTask.runTask(PromiseTask.java:98)
		at io.netty.util.concurrent.ScheduledFutureTask.run(ScheduledFutureTask.java:150)
		at io.netty.util.concurrent.AbstractEventExecutor.safeExecute(AbstractEventExecutor.java:163)
		at io.netty.util.concurrent.SingleThreadEventExecutor.runAllTasks(SingleThreadEventExecutor.java:510)
		at io.netty.channel.nio.NioEventLoop.run(NioEventLoop.java:518)
		... 4 common frames omitted
	Caused by: io.netty.channel.ConnectTimeoutException: connection timed out: /************:7004
		at io.netty.channel.nio.AbstractNioChannel$AbstractNioUnsafe$1.run(AbstractNioChannel.java:261)
		... 9 common frames omitted
Caused by: io.lettuce.core.RedisConnectionException: Unable to connect to [************:7002]: connection timed out: /************:7002
	at io.lettuce.core.cluster.topology.ClusterTopologyRefresh.lambda$getConnections$0(ClusterTopologyRefresh.java:290)
	at java.util.concurrent.CompletableFuture.uniWhenComplete(CompletableFuture.java:760)
	at java.util.concurrent.CompletableFuture$UniWhenComplete.tryFire(CompletableFuture.java:736)
	at java.util.concurrent.CompletableFuture.postComplete(CompletableFuture.java:474)
	at java.util.concurrent.CompletableFuture.completeExceptionally(CompletableFuture.java:1977)
	at io.lettuce.core.AbstractRedisClient.lambda$initializeChannelAsync0$4(AbstractRedisClient.java:329)
	at io.netty.util.concurrent.DefaultPromise.notifyListener0(DefaultPromise.java:577)
	at io.netty.util.concurrent.DefaultPromise.notifyListeners0(DefaultPromise.java:570)
	at io.netty.util.concurrent.DefaultPromise.notifyListenersNow(DefaultPromise.java:549)
	at io.netty.util.concurrent.DefaultPromise.notifyListeners(DefaultPromise.java:490)
	at io.netty.util.concurrent.DefaultPromise.setValue0(DefaultPromise.java:615)
	at io.netty.util.concurrent.DefaultPromise.setFailure0(DefaultPromise.java:608)
	at io.netty.util.concurrent.DefaultPromise.tryFailure(DefaultPromise.java:117)
	at io.netty.channel.nio.AbstractNioChannel$AbstractNioUnsafe$1.run(AbstractNioChannel.java:263)
	at io.netty.util.concurrent.PromiseTask.runTask(PromiseTask.java:98)
	at io.netty.util.concurrent.ScheduledFutureTask.run(ScheduledFutureTask.java:150)
	at io.netty.util.concurrent.AbstractEventExecutor.safeExecute(AbstractEventExecutor.java:163)
	at io.netty.util.concurrent.SingleThreadEventExecutor.runAllTasks(SingleThreadEventExecutor.java:510)
	at io.netty.channel.nio.NioEventLoop.run(NioEventLoop.java:518)
	... 4 common frames omitted
Caused by: io.netty.channel.ConnectTimeoutException: connection timed out: /************:7002
	at io.netty.channel.nio.AbstractNioChannel$AbstractNioUnsafe$1.run(AbstractNioChannel.java:261)
	... 9 common frames omitted
2025-06-05 23:32:19.915 [redisson-netty-5-7] WARN  org.redisson.client.handler.CommandDecoder.completeResponse Line:445 - response has been skipped due to timeout! channel: [id: 0x8d6c3e4d, L:/***********:61644 - R:************/************:7003], command: (CLUSTER NODES), params: [], result: [ClusterNodeInfo [nodeId=4dcfd30d1191cb5e65ef69e88dc76592f8b3d14d, address=redis://************:7005, flags=[MASTER], slaveOf=null, slotRanges=[[10923-16383]]], ClusterNodeInfo [nodeId=248f8c5f1a3d07d891f1bcf924c02097c5509e0d, address=redis://************:7001, flags=[SLAVE], slaveOf=64f45f937e6f8c36d0fb7cf09195403c4cccdf1d, slotRanges=[]], ClusterNodeInfo [nodeId=02aff3b78f8c09c43cbfc2346eac68a16a942097, address=redis://************:7003, flags=[MASTER, MYSELF], slaveOf=null, slotRanges=[[5461-10922]]], ClusterNodeInfo [nodeId=85e61682488d3a355264d212607a63832da60e8c, address=redis://************:7006, flags=[SLAVE], slaveOf=02aff3b78f8c09c43cbfc2346eac68a16a942097, slotRanges=[]], ClusterNodeInfo [nodeId=64f45f937e6f8c36d0fb7cf09195403c4cccdf1d, address=redis://************:7004, flags=[MASTER], slaveOf=null, slotRanges=[[0-5460]]], ClusterNodeInfo [nodeId=5514edc6d1aaab0526b0093a424e9f352e9914ed, address=redis://************:7002, flags=[SLAVE], slaveOf=4dcfd30d1191cb5e65ef69e88dc76592f8b3d14d, slotRanges=[]]]
2025-06-05 23:53:02.988 [redisson-netty-5-5] WARN  org.redisson.client.handler.CommandDecoder.completeResponse Line:445 - response has been skipped due to timeout! channel: [id: 0x85e48361, L:/***********:53979 - R:************/************:7003], command: (CLUSTER NODES), params: [], result: [ClusterNodeInfo [nodeId=4dcfd30d1191cb5e65ef69e88dc76592f8b3d14d, address=redis://************:7005, flags=[MASTER], slaveOf=null, slotRanges=[[10923-16383]]], ClusterNodeInfo [nodeId=248f8c5f1a3d07d891f1bcf924c02097c5509e0d, address=redis://************:7001, flags=[SLAVE], slaveOf=64f45f937e6f8c36d0fb7cf09195403c4cccdf1d, slotRanges=[]], ClusterNodeInfo [nodeId=02aff3b78f8c09c43cbfc2346eac68a16a942097, address=redis://************:7003, flags=[MASTER, MYSELF], slaveOf=null, slotRanges=[[5461-10922]]], ClusterNodeInfo [nodeId=85e61682488d3a355264d212607a63832da60e8c, address=redis://************:7006, flags=[SLAVE], slaveOf=02aff3b78f8c09c43cbfc2346eac68a16a942097, slotRanges=[]], ClusterNodeInfo [nodeId=64f45f937e6f8c36d0fb7cf09195403c4cccdf1d, address=redis://************:7004, flags=[MASTER], slaveOf=null, slotRanges=[[0-5460]]], ClusterNodeInfo [nodeId=5514edc6d1aaab0526b0093a424e9f352e9914ed, address=redis://************:7002, flags=[SLAVE], slaveOf=4dcfd30d1191cb5e65ef69e88dc76592f8b3d14d, slotRanges=[]]]
