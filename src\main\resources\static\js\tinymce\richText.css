/*改变按钮颜色*/
.tox-dialog__footer-end button:nth-child(2) {
    border-color: #39AEF5;
    background-color: #39AEF5;
}

.tox-dialog__footer-end button[title="关闭"] {
    border-color: #39AEF5 !important;
    background-color: #39AEF5 !important;
}

.mce-content-body {
    width: 770px;
}

/*改变预览宽度*/
.tox-dialog--width-lg {
    width: 853px !important;
}

.tox-tinymce-aux {
    position: fixed !important;
}

.tox .tox-notification__body {
    white-space: nowrap !important;
}

.tox-collection__group {
    white-space: nowrap !important;
}

.tox .tox-dialog-wrap {
    width: 853px !important;
    left: 50% !important;
    margin-left: -426px !important;
}

.tox .tox-dialog-wrap__backdrop {
    display: none !important;
}

/*所有弹出框公用类名*/
/*.tox-dialog__content-js{*/
/*    width: 853px !important;*/
/*}*/

.tinydemo image {
    max-width: 770px !important;
}

/* .tox .tox-sidebar-wrap{
    width: 818px !important;
} */
.mce-content-body p img {
    max-width: 770px;
    width: auto;
    height: auto;
}

.tox-tinymce {
    border: none !important;
}

.tox:not([dir=rtl]) .tox-toolbar__group:not(:last-of-type) {
    border-right: 1px solid #ededed !important;
}

.tox .tox-dialog__table tbody tr {
    border-bottom: 1px solid #ededed !important;
}

.tox .tox-toolbar__primary {
    background: none !important;
    border-bottom: 1px solid #ededed !important;
}

.tox-tinymce {
    height: 450px !important;
}