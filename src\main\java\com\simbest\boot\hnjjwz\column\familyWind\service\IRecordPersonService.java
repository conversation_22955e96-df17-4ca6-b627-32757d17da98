package com.simbest.boot.hnjjwz.column.familyWind.service;/**
import	java.util.List;
 * Created by KZH on 2019/7/30 18:12.
 */

import com.simbest.boot.base.service.ILogicService;
import com.simbest.boot.base.web.response.JsonResponse;
import com.simbest.boot.hnjjwz.column.familyWind.model.RecordPerson;
import org.springframework.web.bind.annotation.RequestBody;

import java.util.List;
import java.util.Map;

/**
 * <AUTHOR>
 * @create 2019-07-30 18:12
 * @desc
 **/
public interface IRecordPersonService extends ILogicService<RecordPerson,String> {


    /**
     * 投票
     * @param sumMap
     * @return
     */
    JsonResponse vote(Map<String, String> sumMap);

    Boolean updateOnVoteQuantity();

    JsonResponse findUsername(String userName);

}
