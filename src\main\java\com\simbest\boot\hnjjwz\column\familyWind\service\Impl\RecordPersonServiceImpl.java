package com.simbest.boot.hnjjwz.column.familyWind.service.Impl;/**
import	java.util.HashMap;
 * Created by KZH on 2019/7/30 18:13.
 */

import com.simbest.boot.base.service.impl.LogicService;
import com.simbest.boot.base.web.response.JsonResponse;
import com.simbest.boot.hnjjwz.column.familyWind.model.Record;
import com.simbest.boot.hnjjwz.column.familyWind.model.RecordPerson;
import com.simbest.boot.hnjjwz.column.familyWind.repository.RecordPersonRepository;
import com.simbest.boot.hnjjwz.column.familyWind.service.IFamilyWindService;
import com.simbest.boot.hnjjwz.column.familyWind.service.IRecordPersonService;
import com.simbest.boot.hnjjwz.column.familyWind.service.IRecordService;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;

import java.util.HashMap;
import java.util.Map;

/**
 * <AUTHOR>
 * @create 2019-07-30 18:13
 * @desc
 **/
@Slf4j
@Service
public class RecordPersonServiceImpl extends LogicService<RecordPerson,String> implements IRecordPersonService {

    private RecordPersonRepository recordPersonRepository;

    @Autowired
    public RecordPersonServiceImpl(RecordPersonRepository repository){
        super(repository);
        this.recordPersonRepository=repository;

    }
    @Autowired
    private IFamilyWindService iFamilyWindService;

    @Autowired
    private IRecordService iRecordService;


    @Override
    public JsonResponse vote(Map<String, String> sumMap) {

        String Id =sumMap.get("Id");
        String userName=sumMap.get("userName");
        String type=sumMap.get("type");

        RecordPerson userName1 = recordPersonRepository.findUserName(userName);
        Boolean state= false;
        if (userName1!=null){
            if("书法".equals(type)&&userName1.getCalligraphyVote()>0){
                userName1.setCalligraphyVote(userName1.getCalligraphyVote()-1);
                state=true;
            }
            if("绘画".equals(type)&&userName1.getPaintingVote()>0){
                userName1.setPaintingVote(userName1.getPaintingVote()-1);
                state=true;
            }
            if("征文".equals(type)&&userName1.getCollectarticleVote()>0){
                userName1.setCollectarticleVote(userName1.getCollectarticleVote()-1);
                state=true;
            }
            if("摄影".equals(type)&&userName1.getPhotographyVote()>0){
                userName1.setPhotographyVote(userName1.getPhotographyVote()-1);
                state=true;
            }
            if("廉家事".equals(type)&&userName1.getModel1()>0){
                userName1.setModel1(userName1.getModel1()-1);
                state=true;
            }
            if("廉家宝".equals(type)&&userName1.getModel2()>0){
                userName1.setModel2(userName1.getModel2()-1);
                state=true;
            }
            if("廉家训".equals(type)&&userName1.getModel3()>0){
                userName1.setModel3(userName1.getModel3()-1);
                state=true;
            }
            if(state){
                Boolean aBoolean = iFamilyWindService.updateVoteQuantity(Id);
                if(!aBoolean){
                    return JsonResponse.fail(-1,"投票失败");
                }
                this.update(userName1);
                Record record=new Record();
                record.setUserName(userName);
                record.setOnVoteId(Id);
                iRecordService.insert(record);
                return  JsonResponse.success(true);
            }

            return  JsonResponse.success(false);
        }

        RecordPerson recordPerson = new RecordPerson();
        Boolean aBoolean = iFamilyWindService.updateVoteQuantity(Id);
        if(!aBoolean){
            return JsonResponse.fail(-1,"投票失败");
        }
        if("书法".equals(type)){
            recordPerson.setCalligraphyVote(4);

        }else{
            recordPerson.setCalligraphyVote(5);
        }
        if("绘画".equals(type)){
            recordPerson.setPaintingVote(4);

        }else{
            recordPerson.setPaintingVote(5);
        }
        if("征文".equals(type)){
            recordPerson.setCollectarticleVote(4);

        }else{
            recordPerson.setCollectarticleVote(5);
        }
        if("摄影".equals(type)){
            recordPerson.setPhotographyVote(4);

        }else{
            recordPerson.setPhotographyVote(5);
        }

        if("廉家事".equals(type)){
            recordPerson.setModel1(4);

        }else{
            recordPerson.setModel1(5);
        }

        if("廉家宝".equals(type)){
            recordPerson.setModel2(4);

        }else{
            recordPerson.setModel2(5);
        }

        if("廉家训".equals(type)){
            recordPerson.setModel3(4);

        }else{
            recordPerson.setModel3(5);
        }
        recordPerson.setUserName(userName);

        //recordPerson.setPhotographyVote(4);
        this.insert(recordPerson);
        Record record=new Record();
        record.setUserName(userName);
        record.setOnVoteId(Id);
        iRecordService.insert(record);
        return JsonResponse.success(true);
    }

    @Override
    public Boolean updateOnVoteQuantity() {
        int i = recordPersonRepository.updateOnVoteQuantity();
        if(i<0){

            return false;
        }
        return true;
    }

    @Override
    public JsonResponse findUsername(String userName) {
        return null;
    }


}
