package com.simbest.boot.hnjjwz.backstage.indexMenu.service.impl;/**
 * Created by GZJ on 2019/5/30 16:56.
 */

import com.simbest.boot.base.service.impl.LogicService;
import com.simbest.boot.hnjjwz.backstage.indexMenu.model.SubmenuMenu;
import com.simbest.boot.hnjjwz.backstage.indexMenu.repository.SubmenuMenuRepository;
import com.simbest.boot.hnjjwz.backstage.indexMenu.service.ISubmenuMenuService;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;

import java.util.List;

/**
 * <AUTHOR>
 * @create 2019-05-30 16:56
 * @desc
 **/
@Slf4j
@Service
public class SubmenuMenuServiceImpl extends LogicService<SubmenuMenu,String> implements ISubmenuMenuService {

    private SubmenuMenuRepository submenuMenuRepository;
    @Autowired
    public SubmenuMenuServiceImpl(SubmenuMenuRepository submenuMenuRepository) {
        super(submenuMenuRepository);
        this.submenuMenuRepository=submenuMenuRepository;
    }

    @Override
    public List<SubmenuMenu> findSubmenuMenu() {
        return submenuMenuRepository.findSubmenuMenu();
    }
}
