package com.simbest.boot.hnjjwz.examOnline.repository;

import com.simbest.boot.base.repository.LogicRepository;
import com.simbest.boot.hnjjwz.examOnline.model.ExamAnswer;
import org.springframework.data.domain.Page;
import org.springframework.data.domain.Pageable;
import org.springframework.data.jpa.repository.Modifying;
import org.springframework.data.jpa.repository.Query;
import org.springframework.data.repository.query.Param;

import java.util.List;

/**
 * <AUTHOR>
 * @Data 201/05/08
 * @Description 答案信息
 */
public interface ExamAnswerRepository extends LogicRepository<ExamAnswer,String> {


    @Query(value = "select t.*,t.rowid from US_EXAM_ANSWER t where t.answer_code=:questionCode and  t.enabled=1 ",
            nativeQuery = true)
    List<ExamAnswer> customFindExamAnswer(@Param( "questionCode" )String questionCode);


    /**
     * 查询自己填报信息
     */
    @Query(
            value = "select t.* from US_EXAM_ANSWER t where t.answer_code=:questionCode and  t.enabled=1 ",
            countQuery = "select count(*)  from US_EXAM_ANSWER t where t.answer_code=:questionCode and  t.enabled=1 " ,
            nativeQuery = true
    )
    Page<ExamAnswer> customFindExamAnswer(@Param( "questionCode" )String questionCode,Pageable pageable);


    @Modifying
    @Query(
            value = "update US_EXAM_ANSWER t set t.enabled=0 where t.answer_code=:questionCode and t.enabled=1 and t.removed_time is null",
            nativeQuery = true
    )
    int  deleteExamAnswer(@Param( "questionCode" )String questionCode);

}
