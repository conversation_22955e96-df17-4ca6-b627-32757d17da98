2025-06-01 00:10:00.018 [pool-14-thread-3] WARN  com.simbest.boot.base.repository.CustomDynamicWhere.queryForList Line:50 - 自定义查询SQL输出为：【select * from US_TODO_MODEL t  where t.send_flag = 0 and t.work_flag = 0 and t.enabled =1】
2025-06-01 00:31:05.006 [lettuce-nioEventLoop-4-7] WARN  io.lettuce.core.cluster.topology.ClusterTopologyRefresh.lambda$getConnections$0 Line:284 - Unable to connect to [************:7003]: connection timed out: /************:7003
2025-06-01 00:31:05.006 [lettuce-nioEventLoop-4-6] WARN  io.lettuce.core.cluster.topology.ClusterTopologyRefresh.lambda$getConnections$0 Line:284 - Unable to connect to [************:7002]: connection timed out: /************:7002
2025-06-01 00:31:05.006 [lettuce-nioEventLoop-4-10] WARN  io.lettuce.core.cluster.topology.ClusterTopologyRefresh.lambda$getConnections$0 Line:284 - Unable to connect to [************:7006]: connection timed out: /************:7006
2025-06-01 00:31:05.006 [lettuce-nioEventLoop-4-9] WARN  io.lettuce.core.cluster.topology.ClusterTopologyRefresh.lambda$getConnections$0 Line:284 - Unable to connect to [************:7005]: connection timed out: /************:7005
2025-06-01 00:31:05.006 [lettuce-nioEventLoop-4-5] WARN  io.lettuce.core.cluster.topology.ClusterTopologyRefresh.lambda$getConnections$0 Line:284 - Unable to connect to [************:7001]: connection timed out: /************:7001
2025-06-01 00:31:05.006 [lettuce-nioEventLoop-4-8] WARN  io.lettuce.core.cluster.topology.ClusterTopologyRefresh.lambda$getConnections$0 Line:284 - Unable to connect to [************:7004]: connection timed out: /************:7004
2025-06-01 00:31:09.664 [lettuce-nioEventLoop-4-12] WARN  io.lettuce.core.protocol.ConnectionWatchdog.log Line:153 - Cannot reconnect to [************:7006]: connection timed out: /************:7006
2025-06-01 00:31:09.664 [lettuce-nioEventLoop-4-11] WARN  io.lettuce.core.protocol.ConnectionWatchdog.log Line:153 - Cannot reconnect to [************:7003]: connection timed out: /************:7003
2025-06-01 00:31:09.757 [lettuce-nioEventLoop-4-3] WARN  io.lettuce.core.protocol.ConnectionWatchdog.log Line:153 - Cannot reconnect to [************:7004]: connection timed out: /************:7004
2025-06-01 00:31:09.757 [lettuce-nioEventLoop-4-1] WARN  io.lettuce.core.protocol.ConnectionWatchdog.log Line:153 - Cannot reconnect to [************:7005]: connection timed out: /************:7005
2025-06-01 00:31:09.757 [lettuce-nioEventLoop-4-2] WARN  io.lettuce.core.protocol.ConnectionWatchdog.log Line:153 - Cannot reconnect to [************:7006]: connection timed out: /************:7006
2025-06-01 00:31:15.019 [lettuce-nioEventLoop-4-4] WARN  io.lettuce.core.cluster.topology.ClusterTopologyRefresh.lambda$getConnections$0 Line:284 - Unable to connect to [************:7006]: connection timed out: /************:7006
2025-06-01 00:31:15.019 [lettuce-nioEventLoop-4-5] WARN  io.lettuce.core.cluster.topology.ClusterTopologyRefresh.lambda$getConnections$0 Line:284 - Unable to connect to [************:7004]: connection timed out: /************:7004
2025-06-01 00:31:15.019 [lettuce-nioEventLoop-4-6] WARN  io.lettuce.core.cluster.topology.ClusterTopologyRefresh.lambda$getConnections$0 Line:284 - Unable to connect to [************:7005]: connection timed out: /************:7005
2025-06-01 00:31:15.019 [lettuce-nioEventLoop-4-9] WARN  io.lettuce.core.cluster.topology.ClusterTopologyRefresh.lambda$getConnections$0 Line:284 - Unable to connect to [************:7001]: connection timed out: /************:7001
2025-06-01 00:31:15.019 [lettuce-nioEventLoop-4-7] WARN  io.lettuce.core.cluster.topology.ClusterTopologyRefresh.lambda$getConnections$0 Line:284 - Unable to connect to [************:7002]: connection timed out: /************:7002
2025-06-01 00:31:15.019 [lettuce-nioEventLoop-4-8] WARN  io.lettuce.core.cluster.topology.ClusterTopologyRefresh.lambda$getConnections$0 Line:284 - Unable to connect to [************:7003]: connection timed out: /************:7003
2025-06-01 00:31:15.028 [lettuce-eventExecutorLoop-1-8] WARN  io.lettuce.core.cluster.ClusterTopologyRefreshScheduler.doRun Line:258 - Cannot refresh Redis Cluster topology
io.lettuce.core.RedisException: Cannot retrieve initial cluster partitions from initial URIs [RedisURI [host='************', port=7006], RedisURI [host='************', port=7004], RedisURI [host='************', port=7005], RedisURI [host='************', port=7002], RedisURI [host='************', port=7003], RedisURI [host='************', port=7001]]
	at io.lettuce.core.cluster.RedisClusterClient.loadPartitions(RedisClusterClient.java:848)
	at io.lettuce.core.cluster.RedisClusterClient.reloadPartitions(RedisClusterClient.java:784)
	at io.lettuce.core.cluster.ClusterTopologyRefreshScheduler$ClusterTopologyRefreshTask.doRun(ClusterTopologyRefreshScheduler.java:256)
	at io.lettuce.core.cluster.ClusterTopologyRefreshScheduler$ClusterTopologyRefreshTask.run(ClusterTopologyRefreshScheduler.java:236)
	at io.netty.util.concurrent.PromiseTask.runTask(PromiseTask.java:98)
	at io.netty.util.concurrent.PromiseTask.run(PromiseTask.java:106)
	at io.netty.util.concurrent.DefaultEventExecutor.run(DefaultEventExecutor.java:66)
	at io.netty.util.concurrent.SingleThreadEventExecutor$6.run(SingleThreadEventExecutor.java:1050)
	at io.netty.util.internal.ThreadExecutorMap$2.run(ThreadExecutorMap.java:74)
	at io.netty.util.concurrent.FastThreadLocalRunnable.run(FastThreadLocalRunnable.java:30)
	at java.lang.Thread.run(Thread.java:748)
	Suppressed: io.lettuce.core.RedisConnectionException: Unable to establish a connection to Redis Cluster at [RedisURI [host='************', port=7001], RedisURI [host='************', port=7002], RedisURI [host='************', port=7003], RedisURI [host='************', port=7004], RedisURI [host='************', port=7005], RedisURI [host='************', port=7006]]
		at io.lettuce.core.cluster.topology.AsyncConnections.get(AsyncConnections.java:89)
		at io.lettuce.core.cluster.topology.ClusterTopologyRefresh.loadViews(ClusterTopologyRefresh.java:76)
		at io.lettuce.core.cluster.RedisClusterClient.doLoadPartitions(RedisClusterClient.java:865)
		at io.lettuce.core.cluster.RedisClusterClient.loadPartitions(RedisClusterClient.java:838)
		... 10 common frames omitted
		Suppressed: io.lettuce.core.RedisConnectionException: Unable to connect to [************:7002]: connection timed out: /************:7002
			at io.lettuce.core.cluster.topology.ClusterTopologyRefresh.lambda$getConnections$0(ClusterTopologyRefresh.java:290)
			at java.util.concurrent.CompletableFuture.uniWhenComplete(CompletableFuture.java:760)
			at java.util.concurrent.CompletableFuture$UniWhenComplete.tryFire(CompletableFuture.java:736)
			at java.util.concurrent.CompletableFuture.postComplete(CompletableFuture.java:474)
			at java.util.concurrent.CompletableFuture.completeExceptionally(CompletableFuture.java:1977)
			at io.lettuce.core.AbstractRedisClient.lambda$initializeChannelAsync0$4(AbstractRedisClient.java:329)
			at io.netty.util.concurrent.DefaultPromise.notifyListener0(DefaultPromise.java:577)
			at io.netty.util.concurrent.DefaultPromise.notifyListeners0(DefaultPromise.java:570)
			at io.netty.util.concurrent.DefaultPromise.notifyListenersNow(DefaultPromise.java:549)
			at io.netty.util.concurrent.DefaultPromise.notifyListeners(DefaultPromise.java:490)
			at io.netty.util.concurrent.DefaultPromise.setValue0(DefaultPromise.java:615)
			at io.netty.util.concurrent.DefaultPromise.setFailure0(DefaultPromise.java:608)
			at io.netty.util.concurrent.DefaultPromise.tryFailure(DefaultPromise.java:117)
			at io.netty.channel.nio.AbstractNioChannel$AbstractNioUnsafe$1.run(AbstractNioChannel.java:263)
			at io.netty.util.concurrent.PromiseTask.runTask(PromiseTask.java:98)
			at io.netty.util.concurrent.ScheduledFutureTask.run(ScheduledFutureTask.java:150)
			at io.netty.util.concurrent.AbstractEventExecutor.safeExecute(AbstractEventExecutor.java:163)
			at io.netty.util.concurrent.SingleThreadEventExecutor.runAllTasks(SingleThreadEventExecutor.java:510)
			at io.netty.channel.nio.NioEventLoop.run(NioEventLoop.java:518)
			... 4 common frames omitted
		Caused by: io.netty.channel.ConnectTimeoutException: connection timed out: /************:7002
			at io.netty.channel.nio.AbstractNioChannel$AbstractNioUnsafe$1.run(AbstractNioChannel.java:261)
			... 9 common frames omitted
		Suppressed: io.lettuce.core.RedisConnectionException: Unable to connect to [************:7006]: connection timed out: /************:7006
			at io.lettuce.core.cluster.topology.ClusterTopologyRefresh.lambda$getConnections$0(ClusterTopologyRefresh.java:290)
			at java.util.concurrent.CompletableFuture.uniWhenComplete(CompletableFuture.java:760)
			at java.util.concurrent.CompletableFuture$UniWhenComplete.tryFire(CompletableFuture.java:736)
			at java.util.concurrent.CompletableFuture.postComplete(CompletableFuture.java:474)
			at java.util.concurrent.CompletableFuture.completeExceptionally(CompletableFuture.java:1977)
			at io.lettuce.core.AbstractRedisClient.lambda$initializeChannelAsync0$4(AbstractRedisClient.java:329)
			at io.netty.util.concurrent.DefaultPromise.notifyListener0(DefaultPromise.java:577)
			at io.netty.util.concurrent.DefaultPromise.notifyListeners0(DefaultPromise.java:570)
			at io.netty.util.concurrent.DefaultPromise.notifyListenersNow(DefaultPromise.java:549)
			at io.netty.util.concurrent.DefaultPromise.notifyListeners(DefaultPromise.java:490)
			at io.netty.util.concurrent.DefaultPromise.setValue0(DefaultPromise.java:615)
			at io.netty.util.concurrent.DefaultPromise.setFailure0(DefaultPromise.java:608)
			at io.netty.util.concurrent.DefaultPromise.tryFailure(DefaultPromise.java:117)
			at io.netty.channel.nio.AbstractNioChannel$AbstractNioUnsafe$1.run(AbstractNioChannel.java:263)
			at io.netty.util.concurrent.PromiseTask.runTask(PromiseTask.java:98)
			at io.netty.util.concurrent.ScheduledFutureTask.run(ScheduledFutureTask.java:150)
			at io.netty.util.concurrent.AbstractEventExecutor.safeExecute(AbstractEventExecutor.java:163)
			at io.netty.util.concurrent.SingleThreadEventExecutor.runAllTasks(SingleThreadEventExecutor.java:510)
			at io.netty.channel.nio.NioEventLoop.run(NioEventLoop.java:518)
			... 4 common frames omitted
		Caused by: io.netty.channel.ConnectTimeoutException: connection timed out: /************:7006
			at io.netty.channel.nio.AbstractNioChannel$AbstractNioUnsafe$1.run(AbstractNioChannel.java:261)
			... 9 common frames omitted
		Suppressed: io.lettuce.core.RedisConnectionException: Unable to connect to [************:7005]: connection timed out: /************:7005
			at io.lettuce.core.cluster.topology.ClusterTopologyRefresh.lambda$getConnections$0(ClusterTopologyRefresh.java:290)
			at java.util.concurrent.CompletableFuture.uniWhenComplete(CompletableFuture.java:760)
			at java.util.concurrent.CompletableFuture$UniWhenComplete.tryFire(CompletableFuture.java:736)
			at java.util.concurrent.CompletableFuture.postComplete(CompletableFuture.java:474)
			at java.util.concurrent.CompletableFuture.completeExceptionally(CompletableFuture.java:1977)
			at io.lettuce.core.AbstractRedisClient.lambda$initializeChannelAsync0$4(AbstractRedisClient.java:329)
			at io.netty.util.concurrent.DefaultPromise.notifyListener0(DefaultPromise.java:577)
			at io.netty.util.concurrent.DefaultPromise.notifyListeners0(DefaultPromise.java:570)
			at io.netty.util.concurrent.DefaultPromise.notifyListenersNow(DefaultPromise.java:549)
			at io.netty.util.concurrent.DefaultPromise.notifyListeners(DefaultPromise.java:490)
			at io.netty.util.concurrent.DefaultPromise.setValue0(DefaultPromise.java:615)
			at io.netty.util.concurrent.DefaultPromise.setFailure0(DefaultPromise.java:608)
			at io.netty.util.concurrent.DefaultPromise.tryFailure(DefaultPromise.java:117)
			at io.netty.channel.nio.AbstractNioChannel$AbstractNioUnsafe$1.run(AbstractNioChannel.java:263)
			at io.netty.util.concurrent.PromiseTask.runTask(PromiseTask.java:98)
			at io.netty.util.concurrent.ScheduledFutureTask.run(ScheduledFutureTask.java:150)
			at io.netty.util.concurrent.AbstractEventExecutor.safeExecute(AbstractEventExecutor.java:163)
			at io.netty.util.concurrent.SingleThreadEventExecutor.runAllTasks(SingleThreadEventExecutor.java:510)
			at io.netty.channel.nio.NioEventLoop.run(NioEventLoop.java:518)
			... 4 common frames omitted
		Caused by: io.netty.channel.ConnectTimeoutException: connection timed out: /************:7005
			at io.netty.channel.nio.AbstractNioChannel$AbstractNioUnsafe$1.run(AbstractNioChannel.java:261)
			... 9 common frames omitted
		Suppressed: io.lettuce.core.RedisConnectionException: Unable to connect to [************:7001]: connection timed out: /************:7001
			at io.lettuce.core.cluster.topology.ClusterTopologyRefresh.lambda$getConnections$0(ClusterTopologyRefresh.java:290)
			at java.util.concurrent.CompletableFuture.uniWhenComplete(CompletableFuture.java:760)
			at java.util.concurrent.CompletableFuture$UniWhenComplete.tryFire(CompletableFuture.java:736)
			at java.util.concurrent.CompletableFuture.postComplete(CompletableFuture.java:474)
			at java.util.concurrent.CompletableFuture.completeExceptionally(CompletableFuture.java:1977)
			at io.lettuce.core.AbstractRedisClient.lambda$initializeChannelAsync0$4(AbstractRedisClient.java:329)
			at io.netty.util.concurrent.DefaultPromise.notifyListener0(DefaultPromise.java:577)
			at io.netty.util.concurrent.DefaultPromise.notifyListeners0(DefaultPromise.java:570)
			at io.netty.util.concurrent.DefaultPromise.notifyListenersNow(DefaultPromise.java:549)
			at io.netty.util.concurrent.DefaultPromise.notifyListeners(DefaultPromise.java:490)
			at io.netty.util.concurrent.DefaultPromise.setValue0(DefaultPromise.java:615)
			at io.netty.util.concurrent.DefaultPromise.setFailure0(DefaultPromise.java:608)
			at io.netty.util.concurrent.DefaultPromise.tryFailure(DefaultPromise.java:117)
			at io.netty.channel.nio.AbstractNioChannel$AbstractNioUnsafe$1.run(AbstractNioChannel.java:263)
			at io.netty.util.concurrent.PromiseTask.runTask(PromiseTask.java:98)
			at io.netty.util.concurrent.ScheduledFutureTask.run(ScheduledFutureTask.java:150)
			at io.netty.util.concurrent.AbstractEventExecutor.safeExecute(AbstractEventExecutor.java:163)
			at io.netty.util.concurrent.SingleThreadEventExecutor.runAllTasks(SingleThreadEventExecutor.java:510)
			at io.netty.channel.nio.NioEventLoop.run(NioEventLoop.java:518)
			... 4 common frames omitted
		Caused by: io.netty.channel.ConnectTimeoutException: connection timed out: /************:7001
			at io.netty.channel.nio.AbstractNioChannel$AbstractNioUnsafe$1.run(AbstractNioChannel.java:261)
			... 9 common frames omitted
		Suppressed: io.lettuce.core.RedisConnectionException: Unable to connect to [************:7004]: connection timed out: /************:7004
			at io.lettuce.core.cluster.topology.ClusterTopologyRefresh.lambda$getConnections$0(ClusterTopologyRefresh.java:290)
			at java.util.concurrent.CompletableFuture.uniWhenComplete(CompletableFuture.java:760)
			at java.util.concurrent.CompletableFuture$UniWhenComplete.tryFire(CompletableFuture.java:736)
			at java.util.concurrent.CompletableFuture.postComplete(CompletableFuture.java:474)
			at java.util.concurrent.CompletableFuture.completeExceptionally(CompletableFuture.java:1977)
			at io.lettuce.core.AbstractRedisClient.lambda$initializeChannelAsync0$4(AbstractRedisClient.java:329)
			at io.netty.util.concurrent.DefaultPromise.notifyListener0(DefaultPromise.java:577)
			at io.netty.util.concurrent.DefaultPromise.notifyListeners0(DefaultPromise.java:570)
			at io.netty.util.concurrent.DefaultPromise.notifyListenersNow(DefaultPromise.java:549)
			at io.netty.util.concurrent.DefaultPromise.notifyListeners(DefaultPromise.java:490)
			at io.netty.util.concurrent.DefaultPromise.setValue0(DefaultPromise.java:615)
			at io.netty.util.concurrent.DefaultPromise.setFailure0(DefaultPromise.java:608)
			at io.netty.util.concurrent.DefaultPromise.tryFailure(DefaultPromise.java:117)
			at io.netty.channel.nio.AbstractNioChannel$AbstractNioUnsafe$1.run(AbstractNioChannel.java:263)
			at io.netty.util.concurrent.PromiseTask.runTask(PromiseTask.java:98)
			at io.netty.util.concurrent.ScheduledFutureTask.run(ScheduledFutureTask.java:150)
			at io.netty.util.concurrent.AbstractEventExecutor.safeExecute(AbstractEventExecutor.java:163)
			at io.netty.util.concurrent.SingleThreadEventExecutor.runAllTasks(SingleThreadEventExecutor.java:510)
			at io.netty.channel.nio.NioEventLoop.run(NioEventLoop.java:518)
			... 4 common frames omitted
		Caused by: io.netty.channel.ConnectTimeoutException: connection timed out: /************:7004
			at io.netty.channel.nio.AbstractNioChannel$AbstractNioUnsafe$1.run(AbstractNioChannel.java:261)
			... 9 common frames omitted
	Caused by: io.lettuce.core.RedisConnectionException: Unable to connect to [************:7003]: connection timed out: /************:7003
		at io.lettuce.core.cluster.topology.ClusterTopologyRefresh.lambda$getConnections$0(ClusterTopologyRefresh.java:290)
		at java.util.concurrent.CompletableFuture.uniWhenComplete(CompletableFuture.java:760)
		at java.util.concurrent.CompletableFuture$UniWhenComplete.tryFire(CompletableFuture.java:736)
		at java.util.concurrent.CompletableFuture.postComplete(CompletableFuture.java:474)
		at java.util.concurrent.CompletableFuture.completeExceptionally(CompletableFuture.java:1977)
		at io.lettuce.core.AbstractRedisClient.lambda$initializeChannelAsync0$4(AbstractRedisClient.java:329)
		at io.netty.util.concurrent.DefaultPromise.notifyListener0(DefaultPromise.java:577)
		at io.netty.util.concurrent.DefaultPromise.notifyListeners0(DefaultPromise.java:570)
		at io.netty.util.concurrent.DefaultPromise.notifyListenersNow(DefaultPromise.java:549)
		at io.netty.util.concurrent.DefaultPromise.notifyListeners(DefaultPromise.java:490)
		at io.netty.util.concurrent.DefaultPromise.setValue0(DefaultPromise.java:615)
		at io.netty.util.concurrent.DefaultPromise.setFailure0(DefaultPromise.java:608)
		at io.netty.util.concurrent.DefaultPromise.tryFailure(DefaultPromise.java:117)
		at io.netty.channel.nio.AbstractNioChannel$AbstractNioUnsafe$1.run(AbstractNioChannel.java:263)
		at io.netty.util.concurrent.PromiseTask.runTask(PromiseTask.java:98)
		at io.netty.util.concurrent.ScheduledFutureTask.run(ScheduledFutureTask.java:150)
		at io.netty.util.concurrent.AbstractEventExecutor.safeExecute(AbstractEventExecutor.java:163)
		at io.netty.util.concurrent.SingleThreadEventExecutor.runAllTasks(SingleThreadEventExecutor.java:510)
		at io.netty.channel.nio.NioEventLoop.run(NioEventLoop.java:518)
		... 4 common frames omitted
	Caused by: io.netty.channel.ConnectTimeoutException: connection timed out: /************:7003
		at io.netty.channel.nio.AbstractNioChannel$AbstractNioUnsafe$1.run(AbstractNioChannel.java:261)
		... 9 common frames omitted
Caused by: io.lettuce.core.RedisConnectionException: Unable to establish a connection to Redis Cluster at [RedisURI [host='************', port=7001], RedisURI [host='************', port=7002], RedisURI [host='************', port=7003], RedisURI [host='************', port=7004], RedisURI [host='************', port=7005], RedisURI [host='************', port=7006]]
	at io.lettuce.core.cluster.topology.AsyncConnections.get(AsyncConnections.java:89)
	at io.lettuce.core.cluster.topology.ClusterTopologyRefresh.loadViews(ClusterTopologyRefresh.java:76)
	at io.lettuce.core.cluster.RedisClusterClient.doLoadPartitions(RedisClusterClient.java:865)
	at io.lettuce.core.cluster.RedisClusterClient.loadPartitions(RedisClusterClient.java:845)
	... 10 common frames omitted
	Suppressed: io.lettuce.core.RedisConnectionException: Unable to connect to [************:7006]: connection timed out: /************:7006
		at io.lettuce.core.cluster.topology.ClusterTopologyRefresh.lambda$getConnections$0(ClusterTopologyRefresh.java:290)
		at java.util.concurrent.CompletableFuture.uniWhenComplete(CompletableFuture.java:760)
		at java.util.concurrent.CompletableFuture$UniWhenComplete.tryFire(CompletableFuture.java:736)
		at java.util.concurrent.CompletableFuture.postComplete(CompletableFuture.java:474)
		at java.util.concurrent.CompletableFuture.completeExceptionally(CompletableFuture.java:1977)
		at io.lettuce.core.AbstractRedisClient.lambda$initializeChannelAsync0$4(AbstractRedisClient.java:329)
		at io.netty.util.concurrent.DefaultPromise.notifyListener0(DefaultPromise.java:577)
		at io.netty.util.concurrent.DefaultPromise.notifyListeners0(DefaultPromise.java:570)
		at io.netty.util.concurrent.DefaultPromise.notifyListenersNow(DefaultPromise.java:549)
		at io.netty.util.concurrent.DefaultPromise.notifyListeners(DefaultPromise.java:490)
		at io.netty.util.concurrent.DefaultPromise.setValue0(DefaultPromise.java:615)
		at io.netty.util.concurrent.DefaultPromise.setFailure0(DefaultPromise.java:608)
		at io.netty.util.concurrent.DefaultPromise.tryFailure(DefaultPromise.java:117)
		at io.netty.channel.nio.AbstractNioChannel$AbstractNioUnsafe$1.run(AbstractNioChannel.java:263)
		at io.netty.util.concurrent.PromiseTask.runTask(PromiseTask.java:98)
		at io.netty.util.concurrent.ScheduledFutureTask.run(ScheduledFutureTask.java:150)
		at io.netty.util.concurrent.AbstractEventExecutor.safeExecute(AbstractEventExecutor.java:163)
		at io.netty.util.concurrent.SingleThreadEventExecutor.runAllTasks(SingleThreadEventExecutor.java:510)
		at io.netty.channel.nio.NioEventLoop.run(NioEventLoop.java:518)
		... 4 common frames omitted
	Caused by: io.netty.channel.ConnectTimeoutException: connection timed out: /************:7006
		at io.netty.channel.nio.AbstractNioChannel$AbstractNioUnsafe$1.run(AbstractNioChannel.java:261)
		... 9 common frames omitted
	Suppressed: io.lettuce.core.RedisConnectionException: Unable to connect to [************:7005]: connection timed out: /************:7005
		at io.lettuce.core.cluster.topology.ClusterTopologyRefresh.lambda$getConnections$0(ClusterTopologyRefresh.java:290)
		at java.util.concurrent.CompletableFuture.uniWhenComplete(CompletableFuture.java:760)
		at java.util.concurrent.CompletableFuture$UniWhenComplete.tryFire(CompletableFuture.java:736)
		at java.util.concurrent.CompletableFuture.postComplete(CompletableFuture.java:474)
		at java.util.concurrent.CompletableFuture.completeExceptionally(CompletableFuture.java:1977)
		at io.lettuce.core.AbstractRedisClient.lambda$initializeChannelAsync0$4(AbstractRedisClient.java:329)
		at io.netty.util.concurrent.DefaultPromise.notifyListener0(DefaultPromise.java:577)
		at io.netty.util.concurrent.DefaultPromise.notifyListeners0(DefaultPromise.java:570)
		at io.netty.util.concurrent.DefaultPromise.notifyListenersNow(DefaultPromise.java:549)
		at io.netty.util.concurrent.DefaultPromise.notifyListeners(DefaultPromise.java:490)
		at io.netty.util.concurrent.DefaultPromise.setValue0(DefaultPromise.java:615)
		at io.netty.util.concurrent.DefaultPromise.setFailure0(DefaultPromise.java:608)
		at io.netty.util.concurrent.DefaultPromise.tryFailure(DefaultPromise.java:117)
		at io.netty.channel.nio.AbstractNioChannel$AbstractNioUnsafe$1.run(AbstractNioChannel.java:263)
		at io.netty.util.concurrent.PromiseTask.runTask(PromiseTask.java:98)
		at io.netty.util.concurrent.ScheduledFutureTask.run(ScheduledFutureTask.java:150)
		at io.netty.util.concurrent.AbstractEventExecutor.safeExecute(AbstractEventExecutor.java:163)
		at io.netty.util.concurrent.SingleThreadEventExecutor.runAllTasks(SingleThreadEventExecutor.java:510)
		at io.netty.channel.nio.NioEventLoop.run(NioEventLoop.java:518)
		... 4 common frames omitted
	Caused by: io.netty.channel.ConnectTimeoutException: connection timed out: /************:7005
		at io.netty.channel.nio.AbstractNioChannel$AbstractNioUnsafe$1.run(AbstractNioChannel.java:261)
		... 9 common frames omitted
	Suppressed: io.lettuce.core.RedisConnectionException: Unable to connect to [************:7001]: connection timed out: /************:7001
		at io.lettuce.core.cluster.topology.ClusterTopologyRefresh.lambda$getConnections$0(ClusterTopologyRefresh.java:290)
		at java.util.concurrent.CompletableFuture.uniWhenComplete(CompletableFuture.java:760)
		at java.util.concurrent.CompletableFuture$UniWhenComplete.tryFire(CompletableFuture.java:736)
		at java.util.concurrent.CompletableFuture.postComplete(CompletableFuture.java:474)
		at java.util.concurrent.CompletableFuture.completeExceptionally(CompletableFuture.java:1977)
		at io.lettuce.core.AbstractRedisClient.lambda$initializeChannelAsync0$4(AbstractRedisClient.java:329)
		at io.netty.util.concurrent.DefaultPromise.notifyListener0(DefaultPromise.java:577)
		at io.netty.util.concurrent.DefaultPromise.notifyListeners0(DefaultPromise.java:570)
		at io.netty.util.concurrent.DefaultPromise.notifyListenersNow(DefaultPromise.java:549)
		at io.netty.util.concurrent.DefaultPromise.notifyListeners(DefaultPromise.java:490)
		at io.netty.util.concurrent.DefaultPromise.setValue0(DefaultPromise.java:615)
		at io.netty.util.concurrent.DefaultPromise.setFailure0(DefaultPromise.java:608)
		at io.netty.util.concurrent.DefaultPromise.tryFailure(DefaultPromise.java:117)
		at io.netty.channel.nio.AbstractNioChannel$AbstractNioUnsafe$1.run(AbstractNioChannel.java:263)
		at io.netty.util.concurrent.PromiseTask.runTask(PromiseTask.java:98)
		at io.netty.util.concurrent.ScheduledFutureTask.run(ScheduledFutureTask.java:150)
		at io.netty.util.concurrent.AbstractEventExecutor.safeExecute(AbstractEventExecutor.java:163)
		at io.netty.util.concurrent.SingleThreadEventExecutor.runAllTasks(SingleThreadEventExecutor.java:510)
		at io.netty.channel.nio.NioEventLoop.run(NioEventLoop.java:518)
		... 4 common frames omitted
	Caused by: io.netty.channel.ConnectTimeoutException: connection timed out: /************:7001
		at io.netty.channel.nio.AbstractNioChannel$AbstractNioUnsafe$1.run(AbstractNioChannel.java:261)
		... 9 common frames omitted
	Suppressed: io.lettuce.core.RedisConnectionException: Unable to connect to [************:7002]: connection timed out: /************:7002
		at io.lettuce.core.cluster.topology.ClusterTopologyRefresh.lambda$getConnections$0(ClusterTopologyRefresh.java:290)
		at java.util.concurrent.CompletableFuture.uniWhenComplete(CompletableFuture.java:760)
		at java.util.concurrent.CompletableFuture$UniWhenComplete.tryFire(CompletableFuture.java:736)
		at java.util.concurrent.CompletableFuture.postComplete(CompletableFuture.java:474)
		at java.util.concurrent.CompletableFuture.completeExceptionally(CompletableFuture.java:1977)
		at io.lettuce.core.AbstractRedisClient.lambda$initializeChannelAsync0$4(AbstractRedisClient.java:329)
		at io.netty.util.concurrent.DefaultPromise.notifyListener0(DefaultPromise.java:577)
		at io.netty.util.concurrent.DefaultPromise.notifyListeners0(DefaultPromise.java:570)
		at io.netty.util.concurrent.DefaultPromise.notifyListenersNow(DefaultPromise.java:549)
		at io.netty.util.concurrent.DefaultPromise.notifyListeners(DefaultPromise.java:490)
		at io.netty.util.concurrent.DefaultPromise.setValue0(DefaultPromise.java:615)
		at io.netty.util.concurrent.DefaultPromise.setFailure0(DefaultPromise.java:608)
		at io.netty.util.concurrent.DefaultPromise.tryFailure(DefaultPromise.java:117)
		at io.netty.channel.nio.AbstractNioChannel$AbstractNioUnsafe$1.run(AbstractNioChannel.java:263)
		at io.netty.util.concurrent.PromiseTask.runTask(PromiseTask.java:98)
		at io.netty.util.concurrent.ScheduledFutureTask.run(ScheduledFutureTask.java:150)
		at io.netty.util.concurrent.AbstractEventExecutor.safeExecute(AbstractEventExecutor.java:163)
		at io.netty.util.concurrent.SingleThreadEventExecutor.runAllTasks(SingleThreadEventExecutor.java:510)
		at io.netty.channel.nio.NioEventLoop.run(NioEventLoop.java:518)
		... 4 common frames omitted
	Caused by: io.netty.channel.ConnectTimeoutException: connection timed out: /************:7002
		at io.netty.channel.nio.AbstractNioChannel$AbstractNioUnsafe$1.run(AbstractNioChannel.java:261)
		... 9 common frames omitted
	Suppressed: io.lettuce.core.RedisConnectionException: Unable to connect to [************:7003]: connection timed out: /************:7003
		at io.lettuce.core.cluster.topology.ClusterTopologyRefresh.lambda$getConnections$0(ClusterTopologyRefresh.java:290)
		at java.util.concurrent.CompletableFuture.uniWhenComplete(CompletableFuture.java:760)
		at java.util.concurrent.CompletableFuture$UniWhenComplete.tryFire(CompletableFuture.java:736)
		at java.util.concurrent.CompletableFuture.postComplete(CompletableFuture.java:474)
		at java.util.concurrent.CompletableFuture.completeExceptionally(CompletableFuture.java:1977)
		at io.lettuce.core.AbstractRedisClient.lambda$initializeChannelAsync0$4(AbstractRedisClient.java:329)
		at io.netty.util.concurrent.DefaultPromise.notifyListener0(DefaultPromise.java:577)
		at io.netty.util.concurrent.DefaultPromise.notifyListeners0(DefaultPromise.java:570)
		at io.netty.util.concurrent.DefaultPromise.notifyListenersNow(DefaultPromise.java:549)
		at io.netty.util.concurrent.DefaultPromise.notifyListeners(DefaultPromise.java:490)
		at io.netty.util.concurrent.DefaultPromise.setValue0(DefaultPromise.java:615)
		at io.netty.util.concurrent.DefaultPromise.setFailure0(DefaultPromise.java:608)
		at io.netty.util.concurrent.DefaultPromise.tryFailure(DefaultPromise.java:117)
		at io.netty.channel.nio.AbstractNioChannel$AbstractNioUnsafe$1.run(AbstractNioChannel.java:263)
		at io.netty.util.concurrent.PromiseTask.runTask(PromiseTask.java:98)
		at io.netty.util.concurrent.ScheduledFutureTask.run(ScheduledFutureTask.java:150)
		at io.netty.util.concurrent.AbstractEventExecutor.safeExecute(AbstractEventExecutor.java:163)
		at io.netty.util.concurrent.SingleThreadEventExecutor.runAllTasks(SingleThreadEventExecutor.java:510)
		at io.netty.channel.nio.NioEventLoop.run(NioEventLoop.java:518)
		... 4 common frames omitted
	Caused by: io.netty.channel.ConnectTimeoutException: connection timed out: /************:7003
		at io.netty.channel.nio.AbstractNioChannel$AbstractNioUnsafe$1.run(AbstractNioChannel.java:261)
		... 9 common frames omitted
Caused by: io.lettuce.core.RedisConnectionException: Unable to connect to [************:7004]: connection timed out: /************:7004
	at io.lettuce.core.cluster.topology.ClusterTopologyRefresh.lambda$getConnections$0(ClusterTopologyRefresh.java:290)
	at java.util.concurrent.CompletableFuture.uniWhenComplete(CompletableFuture.java:760)
	at java.util.concurrent.CompletableFuture$UniWhenComplete.tryFire(CompletableFuture.java:736)
	at java.util.concurrent.CompletableFuture.postComplete(CompletableFuture.java:474)
	at java.util.concurrent.CompletableFuture.completeExceptionally(CompletableFuture.java:1977)
	at io.lettuce.core.AbstractRedisClient.lambda$initializeChannelAsync0$4(AbstractRedisClient.java:329)
	at io.netty.util.concurrent.DefaultPromise.notifyListener0(DefaultPromise.java:577)
	at io.netty.util.concurrent.DefaultPromise.notifyListeners0(DefaultPromise.java:570)
	at io.netty.util.concurrent.DefaultPromise.notifyListenersNow(DefaultPromise.java:549)
	at io.netty.util.concurrent.DefaultPromise.notifyListeners(DefaultPromise.java:490)
	at io.netty.util.concurrent.DefaultPromise.setValue0(DefaultPromise.java:615)
	at io.netty.util.concurrent.DefaultPromise.setFailure0(DefaultPromise.java:608)
	at io.netty.util.concurrent.DefaultPromise.tryFailure(DefaultPromise.java:117)
	at io.netty.channel.nio.AbstractNioChannel$AbstractNioUnsafe$1.run(AbstractNioChannel.java:263)
	at io.netty.util.concurrent.PromiseTask.runTask(PromiseTask.java:98)
	at io.netty.util.concurrent.ScheduledFutureTask.run(ScheduledFutureTask.java:150)
	at io.netty.util.concurrent.AbstractEventExecutor.safeExecute(AbstractEventExecutor.java:163)
	at io.netty.util.concurrent.SingleThreadEventExecutor.runAllTasks(SingleThreadEventExecutor.java:510)
	at io.netty.channel.nio.NioEventLoop.run(NioEventLoop.java:518)
	... 4 common frames omitted
Caused by: io.netty.channel.ConnectTimeoutException: connection timed out: /************:7004
	at io.netty.channel.nio.AbstractNioChannel$AbstractNioUnsafe$1.run(AbstractNioChannel.java:261)
	... 9 common frames omitted
2025-06-01 00:31:25.198 [AMQP Connection ************:5672] WARN  com.rabbitmq.client.impl.ForgivingExceptionHandler.log Line:115 - An unexpected connection driver error occured (Exception message: Connection reset)
2025-06-01 00:31:26.143 [AMQP Connection ************:5672] WARN  com.rabbitmq.client.impl.ForgivingExceptionHandler.log Line:115 - An unexpected connection driver error occured (Exception message: Connection reset)
2025-06-01 00:31:27.014 [AMQP Connection ************:5672] WARN  com.rabbitmq.client.impl.ForgivingExceptionHandler.log Line:115 - An unexpected connection driver error occured (Exception message: Connection reset)
2025-06-01 00:31:27.608 [AMQP Connection ************:5672] WARN  com.rabbitmq.client.impl.ForgivingExceptionHandler.log Line:115 - An unexpected connection driver error occured (Exception message: Connection reset)
2025-06-01 00:45:09.283 [pool-14-thread-10] WARN  com.simbest.boot.base.repository.CustomDynamicWhere.queryForList Line:50 - 自定义查询SQL输出为：【select * from US_TODO_MODEL t  where t.send_flag = 0 and t.work_flag = 1 and t.enabled =1】
2025-06-01 00:50:00.009 [pool-14-thread-8] WARN  com.simbest.boot.base.repository.CustomDynamicWhere.queryForList Line:50 - 自定义查询SQL输出为：【select * from US_TODO_MODEL t  where t.send_flag = 0 and t.work_flag = 1 and t.enabled =1】
2025-06-01 00:50:00.009 [pool-14-thread-5] WARN  com.simbest.boot.base.repository.CustomDynamicWhere.queryForList Line:50 - 自定义查询SQL输出为：【select * from US_TODO_MODEL t  where t.send_flag = 0 and t.work_flag = 0 and t.enabled =1】
2025-06-01 01:11:56.841 [AMQP Connection ************:5672] WARN  com.rabbitmq.client.impl.ForgivingExceptionHandler.log Line:115 - An unexpected connection driver error occured (Exception message: Connection reset)
2025-06-01 01:11:57.806 [AMQP Connection ************:5672] WARN  com.rabbitmq.client.impl.ForgivingExceptionHandler.log Line:115 - An unexpected connection driver error occured (Exception message: Connection reset)
2025-06-01 01:11:58.740 [AMQP Connection ************:5672] WARN  com.rabbitmq.client.impl.ForgivingExceptionHandler.log Line:115 - An unexpected connection driver error occured (Exception message: Connection reset)
2025-06-01 01:11:59.313 [AMQP Connection ************:5672] WARN  com.rabbitmq.client.impl.ForgivingExceptionHandler.log Line:115 - An unexpected connection driver error occured (Exception message: Connection reset)
2025-06-01 01:15:03.049 [pool-14-thread-7] WARN  com.simbest.boot.base.repository.CustomDynamicWhere.queryForList Line:50 - 自定义查询SQL输出为：【select * from US_TODO_MODEL t  where t.send_flag = 0 and t.work_flag = 0 and t.enabled =1】
2025-06-01 01:20:00.018 [pool-14-thread-6] WARN  com.simbest.boot.base.repository.CustomDynamicWhere.queryForList Line:50 - 自定义查询SQL输出为：【select * from US_TODO_MODEL t  where t.send_flag = 0 and t.work_flag = 0 and t.enabled =1】
2025-06-01 01:20:09.246 [pool-14-thread-5] WARN  com.simbest.boot.base.repository.CustomDynamicWhere.queryForList Line:50 - 自定义查询SQL输出为：【select * from US_TODO_MODEL t  where t.send_flag = 0 and t.work_flag = 1 and t.enabled =1】
2025-06-01 01:25:00.008 [pool-14-thread-8] WARN  com.simbest.boot.base.repository.CustomDynamicWhere.queryForList Line:50 - 自定义查询SQL输出为：【select * from US_TODO_MODEL t  where t.send_flag = 0 and t.work_flag = 0 and t.enabled =1】
2025-06-01 01:25:00.008 [pool-14-thread-7] WARN  com.simbest.boot.base.repository.CustomDynamicWhere.queryForList Line:50 - 自定义查询SQL输出为：【select * from US_TODO_MODEL t  where t.send_flag = 0 and t.work_flag = 1 and t.enabled =1】
2025-06-01 01:30:00.019 [pool-14-thread-1] WARN  com.simbest.boot.base.repository.CustomDynamicWhere.queryForList Line:50 - 自定义查询SQL输出为：【select * from US_TODO_MODEL t  where t.send_flag = 0 and t.work_flag = 0 and t.enabled =1】
2025-06-01 01:30:00.020 [pool-14-thread-5] WARN  com.simbest.boot.base.repository.CustomDynamicWhere.queryForList Line:50 - 自定义查询SQL输出为：【select * from US_TODO_MODEL t  where t.send_flag = 0 and t.work_flag = 1 and t.enabled =1】
2025-06-01 01:52:57.883 [AMQP Connection ************:5672] WARN  com.rabbitmq.client.impl.ForgivingExceptionHandler.log Line:115 - An unexpected connection driver error occured (Exception message: Connection reset)
2025-06-01 01:52:58.786 [AMQP Connection ************:5672] WARN  com.rabbitmq.client.impl.ForgivingExceptionHandler.log Line:115 - An unexpected connection driver error occured (Exception message: Connection reset)
2025-06-01 01:52:59.816 [AMQP Connection ************:5672] WARN  com.rabbitmq.client.impl.ForgivingExceptionHandler.log Line:115 - An unexpected connection driver error occured (Exception message: Connection reset)
2025-06-01 01:53:00.314 [AMQP Connection ************:5672] WARN  com.rabbitmq.client.impl.ForgivingExceptionHandler.log Line:115 - An unexpected connection driver error occured (Exception message: Connection reset)
2025-06-01 02:32:54.538 [lettuce-nioEventLoop-4-1] WARN  io.lettuce.core.cluster.topology.ClusterTopologyRefresh.lambda$getConnections$0 Line:284 - Unable to connect to [************:7002]: No route to host: no further information: /************:7002
2025-06-01 02:32:54.538 [lettuce-nioEventLoop-4-4] WARN  io.lettuce.core.cluster.topology.ClusterTopologyRefresh.lambda$getConnections$0 Line:284 - Unable to connect to [************:7005]: No route to host: no further information: /************:7005
2025-06-01 02:32:54.538 [lettuce-nioEventLoop-4-12] WARN  io.lettuce.core.cluster.topology.ClusterTopologyRefresh.lambda$getConnections$0 Line:284 - Unable to connect to [************:7001]: No route to host: no further information: /************:7001
2025-06-01 02:32:54.538 [lettuce-nioEventLoop-4-3] WARN  io.lettuce.core.cluster.topology.ClusterTopologyRefresh.lambda$getConnections$0 Line:284 - Unable to connect to [************:7004]: No route to host: no further information: /************:7004
2025-06-01 02:32:54.538 [lettuce-nioEventLoop-4-5] WARN  io.lettuce.core.cluster.topology.ClusterTopologyRefresh.lambda$getConnections$0 Line:284 - Unable to connect to [************:7006]: No route to host: no further information: /************:7006
2025-06-01 02:32:54.539 [lettuce-nioEventLoop-4-2] WARN  io.lettuce.core.cluster.topology.ClusterTopologyRefresh.lambda$getConnections$0 Line:284 - Unable to connect to [************:7003]: No route to host: no further information: /************:7003
2025-06-01 02:32:54.543 [lettuce-nioEventLoop-4-7] WARN  io.lettuce.core.cluster.topology.ClusterTopologyRefresh.lambda$getConnections$0 Line:284 - Unable to connect to [************:7004]: No route to host: no further information: /************:7004
2025-06-01 02:32:54.543 [lettuce-nioEventLoop-4-6] WARN  io.lettuce.core.cluster.topology.ClusterTopologyRefresh.lambda$getConnections$0 Line:284 - Unable to connect to [************:7006]: No route to host: no further information: /************:7006
2025-06-01 02:32:54.543 [lettuce-nioEventLoop-4-9] WARN  io.lettuce.core.cluster.topology.ClusterTopologyRefresh.lambda$getConnections$0 Line:284 - Unable to connect to [************:7002]: No route to host: no further information: /************:7002
2025-06-01 02:32:54.543 [lettuce-nioEventLoop-4-8] WARN  io.lettuce.core.cluster.topology.ClusterTopologyRefresh.lambda$getConnections$0 Line:284 - Unable to connect to [************:7005]: No route to host: no further information: /************:7005
2025-06-01 02:32:54.543 [lettuce-nioEventLoop-4-10] WARN  io.lettuce.core.cluster.topology.ClusterTopologyRefresh.lambda$getConnections$0 Line:284 - Unable to connect to [************:7003]: No route to host: no further information: /************:7003
2025-06-01 02:32:54.543 [lettuce-nioEventLoop-4-11] WARN  io.lettuce.core.cluster.topology.ClusterTopologyRefresh.lambda$getConnections$0 Line:284 - Unable to connect to [************:7001]: No route to host: no further information: /************:7001
2025-06-01 02:32:54.562 [lettuce-eventExecutorLoop-1-2] WARN  io.lettuce.core.cluster.ClusterTopologyRefreshScheduler.doRun Line:258 - Cannot refresh Redis Cluster topology
io.lettuce.core.RedisException: Cannot retrieve initial cluster partitions from initial URIs [RedisURI [host='************', port=7006], RedisURI [host='************', port=7004], RedisURI [host='************', port=7005], RedisURI [host='************', port=7002], RedisURI [host='************', port=7003], RedisURI [host='************', port=7001]]
	at io.lettuce.core.cluster.RedisClusterClient.loadPartitions(RedisClusterClient.java:848)
	at io.lettuce.core.cluster.RedisClusterClient.reloadPartitions(RedisClusterClient.java:784)
	at io.lettuce.core.cluster.ClusterTopologyRefreshScheduler$ClusterTopologyRefreshTask.doRun(ClusterTopologyRefreshScheduler.java:256)
	at io.lettuce.core.cluster.ClusterTopologyRefreshScheduler$ClusterTopologyRefreshTask.run(ClusterTopologyRefreshScheduler.java:236)
	at io.netty.util.concurrent.PromiseTask.runTask(PromiseTask.java:98)
	at io.netty.util.concurrent.PromiseTask.run(PromiseTask.java:106)
	at io.netty.util.concurrent.DefaultEventExecutor.run(DefaultEventExecutor.java:66)
	at io.netty.util.concurrent.SingleThreadEventExecutor$6.run(SingleThreadEventExecutor.java:1050)
	at io.netty.util.internal.ThreadExecutorMap$2.run(ThreadExecutorMap.java:74)
	at io.netty.util.concurrent.FastThreadLocalRunnable.run(FastThreadLocalRunnable.java:30)
	at java.lang.Thread.run(Thread.java:748)
	Suppressed: io.lettuce.core.RedisConnectionException: Unable to establish a connection to Redis Cluster at [RedisURI [host='************', port=7001], RedisURI [host='************', port=7002], RedisURI [host='************', port=7003], RedisURI [host='************', port=7004], RedisURI [host='************', port=7005], RedisURI [host='************', port=7006]]
		at io.lettuce.core.cluster.topology.AsyncConnections.get(AsyncConnections.java:89)
		at io.lettuce.core.cluster.topology.ClusterTopologyRefresh.loadViews(ClusterTopologyRefresh.java:76)
		at io.lettuce.core.cluster.RedisClusterClient.doLoadPartitions(RedisClusterClient.java:865)
		at io.lettuce.core.cluster.RedisClusterClient.loadPartitions(RedisClusterClient.java:838)
		... 10 common frames omitted
		Suppressed: io.lettuce.core.RedisConnectionException: Unable to connect to [************:7005]: No route to host: no further information: /************:7005
			at io.lettuce.core.cluster.topology.ClusterTopologyRefresh.lambda$getConnections$0(ClusterTopologyRefresh.java:290)
			at java.util.concurrent.CompletableFuture.uniWhenComplete(CompletableFuture.java:760)
			at java.util.concurrent.CompletableFuture$UniWhenComplete.tryFire(CompletableFuture.java:736)
			at java.util.concurrent.CompletableFuture.postComplete(CompletableFuture.java:474)
			at java.util.concurrent.CompletableFuture.completeExceptionally(CompletableFuture.java:1977)
			at io.lettuce.core.AbstractRedisClient.lambda$initializeChannelAsync0$4(AbstractRedisClient.java:329)
			at io.netty.util.concurrent.DefaultPromise.notifyListener0(DefaultPromise.java:577)
			at io.netty.util.concurrent.DefaultPromise.notifyListeners0(DefaultPromise.java:570)
			at io.netty.util.concurrent.DefaultPromise.notifyListenersNow(DefaultPromise.java:549)
			at io.netty.util.concurrent.DefaultPromise.notifyListeners(DefaultPromise.java:490)
			at io.netty.util.concurrent.DefaultPromise.setValue0(DefaultPromise.java:615)
			at io.netty.util.concurrent.DefaultPromise.setFailure0(DefaultPromise.java:608)
			at io.netty.util.concurrent.DefaultPromise.tryFailure(DefaultPromise.java:117)
			at io.netty.channel.nio.AbstractNioChannel$AbstractNioUnsafe.fulfillConnectPromise(AbstractNioChannel.java:321)
			at io.netty.channel.nio.AbstractNioChannel$AbstractNioUnsafe.finishConnect(AbstractNioChannel.java:337)
			at io.netty.channel.nio.NioEventLoop.processSelectedKey(NioEventLoop.java:688)
			at io.netty.channel.nio.NioEventLoop.processSelectedKeysOptimized(NioEventLoop.java:635)
			at io.netty.channel.nio.NioEventLoop.processSelectedKeys(NioEventLoop.java:552)
			at io.netty.channel.nio.NioEventLoop.run(NioEventLoop.java:514)
			... 4 common frames omitted
		Caused by: io.netty.channel.AbstractChannel$AnnotatedNoRouteToHostException: No route to host: no further information: /************:7005
		Caused by: java.net.NoRouteToHostException: No route to host: no further information
			at sun.nio.ch.SocketChannelImpl.checkConnect(Native Method)
			at sun.nio.ch.SocketChannelImpl.finishConnect(SocketChannelImpl.java:717)
			at io.netty.channel.socket.nio.NioSocketChannel.doFinishConnect(NioSocketChannel.java:330)
			at io.netty.channel.nio.AbstractNioChannel$AbstractNioUnsafe.finishConnect(AbstractNioChannel.java:334)
			at io.netty.channel.nio.NioEventLoop.processSelectedKey(NioEventLoop.java:688)
			at io.netty.channel.nio.NioEventLoop.processSelectedKeysOptimized(NioEventLoop.java:635)
			at io.netty.channel.nio.NioEventLoop.processSelectedKeys(NioEventLoop.java:552)
			at io.netty.channel.nio.NioEventLoop.run(NioEventLoop.java:514)
			at io.netty.util.concurrent.SingleThreadEventExecutor$6.run(SingleThreadEventExecutor.java:1050)
			at io.netty.util.internal.ThreadExecutorMap$2.run(ThreadExecutorMap.java:74)
			at io.netty.util.concurrent.FastThreadLocalRunnable.run(FastThreadLocalRunnable.java:30)
			at java.lang.Thread.run(Thread.java:748)
		Suppressed: io.lettuce.core.RedisConnectionException: Unable to connect to [************:7001]: No route to host: no further information: /************:7001
			at io.lettuce.core.cluster.topology.ClusterTopologyRefresh.lambda$getConnections$0(ClusterTopologyRefresh.java:290)
			at java.util.concurrent.CompletableFuture.uniWhenComplete(CompletableFuture.java:760)
			at java.util.concurrent.CompletableFuture$UniWhenComplete.tryFire(CompletableFuture.java:736)
			at java.util.concurrent.CompletableFuture.postComplete(CompletableFuture.java:474)
			at java.util.concurrent.CompletableFuture.completeExceptionally(CompletableFuture.java:1977)
			at io.lettuce.core.AbstractRedisClient.lambda$initializeChannelAsync0$4(AbstractRedisClient.java:329)
			at io.netty.util.concurrent.DefaultPromise.notifyListener0(DefaultPromise.java:577)
			at io.netty.util.concurrent.DefaultPromise.notifyListeners0(DefaultPromise.java:570)
			at io.netty.util.concurrent.DefaultPromise.notifyListenersNow(DefaultPromise.java:549)
			at io.netty.util.concurrent.DefaultPromise.notifyListeners(DefaultPromise.java:490)
			at io.netty.util.concurrent.DefaultPromise.setValue0(DefaultPromise.java:615)
			at io.netty.util.concurrent.DefaultPromise.setFailure0(DefaultPromise.java:608)
			at io.netty.util.concurrent.DefaultPromise.tryFailure(DefaultPromise.java:117)
			at io.netty.channel.nio.AbstractNioChannel$AbstractNioUnsafe.fulfillConnectPromise(AbstractNioChannel.java:321)
			at io.netty.channel.nio.AbstractNioChannel$AbstractNioUnsafe.finishConnect(AbstractNioChannel.java:337)
			at io.netty.channel.nio.NioEventLoop.processSelectedKey(NioEventLoop.java:688)
			at io.netty.channel.nio.NioEventLoop.processSelectedKeysOptimized(NioEventLoop.java:635)
			at io.netty.channel.nio.NioEventLoop.processSelectedKeys(NioEventLoop.java:552)
			at io.netty.channel.nio.NioEventLoop.run(NioEventLoop.java:514)
			... 4 common frames omitted
		Caused by: io.netty.channel.AbstractChannel$AnnotatedNoRouteToHostException: No route to host: no further information: /************:7001
		Caused by: java.net.NoRouteToHostException: No route to host: no further information
			at sun.nio.ch.SocketChannelImpl.checkConnect(Native Method)
			at sun.nio.ch.SocketChannelImpl.finishConnect(SocketChannelImpl.java:717)
			at io.netty.channel.socket.nio.NioSocketChannel.doFinishConnect(NioSocketChannel.java:330)
			at io.netty.channel.nio.AbstractNioChannel$AbstractNioUnsafe.finishConnect(AbstractNioChannel.java:334)
			at io.netty.channel.nio.NioEventLoop.processSelectedKey(NioEventLoop.java:688)
			at io.netty.channel.nio.NioEventLoop.processSelectedKeysOptimized(NioEventLoop.java:635)
			at io.netty.channel.nio.NioEventLoop.processSelectedKeys(NioEventLoop.java:552)
			at io.netty.channel.nio.NioEventLoop.run(NioEventLoop.java:514)
			at io.netty.util.concurrent.SingleThreadEventExecutor$6.run(SingleThreadEventExecutor.java:1050)
			at io.netty.util.internal.ThreadExecutorMap$2.run(ThreadExecutorMap.java:74)
			at io.netty.util.concurrent.FastThreadLocalRunnable.run(FastThreadLocalRunnable.java:30)
			at java.lang.Thread.run(Thread.java:748)
		Suppressed: io.lettuce.core.RedisConnectionException: Unable to connect to [************:7004]: No route to host: no further information: /************:7004
			at io.lettuce.core.cluster.topology.ClusterTopologyRefresh.lambda$getConnections$0(ClusterTopologyRefresh.java:290)
			at java.util.concurrent.CompletableFuture.uniWhenComplete(CompletableFuture.java:760)
			at java.util.concurrent.CompletableFuture$UniWhenComplete.tryFire(CompletableFuture.java:736)
			at java.util.concurrent.CompletableFuture.postComplete(CompletableFuture.java:474)
			at java.util.concurrent.CompletableFuture.completeExceptionally(CompletableFuture.java:1977)
			at io.lettuce.core.AbstractRedisClient.lambda$initializeChannelAsync0$4(AbstractRedisClient.java:329)
			at io.netty.util.concurrent.DefaultPromise.notifyListener0(DefaultPromise.java:577)
			at io.netty.util.concurrent.DefaultPromise.notifyListeners0(DefaultPromise.java:570)
			at io.netty.util.concurrent.DefaultPromise.notifyListenersNow(DefaultPromise.java:549)
			at io.netty.util.concurrent.DefaultPromise.notifyListeners(DefaultPromise.java:490)
			at io.netty.util.concurrent.DefaultPromise.setValue0(DefaultPromise.java:615)
			at io.netty.util.concurrent.DefaultPromise.setFailure0(DefaultPromise.java:608)
			at io.netty.util.concurrent.DefaultPromise.tryFailure(DefaultPromise.java:117)
			at io.netty.channel.nio.AbstractNioChannel$AbstractNioUnsafe.fulfillConnectPromise(AbstractNioChannel.java:321)
			at io.netty.channel.nio.AbstractNioChannel$AbstractNioUnsafe.finishConnect(AbstractNioChannel.java:337)
			at io.netty.channel.nio.NioEventLoop.processSelectedKey(NioEventLoop.java:688)
			at io.netty.channel.nio.NioEventLoop.processSelectedKeysOptimized(NioEventLoop.java:635)
			at io.netty.channel.nio.NioEventLoop.processSelectedKeys(NioEventLoop.java:552)
			at io.netty.channel.nio.NioEventLoop.run(NioEventLoop.java:514)
			... 4 common frames omitted
		Caused by: io.netty.channel.AbstractChannel$AnnotatedNoRouteToHostException: No route to host: no further information: /************:7004
		Caused by: java.net.NoRouteToHostException: No route to host: no further information
			at sun.nio.ch.SocketChannelImpl.checkConnect(Native Method)
			at sun.nio.ch.SocketChannelImpl.finishConnect(SocketChannelImpl.java:717)
			at io.netty.channel.socket.nio.NioSocketChannel.doFinishConnect(NioSocketChannel.java:330)
			at io.netty.channel.nio.AbstractNioChannel$AbstractNioUnsafe.finishConnect(AbstractNioChannel.java:334)
			at io.netty.channel.nio.NioEventLoop.processSelectedKey(NioEventLoop.java:688)
			at io.netty.channel.nio.NioEventLoop.processSelectedKeysOptimized(NioEventLoop.java:635)
			at io.netty.channel.nio.NioEventLoop.processSelectedKeys(NioEventLoop.java:552)
			at io.netty.channel.nio.NioEventLoop.run(NioEventLoop.java:514)
			at io.netty.util.concurrent.SingleThreadEventExecutor$6.run(SingleThreadEventExecutor.java:1050)
			at io.netty.util.internal.ThreadExecutorMap$2.run(ThreadExecutorMap.java:74)
			at io.netty.util.concurrent.FastThreadLocalRunnable.run(FastThreadLocalRunnable.java:30)
			at java.lang.Thread.run(Thread.java:748)
		Suppressed: io.lettuce.core.RedisConnectionException: Unable to connect to [************:7006]: No route to host: no further information: /************:7006
			at io.lettuce.core.cluster.topology.ClusterTopologyRefresh.lambda$getConnections$0(ClusterTopologyRefresh.java:290)
			at java.util.concurrent.CompletableFuture.uniWhenComplete(CompletableFuture.java:760)
			at java.util.concurrent.CompletableFuture$UniWhenComplete.tryFire(CompletableFuture.java:736)
			at java.util.concurrent.CompletableFuture.postComplete(CompletableFuture.java:474)
			at java.util.concurrent.CompletableFuture.completeExceptionally(CompletableFuture.java:1977)
			at io.lettuce.core.AbstractRedisClient.lambda$initializeChannelAsync0$4(AbstractRedisClient.java:329)
			at io.netty.util.concurrent.DefaultPromise.notifyListener0(DefaultPromise.java:577)
			at io.netty.util.concurrent.DefaultPromise.notifyListeners0(DefaultPromise.java:570)
			at io.netty.util.concurrent.DefaultPromise.notifyListenersNow(DefaultPromise.java:549)
			at io.netty.util.concurrent.DefaultPromise.notifyListeners(DefaultPromise.java:490)
			at io.netty.util.concurrent.DefaultPromise.setValue0(DefaultPromise.java:615)
			at io.netty.util.concurrent.DefaultPromise.setFailure0(DefaultPromise.java:608)
			at io.netty.util.concurrent.DefaultPromise.tryFailure(DefaultPromise.java:117)
			at io.netty.channel.nio.AbstractNioChannel$AbstractNioUnsafe.fulfillConnectPromise(AbstractNioChannel.java:321)
			at io.netty.channel.nio.AbstractNioChannel$AbstractNioUnsafe.finishConnect(AbstractNioChannel.java:337)
			at io.netty.channel.nio.NioEventLoop.processSelectedKey(NioEventLoop.java:688)
			at io.netty.channel.nio.NioEventLoop.processSelectedKeysOptimized(NioEventLoop.java:635)
			at io.netty.channel.nio.NioEventLoop.processSelectedKeys(NioEventLoop.java:552)
			at io.netty.channel.nio.NioEventLoop.run(NioEventLoop.java:514)
			... 4 common frames omitted
		Caused by: io.netty.channel.AbstractChannel$AnnotatedNoRouteToHostException: No route to host: no further information: /************:7006
		Caused by: java.net.NoRouteToHostException: No route to host: no further information
			at sun.nio.ch.SocketChannelImpl.checkConnect(Native Method)
			at sun.nio.ch.SocketChannelImpl.finishConnect(SocketChannelImpl.java:717)
			at io.netty.channel.socket.nio.NioSocketChannel.doFinishConnect(NioSocketChannel.java:330)
			at io.netty.channel.nio.AbstractNioChannel$AbstractNioUnsafe.finishConnect(AbstractNioChannel.java:334)
			at io.netty.channel.nio.NioEventLoop.processSelectedKey(NioEventLoop.java:688)
			at io.netty.channel.nio.NioEventLoop.processSelectedKeysOptimized(NioEventLoop.java:635)
			at io.netty.channel.nio.NioEventLoop.processSelectedKeys(NioEventLoop.java:552)
			at io.netty.channel.nio.NioEventLoop.run(NioEventLoop.java:514)
			at io.netty.util.concurrent.SingleThreadEventExecutor$6.run(SingleThreadEventExecutor.java:1050)
			at io.netty.util.internal.ThreadExecutorMap$2.run(ThreadExecutorMap.java:74)
			at io.netty.util.concurrent.FastThreadLocalRunnable.run(FastThreadLocalRunnable.java:30)
			at java.lang.Thread.run(Thread.java:748)
		Suppressed: io.lettuce.core.RedisConnectionException: Unable to connect to [************:7003]: No route to host: no further information: /************:7003
			at io.lettuce.core.cluster.topology.ClusterTopologyRefresh.lambda$getConnections$0(ClusterTopologyRefresh.java:290)
			at java.util.concurrent.CompletableFuture.uniWhenComplete(CompletableFuture.java:760)
			at java.util.concurrent.CompletableFuture$UniWhenComplete.tryFire(CompletableFuture.java:736)
			at java.util.concurrent.CompletableFuture.postComplete(CompletableFuture.java:474)
			at java.util.concurrent.CompletableFuture.completeExceptionally(CompletableFuture.java:1977)
			at io.lettuce.core.AbstractRedisClient.lambda$initializeChannelAsync0$4(AbstractRedisClient.java:329)
			at io.netty.util.concurrent.DefaultPromise.notifyListener0(DefaultPromise.java:577)
			at io.netty.util.concurrent.DefaultPromise.notifyListeners0(DefaultPromise.java:570)
			at io.netty.util.concurrent.DefaultPromise.notifyListenersNow(DefaultPromise.java:549)
			at io.netty.util.concurrent.DefaultPromise.notifyListeners(DefaultPromise.java:490)
			at io.netty.util.concurrent.DefaultPromise.setValue0(DefaultPromise.java:615)
			at io.netty.util.concurrent.DefaultPromise.setFailure0(DefaultPromise.java:608)
			at io.netty.util.concurrent.DefaultPromise.tryFailure(DefaultPromise.java:117)
			at io.netty.channel.nio.AbstractNioChannel$AbstractNioUnsafe.fulfillConnectPromise(AbstractNioChannel.java:321)
			at io.netty.channel.nio.AbstractNioChannel$AbstractNioUnsafe.finishConnect(AbstractNioChannel.java:337)
			at io.netty.channel.nio.NioEventLoop.processSelectedKey(NioEventLoop.java:688)
			at io.netty.channel.nio.NioEventLoop.processSelectedKeysOptimized(NioEventLoop.java:635)
			at io.netty.channel.nio.NioEventLoop.processSelectedKeys(NioEventLoop.java:552)
			at io.netty.channel.nio.NioEventLoop.run(NioEventLoop.java:514)
			... 4 common frames omitted
		Caused by: io.netty.channel.AbstractChannel$AnnotatedNoRouteToHostException: No route to host: no further information: /************:7003
		Caused by: java.net.NoRouteToHostException: No route to host: no further information
			at sun.nio.ch.SocketChannelImpl.checkConnect(Native Method)
			at sun.nio.ch.SocketChannelImpl.finishConnect(SocketChannelImpl.java:717)
			at io.netty.channel.socket.nio.NioSocketChannel.doFinishConnect(NioSocketChannel.java:330)
			at io.netty.channel.nio.AbstractNioChannel$AbstractNioUnsafe.finishConnect(AbstractNioChannel.java:334)
			at io.netty.channel.nio.NioEventLoop.processSelectedKey(NioEventLoop.java:688)
			at io.netty.channel.nio.NioEventLoop.processSelectedKeysOptimized(NioEventLoop.java:635)
			at io.netty.channel.nio.NioEventLoop.processSelectedKeys(NioEventLoop.java:552)
			at io.netty.channel.nio.NioEventLoop.run(NioEventLoop.java:514)
			at io.netty.util.concurrent.SingleThreadEventExecutor$6.run(SingleThreadEventExecutor.java:1050)
			at io.netty.util.internal.ThreadExecutorMap$2.run(ThreadExecutorMap.java:74)
			at io.netty.util.concurrent.FastThreadLocalRunnable.run(FastThreadLocalRunnable.java:30)
			at java.lang.Thread.run(Thread.java:748)
	Caused by: io.lettuce.core.RedisConnectionException: Unable to connect to [************:7002]: No route to host: no further information: /************:7002
		at io.lettuce.core.cluster.topology.ClusterTopologyRefresh.lambda$getConnections$0(ClusterTopologyRefresh.java:290)
		at java.util.concurrent.CompletableFuture.uniWhenComplete(CompletableFuture.java:760)
		at java.util.concurrent.CompletableFuture$UniWhenComplete.tryFire(CompletableFuture.java:736)
		at java.util.concurrent.CompletableFuture.postComplete(CompletableFuture.java:474)
		at java.util.concurrent.CompletableFuture.completeExceptionally(CompletableFuture.java:1977)
		at io.lettuce.core.AbstractRedisClient.lambda$initializeChannelAsync0$4(AbstractRedisClient.java:329)
		at io.netty.util.concurrent.DefaultPromise.notifyListener0(DefaultPromise.java:577)
		at io.netty.util.concurrent.DefaultPromise.notifyListeners0(DefaultPromise.java:570)
		at io.netty.util.concurrent.DefaultPromise.notifyListenersNow(DefaultPromise.java:549)
		at io.netty.util.concurrent.DefaultPromise.notifyListeners(DefaultPromise.java:490)
		at io.netty.util.concurrent.DefaultPromise.setValue0(DefaultPromise.java:615)
		at io.netty.util.concurrent.DefaultPromise.setFailure0(DefaultPromise.java:608)
		at io.netty.util.concurrent.DefaultPromise.tryFailure(DefaultPromise.java:117)
		at io.netty.channel.nio.AbstractNioChannel$AbstractNioUnsafe.fulfillConnectPromise(AbstractNioChannel.java:321)
		at io.netty.channel.nio.AbstractNioChannel$AbstractNioUnsafe.finishConnect(AbstractNioChannel.java:337)
		at io.netty.channel.nio.NioEventLoop.processSelectedKey(NioEventLoop.java:688)
		at io.netty.channel.nio.NioEventLoop.processSelectedKeysOptimized(NioEventLoop.java:635)
		at io.netty.channel.nio.NioEventLoop.processSelectedKeys(NioEventLoop.java:552)
		at io.netty.channel.nio.NioEventLoop.run(NioEventLoop.java:514)
		... 4 common frames omitted
	Caused by: io.netty.channel.AbstractChannel$AnnotatedNoRouteToHostException: No route to host: no further information: /************:7002
	Caused by: java.net.NoRouteToHostException: No route to host: no further information
		at sun.nio.ch.SocketChannelImpl.checkConnect(Native Method)
		at sun.nio.ch.SocketChannelImpl.finishConnect(SocketChannelImpl.java:717)
		at io.netty.channel.socket.nio.NioSocketChannel.doFinishConnect(NioSocketChannel.java:330)
		at io.netty.channel.nio.AbstractNioChannel$AbstractNioUnsafe.finishConnect(AbstractNioChannel.java:334)
		at io.netty.channel.nio.NioEventLoop.processSelectedKey(NioEventLoop.java:688)
		at io.netty.channel.nio.NioEventLoop.processSelectedKeysOptimized(NioEventLoop.java:635)
		at io.netty.channel.nio.NioEventLoop.processSelectedKeys(NioEventLoop.java:552)
		at io.netty.channel.nio.NioEventLoop.run(NioEventLoop.java:514)
		at io.netty.util.concurrent.SingleThreadEventExecutor$6.run(SingleThreadEventExecutor.java:1050)
		at io.netty.util.internal.ThreadExecutorMap$2.run(ThreadExecutorMap.java:74)
		at io.netty.util.concurrent.FastThreadLocalRunnable.run(FastThreadLocalRunnable.java:30)
		at java.lang.Thread.run(Thread.java:748)
Caused by: io.lettuce.core.RedisConnectionException: Unable to establish a connection to Redis Cluster at [RedisURI [host='************', port=7001], RedisURI [host='************', port=7002], RedisURI [host='************', port=7003], RedisURI [host='************', port=7004], RedisURI [host='************', port=7005], RedisURI [host='************', port=7006]]
	at io.lettuce.core.cluster.topology.AsyncConnections.get(AsyncConnections.java:89)
	at io.lettuce.core.cluster.topology.ClusterTopologyRefresh.loadViews(ClusterTopologyRefresh.java:76)
	at io.lettuce.core.cluster.RedisClusterClient.doLoadPartitions(RedisClusterClient.java:865)
	at io.lettuce.core.cluster.RedisClusterClient.loadPartitions(RedisClusterClient.java:845)
	... 10 common frames omitted
	Suppressed: io.lettuce.core.RedisConnectionException: Unable to connect to [************:7006]: No route to host: no further information: /************:7006
		at io.lettuce.core.cluster.topology.ClusterTopologyRefresh.lambda$getConnections$0(ClusterTopologyRefresh.java:290)
		at java.util.concurrent.CompletableFuture.uniWhenComplete(CompletableFuture.java:760)
		at java.util.concurrent.CompletableFuture$UniWhenComplete.tryFire(CompletableFuture.java:736)
		at java.util.concurrent.CompletableFuture.postComplete(CompletableFuture.java:474)
		at java.util.concurrent.CompletableFuture.completeExceptionally(CompletableFuture.java:1977)
		at io.lettuce.core.AbstractRedisClient.lambda$initializeChannelAsync0$4(AbstractRedisClient.java:329)
		at io.netty.util.concurrent.DefaultPromise.notifyListener0(DefaultPromise.java:577)
		at io.netty.util.concurrent.DefaultPromise.notifyListeners0(DefaultPromise.java:570)
		at io.netty.util.concurrent.DefaultPromise.notifyListenersNow(DefaultPromise.java:549)
		at io.netty.util.concurrent.DefaultPromise.notifyListeners(DefaultPromise.java:490)
		at io.netty.util.concurrent.DefaultPromise.setValue0(DefaultPromise.java:615)
		at io.netty.util.concurrent.DefaultPromise.setFailure0(DefaultPromise.java:608)
		at io.netty.util.concurrent.DefaultPromise.tryFailure(DefaultPromise.java:117)
		at io.netty.channel.nio.AbstractNioChannel$AbstractNioUnsafe.fulfillConnectPromise(AbstractNioChannel.java:321)
		at io.netty.channel.nio.AbstractNioChannel$AbstractNioUnsafe.finishConnect(AbstractNioChannel.java:337)
		at io.netty.channel.nio.NioEventLoop.processSelectedKey(NioEventLoop.java:688)
		at io.netty.channel.nio.NioEventLoop.processSelectedKeysOptimized(NioEventLoop.java:635)
		at io.netty.channel.nio.NioEventLoop.processSelectedKeys(NioEventLoop.java:552)
		at io.netty.channel.nio.NioEventLoop.run(NioEventLoop.java:514)
		... 4 common frames omitted
	Caused by: io.netty.channel.AbstractChannel$AnnotatedNoRouteToHostException: No route to host: no further information: /************:7006
	Caused by: java.net.NoRouteToHostException: No route to host: no further information
		at sun.nio.ch.SocketChannelImpl.checkConnect(Native Method)
		at sun.nio.ch.SocketChannelImpl.finishConnect(SocketChannelImpl.java:717)
		at io.netty.channel.socket.nio.NioSocketChannel.doFinishConnect(NioSocketChannel.java:330)
		at io.netty.channel.nio.AbstractNioChannel$AbstractNioUnsafe.finishConnect(AbstractNioChannel.java:334)
		at io.netty.channel.nio.NioEventLoop.processSelectedKey(NioEventLoop.java:688)
		at io.netty.channel.nio.NioEventLoop.processSelectedKeysOptimized(NioEventLoop.java:635)
		at io.netty.channel.nio.NioEventLoop.processSelectedKeys(NioEventLoop.java:552)
		at io.netty.channel.nio.NioEventLoop.run(NioEventLoop.java:514)
		at io.netty.util.concurrent.SingleThreadEventExecutor$6.run(SingleThreadEventExecutor.java:1050)
		at io.netty.util.internal.ThreadExecutorMap$2.run(ThreadExecutorMap.java:74)
		at io.netty.util.concurrent.FastThreadLocalRunnable.run(FastThreadLocalRunnable.java:30)
		at java.lang.Thread.run(Thread.java:748)
	Suppressed: io.lettuce.core.RedisConnectionException: Unable to connect to [************:7002]: No route to host: no further information: /************:7002
		at io.lettuce.core.cluster.topology.ClusterTopologyRefresh.lambda$getConnections$0(ClusterTopologyRefresh.java:290)
		at java.util.concurrent.CompletableFuture.uniWhenComplete(CompletableFuture.java:760)
		at java.util.concurrent.CompletableFuture$UniWhenComplete.tryFire(CompletableFuture.java:736)
		at java.util.concurrent.CompletableFuture.postComplete(CompletableFuture.java:474)
		at java.util.concurrent.CompletableFuture.completeExceptionally(CompletableFuture.java:1977)
		at io.lettuce.core.AbstractRedisClient.lambda$initializeChannelAsync0$4(AbstractRedisClient.java:329)
		at io.netty.util.concurrent.DefaultPromise.notifyListener0(DefaultPromise.java:577)
		at io.netty.util.concurrent.DefaultPromise.notifyListeners0(DefaultPromise.java:570)
		at io.netty.util.concurrent.DefaultPromise.notifyListenersNow(DefaultPromise.java:549)
		at io.netty.util.concurrent.DefaultPromise.notifyListeners(DefaultPromise.java:490)
		at io.netty.util.concurrent.DefaultPromise.setValue0(DefaultPromise.java:615)
		at io.netty.util.concurrent.DefaultPromise.setFailure0(DefaultPromise.java:608)
		at io.netty.util.concurrent.DefaultPromise.tryFailure(DefaultPromise.java:117)
		at io.netty.channel.nio.AbstractNioChannel$AbstractNioUnsafe.fulfillConnectPromise(AbstractNioChannel.java:321)
		at io.netty.channel.nio.AbstractNioChannel$AbstractNioUnsafe.finishConnect(AbstractNioChannel.java:337)
		at io.netty.channel.nio.NioEventLoop.processSelectedKey(NioEventLoop.java:688)
		at io.netty.channel.nio.NioEventLoop.processSelectedKeysOptimized(NioEventLoop.java:635)
		at io.netty.channel.nio.NioEventLoop.processSelectedKeys(NioEventLoop.java:552)
		at io.netty.channel.nio.NioEventLoop.run(NioEventLoop.java:514)
		... 4 common frames omitted
	Caused by: io.netty.channel.AbstractChannel$AnnotatedNoRouteToHostException: No route to host: no further information: /************:7002
	Caused by: java.net.NoRouteToHostException: No route to host: no further information
		at sun.nio.ch.SocketChannelImpl.checkConnect(Native Method)
		at sun.nio.ch.SocketChannelImpl.finishConnect(SocketChannelImpl.java:717)
		at io.netty.channel.socket.nio.NioSocketChannel.doFinishConnect(NioSocketChannel.java:330)
		at io.netty.channel.nio.AbstractNioChannel$AbstractNioUnsafe.finishConnect(AbstractNioChannel.java:334)
		at io.netty.channel.nio.NioEventLoop.processSelectedKey(NioEventLoop.java:688)
		at io.netty.channel.nio.NioEventLoop.processSelectedKeysOptimized(NioEventLoop.java:635)
		at io.netty.channel.nio.NioEventLoop.processSelectedKeys(NioEventLoop.java:552)
		at io.netty.channel.nio.NioEventLoop.run(NioEventLoop.java:514)
		at io.netty.util.concurrent.SingleThreadEventExecutor$6.run(SingleThreadEventExecutor.java:1050)
		at io.netty.util.internal.ThreadExecutorMap$2.run(ThreadExecutorMap.java:74)
		at io.netty.util.concurrent.FastThreadLocalRunnable.run(FastThreadLocalRunnable.java:30)
		at java.lang.Thread.run(Thread.java:748)
	Suppressed: io.lettuce.core.RedisConnectionException: Unable to connect to [************:7005]: No route to host: no further information: /************:7005
		at io.lettuce.core.cluster.topology.ClusterTopologyRefresh.lambda$getConnections$0(ClusterTopologyRefresh.java:290)
		at java.util.concurrent.CompletableFuture.uniWhenComplete(CompletableFuture.java:760)
		at java.util.concurrent.CompletableFuture$UniWhenComplete.tryFire(CompletableFuture.java:736)
		at java.util.concurrent.CompletableFuture.postComplete(CompletableFuture.java:474)
		at java.util.concurrent.CompletableFuture.completeExceptionally(CompletableFuture.java:1977)
		at io.lettuce.core.AbstractRedisClient.lambda$initializeChannelAsync0$4(AbstractRedisClient.java:329)
		at io.netty.util.concurrent.DefaultPromise.notifyListener0(DefaultPromise.java:577)
		at io.netty.util.concurrent.DefaultPromise.notifyListeners0(DefaultPromise.java:570)
		at io.netty.util.concurrent.DefaultPromise.notifyListenersNow(DefaultPromise.java:549)
		at io.netty.util.concurrent.DefaultPromise.notifyListeners(DefaultPromise.java:490)
		at io.netty.util.concurrent.DefaultPromise.setValue0(DefaultPromise.java:615)
		at io.netty.util.concurrent.DefaultPromise.setFailure0(DefaultPromise.java:608)
		at io.netty.util.concurrent.DefaultPromise.tryFailure(DefaultPromise.java:117)
		at io.netty.channel.nio.AbstractNioChannel$AbstractNioUnsafe.fulfillConnectPromise(AbstractNioChannel.java:321)
		at io.netty.channel.nio.AbstractNioChannel$AbstractNioUnsafe.finishConnect(AbstractNioChannel.java:337)
		at io.netty.channel.nio.NioEventLoop.processSelectedKey(NioEventLoop.java:688)
		at io.netty.channel.nio.NioEventLoop.processSelectedKeysOptimized(NioEventLoop.java:635)
		at io.netty.channel.nio.NioEventLoop.processSelectedKeys(NioEventLoop.java:552)
		at io.netty.channel.nio.NioEventLoop.run(NioEventLoop.java:514)
		... 4 common frames omitted
	Caused by: io.netty.channel.AbstractChannel$AnnotatedNoRouteToHostException: No route to host: no further information: /************:7005
	Caused by: java.net.NoRouteToHostException: No route to host: no further information
		at sun.nio.ch.SocketChannelImpl.checkConnect(Native Method)
		at sun.nio.ch.SocketChannelImpl.finishConnect(SocketChannelImpl.java:717)
		at io.netty.channel.socket.nio.NioSocketChannel.doFinishConnect(NioSocketChannel.java:330)
		at io.netty.channel.nio.AbstractNioChannel$AbstractNioUnsafe.finishConnect(AbstractNioChannel.java:334)
		at io.netty.channel.nio.NioEventLoop.processSelectedKey(NioEventLoop.java:688)
		at io.netty.channel.nio.NioEventLoop.processSelectedKeysOptimized(NioEventLoop.java:635)
		at io.netty.channel.nio.NioEventLoop.processSelectedKeys(NioEventLoop.java:552)
		at io.netty.channel.nio.NioEventLoop.run(NioEventLoop.java:514)
		at io.netty.util.concurrent.SingleThreadEventExecutor$6.run(SingleThreadEventExecutor.java:1050)
		at io.netty.util.internal.ThreadExecutorMap$2.run(ThreadExecutorMap.java:74)
		at io.netty.util.concurrent.FastThreadLocalRunnable.run(FastThreadLocalRunnable.java:30)
		at java.lang.Thread.run(Thread.java:748)
	Suppressed: io.lettuce.core.RedisConnectionException: Unable to connect to [************:7003]: No route to host: no further information: /************:7003
		at io.lettuce.core.cluster.topology.ClusterTopologyRefresh.lambda$getConnections$0(ClusterTopologyRefresh.java:290)
		at java.util.concurrent.CompletableFuture.uniWhenComplete(CompletableFuture.java:760)
		at java.util.concurrent.CompletableFuture$UniWhenComplete.tryFire(CompletableFuture.java:736)
		at java.util.concurrent.CompletableFuture.postComplete(CompletableFuture.java:474)
		at java.util.concurrent.CompletableFuture.completeExceptionally(CompletableFuture.java:1977)
		at io.lettuce.core.AbstractRedisClient.lambda$initializeChannelAsync0$4(AbstractRedisClient.java:329)
		at io.netty.util.concurrent.DefaultPromise.notifyListener0(DefaultPromise.java:577)
		at io.netty.util.concurrent.DefaultPromise.notifyListeners0(DefaultPromise.java:570)
		at io.netty.util.concurrent.DefaultPromise.notifyListenersNow(DefaultPromise.java:549)
		at io.netty.util.concurrent.DefaultPromise.notifyListeners(DefaultPromise.java:490)
		at io.netty.util.concurrent.DefaultPromise.setValue0(DefaultPromise.java:615)
		at io.netty.util.concurrent.DefaultPromise.setFailure0(DefaultPromise.java:608)
		at io.netty.util.concurrent.DefaultPromise.tryFailure(DefaultPromise.java:117)
		at io.netty.channel.nio.AbstractNioChannel$AbstractNioUnsafe.fulfillConnectPromise(AbstractNioChannel.java:321)
		at io.netty.channel.nio.AbstractNioChannel$AbstractNioUnsafe.finishConnect(AbstractNioChannel.java:337)
		at io.netty.channel.nio.NioEventLoop.processSelectedKey(NioEventLoop.java:688)
		at io.netty.channel.nio.NioEventLoop.processSelectedKeysOptimized(NioEventLoop.java:635)
		at io.netty.channel.nio.NioEventLoop.processSelectedKeys(NioEventLoop.java:552)
		at io.netty.channel.nio.NioEventLoop.run(NioEventLoop.java:514)
		... 4 common frames omitted
	Caused by: io.netty.channel.AbstractChannel$AnnotatedNoRouteToHostException: No route to host: no further information: /************:7003
	Caused by: java.net.NoRouteToHostException: No route to host: no further information
		at sun.nio.ch.SocketChannelImpl.checkConnect(Native Method)
		at sun.nio.ch.SocketChannelImpl.finishConnect(SocketChannelImpl.java:717)
		at io.netty.channel.socket.nio.NioSocketChannel.doFinishConnect(NioSocketChannel.java:330)
		at io.netty.channel.nio.AbstractNioChannel$AbstractNioUnsafe.finishConnect(AbstractNioChannel.java:334)
		at io.netty.channel.nio.NioEventLoop.processSelectedKey(NioEventLoop.java:688)
		at io.netty.channel.nio.NioEventLoop.processSelectedKeysOptimized(NioEventLoop.java:635)
		at io.netty.channel.nio.NioEventLoop.processSelectedKeys(NioEventLoop.java:552)
		at io.netty.channel.nio.NioEventLoop.run(NioEventLoop.java:514)
		at io.netty.util.concurrent.SingleThreadEventExecutor$6.run(SingleThreadEventExecutor.java:1050)
		at io.netty.util.internal.ThreadExecutorMap$2.run(ThreadExecutorMap.java:74)
		at io.netty.util.concurrent.FastThreadLocalRunnable.run(FastThreadLocalRunnable.java:30)
		at java.lang.Thread.run(Thread.java:748)
	Suppressed: io.lettuce.core.RedisConnectionException: Unable to connect to [************:7001]: No route to host: no further information: /************:7001
		at io.lettuce.core.cluster.topology.ClusterTopologyRefresh.lambda$getConnections$0(ClusterTopologyRefresh.java:290)
		at java.util.concurrent.CompletableFuture.uniWhenComplete(CompletableFuture.java:760)
		at java.util.concurrent.CompletableFuture$UniWhenComplete.tryFire(CompletableFuture.java:736)
		at java.util.concurrent.CompletableFuture.postComplete(CompletableFuture.java:474)
		at java.util.concurrent.CompletableFuture.completeExceptionally(CompletableFuture.java:1977)
		at io.lettuce.core.AbstractRedisClient.lambda$initializeChannelAsync0$4(AbstractRedisClient.java:329)
		at io.netty.util.concurrent.DefaultPromise.notifyListener0(DefaultPromise.java:577)
		at io.netty.util.concurrent.DefaultPromise.notifyListeners0(DefaultPromise.java:570)
		at io.netty.util.concurrent.DefaultPromise.notifyListenersNow(DefaultPromise.java:549)
		at io.netty.util.concurrent.DefaultPromise.notifyListeners(DefaultPromise.java:490)
		at io.netty.util.concurrent.DefaultPromise.setValue0(DefaultPromise.java:615)
		at io.netty.util.concurrent.DefaultPromise.setFailure0(DefaultPromise.java:608)
		at io.netty.util.concurrent.DefaultPromise.tryFailure(DefaultPromise.java:117)
		at io.netty.channel.nio.AbstractNioChannel$AbstractNioUnsafe.fulfillConnectPromise(AbstractNioChannel.java:321)
		at io.netty.channel.nio.AbstractNioChannel$AbstractNioUnsafe.finishConnect(AbstractNioChannel.java:337)
		at io.netty.channel.nio.NioEventLoop.processSelectedKey(NioEventLoop.java:688)
		at io.netty.channel.nio.NioEventLoop.processSelectedKeysOptimized(NioEventLoop.java:635)
		at io.netty.channel.nio.NioEventLoop.processSelectedKeys(NioEventLoop.java:552)
		at io.netty.channel.nio.NioEventLoop.run(NioEventLoop.java:514)
		... 4 common frames omitted
	Caused by: io.netty.channel.AbstractChannel$AnnotatedNoRouteToHostException: No route to host: no further information: /************:7001
	Caused by: java.net.NoRouteToHostException: No route to host: no further information
		at sun.nio.ch.SocketChannelImpl.checkConnect(Native Method)
		at sun.nio.ch.SocketChannelImpl.finishConnect(SocketChannelImpl.java:717)
		at io.netty.channel.socket.nio.NioSocketChannel.doFinishConnect(NioSocketChannel.java:330)
		at io.netty.channel.nio.AbstractNioChannel$AbstractNioUnsafe.finishConnect(AbstractNioChannel.java:334)
		at io.netty.channel.nio.NioEventLoop.processSelectedKey(NioEventLoop.java:688)
		at io.netty.channel.nio.NioEventLoop.processSelectedKeysOptimized(NioEventLoop.java:635)
		at io.netty.channel.nio.NioEventLoop.processSelectedKeys(NioEventLoop.java:552)
		at io.netty.channel.nio.NioEventLoop.run(NioEventLoop.java:514)
		at io.netty.util.concurrent.SingleThreadEventExecutor$6.run(SingleThreadEventExecutor.java:1050)
		at io.netty.util.internal.ThreadExecutorMap$2.run(ThreadExecutorMap.java:74)
		at io.netty.util.concurrent.FastThreadLocalRunnable.run(FastThreadLocalRunnable.java:30)
		at java.lang.Thread.run(Thread.java:748)
Caused by: io.lettuce.core.RedisConnectionException: Unable to connect to [************:7004]: No route to host: no further information: /************:7004
	at io.lettuce.core.cluster.topology.ClusterTopologyRefresh.lambda$getConnections$0(ClusterTopologyRefresh.java:290)
	at java.util.concurrent.CompletableFuture.uniWhenComplete(CompletableFuture.java:760)
	at java.util.concurrent.CompletableFuture$UniWhenComplete.tryFire(CompletableFuture.java:736)
	at java.util.concurrent.CompletableFuture.postComplete(CompletableFuture.java:474)
	at java.util.concurrent.CompletableFuture.completeExceptionally(CompletableFuture.java:1977)
	at io.lettuce.core.AbstractRedisClient.lambda$initializeChannelAsync0$4(AbstractRedisClient.java:329)
	at io.netty.util.concurrent.DefaultPromise.notifyListener0(DefaultPromise.java:577)
	at io.netty.util.concurrent.DefaultPromise.notifyListeners0(DefaultPromise.java:570)
	at io.netty.util.concurrent.DefaultPromise.notifyListenersNow(DefaultPromise.java:549)
	at io.netty.util.concurrent.DefaultPromise.notifyListeners(DefaultPromise.java:490)
	at io.netty.util.concurrent.DefaultPromise.setValue0(DefaultPromise.java:615)
	at io.netty.util.concurrent.DefaultPromise.setFailure0(DefaultPromise.java:608)
	at io.netty.util.concurrent.DefaultPromise.tryFailure(DefaultPromise.java:117)
	at io.netty.channel.nio.AbstractNioChannel$AbstractNioUnsafe.fulfillConnectPromise(AbstractNioChannel.java:321)
	at io.netty.channel.nio.AbstractNioChannel$AbstractNioUnsafe.finishConnect(AbstractNioChannel.java:337)
	at io.netty.channel.nio.NioEventLoop.processSelectedKey(NioEventLoop.java:688)
	at io.netty.channel.nio.NioEventLoop.processSelectedKeysOptimized(NioEventLoop.java:635)
	at io.netty.channel.nio.NioEventLoop.processSelectedKeys(NioEventLoop.java:552)
	at io.netty.channel.nio.NioEventLoop.run(NioEventLoop.java:514)
	... 4 common frames omitted
Caused by: io.netty.channel.AbstractChannel$AnnotatedNoRouteToHostException: No route to host: no further information: /************:7004
Caused by: java.net.NoRouteToHostException: No route to host: no further information
	at sun.nio.ch.SocketChannelImpl.checkConnect(Native Method)
	at sun.nio.ch.SocketChannelImpl.finishConnect(SocketChannelImpl.java:717)
	at io.netty.channel.socket.nio.NioSocketChannel.doFinishConnect(NioSocketChannel.java:330)
	at io.netty.channel.nio.AbstractNioChannel$AbstractNioUnsafe.finishConnect(AbstractNioChannel.java:334)
	at io.netty.channel.nio.NioEventLoop.processSelectedKey(NioEventLoop.java:688)
	at io.netty.channel.nio.NioEventLoop.processSelectedKeysOptimized(NioEventLoop.java:635)
	at io.netty.channel.nio.NioEventLoop.processSelectedKeys(NioEventLoop.java:552)
	at io.netty.channel.nio.NioEventLoop.run(NioEventLoop.java:514)
	at io.netty.util.concurrent.SingleThreadEventExecutor$6.run(SingleThreadEventExecutor.java:1050)
	at io.netty.util.internal.ThreadExecutorMap$2.run(ThreadExecutorMap.java:74)
	at io.netty.util.concurrent.FastThreadLocalRunnable.run(FastThreadLocalRunnable.java:30)
	at java.lang.Thread.run(Thread.java:748)
2025-06-01 02:32:54.573 [lettuce-nioEventLoop-4-12] WARN  io.lettuce.core.cluster.topology.ClusterTopologyRefresh.lambda$getConnections$0 Line:284 - Unable to connect to [************:7001]: No route to host: no further information: /************:7001
2025-06-01 02:32:54.574 [lettuce-nioEventLoop-4-5] WARN  io.lettuce.core.cluster.topology.ClusterTopologyRefresh.lambda$getConnections$0 Line:284 - Unable to connect to [************:7006]: No route to host: no further information: /************:7006
2025-06-01 02:32:54.574 [lettuce-nioEventLoop-4-4] WARN  io.lettuce.core.cluster.topology.ClusterTopologyRefresh.lambda$getConnections$0 Line:284 - Unable to connect to [************:7005]: No route to host: no further information: /************:7005
2025-06-01 02:32:54.574 [lettuce-nioEventLoop-4-1] WARN  io.lettuce.core.cluster.topology.ClusterTopologyRefresh.lambda$getConnections$0 Line:284 - Unable to connect to [************:7002]: No route to host: no further information: /************:7002
2025-06-01 02:32:54.574 [lettuce-nioEventLoop-4-3] WARN  io.lettuce.core.cluster.topology.ClusterTopologyRefresh.lambda$getConnections$0 Line:284 - Unable to connect to [************:7004]: No route to host: no further information: /************:7004
2025-06-01 02:32:54.574 [lettuce-nioEventLoop-4-2] WARN  io.lettuce.core.cluster.topology.ClusterTopologyRefresh.lambda$getConnections$0 Line:284 - Unable to connect to [************:7003]: No route to host: no further information: /************:7003
2025-06-01 02:32:54.580 [lettuce-nioEventLoop-4-9] WARN  io.lettuce.core.cluster.topology.ClusterTopologyRefresh.lambda$getConnections$0 Line:284 - Unable to connect to [************:7002]: No route to host: no further information: /************:7002
2025-06-01 02:32:54.580 [lettuce-nioEventLoop-4-6] WARN  io.lettuce.core.cluster.topology.ClusterTopologyRefresh.lambda$getConnections$0 Line:284 - Unable to connect to [************:7006]: No route to host: no further information: /************:7006
2025-06-01 02:32:54.580 [lettuce-nioEventLoop-4-7] WARN  io.lettuce.core.cluster.topology.ClusterTopologyRefresh.lambda$getConnections$0 Line:284 - Unable to connect to [************:7004]: No route to host: no further information: /************:7004
2025-06-01 02:32:54.580 [lettuce-nioEventLoop-4-8] WARN  io.lettuce.core.cluster.topology.ClusterTopologyRefresh.lambda$getConnections$0 Line:284 - Unable to connect to [************:7005]: No route to host: no further information: /************:7005
2025-06-01 02:32:54.580 [lettuce-nioEventLoop-4-10] WARN  io.lettuce.core.cluster.topology.ClusterTopologyRefresh.lambda$getConnections$0 Line:284 - Unable to connect to [************:7003]: No route to host: no further information: /************:7003
2025-06-01 02:32:54.584 [lettuce-nioEventLoop-4-11] WARN  io.lettuce.core.cluster.topology.ClusterTopologyRefresh.lambda$getConnections$0 Line:284 - Unable to connect to [************:7001]: No route to host: no further information: /************:7001
2025-06-01 02:32:54.602 [lettuce-eventExecutorLoop-1-3] WARN  io.lettuce.core.cluster.ClusterTopologyRefreshScheduler.doRun Line:258 - Cannot refresh Redis Cluster topology
io.lettuce.core.RedisException: Cannot retrieve initial cluster partitions from initial URIs [RedisURI [host='************', port=7006], RedisURI [host='************', port=7004], RedisURI [host='************', port=7005], RedisURI [host='************', port=7002], RedisURI [host='************', port=7003], RedisURI [host='************', port=7001]]
	at io.lettuce.core.cluster.RedisClusterClient.loadPartitions(RedisClusterClient.java:848)
	at io.lettuce.core.cluster.RedisClusterClient.reloadPartitions(RedisClusterClient.java:784)
	at io.lettuce.core.cluster.ClusterTopologyRefreshScheduler$ClusterTopologyRefreshTask.doRun(ClusterTopologyRefreshScheduler.java:256)
	at io.lettuce.core.cluster.ClusterTopologyRefreshScheduler$ClusterTopologyRefreshTask.run(ClusterTopologyRefreshScheduler.java:236)
	at io.netty.util.concurrent.PromiseTask.runTask(PromiseTask.java:98)
	at io.netty.util.concurrent.PromiseTask.run(PromiseTask.java:106)
	at io.netty.util.concurrent.DefaultEventExecutor.run(DefaultEventExecutor.java:66)
	at io.netty.util.concurrent.SingleThreadEventExecutor$6.run(SingleThreadEventExecutor.java:1050)
	at io.netty.util.internal.ThreadExecutorMap$2.run(ThreadExecutorMap.java:74)
	at io.netty.util.concurrent.FastThreadLocalRunnable.run(FastThreadLocalRunnable.java:30)
	at java.lang.Thread.run(Thread.java:748)
	Suppressed: io.lettuce.core.RedisConnectionException: Unable to establish a connection to Redis Cluster at [RedisURI [host='************', port=7001], RedisURI [host='************', port=7002], RedisURI [host='************', port=7003], RedisURI [host='************', port=7004], RedisURI [host='************', port=7005], RedisURI [host='************', port=7006]]
		at io.lettuce.core.cluster.topology.AsyncConnections.get(AsyncConnections.java:89)
		at io.lettuce.core.cluster.topology.ClusterTopologyRefresh.loadViews(ClusterTopologyRefresh.java:76)
		at io.lettuce.core.cluster.RedisClusterClient.doLoadPartitions(RedisClusterClient.java:865)
		at io.lettuce.core.cluster.RedisClusterClient.loadPartitions(RedisClusterClient.java:838)
		... 10 common frames omitted
		Suppressed: io.lettuce.core.RedisConnectionException: Unable to connect to [************:7005]: No route to host: no further information: /************:7005
			at io.lettuce.core.cluster.topology.ClusterTopologyRefresh.lambda$getConnections$0(ClusterTopologyRefresh.java:290)
			at java.util.concurrent.CompletableFuture.uniWhenComplete(CompletableFuture.java:760)
			at java.util.concurrent.CompletableFuture$UniWhenComplete.tryFire(CompletableFuture.java:736)
			at java.util.concurrent.CompletableFuture.postComplete(CompletableFuture.java:474)
			at java.util.concurrent.CompletableFuture.completeExceptionally(CompletableFuture.java:1977)
			at io.lettuce.core.AbstractRedisClient.lambda$initializeChannelAsync0$4(AbstractRedisClient.java:329)
			at io.netty.util.concurrent.DefaultPromise.notifyListener0(DefaultPromise.java:577)
			at io.netty.util.concurrent.DefaultPromise.notifyListeners0(DefaultPromise.java:570)
			at io.netty.util.concurrent.DefaultPromise.notifyListenersNow(DefaultPromise.java:549)
			at io.netty.util.concurrent.DefaultPromise.notifyListeners(DefaultPromise.java:490)
			at io.netty.util.concurrent.DefaultPromise.setValue0(DefaultPromise.java:615)
			at io.netty.util.concurrent.DefaultPromise.setFailure0(DefaultPromise.java:608)
			at io.netty.util.concurrent.DefaultPromise.tryFailure(DefaultPromise.java:117)
			at io.netty.channel.nio.AbstractNioChannel$AbstractNioUnsafe.fulfillConnectPromise(AbstractNioChannel.java:321)
			at io.netty.channel.nio.AbstractNioChannel$AbstractNioUnsafe.finishConnect(AbstractNioChannel.java:337)
			at io.netty.channel.nio.NioEventLoop.processSelectedKey(NioEventLoop.java:688)
			at io.netty.channel.nio.NioEventLoop.processSelectedKeysOptimized(NioEventLoop.java:635)
			at io.netty.channel.nio.NioEventLoop.processSelectedKeys(NioEventLoop.java:552)
			at io.netty.channel.nio.NioEventLoop.run(NioEventLoop.java:514)
			... 4 common frames omitted
		Caused by: io.netty.channel.AbstractChannel$AnnotatedNoRouteToHostException: No route to host: no further information: /************:7005
		Caused by: java.net.NoRouteToHostException: No route to host: no further information
			at sun.nio.ch.SocketChannelImpl.checkConnect(Native Method)
			at sun.nio.ch.SocketChannelImpl.finishConnect(SocketChannelImpl.java:717)
			at io.netty.channel.socket.nio.NioSocketChannel.doFinishConnect(NioSocketChannel.java:330)
			at io.netty.channel.nio.AbstractNioChannel$AbstractNioUnsafe.finishConnect(AbstractNioChannel.java:334)
			at io.netty.channel.nio.NioEventLoop.processSelectedKey(NioEventLoop.java:688)
			at io.netty.channel.nio.NioEventLoop.processSelectedKeysOptimized(NioEventLoop.java:635)
			at io.netty.channel.nio.NioEventLoop.processSelectedKeys(NioEventLoop.java:552)
			at io.netty.channel.nio.NioEventLoop.run(NioEventLoop.java:514)
			at io.netty.util.concurrent.SingleThreadEventExecutor$6.run(SingleThreadEventExecutor.java:1050)
			at io.netty.util.internal.ThreadExecutorMap$2.run(ThreadExecutorMap.java:74)
			at io.netty.util.concurrent.FastThreadLocalRunnable.run(FastThreadLocalRunnable.java:30)
			at java.lang.Thread.run(Thread.java:748)
		Suppressed: io.lettuce.core.RedisConnectionException: Unable to connect to [************:7006]: No route to host: no further information: /************:7006
			at io.lettuce.core.cluster.topology.ClusterTopologyRefresh.lambda$getConnections$0(ClusterTopologyRefresh.java:290)
			at java.util.concurrent.CompletableFuture.uniWhenComplete(CompletableFuture.java:760)
			at java.util.concurrent.CompletableFuture$UniWhenComplete.tryFire(CompletableFuture.java:736)
			at java.util.concurrent.CompletableFuture.postComplete(CompletableFuture.java:474)
			at java.util.concurrent.CompletableFuture.completeExceptionally(CompletableFuture.java:1977)
			at io.lettuce.core.AbstractRedisClient.lambda$initializeChannelAsync0$4(AbstractRedisClient.java:329)
			at io.netty.util.concurrent.DefaultPromise.notifyListener0(DefaultPromise.java:577)
			at io.netty.util.concurrent.DefaultPromise.notifyListeners0(DefaultPromise.java:570)
			at io.netty.util.concurrent.DefaultPromise.notifyListenersNow(DefaultPromise.java:549)
			at io.netty.util.concurrent.DefaultPromise.notifyListeners(DefaultPromise.java:490)
			at io.netty.util.concurrent.DefaultPromise.setValue0(DefaultPromise.java:615)
			at io.netty.util.concurrent.DefaultPromise.setFailure0(DefaultPromise.java:608)
			at io.netty.util.concurrent.DefaultPromise.tryFailure(DefaultPromise.java:117)
			at io.netty.channel.nio.AbstractNioChannel$AbstractNioUnsafe.fulfillConnectPromise(AbstractNioChannel.java:321)
			at io.netty.channel.nio.AbstractNioChannel$AbstractNioUnsafe.finishConnect(AbstractNioChannel.java:337)
			at io.netty.channel.nio.NioEventLoop.processSelectedKey(NioEventLoop.java:688)
			at io.netty.channel.nio.NioEventLoop.processSelectedKeysOptimized(NioEventLoop.java:635)
			at io.netty.channel.nio.NioEventLoop.processSelectedKeys(NioEventLoop.java:552)
			at io.netty.channel.nio.NioEventLoop.run(NioEventLoop.java:514)
			... 4 common frames omitted
		Caused by: io.netty.channel.AbstractChannel$AnnotatedNoRouteToHostException: No route to host: no further information: /************:7006
		Caused by: java.net.NoRouteToHostException: No route to host: no further information
			at sun.nio.ch.SocketChannelImpl.checkConnect(Native Method)
			at sun.nio.ch.SocketChannelImpl.finishConnect(SocketChannelImpl.java:717)
			at io.netty.channel.socket.nio.NioSocketChannel.doFinishConnect(NioSocketChannel.java:330)
			at io.netty.channel.nio.AbstractNioChannel$AbstractNioUnsafe.finishConnect(AbstractNioChannel.java:334)
			at io.netty.channel.nio.NioEventLoop.processSelectedKey(NioEventLoop.java:688)
			at io.netty.channel.nio.NioEventLoop.processSelectedKeysOptimized(NioEventLoop.java:635)
			at io.netty.channel.nio.NioEventLoop.processSelectedKeys(NioEventLoop.java:552)
			at io.netty.channel.nio.NioEventLoop.run(NioEventLoop.java:514)
			at io.netty.util.concurrent.SingleThreadEventExecutor$6.run(SingleThreadEventExecutor.java:1050)
			at io.netty.util.internal.ThreadExecutorMap$2.run(ThreadExecutorMap.java:74)
			at io.netty.util.concurrent.FastThreadLocalRunnable.run(FastThreadLocalRunnable.java:30)
			at java.lang.Thread.run(Thread.java:748)
		Suppressed: io.lettuce.core.RedisConnectionException: Unable to connect to [************:7002]: No route to host: no further information: /************:7002
			at io.lettuce.core.cluster.topology.ClusterTopologyRefresh.lambda$getConnections$0(ClusterTopologyRefresh.java:290)
			at java.util.concurrent.CompletableFuture.uniWhenComplete(CompletableFuture.java:760)
			at java.util.concurrent.CompletableFuture$UniWhenComplete.tryFire(CompletableFuture.java:736)
			at java.util.concurrent.CompletableFuture.postComplete(CompletableFuture.java:474)
			at java.util.concurrent.CompletableFuture.completeExceptionally(CompletableFuture.java:1977)
			at io.lettuce.core.AbstractRedisClient.lambda$initializeChannelAsync0$4(AbstractRedisClient.java:329)
			at io.netty.util.concurrent.DefaultPromise.notifyListener0(DefaultPromise.java:577)
			at io.netty.util.concurrent.DefaultPromise.notifyListeners0(DefaultPromise.java:570)
			at io.netty.util.concurrent.DefaultPromise.notifyListenersNow(DefaultPromise.java:549)
			at io.netty.util.concurrent.DefaultPromise.notifyListeners(DefaultPromise.java:490)
			at io.netty.util.concurrent.DefaultPromise.setValue0(DefaultPromise.java:615)
			at io.netty.util.concurrent.DefaultPromise.setFailure0(DefaultPromise.java:608)
			at io.netty.util.concurrent.DefaultPromise.tryFailure(DefaultPromise.java:117)
			at io.netty.channel.nio.AbstractNioChannel$AbstractNioUnsafe.fulfillConnectPromise(AbstractNioChannel.java:321)
			at io.netty.channel.nio.AbstractNioChannel$AbstractNioUnsafe.finishConnect(AbstractNioChannel.java:337)
			at io.netty.channel.nio.NioEventLoop.processSelectedKey(NioEventLoop.java:688)
			at io.netty.channel.nio.NioEventLoop.processSelectedKeysOptimized(NioEventLoop.java:635)
			at io.netty.channel.nio.NioEventLoop.processSelectedKeys(NioEventLoop.java:552)
			at io.netty.channel.nio.NioEventLoop.run(NioEventLoop.java:514)
			... 4 common frames omitted
		Caused by: io.netty.channel.AbstractChannel$AnnotatedNoRouteToHostException: No route to host: no further information: /************:7002
		Caused by: java.net.NoRouteToHostException: No route to host: no further information
			at sun.nio.ch.SocketChannelImpl.checkConnect(Native Method)
			at sun.nio.ch.SocketChannelImpl.finishConnect(SocketChannelImpl.java:717)
			at io.netty.channel.socket.nio.NioSocketChannel.doFinishConnect(NioSocketChannel.java:330)
			at io.netty.channel.nio.AbstractNioChannel$AbstractNioUnsafe.finishConnect(AbstractNioChannel.java:334)
			at io.netty.channel.nio.NioEventLoop.processSelectedKey(NioEventLoop.java:688)
			at io.netty.channel.nio.NioEventLoop.processSelectedKeysOptimized(NioEventLoop.java:635)
			at io.netty.channel.nio.NioEventLoop.processSelectedKeys(NioEventLoop.java:552)
			at io.netty.channel.nio.NioEventLoop.run(NioEventLoop.java:514)
			at io.netty.util.concurrent.SingleThreadEventExecutor$6.run(SingleThreadEventExecutor.java:1050)
			at io.netty.util.internal.ThreadExecutorMap$2.run(ThreadExecutorMap.java:74)
			at io.netty.util.concurrent.FastThreadLocalRunnable.run(FastThreadLocalRunnable.java:30)
			at java.lang.Thread.run(Thread.java:748)
		Suppressed: io.lettuce.core.RedisConnectionException: Unable to connect to [************:7004]: No route to host: no further information: /************:7004
			at io.lettuce.core.cluster.topology.ClusterTopologyRefresh.lambda$getConnections$0(ClusterTopologyRefresh.java:290)
			at java.util.concurrent.CompletableFuture.uniWhenComplete(CompletableFuture.java:760)
			at java.util.concurrent.CompletableFuture$UniWhenComplete.tryFire(CompletableFuture.java:736)
			at java.util.concurrent.CompletableFuture.postComplete(CompletableFuture.java:474)
			at java.util.concurrent.CompletableFuture.completeExceptionally(CompletableFuture.java:1977)
			at io.lettuce.core.AbstractRedisClient.lambda$initializeChannelAsync0$4(AbstractRedisClient.java:329)
			at io.netty.util.concurrent.DefaultPromise.notifyListener0(DefaultPromise.java:577)
			at io.netty.util.concurrent.DefaultPromise.notifyListeners0(DefaultPromise.java:570)
			at io.netty.util.concurrent.DefaultPromise.notifyListenersNow(DefaultPromise.java:549)
			at io.netty.util.concurrent.DefaultPromise.notifyListeners(DefaultPromise.java:490)
			at io.netty.util.concurrent.DefaultPromise.setValue0(DefaultPromise.java:615)
			at io.netty.util.concurrent.DefaultPromise.setFailure0(DefaultPromise.java:608)
			at io.netty.util.concurrent.DefaultPromise.tryFailure(DefaultPromise.java:117)
			at io.netty.channel.nio.AbstractNioChannel$AbstractNioUnsafe.fulfillConnectPromise(AbstractNioChannel.java:321)
			at io.netty.channel.nio.AbstractNioChannel$AbstractNioUnsafe.finishConnect(AbstractNioChannel.java:337)
			at io.netty.channel.nio.NioEventLoop.processSelectedKey(NioEventLoop.java:688)
			at io.netty.channel.nio.NioEventLoop.processSelectedKeysOptimized(NioEventLoop.java:635)
			at io.netty.channel.nio.NioEventLoop.processSelectedKeys(NioEventLoop.java:552)
			at io.netty.channel.nio.NioEventLoop.run(NioEventLoop.java:514)
			... 4 common frames omitted
		Caused by: io.netty.channel.AbstractChannel$AnnotatedNoRouteToHostException: No route to host: no further information: /************:7004
		Caused by: java.net.NoRouteToHostException: No route to host: no further information
			at sun.nio.ch.SocketChannelImpl.checkConnect(Native Method)
			at sun.nio.ch.SocketChannelImpl.finishConnect(SocketChannelImpl.java:717)
			at io.netty.channel.socket.nio.NioSocketChannel.doFinishConnect(NioSocketChannel.java:330)
			at io.netty.channel.nio.AbstractNioChannel$AbstractNioUnsafe.finishConnect(AbstractNioChannel.java:334)
			at io.netty.channel.nio.NioEventLoop.processSelectedKey(NioEventLoop.java:688)
			at io.netty.channel.nio.NioEventLoop.processSelectedKeysOptimized(NioEventLoop.java:635)
			at io.netty.channel.nio.NioEventLoop.processSelectedKeys(NioEventLoop.java:552)
			at io.netty.channel.nio.NioEventLoop.run(NioEventLoop.java:514)
			at io.netty.util.concurrent.SingleThreadEventExecutor$6.run(SingleThreadEventExecutor.java:1050)
			at io.netty.util.internal.ThreadExecutorMap$2.run(ThreadExecutorMap.java:74)
			at io.netty.util.concurrent.FastThreadLocalRunnable.run(FastThreadLocalRunnable.java:30)
			at java.lang.Thread.run(Thread.java:748)
		Suppressed: io.lettuce.core.RedisConnectionException: Unable to connect to [************:7003]: No route to host: no further information: /************:7003
			at io.lettuce.core.cluster.topology.ClusterTopologyRefresh.lambda$getConnections$0(ClusterTopologyRefresh.java:290)
			at java.util.concurrent.CompletableFuture.uniWhenComplete(CompletableFuture.java:760)
			at java.util.concurrent.CompletableFuture$UniWhenComplete.tryFire(CompletableFuture.java:736)
			at java.util.concurrent.CompletableFuture.postComplete(CompletableFuture.java:474)
			at java.util.concurrent.CompletableFuture.completeExceptionally(CompletableFuture.java:1977)
			at io.lettuce.core.AbstractRedisClient.lambda$initializeChannelAsync0$4(AbstractRedisClient.java:329)
			at io.netty.util.concurrent.DefaultPromise.notifyListener0(DefaultPromise.java:577)
			at io.netty.util.concurrent.DefaultPromise.notifyListeners0(DefaultPromise.java:570)
			at io.netty.util.concurrent.DefaultPromise.notifyListenersNow(DefaultPromise.java:549)
			at io.netty.util.concurrent.DefaultPromise.notifyListeners(DefaultPromise.java:490)
			at io.netty.util.concurrent.DefaultPromise.setValue0(DefaultPromise.java:615)
			at io.netty.util.concurrent.DefaultPromise.setFailure0(DefaultPromise.java:608)
			at io.netty.util.concurrent.DefaultPromise.tryFailure(DefaultPromise.java:117)
			at io.netty.channel.nio.AbstractNioChannel$AbstractNioUnsafe.fulfillConnectPromise(AbstractNioChannel.java:321)
			at io.netty.channel.nio.AbstractNioChannel$AbstractNioUnsafe.finishConnect(AbstractNioChannel.java:337)
			at io.netty.channel.nio.NioEventLoop.processSelectedKey(NioEventLoop.java:688)
			at io.netty.channel.nio.NioEventLoop.processSelectedKeysOptimized(NioEventLoop.java:635)
			at io.netty.channel.nio.NioEventLoop.processSelectedKeys(NioEventLoop.java:552)
			at io.netty.channel.nio.NioEventLoop.run(NioEventLoop.java:514)
			... 4 common frames omitted
		Caused by: io.netty.channel.AbstractChannel$AnnotatedNoRouteToHostException: No route to host: no further information: /************:7003
		Caused by: java.net.NoRouteToHostException: No route to host: no further information
			at sun.nio.ch.SocketChannelImpl.checkConnect(Native Method)
			at sun.nio.ch.SocketChannelImpl.finishConnect(SocketChannelImpl.java:717)
			at io.netty.channel.socket.nio.NioSocketChannel.doFinishConnect(NioSocketChannel.java:330)
			at io.netty.channel.nio.AbstractNioChannel$AbstractNioUnsafe.finishConnect(AbstractNioChannel.java:334)
			at io.netty.channel.nio.NioEventLoop.processSelectedKey(NioEventLoop.java:688)
			at io.netty.channel.nio.NioEventLoop.processSelectedKeysOptimized(NioEventLoop.java:635)
			at io.netty.channel.nio.NioEventLoop.processSelectedKeys(NioEventLoop.java:552)
			at io.netty.channel.nio.NioEventLoop.run(NioEventLoop.java:514)
			at io.netty.util.concurrent.SingleThreadEventExecutor$6.run(SingleThreadEventExecutor.java:1050)
			at io.netty.util.internal.ThreadExecutorMap$2.run(ThreadExecutorMap.java:74)
			at io.netty.util.concurrent.FastThreadLocalRunnable.run(FastThreadLocalRunnable.java:30)
			at java.lang.Thread.run(Thread.java:748)
	Caused by: io.lettuce.core.RedisConnectionException: Unable to connect to [************:7001]: No route to host: no further information: /************:7001
		at io.lettuce.core.cluster.topology.ClusterTopologyRefresh.lambda$getConnections$0(ClusterTopologyRefresh.java:290)
		at java.util.concurrent.CompletableFuture.uniWhenComplete(CompletableFuture.java:760)
		at java.util.concurrent.CompletableFuture$UniWhenComplete.tryFire(CompletableFuture.java:736)
		at java.util.concurrent.CompletableFuture.postComplete(CompletableFuture.java:474)
		at java.util.concurrent.CompletableFuture.completeExceptionally(CompletableFuture.java:1977)
		at io.lettuce.core.AbstractRedisClient.lambda$initializeChannelAsync0$4(AbstractRedisClient.java:329)
		at io.netty.util.concurrent.DefaultPromise.notifyListener0(DefaultPromise.java:577)
		at io.netty.util.concurrent.DefaultPromise.notifyListeners0(DefaultPromise.java:570)
		at io.netty.util.concurrent.DefaultPromise.notifyListenersNow(DefaultPromise.java:549)
		at io.netty.util.concurrent.DefaultPromise.notifyListeners(DefaultPromise.java:490)
		at io.netty.util.concurrent.DefaultPromise.setValue0(DefaultPromise.java:615)
		at io.netty.util.concurrent.DefaultPromise.setFailure0(DefaultPromise.java:608)
		at io.netty.util.concurrent.DefaultPromise.tryFailure(DefaultPromise.java:117)
		at io.netty.channel.nio.AbstractNioChannel$AbstractNioUnsafe.fulfillConnectPromise(AbstractNioChannel.java:321)
		at io.netty.channel.nio.AbstractNioChannel$AbstractNioUnsafe.finishConnect(AbstractNioChannel.java:337)
		at io.netty.channel.nio.NioEventLoop.processSelectedKey(NioEventLoop.java:688)
		at io.netty.channel.nio.NioEventLoop.processSelectedKeysOptimized(NioEventLoop.java:635)
		at io.netty.channel.nio.NioEventLoop.processSelectedKeys(NioEventLoop.java:552)
		at io.netty.channel.nio.NioEventLoop.run(NioEventLoop.java:514)
		... 4 common frames omitted
	Caused by: io.netty.channel.AbstractChannel$AnnotatedNoRouteToHostException: No route to host: no further information: /************:7001
	Caused by: java.net.NoRouteToHostException: No route to host: no further information
		at sun.nio.ch.SocketChannelImpl.checkConnect(Native Method)
		at sun.nio.ch.SocketChannelImpl.finishConnect(SocketChannelImpl.java:717)
		at io.netty.channel.socket.nio.NioSocketChannel.doFinishConnect(NioSocketChannel.java:330)
		at io.netty.channel.nio.AbstractNioChannel$AbstractNioUnsafe.finishConnect(AbstractNioChannel.java:334)
		at io.netty.channel.nio.NioEventLoop.processSelectedKey(NioEventLoop.java:688)
		at io.netty.channel.nio.NioEventLoop.processSelectedKeysOptimized(NioEventLoop.java:635)
		at io.netty.channel.nio.NioEventLoop.processSelectedKeys(NioEventLoop.java:552)
		at io.netty.channel.nio.NioEventLoop.run(NioEventLoop.java:514)
		at io.netty.util.concurrent.SingleThreadEventExecutor$6.run(SingleThreadEventExecutor.java:1050)
		at io.netty.util.internal.ThreadExecutorMap$2.run(ThreadExecutorMap.java:74)
		at io.netty.util.concurrent.FastThreadLocalRunnable.run(FastThreadLocalRunnable.java:30)
		at java.lang.Thread.run(Thread.java:748)
Caused by: io.lettuce.core.RedisConnectionException: Unable to establish a connection to Redis Cluster at [RedisURI [host='************', port=7001], RedisURI [host='************', port=7002], RedisURI [host='************', port=7003], RedisURI [host='************', port=7004], RedisURI [host='************', port=7005], RedisURI [host='************', port=7006]]
	at io.lettuce.core.cluster.topology.AsyncConnections.get(AsyncConnections.java:89)
	at io.lettuce.core.cluster.topology.ClusterTopologyRefresh.loadViews(ClusterTopologyRefresh.java:76)
	at io.lettuce.core.cluster.RedisClusterClient.doLoadPartitions(RedisClusterClient.java:865)
	at io.lettuce.core.cluster.RedisClusterClient.loadPartitions(RedisClusterClient.java:845)
	... 10 common frames omitted
	Suppressed: io.lettuce.core.RedisConnectionException: Unable to connect to [************:7006]: No route to host: no further information: /************:7006
		at io.lettuce.core.cluster.topology.ClusterTopologyRefresh.lambda$getConnections$0(ClusterTopologyRefresh.java:290)
		at java.util.concurrent.CompletableFuture.uniWhenComplete(CompletableFuture.java:760)
		at java.util.concurrent.CompletableFuture$UniWhenComplete.tryFire(CompletableFuture.java:736)
		at java.util.concurrent.CompletableFuture.postComplete(CompletableFuture.java:474)
		at java.util.concurrent.CompletableFuture.completeExceptionally(CompletableFuture.java:1977)
		at io.lettuce.core.AbstractRedisClient.lambda$initializeChannelAsync0$4(AbstractRedisClient.java:329)
		at io.netty.util.concurrent.DefaultPromise.notifyListener0(DefaultPromise.java:577)
		at io.netty.util.concurrent.DefaultPromise.notifyListeners0(DefaultPromise.java:570)
		at io.netty.util.concurrent.DefaultPromise.notifyListenersNow(DefaultPromise.java:549)
		at io.netty.util.concurrent.DefaultPromise.notifyListeners(DefaultPromise.java:490)
		at io.netty.util.concurrent.DefaultPromise.setValue0(DefaultPromise.java:615)
		at io.netty.util.concurrent.DefaultPromise.setFailure0(DefaultPromise.java:608)
		at io.netty.util.concurrent.DefaultPromise.tryFailure(DefaultPromise.java:117)
		at io.netty.channel.nio.AbstractNioChannel$AbstractNioUnsafe.fulfillConnectPromise(AbstractNioChannel.java:321)
		at io.netty.channel.nio.AbstractNioChannel$AbstractNioUnsafe.finishConnect(AbstractNioChannel.java:337)
		at io.netty.channel.nio.NioEventLoop.processSelectedKey(NioEventLoop.java:688)
		at io.netty.channel.nio.NioEventLoop.processSelectedKeysOptimized(NioEventLoop.java:635)
		at io.netty.channel.nio.NioEventLoop.processSelectedKeys(NioEventLoop.java:552)
		at io.netty.channel.nio.NioEventLoop.run(NioEventLoop.java:514)
		... 4 common frames omitted
	Caused by: io.netty.channel.AbstractChannel$AnnotatedNoRouteToHostException: No route to host: no further information: /************:7006
	Caused by: java.net.NoRouteToHostException: No route to host: no further information
		at sun.nio.ch.SocketChannelImpl.checkConnect(Native Method)
		at sun.nio.ch.SocketChannelImpl.finishConnect(SocketChannelImpl.java:717)
		at io.netty.channel.socket.nio.NioSocketChannel.doFinishConnect(NioSocketChannel.java:330)
		at io.netty.channel.nio.AbstractNioChannel$AbstractNioUnsafe.finishConnect(AbstractNioChannel.java:334)
		at io.netty.channel.nio.NioEventLoop.processSelectedKey(NioEventLoop.java:688)
		at io.netty.channel.nio.NioEventLoop.processSelectedKeysOptimized(NioEventLoop.java:635)
		at io.netty.channel.nio.NioEventLoop.processSelectedKeys(NioEventLoop.java:552)
		at io.netty.channel.nio.NioEventLoop.run(NioEventLoop.java:514)
		at io.netty.util.concurrent.SingleThreadEventExecutor$6.run(SingleThreadEventExecutor.java:1050)
		at io.netty.util.internal.ThreadExecutorMap$2.run(ThreadExecutorMap.java:74)
		at io.netty.util.concurrent.FastThreadLocalRunnable.run(FastThreadLocalRunnable.java:30)
		at java.lang.Thread.run(Thread.java:748)
	Suppressed: io.lettuce.core.RedisConnectionException: Unable to connect to [************:7004]: No route to host: no further information: /************:7004
		at io.lettuce.core.cluster.topology.ClusterTopologyRefresh.lambda$getConnections$0(ClusterTopologyRefresh.java:290)
		at java.util.concurrent.CompletableFuture.uniWhenComplete(CompletableFuture.java:760)
		at java.util.concurrent.CompletableFuture$UniWhenComplete.tryFire(CompletableFuture.java:736)
		at java.util.concurrent.CompletableFuture.postComplete(CompletableFuture.java:474)
		at java.util.concurrent.CompletableFuture.completeExceptionally(CompletableFuture.java:1977)
		at io.lettuce.core.AbstractRedisClient.lambda$initializeChannelAsync0$4(AbstractRedisClient.java:329)
		at io.netty.util.concurrent.DefaultPromise.notifyListener0(DefaultPromise.java:577)
		at io.netty.util.concurrent.DefaultPromise.notifyListeners0(DefaultPromise.java:570)
		at io.netty.util.concurrent.DefaultPromise.notifyListenersNow(DefaultPromise.java:549)
		at io.netty.util.concurrent.DefaultPromise.notifyListeners(DefaultPromise.java:490)
		at io.netty.util.concurrent.DefaultPromise.setValue0(DefaultPromise.java:615)
		at io.netty.util.concurrent.DefaultPromise.setFailure0(DefaultPromise.java:608)
		at io.netty.util.concurrent.DefaultPromise.tryFailure(DefaultPromise.java:117)
		at io.netty.channel.nio.AbstractNioChannel$AbstractNioUnsafe.fulfillConnectPromise(AbstractNioChannel.java:321)
		at io.netty.channel.nio.AbstractNioChannel$AbstractNioUnsafe.finishConnect(AbstractNioChannel.java:337)
		at io.netty.channel.nio.NioEventLoop.processSelectedKey(NioEventLoop.java:688)
		at io.netty.channel.nio.NioEventLoop.processSelectedKeysOptimized(NioEventLoop.java:635)
		at io.netty.channel.nio.NioEventLoop.processSelectedKeys(NioEventLoop.java:552)
		at io.netty.channel.nio.NioEventLoop.run(NioEventLoop.java:514)
		... 4 common frames omitted
	Caused by: io.netty.channel.AbstractChannel$AnnotatedNoRouteToHostException: No route to host: no further information: /************:7004
	Caused by: java.net.NoRouteToHostException: No route to host: no further information
		at sun.nio.ch.SocketChannelImpl.checkConnect(Native Method)
		at sun.nio.ch.SocketChannelImpl.finishConnect(SocketChannelImpl.java:717)
		at io.netty.channel.socket.nio.NioSocketChannel.doFinishConnect(NioSocketChannel.java:330)
		at io.netty.channel.nio.AbstractNioChannel$AbstractNioUnsafe.finishConnect(AbstractNioChannel.java:334)
		at io.netty.channel.nio.NioEventLoop.processSelectedKey(NioEventLoop.java:688)
		at io.netty.channel.nio.NioEventLoop.processSelectedKeysOptimized(NioEventLoop.java:635)
		at io.netty.channel.nio.NioEventLoop.processSelectedKeys(NioEventLoop.java:552)
		at io.netty.channel.nio.NioEventLoop.run(NioEventLoop.java:514)
		at io.netty.util.concurrent.SingleThreadEventExecutor$6.run(SingleThreadEventExecutor.java:1050)
		at io.netty.util.internal.ThreadExecutorMap$2.run(ThreadExecutorMap.java:74)
		at io.netty.util.concurrent.FastThreadLocalRunnable.run(FastThreadLocalRunnable.java:30)
		at java.lang.Thread.run(Thread.java:748)
	Suppressed: io.lettuce.core.RedisConnectionException: Unable to connect to [************:7005]: No route to host: no further information: /************:7005
		at io.lettuce.core.cluster.topology.ClusterTopologyRefresh.lambda$getConnections$0(ClusterTopologyRefresh.java:290)
		at java.util.concurrent.CompletableFuture.uniWhenComplete(CompletableFuture.java:760)
		at java.util.concurrent.CompletableFuture$UniWhenComplete.tryFire(CompletableFuture.java:736)
		at java.util.concurrent.CompletableFuture.postComplete(CompletableFuture.java:474)
		at java.util.concurrent.CompletableFuture.completeExceptionally(CompletableFuture.java:1977)
		at io.lettuce.core.AbstractRedisClient.lambda$initializeChannelAsync0$4(AbstractRedisClient.java:329)
		at io.netty.util.concurrent.DefaultPromise.notifyListener0(DefaultPromise.java:577)
		at io.netty.util.concurrent.DefaultPromise.notifyListeners0(DefaultPromise.java:570)
		at io.netty.util.concurrent.DefaultPromise.notifyListenersNow(DefaultPromise.java:549)
		at io.netty.util.concurrent.DefaultPromise.notifyListeners(DefaultPromise.java:490)
		at io.netty.util.concurrent.DefaultPromise.setValue0(DefaultPromise.java:615)
		at io.netty.util.concurrent.DefaultPromise.setFailure0(DefaultPromise.java:608)
		at io.netty.util.concurrent.DefaultPromise.tryFailure(DefaultPromise.java:117)
		at io.netty.channel.nio.AbstractNioChannel$AbstractNioUnsafe.fulfillConnectPromise(AbstractNioChannel.java:321)
		at io.netty.channel.nio.AbstractNioChannel$AbstractNioUnsafe.finishConnect(AbstractNioChannel.java:337)
		at io.netty.channel.nio.NioEventLoop.processSelectedKey(NioEventLoop.java:688)
		at io.netty.channel.nio.NioEventLoop.processSelectedKeysOptimized(NioEventLoop.java:635)
		at io.netty.channel.nio.NioEventLoop.processSelectedKeys(NioEventLoop.java:552)
		at io.netty.channel.nio.NioEventLoop.run(NioEventLoop.java:514)
		... 4 common frames omitted
	Caused by: io.netty.channel.AbstractChannel$AnnotatedNoRouteToHostException: No route to host: no further information: /************:7005
	Caused by: java.net.NoRouteToHostException: No route to host: no further information
		at sun.nio.ch.SocketChannelImpl.checkConnect(Native Method)
		at sun.nio.ch.SocketChannelImpl.finishConnect(SocketChannelImpl.java:717)
		at io.netty.channel.socket.nio.NioSocketChannel.doFinishConnect(NioSocketChannel.java:330)
		at io.netty.channel.nio.AbstractNioChannel$AbstractNioUnsafe.finishConnect(AbstractNioChannel.java:334)
		at io.netty.channel.nio.NioEventLoop.processSelectedKey(NioEventLoop.java:688)
		at io.netty.channel.nio.NioEventLoop.processSelectedKeysOptimized(NioEventLoop.java:635)
		at io.netty.channel.nio.NioEventLoop.processSelectedKeys(NioEventLoop.java:552)
		at io.netty.channel.nio.NioEventLoop.run(NioEventLoop.java:514)
		at io.netty.util.concurrent.SingleThreadEventExecutor$6.run(SingleThreadEventExecutor.java:1050)
		at io.netty.util.internal.ThreadExecutorMap$2.run(ThreadExecutorMap.java:74)
		at io.netty.util.concurrent.FastThreadLocalRunnable.run(FastThreadLocalRunnable.java:30)
		at java.lang.Thread.run(Thread.java:748)
	Suppressed: io.lettuce.core.RedisConnectionException: Unable to connect to [************:7003]: No route to host: no further information: /************:7003
		at io.lettuce.core.cluster.topology.ClusterTopologyRefresh.lambda$getConnections$0(ClusterTopologyRefresh.java:290)
		at java.util.concurrent.CompletableFuture.uniWhenComplete(CompletableFuture.java:760)
		at java.util.concurrent.CompletableFuture$UniWhenComplete.tryFire(CompletableFuture.java:736)
		at java.util.concurrent.CompletableFuture.postComplete(CompletableFuture.java:474)
		at java.util.concurrent.CompletableFuture.completeExceptionally(CompletableFuture.java:1977)
		at io.lettuce.core.AbstractRedisClient.lambda$initializeChannelAsync0$4(AbstractRedisClient.java:329)
		at io.netty.util.concurrent.DefaultPromise.notifyListener0(DefaultPromise.java:577)
		at io.netty.util.concurrent.DefaultPromise.notifyListeners0(DefaultPromise.java:570)
		at io.netty.util.concurrent.DefaultPromise.notifyListenersNow(DefaultPromise.java:549)
		at io.netty.util.concurrent.DefaultPromise.notifyListeners(DefaultPromise.java:490)
		at io.netty.util.concurrent.DefaultPromise.setValue0(DefaultPromise.java:615)
		at io.netty.util.concurrent.DefaultPromise.setFailure0(DefaultPromise.java:608)
		at io.netty.util.concurrent.DefaultPromise.tryFailure(DefaultPromise.java:117)
		at io.netty.channel.nio.AbstractNioChannel$AbstractNioUnsafe.fulfillConnectPromise(AbstractNioChannel.java:321)
		at io.netty.channel.nio.AbstractNioChannel$AbstractNioUnsafe.finishConnect(AbstractNioChannel.java:337)
		at io.netty.channel.nio.NioEventLoop.processSelectedKey(NioEventLoop.java:688)
		at io.netty.channel.nio.NioEventLoop.processSelectedKeysOptimized(NioEventLoop.java:635)
		at io.netty.channel.nio.NioEventLoop.processSelectedKeys(NioEventLoop.java:552)
		at io.netty.channel.nio.NioEventLoop.run(NioEventLoop.java:514)
		... 4 common frames omitted
	Caused by: io.netty.channel.AbstractChannel$AnnotatedNoRouteToHostException: No route to host: no further information: /************:7003
	Caused by: java.net.NoRouteToHostException: No route to host: no further information
		at sun.nio.ch.SocketChannelImpl.checkConnect(Native Method)
		at sun.nio.ch.SocketChannelImpl.finishConnect(SocketChannelImpl.java:717)
		at io.netty.channel.socket.nio.NioSocketChannel.doFinishConnect(NioSocketChannel.java:330)
		at io.netty.channel.nio.AbstractNioChannel$AbstractNioUnsafe.finishConnect(AbstractNioChannel.java:334)
		at io.netty.channel.nio.NioEventLoop.processSelectedKey(NioEventLoop.java:688)
		at io.netty.channel.nio.NioEventLoop.processSelectedKeysOptimized(NioEventLoop.java:635)
		at io.netty.channel.nio.NioEventLoop.processSelectedKeys(NioEventLoop.java:552)
		at io.netty.channel.nio.NioEventLoop.run(NioEventLoop.java:514)
		at io.netty.util.concurrent.SingleThreadEventExecutor$6.run(SingleThreadEventExecutor.java:1050)
		at io.netty.util.internal.ThreadExecutorMap$2.run(ThreadExecutorMap.java:74)
		at io.netty.util.concurrent.FastThreadLocalRunnable.run(FastThreadLocalRunnable.java:30)
		at java.lang.Thread.run(Thread.java:748)
	Suppressed: io.lettuce.core.RedisConnectionException: Unable to connect to [************:7001]: No route to host: no further information: /************:7001
		at io.lettuce.core.cluster.topology.ClusterTopologyRefresh.lambda$getConnections$0(ClusterTopologyRefresh.java:290)
		at java.util.concurrent.CompletableFuture.uniWhenComplete(CompletableFuture.java:760)
		at java.util.concurrent.CompletableFuture$UniWhenComplete.tryFire(CompletableFuture.java:736)
		at java.util.concurrent.CompletableFuture.postComplete(CompletableFuture.java:474)
		at java.util.concurrent.CompletableFuture.completeExceptionally(CompletableFuture.java:1977)
		at io.lettuce.core.AbstractRedisClient.lambda$initializeChannelAsync0$4(AbstractRedisClient.java:329)
		at io.netty.util.concurrent.DefaultPromise.notifyListener0(DefaultPromise.java:577)
		at io.netty.util.concurrent.DefaultPromise.notifyListeners0(DefaultPromise.java:570)
		at io.netty.util.concurrent.DefaultPromise.notifyListenersNow(DefaultPromise.java:549)
		at io.netty.util.concurrent.DefaultPromise.notifyListeners(DefaultPromise.java:490)
		at io.netty.util.concurrent.DefaultPromise.setValue0(DefaultPromise.java:615)
		at io.netty.util.concurrent.DefaultPromise.setFailure0(DefaultPromise.java:608)
		at io.netty.util.concurrent.DefaultPromise.tryFailure(DefaultPromise.java:117)
		at io.netty.channel.nio.AbstractNioChannel$AbstractNioUnsafe.fulfillConnectPromise(AbstractNioChannel.java:321)
		at io.netty.channel.nio.AbstractNioChannel$AbstractNioUnsafe.finishConnect(AbstractNioChannel.java:337)
		at io.netty.channel.nio.NioEventLoop.processSelectedKey(NioEventLoop.java:688)
		at io.netty.channel.nio.NioEventLoop.processSelectedKeysOptimized(NioEventLoop.java:635)
		at io.netty.channel.nio.NioEventLoop.processSelectedKeys(NioEventLoop.java:552)
		at io.netty.channel.nio.NioEventLoop.run(NioEventLoop.java:514)
		... 4 common frames omitted
	Caused by: io.netty.channel.AbstractChannel$AnnotatedNoRouteToHostException: No route to host: no further information: /************:7001
	Caused by: java.net.NoRouteToHostException: No route to host: no further information
		at sun.nio.ch.SocketChannelImpl.checkConnect(Native Method)
		at sun.nio.ch.SocketChannelImpl.finishConnect(SocketChannelImpl.java:717)
		at io.netty.channel.socket.nio.NioSocketChannel.doFinishConnect(NioSocketChannel.java:330)
		at io.netty.channel.nio.AbstractNioChannel$AbstractNioUnsafe.finishConnect(AbstractNioChannel.java:334)
		at io.netty.channel.nio.NioEventLoop.processSelectedKey(NioEventLoop.java:688)
		at io.netty.channel.nio.NioEventLoop.processSelectedKeysOptimized(NioEventLoop.java:635)
		at io.netty.channel.nio.NioEventLoop.processSelectedKeys(NioEventLoop.java:552)
		at io.netty.channel.nio.NioEventLoop.run(NioEventLoop.java:514)
		at io.netty.util.concurrent.SingleThreadEventExecutor$6.run(SingleThreadEventExecutor.java:1050)
		at io.netty.util.internal.ThreadExecutorMap$2.run(ThreadExecutorMap.java:74)
		at io.netty.util.concurrent.FastThreadLocalRunnable.run(FastThreadLocalRunnable.java:30)
		at java.lang.Thread.run(Thread.java:748)
Caused by: io.lettuce.core.RedisConnectionException: Unable to connect to [************:7002]: No route to host: no further information: /************:7002
	at io.lettuce.core.cluster.topology.ClusterTopologyRefresh.lambda$getConnections$0(ClusterTopologyRefresh.java:290)
	at java.util.concurrent.CompletableFuture.uniWhenComplete(CompletableFuture.java:760)
	at java.util.concurrent.CompletableFuture$UniWhenComplete.tryFire(CompletableFuture.java:736)
	at java.util.concurrent.CompletableFuture.postComplete(CompletableFuture.java:474)
	at java.util.concurrent.CompletableFuture.completeExceptionally(CompletableFuture.java:1977)
	at io.lettuce.core.AbstractRedisClient.lambda$initializeChannelAsync0$4(AbstractRedisClient.java:329)
	at io.netty.util.concurrent.DefaultPromise.notifyListener0(DefaultPromise.java:577)
	at io.netty.util.concurrent.DefaultPromise.notifyListeners0(DefaultPromise.java:570)
	at io.netty.util.concurrent.DefaultPromise.notifyListenersNow(DefaultPromise.java:549)
	at io.netty.util.concurrent.DefaultPromise.notifyListeners(DefaultPromise.java:490)
	at io.netty.util.concurrent.DefaultPromise.setValue0(DefaultPromise.java:615)
	at io.netty.util.concurrent.DefaultPromise.setFailure0(DefaultPromise.java:608)
	at io.netty.util.concurrent.DefaultPromise.tryFailure(DefaultPromise.java:117)
	at io.netty.channel.nio.AbstractNioChannel$AbstractNioUnsafe.fulfillConnectPromise(AbstractNioChannel.java:321)
	at io.netty.channel.nio.AbstractNioChannel$AbstractNioUnsafe.finishConnect(AbstractNioChannel.java:337)
	at io.netty.channel.nio.NioEventLoop.processSelectedKey(NioEventLoop.java:688)
	at io.netty.channel.nio.NioEventLoop.processSelectedKeysOptimized(NioEventLoop.java:635)
	at io.netty.channel.nio.NioEventLoop.processSelectedKeys(NioEventLoop.java:552)
	at io.netty.channel.nio.NioEventLoop.run(NioEventLoop.java:514)
	... 4 common frames omitted
Caused by: io.netty.channel.AbstractChannel$AnnotatedNoRouteToHostException: No route to host: no further information: /************:7002
Caused by: java.net.NoRouteToHostException: No route to host: no further information
	at sun.nio.ch.SocketChannelImpl.checkConnect(Native Method)
	at sun.nio.ch.SocketChannelImpl.finishConnect(SocketChannelImpl.java:717)
	at io.netty.channel.socket.nio.NioSocketChannel.doFinishConnect(NioSocketChannel.java:330)
	at io.netty.channel.nio.AbstractNioChannel$AbstractNioUnsafe.finishConnect(AbstractNioChannel.java:334)
	at io.netty.channel.nio.NioEventLoop.processSelectedKey(NioEventLoop.java:688)
	at io.netty.channel.nio.NioEventLoop.processSelectedKeysOptimized(NioEventLoop.java:635)
	at io.netty.channel.nio.NioEventLoop.processSelectedKeys(NioEventLoop.java:552)
	at io.netty.channel.nio.NioEventLoop.run(NioEventLoop.java:514)
	at io.netty.util.concurrent.SingleThreadEventExecutor$6.run(SingleThreadEventExecutor.java:1050)
	at io.netty.util.internal.ThreadExecutorMap$2.run(ThreadExecutorMap.java:74)
	at io.netty.util.concurrent.FastThreadLocalRunnable.run(FastThreadLocalRunnable.java:30)
	at java.lang.Thread.run(Thread.java:748)
2025-06-01 02:32:54.605 [lettuce-nioEventLoop-4-12] WARN  io.lettuce.core.cluster.topology.ClusterTopologyRefresh.lambda$getConnections$0 Line:284 - Unable to connect to [************:7001]: No route to host: no further information: /************:7001
2025-06-01 02:32:54.605 [lettuce-nioEventLoop-4-1] WARN  io.lettuce.core.cluster.topology.ClusterTopologyRefresh.lambda$getConnections$0 Line:284 - Unable to connect to [************:7002]: No route to host: no further information: /************:7002
2025-06-01 02:32:54.605 [lettuce-nioEventLoop-4-3] WARN  io.lettuce.core.cluster.topology.ClusterTopologyRefresh.lambda$getConnections$0 Line:284 - Unable to connect to [************:7004]: No route to host: no further information: /************:7004
2025-06-01 02:32:54.605 [lettuce-nioEventLoop-4-2] WARN  io.lettuce.core.cluster.topology.ClusterTopologyRefresh.lambda$getConnections$0 Line:284 - Unable to connect to [************:7003]: No route to host: no further information: /************:7003
2025-06-01 02:32:54.609 [lettuce-nioEventLoop-4-4] WARN  io.lettuce.core.cluster.topology.ClusterTopologyRefresh.lambda$getConnections$0 Line:284 - Unable to connect to [************:7005]: No route to host: no further information: /************:7005
2025-06-01 02:32:54.609 [lettuce-nioEventLoop-4-5] WARN  io.lettuce.core.cluster.topology.ClusterTopologyRefresh.lambda$getConnections$0 Line:284 - Unable to connect to [************:7006]: No route to host: no further information: /************:7006
2025-06-01 02:32:54.610 [lettuce-nioEventLoop-4-6] WARN  io.lettuce.core.cluster.topology.ClusterTopologyRefresh.lambda$getConnections$0 Line:284 - Unable to connect to [************:7006]: No route to host: no further information: /************:7006
2025-06-01 02:32:54.610 [lettuce-nioEventLoop-4-10] WARN  io.lettuce.core.cluster.topology.ClusterTopologyRefresh.lambda$getConnections$0 Line:284 - Unable to connect to [************:7003]: No route to host: no further information: /************:7003
2025-06-01 02:32:54.610 [lettuce-nioEventLoop-4-7] WARN  io.lettuce.core.cluster.topology.ClusterTopologyRefresh.lambda$getConnections$0 Line:284 - Unable to connect to [************:7004]: No route to host: no further information: /************:7004
2025-06-01 02:32:54.610 [lettuce-nioEventLoop-4-9] WARN  io.lettuce.core.cluster.topology.ClusterTopologyRefresh.lambda$getConnections$0 Line:284 - Unable to connect to [************:7002]: No route to host: no further information: /************:7002
2025-06-01 02:32:54.610 [lettuce-nioEventLoop-4-11] WARN  io.lettuce.core.cluster.topology.ClusterTopologyRefresh.lambda$getConnections$0 Line:284 - Unable to connect to [************:7001]: No route to host: no further information: /************:7001
2025-06-01 02:32:54.610 [lettuce-nioEventLoop-4-8] WARN  io.lettuce.core.cluster.topology.ClusterTopologyRefresh.lambda$getConnections$0 Line:284 - Unable to connect to [************:7005]: No route to host: no further information: /************:7005
2025-06-01 02:32:54.623 [lettuce-eventExecutorLoop-1-3] WARN  io.lettuce.core.cluster.ClusterTopologyRefreshScheduler.doRun Line:258 - Cannot refresh Redis Cluster topology
io.lettuce.core.RedisException: Cannot retrieve initial cluster partitions from initial URIs [RedisURI [host='************', port=7006], RedisURI [host='************', port=7004], RedisURI [host='************', port=7005], RedisURI [host='************', port=7002], RedisURI [host='************', port=7003], RedisURI [host='************', port=7001]]
	at io.lettuce.core.cluster.RedisClusterClient.loadPartitions(RedisClusterClient.java:848)
	at io.lettuce.core.cluster.RedisClusterClient.reloadPartitions(RedisClusterClient.java:784)
	at io.lettuce.core.cluster.ClusterTopologyRefreshScheduler$ClusterTopologyRefreshTask.doRun(ClusterTopologyRefreshScheduler.java:256)
	at io.lettuce.core.cluster.ClusterTopologyRefreshScheduler$ClusterTopologyRefreshTask.run(ClusterTopologyRefreshScheduler.java:236)
	at io.netty.util.concurrent.PromiseTask.runTask(PromiseTask.java:98)
	at io.netty.util.concurrent.PromiseTask.run(PromiseTask.java:106)
	at io.netty.util.concurrent.DefaultEventExecutor.run(DefaultEventExecutor.java:66)
	at io.netty.util.concurrent.SingleThreadEventExecutor$6.run(SingleThreadEventExecutor.java:1050)
	at io.netty.util.internal.ThreadExecutorMap$2.run(ThreadExecutorMap.java:74)
	at io.netty.util.concurrent.FastThreadLocalRunnable.run(FastThreadLocalRunnable.java:30)
	at java.lang.Thread.run(Thread.java:748)
	Suppressed: io.lettuce.core.RedisConnectionException: Unable to establish a connection to Redis Cluster at [RedisURI [host='************', port=7001], RedisURI [host='************', port=7002], RedisURI [host='************', port=7003], RedisURI [host='************', port=7004], RedisURI [host='************', port=7005], RedisURI [host='************', port=7006]]
		at io.lettuce.core.cluster.topology.AsyncConnections.get(AsyncConnections.java:89)
		at io.lettuce.core.cluster.topology.ClusterTopologyRefresh.loadViews(ClusterTopologyRefresh.java:76)
		at io.lettuce.core.cluster.RedisClusterClient.doLoadPartitions(RedisClusterClient.java:865)
		at io.lettuce.core.cluster.RedisClusterClient.loadPartitions(RedisClusterClient.java:838)
		... 10 common frames omitted
		Suppressed: io.lettuce.core.RedisConnectionException: Unable to connect to [************:7002]: No route to host: no further information: /************:7002
			at io.lettuce.core.cluster.topology.ClusterTopologyRefresh.lambda$getConnections$0(ClusterTopologyRefresh.java:290)
			at java.util.concurrent.CompletableFuture.uniWhenComplete(CompletableFuture.java:760)
			at java.util.concurrent.CompletableFuture$UniWhenComplete.tryFire(CompletableFuture.java:736)
			at java.util.concurrent.CompletableFuture.postComplete(CompletableFuture.java:474)
			at java.util.concurrent.CompletableFuture.completeExceptionally(CompletableFuture.java:1977)
			at io.lettuce.core.AbstractRedisClient.lambda$initializeChannelAsync0$4(AbstractRedisClient.java:329)
			at io.netty.util.concurrent.DefaultPromise.notifyListener0(DefaultPromise.java:577)
			at io.netty.util.concurrent.DefaultPromise.notifyListeners0(DefaultPromise.java:570)
			at io.netty.util.concurrent.DefaultPromise.notifyListenersNow(DefaultPromise.java:549)
			at io.netty.util.concurrent.DefaultPromise.notifyListeners(DefaultPromise.java:490)
			at io.netty.util.concurrent.DefaultPromise.setValue0(DefaultPromise.java:615)
			at io.netty.util.concurrent.DefaultPromise.setFailure0(DefaultPromise.java:608)
			at io.netty.util.concurrent.DefaultPromise.tryFailure(DefaultPromise.java:117)
			at io.netty.channel.nio.AbstractNioChannel$AbstractNioUnsafe.fulfillConnectPromise(AbstractNioChannel.java:321)
			at io.netty.channel.nio.AbstractNioChannel$AbstractNioUnsafe.finishConnect(AbstractNioChannel.java:337)
			at io.netty.channel.nio.NioEventLoop.processSelectedKey(NioEventLoop.java:688)
			at io.netty.channel.nio.NioEventLoop.processSelectedKeysOptimized(NioEventLoop.java:635)
			at io.netty.channel.nio.NioEventLoop.processSelectedKeys(NioEventLoop.java:552)
			at io.netty.channel.nio.NioEventLoop.run(NioEventLoop.java:514)
			... 4 common frames omitted
		Caused by: io.netty.channel.AbstractChannel$AnnotatedNoRouteToHostException: No route to host: no further information: /************:7002
		Caused by: java.net.NoRouteToHostException: No route to host: no further information
			at sun.nio.ch.SocketChannelImpl.checkConnect(Native Method)
			at sun.nio.ch.SocketChannelImpl.finishConnect(SocketChannelImpl.java:717)
			at io.netty.channel.socket.nio.NioSocketChannel.doFinishConnect(NioSocketChannel.java:330)
			at io.netty.channel.nio.AbstractNioChannel$AbstractNioUnsafe.finishConnect(AbstractNioChannel.java:334)
			at io.netty.channel.nio.NioEventLoop.processSelectedKey(NioEventLoop.java:688)
			at io.netty.channel.nio.NioEventLoop.processSelectedKeysOptimized(NioEventLoop.java:635)
			at io.netty.channel.nio.NioEventLoop.processSelectedKeys(NioEventLoop.java:552)
			at io.netty.channel.nio.NioEventLoop.run(NioEventLoop.java:514)
			at io.netty.util.concurrent.SingleThreadEventExecutor$6.run(SingleThreadEventExecutor.java:1050)
			at io.netty.util.internal.ThreadExecutorMap$2.run(ThreadExecutorMap.java:74)
			at io.netty.util.concurrent.FastThreadLocalRunnable.run(FastThreadLocalRunnable.java:30)
			at java.lang.Thread.run(Thread.java:748)
		Suppressed: io.lettuce.core.RedisConnectionException: Unable to connect to [************:7004]: No route to host: no further information: /************:7004
			at io.lettuce.core.cluster.topology.ClusterTopologyRefresh.lambda$getConnections$0(ClusterTopologyRefresh.java:290)
			at java.util.concurrent.CompletableFuture.uniWhenComplete(CompletableFuture.java:760)
			at java.util.concurrent.CompletableFuture$UniWhenComplete.tryFire(CompletableFuture.java:736)
			at java.util.concurrent.CompletableFuture.postComplete(CompletableFuture.java:474)
			at java.util.concurrent.CompletableFuture.completeExceptionally(CompletableFuture.java:1977)
			at io.lettuce.core.AbstractRedisClient.lambda$initializeChannelAsync0$4(AbstractRedisClient.java:329)
			at io.netty.util.concurrent.DefaultPromise.notifyListener0(DefaultPromise.java:577)
			at io.netty.util.concurrent.DefaultPromise.notifyListeners0(DefaultPromise.java:570)
			at io.netty.util.concurrent.DefaultPromise.notifyListenersNow(DefaultPromise.java:549)
			at io.netty.util.concurrent.DefaultPromise.notifyListeners(DefaultPromise.java:490)
			at io.netty.util.concurrent.DefaultPromise.setValue0(DefaultPromise.java:615)
			at io.netty.util.concurrent.DefaultPromise.setFailure0(DefaultPromise.java:608)
			at io.netty.util.concurrent.DefaultPromise.tryFailure(DefaultPromise.java:117)
			at io.netty.channel.nio.AbstractNioChannel$AbstractNioUnsafe.fulfillConnectPromise(AbstractNioChannel.java:321)
			at io.netty.channel.nio.AbstractNioChannel$AbstractNioUnsafe.finishConnect(AbstractNioChannel.java:337)
			at io.netty.channel.nio.NioEventLoop.processSelectedKey(NioEventLoop.java:688)
			at io.netty.channel.nio.NioEventLoop.processSelectedKeysOptimized(NioEventLoop.java:635)
			at io.netty.channel.nio.NioEventLoop.processSelectedKeys(NioEventLoop.java:552)
			at io.netty.channel.nio.NioEventLoop.run(NioEventLoop.java:514)
			... 4 common frames omitted
		Caused by: io.netty.channel.AbstractChannel$AnnotatedNoRouteToHostException: No route to host: no further information: /************:7004
		Caused by: java.net.NoRouteToHostException: No route to host: no further information
			at sun.nio.ch.SocketChannelImpl.checkConnect(Native Method)
			at sun.nio.ch.SocketChannelImpl.finishConnect(SocketChannelImpl.java:717)
			at io.netty.channel.socket.nio.NioSocketChannel.doFinishConnect(NioSocketChannel.java:330)
			at io.netty.channel.nio.AbstractNioChannel$AbstractNioUnsafe.finishConnect(AbstractNioChannel.java:334)
			at io.netty.channel.nio.NioEventLoop.processSelectedKey(NioEventLoop.java:688)
			at io.netty.channel.nio.NioEventLoop.processSelectedKeysOptimized(NioEventLoop.java:635)
			at io.netty.channel.nio.NioEventLoop.processSelectedKeys(NioEventLoop.java:552)
			at io.netty.channel.nio.NioEventLoop.run(NioEventLoop.java:514)
			at io.netty.util.concurrent.SingleThreadEventExecutor$6.run(SingleThreadEventExecutor.java:1050)
			at io.netty.util.internal.ThreadExecutorMap$2.run(ThreadExecutorMap.java:74)
			at io.netty.util.concurrent.FastThreadLocalRunnable.run(FastThreadLocalRunnable.java:30)
			at java.lang.Thread.run(Thread.java:748)
		Suppressed: io.lettuce.core.RedisConnectionException: Unable to connect to [************:7003]: No route to host: no further information: /************:7003
			at io.lettuce.core.cluster.topology.ClusterTopologyRefresh.lambda$getConnections$0(ClusterTopologyRefresh.java:290)
			at java.util.concurrent.CompletableFuture.uniWhenComplete(CompletableFuture.java:760)
			at java.util.concurrent.CompletableFuture$UniWhenComplete.tryFire(CompletableFuture.java:736)
			at java.util.concurrent.CompletableFuture.postComplete(CompletableFuture.java:474)
			at java.util.concurrent.CompletableFuture.completeExceptionally(CompletableFuture.java:1977)
			at io.lettuce.core.AbstractRedisClient.lambda$initializeChannelAsync0$4(AbstractRedisClient.java:329)
			at io.netty.util.concurrent.DefaultPromise.notifyListener0(DefaultPromise.java:577)
			at io.netty.util.concurrent.DefaultPromise.notifyListeners0(DefaultPromise.java:570)
			at io.netty.util.concurrent.DefaultPromise.notifyListenersNow(DefaultPromise.java:549)
			at io.netty.util.concurrent.DefaultPromise.notifyListeners(DefaultPromise.java:490)
			at io.netty.util.concurrent.DefaultPromise.setValue0(DefaultPromise.java:615)
			at io.netty.util.concurrent.DefaultPromise.setFailure0(DefaultPromise.java:608)
			at io.netty.util.concurrent.DefaultPromise.tryFailure(DefaultPromise.java:117)
			at io.netty.channel.nio.AbstractNioChannel$AbstractNioUnsafe.fulfillConnectPromise(AbstractNioChannel.java:321)
			at io.netty.channel.nio.AbstractNioChannel$AbstractNioUnsafe.finishConnect(AbstractNioChannel.java:337)
			at io.netty.channel.nio.NioEventLoop.processSelectedKey(NioEventLoop.java:688)
			at io.netty.channel.nio.NioEventLoop.processSelectedKeysOptimized(NioEventLoop.java:635)
			at io.netty.channel.nio.NioEventLoop.processSelectedKeys(NioEventLoop.java:552)
			at io.netty.channel.nio.NioEventLoop.run(NioEventLoop.java:514)
			... 4 common frames omitted
		Caused by: io.netty.channel.AbstractChannel$AnnotatedNoRouteToHostException: No route to host: no further information: /************:7003
		Caused by: java.net.NoRouteToHostException: No route to host: no further information
			at sun.nio.ch.SocketChannelImpl.checkConnect(Native Method)
			at sun.nio.ch.SocketChannelImpl.finishConnect(SocketChannelImpl.java:717)
			at io.netty.channel.socket.nio.NioSocketChannel.doFinishConnect(NioSocketChannel.java:330)
			at io.netty.channel.nio.AbstractNioChannel$AbstractNioUnsafe.finishConnect(AbstractNioChannel.java:334)
			at io.netty.channel.nio.NioEventLoop.processSelectedKey(NioEventLoop.java:688)
			at io.netty.channel.nio.NioEventLoop.processSelectedKeysOptimized(NioEventLoop.java:635)
			at io.netty.channel.nio.NioEventLoop.processSelectedKeys(NioEventLoop.java:552)
			at io.netty.channel.nio.NioEventLoop.run(NioEventLoop.java:514)
			at io.netty.util.concurrent.SingleThreadEventExecutor$6.run(SingleThreadEventExecutor.java:1050)
			at io.netty.util.internal.ThreadExecutorMap$2.run(ThreadExecutorMap.java:74)
			at io.netty.util.concurrent.FastThreadLocalRunnable.run(FastThreadLocalRunnable.java:30)
			at java.lang.Thread.run(Thread.java:748)
		Suppressed: io.lettuce.core.RedisConnectionException: Unable to connect to [************:7005]: No route to host: no further information: /************:7005
			at io.lettuce.core.cluster.topology.ClusterTopologyRefresh.lambda$getConnections$0(ClusterTopologyRefresh.java:290)
			at java.util.concurrent.CompletableFuture.uniWhenComplete(CompletableFuture.java:760)
			at java.util.concurrent.CompletableFuture$UniWhenComplete.tryFire(CompletableFuture.java:736)
			at java.util.concurrent.CompletableFuture.postComplete(CompletableFuture.java:474)
			at java.util.concurrent.CompletableFuture.completeExceptionally(CompletableFuture.java:1977)
			at io.lettuce.core.AbstractRedisClient.lambda$initializeChannelAsync0$4(AbstractRedisClient.java:329)
			at io.netty.util.concurrent.DefaultPromise.notifyListener0(DefaultPromise.java:577)
			at io.netty.util.concurrent.DefaultPromise.notifyListeners0(DefaultPromise.java:570)
			at io.netty.util.concurrent.DefaultPromise.notifyListenersNow(DefaultPromise.java:549)
			at io.netty.util.concurrent.DefaultPromise.notifyListeners(DefaultPromise.java:490)
			at io.netty.util.concurrent.DefaultPromise.setValue0(DefaultPromise.java:615)
			at io.netty.util.concurrent.DefaultPromise.setFailure0(DefaultPromise.java:608)
			at io.netty.util.concurrent.DefaultPromise.tryFailure(DefaultPromise.java:117)
			at io.netty.channel.nio.AbstractNioChannel$AbstractNioUnsafe.fulfillConnectPromise(AbstractNioChannel.java:321)
			at io.netty.channel.nio.AbstractNioChannel$AbstractNioUnsafe.finishConnect(AbstractNioChannel.java:337)
			at io.netty.channel.nio.NioEventLoop.processSelectedKey(NioEventLoop.java:688)
			at io.netty.channel.nio.NioEventLoop.processSelectedKeysOptimized(NioEventLoop.java:635)
			at io.netty.channel.nio.NioEventLoop.processSelectedKeys(NioEventLoop.java:552)
			at io.netty.channel.nio.NioEventLoop.run(NioEventLoop.java:514)
			... 4 common frames omitted
		Caused by: io.netty.channel.AbstractChannel$AnnotatedNoRouteToHostException: No route to host: no further information: /************:7005
		Caused by: java.net.NoRouteToHostException: No route to host: no further information
			at sun.nio.ch.SocketChannelImpl.checkConnect(Native Method)
			at sun.nio.ch.SocketChannelImpl.finishConnect(SocketChannelImpl.java:717)
			at io.netty.channel.socket.nio.NioSocketChannel.doFinishConnect(NioSocketChannel.java:330)
			at io.netty.channel.nio.AbstractNioChannel$AbstractNioUnsafe.finishConnect(AbstractNioChannel.java:334)
			at io.netty.channel.nio.NioEventLoop.processSelectedKey(NioEventLoop.java:688)
			at io.netty.channel.nio.NioEventLoop.processSelectedKeysOptimized(NioEventLoop.java:635)
			at io.netty.channel.nio.NioEventLoop.processSelectedKeys(NioEventLoop.java:552)
			at io.netty.channel.nio.NioEventLoop.run(NioEventLoop.java:514)
			at io.netty.util.concurrent.SingleThreadEventExecutor$6.run(SingleThreadEventExecutor.java:1050)
			at io.netty.util.internal.ThreadExecutorMap$2.run(ThreadExecutorMap.java:74)
			at io.netty.util.concurrent.FastThreadLocalRunnable.run(FastThreadLocalRunnable.java:30)
			at java.lang.Thread.run(Thread.java:748)
		Suppressed: io.lettuce.core.RedisConnectionException: Unable to connect to [************:7006]: No route to host: no further information: /************:7006
			at io.lettuce.core.cluster.topology.ClusterTopologyRefresh.lambda$getConnections$0(ClusterTopologyRefresh.java:290)
			at java.util.concurrent.CompletableFuture.uniWhenComplete(CompletableFuture.java:760)
			at java.util.concurrent.CompletableFuture$UniWhenComplete.tryFire(CompletableFuture.java:736)
			at java.util.concurrent.CompletableFuture.postComplete(CompletableFuture.java:474)
			at java.util.concurrent.CompletableFuture.completeExceptionally(CompletableFuture.java:1977)
			at io.lettuce.core.AbstractRedisClient.lambda$initializeChannelAsync0$4(AbstractRedisClient.java:329)
			at io.netty.util.concurrent.DefaultPromise.notifyListener0(DefaultPromise.java:577)
			at io.netty.util.concurrent.DefaultPromise.notifyListeners0(DefaultPromise.java:570)
			at io.netty.util.concurrent.DefaultPromise.notifyListenersNow(DefaultPromise.java:549)
			at io.netty.util.concurrent.DefaultPromise.notifyListeners(DefaultPromise.java:490)
			at io.netty.util.concurrent.DefaultPromise.setValue0(DefaultPromise.java:615)
			at io.netty.util.concurrent.DefaultPromise.setFailure0(DefaultPromise.java:608)
			at io.netty.util.concurrent.DefaultPromise.tryFailure(DefaultPromise.java:117)
			at io.netty.channel.nio.AbstractNioChannel$AbstractNioUnsafe.fulfillConnectPromise(AbstractNioChannel.java:321)
			at io.netty.channel.nio.AbstractNioChannel$AbstractNioUnsafe.finishConnect(AbstractNioChannel.java:337)
			at io.netty.channel.nio.NioEventLoop.processSelectedKey(NioEventLoop.java:688)
			at io.netty.channel.nio.NioEventLoop.processSelectedKeysOptimized(NioEventLoop.java:635)
			at io.netty.channel.nio.NioEventLoop.processSelectedKeys(NioEventLoop.java:552)
			at io.netty.channel.nio.NioEventLoop.run(NioEventLoop.java:514)
			... 4 common frames omitted
		Caused by: io.netty.channel.AbstractChannel$AnnotatedNoRouteToHostException: No route to host: no further information: /************:7006
		Caused by: java.net.NoRouteToHostException: No route to host: no further information
			at sun.nio.ch.SocketChannelImpl.checkConnect(Native Method)
			at sun.nio.ch.SocketChannelImpl.finishConnect(SocketChannelImpl.java:717)
			at io.netty.channel.socket.nio.NioSocketChannel.doFinishConnect(NioSocketChannel.java:330)
			at io.netty.channel.nio.AbstractNioChannel$AbstractNioUnsafe.finishConnect(AbstractNioChannel.java:334)
			at io.netty.channel.nio.NioEventLoop.processSelectedKey(NioEventLoop.java:688)
			at io.netty.channel.nio.NioEventLoop.processSelectedKeysOptimized(NioEventLoop.java:635)
			at io.netty.channel.nio.NioEventLoop.processSelectedKeys(NioEventLoop.java:552)
			at io.netty.channel.nio.NioEventLoop.run(NioEventLoop.java:514)
			at io.netty.util.concurrent.SingleThreadEventExecutor$6.run(SingleThreadEventExecutor.java:1050)
			at io.netty.util.internal.ThreadExecutorMap$2.run(ThreadExecutorMap.java:74)
			at io.netty.util.concurrent.FastThreadLocalRunnable.run(FastThreadLocalRunnable.java:30)
			at java.lang.Thread.run(Thread.java:748)
	Caused by: io.lettuce.core.RedisConnectionException: Unable to connect to [************:7001]: No route to host: no further information: /************:7001
		at io.lettuce.core.cluster.topology.ClusterTopologyRefresh.lambda$getConnections$0(ClusterTopologyRefresh.java:290)
		at java.util.concurrent.CompletableFuture.uniWhenComplete(CompletableFuture.java:760)
		at java.util.concurrent.CompletableFuture$UniWhenComplete.tryFire(CompletableFuture.java:736)
		at java.util.concurrent.CompletableFuture.postComplete(CompletableFuture.java:474)
		at java.util.concurrent.CompletableFuture.completeExceptionally(CompletableFuture.java:1977)
		at io.lettuce.core.AbstractRedisClient.lambda$initializeChannelAsync0$4(AbstractRedisClient.java:329)
		at io.netty.util.concurrent.DefaultPromise.notifyListener0(DefaultPromise.java:577)
		at io.netty.util.concurrent.DefaultPromise.notifyListeners0(DefaultPromise.java:570)
		at io.netty.util.concurrent.DefaultPromise.notifyListenersNow(DefaultPromise.java:549)
		at io.netty.util.concurrent.DefaultPromise.notifyListeners(DefaultPromise.java:490)
		at io.netty.util.concurrent.DefaultPromise.setValue0(DefaultPromise.java:615)
		at io.netty.util.concurrent.DefaultPromise.setFailure0(DefaultPromise.java:608)
		at io.netty.util.concurrent.DefaultPromise.tryFailure(DefaultPromise.java:117)
		at io.netty.channel.nio.AbstractNioChannel$AbstractNioUnsafe.fulfillConnectPromise(AbstractNioChannel.java:321)
		at io.netty.channel.nio.AbstractNioChannel$AbstractNioUnsafe.finishConnect(AbstractNioChannel.java:337)
		at io.netty.channel.nio.NioEventLoop.processSelectedKey(NioEventLoop.java:688)
		at io.netty.channel.nio.NioEventLoop.processSelectedKeysOptimized(NioEventLoop.java:635)
		at io.netty.channel.nio.NioEventLoop.processSelectedKeys(NioEventLoop.java:552)
		at io.netty.channel.nio.NioEventLoop.run(NioEventLoop.java:514)
		... 4 common frames omitted
	Caused by: io.netty.channel.AbstractChannel$AnnotatedNoRouteToHostException: No route to host: no further information: /************:7001
	Caused by: java.net.NoRouteToHostException: No route to host: no further information
		at sun.nio.ch.SocketChannelImpl.checkConnect(Native Method)
		at sun.nio.ch.SocketChannelImpl.finishConnect(SocketChannelImpl.java:717)
		at io.netty.channel.socket.nio.NioSocketChannel.doFinishConnect(NioSocketChannel.java:330)
		at io.netty.channel.nio.AbstractNioChannel$AbstractNioUnsafe.finishConnect(AbstractNioChannel.java:334)
		at io.netty.channel.nio.NioEventLoop.processSelectedKey(NioEventLoop.java:688)
		at io.netty.channel.nio.NioEventLoop.processSelectedKeysOptimized(NioEventLoop.java:635)
		at io.netty.channel.nio.NioEventLoop.processSelectedKeys(NioEventLoop.java:552)
		at io.netty.channel.nio.NioEventLoop.run(NioEventLoop.java:514)
		at io.netty.util.concurrent.SingleThreadEventExecutor$6.run(SingleThreadEventExecutor.java:1050)
		at io.netty.util.internal.ThreadExecutorMap$2.run(ThreadExecutorMap.java:74)
		at io.netty.util.concurrent.FastThreadLocalRunnable.run(FastThreadLocalRunnable.java:30)
		at java.lang.Thread.run(Thread.java:748)
Caused by: io.lettuce.core.RedisConnectionException: Unable to establish a connection to Redis Cluster at [RedisURI [host='************', port=7001], RedisURI [host='************', port=7002], RedisURI [host='************', port=7003], RedisURI [host='************', port=7004], RedisURI [host='************', port=7005], RedisURI [host='************', port=7006]]
	at io.lettuce.core.cluster.topology.AsyncConnections.get(AsyncConnections.java:89)
	at io.lettuce.core.cluster.topology.ClusterTopologyRefresh.loadViews(ClusterTopologyRefresh.java:76)
	at io.lettuce.core.cluster.RedisClusterClient.doLoadPartitions(RedisClusterClient.java:865)
	at io.lettuce.core.cluster.RedisClusterClient.loadPartitions(RedisClusterClient.java:845)
	... 10 common frames omitted
	Suppressed: io.lettuce.core.RedisConnectionException: Unable to connect to [************:7003]: No route to host: no further information: /************:7003
		at io.lettuce.core.cluster.topology.ClusterTopologyRefresh.lambda$getConnections$0(ClusterTopologyRefresh.java:290)
		at java.util.concurrent.CompletableFuture.uniWhenComplete(CompletableFuture.java:760)
		at java.util.concurrent.CompletableFuture$UniWhenComplete.tryFire(CompletableFuture.java:736)
		at java.util.concurrent.CompletableFuture.postComplete(CompletableFuture.java:474)
		at java.util.concurrent.CompletableFuture.completeExceptionally(CompletableFuture.java:1977)
		at io.lettuce.core.AbstractRedisClient.lambda$initializeChannelAsync0$4(AbstractRedisClient.java:329)
		at io.netty.util.concurrent.DefaultPromise.notifyListener0(DefaultPromise.java:577)
		at io.netty.util.concurrent.DefaultPromise.notifyListeners0(DefaultPromise.java:570)
		at io.netty.util.concurrent.DefaultPromise.notifyListenersNow(DefaultPromise.java:549)
		at io.netty.util.concurrent.DefaultPromise.notifyListeners(DefaultPromise.java:490)
		at io.netty.util.concurrent.DefaultPromise.setValue0(DefaultPromise.java:615)
		at io.netty.util.concurrent.DefaultPromise.setFailure0(DefaultPromise.java:608)
		at io.netty.util.concurrent.DefaultPromise.tryFailure(DefaultPromise.java:117)
		at io.netty.channel.nio.AbstractNioChannel$AbstractNioUnsafe.fulfillConnectPromise(AbstractNioChannel.java:321)
		at io.netty.channel.nio.AbstractNioChannel$AbstractNioUnsafe.finishConnect(AbstractNioChannel.java:337)
		at io.netty.channel.nio.NioEventLoop.processSelectedKey(NioEventLoop.java:688)
		at io.netty.channel.nio.NioEventLoop.processSelectedKeysOptimized(NioEventLoop.java:635)
		at io.netty.channel.nio.NioEventLoop.processSelectedKeys(NioEventLoop.java:552)
		at io.netty.channel.nio.NioEventLoop.run(NioEventLoop.java:514)
		... 4 common frames omitted
	Caused by: io.netty.channel.AbstractChannel$AnnotatedNoRouteToHostException: No route to host: no further information: /************:7003
	Caused by: java.net.NoRouteToHostException: No route to host: no further information
		at sun.nio.ch.SocketChannelImpl.checkConnect(Native Method)
		at sun.nio.ch.SocketChannelImpl.finishConnect(SocketChannelImpl.java:717)
		at io.netty.channel.socket.nio.NioSocketChannel.doFinishConnect(NioSocketChannel.java:330)
		at io.netty.channel.nio.AbstractNioChannel$AbstractNioUnsafe.finishConnect(AbstractNioChannel.java:334)
		at io.netty.channel.nio.NioEventLoop.processSelectedKey(NioEventLoop.java:688)
		at io.netty.channel.nio.NioEventLoop.processSelectedKeysOptimized(NioEventLoop.java:635)
		at io.netty.channel.nio.NioEventLoop.processSelectedKeys(NioEventLoop.java:552)
		at io.netty.channel.nio.NioEventLoop.run(NioEventLoop.java:514)
		at io.netty.util.concurrent.SingleThreadEventExecutor$6.run(SingleThreadEventExecutor.java:1050)
		at io.netty.util.internal.ThreadExecutorMap$2.run(ThreadExecutorMap.java:74)
		at io.netty.util.concurrent.FastThreadLocalRunnable.run(FastThreadLocalRunnable.java:30)
		at java.lang.Thread.run(Thread.java:748)
	Suppressed: io.lettuce.core.RedisConnectionException: Unable to connect to [************:7004]: No route to host: no further information: /************:7004
		at io.lettuce.core.cluster.topology.ClusterTopologyRefresh.lambda$getConnections$0(ClusterTopologyRefresh.java:290)
		at java.util.concurrent.CompletableFuture.uniWhenComplete(CompletableFuture.java:760)
		at java.util.concurrent.CompletableFuture$UniWhenComplete.tryFire(CompletableFuture.java:736)
		at java.util.concurrent.CompletableFuture.postComplete(CompletableFuture.java:474)
		at java.util.concurrent.CompletableFuture.completeExceptionally(CompletableFuture.java:1977)
		at io.lettuce.core.AbstractRedisClient.lambda$initializeChannelAsync0$4(AbstractRedisClient.java:329)
		at io.netty.util.concurrent.DefaultPromise.notifyListener0(DefaultPromise.java:577)
		at io.netty.util.concurrent.DefaultPromise.notifyListeners0(DefaultPromise.java:570)
		at io.netty.util.concurrent.DefaultPromise.notifyListenersNow(DefaultPromise.java:549)
		at io.netty.util.concurrent.DefaultPromise.notifyListeners(DefaultPromise.java:490)
		at io.netty.util.concurrent.DefaultPromise.setValue0(DefaultPromise.java:615)
		at io.netty.util.concurrent.DefaultPromise.setFailure0(DefaultPromise.java:608)
		at io.netty.util.concurrent.DefaultPromise.tryFailure(DefaultPromise.java:117)
		at io.netty.channel.nio.AbstractNioChannel$AbstractNioUnsafe.fulfillConnectPromise(AbstractNioChannel.java:321)
		at io.netty.channel.nio.AbstractNioChannel$AbstractNioUnsafe.finishConnect(AbstractNioChannel.java:337)
		at io.netty.channel.nio.NioEventLoop.processSelectedKey(NioEventLoop.java:688)
		at io.netty.channel.nio.NioEventLoop.processSelectedKeysOptimized(NioEventLoop.java:635)
		at io.netty.channel.nio.NioEventLoop.processSelectedKeys(NioEventLoop.java:552)
		at io.netty.channel.nio.NioEventLoop.run(NioEventLoop.java:514)
		... 4 common frames omitted
	Caused by: io.netty.channel.AbstractChannel$AnnotatedNoRouteToHostException: No route to host: no further information: /************:7004
	Caused by: java.net.NoRouteToHostException: No route to host: no further information
		at sun.nio.ch.SocketChannelImpl.checkConnect(Native Method)
		at sun.nio.ch.SocketChannelImpl.finishConnect(SocketChannelImpl.java:717)
		at io.netty.channel.socket.nio.NioSocketChannel.doFinishConnect(NioSocketChannel.java:330)
		at io.netty.channel.nio.AbstractNioChannel$AbstractNioUnsafe.finishConnect(AbstractNioChannel.java:334)
		at io.netty.channel.nio.NioEventLoop.processSelectedKey(NioEventLoop.java:688)
		at io.netty.channel.nio.NioEventLoop.processSelectedKeysOptimized(NioEventLoop.java:635)
		at io.netty.channel.nio.NioEventLoop.processSelectedKeys(NioEventLoop.java:552)
		at io.netty.channel.nio.NioEventLoop.run(NioEventLoop.java:514)
		at io.netty.util.concurrent.SingleThreadEventExecutor$6.run(SingleThreadEventExecutor.java:1050)
		at io.netty.util.internal.ThreadExecutorMap$2.run(ThreadExecutorMap.java:74)
		at io.netty.util.concurrent.FastThreadLocalRunnable.run(FastThreadLocalRunnable.java:30)
		at java.lang.Thread.run(Thread.java:748)
	Suppressed: io.lettuce.core.RedisConnectionException: Unable to connect to [************:7002]: No route to host: no further information: /************:7002
		at io.lettuce.core.cluster.topology.ClusterTopologyRefresh.lambda$getConnections$0(ClusterTopologyRefresh.java:290)
		at java.util.concurrent.CompletableFuture.uniWhenComplete(CompletableFuture.java:760)
		at java.util.concurrent.CompletableFuture$UniWhenComplete.tryFire(CompletableFuture.java:736)
		at java.util.concurrent.CompletableFuture.postComplete(CompletableFuture.java:474)
		at java.util.concurrent.CompletableFuture.completeExceptionally(CompletableFuture.java:1977)
		at io.lettuce.core.AbstractRedisClient.lambda$initializeChannelAsync0$4(AbstractRedisClient.java:329)
		at io.netty.util.concurrent.DefaultPromise.notifyListener0(DefaultPromise.java:577)
		at io.netty.util.concurrent.DefaultPromise.notifyListeners0(DefaultPromise.java:570)
		at io.netty.util.concurrent.DefaultPromise.notifyListenersNow(DefaultPromise.java:549)
		at io.netty.util.concurrent.DefaultPromise.notifyListeners(DefaultPromise.java:490)
		at io.netty.util.concurrent.DefaultPromise.setValue0(DefaultPromise.java:615)
		at io.netty.util.concurrent.DefaultPromise.setFailure0(DefaultPromise.java:608)
		at io.netty.util.concurrent.DefaultPromise.tryFailure(DefaultPromise.java:117)
		at io.netty.channel.nio.AbstractNioChannel$AbstractNioUnsafe.fulfillConnectPromise(AbstractNioChannel.java:321)
		at io.netty.channel.nio.AbstractNioChannel$AbstractNioUnsafe.finishConnect(AbstractNioChannel.java:337)
		at io.netty.channel.nio.NioEventLoop.processSelectedKey(NioEventLoop.java:688)
		at io.netty.channel.nio.NioEventLoop.processSelectedKeysOptimized(NioEventLoop.java:635)
		at io.netty.channel.nio.NioEventLoop.processSelectedKeys(NioEventLoop.java:552)
		at io.netty.channel.nio.NioEventLoop.run(NioEventLoop.java:514)
		... 4 common frames omitted
	Caused by: io.netty.channel.AbstractChannel$AnnotatedNoRouteToHostException: No route to host: no further information: /************:7002
	Caused by: java.net.NoRouteToHostException: No route to host: no further information
		at sun.nio.ch.SocketChannelImpl.checkConnect(Native Method)
		at sun.nio.ch.SocketChannelImpl.finishConnect(SocketChannelImpl.java:717)
		at io.netty.channel.socket.nio.NioSocketChannel.doFinishConnect(NioSocketChannel.java:330)
		at io.netty.channel.nio.AbstractNioChannel$AbstractNioUnsafe.finishConnect(AbstractNioChannel.java:334)
		at io.netty.channel.nio.NioEventLoop.processSelectedKey(NioEventLoop.java:688)
		at io.netty.channel.nio.NioEventLoop.processSelectedKeysOptimized(NioEventLoop.java:635)
		at io.netty.channel.nio.NioEventLoop.processSelectedKeys(NioEventLoop.java:552)
		at io.netty.channel.nio.NioEventLoop.run(NioEventLoop.java:514)
		at io.netty.util.concurrent.SingleThreadEventExecutor$6.run(SingleThreadEventExecutor.java:1050)
		at io.netty.util.internal.ThreadExecutorMap$2.run(ThreadExecutorMap.java:74)
		at io.netty.util.concurrent.FastThreadLocalRunnable.run(FastThreadLocalRunnable.java:30)
		at java.lang.Thread.run(Thread.java:748)
	Suppressed: io.lettuce.core.RedisConnectionException: Unable to connect to [************:7001]: No route to host: no further information: /************:7001
		at io.lettuce.core.cluster.topology.ClusterTopologyRefresh.lambda$getConnections$0(ClusterTopologyRefresh.java:290)
		at java.util.concurrent.CompletableFuture.uniWhenComplete(CompletableFuture.java:760)
		at java.util.concurrent.CompletableFuture$UniWhenComplete.tryFire(CompletableFuture.java:736)
		at java.util.concurrent.CompletableFuture.postComplete(CompletableFuture.java:474)
		at java.util.concurrent.CompletableFuture.completeExceptionally(CompletableFuture.java:1977)
		at io.lettuce.core.AbstractRedisClient.lambda$initializeChannelAsync0$4(AbstractRedisClient.java:329)
		at io.netty.util.concurrent.DefaultPromise.notifyListener0(DefaultPromise.java:577)
		at io.netty.util.concurrent.DefaultPromise.notifyListeners0(DefaultPromise.java:570)
		at io.netty.util.concurrent.DefaultPromise.notifyListenersNow(DefaultPromise.java:549)
		at io.netty.util.concurrent.DefaultPromise.notifyListeners(DefaultPromise.java:490)
		at io.netty.util.concurrent.DefaultPromise.setValue0(DefaultPromise.java:615)
		at io.netty.util.concurrent.DefaultPromise.setFailure0(DefaultPromise.java:608)
		at io.netty.util.concurrent.DefaultPromise.tryFailure(DefaultPromise.java:117)
		at io.netty.channel.nio.AbstractNioChannel$AbstractNioUnsafe.fulfillConnectPromise(AbstractNioChannel.java:321)
		at io.netty.channel.nio.AbstractNioChannel$AbstractNioUnsafe.finishConnect(AbstractNioChannel.java:337)
		at io.netty.channel.nio.NioEventLoop.processSelectedKey(NioEventLoop.java:688)
		at io.netty.channel.nio.NioEventLoop.processSelectedKeysOptimized(NioEventLoop.java:635)
		at io.netty.channel.nio.NioEventLoop.processSelectedKeys(NioEventLoop.java:552)
		at io.netty.channel.nio.NioEventLoop.run(NioEventLoop.java:514)
		... 4 common frames omitted
	Caused by: io.netty.channel.AbstractChannel$AnnotatedNoRouteToHostException: No route to host: no further information: /************:7001
	Caused by: java.net.NoRouteToHostException: No route to host: no further information
		at sun.nio.ch.SocketChannelImpl.checkConnect(Native Method)
		at sun.nio.ch.SocketChannelImpl.finishConnect(SocketChannelImpl.java:717)
		at io.netty.channel.socket.nio.NioSocketChannel.doFinishConnect(NioSocketChannel.java:330)
		at io.netty.channel.nio.AbstractNioChannel$AbstractNioUnsafe.finishConnect(AbstractNioChannel.java:334)
		at io.netty.channel.nio.NioEventLoop.processSelectedKey(NioEventLoop.java:688)
		at io.netty.channel.nio.NioEventLoop.processSelectedKeysOptimized(NioEventLoop.java:635)
		at io.netty.channel.nio.NioEventLoop.processSelectedKeys(NioEventLoop.java:552)
		at io.netty.channel.nio.NioEventLoop.run(NioEventLoop.java:514)
		at io.netty.util.concurrent.SingleThreadEventExecutor$6.run(SingleThreadEventExecutor.java:1050)
		at io.netty.util.internal.ThreadExecutorMap$2.run(ThreadExecutorMap.java:74)
		at io.netty.util.concurrent.FastThreadLocalRunnable.run(FastThreadLocalRunnable.java:30)
		at java.lang.Thread.run(Thread.java:748)
	Suppressed: io.lettuce.core.RedisConnectionException: Unable to connect to [************:7005]: No route to host: no further information: /************:7005
		at io.lettuce.core.cluster.topology.ClusterTopologyRefresh.lambda$getConnections$0(ClusterTopologyRefresh.java:290)
		at java.util.concurrent.CompletableFuture.uniWhenComplete(CompletableFuture.java:760)
		at java.util.concurrent.CompletableFuture$UniWhenComplete.tryFire(CompletableFuture.java:736)
		at java.util.concurrent.CompletableFuture.postComplete(CompletableFuture.java:474)
		at java.util.concurrent.CompletableFuture.completeExceptionally(CompletableFuture.java:1977)
		at io.lettuce.core.AbstractRedisClient.lambda$initializeChannelAsync0$4(AbstractRedisClient.java:329)
		at io.netty.util.concurrent.DefaultPromise.notifyListener0(DefaultPromise.java:577)
		at io.netty.util.concurrent.DefaultPromise.notifyListeners0(DefaultPromise.java:570)
		at io.netty.util.concurrent.DefaultPromise.notifyListenersNow(DefaultPromise.java:549)
		at io.netty.util.concurrent.DefaultPromise.notifyListeners(DefaultPromise.java:490)
		at io.netty.util.concurrent.DefaultPromise.setValue0(DefaultPromise.java:615)
		at io.netty.util.concurrent.DefaultPromise.setFailure0(DefaultPromise.java:608)
		at io.netty.util.concurrent.DefaultPromise.tryFailure(DefaultPromise.java:117)
		at io.netty.channel.nio.AbstractNioChannel$AbstractNioUnsafe.fulfillConnectPromise(AbstractNioChannel.java:321)
		at io.netty.channel.nio.AbstractNioChannel$AbstractNioUnsafe.finishConnect(AbstractNioChannel.java:337)
		at io.netty.channel.nio.NioEventLoop.processSelectedKey(NioEventLoop.java:688)
		at io.netty.channel.nio.NioEventLoop.processSelectedKeysOptimized(NioEventLoop.java:635)
		at io.netty.channel.nio.NioEventLoop.processSelectedKeys(NioEventLoop.java:552)
		at io.netty.channel.nio.NioEventLoop.run(NioEventLoop.java:514)
		... 4 common frames omitted
	Caused by: io.netty.channel.AbstractChannel$AnnotatedNoRouteToHostException: No route to host: no further information: /************:7005
	Caused by: java.net.NoRouteToHostException: No route to host: no further information
		at sun.nio.ch.SocketChannelImpl.checkConnect(Native Method)
		at sun.nio.ch.SocketChannelImpl.finishConnect(SocketChannelImpl.java:717)
		at io.netty.channel.socket.nio.NioSocketChannel.doFinishConnect(NioSocketChannel.java:330)
		at io.netty.channel.nio.AbstractNioChannel$AbstractNioUnsafe.finishConnect(AbstractNioChannel.java:334)
		at io.netty.channel.nio.NioEventLoop.processSelectedKey(NioEventLoop.java:688)
		at io.netty.channel.nio.NioEventLoop.processSelectedKeysOptimized(NioEventLoop.java:635)
		at io.netty.channel.nio.NioEventLoop.processSelectedKeys(NioEventLoop.java:552)
		at io.netty.channel.nio.NioEventLoop.run(NioEventLoop.java:514)
		at io.netty.util.concurrent.SingleThreadEventExecutor$6.run(SingleThreadEventExecutor.java:1050)
		at io.netty.util.internal.ThreadExecutorMap$2.run(ThreadExecutorMap.java:74)
		at io.netty.util.concurrent.FastThreadLocalRunnable.run(FastThreadLocalRunnable.java:30)
		at java.lang.Thread.run(Thread.java:748)
Caused by: io.lettuce.core.RedisConnectionException: Unable to connect to [************:7006]: No route to host: no further information: /************:7006
	at io.lettuce.core.cluster.topology.ClusterTopologyRefresh.lambda$getConnections$0(ClusterTopologyRefresh.java:290)
	at java.util.concurrent.CompletableFuture.uniWhenComplete(CompletableFuture.java:760)
	at java.util.concurrent.CompletableFuture$UniWhenComplete.tryFire(CompletableFuture.java:736)
	at java.util.concurrent.CompletableFuture.postComplete(CompletableFuture.java:474)
	at java.util.concurrent.CompletableFuture.completeExceptionally(CompletableFuture.java:1977)
	at io.lettuce.core.AbstractRedisClient.lambda$initializeChannelAsync0$4(AbstractRedisClient.java:329)
	at io.netty.util.concurrent.DefaultPromise.notifyListener0(DefaultPromise.java:577)
	at io.netty.util.concurrent.DefaultPromise.notifyListeners0(DefaultPromise.java:570)
	at io.netty.util.concurrent.DefaultPromise.notifyListenersNow(DefaultPromise.java:549)
	at io.netty.util.concurrent.DefaultPromise.notifyListeners(DefaultPromise.java:490)
	at io.netty.util.concurrent.DefaultPromise.setValue0(DefaultPromise.java:615)
	at io.netty.util.concurrent.DefaultPromise.setFailure0(DefaultPromise.java:608)
	at io.netty.util.concurrent.DefaultPromise.tryFailure(DefaultPromise.java:117)
	at io.netty.channel.nio.AbstractNioChannel$AbstractNioUnsafe.fulfillConnectPromise(AbstractNioChannel.java:321)
	at io.netty.channel.nio.AbstractNioChannel$AbstractNioUnsafe.finishConnect(AbstractNioChannel.java:337)
	at io.netty.channel.nio.NioEventLoop.processSelectedKey(NioEventLoop.java:688)
	at io.netty.channel.nio.NioEventLoop.processSelectedKeysOptimized(NioEventLoop.java:635)
	at io.netty.channel.nio.NioEventLoop.processSelectedKeys(NioEventLoop.java:552)
	at io.netty.channel.nio.NioEventLoop.run(NioEventLoop.java:514)
	... 4 common frames omitted
Caused by: io.netty.channel.AbstractChannel$AnnotatedNoRouteToHostException: No route to host: no further information: /************:7006
Caused by: java.net.NoRouteToHostException: No route to host: no further information
	at sun.nio.ch.SocketChannelImpl.checkConnect(Native Method)
	at sun.nio.ch.SocketChannelImpl.finishConnect(SocketChannelImpl.java:717)
	at io.netty.channel.socket.nio.NioSocketChannel.doFinishConnect(NioSocketChannel.java:330)
	at io.netty.channel.nio.AbstractNioChannel$AbstractNioUnsafe.finishConnect(AbstractNioChannel.java:334)
	at io.netty.channel.nio.NioEventLoop.processSelectedKey(NioEventLoop.java:688)
	at io.netty.channel.nio.NioEventLoop.processSelectedKeysOptimized(NioEventLoop.java:635)
	at io.netty.channel.nio.NioEventLoop.processSelectedKeys(NioEventLoop.java:552)
	at io.netty.channel.nio.NioEventLoop.run(NioEventLoop.java:514)
	at io.netty.util.concurrent.SingleThreadEventExecutor$6.run(SingleThreadEventExecutor.java:1050)
	at io.netty.util.internal.ThreadExecutorMap$2.run(ThreadExecutorMap.java:74)
	at io.netty.util.concurrent.FastThreadLocalRunnable.run(FastThreadLocalRunnable.java:30)
	at java.lang.Thread.run(Thread.java:748)
2025-06-01 02:33:28.749 [AMQP Connection ************:5672] WARN  com.rabbitmq.client.impl.ForgivingExceptionHandler.log Line:115 - An unexpected connection driver error occured (Exception message: Connection reset)
2025-06-01 02:33:29.695 [AMQP Connection ************:5672] WARN  com.rabbitmq.client.impl.ForgivingExceptionHandler.log Line:115 - An unexpected connection driver error occured (Exception message: Connection reset)
2025-06-01 02:33:30.678 [AMQP Connection ************:5672] WARN  com.rabbitmq.client.impl.ForgivingExceptionHandler.log Line:115 - An unexpected connection driver error occured (Exception message: Connection reset)
2025-06-01 02:33:31.178 [AMQP Connection ************:5672] WARN  com.rabbitmq.client.impl.ForgivingExceptionHandler.log Line:115 - An unexpected connection driver error occured (Exception message: Connection reset)
2025-06-01 02:45:00.017 [pool-14-thread-9] WARN  com.simbest.boot.base.repository.CustomDynamicWhere.queryForList Line:50 - 自定义查询SQL输出为：【select * from US_TODO_MODEL t  where t.send_flag = 0 and t.work_flag = 0 and t.enabled =1】
2025-06-01 02:50:00.007 [pool-14-thread-9] WARN  com.simbest.boot.base.repository.CustomDynamicWhere.queryForList Line:50 - 自定义查询SQL输出为：【select * from US_TODO_MODEL t  where t.send_flag = 0 and t.work_flag = 0 and t.enabled =1】
2025-06-01 03:14:00.958 [AMQP Connection ************:5672] WARN  com.rabbitmq.client.impl.ForgivingExceptionHandler.log Line:115 - An unexpected connection driver error occured (Exception message: Connection reset)
2025-06-01 03:14:01.803 [AMQP Connection ************:5672] WARN  com.rabbitmq.client.impl.ForgivingExceptionHandler.log Line:115 - An unexpected connection driver error occured (Exception message: Connection reset)
2025-06-01 03:14:02.785 [AMQP Connection ************:5672] WARN  com.rabbitmq.client.impl.ForgivingExceptionHandler.log Line:115 - An unexpected connection driver error occured (Exception message: Connection reset)
2025-06-01 03:14:03.341 [AMQP Connection ************:5672] WARN  com.rabbitmq.client.impl.ForgivingExceptionHandler.log Line:115 - An unexpected connection driver error occured (Exception message: Connection reset)
2025-06-01 03:20:00.008 [pool-14-thread-8] WARN  com.simbest.boot.base.repository.CustomDynamicWhere.queryForList Line:50 - 自定义查询SQL输出为：【select * from US_TODO_MODEL t  where t.send_flag = 0 and t.work_flag = 0 and t.enabled =1】
2025-06-01 03:20:06.157 [pool-14-thread-1] WARN  com.simbest.boot.base.repository.CustomDynamicWhere.queryForList Line:50 - 自定义查询SQL输出为：【select * from US_TODO_MODEL t  where t.send_flag = 0 and t.work_flag = 1 and t.enabled =1】
2025-06-01 03:25:00.004 [pool-14-thread-4] WARN  com.simbest.boot.base.repository.CustomDynamicWhere.queryForList Line:50 - 自定义查询SQL输出为：【select * from US_TODO_MODEL t  where t.send_flag = 0 and t.work_flag = 0 and t.enabled =1】
2025-06-01 03:25:00.007 [pool-14-thread-5] WARN  com.simbest.boot.base.repository.CustomDynamicWhere.queryForList Line:50 - 自定义查询SQL输出为：【select * from US_TODO_MODEL t  where t.send_flag = 0 and t.work_flag = 1 and t.enabled =1】
2025-06-01 03:30:00.020 [pool-14-thread-3] WARN  com.simbest.boot.base.repository.CustomDynamicWhere.queryForList Line:50 - 自定义查询SQL输出为：【select * from US_TODO_MODEL t  where t.send_flag = 0 and t.work_flag = 1 and t.enabled =1】
2025-06-01 03:30:00.022 [pool-14-thread-1] WARN  com.simbest.boot.base.repository.CustomDynamicWhere.queryForList Line:50 - 自定义查询SQL输出为：【select * from US_TODO_MODEL t  where t.send_flag = 0 and t.work_flag = 0 and t.enabled =1】
2025-06-01 03:55:01.838 [AMQP Connection ************:5672] WARN  com.rabbitmq.client.impl.ForgivingExceptionHandler.log Line:115 - An unexpected connection driver error occured (Exception message: Connection reset)
2025-06-01 03:55:02.678 [AMQP Connection ************:5672] WARN  com.rabbitmq.client.impl.ForgivingExceptionHandler.log Line:115 - An unexpected connection driver error occured (Exception message: Connection reset)
2025-06-01 03:55:03.692 [AMQP Connection ************:5672] WARN  com.rabbitmq.client.impl.ForgivingExceptionHandler.log Line:115 - An unexpected connection driver error occured (Exception message: Connection reset)
2025-06-01 03:55:04.206 [AMQP Connection ************:5672] WARN  com.rabbitmq.client.impl.ForgivingExceptionHandler.log Line:115 - An unexpected connection driver error occured (Exception message: Connection reset)
2025-06-01 04:00:00.015 [pool-14-thread-2] WARN  com.simbest.boot.base.repository.CustomDynamicWhere.queryForList Line:50 - 自定义查询SQL输出为：【select * from US_TODO_MODEL t  where t.send_flag = 0 and t.work_flag = 0 and t.enabled =1】
2025-06-01 04:05:00.006 [pool-14-thread-10] WARN  com.simbest.boot.base.repository.CustomDynamicWhere.queryForList Line:50 - 自定义查询SQL输出为：【select * from US_TODO_MODEL t  where t.send_flag = 0 and t.work_flag = 0 and t.enabled =1】
2025-06-01 04:05:03.014 [pool-14-thread-6] WARN  com.simbest.boot.base.repository.CustomDynamicWhere.queryForList Line:50 - 自定义查询SQL输出为：【select * from US_TODO_MODEL t  where t.send_flag = 0 and t.work_flag = 1 and t.enabled =1】
2025-06-01 04:10:00.005 [pool-14-thread-8] WARN  com.simbest.boot.base.repository.CustomDynamicWhere.queryForList Line:50 - 自定义查询SQL输出为：【select * from US_TODO_MODEL t  where t.send_flag = 0 and t.work_flag = 1 and t.enabled =1】
2025-06-01 04:10:00.006 [pool-14-thread-5] WARN  com.simbest.boot.base.repository.CustomDynamicWhere.queryForList Line:50 - 自定义查询SQL输出为：【select * from US_TODO_MODEL t  where t.send_flag = 0 and t.work_flag = 0 and t.enabled =1】
2025-06-01 04:35:33.607 [AMQP Connection ************:5672] WARN  com.rabbitmq.client.impl.ForgivingExceptionHandler.log Line:115 - An unexpected connection driver error occured (Exception message: Connection reset)
2025-06-01 04:35:34.399 [AMQP Connection ************:5672] WARN  com.rabbitmq.client.impl.ForgivingExceptionHandler.log Line:115 - An unexpected connection driver error occured (Exception message: Connection reset)
2025-06-01 04:35:35.133 [AMQP Connection ************:5672] WARN  com.rabbitmq.client.impl.ForgivingExceptionHandler.log Line:115 - An unexpected connection driver error occured (Exception message: Connection reset)
2025-06-01 04:35:35.508 [AMQP Connection ************:5672] WARN  com.rabbitmq.client.impl.ForgivingExceptionHandler.log Line:115 - An unexpected connection driver error occured (Exception message: Connection reset)
2025-06-01 04:45:00.008 [pool-14-thread-5] WARN  com.simbest.boot.base.repository.CustomDynamicWhere.queryForList Line:50 - 自定义查询SQL输出为：【select * from US_TODO_MODEL t  where t.send_flag = 0 and t.work_flag = 0 and t.enabled =1】
2025-06-01 04:50:00.013 [pool-14-thread-10] WARN  com.simbest.boot.base.repository.CustomDynamicWhere.queryForList Line:50 - 自定义查询SQL输出为：【select * from US_TODO_MODEL t  where t.send_flag = 0 and t.work_flag = 0 and t.enabled =1】
2025-06-01 04:50:06.183 [pool-14-thread-7] WARN  com.simbest.boot.base.repository.CustomDynamicWhere.queryForList Line:50 - 自定义查询SQL输出为：【select * from US_TODO_MODEL t  where t.send_flag = 0 and t.work_flag = 1 and t.enabled =1】
2025-06-01 04:55:00.012 [pool-14-thread-6] WARN  com.simbest.boot.base.repository.CustomDynamicWhere.queryForList Line:50 - 自定义查询SQL输出为：【select * from US_TODO_MODEL t  where t.send_flag = 0 and t.work_flag = 0 and t.enabled =1】
2025-06-01 04:55:00.012 [pool-14-thread-3] WARN  com.simbest.boot.base.repository.CustomDynamicWhere.queryForList Line:50 - 自定义查询SQL输出为：【select * from US_TODO_MODEL t  where t.send_flag = 0 and t.work_flag = 1 and t.enabled =1】
2025-06-01 05:16:05.943 [AMQP Connection ************:5672] WARN  com.rabbitmq.client.impl.ForgivingExceptionHandler.log Line:115 - An unexpected connection driver error occured (Exception message: Connection reset)
2025-06-01 05:16:06.096 [AMQP Connection ************:5672] WARN  com.rabbitmq.client.impl.ForgivingExceptionHandler.log Line:115 - An unexpected connection driver error occured (Exception message: Connection reset)
2025-06-01 05:16:06.652 [AMQP Connection ************:5672] WARN  com.rabbitmq.client.impl.ForgivingExceptionHandler.log Line:115 - An unexpected connection driver error occured (Exception message: Connection reset)
2025-06-01 05:16:07.960 [AMQP Connection ************:5672] WARN  com.rabbitmq.client.impl.ForgivingExceptionHandler.log Line:115 - An unexpected connection driver error occured (Exception message: Connection reset)
2025-06-01 05:57:06.582 [AMQP Connection ************:5672] WARN  com.rabbitmq.client.impl.ForgivingExceptionHandler.log Line:115 - An unexpected connection driver error occured (Exception message: Connection reset)
2025-06-01 05:57:06.913 [AMQP Connection ************:5672] WARN  com.rabbitmq.client.impl.ForgivingExceptionHandler.log Line:115 - An unexpected connection driver error occured (Exception message: Connection reset)
2025-06-01 05:57:07.353 [AMQP Connection ************:5672] WARN  com.rabbitmq.client.impl.ForgivingExceptionHandler.log Line:115 - An unexpected connection driver error occured (Exception message: Connection reset)
2025-06-01 05:57:08.666 [AMQP Connection ************:5672] WARN  com.rabbitmq.client.impl.ForgivingExceptionHandler.log Line:115 - An unexpected connection driver error occured (Exception message: Connection reset)
2025-06-01 06:10:00.006 [pool-14-thread-1] WARN  com.simbest.boot.base.repository.CustomDynamicWhere.queryForList Line:50 - 自定义查询SQL输出为：【select * from US_TODO_MODEL t  where t.send_flag = 0 and t.work_flag = 0 and t.enabled =1】
2025-06-01 06:15:00.010 [pool-14-thread-8] WARN  com.simbest.boot.base.repository.CustomDynamicWhere.queryForList Line:50 - 自定义查询SQL输出为：【select * from US_TODO_MODEL t  where t.send_flag = 0 and t.work_flag = 0 and t.enabled =1】
2025-06-01 06:37:38.372 [AMQP Connection ************:5672] WARN  com.rabbitmq.client.impl.ForgivingExceptionHandler.log Line:115 - An unexpected connection driver error occured (Exception message: Connection reset)
2025-06-01 06:37:38.586 [AMQP Connection ************:5672] WARN  com.rabbitmq.client.impl.ForgivingExceptionHandler.log Line:115 - An unexpected connection driver error occured (Exception message: Connection reset)
2025-06-01 06:37:39.020 [AMQP Connection ************:5672] WARN  com.rabbitmq.client.impl.ForgivingExceptionHandler.log Line:115 - An unexpected connection driver error occured (Exception message: Connection reset)
2025-06-01 06:37:40.427 [AMQP Connection ************:5672] WARN  com.rabbitmq.client.impl.ForgivingExceptionHandler.log Line:115 - An unexpected connection driver error occured (Exception message: Connection reset)
2025-06-01 06:40:03.088 [pool-14-thread-3] WARN  com.simbest.boot.base.repository.CustomDynamicWhere.queryForList Line:50 - 自定义查询SQL输出为：【select * from US_TODO_MODEL t  where t.send_flag = 0 and t.work_flag = 0 and t.enabled =1】
2025-06-01 07:18:09.560 [AMQP Connection ************:5672] WARN  com.rabbitmq.client.impl.ForgivingExceptionHandler.log Line:115 - An unexpected connection driver error occured (Exception message: Connection reset)
2025-06-01 07:18:09.883 [AMQP Connection ************:5672] WARN  com.rabbitmq.client.impl.ForgivingExceptionHandler.log Line:115 - An unexpected connection driver error occured (Exception message: Connection reset)
2025-06-01 07:18:10.224 [AMQP Connection ************:5672] WARN  com.rabbitmq.client.impl.ForgivingExceptionHandler.log Line:115 - An unexpected connection driver error occured (Exception message: Connection reset)
2025-06-01 07:18:11.632 [AMQP Connection ************:5672] WARN  com.rabbitmq.client.impl.ForgivingExceptionHandler.log Line:115 - An unexpected connection driver error occured (Exception message: Connection reset)
2025-06-01 07:30:00.010 [pool-14-thread-9] WARN  com.simbest.boot.base.repository.CustomDynamicWhere.queryForList Line:50 - 自定义查询SQL输出为：【select * from US_TODO_MODEL t  where t.send_flag = 0 and t.work_flag = 0 and t.enabled =1】
2025-06-01 07:35:00.012 [pool-14-thread-3] WARN  com.simbest.boot.base.repository.CustomDynamicWhere.queryForList Line:50 - 自定义查询SQL输出为：【select * from US_TODO_MODEL t  where t.send_flag = 0 and t.work_flag = 0 and t.enabled =1】
2025-06-01 07:59:11.328 [AMQP Connection ************:5672] WARN  com.rabbitmq.client.impl.ForgivingExceptionHandler.log Line:115 - An unexpected connection driver error occured (Exception message: Connection reset)
2025-06-01 07:59:11.655 [AMQP Connection ************:5672] WARN  com.rabbitmq.client.impl.ForgivingExceptionHandler.log Line:115 - An unexpected connection driver error occured (Exception message: Connection reset)
2025-06-01 07:59:11.981 [AMQP Connection ************:5672] WARN  com.rabbitmq.client.impl.ForgivingExceptionHandler.log Line:115 - An unexpected connection driver error occured (Exception message: Connection reset)
2025-06-01 07:59:13.402 [AMQP Connection ************:5672] WARN  com.rabbitmq.client.impl.ForgivingExceptionHandler.log Line:115 - An unexpected connection driver error occured (Exception message: Connection reset)
2025-06-01 08:05:00.013 [pool-14-thread-8] WARN  com.simbest.boot.base.repository.CustomDynamicWhere.queryForList Line:50 - 自定义查询SQL输出为：【select * from US_TODO_MODEL t  where t.send_flag = 0 and t.work_flag = 0 and t.enabled =1】
2025-06-01 08:05:09.220 [pool-14-thread-1] WARN  com.simbest.boot.base.repository.CustomDynamicWhere.queryForList Line:50 - 自定义查询SQL输出为：【select * from US_TODO_MODEL t  where t.send_flag = 0 and t.work_flag = 1 and t.enabled =1】
2025-06-01 08:10:00.007 [pool-14-thread-6] WARN  com.simbest.boot.base.repository.CustomDynamicWhere.queryForList Line:50 - 自定义查询SQL输出为：【select * from US_TODO_MODEL t  where t.send_flag = 0 and t.work_flag = 0 and t.enabled =1】
2025-06-01 08:10:00.007 [pool-14-thread-9] WARN  com.simbest.boot.base.repository.CustomDynamicWhere.queryForList Line:50 - 自定义查询SQL输出为：【select * from US_TODO_MODEL t  where t.send_flag = 0 and t.work_flag = 1 and t.enabled =1】
2025-06-01 08:15:00.018 [pool-14-thread-2] WARN  com.simbest.boot.base.repository.CustomDynamicWhere.queryForList Line:50 - 自定义查询SQL输出为：【select * from US_TODO_MODEL t  where t.send_flag = 0 and t.work_flag = 1 and t.enabled =1】
2025-06-01 08:15:00.019 [pool-14-thread-7] WARN  com.simbest.boot.base.repository.CustomDynamicWhere.queryForList Line:50 - 自定义查询SQL输出为：【select * from US_TODO_MODEL t  where t.send_flag = 0 and t.work_flag = 0 and t.enabled =1】
2025-06-01 08:39:42.581 [AMQP Connection ************:5672] WARN  com.rabbitmq.client.impl.ForgivingExceptionHandler.log Line:115 - An unexpected connection driver error occured (Exception message: Connection reset)
2025-06-01 08:39:42.798 [AMQP Connection ************:5672] WARN  com.rabbitmq.client.impl.ForgivingExceptionHandler.log Line:115 - An unexpected connection driver error occured (Exception message: Connection reset)
2025-06-01 08:39:43.124 [AMQP Connection ************:5672] WARN  com.rabbitmq.client.impl.ForgivingExceptionHandler.log Line:115 - An unexpected connection driver error occured (Exception message: Connection reset)
2025-06-01 08:39:44.909 [AMQP Connection ************:5672] WARN  com.rabbitmq.client.impl.ForgivingExceptionHandler.log Line:115 - An unexpected connection driver error occured (Exception message: Connection reset)
2025-06-01 08:45:00.020 [pool-14-thread-10] WARN  com.simbest.boot.base.repository.CustomDynamicWhere.queryForList Line:50 - 自定义查询SQL输出为：【select * from US_TODO_MODEL t  where t.send_flag = 0 and t.work_flag = 0 and t.enabled =1】
2025-06-01 08:45:06.195 [pool-14-thread-5] WARN  com.simbest.boot.base.repository.CustomDynamicWhere.queryForList Line:50 - 自定义查询SQL输出为：【select * from US_TODO_MODEL t  where t.send_flag = 0 and t.work_flag = 1 and t.enabled =1】
2025-06-01 08:50:00.015 [pool-14-thread-4] WARN  com.simbest.boot.base.repository.CustomDynamicWhere.queryForList Line:50 - 自定义查询SQL输出为：【select * from US_TODO_MODEL t  where t.send_flag = 0 and t.work_flag = 0 and t.enabled =1】
2025-06-01 08:50:00.016 [pool-14-thread-6] WARN  com.simbest.boot.base.repository.CustomDynamicWhere.queryForList Line:50 - 自定义查询SQL输出为：【select * from US_TODO_MODEL t  where t.send_flag = 0 and t.work_flag = 1 and t.enabled =1】
2025-06-01 08:55:00.016 [pool-14-thread-10] WARN  com.simbest.boot.base.repository.CustomDynamicWhere.queryForList Line:50 - 自定义查询SQL输出为：【select * from US_TODO_MODEL t  where t.send_flag = 0 and t.work_flag = 0 and t.enabled =1】
2025-06-01 08:55:00.016 [pool-14-thread-1] WARN  com.simbest.boot.base.repository.CustomDynamicWhere.queryForList Line:50 - 自定义查询SQL输出为：【select * from US_TODO_MODEL t  where t.send_flag = 0 and t.work_flag = 1 and t.enabled =1】
2025-06-01 09:20:05.454 [lettuce-nioEventLoop-4-5] WARN  io.lettuce.core.cluster.topology.ClusterTopologyRefresh.lambda$getConnections$0 Line:284 - Unable to connect to [************:7002]: connection timed out: /************:7002
2025-06-01 09:20:05.455 [lettuce-nioEventLoop-4-8] WARN  io.lettuce.core.cluster.topology.ClusterTopologyRefresh.lambda$getConnections$0 Line:284 - Unable to connect to [************:7005]: connection timed out: /************:7005
2025-06-01 09:20:05.454 [lettuce-nioEventLoop-4-4] WARN  io.lettuce.core.cluster.topology.ClusterTopologyRefresh.lambda$getConnections$0 Line:284 - Unable to connect to [************:7001]: connection timed out: /************:7001
2025-06-01 09:20:05.454 [lettuce-nioEventLoop-4-6] WARN  io.lettuce.core.cluster.topology.ClusterTopologyRefresh.lambda$getConnections$0 Line:284 - Unable to connect to [************:7003]: connection timed out: /************:7003
2025-06-01 09:20:05.455 [lettuce-nioEventLoop-4-7] WARN  io.lettuce.core.cluster.topology.ClusterTopologyRefresh.lambda$getConnections$0 Line:284 - Unable to connect to [************:7004]: connection timed out: /************:7004
2025-06-01 09:20:05.469 [lettuce-nioEventLoop-4-9] WARN  io.lettuce.core.cluster.topology.ClusterTopologyRefresh.lambda$getConnections$0 Line:284 - Unable to connect to [************:7006]: connection timed out: /************:7006
2025-06-01 09:20:14.523 [AMQP Connection ************:5672] WARN  com.rabbitmq.client.impl.ForgivingExceptionHandler.log Line:115 - An unexpected connection driver error occured (Exception message: Connection reset)
2025-06-01 09:20:14.788 [AMQP Connection ************:5672] WARN  com.rabbitmq.client.impl.ForgivingExceptionHandler.log Line:115 - An unexpected connection driver error occured (Exception message: Connection reset)
2025-06-01 09:20:15.098 [AMQP Connection ************:5672] WARN  com.rabbitmq.client.impl.ForgivingExceptionHandler.log Line:115 - An unexpected connection driver error occured (Exception message: Connection reset)
2025-06-01 09:20:15.484 [lettuce-nioEventLoop-4-1] WARN  io.lettuce.core.cluster.topology.ClusterTopologyRefresh.lambda$getConnections$0 Line:284 - Unable to connect to [************:7002]: connection timed out: /************:7002
2025-06-01 09:20:15.484 [lettuce-nioEventLoop-4-3] WARN  io.lettuce.core.cluster.topology.ClusterTopologyRefresh.lambda$getConnections$0 Line:284 - Unable to connect to [************:7001]: connection timed out: /************:7001
2025-06-01 09:20:15.484 [lettuce-nioEventLoop-4-11] WARN  io.lettuce.core.cluster.topology.ClusterTopologyRefresh.lambda$getConnections$0 Line:284 - Unable to connect to [************:7004]: connection timed out: /************:7004
2025-06-01 09:20:15.484 [lettuce-nioEventLoop-4-12] WARN  io.lettuce.core.cluster.topology.ClusterTopologyRefresh.lambda$getConnections$0 Line:284 - Unable to connect to [************:7005]: connection timed out: /************:7005
2025-06-01 09:20:15.484 [lettuce-nioEventLoop-4-2] WARN  io.lettuce.core.cluster.topology.ClusterTopologyRefresh.lambda$getConnections$0 Line:284 - Unable to connect to [************:7003]: connection timed out: /************:7003
2025-06-01 09:20:15.484 [lettuce-nioEventLoop-4-10] WARN  io.lettuce.core.cluster.topology.ClusterTopologyRefresh.lambda$getConnections$0 Line:284 - Unable to connect to [************:7006]: connection timed out: /************:7006
2025-06-01 09:20:15.496 [lettuce-eventExecutorLoop-1-5] WARN  io.lettuce.core.cluster.ClusterTopologyRefreshScheduler.doRun Line:258 - Cannot refresh Redis Cluster topology
io.lettuce.core.RedisException: Cannot retrieve initial cluster partitions from initial URIs [RedisURI [host='************', port=7006], RedisURI [host='************', port=7004], RedisURI [host='************', port=7005], RedisURI [host='************', port=7002], RedisURI [host='************', port=7003], RedisURI [host='************', port=7001]]
	at io.lettuce.core.cluster.RedisClusterClient.loadPartitions(RedisClusterClient.java:848)
	at io.lettuce.core.cluster.RedisClusterClient.reloadPartitions(RedisClusterClient.java:784)
	at io.lettuce.core.cluster.ClusterTopologyRefreshScheduler$ClusterTopologyRefreshTask.doRun(ClusterTopologyRefreshScheduler.java:256)
	at io.lettuce.core.cluster.ClusterTopologyRefreshScheduler$ClusterTopologyRefreshTask.run(ClusterTopologyRefreshScheduler.java:236)
	at io.netty.util.concurrent.PromiseTask.runTask(PromiseTask.java:98)
	at io.netty.util.concurrent.PromiseTask.run(PromiseTask.java:106)
	at io.netty.util.concurrent.DefaultEventExecutor.run(DefaultEventExecutor.java:66)
	at io.netty.util.concurrent.SingleThreadEventExecutor$6.run(SingleThreadEventExecutor.java:1050)
	at io.netty.util.internal.ThreadExecutorMap$2.run(ThreadExecutorMap.java:74)
	at io.netty.util.concurrent.FastThreadLocalRunnable.run(FastThreadLocalRunnable.java:30)
	at java.lang.Thread.run(Thread.java:748)
	Suppressed: io.lettuce.core.RedisConnectionException: Unable to establish a connection to Redis Cluster at [RedisURI [host='************', port=7001], RedisURI [host='************', port=7002], RedisURI [host='************', port=7003], RedisURI [host='************', port=7004], RedisURI [host='************', port=7005], RedisURI [host='************', port=7006]]
		at io.lettuce.core.cluster.topology.AsyncConnections.get(AsyncConnections.java:89)
		at io.lettuce.core.cluster.topology.ClusterTopologyRefresh.loadViews(ClusterTopologyRefresh.java:76)
		at io.lettuce.core.cluster.RedisClusterClient.doLoadPartitions(RedisClusterClient.java:865)
		at io.lettuce.core.cluster.RedisClusterClient.loadPartitions(RedisClusterClient.java:838)
		... 10 common frames omitted
		Suppressed: io.lettuce.core.RedisConnectionException: Unable to connect to [************:7005]: connection timed out: /************:7005
			at io.lettuce.core.cluster.topology.ClusterTopologyRefresh.lambda$getConnections$0(ClusterTopologyRefresh.java:290)
			at java.util.concurrent.CompletableFuture.uniWhenComplete(CompletableFuture.java:760)
			at java.util.concurrent.CompletableFuture$UniWhenComplete.tryFire(CompletableFuture.java:736)
			at java.util.concurrent.CompletableFuture.postComplete(CompletableFuture.java:474)
			at java.util.concurrent.CompletableFuture.completeExceptionally(CompletableFuture.java:1977)
			at io.lettuce.core.AbstractRedisClient.lambda$initializeChannelAsync0$4(AbstractRedisClient.java:329)
			at io.netty.util.concurrent.DefaultPromise.notifyListener0(DefaultPromise.java:577)
			at io.netty.util.concurrent.DefaultPromise.notifyListeners0(DefaultPromise.java:570)
			at io.netty.util.concurrent.DefaultPromise.notifyListenersNow(DefaultPromise.java:549)
			at io.netty.util.concurrent.DefaultPromise.notifyListeners(DefaultPromise.java:490)
			at io.netty.util.concurrent.DefaultPromise.setValue0(DefaultPromise.java:615)
			at io.netty.util.concurrent.DefaultPromise.setFailure0(DefaultPromise.java:608)
			at io.netty.util.concurrent.DefaultPromise.tryFailure(DefaultPromise.java:117)
			at io.netty.channel.nio.AbstractNioChannel$AbstractNioUnsafe$1.run(AbstractNioChannel.java:263)
			at io.netty.util.concurrent.PromiseTask.runTask(PromiseTask.java:98)
			at io.netty.util.concurrent.ScheduledFutureTask.run(ScheduledFutureTask.java:150)
			at io.netty.util.concurrent.AbstractEventExecutor.safeExecute(AbstractEventExecutor.java:163)
			at io.netty.util.concurrent.SingleThreadEventExecutor.runAllTasks(SingleThreadEventExecutor.java:510)
			at io.netty.channel.nio.NioEventLoop.run(NioEventLoop.java:518)
			... 4 common frames omitted
		Caused by: io.netty.channel.ConnectTimeoutException: connection timed out: /************:7005
			at io.netty.channel.nio.AbstractNioChannel$AbstractNioUnsafe$1.run(AbstractNioChannel.java:261)
			... 9 common frames omitted
		Suppressed: io.lettuce.core.RedisConnectionException: Unable to connect to [************:7001]: connection timed out: /************:7001
			at io.lettuce.core.cluster.topology.ClusterTopologyRefresh.lambda$getConnections$0(ClusterTopologyRefresh.java:290)
			at java.util.concurrent.CompletableFuture.uniWhenComplete(CompletableFuture.java:760)
			at java.util.concurrent.CompletableFuture$UniWhenComplete.tryFire(CompletableFuture.java:736)
			at java.util.concurrent.CompletableFuture.postComplete(CompletableFuture.java:474)
			at java.util.concurrent.CompletableFuture.completeExceptionally(CompletableFuture.java:1977)
			at io.lettuce.core.AbstractRedisClient.lambda$initializeChannelAsync0$4(AbstractRedisClient.java:329)
			at io.netty.util.concurrent.DefaultPromise.notifyListener0(DefaultPromise.java:577)
			at io.netty.util.concurrent.DefaultPromise.notifyListeners0(DefaultPromise.java:570)
			at io.netty.util.concurrent.DefaultPromise.notifyListenersNow(DefaultPromise.java:549)
			at io.netty.util.concurrent.DefaultPromise.notifyListeners(DefaultPromise.java:490)
			at io.netty.util.concurrent.DefaultPromise.setValue0(DefaultPromise.java:615)
			at io.netty.util.concurrent.DefaultPromise.setFailure0(DefaultPromise.java:608)
			at io.netty.util.concurrent.DefaultPromise.tryFailure(DefaultPromise.java:117)
			at io.netty.channel.nio.AbstractNioChannel$AbstractNioUnsafe$1.run(AbstractNioChannel.java:263)
			at io.netty.util.concurrent.PromiseTask.runTask(PromiseTask.java:98)
			at io.netty.util.concurrent.ScheduledFutureTask.run(ScheduledFutureTask.java:150)
			at io.netty.util.concurrent.AbstractEventExecutor.safeExecute(AbstractEventExecutor.java:163)
			at io.netty.util.concurrent.SingleThreadEventExecutor.runAllTasks(SingleThreadEventExecutor.java:510)
			at io.netty.channel.nio.NioEventLoop.run(NioEventLoop.java:518)
			... 4 common frames omitted
		Caused by: io.netty.channel.ConnectTimeoutException: connection timed out: /************:7001
			at io.netty.channel.nio.AbstractNioChannel$AbstractNioUnsafe$1.run(AbstractNioChannel.java:261)
			... 9 common frames omitted
		Suppressed: io.lettuce.core.RedisConnectionException: Unable to connect to [************:7003]: connection timed out: /************:7003
			at io.lettuce.core.cluster.topology.ClusterTopologyRefresh.lambda$getConnections$0(ClusterTopologyRefresh.java:290)
			at java.util.concurrent.CompletableFuture.uniWhenComplete(CompletableFuture.java:760)
			at java.util.concurrent.CompletableFuture$UniWhenComplete.tryFire(CompletableFuture.java:736)
			at java.util.concurrent.CompletableFuture.postComplete(CompletableFuture.java:474)
			at java.util.concurrent.CompletableFuture.completeExceptionally(CompletableFuture.java:1977)
			at io.lettuce.core.AbstractRedisClient.lambda$initializeChannelAsync0$4(AbstractRedisClient.java:329)
			at io.netty.util.concurrent.DefaultPromise.notifyListener0(DefaultPromise.java:577)
			at io.netty.util.concurrent.DefaultPromise.notifyListeners0(DefaultPromise.java:570)
			at io.netty.util.concurrent.DefaultPromise.notifyListenersNow(DefaultPromise.java:549)
			at io.netty.util.concurrent.DefaultPromise.notifyListeners(DefaultPromise.java:490)
			at io.netty.util.concurrent.DefaultPromise.setValue0(DefaultPromise.java:615)
			at io.netty.util.concurrent.DefaultPromise.setFailure0(DefaultPromise.java:608)
			at io.netty.util.concurrent.DefaultPromise.tryFailure(DefaultPromise.java:117)
			at io.netty.channel.nio.AbstractNioChannel$AbstractNioUnsafe$1.run(AbstractNioChannel.java:263)
			at io.netty.util.concurrent.PromiseTask.runTask(PromiseTask.java:98)
			at io.netty.util.concurrent.ScheduledFutureTask.run(ScheduledFutureTask.java:150)
			at io.netty.util.concurrent.AbstractEventExecutor.safeExecute(AbstractEventExecutor.java:163)
			at io.netty.util.concurrent.SingleThreadEventExecutor.runAllTasks(SingleThreadEventExecutor.java:510)
			at io.netty.channel.nio.NioEventLoop.run(NioEventLoop.java:518)
			... 4 common frames omitted
		Caused by: io.netty.channel.ConnectTimeoutException: connection timed out: /************:7003
			at io.netty.channel.nio.AbstractNioChannel$AbstractNioUnsafe$1.run(AbstractNioChannel.java:261)
			... 9 common frames omitted
		Suppressed: io.lettuce.core.RedisConnectionException: Unable to connect to [************:7004]: connection timed out: /************:7004
			at io.lettuce.core.cluster.topology.ClusterTopologyRefresh.lambda$getConnections$0(ClusterTopologyRefresh.java:290)
			at java.util.concurrent.CompletableFuture.uniWhenComplete(CompletableFuture.java:760)
			at java.util.concurrent.CompletableFuture$UniWhenComplete.tryFire(CompletableFuture.java:736)
			at java.util.concurrent.CompletableFuture.postComplete(CompletableFuture.java:474)
			at java.util.concurrent.CompletableFuture.completeExceptionally(CompletableFuture.java:1977)
			at io.lettuce.core.AbstractRedisClient.lambda$initializeChannelAsync0$4(AbstractRedisClient.java:329)
			at io.netty.util.concurrent.DefaultPromise.notifyListener0(DefaultPromise.java:577)
			at io.netty.util.concurrent.DefaultPromise.notifyListeners0(DefaultPromise.java:570)
			at io.netty.util.concurrent.DefaultPromise.notifyListenersNow(DefaultPromise.java:549)
			at io.netty.util.concurrent.DefaultPromise.notifyListeners(DefaultPromise.java:490)
			at io.netty.util.concurrent.DefaultPromise.setValue0(DefaultPromise.java:615)
			at io.netty.util.concurrent.DefaultPromise.setFailure0(DefaultPromise.java:608)
			at io.netty.util.concurrent.DefaultPromise.tryFailure(DefaultPromise.java:117)
			at io.netty.channel.nio.AbstractNioChannel$AbstractNioUnsafe$1.run(AbstractNioChannel.java:263)
			at io.netty.util.concurrent.PromiseTask.runTask(PromiseTask.java:98)
			at io.netty.util.concurrent.ScheduledFutureTask.run(ScheduledFutureTask.java:150)
			at io.netty.util.concurrent.AbstractEventExecutor.safeExecute(AbstractEventExecutor.java:163)
			at io.netty.util.concurrent.SingleThreadEventExecutor.runAllTasks(SingleThreadEventExecutor.java:510)
			at io.netty.channel.nio.NioEventLoop.run(NioEventLoop.java:518)
			... 4 common frames omitted
		Caused by: io.netty.channel.ConnectTimeoutException: connection timed out: /************:7004
			at io.netty.channel.nio.AbstractNioChannel$AbstractNioUnsafe$1.run(AbstractNioChannel.java:261)
			... 9 common frames omitted
		Suppressed: io.lettuce.core.RedisConnectionException: Unable to connect to [************:7006]: connection timed out: /************:7006
			at io.lettuce.core.cluster.topology.ClusterTopologyRefresh.lambda$getConnections$0(ClusterTopologyRefresh.java:290)
			at java.util.concurrent.CompletableFuture.uniWhenComplete(CompletableFuture.java:760)
			at java.util.concurrent.CompletableFuture$UniWhenComplete.tryFire(CompletableFuture.java:736)
			at java.util.concurrent.CompletableFuture.postComplete(CompletableFuture.java:474)
			at java.util.concurrent.CompletableFuture.completeExceptionally(CompletableFuture.java:1977)
			at io.lettuce.core.AbstractRedisClient.lambda$initializeChannelAsync0$4(AbstractRedisClient.java:329)
			at io.netty.util.concurrent.DefaultPromise.notifyListener0(DefaultPromise.java:577)
			at io.netty.util.concurrent.DefaultPromise.notifyListeners0(DefaultPromise.java:570)
			at io.netty.util.concurrent.DefaultPromise.notifyListenersNow(DefaultPromise.java:549)
			at io.netty.util.concurrent.DefaultPromise.notifyListeners(DefaultPromise.java:490)
			at io.netty.util.concurrent.DefaultPromise.setValue0(DefaultPromise.java:615)
			at io.netty.util.concurrent.DefaultPromise.setFailure0(DefaultPromise.java:608)
			at io.netty.util.concurrent.DefaultPromise.tryFailure(DefaultPromise.java:117)
			at io.netty.channel.nio.AbstractNioChannel$AbstractNioUnsafe$1.run(AbstractNioChannel.java:263)
			at io.netty.util.concurrent.PromiseTask.runTask(PromiseTask.java:98)
			at io.netty.util.concurrent.ScheduledFutureTask.run(ScheduledFutureTask.java:150)
			at io.netty.util.concurrent.AbstractEventExecutor.safeExecute(AbstractEventExecutor.java:163)
			at io.netty.util.concurrent.SingleThreadEventExecutor.runAllTasks(SingleThreadEventExecutor.java:510)
			at io.netty.channel.nio.NioEventLoop.run(NioEventLoop.java:518)
			... 4 common frames omitted
		Caused by: io.netty.channel.ConnectTimeoutException: connection timed out: /************:7006
			at io.netty.channel.nio.AbstractNioChannel$AbstractNioUnsafe$1.run(AbstractNioChannel.java:261)
			... 9 common frames omitted
	Caused by: io.lettuce.core.RedisConnectionException: Unable to connect to [************:7002]: connection timed out: /************:7002
		at io.lettuce.core.cluster.topology.ClusterTopologyRefresh.lambda$getConnections$0(ClusterTopologyRefresh.java:290)
		at java.util.concurrent.CompletableFuture.uniWhenComplete(CompletableFuture.java:760)
		at java.util.concurrent.CompletableFuture$UniWhenComplete.tryFire(CompletableFuture.java:736)
		at java.util.concurrent.CompletableFuture.postComplete(CompletableFuture.java:474)
		at java.util.concurrent.CompletableFuture.completeExceptionally(CompletableFuture.java:1977)
		at io.lettuce.core.AbstractRedisClient.lambda$initializeChannelAsync0$4(AbstractRedisClient.java:329)
		at io.netty.util.concurrent.DefaultPromise.notifyListener0(DefaultPromise.java:577)
		at io.netty.util.concurrent.DefaultPromise.notifyListeners0(DefaultPromise.java:570)
		at io.netty.util.concurrent.DefaultPromise.notifyListenersNow(DefaultPromise.java:549)
		at io.netty.util.concurrent.DefaultPromise.notifyListeners(DefaultPromise.java:490)
		at io.netty.util.concurrent.DefaultPromise.setValue0(DefaultPromise.java:615)
		at io.netty.util.concurrent.DefaultPromise.setFailure0(DefaultPromise.java:608)
		at io.netty.util.concurrent.DefaultPromise.tryFailure(DefaultPromise.java:117)
		at io.netty.channel.nio.AbstractNioChannel$AbstractNioUnsafe$1.run(AbstractNioChannel.java:263)
		at io.netty.util.concurrent.PromiseTask.runTask(PromiseTask.java:98)
		at io.netty.util.concurrent.ScheduledFutureTask.run(ScheduledFutureTask.java:150)
		at io.netty.util.concurrent.AbstractEventExecutor.safeExecute(AbstractEventExecutor.java:163)
		at io.netty.util.concurrent.SingleThreadEventExecutor.runAllTasks(SingleThreadEventExecutor.java:510)
		at io.netty.channel.nio.NioEventLoop.run(NioEventLoop.java:518)
		... 4 common frames omitted
	Caused by: io.netty.channel.ConnectTimeoutException: connection timed out: /************:7002
		at io.netty.channel.nio.AbstractNioChannel$AbstractNioUnsafe$1.run(AbstractNioChannel.java:261)
		... 9 common frames omitted
Caused by: io.lettuce.core.RedisConnectionException: Unable to establish a connection to Redis Cluster at [RedisURI [host='************', port=7001], RedisURI [host='************', port=7002], RedisURI [host='************', port=7003], RedisURI [host='************', port=7004], RedisURI [host='************', port=7005], RedisURI [host='************', port=7006]]
	at io.lettuce.core.cluster.topology.AsyncConnections.get(AsyncConnections.java:89)
	at io.lettuce.core.cluster.topology.ClusterTopologyRefresh.loadViews(ClusterTopologyRefresh.java:76)
	at io.lettuce.core.cluster.RedisClusterClient.doLoadPartitions(RedisClusterClient.java:865)
	at io.lettuce.core.cluster.RedisClusterClient.loadPartitions(RedisClusterClient.java:845)
	... 10 common frames omitted
	Suppressed: io.lettuce.core.RedisConnectionException: Unable to connect to [************:7001]: connection timed out: /************:7001
		at io.lettuce.core.cluster.topology.ClusterTopologyRefresh.lambda$getConnections$0(ClusterTopologyRefresh.java:290)
		at java.util.concurrent.CompletableFuture.uniWhenComplete(CompletableFuture.java:760)
		at java.util.concurrent.CompletableFuture$UniWhenComplete.tryFire(CompletableFuture.java:736)
		at java.util.concurrent.CompletableFuture.postComplete(CompletableFuture.java:474)
		at java.util.concurrent.CompletableFuture.completeExceptionally(CompletableFuture.java:1977)
		at io.lettuce.core.AbstractRedisClient.lambda$initializeChannelAsync0$4(AbstractRedisClient.java:329)
		at io.netty.util.concurrent.DefaultPromise.notifyListener0(DefaultPromise.java:577)
		at io.netty.util.concurrent.DefaultPromise.notifyListeners0(DefaultPromise.java:570)
		at io.netty.util.concurrent.DefaultPromise.notifyListenersNow(DefaultPromise.java:549)
		at io.netty.util.concurrent.DefaultPromise.notifyListeners(DefaultPromise.java:490)
		at io.netty.util.concurrent.DefaultPromise.setValue0(DefaultPromise.java:615)
		at io.netty.util.concurrent.DefaultPromise.setFailure0(DefaultPromise.java:608)
		at io.netty.util.concurrent.DefaultPromise.tryFailure(DefaultPromise.java:117)
		at io.netty.channel.nio.AbstractNioChannel$AbstractNioUnsafe$1.run(AbstractNioChannel.java:263)
		at io.netty.util.concurrent.PromiseTask.runTask(PromiseTask.java:98)
		at io.netty.util.concurrent.ScheduledFutureTask.run(ScheduledFutureTask.java:150)
		at io.netty.util.concurrent.AbstractEventExecutor.safeExecute(AbstractEventExecutor.java:163)
		at io.netty.util.concurrent.SingleThreadEventExecutor.runAllTasks(SingleThreadEventExecutor.java:510)
		at io.netty.channel.nio.NioEventLoop.run(NioEventLoop.java:518)
		... 4 common frames omitted
	Caused by: io.netty.channel.ConnectTimeoutException: connection timed out: /************:7001
		at io.netty.channel.nio.AbstractNioChannel$AbstractNioUnsafe$1.run(AbstractNioChannel.java:261)
		... 9 common frames omitted
	Suppressed: io.lettuce.core.RedisConnectionException: Unable to connect to [************:7004]: connection timed out: /************:7004
		at io.lettuce.core.cluster.topology.ClusterTopologyRefresh.lambda$getConnections$0(ClusterTopologyRefresh.java:290)
		at java.util.concurrent.CompletableFuture.uniWhenComplete(CompletableFuture.java:760)
		at java.util.concurrent.CompletableFuture$UniWhenComplete.tryFire(CompletableFuture.java:736)
		at java.util.concurrent.CompletableFuture.postComplete(CompletableFuture.java:474)
		at java.util.concurrent.CompletableFuture.completeExceptionally(CompletableFuture.java:1977)
		at io.lettuce.core.AbstractRedisClient.lambda$initializeChannelAsync0$4(AbstractRedisClient.java:329)
		at io.netty.util.concurrent.DefaultPromise.notifyListener0(DefaultPromise.java:577)
		at io.netty.util.concurrent.DefaultPromise.notifyListeners0(DefaultPromise.java:570)
		at io.netty.util.concurrent.DefaultPromise.notifyListenersNow(DefaultPromise.java:549)
		at io.netty.util.concurrent.DefaultPromise.notifyListeners(DefaultPromise.java:490)
		at io.netty.util.concurrent.DefaultPromise.setValue0(DefaultPromise.java:615)
		at io.netty.util.concurrent.DefaultPromise.setFailure0(DefaultPromise.java:608)
		at io.netty.util.concurrent.DefaultPromise.tryFailure(DefaultPromise.java:117)
		at io.netty.channel.nio.AbstractNioChannel$AbstractNioUnsafe$1.run(AbstractNioChannel.java:263)
		at io.netty.util.concurrent.PromiseTask.runTask(PromiseTask.java:98)
		at io.netty.util.concurrent.ScheduledFutureTask.run(ScheduledFutureTask.java:150)
		at io.netty.util.concurrent.AbstractEventExecutor.safeExecute(AbstractEventExecutor.java:163)
		at io.netty.util.concurrent.SingleThreadEventExecutor.runAllTasks(SingleThreadEventExecutor.java:510)
		at io.netty.channel.nio.NioEventLoop.run(NioEventLoop.java:518)
		... 4 common frames omitted
	Caused by: io.netty.channel.ConnectTimeoutException: connection timed out: /************:7004
		at io.netty.channel.nio.AbstractNioChannel$AbstractNioUnsafe$1.run(AbstractNioChannel.java:261)
		... 9 common frames omitted
	Suppressed: io.lettuce.core.RedisConnectionException: Unable to connect to [************:7005]: connection timed out: /************:7005
		at io.lettuce.core.cluster.topology.ClusterTopologyRefresh.lambda$getConnections$0(ClusterTopologyRefresh.java:290)
		at java.util.concurrent.CompletableFuture.uniWhenComplete(CompletableFuture.java:760)
		at java.util.concurrent.CompletableFuture$UniWhenComplete.tryFire(CompletableFuture.java:736)
		at java.util.concurrent.CompletableFuture.postComplete(CompletableFuture.java:474)
		at java.util.concurrent.CompletableFuture.completeExceptionally(CompletableFuture.java:1977)
		at io.lettuce.core.AbstractRedisClient.lambda$initializeChannelAsync0$4(AbstractRedisClient.java:329)
		at io.netty.util.concurrent.DefaultPromise.notifyListener0(DefaultPromise.java:577)
		at io.netty.util.concurrent.DefaultPromise.notifyListeners0(DefaultPromise.java:570)
		at io.netty.util.concurrent.DefaultPromise.notifyListenersNow(DefaultPromise.java:549)
		at io.netty.util.concurrent.DefaultPromise.notifyListeners(DefaultPromise.java:490)
		at io.netty.util.concurrent.DefaultPromise.setValue0(DefaultPromise.java:615)
		at io.netty.util.concurrent.DefaultPromise.setFailure0(DefaultPromise.java:608)
		at io.netty.util.concurrent.DefaultPromise.tryFailure(DefaultPromise.java:117)
		at io.netty.channel.nio.AbstractNioChannel$AbstractNioUnsafe$1.run(AbstractNioChannel.java:263)
		at io.netty.util.concurrent.PromiseTask.runTask(PromiseTask.java:98)
		at io.netty.util.concurrent.ScheduledFutureTask.run(ScheduledFutureTask.java:150)
		at io.netty.util.concurrent.AbstractEventExecutor.safeExecute(AbstractEventExecutor.java:163)
		at io.netty.util.concurrent.SingleThreadEventExecutor.runAllTasks(SingleThreadEventExecutor.java:510)
		at io.netty.channel.nio.NioEventLoop.run(NioEventLoop.java:518)
		... 4 common frames omitted
	Caused by: io.netty.channel.ConnectTimeoutException: connection timed out: /************:7005
		at io.netty.channel.nio.AbstractNioChannel$AbstractNioUnsafe$1.run(AbstractNioChannel.java:261)
		... 9 common frames omitted
	Suppressed: io.lettuce.core.RedisConnectionException: Unable to connect to [************:7006]: connection timed out: /************:7006
		at io.lettuce.core.cluster.topology.ClusterTopologyRefresh.lambda$getConnections$0(ClusterTopologyRefresh.java:290)
		at java.util.concurrent.CompletableFuture.uniWhenComplete(CompletableFuture.java:760)
		at java.util.concurrent.CompletableFuture$UniWhenComplete.tryFire(CompletableFuture.java:736)
		at java.util.concurrent.CompletableFuture.postComplete(CompletableFuture.java:474)
		at java.util.concurrent.CompletableFuture.completeExceptionally(CompletableFuture.java:1977)
		at io.lettuce.core.AbstractRedisClient.lambda$initializeChannelAsync0$4(AbstractRedisClient.java:329)
		at io.netty.util.concurrent.DefaultPromise.notifyListener0(DefaultPromise.java:577)
		at io.netty.util.concurrent.DefaultPromise.notifyListeners0(DefaultPromise.java:570)
		at io.netty.util.concurrent.DefaultPromise.notifyListenersNow(DefaultPromise.java:549)
		at io.netty.util.concurrent.DefaultPromise.notifyListeners(DefaultPromise.java:490)
		at io.netty.util.concurrent.DefaultPromise.setValue0(DefaultPromise.java:615)
		at io.netty.util.concurrent.DefaultPromise.setFailure0(DefaultPromise.java:608)
		at io.netty.util.concurrent.DefaultPromise.tryFailure(DefaultPromise.java:117)
		at io.netty.channel.nio.AbstractNioChannel$AbstractNioUnsafe$1.run(AbstractNioChannel.java:263)
		at io.netty.util.concurrent.PromiseTask.runTask(PromiseTask.java:98)
		at io.netty.util.concurrent.ScheduledFutureTask.run(ScheduledFutureTask.java:150)
		at io.netty.util.concurrent.AbstractEventExecutor.safeExecute(AbstractEventExecutor.java:163)
		at io.netty.util.concurrent.SingleThreadEventExecutor.runAllTasks(SingleThreadEventExecutor.java:510)
		at io.netty.channel.nio.NioEventLoop.run(NioEventLoop.java:518)
		... 4 common frames omitted
	Caused by: io.netty.channel.ConnectTimeoutException: connection timed out: /************:7006
		at io.netty.channel.nio.AbstractNioChannel$AbstractNioUnsafe$1.run(AbstractNioChannel.java:261)
		... 9 common frames omitted
	Suppressed: io.lettuce.core.RedisConnectionException: Unable to connect to [************:7003]: connection timed out: /************:7003
		at io.lettuce.core.cluster.topology.ClusterTopologyRefresh.lambda$getConnections$0(ClusterTopologyRefresh.java:290)
		at java.util.concurrent.CompletableFuture.uniWhenComplete(CompletableFuture.java:760)
		at java.util.concurrent.CompletableFuture$UniWhenComplete.tryFire(CompletableFuture.java:736)
		at java.util.concurrent.CompletableFuture.postComplete(CompletableFuture.java:474)
		at java.util.concurrent.CompletableFuture.completeExceptionally(CompletableFuture.java:1977)
		at io.lettuce.core.AbstractRedisClient.lambda$initializeChannelAsync0$4(AbstractRedisClient.java:329)
		at io.netty.util.concurrent.DefaultPromise.notifyListener0(DefaultPromise.java:577)
		at io.netty.util.concurrent.DefaultPromise.notifyListeners0(DefaultPromise.java:570)
		at io.netty.util.concurrent.DefaultPromise.notifyListenersNow(DefaultPromise.java:549)
		at io.netty.util.concurrent.DefaultPromise.notifyListeners(DefaultPromise.java:490)
		at io.netty.util.concurrent.DefaultPromise.setValue0(DefaultPromise.java:615)
		at io.netty.util.concurrent.DefaultPromise.setFailure0(DefaultPromise.java:608)
		at io.netty.util.concurrent.DefaultPromise.tryFailure(DefaultPromise.java:117)
		at io.netty.channel.nio.AbstractNioChannel$AbstractNioUnsafe$1.run(AbstractNioChannel.java:263)
		at io.netty.util.concurrent.PromiseTask.runTask(PromiseTask.java:98)
		at io.netty.util.concurrent.ScheduledFutureTask.run(ScheduledFutureTask.java:150)
		at io.netty.util.concurrent.AbstractEventExecutor.safeExecute(AbstractEventExecutor.java:163)
		at io.netty.util.concurrent.SingleThreadEventExecutor.runAllTasks(SingleThreadEventExecutor.java:510)
		at io.netty.channel.nio.NioEventLoop.run(NioEventLoop.java:518)
		... 4 common frames omitted
	Caused by: io.netty.channel.ConnectTimeoutException: connection timed out: /************:7003
		at io.netty.channel.nio.AbstractNioChannel$AbstractNioUnsafe$1.run(AbstractNioChannel.java:261)
		... 9 common frames omitted
Caused by: io.lettuce.core.RedisConnectionException: Unable to connect to [************:7002]: connection timed out: /************:7002
	at io.lettuce.core.cluster.topology.ClusterTopologyRefresh.lambda$getConnections$0(ClusterTopologyRefresh.java:290)
	at java.util.concurrent.CompletableFuture.uniWhenComplete(CompletableFuture.java:760)
	at java.util.concurrent.CompletableFuture$UniWhenComplete.tryFire(CompletableFuture.java:736)
	at java.util.concurrent.CompletableFuture.postComplete(CompletableFuture.java:474)
	at java.util.concurrent.CompletableFuture.completeExceptionally(CompletableFuture.java:1977)
	at io.lettuce.core.AbstractRedisClient.lambda$initializeChannelAsync0$4(AbstractRedisClient.java:329)
	at io.netty.util.concurrent.DefaultPromise.notifyListener0(DefaultPromise.java:577)
	at io.netty.util.concurrent.DefaultPromise.notifyListeners0(DefaultPromise.java:570)
	at io.netty.util.concurrent.DefaultPromise.notifyListenersNow(DefaultPromise.java:549)
	at io.netty.util.concurrent.DefaultPromise.notifyListeners(DefaultPromise.java:490)
	at io.netty.util.concurrent.DefaultPromise.setValue0(DefaultPromise.java:615)
	at io.netty.util.concurrent.DefaultPromise.setFailure0(DefaultPromise.java:608)
	at io.netty.util.concurrent.DefaultPromise.tryFailure(DefaultPromise.java:117)
	at io.netty.channel.nio.AbstractNioChannel$AbstractNioUnsafe$1.run(AbstractNioChannel.java:263)
	at io.netty.util.concurrent.PromiseTask.runTask(PromiseTask.java:98)
	at io.netty.util.concurrent.ScheduledFutureTask.run(ScheduledFutureTask.java:150)
	at io.netty.util.concurrent.AbstractEventExecutor.safeExecute(AbstractEventExecutor.java:163)
	at io.netty.util.concurrent.SingleThreadEventExecutor.runAllTasks(SingleThreadEventExecutor.java:510)
	at io.netty.channel.nio.NioEventLoop.run(NioEventLoop.java:518)
	... 4 common frames omitted
Caused by: io.netty.channel.ConnectTimeoutException: connection timed out: /************:7002
	at io.netty.channel.nio.AbstractNioChannel$AbstractNioUnsafe$1.run(AbstractNioChannel.java:261)
	... 9 common frames omitted
2025-06-01 09:20:16.961 [AMQP Connection ************:5672] WARN  com.rabbitmq.client.impl.ForgivingExceptionHandler.log Line:115 - An unexpected connection driver error occured (Exception message: Connection reset)
2025-06-01 09:20:25.506 [lettuce-nioEventLoop-4-5] WARN  io.lettuce.core.cluster.topology.ClusterTopologyRefresh.lambda$getConnections$0 Line:284 - Unable to connect to [************:7002]: connection timed out: /************:7002
2025-06-01 09:20:25.506 [lettuce-nioEventLoop-4-8] WARN  io.lettuce.core.cluster.topology.ClusterTopologyRefresh.lambda$getConnections$0 Line:284 - Unable to connect to [************:7005]: connection timed out: /************:7005
2025-06-01 09:20:25.506 [lettuce-nioEventLoop-4-6] WARN  io.lettuce.core.cluster.topology.ClusterTopologyRefresh.lambda$getConnections$0 Line:284 - Unable to connect to [************:7003]: connection timed out: /************:7003
2025-06-01 09:20:25.506 [lettuce-nioEventLoop-4-9] WARN  io.lettuce.core.cluster.topology.ClusterTopologyRefresh.lambda$getConnections$0 Line:284 - Unable to connect to [************:7006]: connection timed out: /************:7006
2025-06-01 09:20:25.506 [lettuce-nioEventLoop-4-7] WARN  io.lettuce.core.cluster.topology.ClusterTopologyRefresh.lambda$getConnections$0 Line:284 - Unable to connect to [************:7004]: connection timed out: /************:7004
2025-06-01 09:20:25.506 [lettuce-nioEventLoop-4-4] WARN  io.lettuce.core.cluster.topology.ClusterTopologyRefresh.lambda$getConnections$0 Line:284 - Unable to connect to [************:7001]: connection timed out: /************:7001
2025-06-01 09:20:35.517 [lettuce-nioEventLoop-4-3] WARN  io.lettuce.core.cluster.topology.ClusterTopologyRefresh.lambda$getConnections$0 Line:284 - Unable to connect to [************:7001]: connection timed out: /************:7001
2025-06-01 09:20:35.517 [lettuce-nioEventLoop-4-12] WARN  io.lettuce.core.cluster.topology.ClusterTopologyRefresh.lambda$getConnections$0 Line:284 - Unable to connect to [************:7005]: connection timed out: /************:7005
2025-06-01 09:20:35.517 [lettuce-nioEventLoop-4-2] WARN  io.lettuce.core.cluster.topology.ClusterTopologyRefresh.lambda$getConnections$0 Line:284 - Unable to connect to [************:7003]: connection timed out: /************:7003
2025-06-01 09:20:35.517 [lettuce-nioEventLoop-4-1] WARN  io.lettuce.core.cluster.topology.ClusterTopologyRefresh.lambda$getConnections$0 Line:284 - Unable to connect to [************:7002]: connection timed out: /************:7002
2025-06-01 09:20:35.517 [lettuce-nioEventLoop-4-11] WARN  io.lettuce.core.cluster.topology.ClusterTopologyRefresh.lambda$getConnections$0 Line:284 - Unable to connect to [************:7004]: connection timed out: /************:7004
2025-06-01 09:20:35.517 [lettuce-nioEventLoop-4-10] WARN  io.lettuce.core.cluster.topology.ClusterTopologyRefresh.lambda$getConnections$0 Line:284 - Unable to connect to [************:7006]: connection timed out: /************:7006
2025-06-01 09:20:35.531 [lettuce-eventExecutorLoop-1-5] WARN  io.lettuce.core.cluster.ClusterTopologyRefreshScheduler.doRun Line:258 - Cannot refresh Redis Cluster topology
io.lettuce.core.RedisException: Cannot retrieve initial cluster partitions from initial URIs [RedisURI [host='************', port=7006], RedisURI [host='************', port=7004], RedisURI [host='************', port=7005], RedisURI [host='************', port=7002], RedisURI [host='************', port=7003], RedisURI [host='************', port=7001]]
	at io.lettuce.core.cluster.RedisClusterClient.loadPartitions(RedisClusterClient.java:848)
	at io.lettuce.core.cluster.RedisClusterClient.reloadPartitions(RedisClusterClient.java:784)
	at io.lettuce.core.cluster.ClusterTopologyRefreshScheduler$ClusterTopologyRefreshTask.doRun(ClusterTopologyRefreshScheduler.java:256)
	at io.lettuce.core.cluster.ClusterTopologyRefreshScheduler$ClusterTopologyRefreshTask.run(ClusterTopologyRefreshScheduler.java:236)
	at io.netty.util.concurrent.PromiseTask.runTask(PromiseTask.java:98)
	at io.netty.util.concurrent.PromiseTask.run(PromiseTask.java:106)
	at io.netty.util.concurrent.DefaultEventExecutor.run(DefaultEventExecutor.java:66)
	at io.netty.util.concurrent.SingleThreadEventExecutor$6.run(SingleThreadEventExecutor.java:1050)
	at io.netty.util.internal.ThreadExecutorMap$2.run(ThreadExecutorMap.java:74)
	at io.netty.util.concurrent.FastThreadLocalRunnable.run(FastThreadLocalRunnable.java:30)
	at java.lang.Thread.run(Thread.java:748)
	Suppressed: io.lettuce.core.RedisConnectionException: Unable to establish a connection to Redis Cluster at [RedisURI [host='************', port=7001], RedisURI [host='************', port=7002], RedisURI [host='************', port=7003], RedisURI [host='************', port=7004], RedisURI [host='************', port=7005], RedisURI [host='************', port=7006]]
		at io.lettuce.core.cluster.topology.AsyncConnections.get(AsyncConnections.java:89)
		at io.lettuce.core.cluster.topology.ClusterTopologyRefresh.loadViews(ClusterTopologyRefresh.java:76)
		at io.lettuce.core.cluster.RedisClusterClient.doLoadPartitions(RedisClusterClient.java:865)
		at io.lettuce.core.cluster.RedisClusterClient.loadPartitions(RedisClusterClient.java:838)
		... 10 common frames omitted
		Suppressed: io.lettuce.core.RedisConnectionException: Unable to connect to [************:7003]: connection timed out: /************:7003
			at io.lettuce.core.cluster.topology.ClusterTopologyRefresh.lambda$getConnections$0(ClusterTopologyRefresh.java:290)
			at java.util.concurrent.CompletableFuture.uniWhenComplete(CompletableFuture.java:760)
			at java.util.concurrent.CompletableFuture$UniWhenComplete.tryFire(CompletableFuture.java:736)
			at java.util.concurrent.CompletableFuture.postComplete(CompletableFuture.java:474)
			at java.util.concurrent.CompletableFuture.completeExceptionally(CompletableFuture.java:1977)
			at io.lettuce.core.AbstractRedisClient.lambda$initializeChannelAsync0$4(AbstractRedisClient.java:329)
			at io.netty.util.concurrent.DefaultPromise.notifyListener0(DefaultPromise.java:577)
			at io.netty.util.concurrent.DefaultPromise.notifyListeners0(DefaultPromise.java:570)
			at io.netty.util.concurrent.DefaultPromise.notifyListenersNow(DefaultPromise.java:549)
			at io.netty.util.concurrent.DefaultPromise.notifyListeners(DefaultPromise.java:490)
			at io.netty.util.concurrent.DefaultPromise.setValue0(DefaultPromise.java:615)
			at io.netty.util.concurrent.DefaultPromise.setFailure0(DefaultPromise.java:608)
			at io.netty.util.concurrent.DefaultPromise.tryFailure(DefaultPromise.java:117)
			at io.netty.channel.nio.AbstractNioChannel$AbstractNioUnsafe$1.run(AbstractNioChannel.java:263)
			at io.netty.util.concurrent.PromiseTask.runTask(PromiseTask.java:98)
			at io.netty.util.concurrent.ScheduledFutureTask.run(ScheduledFutureTask.java:150)
			at io.netty.util.concurrent.AbstractEventExecutor.safeExecute(AbstractEventExecutor.java:163)
			at io.netty.util.concurrent.SingleThreadEventExecutor.runAllTasks(SingleThreadEventExecutor.java:510)
			at io.netty.channel.nio.NioEventLoop.run(NioEventLoop.java:518)
			... 4 common frames omitted
		Caused by: io.netty.channel.ConnectTimeoutException: connection timed out: /************:7003
			at io.netty.channel.nio.AbstractNioChannel$AbstractNioUnsafe$1.run(AbstractNioChannel.java:261)
			... 9 common frames omitted
		Suppressed: io.lettuce.core.RedisConnectionException: Unable to connect to [************:7005]: connection timed out: /************:7005
			at io.lettuce.core.cluster.topology.ClusterTopologyRefresh.lambda$getConnections$0(ClusterTopologyRefresh.java:290)
			at java.util.concurrent.CompletableFuture.uniWhenComplete(CompletableFuture.java:760)
			at java.util.concurrent.CompletableFuture$UniWhenComplete.tryFire(CompletableFuture.java:736)
			at java.util.concurrent.CompletableFuture.postComplete(CompletableFuture.java:474)
			at java.util.concurrent.CompletableFuture.completeExceptionally(CompletableFuture.java:1977)
			at io.lettuce.core.AbstractRedisClient.lambda$initializeChannelAsync0$4(AbstractRedisClient.java:329)
			at io.netty.util.concurrent.DefaultPromise.notifyListener0(DefaultPromise.java:577)
			at io.netty.util.concurrent.DefaultPromise.notifyListeners0(DefaultPromise.java:570)
			at io.netty.util.concurrent.DefaultPromise.notifyListenersNow(DefaultPromise.java:549)
			at io.netty.util.concurrent.DefaultPromise.notifyListeners(DefaultPromise.java:490)
			at io.netty.util.concurrent.DefaultPromise.setValue0(DefaultPromise.java:615)
			at io.netty.util.concurrent.DefaultPromise.setFailure0(DefaultPromise.java:608)
			at io.netty.util.concurrent.DefaultPromise.tryFailure(DefaultPromise.java:117)
			at io.netty.channel.nio.AbstractNioChannel$AbstractNioUnsafe$1.run(AbstractNioChannel.java:263)
			at io.netty.util.concurrent.PromiseTask.runTask(PromiseTask.java:98)
			at io.netty.util.concurrent.ScheduledFutureTask.run(ScheduledFutureTask.java:150)
			at io.netty.util.concurrent.AbstractEventExecutor.safeExecute(AbstractEventExecutor.java:163)
			at io.netty.util.concurrent.SingleThreadEventExecutor.runAllTasks(SingleThreadEventExecutor.java:510)
			at io.netty.channel.nio.NioEventLoop.run(NioEventLoop.java:518)
			... 4 common frames omitted
		Caused by: io.netty.channel.ConnectTimeoutException: connection timed out: /************:7005
			at io.netty.channel.nio.AbstractNioChannel$AbstractNioUnsafe$1.run(AbstractNioChannel.java:261)
			... 9 common frames omitted
		Suppressed: io.lettuce.core.RedisConnectionException: Unable to connect to [************:7006]: connection timed out: /************:7006
			at io.lettuce.core.cluster.topology.ClusterTopologyRefresh.lambda$getConnections$0(ClusterTopologyRefresh.java:290)
			at java.util.concurrent.CompletableFuture.uniWhenComplete(CompletableFuture.java:760)
			at java.util.concurrent.CompletableFuture$UniWhenComplete.tryFire(CompletableFuture.java:736)
			at java.util.concurrent.CompletableFuture.postComplete(CompletableFuture.java:474)
			at java.util.concurrent.CompletableFuture.completeExceptionally(CompletableFuture.java:1977)
			at io.lettuce.core.AbstractRedisClient.lambda$initializeChannelAsync0$4(AbstractRedisClient.java:329)
			at io.netty.util.concurrent.DefaultPromise.notifyListener0(DefaultPromise.java:577)
			at io.netty.util.concurrent.DefaultPromise.notifyListeners0(DefaultPromise.java:570)
			at io.netty.util.concurrent.DefaultPromise.notifyListenersNow(DefaultPromise.java:549)
			at io.netty.util.concurrent.DefaultPromise.notifyListeners(DefaultPromise.java:490)
			at io.netty.util.concurrent.DefaultPromise.setValue0(DefaultPromise.java:615)
			at io.netty.util.concurrent.DefaultPromise.setFailure0(DefaultPromise.java:608)
			at io.netty.util.concurrent.DefaultPromise.tryFailure(DefaultPromise.java:117)
			at io.netty.channel.nio.AbstractNioChannel$AbstractNioUnsafe$1.run(AbstractNioChannel.java:263)
			at io.netty.util.concurrent.PromiseTask.runTask(PromiseTask.java:98)
			at io.netty.util.concurrent.ScheduledFutureTask.run(ScheduledFutureTask.java:150)
			at io.netty.util.concurrent.AbstractEventExecutor.safeExecute(AbstractEventExecutor.java:163)
			at io.netty.util.concurrent.SingleThreadEventExecutor.runAllTasks(SingleThreadEventExecutor.java:510)
			at io.netty.channel.nio.NioEventLoop.run(NioEventLoop.java:518)
			... 4 common frames omitted
		Caused by: io.netty.channel.ConnectTimeoutException: connection timed out: /************:7006
			at io.netty.channel.nio.AbstractNioChannel$AbstractNioUnsafe$1.run(AbstractNioChannel.java:261)
			... 9 common frames omitted
		Suppressed: io.lettuce.core.RedisConnectionException: Unable to connect to [************:7004]: connection timed out: /************:7004
			at io.lettuce.core.cluster.topology.ClusterTopologyRefresh.lambda$getConnections$0(ClusterTopologyRefresh.java:290)
			at java.util.concurrent.CompletableFuture.uniWhenComplete(CompletableFuture.java:760)
			at java.util.concurrent.CompletableFuture$UniWhenComplete.tryFire(CompletableFuture.java:736)
			at java.util.concurrent.CompletableFuture.postComplete(CompletableFuture.java:474)
			at java.util.concurrent.CompletableFuture.completeExceptionally(CompletableFuture.java:1977)
			at io.lettuce.core.AbstractRedisClient.lambda$initializeChannelAsync0$4(AbstractRedisClient.java:329)
			at io.netty.util.concurrent.DefaultPromise.notifyListener0(DefaultPromise.java:577)
			at io.netty.util.concurrent.DefaultPromise.notifyListeners0(DefaultPromise.java:570)
			at io.netty.util.concurrent.DefaultPromise.notifyListenersNow(DefaultPromise.java:549)
			at io.netty.util.concurrent.DefaultPromise.notifyListeners(DefaultPromise.java:490)
			at io.netty.util.concurrent.DefaultPromise.setValue0(DefaultPromise.java:615)
			at io.netty.util.concurrent.DefaultPromise.setFailure0(DefaultPromise.java:608)
			at io.netty.util.concurrent.DefaultPromise.tryFailure(DefaultPromise.java:117)
			at io.netty.channel.nio.AbstractNioChannel$AbstractNioUnsafe$1.run(AbstractNioChannel.java:263)
			at io.netty.util.concurrent.PromiseTask.runTask(PromiseTask.java:98)
			at io.netty.util.concurrent.ScheduledFutureTask.run(ScheduledFutureTask.java:150)
			at io.netty.util.concurrent.AbstractEventExecutor.safeExecute(AbstractEventExecutor.java:163)
			at io.netty.util.concurrent.SingleThreadEventExecutor.runAllTasks(SingleThreadEventExecutor.java:510)
			at io.netty.channel.nio.NioEventLoop.run(NioEventLoop.java:518)
			... 4 common frames omitted
		Caused by: io.netty.channel.ConnectTimeoutException: connection timed out: /************:7004
			at io.netty.channel.nio.AbstractNioChannel$AbstractNioUnsafe$1.run(AbstractNioChannel.java:261)
			... 9 common frames omitted
		Suppressed: io.lettuce.core.RedisConnectionException: Unable to connect to [************:7001]: connection timed out: /************:7001
			at io.lettuce.core.cluster.topology.ClusterTopologyRefresh.lambda$getConnections$0(ClusterTopologyRefresh.java:290)
			at java.util.concurrent.CompletableFuture.uniWhenComplete(CompletableFuture.java:760)
			at java.util.concurrent.CompletableFuture$UniWhenComplete.tryFire(CompletableFuture.java:736)
			at java.util.concurrent.CompletableFuture.postComplete(CompletableFuture.java:474)
			at java.util.concurrent.CompletableFuture.completeExceptionally(CompletableFuture.java:1977)
			at io.lettuce.core.AbstractRedisClient.lambda$initializeChannelAsync0$4(AbstractRedisClient.java:329)
			at io.netty.util.concurrent.DefaultPromise.notifyListener0(DefaultPromise.java:577)
			at io.netty.util.concurrent.DefaultPromise.notifyListeners0(DefaultPromise.java:570)
			at io.netty.util.concurrent.DefaultPromise.notifyListenersNow(DefaultPromise.java:549)
			at io.netty.util.concurrent.DefaultPromise.notifyListeners(DefaultPromise.java:490)
			at io.netty.util.concurrent.DefaultPromise.setValue0(DefaultPromise.java:615)
			at io.netty.util.concurrent.DefaultPromise.setFailure0(DefaultPromise.java:608)
			at io.netty.util.concurrent.DefaultPromise.tryFailure(DefaultPromise.java:117)
			at io.netty.channel.nio.AbstractNioChannel$AbstractNioUnsafe$1.run(AbstractNioChannel.java:263)
			at io.netty.util.concurrent.PromiseTask.runTask(PromiseTask.java:98)
			at io.netty.util.concurrent.ScheduledFutureTask.run(ScheduledFutureTask.java:150)
			at io.netty.util.concurrent.AbstractEventExecutor.safeExecute(AbstractEventExecutor.java:163)
			at io.netty.util.concurrent.SingleThreadEventExecutor.runAllTasks(SingleThreadEventExecutor.java:510)
			at io.netty.channel.nio.NioEventLoop.run(NioEventLoop.java:518)
			... 4 common frames omitted
		Caused by: io.netty.channel.ConnectTimeoutException: connection timed out: /************:7001
			at io.netty.channel.nio.AbstractNioChannel$AbstractNioUnsafe$1.run(AbstractNioChannel.java:261)
			... 9 common frames omitted
	Caused by: io.lettuce.core.RedisConnectionException: Unable to connect to [************:7002]: connection timed out: /************:7002
		at io.lettuce.core.cluster.topology.ClusterTopologyRefresh.lambda$getConnections$0(ClusterTopologyRefresh.java:290)
		at java.util.concurrent.CompletableFuture.uniWhenComplete(CompletableFuture.java:760)
		at java.util.concurrent.CompletableFuture$UniWhenComplete.tryFire(CompletableFuture.java:736)
		at java.util.concurrent.CompletableFuture.postComplete(CompletableFuture.java:474)
		at java.util.concurrent.CompletableFuture.completeExceptionally(CompletableFuture.java:1977)
		at io.lettuce.core.AbstractRedisClient.lambda$initializeChannelAsync0$4(AbstractRedisClient.java:329)
		at io.netty.util.concurrent.DefaultPromise.notifyListener0(DefaultPromise.java:577)
		at io.netty.util.concurrent.DefaultPromise.notifyListeners0(DefaultPromise.java:570)
		at io.netty.util.concurrent.DefaultPromise.notifyListenersNow(DefaultPromise.java:549)
		at io.netty.util.concurrent.DefaultPromise.notifyListeners(DefaultPromise.java:490)
		at io.netty.util.concurrent.DefaultPromise.setValue0(DefaultPromise.java:615)
		at io.netty.util.concurrent.DefaultPromise.setFailure0(DefaultPromise.java:608)
		at io.netty.util.concurrent.DefaultPromise.tryFailure(DefaultPromise.java:117)
		at io.netty.channel.nio.AbstractNioChannel$AbstractNioUnsafe$1.run(AbstractNioChannel.java:263)
		at io.netty.util.concurrent.PromiseTask.runTask(PromiseTask.java:98)
		at io.netty.util.concurrent.ScheduledFutureTask.run(ScheduledFutureTask.java:150)
		at io.netty.util.concurrent.AbstractEventExecutor.safeExecute(AbstractEventExecutor.java:163)
		at io.netty.util.concurrent.SingleThreadEventExecutor.runAllTasks(SingleThreadEventExecutor.java:510)
		at io.netty.channel.nio.NioEventLoop.run(NioEventLoop.java:518)
		... 4 common frames omitted
	Caused by: io.netty.channel.ConnectTimeoutException: connection timed out: /************:7002
		at io.netty.channel.nio.AbstractNioChannel$AbstractNioUnsafe$1.run(AbstractNioChannel.java:261)
		... 9 common frames omitted
Caused by: io.lettuce.core.RedisConnectionException: Unable to establish a connection to Redis Cluster at [RedisURI [host='************', port=7001], RedisURI [host='************', port=7002], RedisURI [host='************', port=7003], RedisURI [host='************', port=7004], RedisURI [host='************', port=7005], RedisURI [host='************', port=7006]]
	at io.lettuce.core.cluster.topology.AsyncConnections.get(AsyncConnections.java:89)
	at io.lettuce.core.cluster.topology.ClusterTopologyRefresh.loadViews(ClusterTopologyRefresh.java:76)
	at io.lettuce.core.cluster.RedisClusterClient.doLoadPartitions(RedisClusterClient.java:865)
	at io.lettuce.core.cluster.RedisClusterClient.loadPartitions(RedisClusterClient.java:845)
	... 10 common frames omitted
	Suppressed: io.lettuce.core.RedisConnectionException: Unable to connect to [************:7005]: connection timed out: /************:7005
		at io.lettuce.core.cluster.topology.ClusterTopologyRefresh.lambda$getConnections$0(ClusterTopologyRefresh.java:290)
		at java.util.concurrent.CompletableFuture.uniWhenComplete(CompletableFuture.java:760)
		at java.util.concurrent.CompletableFuture$UniWhenComplete.tryFire(CompletableFuture.java:736)
		at java.util.concurrent.CompletableFuture.postComplete(CompletableFuture.java:474)
		at java.util.concurrent.CompletableFuture.completeExceptionally(CompletableFuture.java:1977)
		at io.lettuce.core.AbstractRedisClient.lambda$initializeChannelAsync0$4(AbstractRedisClient.java:329)
		at io.netty.util.concurrent.DefaultPromise.notifyListener0(DefaultPromise.java:577)
		at io.netty.util.concurrent.DefaultPromise.notifyListeners0(DefaultPromise.java:570)
		at io.netty.util.concurrent.DefaultPromise.notifyListenersNow(DefaultPromise.java:549)
		at io.netty.util.concurrent.DefaultPromise.notifyListeners(DefaultPromise.java:490)
		at io.netty.util.concurrent.DefaultPromise.setValue0(DefaultPromise.java:615)
		at io.netty.util.concurrent.DefaultPromise.setFailure0(DefaultPromise.java:608)
		at io.netty.util.concurrent.DefaultPromise.tryFailure(DefaultPromise.java:117)
		at io.netty.channel.nio.AbstractNioChannel$AbstractNioUnsafe$1.run(AbstractNioChannel.java:263)
		at io.netty.util.concurrent.PromiseTask.runTask(PromiseTask.java:98)
		at io.netty.util.concurrent.ScheduledFutureTask.run(ScheduledFutureTask.java:150)
		at io.netty.util.concurrent.AbstractEventExecutor.safeExecute(AbstractEventExecutor.java:163)
		at io.netty.util.concurrent.SingleThreadEventExecutor.runAllTasks(SingleThreadEventExecutor.java:510)
		at io.netty.channel.nio.NioEventLoop.run(NioEventLoop.java:518)
		... 4 common frames omitted
	Caused by: io.netty.channel.ConnectTimeoutException: connection timed out: /************:7005
		at io.netty.channel.nio.AbstractNioChannel$AbstractNioUnsafe$1.run(AbstractNioChannel.java:261)
		... 9 common frames omitted
	Suppressed: io.lettuce.core.RedisConnectionException: Unable to connect to [************:7003]: connection timed out: /************:7003
		at io.lettuce.core.cluster.topology.ClusterTopologyRefresh.lambda$getConnections$0(ClusterTopologyRefresh.java:290)
		at java.util.concurrent.CompletableFuture.uniWhenComplete(CompletableFuture.java:760)
		at java.util.concurrent.CompletableFuture$UniWhenComplete.tryFire(CompletableFuture.java:736)
		at java.util.concurrent.CompletableFuture.postComplete(CompletableFuture.java:474)
		at java.util.concurrent.CompletableFuture.completeExceptionally(CompletableFuture.java:1977)
		at io.lettuce.core.AbstractRedisClient.lambda$initializeChannelAsync0$4(AbstractRedisClient.java:329)
		at io.netty.util.concurrent.DefaultPromise.notifyListener0(DefaultPromise.java:577)
		at io.netty.util.concurrent.DefaultPromise.notifyListeners0(DefaultPromise.java:570)
		at io.netty.util.concurrent.DefaultPromise.notifyListenersNow(DefaultPromise.java:549)
		at io.netty.util.concurrent.DefaultPromise.notifyListeners(DefaultPromise.java:490)
		at io.netty.util.concurrent.DefaultPromise.setValue0(DefaultPromise.java:615)
		at io.netty.util.concurrent.DefaultPromise.setFailure0(DefaultPromise.java:608)
		at io.netty.util.concurrent.DefaultPromise.tryFailure(DefaultPromise.java:117)
		at io.netty.channel.nio.AbstractNioChannel$AbstractNioUnsafe$1.run(AbstractNioChannel.java:263)
		at io.netty.util.concurrent.PromiseTask.runTask(PromiseTask.java:98)
		at io.netty.util.concurrent.ScheduledFutureTask.run(ScheduledFutureTask.java:150)
		at io.netty.util.concurrent.AbstractEventExecutor.safeExecute(AbstractEventExecutor.java:163)
		at io.netty.util.concurrent.SingleThreadEventExecutor.runAllTasks(SingleThreadEventExecutor.java:510)
		at io.netty.channel.nio.NioEventLoop.run(NioEventLoop.java:518)
		... 4 common frames omitted
	Caused by: io.netty.channel.ConnectTimeoutException: connection timed out: /************:7003
		at io.netty.channel.nio.AbstractNioChannel$AbstractNioUnsafe$1.run(AbstractNioChannel.java:261)
		... 9 common frames omitted
	Suppressed: io.lettuce.core.RedisConnectionException: Unable to connect to [************:7002]: connection timed out: /************:7002
		at io.lettuce.core.cluster.topology.ClusterTopologyRefresh.lambda$getConnections$0(ClusterTopologyRefresh.java:290)
		at java.util.concurrent.CompletableFuture.uniWhenComplete(CompletableFuture.java:760)
		at java.util.concurrent.CompletableFuture$UniWhenComplete.tryFire(CompletableFuture.java:736)
		at java.util.concurrent.CompletableFuture.postComplete(CompletableFuture.java:474)
		at java.util.concurrent.CompletableFuture.completeExceptionally(CompletableFuture.java:1977)
		at io.lettuce.core.AbstractRedisClient.lambda$initializeChannelAsync0$4(AbstractRedisClient.java:329)
		at io.netty.util.concurrent.DefaultPromise.notifyListener0(DefaultPromise.java:577)
		at io.netty.util.concurrent.DefaultPromise.notifyListeners0(DefaultPromise.java:570)
		at io.netty.util.concurrent.DefaultPromise.notifyListenersNow(DefaultPromise.java:549)
		at io.netty.util.concurrent.DefaultPromise.notifyListeners(DefaultPromise.java:490)
		at io.netty.util.concurrent.DefaultPromise.setValue0(DefaultPromise.java:615)
		at io.netty.util.concurrent.DefaultPromise.setFailure0(DefaultPromise.java:608)
		at io.netty.util.concurrent.DefaultPromise.tryFailure(DefaultPromise.java:117)
		at io.netty.channel.nio.AbstractNioChannel$AbstractNioUnsafe$1.run(AbstractNioChannel.java:263)
		at io.netty.util.concurrent.PromiseTask.runTask(PromiseTask.java:98)
		at io.netty.util.concurrent.ScheduledFutureTask.run(ScheduledFutureTask.java:150)
		at io.netty.util.concurrent.AbstractEventExecutor.safeExecute(AbstractEventExecutor.java:163)
		at io.netty.util.concurrent.SingleThreadEventExecutor.runAllTasks(SingleThreadEventExecutor.java:510)
		at io.netty.channel.nio.NioEventLoop.run(NioEventLoop.java:518)
		... 4 common frames omitted
	Caused by: io.netty.channel.ConnectTimeoutException: connection timed out: /************:7002
		at io.netty.channel.nio.AbstractNioChannel$AbstractNioUnsafe$1.run(AbstractNioChannel.java:261)
		... 9 common frames omitted
	Suppressed: io.lettuce.core.RedisConnectionException: Unable to connect to [************:7004]: connection timed out: /************:7004
		at io.lettuce.core.cluster.topology.ClusterTopologyRefresh.lambda$getConnections$0(ClusterTopologyRefresh.java:290)
		at java.util.concurrent.CompletableFuture.uniWhenComplete(CompletableFuture.java:760)
		at java.util.concurrent.CompletableFuture$UniWhenComplete.tryFire(CompletableFuture.java:736)
		at java.util.concurrent.CompletableFuture.postComplete(CompletableFuture.java:474)
		at java.util.concurrent.CompletableFuture.completeExceptionally(CompletableFuture.java:1977)
		at io.lettuce.core.AbstractRedisClient.lambda$initializeChannelAsync0$4(AbstractRedisClient.java:329)
		at io.netty.util.concurrent.DefaultPromise.notifyListener0(DefaultPromise.java:577)
		at io.netty.util.concurrent.DefaultPromise.notifyListeners0(DefaultPromise.java:570)
		at io.netty.util.concurrent.DefaultPromise.notifyListenersNow(DefaultPromise.java:549)
		at io.netty.util.concurrent.DefaultPromise.notifyListeners(DefaultPromise.java:490)
		at io.netty.util.concurrent.DefaultPromise.setValue0(DefaultPromise.java:615)
		at io.netty.util.concurrent.DefaultPromise.setFailure0(DefaultPromise.java:608)
		at io.netty.util.concurrent.DefaultPromise.tryFailure(DefaultPromise.java:117)
		at io.netty.channel.nio.AbstractNioChannel$AbstractNioUnsafe$1.run(AbstractNioChannel.java:263)
		at io.netty.util.concurrent.PromiseTask.runTask(PromiseTask.java:98)
		at io.netty.util.concurrent.ScheduledFutureTask.run(ScheduledFutureTask.java:150)
		at io.netty.util.concurrent.AbstractEventExecutor.safeExecute(AbstractEventExecutor.java:163)
		at io.netty.util.concurrent.SingleThreadEventExecutor.runAllTasks(SingleThreadEventExecutor.java:510)
		at io.netty.channel.nio.NioEventLoop.run(NioEventLoop.java:518)
		... 4 common frames omitted
	Caused by: io.netty.channel.ConnectTimeoutException: connection timed out: /************:7004
		at io.netty.channel.nio.AbstractNioChannel$AbstractNioUnsafe$1.run(AbstractNioChannel.java:261)
		... 9 common frames omitted
	Suppressed: io.lettuce.core.RedisConnectionException: Unable to connect to [************:7006]: connection timed out: /************:7006
		at io.lettuce.core.cluster.topology.ClusterTopologyRefresh.lambda$getConnections$0(ClusterTopologyRefresh.java:290)
		at java.util.concurrent.CompletableFuture.uniWhenComplete(CompletableFuture.java:760)
		at java.util.concurrent.CompletableFuture$UniWhenComplete.tryFire(CompletableFuture.java:736)
		at java.util.concurrent.CompletableFuture.postComplete(CompletableFuture.java:474)
		at java.util.concurrent.CompletableFuture.completeExceptionally(CompletableFuture.java:1977)
		at io.lettuce.core.AbstractRedisClient.lambda$initializeChannelAsync0$4(AbstractRedisClient.java:329)
		at io.netty.util.concurrent.DefaultPromise.notifyListener0(DefaultPromise.java:577)
		at io.netty.util.concurrent.DefaultPromise.notifyListeners0(DefaultPromise.java:570)
		at io.netty.util.concurrent.DefaultPromise.notifyListenersNow(DefaultPromise.java:549)
		at io.netty.util.concurrent.DefaultPromise.notifyListeners(DefaultPromise.java:490)
		at io.netty.util.concurrent.DefaultPromise.setValue0(DefaultPromise.java:615)
		at io.netty.util.concurrent.DefaultPromise.setFailure0(DefaultPromise.java:608)
		at io.netty.util.concurrent.DefaultPromise.tryFailure(DefaultPromise.java:117)
		at io.netty.channel.nio.AbstractNioChannel$AbstractNioUnsafe$1.run(AbstractNioChannel.java:263)
		at io.netty.util.concurrent.PromiseTask.runTask(PromiseTask.java:98)
		at io.netty.util.concurrent.ScheduledFutureTask.run(ScheduledFutureTask.java:150)
		at io.netty.util.concurrent.AbstractEventExecutor.safeExecute(AbstractEventExecutor.java:163)
		at io.netty.util.concurrent.SingleThreadEventExecutor.runAllTasks(SingleThreadEventExecutor.java:510)
		at io.netty.channel.nio.NioEventLoop.run(NioEventLoop.java:518)
		... 4 common frames omitted
	Caused by: io.netty.channel.ConnectTimeoutException: connection timed out: /************:7006
		at io.netty.channel.nio.AbstractNioChannel$AbstractNioUnsafe$1.run(AbstractNioChannel.java:261)
		... 9 common frames omitted
Caused by: io.lettuce.core.RedisConnectionException: Unable to connect to [************:7001]: connection timed out: /************:7001
	at io.lettuce.core.cluster.topology.ClusterTopologyRefresh.lambda$getConnections$0(ClusterTopologyRefresh.java:290)
	at java.util.concurrent.CompletableFuture.uniWhenComplete(CompletableFuture.java:760)
	at java.util.concurrent.CompletableFuture$UniWhenComplete.tryFire(CompletableFuture.java:736)
	at java.util.concurrent.CompletableFuture.postComplete(CompletableFuture.java:474)
	at java.util.concurrent.CompletableFuture.completeExceptionally(CompletableFuture.java:1977)
	at io.lettuce.core.AbstractRedisClient.lambda$initializeChannelAsync0$4(AbstractRedisClient.java:329)
	at io.netty.util.concurrent.DefaultPromise.notifyListener0(DefaultPromise.java:577)
	at io.netty.util.concurrent.DefaultPromise.notifyListeners0(DefaultPromise.java:570)
	at io.netty.util.concurrent.DefaultPromise.notifyListenersNow(DefaultPromise.java:549)
	at io.netty.util.concurrent.DefaultPromise.notifyListeners(DefaultPromise.java:490)
	at io.netty.util.concurrent.DefaultPromise.setValue0(DefaultPromise.java:615)
	at io.netty.util.concurrent.DefaultPromise.setFailure0(DefaultPromise.java:608)
	at io.netty.util.concurrent.DefaultPromise.tryFailure(DefaultPromise.java:117)
	at io.netty.channel.nio.AbstractNioChannel$AbstractNioUnsafe$1.run(AbstractNioChannel.java:263)
	at io.netty.util.concurrent.PromiseTask.runTask(PromiseTask.java:98)
	at io.netty.util.concurrent.ScheduledFutureTask.run(ScheduledFutureTask.java:150)
	at io.netty.util.concurrent.AbstractEventExecutor.safeExecute(AbstractEventExecutor.java:163)
	at io.netty.util.concurrent.SingleThreadEventExecutor.runAllTasks(SingleThreadEventExecutor.java:510)
	at io.netty.channel.nio.NioEventLoop.run(NioEventLoop.java:518)
	... 4 common frames omitted
Caused by: io.netty.channel.ConnectTimeoutException: connection timed out: /************:7001
	at io.netty.channel.nio.AbstractNioChannel$AbstractNioUnsafe$1.run(AbstractNioChannel.java:261)
	... 9 common frames omitted
2025-06-01 09:20:41.586 [org.springframework.amqp.rabbit.RabbitListenerEndpointContainer#1-65] WARN  org.springframework.amqp.rabbit.listener.SimpleMessageListenerContainer.logConsumerException Line:1440 - Consumer raised exception, processing can restart if the connection factory supports it. Exception summary: org.springframework.amqp.AmqpConnectException: java.net.ConnectException: Connection timed out: connect
2025-06-01 09:20:41.834 [org.springframework.amqp.rabbit.RabbitListenerEndpointContainer#0-65] WARN  org.springframework.amqp.rabbit.listener.SimpleMessageListenerContainer.logConsumerException Line:1440 - Consumer raised exception, processing can restart if the connection factory supports it. Exception summary: org.springframework.amqp.AmqpConnectException: java.net.ConnectException: Connection timed out: connect
2025-06-01 09:20:42.222 [org.springframework.amqp.rabbit.RabbitListenerEndpointContainer#3-65] WARN  org.springframework.amqp.rabbit.listener.SimpleMessageListenerContainer.logConsumerException Line:1440 - Consumer raised exception, processing can restart if the connection factory supports it. Exception summary: org.springframework.amqp.AmqpConnectException: java.net.ConnectException: Connection timed out: connect
2025-06-01 09:20:44.125 [org.springframework.amqp.rabbit.RabbitListenerEndpointContainer#2-65] WARN  org.springframework.amqp.rabbit.listener.SimpleMessageListenerContainer.logConsumerException Line:1440 - Consumer raised exception, processing can restart if the connection factory supports it. Exception summary: org.springframework.amqp.AmqpConnectException: java.net.ConnectException: Connection timed out: connect
2025-06-01 10:01:12.369 [AMQP Connection ************:5672] WARN  com.rabbitmq.client.impl.ForgivingExceptionHandler.log Line:115 - An unexpected connection driver error occured (Exception message: Connection reset)
2025-06-01 10:01:12.617 [AMQP Connection ************:5672] WARN  com.rabbitmq.client.impl.ForgivingExceptionHandler.log Line:115 - An unexpected connection driver error occured (Exception message: Connection reset)
2025-06-01 10:01:12.992 [AMQP Connection ************:5672] WARN  com.rabbitmq.client.impl.ForgivingExceptionHandler.log Line:115 - An unexpected connection driver error occured (Exception message: Connection reset)
2025-06-01 10:01:14.881 [AMQP Connection ************:5672] WARN  com.rabbitmq.client.impl.ForgivingExceptionHandler.log Line:115 - An unexpected connection driver error occured (Exception message: Connection reset)
2025-06-01 10:41:42.342 [AMQP Connection ************:5672] WARN  com.rabbitmq.client.impl.ForgivingExceptionHandler.log Line:115 - An unexpected connection driver error occured (Exception message: Connection reset)
2025-06-01 10:41:42.635 [AMQP Connection ************:5672] WARN  com.rabbitmq.client.impl.ForgivingExceptionHandler.log Line:115 - An unexpected connection driver error occured (Exception message: Connection reset)
2025-06-01 10:41:43.023 [AMQP Connection ************:5672] WARN  com.rabbitmq.client.impl.ForgivingExceptionHandler.log Line:115 - An unexpected connection driver error occured (Exception message: Connection reset)
2025-06-01 10:41:44.868 [AMQP Connection ************:5672] WARN  com.rabbitmq.client.impl.ForgivingExceptionHandler.log Line:115 - An unexpected connection driver error occured (Exception message: Connection reset)
2025-06-01 11:21:55.287 [lettuce-nioEventLoop-4-2] WARN  io.lettuce.core.cluster.topology.ClusterTopologyRefresh.lambda$getConnections$0 Line:284 - Unable to connect to [************:7004]: No route to host: no further information: /************:7004
2025-06-01 11:21:55.287 [lettuce-nioEventLoop-4-11] WARN  io.lettuce.core.cluster.topology.ClusterTopologyRefresh.lambda$getConnections$0 Line:284 - Unable to connect to [************:7001]: No route to host: no further information: /************:7001
2025-06-01 11:21:55.287 [lettuce-nioEventLoop-4-1] WARN  io.lettuce.core.cluster.topology.ClusterTopologyRefresh.lambda$getConnections$0 Line:284 - Unable to connect to [************:7003]: No route to host: no further information: /************:7003
2025-06-01 11:21:55.287 [lettuce-nioEventLoop-4-12] WARN  io.lettuce.core.cluster.topology.ClusterTopologyRefresh.lambda$getConnections$0 Line:284 - Unable to connect to [************:7002]: No route to host: no further information: /************:7002
2025-06-01 11:21:55.287 [lettuce-nioEventLoop-4-3] WARN  io.lettuce.core.cluster.topology.ClusterTopologyRefresh.lambda$getConnections$0 Line:284 - Unable to connect to [************:7005]: No route to host: no further information: /************:7005
2025-06-01 11:21:55.288 [lettuce-nioEventLoop-4-4] WARN  io.lettuce.core.cluster.topology.ClusterTopologyRefresh.lambda$getConnections$0 Line:284 - Unable to connect to [************:7006]: No route to host: no further information: /************:7006
2025-06-01 11:21:55.289 [lettuce-nioEventLoop-4-5] WARN  io.lettuce.core.cluster.topology.ClusterTopologyRefresh.lambda$getConnections$0 Line:284 - Unable to connect to [************:7006]: No route to host: no further information: /************:7006
2025-06-01 11:21:55.289 [lettuce-nioEventLoop-4-6] WARN  io.lettuce.core.cluster.topology.ClusterTopologyRefresh.lambda$getConnections$0 Line:284 - Unable to connect to [************:7004]: No route to host: no further information: /************:7004
2025-06-01 11:21:55.289 [lettuce-nioEventLoop-4-7] WARN  io.lettuce.core.cluster.topology.ClusterTopologyRefresh.lambda$getConnections$0 Line:284 - Unable to connect to [************:7005]: No route to host: no further information: /************:7005
2025-06-01 11:21:55.289 [lettuce-nioEventLoop-4-8] WARN  io.lettuce.core.cluster.topology.ClusterTopologyRefresh.lambda$getConnections$0 Line:284 - Unable to connect to [************:7002]: No route to host: no further information: /************:7002
2025-06-01 11:21:55.289 [lettuce-nioEventLoop-4-9] WARN  io.lettuce.core.cluster.topology.ClusterTopologyRefresh.lambda$getConnections$0 Line:284 - Unable to connect to [************:7003]: No route to host: no further information: /************:7003
2025-06-01 11:21:55.289 [lettuce-nioEventLoop-4-10] WARN  io.lettuce.core.cluster.topology.ClusterTopologyRefresh.lambda$getConnections$0 Line:284 - Unable to connect to [************:7001]: No route to host: no further information: /************:7001
2025-06-01 11:21:55.298 [lettuce-eventExecutorLoop-1-8] WARN  io.lettuce.core.cluster.ClusterTopologyRefreshScheduler.doRun Line:258 - Cannot refresh Redis Cluster topology
io.lettuce.core.RedisException: Cannot retrieve initial cluster partitions from initial URIs [RedisURI [host='************', port=7006], RedisURI [host='************', port=7004], RedisURI [host='************', port=7005], RedisURI [host='************', port=7002], RedisURI [host='************', port=7003], RedisURI [host='************', port=7001]]
	at io.lettuce.core.cluster.RedisClusterClient.loadPartitions(RedisClusterClient.java:848)
	at io.lettuce.core.cluster.RedisClusterClient.reloadPartitions(RedisClusterClient.java:784)
	at io.lettuce.core.cluster.ClusterTopologyRefreshScheduler$ClusterTopologyRefreshTask.doRun(ClusterTopologyRefreshScheduler.java:256)
	at io.lettuce.core.cluster.ClusterTopologyRefreshScheduler$ClusterTopologyRefreshTask.run(ClusterTopologyRefreshScheduler.java:236)
	at io.netty.util.concurrent.PromiseTask.runTask(PromiseTask.java:98)
	at io.netty.util.concurrent.PromiseTask.run(PromiseTask.java:106)
	at io.netty.util.concurrent.DefaultEventExecutor.run(DefaultEventExecutor.java:66)
	at io.netty.util.concurrent.SingleThreadEventExecutor$6.run(SingleThreadEventExecutor.java:1050)
	at io.netty.util.internal.ThreadExecutorMap$2.run(ThreadExecutorMap.java:74)
	at io.netty.util.concurrent.FastThreadLocalRunnable.run(FastThreadLocalRunnable.java:30)
	at java.lang.Thread.run(Thread.java:748)
	Suppressed: io.lettuce.core.RedisConnectionException: Unable to establish a connection to Redis Cluster at [RedisURI [host='************', port=7001], RedisURI [host='************', port=7002], RedisURI [host='************', port=7003], RedisURI [host='************', port=7004], RedisURI [host='************', port=7005], RedisURI [host='************', port=7006]]
		at io.lettuce.core.cluster.topology.AsyncConnections.get(AsyncConnections.java:89)
		at io.lettuce.core.cluster.topology.ClusterTopologyRefresh.loadViews(ClusterTopologyRefresh.java:76)
		at io.lettuce.core.cluster.RedisClusterClient.doLoadPartitions(RedisClusterClient.java:865)
		at io.lettuce.core.cluster.RedisClusterClient.loadPartitions(RedisClusterClient.java:838)
		... 10 common frames omitted
		Suppressed: io.lettuce.core.RedisConnectionException: Unable to connect to [************:7001]: No route to host: no further information: /************:7001
			at io.lettuce.core.cluster.topology.ClusterTopologyRefresh.lambda$getConnections$0(ClusterTopologyRefresh.java:290)
			at java.util.concurrent.CompletableFuture.uniWhenComplete(CompletableFuture.java:760)
			at java.util.concurrent.CompletableFuture$UniWhenComplete.tryFire(CompletableFuture.java:736)
			at java.util.concurrent.CompletableFuture.postComplete(CompletableFuture.java:474)
			at java.util.concurrent.CompletableFuture.completeExceptionally(CompletableFuture.java:1977)
			at io.lettuce.core.AbstractRedisClient.lambda$initializeChannelAsync0$4(AbstractRedisClient.java:329)
			at io.netty.util.concurrent.DefaultPromise.notifyListener0(DefaultPromise.java:577)
			at io.netty.util.concurrent.DefaultPromise.notifyListeners0(DefaultPromise.java:570)
			at io.netty.util.concurrent.DefaultPromise.notifyListenersNow(DefaultPromise.java:549)
			at io.netty.util.concurrent.DefaultPromise.notifyListeners(DefaultPromise.java:490)
			at io.netty.util.concurrent.DefaultPromise.setValue0(DefaultPromise.java:615)
			at io.netty.util.concurrent.DefaultPromise.setFailure0(DefaultPromise.java:608)
			at io.netty.util.concurrent.DefaultPromise.tryFailure(DefaultPromise.java:117)
			at io.netty.channel.nio.AbstractNioChannel$AbstractNioUnsafe.fulfillConnectPromise(AbstractNioChannel.java:321)
			at io.netty.channel.nio.AbstractNioChannel$AbstractNioUnsafe.finishConnect(AbstractNioChannel.java:337)
			at io.netty.channel.nio.NioEventLoop.processSelectedKey(NioEventLoop.java:688)
			at io.netty.channel.nio.NioEventLoop.processSelectedKeysOptimized(NioEventLoop.java:635)
			at io.netty.channel.nio.NioEventLoop.processSelectedKeys(NioEventLoop.java:552)
			at io.netty.channel.nio.NioEventLoop.run(NioEventLoop.java:514)
			... 4 common frames omitted
		Caused by: io.netty.channel.AbstractChannel$AnnotatedNoRouteToHostException: No route to host: no further information: /************:7001
		Caused by: java.net.NoRouteToHostException: No route to host: no further information
			at sun.nio.ch.SocketChannelImpl.checkConnect(Native Method)
			at sun.nio.ch.SocketChannelImpl.finishConnect(SocketChannelImpl.java:717)
			at io.netty.channel.socket.nio.NioSocketChannel.doFinishConnect(NioSocketChannel.java:330)
			at io.netty.channel.nio.AbstractNioChannel$AbstractNioUnsafe.finishConnect(AbstractNioChannel.java:334)
			at io.netty.channel.nio.NioEventLoop.processSelectedKey(NioEventLoop.java:688)
			at io.netty.channel.nio.NioEventLoop.processSelectedKeysOptimized(NioEventLoop.java:635)
			at io.netty.channel.nio.NioEventLoop.processSelectedKeys(NioEventLoop.java:552)
			at io.netty.channel.nio.NioEventLoop.run(NioEventLoop.java:514)
			at io.netty.util.concurrent.SingleThreadEventExecutor$6.run(SingleThreadEventExecutor.java:1050)
			at io.netty.util.internal.ThreadExecutorMap$2.run(ThreadExecutorMap.java:74)
			at io.netty.util.concurrent.FastThreadLocalRunnable.run(FastThreadLocalRunnable.java:30)
			at java.lang.Thread.run(Thread.java:748)
		Suppressed: io.lettuce.core.RedisConnectionException: Unable to connect to [************:7003]: No route to host: no further information: /************:7003
			at io.lettuce.core.cluster.topology.ClusterTopologyRefresh.lambda$getConnections$0(ClusterTopologyRefresh.java:290)
			at java.util.concurrent.CompletableFuture.uniWhenComplete(CompletableFuture.java:760)
			at java.util.concurrent.CompletableFuture$UniWhenComplete.tryFire(CompletableFuture.java:736)
			at java.util.concurrent.CompletableFuture.postComplete(CompletableFuture.java:474)
			at java.util.concurrent.CompletableFuture.completeExceptionally(CompletableFuture.java:1977)
			at io.lettuce.core.AbstractRedisClient.lambda$initializeChannelAsync0$4(AbstractRedisClient.java:329)
			at io.netty.util.concurrent.DefaultPromise.notifyListener0(DefaultPromise.java:577)
			at io.netty.util.concurrent.DefaultPromise.notifyListeners0(DefaultPromise.java:570)
			at io.netty.util.concurrent.DefaultPromise.notifyListenersNow(DefaultPromise.java:549)
			at io.netty.util.concurrent.DefaultPromise.notifyListeners(DefaultPromise.java:490)
			at io.netty.util.concurrent.DefaultPromise.setValue0(DefaultPromise.java:615)
			at io.netty.util.concurrent.DefaultPromise.setFailure0(DefaultPromise.java:608)
			at io.netty.util.concurrent.DefaultPromise.tryFailure(DefaultPromise.java:117)
			at io.netty.channel.nio.AbstractNioChannel$AbstractNioUnsafe.fulfillConnectPromise(AbstractNioChannel.java:321)
			at io.netty.channel.nio.AbstractNioChannel$AbstractNioUnsafe.finishConnect(AbstractNioChannel.java:337)
			at io.netty.channel.nio.NioEventLoop.processSelectedKey(NioEventLoop.java:688)
			at io.netty.channel.nio.NioEventLoop.processSelectedKeysOptimized(NioEventLoop.java:635)
			at io.netty.channel.nio.NioEventLoop.processSelectedKeys(NioEventLoop.java:552)
			at io.netty.channel.nio.NioEventLoop.run(NioEventLoop.java:514)
			... 4 common frames omitted
		Caused by: io.netty.channel.AbstractChannel$AnnotatedNoRouteToHostException: No route to host: no further information: /************:7003
		Caused by: java.net.NoRouteToHostException: No route to host: no further information
			at sun.nio.ch.SocketChannelImpl.checkConnect(Native Method)
			at sun.nio.ch.SocketChannelImpl.finishConnect(SocketChannelImpl.java:717)
			at io.netty.channel.socket.nio.NioSocketChannel.doFinishConnect(NioSocketChannel.java:330)
			at io.netty.channel.nio.AbstractNioChannel$AbstractNioUnsafe.finishConnect(AbstractNioChannel.java:334)
			at io.netty.channel.nio.NioEventLoop.processSelectedKey(NioEventLoop.java:688)
			at io.netty.channel.nio.NioEventLoop.processSelectedKeysOptimized(NioEventLoop.java:635)
			at io.netty.channel.nio.NioEventLoop.processSelectedKeys(NioEventLoop.java:552)
			at io.netty.channel.nio.NioEventLoop.run(NioEventLoop.java:514)
			at io.netty.util.concurrent.SingleThreadEventExecutor$6.run(SingleThreadEventExecutor.java:1050)
			at io.netty.util.internal.ThreadExecutorMap$2.run(ThreadExecutorMap.java:74)
			at io.netty.util.concurrent.FastThreadLocalRunnable.run(FastThreadLocalRunnable.java:30)
			at java.lang.Thread.run(Thread.java:748)
		Suppressed: io.lettuce.core.RedisConnectionException: Unable to connect to [************:7002]: No route to host: no further information: /************:7002
			at io.lettuce.core.cluster.topology.ClusterTopologyRefresh.lambda$getConnections$0(ClusterTopologyRefresh.java:290)
			at java.util.concurrent.CompletableFuture.uniWhenComplete(CompletableFuture.java:760)
			at java.util.concurrent.CompletableFuture$UniWhenComplete.tryFire(CompletableFuture.java:736)
			at java.util.concurrent.CompletableFuture.postComplete(CompletableFuture.java:474)
			at java.util.concurrent.CompletableFuture.completeExceptionally(CompletableFuture.java:1977)
			at io.lettuce.core.AbstractRedisClient.lambda$initializeChannelAsync0$4(AbstractRedisClient.java:329)
			at io.netty.util.concurrent.DefaultPromise.notifyListener0(DefaultPromise.java:577)
			at io.netty.util.concurrent.DefaultPromise.notifyListeners0(DefaultPromise.java:570)
			at io.netty.util.concurrent.DefaultPromise.notifyListenersNow(DefaultPromise.java:549)
			at io.netty.util.concurrent.DefaultPromise.notifyListeners(DefaultPromise.java:490)
			at io.netty.util.concurrent.DefaultPromise.setValue0(DefaultPromise.java:615)
			at io.netty.util.concurrent.DefaultPromise.setFailure0(DefaultPromise.java:608)
			at io.netty.util.concurrent.DefaultPromise.tryFailure(DefaultPromise.java:117)
			at io.netty.channel.nio.AbstractNioChannel$AbstractNioUnsafe.fulfillConnectPromise(AbstractNioChannel.java:321)
			at io.netty.channel.nio.AbstractNioChannel$AbstractNioUnsafe.finishConnect(AbstractNioChannel.java:337)
			at io.netty.channel.nio.NioEventLoop.processSelectedKey(NioEventLoop.java:688)
			at io.netty.channel.nio.NioEventLoop.processSelectedKeysOptimized(NioEventLoop.java:635)
			at io.netty.channel.nio.NioEventLoop.processSelectedKeys(NioEventLoop.java:552)
			at io.netty.channel.nio.NioEventLoop.run(NioEventLoop.java:514)
			... 4 common frames omitted
		Caused by: io.netty.channel.AbstractChannel$AnnotatedNoRouteToHostException: No route to host: no further information: /************:7002
		Caused by: java.net.NoRouteToHostException: No route to host: no further information
			at sun.nio.ch.SocketChannelImpl.checkConnect(Native Method)
			at sun.nio.ch.SocketChannelImpl.finishConnect(SocketChannelImpl.java:717)
			at io.netty.channel.socket.nio.NioSocketChannel.doFinishConnect(NioSocketChannel.java:330)
			at io.netty.channel.nio.AbstractNioChannel$AbstractNioUnsafe.finishConnect(AbstractNioChannel.java:334)
			at io.netty.channel.nio.NioEventLoop.processSelectedKey(NioEventLoop.java:688)
			at io.netty.channel.nio.NioEventLoop.processSelectedKeysOptimized(NioEventLoop.java:635)
			at io.netty.channel.nio.NioEventLoop.processSelectedKeys(NioEventLoop.java:552)
			at io.netty.channel.nio.NioEventLoop.run(NioEventLoop.java:514)
			at io.netty.util.concurrent.SingleThreadEventExecutor$6.run(SingleThreadEventExecutor.java:1050)
			at io.netty.util.internal.ThreadExecutorMap$2.run(ThreadExecutorMap.java:74)
			at io.netty.util.concurrent.FastThreadLocalRunnable.run(FastThreadLocalRunnable.java:30)
			at java.lang.Thread.run(Thread.java:748)
		Suppressed: io.lettuce.core.RedisConnectionException: Unable to connect to [************:7005]: No route to host: no further information: /************:7005
			at io.lettuce.core.cluster.topology.ClusterTopologyRefresh.lambda$getConnections$0(ClusterTopologyRefresh.java:290)
			at java.util.concurrent.CompletableFuture.uniWhenComplete(CompletableFuture.java:760)
			at java.util.concurrent.CompletableFuture$UniWhenComplete.tryFire(CompletableFuture.java:736)
			at java.util.concurrent.CompletableFuture.postComplete(CompletableFuture.java:474)
			at java.util.concurrent.CompletableFuture.completeExceptionally(CompletableFuture.java:1977)
			at io.lettuce.core.AbstractRedisClient.lambda$initializeChannelAsync0$4(AbstractRedisClient.java:329)
			at io.netty.util.concurrent.DefaultPromise.notifyListener0(DefaultPromise.java:577)
			at io.netty.util.concurrent.DefaultPromise.notifyListeners0(DefaultPromise.java:570)
			at io.netty.util.concurrent.DefaultPromise.notifyListenersNow(DefaultPromise.java:549)
			at io.netty.util.concurrent.DefaultPromise.notifyListeners(DefaultPromise.java:490)
			at io.netty.util.concurrent.DefaultPromise.setValue0(DefaultPromise.java:615)
			at io.netty.util.concurrent.DefaultPromise.setFailure0(DefaultPromise.java:608)
			at io.netty.util.concurrent.DefaultPromise.tryFailure(DefaultPromise.java:117)
			at io.netty.channel.nio.AbstractNioChannel$AbstractNioUnsafe.fulfillConnectPromise(AbstractNioChannel.java:321)
			at io.netty.channel.nio.AbstractNioChannel$AbstractNioUnsafe.finishConnect(AbstractNioChannel.java:337)
			at io.netty.channel.nio.NioEventLoop.processSelectedKey(NioEventLoop.java:688)
			at io.netty.channel.nio.NioEventLoop.processSelectedKeysOptimized(NioEventLoop.java:635)
			at io.netty.channel.nio.NioEventLoop.processSelectedKeys(NioEventLoop.java:552)
			at io.netty.channel.nio.NioEventLoop.run(NioEventLoop.java:514)
			... 4 common frames omitted
		Caused by: io.netty.channel.AbstractChannel$AnnotatedNoRouteToHostException: No route to host: no further information: /************:7005
		Caused by: java.net.NoRouteToHostException: No route to host: no further information
			at sun.nio.ch.SocketChannelImpl.checkConnect(Native Method)
			at sun.nio.ch.SocketChannelImpl.finishConnect(SocketChannelImpl.java:717)
			at io.netty.channel.socket.nio.NioSocketChannel.doFinishConnect(NioSocketChannel.java:330)
			at io.netty.channel.nio.AbstractNioChannel$AbstractNioUnsafe.finishConnect(AbstractNioChannel.java:334)
			at io.netty.channel.nio.NioEventLoop.processSelectedKey(NioEventLoop.java:688)
			at io.netty.channel.nio.NioEventLoop.processSelectedKeysOptimized(NioEventLoop.java:635)
			at io.netty.channel.nio.NioEventLoop.processSelectedKeys(NioEventLoop.java:552)
			at io.netty.channel.nio.NioEventLoop.run(NioEventLoop.java:514)
			at io.netty.util.concurrent.SingleThreadEventExecutor$6.run(SingleThreadEventExecutor.java:1050)
			at io.netty.util.internal.ThreadExecutorMap$2.run(ThreadExecutorMap.java:74)
			at io.netty.util.concurrent.FastThreadLocalRunnable.run(FastThreadLocalRunnable.java:30)
			at java.lang.Thread.run(Thread.java:748)
		Suppressed: io.lettuce.core.RedisConnectionException: Unable to connect to [************:7006]: No route to host: no further information: /************:7006
			at io.lettuce.core.cluster.topology.ClusterTopologyRefresh.lambda$getConnections$0(ClusterTopologyRefresh.java:290)
			at java.util.concurrent.CompletableFuture.uniWhenComplete(CompletableFuture.java:760)
			at java.util.concurrent.CompletableFuture$UniWhenComplete.tryFire(CompletableFuture.java:736)
			at java.util.concurrent.CompletableFuture.postComplete(CompletableFuture.java:474)
			at java.util.concurrent.CompletableFuture.completeExceptionally(CompletableFuture.java:1977)
			at io.lettuce.core.AbstractRedisClient.lambda$initializeChannelAsync0$4(AbstractRedisClient.java:329)
			at io.netty.util.concurrent.DefaultPromise.notifyListener0(DefaultPromise.java:577)
			at io.netty.util.concurrent.DefaultPromise.notifyListeners0(DefaultPromise.java:570)
			at io.netty.util.concurrent.DefaultPromise.notifyListenersNow(DefaultPromise.java:549)
			at io.netty.util.concurrent.DefaultPromise.notifyListeners(DefaultPromise.java:490)
			at io.netty.util.concurrent.DefaultPromise.setValue0(DefaultPromise.java:615)
			at io.netty.util.concurrent.DefaultPromise.setFailure0(DefaultPromise.java:608)
			at io.netty.util.concurrent.DefaultPromise.tryFailure(DefaultPromise.java:117)
			at io.netty.channel.nio.AbstractNioChannel$AbstractNioUnsafe.fulfillConnectPromise(AbstractNioChannel.java:321)
			at io.netty.channel.nio.AbstractNioChannel$AbstractNioUnsafe.finishConnect(AbstractNioChannel.java:337)
			at io.netty.channel.nio.NioEventLoop.processSelectedKey(NioEventLoop.java:688)
			at io.netty.channel.nio.NioEventLoop.processSelectedKeysOptimized(NioEventLoop.java:635)
			at io.netty.channel.nio.NioEventLoop.processSelectedKeys(NioEventLoop.java:552)
			at io.netty.channel.nio.NioEventLoop.run(NioEventLoop.java:514)
			... 4 common frames omitted
		Caused by: io.netty.channel.AbstractChannel$AnnotatedNoRouteToHostException: No route to host: no further information: /************:7006
		Caused by: java.net.NoRouteToHostException: No route to host: no further information
			at sun.nio.ch.SocketChannelImpl.checkConnect(Native Method)
			at sun.nio.ch.SocketChannelImpl.finishConnect(SocketChannelImpl.java:717)
			at io.netty.channel.socket.nio.NioSocketChannel.doFinishConnect(NioSocketChannel.java:330)
			at io.netty.channel.nio.AbstractNioChannel$AbstractNioUnsafe.finishConnect(AbstractNioChannel.java:334)
			at io.netty.channel.nio.NioEventLoop.processSelectedKey(NioEventLoop.java:688)
			at io.netty.channel.nio.NioEventLoop.processSelectedKeysOptimized(NioEventLoop.java:635)
			at io.netty.channel.nio.NioEventLoop.processSelectedKeys(NioEventLoop.java:552)
			at io.netty.channel.nio.NioEventLoop.run(NioEventLoop.java:514)
			at io.netty.util.concurrent.SingleThreadEventExecutor$6.run(SingleThreadEventExecutor.java:1050)
			at io.netty.util.internal.ThreadExecutorMap$2.run(ThreadExecutorMap.java:74)
			at io.netty.util.concurrent.FastThreadLocalRunnable.run(FastThreadLocalRunnable.java:30)
			at java.lang.Thread.run(Thread.java:748)
	Caused by: io.lettuce.core.RedisConnectionException: Unable to connect to [************:7004]: No route to host: no further information: /************:7004
		at io.lettuce.core.cluster.topology.ClusterTopologyRefresh.lambda$getConnections$0(ClusterTopologyRefresh.java:290)
		at java.util.concurrent.CompletableFuture.uniWhenComplete(CompletableFuture.java:760)
		at java.util.concurrent.CompletableFuture$UniWhenComplete.tryFire(CompletableFuture.java:736)
		at java.util.concurrent.CompletableFuture.postComplete(CompletableFuture.java:474)
		at java.util.concurrent.CompletableFuture.completeExceptionally(CompletableFuture.java:1977)
		at io.lettuce.core.AbstractRedisClient.lambda$initializeChannelAsync0$4(AbstractRedisClient.java:329)
		at io.netty.util.concurrent.DefaultPromise.notifyListener0(DefaultPromise.java:577)
		at io.netty.util.concurrent.DefaultPromise.notifyListeners0(DefaultPromise.java:570)
		at io.netty.util.concurrent.DefaultPromise.notifyListenersNow(DefaultPromise.java:549)
		at io.netty.util.concurrent.DefaultPromise.notifyListeners(DefaultPromise.java:490)
		at io.netty.util.concurrent.DefaultPromise.setValue0(DefaultPromise.java:615)
		at io.netty.util.concurrent.DefaultPromise.setFailure0(DefaultPromise.java:608)
		at io.netty.util.concurrent.DefaultPromise.tryFailure(DefaultPromise.java:117)
		at io.netty.channel.nio.AbstractNioChannel$AbstractNioUnsafe.fulfillConnectPromise(AbstractNioChannel.java:321)
		at io.netty.channel.nio.AbstractNioChannel$AbstractNioUnsafe.finishConnect(AbstractNioChannel.java:337)
		at io.netty.channel.nio.NioEventLoop.processSelectedKey(NioEventLoop.java:688)
		at io.netty.channel.nio.NioEventLoop.processSelectedKeysOptimized(NioEventLoop.java:635)
		at io.netty.channel.nio.NioEventLoop.processSelectedKeys(NioEventLoop.java:552)
		at io.netty.channel.nio.NioEventLoop.run(NioEventLoop.java:514)
		... 4 common frames omitted
	Caused by: io.netty.channel.AbstractChannel$AnnotatedNoRouteToHostException: No route to host: no further information: /************:7004
	Caused by: java.net.NoRouteToHostException: No route to host: no further information
		at sun.nio.ch.SocketChannelImpl.checkConnect(Native Method)
		at sun.nio.ch.SocketChannelImpl.finishConnect(SocketChannelImpl.java:717)
		at io.netty.channel.socket.nio.NioSocketChannel.doFinishConnect(NioSocketChannel.java:330)
		at io.netty.channel.nio.AbstractNioChannel$AbstractNioUnsafe.finishConnect(AbstractNioChannel.java:334)
		at io.netty.channel.nio.NioEventLoop.processSelectedKey(NioEventLoop.java:688)
		at io.netty.channel.nio.NioEventLoop.processSelectedKeysOptimized(NioEventLoop.java:635)
		at io.netty.channel.nio.NioEventLoop.processSelectedKeys(NioEventLoop.java:552)
		at io.netty.channel.nio.NioEventLoop.run(NioEventLoop.java:514)
		at io.netty.util.concurrent.SingleThreadEventExecutor$6.run(SingleThreadEventExecutor.java:1050)
		at io.netty.util.internal.ThreadExecutorMap$2.run(ThreadExecutorMap.java:74)
		at io.netty.util.concurrent.FastThreadLocalRunnable.run(FastThreadLocalRunnable.java:30)
		at java.lang.Thread.run(Thread.java:748)
Caused by: io.lettuce.core.RedisConnectionException: Unable to establish a connection to Redis Cluster at [RedisURI [host='************', port=7001], RedisURI [host='************', port=7002], RedisURI [host='************', port=7003], RedisURI [host='************', port=7004], RedisURI [host='************', port=7005], RedisURI [host='************', port=7006]]
	at io.lettuce.core.cluster.topology.AsyncConnections.get(AsyncConnections.java:89)
	at io.lettuce.core.cluster.topology.ClusterTopologyRefresh.loadViews(ClusterTopologyRefresh.java:76)
	at io.lettuce.core.cluster.RedisClusterClient.doLoadPartitions(RedisClusterClient.java:865)
	at io.lettuce.core.cluster.RedisClusterClient.loadPartitions(RedisClusterClient.java:845)
	... 10 common frames omitted
	Suppressed: io.lettuce.core.RedisConnectionException: Unable to connect to [************:7005]: No route to host: no further information: /************:7005
		at io.lettuce.core.cluster.topology.ClusterTopologyRefresh.lambda$getConnections$0(ClusterTopologyRefresh.java:290)
		at java.util.concurrent.CompletableFuture.uniWhenComplete(CompletableFuture.java:760)
		at java.util.concurrent.CompletableFuture$UniWhenComplete.tryFire(CompletableFuture.java:736)
		at java.util.concurrent.CompletableFuture.postComplete(CompletableFuture.java:474)
		at java.util.concurrent.CompletableFuture.completeExceptionally(CompletableFuture.java:1977)
		at io.lettuce.core.AbstractRedisClient.lambda$initializeChannelAsync0$4(AbstractRedisClient.java:329)
		at io.netty.util.concurrent.DefaultPromise.notifyListener0(DefaultPromise.java:577)
		at io.netty.util.concurrent.DefaultPromise.notifyListeners0(DefaultPromise.java:570)
		at io.netty.util.concurrent.DefaultPromise.notifyListenersNow(DefaultPromise.java:549)
		at io.netty.util.concurrent.DefaultPromise.notifyListeners(DefaultPromise.java:490)
		at io.netty.util.concurrent.DefaultPromise.setValue0(DefaultPromise.java:615)
		at io.netty.util.concurrent.DefaultPromise.setFailure0(DefaultPromise.java:608)
		at io.netty.util.concurrent.DefaultPromise.tryFailure(DefaultPromise.java:117)
		at io.netty.channel.nio.AbstractNioChannel$AbstractNioUnsafe.fulfillConnectPromise(AbstractNioChannel.java:321)
		at io.netty.channel.nio.AbstractNioChannel$AbstractNioUnsafe.finishConnect(AbstractNioChannel.java:337)
		at io.netty.channel.nio.NioEventLoop.processSelectedKey(NioEventLoop.java:688)
		at io.netty.channel.nio.NioEventLoop.processSelectedKeysOptimized(NioEventLoop.java:635)
		at io.netty.channel.nio.NioEventLoop.processSelectedKeys(NioEventLoop.java:552)
		at io.netty.channel.nio.NioEventLoop.run(NioEventLoop.java:514)
		... 4 common frames omitted
	Caused by: io.netty.channel.AbstractChannel$AnnotatedNoRouteToHostException: No route to host: no further information: /************:7005
	Caused by: java.net.NoRouteToHostException: No route to host: no further information
		at sun.nio.ch.SocketChannelImpl.checkConnect(Native Method)
		at sun.nio.ch.SocketChannelImpl.finishConnect(SocketChannelImpl.java:717)
		at io.netty.channel.socket.nio.NioSocketChannel.doFinishConnect(NioSocketChannel.java:330)
		at io.netty.channel.nio.AbstractNioChannel$AbstractNioUnsafe.finishConnect(AbstractNioChannel.java:334)
		at io.netty.channel.nio.NioEventLoop.processSelectedKey(NioEventLoop.java:688)
		at io.netty.channel.nio.NioEventLoop.processSelectedKeysOptimized(NioEventLoop.java:635)
		at io.netty.channel.nio.NioEventLoop.processSelectedKeys(NioEventLoop.java:552)
		at io.netty.channel.nio.NioEventLoop.run(NioEventLoop.java:514)
		at io.netty.util.concurrent.SingleThreadEventExecutor$6.run(SingleThreadEventExecutor.java:1050)
		at io.netty.util.internal.ThreadExecutorMap$2.run(ThreadExecutorMap.java:74)
		at io.netty.util.concurrent.FastThreadLocalRunnable.run(FastThreadLocalRunnable.java:30)
		at java.lang.Thread.run(Thread.java:748)
	Suppressed: io.lettuce.core.RedisConnectionException: Unable to connect to [************:7006]: No route to host: no further information: /************:7006
		at io.lettuce.core.cluster.topology.ClusterTopologyRefresh.lambda$getConnections$0(ClusterTopologyRefresh.java:290)
		at java.util.concurrent.CompletableFuture.uniWhenComplete(CompletableFuture.java:760)
		at java.util.concurrent.CompletableFuture$UniWhenComplete.tryFire(CompletableFuture.java:736)
		at java.util.concurrent.CompletableFuture.postComplete(CompletableFuture.java:474)
		at java.util.concurrent.CompletableFuture.completeExceptionally(CompletableFuture.java:1977)
		at io.lettuce.core.AbstractRedisClient.lambda$initializeChannelAsync0$4(AbstractRedisClient.java:329)
		at io.netty.util.concurrent.DefaultPromise.notifyListener0(DefaultPromise.java:577)
		at io.netty.util.concurrent.DefaultPromise.notifyListeners0(DefaultPromise.java:570)
		at io.netty.util.concurrent.DefaultPromise.notifyListenersNow(DefaultPromise.java:549)
		at io.netty.util.concurrent.DefaultPromise.notifyListeners(DefaultPromise.java:490)
		at io.netty.util.concurrent.DefaultPromise.setValue0(DefaultPromise.java:615)
		at io.netty.util.concurrent.DefaultPromise.setFailure0(DefaultPromise.java:608)
		at io.netty.util.concurrent.DefaultPromise.tryFailure(DefaultPromise.java:117)
		at io.netty.channel.nio.AbstractNioChannel$AbstractNioUnsafe.fulfillConnectPromise(AbstractNioChannel.java:321)
		at io.netty.channel.nio.AbstractNioChannel$AbstractNioUnsafe.finishConnect(AbstractNioChannel.java:337)
		at io.netty.channel.nio.NioEventLoop.processSelectedKey(NioEventLoop.java:688)
		at io.netty.channel.nio.NioEventLoop.processSelectedKeysOptimized(NioEventLoop.java:635)
		at io.netty.channel.nio.NioEventLoop.processSelectedKeys(NioEventLoop.java:552)
		at io.netty.channel.nio.NioEventLoop.run(NioEventLoop.java:514)
		... 4 common frames omitted
	Caused by: io.netty.channel.AbstractChannel$AnnotatedNoRouteToHostException: No route to host: no further information: /************:7006
	Caused by: java.net.NoRouteToHostException: No route to host: no further information
		at sun.nio.ch.SocketChannelImpl.checkConnect(Native Method)
		at sun.nio.ch.SocketChannelImpl.finishConnect(SocketChannelImpl.java:717)
		at io.netty.channel.socket.nio.NioSocketChannel.doFinishConnect(NioSocketChannel.java:330)
		at io.netty.channel.nio.AbstractNioChannel$AbstractNioUnsafe.finishConnect(AbstractNioChannel.java:334)
		at io.netty.channel.nio.NioEventLoop.processSelectedKey(NioEventLoop.java:688)
		at io.netty.channel.nio.NioEventLoop.processSelectedKeysOptimized(NioEventLoop.java:635)
		at io.netty.channel.nio.NioEventLoop.processSelectedKeys(NioEventLoop.java:552)
		at io.netty.channel.nio.NioEventLoop.run(NioEventLoop.java:514)
		at io.netty.util.concurrent.SingleThreadEventExecutor$6.run(SingleThreadEventExecutor.java:1050)
		at io.netty.util.internal.ThreadExecutorMap$2.run(ThreadExecutorMap.java:74)
		at io.netty.util.concurrent.FastThreadLocalRunnable.run(FastThreadLocalRunnable.java:30)
		at java.lang.Thread.run(Thread.java:748)
	Suppressed: io.lettuce.core.RedisConnectionException: Unable to connect to [************:7002]: No route to host: no further information: /************:7002
		at io.lettuce.core.cluster.topology.ClusterTopologyRefresh.lambda$getConnections$0(ClusterTopologyRefresh.java:290)
		at java.util.concurrent.CompletableFuture.uniWhenComplete(CompletableFuture.java:760)
		at java.util.concurrent.CompletableFuture$UniWhenComplete.tryFire(CompletableFuture.java:736)
		at java.util.concurrent.CompletableFuture.postComplete(CompletableFuture.java:474)
		at java.util.concurrent.CompletableFuture.completeExceptionally(CompletableFuture.java:1977)
		at io.lettuce.core.AbstractRedisClient.lambda$initializeChannelAsync0$4(AbstractRedisClient.java:329)
		at io.netty.util.concurrent.DefaultPromise.notifyListener0(DefaultPromise.java:577)
		at io.netty.util.concurrent.DefaultPromise.notifyListeners0(DefaultPromise.java:570)
		at io.netty.util.concurrent.DefaultPromise.notifyListenersNow(DefaultPromise.java:549)
		at io.netty.util.concurrent.DefaultPromise.notifyListeners(DefaultPromise.java:490)
		at io.netty.util.concurrent.DefaultPromise.setValue0(DefaultPromise.java:615)
		at io.netty.util.concurrent.DefaultPromise.setFailure0(DefaultPromise.java:608)
		at io.netty.util.concurrent.DefaultPromise.tryFailure(DefaultPromise.java:117)
		at io.netty.channel.nio.AbstractNioChannel$AbstractNioUnsafe.fulfillConnectPromise(AbstractNioChannel.java:321)
		at io.netty.channel.nio.AbstractNioChannel$AbstractNioUnsafe.finishConnect(AbstractNioChannel.java:337)
		at io.netty.channel.nio.NioEventLoop.processSelectedKey(NioEventLoop.java:688)
		at io.netty.channel.nio.NioEventLoop.processSelectedKeysOptimized(NioEventLoop.java:635)
		at io.netty.channel.nio.NioEventLoop.processSelectedKeys(NioEventLoop.java:552)
		at io.netty.channel.nio.NioEventLoop.run(NioEventLoop.java:514)
		... 4 common frames omitted
	Caused by: io.netty.channel.AbstractChannel$AnnotatedNoRouteToHostException: No route to host: no further information: /************:7002
	Caused by: java.net.NoRouteToHostException: No route to host: no further information
		at sun.nio.ch.SocketChannelImpl.checkConnect(Native Method)
		at sun.nio.ch.SocketChannelImpl.finishConnect(SocketChannelImpl.java:717)
		at io.netty.channel.socket.nio.NioSocketChannel.doFinishConnect(NioSocketChannel.java:330)
		at io.netty.channel.nio.AbstractNioChannel$AbstractNioUnsafe.finishConnect(AbstractNioChannel.java:334)
		at io.netty.channel.nio.NioEventLoop.processSelectedKey(NioEventLoop.java:688)
		at io.netty.channel.nio.NioEventLoop.processSelectedKeysOptimized(NioEventLoop.java:635)
		at io.netty.channel.nio.NioEventLoop.processSelectedKeys(NioEventLoop.java:552)
		at io.netty.channel.nio.NioEventLoop.run(NioEventLoop.java:514)
		at io.netty.util.concurrent.SingleThreadEventExecutor$6.run(SingleThreadEventExecutor.java:1050)
		at io.netty.util.internal.ThreadExecutorMap$2.run(ThreadExecutorMap.java:74)
		at io.netty.util.concurrent.FastThreadLocalRunnable.run(FastThreadLocalRunnable.java:30)
		at java.lang.Thread.run(Thread.java:748)
	Suppressed: io.lettuce.core.RedisConnectionException: Unable to connect to [************:7003]: No route to host: no further information: /************:7003
		at io.lettuce.core.cluster.topology.ClusterTopologyRefresh.lambda$getConnections$0(ClusterTopologyRefresh.java:290)
		at java.util.concurrent.CompletableFuture.uniWhenComplete(CompletableFuture.java:760)
		at java.util.concurrent.CompletableFuture$UniWhenComplete.tryFire(CompletableFuture.java:736)
		at java.util.concurrent.CompletableFuture.postComplete(CompletableFuture.java:474)
		at java.util.concurrent.CompletableFuture.completeExceptionally(CompletableFuture.java:1977)
		at io.lettuce.core.AbstractRedisClient.lambda$initializeChannelAsync0$4(AbstractRedisClient.java:329)
		at io.netty.util.concurrent.DefaultPromise.notifyListener0(DefaultPromise.java:577)
		at io.netty.util.concurrent.DefaultPromise.notifyListeners0(DefaultPromise.java:570)
		at io.netty.util.concurrent.DefaultPromise.notifyListenersNow(DefaultPromise.java:549)
		at io.netty.util.concurrent.DefaultPromise.notifyListeners(DefaultPromise.java:490)
		at io.netty.util.concurrent.DefaultPromise.setValue0(DefaultPromise.java:615)
		at io.netty.util.concurrent.DefaultPromise.setFailure0(DefaultPromise.java:608)
		at io.netty.util.concurrent.DefaultPromise.tryFailure(DefaultPromise.java:117)
		at io.netty.channel.nio.AbstractNioChannel$AbstractNioUnsafe.fulfillConnectPromise(AbstractNioChannel.java:321)
		at io.netty.channel.nio.AbstractNioChannel$AbstractNioUnsafe.finishConnect(AbstractNioChannel.java:337)
		at io.netty.channel.nio.NioEventLoop.processSelectedKey(NioEventLoop.java:688)
		at io.netty.channel.nio.NioEventLoop.processSelectedKeysOptimized(NioEventLoop.java:635)
		at io.netty.channel.nio.NioEventLoop.processSelectedKeys(NioEventLoop.java:552)
		at io.netty.channel.nio.NioEventLoop.run(NioEventLoop.java:514)
		... 4 common frames omitted
	Caused by: io.netty.channel.AbstractChannel$AnnotatedNoRouteToHostException: No route to host: no further information: /************:7003
	Caused by: java.net.NoRouteToHostException: No route to host: no further information
		at sun.nio.ch.SocketChannelImpl.checkConnect(Native Method)
		at sun.nio.ch.SocketChannelImpl.finishConnect(SocketChannelImpl.java:717)
		at io.netty.channel.socket.nio.NioSocketChannel.doFinishConnect(NioSocketChannel.java:330)
		at io.netty.channel.nio.AbstractNioChannel$AbstractNioUnsafe.finishConnect(AbstractNioChannel.java:334)
		at io.netty.channel.nio.NioEventLoop.processSelectedKey(NioEventLoop.java:688)
		at io.netty.channel.nio.NioEventLoop.processSelectedKeysOptimized(NioEventLoop.java:635)
		at io.netty.channel.nio.NioEventLoop.processSelectedKeys(NioEventLoop.java:552)
		at io.netty.channel.nio.NioEventLoop.run(NioEventLoop.java:514)
		at io.netty.util.concurrent.SingleThreadEventExecutor$6.run(SingleThreadEventExecutor.java:1050)
		at io.netty.util.internal.ThreadExecutorMap$2.run(ThreadExecutorMap.java:74)
		at io.netty.util.concurrent.FastThreadLocalRunnable.run(FastThreadLocalRunnable.java:30)
		at java.lang.Thread.run(Thread.java:748)
	Suppressed: io.lettuce.core.RedisConnectionException: Unable to connect to [************:7001]: No route to host: no further information: /************:7001
		at io.lettuce.core.cluster.topology.ClusterTopologyRefresh.lambda$getConnections$0(ClusterTopologyRefresh.java:290)
		at java.util.concurrent.CompletableFuture.uniWhenComplete(CompletableFuture.java:760)
		at java.util.concurrent.CompletableFuture$UniWhenComplete.tryFire(CompletableFuture.java:736)
		at java.util.concurrent.CompletableFuture.postComplete(CompletableFuture.java:474)
		at java.util.concurrent.CompletableFuture.completeExceptionally(CompletableFuture.java:1977)
		at io.lettuce.core.AbstractRedisClient.lambda$initializeChannelAsync0$4(AbstractRedisClient.java:329)
		at io.netty.util.concurrent.DefaultPromise.notifyListener0(DefaultPromise.java:577)
		at io.netty.util.concurrent.DefaultPromise.notifyListeners0(DefaultPromise.java:570)
		at io.netty.util.concurrent.DefaultPromise.notifyListenersNow(DefaultPromise.java:549)
		at io.netty.util.concurrent.DefaultPromise.notifyListeners(DefaultPromise.java:490)
		at io.netty.util.concurrent.DefaultPromise.setValue0(DefaultPromise.java:615)
		at io.netty.util.concurrent.DefaultPromise.setFailure0(DefaultPromise.java:608)
		at io.netty.util.concurrent.DefaultPromise.tryFailure(DefaultPromise.java:117)
		at io.netty.channel.nio.AbstractNioChannel$AbstractNioUnsafe.fulfillConnectPromise(AbstractNioChannel.java:321)
		at io.netty.channel.nio.AbstractNioChannel$AbstractNioUnsafe.finishConnect(AbstractNioChannel.java:337)
		at io.netty.channel.nio.NioEventLoop.processSelectedKey(NioEventLoop.java:688)
		at io.netty.channel.nio.NioEventLoop.processSelectedKeysOptimized(NioEventLoop.java:635)
		at io.netty.channel.nio.NioEventLoop.processSelectedKeys(NioEventLoop.java:552)
		at io.netty.channel.nio.NioEventLoop.run(NioEventLoop.java:514)
		... 4 common frames omitted
	Caused by: io.netty.channel.AbstractChannel$AnnotatedNoRouteToHostException: No route to host: no further information: /************:7001
	Caused by: java.net.NoRouteToHostException: No route to host: no further information
		at sun.nio.ch.SocketChannelImpl.checkConnect(Native Method)
		at sun.nio.ch.SocketChannelImpl.finishConnect(SocketChannelImpl.java:717)
		at io.netty.channel.socket.nio.NioSocketChannel.doFinishConnect(NioSocketChannel.java:330)
		at io.netty.channel.nio.AbstractNioChannel$AbstractNioUnsafe.finishConnect(AbstractNioChannel.java:334)
		at io.netty.channel.nio.NioEventLoop.processSelectedKey(NioEventLoop.java:688)
		at io.netty.channel.nio.NioEventLoop.processSelectedKeysOptimized(NioEventLoop.java:635)
		at io.netty.channel.nio.NioEventLoop.processSelectedKeys(NioEventLoop.java:552)
		at io.netty.channel.nio.NioEventLoop.run(NioEventLoop.java:514)
		at io.netty.util.concurrent.SingleThreadEventExecutor$6.run(SingleThreadEventExecutor.java:1050)
		at io.netty.util.internal.ThreadExecutorMap$2.run(ThreadExecutorMap.java:74)
		at io.netty.util.concurrent.FastThreadLocalRunnable.run(FastThreadLocalRunnable.java:30)
		at java.lang.Thread.run(Thread.java:748)
Caused by: io.lettuce.core.RedisConnectionException: Unable to connect to [************:7004]: No route to host: no further information: /************:7004
	at io.lettuce.core.cluster.topology.ClusterTopologyRefresh.lambda$getConnections$0(ClusterTopologyRefresh.java:290)
	at java.util.concurrent.CompletableFuture.uniWhenComplete(CompletableFuture.java:760)
	at java.util.concurrent.CompletableFuture$UniWhenComplete.tryFire(CompletableFuture.java:736)
	at java.util.concurrent.CompletableFuture.postComplete(CompletableFuture.java:474)
	at java.util.concurrent.CompletableFuture.completeExceptionally(CompletableFuture.java:1977)
	at io.lettuce.core.AbstractRedisClient.lambda$initializeChannelAsync0$4(AbstractRedisClient.java:329)
	at io.netty.util.concurrent.DefaultPromise.notifyListener0(DefaultPromise.java:577)
	at io.netty.util.concurrent.DefaultPromise.notifyListeners0(DefaultPromise.java:570)
	at io.netty.util.concurrent.DefaultPromise.notifyListenersNow(DefaultPromise.java:549)
	at io.netty.util.concurrent.DefaultPromise.notifyListeners(DefaultPromise.java:490)
	at io.netty.util.concurrent.DefaultPromise.setValue0(DefaultPromise.java:615)
	at io.netty.util.concurrent.DefaultPromise.setFailure0(DefaultPromise.java:608)
	at io.netty.util.concurrent.DefaultPromise.tryFailure(DefaultPromise.java:117)
	at io.netty.channel.nio.AbstractNioChannel$AbstractNioUnsafe.fulfillConnectPromise(AbstractNioChannel.java:321)
	at io.netty.channel.nio.AbstractNioChannel$AbstractNioUnsafe.finishConnect(AbstractNioChannel.java:337)
	at io.netty.channel.nio.NioEventLoop.processSelectedKey(NioEventLoop.java:688)
	at io.netty.channel.nio.NioEventLoop.processSelectedKeysOptimized(NioEventLoop.java:635)
	at io.netty.channel.nio.NioEventLoop.processSelectedKeys(NioEventLoop.java:552)
	at io.netty.channel.nio.NioEventLoop.run(NioEventLoop.java:514)
	... 4 common frames omitted
Caused by: io.netty.channel.AbstractChannel$AnnotatedNoRouteToHostException: No route to host: no further information: /************:7004
Caused by: java.net.NoRouteToHostException: No route to host: no further information
	at sun.nio.ch.SocketChannelImpl.checkConnect(Native Method)
	at sun.nio.ch.SocketChannelImpl.finishConnect(SocketChannelImpl.java:717)
	at io.netty.channel.socket.nio.NioSocketChannel.doFinishConnect(NioSocketChannel.java:330)
	at io.netty.channel.nio.AbstractNioChannel$AbstractNioUnsafe.finishConnect(AbstractNioChannel.java:334)
	at io.netty.channel.nio.NioEventLoop.processSelectedKey(NioEventLoop.java:688)
	at io.netty.channel.nio.NioEventLoop.processSelectedKeysOptimized(NioEventLoop.java:635)
	at io.netty.channel.nio.NioEventLoop.processSelectedKeys(NioEventLoop.java:552)
	at io.netty.channel.nio.NioEventLoop.run(NioEventLoop.java:514)
	at io.netty.util.concurrent.SingleThreadEventExecutor$6.run(SingleThreadEventExecutor.java:1050)
	at io.netty.util.internal.ThreadExecutorMap$2.run(ThreadExecutorMap.java:74)
	at io.netty.util.concurrent.FastThreadLocalRunnable.run(FastThreadLocalRunnable.java:30)
	at java.lang.Thread.run(Thread.java:748)
2025-06-01 11:21:55.300 [lettuce-nioEventLoop-4-11] WARN  io.lettuce.core.cluster.topology.ClusterTopologyRefresh.lambda$getConnections$0 Line:284 - Unable to connect to [************:7001]: No route to host: no further information: /************:7001
2025-06-01 11:21:55.300 [lettuce-nioEventLoop-4-12] WARN  io.lettuce.core.cluster.topology.ClusterTopologyRefresh.lambda$getConnections$0 Line:284 - Unable to connect to [************:7002]: No route to host: no further information: /************:7002
2025-06-01 11:21:55.301 [lettuce-nioEventLoop-4-1] WARN  io.lettuce.core.cluster.topology.ClusterTopologyRefresh.lambda$getConnections$0 Line:284 - Unable to connect to [************:7003]: No route to host: no further information: /************:7003
2025-06-01 11:21:55.301 [lettuce-nioEventLoop-4-2] WARN  io.lettuce.core.cluster.topology.ClusterTopologyRefresh.lambda$getConnections$0 Line:284 - Unable to connect to [************:7004]: No route to host: no further information: /************:7004
2025-06-01 11:21:55.301 [lettuce-nioEventLoop-4-3] WARN  io.lettuce.core.cluster.topology.ClusterTopologyRefresh.lambda$getConnections$0 Line:284 - Unable to connect to [************:7005]: No route to host: no further information: /************:7005
2025-06-01 11:21:55.301 [lettuce-nioEventLoop-4-4] WARN  io.lettuce.core.cluster.topology.ClusterTopologyRefresh.lambda$getConnections$0 Line:284 - Unable to connect to [************:7006]: No route to host: no further information: /************:7006
2025-06-01 11:21:55.302 [lettuce-nioEventLoop-4-5] WARN  io.lettuce.core.cluster.topology.ClusterTopologyRefresh.lambda$getConnections$0 Line:284 - Unable to connect to [************:7006]: No route to host: no further information: /************:7006
2025-06-01 11:21:55.302 [lettuce-nioEventLoop-4-6] WARN  io.lettuce.core.cluster.topology.ClusterTopologyRefresh.lambda$getConnections$0 Line:284 - Unable to connect to [************:7004]: No route to host: no further information: /************:7004
2025-06-01 11:21:55.302 [lettuce-nioEventLoop-4-7] WARN  io.lettuce.core.cluster.topology.ClusterTopologyRefresh.lambda$getConnections$0 Line:284 - Unable to connect to [************:7005]: No route to host: no further information: /************:7005
2025-06-01 11:21:55.302 [lettuce-nioEventLoop-4-8] WARN  io.lettuce.core.cluster.topology.ClusterTopologyRefresh.lambda$getConnections$0 Line:284 - Unable to connect to [************:7002]: No route to host: no further information: /************:7002
2025-06-01 11:21:55.302 [lettuce-nioEventLoop-4-9] WARN  io.lettuce.core.cluster.topology.ClusterTopologyRefresh.lambda$getConnections$0 Line:284 - Unable to connect to [************:7003]: No route to host: no further information: /************:7003
2025-06-01 11:21:55.302 [lettuce-nioEventLoop-4-10] WARN  io.lettuce.core.cluster.topology.ClusterTopologyRefresh.lambda$getConnections$0 Line:284 - Unable to connect to [************:7001]: No route to host: no further information: /************:7001
2025-06-01 11:21:55.305 [lettuce-eventExecutorLoop-1-8] WARN  io.lettuce.core.cluster.ClusterTopologyRefreshScheduler.doRun Line:258 - Cannot refresh Redis Cluster topology
io.lettuce.core.RedisException: Cannot retrieve initial cluster partitions from initial URIs [RedisURI [host='************', port=7006], RedisURI [host='************', port=7004], RedisURI [host='************', port=7005], RedisURI [host='************', port=7002], RedisURI [host='************', port=7003], RedisURI [host='************', port=7001]]
	at io.lettuce.core.cluster.RedisClusterClient.loadPartitions(RedisClusterClient.java:848)
	at io.lettuce.core.cluster.RedisClusterClient.reloadPartitions(RedisClusterClient.java:784)
	at io.lettuce.core.cluster.ClusterTopologyRefreshScheduler$ClusterTopologyRefreshTask.doRun(ClusterTopologyRefreshScheduler.java:256)
	at io.lettuce.core.cluster.ClusterTopologyRefreshScheduler$ClusterTopologyRefreshTask.run(ClusterTopologyRefreshScheduler.java:236)
	at io.netty.util.concurrent.PromiseTask.runTask(PromiseTask.java:98)
	at io.netty.util.concurrent.PromiseTask.run(PromiseTask.java:106)
	at io.netty.util.concurrent.DefaultEventExecutor.run(DefaultEventExecutor.java:66)
	at io.netty.util.concurrent.SingleThreadEventExecutor$6.run(SingleThreadEventExecutor.java:1050)
	at io.netty.util.internal.ThreadExecutorMap$2.run(ThreadExecutorMap.java:74)
	at io.netty.util.concurrent.FastThreadLocalRunnable.run(FastThreadLocalRunnable.java:30)
	at java.lang.Thread.run(Thread.java:748)
	Suppressed: io.lettuce.core.RedisConnectionException: Unable to establish a connection to Redis Cluster at [RedisURI [host='************', port=7001], RedisURI [host='************', port=7002], RedisURI [host='************', port=7003], RedisURI [host='************', port=7004], RedisURI [host='************', port=7005], RedisURI [host='************', port=7006]]
		at io.lettuce.core.cluster.topology.AsyncConnections.get(AsyncConnections.java:89)
		at io.lettuce.core.cluster.topology.ClusterTopologyRefresh.loadViews(ClusterTopologyRefresh.java:76)
		at io.lettuce.core.cluster.RedisClusterClient.doLoadPartitions(RedisClusterClient.java:865)
		at io.lettuce.core.cluster.RedisClusterClient.loadPartitions(RedisClusterClient.java:838)
		... 10 common frames omitted
		Suppressed: io.lettuce.core.RedisConnectionException: Unable to connect to [************:7002]: No route to host: no further information: /************:7002
			at io.lettuce.core.cluster.topology.ClusterTopologyRefresh.lambda$getConnections$0(ClusterTopologyRefresh.java:290)
			at java.util.concurrent.CompletableFuture.uniWhenComplete(CompletableFuture.java:760)
			at java.util.concurrent.CompletableFuture$UniWhenComplete.tryFire(CompletableFuture.java:736)
			at java.util.concurrent.CompletableFuture.postComplete(CompletableFuture.java:474)
			at java.util.concurrent.CompletableFuture.completeExceptionally(CompletableFuture.java:1977)
			at io.lettuce.core.AbstractRedisClient.lambda$initializeChannelAsync0$4(AbstractRedisClient.java:329)
			at io.netty.util.concurrent.DefaultPromise.notifyListener0(DefaultPromise.java:577)
			at io.netty.util.concurrent.DefaultPromise.notifyListeners0(DefaultPromise.java:570)
			at io.netty.util.concurrent.DefaultPromise.notifyListenersNow(DefaultPromise.java:549)
			at io.netty.util.concurrent.DefaultPromise.notifyListeners(DefaultPromise.java:490)
			at io.netty.util.concurrent.DefaultPromise.setValue0(DefaultPromise.java:615)
			at io.netty.util.concurrent.DefaultPromise.setFailure0(DefaultPromise.java:608)
			at io.netty.util.concurrent.DefaultPromise.tryFailure(DefaultPromise.java:117)
			at io.netty.channel.nio.AbstractNioChannel$AbstractNioUnsafe.fulfillConnectPromise(AbstractNioChannel.java:321)
			at io.netty.channel.nio.AbstractNioChannel$AbstractNioUnsafe.finishConnect(AbstractNioChannel.java:337)
			at io.netty.channel.nio.NioEventLoop.processSelectedKey(NioEventLoop.java:688)
			at io.netty.channel.nio.NioEventLoop.processSelectedKeysOptimized(NioEventLoop.java:635)
			at io.netty.channel.nio.NioEventLoop.processSelectedKeys(NioEventLoop.java:552)
			at io.netty.channel.nio.NioEventLoop.run(NioEventLoop.java:514)
			... 4 common frames omitted
		Caused by: io.netty.channel.AbstractChannel$AnnotatedNoRouteToHostException: No route to host: no further information: /************:7002
		Caused by: java.net.NoRouteToHostException: No route to host: no further information
			at sun.nio.ch.SocketChannelImpl.checkConnect(Native Method)
			at sun.nio.ch.SocketChannelImpl.finishConnect(SocketChannelImpl.java:717)
			at io.netty.channel.socket.nio.NioSocketChannel.doFinishConnect(NioSocketChannel.java:330)
			at io.netty.channel.nio.AbstractNioChannel$AbstractNioUnsafe.finishConnect(AbstractNioChannel.java:334)
			at io.netty.channel.nio.NioEventLoop.processSelectedKey(NioEventLoop.java:688)
			at io.netty.channel.nio.NioEventLoop.processSelectedKeysOptimized(NioEventLoop.java:635)
			at io.netty.channel.nio.NioEventLoop.processSelectedKeys(NioEventLoop.java:552)
			at io.netty.channel.nio.NioEventLoop.run(NioEventLoop.java:514)
			at io.netty.util.concurrent.SingleThreadEventExecutor$6.run(SingleThreadEventExecutor.java:1050)
			at io.netty.util.internal.ThreadExecutorMap$2.run(ThreadExecutorMap.java:74)
			at io.netty.util.concurrent.FastThreadLocalRunnable.run(FastThreadLocalRunnable.java:30)
			at java.lang.Thread.run(Thread.java:748)
		Suppressed: io.lettuce.core.RedisConnectionException: Unable to connect to [************:7003]: No route to host: no further information: /************:7003
			at io.lettuce.core.cluster.topology.ClusterTopologyRefresh.lambda$getConnections$0(ClusterTopologyRefresh.java:290)
			at java.util.concurrent.CompletableFuture.uniWhenComplete(CompletableFuture.java:760)
			at java.util.concurrent.CompletableFuture$UniWhenComplete.tryFire(CompletableFuture.java:736)
			at java.util.concurrent.CompletableFuture.postComplete(CompletableFuture.java:474)
			at java.util.concurrent.CompletableFuture.completeExceptionally(CompletableFuture.java:1977)
			at io.lettuce.core.AbstractRedisClient.lambda$initializeChannelAsync0$4(AbstractRedisClient.java:329)
			at io.netty.util.concurrent.DefaultPromise.notifyListener0(DefaultPromise.java:577)
			at io.netty.util.concurrent.DefaultPromise.notifyListeners0(DefaultPromise.java:570)
			at io.netty.util.concurrent.DefaultPromise.notifyListenersNow(DefaultPromise.java:549)
			at io.netty.util.concurrent.DefaultPromise.notifyListeners(DefaultPromise.java:490)
			at io.netty.util.concurrent.DefaultPromise.setValue0(DefaultPromise.java:615)
			at io.netty.util.concurrent.DefaultPromise.setFailure0(DefaultPromise.java:608)
			at io.netty.util.concurrent.DefaultPromise.tryFailure(DefaultPromise.java:117)
			at io.netty.channel.nio.AbstractNioChannel$AbstractNioUnsafe.fulfillConnectPromise(AbstractNioChannel.java:321)
			at io.netty.channel.nio.AbstractNioChannel$AbstractNioUnsafe.finishConnect(AbstractNioChannel.java:337)
			at io.netty.channel.nio.NioEventLoop.processSelectedKey(NioEventLoop.java:688)
			at io.netty.channel.nio.NioEventLoop.processSelectedKeysOptimized(NioEventLoop.java:635)
			at io.netty.channel.nio.NioEventLoop.processSelectedKeys(NioEventLoop.java:552)
			at io.netty.channel.nio.NioEventLoop.run(NioEventLoop.java:514)
			... 4 common frames omitted
		Caused by: io.netty.channel.AbstractChannel$AnnotatedNoRouteToHostException: No route to host: no further information: /************:7003
		Caused by: java.net.NoRouteToHostException: No route to host: no further information
			at sun.nio.ch.SocketChannelImpl.checkConnect(Native Method)
			at sun.nio.ch.SocketChannelImpl.finishConnect(SocketChannelImpl.java:717)
			at io.netty.channel.socket.nio.NioSocketChannel.doFinishConnect(NioSocketChannel.java:330)
			at io.netty.channel.nio.AbstractNioChannel$AbstractNioUnsafe.finishConnect(AbstractNioChannel.java:334)
			at io.netty.channel.nio.NioEventLoop.processSelectedKey(NioEventLoop.java:688)
			at io.netty.channel.nio.NioEventLoop.processSelectedKeysOptimized(NioEventLoop.java:635)
			at io.netty.channel.nio.NioEventLoop.processSelectedKeys(NioEventLoop.java:552)
			at io.netty.channel.nio.NioEventLoop.run(NioEventLoop.java:514)
			at io.netty.util.concurrent.SingleThreadEventExecutor$6.run(SingleThreadEventExecutor.java:1050)
			at io.netty.util.internal.ThreadExecutorMap$2.run(ThreadExecutorMap.java:74)
			at io.netty.util.concurrent.FastThreadLocalRunnable.run(FastThreadLocalRunnable.java:30)
			at java.lang.Thread.run(Thread.java:748)
		Suppressed: io.lettuce.core.RedisConnectionException: Unable to connect to [************:7004]: No route to host: no further information: /************:7004
			at io.lettuce.core.cluster.topology.ClusterTopologyRefresh.lambda$getConnections$0(ClusterTopologyRefresh.java:290)
			at java.util.concurrent.CompletableFuture.uniWhenComplete(CompletableFuture.java:760)
			at java.util.concurrent.CompletableFuture$UniWhenComplete.tryFire(CompletableFuture.java:736)
			at java.util.concurrent.CompletableFuture.postComplete(CompletableFuture.java:474)
			at java.util.concurrent.CompletableFuture.completeExceptionally(CompletableFuture.java:1977)
			at io.lettuce.core.AbstractRedisClient.lambda$initializeChannelAsync0$4(AbstractRedisClient.java:329)
			at io.netty.util.concurrent.DefaultPromise.notifyListener0(DefaultPromise.java:577)
			at io.netty.util.concurrent.DefaultPromise.notifyListeners0(DefaultPromise.java:570)
			at io.netty.util.concurrent.DefaultPromise.notifyListenersNow(DefaultPromise.java:549)
			at io.netty.util.concurrent.DefaultPromise.notifyListeners(DefaultPromise.java:490)
			at io.netty.util.concurrent.DefaultPromise.setValue0(DefaultPromise.java:615)
			at io.netty.util.concurrent.DefaultPromise.setFailure0(DefaultPromise.java:608)
			at io.netty.util.concurrent.DefaultPromise.tryFailure(DefaultPromise.java:117)
			at io.netty.channel.nio.AbstractNioChannel$AbstractNioUnsafe.fulfillConnectPromise(AbstractNioChannel.java:321)
			at io.netty.channel.nio.AbstractNioChannel$AbstractNioUnsafe.finishConnect(AbstractNioChannel.java:337)
			at io.netty.channel.nio.NioEventLoop.processSelectedKey(NioEventLoop.java:688)
			at io.netty.channel.nio.NioEventLoop.processSelectedKeysOptimized(NioEventLoop.java:635)
			at io.netty.channel.nio.NioEventLoop.processSelectedKeys(NioEventLoop.java:552)
			at io.netty.channel.nio.NioEventLoop.run(NioEventLoop.java:514)
			... 4 common frames omitted
		Caused by: io.netty.channel.AbstractChannel$AnnotatedNoRouteToHostException: No route to host: no further information: /************:7004
		Caused by: java.net.NoRouteToHostException: No route to host: no further information
			at sun.nio.ch.SocketChannelImpl.checkConnect(Native Method)
			at sun.nio.ch.SocketChannelImpl.finishConnect(SocketChannelImpl.java:717)
			at io.netty.channel.socket.nio.NioSocketChannel.doFinishConnect(NioSocketChannel.java:330)
			at io.netty.channel.nio.AbstractNioChannel$AbstractNioUnsafe.finishConnect(AbstractNioChannel.java:334)
			at io.netty.channel.nio.NioEventLoop.processSelectedKey(NioEventLoop.java:688)
			at io.netty.channel.nio.NioEventLoop.processSelectedKeysOptimized(NioEventLoop.java:635)
			at io.netty.channel.nio.NioEventLoop.processSelectedKeys(NioEventLoop.java:552)
			at io.netty.channel.nio.NioEventLoop.run(NioEventLoop.java:514)
			at io.netty.util.concurrent.SingleThreadEventExecutor$6.run(SingleThreadEventExecutor.java:1050)
			at io.netty.util.internal.ThreadExecutorMap$2.run(ThreadExecutorMap.java:74)
			at io.netty.util.concurrent.FastThreadLocalRunnable.run(FastThreadLocalRunnable.java:30)
			at java.lang.Thread.run(Thread.java:748)
		Suppressed: io.lettuce.core.RedisConnectionException: Unable to connect to [************:7005]: No route to host: no further information: /************:7005
			at io.lettuce.core.cluster.topology.ClusterTopologyRefresh.lambda$getConnections$0(ClusterTopologyRefresh.java:290)
			at java.util.concurrent.CompletableFuture.uniWhenComplete(CompletableFuture.java:760)
			at java.util.concurrent.CompletableFuture$UniWhenComplete.tryFire(CompletableFuture.java:736)
			at java.util.concurrent.CompletableFuture.postComplete(CompletableFuture.java:474)
			at java.util.concurrent.CompletableFuture.completeExceptionally(CompletableFuture.java:1977)
			at io.lettuce.core.AbstractRedisClient.lambda$initializeChannelAsync0$4(AbstractRedisClient.java:329)
			at io.netty.util.concurrent.DefaultPromise.notifyListener0(DefaultPromise.java:577)
			at io.netty.util.concurrent.DefaultPromise.notifyListeners0(DefaultPromise.java:570)
			at io.netty.util.concurrent.DefaultPromise.notifyListenersNow(DefaultPromise.java:549)
			at io.netty.util.concurrent.DefaultPromise.notifyListeners(DefaultPromise.java:490)
			at io.netty.util.concurrent.DefaultPromise.setValue0(DefaultPromise.java:615)
			at io.netty.util.concurrent.DefaultPromise.setFailure0(DefaultPromise.java:608)
			at io.netty.util.concurrent.DefaultPromise.tryFailure(DefaultPromise.java:117)
			at io.netty.channel.nio.AbstractNioChannel$AbstractNioUnsafe.fulfillConnectPromise(AbstractNioChannel.java:321)
			at io.netty.channel.nio.AbstractNioChannel$AbstractNioUnsafe.finishConnect(AbstractNioChannel.java:337)
			at io.netty.channel.nio.NioEventLoop.processSelectedKey(NioEventLoop.java:688)
			at io.netty.channel.nio.NioEventLoop.processSelectedKeysOptimized(NioEventLoop.java:635)
			at io.netty.channel.nio.NioEventLoop.processSelectedKeys(NioEventLoop.java:552)
			at io.netty.channel.nio.NioEventLoop.run(NioEventLoop.java:514)
			... 4 common frames omitted
		Caused by: io.netty.channel.AbstractChannel$AnnotatedNoRouteToHostException: No route to host: no further information: /************:7005
		Caused by: java.net.NoRouteToHostException: No route to host: no further information
			at sun.nio.ch.SocketChannelImpl.checkConnect(Native Method)
			at sun.nio.ch.SocketChannelImpl.finishConnect(SocketChannelImpl.java:717)
			at io.netty.channel.socket.nio.NioSocketChannel.doFinishConnect(NioSocketChannel.java:330)
			at io.netty.channel.nio.AbstractNioChannel$AbstractNioUnsafe.finishConnect(AbstractNioChannel.java:334)
			at io.netty.channel.nio.NioEventLoop.processSelectedKey(NioEventLoop.java:688)
			at io.netty.channel.nio.NioEventLoop.processSelectedKeysOptimized(NioEventLoop.java:635)
			at io.netty.channel.nio.NioEventLoop.processSelectedKeys(NioEventLoop.java:552)
			at io.netty.channel.nio.NioEventLoop.run(NioEventLoop.java:514)
			at io.netty.util.concurrent.SingleThreadEventExecutor$6.run(SingleThreadEventExecutor.java:1050)
			at io.netty.util.internal.ThreadExecutorMap$2.run(ThreadExecutorMap.java:74)
			at io.netty.util.concurrent.FastThreadLocalRunnable.run(FastThreadLocalRunnable.java:30)
			at java.lang.Thread.run(Thread.java:748)
		Suppressed: io.lettuce.core.RedisConnectionException: Unable to connect to [************:7006]: No route to host: no further information: /************:7006
			at io.lettuce.core.cluster.topology.ClusterTopologyRefresh.lambda$getConnections$0(ClusterTopologyRefresh.java:290)
			at java.util.concurrent.CompletableFuture.uniWhenComplete(CompletableFuture.java:760)
			at java.util.concurrent.CompletableFuture$UniWhenComplete.tryFire(CompletableFuture.java:736)
			at java.util.concurrent.CompletableFuture.postComplete(CompletableFuture.java:474)
			at java.util.concurrent.CompletableFuture.completeExceptionally(CompletableFuture.java:1977)
			at io.lettuce.core.AbstractRedisClient.lambda$initializeChannelAsync0$4(AbstractRedisClient.java:329)
			at io.netty.util.concurrent.DefaultPromise.notifyListener0(DefaultPromise.java:577)
			at io.netty.util.concurrent.DefaultPromise.notifyListeners0(DefaultPromise.java:570)
			at io.netty.util.concurrent.DefaultPromise.notifyListenersNow(DefaultPromise.java:549)
			at io.netty.util.concurrent.DefaultPromise.notifyListeners(DefaultPromise.java:490)
			at io.netty.util.concurrent.DefaultPromise.setValue0(DefaultPromise.java:615)
			at io.netty.util.concurrent.DefaultPromise.setFailure0(DefaultPromise.java:608)
			at io.netty.util.concurrent.DefaultPromise.tryFailure(DefaultPromise.java:117)
			at io.netty.channel.nio.AbstractNioChannel$AbstractNioUnsafe.fulfillConnectPromise(AbstractNioChannel.java:321)
			at io.netty.channel.nio.AbstractNioChannel$AbstractNioUnsafe.finishConnect(AbstractNioChannel.java:337)
			at io.netty.channel.nio.NioEventLoop.processSelectedKey(NioEventLoop.java:688)
			at io.netty.channel.nio.NioEventLoop.processSelectedKeysOptimized(NioEventLoop.java:635)
			at io.netty.channel.nio.NioEventLoop.processSelectedKeys(NioEventLoop.java:552)
			at io.netty.channel.nio.NioEventLoop.run(NioEventLoop.java:514)
			... 4 common frames omitted
		Caused by: io.netty.channel.AbstractChannel$AnnotatedNoRouteToHostException: No route to host: no further information: /************:7006
		Caused by: java.net.NoRouteToHostException: No route to host: no further information
			at sun.nio.ch.SocketChannelImpl.checkConnect(Native Method)
			at sun.nio.ch.SocketChannelImpl.finishConnect(SocketChannelImpl.java:717)
			at io.netty.channel.socket.nio.NioSocketChannel.doFinishConnect(NioSocketChannel.java:330)
			at io.netty.channel.nio.AbstractNioChannel$AbstractNioUnsafe.finishConnect(AbstractNioChannel.java:334)
			at io.netty.channel.nio.NioEventLoop.processSelectedKey(NioEventLoop.java:688)
			at io.netty.channel.nio.NioEventLoop.processSelectedKeysOptimized(NioEventLoop.java:635)
			at io.netty.channel.nio.NioEventLoop.processSelectedKeys(NioEventLoop.java:552)
			at io.netty.channel.nio.NioEventLoop.run(NioEventLoop.java:514)
			at io.netty.util.concurrent.SingleThreadEventExecutor$6.run(SingleThreadEventExecutor.java:1050)
			at io.netty.util.internal.ThreadExecutorMap$2.run(ThreadExecutorMap.java:74)
			at io.netty.util.concurrent.FastThreadLocalRunnable.run(FastThreadLocalRunnable.java:30)
			at java.lang.Thread.run(Thread.java:748)
	Caused by: io.lettuce.core.RedisConnectionException: Unable to connect to [************:7001]: No route to host: no further information: /************:7001
		at io.lettuce.core.cluster.topology.ClusterTopologyRefresh.lambda$getConnections$0(ClusterTopologyRefresh.java:290)
		at java.util.concurrent.CompletableFuture.uniWhenComplete(CompletableFuture.java:760)
		at java.util.concurrent.CompletableFuture$UniWhenComplete.tryFire(CompletableFuture.java:736)
		at java.util.concurrent.CompletableFuture.postComplete(CompletableFuture.java:474)
		at java.util.concurrent.CompletableFuture.completeExceptionally(CompletableFuture.java:1977)
		at io.lettuce.core.AbstractRedisClient.lambda$initializeChannelAsync0$4(AbstractRedisClient.java:329)
		at io.netty.util.concurrent.DefaultPromise.notifyListener0(DefaultPromise.java:577)
		at io.netty.util.concurrent.DefaultPromise.notifyListeners0(DefaultPromise.java:570)
		at io.netty.util.concurrent.DefaultPromise.notifyListenersNow(DefaultPromise.java:549)
		at io.netty.util.concurrent.DefaultPromise.notifyListeners(DefaultPromise.java:490)
		at io.netty.util.concurrent.DefaultPromise.setValue0(DefaultPromise.java:615)
		at io.netty.util.concurrent.DefaultPromise.setFailure0(DefaultPromise.java:608)
		at io.netty.util.concurrent.DefaultPromise.tryFailure(DefaultPromise.java:117)
		at io.netty.channel.nio.AbstractNioChannel$AbstractNioUnsafe.fulfillConnectPromise(AbstractNioChannel.java:321)
		at io.netty.channel.nio.AbstractNioChannel$AbstractNioUnsafe.finishConnect(AbstractNioChannel.java:337)
		at io.netty.channel.nio.NioEventLoop.processSelectedKey(NioEventLoop.java:688)
		at io.netty.channel.nio.NioEventLoop.processSelectedKeysOptimized(NioEventLoop.java:635)
		at io.netty.channel.nio.NioEventLoop.processSelectedKeys(NioEventLoop.java:552)
		at io.netty.channel.nio.NioEventLoop.run(NioEventLoop.java:514)
		... 4 common frames omitted
	Caused by: io.netty.channel.AbstractChannel$AnnotatedNoRouteToHostException: No route to host: no further information: /************:7001
	Caused by: java.net.NoRouteToHostException: No route to host: no further information
		at sun.nio.ch.SocketChannelImpl.checkConnect(Native Method)
		at sun.nio.ch.SocketChannelImpl.finishConnect(SocketChannelImpl.java:717)
		at io.netty.channel.socket.nio.NioSocketChannel.doFinishConnect(NioSocketChannel.java:330)
		at io.netty.channel.nio.AbstractNioChannel$AbstractNioUnsafe.finishConnect(AbstractNioChannel.java:334)
		at io.netty.channel.nio.NioEventLoop.processSelectedKey(NioEventLoop.java:688)
		at io.netty.channel.nio.NioEventLoop.processSelectedKeysOptimized(NioEventLoop.java:635)
		at io.netty.channel.nio.NioEventLoop.processSelectedKeys(NioEventLoop.java:552)
		at io.netty.channel.nio.NioEventLoop.run(NioEventLoop.java:514)
		at io.netty.util.concurrent.SingleThreadEventExecutor$6.run(SingleThreadEventExecutor.java:1050)
		at io.netty.util.internal.ThreadExecutorMap$2.run(ThreadExecutorMap.java:74)
		at io.netty.util.concurrent.FastThreadLocalRunnable.run(FastThreadLocalRunnable.java:30)
		at java.lang.Thread.run(Thread.java:748)
Caused by: io.lettuce.core.RedisConnectionException: Unable to establish a connection to Redis Cluster at [RedisURI [host='************', port=7001], RedisURI [host='************', port=7002], RedisURI [host='************', port=7003], RedisURI [host='************', port=7004], RedisURI [host='************', port=7005], RedisURI [host='************', port=7006]]
	at io.lettuce.core.cluster.topology.AsyncConnections.get(AsyncConnections.java:89)
	at io.lettuce.core.cluster.topology.ClusterTopologyRefresh.loadViews(ClusterTopologyRefresh.java:76)
	at io.lettuce.core.cluster.RedisClusterClient.doLoadPartitions(RedisClusterClient.java:865)
	at io.lettuce.core.cluster.RedisClusterClient.loadPartitions(RedisClusterClient.java:845)
	... 10 common frames omitted
	Suppressed: io.lettuce.core.RedisConnectionException: Unable to connect to [************:7004]: No route to host: no further information: /************:7004
		at io.lettuce.core.cluster.topology.ClusterTopologyRefresh.lambda$getConnections$0(ClusterTopologyRefresh.java:290)
		at java.util.concurrent.CompletableFuture.uniWhenComplete(CompletableFuture.java:760)
		at java.util.concurrent.CompletableFuture$UniWhenComplete.tryFire(CompletableFuture.java:736)
		at java.util.concurrent.CompletableFuture.postComplete(CompletableFuture.java:474)
		at java.util.concurrent.CompletableFuture.completeExceptionally(CompletableFuture.java:1977)
		at io.lettuce.core.AbstractRedisClient.lambda$initializeChannelAsync0$4(AbstractRedisClient.java:329)
		at io.netty.util.concurrent.DefaultPromise.notifyListener0(DefaultPromise.java:577)
		at io.netty.util.concurrent.DefaultPromise.notifyListeners0(DefaultPromise.java:570)
		at io.netty.util.concurrent.DefaultPromise.notifyListenersNow(DefaultPromise.java:549)
		at io.netty.util.concurrent.DefaultPromise.notifyListeners(DefaultPromise.java:490)
		at io.netty.util.concurrent.DefaultPromise.setValue0(DefaultPromise.java:615)
		at io.netty.util.concurrent.DefaultPromise.setFailure0(DefaultPromise.java:608)
		at io.netty.util.concurrent.DefaultPromise.tryFailure(DefaultPromise.java:117)
		at io.netty.channel.nio.AbstractNioChannel$AbstractNioUnsafe.fulfillConnectPromise(AbstractNioChannel.java:321)
		at io.netty.channel.nio.AbstractNioChannel$AbstractNioUnsafe.finishConnect(AbstractNioChannel.java:337)
		at io.netty.channel.nio.NioEventLoop.processSelectedKey(NioEventLoop.java:688)
		at io.netty.channel.nio.NioEventLoop.processSelectedKeysOptimized(NioEventLoop.java:635)
		at io.netty.channel.nio.NioEventLoop.processSelectedKeys(NioEventLoop.java:552)
		at io.netty.channel.nio.NioEventLoop.run(NioEventLoop.java:514)
		... 4 common frames omitted
	Caused by: io.netty.channel.AbstractChannel$AnnotatedNoRouteToHostException: No route to host: no further information: /************:7004
	Caused by: java.net.NoRouteToHostException: No route to host: no further information
		at sun.nio.ch.SocketChannelImpl.checkConnect(Native Method)
		at sun.nio.ch.SocketChannelImpl.finishConnect(SocketChannelImpl.java:717)
		at io.netty.channel.socket.nio.NioSocketChannel.doFinishConnect(NioSocketChannel.java:330)
		at io.netty.channel.nio.AbstractNioChannel$AbstractNioUnsafe.finishConnect(AbstractNioChannel.java:334)
		at io.netty.channel.nio.NioEventLoop.processSelectedKey(NioEventLoop.java:688)
		at io.netty.channel.nio.NioEventLoop.processSelectedKeysOptimized(NioEventLoop.java:635)
		at io.netty.channel.nio.NioEventLoop.processSelectedKeys(NioEventLoop.java:552)
		at io.netty.channel.nio.NioEventLoop.run(NioEventLoop.java:514)
		at io.netty.util.concurrent.SingleThreadEventExecutor$6.run(SingleThreadEventExecutor.java:1050)
		at io.netty.util.internal.ThreadExecutorMap$2.run(ThreadExecutorMap.java:74)
		at io.netty.util.concurrent.FastThreadLocalRunnable.run(FastThreadLocalRunnable.java:30)
		at java.lang.Thread.run(Thread.java:748)
	Suppressed: io.lettuce.core.RedisConnectionException: Unable to connect to [************:7005]: No route to host: no further information: /************:7005
		at io.lettuce.core.cluster.topology.ClusterTopologyRefresh.lambda$getConnections$0(ClusterTopologyRefresh.java:290)
		at java.util.concurrent.CompletableFuture.uniWhenComplete(CompletableFuture.java:760)
		at java.util.concurrent.CompletableFuture$UniWhenComplete.tryFire(CompletableFuture.java:736)
		at java.util.concurrent.CompletableFuture.postComplete(CompletableFuture.java:474)
		at java.util.concurrent.CompletableFuture.completeExceptionally(CompletableFuture.java:1977)
		at io.lettuce.core.AbstractRedisClient.lambda$initializeChannelAsync0$4(AbstractRedisClient.java:329)
		at io.netty.util.concurrent.DefaultPromise.notifyListener0(DefaultPromise.java:577)
		at io.netty.util.concurrent.DefaultPromise.notifyListeners0(DefaultPromise.java:570)
		at io.netty.util.concurrent.DefaultPromise.notifyListenersNow(DefaultPromise.java:549)
		at io.netty.util.concurrent.DefaultPromise.notifyListeners(DefaultPromise.java:490)
		at io.netty.util.concurrent.DefaultPromise.setValue0(DefaultPromise.java:615)
		at io.netty.util.concurrent.DefaultPromise.setFailure0(DefaultPromise.java:608)
		at io.netty.util.concurrent.DefaultPromise.tryFailure(DefaultPromise.java:117)
		at io.netty.channel.nio.AbstractNioChannel$AbstractNioUnsafe.fulfillConnectPromise(AbstractNioChannel.java:321)
		at io.netty.channel.nio.AbstractNioChannel$AbstractNioUnsafe.finishConnect(AbstractNioChannel.java:337)
		at io.netty.channel.nio.NioEventLoop.processSelectedKey(NioEventLoop.java:688)
		at io.netty.channel.nio.NioEventLoop.processSelectedKeysOptimized(NioEventLoop.java:635)
		at io.netty.channel.nio.NioEventLoop.processSelectedKeys(NioEventLoop.java:552)
		at io.netty.channel.nio.NioEventLoop.run(NioEventLoop.java:514)
		... 4 common frames omitted
	Caused by: io.netty.channel.AbstractChannel$AnnotatedNoRouteToHostException: No route to host: no further information: /************:7005
	Caused by: java.net.NoRouteToHostException: No route to host: no further information
		at sun.nio.ch.SocketChannelImpl.checkConnect(Native Method)
		at sun.nio.ch.SocketChannelImpl.finishConnect(SocketChannelImpl.java:717)
		at io.netty.channel.socket.nio.NioSocketChannel.doFinishConnect(NioSocketChannel.java:330)
		at io.netty.channel.nio.AbstractNioChannel$AbstractNioUnsafe.finishConnect(AbstractNioChannel.java:334)
		at io.netty.channel.nio.NioEventLoop.processSelectedKey(NioEventLoop.java:688)
		at io.netty.channel.nio.NioEventLoop.processSelectedKeysOptimized(NioEventLoop.java:635)
		at io.netty.channel.nio.NioEventLoop.processSelectedKeys(NioEventLoop.java:552)
		at io.netty.channel.nio.NioEventLoop.run(NioEventLoop.java:514)
		at io.netty.util.concurrent.SingleThreadEventExecutor$6.run(SingleThreadEventExecutor.java:1050)
		at io.netty.util.internal.ThreadExecutorMap$2.run(ThreadExecutorMap.java:74)
		at io.netty.util.concurrent.FastThreadLocalRunnable.run(FastThreadLocalRunnable.java:30)
		at java.lang.Thread.run(Thread.java:748)
	Suppressed: io.lettuce.core.RedisConnectionException: Unable to connect to [************:7002]: No route to host: no further information: /************:7002
		at io.lettuce.core.cluster.topology.ClusterTopologyRefresh.lambda$getConnections$0(ClusterTopologyRefresh.java:290)
		at java.util.concurrent.CompletableFuture.uniWhenComplete(CompletableFuture.java:760)
		at java.util.concurrent.CompletableFuture$UniWhenComplete.tryFire(CompletableFuture.java:736)
		at java.util.concurrent.CompletableFuture.postComplete(CompletableFuture.java:474)
		at java.util.concurrent.CompletableFuture.completeExceptionally(CompletableFuture.java:1977)
		at io.lettuce.core.AbstractRedisClient.lambda$initializeChannelAsync0$4(AbstractRedisClient.java:329)
		at io.netty.util.concurrent.DefaultPromise.notifyListener0(DefaultPromise.java:577)
		at io.netty.util.concurrent.DefaultPromise.notifyListeners0(DefaultPromise.java:570)
		at io.netty.util.concurrent.DefaultPromise.notifyListenersNow(DefaultPromise.java:549)
		at io.netty.util.concurrent.DefaultPromise.notifyListeners(DefaultPromise.java:490)
		at io.netty.util.concurrent.DefaultPromise.setValue0(DefaultPromise.java:615)
		at io.netty.util.concurrent.DefaultPromise.setFailure0(DefaultPromise.java:608)
		at io.netty.util.concurrent.DefaultPromise.tryFailure(DefaultPromise.java:117)
		at io.netty.channel.nio.AbstractNioChannel$AbstractNioUnsafe.fulfillConnectPromise(AbstractNioChannel.java:321)
		at io.netty.channel.nio.AbstractNioChannel$AbstractNioUnsafe.finishConnect(AbstractNioChannel.java:337)
		at io.netty.channel.nio.NioEventLoop.processSelectedKey(NioEventLoop.java:688)
		at io.netty.channel.nio.NioEventLoop.processSelectedKeysOptimized(NioEventLoop.java:635)
		at io.netty.channel.nio.NioEventLoop.processSelectedKeys(NioEventLoop.java:552)
		at io.netty.channel.nio.NioEventLoop.run(NioEventLoop.java:514)
		... 4 common frames omitted
	Caused by: io.netty.channel.AbstractChannel$AnnotatedNoRouteToHostException: No route to host: no further information: /************:7002
	Caused by: java.net.NoRouteToHostException: No route to host: no further information
		at sun.nio.ch.SocketChannelImpl.checkConnect(Native Method)
		at sun.nio.ch.SocketChannelImpl.finishConnect(SocketChannelImpl.java:717)
		at io.netty.channel.socket.nio.NioSocketChannel.doFinishConnect(NioSocketChannel.java:330)
		at io.netty.channel.nio.AbstractNioChannel$AbstractNioUnsafe.finishConnect(AbstractNioChannel.java:334)
		at io.netty.channel.nio.NioEventLoop.processSelectedKey(NioEventLoop.java:688)
		at io.netty.channel.nio.NioEventLoop.processSelectedKeysOptimized(NioEventLoop.java:635)
		at io.netty.channel.nio.NioEventLoop.processSelectedKeys(NioEventLoop.java:552)
		at io.netty.channel.nio.NioEventLoop.run(NioEventLoop.java:514)
		at io.netty.util.concurrent.SingleThreadEventExecutor$6.run(SingleThreadEventExecutor.java:1050)
		at io.netty.util.internal.ThreadExecutorMap$2.run(ThreadExecutorMap.java:74)
		at io.netty.util.concurrent.FastThreadLocalRunnable.run(FastThreadLocalRunnable.java:30)
		at java.lang.Thread.run(Thread.java:748)
	Suppressed: io.lettuce.core.RedisConnectionException: Unable to connect to [************:7003]: No route to host: no further information: /************:7003
		at io.lettuce.core.cluster.topology.ClusterTopologyRefresh.lambda$getConnections$0(ClusterTopologyRefresh.java:290)
		at java.util.concurrent.CompletableFuture.uniWhenComplete(CompletableFuture.java:760)
		at java.util.concurrent.CompletableFuture$UniWhenComplete.tryFire(CompletableFuture.java:736)
		at java.util.concurrent.CompletableFuture.postComplete(CompletableFuture.java:474)
		at java.util.concurrent.CompletableFuture.completeExceptionally(CompletableFuture.java:1977)
		at io.lettuce.core.AbstractRedisClient.lambda$initializeChannelAsync0$4(AbstractRedisClient.java:329)
		at io.netty.util.concurrent.DefaultPromise.notifyListener0(DefaultPromise.java:577)
		at io.netty.util.concurrent.DefaultPromise.notifyListeners0(DefaultPromise.java:570)
		at io.netty.util.concurrent.DefaultPromise.notifyListenersNow(DefaultPromise.java:549)
		at io.netty.util.concurrent.DefaultPromise.notifyListeners(DefaultPromise.java:490)
		at io.netty.util.concurrent.DefaultPromise.setValue0(DefaultPromise.java:615)
		at io.netty.util.concurrent.DefaultPromise.setFailure0(DefaultPromise.java:608)
		at io.netty.util.concurrent.DefaultPromise.tryFailure(DefaultPromise.java:117)
		at io.netty.channel.nio.AbstractNioChannel$AbstractNioUnsafe.fulfillConnectPromise(AbstractNioChannel.java:321)
		at io.netty.channel.nio.AbstractNioChannel$AbstractNioUnsafe.finishConnect(AbstractNioChannel.java:337)
		at io.netty.channel.nio.NioEventLoop.processSelectedKey(NioEventLoop.java:688)
		at io.netty.channel.nio.NioEventLoop.processSelectedKeysOptimized(NioEventLoop.java:635)
		at io.netty.channel.nio.NioEventLoop.processSelectedKeys(NioEventLoop.java:552)
		at io.netty.channel.nio.NioEventLoop.run(NioEventLoop.java:514)
		... 4 common frames omitted
	Caused by: io.netty.channel.AbstractChannel$AnnotatedNoRouteToHostException: No route to host: no further information: /************:7003
	Caused by: java.net.NoRouteToHostException: No route to host: no further information
		at sun.nio.ch.SocketChannelImpl.checkConnect(Native Method)
		at sun.nio.ch.SocketChannelImpl.finishConnect(SocketChannelImpl.java:717)
		at io.netty.channel.socket.nio.NioSocketChannel.doFinishConnect(NioSocketChannel.java:330)
		at io.netty.channel.nio.AbstractNioChannel$AbstractNioUnsafe.finishConnect(AbstractNioChannel.java:334)
		at io.netty.channel.nio.NioEventLoop.processSelectedKey(NioEventLoop.java:688)
		at io.netty.channel.nio.NioEventLoop.processSelectedKeysOptimized(NioEventLoop.java:635)
		at io.netty.channel.nio.NioEventLoop.processSelectedKeys(NioEventLoop.java:552)
		at io.netty.channel.nio.NioEventLoop.run(NioEventLoop.java:514)
		at io.netty.util.concurrent.SingleThreadEventExecutor$6.run(SingleThreadEventExecutor.java:1050)
		at io.netty.util.internal.ThreadExecutorMap$2.run(ThreadExecutorMap.java:74)
		at io.netty.util.concurrent.FastThreadLocalRunnable.run(FastThreadLocalRunnable.java:30)
		at java.lang.Thread.run(Thread.java:748)
	Suppressed: io.lettuce.core.RedisConnectionException: Unable to connect to [************:7001]: No route to host: no further information: /************:7001
		at io.lettuce.core.cluster.topology.ClusterTopologyRefresh.lambda$getConnections$0(ClusterTopologyRefresh.java:290)
		at java.util.concurrent.CompletableFuture.uniWhenComplete(CompletableFuture.java:760)
		at java.util.concurrent.CompletableFuture$UniWhenComplete.tryFire(CompletableFuture.java:736)
		at java.util.concurrent.CompletableFuture.postComplete(CompletableFuture.java:474)
		at java.util.concurrent.CompletableFuture.completeExceptionally(CompletableFuture.java:1977)
		at io.lettuce.core.AbstractRedisClient.lambda$initializeChannelAsync0$4(AbstractRedisClient.java:329)
		at io.netty.util.concurrent.DefaultPromise.notifyListener0(DefaultPromise.java:577)
		at io.netty.util.concurrent.DefaultPromise.notifyListeners0(DefaultPromise.java:570)
		at io.netty.util.concurrent.DefaultPromise.notifyListenersNow(DefaultPromise.java:549)
		at io.netty.util.concurrent.DefaultPromise.notifyListeners(DefaultPromise.java:490)
		at io.netty.util.concurrent.DefaultPromise.setValue0(DefaultPromise.java:615)
		at io.netty.util.concurrent.DefaultPromise.setFailure0(DefaultPromise.java:608)
		at io.netty.util.concurrent.DefaultPromise.tryFailure(DefaultPromise.java:117)
		at io.netty.channel.nio.AbstractNioChannel$AbstractNioUnsafe.fulfillConnectPromise(AbstractNioChannel.java:321)
		at io.netty.channel.nio.AbstractNioChannel$AbstractNioUnsafe.finishConnect(AbstractNioChannel.java:337)
		at io.netty.channel.nio.NioEventLoop.processSelectedKey(NioEventLoop.java:688)
		at io.netty.channel.nio.NioEventLoop.processSelectedKeysOptimized(NioEventLoop.java:635)
		at io.netty.channel.nio.NioEventLoop.processSelectedKeys(NioEventLoop.java:552)
		at io.netty.channel.nio.NioEventLoop.run(NioEventLoop.java:514)
		... 4 common frames omitted
	Caused by: io.netty.channel.AbstractChannel$AnnotatedNoRouteToHostException: No route to host: no further information: /************:7001
	Caused by: java.net.NoRouteToHostException: No route to host: no further information
		at sun.nio.ch.SocketChannelImpl.checkConnect(Native Method)
		at sun.nio.ch.SocketChannelImpl.finishConnect(SocketChannelImpl.java:717)
		at io.netty.channel.socket.nio.NioSocketChannel.doFinishConnect(NioSocketChannel.java:330)
		at io.netty.channel.nio.AbstractNioChannel$AbstractNioUnsafe.finishConnect(AbstractNioChannel.java:334)
		at io.netty.channel.nio.NioEventLoop.processSelectedKey(NioEventLoop.java:688)
		at io.netty.channel.nio.NioEventLoop.processSelectedKeysOptimized(NioEventLoop.java:635)
		at io.netty.channel.nio.NioEventLoop.processSelectedKeys(NioEventLoop.java:552)
		at io.netty.channel.nio.NioEventLoop.run(NioEventLoop.java:514)
		at io.netty.util.concurrent.SingleThreadEventExecutor$6.run(SingleThreadEventExecutor.java:1050)
		at io.netty.util.internal.ThreadExecutorMap$2.run(ThreadExecutorMap.java:74)
		at io.netty.util.concurrent.FastThreadLocalRunnable.run(FastThreadLocalRunnable.java:30)
		at java.lang.Thread.run(Thread.java:748)
Caused by: io.lettuce.core.RedisConnectionException: Unable to connect to [************:7006]: No route to host: no further information: /************:7006
	at io.lettuce.core.cluster.topology.ClusterTopologyRefresh.lambda$getConnections$0(ClusterTopologyRefresh.java:290)
	at java.util.concurrent.CompletableFuture.uniWhenComplete(CompletableFuture.java:760)
	at java.util.concurrent.CompletableFuture$UniWhenComplete.tryFire(CompletableFuture.java:736)
	at java.util.concurrent.CompletableFuture.postComplete(CompletableFuture.java:474)
	at java.util.concurrent.CompletableFuture.completeExceptionally(CompletableFuture.java:1977)
	at io.lettuce.core.AbstractRedisClient.lambda$initializeChannelAsync0$4(AbstractRedisClient.java:329)
	at io.netty.util.concurrent.DefaultPromise.notifyListener0(DefaultPromise.java:577)
	at io.netty.util.concurrent.DefaultPromise.notifyListeners0(DefaultPromise.java:570)
	at io.netty.util.concurrent.DefaultPromise.notifyListenersNow(DefaultPromise.java:549)
	at io.netty.util.concurrent.DefaultPromise.notifyListeners(DefaultPromise.java:490)
	at io.netty.util.concurrent.DefaultPromise.setValue0(DefaultPromise.java:615)
	at io.netty.util.concurrent.DefaultPromise.setFailure0(DefaultPromise.java:608)
	at io.netty.util.concurrent.DefaultPromise.tryFailure(DefaultPromise.java:117)
	at io.netty.channel.nio.AbstractNioChannel$AbstractNioUnsafe.fulfillConnectPromise(AbstractNioChannel.java:321)
	at io.netty.channel.nio.AbstractNioChannel$AbstractNioUnsafe.finishConnect(AbstractNioChannel.java:337)
	at io.netty.channel.nio.NioEventLoop.processSelectedKey(NioEventLoop.java:688)
	at io.netty.channel.nio.NioEventLoop.processSelectedKeysOptimized(NioEventLoop.java:635)
	at io.netty.channel.nio.NioEventLoop.processSelectedKeys(NioEventLoop.java:552)
	at io.netty.channel.nio.NioEventLoop.run(NioEventLoop.java:514)
	... 4 common frames omitted
Caused by: io.netty.channel.AbstractChannel$AnnotatedNoRouteToHostException: No route to host: no further information: /************:7006
Caused by: java.net.NoRouteToHostException: No route to host: no further information
	at sun.nio.ch.SocketChannelImpl.checkConnect(Native Method)
	at sun.nio.ch.SocketChannelImpl.finishConnect(SocketChannelImpl.java:717)
	at io.netty.channel.socket.nio.NioSocketChannel.doFinishConnect(NioSocketChannel.java:330)
	at io.netty.channel.nio.AbstractNioChannel$AbstractNioUnsafe.finishConnect(AbstractNioChannel.java:334)
	at io.netty.channel.nio.NioEventLoop.processSelectedKey(NioEventLoop.java:688)
	at io.netty.channel.nio.NioEventLoop.processSelectedKeysOptimized(NioEventLoop.java:635)
	at io.netty.channel.nio.NioEventLoop.processSelectedKeys(NioEventLoop.java:552)
	at io.netty.channel.nio.NioEventLoop.run(NioEventLoop.java:514)
	at io.netty.util.concurrent.SingleThreadEventExecutor$6.run(SingleThreadEventExecutor.java:1050)
	at io.netty.util.internal.ThreadExecutorMap$2.run(ThreadExecutorMap.java:74)
	at io.netty.util.concurrent.FastThreadLocalRunnable.run(FastThreadLocalRunnable.java:30)
	at java.lang.Thread.run(Thread.java:748)
2025-06-01 11:22:13.767 [AMQP Connection ************:5672] WARN  com.rabbitmq.client.impl.ForgivingExceptionHandler.log Line:115 - An unexpected connection driver error occured (Exception message: Connection reset)
2025-06-01 11:22:14.759 [AMQP Connection ************:5672] WARN  com.rabbitmq.client.impl.ForgivingExceptionHandler.log Line:115 - An unexpected connection driver error occured (Exception message: Connection reset)
2025-06-01 11:22:14.838 [AMQP Connection ************:5672] WARN  com.rabbitmq.client.impl.ForgivingExceptionHandler.log Line:115 - An unexpected connection driver error occured (Exception message: Connection reset)
2025-06-01 11:22:16.777 [AMQP Connection ************:5672] WARN  com.rabbitmq.client.impl.ForgivingExceptionHandler.log Line:115 - An unexpected connection driver error occured (Exception message: Connection reset)
2025-06-01 12:03:14.743 [AMQP Connection ************:5672] WARN  com.rabbitmq.client.impl.ForgivingExceptionHandler.log Line:115 - An unexpected connection driver error occured (Exception message: Connection reset)
2025-06-01 12:03:15.752 [AMQP Connection ************:5672] WARN  com.rabbitmq.client.impl.ForgivingExceptionHandler.log Line:115 - An unexpected connection driver error occured (Exception message: Connection reset)
2025-06-01 12:03:15.767 [AMQP Connection ************:5672] WARN  com.rabbitmq.client.impl.ForgivingExceptionHandler.log Line:115 - An unexpected connection driver error occured (Exception message: Connection reset)
2025-06-01 12:03:17.778 [AMQP Connection ************:5672] WARN  com.rabbitmq.client.impl.ForgivingExceptionHandler.log Line:115 - An unexpected connection driver error occured (Exception message: Connection reset)
2025-06-01 12:43:46.748 [AMQP Connection ************:5672] WARN  com.rabbitmq.client.impl.ForgivingExceptionHandler.log Line:115 - An unexpected connection driver error occured (Exception message: Connection reset)
2025-06-01 12:43:47.738 [AMQP Connection ************:5672] WARN  com.rabbitmq.client.impl.ForgivingExceptionHandler.log Line:115 - An unexpected connection driver error occured (Exception message: Connection reset)
2025-06-01 12:43:47.846 [AMQP Connection ************:5672] WARN  com.rabbitmq.client.impl.ForgivingExceptionHandler.log Line:115 - An unexpected connection driver error occured (Exception message: Connection reset)
2025-06-01 12:43:49.787 [AMQP Connection ************:5672] WARN  com.rabbitmq.client.impl.ForgivingExceptionHandler.log Line:115 - An unexpected connection driver error occured (Exception message: Connection reset)
2025-06-01 13:24:18.768 [AMQP Connection ************:5672] WARN  com.rabbitmq.client.impl.ForgivingExceptionHandler.log Line:115 - An unexpected connection driver error occured (Exception message: Connection reset)
2025-06-01 13:24:19.620 [AMQP Connection ************:5672] WARN  com.rabbitmq.client.impl.ForgivingExceptionHandler.log Line:115 - An unexpected connection driver error occured (Exception message: Connection reset)
2025-06-01 13:24:19.728 [AMQP Connection ************:5672] WARN  com.rabbitmq.client.impl.ForgivingExceptionHandler.log Line:115 - An unexpected connection driver error occured (Exception message: Connection reset)
2025-06-01 13:24:21.622 [AMQP Connection ************:5672] WARN  com.rabbitmq.client.impl.ForgivingExceptionHandler.log Line:115 - An unexpected connection driver error occured (Exception message: Connection reset)
2025-06-01 14:05:19.785 [AMQP Connection ************:5672] WARN  com.rabbitmq.client.impl.ForgivingExceptionHandler.log Line:115 - An unexpected connection driver error occured (Exception message: Connection reset)
2025-06-01 14:05:20.607 [AMQP Connection ************:5672] WARN  com.rabbitmq.client.impl.ForgivingExceptionHandler.log Line:115 - An unexpected connection driver error occured (Exception message: Connection reset)
2025-06-01 14:05:20.714 [AMQP Connection ************:5672] WARN  com.rabbitmq.client.impl.ForgivingExceptionHandler.log Line:115 - An unexpected connection driver error occured (Exception message: Connection reset)
2025-06-01 14:05:22.630 [AMQP Connection ************:5672] WARN  com.rabbitmq.client.impl.ForgivingExceptionHandler.log Line:115 - An unexpected connection driver error occured (Exception message: Connection reset)
2025-06-01 14:45:51.923 [AMQP Connection ************:5672] WARN  com.rabbitmq.client.impl.ForgivingExceptionHandler.log Line:115 - An unexpected connection driver error occured (Exception message: Connection reset)
2025-06-01 14:45:52.734 [AMQP Connection ************:5672] WARN  com.rabbitmq.client.impl.ForgivingExceptionHandler.log Line:115 - An unexpected connection driver error occured (Exception message: Connection reset)
2025-06-01 14:45:52.796 [AMQP Connection ************:5672] WARN  com.rabbitmq.client.impl.ForgivingExceptionHandler.log Line:115 - An unexpected connection driver error occured (Exception message: Connection reset)
2025-06-01 14:45:54.816 [AMQP Connection ************:5672] WARN  com.rabbitmq.client.impl.ForgivingExceptionHandler.log Line:115 - An unexpected connection driver error occured (Exception message: Connection reset)
2025-06-01 15:26:22.761 [AMQP Connection ************:5672] WARN  com.rabbitmq.client.impl.ForgivingExceptionHandler.log Line:115 - An unexpected connection driver error occured (Exception message: Connection reset)
2025-06-01 15:26:23.569 [AMQP Connection ************:5672] WARN  com.rabbitmq.client.impl.ForgivingExceptionHandler.log Line:115 - An unexpected connection driver error occured (Exception message: Connection reset)
2025-06-01 15:26:23.600 [AMQP Connection ************:5672] WARN  com.rabbitmq.client.impl.ForgivingExceptionHandler.log Line:115 - An unexpected connection driver error occured (Exception message: Connection reset)
2025-06-01 15:26:25.622 [AMQP Connection ************:5672] WARN  com.rabbitmq.client.impl.ForgivingExceptionHandler.log Line:115 - An unexpected connection driver error occured (Exception message: Connection reset)
2025-06-01 16:07:24.750 [AMQP Connection ************:5672] WARN  com.rabbitmq.client.impl.ForgivingExceptionHandler.log Line:115 - An unexpected connection driver error occured (Exception message: Connection reset)
2025-06-01 16:07:25.624 [AMQP Connection ************:5672] WARN  com.rabbitmq.client.impl.ForgivingExceptionHandler.log Line:115 - An unexpected connection driver error occured (Exception message: Connection reset)
2025-06-01 16:07:25.670 [AMQP Connection ************:5672] WARN  com.rabbitmq.client.impl.ForgivingExceptionHandler.log Line:115 - An unexpected connection driver error occured (Exception message: Connection reset)
2025-06-01 16:07:27.661 [AMQP Connection ************:5672] WARN  com.rabbitmq.client.impl.ForgivingExceptionHandler.log Line:115 - An unexpected connection driver error occured (Exception message: Connection reset)
2025-06-01 16:47:56.011 [AMQP Connection ************:5672] WARN  com.rabbitmq.client.impl.ForgivingExceptionHandler.log Line:115 - An unexpected connection driver error occured (Exception message: Connection reset)
2025-06-01 16:47:56.895 [AMQP Connection ************:5672] WARN  com.rabbitmq.client.impl.ForgivingExceptionHandler.log Line:115 - An unexpected connection driver error occured (Exception message: Connection reset)
2025-06-01 16:47:56.958 [AMQP Connection ************:5672] WARN  com.rabbitmq.client.impl.ForgivingExceptionHandler.log Line:115 - An unexpected connection driver error occured (Exception message: Connection reset)
2025-06-01 16:47:58.904 [AMQP Connection ************:5672] WARN  com.rabbitmq.client.impl.ForgivingExceptionHandler.log Line:115 - An unexpected connection driver error occured (Exception message: Connection reset)
2025-06-01 17:28:27.829 [AMQP Connection ************:5672] WARN  com.rabbitmq.client.impl.ForgivingExceptionHandler.log Line:115 - An unexpected connection driver error occured (Exception message: Connection reset)
2025-06-01 17:28:28.666 [AMQP Connection ************:5672] WARN  com.rabbitmq.client.impl.ForgivingExceptionHandler.log Line:115 - An unexpected connection driver error occured (Exception message: Connection reset)
2025-06-01 17:28:28.760 [AMQP Connection ************:5672] WARN  com.rabbitmq.client.impl.ForgivingExceptionHandler.log Line:115 - An unexpected connection driver error occured (Exception message: Connection reset)
2025-06-01 17:28:30.665 [AMQP Connection ************:5672] WARN  com.rabbitmq.client.impl.ForgivingExceptionHandler.log Line:115 - An unexpected connection driver error occured (Exception message: Connection reset)
2025-06-01 18:09:29.567 [AMQP Connection ************:5672] WARN  com.rabbitmq.client.impl.ForgivingExceptionHandler.log Line:115 - An unexpected connection driver error occured (Exception message: Connection reset)
2025-06-01 18:09:30.427 [AMQP Connection ************:5672] WARN  com.rabbitmq.client.impl.ForgivingExceptionHandler.log Line:115 - An unexpected connection driver error occured (Exception message: Connection reset)
2025-06-01 18:09:30.489 [AMQP Connection ************:5672] WARN  com.rabbitmq.client.impl.ForgivingExceptionHandler.log Line:115 - An unexpected connection driver error occured (Exception message: Connection reset)
2025-06-01 18:09:32.435 [AMQP Connection ************:5672] WARN  com.rabbitmq.client.impl.ForgivingExceptionHandler.log Line:115 - An unexpected connection driver error occured (Exception message: Connection reset)
2025-06-01 18:50:00.755 [AMQP Connection ************:5672] WARN  com.rabbitmq.client.impl.ForgivingExceptionHandler.log Line:115 - An unexpected connection driver error occured (Exception message: Connection reset)
2025-06-01 18:50:01.713 [AMQP Connection ************:5672] WARN  com.rabbitmq.client.impl.ForgivingExceptionHandler.log Line:115 - An unexpected connection driver error occured (Exception message: Connection reset)
2025-06-01 18:50:01.760 [AMQP Connection ************:5672] WARN  com.rabbitmq.client.impl.ForgivingExceptionHandler.log Line:115 - An unexpected connection driver error occured (Exception message: Connection reset)
2025-06-01 18:50:03.717 [AMQP Connection ************:5672] WARN  com.rabbitmq.client.impl.ForgivingExceptionHandler.log Line:115 - An unexpected connection driver error occured (Exception message: Connection reset)
2025-06-01 19:30:32.120 [AMQP Connection ************:5672] WARN  com.rabbitmq.client.impl.ForgivingExceptionHandler.log Line:115 - An unexpected connection driver error occured (Exception message: Connection reset)
2025-06-01 19:30:33.019 [AMQP Connection ************:5672] WARN  com.rabbitmq.client.impl.ForgivingExceptionHandler.log Line:115 - An unexpected connection driver error occured (Exception message: Connection reset)
2025-06-01 19:30:33.111 [AMQP Connection ************:5672] WARN  com.rabbitmq.client.impl.ForgivingExceptionHandler.log Line:115 - An unexpected connection driver error occured (Exception message: Connection reset)
2025-06-01 19:30:35.142 [AMQP Connection ************:5672] WARN  com.rabbitmq.client.impl.ForgivingExceptionHandler.log Line:115 - An unexpected connection driver error occured (Exception message: Connection reset)
2025-06-01 20:10:54.043 [lettuce-nioEventLoop-4-1] WARN  io.lettuce.core.cluster.topology.ClusterTopologyRefresh.lambda$getConnections$0 Line:284 - Unable to connect to [************:7006]: No route to host: no further information: /************:7006
2025-06-01 20:10:54.043 [lettuce-nioEventLoop-4-11] WARN  io.lettuce.core.cluster.topology.ClusterTopologyRefresh.lambda$getConnections$0 Line:284 - Unable to connect to [************:7004]: No route to host: no further information: /************:7004
2025-06-01 20:10:54.043 [lettuce-nioEventLoop-4-9] WARN  io.lettuce.core.cluster.topology.ClusterTopologyRefresh.lambda$getConnections$0 Line:284 - Unable to connect to [************:7002]: No route to host: no further information: /************:7002
2025-06-01 20:10:54.043 [lettuce-nioEventLoop-4-8] WARN  io.lettuce.core.cluster.topology.ClusterTopologyRefresh.lambda$getConnections$0 Line:284 - Unable to connect to [************:7001]: No route to host: no further information: /************:7001
2025-06-01 20:10:54.043 [lettuce-nioEventLoop-4-10] WARN  io.lettuce.core.cluster.topology.ClusterTopologyRefresh.lambda$getConnections$0 Line:284 - Unable to connect to [************:7003]: No route to host: no further information: /************:7003
2025-06-01 20:10:54.043 [lettuce-nioEventLoop-4-12] WARN  io.lettuce.core.cluster.topology.ClusterTopologyRefresh.lambda$getConnections$0 Line:284 - Unable to connect to [************:7005]: No route to host: no further information: /************:7005
2025-06-01 20:10:54.048 [lettuce-nioEventLoop-4-2] WARN  io.lettuce.core.cluster.topology.ClusterTopologyRefresh.lambda$getConnections$0 Line:284 - Unable to connect to [************:7006]: No route to host: no further information: /************:7006
2025-06-01 20:10:54.050 [lettuce-nioEventLoop-4-6] WARN  io.lettuce.core.cluster.topology.ClusterTopologyRefresh.lambda$getConnections$0 Line:284 - Unable to connect to [************:7003]: No route to host: no further information: /************:7003
2025-06-01 20:10:54.050 [lettuce-nioEventLoop-4-7] WARN  io.lettuce.core.cluster.topology.ClusterTopologyRefresh.lambda$getConnections$0 Line:284 - Unable to connect to [************:7001]: No route to host: no further information: /************:7001
2025-06-01 20:10:54.050 [lettuce-nioEventLoop-4-5] WARN  io.lettuce.core.cluster.topology.ClusterTopologyRefresh.lambda$getConnections$0 Line:284 - Unable to connect to [************:7002]: No route to host: no further information: /************:7002
2025-06-01 20:10:54.050 [lettuce-nioEventLoop-4-4] WARN  io.lettuce.core.cluster.topology.ClusterTopologyRefresh.lambda$getConnections$0 Line:284 - Unable to connect to [************:7005]: No route to host: no further information: /************:7005
2025-06-01 20:10:54.050 [lettuce-nioEventLoop-4-3] WARN  io.lettuce.core.cluster.topology.ClusterTopologyRefresh.lambda$getConnections$0 Line:284 - Unable to connect to [************:7004]: No route to host: no further information: /************:7004
2025-06-01 20:10:54.071 [lettuce-eventExecutorLoop-1-6] WARN  io.lettuce.core.cluster.ClusterTopologyRefreshScheduler.doRun Line:258 - Cannot refresh Redis Cluster topology
io.lettuce.core.RedisException: Cannot retrieve initial cluster partitions from initial URIs [RedisURI [host='************', port=7006], RedisURI [host='************', port=7004], RedisURI [host='************', port=7005], RedisURI [host='************', port=7002], RedisURI [host='************', port=7003], RedisURI [host='************', port=7001]]
	at io.lettuce.core.cluster.RedisClusterClient.loadPartitions(RedisClusterClient.java:848)
	at io.lettuce.core.cluster.RedisClusterClient.reloadPartitions(RedisClusterClient.java:784)
	at io.lettuce.core.cluster.ClusterTopologyRefreshScheduler$ClusterTopologyRefreshTask.doRun(ClusterTopologyRefreshScheduler.java:256)
	at io.lettuce.core.cluster.ClusterTopologyRefreshScheduler$ClusterTopologyRefreshTask.run(ClusterTopologyRefreshScheduler.java:236)
	at io.netty.util.concurrent.PromiseTask.runTask(PromiseTask.java:98)
	at io.netty.util.concurrent.PromiseTask.run(PromiseTask.java:106)
	at io.netty.util.concurrent.DefaultEventExecutor.run(DefaultEventExecutor.java:66)
	at io.netty.util.concurrent.SingleThreadEventExecutor$6.run(SingleThreadEventExecutor.java:1050)
	at io.netty.util.internal.ThreadExecutorMap$2.run(ThreadExecutorMap.java:74)
	at io.netty.util.concurrent.FastThreadLocalRunnable.run(FastThreadLocalRunnable.java:30)
	at java.lang.Thread.run(Thread.java:748)
	Suppressed: io.lettuce.core.RedisConnectionException: Unable to establish a connection to Redis Cluster at [RedisURI [host='************', port=7001], RedisURI [host='************', port=7002], RedisURI [host='************', port=7003], RedisURI [host='************', port=7004], RedisURI [host='************', port=7005], RedisURI [host='************', port=7006]]
		at io.lettuce.core.cluster.topology.AsyncConnections.get(AsyncConnections.java:89)
		at io.lettuce.core.cluster.topology.ClusterTopologyRefresh.loadViews(ClusterTopologyRefresh.java:76)
		at io.lettuce.core.cluster.RedisClusterClient.doLoadPartitions(RedisClusterClient.java:865)
		at io.lettuce.core.cluster.RedisClusterClient.loadPartitions(RedisClusterClient.java:838)
		... 10 common frames omitted
		Suppressed: io.lettuce.core.RedisConnectionException: Unable to connect to [************:7006]: No route to host: no further information: /************:7006
			at io.lettuce.core.cluster.topology.ClusterTopologyRefresh.lambda$getConnections$0(ClusterTopologyRefresh.java:290)
			at java.util.concurrent.CompletableFuture.uniWhenComplete(CompletableFuture.java:760)
			at java.util.concurrent.CompletableFuture$UniWhenComplete.tryFire(CompletableFuture.java:736)
			at java.util.concurrent.CompletableFuture.postComplete(CompletableFuture.java:474)
			at java.util.concurrent.CompletableFuture.completeExceptionally(CompletableFuture.java:1977)
			at io.lettuce.core.AbstractRedisClient.lambda$initializeChannelAsync0$4(AbstractRedisClient.java:329)
			at io.netty.util.concurrent.DefaultPromise.notifyListener0(DefaultPromise.java:577)
			at io.netty.util.concurrent.DefaultPromise.notifyListeners0(DefaultPromise.java:570)
			at io.netty.util.concurrent.DefaultPromise.notifyListenersNow(DefaultPromise.java:549)
			at io.netty.util.concurrent.DefaultPromise.notifyListeners(DefaultPromise.java:490)
			at io.netty.util.concurrent.DefaultPromise.setValue0(DefaultPromise.java:615)
			at io.netty.util.concurrent.DefaultPromise.setFailure0(DefaultPromise.java:608)
			at io.netty.util.concurrent.DefaultPromise.tryFailure(DefaultPromise.java:117)
			at io.netty.channel.nio.AbstractNioChannel$AbstractNioUnsafe.fulfillConnectPromise(AbstractNioChannel.java:321)
			at io.netty.channel.nio.AbstractNioChannel$AbstractNioUnsafe.finishConnect(AbstractNioChannel.java:337)
			at io.netty.channel.nio.NioEventLoop.processSelectedKey(NioEventLoop.java:688)
			at io.netty.channel.nio.NioEventLoop.processSelectedKeysOptimized(NioEventLoop.java:635)
			at io.netty.channel.nio.NioEventLoop.processSelectedKeys(NioEventLoop.java:552)
			at io.netty.channel.nio.NioEventLoop.run(NioEventLoop.java:514)
			... 4 common frames omitted
		Caused by: io.netty.channel.AbstractChannel$AnnotatedNoRouteToHostException: No route to host: no further information: /************:7006
		Caused by: java.net.NoRouteToHostException: No route to host: no further information
			at sun.nio.ch.SocketChannelImpl.checkConnect(Native Method)
			at sun.nio.ch.SocketChannelImpl.finishConnect(SocketChannelImpl.java:717)
			at io.netty.channel.socket.nio.NioSocketChannel.doFinishConnect(NioSocketChannel.java:330)
			at io.netty.channel.nio.AbstractNioChannel$AbstractNioUnsafe.finishConnect(AbstractNioChannel.java:334)
			at io.netty.channel.nio.NioEventLoop.processSelectedKey(NioEventLoop.java:688)
			at io.netty.channel.nio.NioEventLoop.processSelectedKeysOptimized(NioEventLoop.java:635)
			at io.netty.channel.nio.NioEventLoop.processSelectedKeys(NioEventLoop.java:552)
			at io.netty.channel.nio.NioEventLoop.run(NioEventLoop.java:514)
			at io.netty.util.concurrent.SingleThreadEventExecutor$6.run(SingleThreadEventExecutor.java:1050)
			at io.netty.util.internal.ThreadExecutorMap$2.run(ThreadExecutorMap.java:74)
			at io.netty.util.concurrent.FastThreadLocalRunnable.run(FastThreadLocalRunnable.java:30)
			at java.lang.Thread.run(Thread.java:748)
		Suppressed: io.lettuce.core.RedisConnectionException: Unable to connect to [************:7004]: No route to host: no further information: /************:7004
			at io.lettuce.core.cluster.topology.ClusterTopologyRefresh.lambda$getConnections$0(ClusterTopologyRefresh.java:290)
			at java.util.concurrent.CompletableFuture.uniWhenComplete(CompletableFuture.java:760)
			at java.util.concurrent.CompletableFuture$UniWhenComplete.tryFire(CompletableFuture.java:736)
			at java.util.concurrent.CompletableFuture.postComplete(CompletableFuture.java:474)
			at java.util.concurrent.CompletableFuture.completeExceptionally(CompletableFuture.java:1977)
			at io.lettuce.core.AbstractRedisClient.lambda$initializeChannelAsync0$4(AbstractRedisClient.java:329)
			at io.netty.util.concurrent.DefaultPromise.notifyListener0(DefaultPromise.java:577)
			at io.netty.util.concurrent.DefaultPromise.notifyListeners0(DefaultPromise.java:570)
			at io.netty.util.concurrent.DefaultPromise.notifyListenersNow(DefaultPromise.java:549)
			at io.netty.util.concurrent.DefaultPromise.notifyListeners(DefaultPromise.java:490)
			at io.netty.util.concurrent.DefaultPromise.setValue0(DefaultPromise.java:615)
			at io.netty.util.concurrent.DefaultPromise.setFailure0(DefaultPromise.java:608)
			at io.netty.util.concurrent.DefaultPromise.tryFailure(DefaultPromise.java:117)
			at io.netty.channel.nio.AbstractNioChannel$AbstractNioUnsafe.fulfillConnectPromise(AbstractNioChannel.java:321)
			at io.netty.channel.nio.AbstractNioChannel$AbstractNioUnsafe.finishConnect(AbstractNioChannel.java:337)
			at io.netty.channel.nio.NioEventLoop.processSelectedKey(NioEventLoop.java:688)
			at io.netty.channel.nio.NioEventLoop.processSelectedKeysOptimized(NioEventLoop.java:635)
			at io.netty.channel.nio.NioEventLoop.processSelectedKeys(NioEventLoop.java:552)
			at io.netty.channel.nio.NioEventLoop.run(NioEventLoop.java:514)
			... 4 common frames omitted
		Caused by: io.netty.channel.AbstractChannel$AnnotatedNoRouteToHostException: No route to host: no further information: /************:7004
		Caused by: java.net.NoRouteToHostException: No route to host: no further information
			at sun.nio.ch.SocketChannelImpl.checkConnect(Native Method)
			at sun.nio.ch.SocketChannelImpl.finishConnect(SocketChannelImpl.java:717)
			at io.netty.channel.socket.nio.NioSocketChannel.doFinishConnect(NioSocketChannel.java:330)
			at io.netty.channel.nio.AbstractNioChannel$AbstractNioUnsafe.finishConnect(AbstractNioChannel.java:334)
			at io.netty.channel.nio.NioEventLoop.processSelectedKey(NioEventLoop.java:688)
			at io.netty.channel.nio.NioEventLoop.processSelectedKeysOptimized(NioEventLoop.java:635)
			at io.netty.channel.nio.NioEventLoop.processSelectedKeys(NioEventLoop.java:552)
			at io.netty.channel.nio.NioEventLoop.run(NioEventLoop.java:514)
			at io.netty.util.concurrent.SingleThreadEventExecutor$6.run(SingleThreadEventExecutor.java:1050)
			at io.netty.util.internal.ThreadExecutorMap$2.run(ThreadExecutorMap.java:74)
			at io.netty.util.concurrent.FastThreadLocalRunnable.run(FastThreadLocalRunnable.java:30)
			at java.lang.Thread.run(Thread.java:748)
		Suppressed: io.lettuce.core.RedisConnectionException: Unable to connect to [************:7001]: No route to host: no further information: /************:7001
			at io.lettuce.core.cluster.topology.ClusterTopologyRefresh.lambda$getConnections$0(ClusterTopologyRefresh.java:290)
			at java.util.concurrent.CompletableFuture.uniWhenComplete(CompletableFuture.java:760)
			at java.util.concurrent.CompletableFuture$UniWhenComplete.tryFire(CompletableFuture.java:736)
			at java.util.concurrent.CompletableFuture.postComplete(CompletableFuture.java:474)
			at java.util.concurrent.CompletableFuture.completeExceptionally(CompletableFuture.java:1977)
			at io.lettuce.core.AbstractRedisClient.lambda$initializeChannelAsync0$4(AbstractRedisClient.java:329)
			at io.netty.util.concurrent.DefaultPromise.notifyListener0(DefaultPromise.java:577)
			at io.netty.util.concurrent.DefaultPromise.notifyListeners0(DefaultPromise.java:570)
			at io.netty.util.concurrent.DefaultPromise.notifyListenersNow(DefaultPromise.java:549)
			at io.netty.util.concurrent.DefaultPromise.notifyListeners(DefaultPromise.java:490)
			at io.netty.util.concurrent.DefaultPromise.setValue0(DefaultPromise.java:615)
			at io.netty.util.concurrent.DefaultPromise.setFailure0(DefaultPromise.java:608)
			at io.netty.util.concurrent.DefaultPromise.tryFailure(DefaultPromise.java:117)
			at io.netty.channel.nio.AbstractNioChannel$AbstractNioUnsafe.fulfillConnectPromise(AbstractNioChannel.java:321)
			at io.netty.channel.nio.AbstractNioChannel$AbstractNioUnsafe.finishConnect(AbstractNioChannel.java:337)
			at io.netty.channel.nio.NioEventLoop.processSelectedKey(NioEventLoop.java:688)
			at io.netty.channel.nio.NioEventLoop.processSelectedKeysOptimized(NioEventLoop.java:635)
			at io.netty.channel.nio.NioEventLoop.processSelectedKeys(NioEventLoop.java:552)
			at io.netty.channel.nio.NioEventLoop.run(NioEventLoop.java:514)
			... 4 common frames omitted
		Caused by: io.netty.channel.AbstractChannel$AnnotatedNoRouteToHostException: No route to host: no further information: /************:7001
		Caused by: java.net.NoRouteToHostException: No route to host: no further information
			at sun.nio.ch.SocketChannelImpl.checkConnect(Native Method)
			at sun.nio.ch.SocketChannelImpl.finishConnect(SocketChannelImpl.java:717)
			at io.netty.channel.socket.nio.NioSocketChannel.doFinishConnect(NioSocketChannel.java:330)
			at io.netty.channel.nio.AbstractNioChannel$AbstractNioUnsafe.finishConnect(AbstractNioChannel.java:334)
			at io.netty.channel.nio.NioEventLoop.processSelectedKey(NioEventLoop.java:688)
			at io.netty.channel.nio.NioEventLoop.processSelectedKeysOptimized(NioEventLoop.java:635)
			at io.netty.channel.nio.NioEventLoop.processSelectedKeys(NioEventLoop.java:552)
			at io.netty.channel.nio.NioEventLoop.run(NioEventLoop.java:514)
			at io.netty.util.concurrent.SingleThreadEventExecutor$6.run(SingleThreadEventExecutor.java:1050)
			at io.netty.util.internal.ThreadExecutorMap$2.run(ThreadExecutorMap.java:74)
			at io.netty.util.concurrent.FastThreadLocalRunnable.run(FastThreadLocalRunnable.java:30)
			at java.lang.Thread.run(Thread.java:748)
		Suppressed: io.lettuce.core.RedisConnectionException: Unable to connect to [************:7003]: No route to host: no further information: /************:7003
			at io.lettuce.core.cluster.topology.ClusterTopologyRefresh.lambda$getConnections$0(ClusterTopologyRefresh.java:290)
			at java.util.concurrent.CompletableFuture.uniWhenComplete(CompletableFuture.java:760)
			at java.util.concurrent.CompletableFuture$UniWhenComplete.tryFire(CompletableFuture.java:736)
			at java.util.concurrent.CompletableFuture.postComplete(CompletableFuture.java:474)
			at java.util.concurrent.CompletableFuture.completeExceptionally(CompletableFuture.java:1977)
			at io.lettuce.core.AbstractRedisClient.lambda$initializeChannelAsync0$4(AbstractRedisClient.java:329)
			at io.netty.util.concurrent.DefaultPromise.notifyListener0(DefaultPromise.java:577)
			at io.netty.util.concurrent.DefaultPromise.notifyListeners0(DefaultPromise.java:570)
			at io.netty.util.concurrent.DefaultPromise.notifyListenersNow(DefaultPromise.java:549)
			at io.netty.util.concurrent.DefaultPromise.notifyListeners(DefaultPromise.java:490)
			at io.netty.util.concurrent.DefaultPromise.setValue0(DefaultPromise.java:615)
			at io.netty.util.concurrent.DefaultPromise.setFailure0(DefaultPromise.java:608)
			at io.netty.util.concurrent.DefaultPromise.tryFailure(DefaultPromise.java:117)
			at io.netty.channel.nio.AbstractNioChannel$AbstractNioUnsafe.fulfillConnectPromise(AbstractNioChannel.java:321)
			at io.netty.channel.nio.AbstractNioChannel$AbstractNioUnsafe.finishConnect(AbstractNioChannel.java:337)
			at io.netty.channel.nio.NioEventLoop.processSelectedKey(NioEventLoop.java:688)
			at io.netty.channel.nio.NioEventLoop.processSelectedKeysOptimized(NioEventLoop.java:635)
			at io.netty.channel.nio.NioEventLoop.processSelectedKeys(NioEventLoop.java:552)
			at io.netty.channel.nio.NioEventLoop.run(NioEventLoop.java:514)
			... 4 common frames omitted
		Caused by: io.netty.channel.AbstractChannel$AnnotatedNoRouteToHostException: No route to host: no further information: /************:7003
		Caused by: java.net.NoRouteToHostException: No route to host: no further information
			at sun.nio.ch.SocketChannelImpl.checkConnect(Native Method)
			at sun.nio.ch.SocketChannelImpl.finishConnect(SocketChannelImpl.java:717)
			at io.netty.channel.socket.nio.NioSocketChannel.doFinishConnect(NioSocketChannel.java:330)
			at io.netty.channel.nio.AbstractNioChannel$AbstractNioUnsafe.finishConnect(AbstractNioChannel.java:334)
			at io.netty.channel.nio.NioEventLoop.processSelectedKey(NioEventLoop.java:688)
			at io.netty.channel.nio.NioEventLoop.processSelectedKeysOptimized(NioEventLoop.java:635)
			at io.netty.channel.nio.NioEventLoop.processSelectedKeys(NioEventLoop.java:552)
			at io.netty.channel.nio.NioEventLoop.run(NioEventLoop.java:514)
			at io.netty.util.concurrent.SingleThreadEventExecutor$6.run(SingleThreadEventExecutor.java:1050)
			at io.netty.util.internal.ThreadExecutorMap$2.run(ThreadExecutorMap.java:74)
			at io.netty.util.concurrent.FastThreadLocalRunnable.run(FastThreadLocalRunnable.java:30)
			at java.lang.Thread.run(Thread.java:748)
		Suppressed: io.lettuce.core.RedisConnectionException: Unable to connect to [************:7005]: No route to host: no further information: /************:7005
			at io.lettuce.core.cluster.topology.ClusterTopologyRefresh.lambda$getConnections$0(ClusterTopologyRefresh.java:290)
			at java.util.concurrent.CompletableFuture.uniWhenComplete(CompletableFuture.java:760)
			at java.util.concurrent.CompletableFuture$UniWhenComplete.tryFire(CompletableFuture.java:736)
			at java.util.concurrent.CompletableFuture.postComplete(CompletableFuture.java:474)
			at java.util.concurrent.CompletableFuture.completeExceptionally(CompletableFuture.java:1977)
			at io.lettuce.core.AbstractRedisClient.lambda$initializeChannelAsync0$4(AbstractRedisClient.java:329)
			at io.netty.util.concurrent.DefaultPromise.notifyListener0(DefaultPromise.java:577)
			at io.netty.util.concurrent.DefaultPromise.notifyListeners0(DefaultPromise.java:570)
			at io.netty.util.concurrent.DefaultPromise.notifyListenersNow(DefaultPromise.java:549)
			at io.netty.util.concurrent.DefaultPromise.notifyListeners(DefaultPromise.java:490)
			at io.netty.util.concurrent.DefaultPromise.setValue0(DefaultPromise.java:615)
			at io.netty.util.concurrent.DefaultPromise.setFailure0(DefaultPromise.java:608)
			at io.netty.util.concurrent.DefaultPromise.tryFailure(DefaultPromise.java:117)
			at io.netty.channel.nio.AbstractNioChannel$AbstractNioUnsafe.fulfillConnectPromise(AbstractNioChannel.java:321)
			at io.netty.channel.nio.AbstractNioChannel$AbstractNioUnsafe.finishConnect(AbstractNioChannel.java:337)
			at io.netty.channel.nio.NioEventLoop.processSelectedKey(NioEventLoop.java:688)
			at io.netty.channel.nio.NioEventLoop.processSelectedKeysOptimized(NioEventLoop.java:635)
			at io.netty.channel.nio.NioEventLoop.processSelectedKeys(NioEventLoop.java:552)
			at io.netty.channel.nio.NioEventLoop.run(NioEventLoop.java:514)
			... 4 common frames omitted
		Caused by: io.netty.channel.AbstractChannel$AnnotatedNoRouteToHostException: No route to host: no further information: /************:7005
		Caused by: java.net.NoRouteToHostException: No route to host: no further information
			at sun.nio.ch.SocketChannelImpl.checkConnect(Native Method)
			at sun.nio.ch.SocketChannelImpl.finishConnect(SocketChannelImpl.java:717)
			at io.netty.channel.socket.nio.NioSocketChannel.doFinishConnect(NioSocketChannel.java:330)
			at io.netty.channel.nio.AbstractNioChannel$AbstractNioUnsafe.finishConnect(AbstractNioChannel.java:334)
			at io.netty.channel.nio.NioEventLoop.processSelectedKey(NioEventLoop.java:688)
			at io.netty.channel.nio.NioEventLoop.processSelectedKeysOptimized(NioEventLoop.java:635)
			at io.netty.channel.nio.NioEventLoop.processSelectedKeys(NioEventLoop.java:552)
			at io.netty.channel.nio.NioEventLoop.run(NioEventLoop.java:514)
			at io.netty.util.concurrent.SingleThreadEventExecutor$6.run(SingleThreadEventExecutor.java:1050)
			at io.netty.util.internal.ThreadExecutorMap$2.run(ThreadExecutorMap.java:74)
			at io.netty.util.concurrent.FastThreadLocalRunnable.run(FastThreadLocalRunnable.java:30)
			at java.lang.Thread.run(Thread.java:748)
	Caused by: io.lettuce.core.RedisConnectionException: Unable to connect to [************:7002]: No route to host: no further information: /************:7002
		at io.lettuce.core.cluster.topology.ClusterTopologyRefresh.lambda$getConnections$0(ClusterTopologyRefresh.java:290)
		at java.util.concurrent.CompletableFuture.uniWhenComplete(CompletableFuture.java:760)
		at java.util.concurrent.CompletableFuture$UniWhenComplete.tryFire(CompletableFuture.java:736)
		at java.util.concurrent.CompletableFuture.postComplete(CompletableFuture.java:474)
		at java.util.concurrent.CompletableFuture.completeExceptionally(CompletableFuture.java:1977)
		at io.lettuce.core.AbstractRedisClient.lambda$initializeChannelAsync0$4(AbstractRedisClient.java:329)
		at io.netty.util.concurrent.DefaultPromise.notifyListener0(DefaultPromise.java:577)
		at io.netty.util.concurrent.DefaultPromise.notifyListeners0(DefaultPromise.java:570)
		at io.netty.util.concurrent.DefaultPromise.notifyListenersNow(DefaultPromise.java:549)
		at io.netty.util.concurrent.DefaultPromise.notifyListeners(DefaultPromise.java:490)
		at io.netty.util.concurrent.DefaultPromise.setValue0(DefaultPromise.java:615)
		at io.netty.util.concurrent.DefaultPromise.setFailure0(DefaultPromise.java:608)
		at io.netty.util.concurrent.DefaultPromise.tryFailure(DefaultPromise.java:117)
		at io.netty.channel.nio.AbstractNioChannel$AbstractNioUnsafe.fulfillConnectPromise(AbstractNioChannel.java:321)
		at io.netty.channel.nio.AbstractNioChannel$AbstractNioUnsafe.finishConnect(AbstractNioChannel.java:337)
		at io.netty.channel.nio.NioEventLoop.processSelectedKey(NioEventLoop.java:688)
		at io.netty.channel.nio.NioEventLoop.processSelectedKeysOptimized(NioEventLoop.java:635)
		at io.netty.channel.nio.NioEventLoop.processSelectedKeys(NioEventLoop.java:552)
		at io.netty.channel.nio.NioEventLoop.run(NioEventLoop.java:514)
		... 4 common frames omitted
	Caused by: io.netty.channel.AbstractChannel$AnnotatedNoRouteToHostException: No route to host: no further information: /************:7002
	Caused by: java.net.NoRouteToHostException: No route to host: no further information
		at sun.nio.ch.SocketChannelImpl.checkConnect(Native Method)
		at sun.nio.ch.SocketChannelImpl.finishConnect(SocketChannelImpl.java:717)
		at io.netty.channel.socket.nio.NioSocketChannel.doFinishConnect(NioSocketChannel.java:330)
		at io.netty.channel.nio.AbstractNioChannel$AbstractNioUnsafe.finishConnect(AbstractNioChannel.java:334)
		at io.netty.channel.nio.NioEventLoop.processSelectedKey(NioEventLoop.java:688)
		at io.netty.channel.nio.NioEventLoop.processSelectedKeysOptimized(NioEventLoop.java:635)
		at io.netty.channel.nio.NioEventLoop.processSelectedKeys(NioEventLoop.java:552)
		at io.netty.channel.nio.NioEventLoop.run(NioEventLoop.java:514)
		at io.netty.util.concurrent.SingleThreadEventExecutor$6.run(SingleThreadEventExecutor.java:1050)
		at io.netty.util.internal.ThreadExecutorMap$2.run(ThreadExecutorMap.java:74)
		at io.netty.util.concurrent.FastThreadLocalRunnable.run(FastThreadLocalRunnable.java:30)
		at java.lang.Thread.run(Thread.java:748)
Caused by: io.lettuce.core.RedisConnectionException: Unable to establish a connection to Redis Cluster at [RedisURI [host='************', port=7001], RedisURI [host='************', port=7002], RedisURI [host='************', port=7003], RedisURI [host='************', port=7004], RedisURI [host='************', port=7005], RedisURI [host='************', port=7006]]
	at io.lettuce.core.cluster.topology.AsyncConnections.get(AsyncConnections.java:89)
	at io.lettuce.core.cluster.topology.ClusterTopologyRefresh.loadViews(ClusterTopologyRefresh.java:76)
	at io.lettuce.core.cluster.RedisClusterClient.doLoadPartitions(RedisClusterClient.java:865)
	at io.lettuce.core.cluster.RedisClusterClient.loadPartitions(RedisClusterClient.java:845)
	... 10 common frames omitted
	Suppressed: io.lettuce.core.RedisConnectionException: Unable to connect to [************:7003]: No route to host: no further information: /************:7003
		at io.lettuce.core.cluster.topology.ClusterTopologyRefresh.lambda$getConnections$0(ClusterTopologyRefresh.java:290)
		at java.util.concurrent.CompletableFuture.uniWhenComplete(CompletableFuture.java:760)
		at java.util.concurrent.CompletableFuture$UniWhenComplete.tryFire(CompletableFuture.java:736)
		at java.util.concurrent.CompletableFuture.postComplete(CompletableFuture.java:474)
		at java.util.concurrent.CompletableFuture.completeExceptionally(CompletableFuture.java:1977)
		at io.lettuce.core.AbstractRedisClient.lambda$initializeChannelAsync0$4(AbstractRedisClient.java:329)
		at io.netty.util.concurrent.DefaultPromise.notifyListener0(DefaultPromise.java:577)
		at io.netty.util.concurrent.DefaultPromise.notifyListeners0(DefaultPromise.java:570)
		at io.netty.util.concurrent.DefaultPromise.notifyListenersNow(DefaultPromise.java:549)
		at io.netty.util.concurrent.DefaultPromise.notifyListeners(DefaultPromise.java:490)
		at io.netty.util.concurrent.DefaultPromise.setValue0(DefaultPromise.java:615)
		at io.netty.util.concurrent.DefaultPromise.setFailure0(DefaultPromise.java:608)
		at io.netty.util.concurrent.DefaultPromise.tryFailure(DefaultPromise.java:117)
		at io.netty.channel.nio.AbstractNioChannel$AbstractNioUnsafe.fulfillConnectPromise(AbstractNioChannel.java:321)
		at io.netty.channel.nio.AbstractNioChannel$AbstractNioUnsafe.finishConnect(AbstractNioChannel.java:337)
		at io.netty.channel.nio.NioEventLoop.processSelectedKey(NioEventLoop.java:688)
		at io.netty.channel.nio.NioEventLoop.processSelectedKeysOptimized(NioEventLoop.java:635)
		at io.netty.channel.nio.NioEventLoop.processSelectedKeys(NioEventLoop.java:552)
		at io.netty.channel.nio.NioEventLoop.run(NioEventLoop.java:514)
		... 4 common frames omitted
	Caused by: io.netty.channel.AbstractChannel$AnnotatedNoRouteToHostException: No route to host: no further information: /************:7003
	Caused by: java.net.NoRouteToHostException: No route to host: no further information
		at sun.nio.ch.SocketChannelImpl.checkConnect(Native Method)
		at sun.nio.ch.SocketChannelImpl.finishConnect(SocketChannelImpl.java:717)
		at io.netty.channel.socket.nio.NioSocketChannel.doFinishConnect(NioSocketChannel.java:330)
		at io.netty.channel.nio.AbstractNioChannel$AbstractNioUnsafe.finishConnect(AbstractNioChannel.java:334)
		at io.netty.channel.nio.NioEventLoop.processSelectedKey(NioEventLoop.java:688)
		at io.netty.channel.nio.NioEventLoop.processSelectedKeysOptimized(NioEventLoop.java:635)
		at io.netty.channel.nio.NioEventLoop.processSelectedKeys(NioEventLoop.java:552)
		at io.netty.channel.nio.NioEventLoop.run(NioEventLoop.java:514)
		at io.netty.util.concurrent.SingleThreadEventExecutor$6.run(SingleThreadEventExecutor.java:1050)
		at io.netty.util.internal.ThreadExecutorMap$2.run(ThreadExecutorMap.java:74)
		at io.netty.util.concurrent.FastThreadLocalRunnable.run(FastThreadLocalRunnable.java:30)
		at java.lang.Thread.run(Thread.java:748)
	Suppressed: io.lettuce.core.RedisConnectionException: Unable to connect to [************:7001]: No route to host: no further information: /************:7001
		at io.lettuce.core.cluster.topology.ClusterTopologyRefresh.lambda$getConnections$0(ClusterTopologyRefresh.java:290)
		at java.util.concurrent.CompletableFuture.uniWhenComplete(CompletableFuture.java:760)
		at java.util.concurrent.CompletableFuture$UniWhenComplete.tryFire(CompletableFuture.java:736)
		at java.util.concurrent.CompletableFuture.postComplete(CompletableFuture.java:474)
		at java.util.concurrent.CompletableFuture.completeExceptionally(CompletableFuture.java:1977)
		at io.lettuce.core.AbstractRedisClient.lambda$initializeChannelAsync0$4(AbstractRedisClient.java:329)
		at io.netty.util.concurrent.DefaultPromise.notifyListener0(DefaultPromise.java:577)
		at io.netty.util.concurrent.DefaultPromise.notifyListeners0(DefaultPromise.java:570)
		at io.netty.util.concurrent.DefaultPromise.notifyListenersNow(DefaultPromise.java:549)
		at io.netty.util.concurrent.DefaultPromise.notifyListeners(DefaultPromise.java:490)
		at io.netty.util.concurrent.DefaultPromise.setValue0(DefaultPromise.java:615)
		at io.netty.util.concurrent.DefaultPromise.setFailure0(DefaultPromise.java:608)
		at io.netty.util.concurrent.DefaultPromise.tryFailure(DefaultPromise.java:117)
		at io.netty.channel.nio.AbstractNioChannel$AbstractNioUnsafe.fulfillConnectPromise(AbstractNioChannel.java:321)
		at io.netty.channel.nio.AbstractNioChannel$AbstractNioUnsafe.finishConnect(AbstractNioChannel.java:337)
		at io.netty.channel.nio.NioEventLoop.processSelectedKey(NioEventLoop.java:688)
		at io.netty.channel.nio.NioEventLoop.processSelectedKeysOptimized(NioEventLoop.java:635)
		at io.netty.channel.nio.NioEventLoop.processSelectedKeys(NioEventLoop.java:552)
		at io.netty.channel.nio.NioEventLoop.run(NioEventLoop.java:514)
		... 4 common frames omitted
	Caused by: io.netty.channel.AbstractChannel$AnnotatedNoRouteToHostException: No route to host: no further information: /************:7001
	Caused by: java.net.NoRouteToHostException: No route to host: no further information
		at sun.nio.ch.SocketChannelImpl.checkConnect(Native Method)
		at sun.nio.ch.SocketChannelImpl.finishConnect(SocketChannelImpl.java:717)
		at io.netty.channel.socket.nio.NioSocketChannel.doFinishConnect(NioSocketChannel.java:330)
		at io.netty.channel.nio.AbstractNioChannel$AbstractNioUnsafe.finishConnect(AbstractNioChannel.java:334)
		at io.netty.channel.nio.NioEventLoop.processSelectedKey(NioEventLoop.java:688)
		at io.netty.channel.nio.NioEventLoop.processSelectedKeysOptimized(NioEventLoop.java:635)
		at io.netty.channel.nio.NioEventLoop.processSelectedKeys(NioEventLoop.java:552)
		at io.netty.channel.nio.NioEventLoop.run(NioEventLoop.java:514)
		at io.netty.util.concurrent.SingleThreadEventExecutor$6.run(SingleThreadEventExecutor.java:1050)
		at io.netty.util.internal.ThreadExecutorMap$2.run(ThreadExecutorMap.java:74)
		at io.netty.util.concurrent.FastThreadLocalRunnable.run(FastThreadLocalRunnable.java:30)
		at java.lang.Thread.run(Thread.java:748)
	Suppressed: io.lettuce.core.RedisConnectionException: Unable to connect to [************:7002]: No route to host: no further information: /************:7002
		at io.lettuce.core.cluster.topology.ClusterTopologyRefresh.lambda$getConnections$0(ClusterTopologyRefresh.java:290)
		at java.util.concurrent.CompletableFuture.uniWhenComplete(CompletableFuture.java:760)
		at java.util.concurrent.CompletableFuture$UniWhenComplete.tryFire(CompletableFuture.java:736)
		at java.util.concurrent.CompletableFuture.postComplete(CompletableFuture.java:474)
		at java.util.concurrent.CompletableFuture.completeExceptionally(CompletableFuture.java:1977)
		at io.lettuce.core.AbstractRedisClient.lambda$initializeChannelAsync0$4(AbstractRedisClient.java:329)
		at io.netty.util.concurrent.DefaultPromise.notifyListener0(DefaultPromise.java:577)
		at io.netty.util.concurrent.DefaultPromise.notifyListeners0(DefaultPromise.java:570)
		at io.netty.util.concurrent.DefaultPromise.notifyListenersNow(DefaultPromise.java:549)
		at io.netty.util.concurrent.DefaultPromise.notifyListeners(DefaultPromise.java:490)
		at io.netty.util.concurrent.DefaultPromise.setValue0(DefaultPromise.java:615)
		at io.netty.util.concurrent.DefaultPromise.setFailure0(DefaultPromise.java:608)
		at io.netty.util.concurrent.DefaultPromise.tryFailure(DefaultPromise.java:117)
		at io.netty.channel.nio.AbstractNioChannel$AbstractNioUnsafe.fulfillConnectPromise(AbstractNioChannel.java:321)
		at io.netty.channel.nio.AbstractNioChannel$AbstractNioUnsafe.finishConnect(AbstractNioChannel.java:337)
		at io.netty.channel.nio.NioEventLoop.processSelectedKey(NioEventLoop.java:688)
		at io.netty.channel.nio.NioEventLoop.processSelectedKeysOptimized(NioEventLoop.java:635)
		at io.netty.channel.nio.NioEventLoop.processSelectedKeys(NioEventLoop.java:552)
		at io.netty.channel.nio.NioEventLoop.run(NioEventLoop.java:514)
		... 4 common frames omitted
	Caused by: io.netty.channel.AbstractChannel$AnnotatedNoRouteToHostException: No route to host: no further information: /************:7002
	Caused by: java.net.NoRouteToHostException: No route to host: no further information
		at sun.nio.ch.SocketChannelImpl.checkConnect(Native Method)
		at sun.nio.ch.SocketChannelImpl.finishConnect(SocketChannelImpl.java:717)
		at io.netty.channel.socket.nio.NioSocketChannel.doFinishConnect(NioSocketChannel.java:330)
		at io.netty.channel.nio.AbstractNioChannel$AbstractNioUnsafe.finishConnect(AbstractNioChannel.java:334)
		at io.netty.channel.nio.NioEventLoop.processSelectedKey(NioEventLoop.java:688)
		at io.netty.channel.nio.NioEventLoop.processSelectedKeysOptimized(NioEventLoop.java:635)
		at io.netty.channel.nio.NioEventLoop.processSelectedKeys(NioEventLoop.java:552)
		at io.netty.channel.nio.NioEventLoop.run(NioEventLoop.java:514)
		at io.netty.util.concurrent.SingleThreadEventExecutor$6.run(SingleThreadEventExecutor.java:1050)
		at io.netty.util.internal.ThreadExecutorMap$2.run(ThreadExecutorMap.java:74)
		at io.netty.util.concurrent.FastThreadLocalRunnable.run(FastThreadLocalRunnable.java:30)
		at java.lang.Thread.run(Thread.java:748)
	Suppressed: io.lettuce.core.RedisConnectionException: Unable to connect to [************:7005]: No route to host: no further information: /************:7005
		at io.lettuce.core.cluster.topology.ClusterTopologyRefresh.lambda$getConnections$0(ClusterTopologyRefresh.java:290)
		at java.util.concurrent.CompletableFuture.uniWhenComplete(CompletableFuture.java:760)
		at java.util.concurrent.CompletableFuture$UniWhenComplete.tryFire(CompletableFuture.java:736)
		at java.util.concurrent.CompletableFuture.postComplete(CompletableFuture.java:474)
		at java.util.concurrent.CompletableFuture.completeExceptionally(CompletableFuture.java:1977)
		at io.lettuce.core.AbstractRedisClient.lambda$initializeChannelAsync0$4(AbstractRedisClient.java:329)
		at io.netty.util.concurrent.DefaultPromise.notifyListener0(DefaultPromise.java:577)
		at io.netty.util.concurrent.DefaultPromise.notifyListeners0(DefaultPromise.java:570)
		at io.netty.util.concurrent.DefaultPromise.notifyListenersNow(DefaultPromise.java:549)
		at io.netty.util.concurrent.DefaultPromise.notifyListeners(DefaultPromise.java:490)
		at io.netty.util.concurrent.DefaultPromise.setValue0(DefaultPromise.java:615)
		at io.netty.util.concurrent.DefaultPromise.setFailure0(DefaultPromise.java:608)
		at io.netty.util.concurrent.DefaultPromise.tryFailure(DefaultPromise.java:117)
		at io.netty.channel.nio.AbstractNioChannel$AbstractNioUnsafe.fulfillConnectPromise(AbstractNioChannel.java:321)
		at io.netty.channel.nio.AbstractNioChannel$AbstractNioUnsafe.finishConnect(AbstractNioChannel.java:337)
		at io.netty.channel.nio.NioEventLoop.processSelectedKey(NioEventLoop.java:688)
		at io.netty.channel.nio.NioEventLoop.processSelectedKeysOptimized(NioEventLoop.java:635)
		at io.netty.channel.nio.NioEventLoop.processSelectedKeys(NioEventLoop.java:552)
		at io.netty.channel.nio.NioEventLoop.run(NioEventLoop.java:514)
		... 4 common frames omitted
	Caused by: io.netty.channel.AbstractChannel$AnnotatedNoRouteToHostException: No route to host: no further information: /************:7005
	Caused by: java.net.NoRouteToHostException: No route to host: no further information
		at sun.nio.ch.SocketChannelImpl.checkConnect(Native Method)
		at sun.nio.ch.SocketChannelImpl.finishConnect(SocketChannelImpl.java:717)
		at io.netty.channel.socket.nio.NioSocketChannel.doFinishConnect(NioSocketChannel.java:330)
		at io.netty.channel.nio.AbstractNioChannel$AbstractNioUnsafe.finishConnect(AbstractNioChannel.java:334)
		at io.netty.channel.nio.NioEventLoop.processSelectedKey(NioEventLoop.java:688)
		at io.netty.channel.nio.NioEventLoop.processSelectedKeysOptimized(NioEventLoop.java:635)
		at io.netty.channel.nio.NioEventLoop.processSelectedKeys(NioEventLoop.java:552)
		at io.netty.channel.nio.NioEventLoop.run(NioEventLoop.java:514)
		at io.netty.util.concurrent.SingleThreadEventExecutor$6.run(SingleThreadEventExecutor.java:1050)
		at io.netty.util.internal.ThreadExecutorMap$2.run(ThreadExecutorMap.java:74)
		at io.netty.util.concurrent.FastThreadLocalRunnable.run(FastThreadLocalRunnable.java:30)
		at java.lang.Thread.run(Thread.java:748)
	Suppressed: io.lettuce.core.RedisConnectionException: Unable to connect to [************:7004]: No route to host: no further information: /************:7004
		at io.lettuce.core.cluster.topology.ClusterTopologyRefresh.lambda$getConnections$0(ClusterTopologyRefresh.java:290)
		at java.util.concurrent.CompletableFuture.uniWhenComplete(CompletableFuture.java:760)
		at java.util.concurrent.CompletableFuture$UniWhenComplete.tryFire(CompletableFuture.java:736)
		at java.util.concurrent.CompletableFuture.postComplete(CompletableFuture.java:474)
		at java.util.concurrent.CompletableFuture.completeExceptionally(CompletableFuture.java:1977)
		at io.lettuce.core.AbstractRedisClient.lambda$initializeChannelAsync0$4(AbstractRedisClient.java:329)
		at io.netty.util.concurrent.DefaultPromise.notifyListener0(DefaultPromise.java:577)
		at io.netty.util.concurrent.DefaultPromise.notifyListeners0(DefaultPromise.java:570)
		at io.netty.util.concurrent.DefaultPromise.notifyListenersNow(DefaultPromise.java:549)
		at io.netty.util.concurrent.DefaultPromise.notifyListeners(DefaultPromise.java:490)
		at io.netty.util.concurrent.DefaultPromise.setValue0(DefaultPromise.java:615)
		at io.netty.util.concurrent.DefaultPromise.setFailure0(DefaultPromise.java:608)
		at io.netty.util.concurrent.DefaultPromise.tryFailure(DefaultPromise.java:117)
		at io.netty.channel.nio.AbstractNioChannel$AbstractNioUnsafe.fulfillConnectPromise(AbstractNioChannel.java:321)
		at io.netty.channel.nio.AbstractNioChannel$AbstractNioUnsafe.finishConnect(AbstractNioChannel.java:337)
		at io.netty.channel.nio.NioEventLoop.processSelectedKey(NioEventLoop.java:688)
		at io.netty.channel.nio.NioEventLoop.processSelectedKeysOptimized(NioEventLoop.java:635)
		at io.netty.channel.nio.NioEventLoop.processSelectedKeys(NioEventLoop.java:552)
		at io.netty.channel.nio.NioEventLoop.run(NioEventLoop.java:514)
		... 4 common frames omitted
	Caused by: io.netty.channel.AbstractChannel$AnnotatedNoRouteToHostException: No route to host: no further information: /************:7004
	Caused by: java.net.NoRouteToHostException: No route to host: no further information
		at sun.nio.ch.SocketChannelImpl.checkConnect(Native Method)
		at sun.nio.ch.SocketChannelImpl.finishConnect(SocketChannelImpl.java:717)
		at io.netty.channel.socket.nio.NioSocketChannel.doFinishConnect(NioSocketChannel.java:330)
		at io.netty.channel.nio.AbstractNioChannel$AbstractNioUnsafe.finishConnect(AbstractNioChannel.java:334)
		at io.netty.channel.nio.NioEventLoop.processSelectedKey(NioEventLoop.java:688)
		at io.netty.channel.nio.NioEventLoop.processSelectedKeysOptimized(NioEventLoop.java:635)
		at io.netty.channel.nio.NioEventLoop.processSelectedKeys(NioEventLoop.java:552)
		at io.netty.channel.nio.NioEventLoop.run(NioEventLoop.java:514)
		at io.netty.util.concurrent.SingleThreadEventExecutor$6.run(SingleThreadEventExecutor.java:1050)
		at io.netty.util.internal.ThreadExecutorMap$2.run(ThreadExecutorMap.java:74)
		at io.netty.util.concurrent.FastThreadLocalRunnable.run(FastThreadLocalRunnable.java:30)
		at java.lang.Thread.run(Thread.java:748)
Caused by: io.lettuce.core.RedisConnectionException: Unable to connect to [************:7006]: No route to host: no further information: /************:7006
	at io.lettuce.core.cluster.topology.ClusterTopologyRefresh.lambda$getConnections$0(ClusterTopologyRefresh.java:290)
	at java.util.concurrent.CompletableFuture.uniWhenComplete(CompletableFuture.java:760)
	at java.util.concurrent.CompletableFuture$UniWhenComplete.tryFire(CompletableFuture.java:736)
	at java.util.concurrent.CompletableFuture.postComplete(CompletableFuture.java:474)
	at java.util.concurrent.CompletableFuture.completeExceptionally(CompletableFuture.java:1977)
	at io.lettuce.core.AbstractRedisClient.lambda$initializeChannelAsync0$4(AbstractRedisClient.java:329)
	at io.netty.util.concurrent.DefaultPromise.notifyListener0(DefaultPromise.java:577)
	at io.netty.util.concurrent.DefaultPromise.notifyListeners0(DefaultPromise.java:570)
	at io.netty.util.concurrent.DefaultPromise.notifyListenersNow(DefaultPromise.java:549)
	at io.netty.util.concurrent.DefaultPromise.notifyListeners(DefaultPromise.java:490)
	at io.netty.util.concurrent.DefaultPromise.setValue0(DefaultPromise.java:615)
	at io.netty.util.concurrent.DefaultPromise.setFailure0(DefaultPromise.java:608)
	at io.netty.util.concurrent.DefaultPromise.tryFailure(DefaultPromise.java:117)
	at io.netty.channel.nio.AbstractNioChannel$AbstractNioUnsafe.fulfillConnectPromise(AbstractNioChannel.java:321)
	at io.netty.channel.nio.AbstractNioChannel$AbstractNioUnsafe.finishConnect(AbstractNioChannel.java:337)
	at io.netty.channel.nio.NioEventLoop.processSelectedKey(NioEventLoop.java:688)
	at io.netty.channel.nio.NioEventLoop.processSelectedKeysOptimized(NioEventLoop.java:635)
	at io.netty.channel.nio.NioEventLoop.processSelectedKeys(NioEventLoop.java:552)
	at io.netty.channel.nio.NioEventLoop.run(NioEventLoop.java:514)
	... 4 common frames omitted
Caused by: io.netty.channel.AbstractChannel$AnnotatedNoRouteToHostException: No route to host: no further information: /************:7006
Caused by: java.net.NoRouteToHostException: No route to host: no further information
	at sun.nio.ch.SocketChannelImpl.checkConnect(Native Method)
	at sun.nio.ch.SocketChannelImpl.finishConnect(SocketChannelImpl.java:717)
	at io.netty.channel.socket.nio.NioSocketChannel.doFinishConnect(NioSocketChannel.java:330)
	at io.netty.channel.nio.AbstractNioChannel$AbstractNioUnsafe.finishConnect(AbstractNioChannel.java:334)
	at io.netty.channel.nio.NioEventLoop.processSelectedKey(NioEventLoop.java:688)
	at io.netty.channel.nio.NioEventLoop.processSelectedKeysOptimized(NioEventLoop.java:635)
	at io.netty.channel.nio.NioEventLoop.processSelectedKeys(NioEventLoop.java:552)
	at io.netty.channel.nio.NioEventLoop.run(NioEventLoop.java:514)
	at io.netty.util.concurrent.SingleThreadEventExecutor$6.run(SingleThreadEventExecutor.java:1050)
	at io.netty.util.internal.ThreadExecutorMap$2.run(ThreadExecutorMap.java:74)
	at io.netty.util.concurrent.FastThreadLocalRunnable.run(FastThreadLocalRunnable.java:30)
	at java.lang.Thread.run(Thread.java:748)
2025-06-01 20:10:54.327 [lettuce-nioEventLoop-4-1] WARN  io.lettuce.core.cluster.topology.ClusterTopologyRefresh.lambda$getConnections$0 Line:284 - Unable to connect to [************:7006]: No route to host: no further information: /************:7006
2025-06-01 20:10:54.328 [lettuce-nioEventLoop-4-9] WARN  io.lettuce.core.cluster.topology.ClusterTopologyRefresh.lambda$getConnections$0 Line:284 - Unable to connect to [************:7002]: No route to host: no further information: /************:7002
2025-06-01 20:10:54.328 [lettuce-nioEventLoop-4-10] WARN  io.lettuce.core.cluster.topology.ClusterTopologyRefresh.lambda$getConnections$0 Line:284 - Unable to connect to [************:7003]: No route to host: no further information: /************:7003
2025-06-01 20:10:54.328 [lettuce-nioEventLoop-4-12] WARN  io.lettuce.core.cluster.topology.ClusterTopologyRefresh.lambda$getConnections$0 Line:284 - Unable to connect to [************:7005]: No route to host: no further information: /************:7005
2025-06-01 20:10:54.328 [lettuce-nioEventLoop-4-11] WARN  io.lettuce.core.cluster.topology.ClusterTopologyRefresh.lambda$getConnections$0 Line:284 - Unable to connect to [************:7004]: No route to host: no further information: /************:7004
2025-06-01 20:10:54.328 [lettuce-nioEventLoop-4-8] WARN  io.lettuce.core.cluster.topology.ClusterTopologyRefresh.lambda$getConnections$0 Line:284 - Unable to connect to [************:7001]: No route to host: no further information: /************:7001
2025-06-01 20:10:54.330 [lettuce-nioEventLoop-4-2] WARN  io.lettuce.core.cluster.topology.ClusterTopologyRefresh.lambda$getConnections$0 Line:284 - Unable to connect to [************:7006]: No route to host: no further information: /************:7006
2025-06-01 20:10:54.350 [lettuce-nioEventLoop-4-5] WARN  io.lettuce.core.cluster.topology.ClusterTopologyRefresh.lambda$getConnections$0 Line:284 - Unable to connect to [************:7002]: No route to host: no further information: /************:7002
2025-06-01 20:10:54.350 [lettuce-nioEventLoop-4-7] WARN  io.lettuce.core.cluster.topology.ClusterTopologyRefresh.lambda$getConnections$0 Line:284 - Unable to connect to [************:7001]: No route to host: no further information: /************:7001
2025-06-01 20:10:54.350 [lettuce-nioEventLoop-4-6] WARN  io.lettuce.core.cluster.topology.ClusterTopologyRefresh.lambda$getConnections$0 Line:284 - Unable to connect to [************:7003]: No route to host: no further information: /************:7003
2025-06-01 20:10:54.350 [lettuce-nioEventLoop-4-3] WARN  io.lettuce.core.cluster.topology.ClusterTopologyRefresh.lambda$getConnections$0 Line:284 - Unable to connect to [************:7004]: No route to host: no further information: /************:7004
2025-06-01 20:10:54.350 [lettuce-nioEventLoop-4-4] WARN  io.lettuce.core.cluster.topology.ClusterTopologyRefresh.lambda$getConnections$0 Line:284 - Unable to connect to [************:7005]: No route to host: no further information: /************:7005
2025-06-01 20:10:54.358 [lettuce-eventExecutorLoop-1-6] WARN  io.lettuce.core.cluster.ClusterTopologyRefreshScheduler.doRun Line:258 - Cannot refresh Redis Cluster topology
io.lettuce.core.RedisException: Cannot retrieve initial cluster partitions from initial URIs [RedisURI [host='************', port=7006], RedisURI [host='************', port=7004], RedisURI [host='************', port=7005], RedisURI [host='************', port=7002], RedisURI [host='************', port=7003], RedisURI [host='************', port=7001]]
	at io.lettuce.core.cluster.RedisClusterClient.loadPartitions(RedisClusterClient.java:848)
	at io.lettuce.core.cluster.RedisClusterClient.reloadPartitions(RedisClusterClient.java:784)
	at io.lettuce.core.cluster.ClusterTopologyRefreshScheduler$ClusterTopologyRefreshTask.doRun(ClusterTopologyRefreshScheduler.java:256)
	at io.lettuce.core.cluster.ClusterTopologyRefreshScheduler$ClusterTopologyRefreshTask.run(ClusterTopologyRefreshScheduler.java:236)
	at io.netty.util.concurrent.PromiseTask.runTask(PromiseTask.java:98)
	at io.netty.util.concurrent.PromiseTask.run(PromiseTask.java:106)
	at io.netty.util.concurrent.DefaultEventExecutor.run(DefaultEventExecutor.java:66)
	at io.netty.util.concurrent.SingleThreadEventExecutor$6.run(SingleThreadEventExecutor.java:1050)
	at io.netty.util.internal.ThreadExecutorMap$2.run(ThreadExecutorMap.java:74)
	at io.netty.util.concurrent.FastThreadLocalRunnable.run(FastThreadLocalRunnable.java:30)
	at java.lang.Thread.run(Thread.java:748)
	Suppressed: io.lettuce.core.RedisConnectionException: Unable to establish a connection to Redis Cluster at [RedisURI [host='************', port=7001], RedisURI [host='************', port=7002], RedisURI [host='************', port=7003], RedisURI [host='************', port=7004], RedisURI [host='************', port=7005], RedisURI [host='************', port=7006]]
		at io.lettuce.core.cluster.topology.AsyncConnections.get(AsyncConnections.java:89)
		at io.lettuce.core.cluster.topology.ClusterTopologyRefresh.loadViews(ClusterTopologyRefresh.java:76)
		at io.lettuce.core.cluster.RedisClusterClient.doLoadPartitions(RedisClusterClient.java:865)
		at io.lettuce.core.cluster.RedisClusterClient.loadPartitions(RedisClusterClient.java:838)
		... 10 common frames omitted
		Suppressed: io.lettuce.core.RedisConnectionException: Unable to connect to [************:7002]: No route to host: no further information: /************:7002
			at io.lettuce.core.cluster.topology.ClusterTopologyRefresh.lambda$getConnections$0(ClusterTopologyRefresh.java:290)
			at java.util.concurrent.CompletableFuture.uniWhenComplete(CompletableFuture.java:760)
			at java.util.concurrent.CompletableFuture$UniWhenComplete.tryFire(CompletableFuture.java:736)
			at java.util.concurrent.CompletableFuture.postComplete(CompletableFuture.java:474)
			at java.util.concurrent.CompletableFuture.completeExceptionally(CompletableFuture.java:1977)
			at io.lettuce.core.AbstractRedisClient.lambda$initializeChannelAsync0$4(AbstractRedisClient.java:329)
			at io.netty.util.concurrent.DefaultPromise.notifyListener0(DefaultPromise.java:577)
			at io.netty.util.concurrent.DefaultPromise.notifyListeners0(DefaultPromise.java:570)
			at io.netty.util.concurrent.DefaultPromise.notifyListenersNow(DefaultPromise.java:549)
			at io.netty.util.concurrent.DefaultPromise.notifyListeners(DefaultPromise.java:490)
			at io.netty.util.concurrent.DefaultPromise.setValue0(DefaultPromise.java:615)
			at io.netty.util.concurrent.DefaultPromise.setFailure0(DefaultPromise.java:608)
			at io.netty.util.concurrent.DefaultPromise.tryFailure(DefaultPromise.java:117)
			at io.netty.channel.nio.AbstractNioChannel$AbstractNioUnsafe.fulfillConnectPromise(AbstractNioChannel.java:321)
			at io.netty.channel.nio.AbstractNioChannel$AbstractNioUnsafe.finishConnect(AbstractNioChannel.java:337)
			at io.netty.channel.nio.NioEventLoop.processSelectedKey(NioEventLoop.java:688)
			at io.netty.channel.nio.NioEventLoop.processSelectedKeysOptimized(NioEventLoop.java:635)
			at io.netty.channel.nio.NioEventLoop.processSelectedKeys(NioEventLoop.java:552)
			at io.netty.channel.nio.NioEventLoop.run(NioEventLoop.java:514)
			... 4 common frames omitted
		Caused by: io.netty.channel.AbstractChannel$AnnotatedNoRouteToHostException: No route to host: no further information: /************:7002
		Caused by: java.net.NoRouteToHostException: No route to host: no further information
			at sun.nio.ch.SocketChannelImpl.checkConnect(Native Method)
			at sun.nio.ch.SocketChannelImpl.finishConnect(SocketChannelImpl.java:717)
			at io.netty.channel.socket.nio.NioSocketChannel.doFinishConnect(NioSocketChannel.java:330)
			at io.netty.channel.nio.AbstractNioChannel$AbstractNioUnsafe.finishConnect(AbstractNioChannel.java:334)
			at io.netty.channel.nio.NioEventLoop.processSelectedKey(NioEventLoop.java:688)
			at io.netty.channel.nio.NioEventLoop.processSelectedKeysOptimized(NioEventLoop.java:635)
			at io.netty.channel.nio.NioEventLoop.processSelectedKeys(NioEventLoop.java:552)
			at io.netty.channel.nio.NioEventLoop.run(NioEventLoop.java:514)
			at io.netty.util.concurrent.SingleThreadEventExecutor$6.run(SingleThreadEventExecutor.java:1050)
			at io.netty.util.internal.ThreadExecutorMap$2.run(ThreadExecutorMap.java:74)
			at io.netty.util.concurrent.FastThreadLocalRunnable.run(FastThreadLocalRunnable.java:30)
			at java.lang.Thread.run(Thread.java:748)
		Suppressed: io.lettuce.core.RedisConnectionException: Unable to connect to [************:7003]: No route to host: no further information: /************:7003
			at io.lettuce.core.cluster.topology.ClusterTopologyRefresh.lambda$getConnections$0(ClusterTopologyRefresh.java:290)
			at java.util.concurrent.CompletableFuture.uniWhenComplete(CompletableFuture.java:760)
			at java.util.concurrent.CompletableFuture$UniWhenComplete.tryFire(CompletableFuture.java:736)
			at java.util.concurrent.CompletableFuture.postComplete(CompletableFuture.java:474)
			at java.util.concurrent.CompletableFuture.completeExceptionally(CompletableFuture.java:1977)
			at io.lettuce.core.AbstractRedisClient.lambda$initializeChannelAsync0$4(AbstractRedisClient.java:329)
			at io.netty.util.concurrent.DefaultPromise.notifyListener0(DefaultPromise.java:577)
			at io.netty.util.concurrent.DefaultPromise.notifyListeners0(DefaultPromise.java:570)
			at io.netty.util.concurrent.DefaultPromise.notifyListenersNow(DefaultPromise.java:549)
			at io.netty.util.concurrent.DefaultPromise.notifyListeners(DefaultPromise.java:490)
			at io.netty.util.concurrent.DefaultPromise.setValue0(DefaultPromise.java:615)
			at io.netty.util.concurrent.DefaultPromise.setFailure0(DefaultPromise.java:608)
			at io.netty.util.concurrent.DefaultPromise.tryFailure(DefaultPromise.java:117)
			at io.netty.channel.nio.AbstractNioChannel$AbstractNioUnsafe.fulfillConnectPromise(AbstractNioChannel.java:321)
			at io.netty.channel.nio.AbstractNioChannel$AbstractNioUnsafe.finishConnect(AbstractNioChannel.java:337)
			at io.netty.channel.nio.NioEventLoop.processSelectedKey(NioEventLoop.java:688)
			at io.netty.channel.nio.NioEventLoop.processSelectedKeysOptimized(NioEventLoop.java:635)
			at io.netty.channel.nio.NioEventLoop.processSelectedKeys(NioEventLoop.java:552)
			at io.netty.channel.nio.NioEventLoop.run(NioEventLoop.java:514)
			... 4 common frames omitted
		Caused by: io.netty.channel.AbstractChannel$AnnotatedNoRouteToHostException: No route to host: no further information: /************:7003
		Caused by: java.net.NoRouteToHostException: No route to host: no further information
			at sun.nio.ch.SocketChannelImpl.checkConnect(Native Method)
			at sun.nio.ch.SocketChannelImpl.finishConnect(SocketChannelImpl.java:717)
			at io.netty.channel.socket.nio.NioSocketChannel.doFinishConnect(NioSocketChannel.java:330)
			at io.netty.channel.nio.AbstractNioChannel$AbstractNioUnsafe.finishConnect(AbstractNioChannel.java:334)
			at io.netty.channel.nio.NioEventLoop.processSelectedKey(NioEventLoop.java:688)
			at io.netty.channel.nio.NioEventLoop.processSelectedKeysOptimized(NioEventLoop.java:635)
			at io.netty.channel.nio.NioEventLoop.processSelectedKeys(NioEventLoop.java:552)
			at io.netty.channel.nio.NioEventLoop.run(NioEventLoop.java:514)
			at io.netty.util.concurrent.SingleThreadEventExecutor$6.run(SingleThreadEventExecutor.java:1050)
			at io.netty.util.internal.ThreadExecutorMap$2.run(ThreadExecutorMap.java:74)
			at io.netty.util.concurrent.FastThreadLocalRunnable.run(FastThreadLocalRunnable.java:30)
			at java.lang.Thread.run(Thread.java:748)
		Suppressed: io.lettuce.core.RedisConnectionException: Unable to connect to [************:7005]: No route to host: no further information: /************:7005
			at io.lettuce.core.cluster.topology.ClusterTopologyRefresh.lambda$getConnections$0(ClusterTopologyRefresh.java:290)
			at java.util.concurrent.CompletableFuture.uniWhenComplete(CompletableFuture.java:760)
			at java.util.concurrent.CompletableFuture$UniWhenComplete.tryFire(CompletableFuture.java:736)
			at java.util.concurrent.CompletableFuture.postComplete(CompletableFuture.java:474)
			at java.util.concurrent.CompletableFuture.completeExceptionally(CompletableFuture.java:1977)
			at io.lettuce.core.AbstractRedisClient.lambda$initializeChannelAsync0$4(AbstractRedisClient.java:329)
			at io.netty.util.concurrent.DefaultPromise.notifyListener0(DefaultPromise.java:577)
			at io.netty.util.concurrent.DefaultPromise.notifyListeners0(DefaultPromise.java:570)
			at io.netty.util.concurrent.DefaultPromise.notifyListenersNow(DefaultPromise.java:549)
			at io.netty.util.concurrent.DefaultPromise.notifyListeners(DefaultPromise.java:490)
			at io.netty.util.concurrent.DefaultPromise.setValue0(DefaultPromise.java:615)
			at io.netty.util.concurrent.DefaultPromise.setFailure0(DefaultPromise.java:608)
			at io.netty.util.concurrent.DefaultPromise.tryFailure(DefaultPromise.java:117)
			at io.netty.channel.nio.AbstractNioChannel$AbstractNioUnsafe.fulfillConnectPromise(AbstractNioChannel.java:321)
			at io.netty.channel.nio.AbstractNioChannel$AbstractNioUnsafe.finishConnect(AbstractNioChannel.java:337)
			at io.netty.channel.nio.NioEventLoop.processSelectedKey(NioEventLoop.java:688)
			at io.netty.channel.nio.NioEventLoop.processSelectedKeysOptimized(NioEventLoop.java:635)
			at io.netty.channel.nio.NioEventLoop.processSelectedKeys(NioEventLoop.java:552)
			at io.netty.channel.nio.NioEventLoop.run(NioEventLoop.java:514)
			... 4 common frames omitted
		Caused by: io.netty.channel.AbstractChannel$AnnotatedNoRouteToHostException: No route to host: no further information: /************:7005
		Caused by: java.net.NoRouteToHostException: No route to host: no further information
			at sun.nio.ch.SocketChannelImpl.checkConnect(Native Method)
			at sun.nio.ch.SocketChannelImpl.finishConnect(SocketChannelImpl.java:717)
			at io.netty.channel.socket.nio.NioSocketChannel.doFinishConnect(NioSocketChannel.java:330)
			at io.netty.channel.nio.AbstractNioChannel$AbstractNioUnsafe.finishConnect(AbstractNioChannel.java:334)
			at io.netty.channel.nio.NioEventLoop.processSelectedKey(NioEventLoop.java:688)
			at io.netty.channel.nio.NioEventLoop.processSelectedKeysOptimized(NioEventLoop.java:635)
			at io.netty.channel.nio.NioEventLoop.processSelectedKeys(NioEventLoop.java:552)
			at io.netty.channel.nio.NioEventLoop.run(NioEventLoop.java:514)
			at io.netty.util.concurrent.SingleThreadEventExecutor$6.run(SingleThreadEventExecutor.java:1050)
			at io.netty.util.internal.ThreadExecutorMap$2.run(ThreadExecutorMap.java:74)
			at io.netty.util.concurrent.FastThreadLocalRunnable.run(FastThreadLocalRunnable.java:30)
			at java.lang.Thread.run(Thread.java:748)
		Suppressed: io.lettuce.core.RedisConnectionException: Unable to connect to [************:7001]: No route to host: no further information: /************:7001
			at io.lettuce.core.cluster.topology.ClusterTopologyRefresh.lambda$getConnections$0(ClusterTopologyRefresh.java:290)
			at java.util.concurrent.CompletableFuture.uniWhenComplete(CompletableFuture.java:760)
			at java.util.concurrent.CompletableFuture$UniWhenComplete.tryFire(CompletableFuture.java:736)
			at java.util.concurrent.CompletableFuture.postComplete(CompletableFuture.java:474)
			at java.util.concurrent.CompletableFuture.completeExceptionally(CompletableFuture.java:1977)
			at io.lettuce.core.AbstractRedisClient.lambda$initializeChannelAsync0$4(AbstractRedisClient.java:329)
			at io.netty.util.concurrent.DefaultPromise.notifyListener0(DefaultPromise.java:577)
			at io.netty.util.concurrent.DefaultPromise.notifyListeners0(DefaultPromise.java:570)
			at io.netty.util.concurrent.DefaultPromise.notifyListenersNow(DefaultPromise.java:549)
			at io.netty.util.concurrent.DefaultPromise.notifyListeners(DefaultPromise.java:490)
			at io.netty.util.concurrent.DefaultPromise.setValue0(DefaultPromise.java:615)
			at io.netty.util.concurrent.DefaultPromise.setFailure0(DefaultPromise.java:608)
			at io.netty.util.concurrent.DefaultPromise.tryFailure(DefaultPromise.java:117)
			at io.netty.channel.nio.AbstractNioChannel$AbstractNioUnsafe.fulfillConnectPromise(AbstractNioChannel.java:321)
			at io.netty.channel.nio.AbstractNioChannel$AbstractNioUnsafe.finishConnect(AbstractNioChannel.java:337)
			at io.netty.channel.nio.NioEventLoop.processSelectedKey(NioEventLoop.java:688)
			at io.netty.channel.nio.NioEventLoop.processSelectedKeysOptimized(NioEventLoop.java:635)
			at io.netty.channel.nio.NioEventLoop.processSelectedKeys(NioEventLoop.java:552)
			at io.netty.channel.nio.NioEventLoop.run(NioEventLoop.java:514)
			... 4 common frames omitted
		Caused by: io.netty.channel.AbstractChannel$AnnotatedNoRouteToHostException: No route to host: no further information: /************:7001
		Caused by: java.net.NoRouteToHostException: No route to host: no further information
			at sun.nio.ch.SocketChannelImpl.checkConnect(Native Method)
			at sun.nio.ch.SocketChannelImpl.finishConnect(SocketChannelImpl.java:717)
			at io.netty.channel.socket.nio.NioSocketChannel.doFinishConnect(NioSocketChannel.java:330)
			at io.netty.channel.nio.AbstractNioChannel$AbstractNioUnsafe.finishConnect(AbstractNioChannel.java:334)
			at io.netty.channel.nio.NioEventLoop.processSelectedKey(NioEventLoop.java:688)
			at io.netty.channel.nio.NioEventLoop.processSelectedKeysOptimized(NioEventLoop.java:635)
			at io.netty.channel.nio.NioEventLoop.processSelectedKeys(NioEventLoop.java:552)
			at io.netty.channel.nio.NioEventLoop.run(NioEventLoop.java:514)
			at io.netty.util.concurrent.SingleThreadEventExecutor$6.run(SingleThreadEventExecutor.java:1050)
			at io.netty.util.internal.ThreadExecutorMap$2.run(ThreadExecutorMap.java:74)
			at io.netty.util.concurrent.FastThreadLocalRunnable.run(FastThreadLocalRunnable.java:30)
			at java.lang.Thread.run(Thread.java:748)
		Suppressed: io.lettuce.core.RedisConnectionException: Unable to connect to [************:7004]: No route to host: no further information: /************:7004
			at io.lettuce.core.cluster.topology.ClusterTopologyRefresh.lambda$getConnections$0(ClusterTopologyRefresh.java:290)
			at java.util.concurrent.CompletableFuture.uniWhenComplete(CompletableFuture.java:760)
			at java.util.concurrent.CompletableFuture$UniWhenComplete.tryFire(CompletableFuture.java:736)
			at java.util.concurrent.CompletableFuture.postComplete(CompletableFuture.java:474)
			at java.util.concurrent.CompletableFuture.completeExceptionally(CompletableFuture.java:1977)
			at io.lettuce.core.AbstractRedisClient.lambda$initializeChannelAsync0$4(AbstractRedisClient.java:329)
			at io.netty.util.concurrent.DefaultPromise.notifyListener0(DefaultPromise.java:577)
			at io.netty.util.concurrent.DefaultPromise.notifyListeners0(DefaultPromise.java:570)
			at io.netty.util.concurrent.DefaultPromise.notifyListenersNow(DefaultPromise.java:549)
			at io.netty.util.concurrent.DefaultPromise.notifyListeners(DefaultPromise.java:490)
			at io.netty.util.concurrent.DefaultPromise.setValue0(DefaultPromise.java:615)
			at io.netty.util.concurrent.DefaultPromise.setFailure0(DefaultPromise.java:608)
			at io.netty.util.concurrent.DefaultPromise.tryFailure(DefaultPromise.java:117)
			at io.netty.channel.nio.AbstractNioChannel$AbstractNioUnsafe.fulfillConnectPromise(AbstractNioChannel.java:321)
			at io.netty.channel.nio.AbstractNioChannel$AbstractNioUnsafe.finishConnect(AbstractNioChannel.java:337)
			at io.netty.channel.nio.NioEventLoop.processSelectedKey(NioEventLoop.java:688)
			at io.netty.channel.nio.NioEventLoop.processSelectedKeysOptimized(NioEventLoop.java:635)
			at io.netty.channel.nio.NioEventLoop.processSelectedKeys(NioEventLoop.java:552)
			at io.netty.channel.nio.NioEventLoop.run(NioEventLoop.java:514)
			... 4 common frames omitted
		Caused by: io.netty.channel.AbstractChannel$AnnotatedNoRouteToHostException: No route to host: no further information: /************:7004
		Caused by: java.net.NoRouteToHostException: No route to host: no further information
			at sun.nio.ch.SocketChannelImpl.checkConnect(Native Method)
			at sun.nio.ch.SocketChannelImpl.finishConnect(SocketChannelImpl.java:717)
			at io.netty.channel.socket.nio.NioSocketChannel.doFinishConnect(NioSocketChannel.java:330)
			at io.netty.channel.nio.AbstractNioChannel$AbstractNioUnsafe.finishConnect(AbstractNioChannel.java:334)
			at io.netty.channel.nio.NioEventLoop.processSelectedKey(NioEventLoop.java:688)
			at io.netty.channel.nio.NioEventLoop.processSelectedKeysOptimized(NioEventLoop.java:635)
			at io.netty.channel.nio.NioEventLoop.processSelectedKeys(NioEventLoop.java:552)
			at io.netty.channel.nio.NioEventLoop.run(NioEventLoop.java:514)
			at io.netty.util.concurrent.SingleThreadEventExecutor$6.run(SingleThreadEventExecutor.java:1050)
			at io.netty.util.internal.ThreadExecutorMap$2.run(ThreadExecutorMap.java:74)
			at io.netty.util.concurrent.FastThreadLocalRunnable.run(FastThreadLocalRunnable.java:30)
			at java.lang.Thread.run(Thread.java:748)
	Caused by: io.lettuce.core.RedisConnectionException: Unable to connect to [************:7006]: No route to host: no further information: /************:7006
		at io.lettuce.core.cluster.topology.ClusterTopologyRefresh.lambda$getConnections$0(ClusterTopologyRefresh.java:290)
		at java.util.concurrent.CompletableFuture.uniWhenComplete(CompletableFuture.java:760)
		at java.util.concurrent.CompletableFuture$UniWhenComplete.tryFire(CompletableFuture.java:736)
		at java.util.concurrent.CompletableFuture.postComplete(CompletableFuture.java:474)
		at java.util.concurrent.CompletableFuture.completeExceptionally(CompletableFuture.java:1977)
		at io.lettuce.core.AbstractRedisClient.lambda$initializeChannelAsync0$4(AbstractRedisClient.java:329)
		at io.netty.util.concurrent.DefaultPromise.notifyListener0(DefaultPromise.java:577)
		at io.netty.util.concurrent.DefaultPromise.notifyListeners0(DefaultPromise.java:570)
		at io.netty.util.concurrent.DefaultPromise.notifyListenersNow(DefaultPromise.java:549)
		at io.netty.util.concurrent.DefaultPromise.notifyListeners(DefaultPromise.java:490)
		at io.netty.util.concurrent.DefaultPromise.setValue0(DefaultPromise.java:615)
		at io.netty.util.concurrent.DefaultPromise.setFailure0(DefaultPromise.java:608)
		at io.netty.util.concurrent.DefaultPromise.tryFailure(DefaultPromise.java:117)
		at io.netty.channel.nio.AbstractNioChannel$AbstractNioUnsafe.fulfillConnectPromise(AbstractNioChannel.java:321)
		at io.netty.channel.nio.AbstractNioChannel$AbstractNioUnsafe.finishConnect(AbstractNioChannel.java:337)
		at io.netty.channel.nio.NioEventLoop.processSelectedKey(NioEventLoop.java:688)
		at io.netty.channel.nio.NioEventLoop.processSelectedKeysOptimized(NioEventLoop.java:635)
		at io.netty.channel.nio.NioEventLoop.processSelectedKeys(NioEventLoop.java:552)
		at io.netty.channel.nio.NioEventLoop.run(NioEventLoop.java:514)
		... 4 common frames omitted
	Caused by: io.netty.channel.AbstractChannel$AnnotatedNoRouteToHostException: No route to host: no further information: /************:7006
	Caused by: java.net.NoRouteToHostException: No route to host: no further information
		at sun.nio.ch.SocketChannelImpl.checkConnect(Native Method)
		at sun.nio.ch.SocketChannelImpl.finishConnect(SocketChannelImpl.java:717)
		at io.netty.channel.socket.nio.NioSocketChannel.doFinishConnect(NioSocketChannel.java:330)
		at io.netty.channel.nio.AbstractNioChannel$AbstractNioUnsafe.finishConnect(AbstractNioChannel.java:334)
		at io.netty.channel.nio.NioEventLoop.processSelectedKey(NioEventLoop.java:688)
		at io.netty.channel.nio.NioEventLoop.processSelectedKeysOptimized(NioEventLoop.java:635)
		at io.netty.channel.nio.NioEventLoop.processSelectedKeys(NioEventLoop.java:552)
		at io.netty.channel.nio.NioEventLoop.run(NioEventLoop.java:514)
		at io.netty.util.concurrent.SingleThreadEventExecutor$6.run(SingleThreadEventExecutor.java:1050)
		at io.netty.util.internal.ThreadExecutorMap$2.run(ThreadExecutorMap.java:74)
		at io.netty.util.concurrent.FastThreadLocalRunnable.run(FastThreadLocalRunnable.java:30)
		at java.lang.Thread.run(Thread.java:748)
Caused by: io.lettuce.core.RedisConnectionException: Unable to establish a connection to Redis Cluster at [RedisURI [host='************', port=7001], RedisURI [host='************', port=7002], RedisURI [host='************', port=7003], RedisURI [host='************', port=7004], RedisURI [host='************', port=7005], RedisURI [host='************', port=7006]]
	at io.lettuce.core.cluster.topology.AsyncConnections.get(AsyncConnections.java:89)
	at io.lettuce.core.cluster.topology.ClusterTopologyRefresh.loadViews(ClusterTopologyRefresh.java:76)
	at io.lettuce.core.cluster.RedisClusterClient.doLoadPartitions(RedisClusterClient.java:865)
	at io.lettuce.core.cluster.RedisClusterClient.loadPartitions(RedisClusterClient.java:845)
	... 10 common frames omitted
	Suppressed: io.lettuce.core.RedisConnectionException: Unable to connect to [************:7002]: No route to host: no further information: /************:7002
		at io.lettuce.core.cluster.topology.ClusterTopologyRefresh.lambda$getConnections$0(ClusterTopologyRefresh.java:290)
		at java.util.concurrent.CompletableFuture.uniWhenComplete(CompletableFuture.java:760)
		at java.util.concurrent.CompletableFuture$UniWhenComplete.tryFire(CompletableFuture.java:736)
		at java.util.concurrent.CompletableFuture.postComplete(CompletableFuture.java:474)
		at java.util.concurrent.CompletableFuture.completeExceptionally(CompletableFuture.java:1977)
		at io.lettuce.core.AbstractRedisClient.lambda$initializeChannelAsync0$4(AbstractRedisClient.java:329)
		at io.netty.util.concurrent.DefaultPromise.notifyListener0(DefaultPromise.java:577)
		at io.netty.util.concurrent.DefaultPromise.notifyListeners0(DefaultPromise.java:570)
		at io.netty.util.concurrent.DefaultPromise.notifyListenersNow(DefaultPromise.java:549)
		at io.netty.util.concurrent.DefaultPromise.notifyListeners(DefaultPromise.java:490)
		at io.netty.util.concurrent.DefaultPromise.setValue0(DefaultPromise.java:615)
		at io.netty.util.concurrent.DefaultPromise.setFailure0(DefaultPromise.java:608)
		at io.netty.util.concurrent.DefaultPromise.tryFailure(DefaultPromise.java:117)
		at io.netty.channel.nio.AbstractNioChannel$AbstractNioUnsafe.fulfillConnectPromise(AbstractNioChannel.java:321)
		at io.netty.channel.nio.AbstractNioChannel$AbstractNioUnsafe.finishConnect(AbstractNioChannel.java:337)
		at io.netty.channel.nio.NioEventLoop.processSelectedKey(NioEventLoop.java:688)
		at io.netty.channel.nio.NioEventLoop.processSelectedKeysOptimized(NioEventLoop.java:635)
		at io.netty.channel.nio.NioEventLoop.processSelectedKeys(NioEventLoop.java:552)
		at io.netty.channel.nio.NioEventLoop.run(NioEventLoop.java:514)
		... 4 common frames omitted
	Caused by: io.netty.channel.AbstractChannel$AnnotatedNoRouteToHostException: No route to host: no further information: /************:7002
	Caused by: java.net.NoRouteToHostException: No route to host: no further information
		at sun.nio.ch.SocketChannelImpl.checkConnect(Native Method)
		at sun.nio.ch.SocketChannelImpl.finishConnect(SocketChannelImpl.java:717)
		at io.netty.channel.socket.nio.NioSocketChannel.doFinishConnect(NioSocketChannel.java:330)
		at io.netty.channel.nio.AbstractNioChannel$AbstractNioUnsafe.finishConnect(AbstractNioChannel.java:334)
		at io.netty.channel.nio.NioEventLoop.processSelectedKey(NioEventLoop.java:688)
		at io.netty.channel.nio.NioEventLoop.processSelectedKeysOptimized(NioEventLoop.java:635)
		at io.netty.channel.nio.NioEventLoop.processSelectedKeys(NioEventLoop.java:552)
		at io.netty.channel.nio.NioEventLoop.run(NioEventLoop.java:514)
		at io.netty.util.concurrent.SingleThreadEventExecutor$6.run(SingleThreadEventExecutor.java:1050)
		at io.netty.util.internal.ThreadExecutorMap$2.run(ThreadExecutorMap.java:74)
		at io.netty.util.concurrent.FastThreadLocalRunnable.run(FastThreadLocalRunnable.java:30)
		at java.lang.Thread.run(Thread.java:748)
	Suppressed: io.lettuce.core.RedisConnectionException: Unable to connect to [************:7001]: No route to host: no further information: /************:7001
		at io.lettuce.core.cluster.topology.ClusterTopologyRefresh.lambda$getConnections$0(ClusterTopologyRefresh.java:290)
		at java.util.concurrent.CompletableFuture.uniWhenComplete(CompletableFuture.java:760)
		at java.util.concurrent.CompletableFuture$UniWhenComplete.tryFire(CompletableFuture.java:736)
		at java.util.concurrent.CompletableFuture.postComplete(CompletableFuture.java:474)
		at java.util.concurrent.CompletableFuture.completeExceptionally(CompletableFuture.java:1977)
		at io.lettuce.core.AbstractRedisClient.lambda$initializeChannelAsync0$4(AbstractRedisClient.java:329)
		at io.netty.util.concurrent.DefaultPromise.notifyListener0(DefaultPromise.java:577)
		at io.netty.util.concurrent.DefaultPromise.notifyListeners0(DefaultPromise.java:570)
		at io.netty.util.concurrent.DefaultPromise.notifyListenersNow(DefaultPromise.java:549)
		at io.netty.util.concurrent.DefaultPromise.notifyListeners(DefaultPromise.java:490)
		at io.netty.util.concurrent.DefaultPromise.setValue0(DefaultPromise.java:615)
		at io.netty.util.concurrent.DefaultPromise.setFailure0(DefaultPromise.java:608)
		at io.netty.util.concurrent.DefaultPromise.tryFailure(DefaultPromise.java:117)
		at io.netty.channel.nio.AbstractNioChannel$AbstractNioUnsafe.fulfillConnectPromise(AbstractNioChannel.java:321)
		at io.netty.channel.nio.AbstractNioChannel$AbstractNioUnsafe.finishConnect(AbstractNioChannel.java:337)
		at io.netty.channel.nio.NioEventLoop.processSelectedKey(NioEventLoop.java:688)
		at io.netty.channel.nio.NioEventLoop.processSelectedKeysOptimized(NioEventLoop.java:635)
		at io.netty.channel.nio.NioEventLoop.processSelectedKeys(NioEventLoop.java:552)
		at io.netty.channel.nio.NioEventLoop.run(NioEventLoop.java:514)
		... 4 common frames omitted
	Caused by: io.netty.channel.AbstractChannel$AnnotatedNoRouteToHostException: No route to host: no further information: /************:7001
	Caused by: java.net.NoRouteToHostException: No route to host: no further information
		at sun.nio.ch.SocketChannelImpl.checkConnect(Native Method)
		at sun.nio.ch.SocketChannelImpl.finishConnect(SocketChannelImpl.java:717)
		at io.netty.channel.socket.nio.NioSocketChannel.doFinishConnect(NioSocketChannel.java:330)
		at io.netty.channel.nio.AbstractNioChannel$AbstractNioUnsafe.finishConnect(AbstractNioChannel.java:334)
		at io.netty.channel.nio.NioEventLoop.processSelectedKey(NioEventLoop.java:688)
		at io.netty.channel.nio.NioEventLoop.processSelectedKeysOptimized(NioEventLoop.java:635)
		at io.netty.channel.nio.NioEventLoop.processSelectedKeys(NioEventLoop.java:552)
		at io.netty.channel.nio.NioEventLoop.run(NioEventLoop.java:514)
		at io.netty.util.concurrent.SingleThreadEventExecutor$6.run(SingleThreadEventExecutor.java:1050)
		at io.netty.util.internal.ThreadExecutorMap$2.run(ThreadExecutorMap.java:74)
		at io.netty.util.concurrent.FastThreadLocalRunnable.run(FastThreadLocalRunnable.java:30)
		at java.lang.Thread.run(Thread.java:748)
	Suppressed: io.lettuce.core.RedisConnectionException: Unable to connect to [************:7003]: No route to host: no further information: /************:7003
		at io.lettuce.core.cluster.topology.ClusterTopologyRefresh.lambda$getConnections$0(ClusterTopologyRefresh.java:290)
		at java.util.concurrent.CompletableFuture.uniWhenComplete(CompletableFuture.java:760)
		at java.util.concurrent.CompletableFuture$UniWhenComplete.tryFire(CompletableFuture.java:736)
		at java.util.concurrent.CompletableFuture.postComplete(CompletableFuture.java:474)
		at java.util.concurrent.CompletableFuture.completeExceptionally(CompletableFuture.java:1977)
		at io.lettuce.core.AbstractRedisClient.lambda$initializeChannelAsync0$4(AbstractRedisClient.java:329)
		at io.netty.util.concurrent.DefaultPromise.notifyListener0(DefaultPromise.java:577)
		at io.netty.util.concurrent.DefaultPromise.notifyListeners0(DefaultPromise.java:570)
		at io.netty.util.concurrent.DefaultPromise.notifyListenersNow(DefaultPromise.java:549)
		at io.netty.util.concurrent.DefaultPromise.notifyListeners(DefaultPromise.java:490)
		at io.netty.util.concurrent.DefaultPromise.setValue0(DefaultPromise.java:615)
		at io.netty.util.concurrent.DefaultPromise.setFailure0(DefaultPromise.java:608)
		at io.netty.util.concurrent.DefaultPromise.tryFailure(DefaultPromise.java:117)
		at io.netty.channel.nio.AbstractNioChannel$AbstractNioUnsafe.fulfillConnectPromise(AbstractNioChannel.java:321)
		at io.netty.channel.nio.AbstractNioChannel$AbstractNioUnsafe.finishConnect(AbstractNioChannel.java:337)
		at io.netty.channel.nio.NioEventLoop.processSelectedKey(NioEventLoop.java:688)
		at io.netty.channel.nio.NioEventLoop.processSelectedKeysOptimized(NioEventLoop.java:635)
		at io.netty.channel.nio.NioEventLoop.processSelectedKeys(NioEventLoop.java:552)
		at io.netty.channel.nio.NioEventLoop.run(NioEventLoop.java:514)
		... 4 common frames omitted
	Caused by: io.netty.channel.AbstractChannel$AnnotatedNoRouteToHostException: No route to host: no further information: /************:7003
	Caused by: java.net.NoRouteToHostException: No route to host: no further information
		at sun.nio.ch.SocketChannelImpl.checkConnect(Native Method)
		at sun.nio.ch.SocketChannelImpl.finishConnect(SocketChannelImpl.java:717)
		at io.netty.channel.socket.nio.NioSocketChannel.doFinishConnect(NioSocketChannel.java:330)
		at io.netty.channel.nio.AbstractNioChannel$AbstractNioUnsafe.finishConnect(AbstractNioChannel.java:334)
		at io.netty.channel.nio.NioEventLoop.processSelectedKey(NioEventLoop.java:688)
		at io.netty.channel.nio.NioEventLoop.processSelectedKeysOptimized(NioEventLoop.java:635)
		at io.netty.channel.nio.NioEventLoop.processSelectedKeys(NioEventLoop.java:552)
		at io.netty.channel.nio.NioEventLoop.run(NioEventLoop.java:514)
		at io.netty.util.concurrent.SingleThreadEventExecutor$6.run(SingleThreadEventExecutor.java:1050)
		at io.netty.util.internal.ThreadExecutorMap$2.run(ThreadExecutorMap.java:74)
		at io.netty.util.concurrent.FastThreadLocalRunnable.run(FastThreadLocalRunnable.java:30)
		at java.lang.Thread.run(Thread.java:748)
	Suppressed: io.lettuce.core.RedisConnectionException: Unable to connect to [************:7004]: No route to host: no further information: /************:7004
		at io.lettuce.core.cluster.topology.ClusterTopologyRefresh.lambda$getConnections$0(ClusterTopologyRefresh.java:290)
		at java.util.concurrent.CompletableFuture.uniWhenComplete(CompletableFuture.java:760)
		at java.util.concurrent.CompletableFuture$UniWhenComplete.tryFire(CompletableFuture.java:736)
		at java.util.concurrent.CompletableFuture.postComplete(CompletableFuture.java:474)
		at java.util.concurrent.CompletableFuture.completeExceptionally(CompletableFuture.java:1977)
		at io.lettuce.core.AbstractRedisClient.lambda$initializeChannelAsync0$4(AbstractRedisClient.java:329)
		at io.netty.util.concurrent.DefaultPromise.notifyListener0(DefaultPromise.java:577)
		at io.netty.util.concurrent.DefaultPromise.notifyListeners0(DefaultPromise.java:570)
		at io.netty.util.concurrent.DefaultPromise.notifyListenersNow(DefaultPromise.java:549)
		at io.netty.util.concurrent.DefaultPromise.notifyListeners(DefaultPromise.java:490)
		at io.netty.util.concurrent.DefaultPromise.setValue0(DefaultPromise.java:615)
		at io.netty.util.concurrent.DefaultPromise.setFailure0(DefaultPromise.java:608)
		at io.netty.util.concurrent.DefaultPromise.tryFailure(DefaultPromise.java:117)
		at io.netty.channel.nio.AbstractNioChannel$AbstractNioUnsafe.fulfillConnectPromise(AbstractNioChannel.java:321)
		at io.netty.channel.nio.AbstractNioChannel$AbstractNioUnsafe.finishConnect(AbstractNioChannel.java:337)
		at io.netty.channel.nio.NioEventLoop.processSelectedKey(NioEventLoop.java:688)
		at io.netty.channel.nio.NioEventLoop.processSelectedKeysOptimized(NioEventLoop.java:635)
		at io.netty.channel.nio.NioEventLoop.processSelectedKeys(NioEventLoop.java:552)
		at io.netty.channel.nio.NioEventLoop.run(NioEventLoop.java:514)
		... 4 common frames omitted
	Caused by: io.netty.channel.AbstractChannel$AnnotatedNoRouteToHostException: No route to host: no further information: /************:7004
	Caused by: java.net.NoRouteToHostException: No route to host: no further information
		at sun.nio.ch.SocketChannelImpl.checkConnect(Native Method)
		at sun.nio.ch.SocketChannelImpl.finishConnect(SocketChannelImpl.java:717)
		at io.netty.channel.socket.nio.NioSocketChannel.doFinishConnect(NioSocketChannel.java:330)
		at io.netty.channel.nio.AbstractNioChannel$AbstractNioUnsafe.finishConnect(AbstractNioChannel.java:334)
		at io.netty.channel.nio.NioEventLoop.processSelectedKey(NioEventLoop.java:688)
		at io.netty.channel.nio.NioEventLoop.processSelectedKeysOptimized(NioEventLoop.java:635)
		at io.netty.channel.nio.NioEventLoop.processSelectedKeys(NioEventLoop.java:552)
		at io.netty.channel.nio.NioEventLoop.run(NioEventLoop.java:514)
		at io.netty.util.concurrent.SingleThreadEventExecutor$6.run(SingleThreadEventExecutor.java:1050)
		at io.netty.util.internal.ThreadExecutorMap$2.run(ThreadExecutorMap.java:74)
		at io.netty.util.concurrent.FastThreadLocalRunnable.run(FastThreadLocalRunnable.java:30)
		at java.lang.Thread.run(Thread.java:748)
	Suppressed: io.lettuce.core.RedisConnectionException: Unable to connect to [************:7005]: No route to host: no further information: /************:7005
		at io.lettuce.core.cluster.topology.ClusterTopologyRefresh.lambda$getConnections$0(ClusterTopologyRefresh.java:290)
		at java.util.concurrent.CompletableFuture.uniWhenComplete(CompletableFuture.java:760)
		at java.util.concurrent.CompletableFuture$UniWhenComplete.tryFire(CompletableFuture.java:736)
		at java.util.concurrent.CompletableFuture.postComplete(CompletableFuture.java:474)
		at java.util.concurrent.CompletableFuture.completeExceptionally(CompletableFuture.java:1977)
		at io.lettuce.core.AbstractRedisClient.lambda$initializeChannelAsync0$4(AbstractRedisClient.java:329)
		at io.netty.util.concurrent.DefaultPromise.notifyListener0(DefaultPromise.java:577)
		at io.netty.util.concurrent.DefaultPromise.notifyListeners0(DefaultPromise.java:570)
		at io.netty.util.concurrent.DefaultPromise.notifyListenersNow(DefaultPromise.java:549)
		at io.netty.util.concurrent.DefaultPromise.notifyListeners(DefaultPromise.java:490)
		at io.netty.util.concurrent.DefaultPromise.setValue0(DefaultPromise.java:615)
		at io.netty.util.concurrent.DefaultPromise.setFailure0(DefaultPromise.java:608)
		at io.netty.util.concurrent.DefaultPromise.tryFailure(DefaultPromise.java:117)
		at io.netty.channel.nio.AbstractNioChannel$AbstractNioUnsafe.fulfillConnectPromise(AbstractNioChannel.java:321)
		at io.netty.channel.nio.AbstractNioChannel$AbstractNioUnsafe.finishConnect(AbstractNioChannel.java:337)
		at io.netty.channel.nio.NioEventLoop.processSelectedKey(NioEventLoop.java:688)
		at io.netty.channel.nio.NioEventLoop.processSelectedKeysOptimized(NioEventLoop.java:635)
		at io.netty.channel.nio.NioEventLoop.processSelectedKeys(NioEventLoop.java:552)
		at io.netty.channel.nio.NioEventLoop.run(NioEventLoop.java:514)
		... 4 common frames omitted
	Caused by: io.netty.channel.AbstractChannel$AnnotatedNoRouteToHostException: No route to host: no further information: /************:7005
	Caused by: java.net.NoRouteToHostException: No route to host: no further information
		at sun.nio.ch.SocketChannelImpl.checkConnect(Native Method)
		at sun.nio.ch.SocketChannelImpl.finishConnect(SocketChannelImpl.java:717)
		at io.netty.channel.socket.nio.NioSocketChannel.doFinishConnect(NioSocketChannel.java:330)
		at io.netty.channel.nio.AbstractNioChannel$AbstractNioUnsafe.finishConnect(AbstractNioChannel.java:334)
		at io.netty.channel.nio.NioEventLoop.processSelectedKey(NioEventLoop.java:688)
		at io.netty.channel.nio.NioEventLoop.processSelectedKeysOptimized(NioEventLoop.java:635)
		at io.netty.channel.nio.NioEventLoop.processSelectedKeys(NioEventLoop.java:552)
		at io.netty.channel.nio.NioEventLoop.run(NioEventLoop.java:514)
		at io.netty.util.concurrent.SingleThreadEventExecutor$6.run(SingleThreadEventExecutor.java:1050)
		at io.netty.util.internal.ThreadExecutorMap$2.run(ThreadExecutorMap.java:74)
		at io.netty.util.concurrent.FastThreadLocalRunnable.run(FastThreadLocalRunnable.java:30)
		at java.lang.Thread.run(Thread.java:748)
Caused by: io.lettuce.core.RedisConnectionException: Unable to connect to [************:7006]: No route to host: no further information: /************:7006
	at io.lettuce.core.cluster.topology.ClusterTopologyRefresh.lambda$getConnections$0(ClusterTopologyRefresh.java:290)
	at java.util.concurrent.CompletableFuture.uniWhenComplete(CompletableFuture.java:760)
	at java.util.concurrent.CompletableFuture$UniWhenComplete.tryFire(CompletableFuture.java:736)
	at java.util.concurrent.CompletableFuture.postComplete(CompletableFuture.java:474)
	at java.util.concurrent.CompletableFuture.completeExceptionally(CompletableFuture.java:1977)
	at io.lettuce.core.AbstractRedisClient.lambda$initializeChannelAsync0$4(AbstractRedisClient.java:329)
	at io.netty.util.concurrent.DefaultPromise.notifyListener0(DefaultPromise.java:577)
	at io.netty.util.concurrent.DefaultPromise.notifyListeners0(DefaultPromise.java:570)
	at io.netty.util.concurrent.DefaultPromise.notifyListenersNow(DefaultPromise.java:549)
	at io.netty.util.concurrent.DefaultPromise.notifyListeners(DefaultPromise.java:490)
	at io.netty.util.concurrent.DefaultPromise.setValue0(DefaultPromise.java:615)
	at io.netty.util.concurrent.DefaultPromise.setFailure0(DefaultPromise.java:608)
	at io.netty.util.concurrent.DefaultPromise.tryFailure(DefaultPromise.java:117)
	at io.netty.channel.nio.AbstractNioChannel$AbstractNioUnsafe.fulfillConnectPromise(AbstractNioChannel.java:321)
	at io.netty.channel.nio.AbstractNioChannel$AbstractNioUnsafe.finishConnect(AbstractNioChannel.java:337)
	at io.netty.channel.nio.NioEventLoop.processSelectedKey(NioEventLoop.java:688)
	at io.netty.channel.nio.NioEventLoop.processSelectedKeysOptimized(NioEventLoop.java:635)
	at io.netty.channel.nio.NioEventLoop.processSelectedKeys(NioEventLoop.java:552)
	at io.netty.channel.nio.NioEventLoop.run(NioEventLoop.java:514)
	... 4 common frames omitted
Caused by: io.netty.channel.AbstractChannel$AnnotatedNoRouteToHostException: No route to host: no further information: /************:7006
Caused by: java.net.NoRouteToHostException: No route to host: no further information
	at sun.nio.ch.SocketChannelImpl.checkConnect(Native Method)
	at sun.nio.ch.SocketChannelImpl.finishConnect(SocketChannelImpl.java:717)
	at io.netty.channel.socket.nio.NioSocketChannel.doFinishConnect(NioSocketChannel.java:330)
	at io.netty.channel.nio.AbstractNioChannel$AbstractNioUnsafe.finishConnect(AbstractNioChannel.java:334)
	at io.netty.channel.nio.NioEventLoop.processSelectedKey(NioEventLoop.java:688)
	at io.netty.channel.nio.NioEventLoop.processSelectedKeysOptimized(NioEventLoop.java:635)
	at io.netty.channel.nio.NioEventLoop.processSelectedKeys(NioEventLoop.java:552)
	at io.netty.channel.nio.NioEventLoop.run(NioEventLoop.java:514)
	at io.netty.util.concurrent.SingleThreadEventExecutor$6.run(SingleThreadEventExecutor.java:1050)
	at io.netty.util.internal.ThreadExecutorMap$2.run(ThreadExecutorMap.java:74)
	at io.netty.util.concurrent.FastThreadLocalRunnable.run(FastThreadLocalRunnable.java:30)
	at java.lang.Thread.run(Thread.java:748)
2025-06-01 20:11:33.281 [AMQP Connection ************:5672] WARN  com.rabbitmq.client.impl.ForgivingExceptionHandler.log Line:115 - An unexpected connection driver error occured (Exception message: Connection reset)
2025-06-01 20:11:34.278 [AMQP Connection ************:5672] WARN  com.rabbitmq.client.impl.ForgivingExceptionHandler.log Line:115 - An unexpected connection driver error occured (Exception message: Connection reset)
2025-06-01 20:11:34.294 [AMQP Connection ************:5672] WARN  com.rabbitmq.client.impl.ForgivingExceptionHandler.log Line:115 - An unexpected connection driver error occured (Exception message: Connection reset)
2025-06-01 20:11:36.300 [AMQP Connection ************:5672] WARN  com.rabbitmq.client.impl.ForgivingExceptionHandler.log Line:115 - An unexpected connection driver error occured (Exception message: Connection reset)
2025-06-01 20:52:05.198 [AMQP Connection ************:5672] WARN  com.rabbitmq.client.impl.ForgivingExceptionHandler.log Line:115 - An unexpected connection driver error occured (Exception message: Connection reset)
2025-06-01 20:52:06.162 [AMQP Connection ************:5672] WARN  com.rabbitmq.client.impl.ForgivingExceptionHandler.log Line:115 - An unexpected connection driver error occured (Exception message: Connection reset)
2025-06-01 20:52:06.208 [AMQP Connection ************:5672] WARN  com.rabbitmq.client.impl.ForgivingExceptionHandler.log Line:115 - An unexpected connection driver error occured (Exception message: Connection reset)
2025-06-01 20:52:08.226 [AMQP Connection ************:5672] WARN  com.rabbitmq.client.impl.ForgivingExceptionHandler.log Line:115 - An unexpected connection driver error occured (Exception message: Connection reset)
2025-06-01 21:32:37.499 [AMQP Connection ************:5672] WARN  com.rabbitmq.client.impl.ForgivingExceptionHandler.log Line:115 - An unexpected connection driver error occured (Exception message: Connection reset)
2025-06-01 21:32:38.495 [AMQP Connection ************:5672] WARN  com.rabbitmq.client.impl.ForgivingExceptionHandler.log Line:115 - An unexpected connection driver error occured (Exception message: Connection reset)
2025-06-01 21:32:38.510 [AMQP Connection ************:5672] WARN  com.rabbitmq.client.impl.ForgivingExceptionHandler.log Line:115 - An unexpected connection driver error occured (Exception message: Connection reset)
2025-06-01 21:32:40.513 [AMQP Connection ************:5672] WARN  com.rabbitmq.client.impl.ForgivingExceptionHandler.log Line:115 - An unexpected connection driver error occured (Exception message: Connection reset)
2025-06-01 22:13:38.464 [AMQP Connection ************:5672] WARN  com.rabbitmq.client.impl.ForgivingExceptionHandler.log Line:115 - An unexpected connection driver error occured (Exception message: Connection reset)
2025-06-01 22:13:39.470 [AMQP Connection ************:5672] WARN  com.rabbitmq.client.impl.ForgivingExceptionHandler.log Line:115 - An unexpected connection driver error occured (Exception message: Connection reset)
2025-06-01 22:13:39.486 [AMQP Connection ************:5672] WARN  com.rabbitmq.client.impl.ForgivingExceptionHandler.log Line:115 - An unexpected connection driver error occured (Exception message: Connection reset)
2025-06-01 22:13:40.339 [lettuce-nioEventLoop-4-11] WARN  io.lettuce.core.protocol.ConnectionWatchdog.log Line:153 - Cannot reconnect to [************:7004]: connection timed out: /************:7004
2025-06-01 22:13:41.487 [AMQP Connection ************:5672] WARN  com.rabbitmq.client.impl.ForgivingExceptionHandler.log Line:115 - An unexpected connection driver error occured (Exception message: Connection reset)
2025-06-01 22:54:14.193 [AMQP Connection ************:5672] WARN  com.rabbitmq.client.impl.ForgivingExceptionHandler.log Line:115 - An unexpected connection driver error occured (Exception message: Connection reset)
2025-06-01 22:54:14.209 [AMQP Connection ************:5672] WARN  com.rabbitmq.client.impl.ForgivingExceptionHandler.log Line:115 - An unexpected connection driver error occured (Exception message: Connection reset)
2025-06-01 22:54:14.209 [AMQP Connection ************:5672] WARN  com.rabbitmq.client.impl.ForgivingExceptionHandler.log Line:115 - An unexpected connection driver error occured (Exception message: Connection reset)
2025-06-01 22:54:17.203 [AMQP Connection ************:5672] WARN  com.rabbitmq.client.impl.ForgivingExceptionHandler.log Line:115 - An unexpected connection driver error occured (Exception message: Connection reset)
2025-06-01 23:34:44.180 [AMQP Connection ************:5672] WARN  com.rabbitmq.client.impl.ForgivingExceptionHandler.log Line:115 - An unexpected connection driver error occured (Exception message: Connection reset)
2025-06-01 23:34:44.180 [AMQP Connection ************:5672] WARN  com.rabbitmq.client.impl.ForgivingExceptionHandler.log Line:115 - An unexpected connection driver error occured (Exception message: Connection reset)
2025-06-01 23:34:44.180 [AMQP Connection ************:5672] WARN  com.rabbitmq.client.impl.ForgivingExceptionHandler.log Line:115 - An unexpected connection driver error occured (Exception message: Connection reset)
2025-06-01 23:34:47.176 [AMQP Connection ************:5672] WARN  com.rabbitmq.client.impl.ForgivingExceptionHandler.log Line:115 - An unexpected connection driver error occured (Exception message: Connection reset)
