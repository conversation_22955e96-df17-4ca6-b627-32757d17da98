package com.simbest.boot.hnjjwz.column.familyWind.model;/**
 * Created by KZH on 2019/8/5 17:34.
 */

import com.simbest.boot.base.annotations.EntityIdPrefix;
import com.simbest.boot.base.model.LogicModel;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.AllArgsConstructor;
import lombok.Data;
import lombok.EqualsAndHashCode;
import lombok.NoArgsConstructor;
import org.hibernate.annotations.GenericGenerator;

import javax.persistence.Column;
import javax.persistence.Entity;
import javax.persistence.GeneratedValue;
import javax.persistence.Id;

/**
 * <AUTHOR>
 * @create 2019-08-05 17:34
 * @desc
 **/
@Data
@EqualsAndHashCode(callSuper = true)
@NoArgsConstructor
@AllArgsConstructor
@Entity(name = "us_record")
@ApiModel(value = "记录人的投票记录")
public class Record extends LogicModel {
    @Id
    @Column(name = "id", length = 40)
    @GeneratedValue(generator = "snowFlakeId")
    @GenericGenerator(name = "snowFlakeId", strategy = "com.simbest.boot.util.distribution.id.SnowflakeId")
    @EntityIdPrefix(prefix = "RP") //主键前缀，此为可选项注解
    private String id;

    @Column(length = 40)
    @ApiModelProperty(value = "OA账号")
    private String userName;

    @Column(length = 40)
    @ApiModelProperty(value = "投票记录")
    private String onVoteId;
}
