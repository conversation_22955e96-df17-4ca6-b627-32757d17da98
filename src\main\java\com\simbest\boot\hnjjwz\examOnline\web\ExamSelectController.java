/*
 * 版权所有 © 北京晟壁科技有限公司 2008-2027。保留一切权利!
 */
package com.simbest.boot.hnjjwz.examOnline.web;

import com.simbest.boot.base.web.response.JsonResponse;
import com.simbest.boot.hnjjwz.examOnline.service.IExamSelectService;
import io.swagger.annotations.Api;
import io.swagger.annotations.ApiImplicitParam;
import io.swagger.annotations.ApiImplicitParams;
import io.swagger.annotations.ApiOperation;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.web.bind.annotation.*;

/**
 * <strong>Title : ExamSelectController</strong><br>
 * <strong>Description : 成绩查询控制器 </strong><br>
 * <strong>Create on : 2020/11/12</strong><br>
 * <strong>Modify on : 2020/11/12</strong><br>
 * <strong>Copyright SimBest(C) Ltd.</strong><br>
 *
 * <AUTHOR>
 * @version <strong>V1.0.0</strong><br>
 * <strong>修改历史:</strong><br>
 * 修改人 修改日期 修改描述<br>
 * -------------------------------------------<br>
 */
@Slf4j
@RestController
@RequestMapping (value = "/action/exam")
@Api(description = "成绩查询相关接口",tags={"成绩查询相关接口"})
public class ExamSelectController {

    @Autowired
    private IExamSelectService service;

    @ApiOperation(value = "成绩查询接口", notes = "成绩查询接口")
    @ApiImplicitParams({
            @ApiImplicitParam(name = "username", value = "账户", dataType = "String", paramType = "query"),
    })
    @RequestMapping(value = {"/findByExamInfoByUsername","/sso/findByExamInfoByUsername","/api/findByExamInfoByUsername"},method = RequestMethod.POST)
    public JsonResponse findByExamInfoByUsername( @RequestParam(required = false) String username){
        JsonResponse jsonResponse = service.findByExamInfoByUsername(username);
        return jsonResponse;
    }


    @ApiOperation(value = "获取考试汇总权限", notes = "获取考试汇总权限")
    @ApiImplicitParam(name = "examCode", value = "考试编码", dataType = "String", paramType = "query")
    @PostMapping(value = {"/findEffectiveExam", "/sso/findEffectiveExam", "/api/findEffectiveExam"})
    public JsonResponse findEffectiveExam(@RequestParam String examCode) {
        return service.findEffectiveExam(examCode);
    }
}
