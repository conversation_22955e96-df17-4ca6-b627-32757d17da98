2025-06-02 00:15:45.399 [AMQP Connection ************:5672] WARN  com.rabbitmq.client.impl.ForgivingExceptionHandler.log Line:115 - An unexpected connection driver error occured (Exception message: Connection reset)
2025-06-02 00:15:46.315 [AMQP Connection ************:5672] WARN  com.rabbitmq.client.impl.ForgivingExceptionHandler.log Line:115 - An unexpected connection driver error occured (Exception message: Connection reset)
2025-06-02 00:15:46.330 [AMQP Connection ************:5672] WARN  com.rabbitmq.client.impl.ForgivingExceptionHandler.log Line:115 - An unexpected connection driver error occured (Exception message: Connection reset)
2025-06-02 00:15:49.257 [AMQP Connection ************:5672] WARN  com.rabbitmq.client.impl.ForgivingExceptionHandler.log Line:115 - An unexpected connection driver error occured (Exception message: Connection reset)
2025-06-02 00:25:00.017 [pool-14-thread-10] WARN  com.simbest.boot.base.repository.CustomDynamicWhere.queryForList Line:50 - 自定义查询SQL输出为：【select * from US_TODO_MODEL t  where t.send_flag = 0 and t.work_flag = 0 and t.enabled =1】
2025-06-02 00:35:09.241 [pool-14-thread-3] WARN  com.simbest.boot.base.repository.CustomDynamicWhere.queryForList Line:50 - 自定义查询SQL输出为：【select * from US_TODO_MODEL t  where t.send_flag = 0 and t.work_flag = 1 and t.enabled =1】
2025-06-02 00:56:17.318 [AMQP Connection ************:5672] WARN  com.rabbitmq.client.impl.ForgivingExceptionHandler.log Line:115 - An unexpected connection driver error occured (Exception message: Connection reset)
2025-06-02 00:56:18.253 [AMQP Connection ************:5672] WARN  com.rabbitmq.client.impl.ForgivingExceptionHandler.log Line:115 - An unexpected connection driver error occured (Exception message: Connection reset)
2025-06-02 00:56:18.253 [AMQP Connection ************:5672] WARN  com.rabbitmq.client.impl.ForgivingExceptionHandler.log Line:115 - An unexpected connection driver error occured (Exception message: Connection reset)
2025-06-02 00:56:21.206 [AMQP Connection ************:5672] WARN  com.rabbitmq.client.impl.ForgivingExceptionHandler.log Line:115 - An unexpected connection driver error occured (Exception message: Connection reset)
2025-06-02 01:05:00.012 [pool-14-thread-3] WARN  com.simbest.boot.base.repository.CustomDynamicWhere.queryForList Line:50 - 自定义查询SQL输出为：【select * from US_TODO_MODEL t  where t.send_flag = 0 and t.work_flag = 0 and t.enabled =1】
2025-06-02 01:10:00.010 [pool-14-thread-2] WARN  com.simbest.boot.base.repository.CustomDynamicWhere.queryForList Line:50 - 自定义查询SQL输出为：【select * from US_TODO_MODEL t  where t.send_flag = 0 and t.work_flag = 0 and t.enabled =1】
2025-06-02 01:10:09.312 [pool-14-thread-6] WARN  com.simbest.boot.base.repository.CustomDynamicWhere.queryForList Line:50 - 自定义查询SQL输出为：【select * from US_TODO_MODEL t  where t.send_flag = 0 and t.work_flag = 1 and t.enabled =1】
2025-06-02 01:15:00.014 [pool-14-thread-6] WARN  com.simbest.boot.base.repository.CustomDynamicWhere.queryForList Line:50 - 自定义查询SQL输出为：【select * from US_TODO_MODEL t  where t.send_flag = 0 and t.work_flag = 0 and t.enabled =1】
2025-06-02 01:15:00.014 [pool-14-thread-3] WARN  com.simbest.boot.base.repository.CustomDynamicWhere.queryForList Line:50 - 自定义查询SQL输出为：【select * from US_TODO_MODEL t  where t.send_flag = 0 and t.work_flag = 1 and t.enabled =1】
2025-06-02 01:36:48.379 [AMQP Connection ************:5672] WARN  com.rabbitmq.client.impl.ForgivingExceptionHandler.log Line:115 - An unexpected connection driver error occured (Exception message: Connection reset)
2025-06-02 01:36:49.263 [AMQP Connection ************:5672] WARN  com.rabbitmq.client.impl.ForgivingExceptionHandler.log Line:115 - An unexpected connection driver error occured (Exception message: Connection reset)
2025-06-02 01:36:49.309 [AMQP Connection ************:5672] WARN  com.rabbitmq.client.impl.ForgivingExceptionHandler.log Line:115 - An unexpected connection driver error occured (Exception message: Connection reset)
2025-06-02 01:36:52.234 [AMQP Connection ************:5672] WARN  com.rabbitmq.client.impl.ForgivingExceptionHandler.log Line:115 - An unexpected connection driver error occured (Exception message: Connection reset)
2025-06-02 01:40:03.076 [pool-14-thread-2] WARN  com.simbest.boot.base.repository.CustomDynamicWhere.queryForList Line:50 - 自定义查询SQL输出为：【select * from US_TODO_MODEL t  where t.send_flag = 0 and t.work_flag = 0 and t.enabled =1】
2025-06-02 01:45:00.008 [pool-14-thread-10] WARN  com.simbest.boot.base.repository.CustomDynamicWhere.queryForList Line:50 - 自定义查询SQL输出为：【select * from US_TODO_MODEL t  where t.send_flag = 0 and t.work_flag = 0 and t.enabled =1】
2025-06-02 01:45:09.278 [pool-14-thread-1] WARN  com.simbest.boot.base.repository.CustomDynamicWhere.queryForList Line:50 - 自定义查询SQL输出为：【select * from US_TODO_MODEL t  where t.send_flag = 0 and t.work_flag = 1 and t.enabled =1】
2025-06-02 01:50:00.020 [pool-14-thread-7] WARN  com.simbest.boot.base.repository.CustomDynamicWhere.queryForList Line:50 - 自定义查询SQL输出为：【select * from US_TODO_MODEL t  where t.send_flag = 0 and t.work_flag = 1 and t.enabled =1】
2025-06-02 01:50:00.020 [pool-14-thread-3] WARN  com.simbest.boot.base.repository.CustomDynamicWhere.queryForList Line:50 - 自定义查询SQL输出为：【select * from US_TODO_MODEL t  where t.send_flag = 0 and t.work_flag = 0 and t.enabled =1】
2025-06-02 01:55:00.014 [pool-14-thread-4] WARN  com.simbest.boot.base.repository.CustomDynamicWhere.queryForList Line:50 - 自定义查询SQL输出为：【select * from US_TODO_MODEL t  where t.send_flag = 0 and t.work_flag = 1 and t.enabled =1】
2025-06-02 01:55:00.014 [pool-14-thread-10] WARN  com.simbest.boot.base.repository.CustomDynamicWhere.queryForList Line:50 - 自定义查询SQL输出为：【select * from US_TODO_MODEL t  where t.send_flag = 0 and t.work_flag = 0 and t.enabled =1】
2025-06-02 02:17:50.244 [AMQP Connection ************:5672] WARN  com.rabbitmq.client.impl.ForgivingExceptionHandler.log Line:115 - An unexpected connection driver error occured (Exception message: Connection reset)
2025-06-02 02:17:51.184 [AMQP Connection ************:5672] WARN  com.rabbitmq.client.impl.ForgivingExceptionHandler.log Line:115 - An unexpected connection driver error occured (Exception message: Connection reset)
2025-06-02 02:17:51.199 [AMQP Connection ************:5672] WARN  com.rabbitmq.client.impl.ForgivingExceptionHandler.log Line:115 - An unexpected connection driver error occured (Exception message: Connection reset)
2025-06-02 02:17:54.178 [AMQP Connection ************:5672] WARN  com.rabbitmq.client.impl.ForgivingExceptionHandler.log Line:115 - An unexpected connection driver error occured (Exception message: Connection reset)
2025-06-02 02:20:03.052 [pool-14-thread-3] WARN  com.simbest.boot.base.repository.CustomDynamicWhere.queryForList Line:50 - 自定义查询SQL输出为：【select * from US_TODO_MODEL t  where t.send_flag = 0 and t.work_flag = 0 and t.enabled =1】
2025-06-02 02:25:00.008 [pool-14-thread-2] WARN  com.simbest.boot.base.repository.CustomDynamicWhere.queryForList Line:50 - 自定义查询SQL输出为：【select * from US_TODO_MODEL t  where t.send_flag = 0 and t.work_flag = 0 and t.enabled =1】
2025-06-02 02:25:09.246 [pool-14-thread-4] WARN  com.simbest.boot.base.repository.CustomDynamicWhere.queryForList Line:50 - 自定义查询SQL输出为：【select * from US_TODO_MODEL t  where t.send_flag = 0 and t.work_flag = 1 and t.enabled =1】
2025-06-02 02:30:00.010 [pool-14-thread-8] WARN  com.simbest.boot.base.repository.CustomDynamicWhere.queryForList Line:50 - 自定义查询SQL输出为：【select * from US_TODO_MODEL t  where t.send_flag = 0 and t.work_flag = 1 and t.enabled =1】
2025-06-02 02:30:00.014 [pool-14-thread-3] WARN  com.simbest.boot.base.repository.CustomDynamicWhere.queryForList Line:50 - 自定义查询SQL输出为：【select * from US_TODO_MODEL t  where t.send_flag = 0 and t.work_flag = 0 and t.enabled =1】
2025-06-02 02:35:00.008 [pool-14-thread-5] WARN  com.simbest.boot.base.repository.CustomDynamicWhere.queryForList Line:50 - 自定义查询SQL输出为：【select * from US_TODO_MODEL t  where t.send_flag = 0 and t.work_flag = 1 and t.enabled =1】
2025-06-02 02:35:00.009 [pool-14-thread-9] WARN  com.simbest.boot.base.repository.CustomDynamicWhere.queryForList Line:50 - 自定义查询SQL输出为：【select * from US_TODO_MODEL t  where t.send_flag = 0 and t.work_flag = 0 and t.enabled =1】
2025-06-02 02:58:21.079 [AMQP Connection ************:5672] WARN  com.rabbitmq.client.impl.ForgivingExceptionHandler.log Line:115 - An unexpected connection driver error occured (Exception message: Connection reset)
2025-06-02 02:58:22.077 [AMQP Connection ************:5672] WARN  com.rabbitmq.client.impl.ForgivingExceptionHandler.log Line:115 - An unexpected connection driver error occured (Exception message: Connection reset)
2025-06-02 02:58:22.092 [AMQP Connection ************:5672] WARN  com.rabbitmq.client.impl.ForgivingExceptionHandler.log Line:115 - An unexpected connection driver error occured (Exception message: Connection reset)
2025-06-02 02:58:25.058 [AMQP Connection ************:5672] WARN  com.rabbitmq.client.impl.ForgivingExceptionHandler.log Line:115 - An unexpected connection driver error occured (Exception message: Connection reset)
2025-06-02 03:05:00.013 [pool-14-thread-9] WARN  com.simbest.boot.base.repository.CustomDynamicWhere.queryForList Line:50 - 自定义查询SQL输出为：【select * from US_TODO_MODEL t  where t.send_flag = 0 and t.work_flag = 0 and t.enabled =1】
2025-06-02 03:05:09.319 [pool-14-thread-4] WARN  com.simbest.boot.base.repository.CustomDynamicWhere.queryForList Line:50 - 自定义查询SQL输出为：【select * from US_TODO_MODEL t  where t.send_flag = 0 and t.work_flag = 1 and t.enabled =1】
2025-06-02 03:10:00.011 [pool-14-thread-10] WARN  com.simbest.boot.base.repository.CustomDynamicWhere.queryForList Line:50 - 自定义查询SQL输出为：【select * from US_TODO_MODEL t  where t.send_flag = 0 and t.work_flag = 0 and t.enabled =1】
2025-06-02 03:10:00.014 [pool-14-thread-8] WARN  com.simbest.boot.base.repository.CustomDynamicWhere.queryForList Line:50 - 自定义查询SQL输出为：【select * from US_TODO_MODEL t  where t.send_flag = 0 and t.work_flag = 1 and t.enabled =1】
2025-06-02 03:15:00.013 [pool-14-thread-5] WARN  com.simbest.boot.base.repository.CustomDynamicWhere.queryForList Line:50 - 自定义查询SQL输出为：【select * from US_TODO_MODEL t  where t.send_flag = 0 and t.work_flag = 1 and t.enabled =1】
2025-06-02 03:15:00.013 [pool-14-thread-3] WARN  com.simbest.boot.base.repository.CustomDynamicWhere.queryForList Line:50 - 自定义查询SQL输出为：【select * from US_TODO_MODEL t  where t.send_flag = 0 and t.work_flag = 0 and t.enabled =1】
2025-06-02 03:38:52.072 [AMQP Connection ************:5672] WARN  com.rabbitmq.client.impl.ForgivingExceptionHandler.log Line:115 - An unexpected connection driver error occured (Exception message: Connection reset)
2025-06-02 03:38:52.957 [AMQP Connection ************:5672] WARN  com.rabbitmq.client.impl.ForgivingExceptionHandler.log Line:115 - An unexpected connection driver error occured (Exception message: Connection reset)
2025-06-02 03:38:53.050 [AMQP Connection ************:5672] WARN  com.rabbitmq.client.impl.ForgivingExceptionHandler.log Line:115 - An unexpected connection driver error occured (Exception message: Connection reset)
2025-06-02 03:38:56.052 [AMQP Connection ************:5672] WARN  com.rabbitmq.client.impl.ForgivingExceptionHandler.log Line:115 - An unexpected connection driver error occured (Exception message: Connection reset)
2025-06-02 03:45:00.020 [pool-14-thread-4] WARN  com.simbest.boot.base.repository.CustomDynamicWhere.queryForList Line:50 - 自定义查询SQL输出为：【select * from US_TODO_MODEL t  where t.send_flag = 0 and t.work_flag = 0 and t.enabled =1】
2025-06-02 03:45:09.287 [pool-14-thread-8] WARN  com.simbest.boot.base.repository.CustomDynamicWhere.queryForList Line:50 - 自定义查询SQL输出为：【select * from US_TODO_MODEL t  where t.send_flag = 0 and t.work_flag = 1 and t.enabled =1】
2025-06-02 03:50:00.019 [pool-14-thread-6] WARN  com.simbest.boot.base.repository.CustomDynamicWhere.queryForList Line:50 - 自定义查询SQL输出为：【select * from US_TODO_MODEL t  where t.send_flag = 0 and t.work_flag = 0 and t.enabled =1】
2025-06-02 03:50:00.020 [pool-14-thread-8] WARN  com.simbest.boot.base.repository.CustomDynamicWhere.queryForList Line:50 - 自定义查询SQL输出为：【select * from US_TODO_MODEL t  where t.send_flag = 0 and t.work_flag = 1 and t.enabled =1】
2025-06-02 03:55:00.017 [pool-14-thread-10] WARN  com.simbest.boot.base.repository.CustomDynamicWhere.queryForList Line:50 - 自定义查询SQL输出为：【select * from US_TODO_MODEL t  where t.send_flag = 0 and t.work_flag = 1 and t.enabled =1】
2025-06-02 03:55:00.017 [pool-14-thread-6] WARN  com.simbest.boot.base.repository.CustomDynamicWhere.queryForList Line:50 - 自定义查询SQL输出为：【select * from US_TODO_MODEL t  where t.send_flag = 0 and t.work_flag = 0 and t.enabled =1】
2025-06-02 04:19:53.032 [AMQP Connection ************:5672] WARN  com.rabbitmq.client.impl.ForgivingExceptionHandler.log Line:115 - An unexpected connection driver error occured (Exception message: Connection reset)
2025-06-02 04:19:53.032 [AMQP Connection ************:5672] WARN  com.rabbitmq.client.impl.ForgivingExceptionHandler.log Line:115 - An unexpected connection driver error occured (Exception message: Connection reset)
2025-06-02 04:19:54.018 [AMQP Connection ************:5672] WARN  com.rabbitmq.client.impl.ForgivingExceptionHandler.log Line:115 - An unexpected connection driver error occured (Exception message: Connection reset)
2025-06-02 04:25:00.013 [pool-14-thread-2] WARN  com.simbest.boot.base.repository.CustomDynamicWhere.queryForList Line:50 - 自定义查询SQL输出为：【select * from US_TODO_MODEL t  where t.send_flag = 0 and t.work_flag = 0 and t.enabled =1】
2025-06-02 04:25:09.253 [pool-14-thread-8] WARN  com.simbest.boot.base.repository.CustomDynamicWhere.queryForList Line:50 - 自定义查询SQL输出为：【select * from US_TODO_MODEL t  where t.send_flag = 0 and t.work_flag = 1 and t.enabled =1】
2025-06-02 04:30:00.014 [pool-14-thread-8] WARN  com.simbest.boot.base.repository.CustomDynamicWhere.queryForList Line:50 - 自定义查询SQL输出为：【select * from US_TODO_MODEL t  where t.send_flag = 0 and t.work_flag = 1 and t.enabled =1】
2025-06-02 04:30:00.018 [pool-14-thread-10] WARN  com.simbest.boot.base.repository.CustomDynamicWhere.queryForList Line:50 - 自定义查询SQL输出为：【select * from US_TODO_MODEL t  where t.send_flag = 0 and t.work_flag = 0 and t.enabled =1】
2025-06-02 04:35:00.008 [pool-14-thread-1] WARN  com.simbest.boot.base.repository.CustomDynamicWhere.queryForList Line:50 - 自定义查询SQL输出为：【select * from US_TODO_MODEL t  where t.send_flag = 0 and t.work_flag = 0 and t.enabled =1】
2025-06-02 04:35:00.008 [pool-14-thread-4] WARN  com.simbest.boot.base.repository.CustomDynamicWhere.queryForList Line:50 - 自定义查询SQL输出为：【select * from US_TODO_MODEL t  where t.send_flag = 0 and t.work_flag = 1 and t.enabled =1】
2025-06-02 04:59:53.276 [lettuce-nioEventLoop-4-5] WARN  io.lettuce.core.cluster.topology.ClusterTopologyRefresh.lambda$getConnections$0 Line:284 - Unable to connect to [************:7001]: No route to host: no further information: /************:7001
2025-06-02 04:59:53.277 [lettuce-nioEventLoop-4-6] WARN  io.lettuce.core.cluster.topology.ClusterTopologyRefresh.lambda$getConnections$0 Line:284 - Unable to connect to [************:7002]: No route to host: no further information: /************:7002
2025-06-02 04:59:53.279 [lettuce-nioEventLoop-4-7] WARN  io.lettuce.core.cluster.topology.ClusterTopologyRefresh.lambda$getConnections$0 Line:284 - Unable to connect to [************:7003]: No route to host: no further information: /************:7003
2025-06-02 04:59:53.279 [lettuce-nioEventLoop-4-8] WARN  io.lettuce.core.cluster.topology.ClusterTopologyRefresh.lambda$getConnections$0 Line:284 - Unable to connect to [************:7004]: No route to host: no further information: /************:7004
2025-06-02 04:59:53.279 [lettuce-nioEventLoop-4-9] WARN  io.lettuce.core.cluster.topology.ClusterTopologyRefresh.lambda$getConnections$0 Line:284 - Unable to connect to [************:7005]: No route to host: no further information: /************:7005
2025-06-02 04:59:53.279 [lettuce-nioEventLoop-4-10] WARN  io.lettuce.core.cluster.topology.ClusterTopologyRefresh.lambda$getConnections$0 Line:284 - Unable to connect to [************:7006]: No route to host: no further information: /************:7006
2025-06-02 04:59:53.280 [lettuce-nioEventLoop-4-11] WARN  io.lettuce.core.cluster.topology.ClusterTopologyRefresh.lambda$getConnections$0 Line:284 - Unable to connect to [************:7006]: No route to host: no further information: /************:7006
2025-06-02 04:59:53.280 [lettuce-nioEventLoop-4-2] WARN  io.lettuce.core.cluster.topology.ClusterTopologyRefresh.lambda$getConnections$0 Line:284 - Unable to connect to [************:7002]: No route to host: no further information: /************:7002
2025-06-02 04:59:53.280 [lettuce-nioEventLoop-4-1] WARN  io.lettuce.core.cluster.topology.ClusterTopologyRefresh.lambda$getConnections$0 Line:284 - Unable to connect to [************:7005]: No route to host: no further information: /************:7005
2025-06-02 04:59:53.280 [lettuce-nioEventLoop-4-3] WARN  io.lettuce.core.cluster.topology.ClusterTopologyRefresh.lambda$getConnections$0 Line:284 - Unable to connect to [************:7003]: No route to host: no further information: /************:7003
2025-06-02 04:59:53.280 [lettuce-nioEventLoop-4-4] WARN  io.lettuce.core.cluster.topology.ClusterTopologyRefresh.lambda$getConnections$0 Line:284 - Unable to connect to [************:7001]: No route to host: no further information: /************:7001
2025-06-02 04:59:53.302 [lettuce-nioEventLoop-4-12] WARN  io.lettuce.core.cluster.topology.ClusterTopologyRefresh.lambda$getConnections$0 Line:284 - Unable to connect to [************:7004]: No route to host: no further information: /************:7004
2025-06-02 04:59:53.320 [lettuce-eventExecutorLoop-1-10] WARN  io.lettuce.core.cluster.ClusterTopologyRefreshScheduler.doRun Line:258 - Cannot refresh Redis Cluster topology
io.lettuce.core.RedisException: Cannot retrieve initial cluster partitions from initial URIs [RedisURI [host='************', port=7006], RedisURI [host='************', port=7004], RedisURI [host='************', port=7005], RedisURI [host='************', port=7002], RedisURI [host='************', port=7003], RedisURI [host='************', port=7001]]
	at io.lettuce.core.cluster.RedisClusterClient.loadPartitions(RedisClusterClient.java:848)
	at io.lettuce.core.cluster.RedisClusterClient.reloadPartitions(RedisClusterClient.java:784)
	at io.lettuce.core.cluster.ClusterTopologyRefreshScheduler$ClusterTopologyRefreshTask.doRun(ClusterTopologyRefreshScheduler.java:256)
	at io.lettuce.core.cluster.ClusterTopologyRefreshScheduler$ClusterTopologyRefreshTask.run(ClusterTopologyRefreshScheduler.java:236)
	at io.netty.util.concurrent.PromiseTask.runTask(PromiseTask.java:98)
	at io.netty.util.concurrent.PromiseTask.run(PromiseTask.java:106)
	at io.netty.util.concurrent.DefaultEventExecutor.run(DefaultEventExecutor.java:66)
	at io.netty.util.concurrent.SingleThreadEventExecutor$6.run(SingleThreadEventExecutor.java:1050)
	at io.netty.util.internal.ThreadExecutorMap$2.run(ThreadExecutorMap.java:74)
	at io.netty.util.concurrent.FastThreadLocalRunnable.run(FastThreadLocalRunnable.java:30)
	at java.lang.Thread.run(Thread.java:748)
	Suppressed: io.lettuce.core.RedisConnectionException: Unable to establish a connection to Redis Cluster at [RedisURI [host='************', port=7001], RedisURI [host='************', port=7002], RedisURI [host='************', port=7003], RedisURI [host='************', port=7004], RedisURI [host='************', port=7005], RedisURI [host='************', port=7006]]
		at io.lettuce.core.cluster.topology.AsyncConnections.get(AsyncConnections.java:89)
		at io.lettuce.core.cluster.topology.ClusterTopologyRefresh.loadViews(ClusterTopologyRefresh.java:76)
		at io.lettuce.core.cluster.RedisClusterClient.doLoadPartitions(RedisClusterClient.java:865)
		at io.lettuce.core.cluster.RedisClusterClient.loadPartitions(RedisClusterClient.java:838)
		... 10 common frames omitted
		Suppressed: io.lettuce.core.RedisConnectionException: Unable to connect to [************:7002]: No route to host: no further information: /************:7002
			at io.lettuce.core.cluster.topology.ClusterTopologyRefresh.lambda$getConnections$0(ClusterTopologyRefresh.java:290)
			at java.util.concurrent.CompletableFuture.uniWhenComplete(CompletableFuture.java:760)
			at java.util.concurrent.CompletableFuture$UniWhenComplete.tryFire(CompletableFuture.java:736)
			at java.util.concurrent.CompletableFuture.postComplete(CompletableFuture.java:474)
			at java.util.concurrent.CompletableFuture.completeExceptionally(CompletableFuture.java:1977)
			at io.lettuce.core.AbstractRedisClient.lambda$initializeChannelAsync0$4(AbstractRedisClient.java:329)
			at io.netty.util.concurrent.DefaultPromise.notifyListener0(DefaultPromise.java:577)
			at io.netty.util.concurrent.DefaultPromise.notifyListeners0(DefaultPromise.java:570)
			at io.netty.util.concurrent.DefaultPromise.notifyListenersNow(DefaultPromise.java:549)
			at io.netty.util.concurrent.DefaultPromise.notifyListeners(DefaultPromise.java:490)
			at io.netty.util.concurrent.DefaultPromise.setValue0(DefaultPromise.java:615)
			at io.netty.util.concurrent.DefaultPromise.setFailure0(DefaultPromise.java:608)
			at io.netty.util.concurrent.DefaultPromise.tryFailure(DefaultPromise.java:117)
			at io.netty.channel.nio.AbstractNioChannel$AbstractNioUnsafe.fulfillConnectPromise(AbstractNioChannel.java:321)
			at io.netty.channel.nio.AbstractNioChannel$AbstractNioUnsafe.finishConnect(AbstractNioChannel.java:337)
			at io.netty.channel.nio.NioEventLoop.processSelectedKey(NioEventLoop.java:688)
			at io.netty.channel.nio.NioEventLoop.processSelectedKeysOptimized(NioEventLoop.java:635)
			at io.netty.channel.nio.NioEventLoop.processSelectedKeys(NioEventLoop.java:552)
			at io.netty.channel.nio.NioEventLoop.run(NioEventLoop.java:514)
			... 4 common frames omitted
		Caused by: io.netty.channel.AbstractChannel$AnnotatedNoRouteToHostException: No route to host: no further information: /************:7002
		Caused by: java.net.NoRouteToHostException: No route to host: no further information
			at sun.nio.ch.SocketChannelImpl.checkConnect(Native Method)
			at sun.nio.ch.SocketChannelImpl.finishConnect(SocketChannelImpl.java:717)
			at io.netty.channel.socket.nio.NioSocketChannel.doFinishConnect(NioSocketChannel.java:330)
			at io.netty.channel.nio.AbstractNioChannel$AbstractNioUnsafe.finishConnect(AbstractNioChannel.java:334)
			at io.netty.channel.nio.NioEventLoop.processSelectedKey(NioEventLoop.java:688)
			at io.netty.channel.nio.NioEventLoop.processSelectedKeysOptimized(NioEventLoop.java:635)
			at io.netty.channel.nio.NioEventLoop.processSelectedKeys(NioEventLoop.java:552)
			at io.netty.channel.nio.NioEventLoop.run(NioEventLoop.java:514)
			at io.netty.util.concurrent.SingleThreadEventExecutor$6.run(SingleThreadEventExecutor.java:1050)
			at io.netty.util.internal.ThreadExecutorMap$2.run(ThreadExecutorMap.java:74)
			at io.netty.util.concurrent.FastThreadLocalRunnable.run(FastThreadLocalRunnable.java:30)
			at java.lang.Thread.run(Thread.java:748)
		Suppressed: io.lettuce.core.RedisConnectionException: Unable to connect to [************:7003]: No route to host: no further information: /************:7003
			at io.lettuce.core.cluster.topology.ClusterTopologyRefresh.lambda$getConnections$0(ClusterTopologyRefresh.java:290)
			at java.util.concurrent.CompletableFuture.uniWhenComplete(CompletableFuture.java:760)
			at java.util.concurrent.CompletableFuture$UniWhenComplete.tryFire(CompletableFuture.java:736)
			at java.util.concurrent.CompletableFuture.postComplete(CompletableFuture.java:474)
			at java.util.concurrent.CompletableFuture.completeExceptionally(CompletableFuture.java:1977)
			at io.lettuce.core.AbstractRedisClient.lambda$initializeChannelAsync0$4(AbstractRedisClient.java:329)
			at io.netty.util.concurrent.DefaultPromise.notifyListener0(DefaultPromise.java:577)
			at io.netty.util.concurrent.DefaultPromise.notifyListeners0(DefaultPromise.java:570)
			at io.netty.util.concurrent.DefaultPromise.notifyListenersNow(DefaultPromise.java:549)
			at io.netty.util.concurrent.DefaultPromise.notifyListeners(DefaultPromise.java:490)
			at io.netty.util.concurrent.DefaultPromise.setValue0(DefaultPromise.java:615)
			at io.netty.util.concurrent.DefaultPromise.setFailure0(DefaultPromise.java:608)
			at io.netty.util.concurrent.DefaultPromise.tryFailure(DefaultPromise.java:117)
			at io.netty.channel.nio.AbstractNioChannel$AbstractNioUnsafe.fulfillConnectPromise(AbstractNioChannel.java:321)
			at io.netty.channel.nio.AbstractNioChannel$AbstractNioUnsafe.finishConnect(AbstractNioChannel.java:337)
			at io.netty.channel.nio.NioEventLoop.processSelectedKey(NioEventLoop.java:688)
			at io.netty.channel.nio.NioEventLoop.processSelectedKeysOptimized(NioEventLoop.java:635)
			at io.netty.channel.nio.NioEventLoop.processSelectedKeys(NioEventLoop.java:552)
			at io.netty.channel.nio.NioEventLoop.run(NioEventLoop.java:514)
			... 4 common frames omitted
		Caused by: io.netty.channel.AbstractChannel$AnnotatedNoRouteToHostException: No route to host: no further information: /************:7003
		Caused by: java.net.NoRouteToHostException: No route to host: no further information
			at sun.nio.ch.SocketChannelImpl.checkConnect(Native Method)
			at sun.nio.ch.SocketChannelImpl.finishConnect(SocketChannelImpl.java:717)
			at io.netty.channel.socket.nio.NioSocketChannel.doFinishConnect(NioSocketChannel.java:330)
			at io.netty.channel.nio.AbstractNioChannel$AbstractNioUnsafe.finishConnect(AbstractNioChannel.java:334)
			at io.netty.channel.nio.NioEventLoop.processSelectedKey(NioEventLoop.java:688)
			at io.netty.channel.nio.NioEventLoop.processSelectedKeysOptimized(NioEventLoop.java:635)
			at io.netty.channel.nio.NioEventLoop.processSelectedKeys(NioEventLoop.java:552)
			at io.netty.channel.nio.NioEventLoop.run(NioEventLoop.java:514)
			at io.netty.util.concurrent.SingleThreadEventExecutor$6.run(SingleThreadEventExecutor.java:1050)
			at io.netty.util.internal.ThreadExecutorMap$2.run(ThreadExecutorMap.java:74)
			at io.netty.util.concurrent.FastThreadLocalRunnable.run(FastThreadLocalRunnable.java:30)
			at java.lang.Thread.run(Thread.java:748)
		Suppressed: io.lettuce.core.RedisConnectionException: Unable to connect to [************:7004]: No route to host: no further information: /************:7004
			at io.lettuce.core.cluster.topology.ClusterTopologyRefresh.lambda$getConnections$0(ClusterTopologyRefresh.java:290)
			at java.util.concurrent.CompletableFuture.uniWhenComplete(CompletableFuture.java:760)
			at java.util.concurrent.CompletableFuture$UniWhenComplete.tryFire(CompletableFuture.java:736)
			at java.util.concurrent.CompletableFuture.postComplete(CompletableFuture.java:474)
			at java.util.concurrent.CompletableFuture.completeExceptionally(CompletableFuture.java:1977)
			at io.lettuce.core.AbstractRedisClient.lambda$initializeChannelAsync0$4(AbstractRedisClient.java:329)
			at io.netty.util.concurrent.DefaultPromise.notifyListener0(DefaultPromise.java:577)
			at io.netty.util.concurrent.DefaultPromise.notifyListeners0(DefaultPromise.java:570)
			at io.netty.util.concurrent.DefaultPromise.notifyListenersNow(DefaultPromise.java:549)
			at io.netty.util.concurrent.DefaultPromise.notifyListeners(DefaultPromise.java:490)
			at io.netty.util.concurrent.DefaultPromise.setValue0(DefaultPromise.java:615)
			at io.netty.util.concurrent.DefaultPromise.setFailure0(DefaultPromise.java:608)
			at io.netty.util.concurrent.DefaultPromise.tryFailure(DefaultPromise.java:117)
			at io.netty.channel.nio.AbstractNioChannel$AbstractNioUnsafe.fulfillConnectPromise(AbstractNioChannel.java:321)
			at io.netty.channel.nio.AbstractNioChannel$AbstractNioUnsafe.finishConnect(AbstractNioChannel.java:337)
			at io.netty.channel.nio.NioEventLoop.processSelectedKey(NioEventLoop.java:688)
			at io.netty.channel.nio.NioEventLoop.processSelectedKeysOptimized(NioEventLoop.java:635)
			at io.netty.channel.nio.NioEventLoop.processSelectedKeys(NioEventLoop.java:552)
			at io.netty.channel.nio.NioEventLoop.run(NioEventLoop.java:514)
			... 4 common frames omitted
		Caused by: io.netty.channel.AbstractChannel$AnnotatedNoRouteToHostException: No route to host: no further information: /************:7004
		Caused by: java.net.NoRouteToHostException: No route to host: no further information
			at sun.nio.ch.SocketChannelImpl.checkConnect(Native Method)
			at sun.nio.ch.SocketChannelImpl.finishConnect(SocketChannelImpl.java:717)
			at io.netty.channel.socket.nio.NioSocketChannel.doFinishConnect(NioSocketChannel.java:330)
			at io.netty.channel.nio.AbstractNioChannel$AbstractNioUnsafe.finishConnect(AbstractNioChannel.java:334)
			at io.netty.channel.nio.NioEventLoop.processSelectedKey(NioEventLoop.java:688)
			at io.netty.channel.nio.NioEventLoop.processSelectedKeysOptimized(NioEventLoop.java:635)
			at io.netty.channel.nio.NioEventLoop.processSelectedKeys(NioEventLoop.java:552)
			at io.netty.channel.nio.NioEventLoop.run(NioEventLoop.java:514)
			at io.netty.util.concurrent.SingleThreadEventExecutor$6.run(SingleThreadEventExecutor.java:1050)
			at io.netty.util.internal.ThreadExecutorMap$2.run(ThreadExecutorMap.java:74)
			at io.netty.util.concurrent.FastThreadLocalRunnable.run(FastThreadLocalRunnable.java:30)
			at java.lang.Thread.run(Thread.java:748)
		Suppressed: io.lettuce.core.RedisConnectionException: Unable to connect to [************:7005]: No route to host: no further information: /************:7005
			at io.lettuce.core.cluster.topology.ClusterTopologyRefresh.lambda$getConnections$0(ClusterTopologyRefresh.java:290)
			at java.util.concurrent.CompletableFuture.uniWhenComplete(CompletableFuture.java:760)
			at java.util.concurrent.CompletableFuture$UniWhenComplete.tryFire(CompletableFuture.java:736)
			at java.util.concurrent.CompletableFuture.postComplete(CompletableFuture.java:474)
			at java.util.concurrent.CompletableFuture.completeExceptionally(CompletableFuture.java:1977)
			at io.lettuce.core.AbstractRedisClient.lambda$initializeChannelAsync0$4(AbstractRedisClient.java:329)
			at io.netty.util.concurrent.DefaultPromise.notifyListener0(DefaultPromise.java:577)
			at io.netty.util.concurrent.DefaultPromise.notifyListeners0(DefaultPromise.java:570)
			at io.netty.util.concurrent.DefaultPromise.notifyListenersNow(DefaultPromise.java:549)
			at io.netty.util.concurrent.DefaultPromise.notifyListeners(DefaultPromise.java:490)
			at io.netty.util.concurrent.DefaultPromise.setValue0(DefaultPromise.java:615)
			at io.netty.util.concurrent.DefaultPromise.setFailure0(DefaultPromise.java:608)
			at io.netty.util.concurrent.DefaultPromise.tryFailure(DefaultPromise.java:117)
			at io.netty.channel.nio.AbstractNioChannel$AbstractNioUnsafe.fulfillConnectPromise(AbstractNioChannel.java:321)
			at io.netty.channel.nio.AbstractNioChannel$AbstractNioUnsafe.finishConnect(AbstractNioChannel.java:337)
			at io.netty.channel.nio.NioEventLoop.processSelectedKey(NioEventLoop.java:688)
			at io.netty.channel.nio.NioEventLoop.processSelectedKeysOptimized(NioEventLoop.java:635)
			at io.netty.channel.nio.NioEventLoop.processSelectedKeys(NioEventLoop.java:552)
			at io.netty.channel.nio.NioEventLoop.run(NioEventLoop.java:514)
			... 4 common frames omitted
		Caused by: io.netty.channel.AbstractChannel$AnnotatedNoRouteToHostException: No route to host: no further information: /************:7005
		Caused by: java.net.NoRouteToHostException: No route to host: no further information
			at sun.nio.ch.SocketChannelImpl.checkConnect(Native Method)
			at sun.nio.ch.SocketChannelImpl.finishConnect(SocketChannelImpl.java:717)
			at io.netty.channel.socket.nio.NioSocketChannel.doFinishConnect(NioSocketChannel.java:330)
			at io.netty.channel.nio.AbstractNioChannel$AbstractNioUnsafe.finishConnect(AbstractNioChannel.java:334)
			at io.netty.channel.nio.NioEventLoop.processSelectedKey(NioEventLoop.java:688)
			at io.netty.channel.nio.NioEventLoop.processSelectedKeysOptimized(NioEventLoop.java:635)
			at io.netty.channel.nio.NioEventLoop.processSelectedKeys(NioEventLoop.java:552)
			at io.netty.channel.nio.NioEventLoop.run(NioEventLoop.java:514)
			at io.netty.util.concurrent.SingleThreadEventExecutor$6.run(SingleThreadEventExecutor.java:1050)
			at io.netty.util.internal.ThreadExecutorMap$2.run(ThreadExecutorMap.java:74)
			at io.netty.util.concurrent.FastThreadLocalRunnable.run(FastThreadLocalRunnable.java:30)
			at java.lang.Thread.run(Thread.java:748)
		Suppressed: io.lettuce.core.RedisConnectionException: Unable to connect to [************:7006]: No route to host: no further information: /************:7006
			at io.lettuce.core.cluster.topology.ClusterTopologyRefresh.lambda$getConnections$0(ClusterTopologyRefresh.java:290)
			at java.util.concurrent.CompletableFuture.uniWhenComplete(CompletableFuture.java:760)
			at java.util.concurrent.CompletableFuture$UniWhenComplete.tryFire(CompletableFuture.java:736)
			at java.util.concurrent.CompletableFuture.postComplete(CompletableFuture.java:474)
			at java.util.concurrent.CompletableFuture.completeExceptionally(CompletableFuture.java:1977)
			at io.lettuce.core.AbstractRedisClient.lambda$initializeChannelAsync0$4(AbstractRedisClient.java:329)
			at io.netty.util.concurrent.DefaultPromise.notifyListener0(DefaultPromise.java:577)
			at io.netty.util.concurrent.DefaultPromise.notifyListeners0(DefaultPromise.java:570)
			at io.netty.util.concurrent.DefaultPromise.notifyListenersNow(DefaultPromise.java:549)
			at io.netty.util.concurrent.DefaultPromise.notifyListeners(DefaultPromise.java:490)
			at io.netty.util.concurrent.DefaultPromise.setValue0(DefaultPromise.java:615)
			at io.netty.util.concurrent.DefaultPromise.setFailure0(DefaultPromise.java:608)
			at io.netty.util.concurrent.DefaultPromise.tryFailure(DefaultPromise.java:117)
			at io.netty.channel.nio.AbstractNioChannel$AbstractNioUnsafe.fulfillConnectPromise(AbstractNioChannel.java:321)
			at io.netty.channel.nio.AbstractNioChannel$AbstractNioUnsafe.finishConnect(AbstractNioChannel.java:337)
			at io.netty.channel.nio.NioEventLoop.processSelectedKey(NioEventLoop.java:688)
			at io.netty.channel.nio.NioEventLoop.processSelectedKeysOptimized(NioEventLoop.java:635)
			at io.netty.channel.nio.NioEventLoop.processSelectedKeys(NioEventLoop.java:552)
			at io.netty.channel.nio.NioEventLoop.run(NioEventLoop.java:514)
			... 4 common frames omitted
		Caused by: io.netty.channel.AbstractChannel$AnnotatedNoRouteToHostException: No route to host: no further information: /************:7006
		Caused by: java.net.NoRouteToHostException: No route to host: no further information
			at sun.nio.ch.SocketChannelImpl.checkConnect(Native Method)
			at sun.nio.ch.SocketChannelImpl.finishConnect(SocketChannelImpl.java:717)
			at io.netty.channel.socket.nio.NioSocketChannel.doFinishConnect(NioSocketChannel.java:330)
			at io.netty.channel.nio.AbstractNioChannel$AbstractNioUnsafe.finishConnect(AbstractNioChannel.java:334)
			at io.netty.channel.nio.NioEventLoop.processSelectedKey(NioEventLoop.java:688)
			at io.netty.channel.nio.NioEventLoop.processSelectedKeysOptimized(NioEventLoop.java:635)
			at io.netty.channel.nio.NioEventLoop.processSelectedKeys(NioEventLoop.java:552)
			at io.netty.channel.nio.NioEventLoop.run(NioEventLoop.java:514)
			at io.netty.util.concurrent.SingleThreadEventExecutor$6.run(SingleThreadEventExecutor.java:1050)
			at io.netty.util.internal.ThreadExecutorMap$2.run(ThreadExecutorMap.java:74)
			at io.netty.util.concurrent.FastThreadLocalRunnable.run(FastThreadLocalRunnable.java:30)
			at java.lang.Thread.run(Thread.java:748)
	Caused by: io.lettuce.core.RedisConnectionException: Unable to connect to [************:7001]: No route to host: no further information: /************:7001
		at io.lettuce.core.cluster.topology.ClusterTopologyRefresh.lambda$getConnections$0(ClusterTopologyRefresh.java:290)
		at java.util.concurrent.CompletableFuture.uniWhenComplete(CompletableFuture.java:760)
		at java.util.concurrent.CompletableFuture$UniWhenComplete.tryFire(CompletableFuture.java:736)
		at java.util.concurrent.CompletableFuture.postComplete(CompletableFuture.java:474)
		at java.util.concurrent.CompletableFuture.completeExceptionally(CompletableFuture.java:1977)
		at io.lettuce.core.AbstractRedisClient.lambda$initializeChannelAsync0$4(AbstractRedisClient.java:329)
		at io.netty.util.concurrent.DefaultPromise.notifyListener0(DefaultPromise.java:577)
		at io.netty.util.concurrent.DefaultPromise.notifyListeners0(DefaultPromise.java:570)
		at io.netty.util.concurrent.DefaultPromise.notifyListenersNow(DefaultPromise.java:549)
		at io.netty.util.concurrent.DefaultPromise.notifyListeners(DefaultPromise.java:490)
		at io.netty.util.concurrent.DefaultPromise.setValue0(DefaultPromise.java:615)
		at io.netty.util.concurrent.DefaultPromise.setFailure0(DefaultPromise.java:608)
		at io.netty.util.concurrent.DefaultPromise.tryFailure(DefaultPromise.java:117)
		at io.netty.channel.nio.AbstractNioChannel$AbstractNioUnsafe.fulfillConnectPromise(AbstractNioChannel.java:321)
		at io.netty.channel.nio.AbstractNioChannel$AbstractNioUnsafe.finishConnect(AbstractNioChannel.java:337)
		at io.netty.channel.nio.NioEventLoop.processSelectedKey(NioEventLoop.java:688)
		at io.netty.channel.nio.NioEventLoop.processSelectedKeysOptimized(NioEventLoop.java:635)
		at io.netty.channel.nio.NioEventLoop.processSelectedKeys(NioEventLoop.java:552)
		at io.netty.channel.nio.NioEventLoop.run(NioEventLoop.java:514)
		... 4 common frames omitted
	Caused by: io.netty.channel.AbstractChannel$AnnotatedNoRouteToHostException: No route to host: no further information: /************:7001
	Caused by: java.net.NoRouteToHostException: No route to host: no further information
		at sun.nio.ch.SocketChannelImpl.checkConnect(Native Method)
		at sun.nio.ch.SocketChannelImpl.finishConnect(SocketChannelImpl.java:717)
		at io.netty.channel.socket.nio.NioSocketChannel.doFinishConnect(NioSocketChannel.java:330)
		at io.netty.channel.nio.AbstractNioChannel$AbstractNioUnsafe.finishConnect(AbstractNioChannel.java:334)
		at io.netty.channel.nio.NioEventLoop.processSelectedKey(NioEventLoop.java:688)
		at io.netty.channel.nio.NioEventLoop.processSelectedKeysOptimized(NioEventLoop.java:635)
		at io.netty.channel.nio.NioEventLoop.processSelectedKeys(NioEventLoop.java:552)
		at io.netty.channel.nio.NioEventLoop.run(NioEventLoop.java:514)
		at io.netty.util.concurrent.SingleThreadEventExecutor$6.run(SingleThreadEventExecutor.java:1050)
		at io.netty.util.internal.ThreadExecutorMap$2.run(ThreadExecutorMap.java:74)
		at io.netty.util.concurrent.FastThreadLocalRunnable.run(FastThreadLocalRunnable.java:30)
		at java.lang.Thread.run(Thread.java:748)
Caused by: io.lettuce.core.RedisConnectionException: Unable to establish a connection to Redis Cluster at [RedisURI [host='************', port=7001], RedisURI [host='************', port=7002], RedisURI [host='************', port=7003], RedisURI [host='************', port=7004], RedisURI [host='************', port=7005], RedisURI [host='************', port=7006]]
	at io.lettuce.core.cluster.topology.AsyncConnections.get(AsyncConnections.java:89)
	at io.lettuce.core.cluster.topology.ClusterTopologyRefresh.loadViews(ClusterTopologyRefresh.java:76)
	at io.lettuce.core.cluster.RedisClusterClient.doLoadPartitions(RedisClusterClient.java:865)
	at io.lettuce.core.cluster.RedisClusterClient.loadPartitions(RedisClusterClient.java:845)
	... 10 common frames omitted
	Suppressed: io.lettuce.core.RedisConnectionException: Unable to connect to [************:7002]: No route to host: no further information: /************:7002
		at io.lettuce.core.cluster.topology.ClusterTopologyRefresh.lambda$getConnections$0(ClusterTopologyRefresh.java:290)
		at java.util.concurrent.CompletableFuture.uniWhenComplete(CompletableFuture.java:760)
		at java.util.concurrent.CompletableFuture$UniWhenComplete.tryFire(CompletableFuture.java:736)
		at java.util.concurrent.CompletableFuture.postComplete(CompletableFuture.java:474)
		at java.util.concurrent.CompletableFuture.completeExceptionally(CompletableFuture.java:1977)
		at io.lettuce.core.AbstractRedisClient.lambda$initializeChannelAsync0$4(AbstractRedisClient.java:329)
		at io.netty.util.concurrent.DefaultPromise.notifyListener0(DefaultPromise.java:577)
		at io.netty.util.concurrent.DefaultPromise.notifyListeners0(DefaultPromise.java:570)
		at io.netty.util.concurrent.DefaultPromise.notifyListenersNow(DefaultPromise.java:549)
		at io.netty.util.concurrent.DefaultPromise.notifyListeners(DefaultPromise.java:490)
		at io.netty.util.concurrent.DefaultPromise.setValue0(DefaultPromise.java:615)
		at io.netty.util.concurrent.DefaultPromise.setFailure0(DefaultPromise.java:608)
		at io.netty.util.concurrent.DefaultPromise.tryFailure(DefaultPromise.java:117)
		at io.netty.channel.nio.AbstractNioChannel$AbstractNioUnsafe.fulfillConnectPromise(AbstractNioChannel.java:321)
		at io.netty.channel.nio.AbstractNioChannel$AbstractNioUnsafe.finishConnect(AbstractNioChannel.java:337)
		at io.netty.channel.nio.NioEventLoop.processSelectedKey(NioEventLoop.java:688)
		at io.netty.channel.nio.NioEventLoop.processSelectedKeysOptimized(NioEventLoop.java:635)
		at io.netty.channel.nio.NioEventLoop.processSelectedKeys(NioEventLoop.java:552)
		at io.netty.channel.nio.NioEventLoop.run(NioEventLoop.java:514)
		... 4 common frames omitted
	Caused by: io.netty.channel.AbstractChannel$AnnotatedNoRouteToHostException: No route to host: no further information: /************:7002
	Caused by: java.net.NoRouteToHostException: No route to host: no further information
		at sun.nio.ch.SocketChannelImpl.checkConnect(Native Method)
		at sun.nio.ch.SocketChannelImpl.finishConnect(SocketChannelImpl.java:717)
		at io.netty.channel.socket.nio.NioSocketChannel.doFinishConnect(NioSocketChannel.java:330)
		at io.netty.channel.nio.AbstractNioChannel$AbstractNioUnsafe.finishConnect(AbstractNioChannel.java:334)
		at io.netty.channel.nio.NioEventLoop.processSelectedKey(NioEventLoop.java:688)
		at io.netty.channel.nio.NioEventLoop.processSelectedKeysOptimized(NioEventLoop.java:635)
		at io.netty.channel.nio.NioEventLoop.processSelectedKeys(NioEventLoop.java:552)
		at io.netty.channel.nio.NioEventLoop.run(NioEventLoop.java:514)
		at io.netty.util.concurrent.SingleThreadEventExecutor$6.run(SingleThreadEventExecutor.java:1050)
		at io.netty.util.internal.ThreadExecutorMap$2.run(ThreadExecutorMap.java:74)
		at io.netty.util.concurrent.FastThreadLocalRunnable.run(FastThreadLocalRunnable.java:30)
		at java.lang.Thread.run(Thread.java:748)
	Suppressed: io.lettuce.core.RedisConnectionException: Unable to connect to [************:7005]: No route to host: no further information: /************:7005
		at io.lettuce.core.cluster.topology.ClusterTopologyRefresh.lambda$getConnections$0(ClusterTopologyRefresh.java:290)
		at java.util.concurrent.CompletableFuture.uniWhenComplete(CompletableFuture.java:760)
		at java.util.concurrent.CompletableFuture$UniWhenComplete.tryFire(CompletableFuture.java:736)
		at java.util.concurrent.CompletableFuture.postComplete(CompletableFuture.java:474)
		at java.util.concurrent.CompletableFuture.completeExceptionally(CompletableFuture.java:1977)
		at io.lettuce.core.AbstractRedisClient.lambda$initializeChannelAsync0$4(AbstractRedisClient.java:329)
		at io.netty.util.concurrent.DefaultPromise.notifyListener0(DefaultPromise.java:577)
		at io.netty.util.concurrent.DefaultPromise.notifyListeners0(DefaultPromise.java:570)
		at io.netty.util.concurrent.DefaultPromise.notifyListenersNow(DefaultPromise.java:549)
		at io.netty.util.concurrent.DefaultPromise.notifyListeners(DefaultPromise.java:490)
		at io.netty.util.concurrent.DefaultPromise.setValue0(DefaultPromise.java:615)
		at io.netty.util.concurrent.DefaultPromise.setFailure0(DefaultPromise.java:608)
		at io.netty.util.concurrent.DefaultPromise.tryFailure(DefaultPromise.java:117)
		at io.netty.channel.nio.AbstractNioChannel$AbstractNioUnsafe.fulfillConnectPromise(AbstractNioChannel.java:321)
		at io.netty.channel.nio.AbstractNioChannel$AbstractNioUnsafe.finishConnect(AbstractNioChannel.java:337)
		at io.netty.channel.nio.NioEventLoop.processSelectedKey(NioEventLoop.java:688)
		at io.netty.channel.nio.NioEventLoop.processSelectedKeysOptimized(NioEventLoop.java:635)
		at io.netty.channel.nio.NioEventLoop.processSelectedKeys(NioEventLoop.java:552)
		at io.netty.channel.nio.NioEventLoop.run(NioEventLoop.java:514)
		... 4 common frames omitted
	Caused by: io.netty.channel.AbstractChannel$AnnotatedNoRouteToHostException: No route to host: no further information: /************:7005
	Caused by: java.net.NoRouteToHostException: No route to host: no further information
		at sun.nio.ch.SocketChannelImpl.checkConnect(Native Method)
		at sun.nio.ch.SocketChannelImpl.finishConnect(SocketChannelImpl.java:717)
		at io.netty.channel.socket.nio.NioSocketChannel.doFinishConnect(NioSocketChannel.java:330)
		at io.netty.channel.nio.AbstractNioChannel$AbstractNioUnsafe.finishConnect(AbstractNioChannel.java:334)
		at io.netty.channel.nio.NioEventLoop.processSelectedKey(NioEventLoop.java:688)
		at io.netty.channel.nio.NioEventLoop.processSelectedKeysOptimized(NioEventLoop.java:635)
		at io.netty.channel.nio.NioEventLoop.processSelectedKeys(NioEventLoop.java:552)
		at io.netty.channel.nio.NioEventLoop.run(NioEventLoop.java:514)
		at io.netty.util.concurrent.SingleThreadEventExecutor$6.run(SingleThreadEventExecutor.java:1050)
		at io.netty.util.internal.ThreadExecutorMap$2.run(ThreadExecutorMap.java:74)
		at io.netty.util.concurrent.FastThreadLocalRunnable.run(FastThreadLocalRunnable.java:30)
		at java.lang.Thread.run(Thread.java:748)
	Suppressed: io.lettuce.core.RedisConnectionException: Unable to connect to [************:7003]: No route to host: no further information: /************:7003
		at io.lettuce.core.cluster.topology.ClusterTopologyRefresh.lambda$getConnections$0(ClusterTopologyRefresh.java:290)
		at java.util.concurrent.CompletableFuture.uniWhenComplete(CompletableFuture.java:760)
		at java.util.concurrent.CompletableFuture$UniWhenComplete.tryFire(CompletableFuture.java:736)
		at java.util.concurrent.CompletableFuture.postComplete(CompletableFuture.java:474)
		at java.util.concurrent.CompletableFuture.completeExceptionally(CompletableFuture.java:1977)
		at io.lettuce.core.AbstractRedisClient.lambda$initializeChannelAsync0$4(AbstractRedisClient.java:329)
		at io.netty.util.concurrent.DefaultPromise.notifyListener0(DefaultPromise.java:577)
		at io.netty.util.concurrent.DefaultPromise.notifyListeners0(DefaultPromise.java:570)
		at io.netty.util.concurrent.DefaultPromise.notifyListenersNow(DefaultPromise.java:549)
		at io.netty.util.concurrent.DefaultPromise.notifyListeners(DefaultPromise.java:490)
		at io.netty.util.concurrent.DefaultPromise.setValue0(DefaultPromise.java:615)
		at io.netty.util.concurrent.DefaultPromise.setFailure0(DefaultPromise.java:608)
		at io.netty.util.concurrent.DefaultPromise.tryFailure(DefaultPromise.java:117)
		at io.netty.channel.nio.AbstractNioChannel$AbstractNioUnsafe.fulfillConnectPromise(AbstractNioChannel.java:321)
		at io.netty.channel.nio.AbstractNioChannel$AbstractNioUnsafe.finishConnect(AbstractNioChannel.java:337)
		at io.netty.channel.nio.NioEventLoop.processSelectedKey(NioEventLoop.java:688)
		at io.netty.channel.nio.NioEventLoop.processSelectedKeysOptimized(NioEventLoop.java:635)
		at io.netty.channel.nio.NioEventLoop.processSelectedKeys(NioEventLoop.java:552)
		at io.netty.channel.nio.NioEventLoop.run(NioEventLoop.java:514)
		... 4 common frames omitted
	Caused by: io.netty.channel.AbstractChannel$AnnotatedNoRouteToHostException: No route to host: no further information: /************:7003
	Caused by: java.net.NoRouteToHostException: No route to host: no further information
		at sun.nio.ch.SocketChannelImpl.checkConnect(Native Method)
		at sun.nio.ch.SocketChannelImpl.finishConnect(SocketChannelImpl.java:717)
		at io.netty.channel.socket.nio.NioSocketChannel.doFinishConnect(NioSocketChannel.java:330)
		at io.netty.channel.nio.AbstractNioChannel$AbstractNioUnsafe.finishConnect(AbstractNioChannel.java:334)
		at io.netty.channel.nio.NioEventLoop.processSelectedKey(NioEventLoop.java:688)
		at io.netty.channel.nio.NioEventLoop.processSelectedKeysOptimized(NioEventLoop.java:635)
		at io.netty.channel.nio.NioEventLoop.processSelectedKeys(NioEventLoop.java:552)
		at io.netty.channel.nio.NioEventLoop.run(NioEventLoop.java:514)
		at io.netty.util.concurrent.SingleThreadEventExecutor$6.run(SingleThreadEventExecutor.java:1050)
		at io.netty.util.internal.ThreadExecutorMap$2.run(ThreadExecutorMap.java:74)
		at io.netty.util.concurrent.FastThreadLocalRunnable.run(FastThreadLocalRunnable.java:30)
		at java.lang.Thread.run(Thread.java:748)
	Suppressed: io.lettuce.core.RedisConnectionException: Unable to connect to [************:7001]: No route to host: no further information: /************:7001
		at io.lettuce.core.cluster.topology.ClusterTopologyRefresh.lambda$getConnections$0(ClusterTopologyRefresh.java:290)
		at java.util.concurrent.CompletableFuture.uniWhenComplete(CompletableFuture.java:760)
		at java.util.concurrent.CompletableFuture$UniWhenComplete.tryFire(CompletableFuture.java:736)
		at java.util.concurrent.CompletableFuture.postComplete(CompletableFuture.java:474)
		at java.util.concurrent.CompletableFuture.completeExceptionally(CompletableFuture.java:1977)
		at io.lettuce.core.AbstractRedisClient.lambda$initializeChannelAsync0$4(AbstractRedisClient.java:329)
		at io.netty.util.concurrent.DefaultPromise.notifyListener0(DefaultPromise.java:577)
		at io.netty.util.concurrent.DefaultPromise.notifyListeners0(DefaultPromise.java:570)
		at io.netty.util.concurrent.DefaultPromise.notifyListenersNow(DefaultPromise.java:549)
		at io.netty.util.concurrent.DefaultPromise.notifyListeners(DefaultPromise.java:490)
		at io.netty.util.concurrent.DefaultPromise.setValue0(DefaultPromise.java:615)
		at io.netty.util.concurrent.DefaultPromise.setFailure0(DefaultPromise.java:608)
		at io.netty.util.concurrent.DefaultPromise.tryFailure(DefaultPromise.java:117)
		at io.netty.channel.nio.AbstractNioChannel$AbstractNioUnsafe.fulfillConnectPromise(AbstractNioChannel.java:321)
		at io.netty.channel.nio.AbstractNioChannel$AbstractNioUnsafe.finishConnect(AbstractNioChannel.java:337)
		at io.netty.channel.nio.NioEventLoop.processSelectedKey(NioEventLoop.java:688)
		at io.netty.channel.nio.NioEventLoop.processSelectedKeysOptimized(NioEventLoop.java:635)
		at io.netty.channel.nio.NioEventLoop.processSelectedKeys(NioEventLoop.java:552)
		at io.netty.channel.nio.NioEventLoop.run(NioEventLoop.java:514)
		... 4 common frames omitted
	Caused by: io.netty.channel.AbstractChannel$AnnotatedNoRouteToHostException: No route to host: no further information: /************:7001
	Caused by: java.net.NoRouteToHostException: No route to host: no further information
		at sun.nio.ch.SocketChannelImpl.checkConnect(Native Method)
		at sun.nio.ch.SocketChannelImpl.finishConnect(SocketChannelImpl.java:717)
		at io.netty.channel.socket.nio.NioSocketChannel.doFinishConnect(NioSocketChannel.java:330)
		at io.netty.channel.nio.AbstractNioChannel$AbstractNioUnsafe.finishConnect(AbstractNioChannel.java:334)
		at io.netty.channel.nio.NioEventLoop.processSelectedKey(NioEventLoop.java:688)
		at io.netty.channel.nio.NioEventLoop.processSelectedKeysOptimized(NioEventLoop.java:635)
		at io.netty.channel.nio.NioEventLoop.processSelectedKeys(NioEventLoop.java:552)
		at io.netty.channel.nio.NioEventLoop.run(NioEventLoop.java:514)
		at io.netty.util.concurrent.SingleThreadEventExecutor$6.run(SingleThreadEventExecutor.java:1050)
		at io.netty.util.internal.ThreadExecutorMap$2.run(ThreadExecutorMap.java:74)
		at io.netty.util.concurrent.FastThreadLocalRunnable.run(FastThreadLocalRunnable.java:30)
		at java.lang.Thread.run(Thread.java:748)
	Suppressed: io.lettuce.core.RedisConnectionException: Unable to connect to [************:7004]: No route to host: no further information: /************:7004
		at io.lettuce.core.cluster.topology.ClusterTopologyRefresh.lambda$getConnections$0(ClusterTopologyRefresh.java:290)
		at java.util.concurrent.CompletableFuture.uniWhenComplete(CompletableFuture.java:760)
		at java.util.concurrent.CompletableFuture$UniWhenComplete.tryFire(CompletableFuture.java:736)
		at java.util.concurrent.CompletableFuture.postComplete(CompletableFuture.java:474)
		at java.util.concurrent.CompletableFuture.completeExceptionally(CompletableFuture.java:1977)
		at io.lettuce.core.AbstractRedisClient.lambda$initializeChannelAsync0$4(AbstractRedisClient.java:329)
		at io.netty.util.concurrent.DefaultPromise.notifyListener0(DefaultPromise.java:577)
		at io.netty.util.concurrent.DefaultPromise.notifyListeners0(DefaultPromise.java:570)
		at io.netty.util.concurrent.DefaultPromise.notifyListenersNow(DefaultPromise.java:549)
		at io.netty.util.concurrent.DefaultPromise.notifyListeners(DefaultPromise.java:490)
		at io.netty.util.concurrent.DefaultPromise.setValue0(DefaultPromise.java:615)
		at io.netty.util.concurrent.DefaultPromise.setFailure0(DefaultPromise.java:608)
		at io.netty.util.concurrent.DefaultPromise.tryFailure(DefaultPromise.java:117)
		at io.netty.channel.nio.AbstractNioChannel$AbstractNioUnsafe.fulfillConnectPromise(AbstractNioChannel.java:321)
		at io.netty.channel.nio.AbstractNioChannel$AbstractNioUnsafe.finishConnect(AbstractNioChannel.java:337)
		at io.netty.channel.nio.NioEventLoop.processSelectedKey(NioEventLoop.java:688)
		at io.netty.channel.nio.NioEventLoop.processSelectedKeysOptimized(NioEventLoop.java:635)
		at io.netty.channel.nio.NioEventLoop.processSelectedKeys(NioEventLoop.java:552)
		at io.netty.channel.nio.NioEventLoop.run(NioEventLoop.java:514)
		... 4 common frames omitted
	Caused by: io.netty.channel.AbstractChannel$AnnotatedNoRouteToHostException: No route to host: no further information: /************:7004
	Caused by: java.net.NoRouteToHostException: No route to host: no further information
		at sun.nio.ch.SocketChannelImpl.checkConnect(Native Method)
		at sun.nio.ch.SocketChannelImpl.finishConnect(SocketChannelImpl.java:717)
		at io.netty.channel.socket.nio.NioSocketChannel.doFinishConnect(NioSocketChannel.java:330)
		at io.netty.channel.nio.AbstractNioChannel$AbstractNioUnsafe.finishConnect(AbstractNioChannel.java:334)
		at io.netty.channel.nio.NioEventLoop.processSelectedKey(NioEventLoop.java:688)
		at io.netty.channel.nio.NioEventLoop.processSelectedKeysOptimized(NioEventLoop.java:635)
		at io.netty.channel.nio.NioEventLoop.processSelectedKeys(NioEventLoop.java:552)
		at io.netty.channel.nio.NioEventLoop.run(NioEventLoop.java:514)
		at io.netty.util.concurrent.SingleThreadEventExecutor$6.run(SingleThreadEventExecutor.java:1050)
		at io.netty.util.internal.ThreadExecutorMap$2.run(ThreadExecutorMap.java:74)
		at io.netty.util.concurrent.FastThreadLocalRunnable.run(FastThreadLocalRunnable.java:30)
		at java.lang.Thread.run(Thread.java:748)
Caused by: io.lettuce.core.RedisConnectionException: Unable to connect to [************:7006]: No route to host: no further information: /************:7006
	at io.lettuce.core.cluster.topology.ClusterTopologyRefresh.lambda$getConnections$0(ClusterTopologyRefresh.java:290)
	at java.util.concurrent.CompletableFuture.uniWhenComplete(CompletableFuture.java:760)
	at java.util.concurrent.CompletableFuture$UniWhenComplete.tryFire(CompletableFuture.java:736)
	at java.util.concurrent.CompletableFuture.postComplete(CompletableFuture.java:474)
	at java.util.concurrent.CompletableFuture.completeExceptionally(CompletableFuture.java:1977)
	at io.lettuce.core.AbstractRedisClient.lambda$initializeChannelAsync0$4(AbstractRedisClient.java:329)
	at io.netty.util.concurrent.DefaultPromise.notifyListener0(DefaultPromise.java:577)
	at io.netty.util.concurrent.DefaultPromise.notifyListeners0(DefaultPromise.java:570)
	at io.netty.util.concurrent.DefaultPromise.notifyListenersNow(DefaultPromise.java:549)
	at io.netty.util.concurrent.DefaultPromise.notifyListeners(DefaultPromise.java:490)
	at io.netty.util.concurrent.DefaultPromise.setValue0(DefaultPromise.java:615)
	at io.netty.util.concurrent.DefaultPromise.setFailure0(DefaultPromise.java:608)
	at io.netty.util.concurrent.DefaultPromise.tryFailure(DefaultPromise.java:117)
	at io.netty.channel.nio.AbstractNioChannel$AbstractNioUnsafe.fulfillConnectPromise(AbstractNioChannel.java:321)
	at io.netty.channel.nio.AbstractNioChannel$AbstractNioUnsafe.finishConnect(AbstractNioChannel.java:337)
	at io.netty.channel.nio.NioEventLoop.processSelectedKey(NioEventLoop.java:688)
	at io.netty.channel.nio.NioEventLoop.processSelectedKeysOptimized(NioEventLoop.java:635)
	at io.netty.channel.nio.NioEventLoop.processSelectedKeys(NioEventLoop.java:552)
	at io.netty.channel.nio.NioEventLoop.run(NioEventLoop.java:514)
	... 4 common frames omitted
Caused by: io.netty.channel.AbstractChannel$AnnotatedNoRouteToHostException: No route to host: no further information: /************:7006
Caused by: java.net.NoRouteToHostException: No route to host: no further information
	at sun.nio.ch.SocketChannelImpl.checkConnect(Native Method)
	at sun.nio.ch.SocketChannelImpl.finishConnect(SocketChannelImpl.java:717)
	at io.netty.channel.socket.nio.NioSocketChannel.doFinishConnect(NioSocketChannel.java:330)
	at io.netty.channel.nio.AbstractNioChannel$AbstractNioUnsafe.finishConnect(AbstractNioChannel.java:334)
	at io.netty.channel.nio.NioEventLoop.processSelectedKey(NioEventLoop.java:688)
	at io.netty.channel.nio.NioEventLoop.processSelectedKeysOptimized(NioEventLoop.java:635)
	at io.netty.channel.nio.NioEventLoop.processSelectedKeys(NioEventLoop.java:552)
	at io.netty.channel.nio.NioEventLoop.run(NioEventLoop.java:514)
	at io.netty.util.concurrent.SingleThreadEventExecutor$6.run(SingleThreadEventExecutor.java:1050)
	at io.netty.util.internal.ThreadExecutorMap$2.run(ThreadExecutorMap.java:74)
	at io.netty.util.concurrent.FastThreadLocalRunnable.run(FastThreadLocalRunnable.java:30)
	at java.lang.Thread.run(Thread.java:748)
2025-06-02 04:59:53.322 [lettuce-nioEventLoop-4-6] WARN  io.lettuce.core.cluster.topology.ClusterTopologyRefresh.lambda$getConnections$0 Line:284 - Unable to connect to [************:7002]: No route to host: no further information: /************:7002
2025-06-02 04:59:53.322 [lettuce-nioEventLoop-4-7] WARN  io.lettuce.core.cluster.topology.ClusterTopologyRefresh.lambda$getConnections$0 Line:284 - Unable to connect to [************:7003]: No route to host: no further information: /************:7003
2025-06-02 04:59:53.322 [lettuce-nioEventLoop-4-10] WARN  io.lettuce.core.cluster.topology.ClusterTopologyRefresh.lambda$getConnections$0 Line:284 - Unable to connect to [************:7006]: No route to host: no further information: /************:7006
2025-06-02 04:59:53.322 [lettuce-nioEventLoop-4-8] WARN  io.lettuce.core.cluster.topology.ClusterTopologyRefresh.lambda$getConnections$0 Line:284 - Unable to connect to [************:7004]: No route to host: no further information: /************:7004
2025-06-02 04:59:53.322 [lettuce-nioEventLoop-4-5] WARN  io.lettuce.core.cluster.topology.ClusterTopologyRefresh.lambda$getConnections$0 Line:284 - Unable to connect to [************:7001]: No route to host: no further information: /************:7001
2025-06-02 04:59:53.333 [lettuce-nioEventLoop-4-9] WARN  io.lettuce.core.cluster.topology.ClusterTopologyRefresh.lambda$getConnections$0 Line:284 - Unable to connect to [************:7005]: No route to host: no further information: /************:7005
2025-06-02 04:59:53.334 [lettuce-nioEventLoop-4-11] WARN  io.lettuce.core.cluster.topology.ClusterTopologyRefresh.lambda$getConnections$0 Line:284 - Unable to connect to [************:7006]: No route to host: no further information: /************:7006
2025-06-02 04:59:53.334 [lettuce-nioEventLoop-4-1] WARN  io.lettuce.core.cluster.topology.ClusterTopologyRefresh.lambda$getConnections$0 Line:284 - Unable to connect to [************:7005]: No route to host: no further information: /************:7005
2025-06-02 04:59:53.334 [lettuce-nioEventLoop-4-2] WARN  io.lettuce.core.cluster.topology.ClusterTopologyRefresh.lambda$getConnections$0 Line:284 - Unable to connect to [************:7002]: No route to host: no further information: /************:7002
2025-06-02 04:59:53.334 [lettuce-nioEventLoop-4-12] WARN  io.lettuce.core.cluster.topology.ClusterTopologyRefresh.lambda$getConnections$0 Line:284 - Unable to connect to [************:7004]: No route to host: no further information: /************:7004
2025-06-02 04:59:53.334 [lettuce-nioEventLoop-4-4] WARN  io.lettuce.core.cluster.topology.ClusterTopologyRefresh.lambda$getConnections$0 Line:284 - Unable to connect to [************:7001]: No route to host: no further information: /************:7001
2025-06-02 04:59:53.334 [lettuce-nioEventLoop-4-3] WARN  io.lettuce.core.cluster.topology.ClusterTopologyRefresh.lambda$getConnections$0 Line:284 - Unable to connect to [************:7003]: No route to host: no further information: /************:7003
2025-06-02 04:59:53.345 [lettuce-eventExecutorLoop-1-10] WARN  io.lettuce.core.cluster.ClusterTopologyRefreshScheduler.doRun Line:258 - Cannot refresh Redis Cluster topology
io.lettuce.core.RedisException: Cannot retrieve initial cluster partitions from initial URIs [RedisURI [host='************', port=7006], RedisURI [host='************', port=7004], RedisURI [host='************', port=7005], RedisURI [host='************', port=7002], RedisURI [host='************', port=7003], RedisURI [host='************', port=7001]]
	at io.lettuce.core.cluster.RedisClusterClient.loadPartitions(RedisClusterClient.java:848)
	at io.lettuce.core.cluster.RedisClusterClient.reloadPartitions(RedisClusterClient.java:784)
	at io.lettuce.core.cluster.ClusterTopologyRefreshScheduler$ClusterTopologyRefreshTask.doRun(ClusterTopologyRefreshScheduler.java:256)
	at io.lettuce.core.cluster.ClusterTopologyRefreshScheduler$ClusterTopologyRefreshTask.run(ClusterTopologyRefreshScheduler.java:236)
	at io.netty.util.concurrent.PromiseTask.runTask(PromiseTask.java:98)
	at io.netty.util.concurrent.PromiseTask.run(PromiseTask.java:106)
	at io.netty.util.concurrent.DefaultEventExecutor.run(DefaultEventExecutor.java:66)
	at io.netty.util.concurrent.SingleThreadEventExecutor$6.run(SingleThreadEventExecutor.java:1050)
	at io.netty.util.internal.ThreadExecutorMap$2.run(ThreadExecutorMap.java:74)
	at io.netty.util.concurrent.FastThreadLocalRunnable.run(FastThreadLocalRunnable.java:30)
	at java.lang.Thread.run(Thread.java:748)
	Suppressed: io.lettuce.core.RedisConnectionException: Unable to establish a connection to Redis Cluster at [RedisURI [host='************', port=7001], RedisURI [host='************', port=7002], RedisURI [host='************', port=7003], RedisURI [host='************', port=7004], RedisURI [host='************', port=7005], RedisURI [host='************', port=7006]]
		at io.lettuce.core.cluster.topology.AsyncConnections.get(AsyncConnections.java:89)
		at io.lettuce.core.cluster.topology.ClusterTopologyRefresh.loadViews(ClusterTopologyRefresh.java:76)
		at io.lettuce.core.cluster.RedisClusterClient.doLoadPartitions(RedisClusterClient.java:865)
		at io.lettuce.core.cluster.RedisClusterClient.loadPartitions(RedisClusterClient.java:838)
		... 10 common frames omitted
		Suppressed: io.lettuce.core.RedisConnectionException: Unable to connect to [************:7003]: No route to host: no further information: /************:7003
			at io.lettuce.core.cluster.topology.ClusterTopologyRefresh.lambda$getConnections$0(ClusterTopologyRefresh.java:290)
			at java.util.concurrent.CompletableFuture.uniWhenComplete(CompletableFuture.java:760)
			at java.util.concurrent.CompletableFuture$UniWhenComplete.tryFire(CompletableFuture.java:736)
			at java.util.concurrent.CompletableFuture.postComplete(CompletableFuture.java:474)
			at java.util.concurrent.CompletableFuture.completeExceptionally(CompletableFuture.java:1977)
			at io.lettuce.core.AbstractRedisClient.lambda$initializeChannelAsync0$4(AbstractRedisClient.java:329)
			at io.netty.util.concurrent.DefaultPromise.notifyListener0(DefaultPromise.java:577)
			at io.netty.util.concurrent.DefaultPromise.notifyListeners0(DefaultPromise.java:570)
			at io.netty.util.concurrent.DefaultPromise.notifyListenersNow(DefaultPromise.java:549)
			at io.netty.util.concurrent.DefaultPromise.notifyListeners(DefaultPromise.java:490)
			at io.netty.util.concurrent.DefaultPromise.setValue0(DefaultPromise.java:615)
			at io.netty.util.concurrent.DefaultPromise.setFailure0(DefaultPromise.java:608)
			at io.netty.util.concurrent.DefaultPromise.tryFailure(DefaultPromise.java:117)
			at io.netty.channel.nio.AbstractNioChannel$AbstractNioUnsafe.fulfillConnectPromise(AbstractNioChannel.java:321)
			at io.netty.channel.nio.AbstractNioChannel$AbstractNioUnsafe.finishConnect(AbstractNioChannel.java:337)
			at io.netty.channel.nio.NioEventLoop.processSelectedKey(NioEventLoop.java:688)
			at io.netty.channel.nio.NioEventLoop.processSelectedKeysOptimized(NioEventLoop.java:635)
			at io.netty.channel.nio.NioEventLoop.processSelectedKeys(NioEventLoop.java:552)
			at io.netty.channel.nio.NioEventLoop.run(NioEventLoop.java:514)
			... 4 common frames omitted
		Caused by: io.netty.channel.AbstractChannel$AnnotatedNoRouteToHostException: No route to host: no further information: /************:7003
		Caused by: java.net.NoRouteToHostException: No route to host: no further information
			at sun.nio.ch.SocketChannelImpl.checkConnect(Native Method)
			at sun.nio.ch.SocketChannelImpl.finishConnect(SocketChannelImpl.java:717)
			at io.netty.channel.socket.nio.NioSocketChannel.doFinishConnect(NioSocketChannel.java:330)
			at io.netty.channel.nio.AbstractNioChannel$AbstractNioUnsafe.finishConnect(AbstractNioChannel.java:334)
			at io.netty.channel.nio.NioEventLoop.processSelectedKey(NioEventLoop.java:688)
			at io.netty.channel.nio.NioEventLoop.processSelectedKeysOptimized(NioEventLoop.java:635)
			at io.netty.channel.nio.NioEventLoop.processSelectedKeys(NioEventLoop.java:552)
			at io.netty.channel.nio.NioEventLoop.run(NioEventLoop.java:514)
			at io.netty.util.concurrent.SingleThreadEventExecutor$6.run(SingleThreadEventExecutor.java:1050)
			at io.netty.util.internal.ThreadExecutorMap$2.run(ThreadExecutorMap.java:74)
			at io.netty.util.concurrent.FastThreadLocalRunnable.run(FastThreadLocalRunnable.java:30)
			at java.lang.Thread.run(Thread.java:748)
		Suppressed: io.lettuce.core.RedisConnectionException: Unable to connect to [************:7006]: No route to host: no further information: /************:7006
			at io.lettuce.core.cluster.topology.ClusterTopologyRefresh.lambda$getConnections$0(ClusterTopologyRefresh.java:290)
			at java.util.concurrent.CompletableFuture.uniWhenComplete(CompletableFuture.java:760)
			at java.util.concurrent.CompletableFuture$UniWhenComplete.tryFire(CompletableFuture.java:736)
			at java.util.concurrent.CompletableFuture.postComplete(CompletableFuture.java:474)
			at java.util.concurrent.CompletableFuture.completeExceptionally(CompletableFuture.java:1977)
			at io.lettuce.core.AbstractRedisClient.lambda$initializeChannelAsync0$4(AbstractRedisClient.java:329)
			at io.netty.util.concurrent.DefaultPromise.notifyListener0(DefaultPromise.java:577)
			at io.netty.util.concurrent.DefaultPromise.notifyListeners0(DefaultPromise.java:570)
			at io.netty.util.concurrent.DefaultPromise.notifyListenersNow(DefaultPromise.java:549)
			at io.netty.util.concurrent.DefaultPromise.notifyListeners(DefaultPromise.java:490)
			at io.netty.util.concurrent.DefaultPromise.setValue0(DefaultPromise.java:615)
			at io.netty.util.concurrent.DefaultPromise.setFailure0(DefaultPromise.java:608)
			at io.netty.util.concurrent.DefaultPromise.tryFailure(DefaultPromise.java:117)
			at io.netty.channel.nio.AbstractNioChannel$AbstractNioUnsafe.fulfillConnectPromise(AbstractNioChannel.java:321)
			at io.netty.channel.nio.AbstractNioChannel$AbstractNioUnsafe.finishConnect(AbstractNioChannel.java:337)
			at io.netty.channel.nio.NioEventLoop.processSelectedKey(NioEventLoop.java:688)
			at io.netty.channel.nio.NioEventLoop.processSelectedKeysOptimized(NioEventLoop.java:635)
			at io.netty.channel.nio.NioEventLoop.processSelectedKeys(NioEventLoop.java:552)
			at io.netty.channel.nio.NioEventLoop.run(NioEventLoop.java:514)
			... 4 common frames omitted
		Caused by: io.netty.channel.AbstractChannel$AnnotatedNoRouteToHostException: No route to host: no further information: /************:7006
		Caused by: java.net.NoRouteToHostException: No route to host: no further information
			at sun.nio.ch.SocketChannelImpl.checkConnect(Native Method)
			at sun.nio.ch.SocketChannelImpl.finishConnect(SocketChannelImpl.java:717)
			at io.netty.channel.socket.nio.NioSocketChannel.doFinishConnect(NioSocketChannel.java:330)
			at io.netty.channel.nio.AbstractNioChannel$AbstractNioUnsafe.finishConnect(AbstractNioChannel.java:334)
			at io.netty.channel.nio.NioEventLoop.processSelectedKey(NioEventLoop.java:688)
			at io.netty.channel.nio.NioEventLoop.processSelectedKeysOptimized(NioEventLoop.java:635)
			at io.netty.channel.nio.NioEventLoop.processSelectedKeys(NioEventLoop.java:552)
			at io.netty.channel.nio.NioEventLoop.run(NioEventLoop.java:514)
			at io.netty.util.concurrent.SingleThreadEventExecutor$6.run(SingleThreadEventExecutor.java:1050)
			at io.netty.util.internal.ThreadExecutorMap$2.run(ThreadExecutorMap.java:74)
			at io.netty.util.concurrent.FastThreadLocalRunnable.run(FastThreadLocalRunnable.java:30)
			at java.lang.Thread.run(Thread.java:748)
		Suppressed: io.lettuce.core.RedisConnectionException: Unable to connect to [************:7004]: No route to host: no further information: /************:7004
			at io.lettuce.core.cluster.topology.ClusterTopologyRefresh.lambda$getConnections$0(ClusterTopologyRefresh.java:290)
			at java.util.concurrent.CompletableFuture.uniWhenComplete(CompletableFuture.java:760)
			at java.util.concurrent.CompletableFuture$UniWhenComplete.tryFire(CompletableFuture.java:736)
			at java.util.concurrent.CompletableFuture.postComplete(CompletableFuture.java:474)
			at java.util.concurrent.CompletableFuture.completeExceptionally(CompletableFuture.java:1977)
			at io.lettuce.core.AbstractRedisClient.lambda$initializeChannelAsync0$4(AbstractRedisClient.java:329)
			at io.netty.util.concurrent.DefaultPromise.notifyListener0(DefaultPromise.java:577)
			at io.netty.util.concurrent.DefaultPromise.notifyListeners0(DefaultPromise.java:570)
			at io.netty.util.concurrent.DefaultPromise.notifyListenersNow(DefaultPromise.java:549)
			at io.netty.util.concurrent.DefaultPromise.notifyListeners(DefaultPromise.java:490)
			at io.netty.util.concurrent.DefaultPromise.setValue0(DefaultPromise.java:615)
			at io.netty.util.concurrent.DefaultPromise.setFailure0(DefaultPromise.java:608)
			at io.netty.util.concurrent.DefaultPromise.tryFailure(DefaultPromise.java:117)
			at io.netty.channel.nio.AbstractNioChannel$AbstractNioUnsafe.fulfillConnectPromise(AbstractNioChannel.java:321)
			at io.netty.channel.nio.AbstractNioChannel$AbstractNioUnsafe.finishConnect(AbstractNioChannel.java:337)
			at io.netty.channel.nio.NioEventLoop.processSelectedKey(NioEventLoop.java:688)
			at io.netty.channel.nio.NioEventLoop.processSelectedKeysOptimized(NioEventLoop.java:635)
			at io.netty.channel.nio.NioEventLoop.processSelectedKeys(NioEventLoop.java:552)
			at io.netty.channel.nio.NioEventLoop.run(NioEventLoop.java:514)
			... 4 common frames omitted
		Caused by: io.netty.channel.AbstractChannel$AnnotatedNoRouteToHostException: No route to host: no further information: /************:7004
		Caused by: java.net.NoRouteToHostException: No route to host: no further information
			at sun.nio.ch.SocketChannelImpl.checkConnect(Native Method)
			at sun.nio.ch.SocketChannelImpl.finishConnect(SocketChannelImpl.java:717)
			at io.netty.channel.socket.nio.NioSocketChannel.doFinishConnect(NioSocketChannel.java:330)
			at io.netty.channel.nio.AbstractNioChannel$AbstractNioUnsafe.finishConnect(AbstractNioChannel.java:334)
			at io.netty.channel.nio.NioEventLoop.processSelectedKey(NioEventLoop.java:688)
			at io.netty.channel.nio.NioEventLoop.processSelectedKeysOptimized(NioEventLoop.java:635)
			at io.netty.channel.nio.NioEventLoop.processSelectedKeys(NioEventLoop.java:552)
			at io.netty.channel.nio.NioEventLoop.run(NioEventLoop.java:514)
			at io.netty.util.concurrent.SingleThreadEventExecutor$6.run(SingleThreadEventExecutor.java:1050)
			at io.netty.util.internal.ThreadExecutorMap$2.run(ThreadExecutorMap.java:74)
			at io.netty.util.concurrent.FastThreadLocalRunnable.run(FastThreadLocalRunnable.java:30)
			at java.lang.Thread.run(Thread.java:748)
		Suppressed: io.lettuce.core.RedisConnectionException: Unable to connect to [************:7001]: No route to host: no further information: /************:7001
			at io.lettuce.core.cluster.topology.ClusterTopologyRefresh.lambda$getConnections$0(ClusterTopologyRefresh.java:290)
			at java.util.concurrent.CompletableFuture.uniWhenComplete(CompletableFuture.java:760)
			at java.util.concurrent.CompletableFuture$UniWhenComplete.tryFire(CompletableFuture.java:736)
			at java.util.concurrent.CompletableFuture.postComplete(CompletableFuture.java:474)
			at java.util.concurrent.CompletableFuture.completeExceptionally(CompletableFuture.java:1977)
			at io.lettuce.core.AbstractRedisClient.lambda$initializeChannelAsync0$4(AbstractRedisClient.java:329)
			at io.netty.util.concurrent.DefaultPromise.notifyListener0(DefaultPromise.java:577)
			at io.netty.util.concurrent.DefaultPromise.notifyListeners0(DefaultPromise.java:570)
			at io.netty.util.concurrent.DefaultPromise.notifyListenersNow(DefaultPromise.java:549)
			at io.netty.util.concurrent.DefaultPromise.notifyListeners(DefaultPromise.java:490)
			at io.netty.util.concurrent.DefaultPromise.setValue0(DefaultPromise.java:615)
			at io.netty.util.concurrent.DefaultPromise.setFailure0(DefaultPromise.java:608)
			at io.netty.util.concurrent.DefaultPromise.tryFailure(DefaultPromise.java:117)
			at io.netty.channel.nio.AbstractNioChannel$AbstractNioUnsafe.fulfillConnectPromise(AbstractNioChannel.java:321)
			at io.netty.channel.nio.AbstractNioChannel$AbstractNioUnsafe.finishConnect(AbstractNioChannel.java:337)
			at io.netty.channel.nio.NioEventLoop.processSelectedKey(NioEventLoop.java:688)
			at io.netty.channel.nio.NioEventLoop.processSelectedKeysOptimized(NioEventLoop.java:635)
			at io.netty.channel.nio.NioEventLoop.processSelectedKeys(NioEventLoop.java:552)
			at io.netty.channel.nio.NioEventLoop.run(NioEventLoop.java:514)
			... 4 common frames omitted
		Caused by: io.netty.channel.AbstractChannel$AnnotatedNoRouteToHostException: No route to host: no further information: /************:7001
		Caused by: java.net.NoRouteToHostException: No route to host: no further information
			at sun.nio.ch.SocketChannelImpl.checkConnect(Native Method)
			at sun.nio.ch.SocketChannelImpl.finishConnect(SocketChannelImpl.java:717)
			at io.netty.channel.socket.nio.NioSocketChannel.doFinishConnect(NioSocketChannel.java:330)
			at io.netty.channel.nio.AbstractNioChannel$AbstractNioUnsafe.finishConnect(AbstractNioChannel.java:334)
			at io.netty.channel.nio.NioEventLoop.processSelectedKey(NioEventLoop.java:688)
			at io.netty.channel.nio.NioEventLoop.processSelectedKeysOptimized(NioEventLoop.java:635)
			at io.netty.channel.nio.NioEventLoop.processSelectedKeys(NioEventLoop.java:552)
			at io.netty.channel.nio.NioEventLoop.run(NioEventLoop.java:514)
			at io.netty.util.concurrent.SingleThreadEventExecutor$6.run(SingleThreadEventExecutor.java:1050)
			at io.netty.util.internal.ThreadExecutorMap$2.run(ThreadExecutorMap.java:74)
			at io.netty.util.concurrent.FastThreadLocalRunnable.run(FastThreadLocalRunnable.java:30)
			at java.lang.Thread.run(Thread.java:748)
		Suppressed: io.lettuce.core.RedisConnectionException: Unable to connect to [************:7005]: No route to host: no further information: /************:7005
			at io.lettuce.core.cluster.topology.ClusterTopologyRefresh.lambda$getConnections$0(ClusterTopologyRefresh.java:290)
			at java.util.concurrent.CompletableFuture.uniWhenComplete(CompletableFuture.java:760)
			at java.util.concurrent.CompletableFuture$UniWhenComplete.tryFire(CompletableFuture.java:736)
			at java.util.concurrent.CompletableFuture.postComplete(CompletableFuture.java:474)
			at java.util.concurrent.CompletableFuture.completeExceptionally(CompletableFuture.java:1977)
			at io.lettuce.core.AbstractRedisClient.lambda$initializeChannelAsync0$4(AbstractRedisClient.java:329)
			at io.netty.util.concurrent.DefaultPromise.notifyListener0(DefaultPromise.java:577)
			at io.netty.util.concurrent.DefaultPromise.notifyListeners0(DefaultPromise.java:570)
			at io.netty.util.concurrent.DefaultPromise.notifyListenersNow(DefaultPromise.java:549)
			at io.netty.util.concurrent.DefaultPromise.notifyListeners(DefaultPromise.java:490)
			at io.netty.util.concurrent.DefaultPromise.setValue0(DefaultPromise.java:615)
			at io.netty.util.concurrent.DefaultPromise.setFailure0(DefaultPromise.java:608)
			at io.netty.util.concurrent.DefaultPromise.tryFailure(DefaultPromise.java:117)
			at io.netty.channel.nio.AbstractNioChannel$AbstractNioUnsafe.fulfillConnectPromise(AbstractNioChannel.java:321)
			at io.netty.channel.nio.AbstractNioChannel$AbstractNioUnsafe.finishConnect(AbstractNioChannel.java:337)
			at io.netty.channel.nio.NioEventLoop.processSelectedKey(NioEventLoop.java:688)
			at io.netty.channel.nio.NioEventLoop.processSelectedKeysOptimized(NioEventLoop.java:635)
			at io.netty.channel.nio.NioEventLoop.processSelectedKeys(NioEventLoop.java:552)
			at io.netty.channel.nio.NioEventLoop.run(NioEventLoop.java:514)
			... 4 common frames omitted
		Caused by: io.netty.channel.AbstractChannel$AnnotatedNoRouteToHostException: No route to host: no further information: /************:7005
		Caused by: java.net.NoRouteToHostException: No route to host: no further information
			at sun.nio.ch.SocketChannelImpl.checkConnect(Native Method)
			at sun.nio.ch.SocketChannelImpl.finishConnect(SocketChannelImpl.java:717)
			at io.netty.channel.socket.nio.NioSocketChannel.doFinishConnect(NioSocketChannel.java:330)
			at io.netty.channel.nio.AbstractNioChannel$AbstractNioUnsafe.finishConnect(AbstractNioChannel.java:334)
			at io.netty.channel.nio.NioEventLoop.processSelectedKey(NioEventLoop.java:688)
			at io.netty.channel.nio.NioEventLoop.processSelectedKeysOptimized(NioEventLoop.java:635)
			at io.netty.channel.nio.NioEventLoop.processSelectedKeys(NioEventLoop.java:552)
			at io.netty.channel.nio.NioEventLoop.run(NioEventLoop.java:514)
			at io.netty.util.concurrent.SingleThreadEventExecutor$6.run(SingleThreadEventExecutor.java:1050)
			at io.netty.util.internal.ThreadExecutorMap$2.run(ThreadExecutorMap.java:74)
			at io.netty.util.concurrent.FastThreadLocalRunnable.run(FastThreadLocalRunnable.java:30)
			at java.lang.Thread.run(Thread.java:748)
	Caused by: io.lettuce.core.RedisConnectionException: Unable to connect to [************:7002]: No route to host: no further information: /************:7002
		at io.lettuce.core.cluster.topology.ClusterTopologyRefresh.lambda$getConnections$0(ClusterTopologyRefresh.java:290)
		at java.util.concurrent.CompletableFuture.uniWhenComplete(CompletableFuture.java:760)
		at java.util.concurrent.CompletableFuture$UniWhenComplete.tryFire(CompletableFuture.java:736)
		at java.util.concurrent.CompletableFuture.postComplete(CompletableFuture.java:474)
		at java.util.concurrent.CompletableFuture.completeExceptionally(CompletableFuture.java:1977)
		at io.lettuce.core.AbstractRedisClient.lambda$initializeChannelAsync0$4(AbstractRedisClient.java:329)
		at io.netty.util.concurrent.DefaultPromise.notifyListener0(DefaultPromise.java:577)
		at io.netty.util.concurrent.DefaultPromise.notifyListeners0(DefaultPromise.java:570)
		at io.netty.util.concurrent.DefaultPromise.notifyListenersNow(DefaultPromise.java:549)
		at io.netty.util.concurrent.DefaultPromise.notifyListeners(DefaultPromise.java:490)
		at io.netty.util.concurrent.DefaultPromise.setValue0(DefaultPromise.java:615)
		at io.netty.util.concurrent.DefaultPromise.setFailure0(DefaultPromise.java:608)
		at io.netty.util.concurrent.DefaultPromise.tryFailure(DefaultPromise.java:117)
		at io.netty.channel.nio.AbstractNioChannel$AbstractNioUnsafe.fulfillConnectPromise(AbstractNioChannel.java:321)
		at io.netty.channel.nio.AbstractNioChannel$AbstractNioUnsafe.finishConnect(AbstractNioChannel.java:337)
		at io.netty.channel.nio.NioEventLoop.processSelectedKey(NioEventLoop.java:688)
		at io.netty.channel.nio.NioEventLoop.processSelectedKeysOptimized(NioEventLoop.java:635)
		at io.netty.channel.nio.NioEventLoop.processSelectedKeys(NioEventLoop.java:552)
		at io.netty.channel.nio.NioEventLoop.run(NioEventLoop.java:514)
		... 4 common frames omitted
	Caused by: io.netty.channel.AbstractChannel$AnnotatedNoRouteToHostException: No route to host: no further information: /************:7002
	Caused by: java.net.NoRouteToHostException: No route to host: no further information
		at sun.nio.ch.SocketChannelImpl.checkConnect(Native Method)
		at sun.nio.ch.SocketChannelImpl.finishConnect(SocketChannelImpl.java:717)
		at io.netty.channel.socket.nio.NioSocketChannel.doFinishConnect(NioSocketChannel.java:330)
		at io.netty.channel.nio.AbstractNioChannel$AbstractNioUnsafe.finishConnect(AbstractNioChannel.java:334)
		at io.netty.channel.nio.NioEventLoop.processSelectedKey(NioEventLoop.java:688)
		at io.netty.channel.nio.NioEventLoop.processSelectedKeysOptimized(NioEventLoop.java:635)
		at io.netty.channel.nio.NioEventLoop.processSelectedKeys(NioEventLoop.java:552)
		at io.netty.channel.nio.NioEventLoop.run(NioEventLoop.java:514)
		at io.netty.util.concurrent.SingleThreadEventExecutor$6.run(SingleThreadEventExecutor.java:1050)
		at io.netty.util.internal.ThreadExecutorMap$2.run(ThreadExecutorMap.java:74)
		at io.netty.util.concurrent.FastThreadLocalRunnable.run(FastThreadLocalRunnable.java:30)
		at java.lang.Thread.run(Thread.java:748)
Caused by: io.lettuce.core.RedisConnectionException: Unable to establish a connection to Redis Cluster at [RedisURI [host='************', port=7001], RedisURI [host='************', port=7002], RedisURI [host='************', port=7003], RedisURI [host='************', port=7004], RedisURI [host='************', port=7005], RedisURI [host='************', port=7006]]
	at io.lettuce.core.cluster.topology.AsyncConnections.get(AsyncConnections.java:89)
	at io.lettuce.core.cluster.topology.ClusterTopologyRefresh.loadViews(ClusterTopologyRefresh.java:76)
	at io.lettuce.core.cluster.RedisClusterClient.doLoadPartitions(RedisClusterClient.java:865)
	at io.lettuce.core.cluster.RedisClusterClient.loadPartitions(RedisClusterClient.java:845)
	... 10 common frames omitted
	Suppressed: io.lettuce.core.RedisConnectionException: Unable to connect to [************:7005]: No route to host: no further information: /************:7005
		at io.lettuce.core.cluster.topology.ClusterTopologyRefresh.lambda$getConnections$0(ClusterTopologyRefresh.java:290)
		at java.util.concurrent.CompletableFuture.uniWhenComplete(CompletableFuture.java:760)
		at java.util.concurrent.CompletableFuture$UniWhenComplete.tryFire(CompletableFuture.java:736)
		at java.util.concurrent.CompletableFuture.postComplete(CompletableFuture.java:474)
		at java.util.concurrent.CompletableFuture.completeExceptionally(CompletableFuture.java:1977)
		at io.lettuce.core.AbstractRedisClient.lambda$initializeChannelAsync0$4(AbstractRedisClient.java:329)
		at io.netty.util.concurrent.DefaultPromise.notifyListener0(DefaultPromise.java:577)
		at io.netty.util.concurrent.DefaultPromise.notifyListeners0(DefaultPromise.java:570)
		at io.netty.util.concurrent.DefaultPromise.notifyListenersNow(DefaultPromise.java:549)
		at io.netty.util.concurrent.DefaultPromise.notifyListeners(DefaultPromise.java:490)
		at io.netty.util.concurrent.DefaultPromise.setValue0(DefaultPromise.java:615)
		at io.netty.util.concurrent.DefaultPromise.setFailure0(DefaultPromise.java:608)
		at io.netty.util.concurrent.DefaultPromise.tryFailure(DefaultPromise.java:117)
		at io.netty.channel.nio.AbstractNioChannel$AbstractNioUnsafe.fulfillConnectPromise(AbstractNioChannel.java:321)
		at io.netty.channel.nio.AbstractNioChannel$AbstractNioUnsafe.finishConnect(AbstractNioChannel.java:337)
		at io.netty.channel.nio.NioEventLoop.processSelectedKey(NioEventLoop.java:688)
		at io.netty.channel.nio.NioEventLoop.processSelectedKeysOptimized(NioEventLoop.java:635)
		at io.netty.channel.nio.NioEventLoop.processSelectedKeys(NioEventLoop.java:552)
		at io.netty.channel.nio.NioEventLoop.run(NioEventLoop.java:514)
		... 4 common frames omitted
	Caused by: io.netty.channel.AbstractChannel$AnnotatedNoRouteToHostException: No route to host: no further information: /************:7005
	Caused by: java.net.NoRouteToHostException: No route to host: no further information
		at sun.nio.ch.SocketChannelImpl.checkConnect(Native Method)
		at sun.nio.ch.SocketChannelImpl.finishConnect(SocketChannelImpl.java:717)
		at io.netty.channel.socket.nio.NioSocketChannel.doFinishConnect(NioSocketChannel.java:330)
		at io.netty.channel.nio.AbstractNioChannel$AbstractNioUnsafe.finishConnect(AbstractNioChannel.java:334)
		at io.netty.channel.nio.NioEventLoop.processSelectedKey(NioEventLoop.java:688)
		at io.netty.channel.nio.NioEventLoop.processSelectedKeysOptimized(NioEventLoop.java:635)
		at io.netty.channel.nio.NioEventLoop.processSelectedKeys(NioEventLoop.java:552)
		at io.netty.channel.nio.NioEventLoop.run(NioEventLoop.java:514)
		at io.netty.util.concurrent.SingleThreadEventExecutor$6.run(SingleThreadEventExecutor.java:1050)
		at io.netty.util.internal.ThreadExecutorMap$2.run(ThreadExecutorMap.java:74)
		at io.netty.util.concurrent.FastThreadLocalRunnable.run(FastThreadLocalRunnable.java:30)
		at java.lang.Thread.run(Thread.java:748)
	Suppressed: io.lettuce.core.RedisConnectionException: Unable to connect to [************:7002]: No route to host: no further information: /************:7002
		at io.lettuce.core.cluster.topology.ClusterTopologyRefresh.lambda$getConnections$0(ClusterTopologyRefresh.java:290)
		at java.util.concurrent.CompletableFuture.uniWhenComplete(CompletableFuture.java:760)
		at java.util.concurrent.CompletableFuture$UniWhenComplete.tryFire(CompletableFuture.java:736)
		at java.util.concurrent.CompletableFuture.postComplete(CompletableFuture.java:474)
		at java.util.concurrent.CompletableFuture.completeExceptionally(CompletableFuture.java:1977)
		at io.lettuce.core.AbstractRedisClient.lambda$initializeChannelAsync0$4(AbstractRedisClient.java:329)
		at io.netty.util.concurrent.DefaultPromise.notifyListener0(DefaultPromise.java:577)
		at io.netty.util.concurrent.DefaultPromise.notifyListeners0(DefaultPromise.java:570)
		at io.netty.util.concurrent.DefaultPromise.notifyListenersNow(DefaultPromise.java:549)
		at io.netty.util.concurrent.DefaultPromise.notifyListeners(DefaultPromise.java:490)
		at io.netty.util.concurrent.DefaultPromise.setValue0(DefaultPromise.java:615)
		at io.netty.util.concurrent.DefaultPromise.setFailure0(DefaultPromise.java:608)
		at io.netty.util.concurrent.DefaultPromise.tryFailure(DefaultPromise.java:117)
		at io.netty.channel.nio.AbstractNioChannel$AbstractNioUnsafe.fulfillConnectPromise(AbstractNioChannel.java:321)
		at io.netty.channel.nio.AbstractNioChannel$AbstractNioUnsafe.finishConnect(AbstractNioChannel.java:337)
		at io.netty.channel.nio.NioEventLoop.processSelectedKey(NioEventLoop.java:688)
		at io.netty.channel.nio.NioEventLoop.processSelectedKeysOptimized(NioEventLoop.java:635)
		at io.netty.channel.nio.NioEventLoop.processSelectedKeys(NioEventLoop.java:552)
		at io.netty.channel.nio.NioEventLoop.run(NioEventLoop.java:514)
		... 4 common frames omitted
	Caused by: io.netty.channel.AbstractChannel$AnnotatedNoRouteToHostException: No route to host: no further information: /************:7002
	Caused by: java.net.NoRouteToHostException: No route to host: no further information
		at sun.nio.ch.SocketChannelImpl.checkConnect(Native Method)
		at sun.nio.ch.SocketChannelImpl.finishConnect(SocketChannelImpl.java:717)
		at io.netty.channel.socket.nio.NioSocketChannel.doFinishConnect(NioSocketChannel.java:330)
		at io.netty.channel.nio.AbstractNioChannel$AbstractNioUnsafe.finishConnect(AbstractNioChannel.java:334)
		at io.netty.channel.nio.NioEventLoop.processSelectedKey(NioEventLoop.java:688)
		at io.netty.channel.nio.NioEventLoop.processSelectedKeysOptimized(NioEventLoop.java:635)
		at io.netty.channel.nio.NioEventLoop.processSelectedKeys(NioEventLoop.java:552)
		at io.netty.channel.nio.NioEventLoop.run(NioEventLoop.java:514)
		at io.netty.util.concurrent.SingleThreadEventExecutor$6.run(SingleThreadEventExecutor.java:1050)
		at io.netty.util.internal.ThreadExecutorMap$2.run(ThreadExecutorMap.java:74)
		at io.netty.util.concurrent.FastThreadLocalRunnable.run(FastThreadLocalRunnable.java:30)
		at java.lang.Thread.run(Thread.java:748)
	Suppressed: io.lettuce.core.RedisConnectionException: Unable to connect to [************:7004]: No route to host: no further information: /************:7004
		at io.lettuce.core.cluster.topology.ClusterTopologyRefresh.lambda$getConnections$0(ClusterTopologyRefresh.java:290)
		at java.util.concurrent.CompletableFuture.uniWhenComplete(CompletableFuture.java:760)
		at java.util.concurrent.CompletableFuture$UniWhenComplete.tryFire(CompletableFuture.java:736)
		at java.util.concurrent.CompletableFuture.postComplete(CompletableFuture.java:474)
		at java.util.concurrent.CompletableFuture.completeExceptionally(CompletableFuture.java:1977)
		at io.lettuce.core.AbstractRedisClient.lambda$initializeChannelAsync0$4(AbstractRedisClient.java:329)
		at io.netty.util.concurrent.DefaultPromise.notifyListener0(DefaultPromise.java:577)
		at io.netty.util.concurrent.DefaultPromise.notifyListeners0(DefaultPromise.java:570)
		at io.netty.util.concurrent.DefaultPromise.notifyListenersNow(DefaultPromise.java:549)
		at io.netty.util.concurrent.DefaultPromise.notifyListeners(DefaultPromise.java:490)
		at io.netty.util.concurrent.DefaultPromise.setValue0(DefaultPromise.java:615)
		at io.netty.util.concurrent.DefaultPromise.setFailure0(DefaultPromise.java:608)
		at io.netty.util.concurrent.DefaultPromise.tryFailure(DefaultPromise.java:117)
		at io.netty.channel.nio.AbstractNioChannel$AbstractNioUnsafe.fulfillConnectPromise(AbstractNioChannel.java:321)
		at io.netty.channel.nio.AbstractNioChannel$AbstractNioUnsafe.finishConnect(AbstractNioChannel.java:337)
		at io.netty.channel.nio.NioEventLoop.processSelectedKey(NioEventLoop.java:688)
		at io.netty.channel.nio.NioEventLoop.processSelectedKeysOptimized(NioEventLoop.java:635)
		at io.netty.channel.nio.NioEventLoop.processSelectedKeys(NioEventLoop.java:552)
		at io.netty.channel.nio.NioEventLoop.run(NioEventLoop.java:514)
		... 4 common frames omitted
	Caused by: io.netty.channel.AbstractChannel$AnnotatedNoRouteToHostException: No route to host: no further information: /************:7004
	Caused by: java.net.NoRouteToHostException: No route to host: no further information
		at sun.nio.ch.SocketChannelImpl.checkConnect(Native Method)
		at sun.nio.ch.SocketChannelImpl.finishConnect(SocketChannelImpl.java:717)
		at io.netty.channel.socket.nio.NioSocketChannel.doFinishConnect(NioSocketChannel.java:330)
		at io.netty.channel.nio.AbstractNioChannel$AbstractNioUnsafe.finishConnect(AbstractNioChannel.java:334)
		at io.netty.channel.nio.NioEventLoop.processSelectedKey(NioEventLoop.java:688)
		at io.netty.channel.nio.NioEventLoop.processSelectedKeysOptimized(NioEventLoop.java:635)
		at io.netty.channel.nio.NioEventLoop.processSelectedKeys(NioEventLoop.java:552)
		at io.netty.channel.nio.NioEventLoop.run(NioEventLoop.java:514)
		at io.netty.util.concurrent.SingleThreadEventExecutor$6.run(SingleThreadEventExecutor.java:1050)
		at io.netty.util.internal.ThreadExecutorMap$2.run(ThreadExecutorMap.java:74)
		at io.netty.util.concurrent.FastThreadLocalRunnable.run(FastThreadLocalRunnable.java:30)
		at java.lang.Thread.run(Thread.java:748)
	Suppressed: io.lettuce.core.RedisConnectionException: Unable to connect to [************:7001]: No route to host: no further information: /************:7001
		at io.lettuce.core.cluster.topology.ClusterTopologyRefresh.lambda$getConnections$0(ClusterTopologyRefresh.java:290)
		at java.util.concurrent.CompletableFuture.uniWhenComplete(CompletableFuture.java:760)
		at java.util.concurrent.CompletableFuture$UniWhenComplete.tryFire(CompletableFuture.java:736)
		at java.util.concurrent.CompletableFuture.postComplete(CompletableFuture.java:474)
		at java.util.concurrent.CompletableFuture.completeExceptionally(CompletableFuture.java:1977)
		at io.lettuce.core.AbstractRedisClient.lambda$initializeChannelAsync0$4(AbstractRedisClient.java:329)
		at io.netty.util.concurrent.DefaultPromise.notifyListener0(DefaultPromise.java:577)
		at io.netty.util.concurrent.DefaultPromise.notifyListeners0(DefaultPromise.java:570)
		at io.netty.util.concurrent.DefaultPromise.notifyListenersNow(DefaultPromise.java:549)
		at io.netty.util.concurrent.DefaultPromise.notifyListeners(DefaultPromise.java:490)
		at io.netty.util.concurrent.DefaultPromise.setValue0(DefaultPromise.java:615)
		at io.netty.util.concurrent.DefaultPromise.setFailure0(DefaultPromise.java:608)
		at io.netty.util.concurrent.DefaultPromise.tryFailure(DefaultPromise.java:117)
		at io.netty.channel.nio.AbstractNioChannel$AbstractNioUnsafe.fulfillConnectPromise(AbstractNioChannel.java:321)
		at io.netty.channel.nio.AbstractNioChannel$AbstractNioUnsafe.finishConnect(AbstractNioChannel.java:337)
		at io.netty.channel.nio.NioEventLoop.processSelectedKey(NioEventLoop.java:688)
		at io.netty.channel.nio.NioEventLoop.processSelectedKeysOptimized(NioEventLoop.java:635)
		at io.netty.channel.nio.NioEventLoop.processSelectedKeys(NioEventLoop.java:552)
		at io.netty.channel.nio.NioEventLoop.run(NioEventLoop.java:514)
		... 4 common frames omitted
	Caused by: io.netty.channel.AbstractChannel$AnnotatedNoRouteToHostException: No route to host: no further information: /************:7001
	Caused by: java.net.NoRouteToHostException: No route to host: no further information
		at sun.nio.ch.SocketChannelImpl.checkConnect(Native Method)
		at sun.nio.ch.SocketChannelImpl.finishConnect(SocketChannelImpl.java:717)
		at io.netty.channel.socket.nio.NioSocketChannel.doFinishConnect(NioSocketChannel.java:330)
		at io.netty.channel.nio.AbstractNioChannel$AbstractNioUnsafe.finishConnect(AbstractNioChannel.java:334)
		at io.netty.channel.nio.NioEventLoop.processSelectedKey(NioEventLoop.java:688)
		at io.netty.channel.nio.NioEventLoop.processSelectedKeysOptimized(NioEventLoop.java:635)
		at io.netty.channel.nio.NioEventLoop.processSelectedKeys(NioEventLoop.java:552)
		at io.netty.channel.nio.NioEventLoop.run(NioEventLoop.java:514)
		at io.netty.util.concurrent.SingleThreadEventExecutor$6.run(SingleThreadEventExecutor.java:1050)
		at io.netty.util.internal.ThreadExecutorMap$2.run(ThreadExecutorMap.java:74)
		at io.netty.util.concurrent.FastThreadLocalRunnable.run(FastThreadLocalRunnable.java:30)
		at java.lang.Thread.run(Thread.java:748)
	Suppressed: io.lettuce.core.RedisConnectionException: Unable to connect to [************:7003]: No route to host: no further information: /************:7003
		at io.lettuce.core.cluster.topology.ClusterTopologyRefresh.lambda$getConnections$0(ClusterTopologyRefresh.java:290)
		at java.util.concurrent.CompletableFuture.uniWhenComplete(CompletableFuture.java:760)
		at java.util.concurrent.CompletableFuture$UniWhenComplete.tryFire(CompletableFuture.java:736)
		at java.util.concurrent.CompletableFuture.postComplete(CompletableFuture.java:474)
		at java.util.concurrent.CompletableFuture.completeExceptionally(CompletableFuture.java:1977)
		at io.lettuce.core.AbstractRedisClient.lambda$initializeChannelAsync0$4(AbstractRedisClient.java:329)
		at io.netty.util.concurrent.DefaultPromise.notifyListener0(DefaultPromise.java:577)
		at io.netty.util.concurrent.DefaultPromise.notifyListeners0(DefaultPromise.java:570)
		at io.netty.util.concurrent.DefaultPromise.notifyListenersNow(DefaultPromise.java:549)
		at io.netty.util.concurrent.DefaultPromise.notifyListeners(DefaultPromise.java:490)
		at io.netty.util.concurrent.DefaultPromise.setValue0(DefaultPromise.java:615)
		at io.netty.util.concurrent.DefaultPromise.setFailure0(DefaultPromise.java:608)
		at io.netty.util.concurrent.DefaultPromise.tryFailure(DefaultPromise.java:117)
		at io.netty.channel.nio.AbstractNioChannel$AbstractNioUnsafe.fulfillConnectPromise(AbstractNioChannel.java:321)
		at io.netty.channel.nio.AbstractNioChannel$AbstractNioUnsafe.finishConnect(AbstractNioChannel.java:337)
		at io.netty.channel.nio.NioEventLoop.processSelectedKey(NioEventLoop.java:688)
		at io.netty.channel.nio.NioEventLoop.processSelectedKeysOptimized(NioEventLoop.java:635)
		at io.netty.channel.nio.NioEventLoop.processSelectedKeys(NioEventLoop.java:552)
		at io.netty.channel.nio.NioEventLoop.run(NioEventLoop.java:514)
		... 4 common frames omitted
	Caused by: io.netty.channel.AbstractChannel$AnnotatedNoRouteToHostException: No route to host: no further information: /************:7003
	Caused by: java.net.NoRouteToHostException: No route to host: no further information
		at sun.nio.ch.SocketChannelImpl.checkConnect(Native Method)
		at sun.nio.ch.SocketChannelImpl.finishConnect(SocketChannelImpl.java:717)
		at io.netty.channel.socket.nio.NioSocketChannel.doFinishConnect(NioSocketChannel.java:330)
		at io.netty.channel.nio.AbstractNioChannel$AbstractNioUnsafe.finishConnect(AbstractNioChannel.java:334)
		at io.netty.channel.nio.NioEventLoop.processSelectedKey(NioEventLoop.java:688)
		at io.netty.channel.nio.NioEventLoop.processSelectedKeysOptimized(NioEventLoop.java:635)
		at io.netty.channel.nio.NioEventLoop.processSelectedKeys(NioEventLoop.java:552)
		at io.netty.channel.nio.NioEventLoop.run(NioEventLoop.java:514)
		at io.netty.util.concurrent.SingleThreadEventExecutor$6.run(SingleThreadEventExecutor.java:1050)
		at io.netty.util.internal.ThreadExecutorMap$2.run(ThreadExecutorMap.java:74)
		at io.netty.util.concurrent.FastThreadLocalRunnable.run(FastThreadLocalRunnable.java:30)
		at java.lang.Thread.run(Thread.java:748)
Caused by: io.lettuce.core.RedisConnectionException: Unable to connect to [************:7006]: No route to host: no further information: /************:7006
	at io.lettuce.core.cluster.topology.ClusterTopologyRefresh.lambda$getConnections$0(ClusterTopologyRefresh.java:290)
	at java.util.concurrent.CompletableFuture.uniWhenComplete(CompletableFuture.java:760)
	at java.util.concurrent.CompletableFuture$UniWhenComplete.tryFire(CompletableFuture.java:736)
	at java.util.concurrent.CompletableFuture.postComplete(CompletableFuture.java:474)
	at java.util.concurrent.CompletableFuture.completeExceptionally(CompletableFuture.java:1977)
	at io.lettuce.core.AbstractRedisClient.lambda$initializeChannelAsync0$4(AbstractRedisClient.java:329)
	at io.netty.util.concurrent.DefaultPromise.notifyListener0(DefaultPromise.java:577)
	at io.netty.util.concurrent.DefaultPromise.notifyListeners0(DefaultPromise.java:570)
	at io.netty.util.concurrent.DefaultPromise.notifyListenersNow(DefaultPromise.java:549)
	at io.netty.util.concurrent.DefaultPromise.notifyListeners(DefaultPromise.java:490)
	at io.netty.util.concurrent.DefaultPromise.setValue0(DefaultPromise.java:615)
	at io.netty.util.concurrent.DefaultPromise.setFailure0(DefaultPromise.java:608)
	at io.netty.util.concurrent.DefaultPromise.tryFailure(DefaultPromise.java:117)
	at io.netty.channel.nio.AbstractNioChannel$AbstractNioUnsafe.fulfillConnectPromise(AbstractNioChannel.java:321)
	at io.netty.channel.nio.AbstractNioChannel$AbstractNioUnsafe.finishConnect(AbstractNioChannel.java:337)
	at io.netty.channel.nio.NioEventLoop.processSelectedKey(NioEventLoop.java:688)
	at io.netty.channel.nio.NioEventLoop.processSelectedKeysOptimized(NioEventLoop.java:635)
	at io.netty.channel.nio.NioEventLoop.processSelectedKeys(NioEventLoop.java:552)
	at io.netty.channel.nio.NioEventLoop.run(NioEventLoop.java:514)
	... 4 common frames omitted
Caused by: io.netty.channel.AbstractChannel$AnnotatedNoRouteToHostException: No route to host: no further information: /************:7006
Caused by: java.net.NoRouteToHostException: No route to host: no further information
	at sun.nio.ch.SocketChannelImpl.checkConnect(Native Method)
	at sun.nio.ch.SocketChannelImpl.finishConnect(SocketChannelImpl.java:717)
	at io.netty.channel.socket.nio.NioSocketChannel.doFinishConnect(NioSocketChannel.java:330)
	at io.netty.channel.nio.AbstractNioChannel$AbstractNioUnsafe.finishConnect(AbstractNioChannel.java:334)
	at io.netty.channel.nio.NioEventLoop.processSelectedKey(NioEventLoop.java:688)
	at io.netty.channel.nio.NioEventLoop.processSelectedKeysOptimized(NioEventLoop.java:635)
	at io.netty.channel.nio.NioEventLoop.processSelectedKeys(NioEventLoop.java:552)
	at io.netty.channel.nio.NioEventLoop.run(NioEventLoop.java:514)
	at io.netty.util.concurrent.SingleThreadEventExecutor$6.run(SingleThreadEventExecutor.java:1050)
	at io.netty.util.internal.ThreadExecutorMap$2.run(ThreadExecutorMap.java:74)
	at io.netty.util.concurrent.FastThreadLocalRunnable.run(FastThreadLocalRunnable.java:30)
	at java.lang.Thread.run(Thread.java:748)
2025-06-02 05:00:23.754 [AMQP Connection ************:5672] WARN  com.rabbitmq.client.impl.ForgivingExceptionHandler.log Line:115 - An unexpected connection driver error occured (Exception message: Connection reset)
2025-06-02 05:00:23.800 [AMQP Connection ************:5672] WARN  com.rabbitmq.client.impl.ForgivingExceptionHandler.log Line:115 - An unexpected connection driver error occured (Exception message: Connection reset)
2025-06-02 05:00:24.781 [AMQP Connection ************:5672] WARN  com.rabbitmq.client.impl.ForgivingExceptionHandler.log Line:115 - An unexpected connection driver error occured (Exception message: Connection reset)
2025-06-02 05:00:26.774 [AMQP Connection ************:5672] WARN  com.rabbitmq.client.impl.ForgivingExceptionHandler.log Line:115 - An unexpected connection driver error occured (Exception message: Connection reset)
2025-06-02 05:05:00.010 [pool-14-thread-7] WARN  com.simbest.boot.base.repository.CustomDynamicWhere.queryForList Line:50 - 自定义查询SQL输出为：【select * from US_TODO_MODEL t  where t.send_flag = 0 and t.work_flag = 0 and t.enabled =1】
2025-06-02 05:10:00.015 [pool-14-thread-1] WARN  com.simbest.boot.base.repository.CustomDynamicWhere.queryForList Line:50 - 自定义查询SQL输出为：【select * from US_TODO_MODEL t  where t.send_flag = 0 and t.work_flag = 0 and t.enabled =1】
2025-06-02 05:10:09.312 [pool-14-thread-7] WARN  com.simbest.boot.base.repository.CustomDynamicWhere.queryForList Line:50 - 自定义查询SQL输出为：【select * from US_TODO_MODEL t  where t.send_flag = 0 and t.work_flag = 1 and t.enabled =1】
2025-06-02 05:15:00.015 [pool-14-thread-4] WARN  com.simbest.boot.base.repository.CustomDynamicWhere.queryForList Line:50 - 自定义查询SQL输出为：【select * from US_TODO_MODEL t  where t.send_flag = 0 and t.work_flag = 0 and t.enabled =1】
2025-06-02 05:15:00.015 [pool-14-thread-8] WARN  com.simbest.boot.base.repository.CustomDynamicWhere.queryForList Line:50 - 自定义查询SQL输出为：【select * from US_TODO_MODEL t  where t.send_flag = 0 and t.work_flag = 1 and t.enabled =1】
2025-06-02 05:20:00.020 [pool-14-thread-10] WARN  com.simbest.boot.base.repository.CustomDynamicWhere.queryForList Line:50 - 自定义查询SQL输出为：【select * from US_TODO_MODEL t  where t.send_flag = 0 and t.work_flag = 1 and t.enabled =1】
2025-06-02 05:20:00.023 [pool-14-thread-3] WARN  com.simbest.boot.base.repository.CustomDynamicWhere.queryForList Line:50 - 自定义查询SQL输出为：【select * from US_TODO_MODEL t  where t.send_flag = 0 and t.work_flag = 0 and t.enabled =1】
2025-06-02 05:40:55.527 [AMQP Connection ************:5672] WARN  com.rabbitmq.client.impl.ForgivingExceptionHandler.log Line:115 - An unexpected connection driver error occured (Exception message: Connection reset)
2025-06-02 05:40:55.527 [AMQP Connection ************:5672] WARN  com.rabbitmq.client.impl.ForgivingExceptionHandler.log Line:115 - An unexpected connection driver error occured (Exception message: Connection reset)
2025-06-02 05:40:56.516 [AMQP Connection ************:5672] WARN  com.rabbitmq.client.impl.ForgivingExceptionHandler.log Line:115 - An unexpected connection driver error occured (Exception message: Connection reset)
2025-06-02 05:40:58.582 [AMQP Connection ************:5672] WARN  com.rabbitmq.client.impl.ForgivingExceptionHandler.log Line:115 - An unexpected connection driver error occured (Exception message: Connection reset)
2025-06-02 06:21:55.424 [AMQP Connection ************:5672] WARN  com.rabbitmq.client.impl.ForgivingExceptionHandler.log Line:115 - An unexpected connection driver error occured (Exception message: Connection reset)
2025-06-02 06:21:55.433 [AMQP Connection ************:5672] WARN  com.rabbitmq.client.impl.ForgivingExceptionHandler.log Line:115 - An unexpected connection driver error occured (Exception message: Connection reset)
2025-06-02 06:21:56.428 [AMQP Connection ************:5672] WARN  com.rabbitmq.client.impl.ForgivingExceptionHandler.log Line:115 - An unexpected connection driver error occured (Exception message: Connection reset)
2025-06-02 06:21:58.447 [AMQP Connection ************:5672] WARN  com.rabbitmq.client.impl.ForgivingExceptionHandler.log Line:115 - An unexpected connection driver error occured (Exception message: Connection reset)
2025-06-02 06:35:00.015 [pool-14-thread-2] WARN  com.simbest.boot.base.repository.CustomDynamicWhere.queryForList Line:50 - 自定义查询SQL输出为：【select * from US_TODO_MODEL t  where t.send_flag = 0 and t.work_flag = 0 and t.enabled =1】
2025-06-02 06:40:00.011 [pool-14-thread-9] WARN  com.simbest.boot.base.repository.CustomDynamicWhere.queryForList Line:50 - 自定义查询SQL输出为：【select * from US_TODO_MODEL t  where t.send_flag = 0 and t.work_flag = 0 and t.enabled =1】
2025-06-02 07:02:26.269 [AMQP Connection ************:5672] WARN  com.rabbitmq.client.impl.ForgivingExceptionHandler.log Line:115 - An unexpected connection driver error occured (Exception message: Connection reset)
2025-06-02 07:02:26.269 [AMQP Connection ************:5672] WARN  com.rabbitmq.client.impl.ForgivingExceptionHandler.log Line:115 - An unexpected connection driver error occured (Exception message: Connection reset)
2025-06-02 07:02:27.279 [AMQP Connection ************:5672] WARN  com.rabbitmq.client.impl.ForgivingExceptionHandler.log Line:115 - An unexpected connection driver error occured (Exception message: Connection reset)
2025-06-02 07:02:29.280 [AMQP Connection ************:5672] WARN  com.rabbitmq.client.impl.ForgivingExceptionHandler.log Line:115 - An unexpected connection driver error occured (Exception message: Connection reset)
2025-06-02 07:05:03.016 [pool-14-thread-10] WARN  com.simbest.boot.base.repository.CustomDynamicWhere.queryForList Line:50 - 自定义查询SQL输出为：【select * from US_TODO_MODEL t  where t.send_flag = 0 and t.work_flag = 0 and t.enabled =1】
2025-06-02 07:42:57.205 [AMQP Connection ************:5672] WARN  com.rabbitmq.client.impl.ForgivingExceptionHandler.log Line:115 - An unexpected connection driver error occured (Exception message: Connection reset)
2025-06-02 07:42:57.205 [AMQP Connection ************:5672] WARN  com.rabbitmq.client.impl.ForgivingExceptionHandler.log Line:115 - An unexpected connection driver error occured (Exception message: Connection reset)
2025-06-02 07:42:58.234 [AMQP Connection ************:5672] WARN  com.rabbitmq.client.impl.ForgivingExceptionHandler.log Line:115 - An unexpected connection driver error occured (Exception message: Connection reset)
2025-06-02 07:43:00.240 [AMQP Connection ************:5672] WARN  com.rabbitmq.client.impl.ForgivingExceptionHandler.log Line:115 - An unexpected connection driver error occured (Exception message: Connection reset)
2025-06-02 07:55:00.010 [pool-14-thread-1] WARN  com.simbest.boot.base.repository.CustomDynamicWhere.queryForList Line:50 - 自定义查询SQL输出为：【select * from US_TODO_MODEL t  where t.send_flag = 0 and t.work_flag = 0 and t.enabled =1】
2025-06-02 08:00:00.015 [pool-14-thread-5] WARN  com.simbest.boot.base.repository.CustomDynamicWhere.queryForList Line:50 - 自定义查询SQL输出为：【select * from US_TODO_MODEL t  where t.send_flag = 0 and t.work_flag = 0 and t.enabled =1】
2025-06-02 08:23:59.276 [AMQP Connection ************:5672] WARN  com.rabbitmq.client.impl.ForgivingExceptionHandler.log Line:115 - An unexpected connection driver error occured (Exception message: Connection reset)
2025-06-02 08:23:59.276 [AMQP Connection ************:5672] WARN  com.rabbitmq.client.impl.ForgivingExceptionHandler.log Line:115 - An unexpected connection driver error occured (Exception message: Connection reset)
2025-06-02 08:24:00.335 [AMQP Connection ************:5672] WARN  com.rabbitmq.client.impl.ForgivingExceptionHandler.log Line:115 - An unexpected connection driver error occured (Exception message: Connection reset)
2025-06-02 08:24:02.313 [AMQP Connection ************:5672] WARN  com.rabbitmq.client.impl.ForgivingExceptionHandler.log Line:115 - An unexpected connection driver error occured (Exception message: Connection reset)
2025-06-02 08:30:00.019 [pool-14-thread-10] WARN  com.simbest.boot.base.repository.CustomDynamicWhere.queryForList Line:50 - 自定义查询SQL输出为：【select * from US_TODO_MODEL t  where t.send_flag = 0 and t.work_flag = 0 and t.enabled =1】
2025-06-02 08:30:06.151 [pool-14-thread-1] WARN  com.simbest.boot.base.repository.CustomDynamicWhere.queryForList Line:50 - 自定义查询SQL输出为：【select * from US_TODO_MODEL t  where t.send_flag = 0 and t.work_flag = 1 and t.enabled =1】
2025-06-02 08:35:00.025 [pool-14-thread-8] WARN  com.simbest.boot.base.repository.CustomDynamicWhere.queryForList Line:50 - 自定义查询SQL输出为：【select * from US_TODO_MODEL t  where t.send_flag = 0 and t.work_flag = 1 and t.enabled =1】
2025-06-02 08:35:00.025 [pool-14-thread-7] WARN  com.simbest.boot.base.repository.CustomDynamicWhere.queryForList Line:50 - 自定义查询SQL输出为：【select * from US_TODO_MODEL t  where t.send_flag = 0 and t.work_flag = 0 and t.enabled =1】
2025-06-02 08:40:00.009 [pool-14-thread-4] WARN  com.simbest.boot.base.repository.CustomDynamicWhere.queryForList Line:50 - 自定义查询SQL输出为：【select * from US_TODO_MODEL t  where t.send_flag = 0 and t.work_flag = 1 and t.enabled =1】
2025-06-02 08:40:00.009 [pool-14-thread-8] WARN  com.simbest.boot.base.repository.CustomDynamicWhere.queryForList Line:50 - 自定义查询SQL输出为：【select * from US_TODO_MODEL t  where t.send_flag = 0 and t.work_flag = 0 and t.enabled =1】
2025-06-02 09:04:30.903 [AMQP Connection ************:5672] WARN  com.rabbitmq.client.impl.ForgivingExceptionHandler.log Line:115 - An unexpected connection driver error occured (Exception message: Connection reset)
2025-06-02 09:04:30.948 [AMQP Connection ************:5672] WARN  com.rabbitmq.client.impl.ForgivingExceptionHandler.log Line:115 - An unexpected connection driver error occured (Exception message: Connection reset)
2025-06-02 09:04:31.967 [AMQP Connection ************:5672] WARN  com.rabbitmq.client.impl.ForgivingExceptionHandler.log Line:115 - An unexpected connection driver error occured (Exception message: Connection reset)
2025-06-02 09:04:33.990 [AMQP Connection ************:5672] WARN  com.rabbitmq.client.impl.ForgivingExceptionHandler.log Line:115 - An unexpected connection driver error occured (Exception message: Connection reset)
2025-06-02 09:10:00.015 [pool-14-thread-1] WARN  com.simbest.boot.base.repository.CustomDynamicWhere.queryForList Line:50 - 自定义查询SQL输出为：【select * from US_TODO_MODEL t  where t.send_flag = 0 and t.work_flag = 0 and t.enabled =1】
2025-06-02 09:10:06.127 [pool-14-thread-2] WARN  com.simbest.boot.base.repository.CustomDynamicWhere.queryForList Line:50 - 自定义查询SQL输出为：【select * from US_TODO_MODEL t  where t.send_flag = 0 and t.work_flag = 1 and t.enabled =1】
2025-06-02 09:15:00.010 [pool-14-thread-3] WARN  com.simbest.boot.base.repository.CustomDynamicWhere.queryForList Line:50 - 自定义查询SQL输出为：【select * from US_TODO_MODEL t  where t.send_flag = 0 and t.work_flag = 0 and t.enabled =1】
2025-06-02 09:15:00.010 [pool-14-thread-7] WARN  com.simbest.boot.base.repository.CustomDynamicWhere.queryForList Line:50 - 自定义查询SQL输出为：【select * from US_TODO_MODEL t  where t.send_flag = 0 and t.work_flag = 1 and t.enabled =1】
2025-06-02 09:20:00.015 [pool-14-thread-1] WARN  com.simbest.boot.base.repository.CustomDynamicWhere.queryForList Line:50 - 自定义查询SQL输出为：【select * from US_TODO_MODEL t  where t.send_flag = 0 and t.work_flag = 1 and t.enabled =1】
2025-06-02 09:20:00.015 [pool-14-thread-2] WARN  com.simbest.boot.base.repository.CustomDynamicWhere.queryForList Line:50 - 自定义查询SQL输出为：【select * from US_TODO_MODEL t  where t.send_flag = 0 and t.work_flag = 0 and t.enabled =1】
2025-06-02 09:45:01.939 [AMQP Connection ************:5672] WARN  com.rabbitmq.client.impl.ForgivingExceptionHandler.log Line:115 - An unexpected connection driver error occured (Exception message: Connection reset)
2025-06-02 09:45:02.079 [AMQP Connection ************:5672] WARN  com.rabbitmq.client.impl.ForgivingExceptionHandler.log Line:115 - An unexpected connection driver error occured (Exception message: Connection reset)
2025-06-02 09:45:02.997 [AMQP Connection ************:5672] WARN  com.rabbitmq.client.impl.ForgivingExceptionHandler.log Line:115 - An unexpected connection driver error occured (Exception message: Connection reset)
2025-06-02 09:45:05.016 [AMQP Connection ************:5672] WARN  com.rabbitmq.client.impl.ForgivingExceptionHandler.log Line:115 - An unexpected connection driver error occured (Exception message: Connection reset)
2025-06-02 10:26:03.849 [AMQP Connection ************:5672] WARN  com.rabbitmq.client.impl.ForgivingExceptionHandler.log Line:115 - An unexpected connection driver error occured (Exception message: Connection reset)
2025-06-02 10:26:04.020 [AMQP Connection ************:5672] WARN  com.rabbitmq.client.impl.ForgivingExceptionHandler.log Line:115 - An unexpected connection driver error occured (Exception message: Connection reset)
2025-06-02 10:26:04.956 [AMQP Connection ************:5672] WARN  com.rabbitmq.client.impl.ForgivingExceptionHandler.log Line:115 - An unexpected connection driver error occured (Exception message: Connection reset)
2025-06-02 10:26:06.994 [AMQP Connection ************:5672] WARN  com.rabbitmq.client.impl.ForgivingExceptionHandler.log Line:115 - An unexpected connection driver error occured (Exception message: Connection reset)
2025-06-02 11:06:34.057 [AMQP Connection ************:5672] WARN  com.rabbitmq.client.impl.ForgivingExceptionHandler.log Line:115 - An unexpected connection driver error occured (Exception message: Connection reset)
2025-06-02 11:06:34.244 [AMQP Connection ************:5672] WARN  com.rabbitmq.client.impl.ForgivingExceptionHandler.log Line:115 - An unexpected connection driver error occured (Exception message: Connection reset)
2025-06-02 11:06:35.238 [AMQP Connection ************:5672] WARN  com.rabbitmq.client.impl.ForgivingExceptionHandler.log Line:115 - An unexpected connection driver error occured (Exception message: Connection reset)
2025-06-02 11:06:37.254 [AMQP Connection ************:5672] WARN  com.rabbitmq.client.impl.ForgivingExceptionHandler.log Line:115 - An unexpected connection driver error occured (Exception message: Connection reset)
2025-06-02 11:47:05.887 [AMQP Connection ************:5672] WARN  com.rabbitmq.client.impl.ForgivingExceptionHandler.log Line:115 - An unexpected connection driver error occured (Exception message: Connection reset)
2025-06-02 11:47:06.087 [AMQP Connection ************:5672] WARN  com.rabbitmq.client.impl.ForgivingExceptionHandler.log Line:115 - An unexpected connection driver error occured (Exception message: Connection reset)
2025-06-02 11:47:07.077 [AMQP Connection ************:5672] WARN  com.rabbitmq.client.impl.ForgivingExceptionHandler.log Line:115 - An unexpected connection driver error occured (Exception message: Connection reset)
2025-06-02 11:47:09.108 [AMQP Connection ************:5672] WARN  com.rabbitmq.client.impl.ForgivingExceptionHandler.log Line:115 - An unexpected connection driver error occured (Exception message: Connection reset)
2025-06-02 12:28:05.773 [AMQP Connection ************:5672] WARN  com.rabbitmq.client.impl.ForgivingExceptionHandler.log Line:115 - An unexpected connection driver error occured (Exception message: Connection reset)
2025-06-02 12:28:06.023 [AMQP Connection ************:5672] WARN  com.rabbitmq.client.impl.ForgivingExceptionHandler.log Line:115 - An unexpected connection driver error occured (Exception message: Connection reset)
2025-06-02 12:28:07.001 [AMQP Connection ************:5672] WARN  com.rabbitmq.client.impl.ForgivingExceptionHandler.log Line:115 - An unexpected connection driver error occured (Exception message: Connection reset)
2025-06-02 12:28:09.010 [AMQP Connection ************:5672] WARN  com.rabbitmq.client.impl.ForgivingExceptionHandler.log Line:115 - An unexpected connection driver error occured (Exception message: Connection reset)
2025-06-02 12:40:00.014 [pool-14-thread-2] WARN  com.simbest.boot.base.repository.CustomDynamicWhere.queryForList Line:50 - 自定义查询SQL输出为：【select * from US_TODO_MODEL t  where t.send_flag = 0 and t.work_flag = 0 and t.enabled =1】
2025-06-02 12:45:00.012 [pool-14-thread-9] WARN  com.simbest.boot.base.repository.CustomDynamicWhere.queryForList Line:50 - 自定义查询SQL输出为：【select * from US_TODO_MODEL t  where t.send_flag = 0 and t.work_flag = 0 and t.enabled =1】
2025-06-02 13:08:36.324 [AMQP Connection ************:5672] WARN  com.rabbitmq.client.impl.ForgivingExceptionHandler.log Line:115 - An unexpected connection driver error occured (Exception message: Connection reset)
2025-06-02 13:08:36.619 [AMQP Connection ************:5672] WARN  com.rabbitmq.client.impl.ForgivingExceptionHandler.log Line:115 - An unexpected connection driver error occured (Exception message: Connection reset)
2025-06-02 13:08:37.554 [AMQP Connection ************:5672] WARN  com.rabbitmq.client.impl.ForgivingExceptionHandler.log Line:115 - An unexpected connection driver error occured (Exception message: Connection reset)
2025-06-02 13:08:39.576 [AMQP Connection ************:5672] WARN  com.rabbitmq.client.impl.ForgivingExceptionHandler.log Line:115 - An unexpected connection driver error occured (Exception message: Connection reset)
2025-06-02 13:15:00.018 [pool-14-thread-7] WARN  com.simbest.boot.base.repository.CustomDynamicWhere.queryForList Line:50 - 自定义查询SQL输出为：【select * from US_TODO_MODEL t  where t.send_flag = 0 and t.work_flag = 0 and t.enabled =1】
2025-06-02 13:15:09.224 [pool-14-thread-10] WARN  com.simbest.boot.base.repository.CustomDynamicWhere.queryForList Line:50 - 自定义查询SQL输出为：【select * from US_TODO_MODEL t  where t.send_flag = 0 and t.work_flag = 1 and t.enabled =1】
2025-06-02 13:20:00.016 [pool-14-thread-1] WARN  com.simbest.boot.base.repository.CustomDynamicWhere.queryForList Line:50 - 自定义查询SQL输出为：【select * from US_TODO_MODEL t  where t.send_flag = 0 and t.work_flag = 0 and t.enabled =1】
2025-06-02 13:20:00.026 [pool-14-thread-6] WARN  com.simbest.boot.base.repository.CustomDynamicWhere.queryForList Line:50 - 自定义查询SQL输出为：【select * from US_TODO_MODEL t  where t.send_flag = 0 and t.work_flag = 1 and t.enabled =1】
2025-06-02 13:25:00.010 [pool-14-thread-10] WARN  com.simbest.boot.base.repository.CustomDynamicWhere.queryForList Line:50 - 自定义查询SQL输出为：【select * from US_TODO_MODEL t  where t.send_flag = 0 and t.work_flag = 1 and t.enabled =1】
2025-06-02 13:25:00.011 [pool-14-thread-3] WARN  com.simbest.boot.base.repository.CustomDynamicWhere.queryForList Line:50 - 自定义查询SQL输出为：【select * from US_TODO_MODEL t  where t.send_flag = 0 and t.work_flag = 0 and t.enabled =1】
2025-06-02 13:49:08.546 [AMQP Connection ************:5672] WARN  com.rabbitmq.client.impl.ForgivingExceptionHandler.log Line:115 - An unexpected connection driver error occured (Exception message: Connection reset)
2025-06-02 13:49:08.654 [AMQP Connection ************:5672] WARN  com.rabbitmq.client.impl.ForgivingExceptionHandler.log Line:115 - An unexpected connection driver error occured (Exception message: Connection reset)
2025-06-02 13:49:09.615 [AMQP Connection ************:5672] WARN  com.rabbitmq.client.impl.ForgivingExceptionHandler.log Line:115 - An unexpected connection driver error occured (Exception message: Connection reset)
2025-06-02 13:49:11.662 [AMQP Connection ************:5672] WARN  com.rabbitmq.client.impl.ForgivingExceptionHandler.log Line:115 - An unexpected connection driver error occured (Exception message: Connection reset)
2025-06-02 13:55:00.004 [pool-14-thread-2] WARN  com.simbest.boot.base.repository.CustomDynamicWhere.queryForList Line:50 - 自定义查询SQL输出为：【select * from US_TODO_MODEL t  where t.send_flag = 0 and t.work_flag = 0 and t.enabled =1】
2025-06-02 13:55:06.192 [pool-14-thread-6] WARN  com.simbest.boot.base.repository.CustomDynamicWhere.queryForList Line:50 - 自定义查询SQL输出为：【select * from US_TODO_MODEL t  where t.send_flag = 0 and t.work_flag = 1 and t.enabled =1】
2025-06-02 14:00:00.018 [pool-14-thread-1] WARN  com.simbest.boot.base.repository.CustomDynamicWhere.queryForList Line:50 - 自定义查询SQL输出为：【select * from US_TODO_MODEL t  where t.send_flag = 0 and t.work_flag = 1 and t.enabled =1】
2025-06-02 14:00:00.018 [pool-14-thread-3] WARN  com.simbest.boot.base.repository.CustomDynamicWhere.queryForList Line:50 - 自定义查询SQL输出为：【select * from US_TODO_MODEL t  where t.send_flag = 0 and t.work_flag = 0 and t.enabled =1】
2025-06-02 14:00:02.999 [pool-14-thread-4] WARN  com.simbest.boot.cmcc.a4.task.A4TaskCreateXmlFile.execute Line:76 - >>>>>>>>>>>>>>>>>>>>>>>>>>>>>>>>>>>>>>>>>>>>>>>>>>>>>A4TaskCreateXmlFile:执行定时任务但不是Prd环境【2025-06-02T14:00:02.999】
2025-06-02 14:05:00.012 [pool-14-thread-1] WARN  com.simbest.boot.base.repository.CustomDynamicWhere.queryForList Line:50 - 自定义查询SQL输出为：【select * from US_TODO_MODEL t  where t.send_flag = 0 and t.work_flag = 0 and t.enabled =1】
2025-06-02 14:05:00.012 [pool-14-thread-9] WARN  com.simbest.boot.base.repository.CustomDynamicWhere.queryForList Line:50 - 自定义查询SQL输出为：【select * from US_TODO_MODEL t  where t.send_flag = 0 and t.work_flag = 1 and t.enabled =1】
2025-06-02 14:30:09.414 [AMQP Connection ************:5672] WARN  com.rabbitmq.client.impl.ForgivingExceptionHandler.log Line:115 - An unexpected connection driver error occured (Exception message: Connection reset)
2025-06-02 14:30:09.571 [AMQP Connection ************:5672] WARN  com.rabbitmq.client.impl.ForgivingExceptionHandler.log Line:115 - An unexpected connection driver error occured (Exception message: Connection reset)
2025-06-02 14:30:10.515 [AMQP Connection ************:5672] WARN  com.rabbitmq.client.impl.ForgivingExceptionHandler.log Line:115 - An unexpected connection driver error occured (Exception message: Connection reset)
2025-06-02 14:30:12.553 [AMQP Connection ************:5672] WARN  com.rabbitmq.client.impl.ForgivingExceptionHandler.log Line:115 - An unexpected connection driver error occured (Exception message: Connection reset)
2025-06-02 15:10:40.276 [AMQP Connection ************:5672] WARN  com.rabbitmq.client.impl.ForgivingExceptionHandler.log Line:115 - An unexpected connection driver error occured (Exception message: Connection reset)
2025-06-02 15:10:40.418 [AMQP Connection ************:5672] WARN  com.rabbitmq.client.impl.ForgivingExceptionHandler.log Line:115 - An unexpected connection driver error occured (Exception message: Connection reset)
2025-06-02 15:10:41.351 [AMQP Connection ************:5672] WARN  com.rabbitmq.client.impl.ForgivingExceptionHandler.log Line:115 - An unexpected connection driver error occured (Exception message: Connection reset)
2025-06-02 15:10:43.402 [AMQP Connection ************:5672] WARN  com.rabbitmq.client.impl.ForgivingExceptionHandler.log Line:115 - An unexpected connection driver error occured (Exception message: Connection reset)
2025-06-02 15:50:52.995 [lettuce-nioEventLoop-4-4] WARN  io.lettuce.core.cluster.topology.ClusterTopologyRefresh.lambda$getConnections$0 Line:284 - Unable to connect to [************:7001]: No route to host: no further information: /************:7001
2025-06-02 15:50:52.995 [lettuce-nioEventLoop-4-5] WARN  io.lettuce.core.cluster.topology.ClusterTopologyRefresh.lambda$getConnections$0 Line:284 - Unable to connect to [************:7002]: No route to host: no further information: /************:7002
2025-06-02 15:50:52.995 [lettuce-nioEventLoop-4-6] WARN  io.lettuce.core.cluster.topology.ClusterTopologyRefresh.lambda$getConnections$0 Line:284 - Unable to connect to [************:7003]: No route to host: no further information: /************:7003
2025-06-02 15:50:52.995 [lettuce-nioEventLoop-4-7] WARN  io.lettuce.core.cluster.topology.ClusterTopologyRefresh.lambda$getConnections$0 Line:284 - Unable to connect to [************:7004]: No route to host: no further information: /************:7004
2025-06-02 15:50:52.995 [lettuce-nioEventLoop-4-8] WARN  io.lettuce.core.cluster.topology.ClusterTopologyRefresh.lambda$getConnections$0 Line:284 - Unable to connect to [************:7005]: No route to host: no further information: /************:7005
2025-06-02 15:50:52.995 [lettuce-nioEventLoop-4-9] WARN  io.lettuce.core.cluster.topology.ClusterTopologyRefresh.lambda$getConnections$0 Line:284 - Unable to connect to [************:7006]: No route to host: no further information: /************:7006
2025-06-02 15:50:52.996 [lettuce-nioEventLoop-4-10] WARN  io.lettuce.core.cluster.topology.ClusterTopologyRefresh.lambda$getConnections$0 Line:284 - Unable to connect to [************:7006]: No route to host: no further information: /************:7006
2025-06-02 15:50:52.996 [lettuce-nioEventLoop-4-11] WARN  io.lettuce.core.cluster.topology.ClusterTopologyRefresh.lambda$getConnections$0 Line:284 - Unable to connect to [************:7004]: No route to host: no further information: /************:7004
2025-06-02 15:50:52.996 [lettuce-nioEventLoop-4-12] WARN  io.lettuce.core.cluster.topology.ClusterTopologyRefresh.lambda$getConnections$0 Line:284 - Unable to connect to [************:7005]: No route to host: no further information: /************:7005
2025-06-02 15:50:52.996 [lettuce-nioEventLoop-4-1] WARN  io.lettuce.core.cluster.topology.ClusterTopologyRefresh.lambda$getConnections$0 Line:284 - Unable to connect to [************:7002]: No route to host: no further information: /************:7002
2025-06-02 15:50:52.996 [lettuce-nioEventLoop-4-2] WARN  io.lettuce.core.cluster.topology.ClusterTopologyRefresh.lambda$getConnections$0 Line:284 - Unable to connect to [************:7003]: No route to host: no further information: /************:7003
2025-06-02 15:50:52.996 [lettuce-nioEventLoop-4-3] WARN  io.lettuce.core.cluster.topology.ClusterTopologyRefresh.lambda$getConnections$0 Line:284 - Unable to connect to [************:7001]: No route to host: no further information: /************:7001
2025-06-02 15:50:53.002 [lettuce-eventExecutorLoop-1-6] WARN  io.lettuce.core.cluster.ClusterTopologyRefreshScheduler.doRun Line:258 - Cannot refresh Redis Cluster topology
io.lettuce.core.RedisException: Cannot retrieve initial cluster partitions from initial URIs [RedisURI [host='************', port=7006], RedisURI [host='************', port=7004], RedisURI [host='************', port=7005], RedisURI [host='************', port=7002], RedisURI [host='************', port=7003], RedisURI [host='************', port=7001]]
	at io.lettuce.core.cluster.RedisClusterClient.loadPartitions(RedisClusterClient.java:848)
	at io.lettuce.core.cluster.RedisClusterClient.reloadPartitions(RedisClusterClient.java:784)
	at io.lettuce.core.cluster.ClusterTopologyRefreshScheduler$ClusterTopologyRefreshTask.doRun(ClusterTopologyRefreshScheduler.java:256)
	at io.lettuce.core.cluster.ClusterTopologyRefreshScheduler$ClusterTopologyRefreshTask.run(ClusterTopologyRefreshScheduler.java:236)
	at io.netty.util.concurrent.PromiseTask.runTask(PromiseTask.java:98)
	at io.netty.util.concurrent.PromiseTask.run(PromiseTask.java:106)
	at io.netty.util.concurrent.DefaultEventExecutor.run(DefaultEventExecutor.java:66)
	at io.netty.util.concurrent.SingleThreadEventExecutor$6.run(SingleThreadEventExecutor.java:1050)
	at io.netty.util.internal.ThreadExecutorMap$2.run(ThreadExecutorMap.java:74)
	at io.netty.util.concurrent.FastThreadLocalRunnable.run(FastThreadLocalRunnable.java:30)
	at java.lang.Thread.run(Thread.java:748)
	Suppressed: io.lettuce.core.RedisConnectionException: Unable to establish a connection to Redis Cluster at [RedisURI [host='************', port=7001], RedisURI [host='************', port=7002], RedisURI [host='************', port=7003], RedisURI [host='************', port=7004], RedisURI [host='************', port=7005], RedisURI [host='************', port=7006]]
		at io.lettuce.core.cluster.topology.AsyncConnections.get(AsyncConnections.java:89)
		at io.lettuce.core.cluster.topology.ClusterTopologyRefresh.loadViews(ClusterTopologyRefresh.java:76)
		at io.lettuce.core.cluster.RedisClusterClient.doLoadPartitions(RedisClusterClient.java:865)
		at io.lettuce.core.cluster.RedisClusterClient.loadPartitions(RedisClusterClient.java:838)
		... 10 common frames omitted
		Suppressed: io.lettuce.core.RedisConnectionException: Unable to connect to [************:7002]: No route to host: no further information: /************:7002
			at io.lettuce.core.cluster.topology.ClusterTopologyRefresh.lambda$getConnections$0(ClusterTopologyRefresh.java:290)
			at java.util.concurrent.CompletableFuture.uniWhenComplete(CompletableFuture.java:760)
			at java.util.concurrent.CompletableFuture$UniWhenComplete.tryFire(CompletableFuture.java:736)
			at java.util.concurrent.CompletableFuture.postComplete(CompletableFuture.java:474)
			at java.util.concurrent.CompletableFuture.completeExceptionally(CompletableFuture.java:1977)
			at io.lettuce.core.AbstractRedisClient.lambda$initializeChannelAsync0$4(AbstractRedisClient.java:329)
			at io.netty.util.concurrent.DefaultPromise.notifyListener0(DefaultPromise.java:577)
			at io.netty.util.concurrent.DefaultPromise.notifyListeners0(DefaultPromise.java:570)
			at io.netty.util.concurrent.DefaultPromise.notifyListenersNow(DefaultPromise.java:549)
			at io.netty.util.concurrent.DefaultPromise.notifyListeners(DefaultPromise.java:490)
			at io.netty.util.concurrent.DefaultPromise.setValue0(DefaultPromise.java:615)
			at io.netty.util.concurrent.DefaultPromise.setFailure0(DefaultPromise.java:608)
			at io.netty.util.concurrent.DefaultPromise.tryFailure(DefaultPromise.java:117)
			at io.netty.channel.nio.AbstractNioChannel$AbstractNioUnsafe.fulfillConnectPromise(AbstractNioChannel.java:321)
			at io.netty.channel.nio.AbstractNioChannel$AbstractNioUnsafe.finishConnect(AbstractNioChannel.java:337)
			at io.netty.channel.nio.NioEventLoop.processSelectedKey(NioEventLoop.java:688)
			at io.netty.channel.nio.NioEventLoop.processSelectedKeysOptimized(NioEventLoop.java:635)
			at io.netty.channel.nio.NioEventLoop.processSelectedKeys(NioEventLoop.java:552)
			at io.netty.channel.nio.NioEventLoop.run(NioEventLoop.java:514)
			... 4 common frames omitted
		Caused by: io.netty.channel.AbstractChannel$AnnotatedNoRouteToHostException: No route to host: no further information: /************:7002
		Caused by: java.net.NoRouteToHostException: No route to host: no further information
			at sun.nio.ch.SocketChannelImpl.checkConnect(Native Method)
			at sun.nio.ch.SocketChannelImpl.finishConnect(SocketChannelImpl.java:717)
			at io.netty.channel.socket.nio.NioSocketChannel.doFinishConnect(NioSocketChannel.java:330)
			at io.netty.channel.nio.AbstractNioChannel$AbstractNioUnsafe.finishConnect(AbstractNioChannel.java:334)
			at io.netty.channel.nio.NioEventLoop.processSelectedKey(NioEventLoop.java:688)
			at io.netty.channel.nio.NioEventLoop.processSelectedKeysOptimized(NioEventLoop.java:635)
			at io.netty.channel.nio.NioEventLoop.processSelectedKeys(NioEventLoop.java:552)
			at io.netty.channel.nio.NioEventLoop.run(NioEventLoop.java:514)
			at io.netty.util.concurrent.SingleThreadEventExecutor$6.run(SingleThreadEventExecutor.java:1050)
			at io.netty.util.internal.ThreadExecutorMap$2.run(ThreadExecutorMap.java:74)
			at io.netty.util.concurrent.FastThreadLocalRunnable.run(FastThreadLocalRunnable.java:30)
			at java.lang.Thread.run(Thread.java:748)
		Suppressed: io.lettuce.core.RedisConnectionException: Unable to connect to [************:7003]: No route to host: no further information: /************:7003
			at io.lettuce.core.cluster.topology.ClusterTopologyRefresh.lambda$getConnections$0(ClusterTopologyRefresh.java:290)
			at java.util.concurrent.CompletableFuture.uniWhenComplete(CompletableFuture.java:760)
			at java.util.concurrent.CompletableFuture$UniWhenComplete.tryFire(CompletableFuture.java:736)
			at java.util.concurrent.CompletableFuture.postComplete(CompletableFuture.java:474)
			at java.util.concurrent.CompletableFuture.completeExceptionally(CompletableFuture.java:1977)
			at io.lettuce.core.AbstractRedisClient.lambda$initializeChannelAsync0$4(AbstractRedisClient.java:329)
			at io.netty.util.concurrent.DefaultPromise.notifyListener0(DefaultPromise.java:577)
			at io.netty.util.concurrent.DefaultPromise.notifyListeners0(DefaultPromise.java:570)
			at io.netty.util.concurrent.DefaultPromise.notifyListenersNow(DefaultPromise.java:549)
			at io.netty.util.concurrent.DefaultPromise.notifyListeners(DefaultPromise.java:490)
			at io.netty.util.concurrent.DefaultPromise.setValue0(DefaultPromise.java:615)
			at io.netty.util.concurrent.DefaultPromise.setFailure0(DefaultPromise.java:608)
			at io.netty.util.concurrent.DefaultPromise.tryFailure(DefaultPromise.java:117)
			at io.netty.channel.nio.AbstractNioChannel$AbstractNioUnsafe.fulfillConnectPromise(AbstractNioChannel.java:321)
			at io.netty.channel.nio.AbstractNioChannel$AbstractNioUnsafe.finishConnect(AbstractNioChannel.java:337)
			at io.netty.channel.nio.NioEventLoop.processSelectedKey(NioEventLoop.java:688)
			at io.netty.channel.nio.NioEventLoop.processSelectedKeysOptimized(NioEventLoop.java:635)
			at io.netty.channel.nio.NioEventLoop.processSelectedKeys(NioEventLoop.java:552)
			at io.netty.channel.nio.NioEventLoop.run(NioEventLoop.java:514)
			... 4 common frames omitted
		Caused by: io.netty.channel.AbstractChannel$AnnotatedNoRouteToHostException: No route to host: no further information: /************:7003
		Caused by: java.net.NoRouteToHostException: No route to host: no further information
			at sun.nio.ch.SocketChannelImpl.checkConnect(Native Method)
			at sun.nio.ch.SocketChannelImpl.finishConnect(SocketChannelImpl.java:717)
			at io.netty.channel.socket.nio.NioSocketChannel.doFinishConnect(NioSocketChannel.java:330)
			at io.netty.channel.nio.AbstractNioChannel$AbstractNioUnsafe.finishConnect(AbstractNioChannel.java:334)
			at io.netty.channel.nio.NioEventLoop.processSelectedKey(NioEventLoop.java:688)
			at io.netty.channel.nio.NioEventLoop.processSelectedKeysOptimized(NioEventLoop.java:635)
			at io.netty.channel.nio.NioEventLoop.processSelectedKeys(NioEventLoop.java:552)
			at io.netty.channel.nio.NioEventLoop.run(NioEventLoop.java:514)
			at io.netty.util.concurrent.SingleThreadEventExecutor$6.run(SingleThreadEventExecutor.java:1050)
			at io.netty.util.internal.ThreadExecutorMap$2.run(ThreadExecutorMap.java:74)
			at io.netty.util.concurrent.FastThreadLocalRunnable.run(FastThreadLocalRunnable.java:30)
			at java.lang.Thread.run(Thread.java:748)
		Suppressed: io.lettuce.core.RedisConnectionException: Unable to connect to [************:7004]: No route to host: no further information: /************:7004
			at io.lettuce.core.cluster.topology.ClusterTopologyRefresh.lambda$getConnections$0(ClusterTopologyRefresh.java:290)
			at java.util.concurrent.CompletableFuture.uniWhenComplete(CompletableFuture.java:760)
			at java.util.concurrent.CompletableFuture$UniWhenComplete.tryFire(CompletableFuture.java:736)
			at java.util.concurrent.CompletableFuture.postComplete(CompletableFuture.java:474)
			at java.util.concurrent.CompletableFuture.completeExceptionally(CompletableFuture.java:1977)
			at io.lettuce.core.AbstractRedisClient.lambda$initializeChannelAsync0$4(AbstractRedisClient.java:329)
			at io.netty.util.concurrent.DefaultPromise.notifyListener0(DefaultPromise.java:577)
			at io.netty.util.concurrent.DefaultPromise.notifyListeners0(DefaultPromise.java:570)
			at io.netty.util.concurrent.DefaultPromise.notifyListenersNow(DefaultPromise.java:549)
			at io.netty.util.concurrent.DefaultPromise.notifyListeners(DefaultPromise.java:490)
			at io.netty.util.concurrent.DefaultPromise.setValue0(DefaultPromise.java:615)
			at io.netty.util.concurrent.DefaultPromise.setFailure0(DefaultPromise.java:608)
			at io.netty.util.concurrent.DefaultPromise.tryFailure(DefaultPromise.java:117)
			at io.netty.channel.nio.AbstractNioChannel$AbstractNioUnsafe.fulfillConnectPromise(AbstractNioChannel.java:321)
			at io.netty.channel.nio.AbstractNioChannel$AbstractNioUnsafe.finishConnect(AbstractNioChannel.java:337)
			at io.netty.channel.nio.NioEventLoop.processSelectedKey(NioEventLoop.java:688)
			at io.netty.channel.nio.NioEventLoop.processSelectedKeysOptimized(NioEventLoop.java:635)
			at io.netty.channel.nio.NioEventLoop.processSelectedKeys(NioEventLoop.java:552)
			at io.netty.channel.nio.NioEventLoop.run(NioEventLoop.java:514)
			... 4 common frames omitted
		Caused by: io.netty.channel.AbstractChannel$AnnotatedNoRouteToHostException: No route to host: no further information: /************:7004
		Caused by: java.net.NoRouteToHostException: No route to host: no further information
			at sun.nio.ch.SocketChannelImpl.checkConnect(Native Method)
			at sun.nio.ch.SocketChannelImpl.finishConnect(SocketChannelImpl.java:717)
			at io.netty.channel.socket.nio.NioSocketChannel.doFinishConnect(NioSocketChannel.java:330)
			at io.netty.channel.nio.AbstractNioChannel$AbstractNioUnsafe.finishConnect(AbstractNioChannel.java:334)
			at io.netty.channel.nio.NioEventLoop.processSelectedKey(NioEventLoop.java:688)
			at io.netty.channel.nio.NioEventLoop.processSelectedKeysOptimized(NioEventLoop.java:635)
			at io.netty.channel.nio.NioEventLoop.processSelectedKeys(NioEventLoop.java:552)
			at io.netty.channel.nio.NioEventLoop.run(NioEventLoop.java:514)
			at io.netty.util.concurrent.SingleThreadEventExecutor$6.run(SingleThreadEventExecutor.java:1050)
			at io.netty.util.internal.ThreadExecutorMap$2.run(ThreadExecutorMap.java:74)
			at io.netty.util.concurrent.FastThreadLocalRunnable.run(FastThreadLocalRunnable.java:30)
			at java.lang.Thread.run(Thread.java:748)
		Suppressed: io.lettuce.core.RedisConnectionException: Unable to connect to [************:7005]: No route to host: no further information: /************:7005
			at io.lettuce.core.cluster.topology.ClusterTopologyRefresh.lambda$getConnections$0(ClusterTopologyRefresh.java:290)
			at java.util.concurrent.CompletableFuture.uniWhenComplete(CompletableFuture.java:760)
			at java.util.concurrent.CompletableFuture$UniWhenComplete.tryFire(CompletableFuture.java:736)
			at java.util.concurrent.CompletableFuture.postComplete(CompletableFuture.java:474)
			at java.util.concurrent.CompletableFuture.completeExceptionally(CompletableFuture.java:1977)
			at io.lettuce.core.AbstractRedisClient.lambda$initializeChannelAsync0$4(AbstractRedisClient.java:329)
			at io.netty.util.concurrent.DefaultPromise.notifyListener0(DefaultPromise.java:577)
			at io.netty.util.concurrent.DefaultPromise.notifyListeners0(DefaultPromise.java:570)
			at io.netty.util.concurrent.DefaultPromise.notifyListenersNow(DefaultPromise.java:549)
			at io.netty.util.concurrent.DefaultPromise.notifyListeners(DefaultPromise.java:490)
			at io.netty.util.concurrent.DefaultPromise.setValue0(DefaultPromise.java:615)
			at io.netty.util.concurrent.DefaultPromise.setFailure0(DefaultPromise.java:608)
			at io.netty.util.concurrent.DefaultPromise.tryFailure(DefaultPromise.java:117)
			at io.netty.channel.nio.AbstractNioChannel$AbstractNioUnsafe.fulfillConnectPromise(AbstractNioChannel.java:321)
			at io.netty.channel.nio.AbstractNioChannel$AbstractNioUnsafe.finishConnect(AbstractNioChannel.java:337)
			at io.netty.channel.nio.NioEventLoop.processSelectedKey(NioEventLoop.java:688)
			at io.netty.channel.nio.NioEventLoop.processSelectedKeysOptimized(NioEventLoop.java:635)
			at io.netty.channel.nio.NioEventLoop.processSelectedKeys(NioEventLoop.java:552)
			at io.netty.channel.nio.NioEventLoop.run(NioEventLoop.java:514)
			... 4 common frames omitted
		Caused by: io.netty.channel.AbstractChannel$AnnotatedNoRouteToHostException: No route to host: no further information: /************:7005
		Caused by: java.net.NoRouteToHostException: No route to host: no further information
			at sun.nio.ch.SocketChannelImpl.checkConnect(Native Method)
			at sun.nio.ch.SocketChannelImpl.finishConnect(SocketChannelImpl.java:717)
			at io.netty.channel.socket.nio.NioSocketChannel.doFinishConnect(NioSocketChannel.java:330)
			at io.netty.channel.nio.AbstractNioChannel$AbstractNioUnsafe.finishConnect(AbstractNioChannel.java:334)
			at io.netty.channel.nio.NioEventLoop.processSelectedKey(NioEventLoop.java:688)
			at io.netty.channel.nio.NioEventLoop.processSelectedKeysOptimized(NioEventLoop.java:635)
			at io.netty.channel.nio.NioEventLoop.processSelectedKeys(NioEventLoop.java:552)
			at io.netty.channel.nio.NioEventLoop.run(NioEventLoop.java:514)
			at io.netty.util.concurrent.SingleThreadEventExecutor$6.run(SingleThreadEventExecutor.java:1050)
			at io.netty.util.internal.ThreadExecutorMap$2.run(ThreadExecutorMap.java:74)
			at io.netty.util.concurrent.FastThreadLocalRunnable.run(FastThreadLocalRunnable.java:30)
			at java.lang.Thread.run(Thread.java:748)
		Suppressed: io.lettuce.core.RedisConnectionException: Unable to connect to [************:7006]: No route to host: no further information: /************:7006
			at io.lettuce.core.cluster.topology.ClusterTopologyRefresh.lambda$getConnections$0(ClusterTopologyRefresh.java:290)
			at java.util.concurrent.CompletableFuture.uniWhenComplete(CompletableFuture.java:760)
			at java.util.concurrent.CompletableFuture$UniWhenComplete.tryFire(CompletableFuture.java:736)
			at java.util.concurrent.CompletableFuture.postComplete(CompletableFuture.java:474)
			at java.util.concurrent.CompletableFuture.completeExceptionally(CompletableFuture.java:1977)
			at io.lettuce.core.AbstractRedisClient.lambda$initializeChannelAsync0$4(AbstractRedisClient.java:329)
			at io.netty.util.concurrent.DefaultPromise.notifyListener0(DefaultPromise.java:577)
			at io.netty.util.concurrent.DefaultPromise.notifyListeners0(DefaultPromise.java:570)
			at io.netty.util.concurrent.DefaultPromise.notifyListenersNow(DefaultPromise.java:549)
			at io.netty.util.concurrent.DefaultPromise.notifyListeners(DefaultPromise.java:490)
			at io.netty.util.concurrent.DefaultPromise.setValue0(DefaultPromise.java:615)
			at io.netty.util.concurrent.DefaultPromise.setFailure0(DefaultPromise.java:608)
			at io.netty.util.concurrent.DefaultPromise.tryFailure(DefaultPromise.java:117)
			at io.netty.channel.nio.AbstractNioChannel$AbstractNioUnsafe.fulfillConnectPromise(AbstractNioChannel.java:321)
			at io.netty.channel.nio.AbstractNioChannel$AbstractNioUnsafe.finishConnect(AbstractNioChannel.java:337)
			at io.netty.channel.nio.NioEventLoop.processSelectedKey(NioEventLoop.java:688)
			at io.netty.channel.nio.NioEventLoop.processSelectedKeysOptimized(NioEventLoop.java:635)
			at io.netty.channel.nio.NioEventLoop.processSelectedKeys(NioEventLoop.java:552)
			at io.netty.channel.nio.NioEventLoop.run(NioEventLoop.java:514)
			... 4 common frames omitted
		Caused by: io.netty.channel.AbstractChannel$AnnotatedNoRouteToHostException: No route to host: no further information: /************:7006
		Caused by: java.net.NoRouteToHostException: No route to host: no further information
			at sun.nio.ch.SocketChannelImpl.checkConnect(Native Method)
			at sun.nio.ch.SocketChannelImpl.finishConnect(SocketChannelImpl.java:717)
			at io.netty.channel.socket.nio.NioSocketChannel.doFinishConnect(NioSocketChannel.java:330)
			at io.netty.channel.nio.AbstractNioChannel$AbstractNioUnsafe.finishConnect(AbstractNioChannel.java:334)
			at io.netty.channel.nio.NioEventLoop.processSelectedKey(NioEventLoop.java:688)
			at io.netty.channel.nio.NioEventLoop.processSelectedKeysOptimized(NioEventLoop.java:635)
			at io.netty.channel.nio.NioEventLoop.processSelectedKeys(NioEventLoop.java:552)
			at io.netty.channel.nio.NioEventLoop.run(NioEventLoop.java:514)
			at io.netty.util.concurrent.SingleThreadEventExecutor$6.run(SingleThreadEventExecutor.java:1050)
			at io.netty.util.internal.ThreadExecutorMap$2.run(ThreadExecutorMap.java:74)
			at io.netty.util.concurrent.FastThreadLocalRunnable.run(FastThreadLocalRunnable.java:30)
			at java.lang.Thread.run(Thread.java:748)
	Caused by: io.lettuce.core.RedisConnectionException: Unable to connect to [************:7001]: No route to host: no further information: /************:7001
		at io.lettuce.core.cluster.topology.ClusterTopologyRefresh.lambda$getConnections$0(ClusterTopologyRefresh.java:290)
		at java.util.concurrent.CompletableFuture.uniWhenComplete(CompletableFuture.java:760)
		at java.util.concurrent.CompletableFuture$UniWhenComplete.tryFire(CompletableFuture.java:736)
		at java.util.concurrent.CompletableFuture.postComplete(CompletableFuture.java:474)
		at java.util.concurrent.CompletableFuture.completeExceptionally(CompletableFuture.java:1977)
		at io.lettuce.core.AbstractRedisClient.lambda$initializeChannelAsync0$4(AbstractRedisClient.java:329)
		at io.netty.util.concurrent.DefaultPromise.notifyListener0(DefaultPromise.java:577)
		at io.netty.util.concurrent.DefaultPromise.notifyListeners0(DefaultPromise.java:570)
		at io.netty.util.concurrent.DefaultPromise.notifyListenersNow(DefaultPromise.java:549)
		at io.netty.util.concurrent.DefaultPromise.notifyListeners(DefaultPromise.java:490)
		at io.netty.util.concurrent.DefaultPromise.setValue0(DefaultPromise.java:615)
		at io.netty.util.concurrent.DefaultPromise.setFailure0(DefaultPromise.java:608)
		at io.netty.util.concurrent.DefaultPromise.tryFailure(DefaultPromise.java:117)
		at io.netty.channel.nio.AbstractNioChannel$AbstractNioUnsafe.fulfillConnectPromise(AbstractNioChannel.java:321)
		at io.netty.channel.nio.AbstractNioChannel$AbstractNioUnsafe.finishConnect(AbstractNioChannel.java:337)
		at io.netty.channel.nio.NioEventLoop.processSelectedKey(NioEventLoop.java:688)
		at io.netty.channel.nio.NioEventLoop.processSelectedKeysOptimized(NioEventLoop.java:635)
		at io.netty.channel.nio.NioEventLoop.processSelectedKeys(NioEventLoop.java:552)
		at io.netty.channel.nio.NioEventLoop.run(NioEventLoop.java:514)
		... 4 common frames omitted
	Caused by: io.netty.channel.AbstractChannel$AnnotatedNoRouteToHostException: No route to host: no further information: /************:7001
	Caused by: java.net.NoRouteToHostException: No route to host: no further information
		at sun.nio.ch.SocketChannelImpl.checkConnect(Native Method)
		at sun.nio.ch.SocketChannelImpl.finishConnect(SocketChannelImpl.java:717)
		at io.netty.channel.socket.nio.NioSocketChannel.doFinishConnect(NioSocketChannel.java:330)
		at io.netty.channel.nio.AbstractNioChannel$AbstractNioUnsafe.finishConnect(AbstractNioChannel.java:334)
		at io.netty.channel.nio.NioEventLoop.processSelectedKey(NioEventLoop.java:688)
		at io.netty.channel.nio.NioEventLoop.processSelectedKeysOptimized(NioEventLoop.java:635)
		at io.netty.channel.nio.NioEventLoop.processSelectedKeys(NioEventLoop.java:552)
		at io.netty.channel.nio.NioEventLoop.run(NioEventLoop.java:514)
		at io.netty.util.concurrent.SingleThreadEventExecutor$6.run(SingleThreadEventExecutor.java:1050)
		at io.netty.util.internal.ThreadExecutorMap$2.run(ThreadExecutorMap.java:74)
		at io.netty.util.concurrent.FastThreadLocalRunnable.run(FastThreadLocalRunnable.java:30)
		at java.lang.Thread.run(Thread.java:748)
Caused by: io.lettuce.core.RedisConnectionException: Unable to establish a connection to Redis Cluster at [RedisURI [host='************', port=7001], RedisURI [host='************', port=7002], RedisURI [host='************', port=7003], RedisURI [host='************', port=7004], RedisURI [host='************', port=7005], RedisURI [host='************', port=7006]]
	at io.lettuce.core.cluster.topology.AsyncConnections.get(AsyncConnections.java:89)
	at io.lettuce.core.cluster.topology.ClusterTopologyRefresh.loadViews(ClusterTopologyRefresh.java:76)
	at io.lettuce.core.cluster.RedisClusterClient.doLoadPartitions(RedisClusterClient.java:865)
	at io.lettuce.core.cluster.RedisClusterClient.loadPartitions(RedisClusterClient.java:845)
	... 10 common frames omitted
	Suppressed: io.lettuce.core.RedisConnectionException: Unable to connect to [************:7005]: No route to host: no further information: /************:7005
		at io.lettuce.core.cluster.topology.ClusterTopologyRefresh.lambda$getConnections$0(ClusterTopologyRefresh.java:290)
		at java.util.concurrent.CompletableFuture.uniWhenComplete(CompletableFuture.java:760)
		at java.util.concurrent.CompletableFuture$UniWhenComplete.tryFire(CompletableFuture.java:736)
		at java.util.concurrent.CompletableFuture.postComplete(CompletableFuture.java:474)
		at java.util.concurrent.CompletableFuture.completeExceptionally(CompletableFuture.java:1977)
		at io.lettuce.core.AbstractRedisClient.lambda$initializeChannelAsync0$4(AbstractRedisClient.java:329)
		at io.netty.util.concurrent.DefaultPromise.notifyListener0(DefaultPromise.java:577)
		at io.netty.util.concurrent.DefaultPromise.notifyListeners0(DefaultPromise.java:570)
		at io.netty.util.concurrent.DefaultPromise.notifyListenersNow(DefaultPromise.java:549)
		at io.netty.util.concurrent.DefaultPromise.notifyListeners(DefaultPromise.java:490)
		at io.netty.util.concurrent.DefaultPromise.setValue0(DefaultPromise.java:615)
		at io.netty.util.concurrent.DefaultPromise.setFailure0(DefaultPromise.java:608)
		at io.netty.util.concurrent.DefaultPromise.tryFailure(DefaultPromise.java:117)
		at io.netty.channel.nio.AbstractNioChannel$AbstractNioUnsafe.fulfillConnectPromise(AbstractNioChannel.java:321)
		at io.netty.channel.nio.AbstractNioChannel$AbstractNioUnsafe.finishConnect(AbstractNioChannel.java:337)
		at io.netty.channel.nio.NioEventLoop.processSelectedKey(NioEventLoop.java:688)
		at io.netty.channel.nio.NioEventLoop.processSelectedKeysOptimized(NioEventLoop.java:635)
		at io.netty.channel.nio.NioEventLoop.processSelectedKeys(NioEventLoop.java:552)
		at io.netty.channel.nio.NioEventLoop.run(NioEventLoop.java:514)
		... 4 common frames omitted
	Caused by: io.netty.channel.AbstractChannel$AnnotatedNoRouteToHostException: No route to host: no further information: /************:7005
	Caused by: java.net.NoRouteToHostException: No route to host: no further information
		at sun.nio.ch.SocketChannelImpl.checkConnect(Native Method)
		at sun.nio.ch.SocketChannelImpl.finishConnect(SocketChannelImpl.java:717)
		at io.netty.channel.socket.nio.NioSocketChannel.doFinishConnect(NioSocketChannel.java:330)
		at io.netty.channel.nio.AbstractNioChannel$AbstractNioUnsafe.finishConnect(AbstractNioChannel.java:334)
		at io.netty.channel.nio.NioEventLoop.processSelectedKey(NioEventLoop.java:688)
		at io.netty.channel.nio.NioEventLoop.processSelectedKeysOptimized(NioEventLoop.java:635)
		at io.netty.channel.nio.NioEventLoop.processSelectedKeys(NioEventLoop.java:552)
		at io.netty.channel.nio.NioEventLoop.run(NioEventLoop.java:514)
		at io.netty.util.concurrent.SingleThreadEventExecutor$6.run(SingleThreadEventExecutor.java:1050)
		at io.netty.util.internal.ThreadExecutorMap$2.run(ThreadExecutorMap.java:74)
		at io.netty.util.concurrent.FastThreadLocalRunnable.run(FastThreadLocalRunnable.java:30)
		at java.lang.Thread.run(Thread.java:748)
	Suppressed: io.lettuce.core.RedisConnectionException: Unable to connect to [************:7006]: No route to host: no further information: /************:7006
		at io.lettuce.core.cluster.topology.ClusterTopologyRefresh.lambda$getConnections$0(ClusterTopologyRefresh.java:290)
		at java.util.concurrent.CompletableFuture.uniWhenComplete(CompletableFuture.java:760)
		at java.util.concurrent.CompletableFuture$UniWhenComplete.tryFire(CompletableFuture.java:736)
		at java.util.concurrent.CompletableFuture.postComplete(CompletableFuture.java:474)
		at java.util.concurrent.CompletableFuture.completeExceptionally(CompletableFuture.java:1977)
		at io.lettuce.core.AbstractRedisClient.lambda$initializeChannelAsync0$4(AbstractRedisClient.java:329)
		at io.netty.util.concurrent.DefaultPromise.notifyListener0(DefaultPromise.java:577)
		at io.netty.util.concurrent.DefaultPromise.notifyListeners0(DefaultPromise.java:570)
		at io.netty.util.concurrent.DefaultPromise.notifyListenersNow(DefaultPromise.java:549)
		at io.netty.util.concurrent.DefaultPromise.notifyListeners(DefaultPromise.java:490)
		at io.netty.util.concurrent.DefaultPromise.setValue0(DefaultPromise.java:615)
		at io.netty.util.concurrent.DefaultPromise.setFailure0(DefaultPromise.java:608)
		at io.netty.util.concurrent.DefaultPromise.tryFailure(DefaultPromise.java:117)
		at io.netty.channel.nio.AbstractNioChannel$AbstractNioUnsafe.fulfillConnectPromise(AbstractNioChannel.java:321)
		at io.netty.channel.nio.AbstractNioChannel$AbstractNioUnsafe.finishConnect(AbstractNioChannel.java:337)
		at io.netty.channel.nio.NioEventLoop.processSelectedKey(NioEventLoop.java:688)
		at io.netty.channel.nio.NioEventLoop.processSelectedKeysOptimized(NioEventLoop.java:635)
		at io.netty.channel.nio.NioEventLoop.processSelectedKeys(NioEventLoop.java:552)
		at io.netty.channel.nio.NioEventLoop.run(NioEventLoop.java:514)
		... 4 common frames omitted
	Caused by: io.netty.channel.AbstractChannel$AnnotatedNoRouteToHostException: No route to host: no further information: /************:7006
	Caused by: java.net.NoRouteToHostException: No route to host: no further information
		at sun.nio.ch.SocketChannelImpl.checkConnect(Native Method)
		at sun.nio.ch.SocketChannelImpl.finishConnect(SocketChannelImpl.java:717)
		at io.netty.channel.socket.nio.NioSocketChannel.doFinishConnect(NioSocketChannel.java:330)
		at io.netty.channel.nio.AbstractNioChannel$AbstractNioUnsafe.finishConnect(AbstractNioChannel.java:334)
		at io.netty.channel.nio.NioEventLoop.processSelectedKey(NioEventLoop.java:688)
		at io.netty.channel.nio.NioEventLoop.processSelectedKeysOptimized(NioEventLoop.java:635)
		at io.netty.channel.nio.NioEventLoop.processSelectedKeys(NioEventLoop.java:552)
		at io.netty.channel.nio.NioEventLoop.run(NioEventLoop.java:514)
		at io.netty.util.concurrent.SingleThreadEventExecutor$6.run(SingleThreadEventExecutor.java:1050)
		at io.netty.util.internal.ThreadExecutorMap$2.run(ThreadExecutorMap.java:74)
		at io.netty.util.concurrent.FastThreadLocalRunnable.run(FastThreadLocalRunnable.java:30)
		at java.lang.Thread.run(Thread.java:748)
	Suppressed: io.lettuce.core.RedisConnectionException: Unable to connect to [************:7002]: No route to host: no further information: /************:7002
		at io.lettuce.core.cluster.topology.ClusterTopologyRefresh.lambda$getConnections$0(ClusterTopologyRefresh.java:290)
		at java.util.concurrent.CompletableFuture.uniWhenComplete(CompletableFuture.java:760)
		at java.util.concurrent.CompletableFuture$UniWhenComplete.tryFire(CompletableFuture.java:736)
		at java.util.concurrent.CompletableFuture.postComplete(CompletableFuture.java:474)
		at java.util.concurrent.CompletableFuture.completeExceptionally(CompletableFuture.java:1977)
		at io.lettuce.core.AbstractRedisClient.lambda$initializeChannelAsync0$4(AbstractRedisClient.java:329)
		at io.netty.util.concurrent.DefaultPromise.notifyListener0(DefaultPromise.java:577)
		at io.netty.util.concurrent.DefaultPromise.notifyListeners0(DefaultPromise.java:570)
		at io.netty.util.concurrent.DefaultPromise.notifyListenersNow(DefaultPromise.java:549)
		at io.netty.util.concurrent.DefaultPromise.notifyListeners(DefaultPromise.java:490)
		at io.netty.util.concurrent.DefaultPromise.setValue0(DefaultPromise.java:615)
		at io.netty.util.concurrent.DefaultPromise.setFailure0(DefaultPromise.java:608)
		at io.netty.util.concurrent.DefaultPromise.tryFailure(DefaultPromise.java:117)
		at io.netty.channel.nio.AbstractNioChannel$AbstractNioUnsafe.fulfillConnectPromise(AbstractNioChannel.java:321)
		at io.netty.channel.nio.AbstractNioChannel$AbstractNioUnsafe.finishConnect(AbstractNioChannel.java:337)
		at io.netty.channel.nio.NioEventLoop.processSelectedKey(NioEventLoop.java:688)
		at io.netty.channel.nio.NioEventLoop.processSelectedKeysOptimized(NioEventLoop.java:635)
		at io.netty.channel.nio.NioEventLoop.processSelectedKeys(NioEventLoop.java:552)
		at io.netty.channel.nio.NioEventLoop.run(NioEventLoop.java:514)
		... 4 common frames omitted
	Caused by: io.netty.channel.AbstractChannel$AnnotatedNoRouteToHostException: No route to host: no further information: /************:7002
	Caused by: java.net.NoRouteToHostException: No route to host: no further information
		at sun.nio.ch.SocketChannelImpl.checkConnect(Native Method)
		at sun.nio.ch.SocketChannelImpl.finishConnect(SocketChannelImpl.java:717)
		at io.netty.channel.socket.nio.NioSocketChannel.doFinishConnect(NioSocketChannel.java:330)
		at io.netty.channel.nio.AbstractNioChannel$AbstractNioUnsafe.finishConnect(AbstractNioChannel.java:334)
		at io.netty.channel.nio.NioEventLoop.processSelectedKey(NioEventLoop.java:688)
		at io.netty.channel.nio.NioEventLoop.processSelectedKeysOptimized(NioEventLoop.java:635)
		at io.netty.channel.nio.NioEventLoop.processSelectedKeys(NioEventLoop.java:552)
		at io.netty.channel.nio.NioEventLoop.run(NioEventLoop.java:514)
		at io.netty.util.concurrent.SingleThreadEventExecutor$6.run(SingleThreadEventExecutor.java:1050)
		at io.netty.util.internal.ThreadExecutorMap$2.run(ThreadExecutorMap.java:74)
		at io.netty.util.concurrent.FastThreadLocalRunnable.run(FastThreadLocalRunnable.java:30)
		at java.lang.Thread.run(Thread.java:748)
	Suppressed: io.lettuce.core.RedisConnectionException: Unable to connect to [************:7003]: No route to host: no further information: /************:7003
		at io.lettuce.core.cluster.topology.ClusterTopologyRefresh.lambda$getConnections$0(ClusterTopologyRefresh.java:290)
		at java.util.concurrent.CompletableFuture.uniWhenComplete(CompletableFuture.java:760)
		at java.util.concurrent.CompletableFuture$UniWhenComplete.tryFire(CompletableFuture.java:736)
		at java.util.concurrent.CompletableFuture.postComplete(CompletableFuture.java:474)
		at java.util.concurrent.CompletableFuture.completeExceptionally(CompletableFuture.java:1977)
		at io.lettuce.core.AbstractRedisClient.lambda$initializeChannelAsync0$4(AbstractRedisClient.java:329)
		at io.netty.util.concurrent.DefaultPromise.notifyListener0(DefaultPromise.java:577)
		at io.netty.util.concurrent.DefaultPromise.notifyListeners0(DefaultPromise.java:570)
		at io.netty.util.concurrent.DefaultPromise.notifyListenersNow(DefaultPromise.java:549)
		at io.netty.util.concurrent.DefaultPromise.notifyListeners(DefaultPromise.java:490)
		at io.netty.util.concurrent.DefaultPromise.setValue0(DefaultPromise.java:615)
		at io.netty.util.concurrent.DefaultPromise.setFailure0(DefaultPromise.java:608)
		at io.netty.util.concurrent.DefaultPromise.tryFailure(DefaultPromise.java:117)
		at io.netty.channel.nio.AbstractNioChannel$AbstractNioUnsafe.fulfillConnectPromise(AbstractNioChannel.java:321)
		at io.netty.channel.nio.AbstractNioChannel$AbstractNioUnsafe.finishConnect(AbstractNioChannel.java:337)
		at io.netty.channel.nio.NioEventLoop.processSelectedKey(NioEventLoop.java:688)
		at io.netty.channel.nio.NioEventLoop.processSelectedKeysOptimized(NioEventLoop.java:635)
		at io.netty.channel.nio.NioEventLoop.processSelectedKeys(NioEventLoop.java:552)
		at io.netty.channel.nio.NioEventLoop.run(NioEventLoop.java:514)
		... 4 common frames omitted
	Caused by: io.netty.channel.AbstractChannel$AnnotatedNoRouteToHostException: No route to host: no further information: /************:7003
	Caused by: java.net.NoRouteToHostException: No route to host: no further information
		at sun.nio.ch.SocketChannelImpl.checkConnect(Native Method)
		at sun.nio.ch.SocketChannelImpl.finishConnect(SocketChannelImpl.java:717)
		at io.netty.channel.socket.nio.NioSocketChannel.doFinishConnect(NioSocketChannel.java:330)
		at io.netty.channel.nio.AbstractNioChannel$AbstractNioUnsafe.finishConnect(AbstractNioChannel.java:334)
		at io.netty.channel.nio.NioEventLoop.processSelectedKey(NioEventLoop.java:688)
		at io.netty.channel.nio.NioEventLoop.processSelectedKeysOptimized(NioEventLoop.java:635)
		at io.netty.channel.nio.NioEventLoop.processSelectedKeys(NioEventLoop.java:552)
		at io.netty.channel.nio.NioEventLoop.run(NioEventLoop.java:514)
		at io.netty.util.concurrent.SingleThreadEventExecutor$6.run(SingleThreadEventExecutor.java:1050)
		at io.netty.util.internal.ThreadExecutorMap$2.run(ThreadExecutorMap.java:74)
		at io.netty.util.concurrent.FastThreadLocalRunnable.run(FastThreadLocalRunnable.java:30)
		at java.lang.Thread.run(Thread.java:748)
	Suppressed: io.lettuce.core.RedisConnectionException: Unable to connect to [************:7001]: No route to host: no further information: /************:7001
		at io.lettuce.core.cluster.topology.ClusterTopologyRefresh.lambda$getConnections$0(ClusterTopologyRefresh.java:290)
		at java.util.concurrent.CompletableFuture.uniWhenComplete(CompletableFuture.java:760)
		at java.util.concurrent.CompletableFuture$UniWhenComplete.tryFire(CompletableFuture.java:736)
		at java.util.concurrent.CompletableFuture.postComplete(CompletableFuture.java:474)
		at java.util.concurrent.CompletableFuture.completeExceptionally(CompletableFuture.java:1977)
		at io.lettuce.core.AbstractRedisClient.lambda$initializeChannelAsync0$4(AbstractRedisClient.java:329)
		at io.netty.util.concurrent.DefaultPromise.notifyListener0(DefaultPromise.java:577)
		at io.netty.util.concurrent.DefaultPromise.notifyListeners0(DefaultPromise.java:570)
		at io.netty.util.concurrent.DefaultPromise.notifyListenersNow(DefaultPromise.java:549)
		at io.netty.util.concurrent.DefaultPromise.notifyListeners(DefaultPromise.java:490)
		at io.netty.util.concurrent.DefaultPromise.setValue0(DefaultPromise.java:615)
		at io.netty.util.concurrent.DefaultPromise.setFailure0(DefaultPromise.java:608)
		at io.netty.util.concurrent.DefaultPromise.tryFailure(DefaultPromise.java:117)
		at io.netty.channel.nio.AbstractNioChannel$AbstractNioUnsafe.fulfillConnectPromise(AbstractNioChannel.java:321)
		at io.netty.channel.nio.AbstractNioChannel$AbstractNioUnsafe.finishConnect(AbstractNioChannel.java:337)
		at io.netty.channel.nio.NioEventLoop.processSelectedKey(NioEventLoop.java:688)
		at io.netty.channel.nio.NioEventLoop.processSelectedKeysOptimized(NioEventLoop.java:635)
		at io.netty.channel.nio.NioEventLoop.processSelectedKeys(NioEventLoop.java:552)
		at io.netty.channel.nio.NioEventLoop.run(NioEventLoop.java:514)
		... 4 common frames omitted
	Caused by: io.netty.channel.AbstractChannel$AnnotatedNoRouteToHostException: No route to host: no further information: /************:7001
	Caused by: java.net.NoRouteToHostException: No route to host: no further information
		at sun.nio.ch.SocketChannelImpl.checkConnect(Native Method)
		at sun.nio.ch.SocketChannelImpl.finishConnect(SocketChannelImpl.java:717)
		at io.netty.channel.socket.nio.NioSocketChannel.doFinishConnect(NioSocketChannel.java:330)
		at io.netty.channel.nio.AbstractNioChannel$AbstractNioUnsafe.finishConnect(AbstractNioChannel.java:334)
		at io.netty.channel.nio.NioEventLoop.processSelectedKey(NioEventLoop.java:688)
		at io.netty.channel.nio.NioEventLoop.processSelectedKeysOptimized(NioEventLoop.java:635)
		at io.netty.channel.nio.NioEventLoop.processSelectedKeys(NioEventLoop.java:552)
		at io.netty.channel.nio.NioEventLoop.run(NioEventLoop.java:514)
		at io.netty.util.concurrent.SingleThreadEventExecutor$6.run(SingleThreadEventExecutor.java:1050)
		at io.netty.util.internal.ThreadExecutorMap$2.run(ThreadExecutorMap.java:74)
		at io.netty.util.concurrent.FastThreadLocalRunnable.run(FastThreadLocalRunnable.java:30)
		at java.lang.Thread.run(Thread.java:748)
Caused by: io.lettuce.core.RedisConnectionException: Unable to connect to [************:7004]: No route to host: no further information: /************:7004
	at io.lettuce.core.cluster.topology.ClusterTopologyRefresh.lambda$getConnections$0(ClusterTopologyRefresh.java:290)
	at java.util.concurrent.CompletableFuture.uniWhenComplete(CompletableFuture.java:760)
	at java.util.concurrent.CompletableFuture$UniWhenComplete.tryFire(CompletableFuture.java:736)
	at java.util.concurrent.CompletableFuture.postComplete(CompletableFuture.java:474)
	at java.util.concurrent.CompletableFuture.completeExceptionally(CompletableFuture.java:1977)
	at io.lettuce.core.AbstractRedisClient.lambda$initializeChannelAsync0$4(AbstractRedisClient.java:329)
	at io.netty.util.concurrent.DefaultPromise.notifyListener0(DefaultPromise.java:577)
	at io.netty.util.concurrent.DefaultPromise.notifyListeners0(DefaultPromise.java:570)
	at io.netty.util.concurrent.DefaultPromise.notifyListenersNow(DefaultPromise.java:549)
	at io.netty.util.concurrent.DefaultPromise.notifyListeners(DefaultPromise.java:490)
	at io.netty.util.concurrent.DefaultPromise.setValue0(DefaultPromise.java:615)
	at io.netty.util.concurrent.DefaultPromise.setFailure0(DefaultPromise.java:608)
	at io.netty.util.concurrent.DefaultPromise.tryFailure(DefaultPromise.java:117)
	at io.netty.channel.nio.AbstractNioChannel$AbstractNioUnsafe.fulfillConnectPromise(AbstractNioChannel.java:321)
	at io.netty.channel.nio.AbstractNioChannel$AbstractNioUnsafe.finishConnect(AbstractNioChannel.java:337)
	at io.netty.channel.nio.NioEventLoop.processSelectedKey(NioEventLoop.java:688)
	at io.netty.channel.nio.NioEventLoop.processSelectedKeysOptimized(NioEventLoop.java:635)
	at io.netty.channel.nio.NioEventLoop.processSelectedKeys(NioEventLoop.java:552)
	at io.netty.channel.nio.NioEventLoop.run(NioEventLoop.java:514)
	... 4 common frames omitted
Caused by: io.netty.channel.AbstractChannel$AnnotatedNoRouteToHostException: No route to host: no further information: /************:7004
Caused by: java.net.NoRouteToHostException: No route to host: no further information
	at sun.nio.ch.SocketChannelImpl.checkConnect(Native Method)
	at sun.nio.ch.SocketChannelImpl.finishConnect(SocketChannelImpl.java:717)
	at io.netty.channel.socket.nio.NioSocketChannel.doFinishConnect(NioSocketChannel.java:330)
	at io.netty.channel.nio.AbstractNioChannel$AbstractNioUnsafe.finishConnect(AbstractNioChannel.java:334)
	at io.netty.channel.nio.NioEventLoop.processSelectedKey(NioEventLoop.java:688)
	at io.netty.channel.nio.NioEventLoop.processSelectedKeysOptimized(NioEventLoop.java:635)
	at io.netty.channel.nio.NioEventLoop.processSelectedKeys(NioEventLoop.java:552)
	at io.netty.channel.nio.NioEventLoop.run(NioEventLoop.java:514)
	at io.netty.util.concurrent.SingleThreadEventExecutor$6.run(SingleThreadEventExecutor.java:1050)
	at io.netty.util.internal.ThreadExecutorMap$2.run(ThreadExecutorMap.java:74)
	at io.netty.util.concurrent.FastThreadLocalRunnable.run(FastThreadLocalRunnable.java:30)
	at java.lang.Thread.run(Thread.java:748)
2025-06-02 15:50:53.004 [lettuce-nioEventLoop-4-6] WARN  io.lettuce.core.cluster.topology.ClusterTopologyRefresh.lambda$getConnections$0 Line:284 - Unable to connect to [************:7003]: No route to host: no further information: /************:7003
2025-06-02 15:50:53.004 [lettuce-nioEventLoop-4-8] WARN  io.lettuce.core.cluster.topology.ClusterTopologyRefresh.lambda$getConnections$0 Line:284 - Unable to connect to [************:7005]: No route to host: no further information: /************:7005
2025-06-02 15:50:53.004 [lettuce-nioEventLoop-4-5] WARN  io.lettuce.core.cluster.topology.ClusterTopologyRefresh.lambda$getConnections$0 Line:284 - Unable to connect to [************:7002]: No route to host: no further information: /************:7002
2025-06-02 15:50:53.004 [lettuce-nioEventLoop-4-9] WARN  io.lettuce.core.cluster.topology.ClusterTopologyRefresh.lambda$getConnections$0 Line:284 - Unable to connect to [************:7006]: No route to host: no further information: /************:7006
2025-06-02 15:50:53.004 [lettuce-nioEventLoop-4-4] WARN  io.lettuce.core.cluster.topology.ClusterTopologyRefresh.lambda$getConnections$0 Line:284 - Unable to connect to [************:7001]: No route to host: no further information: /************:7001
2025-06-02 15:50:53.004 [lettuce-nioEventLoop-4-7] WARN  io.lettuce.core.cluster.topology.ClusterTopologyRefresh.lambda$getConnections$0 Line:284 - Unable to connect to [************:7004]: No route to host: no further information: /************:7004
2025-06-02 15:50:53.004 [lettuce-nioEventLoop-4-10] WARN  io.lettuce.core.cluster.topology.ClusterTopologyRefresh.lambda$getConnections$0 Line:284 - Unable to connect to [************:7006]: No route to host: no further information: /************:7006
2025-06-02 15:50:53.004 [lettuce-nioEventLoop-4-11] WARN  io.lettuce.core.cluster.topology.ClusterTopologyRefresh.lambda$getConnections$0 Line:284 - Unable to connect to [************:7004]: No route to host: no further information: /************:7004
2025-06-02 15:50:53.004 [lettuce-nioEventLoop-4-12] WARN  io.lettuce.core.cluster.topology.ClusterTopologyRefresh.lambda$getConnections$0 Line:284 - Unable to connect to [************:7005]: No route to host: no further information: /************:7005
2025-06-02 15:50:53.005 [lettuce-nioEventLoop-4-1] WARN  io.lettuce.core.cluster.topology.ClusterTopologyRefresh.lambda$getConnections$0 Line:284 - Unable to connect to [************:7002]: No route to host: no further information: /************:7002
2025-06-02 15:50:53.005 [lettuce-nioEventLoop-4-2] WARN  io.lettuce.core.cluster.topology.ClusterTopologyRefresh.lambda$getConnections$0 Line:284 - Unable to connect to [************:7003]: No route to host: no further information: /************:7003
2025-06-02 15:50:53.005 [lettuce-nioEventLoop-4-3] WARN  io.lettuce.core.cluster.topology.ClusterTopologyRefresh.lambda$getConnections$0 Line:284 - Unable to connect to [************:7001]: No route to host: no further information: /************:7001
2025-06-02 15:50:53.010 [lettuce-eventExecutorLoop-1-6] WARN  io.lettuce.core.cluster.ClusterTopologyRefreshScheduler.doRun Line:258 - Cannot refresh Redis Cluster topology
io.lettuce.core.RedisException: Cannot retrieve initial cluster partitions from initial URIs [RedisURI [host='************', port=7006], RedisURI [host='************', port=7004], RedisURI [host='************', port=7005], RedisURI [host='************', port=7002], RedisURI [host='************', port=7003], RedisURI [host='************', port=7001]]
	at io.lettuce.core.cluster.RedisClusterClient.loadPartitions(RedisClusterClient.java:848)
	at io.lettuce.core.cluster.RedisClusterClient.reloadPartitions(RedisClusterClient.java:784)
	at io.lettuce.core.cluster.ClusterTopologyRefreshScheduler$ClusterTopologyRefreshTask.doRun(ClusterTopologyRefreshScheduler.java:256)
	at io.lettuce.core.cluster.ClusterTopologyRefreshScheduler$ClusterTopologyRefreshTask.run(ClusterTopologyRefreshScheduler.java:236)
	at io.netty.util.concurrent.PromiseTask.runTask(PromiseTask.java:98)
	at io.netty.util.concurrent.PromiseTask.run(PromiseTask.java:106)
	at io.netty.util.concurrent.DefaultEventExecutor.run(DefaultEventExecutor.java:66)
	at io.netty.util.concurrent.SingleThreadEventExecutor$6.run(SingleThreadEventExecutor.java:1050)
	at io.netty.util.internal.ThreadExecutorMap$2.run(ThreadExecutorMap.java:74)
	at io.netty.util.concurrent.FastThreadLocalRunnable.run(FastThreadLocalRunnable.java:30)
	at java.lang.Thread.run(Thread.java:748)
	Suppressed: io.lettuce.core.RedisConnectionException: Unable to establish a connection to Redis Cluster at [RedisURI [host='************', port=7001], RedisURI [host='************', port=7002], RedisURI [host='************', port=7003], RedisURI [host='************', port=7004], RedisURI [host='************', port=7005], RedisURI [host='************', port=7006]]
		at io.lettuce.core.cluster.topology.AsyncConnections.get(AsyncConnections.java:89)
		at io.lettuce.core.cluster.topology.ClusterTopologyRefresh.loadViews(ClusterTopologyRefresh.java:76)
		at io.lettuce.core.cluster.RedisClusterClient.doLoadPartitions(RedisClusterClient.java:865)
		at io.lettuce.core.cluster.RedisClusterClient.loadPartitions(RedisClusterClient.java:838)
		... 10 common frames omitted
		Suppressed: io.lettuce.core.RedisConnectionException: Unable to connect to [************:7005]: No route to host: no further information: /************:7005
			at io.lettuce.core.cluster.topology.ClusterTopologyRefresh.lambda$getConnections$0(ClusterTopologyRefresh.java:290)
			at java.util.concurrent.CompletableFuture.uniWhenComplete(CompletableFuture.java:760)
			at java.util.concurrent.CompletableFuture$UniWhenComplete.tryFire(CompletableFuture.java:736)
			at java.util.concurrent.CompletableFuture.postComplete(CompletableFuture.java:474)
			at java.util.concurrent.CompletableFuture.completeExceptionally(CompletableFuture.java:1977)
			at io.lettuce.core.AbstractRedisClient.lambda$initializeChannelAsync0$4(AbstractRedisClient.java:329)
			at io.netty.util.concurrent.DefaultPromise.notifyListener0(DefaultPromise.java:577)
			at io.netty.util.concurrent.DefaultPromise.notifyListeners0(DefaultPromise.java:570)
			at io.netty.util.concurrent.DefaultPromise.notifyListenersNow(DefaultPromise.java:549)
			at io.netty.util.concurrent.DefaultPromise.notifyListeners(DefaultPromise.java:490)
			at io.netty.util.concurrent.DefaultPromise.setValue0(DefaultPromise.java:615)
			at io.netty.util.concurrent.DefaultPromise.setFailure0(DefaultPromise.java:608)
			at io.netty.util.concurrent.DefaultPromise.tryFailure(DefaultPromise.java:117)
			at io.netty.channel.nio.AbstractNioChannel$AbstractNioUnsafe.fulfillConnectPromise(AbstractNioChannel.java:321)
			at io.netty.channel.nio.AbstractNioChannel$AbstractNioUnsafe.finishConnect(AbstractNioChannel.java:337)
			at io.netty.channel.nio.NioEventLoop.processSelectedKey(NioEventLoop.java:688)
			at io.netty.channel.nio.NioEventLoop.processSelectedKeysOptimized(NioEventLoop.java:635)
			at io.netty.channel.nio.NioEventLoop.processSelectedKeys(NioEventLoop.java:552)
			at io.netty.channel.nio.NioEventLoop.run(NioEventLoop.java:514)
			... 4 common frames omitted
		Caused by: io.netty.channel.AbstractChannel$AnnotatedNoRouteToHostException: No route to host: no further information: /************:7005
		Caused by: java.net.NoRouteToHostException: No route to host: no further information
			at sun.nio.ch.SocketChannelImpl.checkConnect(Native Method)
			at sun.nio.ch.SocketChannelImpl.finishConnect(SocketChannelImpl.java:717)
			at io.netty.channel.socket.nio.NioSocketChannel.doFinishConnect(NioSocketChannel.java:330)
			at io.netty.channel.nio.AbstractNioChannel$AbstractNioUnsafe.finishConnect(AbstractNioChannel.java:334)
			at io.netty.channel.nio.NioEventLoop.processSelectedKey(NioEventLoop.java:688)
			at io.netty.channel.nio.NioEventLoop.processSelectedKeysOptimized(NioEventLoop.java:635)
			at io.netty.channel.nio.NioEventLoop.processSelectedKeys(NioEventLoop.java:552)
			at io.netty.channel.nio.NioEventLoop.run(NioEventLoop.java:514)
			at io.netty.util.concurrent.SingleThreadEventExecutor$6.run(SingleThreadEventExecutor.java:1050)
			at io.netty.util.internal.ThreadExecutorMap$2.run(ThreadExecutorMap.java:74)
			at io.netty.util.concurrent.FastThreadLocalRunnable.run(FastThreadLocalRunnable.java:30)
			at java.lang.Thread.run(Thread.java:748)
		Suppressed: io.lettuce.core.RedisConnectionException: Unable to connect to [************:7006]: No route to host: no further information: /************:7006
			at io.lettuce.core.cluster.topology.ClusterTopologyRefresh.lambda$getConnections$0(ClusterTopologyRefresh.java:290)
			at java.util.concurrent.CompletableFuture.uniWhenComplete(CompletableFuture.java:760)
			at java.util.concurrent.CompletableFuture$UniWhenComplete.tryFire(CompletableFuture.java:736)
			at java.util.concurrent.CompletableFuture.postComplete(CompletableFuture.java:474)
			at java.util.concurrent.CompletableFuture.completeExceptionally(CompletableFuture.java:1977)
			at io.lettuce.core.AbstractRedisClient.lambda$initializeChannelAsync0$4(AbstractRedisClient.java:329)
			at io.netty.util.concurrent.DefaultPromise.notifyListener0(DefaultPromise.java:577)
			at io.netty.util.concurrent.DefaultPromise.notifyListeners0(DefaultPromise.java:570)
			at io.netty.util.concurrent.DefaultPromise.notifyListenersNow(DefaultPromise.java:549)
			at io.netty.util.concurrent.DefaultPromise.notifyListeners(DefaultPromise.java:490)
			at io.netty.util.concurrent.DefaultPromise.setValue0(DefaultPromise.java:615)
			at io.netty.util.concurrent.DefaultPromise.setFailure0(DefaultPromise.java:608)
			at io.netty.util.concurrent.DefaultPromise.tryFailure(DefaultPromise.java:117)
			at io.netty.channel.nio.AbstractNioChannel$AbstractNioUnsafe.fulfillConnectPromise(AbstractNioChannel.java:321)
			at io.netty.channel.nio.AbstractNioChannel$AbstractNioUnsafe.finishConnect(AbstractNioChannel.java:337)
			at io.netty.channel.nio.NioEventLoop.processSelectedKey(NioEventLoop.java:688)
			at io.netty.channel.nio.NioEventLoop.processSelectedKeysOptimized(NioEventLoop.java:635)
			at io.netty.channel.nio.NioEventLoop.processSelectedKeys(NioEventLoop.java:552)
			at io.netty.channel.nio.NioEventLoop.run(NioEventLoop.java:514)
			... 4 common frames omitted
		Caused by: io.netty.channel.AbstractChannel$AnnotatedNoRouteToHostException: No route to host: no further information: /************:7006
		Caused by: java.net.NoRouteToHostException: No route to host: no further information
			at sun.nio.ch.SocketChannelImpl.checkConnect(Native Method)
			at sun.nio.ch.SocketChannelImpl.finishConnect(SocketChannelImpl.java:717)
			at io.netty.channel.socket.nio.NioSocketChannel.doFinishConnect(NioSocketChannel.java:330)
			at io.netty.channel.nio.AbstractNioChannel$AbstractNioUnsafe.finishConnect(AbstractNioChannel.java:334)
			at io.netty.channel.nio.NioEventLoop.processSelectedKey(NioEventLoop.java:688)
			at io.netty.channel.nio.NioEventLoop.processSelectedKeysOptimized(NioEventLoop.java:635)
			at io.netty.channel.nio.NioEventLoop.processSelectedKeys(NioEventLoop.java:552)
			at io.netty.channel.nio.NioEventLoop.run(NioEventLoop.java:514)
			at io.netty.util.concurrent.SingleThreadEventExecutor$6.run(SingleThreadEventExecutor.java:1050)
			at io.netty.util.internal.ThreadExecutorMap$2.run(ThreadExecutorMap.java:74)
			at io.netty.util.concurrent.FastThreadLocalRunnable.run(FastThreadLocalRunnable.java:30)
			at java.lang.Thread.run(Thread.java:748)
		Suppressed: io.lettuce.core.RedisConnectionException: Unable to connect to [************:7002]: No route to host: no further information: /************:7002
			at io.lettuce.core.cluster.topology.ClusterTopologyRefresh.lambda$getConnections$0(ClusterTopologyRefresh.java:290)
			at java.util.concurrent.CompletableFuture.uniWhenComplete(CompletableFuture.java:760)
			at java.util.concurrent.CompletableFuture$UniWhenComplete.tryFire(CompletableFuture.java:736)
			at java.util.concurrent.CompletableFuture.postComplete(CompletableFuture.java:474)
			at java.util.concurrent.CompletableFuture.completeExceptionally(CompletableFuture.java:1977)
			at io.lettuce.core.AbstractRedisClient.lambda$initializeChannelAsync0$4(AbstractRedisClient.java:329)
			at io.netty.util.concurrent.DefaultPromise.notifyListener0(DefaultPromise.java:577)
			at io.netty.util.concurrent.DefaultPromise.notifyListeners0(DefaultPromise.java:570)
			at io.netty.util.concurrent.DefaultPromise.notifyListenersNow(DefaultPromise.java:549)
			at io.netty.util.concurrent.DefaultPromise.notifyListeners(DefaultPromise.java:490)
			at io.netty.util.concurrent.DefaultPromise.setValue0(DefaultPromise.java:615)
			at io.netty.util.concurrent.DefaultPromise.setFailure0(DefaultPromise.java:608)
			at io.netty.util.concurrent.DefaultPromise.tryFailure(DefaultPromise.java:117)
			at io.netty.channel.nio.AbstractNioChannel$AbstractNioUnsafe.fulfillConnectPromise(AbstractNioChannel.java:321)
			at io.netty.channel.nio.AbstractNioChannel$AbstractNioUnsafe.finishConnect(AbstractNioChannel.java:337)
			at io.netty.channel.nio.NioEventLoop.processSelectedKey(NioEventLoop.java:688)
			at io.netty.channel.nio.NioEventLoop.processSelectedKeysOptimized(NioEventLoop.java:635)
			at io.netty.channel.nio.NioEventLoop.processSelectedKeys(NioEventLoop.java:552)
			at io.netty.channel.nio.NioEventLoop.run(NioEventLoop.java:514)
			... 4 common frames omitted
		Caused by: io.netty.channel.AbstractChannel$AnnotatedNoRouteToHostException: No route to host: no further information: /************:7002
		Caused by: java.net.NoRouteToHostException: No route to host: no further information
			at sun.nio.ch.SocketChannelImpl.checkConnect(Native Method)
			at sun.nio.ch.SocketChannelImpl.finishConnect(SocketChannelImpl.java:717)
			at io.netty.channel.socket.nio.NioSocketChannel.doFinishConnect(NioSocketChannel.java:330)
			at io.netty.channel.nio.AbstractNioChannel$AbstractNioUnsafe.finishConnect(AbstractNioChannel.java:334)
			at io.netty.channel.nio.NioEventLoop.processSelectedKey(NioEventLoop.java:688)
			at io.netty.channel.nio.NioEventLoop.processSelectedKeysOptimized(NioEventLoop.java:635)
			at io.netty.channel.nio.NioEventLoop.processSelectedKeys(NioEventLoop.java:552)
			at io.netty.channel.nio.NioEventLoop.run(NioEventLoop.java:514)
			at io.netty.util.concurrent.SingleThreadEventExecutor$6.run(SingleThreadEventExecutor.java:1050)
			at io.netty.util.internal.ThreadExecutorMap$2.run(ThreadExecutorMap.java:74)
			at io.netty.util.concurrent.FastThreadLocalRunnable.run(FastThreadLocalRunnable.java:30)
			at java.lang.Thread.run(Thread.java:748)
		Suppressed: io.lettuce.core.RedisConnectionException: Unable to connect to [************:7004]: No route to host: no further information: /************:7004
			at io.lettuce.core.cluster.topology.ClusterTopologyRefresh.lambda$getConnections$0(ClusterTopologyRefresh.java:290)
			at java.util.concurrent.CompletableFuture.uniWhenComplete(CompletableFuture.java:760)
			at java.util.concurrent.CompletableFuture$UniWhenComplete.tryFire(CompletableFuture.java:736)
			at java.util.concurrent.CompletableFuture.postComplete(CompletableFuture.java:474)
			at java.util.concurrent.CompletableFuture.completeExceptionally(CompletableFuture.java:1977)
			at io.lettuce.core.AbstractRedisClient.lambda$initializeChannelAsync0$4(AbstractRedisClient.java:329)
			at io.netty.util.concurrent.DefaultPromise.notifyListener0(DefaultPromise.java:577)
			at io.netty.util.concurrent.DefaultPromise.notifyListeners0(DefaultPromise.java:570)
			at io.netty.util.concurrent.DefaultPromise.notifyListenersNow(DefaultPromise.java:549)
			at io.netty.util.concurrent.DefaultPromise.notifyListeners(DefaultPromise.java:490)
			at io.netty.util.concurrent.DefaultPromise.setValue0(DefaultPromise.java:615)
			at io.netty.util.concurrent.DefaultPromise.setFailure0(DefaultPromise.java:608)
			at io.netty.util.concurrent.DefaultPromise.tryFailure(DefaultPromise.java:117)
			at io.netty.channel.nio.AbstractNioChannel$AbstractNioUnsafe.fulfillConnectPromise(AbstractNioChannel.java:321)
			at io.netty.channel.nio.AbstractNioChannel$AbstractNioUnsafe.finishConnect(AbstractNioChannel.java:337)
			at io.netty.channel.nio.NioEventLoop.processSelectedKey(NioEventLoop.java:688)
			at io.netty.channel.nio.NioEventLoop.processSelectedKeysOptimized(NioEventLoop.java:635)
			at io.netty.channel.nio.NioEventLoop.processSelectedKeys(NioEventLoop.java:552)
			at io.netty.channel.nio.NioEventLoop.run(NioEventLoop.java:514)
			... 4 common frames omitted
		Caused by: io.netty.channel.AbstractChannel$AnnotatedNoRouteToHostException: No route to host: no further information: /************:7004
		Caused by: java.net.NoRouteToHostException: No route to host: no further information
			at sun.nio.ch.SocketChannelImpl.checkConnect(Native Method)
			at sun.nio.ch.SocketChannelImpl.finishConnect(SocketChannelImpl.java:717)
			at io.netty.channel.socket.nio.NioSocketChannel.doFinishConnect(NioSocketChannel.java:330)
			at io.netty.channel.nio.AbstractNioChannel$AbstractNioUnsafe.finishConnect(AbstractNioChannel.java:334)
			at io.netty.channel.nio.NioEventLoop.processSelectedKey(NioEventLoop.java:688)
			at io.netty.channel.nio.NioEventLoop.processSelectedKeysOptimized(NioEventLoop.java:635)
			at io.netty.channel.nio.NioEventLoop.processSelectedKeys(NioEventLoop.java:552)
			at io.netty.channel.nio.NioEventLoop.run(NioEventLoop.java:514)
			at io.netty.util.concurrent.SingleThreadEventExecutor$6.run(SingleThreadEventExecutor.java:1050)
			at io.netty.util.internal.ThreadExecutorMap$2.run(ThreadExecutorMap.java:74)
			at io.netty.util.concurrent.FastThreadLocalRunnable.run(FastThreadLocalRunnable.java:30)
			at java.lang.Thread.run(Thread.java:748)
		Suppressed: io.lettuce.core.RedisConnectionException: Unable to connect to [************:7001]: No route to host: no further information: /************:7001
			at io.lettuce.core.cluster.topology.ClusterTopologyRefresh.lambda$getConnections$0(ClusterTopologyRefresh.java:290)
			at java.util.concurrent.CompletableFuture.uniWhenComplete(CompletableFuture.java:760)
			at java.util.concurrent.CompletableFuture$UniWhenComplete.tryFire(CompletableFuture.java:736)
			at java.util.concurrent.CompletableFuture.postComplete(CompletableFuture.java:474)
			at java.util.concurrent.CompletableFuture.completeExceptionally(CompletableFuture.java:1977)
			at io.lettuce.core.AbstractRedisClient.lambda$initializeChannelAsync0$4(AbstractRedisClient.java:329)
			at io.netty.util.concurrent.DefaultPromise.notifyListener0(DefaultPromise.java:577)
			at io.netty.util.concurrent.DefaultPromise.notifyListeners0(DefaultPromise.java:570)
			at io.netty.util.concurrent.DefaultPromise.notifyListenersNow(DefaultPromise.java:549)
			at io.netty.util.concurrent.DefaultPromise.notifyListeners(DefaultPromise.java:490)
			at io.netty.util.concurrent.DefaultPromise.setValue0(DefaultPromise.java:615)
			at io.netty.util.concurrent.DefaultPromise.setFailure0(DefaultPromise.java:608)
			at io.netty.util.concurrent.DefaultPromise.tryFailure(DefaultPromise.java:117)
			at io.netty.channel.nio.AbstractNioChannel$AbstractNioUnsafe.fulfillConnectPromise(AbstractNioChannel.java:321)
			at io.netty.channel.nio.AbstractNioChannel$AbstractNioUnsafe.finishConnect(AbstractNioChannel.java:337)
			at io.netty.channel.nio.NioEventLoop.processSelectedKey(NioEventLoop.java:688)
			at io.netty.channel.nio.NioEventLoop.processSelectedKeysOptimized(NioEventLoop.java:635)
			at io.netty.channel.nio.NioEventLoop.processSelectedKeys(NioEventLoop.java:552)
			at io.netty.channel.nio.NioEventLoop.run(NioEventLoop.java:514)
			... 4 common frames omitted
		Caused by: io.netty.channel.AbstractChannel$AnnotatedNoRouteToHostException: No route to host: no further information: /************:7001
		Caused by: java.net.NoRouteToHostException: No route to host: no further information
			at sun.nio.ch.SocketChannelImpl.checkConnect(Native Method)
			at sun.nio.ch.SocketChannelImpl.finishConnect(SocketChannelImpl.java:717)
			at io.netty.channel.socket.nio.NioSocketChannel.doFinishConnect(NioSocketChannel.java:330)
			at io.netty.channel.nio.AbstractNioChannel$AbstractNioUnsafe.finishConnect(AbstractNioChannel.java:334)
			at io.netty.channel.nio.NioEventLoop.processSelectedKey(NioEventLoop.java:688)
			at io.netty.channel.nio.NioEventLoop.processSelectedKeysOptimized(NioEventLoop.java:635)
			at io.netty.channel.nio.NioEventLoop.processSelectedKeys(NioEventLoop.java:552)
			at io.netty.channel.nio.NioEventLoop.run(NioEventLoop.java:514)
			at io.netty.util.concurrent.SingleThreadEventExecutor$6.run(SingleThreadEventExecutor.java:1050)
			at io.netty.util.internal.ThreadExecutorMap$2.run(ThreadExecutorMap.java:74)
			at io.netty.util.concurrent.FastThreadLocalRunnable.run(FastThreadLocalRunnable.java:30)
			at java.lang.Thread.run(Thread.java:748)
	Caused by: io.lettuce.core.RedisConnectionException: Unable to connect to [************:7003]: No route to host: no further information: /************:7003
		at io.lettuce.core.cluster.topology.ClusterTopologyRefresh.lambda$getConnections$0(ClusterTopologyRefresh.java:290)
		at java.util.concurrent.CompletableFuture.uniWhenComplete(CompletableFuture.java:760)
		at java.util.concurrent.CompletableFuture$UniWhenComplete.tryFire(CompletableFuture.java:736)
		at java.util.concurrent.CompletableFuture.postComplete(CompletableFuture.java:474)
		at java.util.concurrent.CompletableFuture.completeExceptionally(CompletableFuture.java:1977)
		at io.lettuce.core.AbstractRedisClient.lambda$initializeChannelAsync0$4(AbstractRedisClient.java:329)
		at io.netty.util.concurrent.DefaultPromise.notifyListener0(DefaultPromise.java:577)
		at io.netty.util.concurrent.DefaultPromise.notifyListeners0(DefaultPromise.java:570)
		at io.netty.util.concurrent.DefaultPromise.notifyListenersNow(DefaultPromise.java:549)
		at io.netty.util.concurrent.DefaultPromise.notifyListeners(DefaultPromise.java:490)
		at io.netty.util.concurrent.DefaultPromise.setValue0(DefaultPromise.java:615)
		at io.netty.util.concurrent.DefaultPromise.setFailure0(DefaultPromise.java:608)
		at io.netty.util.concurrent.DefaultPromise.tryFailure(DefaultPromise.java:117)
		at io.netty.channel.nio.AbstractNioChannel$AbstractNioUnsafe.fulfillConnectPromise(AbstractNioChannel.java:321)
		at io.netty.channel.nio.AbstractNioChannel$AbstractNioUnsafe.finishConnect(AbstractNioChannel.java:337)
		at io.netty.channel.nio.NioEventLoop.processSelectedKey(NioEventLoop.java:688)
		at io.netty.channel.nio.NioEventLoop.processSelectedKeysOptimized(NioEventLoop.java:635)
		at io.netty.channel.nio.NioEventLoop.processSelectedKeys(NioEventLoop.java:552)
		at io.netty.channel.nio.NioEventLoop.run(NioEventLoop.java:514)
		... 4 common frames omitted
	Caused by: io.netty.channel.AbstractChannel$AnnotatedNoRouteToHostException: No route to host: no further information: /************:7003
	Caused by: java.net.NoRouteToHostException: No route to host: no further information
		at sun.nio.ch.SocketChannelImpl.checkConnect(Native Method)
		at sun.nio.ch.SocketChannelImpl.finishConnect(SocketChannelImpl.java:717)
		at io.netty.channel.socket.nio.NioSocketChannel.doFinishConnect(NioSocketChannel.java:330)
		at io.netty.channel.nio.AbstractNioChannel$AbstractNioUnsafe.finishConnect(AbstractNioChannel.java:334)
		at io.netty.channel.nio.NioEventLoop.processSelectedKey(NioEventLoop.java:688)
		at io.netty.channel.nio.NioEventLoop.processSelectedKeysOptimized(NioEventLoop.java:635)
		at io.netty.channel.nio.NioEventLoop.processSelectedKeys(NioEventLoop.java:552)
		at io.netty.channel.nio.NioEventLoop.run(NioEventLoop.java:514)
		at io.netty.util.concurrent.SingleThreadEventExecutor$6.run(SingleThreadEventExecutor.java:1050)
		at io.netty.util.internal.ThreadExecutorMap$2.run(ThreadExecutorMap.java:74)
		at io.netty.util.concurrent.FastThreadLocalRunnable.run(FastThreadLocalRunnable.java:30)
		at java.lang.Thread.run(Thread.java:748)
Caused by: io.lettuce.core.RedisConnectionException: Unable to establish a connection to Redis Cluster at [RedisURI [host='************', port=7001], RedisURI [host='************', port=7002], RedisURI [host='************', port=7003], RedisURI [host='************', port=7004], RedisURI [host='************', port=7005], RedisURI [host='************', port=7006]]
	at io.lettuce.core.cluster.topology.AsyncConnections.get(AsyncConnections.java:89)
	at io.lettuce.core.cluster.topology.ClusterTopologyRefresh.loadViews(ClusterTopologyRefresh.java:76)
	at io.lettuce.core.cluster.RedisClusterClient.doLoadPartitions(RedisClusterClient.java:865)
	at io.lettuce.core.cluster.RedisClusterClient.loadPartitions(RedisClusterClient.java:845)
	... 10 common frames omitted
	Suppressed: io.lettuce.core.RedisConnectionException: Unable to connect to [************:7004]: No route to host: no further information: /************:7004
		at io.lettuce.core.cluster.topology.ClusterTopologyRefresh.lambda$getConnections$0(ClusterTopologyRefresh.java:290)
		at java.util.concurrent.CompletableFuture.uniWhenComplete(CompletableFuture.java:760)
		at java.util.concurrent.CompletableFuture$UniWhenComplete.tryFire(CompletableFuture.java:736)
		at java.util.concurrent.CompletableFuture.postComplete(CompletableFuture.java:474)
		at java.util.concurrent.CompletableFuture.completeExceptionally(CompletableFuture.java:1977)
		at io.lettuce.core.AbstractRedisClient.lambda$initializeChannelAsync0$4(AbstractRedisClient.java:329)
		at io.netty.util.concurrent.DefaultPromise.notifyListener0(DefaultPromise.java:577)
		at io.netty.util.concurrent.DefaultPromise.notifyListeners0(DefaultPromise.java:570)
		at io.netty.util.concurrent.DefaultPromise.notifyListenersNow(DefaultPromise.java:549)
		at io.netty.util.concurrent.DefaultPromise.notifyListeners(DefaultPromise.java:490)
		at io.netty.util.concurrent.DefaultPromise.setValue0(DefaultPromise.java:615)
		at io.netty.util.concurrent.DefaultPromise.setFailure0(DefaultPromise.java:608)
		at io.netty.util.concurrent.DefaultPromise.tryFailure(DefaultPromise.java:117)
		at io.netty.channel.nio.AbstractNioChannel$AbstractNioUnsafe.fulfillConnectPromise(AbstractNioChannel.java:321)
		at io.netty.channel.nio.AbstractNioChannel$AbstractNioUnsafe.finishConnect(AbstractNioChannel.java:337)
		at io.netty.channel.nio.NioEventLoop.processSelectedKey(NioEventLoop.java:688)
		at io.netty.channel.nio.NioEventLoop.processSelectedKeysOptimized(NioEventLoop.java:635)
		at io.netty.channel.nio.NioEventLoop.processSelectedKeys(NioEventLoop.java:552)
		at io.netty.channel.nio.NioEventLoop.run(NioEventLoop.java:514)
		... 4 common frames omitted
	Caused by: io.netty.channel.AbstractChannel$AnnotatedNoRouteToHostException: No route to host: no further information: /************:7004
	Caused by: java.net.NoRouteToHostException: No route to host: no further information
		at sun.nio.ch.SocketChannelImpl.checkConnect(Native Method)
		at sun.nio.ch.SocketChannelImpl.finishConnect(SocketChannelImpl.java:717)
		at io.netty.channel.socket.nio.NioSocketChannel.doFinishConnect(NioSocketChannel.java:330)
		at io.netty.channel.nio.AbstractNioChannel$AbstractNioUnsafe.finishConnect(AbstractNioChannel.java:334)
		at io.netty.channel.nio.NioEventLoop.processSelectedKey(NioEventLoop.java:688)
		at io.netty.channel.nio.NioEventLoop.processSelectedKeysOptimized(NioEventLoop.java:635)
		at io.netty.channel.nio.NioEventLoop.processSelectedKeys(NioEventLoop.java:552)
		at io.netty.channel.nio.NioEventLoop.run(NioEventLoop.java:514)
		at io.netty.util.concurrent.SingleThreadEventExecutor$6.run(SingleThreadEventExecutor.java:1050)
		at io.netty.util.internal.ThreadExecutorMap$2.run(ThreadExecutorMap.java:74)
		at io.netty.util.concurrent.FastThreadLocalRunnable.run(FastThreadLocalRunnable.java:30)
		at java.lang.Thread.run(Thread.java:748)
	Suppressed: io.lettuce.core.RedisConnectionException: Unable to connect to [************:7005]: No route to host: no further information: /************:7005
		at io.lettuce.core.cluster.topology.ClusterTopologyRefresh.lambda$getConnections$0(ClusterTopologyRefresh.java:290)
		at java.util.concurrent.CompletableFuture.uniWhenComplete(CompletableFuture.java:760)
		at java.util.concurrent.CompletableFuture$UniWhenComplete.tryFire(CompletableFuture.java:736)
		at java.util.concurrent.CompletableFuture.postComplete(CompletableFuture.java:474)
		at java.util.concurrent.CompletableFuture.completeExceptionally(CompletableFuture.java:1977)
		at io.lettuce.core.AbstractRedisClient.lambda$initializeChannelAsync0$4(AbstractRedisClient.java:329)
		at io.netty.util.concurrent.DefaultPromise.notifyListener0(DefaultPromise.java:577)
		at io.netty.util.concurrent.DefaultPromise.notifyListeners0(DefaultPromise.java:570)
		at io.netty.util.concurrent.DefaultPromise.notifyListenersNow(DefaultPromise.java:549)
		at io.netty.util.concurrent.DefaultPromise.notifyListeners(DefaultPromise.java:490)
		at io.netty.util.concurrent.DefaultPromise.setValue0(DefaultPromise.java:615)
		at io.netty.util.concurrent.DefaultPromise.setFailure0(DefaultPromise.java:608)
		at io.netty.util.concurrent.DefaultPromise.tryFailure(DefaultPromise.java:117)
		at io.netty.channel.nio.AbstractNioChannel$AbstractNioUnsafe.fulfillConnectPromise(AbstractNioChannel.java:321)
		at io.netty.channel.nio.AbstractNioChannel$AbstractNioUnsafe.finishConnect(AbstractNioChannel.java:337)
		at io.netty.channel.nio.NioEventLoop.processSelectedKey(NioEventLoop.java:688)
		at io.netty.channel.nio.NioEventLoop.processSelectedKeysOptimized(NioEventLoop.java:635)
		at io.netty.channel.nio.NioEventLoop.processSelectedKeys(NioEventLoop.java:552)
		at io.netty.channel.nio.NioEventLoop.run(NioEventLoop.java:514)
		... 4 common frames omitted
	Caused by: io.netty.channel.AbstractChannel$AnnotatedNoRouteToHostException: No route to host: no further information: /************:7005
	Caused by: java.net.NoRouteToHostException: No route to host: no further information
		at sun.nio.ch.SocketChannelImpl.checkConnect(Native Method)
		at sun.nio.ch.SocketChannelImpl.finishConnect(SocketChannelImpl.java:717)
		at io.netty.channel.socket.nio.NioSocketChannel.doFinishConnect(NioSocketChannel.java:330)
		at io.netty.channel.nio.AbstractNioChannel$AbstractNioUnsafe.finishConnect(AbstractNioChannel.java:334)
		at io.netty.channel.nio.NioEventLoop.processSelectedKey(NioEventLoop.java:688)
		at io.netty.channel.nio.NioEventLoop.processSelectedKeysOptimized(NioEventLoop.java:635)
		at io.netty.channel.nio.NioEventLoop.processSelectedKeys(NioEventLoop.java:552)
		at io.netty.channel.nio.NioEventLoop.run(NioEventLoop.java:514)
		at io.netty.util.concurrent.SingleThreadEventExecutor$6.run(SingleThreadEventExecutor.java:1050)
		at io.netty.util.internal.ThreadExecutorMap$2.run(ThreadExecutorMap.java:74)
		at io.netty.util.concurrent.FastThreadLocalRunnable.run(FastThreadLocalRunnable.java:30)
		at java.lang.Thread.run(Thread.java:748)
	Suppressed: io.lettuce.core.RedisConnectionException: Unable to connect to [************:7002]: No route to host: no further information: /************:7002
		at io.lettuce.core.cluster.topology.ClusterTopologyRefresh.lambda$getConnections$0(ClusterTopologyRefresh.java:290)
		at java.util.concurrent.CompletableFuture.uniWhenComplete(CompletableFuture.java:760)
		at java.util.concurrent.CompletableFuture$UniWhenComplete.tryFire(CompletableFuture.java:736)
		at java.util.concurrent.CompletableFuture.postComplete(CompletableFuture.java:474)
		at java.util.concurrent.CompletableFuture.completeExceptionally(CompletableFuture.java:1977)
		at io.lettuce.core.AbstractRedisClient.lambda$initializeChannelAsync0$4(AbstractRedisClient.java:329)
		at io.netty.util.concurrent.DefaultPromise.notifyListener0(DefaultPromise.java:577)
		at io.netty.util.concurrent.DefaultPromise.notifyListeners0(DefaultPromise.java:570)
		at io.netty.util.concurrent.DefaultPromise.notifyListenersNow(DefaultPromise.java:549)
		at io.netty.util.concurrent.DefaultPromise.notifyListeners(DefaultPromise.java:490)
		at io.netty.util.concurrent.DefaultPromise.setValue0(DefaultPromise.java:615)
		at io.netty.util.concurrent.DefaultPromise.setFailure0(DefaultPromise.java:608)
		at io.netty.util.concurrent.DefaultPromise.tryFailure(DefaultPromise.java:117)
		at io.netty.channel.nio.AbstractNioChannel$AbstractNioUnsafe.fulfillConnectPromise(AbstractNioChannel.java:321)
		at io.netty.channel.nio.AbstractNioChannel$AbstractNioUnsafe.finishConnect(AbstractNioChannel.java:337)
		at io.netty.channel.nio.NioEventLoop.processSelectedKey(NioEventLoop.java:688)
		at io.netty.channel.nio.NioEventLoop.processSelectedKeysOptimized(NioEventLoop.java:635)
		at io.netty.channel.nio.NioEventLoop.processSelectedKeys(NioEventLoop.java:552)
		at io.netty.channel.nio.NioEventLoop.run(NioEventLoop.java:514)
		... 4 common frames omitted
	Caused by: io.netty.channel.AbstractChannel$AnnotatedNoRouteToHostException: No route to host: no further information: /************:7002
	Caused by: java.net.NoRouteToHostException: No route to host: no further information
		at sun.nio.ch.SocketChannelImpl.checkConnect(Native Method)
		at sun.nio.ch.SocketChannelImpl.finishConnect(SocketChannelImpl.java:717)
		at io.netty.channel.socket.nio.NioSocketChannel.doFinishConnect(NioSocketChannel.java:330)
		at io.netty.channel.nio.AbstractNioChannel$AbstractNioUnsafe.finishConnect(AbstractNioChannel.java:334)
		at io.netty.channel.nio.NioEventLoop.processSelectedKey(NioEventLoop.java:688)
		at io.netty.channel.nio.NioEventLoop.processSelectedKeysOptimized(NioEventLoop.java:635)
		at io.netty.channel.nio.NioEventLoop.processSelectedKeys(NioEventLoop.java:552)
		at io.netty.channel.nio.NioEventLoop.run(NioEventLoop.java:514)
		at io.netty.util.concurrent.SingleThreadEventExecutor$6.run(SingleThreadEventExecutor.java:1050)
		at io.netty.util.internal.ThreadExecutorMap$2.run(ThreadExecutorMap.java:74)
		at io.netty.util.concurrent.FastThreadLocalRunnable.run(FastThreadLocalRunnable.java:30)
		at java.lang.Thread.run(Thread.java:748)
	Suppressed: io.lettuce.core.RedisConnectionException: Unable to connect to [************:7003]: No route to host: no further information: /************:7003
		at io.lettuce.core.cluster.topology.ClusterTopologyRefresh.lambda$getConnections$0(ClusterTopologyRefresh.java:290)
		at java.util.concurrent.CompletableFuture.uniWhenComplete(CompletableFuture.java:760)
		at java.util.concurrent.CompletableFuture$UniWhenComplete.tryFire(CompletableFuture.java:736)
		at java.util.concurrent.CompletableFuture.postComplete(CompletableFuture.java:474)
		at java.util.concurrent.CompletableFuture.completeExceptionally(CompletableFuture.java:1977)
		at io.lettuce.core.AbstractRedisClient.lambda$initializeChannelAsync0$4(AbstractRedisClient.java:329)
		at io.netty.util.concurrent.DefaultPromise.notifyListener0(DefaultPromise.java:577)
		at io.netty.util.concurrent.DefaultPromise.notifyListeners0(DefaultPromise.java:570)
		at io.netty.util.concurrent.DefaultPromise.notifyListenersNow(DefaultPromise.java:549)
		at io.netty.util.concurrent.DefaultPromise.notifyListeners(DefaultPromise.java:490)
		at io.netty.util.concurrent.DefaultPromise.setValue0(DefaultPromise.java:615)
		at io.netty.util.concurrent.DefaultPromise.setFailure0(DefaultPromise.java:608)
		at io.netty.util.concurrent.DefaultPromise.tryFailure(DefaultPromise.java:117)
		at io.netty.channel.nio.AbstractNioChannel$AbstractNioUnsafe.fulfillConnectPromise(AbstractNioChannel.java:321)
		at io.netty.channel.nio.AbstractNioChannel$AbstractNioUnsafe.finishConnect(AbstractNioChannel.java:337)
		at io.netty.channel.nio.NioEventLoop.processSelectedKey(NioEventLoop.java:688)
		at io.netty.channel.nio.NioEventLoop.processSelectedKeysOptimized(NioEventLoop.java:635)
		at io.netty.channel.nio.NioEventLoop.processSelectedKeys(NioEventLoop.java:552)
		at io.netty.channel.nio.NioEventLoop.run(NioEventLoop.java:514)
		... 4 common frames omitted
	Caused by: io.netty.channel.AbstractChannel$AnnotatedNoRouteToHostException: No route to host: no further information: /************:7003
	Caused by: java.net.NoRouteToHostException: No route to host: no further information
		at sun.nio.ch.SocketChannelImpl.checkConnect(Native Method)
		at sun.nio.ch.SocketChannelImpl.finishConnect(SocketChannelImpl.java:717)
		at io.netty.channel.socket.nio.NioSocketChannel.doFinishConnect(NioSocketChannel.java:330)
		at io.netty.channel.nio.AbstractNioChannel$AbstractNioUnsafe.finishConnect(AbstractNioChannel.java:334)
		at io.netty.channel.nio.NioEventLoop.processSelectedKey(NioEventLoop.java:688)
		at io.netty.channel.nio.NioEventLoop.processSelectedKeysOptimized(NioEventLoop.java:635)
		at io.netty.channel.nio.NioEventLoop.processSelectedKeys(NioEventLoop.java:552)
		at io.netty.channel.nio.NioEventLoop.run(NioEventLoop.java:514)
		at io.netty.util.concurrent.SingleThreadEventExecutor$6.run(SingleThreadEventExecutor.java:1050)
		at io.netty.util.internal.ThreadExecutorMap$2.run(ThreadExecutorMap.java:74)
		at io.netty.util.concurrent.FastThreadLocalRunnable.run(FastThreadLocalRunnable.java:30)
		at java.lang.Thread.run(Thread.java:748)
	Suppressed: io.lettuce.core.RedisConnectionException: Unable to connect to [************:7001]: No route to host: no further information: /************:7001
		at io.lettuce.core.cluster.topology.ClusterTopologyRefresh.lambda$getConnections$0(ClusterTopologyRefresh.java:290)
		at java.util.concurrent.CompletableFuture.uniWhenComplete(CompletableFuture.java:760)
		at java.util.concurrent.CompletableFuture$UniWhenComplete.tryFire(CompletableFuture.java:736)
		at java.util.concurrent.CompletableFuture.postComplete(CompletableFuture.java:474)
		at java.util.concurrent.CompletableFuture.completeExceptionally(CompletableFuture.java:1977)
		at io.lettuce.core.AbstractRedisClient.lambda$initializeChannelAsync0$4(AbstractRedisClient.java:329)
		at io.netty.util.concurrent.DefaultPromise.notifyListener0(DefaultPromise.java:577)
		at io.netty.util.concurrent.DefaultPromise.notifyListeners0(DefaultPromise.java:570)
		at io.netty.util.concurrent.DefaultPromise.notifyListenersNow(DefaultPromise.java:549)
		at io.netty.util.concurrent.DefaultPromise.notifyListeners(DefaultPromise.java:490)
		at io.netty.util.concurrent.DefaultPromise.setValue0(DefaultPromise.java:615)
		at io.netty.util.concurrent.DefaultPromise.setFailure0(DefaultPromise.java:608)
		at io.netty.util.concurrent.DefaultPromise.tryFailure(DefaultPromise.java:117)
		at io.netty.channel.nio.AbstractNioChannel$AbstractNioUnsafe.fulfillConnectPromise(AbstractNioChannel.java:321)
		at io.netty.channel.nio.AbstractNioChannel$AbstractNioUnsafe.finishConnect(AbstractNioChannel.java:337)
		at io.netty.channel.nio.NioEventLoop.processSelectedKey(NioEventLoop.java:688)
		at io.netty.channel.nio.NioEventLoop.processSelectedKeysOptimized(NioEventLoop.java:635)
		at io.netty.channel.nio.NioEventLoop.processSelectedKeys(NioEventLoop.java:552)
		at io.netty.channel.nio.NioEventLoop.run(NioEventLoop.java:514)
		... 4 common frames omitted
	Caused by: io.netty.channel.AbstractChannel$AnnotatedNoRouteToHostException: No route to host: no further information: /************:7001
	Caused by: java.net.NoRouteToHostException: No route to host: no further information
		at sun.nio.ch.SocketChannelImpl.checkConnect(Native Method)
		at sun.nio.ch.SocketChannelImpl.finishConnect(SocketChannelImpl.java:717)
		at io.netty.channel.socket.nio.NioSocketChannel.doFinishConnect(NioSocketChannel.java:330)
		at io.netty.channel.nio.AbstractNioChannel$AbstractNioUnsafe.finishConnect(AbstractNioChannel.java:334)
		at io.netty.channel.nio.NioEventLoop.processSelectedKey(NioEventLoop.java:688)
		at io.netty.channel.nio.NioEventLoop.processSelectedKeysOptimized(NioEventLoop.java:635)
		at io.netty.channel.nio.NioEventLoop.processSelectedKeys(NioEventLoop.java:552)
		at io.netty.channel.nio.NioEventLoop.run(NioEventLoop.java:514)
		at io.netty.util.concurrent.SingleThreadEventExecutor$6.run(SingleThreadEventExecutor.java:1050)
		at io.netty.util.internal.ThreadExecutorMap$2.run(ThreadExecutorMap.java:74)
		at io.netty.util.concurrent.FastThreadLocalRunnable.run(FastThreadLocalRunnable.java:30)
		at java.lang.Thread.run(Thread.java:748)
Caused by: io.lettuce.core.RedisConnectionException: Unable to connect to [************:7006]: No route to host: no further information: /************:7006
	at io.lettuce.core.cluster.topology.ClusterTopologyRefresh.lambda$getConnections$0(ClusterTopologyRefresh.java:290)
	at java.util.concurrent.CompletableFuture.uniWhenComplete(CompletableFuture.java:760)
	at java.util.concurrent.CompletableFuture$UniWhenComplete.tryFire(CompletableFuture.java:736)
	at java.util.concurrent.CompletableFuture.postComplete(CompletableFuture.java:474)
	at java.util.concurrent.CompletableFuture.completeExceptionally(CompletableFuture.java:1977)
	at io.lettuce.core.AbstractRedisClient.lambda$initializeChannelAsync0$4(AbstractRedisClient.java:329)
	at io.netty.util.concurrent.DefaultPromise.notifyListener0(DefaultPromise.java:577)
	at io.netty.util.concurrent.DefaultPromise.notifyListeners0(DefaultPromise.java:570)
	at io.netty.util.concurrent.DefaultPromise.notifyListenersNow(DefaultPromise.java:549)
	at io.netty.util.concurrent.DefaultPromise.notifyListeners(DefaultPromise.java:490)
	at io.netty.util.concurrent.DefaultPromise.setValue0(DefaultPromise.java:615)
	at io.netty.util.concurrent.DefaultPromise.setFailure0(DefaultPromise.java:608)
	at io.netty.util.concurrent.DefaultPromise.tryFailure(DefaultPromise.java:117)
	at io.netty.channel.nio.AbstractNioChannel$AbstractNioUnsafe.fulfillConnectPromise(AbstractNioChannel.java:321)
	at io.netty.channel.nio.AbstractNioChannel$AbstractNioUnsafe.finishConnect(AbstractNioChannel.java:337)
	at io.netty.channel.nio.NioEventLoop.processSelectedKey(NioEventLoop.java:688)
	at io.netty.channel.nio.NioEventLoop.processSelectedKeysOptimized(NioEventLoop.java:635)
	at io.netty.channel.nio.NioEventLoop.processSelectedKeys(NioEventLoop.java:552)
	at io.netty.channel.nio.NioEventLoop.run(NioEventLoop.java:514)
	... 4 common frames omitted
Caused by: io.netty.channel.AbstractChannel$AnnotatedNoRouteToHostException: No route to host: no further information: /************:7006
Caused by: java.net.NoRouteToHostException: No route to host: no further information
	at sun.nio.ch.SocketChannelImpl.checkConnect(Native Method)
	at sun.nio.ch.SocketChannelImpl.finishConnect(SocketChannelImpl.java:717)
	at io.netty.channel.socket.nio.NioSocketChannel.doFinishConnect(NioSocketChannel.java:330)
	at io.netty.channel.nio.AbstractNioChannel$AbstractNioUnsafe.finishConnect(AbstractNioChannel.java:334)
	at io.netty.channel.nio.NioEventLoop.processSelectedKey(NioEventLoop.java:688)
	at io.netty.channel.nio.NioEventLoop.processSelectedKeysOptimized(NioEventLoop.java:635)
	at io.netty.channel.nio.NioEventLoop.processSelectedKeys(NioEventLoop.java:552)
	at io.netty.channel.nio.NioEventLoop.run(NioEventLoop.java:514)
	at io.netty.util.concurrent.SingleThreadEventExecutor$6.run(SingleThreadEventExecutor.java:1050)
	at io.netty.util.internal.ThreadExecutorMap$2.run(ThreadExecutorMap.java:74)
	at io.netty.util.concurrent.FastThreadLocalRunnable.run(FastThreadLocalRunnable.java:30)
	at java.lang.Thread.run(Thread.java:748)
2025-06-02 15:51:11.361 [AMQP Connection ************:5672] WARN  com.rabbitmq.client.impl.ForgivingExceptionHandler.log Line:115 - An unexpected connection driver error occured (Exception message: Connection reset)
2025-06-02 15:51:11.479 [AMQP Connection ************:5672] WARN  com.rabbitmq.client.impl.ForgivingExceptionHandler.log Line:115 - An unexpected connection driver error occured (Exception message: Connection reset)
2025-06-02 15:51:12.474 [AMQP Connection ************:5672] WARN  com.rabbitmq.client.impl.ForgivingExceptionHandler.log Line:115 - An unexpected connection driver error occured (Exception message: Connection reset)
2025-06-02 15:51:14.492 [AMQP Connection ************:5672] WARN  com.rabbitmq.client.impl.ForgivingExceptionHandler.log Line:115 - An unexpected connection driver error occured (Exception message: Connection reset)
2025-06-02 16:05:00.010 [pool-14-thread-7] WARN  com.simbest.boot.base.repository.CustomDynamicWhere.queryForList Line:50 - 自定义查询SQL输出为：【select * from US_TODO_MODEL t  where t.send_flag = 0 and t.work_flag = 0 and t.enabled =1】
2025-06-02 16:10:00.011 [pool-14-thread-10] WARN  com.simbest.boot.base.repository.CustomDynamicWhere.queryForList Line:50 - 自定义查询SQL输出为：【select * from US_TODO_MODEL t  where t.send_flag = 0 and t.work_flag = 0 and t.enabled =1】
2025-06-02 16:32:12.949 [AMQP Connection ************:5672] WARN  com.rabbitmq.client.impl.ForgivingExceptionHandler.log Line:115 - An unexpected connection driver error occured (Exception message: Connection reset)
2025-06-02 16:32:13.169 [AMQP Connection ************:5672] WARN  com.rabbitmq.client.impl.ForgivingExceptionHandler.log Line:115 - An unexpected connection driver error occured (Exception message: Connection reset)
2025-06-02 16:32:14.165 [AMQP Connection ************:5672] WARN  com.rabbitmq.client.impl.ForgivingExceptionHandler.log Line:115 - An unexpected connection driver error occured (Exception message: Connection reset)
2025-06-02 16:32:16.178 [AMQP Connection ************:5672] WARN  com.rabbitmq.client.impl.ForgivingExceptionHandler.log Line:115 - An unexpected connection driver error occured (Exception message: Connection reset)
2025-06-02 16:35:03.066 [pool-14-thread-2] WARN  com.simbest.boot.base.repository.CustomDynamicWhere.queryForList Line:50 - 自定义查询SQL输出为：【select * from US_TODO_MODEL t  where t.send_flag = 0 and t.work_flag = 0 and t.enabled =1】
2025-06-02 17:12:45.173 [AMQP Connection ************:5672] WARN  com.rabbitmq.client.impl.ForgivingExceptionHandler.log Line:115 - An unexpected connection driver error occured (Exception message: Connection reset)
2025-06-02 17:12:45.515 [AMQP Connection ************:5672] WARN  com.rabbitmq.client.impl.ForgivingExceptionHandler.log Line:115 - An unexpected connection driver error occured (Exception message: Connection reset)
2025-06-02 17:12:46.500 [AMQP Connection ************:5672] WARN  com.rabbitmq.client.impl.ForgivingExceptionHandler.log Line:115 - An unexpected connection driver error occured (Exception message: Connection reset)
2025-06-02 17:12:48.542 [AMQP Connection ************:5672] WARN  com.rabbitmq.client.impl.ForgivingExceptionHandler.log Line:115 - An unexpected connection driver error occured (Exception message: Connection reset)
2025-06-02 17:53:15.785 [AMQP Connection ************:5672] WARN  com.rabbitmq.client.impl.ForgivingExceptionHandler.log Line:115 - An unexpected connection driver error occured (Exception message: Connection reset)
2025-06-02 17:53:16.113 [AMQP Connection ************:5672] WARN  com.rabbitmq.client.impl.ForgivingExceptionHandler.log Line:115 - An unexpected connection driver error occured (Exception message: Connection reset)
2025-06-02 17:53:17.153 [AMQP Connection ************:5672] WARN  com.rabbitmq.client.impl.ForgivingExceptionHandler.log Line:115 - An unexpected connection driver error occured (Exception message: Connection reset)
2025-06-02 17:53:19.183 [AMQP Connection ************:5672] WARN  com.rabbitmq.client.impl.ForgivingExceptionHandler.log Line:115 - An unexpected connection driver error occured (Exception message: Connection reset)
2025-06-02 18:34:17.918 [AMQP Connection ************:5672] WARN  com.rabbitmq.client.impl.ForgivingExceptionHandler.log Line:115 - An unexpected connection driver error occured (Exception message: Connection reset)
2025-06-02 18:34:18.306 [AMQP Connection ************:5672] WARN  com.rabbitmq.client.impl.ForgivingExceptionHandler.log Line:115 - An unexpected connection driver error occured (Exception message: Connection reset)
2025-06-02 18:34:19.332 [AMQP Connection ************:5672] WARN  com.rabbitmq.client.impl.ForgivingExceptionHandler.log Line:115 - An unexpected connection driver error occured (Exception message: Connection reset)
2025-06-02 18:34:21.383 [AMQP Connection ************:5672] WARN  com.rabbitmq.client.impl.ForgivingExceptionHandler.log Line:115 - An unexpected connection driver error occured (Exception message: Connection reset)
2025-06-02 19:14:48.351 [AMQP Connection ************:5672] WARN  com.rabbitmq.client.impl.ForgivingExceptionHandler.log Line:115 - An unexpected connection driver error occured (Exception message: Connection reset)
2025-06-02 19:14:48.724 [AMQP Connection ************:5672] WARN  com.rabbitmq.client.impl.ForgivingExceptionHandler.log Line:115 - An unexpected connection driver error occured (Exception message: Connection reset)
2025-06-02 19:14:49.750 [AMQP Connection ************:5672] WARN  com.rabbitmq.client.impl.ForgivingExceptionHandler.log Line:115 - An unexpected connection driver error occured (Exception message: Connection reset)
2025-06-02 19:14:51.694 [AMQP Connection ************:5672] WARN  com.rabbitmq.client.impl.ForgivingExceptionHandler.log Line:115 - An unexpected connection driver error occured (Exception message: Connection reset)
2025-06-02 19:55:21.375 [AMQP Connection ************:5672] WARN  com.rabbitmq.client.impl.ForgivingExceptionHandler.log Line:115 - An unexpected connection driver error occured (Exception message: Connection reset)
2025-06-02 19:55:23.348 [AMQP Connection ************:5672] WARN  com.rabbitmq.client.impl.ForgivingExceptionHandler.log Line:115 - An unexpected connection driver error occured (Exception message: Connection reset)
2025-06-02 19:55:49.972 [AMQP Connection ************:5672] WARN  com.rabbitmq.client.impl.ForgivingExceptionHandler.log Line:115 - An unexpected connection driver error occured (Exception message: Connection reset)
2025-06-02 19:55:50.375 [AMQP Connection ************:5672] WARN  com.rabbitmq.client.impl.ForgivingExceptionHandler.log Line:115 - An unexpected connection driver error occured (Exception message: Connection reset)
2025-06-02 20:36:01.646 [lettuce-nioEventLoop-4-3] WARN  io.lettuce.core.cluster.topology.ClusterTopologyRefresh.lambda$getConnections$0 Line:284 - Unable to connect to [************:7005]: connection timed out: /************:7005
2025-06-02 20:36:01.646 [lettuce-nioEventLoop-4-11] WARN  io.lettuce.core.cluster.topology.ClusterTopologyRefresh.lambda$getConnections$0 Line:284 - Unable to connect to [************:7001]: connection timed out: /************:7001
2025-06-02 20:36:01.646 [lettuce-nioEventLoop-4-4] WARN  io.lettuce.core.cluster.topology.ClusterTopologyRefresh.lambda$getConnections$0 Line:284 - Unable to connect to [************:7006]: connection timed out: /************:7006
2025-06-02 20:36:01.646 [lettuce-nioEventLoop-4-1] WARN  io.lettuce.core.cluster.topology.ClusterTopologyRefresh.lambda$getConnections$0 Line:284 - Unable to connect to [************:7003]: connection timed out: /************:7003
2025-06-02 20:36:01.646 [lettuce-nioEventLoop-4-12] WARN  io.lettuce.core.cluster.topology.ClusterTopologyRefresh.lambda$getConnections$0 Line:284 - Unable to connect to [************:7002]: connection timed out: /************:7002
2025-06-02 20:36:01.646 [lettuce-nioEventLoop-4-2] WARN  io.lettuce.core.cluster.topology.ClusterTopologyRefresh.lambda$getConnections$0 Line:284 - Unable to connect to [************:7004]: connection timed out: /************:7004
2025-06-02 20:36:11.652 [lettuce-nioEventLoop-4-6] WARN  io.lettuce.core.cluster.topology.ClusterTopologyRefresh.lambda$getConnections$0 Line:284 - Unable to connect to [************:7004]: connection timed out: /************:7004
2025-06-02 20:36:11.652 [lettuce-nioEventLoop-4-10] WARN  io.lettuce.core.cluster.topology.ClusterTopologyRefresh.lambda$getConnections$0 Line:284 - Unable to connect to [************:7001]: connection timed out: /************:7001
2025-06-02 20:36:11.652 [lettuce-nioEventLoop-4-7] WARN  io.lettuce.core.cluster.topology.ClusterTopologyRefresh.lambda$getConnections$0 Line:284 - Unable to connect to [************:7005]: connection timed out: /************:7005
2025-06-02 20:36:11.652 [lettuce-nioEventLoop-4-8] WARN  io.lettuce.core.cluster.topology.ClusterTopologyRefresh.lambda$getConnections$0 Line:284 - Unable to connect to [************:7002]: connection timed out: /************:7002
2025-06-02 20:36:11.652 [lettuce-nioEventLoop-4-9] WARN  io.lettuce.core.cluster.topology.ClusterTopologyRefresh.lambda$getConnections$0 Line:284 - Unable to connect to [************:7003]: connection timed out: /************:7003
2025-06-02 20:36:11.652 [lettuce-nioEventLoop-4-5] WARN  io.lettuce.core.cluster.topology.ClusterTopologyRefresh.lambda$getConnections$0 Line:284 - Unable to connect to [************:7006]: connection timed out: /************:7006
2025-06-02 20:36:11.657 [lettuce-eventExecutorLoop-1-11] WARN  io.lettuce.core.cluster.ClusterTopologyRefreshScheduler.doRun Line:258 - Cannot refresh Redis Cluster topology
io.lettuce.core.RedisException: Cannot retrieve initial cluster partitions from initial URIs [RedisURI [host='************', port=7006], RedisURI [host='************', port=7004], RedisURI [host='************', port=7005], RedisURI [host='************', port=7002], RedisURI [host='************', port=7003], RedisURI [host='************', port=7001]]
	at io.lettuce.core.cluster.RedisClusterClient.loadPartitions(RedisClusterClient.java:848)
	at io.lettuce.core.cluster.RedisClusterClient.reloadPartitions(RedisClusterClient.java:784)
	at io.lettuce.core.cluster.ClusterTopologyRefreshScheduler$ClusterTopologyRefreshTask.doRun(ClusterTopologyRefreshScheduler.java:256)
	at io.lettuce.core.cluster.ClusterTopologyRefreshScheduler$ClusterTopologyRefreshTask.run(ClusterTopologyRefreshScheduler.java:236)
	at io.netty.util.concurrent.PromiseTask.runTask(PromiseTask.java:98)
	at io.netty.util.concurrent.PromiseTask.run(PromiseTask.java:106)
	at io.netty.util.concurrent.DefaultEventExecutor.run(DefaultEventExecutor.java:66)
	at io.netty.util.concurrent.SingleThreadEventExecutor$6.run(SingleThreadEventExecutor.java:1050)
	at io.netty.util.internal.ThreadExecutorMap$2.run(ThreadExecutorMap.java:74)
	at io.netty.util.concurrent.FastThreadLocalRunnable.run(FastThreadLocalRunnable.java:30)
	at java.lang.Thread.run(Thread.java:748)
	Suppressed: io.lettuce.core.RedisConnectionException: Unable to establish a connection to Redis Cluster at [RedisURI [host='************', port=7001], RedisURI [host='************', port=7002], RedisURI [host='************', port=7003], RedisURI [host='************', port=7004], RedisURI [host='************', port=7005], RedisURI [host='************', port=7006]]
		at io.lettuce.core.cluster.topology.AsyncConnections.get(AsyncConnections.java:89)
		at io.lettuce.core.cluster.topology.ClusterTopologyRefresh.loadViews(ClusterTopologyRefresh.java:76)
		at io.lettuce.core.cluster.RedisClusterClient.doLoadPartitions(RedisClusterClient.java:865)
		at io.lettuce.core.cluster.RedisClusterClient.loadPartitions(RedisClusterClient.java:838)
		... 10 common frames omitted
		Suppressed: io.lettuce.core.RedisConnectionException: Unable to connect to [************:7001]: connection timed out: /************:7001
			at io.lettuce.core.cluster.topology.ClusterTopologyRefresh.lambda$getConnections$0(ClusterTopologyRefresh.java:290)
			at java.util.concurrent.CompletableFuture.uniWhenComplete(CompletableFuture.java:760)
			at java.util.concurrent.CompletableFuture$UniWhenComplete.tryFire(CompletableFuture.java:736)
			at java.util.concurrent.CompletableFuture.postComplete(CompletableFuture.java:474)
			at java.util.concurrent.CompletableFuture.completeExceptionally(CompletableFuture.java:1977)
			at io.lettuce.core.AbstractRedisClient.lambda$initializeChannelAsync0$4(AbstractRedisClient.java:329)
			at io.netty.util.concurrent.DefaultPromise.notifyListener0(DefaultPromise.java:577)
			at io.netty.util.concurrent.DefaultPromise.notifyListeners0(DefaultPromise.java:570)
			at io.netty.util.concurrent.DefaultPromise.notifyListenersNow(DefaultPromise.java:549)
			at io.netty.util.concurrent.DefaultPromise.notifyListeners(DefaultPromise.java:490)
			at io.netty.util.concurrent.DefaultPromise.setValue0(DefaultPromise.java:615)
			at io.netty.util.concurrent.DefaultPromise.setFailure0(DefaultPromise.java:608)
			at io.netty.util.concurrent.DefaultPromise.tryFailure(DefaultPromise.java:117)
			at io.netty.channel.nio.AbstractNioChannel$AbstractNioUnsafe$1.run(AbstractNioChannel.java:263)
			at io.netty.util.concurrent.PromiseTask.runTask(PromiseTask.java:98)
			at io.netty.util.concurrent.ScheduledFutureTask.run(ScheduledFutureTask.java:150)
			at io.netty.util.concurrent.AbstractEventExecutor.safeExecute(AbstractEventExecutor.java:163)
			at io.netty.util.concurrent.SingleThreadEventExecutor.runAllTasks(SingleThreadEventExecutor.java:510)
			at io.netty.channel.nio.NioEventLoop.run(NioEventLoop.java:518)
			... 4 common frames omitted
		Caused by: io.netty.channel.ConnectTimeoutException: connection timed out: /************:7001
			at io.netty.channel.nio.AbstractNioChannel$AbstractNioUnsafe$1.run(AbstractNioChannel.java:261)
			... 9 common frames omitted
		Suppressed: io.lettuce.core.RedisConnectionException: Unable to connect to [************:7006]: connection timed out: /************:7006
			at io.lettuce.core.cluster.topology.ClusterTopologyRefresh.lambda$getConnections$0(ClusterTopologyRefresh.java:290)
			at java.util.concurrent.CompletableFuture.uniWhenComplete(CompletableFuture.java:760)
			at java.util.concurrent.CompletableFuture$UniWhenComplete.tryFire(CompletableFuture.java:736)
			at java.util.concurrent.CompletableFuture.postComplete(CompletableFuture.java:474)
			at java.util.concurrent.CompletableFuture.completeExceptionally(CompletableFuture.java:1977)
			at io.lettuce.core.AbstractRedisClient.lambda$initializeChannelAsync0$4(AbstractRedisClient.java:329)
			at io.netty.util.concurrent.DefaultPromise.notifyListener0(DefaultPromise.java:577)
			at io.netty.util.concurrent.DefaultPromise.notifyListeners0(DefaultPromise.java:570)
			at io.netty.util.concurrent.DefaultPromise.notifyListenersNow(DefaultPromise.java:549)
			at io.netty.util.concurrent.DefaultPromise.notifyListeners(DefaultPromise.java:490)
			at io.netty.util.concurrent.DefaultPromise.setValue0(DefaultPromise.java:615)
			at io.netty.util.concurrent.DefaultPromise.setFailure0(DefaultPromise.java:608)
			at io.netty.util.concurrent.DefaultPromise.tryFailure(DefaultPromise.java:117)
			at io.netty.channel.nio.AbstractNioChannel$AbstractNioUnsafe$1.run(AbstractNioChannel.java:263)
			at io.netty.util.concurrent.PromiseTask.runTask(PromiseTask.java:98)
			at io.netty.util.concurrent.ScheduledFutureTask.run(ScheduledFutureTask.java:150)
			at io.netty.util.concurrent.AbstractEventExecutor.safeExecute(AbstractEventExecutor.java:163)
			at io.netty.util.concurrent.SingleThreadEventExecutor.runAllTasks(SingleThreadEventExecutor.java:510)
			at io.netty.channel.nio.NioEventLoop.run(NioEventLoop.java:518)
			... 4 common frames omitted
		Caused by: io.netty.channel.ConnectTimeoutException: connection timed out: /************:7006
			at io.netty.channel.nio.AbstractNioChannel$AbstractNioUnsafe$1.run(AbstractNioChannel.java:261)
			... 9 common frames omitted
		Suppressed: io.lettuce.core.RedisConnectionException: Unable to connect to [************:7002]: connection timed out: /************:7002
			at io.lettuce.core.cluster.topology.ClusterTopologyRefresh.lambda$getConnections$0(ClusterTopologyRefresh.java:290)
			at java.util.concurrent.CompletableFuture.uniWhenComplete(CompletableFuture.java:760)
			at java.util.concurrent.CompletableFuture$UniWhenComplete.tryFire(CompletableFuture.java:736)
			at java.util.concurrent.CompletableFuture.postComplete(CompletableFuture.java:474)
			at java.util.concurrent.CompletableFuture.completeExceptionally(CompletableFuture.java:1977)
			at io.lettuce.core.AbstractRedisClient.lambda$initializeChannelAsync0$4(AbstractRedisClient.java:329)
			at io.netty.util.concurrent.DefaultPromise.notifyListener0(DefaultPromise.java:577)
			at io.netty.util.concurrent.DefaultPromise.notifyListeners0(DefaultPromise.java:570)
			at io.netty.util.concurrent.DefaultPromise.notifyListenersNow(DefaultPromise.java:549)
			at io.netty.util.concurrent.DefaultPromise.notifyListeners(DefaultPromise.java:490)
			at io.netty.util.concurrent.DefaultPromise.setValue0(DefaultPromise.java:615)
			at io.netty.util.concurrent.DefaultPromise.setFailure0(DefaultPromise.java:608)
			at io.netty.util.concurrent.DefaultPromise.tryFailure(DefaultPromise.java:117)
			at io.netty.channel.nio.AbstractNioChannel$AbstractNioUnsafe$1.run(AbstractNioChannel.java:263)
			at io.netty.util.concurrent.PromiseTask.runTask(PromiseTask.java:98)
			at io.netty.util.concurrent.ScheduledFutureTask.run(ScheduledFutureTask.java:150)
			at io.netty.util.concurrent.AbstractEventExecutor.safeExecute(AbstractEventExecutor.java:163)
			at io.netty.util.concurrent.SingleThreadEventExecutor.runAllTasks(SingleThreadEventExecutor.java:510)
			at io.netty.channel.nio.NioEventLoop.run(NioEventLoop.java:518)
			... 4 common frames omitted
		Caused by: io.netty.channel.ConnectTimeoutException: connection timed out: /************:7002
			at io.netty.channel.nio.AbstractNioChannel$AbstractNioUnsafe$1.run(AbstractNioChannel.java:261)
			... 9 common frames omitted
		Suppressed: io.lettuce.core.RedisConnectionException: Unable to connect to [************:7004]: connection timed out: /************:7004
			at io.lettuce.core.cluster.topology.ClusterTopologyRefresh.lambda$getConnections$0(ClusterTopologyRefresh.java:290)
			at java.util.concurrent.CompletableFuture.uniWhenComplete(CompletableFuture.java:760)
			at java.util.concurrent.CompletableFuture$UniWhenComplete.tryFire(CompletableFuture.java:736)
			at java.util.concurrent.CompletableFuture.postComplete(CompletableFuture.java:474)
			at java.util.concurrent.CompletableFuture.completeExceptionally(CompletableFuture.java:1977)
			at io.lettuce.core.AbstractRedisClient.lambda$initializeChannelAsync0$4(AbstractRedisClient.java:329)
			at io.netty.util.concurrent.DefaultPromise.notifyListener0(DefaultPromise.java:577)
			at io.netty.util.concurrent.DefaultPromise.notifyListeners0(DefaultPromise.java:570)
			at io.netty.util.concurrent.DefaultPromise.notifyListenersNow(DefaultPromise.java:549)
			at io.netty.util.concurrent.DefaultPromise.notifyListeners(DefaultPromise.java:490)
			at io.netty.util.concurrent.DefaultPromise.setValue0(DefaultPromise.java:615)
			at io.netty.util.concurrent.DefaultPromise.setFailure0(DefaultPromise.java:608)
			at io.netty.util.concurrent.DefaultPromise.tryFailure(DefaultPromise.java:117)
			at io.netty.channel.nio.AbstractNioChannel$AbstractNioUnsafe$1.run(AbstractNioChannel.java:263)
			at io.netty.util.concurrent.PromiseTask.runTask(PromiseTask.java:98)
			at io.netty.util.concurrent.ScheduledFutureTask.run(ScheduledFutureTask.java:150)
			at io.netty.util.concurrent.AbstractEventExecutor.safeExecute(AbstractEventExecutor.java:163)
			at io.netty.util.concurrent.SingleThreadEventExecutor.runAllTasks(SingleThreadEventExecutor.java:510)
			at io.netty.channel.nio.NioEventLoop.run(NioEventLoop.java:518)
			... 4 common frames omitted
		Caused by: io.netty.channel.ConnectTimeoutException: connection timed out: /************:7004
			at io.netty.channel.nio.AbstractNioChannel$AbstractNioUnsafe$1.run(AbstractNioChannel.java:261)
			... 9 common frames omitted
		Suppressed: io.lettuce.core.RedisConnectionException: Unable to connect to [************:7003]: connection timed out: /************:7003
			at io.lettuce.core.cluster.topology.ClusterTopologyRefresh.lambda$getConnections$0(ClusterTopologyRefresh.java:290)
			at java.util.concurrent.CompletableFuture.uniWhenComplete(CompletableFuture.java:760)
			at java.util.concurrent.CompletableFuture$UniWhenComplete.tryFire(CompletableFuture.java:736)
			at java.util.concurrent.CompletableFuture.postComplete(CompletableFuture.java:474)
			at java.util.concurrent.CompletableFuture.completeExceptionally(CompletableFuture.java:1977)
			at io.lettuce.core.AbstractRedisClient.lambda$initializeChannelAsync0$4(AbstractRedisClient.java:329)
			at io.netty.util.concurrent.DefaultPromise.notifyListener0(DefaultPromise.java:577)
			at io.netty.util.concurrent.DefaultPromise.notifyListeners0(DefaultPromise.java:570)
			at io.netty.util.concurrent.DefaultPromise.notifyListenersNow(DefaultPromise.java:549)
			at io.netty.util.concurrent.DefaultPromise.notifyListeners(DefaultPromise.java:490)
			at io.netty.util.concurrent.DefaultPromise.setValue0(DefaultPromise.java:615)
			at io.netty.util.concurrent.DefaultPromise.setFailure0(DefaultPromise.java:608)
			at io.netty.util.concurrent.DefaultPromise.tryFailure(DefaultPromise.java:117)
			at io.netty.channel.nio.AbstractNioChannel$AbstractNioUnsafe$1.run(AbstractNioChannel.java:263)
			at io.netty.util.concurrent.PromiseTask.runTask(PromiseTask.java:98)
			at io.netty.util.concurrent.ScheduledFutureTask.run(ScheduledFutureTask.java:150)
			at io.netty.util.concurrent.AbstractEventExecutor.safeExecute(AbstractEventExecutor.java:163)
			at io.netty.util.concurrent.SingleThreadEventExecutor.runAllTasks(SingleThreadEventExecutor.java:510)
			at io.netty.channel.nio.NioEventLoop.run(NioEventLoop.java:518)
			... 4 common frames omitted
		Caused by: io.netty.channel.ConnectTimeoutException: connection timed out: /************:7003
			at io.netty.channel.nio.AbstractNioChannel$AbstractNioUnsafe$1.run(AbstractNioChannel.java:261)
			... 9 common frames omitted
	Caused by: io.lettuce.core.RedisConnectionException: Unable to connect to [************:7005]: connection timed out: /************:7005
		at io.lettuce.core.cluster.topology.ClusterTopologyRefresh.lambda$getConnections$0(ClusterTopologyRefresh.java:290)
		at java.util.concurrent.CompletableFuture.uniWhenComplete(CompletableFuture.java:760)
		at java.util.concurrent.CompletableFuture$UniWhenComplete.tryFire(CompletableFuture.java:736)
		at java.util.concurrent.CompletableFuture.postComplete(CompletableFuture.java:474)
		at java.util.concurrent.CompletableFuture.completeExceptionally(CompletableFuture.java:1977)
		at io.lettuce.core.AbstractRedisClient.lambda$initializeChannelAsync0$4(AbstractRedisClient.java:329)
		at io.netty.util.concurrent.DefaultPromise.notifyListener0(DefaultPromise.java:577)
		at io.netty.util.concurrent.DefaultPromise.notifyListeners0(DefaultPromise.java:570)
		at io.netty.util.concurrent.DefaultPromise.notifyListenersNow(DefaultPromise.java:549)
		at io.netty.util.concurrent.DefaultPromise.notifyListeners(DefaultPromise.java:490)
		at io.netty.util.concurrent.DefaultPromise.setValue0(DefaultPromise.java:615)
		at io.netty.util.concurrent.DefaultPromise.setFailure0(DefaultPromise.java:608)
		at io.netty.util.concurrent.DefaultPromise.tryFailure(DefaultPromise.java:117)
		at io.netty.channel.nio.AbstractNioChannel$AbstractNioUnsafe$1.run(AbstractNioChannel.java:263)
		at io.netty.util.concurrent.PromiseTask.runTask(PromiseTask.java:98)
		at io.netty.util.concurrent.ScheduledFutureTask.run(ScheduledFutureTask.java:150)
		at io.netty.util.concurrent.AbstractEventExecutor.safeExecute(AbstractEventExecutor.java:163)
		at io.netty.util.concurrent.SingleThreadEventExecutor.runAllTasks(SingleThreadEventExecutor.java:510)
		at io.netty.channel.nio.NioEventLoop.run(NioEventLoop.java:518)
		... 4 common frames omitted
	Caused by: io.netty.channel.ConnectTimeoutException: connection timed out: /************:7005
		at io.netty.channel.nio.AbstractNioChannel$AbstractNioUnsafe$1.run(AbstractNioChannel.java:261)
		... 9 common frames omitted
Caused by: io.lettuce.core.RedisConnectionException: Unable to establish a connection to Redis Cluster at [RedisURI [host='************', port=7001], RedisURI [host='************', port=7002], RedisURI [host='************', port=7003], RedisURI [host='************', port=7004], RedisURI [host='************', port=7005], RedisURI [host='************', port=7006]]
	at io.lettuce.core.cluster.topology.AsyncConnections.get(AsyncConnections.java:89)
	at io.lettuce.core.cluster.topology.ClusterTopologyRefresh.loadViews(ClusterTopologyRefresh.java:76)
	at io.lettuce.core.cluster.RedisClusterClient.doLoadPartitions(RedisClusterClient.java:865)
	at io.lettuce.core.cluster.RedisClusterClient.loadPartitions(RedisClusterClient.java:845)
	... 10 common frames omitted
	Suppressed: io.lettuce.core.RedisConnectionException: Unable to connect to [************:7001]: connection timed out: /************:7001
		at io.lettuce.core.cluster.topology.ClusterTopologyRefresh.lambda$getConnections$0(ClusterTopologyRefresh.java:290)
		at java.util.concurrent.CompletableFuture.uniWhenComplete(CompletableFuture.java:760)
		at java.util.concurrent.CompletableFuture$UniWhenComplete.tryFire(CompletableFuture.java:736)
		at java.util.concurrent.CompletableFuture.postComplete(CompletableFuture.java:474)
		at java.util.concurrent.CompletableFuture.completeExceptionally(CompletableFuture.java:1977)
		at io.lettuce.core.AbstractRedisClient.lambda$initializeChannelAsync0$4(AbstractRedisClient.java:329)
		at io.netty.util.concurrent.DefaultPromise.notifyListener0(DefaultPromise.java:577)
		at io.netty.util.concurrent.DefaultPromise.notifyListeners0(DefaultPromise.java:570)
		at io.netty.util.concurrent.DefaultPromise.notifyListenersNow(DefaultPromise.java:549)
		at io.netty.util.concurrent.DefaultPromise.notifyListeners(DefaultPromise.java:490)
		at io.netty.util.concurrent.DefaultPromise.setValue0(DefaultPromise.java:615)
		at io.netty.util.concurrent.DefaultPromise.setFailure0(DefaultPromise.java:608)
		at io.netty.util.concurrent.DefaultPromise.tryFailure(DefaultPromise.java:117)
		at io.netty.channel.nio.AbstractNioChannel$AbstractNioUnsafe$1.run(AbstractNioChannel.java:263)
		at io.netty.util.concurrent.PromiseTask.runTask(PromiseTask.java:98)
		at io.netty.util.concurrent.ScheduledFutureTask.run(ScheduledFutureTask.java:150)
		at io.netty.util.concurrent.AbstractEventExecutor.safeExecute(AbstractEventExecutor.java:163)
		at io.netty.util.concurrent.SingleThreadEventExecutor.runAllTasks(SingleThreadEventExecutor.java:510)
		at io.netty.channel.nio.NioEventLoop.run(NioEventLoop.java:518)
		... 4 common frames omitted
	Caused by: io.netty.channel.ConnectTimeoutException: connection timed out: /************:7001
		at io.netty.channel.nio.AbstractNioChannel$AbstractNioUnsafe$1.run(AbstractNioChannel.java:261)
		... 9 common frames omitted
	Suppressed: io.lettuce.core.RedisConnectionException: Unable to connect to [************:7005]: connection timed out: /************:7005
		at io.lettuce.core.cluster.topology.ClusterTopologyRefresh.lambda$getConnections$0(ClusterTopologyRefresh.java:290)
		at java.util.concurrent.CompletableFuture.uniWhenComplete(CompletableFuture.java:760)
		at java.util.concurrent.CompletableFuture$UniWhenComplete.tryFire(CompletableFuture.java:736)
		at java.util.concurrent.CompletableFuture.postComplete(CompletableFuture.java:474)
		at java.util.concurrent.CompletableFuture.completeExceptionally(CompletableFuture.java:1977)
		at io.lettuce.core.AbstractRedisClient.lambda$initializeChannelAsync0$4(AbstractRedisClient.java:329)
		at io.netty.util.concurrent.DefaultPromise.notifyListener0(DefaultPromise.java:577)
		at io.netty.util.concurrent.DefaultPromise.notifyListeners0(DefaultPromise.java:570)
		at io.netty.util.concurrent.DefaultPromise.notifyListenersNow(DefaultPromise.java:549)
		at io.netty.util.concurrent.DefaultPromise.notifyListeners(DefaultPromise.java:490)
		at io.netty.util.concurrent.DefaultPromise.setValue0(DefaultPromise.java:615)
		at io.netty.util.concurrent.DefaultPromise.setFailure0(DefaultPromise.java:608)
		at io.netty.util.concurrent.DefaultPromise.tryFailure(DefaultPromise.java:117)
		at io.netty.channel.nio.AbstractNioChannel$AbstractNioUnsafe$1.run(AbstractNioChannel.java:263)
		at io.netty.util.concurrent.PromiseTask.runTask(PromiseTask.java:98)
		at io.netty.util.concurrent.ScheduledFutureTask.run(ScheduledFutureTask.java:150)
		at io.netty.util.concurrent.AbstractEventExecutor.safeExecute(AbstractEventExecutor.java:163)
		at io.netty.util.concurrent.SingleThreadEventExecutor.runAllTasks(SingleThreadEventExecutor.java:510)
		at io.netty.channel.nio.NioEventLoop.run(NioEventLoop.java:518)
		... 4 common frames omitted
	Caused by: io.netty.channel.ConnectTimeoutException: connection timed out: /************:7005
		at io.netty.channel.nio.AbstractNioChannel$AbstractNioUnsafe$1.run(AbstractNioChannel.java:261)
		... 9 common frames omitted
	Suppressed: io.lettuce.core.RedisConnectionException: Unable to connect to [************:7003]: connection timed out: /************:7003
		at io.lettuce.core.cluster.topology.ClusterTopologyRefresh.lambda$getConnections$0(ClusterTopologyRefresh.java:290)
		at java.util.concurrent.CompletableFuture.uniWhenComplete(CompletableFuture.java:760)
		at java.util.concurrent.CompletableFuture$UniWhenComplete.tryFire(CompletableFuture.java:736)
		at java.util.concurrent.CompletableFuture.postComplete(CompletableFuture.java:474)
		at java.util.concurrent.CompletableFuture.completeExceptionally(CompletableFuture.java:1977)
		at io.lettuce.core.AbstractRedisClient.lambda$initializeChannelAsync0$4(AbstractRedisClient.java:329)
		at io.netty.util.concurrent.DefaultPromise.notifyListener0(DefaultPromise.java:577)
		at io.netty.util.concurrent.DefaultPromise.notifyListeners0(DefaultPromise.java:570)
		at io.netty.util.concurrent.DefaultPromise.notifyListenersNow(DefaultPromise.java:549)
		at io.netty.util.concurrent.DefaultPromise.notifyListeners(DefaultPromise.java:490)
		at io.netty.util.concurrent.DefaultPromise.setValue0(DefaultPromise.java:615)
		at io.netty.util.concurrent.DefaultPromise.setFailure0(DefaultPromise.java:608)
		at io.netty.util.concurrent.DefaultPromise.tryFailure(DefaultPromise.java:117)
		at io.netty.channel.nio.AbstractNioChannel$AbstractNioUnsafe$1.run(AbstractNioChannel.java:263)
		at io.netty.util.concurrent.PromiseTask.runTask(PromiseTask.java:98)
		at io.netty.util.concurrent.ScheduledFutureTask.run(ScheduledFutureTask.java:150)
		at io.netty.util.concurrent.AbstractEventExecutor.safeExecute(AbstractEventExecutor.java:163)
		at io.netty.util.concurrent.SingleThreadEventExecutor.runAllTasks(SingleThreadEventExecutor.java:510)
		at io.netty.channel.nio.NioEventLoop.run(NioEventLoop.java:518)
		... 4 common frames omitted
	Caused by: io.netty.channel.ConnectTimeoutException: connection timed out: /************:7003
		at io.netty.channel.nio.AbstractNioChannel$AbstractNioUnsafe$1.run(AbstractNioChannel.java:261)
		... 9 common frames omitted
	Suppressed: io.lettuce.core.RedisConnectionException: Unable to connect to [************:7002]: connection timed out: /************:7002
		at io.lettuce.core.cluster.topology.ClusterTopologyRefresh.lambda$getConnections$0(ClusterTopologyRefresh.java:290)
		at java.util.concurrent.CompletableFuture.uniWhenComplete(CompletableFuture.java:760)
		at java.util.concurrent.CompletableFuture$UniWhenComplete.tryFire(CompletableFuture.java:736)
		at java.util.concurrent.CompletableFuture.postComplete(CompletableFuture.java:474)
		at java.util.concurrent.CompletableFuture.completeExceptionally(CompletableFuture.java:1977)
		at io.lettuce.core.AbstractRedisClient.lambda$initializeChannelAsync0$4(AbstractRedisClient.java:329)
		at io.netty.util.concurrent.DefaultPromise.notifyListener0(DefaultPromise.java:577)
		at io.netty.util.concurrent.DefaultPromise.notifyListeners0(DefaultPromise.java:570)
		at io.netty.util.concurrent.DefaultPromise.notifyListenersNow(DefaultPromise.java:549)
		at io.netty.util.concurrent.DefaultPromise.notifyListeners(DefaultPromise.java:490)
		at io.netty.util.concurrent.DefaultPromise.setValue0(DefaultPromise.java:615)
		at io.netty.util.concurrent.DefaultPromise.setFailure0(DefaultPromise.java:608)
		at io.netty.util.concurrent.DefaultPromise.tryFailure(DefaultPromise.java:117)
		at io.netty.channel.nio.AbstractNioChannel$AbstractNioUnsafe$1.run(AbstractNioChannel.java:263)
		at io.netty.util.concurrent.PromiseTask.runTask(PromiseTask.java:98)
		at io.netty.util.concurrent.ScheduledFutureTask.run(ScheduledFutureTask.java:150)
		at io.netty.util.concurrent.AbstractEventExecutor.safeExecute(AbstractEventExecutor.java:163)
		at io.netty.util.concurrent.SingleThreadEventExecutor.runAllTasks(SingleThreadEventExecutor.java:510)
		at io.netty.channel.nio.NioEventLoop.run(NioEventLoop.java:518)
		... 4 common frames omitted
	Caused by: io.netty.channel.ConnectTimeoutException: connection timed out: /************:7002
		at io.netty.channel.nio.AbstractNioChannel$AbstractNioUnsafe$1.run(AbstractNioChannel.java:261)
		... 9 common frames omitted
	Suppressed: io.lettuce.core.RedisConnectionException: Unable to connect to [************:7006]: connection timed out: /************:7006
		at io.lettuce.core.cluster.topology.ClusterTopologyRefresh.lambda$getConnections$0(ClusterTopologyRefresh.java:290)
		at java.util.concurrent.CompletableFuture.uniWhenComplete(CompletableFuture.java:760)
		at java.util.concurrent.CompletableFuture$UniWhenComplete.tryFire(CompletableFuture.java:736)
		at java.util.concurrent.CompletableFuture.postComplete(CompletableFuture.java:474)
		at java.util.concurrent.CompletableFuture.completeExceptionally(CompletableFuture.java:1977)
		at io.lettuce.core.AbstractRedisClient.lambda$initializeChannelAsync0$4(AbstractRedisClient.java:329)
		at io.netty.util.concurrent.DefaultPromise.notifyListener0(DefaultPromise.java:577)
		at io.netty.util.concurrent.DefaultPromise.notifyListeners0(DefaultPromise.java:570)
		at io.netty.util.concurrent.DefaultPromise.notifyListenersNow(DefaultPromise.java:549)
		at io.netty.util.concurrent.DefaultPromise.notifyListeners(DefaultPromise.java:490)
		at io.netty.util.concurrent.DefaultPromise.setValue0(DefaultPromise.java:615)
		at io.netty.util.concurrent.DefaultPromise.setFailure0(DefaultPromise.java:608)
		at io.netty.util.concurrent.DefaultPromise.tryFailure(DefaultPromise.java:117)
		at io.netty.channel.nio.AbstractNioChannel$AbstractNioUnsafe$1.run(AbstractNioChannel.java:263)
		at io.netty.util.concurrent.PromiseTask.runTask(PromiseTask.java:98)
		at io.netty.util.concurrent.ScheduledFutureTask.run(ScheduledFutureTask.java:150)
		at io.netty.util.concurrent.AbstractEventExecutor.safeExecute(AbstractEventExecutor.java:163)
		at io.netty.util.concurrent.SingleThreadEventExecutor.runAllTasks(SingleThreadEventExecutor.java:510)
		at io.netty.channel.nio.NioEventLoop.run(NioEventLoop.java:518)
		... 4 common frames omitted
	Caused by: io.netty.channel.ConnectTimeoutException: connection timed out: /************:7006
		at io.netty.channel.nio.AbstractNioChannel$AbstractNioUnsafe$1.run(AbstractNioChannel.java:261)
		... 9 common frames omitted
Caused by: io.lettuce.core.RedisConnectionException: Unable to connect to [************:7004]: connection timed out: /************:7004
	at io.lettuce.core.cluster.topology.ClusterTopologyRefresh.lambda$getConnections$0(ClusterTopologyRefresh.java:290)
	at java.util.concurrent.CompletableFuture.uniWhenComplete(CompletableFuture.java:760)
	at java.util.concurrent.CompletableFuture$UniWhenComplete.tryFire(CompletableFuture.java:736)
	at java.util.concurrent.CompletableFuture.postComplete(CompletableFuture.java:474)
	at java.util.concurrent.CompletableFuture.completeExceptionally(CompletableFuture.java:1977)
	at io.lettuce.core.AbstractRedisClient.lambda$initializeChannelAsync0$4(AbstractRedisClient.java:329)
	at io.netty.util.concurrent.DefaultPromise.notifyListener0(DefaultPromise.java:577)
	at io.netty.util.concurrent.DefaultPromise.notifyListeners0(DefaultPromise.java:570)
	at io.netty.util.concurrent.DefaultPromise.notifyListenersNow(DefaultPromise.java:549)
	at io.netty.util.concurrent.DefaultPromise.notifyListeners(DefaultPromise.java:490)
	at io.netty.util.concurrent.DefaultPromise.setValue0(DefaultPromise.java:615)
	at io.netty.util.concurrent.DefaultPromise.setFailure0(DefaultPromise.java:608)
	at io.netty.util.concurrent.DefaultPromise.tryFailure(DefaultPromise.java:117)
	at io.netty.channel.nio.AbstractNioChannel$AbstractNioUnsafe$1.run(AbstractNioChannel.java:263)
	at io.netty.util.concurrent.PromiseTask.runTask(PromiseTask.java:98)
	at io.netty.util.concurrent.ScheduledFutureTask.run(ScheduledFutureTask.java:150)
	at io.netty.util.concurrent.AbstractEventExecutor.safeExecute(AbstractEventExecutor.java:163)
	at io.netty.util.concurrent.SingleThreadEventExecutor.runAllTasks(SingleThreadEventExecutor.java:510)
	at io.netty.channel.nio.NioEventLoop.run(NioEventLoop.java:518)
	... 4 common frames omitted
Caused by: io.netty.channel.ConnectTimeoutException: connection timed out: /************:7004
	at io.netty.channel.nio.AbstractNioChannel$AbstractNioUnsafe$1.run(AbstractNioChannel.java:261)
	... 9 common frames omitted
2025-06-02 20:36:19.947 [AMQP Connection ************:5672] WARN  com.rabbitmq.client.impl.ForgivingExceptionHandler.log Line:115 - An unexpected connection driver error occured (Exception message: Connection reset)
2025-06-02 20:36:20.315 [AMQP Connection ************:5672] WARN  com.rabbitmq.client.impl.ForgivingExceptionHandler.log Line:115 - An unexpected connection driver error occured (Exception message: Connection reset)
2025-06-02 20:36:21.030 [AMQP Connection ************:5672] WARN  com.rabbitmq.client.impl.ForgivingExceptionHandler.log Line:115 - An unexpected connection driver error occured (Exception message: Connection reset)
2025-06-02 20:36:21.669 [lettuce-nioEventLoop-4-12] WARN  io.lettuce.core.cluster.topology.ClusterTopologyRefresh.lambda$getConnections$0 Line:284 - Unable to connect to [************:7002]: connection timed out: /************:7002
2025-06-02 20:36:21.669 [lettuce-nioEventLoop-4-2] WARN  io.lettuce.core.cluster.topology.ClusterTopologyRefresh.lambda$getConnections$0 Line:284 - Unable to connect to [************:7004]: connection timed out: /************:7004
2025-06-02 20:36:21.669 [lettuce-nioEventLoop-4-3] WARN  io.lettuce.core.cluster.topology.ClusterTopologyRefresh.lambda$getConnections$0 Line:284 - Unable to connect to [************:7005]: connection timed out: /************:7005
2025-06-02 20:36:21.669 [lettuce-nioEventLoop-4-11] WARN  io.lettuce.core.cluster.topology.ClusterTopologyRefresh.lambda$getConnections$0 Line:284 - Unable to connect to [************:7001]: connection timed out: /************:7001
2025-06-02 20:36:21.669 [lettuce-nioEventLoop-4-1] WARN  io.lettuce.core.cluster.topology.ClusterTopologyRefresh.lambda$getConnections$0 Line:284 - Unable to connect to [************:7003]: connection timed out: /************:7003
2025-06-02 20:36:21.669 [lettuce-nioEventLoop-4-4] WARN  io.lettuce.core.cluster.topology.ClusterTopologyRefresh.lambda$getConnections$0 Line:284 - Unable to connect to [************:7006]: connection timed out: /************:7006
2025-06-02 20:36:23.040 [AMQP Connection ************:5672] WARN  com.rabbitmq.client.impl.ForgivingExceptionHandler.log Line:115 - An unexpected connection driver error occured (Exception message: Connection reset)
2025-06-02 21:16:59.083 [AMQP Connection ************:5672] WARN  com.rabbitmq.client.impl.ForgivingExceptionHandler.log Line:115 - An unexpected connection driver error occured (Exception message: Connection reset)
2025-06-02 21:16:59.765 [AMQP Connection ************:5672] WARN  com.rabbitmq.client.impl.ForgivingExceptionHandler.log Line:115 - An unexpected connection driver error occured (Exception message: Connection reset)
2025-06-02 21:17:00.076 [AMQP Connection ************:5672] WARN  com.rabbitmq.client.impl.ForgivingExceptionHandler.log Line:115 - An unexpected connection driver error occured (Exception message: Connection reset)
2025-06-02 21:17:01.088 [AMQP Connection ************:5672] WARN  com.rabbitmq.client.impl.ForgivingExceptionHandler.log Line:115 - An unexpected connection driver error occured (Exception message: Connection reset)
2025-06-02 21:57:31.022 [AMQP Connection ************:5672] WARN  com.rabbitmq.client.impl.ForgivingExceptionHandler.log Line:115 - An unexpected connection driver error occured (Exception message: Connection reset)
2025-06-02 21:57:31.659 [AMQP Connection ************:5672] WARN  com.rabbitmq.client.impl.ForgivingExceptionHandler.log Line:115 - An unexpected connection driver error occured (Exception message: Connection reset)
2025-06-02 21:57:32.016 [AMQP Connection ************:5672] WARN  com.rabbitmq.client.impl.ForgivingExceptionHandler.log Line:115 - An unexpected connection driver error occured (Exception message: Connection reset)
2025-06-02 21:57:33.039 [AMQP Connection ************:5672] WARN  com.rabbitmq.client.impl.ForgivingExceptionHandler.log Line:115 - An unexpected connection driver error occured (Exception message: Connection reset)
2025-06-02 22:38:31.792 [AMQP Connection ************:5672] WARN  com.rabbitmq.client.impl.ForgivingExceptionHandler.log Line:115 - An unexpected connection driver error occured (Exception message: Connection reset)
2025-06-02 22:38:32.428 [AMQP Connection ************:5672] WARN  com.rabbitmq.client.impl.ForgivingExceptionHandler.log Line:115 - An unexpected connection driver error occured (Exception message: Connection reset)
2025-06-02 22:38:32.800 [AMQP Connection ************:5672] WARN  com.rabbitmq.client.impl.ForgivingExceptionHandler.log Line:115 - An unexpected connection driver error occured (Exception message: Connection reset)
2025-06-02 22:38:33.797 [AMQP Connection ************:5672] WARN  com.rabbitmq.client.impl.ForgivingExceptionHandler.log Line:115 - An unexpected connection driver error occured (Exception message: Connection reset)
2025-06-02 23:19:03.897 [AMQP Connection ************:5672] WARN  com.rabbitmq.client.impl.ForgivingExceptionHandler.log Line:115 - An unexpected connection driver error occured (Exception message: Connection reset)
2025-06-02 23:19:04.552 [AMQP Connection ************:5672] WARN  com.rabbitmq.client.impl.ForgivingExceptionHandler.log Line:115 - An unexpected connection driver error occured (Exception message: Connection reset)
2025-06-02 23:19:04.958 [AMQP Connection ************:5672] WARN  com.rabbitmq.client.impl.ForgivingExceptionHandler.log Line:115 - An unexpected connection driver error occured (Exception message: Connection reset)
2025-06-02 23:19:05.955 [AMQP Connection ************:5672] WARN  com.rabbitmq.client.impl.ForgivingExceptionHandler.log Line:115 - An unexpected connection driver error occured (Exception message: Connection reset)
2025-06-02 23:59:34.552 [AMQP Connection ************:5672] WARN  com.rabbitmq.client.impl.ForgivingExceptionHandler.log Line:115 - An unexpected connection driver error occured (Exception message: Connection reset)
2025-06-02 23:59:35.594 [AMQP Connection ************:5672] WARN  com.rabbitmq.client.impl.ForgivingExceptionHandler.log Line:115 - An unexpected connection driver error occured (Exception message: Connection reset)
2025-06-02 23:59:36.687 [AMQP Connection ************:5672] WARN  com.rabbitmq.client.impl.ForgivingExceptionHandler.log Line:115 - An unexpected connection driver error occured (Exception message: Connection reset)
