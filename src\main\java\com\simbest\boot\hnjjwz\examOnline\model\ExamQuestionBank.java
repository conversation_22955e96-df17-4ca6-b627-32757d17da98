package com.simbest.boot.hnjjwz.examOnline.model;

import com.simbest.boot.base.annotations.EntityIdPrefix;
import com.simbest.boot.base.model.LogicModel;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.*;
import org.hibernate.annotations.GenericGenerator;

import javax.persistence.*;
import java.util.List;
import java.util.Map;

/**
 * <AUTHOR>
 * @Data 2019/05/08
 * Description
 */
@Data
@EqualsAndHashCode(callSuper = true)
@NoArgsConstructor
@AllArgsConstructor
@Entity(name = "us_exam_question_bank")
@ApiModel(value = "题库表")
public class ExamQuestionBank extends LogicModel {
    @Id
    @Column(name = "id", length = 40)
    @GeneratedValue(generator = "snowFlakeId")
    @GenericGenerator (name = "snowFlakeId", strategy = "com.simbest.boot.util.distribution.id.SnowflakeId")
    @EntityIdPrefix (prefix = "QB") //主键前缀，此为可选项注解
    private String id;

    @Column(length = 250)
    @Setter
    @Getter
    @ApiModelProperty(value = "题库编码", required = true)
    private String questionBankCode;

    @Column(length = 250)
    @Setter
    @Getter
    @ApiModelProperty(value = "题库名", required = true)
    private String questionBankName;

    @Column(length = 250)
    @Setter
    @Getter
    @ApiModelProperty(value = "题库规定时间限制", required = true)
    private String setTime;

    @Column(length = 250)
    @Setter
    @Getter
    @ApiModelProperty(value = "题库题数", required = true)
    private String questionBankSize;

    @Transient
    private String publishName;

    @Transient
    private String publishOrgCode;//发布人所在组织code

    @Transient
    private String publishOrgName;//发布人所在组织名

    @Transient
    private String publishDisplayName;//发布人所在组织全路径

    @Column(length = 250)
    @Setter
    @Getter
    @ApiModelProperty(value = "额外字段1", required = true)
    private String spare1;

    @Column(length = 250)
    @Setter
    @Getter
    @ApiModelProperty(value = "额外字段2", required = true)
    private String spare2;

    @Column(length = 250)
    @Setter
    @Getter
    @ApiModelProperty(value = "额外字段3", required = true)
    private String spare3;

    @Transient
    Map<Object,List<Object>> examQuestionList;

}
