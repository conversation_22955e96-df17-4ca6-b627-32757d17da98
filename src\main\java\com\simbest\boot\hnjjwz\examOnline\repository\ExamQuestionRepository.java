package com.simbest.boot.hnjjwz.examOnline.repository;

import com.simbest.boot.base.repository.LogicRepository;
import com.simbest.boot.hnjjwz.examOnline.model.ExamQuestion;
import org.springframework.data.domain.Page;
import org.springframework.data.domain.Pageable;
import org.springframework.data.jpa.repository.Query;
import org.springframework.data.repository.query.Param;

import java.util.List;
import java.util.Map;
import java.util.Set;

/**
 * <AUTHOR>
 * @Data 201/05/08
 * @Description 题目表
 */
public interface ExamQuestionRepository extends LogicRepository<ExamQuestion,String> {

    /**
     * 查询可用的题库内容
     * @return
     */
    @Query (value =  " SELECT *  " +
            " FROM us_exam_question ueq,us_exam_question_answer ueqa, " +
            " us_exam_question_bank_question ueqbq,us_exam_question_bank ueqb, " +
            " us_exam_answer uea " +
            " WHERE ueq.question_code = ueqa.question_code AND ueqa.answer_code = uea.answer_code " +
            " AND ueqbq.question_bank_code = ueqb.question_bank_code AND ueqbq.question_code = ueq.question_code" +
            " AND ueq.enabled = 1 AND ueq.removed_time IS NULL " +
            " AND ueqa.enabled = 1 AND ueqa.removed_time IS NULL " +
            " AND ueqbq.enabled = 1 AND ueqbq.removed_time IS NULL " +
            " AND ueqb.enabled = 1 AND ueqb.removed_time IS NULL " +
            " AND uea.enabled = 1 AND uea.removed_time IS NULL ",
            nativeQuery = true)
    Set<Map<String,Object>> findEnabledExamQuestion(  );


    @Query(value = "select t.* from(select t.* from US_EXAM_QUESTION t order by DBMS_RANDOM.random()) t where t.question_code like concat(:questionBankCode,'%') and t.question_type=:questionType and enabled = 1 and rownum<=:choice",
            nativeQuery = true)
    List<ExamQuestion> customFindAll(@Param("questionBankCode")String questionBankCode,@Param( "choice" )String choice,@Param( "questionType" )String questionType);

    @Query(value = "select t.* from US_EXAM_QUESTION t  where enabled = 1 order by t.question_type",
            nativeQuery = true)
    List<ExamQuestion> findAllExamQuestion();

    /**
     * 查询自己填报信息
     */
    @Query(
            value = "select t.* from US_EXAM_QUESTION t  where enabled = 1 ",
            countQuery = "select count(*)  from US_EXAM_QUESTION t  where enabled = 1 " ,
            nativeQuery = true
    )
    Page<ExamQuestion> findAllExamQuestion(Pageable pageable);

}
