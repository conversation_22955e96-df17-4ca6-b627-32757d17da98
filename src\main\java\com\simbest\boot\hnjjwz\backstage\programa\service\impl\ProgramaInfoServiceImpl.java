package com.simbest.boot.hnjjwz.backstage.programa.service.impl;

import com.simbest.boot.base.exception.Exceptions;
import com.simbest.boot.base.service.impl.LogicService;
import com.simbest.boot.base.web.response.JsonResponse;
import com.simbest.boot.constants.ApplicationConstants;
import com.simbest.boot.hnjjwz.backstage.programa.model.ProgramaInfo;
import com.simbest.boot.hnjjwz.backstage.programa.model.ProgramaPower;
import com.simbest.boot.hnjjwz.backstage.programa.model.TabsData;
import com.simbest.boot.hnjjwz.backstage.programa.repository.ProgramaInfoRepository;
import com.simbest.boot.hnjjwz.backstage.programa.service.IProgramaClassifyInfoService;
import com.simbest.boot.hnjjwz.backstage.programa.service.IProgramaInfoService;
import com.simbest.boot.hnjjwz.backstage.programa.service.IProgramaPowerImpl;
import com.simbest.boot.hnjjwz.constants.Constants;
import com.simbest.boot.sys.model.SysDictValue;
import com.simbest.boot.sys.model.SysFile;
import com.simbest.boot.sys.service.ISysFileService;
import com.simbest.boot.sys.service.impl.SysDictValueService;
import com.simbest.boot.util.MapUtil;
import com.simbest.boot.util.security.SecurityUtils;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.data.domain.Page;
import org.springframework.data.domain.PageImpl;
import org.springframework.data.domain.Pageable;
import org.springframework.stereotype.Service;
import org.springframework.util.StringUtils;

import java.util.*;

/**
 * <AUTHOR>
 * @Data 2019/03/29
 * @Description
 */
@Slf4j
@Service
public class ProgramaInfoServiceImpl extends LogicService<ProgramaInfo,String> implements IProgramaInfoService {

    private ProgramaInfoRepository programaInfoRepository;

    @Autowired
    private ISysFileService sysFileService;

    @Autowired
    public ProgramaInfoServiceImpl ( ProgramaInfoRepository programaInfoRepository) {
        super(programaInfoRepository);
        this.programaInfoRepository = programaInfoRepository;
    }

    @Autowired
    private IProgramaClassifyInfoService programaClassifyInfoService;

    @Autowired
    private SysDictValueService sysDictValueService;

    @Autowired
    private IProgramaPowerImpl iProgramaPower;

    /**
     * 自动生成栏目编码
     * @param parentProgramaCode 父栏目code
     * @return
     */
    @Override
    public String autoGenerationCode ( String parentProgramaCode ) {
        String maxCode = ApplicationConstants.EMPTY;
        String newCode = ApplicationConstants.EMPTY;
        //看是否有父栏目编码，若有的话再查询此父栏目编码下所有的栏目中最大的编码，在原有的基础上最后一个数字加1。
        if( !StringUtils.isEmpty( parentProgramaCode ) ){
            //查询此父栏目编码下最大的编码
            maxCode = programaInfoRepository.findMaxSon( parentProgramaCode);
            //没有的话就说明其为新增的一个栏目分类，是顶级的栏目
        }else{
            maxCode = programaInfoRepository.findMaxRoot();
        }
        newCode = findNewCode( maxCode, newCode, parentProgramaCode);
        return newCode;
    }

    /**
     * 根据所属栏目分类id、结点类型、父栏目名（模糊）、栏目名称（模糊）
     * @param programaClassifyIds
     * @param nodeStyles
     * @param parentProgramaName
     * @param programaName
     * @return
     */
    @Override
    public JsonResponse findDimProgramaInfo ( String programaClassifyIds, String nodeStyles, String parentProgramaName, String programaName, Pageable pageable ) {
        List<String> programaClassifyList = new ArrayList<>(  );
        if(StringUtils.isEmpty( programaClassifyIds )){
            //查询所有的类型
            programaClassifyList =  programaClassifyInfoService.findAllProgramaClassifyIds();
        }else{
            programaClassifyList.add(programaClassifyIds);
        }
        SysDictValue sysDictValue = new SysDictValue(  );
        sysDictValue.setDictType( Constants.DICT_TYPE );
        List<String> nodeStyleList = new ArrayList<>(  );
        if(StringUtils.isEmpty( nodeStyles )){
            //查询所有的nodeStyles
            List<SysDictValue> sysDictValueList  = sysDictValueService.findDictValue ( sysDictValue);
            for(SysDictValue sysDictValueNew:sysDictValueList){
                nodeStyleList.add( sysDictValueNew.getValue() );
            }
        }else{
            nodeStyleList.add( nodeStyles );
        }
        if(StringUtils.isEmpty( programaName )){
            programaName = "";
        }
        Page<Map<String,Object>> programaInfoPage = null;
        if(StringUtils.isEmpty( parentProgramaName )){
            programaInfoPage= programaInfoRepository.findDimProgramaInfoNoParent( programaClassifyList, nodeStyleList,  programaName,pageable);

        }else{
            programaInfoPage= programaInfoRepository.findDimProgramaInfo( programaClassifyList, nodeStyleList,  parentProgramaName,  programaName,pageable);

        }
        List<Map<String,Object>> programaInfoListOld= programaInfoPage.getContent();
        List<ProgramaInfo> programaInfoList = new ArrayList<>(  );
        for(Map<String,Object> programaInfoMap:programaInfoListOld){
            try {
                ProgramaInfo programaInfo  = (ProgramaInfo)MapUtil.mapToObject(programaInfoMap,ProgramaInfo.class  );
                programaInfoList.add( programaInfo );
            } catch ( Exception e ) {
                Exceptions.printException( e );
            }
        }
        Page<ProgramaInfo> programaInfoPageNew = new PageImpl<ProgramaInfo>(programaInfoList,pageable,programaInfoPage.getTotalElements() );
        return JsonResponse.success( programaInfoPageNew,"获取成功!" );
    }

    /**
     * 页面初始化时获取根栏目以及下一级栏目
     * @return
     */
    @Override
    public Set<ProgramaInfo> findRootAndNext ( ) {
        Set<ProgramaInfo> programaInfoAllSet = new LinkedHashSet<>(  );
        //获取顶级栏目
        Set<ProgramaInfo> programaInfoSet = programaInfoRepository.findRoot();
        programaInfoAllSet.addAll( programaInfoSet );
        for(ProgramaInfo programaInfo:programaInfoSet){
            Set<ProgramaInfo> programaInfoSonSet=programaInfoRepository.findSons( programaInfo.getProgramaCode());
            programaInfoAllSet.addAll( programaInfoSonSet );
        }
        return programaInfoAllSet;
    }

    @Override
    public List<TabsData> findUserNameRootAndNext(String CompanyName) {

        Set<ProgramaInfo> programaInfoSet=new LinkedHashSet<>(  );

        List<TabsData> tabsDataList=new ArrayList<>();//初始化最后返回的集合数据

        String username = SecurityUtils.getCurrentUserName();
        List<ProgramaPower> power = iProgramaPower.findPower(username);//取到每个人的权限

        List<String> findGather=new ArrayList<>();

        if(CompanyName.contains("分公司")){
            List<String> list=new ArrayList<>();
            list.add("012");
            list.add("013");
            list.add("021");
            for(String str:list){
                ProgramaInfo  ProgramaInfo = programaInfoRepository.findFilialeRoot(str);//获取012 013栏目
                programaInfoSet.add(ProgramaInfo);
            }


            for(ProgramaInfo programaInfo:programaInfoSet){
                TabsData tabsData=new TabsData();
                tabsData.setOneStair(programaInfo);//放入一级栏目
                //根据一级栏目去获取二级栏目
                Set<ProgramaInfo> programaInfoSonSet=programaInfoRepository.findSons( programaInfo.getProgramaCode());
                tabsData.setTwoStair(programaInfoSonSet);//放入二级栏目
                //循环二级栏目
                List listAll=new ArrayList();
                for(ProgramaInfo threeprogramaInfo:programaInfoSonSet){
                    //根据二级栏目获取三级栏目
                    List<ProgramaInfo> sonByProgramaInfo = programaInfoRepository.findSonByProgramaInfo(threeprogramaInfo.getProgramaCode());
                    if(sonByProgramaInfo.size()>0){
                        listAll.add(sonByProgramaInfo);
                    }

                }
                if(listAll.size()>0){
                    tabsData.setThreeStair(listAll);
                }

                tabsDataList.add(tabsData);

            }
            return tabsDataList;
        }else {
            List<String> retained =new ArrayList<>();
            Set<ProgramaInfo> temporarySet=new LinkedHashSet<>(  );
            /*String programa_code="013";
            findGather.add(programa_code);*/
            for(ProgramaPower programaPower:power){

                if(programaPower.getAloneCode()!=null){
                    retained.add(programaPower.getAloneCode());
                }
                findGather.add(programaPower.getPossessCode());

            }

            Set<ProgramaInfo> programaInfoSet2=programaInfoRepository.findTotalRoot(findGather);


            for(ProgramaInfo programaInfo:programaInfoSet2){

                TabsData tabsData=new TabsData();
                tabsData.setOneStair(programaInfo);//放入一级栏目
                //根据一级栏目去获取二级栏目
                Set<ProgramaInfo> programaInfoSonSet=programaInfoRepository.findSons( programaInfo.getProgramaCode());

                if(retained.size()>0){
                    for(String str:retained){
                        String str2=str.substring(0,3);
                        if(str2.equals(programaInfo.getProgramaCode())){
                            if(programaInfoSonSet.size()>0){
                                for (ProgramaInfo programaInfo1:programaInfoSonSet){
                                    if(str.equals(programaInfo1.getProgramaCode())){
                                        temporarySet.add(programaInfo1);

                                    }
                                }
                                tabsData.setTwoStair(temporarySet);//放入二级栏目
                            }

                        }
                        if(temporarySet.size()==0){
                            tabsData.setTwoStair(programaInfoSonSet);//放入二级栏目

                        }
                    }
                }else {
                    tabsData.setTwoStair(programaInfoSonSet);//放入二级栏目
                }

                    //tabsData.setTwoStair(programaInfoSonSet);//放入二级栏目


                //循环二级栏目
                List listAll=new ArrayList();
                for(ProgramaInfo threeprogramaInfo:programaInfoSonSet){
                    //根据二级栏目获取三级栏目
                    List<ProgramaInfo> sonByProgramaInfo = programaInfoRepository.findSonByProgramaInfo(threeprogramaInfo.getProgramaCode());
                    if(sonByProgramaInfo.size()>0){
                        listAll.add(sonByProgramaInfo);
                    }
                }
                if(listAll.size()>0){
                    tabsData.setThreeStair(listAll);
                }

                tabsDataList.add(tabsData);

            }
            return tabsDataList;

        }

    }

    /**
     * 获取栏目的下一级栏目
     * @param parentProgramaCode
     * @return
     */
    @Override
    public JsonResponse findSonByParentCode ( String parentProgramaCode ) {
        Set<ProgramaInfo> programaInfoSonSet=programaInfoRepository.findSons( parentProgramaCode);

        for (ProgramaInfo programaInfo:programaInfoSonSet) {
            String fileId = programaInfo.getProgramaCoverId();//获取封面文件id
            List<SysFile> sysFileList = new ArrayList<>(  );
            if( !StringUtils.isEmpty( fileId )){
                    SysFile sysfile = sysFileService.findById( fileId );
                    sysFileList.add( sysfile );
            }
            programaInfo.setProgramaCoverFile(sysFileList);


        }
        return JsonResponse.success( programaInfoSonSet );
    }

    /**
     * 根据页面上的需求通过id查询栏目信息
     * @param ProgramaCode
     * @return
     */
    @Override
    public ProgramaInfo findByFromProCode ( String ProgramaCode) {
        return programaInfoRepository.findFromProgramaCode(ProgramaCode);
    }

    /**
     * 获取栏目的下一级栏目2
     * @param locationId
     * @return
     */
    @Override
    public List<ProgramaInfo> findSonByProgramaInfo(String locationId) {
        return programaInfoRepository.findSonByProgramaInfo(locationId);
    }

    /**
     * 最大顶级栏目或者下一级最大栏目的code为null，则在父栏目code的字符串基础上在后面拼接001，否则取最大的code加1
     * @param maxCode 最大code
     * @param newCode
     * @param parentProgramaCode
     * @return
     */
    private String findNewCode(String maxCode,String newCode,String parentProgramaCode){
        if(parentProgramaCode==null){
            parentProgramaCode = "";
        }
        if(!StringUtils.isEmpty( maxCode )){
            newCode = jointString(Integer.parseInt( maxCode )+1);
        }else{
            newCode = parentProgramaCode+ Constants.ONE;
        }
        return newCode;
    }

    /**
     * 按照位数给数字前面补0，返回String类型
     * @param code
     * @return
     */
    private String jointString(int code){
        String newCode = String.valueOf( code );
        int count = 0 ;
        while(code > 0){
            code = code/10;
            count = count + 1;
        }

        if(count==2){
            int zeroNum = 1 -count ;
            while(zeroNum<0){
                newCode = "0" + newCode;
                zeroNum ++ ;
            }
        }else if(count==5){//二级栏目补位
            int zeroNum = 4 -count ;
            while(zeroNum<0){
                newCode = "0" + newCode;
                zeroNum ++ ;
            }
        }else if(count==8){//三级栏目补位
            int zeroNum = 7 -count ;
            while(zeroNum<0){
                newCode = "0" + newCode;
                zeroNum ++ ;
            }
        }else {
            int zeroNum = 2 -count ;
            while(zeroNum<0){
                newCode = "0" + newCode;
                zeroNum ++ ;
            }
        }

        return newCode;
    }



}
