package com.simbest.boot.hnjjwz.column.familyWind.web;/**
 * Created by KZH on 2019/8/5 17:46.
 */

import com.simbest.boot.base.web.controller.LogicController;
import com.simbest.boot.base.web.response.JsonResponse;
import com.simbest.boot.hnjjwz.column.familyWind.model.Record;
import com.simbest.boot.hnjjwz.column.familyWind.service.IRecordService;
import io.swagger.annotations.Api;
import io.swagger.annotations.ApiOperation;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.web.bind.annotation.*;

import java.util.Map;

/**
 * <AUTHOR>
 * @create 2019-08-05 17:46
 * @desc
 **/
@Api(description = "家风栏目")
@Slf4j
@RestController
@RequestMapping(value = "/action/record")
public class RecordController extends LogicController<Record,String> {

    private IRecordService iRecordService;

    @Autowired
    public RecordController(IRecordService service){
        super(service);
        this.iRecordService=service;
    }

    @ApiOperation(value = "获取人的投票记录", notes = "获取人的投票记录")
    @PostMapping (value = {"/findRecord","/findRecord/sso"})
    public JsonResponse findRecord(@RequestParam(required = false) String username){

        return iRecordService.findRecord(username);

    }
}
