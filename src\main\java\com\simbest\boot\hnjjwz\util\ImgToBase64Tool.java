package com.simbest.boot.hnjjwz.util;

import cn.hutool.core.util.ObjectUtil;
import cn.hutool.core.util.StrUtil;
import com.simbest.boot.sys.service.ISysFileService;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.codec.binary.Base64;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Component;
import org.springframework.util.Assert;
import sun.misc.BASE64Encoder;

import javax.imageio.ImageIO;
import java.awt.image.BufferedImage;
import java.io.*;
import java.util.regex.Matcher;
import java.util.regex.Pattern;


/**
 * @用途: 发送短信
 * @作者：zsf
 * @时间: 2018/12/7
 */
@Slf4j
@Component
public class ImgToBase64Tool {

    static BASE64Encoder encoder = new BASE64Encoder();

    @Autowired
    private ISysFileService sysFileService;
   public  String ImgToBase64(String str) {
       log.info("开始转字符串----"+str);
       Assert.notNull(str , "数据不能为空！");
       Pattern compile = Pattern.compile("data-ke-src=&quot;.*?&quot;");
       Matcher matcher = compile.matcher(str);
       while (matcher.find()) {
          str =  str.replace(matcher.group() , "");
       }
       Pattern compileTwo = Pattern.compile("&lt;img.*?&gt;");
       Matcher matcherTwo = compileTwo.matcher(str);
       while (matcherTwo.find()) {
           String matcherStr = matcherTwo.group();
           boolean ret = true;
           StringBuffer newStr = new StringBuffer("&lt;img src=\"");
           if (matcherStr.indexOf("/download") > -1 ) {
               //String fileId = matcherStr.substring(matcherStr.indexOf("?id") + 1 , matcherStr.indexOf("&quot;"));
               String fileId = null;
               Pattern compileFour = Pattern.compile("id=.*?&quot;");
               Matcher matcherFour = compileFour.matcher(matcherStr);
               if (matcherFour.find()) {
                   fileId = matcherFour.group().replace("id=" , "").replace("&quot;" , "");
               }
               log.info("查询FileId----"+fileId);
               if (StrUtil.isNotEmpty(fileId)) {
                   File realFile = sysFileService.getRealFileById(fileId);
                   log.info("获取File----"+realFile.getAbsolutePath()+"------------"+realFile);
//                   File realFile = new File("C:\\Users\\<USER>\\Pictures\\图库\\2.jpeg");
                   //String base64 = ImgToBase64(realFile);
                   if (ObjectUtil.isNotEmpty(realFile)) {
                       String base64 = getImgStr(realFile);
                       newStr.append(base64).append("\"");
                       Pattern compileThree = Pattern.compile("alt=.*?&gt;");
                       Matcher matcherThree = compileThree.matcher(matcherStr);
                       while (matcherThree.find()) {
                           newStr.append(" ").append(matcherThree.group());
                       }
                   }
               } else {
                   ret = false;
               }
           }else{
               ret = false;
           }
           str = ret ? str.replace(matcherStr ,newStr ) : str;
       }
       return str;
   }

    public  String getImgStr(File file) {
        // 将图片文件转化为字节数组字符串，并对其进行Base64编码处理

        InputStream in = null;
        byte[] data = null;
        // 读取图片字节数组
        try {
            in = new FileInputStream(file);
            data = new byte[in.available()];
            in.read(data);
            in.close();
        } catch (IOException e) {
            e.printStackTrace();
        }
        return  "data:image/jpeg;base64," + Base64.encodeBase64String(data);
    }

  /*  public static void main(String[] args) {
        String str = "&lt;img src=&quot;http://10.92.82.161:8088/sjzxcr/sys/file/download/anonymous?id=F406116078630043648&quot; data-ke-src=&quot;http://10.92.82.161:8088/sjzxcr/sys/file/download/anonymous?id=F406116078630043648&quot; alt=&quot;&quot; width=&quot;550&quot; height=&quot;400&quot; align=&quot;middle&quot;&gt;&lt;br&gt;";
        String base64 = ImgToBase64(str);
        System.out.println(base64);
    }*/
}
