﻿<!DOCTYPE html>
<html xmlns="http://www.w3.org/1999/xhtml"
      xmlns:th="http://www.thymeleaf.org">
<head>
    <title>申请列表</title>
    <meta http-equiv="Content-Type" content="text/html; charset=utf-8"/>
    <meta http-equiv="X-UA-Compatible" content="IE=edge" />
    <meta name="viewport" content="width=device-width, initial-scale=1, shrink-to-fit=no"/>
    <!--
    <link href="http://************:8088/simbestui/fonts/iconfont/iconfont.css?v=svn.revision" th:href="@{http://************:8088/simbestui/fonts/iconfont/iconfont.css?v=svn.revision}" rel="stylesheet"/>
    -->
    <link href="../../fonts/iconfont/iconfont.css?v=svn.revision" th:href="@{/fonts/iconfont/iconfont.css?v=svn.revision}" rel="stylesheet"/>
    <link href="http://************:8088/simbestui/js/themes/default/easyui.css?v=svn.revision" th:href="@{http://************:8088/simbestui/js/themes/default/easyui.css?v=svn.revision}" rel="stylesheet"/>
    <link href="http://************:8088/simbestui/js/themes/icon.css?v=svn.revision" th:href="@{http://************:8088/simbestui/js/themes/icon.css?v=svn.revision}" rel="stylesheet"/>
    <link href="http://************:8088/simbestui/css/public.css?v=svn.revision" th:href="@{http://************:8088/simbestui/css/public.css?v=svn.revision}" rel="stylesheet"/>
    <link href="http://************:8088/simbestui/css/pages.css?v=svn.revision" th:href="@{http://************:8088/simbestui/css/pages.css?v=svn.revision}" rel="stylesheet"/>
    <script src="http://************:8088/simbestui/js/jquery.min.js?v=svn.revision" th:src="@{http://************:8088/simbestui/js/jquery.min.js?v=svn.revision}" type="text/javascript"></script>
    <script src="http://************:8088/simbestui/js/jquery.easyui.min.js?v=svn.revision" th:src="@{http://************:8088/simbestui/js/jquery.easyui.min.js?v=svn.revision}" type="text/javascript"></script>
    <script src="http://************:8088/simbestui/js/easyui-lang-zh_CN.js?v=svn.revision" th:src="@{http://************:8088/simbestui/js/easyui-lang-zh_CN.js?v=svn.revision}" type="text/javascript"></script>
    <script src="../../js/jquery.config.js?v=svn.revision" th:src="@{/js/jquery.config.js?v=svn.revision}" type="text/javascript"></script>
    <script src="http://************:8088/simbestui/js/jquery.zjsfile.js?v=svn.revision" th:src="@{http://************:8088/simbestui/js/jquery.zjsfile.js?v=svn.revision}" type="text/javascript"></script>
    <script src="http://************:8088/simbestui/js/jquery.aduq.js?v=svn.revision" th:src="@{http://************:8088/simbestui/js/jquery.aduq.js?v=svn.revision}" type="text/javascript"></script>
    <script src="http://************:8088/simbestui/js/jquery.process.js?v=svn.revision" th:src="@{http://************:8088/simbestui/js/jquery.process.js?v=svn.revision}" type="text/javascript"></script>
    <script type="text/javascript">
		$(function(){
			//showDialog做为打开对话框的公共class名称   showDialogindex做为打开修改对话框传行的索引   delete做为删除按钮配置命令   deleteid做为删除传值  searchtable做为条件查询的按钮公共class名称
			var pageparam={
				"listtable":{
					"listname":"#applyTable",//table列表的id名称，需加#
					"querycmd":"action/myProcess/queryMyApply",//table列表的查询命令
					"contentType":"application/json; charset=utf-8",//table列表的传参方式，如果是json就写，不写默认kv
					"nowrap": true,//把数据显示在一行里,默认true
                    "styleClass": "noScroll",
					"frozenColumns":[],//固定在左侧的列
					"columns":[[//列   
						{ title: "流程名称", field: "headerDesc", width: 200},
						{ title: "任务标题", field: "title", width: 200},
						{ title: "提交部门", field: "orgName", width: 200},
						{ title: "提交人", field: "createUserName", width: 100 },
						{ title: "提交时间", field: "createDate", width: 150 },//排序sortable: true
						{ title: "当前状态", field: "stepDesc", width: 100},
						{ title: "当前待处理人", field: "subjectsDesc", width: 150},
						{
							field: "opt", title: "操作", width: 160, rowspan: 1,//align：对齐此列的数据，可以用left、right、center
							formatter: function (value, row,index) {//单元格的格式化函数，需要三个参数：value：字段的值；rowData：行的记录数据；rowIndex：行的索引
								var g = "<a title=\"查看\" index=\""+index+"\" path=\"read\">【查看】</a>"
										+"<a title=\"撤回\" index=\""+index+"\" path=\"delete\">【撤回】</a>"
										+"<a title=\"编辑\" index=\""+index+"\" path=\"edit\">【编辑】</a>";
								return g;
							}
						}
					] ]
				}
			};
			loadGrid(pageparam);
			$(document).on("click","a[path]",function(){
				var $t=$(this);
				var index=$t.attr("index");
				$("#applyTable").datagrid("selectRow", index);//选择一行，行索引从0开始
				var row = $("#applyTable").datagrid("getSelected");//返回第一个被选中的行或如果没有选中的行则返回null
				var action=$t.attr("path");
				web.ajaxgeneral({
					url:"action/process/handleProcess/" + action + "/" + row.typeId + "/" + row.headerId + "/" + row.stepId + "/" + row.receiptId,
					success:function(data){//成功
						if(action=="delete"){
							$.messager.confirm("撤回","确定撤回吗？",function(r){
								if(r){
									web.ajaxgeneral({
										url:getvals("deleteprocess",row.headerId),
										data:{ "id": row.receiptId },
										success:function(data){//成功
											$("#applyTable").datagrid("reload").datagrid("unselectAll");//刷新table
										}
									});
								}
							});
						}else{//审批跳转在这里写
							var urlparas="&typeId="+row.typeId+"&headerId="+row.headerId+"&receiptId="+row.receiptId+"&stepId="+row.stepId+"&stepCode="+row.stepCode;
							switch (row.headerId) {//headerId用来判断是要素流程还是情报流程
								case 1://1表示系统日志审计报告                                  
									window.location.href = web.rootdir + "/html/" + getvals("headerurl", row.headerId) + ".html?web=task&action=" + action + "&id=" + row.receiptId + urlparas;//需要判断大类型 跳转的地址                                    
									break;
							}
						}
					}
				});
			});
		});
    </script>
</head>
<body class="page_body">
<!--searchform-->
<form id="applyTableQueryForm"> 
    <table border="0" cellpadding="0" cellspacing="18" width="100%">
        <tr>
            <td width="90" align="right">流程名称：</td><td width="150"><input name="headerDesc" type="text" value="" /></td>
            <td width="90" align="right">任务标题：</td><td width="150"><input name="title" type="text" value="" /></td>
            <td></td>
        </tr>
		<tr>
            <td width="90" align="right">当前状态：</td><td width="150"><input name="stepDesc" type="text" value="" /></td>
            <td width="90" align="right">提交人：</td><td width="150"><input name="createUserName" type="text" value="" /></td>
            <td>
                <div class="button w100">
                    <a class="fl searchtable"><span>查询</span></a>  
                </div>
                </td>
        </tr>
    </table>
</form>
<!--table-->
<div class="plr18"><table id="applyTable"></table></div>
</body>
</html>
