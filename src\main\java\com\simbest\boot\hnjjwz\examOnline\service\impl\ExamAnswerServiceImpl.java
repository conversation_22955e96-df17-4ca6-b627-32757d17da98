package com.simbest.boot.hnjjwz.examOnline.service.impl;

import com.simbest.boot.base.service.impl.LogicService;
import com.simbest.boot.base.web.response.JsonResponse;
import com.simbest.boot.hnjjwz.examOnline.model.ExamAnswer;
import com.simbest.boot.hnjjwz.examOnline.repository.ExamAnswerRepository;
import com.simbest.boot.hnjjwz.examOnline.service.IExamAnswerService;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.data.domain.Pageable;
import org.springframework.stereotype.Service;

import java.util.List;

/**
 * <AUTHOR>
 * @Data 2019/05/09
 * @Description
 */
@Slf4j
@Service
public class ExamAnswerServiceImpl extends LogicService<ExamAnswer,String> implements IExamAnswerService {

    private ExamAnswerRepository examAnswerRepository;

    @Autowired
    public ExamAnswerServiceImpl ( ExamAnswerRepository examAnswerRepository) {
        super(examAnswerRepository);
        this.examAnswerRepository = examAnswerRepository;
    }

    @Override
    public List<ExamAnswer> customFindExamAnswer(String questionCode) {
        return examAnswerRepository.customFindExamAnswer(questionCode);
    }

    @Override
    public JsonResponse customFindExamAnswer(String questionCode, Pageable pageable) {
        return JsonResponse.success(examAnswerRepository.customFindExamAnswer(questionCode,pageable));
    }

    @Override
    public int deleteExamAnswer(String questionCode) {
        return examAnswerRepository.deleteExamAnswer(questionCode);
    }
}
