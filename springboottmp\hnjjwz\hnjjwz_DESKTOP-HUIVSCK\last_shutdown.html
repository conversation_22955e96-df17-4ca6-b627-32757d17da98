<!DOCTYPE html PUBLIC "-//W3C//DTD XHTML 1.0 Transitional//EN" "http://www.w3.org/TR/xhtml1/DTD/xhtml1-transitional.dtd">
<html lang='zh'><head><title>监控系统在 /hnjjwz_DESKTOP-HUIVSCK</title>
<style type='text/css'>
/* style sheet */
body {
	background-color: white;
	background: url(?resource=backgroundGradient.png) bottom left fixed
		repeat-x;
	font-family: Arial, Helvetica, sans-serif;
	font-size: 15px;
}

img {
	border: 0px;
	vertical-align: middle;
}

a {
	text-decoration: none;
	color: #0000BB;
}

a:hover, a:visited:hover {
	text-decoration: underline;
	color: #0000BB;
}

a:visited {
	color: #6600BB;
}

a img {
	transition: all 0.3s ease;
}

a:hover img {
	transform: scale(1.25);
	-webkit-filter: drop-shadow(2px 2px 1px grey) /* Safari and Chrome */
}

.chapterTitle img {
	width: 24px;
	height: 24px;
}

.synthese {
	box-shadow: 2px 2px 3px #888;
	transition: all 0.3s ease;
}

.synthese:hover {
	transform: scale(1.05);
	-webkit-filter: drop-shadow(2px 2px 1px grey) /* Safari and Chrome */
}

table {
	border-color: gray;
	border-spacing: 0;
	border-radius: 5px;
}

th {
	font-size: 11px;
	padding: 2px;
	background-color: #F0F0F0;
	cursor: pointer;
}

table tr.odd td {
	background-color: #F5F5F5;
}

table tr.highlight td {
	background-color: #E0E0E0;
}

td {
	font-size: 11px;
	padding: 2px;
}

table tr:last-child td:first-child {
	border-bottom-left-radius: 5px
}

table tr:last-child td:last-child {
	border-bottom-right-radius: 5px
}

table thead th:first-child {
	border-top-left-radius: 5px
}

table thead th:last-child {
	border-top-right-radius: 5px
}

.wrappedText {
	/* wrap very long words in requests and errors cells */
	word-wrap: break-word;
	word-break: break-all;
}

.tooltip {
	/* pour que <div class=tooltip> soit comme <a> */
	color: #0000BB;
}

.tooltip em {
	display: none;
}

.tooltip:hover {
	border: 0;
	position: relative;
	/* marche pas avec texte long qui se wrappe: z-index: 500; */
	text-decoration: none;
	overflow: visible; /* for RUM tooltips */
}

.tooltip:hover em {
	font-style: normal;
	display: block;
	position: absolute;
	top: 12px;
	left: 20px;
	/* une width fixe est n�cessaire pour MSIE 7,
	mais cela g�cherait tout sur firefox, chrome ou MSIE8 qui n'en ont pas besoin
	width: 800px; */
	border-radius: 6px;
	padding: 5px;
	color: #000;
	border: 1px solid #bbb;
	background: #ffc;
	box-shadow: 3px 3px 4px #888;
	text-align: left;
}

div#track, div#handle {
	height: 18px;
}

div#track {
	width: 200px;
	margin-left: 300px;
	background: url( '?resource=scaler_slider_track.gif' ) center left
		repeat-x;
}

div#handle {
	width: 18px;
	cursor: col-resize;
}

.sqlKeyword {
	color: #880000;
	font-weight: bold;
	font-size: 12px;
}

.explainPlan {
	font-size: 8pt;
	font-family: monospace;
}

.rumData {
	width: 100%;
	height: 20px;
	table-layout: fixed; /* works with overflow: hidden; white-space: nowrap; below */
}

.rumDataNetwork {
	border-top-left-radius: 5px;
	border-bottom-left-radius: 5px;
}

.rumDataPageRendering {
	border-top-right-radius: 5px;
	border-bottom-right-radius: 5px;
}

.rumDataNetwork, .rumDataServer, .rumDataDomProcessing, .rumDataPageRendering {
	text-align: center;
	font-size: 11px;
	color: white;
	box-shadow: 0 1px 2px rgba(0, 0, 0, 0.1) inset;
	overflow: hidden;
	white-space: nowrap;
	padding: 0px;
}

.rumDataNetwork {
	/* green gradient */
	background-image: linear-gradient(to bottom, #5cb85c 0px, #449d44 100%);
}

.rumDataServer {
	/* red gradient */
	background-image: linear-gradient(to bottom, #d9534f 0px, #c9302c 100%);
}

.rumDataDomProcessing {
	/* orange gradient */
	background-image: linear-gradient(to bottom, #f0ad4e 0px, #ec971f 100%);	
}

.rumDataPageRendering {
	/* blue gradient */
	background-image: linear-gradient(to bottom, #5bc0de 0px, #31b0d5 100%);
}

.info {
	color: green;
}

.warning {
	color: orange;
	font-weight: bold;
}

.severe {
	color: red;
	font-weight: bold;
}

form {
	background-color: #F5F5F5;
	font-size: 11px;
	border-radius: 2px;
	box-shadow: 0px 3px 3px rgba(0, 0, 0, 0.3);
}

input[type=text] {
	border: 1px solid #b9b9b9;
	border-top: 1px solid #a0a0a0;
	border-radius: 1px;
	transition: all 0.3s ease;
}

input[type=text]:hover {
	border: 1px solid #999999;
	border-top: 1px solid #808080;
	box-shadow: inset 0 1px 2px rgba(0, 0, 0, 0.1);
}

input[type=text]:focus {
	outline: none;
	border: 1px solid #4d90fe;
	box-shadow: inset 0 1px 2px rgba(0, 0, 0, 0.3);
}

input[type=submit]:active {
	border: medium none transparent;
	background-image: linear-gradient(rgba(0, 0, 0, 0.1),
		rgba(0, 0, 0, 0.15));
	box-shadow: 0px 0px 0px 1px rgba(0, 0, 0, 0.25) inset, 0px 2px 4px
		rgba(0, 0, 0, 0.3) inset;
}

input[type=submit]:hover {
	background-image: linear-gradient(transparent, rgba(0, 0, 0, 0.05) 40%,
		rgba(0, 0, 0, 0.15));
}

input[type=submit] {
	display: inline-block;
	font-size: 100%;
	padding: 0.4em 1em 0.45em;
	line-height: normal;
	white-space: nowrap;
	vertical-align: baseline;
	text-align: center;
	cursor: pointer;
	color: rgba(0, 0, 0, 0.8);
	border: medium none transparent;
	background-color: #E6E6E6;
	background-image: linear-gradient(rgba(255, 255, 255, 0.3),
		rgba(255, 255, 255, 0.15) 40%, transparent);
	text-decoration: none;
	border-radius: 4px;
	box-shadow: 0px 0px 0px 1px rgba(0, 0, 0, 0.25) inset, 0px 2px 0px
		rgba(255, 255, 255, 0.3) inset, 0px 1px 2px rgba(0, 0, 0, 0.15);
	transition: box-shadow 0.1s linear 0s;
}

input[type=checkbox] {
	vertical-align: bottom;
	margin: 0px;
}

#menuBox {
	width: 268px;
	font-size: 12px;
	line-height: 20px;
	right: -240px;
	top: 57px; /* change this value to place the menu higher or lower */
	position: fixed;
	z-index: 100;
}

#menuTab {
	float: left;
	list-style: none outside none;
	margin: 0;
	padding: 0;
	position: relative;
	z-index: 99;
}

#menuTab li span {
	display: block;
	padding: 0 5px;
	position: relative;
}

#menuLinks {
	width: 180px;
	padding: 2px;
	/*border-left: 1px solid #B4B8BB;*/
	float: left;
}

.menuShow, .menuHide {
	/* we specify the transition length for hiding and showing */
	transition: margin-right .4s ease-in;
}

.menuHide {
	margin-right: 0px;
}

.menuShow {
	margin-right: 195px;
}

#menuToggle, .menuButton {
	cursor: pointer;
}

#menuToggle {
	width: 30px;
	height: 26px;
}

.menuButton {
	width: 190px;
	height: 25px;
	margin: -1px;
	text-align: center;
	border: 1px solid #8A8D92;
	font: bold 13px Helvetica, Arial, sans-serif;
	text-shadow: 0px 0px 5px rgba(0, 0, 0, 0.75);
	background: #1D4151;
	background-image: -o-linear-gradient(left, #A1A1A1, #6C6C6C);
	background-image: -ms-linear-gradient(left, #A1A1A1, #6C6C6C);
	background-image: -moz-linear-gradient(left, #A1A1A1, #6C6C6C);
	background-image: -webkit-linear-gradient(left, #A1A1A1, #6C6C6C);
	background-image: -webkit-gradient(linear, left top, right top, from(#A1A1A1),
		to(#6C6C6C));
}

.menuButton a {
	line-height: 25px;
	color: #E5E5E5;
	display: block;
	text-decoration: none;
}

.menuButton:hover {
	background: #6C6D76;
	transition: background .3s linear;
}

#menuDeco {
	width: 188px;
	float: left;
	box-shadow: 0px 0px 5px #000;
}

@media print {
	.noPrint {
		display: none;
	}
}

/* JavaHTMLizer */
code {
	font-size: 12px;
}

code .string {
	color: blue;
}

code .comment {
	font-style: italic;
	color: green;
}

code .keyword {
	font-weight: bold;
	color: purple;
}

code .comment .keyword {
	color: green;
	font-weight: normal;
}

code .comment .string {
	color: green;
}

/* lightwindow, http://www.p51labs.com/lightwindow/ */
#lightwindow_overlay {
	/* REQUIRED */
	display: none;
	visibility: hidden;
	position: absolute;
	top: 0;
	left: 0;
	width: 100%;
	height: 100px;
	z-index: 500;
	/* REQUIRED */
}

#lightwindow {
	/* REQUIRED */
	/* Of Note - The height and width of this element are set to 0px */
	display: none;
	visibility: hidden;
	position: absolute;
	z-index: 999;
	line-height: 0px;
	/* REQUIRED */
}

	#lightwindow_container {
		/* REQUIRED */
		display: none;
		visibility: hidden;
		position: absolute;
		/* REQUIRED */
		padding: 0 0 0 0;
		margin: 0 0 0 0;
	}

	/* IE6 needs this or it messes with our positioning */
	* html #lightwindow_container {
		overflow: hidden;
	}

	#lightwindow_contents {
		overflow: hidden;
		z-index: 0;
		position: relative;
		border: 10px solid #ffffff;
		background-color: #ffffff;
	}

#lightwindow_loading {
	/* REQUIRED */
	height: 100%;
	width: 100%;
	top: 0px;
	left: 0px;
	z-index: 9999;
	position: absolute;
	/* REQUIRED */
	background-color: #f0f0f0;
	padding: 10px;
}

	#lightwindow_loading_shim {
		display: none;
		left: 0px; 
		position: absolute; 
		top: 0px;
		width: 100%; 
		height: 100%;
	}

	#lightwindow_loading span {
		font-size: 12px;
		line-height: 32px;
		color: #444444;
		float: left;
		padding: 0 10px 0 0;
	}

	#lightwindow_loading span a,
	#lightwindow_loading span a:link,
	#lightwindow_loading span a:visited {
		color: #09F;
		text-decoration: none;
		cursor: pointer;
	}

	#lightwindow_loading span a:hover,
	#lightwindow_loading span a:active {
		text-decoration: underline;
	}

	#lightwindow_loading img {
		float: left;
		margin: 0 10px 0 0;
	}
	
#lightwindow_title_bar {
	height: 25px;
	overflow: hidden;
}

	#lightwindow_title_bar_title {
		color: lightgray;
		font-size: 14px;
		font-weight: bold;
		line-height: 25px;
		text-align: left;
		float: left;
	}

	a#lightwindow_title_bar_close_link,	
	a:link#lightwindow_title_bar_close_link,
	a:visited#lightwindow_title_bar_close_link {
		float: right;
		text-align: right;
		cursor: pointer;
		color: lightgray;
		line-height: 25px;
		padding: 0;
		margin: 0;
	}

	a:hover#lightwindow_title_bar_close_link,
	a:active#lightwindow_title_bar_close_link {
		color: #ffffff;
		text-decoration: none;
	}

#lightwindow p {
	color: #000000;
	padding-right: 10px;
}

/*!
 * "Fork me on GitHub" CSS ribbon v0.2.0 | MIT License
 * https://github.com/simonwhitaker/github-fork-ribbon-css
*/

.github-fork-ribbon {
  width: 12.1em;
  height: 12.1em;
  position: absolute;
  overflow: hidden;
  top: 0;
  right: 0;
  z-index: 9999;
  pointer-events: none;
  font-size: 13px;
  text-decoration: none;
  text-indent: -999999px;
}

.github-fork-ribbon.fixed {
  position: fixed;
}

.github-fork-ribbon:before, .github-fork-ribbon:after {
  /* The right and left classes determine the side we attach our banner to */
  position: absolute;
  display: block;
  width: 15.38em;
  height: 1.54em;
  
  top: 3.23em;
  right: -3.23em;
  
  box-sizing: content-box;

  transform: rotate(45deg);
}

.github-fork-ribbon:before {
  content: "";

  /* Add a bit of padding to give some substance outside the "stitching" */
  padding: .38em 0;

  /* Set the base colour */
  background-color: #a00;

  /* Set a gradient: transparent black at the top to almost-transparent black at the bottom */
  background-image: linear-gradient(to bottom, rgba(0, 0, 0, 0), rgba(0, 0, 0, 0.15));

  /* Add a drop shadow */
  box-shadow: 0 .15em .23em 0 rgba(0, 0, 0, 0.5);

  pointer-events: auto;
}

.github-fork-ribbon:after {
  /* Set the text from the title attribute */
  content: attr(title);

  /* Set the text properties */
  color: #fff;
  font: 700 1em "Helvetica Neue", Helvetica, Arial, sans-serif;
  line-height: 1.54em;
  text-decoration: none;
  text-shadow: 0 -.08em rgba(0, 0, 0, 0.5);
  text-align: center;
  text-indent: 0;

  /* Set the layout properties */
  padding: .15em 0;
  margin: .15em 0;

  /* Add "stitching" effect */
  border-width: .08em 0;
  border-style: dotted;
  border-color: #fff;
  border-color: rgba(255, 255, 255, 0.7);
}

.github-fork-ribbon.left-top, .github-fork-ribbon.left-bottom {
  right: auto;
  left: 0;
}

.github-fork-ribbon.left-bottom, .github-fork-ribbon.right-bottom {
  top: auto;
  bottom: 0;
}

.github-fork-ribbon.left-top:before, .github-fork-ribbon.left-top:after, .github-fork-ribbon.left-bottom:before, .github-fork-ribbon.left-bottom:after {
  right: auto;
  left: -3.23em;
}

.github-fork-ribbon.left-bottom:before, .github-fork-ribbon.left-bottom:after, .github-fork-ribbon.right-bottom:before, .github-fork-ribbon.right-bottom:after {
  top: auto;
  bottom: 3.23em;
}

.github-fork-ribbon.left-top:before, .github-fork-ribbon.left-top:after, .github-fork-ribbon.right-bottom:before, .github-fork-ribbon.right-bottom:after {
  transform: rotate(-45deg);
}
</style>
<link type='image/png' rel='shortcut icon' href='?resource=systemmonitor.png' />
<script type='text/javascript' src='?resource=resizable_tables.js'></script>
<script type='text/javascript' src='?resource=sorttable.js'></script>
<script type='text/javascript' src='?resource=prototype.js'></script>
<script type='text/javascript' src='?resource=effects.js'></script>
<script type='text/javascript' src='?resource=lightwindow.js'></script>
<script type='text/javascript' src='?resource=customizableMonitoring.js'></script>
<script type='text/javascript'>
function showHide(id){
  if (document.getElementById(id).style.display=='none') {
    if (document.getElementById(id + 'Img') != null) {
      document.getElementById(id + 'Img').src='?resource=bullets/minus.png';
    }
    try {
      Effect.SlideDown(id, { duration: 0.5 });
    } catch (e) {
      document.getElementById(id).style.display='inline';
    }
  } else {
    if (document.getElementById(id + 'Img') != null) {
      document.getElementById(id + 'Img').src='?resource=bullets/plus.png';
    }
    try {
      Effect.SlideUp(id, { duration: 0.5 });
    } catch (e) {
      document.getElementById(id).style.display='none';
    }
  }
}
</script>
</head><body>
<h3 class='chapterTitle'><img src='?resource=systemmonitor.png' alt='状态'/>
<a name='top'></a>图表 <a href='https://github.com/javamelody/javamelody/wiki' target='_blank'>JavaMelody</a> 监控从 25-6-4 下午2:16 到/hnjjwz_DESKTOP-HUIVSCK (application)
</h3>
<a href='https://github.com/javamelody/javamelody/wiki/Donate'><img class='noPrint' style='position: absolute; top: 15px; right: 10px; border: 0;' src='?resource=donate.gif' alt='Donate' /></a>
<div align='center'>
<div class='noPrint'>
<a href='?' title='刷新当前页'><img src='?resource=action_refresh.png' alt='更新'/> 更新</a>&nbsp;&nbsp;&nbsp;&nbsp;
<a href='?resource=help/help.html' target='_blank' title="在新打开的一个页面中显示"><img src='?resource=action_help.png' alt='在线帮助'/> 在线帮助</a>&nbsp;&nbsp;&nbsp;&nbsp;
<a href='?part=jnlp' title="Monitoring with a Rich Desktop Application"><img src='?resource=systemmonitor.png' width='16' height='16' alt='Monitoring with a Rich Desktop Application'/> Desktop</a>&nbsp;&nbsp;&nbsp;&nbsp;
选着一个时期 :&nbsp;
<a href='?period=jour' title='为图表和请求选着时间 天 '><img src='?resource=calendar_view_day.png' alt='天' /> 天</a>&nbsp;
<a href='?period=semaine' title='为图表和请求选着时间 周 '><img src='?resource=calendar_view_week.png' alt='周' /> 周</a>&nbsp;
<a href='?period=mois' title='为图表和请求选着时间 月 '><img src='?resource=calendar_view_month.png' alt='月' /> 月</a>&nbsp;
<a href='?period=annee' title='为图表和请求选着时间 年 '><img src='?resource=calendar.png' alt='年' /> 年</a>&nbsp;
<a href='?period=tout' title='为图表和请求选着时间 All '><img src='?resource=calendar.png' alt='All' /> All</a>&nbsp;
<a href="javascript:showHide('customPeriod');document.customPeriodForm.startDate.focus();" 
title='为图表和请求选着时间 自定义 '>
<img src='?resource=calendar.png' alt='自定义' /> 自定义</a>
<div id='customPeriod' style='display: none;'>
<br/>
<form name='customPeriodForm' method='get' action='' onsubmit='return validateCustomPeriodForm();'>
<br/><b><label for='customPeriodStartDate'>从</label></b>&nbsp;&nbsp;
<input type='date' id='customPeriodStartDate' name='startDate' size='10' required max='2025-06-04' 
/>&nbsp;&nbsp;<b><label for='customPeriodEndDate'>到</label></b>&nbsp;&nbsp;
<input type='date' id='customPeriodEndDate' name='endDate' size='10' required max='2025-06-04' 
/>&nbsp;&nbsp;
<span id='customPeriodPattern' style='display: none;'>(yy-m-d)</span>&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;<input type='submit' value='OK'/><br/><br/>
<input type='hidden' name='period' value=''/>
<input type='hidden' name='pattern' value='yyyy-MM-dd'/>
<script type='text/javascript'>
var test = document.createElement('input'); test.type = 'date';
if(test.type === 'text') {
  document.customPeriodForm.pattern.value = '';
  document.getElementById('customPeriodPattern').style.display='inline';
}
function validateCustomPeriodForm() {
   periodForm = document.customPeriodForm;
   if (periodForm.startDate.value.length == 0) {
      alert('托管日期');
      periodForm.startDate.focus();
      return false;
   }
   if (periodForm.endDate.value.length == 0) {
      alert('托管日期');
      periodForm.endDate.focus();
      return false;
   }
   periodForm.period.value=periodForm.startDate.value + '|' + periodForm.endDate.value;
   return true;
}
</script>
</form><br/>
</div>
</div>
<a href='?part=graph&amp;graph=usedMemory'><img class='synthese' src='?width=200&amp;height=50&amp;graph=usedMemory' alt="被用的内存" title="被用的内存"/></a>
<a href='?part=graph&amp;graph=cpu'><img class='synthese' src='?width=200&amp;height=50&amp;graph=cpu' alt="% CPU" title="% CPU"/></a>
<a href='?part=graph&amp;graph=httpSessions'><img class='synthese' src='?width=200&amp;height=50&amp;graph=httpSessions' alt="Http sessions" title="Http sessions"/></a>
<br/>
<a href='?part=graph&amp;graph=activeThreads'><img class='synthese' src='?width=200&amp;height=50&amp;graph=activeThreads' alt="活动的线程" title="活动的线程"/></a>
<a href='?part=graph&amp;graph=activeConnections'><img class='synthese' src='?width=200&amp;height=50&amp;graph=activeConnections' alt="活跃的jdbc连接数" title="活跃的jdbc连接数"/></a>
<a href='?part=graph&amp;graph=usedConnections'><img class='synthese' src='?width=200&amp;height=50&amp;graph=usedConnections' alt="被使用的jdbc连接数" title="被使用的jdbc连接数"/></a>
<br/>
<a href='?part=graph&amp;graph=httpHitsRate'><img class='synthese' src='?width=200&amp;height=50&amp;graph=httpHitsRate' alt="http 每分钟hits" title="http 每分钟hits"/></a>
<a href='?part=graph&amp;graph=httpMeanTimes'><img class='synthese' src='?width=200&amp;height=50&amp;graph=httpMeanTimes' alt="Http 平均时间 (ms)" title="Http 平均时间 (ms)"/></a>
<a href='?part=graph&amp;graph=httpSystemErrors'><img class='synthese' src='?width=200&amp;height=50&amp;graph=httpSystemErrors' alt="% http错误" title="% http错误"/></a>
<br/>
<a href='?part=graph&amp;graph=sqlHitsRate'><img class='synthese' src='?width=200&amp;height=50&amp;graph=sqlHitsRate' alt="Sql 使用每分钟" title="Sql 使用每分钟"/></a>
<a href='?part=graph&amp;graph=sqlMeanTimes'><img class='synthese' src='?width=200&amp;height=50&amp;graph=sqlMeanTimes' alt="Sql平均时间 (ms)" title="Sql平均时间 (ms)"/></a>
<a href='?part=graph&amp;graph=sqlSystemErrors'><img class='synthese' src='?width=200&amp;height=50&amp;graph=sqlSystemErrors' alt="% sql 错误" title="% sql 错误"/></a>
<br/>
<a href='?part=graph&amp;graph=springHitsRate'><img class='synthese' src='?width=200&amp;height=50&amp;graph=springHitsRate' alt="Spring 使用每分钟" title="Spring 使用每分钟"/></a>
<a href='?part=graph&amp;graph=springMeanTimes'><img class='synthese' src='?width=200&amp;height=50&amp;graph=springMeanTimes' alt="Spring 平均时间 (ms)" title="Spring 平均时间 (ms)"/></a>
<a href='?part=graph&amp;graph=springSystemErrors'><img class='synthese' src='?width=200&amp;height=50&amp;graph=springSystemErrors' alt="% spring 错误" title="% spring 错误"/></a>
<br/>
<div align='right'>
<a href="javascript:showHide('detailsGraphs');" class='noPrint'><img id='detailsGraphsImg' src='?resource=bullets/plus.png' alt=''/> 其他的图</a>
</div>
<div id='detailsGraphs' style='display: none;'><div>
<a href='?part=graph&amp;graph=gc'><img class='synthese' src='?width=200&amp;height=50&amp;graph=gc' alt="% 垃圾回收时间" title="% 垃圾回收时间"/></a>
<a href='?part=graph&amp;graph=threadCount'><img class='synthese' src='?width=200&amp;height=50&amp;graph=threadCount' alt="现场统计" title="现场统计"/></a>
<a href='?part=graph&amp;graph=loadedClassesCount'><img class='synthese' src='?width=200&amp;height=50&amp;graph=loadedClassesCount' alt="加载的类的数目" title="加载的类的数目"/></a>
<br/>
<a href='?part=graph&amp;graph=usedBufferedMemory'><img class='synthese' src='?width=200&amp;height=50&amp;graph=usedBufferedMemory' alt="Used buffered memory" title="Used buffered memory"/></a>
<a href='?part=graph&amp;graph=usedNonHeapMemory'><img class='synthese' src='?width=200&amp;height=50&amp;graph=usedNonHeapMemory' alt="被用的堆得内存" title="被用的堆得内存"/></a>
<a href='?part=graph&amp;graph=usedPhysicalMemorySize'><img class='synthese' src='?width=200&amp;height=50&amp;graph=usedPhysicalMemorySize' alt="被用的物理内存" title="被用的物理内存"/></a>
<br/>
<a href='?part=graph&amp;graph=usedSwapSpaceSize'><img class='synthese' src='?width=200&amp;height=50&amp;graph=usedSwapSpaceSize' alt="被用的交换的空间" title="被用的交换的空间"/></a>
<a href='?part=graph&amp;graph=systemCpuLoad'><img class='synthese' src='?width=200&amp;height=50&amp;graph=systemCpuLoad' alt="% System CPU" title="% System CPU"/></a>
<a href='?part=graph&amp;graph=httpSessionsMeanAge'><img class='synthese' src='?width=200&amp;height=50&amp;graph=httpSessionsMeanAge' alt="平均http时间 (min)" title="平均http时间 (min)"/></a>
<br/>
<a href='?part=graph&amp;graph=transactionsRate'><img class='synthese' src='?width=200&amp;height=50&amp;graph=transactionsRate' alt="Transactions per minute" title="Transactions per minute"/></a>
<a href='?part=graph&amp;graph=Free_disk_space'><img class='synthese' src='?width=200&amp;height=50&amp;graph=Free_disk_space' alt="剩余的硬盘空间" title="剩余的硬盘空间"/></a>
</div></div>
</div>
<h3 class='chapterTitle'><img src='?resource=dbweb.png' alt='http'/>
<a name='http'></a>图表http - 1 天</h3>
<table class='sortable' width='100%' border='1' summary='http global'>
<thead><tr><th>请求</th><th class='sorttable_numeric'>% 总得积累时间</th><th class='sorttable_numeric'>点击</th><th class='sorttable_numeric'>平均时间 (ms)</th><th class='sorttable_numeric'>最大时间 (ms)</th><th class='sorttable_numeric'>标准偏差</th><th class='sorttable_numeric'>% 积累cpu时间</th><th class='sorttable_numeric'>平均CPU使用时间 (ms)</th><th class='sorttable_numeric'>Mean allocated Kb</th><th class='sorttable_numeric'>% 系统错误</th><th class='sorttable_numeric'>平均大小(Kb)</th><th class='sorttable_numeric'>平均 hits sql</th><th class='sorttable_numeric'>平均时间 sql (ms)</th></tr></thead><tbody>
<tr onmouseover="this.className='highlight'" onmouseout="this.className=''">
<td class='wrappedText'>http global</td> <td align='right'>100</td> <td align='right'>49</td> <td align='right'><span class='info'>115</span></td> <td align='right'>459</td> <td align='right'>114</td> <td align='right'>100</td> <td align='right'><span class='info'>37</span></td> <td align='right'>4,949</td> <td align='right'>0.00</td> <td align='right'>82</td> <td align='right'>0</td> <td align='right'>2</td></tr><tr class='odd' onmouseover="this.className='highlight'" onmouseout="this.className='odd'">
<td class='wrappedText'>http warning</td> <td align='right'>9</td> <td align='right'>2</td> <td align='right'><span class='warning'>255</span></td> <td align='right'>388</td> <td align='right'>187</td> <td align='right'>13</td> <td align='right'><span class='info'>124</span></td> <td align='right'>19,076</td> <td align='right'>0.00</td> <td align='right'>11</td> <td align='right'>2</td> <td align='right'>25</td></tr><tr onmouseover="this.className='highlight'" onmouseout="this.className=''">
<td class='wrappedText'>http severe</td> <td align='right'>27</td> <td align='right'>4</td> <td align='right'><span class='severe'>393</span></td> <td align='right'>459</td> <td align='right'>47</td> <td align='right'>35</td> <td align='right'><span class='info'>163</span></td> <td align='right'>18,118</td> <td align='right'>0.00</td> <td align='right'>2</td> <td align='right'>0</td> <td align='right'>0</td></tr></tbody></table>
<div align='right'>
5 使用每分钟在 13 请求
&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;
<a href="javascript:showHide('detailshttp');" class='noPrint'><img id='detailshttpImg' src='?resource=bullets/plus.png' alt=''/> 描述</a>
&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;
</div>
<div id='detailshttp' style='display: none;'>
<table class='sortable' width='100%' border='1' summary='http'>
<thead><tr><th>请求</th><th class='sorttable_numeric'>% 总得积累时间</th><th class='sorttable_numeric'>点击</th><th class='sorttable_numeric'>平均时间 (ms)</th><th class='sorttable_numeric'>最大时间 (ms)</th><th class='sorttable_numeric'>标准偏差</th><th class='sorttable_numeric'>% 积累cpu时间</th><th class='sorttable_numeric'>平均CPU使用时间 (ms)</th><th class='sorttable_numeric'>Mean allocated Kb</th><th class='sorttable_numeric'>% 系统错误</th><th class='sorttable_numeric'>平均大小(Kb)</th><th class='sorttable_numeric'>平均 hits sql</th><th class='sorttable_numeric'>平均时间 sql (ms)</th></tr></thead><tbody>
<tr onmouseover="this.className='highlight'" onmouseout="this.className=''">
<td class='wrappedText'><a class='tooltip' href='?part=graph&amp;graph=http8464259cdda1ef17693397388f0cf73b1cfee06b' onmouseover="document.getElementById('id1').src='?graph=http8464259cdda1ef17693397388f0cf73b1cfee06b&amp;width=100&amp;height=50'; this.onmouseover=null;" ><em><img src='?resource=db.png' id='id1' alt='graph'/></em>/js/** GET</a></td> <td align='right'>36</td> <td align='right'>23</td> <td align='right'><span class='info'>89</span></td> <td align='right'>313</td> <td align='right'>71</td> <td align='right'>14</td> <td align='right'><span class='info'>11</span></td> <td align='right'>2,256</td> <td align='right'>0.00</td> <td align='right'>150</td> <td align='right'>0</td> <td align='right'>0</td></tr><tr class='odd' onmouseover="this.className='highlight'" onmouseout="this.className='odd'">
<td class='wrappedText'><a class='tooltip' href='?part=graph&amp;graph=http63dcceb23443f1f6acf044afc62a984648942dd6' onmouseover="document.getElementById('id2').src='?graph=http63dcceb23443f1f6acf044afc62a984648942dd6&amp;width=100&amp;height=50'; this.onmouseover=null;" ><em><img src='?resource=db.png' id='id2' alt='graph'/></em>/uums/sys/userinfo/findPermissionByAppUser ajax POST</a></td> <td align='right'>13</td> <td align='right'>2</td> <td align='right'><span class='severe'>367</span></td> <td align='right'>389</td> <td align='right'>31</td> <td align='right'>5</td> <td align='right'><span class='info'>46</span></td> <td align='right'>5,746</td> <td align='right'>0.00</td> <td align='right'>2</td> <td align='right'>0</td> <td align='right'>0</td></tr><tr onmouseover="this.className='highlight'" onmouseout="this.className=''">
<td class='wrappedText'><a class='tooltip' href='?part=graph&amp;graph=httpa342a9e413b23b5792a553333913561b60155bd7' onmouseover="document.getElementById('id3').src='?graph=httpa342a9e413b23b5792a553333913561b60155bd7&amp;width=100&amp;height=50'; this.onmouseover=null;" ><em><img src='?resource=db.png' id='id3' alt='graph'/></em>/action/queryActBusinessStatus/myTaskToDo ajax POST</a></td> <td align='right'>9</td> <td align='right'>2</td> <td align='right'><span class='warning'>255</span></td> <td align='right'>388</td> <td align='right'>187</td> <td align='right'>13</td> <td align='right'><span class='info'>124</span></td> <td align='right'>19,076</td> <td align='right'>0.00</td> <td align='right'>11</td> <td align='right'>2</td> <td align='right'>25</td></tr><tr class='odd' onmouseover="this.className='highlight'" onmouseout="this.className='odd'">
<td class='wrappedText'><a class='tooltip' href='?part=graph&amp;graph=http2cde42ecb66ae58544af5c46ad4c835e9ea52ac6' onmouseover="document.getElementById('id4').src='?graph=http2cde42ecb66ae58544af5c46ad4c835e9ea52ac6&amp;width=100&amp;height=50'; this.onmouseover=null;" ><em><img src='?resource=db.png' id='id4' alt='graph'/></em>/login GET</a></td> <td align='right'>8</td> <td align='right'>1</td> <td align='right'><span class='severe'>459</span></td> <td align='right'>459</td> <td align='right'>0</td> <td align='right'>18</td> <td align='right'><span class='severe'>343</span></td> <td align='right'>56,247</td> <td align='right'>0.00</td> <td align='right'>3</td> <td align='right'>0</td> <td align='right'>0</td></tr><tr onmouseover="this.className='highlight'" onmouseout="this.className=''">
<td class='wrappedText'><a class='tooltip' href='?part=graph&amp;graph=http8bd1f55d1764edc70aca5749d322b29550e711b8' onmouseover="document.getElementById('id5').src='?graph=http8bd1f55d1764edc70aca5749d322b29550e711b8&amp;width=100&amp;height=50'; this.onmouseover=null;" ><em><img src='?resource=db.png' id='id5' alt='graph'/></em>/sys/file/uploadProcessFiles POST</a></td> <td align='right'>7</td> <td align='right'>2</td> <td align='right'><span class='info'>215</span></td> <td align='right'>263</td> <td align='right'>67</td> <td align='right'>13</td> <td align='right'><span class='info'>124</span></td> <td align='right'>14,049</td> <td align='right'>0.00</td> <td align='right'>0</td> <td align='right'>4</td> <td align='right'>17</td></tr><tr class='odd' onmouseover="this.className='highlight'" onmouseout="this.className='odd'">
<td class='wrappedText'><a class='tooltip' href='?part=graph&amp;graph=http687e7eec039e48d5f91dbef7a03df0dba9993e8b' onmouseover="document.getElementById('id6').src='?graph=http687e7eec039e48d5f91dbef7a03df0dba9993e8b&amp;width=100&amp;height=50'; this.onmouseover=null;" ><em><img src='?resource=db.png' id='id6' alt='graph'/></em>/captcha GET</a></td> <td align='right'>6</td> <td align='right'>1</td> <td align='right'><span class='severe'>382</span></td> <td align='right'>382</td> <td align='right'>0</td> <td align='right'>11</td> <td align='right'><span class='info'>218</span></td> <td align='right'>4,734</td> <td align='right'>0.00</td> <td align='right'>0</td> <td align='right'>0</td> <td align='right'>0</td></tr><tr onmouseover="this.className='highlight'" onmouseout="this.className=''">
<td class='wrappedText'><a class='tooltip' href='?part=graph&amp;graph=httpf365741f249a5238cd70700c8b081ceeab6b987d' onmouseover="document.getElementById('id7').src='?graph=httpf365741f249a5238cd70700c8b081ceeab6b987d&amp;width=100&amp;height=50'; this.onmouseover=null;" ><em><img src='?resource=db.png' id='id7' alt='graph'/></em>/fonts/** GET</a></td> <td align='right'>3</td> <td align='right'>3</td> <td align='right'><span class='info'>69</span></td> <td align='right'>133</td> <td align='right'>55</td> <td align='right'>4</td> <td align='right'><span class='info'>25</span></td> <td align='right'>4,121</td> <td align='right'>0.00</td> <td align='right'>52</td> <td align='right'>0</td> <td align='right'>0</td></tr><tr class='odd' onmouseover="this.className='highlight'" onmouseout="this.className='odd'">
<td class='wrappedText'><a class='tooltip' href='?part=graph&amp;graph=http3f49765be9be73570f919605be208edb89d22adb' onmouseover="document.getElementById('id8').src='?graph=http3f49765be9be73570f919605be208edb89d22adb&amp;width=100&amp;height=50'; this.onmouseover=null;" ><em><img src='?resource=db.png' id='id8' alt='graph'/></em>/html/** GET</a></td> <td align='right'>3</td> <td align='right'>5</td> <td align='right'><span class='info'>41</span></td> <td align='right'>86</td> <td align='right'>31</td> <td align='right'>2</td> <td align='right'><span class='info'>9</span></td> <td align='right'>2,258</td> <td align='right'>0.00</td> <td align='right'>19</td> <td align='right'>0</td> <td align='right'>0</td></tr><tr onmouseover="this.className='highlight'" onmouseout="this.className=''">
<td class='wrappedText'><a class='tooltip' href='?part=graph&amp;graph=http2ba135688555d21afa67433b6e94749adce60206' onmouseover="document.getElementById('id9').src='?graph=http2ba135688555d21afa67433b6e94749adce60206&amp;width=100&amp;height=50'; this.onmouseover=null;" ><em><img src='?resource=db.png' id='id9' alt='graph'/></em>/index GET</a></td> <td align='right'>3</td> <td align='right'>2</td> <td align='right'><span class='info'>98</span></td> <td align='right'>167</td> <td align='right'>97</td> <td align='right'>1</td> <td align='right'><span class='info'>15</span></td> <td align='right'>3,755</td> <td align='right'>0.00</td> <td align='right'>13</td> <td align='right'>0</td> <td align='right'>0</td></tr><tr class='odd' onmouseover="this.className='highlight'" onmouseout="this.className='odd'">
<td class='wrappedText'><a class='tooltip' href='?part=graph&amp;graph=http147cca9384536c986f35604a71e1d1c40c8ac4a1' onmouseover="document.getElementById('id10').src='?graph=http147cca9384536c986f35604a71e1d1c40c8ac4a1&amp;width=100&amp;height=50'; this.onmouseover=null;" ><em><img src='?resource=db.png' id='id10' alt='graph'/></em>/sys/file/download GET</a></td> <td align='right'>3</td> <td align='right'>2</td> <td align='right'><span class='info'>88</span></td> <td align='right'>94</td> <td align='right'>8</td> <td align='right'>7</td> <td align='right'><span class='info'>70</span></td> <td align='right'>1,717</td> <td align='right'>0.00</td> <td align='right'>17</td> <td align='right'>2</td> <td align='right'>8</td></tr><tr onmouseover="this.className='highlight'" onmouseout="this.className=''">
<td class='wrappedText'><a class='tooltip' href='?part=graph&amp;graph=httpf7c526fe1fffb461148195fdb774fabaf064a4f1' onmouseover="document.getElementById('id11').src='?graph=httpf7c526fe1fffb461148195fdb774fabaf064a4f1&amp;width=100&amp;height=50'; this.onmouseover=null;" ><em><img src='?resource=db.png' id='id11' alt='graph'/></em>/action/newcolumn/findAllByUser ajax POST</a></td> <td align='right'>2</td> <td align='right'>2</td> <td align='right'><span class='info'>61</span></td> <td align='right'>62</td> <td align='right'>0</td> <td align='right'>3</td> <td align='right'><span class='info'>30</span></td> <td align='right'>3,192</td> <td align='right'>0.00</td> <td align='right'>17</td> <td align='right'>1</td> <td align='right'>2</td></tr><tr class='odd' onmouseover="this.className='highlight'" onmouseout="this.className='odd'">
<td class='wrappedText'><a class='tooltip' href='?part=graph&amp;graph=http67c309a1e88db1c44a4099c09ce926cf8d591a13' onmouseover="document.getElementById('id12').src='?graph=http67c309a1e88db1c44a4099c09ce926cf8d591a13&amp;width=100&amp;height=50'; this.onmouseover=null;" ><em><img src='?resource=db.png' id='id12' alt='graph'/></em>/images/** GET</a></td> <td align='right'>1</td> <td align='right'>2</td> <td align='right'><span class='info'>50</span></td> <td align='right'>64</td> <td align='right'>19</td> <td align='right'>1</td> <td align='right'><span class='info'>15</span></td> <td align='right'>4,875</td> <td align='right'>0.00</td> <td align='right'>90</td> <td align='right'>0</td> <td align='right'>0</td></tr><tr onmouseover="this.className='highlight'" onmouseout="this.className=''">
<td class='wrappedText'><a class='tooltip' href='?part=graph&amp;graph=httpca08584fd144eef6a1af131ef41bd5b0ef0936f4' onmouseover="document.getElementById('id13').src='?graph=httpca08584fd144eef6a1af131ef41bd5b0ef0936f4&amp;width=100&amp;height=50'; this.onmouseover=null;" ><em><img src='?resource=db.png' id='id13' alt='graph'/></em>/getCurrentUser ajax POST</a></td> <td align='right'>1</td> <td align='right'>2</td> <td align='right'><span class='info'>33</span></td> <td align='right'>36</td> <td align='right'>3</td> <td align='right'>0</td> <td align='right'><span class='info'>7</span></td> <td align='right'>593</td> <td align='right'>0.00</td> <td align='right'>7</td> <td align='right'>0</td> <td align='right'>0</td></tr></tbody></table>
</div>
<h3 class='chapterTitle'><img src='?resource=db.png' alt='sql'/>
<a name='sql'></a>图表sql - 1 天</h3>
<table class='sortable' width='100%' border='1' summary='sql global'>
<thead><tr><th>请求</th><th class='sorttable_numeric'>% 总得积累时间</th><th class='sorttable_numeric'>点击</th><th class='sorttable_numeric'>平均时间 (ms)</th><th class='sorttable_numeric'>最大时间 (ms)</th><th class='sorttable_numeric'>标准偏差</th><th class='sorttable_numeric'>% 系统错误</th></tr></thead><tbody>
<tr onmouseover="this.className='highlight'" onmouseout="this.className=''">
<td class='wrappedText'>sql global</td> <td align='right'>100</td> <td align='right'>46</td> <td align='right'><span class='info'>4</span></td> <td align='right'>21</td> <td align='right'>4</td> <td align='right'>0.00</td></tr><tr class='odd' onmouseover="this.className='highlight'" onmouseout="this.className='odd'">
<td class='wrappedText'>sql warning</td> <td align='right'>5</td> <td align='right'>1</td> <td align='right'><span class='severe'>12</span></td> <td align='right'>12</td> <td align='right'>0</td> <td align='right'>0.00</td></tr><tr onmouseover="this.className='highlight'" onmouseout="this.className=''">
<td class='wrappedText'>sql severe</td> <td align='right'>11</td> <td align='right'>2</td> <td align='right'><span class='severe'>13</span></td> <td align='right'>21</td> <td align='right'>11</td> <td align='right'>0.00</td></tr></tbody></table>
<div align='right'>
5 使用每分钟在 17 请求
&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;
<a href="javascript:showHide('detailssql');" class='noPrint'><img id='detailssqlImg' src='?resource=bullets/plus.png' alt=''/> 描述</a>
&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;
</div>
<div id='detailssql' style='display: none;'>
<table class='sortable' width='100%' border='1' summary='sql'>
<thead><tr><th>请求</th><th class='sorttable_numeric'>% 总得积累时间</th><th class='sorttable_numeric'>点击</th><th class='sorttable_numeric'>平均时间 (ms)</th><th class='sorttable_numeric'>最大时间 (ms)</th><th class='sorttable_numeric'>标准偏差</th><th class='sorttable_numeric'>% 系统错误</th></tr></thead><tbody>
<tr onmouseover="this.className='highlight'" onmouseout="this.className=''">
<td class='wrappedText'><a class='tooltip' href='?part=graph&amp;graph=sql0e01b002bd96355ee55856e0e441eea18f26ae6e' onmouseover="document.getElementById('id14').src='?graph=sql0e01b002bd96355ee55856e0e441eea18f26ae6e&amp;width=100&amp;height=50'; this.onmouseover=null;" ><em><img src='?resource=db.png' id='id14' alt='graph'/></em>/* insert com.simbest.boot.sys.model.SysTaskExecutedLog */ <span class='sqlKeyword'>insert into</span> sys_task_executed_log (created_time, modified_time, content, duration_time, execute_flag, hostname, port, task_name, id) <span class='sqlKeyword'>values</span> (?, ?, ?, ?, ?, ?, ?, ?, ?)</a></td> <td align='right'>13</td> <td align='right'>12</td> <td align='right'><span class='info'>2</span></td> <td align='right'>6</td> <td align='right'>1</td> <td align='right'>0.00</td></tr><tr class='odd' onmouseover="this.className='highlight'" onmouseout="this.className='odd'">
<td class='wrappedText'><a class='tooltip' href='?part=graph&amp;graph=sqld4e4a0f4abaa657536a05e60000387866aed79fa' onmouseover="document.getElementById('id15').src='?graph=sqld4e4a0f4abaa657536a05e60000387866aed79fa&amp;width=100&amp;height=50'; this.onmouseover=null;" ><em><img src='?resource=db.png' id='id15' alt='graph'/></em>/* dynamic native SQL query */  <span class='sqlKeyword'>SELECT</span> act.id, act.business_key, act.create_org_code, act.create_org_name, to_char(act.CREATE_TIME,'yyyy-MM-dd HH24:mi:ss') as CREATE_TIME, act.create_user_code, act.create_user_id, act.create_user_name,        act.current_state, act.duration, decode(act.enabled,0,'false',1,'true') as enabled, act.end_time, act.parent_proc_id, act.previous_assistant, to_char(act.previous_assistant_date,'yyyy-MM-dd HH24:mi:ss') as PREVIOUS_ASSISTANT_DATE, act.previous_assistant_name,        act.previous_assistant_org_code, act.previous_assistant_org_name, act.process_ch_name, act.process_def_id, act.process_def_name, act.process_inst_id,        act.receipt_code, act.receipt_title,decode(act.removed,0,'false',1,'true') as removed,to_char(act.start_time,'yyyy-MM-dd HH24:mi:ss') as START_TIME, to_char(act.update_time,'yyyy-MM-dd HH24:mi:ss') as UPDATE_TIME,       REGEXP_SUBSTR(act.receipt_code, '[a-zA-Z]+') as PM_INS_TYPE, wk.WORK_ITEM_ID, wk.ACTIVITY_DEF_ID, wk.ACTIVITY_INST_NAME,       wk.participant, wk.assistant,to_char(wk.start_Time,'yyyy-MM-dd HH24:mi:ss') as WORK_ITEM_START_TIME,to_char(wk.end_time,'yyyy-MM-dd HH24:mi:ss') as WORK_ITEM_END_TIME <span class='sqlKeyword'>FROM</span> act_business_status act,       us_pm_instence us,       wf_workitem_model wk <span class='sqlKeyword'>WHERE</span> act.PROCESS_INST_ID = wk.PROCESS_INST_ID   and act.BUSINESS_KEY = us.id   and us.pm_ins_type in (?, ?, ?, ?, ?, ?, ?, ?)   and wk.participant = ?   and act.RECEIPT_TITLE like concat(concat('%', ?), '%')   and wk.current_State = 10   and act.enabled = 1   and wk.enabled = 1   and us.enabled = 1 <span class='sqlKeyword'>order by</span> wk.START_TIME desc</a></td> <td align='right'>11</td> <td align='right'>2</td> <td align='right'><span class='severe'>13</span></td> <td align='right'>21</td> <td align='right'>11</td> <td align='right'>0.00</td></tr><tr onmouseover="this.className='highlight'" onmouseout="this.className=''">
<td class='wrappedText'><a class='tooltip' href='?part=graph&amp;graph=sql2029d8b564290770baf7a5078b4ea76eaed1dba5' onmouseover="document.getElementById('id16').src='?graph=sql2029d8b564290770baf7a5078b4ea76eaed1dba5&amp;width=100&amp;height=50'; this.onmouseover=null;" ><em><img src='?resource=db.png' id='id16' alt='graph'/></em>/* <span class='sqlKeyword'>select</span> generatedAlias0 <span class='sqlKeyword'>from</span> SysFile as generatedAlias0 <span class='sqlKeyword'>where</span> ( ( generatedAlias0.removedTime&gt;:param0 ) or ( generatedAlias0.removedTime is null ) ) and ( generatedAlias0.id=:param1 ) */ <span class='sqlKeyword'>select</span> sysfile0_.id as id1_29_, sysfile0_.created_time as created_time2_29_, sysfile0_.modified_time as modified_time3_29_, sysfile0_.creator as creator4_29_, sysfile0_.enabled as enabled5_29_, sysfile0_.modifier as modifier6_29_, sysfile0_.removed_time as removed_time7_29_, sysfile0_.anonymous_file_path as anonymous_file_pat8_29_, sysfile0_.api_file_path as api_file_path9_29_, sysfile0_.backup_path as backup_path10_29_, sysfile0_.down_load_url as down_load_url11_29_, sysfile0_.file_name as file_name12_29_, sysfile0_.file_path as file_path13_29_, sysfile0_.file_size as file_size14_29_, sysfile0_.file_type as file_type15_29_, sysfile0_.is_local as is_local16_29_, sysfile0_.mobile_file_path as mobile_file_path17_29_, sysfile0_.pm_ins_id as pm_ins_id18_29_, sysfile0_.pm_ins_type as pm_ins_type19_29_, sysfile0_.pm_ins_type_part as pm_ins_type_part20_29_, sysfile0_.store_location as store_location21_29_ <span class='sqlKeyword'>from</span> sys_file sysfile0_ <span class='sqlKeyword'>where</span> (sysfile0_.removed_time&gt;? or sysfile0_.removed_time is null) and sysfile0_.id=?</a></td> <td align='right'>9</td> <td align='right'>6</td> <td align='right'><span class='info'>3</span></td> <td align='right'>5</td> <td align='right'>1</td> <td align='right'>0.00</td></tr><tr class='odd' onmouseover="this.className='highlight'" onmouseout="this.className='odd'">
<td class='wrappedText'><a class='tooltip' href='?part=graph&amp;graph=sqlcfad7c94bbfa09cd69690cb639cd4cbc9befd277' onmouseover="document.getElementById('id17').src='?graph=sqlcfad7c94bbfa09cd69690cb639cd4cbc9befd277&amp;width=100&amp;height=50'; this.onmouseover=null;" ><em><img src='?resource=db.png' id='id17' alt='graph'/></em>/* insert com.simbest.boot.sys.model.SysFile */ <span class='sqlKeyword'>insert into</span> sys_file (created_time, modified_time, creator, enabled, modifier, removed_time, anonymous_file_path, api_file_path, backup_path, down_load_url, file_name, file_path, file_size, file_type, is_local, mobile_file_path, pm_ins_id, pm_ins_type, pm_ins_type_part, store_location, id) <span class='sqlKeyword'>values</span> (?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?)</a></td> <td align='right'>7</td> <td align='right'>2</td> <td align='right'><span class='warning'>8</span></td> <td align='right'>15</td> <td align='right'>9</td> <td align='right'>0.00</td></tr><tr onmouseover="this.className='highlight'" onmouseout="this.className=''">
<td class='wrappedText'><a class='tooltip' href='?part=graph&amp;graph=sql5ad311f5a1c5f880b4442931fec87a275a920a95' onmouseover="document.getElementById('id18').src='?graph=sql5ad311f5a1c5f880b4442931fec87a275a920a95&amp;width=100&amp;height=50'; this.onmouseover=null;" ><em><img src='?resource=db.png' id='id18' alt='graph'/></em>/* insert com.simbest.boot.sys.model.SysOperateLog */ <span class='sqlKeyword'>insert into</span> sys_log_operate (created_time, modified_time, creator, enabled, modifier, removed_time, bussiness_key, error_msg, operate_flag, operate_interface, result_msg, id, interface_param) <span class='sqlKeyword'>values</span> (?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?)</a></td> <td align='right'>7</td> <td align='right'>2</td> <td align='right'><span class='warning'>8</span></td> <td align='right'>15</td> <td align='right'>9</td> <td align='right'>0.00</td></tr><tr class='odd' onmouseover="this.className='highlight'" onmouseout="this.className='odd'">
<td class='wrappedText'><a class='tooltip' href='?part=graph&amp;graph=sql1f65667b9424a95d72bb859ec0671da7a29868eb' onmouseover="document.getElementById('id19').src='?graph=sql1f65667b9424a95d72bb859ec0671da7a29868eb&amp;width=100&amp;height=50'; this.onmouseover=null;" ><em><img src='?resource=db.png' id='id19' alt='graph'/></em>/* dynamic native SQL query */ <span class='sqlKeyword'>select</span> t.id,       to_char(t.created_time, 'yyyy-mm-dd hh:mm:ss') as created_time,       to_char(t.modified_time, 'yyyy-mm-dd hh:mm:ss') as modified_time,       t.creator,       t.enabled,       t.modifier,       t.removed_time,       t.a_belong_company_code,       t.a_belong_company_name,       t.a_belong_department_code,       t.a_belong_department_name,       t.a_belong_org_code,       t.a_belong_org_name,       t.a_company_type_dict_desc,       t.a_company_type_dict_value,       t.actionurl,       t.activity_def_id,       t.activity_inst_id,       t.activity_inst_name,       t.agent_user,       t.agent_user_name,       t.allow_agent,       t.assistant,       t.biz_state,       t.catalog_name,       t.cataloguuid,       t.create_time,       t.current_state,       to_char(t.end_time, 'yyyy-mm-dd hh:mm:ss') as end_time,       t.final_time,       t.is_time_out,       t.p_belong_company_code,       t.p_belong_company_name,       t.p_belong_department_code,       t.p_belong_department_name,       t.p_belong_org_code,       t.p_belong_org_name,       t.p_company_type_dict_desc,       t.p_company_type_dict_value,       t.parti_name,       t.participant,       t.participant_identity,       t.priority,       t.process_ch_name,       t.process_def_id,       t.process_def_name,       t.process_inst_id,       t.process_inst_name,       t.receipt_code,       t.receipt_id,       t.receipt_title,       t.remind_time,       t.root_proc_inst_id,       to_char(t.start_time, 'yyyy-mm-dd hh:mm:ss') as start_time,       t.url_type,       t.work_item_desc,       t.work_item_id,       t.work_item_name <span class='sqlKeyword'>from</span> WF_WORKITEM_MODEL t <span class='sqlKeyword'>where</span> t.spare01 is null and t.created_time &gt;= to_date(?, 'yyyy-MM-dd HH24:mi:ss') and t.created_time &lt;= to_date(?, 'yyyy-MM-dd HH24:mi:ss') and rownum &lt;= 50</a></td> <td align='right'>6</td> <td align='right'>2</td> <td align='right'><span class='info'>7</span></td> <td align='right'>9</td> <td align='right'>2</td> <td align='right'>0.00</td></tr><tr onmouseover="this.className='highlight'" onmouseout="this.className=''">
<td class='wrappedText'><a class='tooltip' href='?part=graph&amp;graph=sql237d4bc4eb1a9cf74d52e27d7815ff93318f217a' onmouseover="document.getElementById('id20').src='?graph=sql237d4bc4eb1a9cf74d52e27d7815ff93318f217a&amp;width=100&amp;height=50'; this.onmouseover=null;" ><em><img src='?resource=db.png' id='id20' alt='graph'/></em>/* dynamic native SQL query */ <span class='sqlKeyword'>select</span>  t.id,       to_char(t.created_time, 'yyyy-mm-dd hh:mm:ss') as created_time,       to_char(t.modified_time, 'yyyy-mm-dd hh:mm:ss') as modified_time,       t.creator,       t.enabled,       t.modifier,       t.removed_time,       t.catalog_name,       t.cataloguuid,       t.create_time,       t.current_state,       to_char(t.end_time, 'yyyy-mm-dd hh:mm:ss') as end_time,       t.final_time,       t.is_time_out,       t.owner,       t.parent_act_id,       t.parent_proc_id,       t.process_def_id,       t.process_def_name,       t.process_inst_desc,       t.process_inst_id,       t.process_inst_name,       t.receipt_code,       t.receipt_id,       t.receipt_title,       t.remind_time,       to_char(t.start_time, 'yyyy-mm-dd hh:mm:ss') as start_time,       t.time_out_num,       t.time_out_num_desc,       t.update_version <span class='sqlKeyword'>from</span> WF_PROCESS_INST_MODEL t <span class='sqlKeyword'>where</span> t.spare01 is null and t.created_time &gt;= to_date(?, 'yyyy-MM-dd HH24:mi:ss') and t.created_time &lt;= to_date(?, 'yyyy-MM-dd HH24:mi:ss') and rownum &lt;= 50</a></td> <td align='right'>6</td> <td align='right'>2</td> <td align='right'><span class='info'>7</span></td> <td align='right'>12</td> <td align='right'>7</td> <td align='right'>0.00</td></tr><tr class='odd' onmouseover="this.className='highlight'" onmouseout="this.className='odd'">
<td class='wrappedText'><a class='tooltip' href='?part=graph&amp;graph=sqlefe6a3916ca48b7c5ab6a59112bc68de92b70e22' onmouseover="document.getElementById('id21').src='?graph=sqlefe6a3916ca48b7c5ab6a59112bc68de92b70e22&amp;width=100&amp;height=50'; this.onmouseover=null;" ><em><img src='?resource=db.png' id='id21' alt='graph'/></em><span class='sqlKeyword'>select</span> * <span class='sqlKeyword'>from</span> US_TODO_MODEL t  <span class='sqlKeyword'>where</span> t.send_flag = 0 and t.work_flag = 1 and t.enabled =1</a></td> <td align='right'>6</td> <td align='right'>2</td> <td align='right'><span class='info'>7</span></td> <td align='right'>9</td> <td align='right'>2</td> <td align='right'>0.00</td></tr><tr onmouseover="this.className='highlight'" onmouseout="this.className=''">
<td class='wrappedText'><a class='tooltip' href='?part=graph&amp;graph=sql5da6661503448197dcd8c2c5e77063722c14e977' onmouseover="document.getElementById('id22').src='?graph=sql5da6661503448197dcd8c2c5e77063722c14e977&amp;width=100&amp;height=50'; this.onmouseover=null;" ><em><img src='?resource=db.png' id='id22' alt='graph'/></em>/* dynamic native SQL query */ <span class='sqlKeyword'>select</span> t.id,       to_char(t.created_time, 'yyyy-mm-dd hh:mm:ss') as created_time,       to_char(t.modified_time, 'yyyy-mm-dd hh:mm:ss') as modified_time,       t.creator,       t.enabled,       t.modifier,       t.removed_time,       t.activityinstid,       t.content,       t.correlationid,       t.correlationtype,       t.messageid,       t.operationtype,       t.processdefid,       t.processinstid,       t.producer,       t.producer_name,       t.receipt_code,       t.receipt_id,       t.receipt_title,       t.receiver,       t.root_proc_inst_id,       t.workitemid,       t.producer_identity,       t.p_belong_company_code,       t.p_belong_company_name,       t.p_belong_department_code,       t.p_belong_department_name,       t.p_belong_org_code,       t.p_belong_org_name,       t.p_company_type_dict_desc,       t.p_company_type_dict_value,       t.spare01 <span class='sqlKeyword'>from</span> WF_OPTMSG_MODEL t <span class='sqlKeyword'>where</span> t.spare01 is null and t.created_time &gt;= to_date(?, 'yyyy-MM-dd HH24:mi:ss') and t.created_time &lt;= to_date(?, 'yyyy-MM-dd HH24:mi:ss') and rownum &lt;= 50</a></td> <td align='right'>6</td> <td align='right'>2</td> <td align='right'><span class='info'>7</span></td> <td align='right'>12</td> <td align='right'>7</td> <td align='right'>0.00</td></tr><tr class='odd' onmouseover="this.className='highlight'" onmouseout="this.className='odd'">
<td class='wrappedText'><a class='tooltip' href='?part=graph&amp;graph=sql2f0672a6ac8f857e2dd8c367ece660035214da3b' onmouseover="document.getElementById('id23').src='?graph=sql2f0672a6ac8f857e2dd8c367ece660035214da3b&amp;width=100&amp;height=50'; this.onmouseover=null;" ><em><img src='?resource=db.png' id='id23' alt='graph'/></em>/* insert com.simbest.boot.sys.model.SysLogLoginAdmin */ <span class='sqlKeyword'>insert into</span> sys_log_login_admin (account, belong_org_name, browser_engine, browser_engine_version, browser_name, browser_version, ip, is_mobile, is_success, login_entry, login_time, login_type, logout_time, mac, os, os_family, remark, serverip, sessionid, true_name, id) <span class='sqlKeyword'>values</span> (?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?)</a></td> <td align='right'>5</td> <td align='right'>1</td> <td align='right'><span class='severe'>12</span></td> <td align='right'>12</td> <td align='right'>0</td> <td align='right'>0.00</td></tr><tr onmouseover="this.className='highlight'" onmouseout="this.className=''">
<td class='wrappedText'><a class='tooltip' href='?part=graph&amp;graph=sqld3da2cf9e3432844c33293503ffbd984978d8bc5' onmouseover="document.getElementById('id24').src='?graph=sqld3da2cf9e3432844c33293503ffbd984978d8bc5&amp;width=100&amp;height=50'; this.onmouseover=null;" ><em><img src='?resource=db.png' id='id24' alt='graph'/></em>/* <span class='sqlKeyword'>update</span> com.simbest.boot.sys.model.SysFile */ <span class='sqlKeyword'>update</span> sys_file set created_time=?, modified_time=?, creator=?, enabled=?, modifier=?, removed_time=?, anonymous_file_path=?, api_file_path=?, backup_path=?, down_load_url=?, file_name=?, file_path=?, file_size=?, file_type=?, is_local=?, mobile_file_path=?, pm_ins_id=?, pm_ins_type=?, pm_ins_type_part=?, store_location=? <span class='sqlKeyword'>where</span> id=?</a></td> <td align='right'>5</td> <td align='right'>4</td> <td align='right'><span class='info'>3</span></td> <td align='right'>4</td> <td align='right'>1</td> <td align='right'>0.00</td></tr><tr class='odd' onmouseover="this.className='highlight'" onmouseout="this.className='odd'">
<td class='wrappedText'><a class='tooltip' href='?part=graph&amp;graph=sqle46e7d07b906db2e1845a24fa4699414a5658b64' onmouseover="document.getElementById('id25').src='?graph=sqle46e7d07b906db2e1845a24fa4699414a5658b64&amp;width=100&amp;height=50'; this.onmouseover=null;" ><em><img src='?resource=db.png' id='id25' alt='graph'/></em><span class='sqlKeyword'>select</span> * <span class='sqlKeyword'>from</span> US_TODO_MODEL t  <span class='sqlKeyword'>where</span> t.send_flag = 0 and t.work_flag = 0 and t.enabled =1</a></td> <td align='right'>4</td> <td align='right'>2</td> <td align='right'><span class='info'>5</span></td> <td align='right'>8</td> <td align='right'>3</td> <td align='right'>0.00</td></tr><tr onmouseover="this.className='highlight'" onmouseout="this.className=''">
<td class='wrappedText'><a class='tooltip' href='?part=graph&amp;graph=sql87ea11af99797fed2c650ad72ec0295c37116861' onmouseover="document.getElementById('id26').src='?graph=sql87ea11af99797fed2c650ad72ec0295c37116861&amp;width=100&amp;height=50'; this.onmouseover=null;" ><em><img src='?resource=db.png' id='id26' alt='graph'/></em>/* dynamic native SQL query */ <span class='sqlKeyword'>select</span> * <span class='sqlKeyword'>from</span> wf_process_error_log t <span class='sqlKeyword'>where</span> spare1 is null and ENABLED=1 and to_char(t.created_time,'yyyy-mm-dd') &gt;= to_char(sysdate,'yyyy-mm-dd')</a></td> <td align='right'>4</td> <td align='right'>2</td> <td align='right'><span class='info'>5</span></td> <td align='right'>9</td> <td align='right'>5</td> <td align='right'>0.00</td></tr><tr class='odd' onmouseover="this.className='highlight'" onmouseout="this.className='odd'">
<td class='wrappedText'><a class='tooltip' href='?part=graph&amp;graph=sqlaabaa600ae209950db6f61d5eaf66ebfaf0d585f' onmouseover="document.getElementById('id27').src='?graph=sqlaabaa600ae209950db6f61d5eaf66ebfaf0d585f&amp;width=100&amp;height=50'; this.onmouseover=null;" ><em><img src='?resource=db.png' id='id27' alt='graph'/></em>/* dynamic native SQL query */ <span class='sqlKeyword'>SELECT</span> dv.* <span class='sqlKeyword'>from</span> sys_dict d,sys_dict_value dv <span class='sqlKeyword'>WHERE</span> d.dict_type=dv.dict_type and d.enabled=1 and dv.enabled=1 AND d.dict_type=? AND dv.dict_type=?  <span class='sqlKeyword'>order by</span> dv.display_order asc</a></td> <td align='right'>3</td> <td align='right'>1</td> <td align='right'><span class='info'>7</span></td> <td align='right'>7</td> <td align='right'>0</td> <td align='right'>0.00</td></tr><tr onmouseover="this.className='highlight'" onmouseout="this.className=''">
<td class='wrappedText'><a class='tooltip' href='?part=graph&amp;graph=sql57049223b136a4706dd7bae8c12f6c228c75e117' onmouseover="document.getElementById('id28').src='?graph=sql57049223b136a4706dd7bae8c12f6c228c75e117&amp;width=100&amp;height=50'; this.onmouseover=null;" ><em><img src='?resource=db.png' id='id28' alt='graph'/></em>/* dynamic native SQL query */ <span class='sqlKeyword'>select</span> * <span class='sqlKeyword'>from</span> sys_new_column_model  <span class='sqlKeyword'>where</span> enabled = 1   <span class='sqlKeyword'>order by</span> column_level,column_order</a></td> <td align='right'>1</td> <td align='right'>2</td> <td align='right'><span class='info'>2</span></td> <td align='right'>3</td> <td align='right'>1</td> <td align='right'>0.00</td></tr><tr class='odd' onmouseover="this.className='highlight'" onmouseout="this.className='odd'">
<td class='wrappedText'><a class='tooltip' href='?part=graph&amp;graph=sql871007917fa35096055000c5069de1d3ed424b3c' onmouseover="document.getElementById('id29').src='?graph=sql871007917fa35096055000c5069de1d3ed424b3c&amp;width=100&amp;height=50'; this.onmouseover=null;" ><em><img src='?resource=db.png' id='id29' alt='graph'/></em>/* <span class='sqlKeyword'>select</span> generatedAlias0 <span class='sqlKeyword'>from</span> SysRedisIdKey as generatedAlias0 <span class='sqlKeyword'>where</span> generatedAlias0.day=:param0 */ <span class='sqlKeyword'>select</span> sysredisid0_.id as id1_26_, sysredisid0_.day as day2_26_, sysredisid0_.name as name3_26_, sysredisid0_.value as value4_26_ <span class='sqlKeyword'>from</span> sys_redis_id_key sysredisid0_ <span class='sqlKeyword'>where</span> sysredisid0_.day=?</a></td> <td align='right'>0</td> <td align='right'>1</td> <td align='right'><span class='info'>2</span></td> <td align='right'>2</td> <td align='right'>0</td> <td align='right'>0.00</td></tr><tr onmouseover="this.className='highlight'" onmouseout="this.className=''">
<td class='wrappedText'><a class='tooltip' href='?part=graph&amp;graph=sqlaa963054e6956df5dbfee449f2b67ec2ccdc3f1b' onmouseover="document.getElementById('id30').src='?graph=sqlaa963054e6956df5dbfee449f2b67ec2ccdc3f1b&amp;width=100&amp;height=50'; this.onmouseover=null;" ><em><img src='?resource=db.png' id='id30' alt='graph'/></em>/* <span class='sqlKeyword'>select</span> generatedAlias0 <span class='sqlKeyword'>from</span> sys_4a_audit_config as generatedAlias0 */ <span class='sqlKeyword'>select</span> a4logconfi0_.id as id1_12_, a4logconfi0_.created_time as created_time2_12_, a4logconfi0_.modified_time as modified_time3_12_, a4logconfi0_.boby_params as boby_params4_12_, a4logconfi0_.identity_name as identity_name5_12_, a4logconfi0_.method_name as method_name6_12_, a4logconfi0_.module_id as module_id7_12_, a4logconfi0_.module_names as module_names8_12_, a4logconfi0_.op_level_id as op_level_id9_12_, a4logconfi0_.op_type_id as op_type_id10_12_, a4logconfi0_.op_type_name as op_type_name11_12_, a4logconfi0_.resource_code as resource_code12_12_, a4logconfi0_.spare01 as spare13_12_, a4logconfi0_.spare02 as spare14_12_, a4logconfi0_.spare03 as spare15_12_, a4logconfi0_.url_params as url_params16_12_, a4logconfi0_.web_name as web_name17_12_ <span class='sqlKeyword'>from</span> sys_4a_audit_config a4logconfi0_</a></td> <td align='right'>0</td> <td align='right'>1</td> <td align='right'><span class='info'>2</span></td> <td align='right'>2</td> <td align='right'>0</td> <td align='right'>0.00</td></tr></tbody></table>
</div>
<h3 class='chapterTitle'><img src='?resource=beans.png' alt='spring'/>
<a name='spring'></a>图表spring - 1 天</h3>
<table class='sortable' width='100%' border='1' summary='spring global'>
<thead><tr><th>请求</th><th class='sorttable_numeric'>% 总得积累时间</th><th class='sorttable_numeric'>点击</th><th class='sorttable_numeric'>平均时间 (ms)</th><th class='sorttable_numeric'>最大时间 (ms)</th><th class='sorttable_numeric'>标准偏差</th><th class='sorttable_numeric'>% 积累cpu时间</th><th class='sorttable_numeric'>平均CPU使用时间 (ms)</th><th class='sorttable_numeric'>Mean allocated Kb</th><th class='sorttable_numeric'>% 系统错误</th><th class='sorttable_numeric'>平均 hits sql</th><th class='sorttable_numeric'>平均时间 sql (ms)</th></tr></thead><tbody>
<tr onmouseover="this.className='highlight'" onmouseout="this.className=''">
<td class='wrappedText'>spring global</td> <td align='right'>100</td> <td align='right'>105</td> <td align='right'><span class='info'>177</span></td> <td align='right'>1,750</td> <td align='right'>426</td> <td align='right'>100</td> <td align='right'><span class='info'>20</span></td> <td align='right'>2,506</td> <td align='right'>0.00</td> <td align='right'>0</td> <td align='right'>4</td></tr><tr class='odd' onmouseover="this.className='highlight'" onmouseout="this.className='odd'">
<td class='wrappedText'>spring warning</td> <td align='right'>0</td> <td align='right'>0</td> <td align='right'><span class='info'>-1</span></td> <td align='right'>0</td> <td align='right'>-1</td> <td align='right'>0</td> <td align='right'><span class='info'>-1</span></td> <td align='right'>-1</td> <td align='right'>0.00</td> <td align='right'>-1</td> <td align='right'>-1</td></tr><tr onmouseover="this.className='highlight'" onmouseout="this.className=''">
<td class='wrappedText'>spring severe</td> <td align='right'>65</td> <td align='right'>7</td> <td align='right'><span class='severe'>1,733</span></td> <td align='right'>1,750</td> <td align='right'>10</td> <td align='right'>4</td> <td align='right'><span class='info'>13</span></td> <td align='right'>843</td> <td align='right'>0.00</td> <td align='right'>0</td> <td align='right'>0</td></tr></tbody></table>
<div align='right'>
12 使用每分钟在 47 请求
&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;
<a href='?part=counterSummaryPerClass&amp;counter=spring' class='noPrint'>类的概要</a>
&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;
<a href="javascript:showHide('detailsspring');" class='noPrint'><img id='detailsspringImg' src='?resource=bullets/plus.png' alt=''/> 描述</a>
&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;
</div>
<div id='detailsspring' style='display: none;'>
<table class='sortable' width='100%' border='1' summary='spring'>
<thead><tr><th>请求</th><th class='sorttable_numeric'>% 总得积累时间</th><th class='sorttable_numeric'>点击</th><th class='sorttable_numeric'>平均时间 (ms)</th><th class='sorttable_numeric'>最大时间 (ms)</th><th class='sorttable_numeric'>标准偏差</th><th class='sorttable_numeric'>% 积累cpu时间</th><th class='sorttable_numeric'>平均CPU使用时间 (ms)</th><th class='sorttable_numeric'>Mean allocated Kb</th><th class='sorttable_numeric'>% 系统错误</th><th class='sorttable_numeric'>平均 hits sql</th><th class='sorttable_numeric'>平均时间 sql (ms)</th></tr></thead><tbody>
<tr onmouseover="this.className='highlight'" onmouseout="this.className=''">
<td class='wrappedText'><a class='tooltip' href='?part=graph&amp;graph=spring36390f210087277bc83d9fce32a8f77965408c08' onmouseover="document.getElementById('id31').src='?graph=spring36390f210087277bc83d9fce32a8f77965408c08&amp;width=100&amp;height=50'; this.onmouseover=null;" ><em><img src='?resource=db.png' id='id31' alt='graph'/></em>AppRuntimeMaster.becameMasertIfNotExist</a></td> <td align='right'>65</td> <td align='right'>7</td> <td align='right'><span class='severe'>1,733</span></td> <td align='right'>1,750</td> <td align='right'>10</td> <td align='right'>4</td> <td align='right'><span class='info'>13</span></td> <td align='right'>843</td> <td align='right'>0.00</td> <td align='right'>0</td> <td align='right'>0</td></tr><tr class='odd' onmouseover="this.className='highlight'" onmouseout="this.className='odd'">
<td class='wrappedText'><a class='tooltip' href='?part=graph&amp;graph=springb272542c95f7ddcdee26c5b448c35d817df665d5' onmouseover="document.getElementById('id32').src='?graph=springb272542c95f7ddcdee26c5b448c35d817df665d5&amp;width=100&amp;height=50'; this.onmouseover=null;" ><em><img src='?resource=db.png' id='id32' alt='graph'/></em>UumsSysUserInfoController.findPermissionByAppUser</a></td> <td align='right'>3</td> <td align='right'>2</td> <td align='right'><span class='info'>315</span></td> <td align='right'>332</td> <td align='right'>23</td> <td align='right'>2</td> <td align='right'><span class='info'>31</span></td> <td align='right'>4,416</td> <td align='right'>0.00</td> <td align='right'>0</td> <td align='right'>0</td></tr><tr onmouseover="this.className='highlight'" onmouseout="this.className=''">
<td class='wrappedText'><a class='tooltip' href='?part=graph&amp;graph=spring29390caa0513a7b8421cb01ef9a9d88272041a6d' onmouseover="document.getElementById('id33').src='?graph=spring29390caa0513a7b8421cb01ef9a9d88272041a6d&amp;width=100&amp;height=50'; this.onmouseover=null;" ><em><img src='?resource=db.png' id='id33' alt='graph'/></em>QueryActBusinessStatusController.myTaskToDo</a></td> <td align='right'>2</td> <td align='right'>2</td> <td align='right'><span class='info'>224</span></td> <td align='right'>349</td> <td align='right'>176</td> <td align='right'>10</td> <td align='right'><span class='info'>109</span></td> <td align='right'>18,258</td> <td align='right'>0.00</td> <td align='right'>2</td> <td align='right'>25</td></tr><tr class='odd' onmouseover="this.className='highlight'" onmouseout="this.className='odd'">
<td class='wrappedText'><a class='tooltip' href='?part=graph&amp;graph=spring0399a717a5a17b9d14d0d0252abf55d412c5b36f' onmouseover="document.getElementById('id34').src='?graph=spring0399a717a5a17b9d14d0d0252abf55d412c5b36f&amp;width=100&amp;height=50'; this.onmouseover=null;" ><em><img src='?resource=db.png' id='id34' alt='graph'/></em>QueryActBusinessStatusImpl.myTaskToDo</a></td> <td align='right'>2</td> <td align='right'>2</td> <td align='right'><span class='info'>223</span></td> <td align='right'>346</td> <td align='right'>173</td> <td align='right'>10</td> <td align='right'><span class='info'>109</span></td> <td align='right'>17,765</td> <td align='right'>0.00</td> <td align='right'>2</td> <td align='right'>25</td></tr><tr onmouseover="this.className='highlight'" onmouseout="this.className=''">
<td class='wrappedText'><a class='tooltip' href='?part=graph&amp;graph=springf44d8a3a5a2dd59fccb2e37e45bfab2f1f175ab7' onmouseover="document.getElementById('id35').src='?graph=springf44d8a3a5a2dd59fccb2e37e45bfab2f1f175ab7&amp;width=100&amp;height=50'; this.onmouseover=null;" ><em><img src='?resource=db.png' id='id35' alt='graph'/></em>SysFileController.uploadFile</a></td> <td align='right'>2</td> <td align='right'>2</td> <td align='right'><span class='info'>188</span></td> <td align='right'>217</td> <td align='right'>40</td> <td align='right'>9</td> <td align='right'><span class='info'>101</span></td> <td align='right'>11,961</td> <td align='right'>0.00</td> <td align='right'>4</td> <td align='right'>17</td></tr><tr class='odd' onmouseover="this.className='highlight'" onmouseout="this.className='odd'">
<td class='wrappedText'><a class='tooltip' href='?part=graph&amp;graph=spring36d6e0c1d9b0a83520515232c9a102fcdfa73557' onmouseover="document.getElementById('id36').src='?graph=spring36d6e0c1d9b0a83520515232c9a102fcdfa73557&amp;width=100&amp;height=50'; this.onmouseover=null;" ><em><img src='?resource=db.png' id='id36' alt='graph'/></em>CaptchaController.getJPGCode</a></td> <td align='right'>1</td> <td align='right'>1</td> <td align='right'><span class='info'>368</span></td> <td align='right'>368</td> <td align='right'>0</td> <td align='right'>10</td> <td align='right'><span class='info'>218</span></td> <td align='right'>3,606</td> <td align='right'>0.00</td> <td align='right'>0</td> <td align='right'>0</td></tr><tr onmouseover="this.className='highlight'" onmouseout="this.className=''">
<td class='wrappedText'><a class='tooltip' href='?part=graph&amp;graph=spring876a4f285fdf31ef80f4de66a9c930b78b0a7950' onmouseover="document.getElementById('id37').src='?graph=spring876a4f285fdf31ef80f4de66a9c930b78b0a7950&amp;width=100&amp;height=50'; this.onmouseover=null;" ><em><img src='?resource=db.png' id='id37' alt='graph'/></em>SysFileService.uploadProcessFiles</a></td> <td align='right'>1</td> <td align='right'>2</td> <td align='right'><span class='info'>165</span></td> <td align='right'>188</td> <td align='right'>32</td> <td align='right'>7</td> <td align='right'><span class='info'>85</span></td> <td align='right'>10,492</td> <td align='right'>0.00</td> <td align='right'>4</td> <td align='right'>17</td></tr><tr class='odd' onmouseover="this.className='highlight'" onmouseout="this.className='odd'">
<td class='wrappedText'><a class='tooltip' href='?part=graph&amp;graph=spring363a0c68c88324c7390eb49e27d4f6776a6bb9d7' onmouseover="document.getElementById('id38').src='?graph=spring363a0c68c88324c7390eb49e27d4f6776a6bb9d7&amp;width=100&amp;height=50'; this.onmouseover=null;" ><em><img src='?resource=db.png' id='id38' alt='graph'/></em>SyncTaskDataToWorkManagerTask.checkAndExecute</a></td> <td align='right'>1</td> <td align='right'>2</td> <td align='right'><span class='info'>151</span></td> <td align='right'>163</td> <td align='right'>16</td> <td align='right'>0</td> <td align='right'><span class='info'>0</span></td> <td align='right'>3,319</td> <td align='right'>0.00</td> <td align='right'>2</td> <td align='right'>9</td></tr><tr onmouseover="this.className='highlight'" onmouseout="this.className=''">
<td class='wrappedText'><a class='tooltip' href='?part=graph&amp;graph=spring436d2bd37c60b752ab4bd99e0d0907b7d1921929' onmouseover="document.getElementById('id39').src='?graph=spring436d2bd37c60b752ab4bd99e0d0907b7d1921929&amp;width=100&amp;height=50'; this.onmouseover=null;" ><em><img src='?resource=db.png' id='id39' alt='graph'/></em>MyAuthService.findUserPermissionByAppcode</a></td> <td align='right'>1</td> <td align='right'>1</td> <td align='right'><span class='info'>289</span></td> <td align='right'>289</td> <td align='right'>0</td> <td align='right'>0</td> <td align='right'><span class='info'>0</span></td> <td align='right'>1,203</td> <td align='right'>0.00</td> <td align='right'>0</td> <td align='right'>0</td></tr><tr class='odd' onmouseover="this.className='highlight'" onmouseout="this.className='odd'">
<td class='wrappedText'><a class='tooltip' href='?part=graph&amp;graph=springb7443eb7525cdcf5dcdfe98345302acd351c5de8' onmouseover="document.getElementById('id40').src='?graph=springb7443eb7525cdcf5dcdfe98345302acd351c5de8&amp;width=100&amp;height=50'; this.onmouseover=null;" ><em><img src='?resource=db.png' id='id40' alt='graph'/></em>AutoCheckWithUpdateTask.checkAndExecute</a></td> <td align='right'>1</td> <td align='right'>2</td> <td align='right'><span class='info'>132</span></td> <td align='right'>140</td> <td align='right'>10</td> <td align='right'>2</td> <td align='right'><span class='info'>30</span></td> <td align='right'>5,335</td> <td align='right'>0.00</td> <td align='right'>2</td> <td align='right'>8</td></tr><tr onmouseover="this.className='highlight'" onmouseout="this.className=''">
<td class='wrappedText'><a class='tooltip' href='?part=graph&amp;graph=spring0fb0de19d2c6997d7553f08c5641b09ed553016f' onmouseover="document.getElementById('id41').src='?graph=spring0fb0de19d2c6997d7553f08c5641b09ed553016f&amp;width=100&amp;height=50'; this.onmouseover=null;" ><em><img src='?resource=db.png' id='id41' alt='graph'/></em>SyncProcessDataToWorkManagerTask.checkAndExecute</a></td> <td align='right'>1</td> <td align='right'>2</td> <td align='right'><span class='info'>132</span></td> <td align='right'>140</td> <td align='right'>10</td> <td align='right'>2</td> <td align='right'><span class='info'>23</span></td> <td align='right'>3,165</td> <td align='right'>0.00</td> <td align='right'>2</td> <td align='right'>9</td></tr><tr class='odd' onmouseover="this.className='highlight'" onmouseout="this.className='odd'">
<td class='wrappedText'><a class='tooltip' href='?part=graph&amp;graph=spring8a74e50d146c4b9d8e879f871cf34cf92491b1e0' onmouseover="document.getElementById('id42').src='?graph=spring8a74e50d146c4b9d8e879f871cf34cf92491b1e0&amp;width=100&amp;height=50'; this.onmouseover=null;" ><em><img src='?resource=db.png' id='id42' alt='graph'/></em>AutoAddTodoTask.checkAndExecute</a></td> <td align='right'>1</td> <td align='right'>2</td> <td align='right'><span class='info'>123</span></td> <td align='right'>140</td> <td align='right'>23</td> <td align='right'>1</td> <td align='right'><span class='info'>15</span></td> <td align='right'>3,296</td> <td align='right'>0.00</td> <td align='right'>2</td> <td align='right'>9</td></tr><tr onmouseover="this.className='highlight'" onmouseout="this.className=''">
<td class='wrappedText'><a class='tooltip' href='?part=graph&amp;graph=springf009d8e9ea761603f40d052329d7792edefbeec7' onmouseover="document.getElementById('id43').src='?graph=springf009d8e9ea761603f40d052329d7792edefbeec7&amp;width=100&amp;height=50'; this.onmouseover=null;" ><em><img src='?resource=db.png' id='id43' alt='graph'/></em>SyncCommentDataToWorkManagerTask.checkAndExecute</a></td> <td align='right'>1</td> <td align='right'>2</td> <td align='right'><span class='info'>122</span></td> <td align='right'>140</td> <td align='right'>25</td> <td align='right'>2</td> <td align='right'><span class='info'>31</span></td> <td align='right'>3,816</td> <td align='right'>0.00</td> <td align='right'>2</td> <td align='right'>8</td></tr><tr class='odd' onmouseover="this.className='highlight'" onmouseout="this.className='odd'">
<td class='wrappedText'><a class='tooltip' href='?part=graph&amp;graph=spring160d7fd67c926decfb22fa2712b3a7085904b779' onmouseover="document.getElementById('id44').src='?graph=spring160d7fd67c926decfb22fa2712b3a7085904b779&amp;width=100&amp;height=50'; this.onmouseover=null;" ><em><img src='?resource=db.png' id='id44' alt='graph'/></em>AutoCloseTodoTask.checkAndExecute</a></td> <td align='right'>1</td> <td align='right'>2</td> <td align='right'><span class='info'>106</span></td> <td align='right'>140</td> <td align='right'>48</td> <td align='right'>1</td> <td align='right'><span class='info'>15</span></td> <td align='right'>3,354</td> <td align='right'>0.00</td> <td align='right'>2</td> <td align='right'>10</td></tr><tr onmouseover="this.className='highlight'" onmouseout="this.className=''">
<td class='wrappedText'><a class='tooltip' href='?part=graph&amp;graph=spring2967fd0c9041856ecd8f983c9ac73c7c59a6efcb' onmouseover="document.getElementById('id45').src='?graph=spring2967fd0c9041856ecd8f983c9ac73c7c59a6efcb&amp;width=100&amp;height=50'; this.onmouseover=null;" ><em><img src='?resource=db.png' id='id45' alt='graph'/></em>MyAuthService.checkUserAccessApp</a></td> <td align='right'>1</td> <td align='right'>1</td> <td align='right'><span class='info'>209</span></td> <td align='right'>209</td> <td align='right'>0</td> <td align='right'>1</td> <td align='right'><span class='info'>31</span></td> <td align='right'>144</td> <td align='right'>0.00</td> <td align='right'>0</td> <td align='right'>0</td></tr><tr class='odd' onmouseover="this.className='highlight'" onmouseout="this.className='odd'">
<td class='wrappedText'><a class='tooltip' href='?part=graph&amp;graph=spring6ce7fab74c4b885d6ba5304c1f20bfc46bb209e5' onmouseover="document.getElementById('id46').src='?graph=spring6ce7fab74c4b885d6ba5304c1f20bfc46bb209e5&amp;width=100&amp;height=50'; this.onmouseover=null;" ><em><img src='?resource=db.png' id='id46' alt='graph'/></em>SysOperateLogService.saveLog</a></td> <td align='right'>1</td> <td align='right'>2</td> <td align='right'><span class='info'>95</span></td> <td align='right'>131</td> <td align='right'>50</td> <td align='right'>2</td> <td align='right'><span class='info'>23</span></td> <td align='right'>4,277</td> <td align='right'>0.00</td> <td align='right'>1</td> <td align='right'>8</td></tr><tr onmouseover="this.className='highlight'" onmouseout="this.className=''">
<td class='wrappedText'><a class='tooltip' href='?part=graph&amp;graph=spring7f6e617d882c5b9ffd7bdda97632d931c2265c5a' onmouseover="document.getElementById('id47').src='?graph=spring7f6e617d882c5b9ffd7bdda97632d931c2265c5a&amp;width=100&amp;height=50'; this.onmouseover=null;" ><em><img src='?resource=db.png' id='id47' alt='graph'/></em>RabbitMqHeart.execute</a></td> <td align='right'>0</td> <td align='right'>7</td> <td align='right'><span class='info'>22</span></td> <td align='right'>45</td> <td align='right'>10</td> <td align='right'>0</td> <td align='right'><span class='info'>2</span></td> <td align='right'>298</td> <td align='right'>0.00</td> <td align='right'>0</td> <td align='right'>0</td></tr><tr class='odd' onmouseover="this.className='highlight'" onmouseout="this.className='odd'">
<td class='wrappedText'><a class='tooltip' href='?part=graph&amp;graph=spring185d9df1a228b1e43b07db33fdf35fa05c153535' onmouseover="document.getElementById('id48').src='?graph=spring185d9df1a228b1e43b07db33fdf35fa05c153535&amp;width=100&amp;height=50'; this.onmouseover=null;" ><em><img src='?resource=db.png' id='id48' alt='graph'/></em>SysFileController.download</a></td> <td align='right'>0</td> <td align='right'>2</td> <td align='right'><span class='info'>66</span></td> <td align='right'>67</td> <td align='right'>0</td> <td align='right'>5</td> <td align='right'><span class='info'>54</span></td> <td align='right'>1,417</td> <td align='right'>0.00</td> <td align='right'>2</td> <td align='right'>8</td></tr><tr onmouseover="this.className='highlight'" onmouseout="this.className=''">
<td class='wrappedText'><a class='tooltip' href='?part=graph&amp;graph=spring1b4ec793bf41057adb781285684b946805f991da' onmouseover="document.getElementById('id49').src='?graph=spring1b4ec793bf41057adb781285684b946805f991da&amp;width=100&amp;height=50'; this.onmouseover=null;" ><em><img src='?resource=db.png' id='id49' alt='graph'/></em>ActBusinessStatusService.getTodoByUserNamePage</a></td> <td align='right'>0</td> <td align='right'>2</td> <td align='right'><span class='info'>64</span></td> <td align='right'>96</td> <td align='right'>45</td> <td align='right'>2</td> <td align='right'><span class='info'>31</span></td> <td align='right'>5,005</td> <td align='right'>0.00</td> <td align='right'>1</td> <td align='right'>13</td></tr><tr class='odd' onmouseover="this.className='highlight'" onmouseout="this.className='odd'">
<td class='wrappedText'><a class='tooltip' href='?part=graph&amp;graph=spring1ce37d43389d49d16487b9f9e27b968c99cde415' onmouseover="document.getElementById('id50').src='?graph=spring1ce37d43389d49d16487b9f9e27b968c99cde415&amp;width=100&amp;height=50'; this.onmouseover=null;" ><em><img src='?resource=db.png' id='id50' alt='graph'/></em>SysDictValueService.findDictValue</a></td> <td align='right'>0</td> <td align='right'>2</td> <td align='right'><span class='info'>55</span></td> <td align='right'>106</td> <td align='right'>72</td> <td align='right'>3</td> <td align='right'><span class='info'>39</span></td> <td align='right'>7,486</td> <td align='right'>0.00</td> <td align='right'>0</td> <td align='right'>3</td></tr><tr onmouseover="this.className='highlight'" onmouseout="this.className=''">
<td class='wrappedText'><a class='tooltip' href='?part=graph&amp;graph=springe661d8463d82af557e7929b97c2b003cc43d931e' onmouseover="document.getElementById('id51').src='?graph=springe661d8463d82af557e7929b97c2b003cc43d931e&amp;width=100&amp;height=50'; this.onmouseover=null;" ><em><img src='?resource=db.png' id='id51' alt='graph'/></em>HeartTestTask.checkAndExecute</a></td> <td align='right'>0</td> <td align='right'>7</td> <td align='right'><span class='info'>15</span></td> <td align='right'>19</td> <td align='right'>4</td> <td align='right'>1</td> <td align='right'><span class='info'>4</span></td> <td align='right'>156</td> <td align='right'>0.00</td> <td align='right'>0</td> <td align='right'>0</td></tr><tr class='odd' onmouseover="this.className='highlight'" onmouseout="this.className='odd'">
<td class='wrappedText'><a class='tooltip' href='?part=graph&amp;graph=springfa4a8281fb35ce238917e8fadfd2b41dfe6622b0' onmouseover="document.getElementById('id52').src='?graph=springfa4a8281fb35ce238917e8fadfd2b41dfe6622b0&amp;width=100&amp;height=50'; this.onmouseover=null;" ><em><img src='?resource=db.png' id='id52' alt='graph'/></em>http://************:15672/api/nodes GET</a></td> <td align='right'>0</td> <td align='right'>9</td> <td align='right'><span class='info'>11</span></td> <td align='right'>24</td> <td align='right'>6</td> <td align='right'>1</td> <td align='right'><span class='info'>3</span></td> <td align='right'>54</td> <td align='right'>0.00</td> <td align='right'>0</td> <td align='right'>0</td></tr><tr onmouseover="this.className='highlight'" onmouseout="this.className=''">
<td class='wrappedText'><a class='tooltip' href='?part=graph&amp;graph=spring0465499b08b06d4c913520c71a66aab0afb0cc9e' onmouseover="document.getElementById('id53').src='?graph=spring0465499b08b06d4c913520c71a66aab0afb0cc9e&amp;width=100&amp;height=50'; this.onmouseover=null;" ><em><img src='?resource=db.png' id='id53' alt='graph'/></em>ProcessdataPushServiceImpl.syncTaskInstHisData</a></td> <td align='right'>0</td> <td align='right'>2</td> <td align='right'><span class='info'>45</span></td> <td align='right'>52</td> <td align='right'>9</td> <td align='right'>0</td> <td align='right'><span class='info'>0</span></td> <td align='right'>526</td> <td align='right'>0.00</td> <td align='right'>1</td> <td align='right'>7</td></tr><tr class='odd' onmouseover="this.className='highlight'" onmouseout="this.className='odd'">
<td class='wrappedText'><a class='tooltip' href='?part=graph&amp;graph=spring6ee90352436400745c74ea743c174b56ecff9792' onmouseover="document.getElementById('id54').src='?graph=spring6ee90352436400745c74ea743c174b56ecff9792&amp;width=100&amp;height=50'; this.onmouseover=null;" ><em><img src='?resource=db.png' id='id54' alt='graph'/></em>AutoCheckWithUpdateTaskServiceImpl.checkWorkEngineTaskStatus</a></td> <td align='right'>0</td> <td align='right'>2</td> <td align='right'><span class='info'>42</span></td> <td align='right'>60</td> <td align='right'>24</td> <td align='right'>2</td> <td align='right'><span class='info'>23</span></td> <td align='right'>2,577</td> <td align='right'>0.00</td> <td align='right'>1</td> <td align='right'>5</td></tr><tr onmouseover="this.className='highlight'" onmouseout="this.className=''">
<td class='wrappedText'><a class='tooltip' href='?part=graph&amp;graph=spring36623252eb858f975d67b6630d3dc2f415f45c63' onmouseover="document.getElementById('id55').src='?graph=spring36623252eb858f975d67b6630d3dc2f415f45c63&amp;width=100&amp;height=50'; this.onmouseover=null;" ><em><img src='?resource=db.png' id='id55' alt='graph'/></em>WfProcessErrorLogServiceImpl.checkWorkEngineTaskStatus</a></td> <td align='right'>0</td> <td align='right'>2</td> <td align='right'><span class='info'>40</span></td> <td align='right'>55</td> <td align='right'>21</td> <td align='right'>1</td> <td align='right'><span class='info'>15</span></td> <td align='right'>1,755</td> <td align='right'>0.00</td> <td align='right'>1</td> <td align='right'>5</td></tr><tr class='odd' onmouseover="this.className='highlight'" onmouseout="this.className='odd'">
<td class='wrappedText'><a class='tooltip' href='?part=graph&amp;graph=spring2a8d613a8ac1622773ad166f4b1d9abaa27550eb' onmouseover="document.getElementById('id56').src='?graph=spring2a8d613a8ac1622773ad166f4b1d9abaa27550eb&amp;width=100&amp;height=50'; this.onmouseover=null;" ><em><img src='?resource=db.png' id='id56' alt='graph'/></em>SysNewColumnController.findAllByUser</a></td> <td align='right'>0</td> <td align='right'>2</td> <td align='right'><span class='info'>37</span></td> <td align='right'>40</td> <td align='right'>4</td> <td align='right'>2</td> <td align='right'><span class='info'>23</span></td> <td align='right'>2,688</td> <td align='right'>0.00</td> <td align='right'>1</td> <td align='right'>2</td></tr><tr onmouseover="this.className='highlight'" onmouseout="this.className=''">
<td class='wrappedText'><a class='tooltip' href='?part=graph&amp;graph=spring4526f87d4dbad7564d1728825f0387aa540ea284' onmouseover="document.getElementById('id57').src='?graph=spring4526f87d4dbad7564d1728825f0387aa540ea284&amp;width=100&amp;height=50'; this.onmouseover=null;" ><em><img src='?resource=db.png' id='id57' alt='graph'/></em>SysNewColumnServiceImpl.findAllByUser</a></td> <td align='right'>0</td> <td align='right'>2</td> <td align='right'><span class='info'>34</span></td> <td align='right'>39</td> <td align='right'>6</td> <td align='right'>2</td> <td align='right'><span class='info'>23</span></td> <td align='right'>1,928</td> <td align='right'>0.00</td> <td align='right'>1</td> <td align='right'>2</td></tr><tr class='odd' onmouseover="this.className='highlight'" onmouseout="this.className='odd'">
<td class='wrappedText'><a class='tooltip' href='?part=graph&amp;graph=spring36bfe5b309d60f63d7523f97b58fdef318f9bb6e' onmouseover="document.getElementById('id58').src='?graph=spring36bfe5b309d60f63d7523f97b58fdef318f9bb6e&amp;width=100&amp;height=50'; this.onmouseover=null;" ><em><img src='?resource=db.png' id='id58' alt='graph'/></em>MyAuthService.findByKey</a></td> <td align='right'>0</td> <td align='right'>1</td> <td align='right'><span class='info'>69</span></td> <td align='right'>69</td> <td align='right'>0</td> <td align='right'>2</td> <td align='right'><span class='info'>46</span></td> <td align='right'>7,094</td> <td align='right'>0.00</td> <td align='right'>0</td> <td align='right'>0</td></tr><tr onmouseover="this.className='highlight'" onmouseout="this.className=''">
<td class='wrappedText'><a class='tooltip' href='?part=graph&amp;graph=spring5a6e4a9882aeebbe975d84ef11e240298a720622' onmouseover="document.getElementById('id59').src='?graph=spring5a6e4a9882aeebbe975d84ef11e240298a720622&amp;width=100&amp;height=50'; this.onmouseover=null;" ><em><img src='?resource=db.png' id='id59' alt='graph'/></em>ProcessdataPushServiceImpl.syncProcessInstHisData</a></td> <td align='right'>0</td> <td align='right'>2</td> <td align='right'><span class='info'>33</span></td> <td align='right'>40</td> <td align='right'>9</td> <td align='right'>0</td> <td align='right'><span class='info'>7</span></td> <td align='right'>250</td> <td align='right'>0.00</td> <td align='right'>1</td> <td align='right'>7</td></tr><tr class='odd' onmouseover="this.className='highlight'" onmouseout="this.className='odd'">
<td class='wrappedText'><a class='tooltip' href='?part=graph&amp;graph=springb5c206bacd840538b0f30e95cc333aecca19c225' onmouseover="document.getElementById('id60').src='?graph=springb5c206bacd840538b0f30e95cc333aecca19c225&amp;width=100&amp;height=50'; this.onmouseover=null;" ><em><img src='?resource=db.png' id='id60' alt='graph'/></em>AutoAddAndCloseTodoServiceImpl.queryAddTodos</a></td> <td align='right'>0</td> <td align='right'>2</td> <td align='right'><span class='info'>30</span></td> <td align='right'>43</td> <td align='right'>18</td> <td align='right'>0</td> <td align='right'><span class='info'>7</span></td> <td align='right'>555</td> <td align='right'>0.00</td> <td align='right'>1</td> <td align='right'>7</td></tr><tr onmouseover="this.className='highlight'" onmouseout="this.className=''">
<td class='wrappedText'><a class='tooltip' href='?part=graph&amp;graph=springa21d4616716b75488c9337a70d313094c609bfd4' onmouseover="document.getElementById('id61').src='?graph=springa21d4616716b75488c9337a70d313094c609bfd4&amp;width=100&amp;height=50'; this.onmouseover=null;" ><em><img src='?resource=db.png' id='id61' alt='graph'/></em>ProcessdataPushServiceImpl.syncCommentHisData</a></td> <td align='right'>0</td> <td align='right'>2</td> <td align='right'><span class='info'>28</span></td> <td align='right'>38</td> <td align='right'>14</td> <td align='right'>0</td> <td align='right'><span class='info'>7</span></td> <td align='right'>1,013</td> <td align='right'>0.00</td> <td align='right'>1</td> <td align='right'>7</td></tr><tr class='odd' onmouseover="this.className='highlight'" onmouseout="this.className='odd'">
<td class='wrappedText'><a class='tooltip' href='?part=graph&amp;graph=springe1f6a57abfcd230105a80852b46c362199f9df60' onmouseover="document.getElementById('id62').src='?graph=springe1f6a57abfcd230105a80852b46c362199f9df60&amp;width=100&amp;height=50'; this.onmouseover=null;" ><em><img src='?resource=db.png' id='id62' alt='graph'/></em>AutoAddAndCloseTodoServiceImpl.queryCloseTodos</a></td> <td align='right'>0</td> <td align='right'>2</td> <td align='right'><span class='info'>25</span></td> <td align='right'>42</td> <td align='right'>23</td> <td align='right'>0</td> <td align='right'><span class='info'>0</span></td> <td align='right'>588</td> <td align='right'>0.00</td> <td align='right'>1</td> <td align='right'>5</td></tr><tr onmouseover="this.className='highlight'" onmouseout="this.className=''">
<td class='wrappedText'><a class='tooltip' href='?part=graph&amp;graph=spring9ebfd91037865a3ee705d26fbdfa230195fb5ea3' onmouseover="document.getElementById('id63').src='?graph=spring9ebfd91037865a3ee705d26fbdfa230195fb5ea3&amp;width=100&amp;height=50'; this.onmouseover=null;" ><em><img src='?resource=db.png' id='id63' alt='graph'/></em>SysFileService.findById</a></td> <td align='right'>0</td> <td align='right'>2</td> <td align='right'><span class='info'>24</span></td> <td align='right'>26</td> <td align='right'>2</td> <td align='right'>2</td> <td align='right'><span class='info'>23</span></td> <td align='right'>673</td> <td align='right'>0.00</td> <td align='right'>1</td> <td align='right'>4</td></tr><tr class='odd' onmouseover="this.className='highlight'" onmouseout="this.className='odd'">
<td class='wrappedText'><a class='tooltip' href='?part=graph&amp;graph=spring8b522d9bd8cf42de5f6cb11be921dfd2d5ccf854' onmouseover="document.getElementById('id64').src='?graph=spring8b522d9bd8cf42de5f6cb11be921dfd2d5ccf854&amp;width=100&amp;height=50'; this.onmouseover=null;" ><em><img src='?resource=db.png' id='id64' alt='graph'/></em>SysFileService.getRealFileById</a></td> <td align='right'>0</td> <td align='right'>2</td> <td align='right'><span class='info'>19</span></td> <td align='right'>23</td> <td align='right'>4</td> <td align='right'>1</td> <td align='right'><span class='info'>15</span></td> <td align='right'>337</td> <td align='right'>0.00</td> <td align='right'>1</td> <td align='right'>4</td></tr><tr onmouseover="this.className='highlight'" onmouseout="this.className=''">
<td class='wrappedText'><a class='tooltip' href='?part=graph&amp;graph=springc13aab2f4f9fa110d259b8dd03e7be9eaf311abe' onmouseover="document.getElementById('id65').src='?graph=springc13aab2f4f9fa110d259b8dd03e7be9eaf311abe&amp;width=100&amp;height=50'; this.onmouseover=null;" ><em><img src='?resource=db.png' id='id65' alt='graph'/></em>http://************:8088/uums/action/user/user/findPermissionByAppUser/sso POST</a></td> <td align='right'>0</td> <td align='right'>3</td> <td align='right'><span class='info'>11</span></td> <td align='right'>19</td> <td align='right'>7</td> <td align='right'>0</td> <td align='right'><span class='info'>0</span></td> <td align='right'>32</td> <td align='right'>0.00</td> <td align='right'>0</td> <td align='right'>0</td></tr><tr class='odd' onmouseover="this.className='highlight'" onmouseout="this.className='odd'">
<td class='wrappedText'><a class='tooltip' href='?part=graph&amp;graph=spring85949bc7fe3ae78a911111d13505f1fbcead17fe' onmouseover="document.getElementById('id66').src='?graph=spring85949bc7fe3ae78a911111d13505f1fbcead17fe&amp;width=100&amp;height=50'; this.onmouseover=null;" ><em><img src='?resource=db.png' id='id66' alt='graph'/></em>A4LogConfigServiceImpl.queryAuditDateAll</a></td> <td align='right'>0</td> <td align='right'>1</td> <td align='right'><span class='info'>28</span></td> <td align='right'>28</td> <td align='right'>0</td> <td align='right'>0</td> <td align='right'><span class='info'>15</span></td> <td align='right'>5,412</td> <td align='right'>0.00</td> <td align='right'>1</td> <td align='right'>2</td></tr><tr onmouseover="this.className='highlight'" onmouseout="this.className=''">
<td class='wrappedText'><a class='tooltip' href='?part=graph&amp;graph=springa7cdbbb0a9c41207cc27c3c854e9f2b6e597bc00' onmouseover="document.getElementById('id67').src='?graph=springa7cdbbb0a9c41207cc27c3c854e9f2b6e597bc00&amp;width=100&amp;height=50'; this.onmouseover=null;" ><em><img src='?resource=db.png' id='id67' alt='graph'/></em>A4PasswordCheck.shouldCheckFrom4A</a></td> <td align='right'>0</td> <td align='right'>1</td> <td align='right'><span class='info'>26</span></td> <td align='right'>26</td> <td align='right'>0</td> <td align='right'>0</td> <td align='right'><span class='info'>0</span></td> <td align='right'>834</td> <td align='right'>0.00</td> <td align='right'>0</td> <td align='right'>0</td></tr><tr class='odd' onmouseover="this.className='highlight'" onmouseout="this.className='odd'">
<td class='wrappedText'><a class='tooltip' href='?part=graph&amp;graph=spring1686870c20ef3136367d89d98209e77c959ec6b9' onmouseover="document.getElementById('id68').src='?graph=spring1686870c20ef3136367d89d98209e77c959ec6b9&amp;width=100&amp;height=50'; this.onmouseover=null;" ><em><img src='?resource=db.png' id='id68' alt='graph'/></em>http://************:8088/uums/action/user/user/checkUserAccessApp/sso POST</a></td> <td align='right'>0</td> <td align='right'>1</td> <td align='right'><span class='info'>12</span></td> <td align='right'>12</td> <td align='right'>0</td> <td align='right'>0</td> <td align='right'><span class='info'>0</span></td> <td align='right'>24</td> <td align='right'>0.00</td> <td align='right'>0</td> <td align='right'>0</td></tr><tr onmouseover="this.className='highlight'" onmouseout="this.className=''">
<td class='wrappedText'><a class='tooltip' href='?part=graph&amp;graph=spring6787de476256a7b19e59fe4df242f5441e5da228' onmouseover="document.getElementById('id69').src='?graph=spring6787de476256a7b19e59fe4df242f5441e5da228&amp;width=100&amp;height=50'; this.onmouseover=null;" ><em><img src='?resource=db.png' id='id69' alt='graph'/></em>http://************:8088/uums/action/a4/a4switch/getA4switchValue/anonymous POST</a></td> <td align='right'>0</td> <td align='right'>1</td> <td align='right'><span class='info'>6</span></td> <td align='right'>6</td> <td align='right'>0</td> <td align='right'>0</td> <td align='right'><span class='info'>0</span></td> <td align='right'>47</td> <td align='right'>0.00</td> <td align='right'>0</td> <td align='right'>0</td></tr><tr class='odd' onmouseover="this.className='highlight'" onmouseout="this.className='odd'">
<td class='wrappedText'><a class='tooltip' href='?part=graph&amp;graph=springda82ab418e68da1240860799e0aaf8a10c45bb16' onmouseover="document.getElementById('id70').src='?graph=springda82ab418e68da1240860799e0aaf8a10c45bb16&amp;width=100&amp;height=50'; this.onmouseover=null;" ><em><img src='?resource=db.png' id='id70' alt='graph'/></em>http://************:8088/uums/httpauth/validate POST</a></td> <td align='right'>0</td> <td align='right'>1</td> <td align='right'><span class='info'>6</span></td> <td align='right'>6</td> <td align='right'>0</td> <td align='right'>0</td> <td align='right'><span class='info'>0</span></td> <td align='right'>24</td> <td align='right'>0.00</td> <td align='right'>0</td> <td align='right'>0</td></tr><tr onmouseover="this.className='highlight'" onmouseout="this.className=''">
<td class='wrappedText'><a class='tooltip' href='?part=graph&amp;graph=springfe4a93a008d33683e5bcbe2e59b8daf8b8603bd0' onmouseover="document.getElementById('id71').src='?graph=springfe4a93a008d33683e5bcbe2e59b8daf8b8603bd0&amp;width=100&amp;height=50'; this.onmouseover=null;" ><em><img src='?resource=db.png' id='id71' alt='graph'/></em>http://************:8088/uums/action/app/config/findAppConfigByStyle/sso POST</a></td> <td align='right'>0</td> <td align='right'>2</td> <td align='right'><span class='info'>2</span></td> <td align='right'>3</td> <td align='right'>0</td> <td align='right'>0</td> <td align='right'><span class='info'>0</span></td> <td align='right'>153</td> <td align='right'>0.00</td> <td align='right'>0</td> <td align='right'>0</td></tr><tr class='odd' onmouseover="this.className='highlight'" onmouseout="this.className='odd'">
<td class='wrappedText'><a class='tooltip' href='?part=graph&amp;graph=spring15ebb042c63e8bb4ac2c2fe94daf3d1d0c8670f7' onmouseover="document.getElementById('id72').src='?graph=spring15ebb042c63e8bb4ac2c2fe94daf3d1d0c8670f7&amp;width=100&amp;height=50'; this.onmouseover=null;" ><em><img src='?resource=db.png' id='id72' alt='graph'/></em>WorkFlowBpsLoginManager.bpsLogin</a></td> <td align='right'>0</td> <td align='right'>2</td> <td align='right'><span class='info'>2</span></td> <td align='right'>4</td> <td align='right'>2</td> <td align='right'>0</td> <td align='right'><span class='info'>0</span></td> <td align='right'>319</td> <td align='right'>0.00</td> <td align='right'>0</td> <td align='right'>0</td></tr><tr onmouseover="this.className='highlight'" onmouseout="this.className=''">
<td class='wrappedText'><a class='tooltip' href='?part=graph&amp;graph=spring7b806d6777bde10adc39b94302abe9aaffddb96a' onmouseover="document.getElementById('id73').src='?graph=spring7b806d6777bde10adc39b94302abe9aaffddb96a&amp;width=100&amp;height=50'; this.onmouseover=null;" ><em><img src='?resource=db.png' id='id73' alt='graph'/></em>IndexController.home</a></td> <td align='right'>0</td> <td align='right'>1</td> <td align='right'><span class='info'>3</span></td> <td align='right'>3</td> <td align='right'>0</td> <td align='right'>0</td> <td align='right'><span class='info'>0</span></td> <td align='right'>990</td> <td align='right'>0.00</td> <td align='right'>0</td> <td align='right'>0</td></tr><tr class='odd' onmouseover="this.className='highlight'" onmouseout="this.className='odd'">
<td class='wrappedText'><a class='tooltip' href='?part=graph&amp;graph=spring9b91476eebb87da42edeb2e8bf9e1e243f2e550b' onmouseover="document.getElementById('id74').src='?graph=spring9b91476eebb87da42edeb2e8bf9e1e243f2e550b&amp;width=100&amp;height=50'; this.onmouseover=null;" ><em><img src='?resource=db.png' id='id74' alt='graph'/></em>LoginController.login</a></td> <td align='right'>0</td> <td align='right'>1</td> <td align='right'><span class='info'>2</span></td> <td align='right'>2</td> <td align='right'>0</td> <td align='right'>0</td> <td align='right'><span class='info'>0</span></td> <td align='right'>637</td> <td align='right'>0.00</td> <td align='right'>0</td> <td align='right'>0</td></tr><tr onmouseover="this.className='highlight'" onmouseout="this.className=''">
<td class='wrappedText'><a class='tooltip' href='?part=graph&amp;graph=spring246e359e189fcae8eeb3bf52f7f896bb380df6b9' onmouseover="document.getElementById('id75').src='?graph=spring246e359e189fcae8eeb3bf52f7f896bb380df6b9&amp;width=100&amp;height=50'; this.onmouseover=null;" ><em><img src='?resource=db.png' id='id75' alt='graph'/></em>IndexController.index</a></td> <td align='right'>0</td> <td align='right'>2</td> <td align='right'><span class='info'>0</span></td> <td align='right'>0</td> <td align='right'>0</td> <td align='right'>0</td> <td align='right'><span class='info'>0</span></td> <td align='right'>2</td> <td align='right'>0.00</td> <td align='right'>0</td> <td align='right'>0</td></tr><tr class='odd' onmouseover="this.className='highlight'" onmouseout="this.className='odd'">
<td class='wrappedText'><a class='tooltip' href='?part=graph&amp;graph=springa38ecf484e3d95c1a3dd0b3ea61b09a504f1afd8' onmouseover="document.getElementById('id76').src='?graph=springa38ecf484e3d95c1a3dd0b3ea61b09a504f1afd8&amp;width=100&amp;height=50'; this.onmouseover=null;" ><em><img src='?resource=db.png' id='id76' alt='graph'/></em>MyAuthService.customUserForApp</a></td> <td align='right'>0</td> <td align='right'>1</td> <td align='right'><span class='info'>0</span></td> <td align='right'>0</td> <td align='right'>0</td> <td align='right'>0</td> <td align='right'><span class='info'>0</span></td> <td align='right'>2</td> <td align='right'>0.00</td> <td align='right'>0</td> <td align='right'>0</td></tr><tr onmouseover="this.className='highlight'" onmouseout="this.className=''">
<td class='wrappedText'><a class='tooltip' href='?part=graph&amp;graph=spring7166716602a7081684c513dab6bc07af12aa8496' onmouseover="document.getElementById('id77').src='?graph=spring7166716602a7081684c513dab6bc07af12aa8496&amp;width=100&amp;height=50'; this.onmouseover=null;" ><em><img src='?resource=db.png' id='id77' alt='graph'/></em>IndexController.getCurrentUser</a></td> <td align='right'>0</td> <td align='right'>2</td> <td align='right'><span class='info'>0</span></td> <td align='right'>0</td> <td align='right'>0</td> <td align='right'>0</td> <td align='right'><span class='info'>0</span></td> <td align='right'>1</td> <td align='right'>0.00</td> <td align='right'>0</td> <td align='right'>0</td></tr></tbody></table>
</div>
<h3 class='chapterTitle'><img src='?resource=error.png' alt='error'/>
<a name='error'></a>图表http 系统错误 - 1 天</h3>
空
<h3 class='chapterTitle'><img src='?resource=log.png' alt='log'/>
<a name='log'></a>图表系统错误日志 - 1 天</h3>
<table class='sortable' width='100%' border='1' summary='log'>
<thead><tr><th>错误</th><th class='sorttable_numeric'>点击</th></tr></thead><tbody>
<tr onmouseover="this.className='highlight'" onmouseout="this.className=''">
<td class='wrappedText'><a href='?part=graph&amp;graph=logda8241bea01de3bda1585ce315a713ddc9094e8c'>WARN  com.simbest.boot.util.AppFileUtil - 文件URL路径【组 2.png】无法通过URL获取路径，直接通过工具类提取文件后缀为【png】
<br/></a></td> <td align='right'>6</td></tr></tbody></table>
<div align='right'>
 2 使用每分钟在 10 错误
&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;
<a href="javascript:showHide('detailslog');" class='noPrint'><img id='detailslogImg' src='?resource=bullets/plus.png' alt=''/> 描述</a>
&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;
<a href="javascript:showHide('logslog');" class='noPrint'><img id='logslogImg' src='?resource=bullets/plus.png' alt=''/> 最后的错误</a>
&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;
</div>
<div id='detailslog' style='display: none;'>
<table class='sortable' width='100%' border='1' summary='log'>
<thead><tr><th>错误</th><th class='sorttable_numeric'>点击</th></tr></thead><tbody>
<tr onmouseover="this.className='highlight'" onmouseout="this.className=''">
<td class='wrappedText'><a href='?part=graph&amp;graph=logda8241bea01de3bda1585ce315a713ddc9094e8c'>WARN  com.simbest.boot.util.AppFileUtil - 文件URL路径【组 2.png】无法通过URL获取路径，直接通过工具类提取文件后缀为【png】
<br/></a></td> <td align='right'>6</td></tr><tr class='odd' onmouseover="this.className='highlight'" onmouseout="this.className='odd'">
<td class='wrappedText'><a href='?part=graph&amp;graph=log136081f9122bade61d98108c386accb2cc51d5c8'>WARN  com.simbest.boot.util.AppFileUtil - 文件URL路径【组 2.png】无法通过URL获取路径，直接通过工具类提取文件名称为【组 2】
<br/></a></td> <td align='right'>2</td></tr><tr onmouseover="this.className='highlight'" onmouseout="this.className=''">
<td class='wrappedText'><a href='?part=graph&amp;graph=logb9141948de81b9a5f85627c28d26966d7bc26050'>WARN  c.s.b.b.r.CustomDynamicWhere - 自定义查询SQL输出为：【select * from US_TODO_MODEL t  where t.send_flag = 0 and t.work_flag = 1 and t.enabled =1】
<br/></a></td> <td align='right'>2</td></tr><tr class='odd' onmouseover="this.className='highlight'" onmouseout="this.className='odd'">
<td class='wrappedText'><a href='?part=graph&amp;graph=log58023c2da6cff657b1cac5c43e1762ffa3a87ad5'>WARN  c.s.b.b.r.CustomDynamicWhere - 自定义查询SQL输出为：【select * from US_TODO_MODEL t  where t.send_flag = 0 and t.work_flag = 0 and t.enabled =1】
<br/></a></td> <td align='right'>2</td></tr><tr onmouseover="this.className='highlight'" onmouseout="this.className=''">
<td class='wrappedText'><a href='?part=graph&amp;graph=log2aed1abf7a5adf0f75e23a5dbe155aba81ccda9b'>WARN  com.simbest.boot.util.AppFileUtil - 文件URL路径【组 2.png】无法通过URL获取路径，直接通过工具类提取文件全称为【组 2.png】
<br/></a></td> <td align='right'>2</td></tr><tr class='odd' onmouseover="this.className='highlight'" onmouseout="this.className='odd'">
<td class='wrappedText'><a href='?part=graph&amp;graph=log02a37ef6a92873384e4bcdf3336101541bb4d8e6'>WARN  com.simbest.boot.SimbestApplication - Application started successfully, lets go and have fun......
<br/></a></td> <td align='right'>1</td></tr><tr onmouseover="this.className='highlight'" onmouseout="this.className=''">
<td class='wrappedText'><a href='?part=graph&amp;graph=log38365cab4010d2ca00d9d7a3a64455d39fac09ab'>WARN  com.simbest.boot.SimbestApplication - 加载环境信息为: 【obuat】
<br/></a></td> <td align='right'>1</td></tr><tr class='odd' onmouseover="this.className='highlight'" onmouseout="this.className='odd'">
<td class='wrappedText'><a href='?part=graph&amp;graph=log54ec70c07118c3680c84cedf95bd1d0bba51eecc'>WARN  c.s.boot.component.GracefulShutdown - executor非ThreadPoolExecutor，具体类型为【org.apache.tomcat.util.threads.ThreadPoolExecutor】
<br/></a></td> <td align='right'>1</td></tr><tr onmouseover="this.className='highlight'" onmouseout="this.className=''">
<td class='wrappedText'><a href='?part=graph&amp;graph=logc625ae11dd7cf7893f0352e63fb3f2f3be326f8f'>WARN  com.simbest.boot.SimbestApplication - <br/>---------------------------------------------------------<br/>	应用已成功启动，运行地址如下：:<br/>	Local:		http://localhost:8092/hnjjwz<br/>	External:	http://***********:8092/hnjjwz<br/>Aplication started successfully, lets go and have fun......<br/>---------------------------------------------------------<br/>
<br/></a></td> <td align='right'>1</td></tr><tr class='odd' onmouseover="this.className='highlight'" onmouseout="this.className='odd'">
<td class='wrappedText'><a href='?part=graph&amp;graph=log7d3549c92f0bc9356fab89a93da9c3d1848e4032'>ERROR c.s.boot.base.exception.Exceptions - java.io.IOException: Unable to delete file: E:\晟壁\党风廉政视窗\simbest-boot-hnjjwz\springboottmp\hnjjwz\hnjjwz_DESKTOP-HUIVSCK\javamelody.lock
<br/>	at org.apache.commons.io.FileUtils.forceDelete(FileUtils.java:2400)
<br/>	at org.apache.commons.io.FileUtils.cleanDirectory(FileUtils.java:1721)
<br/>	at org.apache.commons.io.FileUtils.deleteDirectory(FileUtils.java:1617)
<br/>	at org.apache.commons.io.FileUtils.forceDelete(FileUtils.java:2391)
<br/>	at org.apache.commons.io.FileUtils.cleanDirectory(FileUtils.java:1721)
<br/>	at com.simbest.boot.component.GracefulShutdown.onApplicationEvent(GracefulShutdown.java:166)
<br/>	at com.simbest.boot.component.GracefulShutdown.onApplicationEvent(GracefulShutdown.java:46)
<br/>	at org.springframework.context.event.SimpleApplicationEventMulticaster.doInvokeListener(SimpleApplicationEventMulticaster.java:172)
<br/>	at org.springframework.context.event.SimpleApplicationEventMulticaster.invokeListener(SimpleApplicationEventMulticaster.java:165)
<br/>	at org.springframework.context.event.SimpleApplicationEventMulticaster.multicastEvent(SimpleApplicationEventMulticaster.java:139)
<br/>	at org.springframework.context.support.AbstractApplicationContext.publishEvent(AbstractApplicationContext.java:403)
<br/>	at org.springframework.context.support.AbstractApplicationContext.publishEvent(AbstractApplicationContext.java:360)
<br/>	at org.springframework.context.support.AbstractApplicationContext.doClose(AbstractApplicationContext.java:1012)
<br/>	at org.springframework.context.support.AbstractApplicationContext.close(AbstractApplicationContext.java:978)
<br/>	at org.springframework.boot.builder.ParentContextCloserApplicationListener$ContextCloserListener.onApplicationEvent(ParentContextCloserApplicationListener.java:94)
<br/>	at org.springframework.boot.builder.ParentContextCloserApplicationListener$ContextCloserListener.onApplicationEvent(ParentContextCloserApplicationListener.java:82)
<br/>	at org.springframework.context.event.SimpleApplicationEventMulticaster.doInvokeListener(SimpleApplicationEventMulticaster.java:172)
<br/>	at org.springframework.context.event.SimpleApplicationEventMulticaster.invokeListener(SimpleApplicationEventMulticaster.java:165)
<br/>	at org.springframework.context.event.SimpleApplicationEventMulticaster.multicastEvent(SimpleApplicationEventMulticaster.java:139)
<br/>	at org.springframework.context.support.AbstractApplicationContext.publishEvent(AbstractApplicationContext.java:403)
<br/>	at org.springframework.context.support.AbstractApplicationContext.publishEvent(AbstractApplicationContext.java:360)
<br/>	at org.springframework.context.support.AbstractApplicationContext.doClose(AbstractApplicationContext.java:1012)
<br/>	at org.springframework.context.support.AbstractApplicationContext.close(AbstractApplicationContext.java:978)
<br/>	at org.springframework.boot.devtools.restart.Restarter.stop(Restarter.java:309)
<br/>	at org.springframework.boot.devtools.restart.Restarter.lambda$restart$1(Restarter.java:251)
<br/>	at org.springframework.boot.devtools.restart.Restarter$LeakSafeThread.run(Restarter.java:629)
<br/>
<br/></a></td> <td align='right'>1</td></tr></tbody></table>
</div>
<div id='logslog' style='display: none;'><div>
<table class='sortable' width='100%' border='1' summary='log'>
<thead><tr><th class='sorttable_date'>日期</th><th>请求</th><th>用户</th><th>错误</th></tr></thead><tbody>
<tr onmouseover="this.className='highlight'" onmouseout="this.className=''">
<td align='right'>25-6-4 14:08:17</td><td class='wrappedText'>&nbsp;</td><td class='wrappedText'>&nbsp;</td><td class='wrappedText'>WARN&nbsp;&nbsp;com.simbest.boot.SimbestApplication&nbsp;-&nbsp;加载环境信息为:&nbsp;【obuat】
<br/></td></tr><tr class='odd' onmouseover="this.className='highlight'" onmouseout="this.className='odd'">
<td align='right'>25-6-4 14:08:17</td><td class='wrappedText'>&nbsp;</td><td class='wrappedText'>&nbsp;</td><td class='wrappedText'>WARN&nbsp;&nbsp;com.simbest.boot.SimbestApplication&nbsp;-&nbsp;Application&nbsp;started&nbsp;successfully,&nbsp;lets&nbsp;go&nbsp;and&nbsp;have&nbsp;fun......
<br/></td></tr><tr onmouseover="this.className='highlight'" onmouseout="this.className=''">
<td align='right'>25-6-4 14:08:17</td><td class='wrappedText'>&nbsp;</td><td class='wrappedText'>&nbsp;</td><td class='wrappedText'>WARN&nbsp;&nbsp;com.simbest.boot.SimbestApplication&nbsp;-&nbsp;<br/>---------------------------------------------------------<br/>	应用已成功启动，运行地址如下：:<br/>	Local:		http://localhost:8092/hnjjwz<br/>	External:	http://***********:8092/hnjjwz<br/>Aplication&nbsp;started&nbsp;successfully,&nbsp;lets&nbsp;go&nbsp;and&nbsp;have&nbsp;fun......<br/>---------------------------------------------------------<br/>
<br/></td></tr><tr class='odd' onmouseover="this.className='highlight'" onmouseout="this.className='odd'">
<td align='right'>25-6-4 14:10:00</td><td class='wrappedText'>&nbsp;</td><td class='wrappedText'>&nbsp;</td><td class='wrappedText'>WARN&nbsp;&nbsp;c.s.b.b.r.CustomDynamicWhere&nbsp;-&nbsp;自定义查询SQL输出为：【select&nbsp;*&nbsp;from&nbsp;US_TODO_MODEL&nbsp;t&nbsp;&nbsp;where&nbsp;t.send_flag&nbsp;=&nbsp;0&nbsp;and&nbsp;t.work_flag&nbsp;=&nbsp;1&nbsp;and&nbsp;t.enabled&nbsp;=1】
<br/></td></tr><tr onmouseover="this.className='highlight'" onmouseout="this.className=''">
<td align='right'>25-6-4 14:10:00</td><td class='wrappedText'>&nbsp;</td><td class='wrappedText'>&nbsp;</td><td class='wrappedText'>WARN&nbsp;&nbsp;c.s.b.b.r.CustomDynamicWhere&nbsp;-&nbsp;自定义查询SQL输出为：【select&nbsp;*&nbsp;from&nbsp;US_TODO_MODEL&nbsp;t&nbsp;&nbsp;where&nbsp;t.send_flag&nbsp;=&nbsp;0&nbsp;and&nbsp;t.work_flag&nbsp;=&nbsp;0&nbsp;and&nbsp;t.enabled&nbsp;=1】
<br/></td></tr><tr class='odd' onmouseover="this.className='highlight'" onmouseout="this.className='odd'">
<td align='right'>25-6-4 14:10:03</td><td class='wrappedText'>/sys/file/uploadProcessFiles?pmInsType=slideShowType&amp;pmInsTypePart=1&nbsp;POST</td><td class='wrappedText'>chenhong</td><td class='wrappedText'>WARN&nbsp;&nbsp;com.simbest.boot.util.AppFileUtil&nbsp;-&nbsp;文件URL路径【组&nbsp;2.png】无法通过URL获取路径，直接通过工具类提取文件全称为【组&nbsp;2.png】
<br/></td></tr><tr onmouseover="this.className='highlight'" onmouseout="this.className=''">
<td align='right'>25-6-4 14:10:03</td><td class='wrappedText'>/sys/file/uploadProcessFiles?pmInsType=slideShowType&amp;pmInsTypePart=1&nbsp;POST</td><td class='wrappedText'>chenhong</td><td class='wrappedText'>WARN&nbsp;&nbsp;com.simbest.boot.util.AppFileUtil&nbsp;-&nbsp;文件URL路径【组&nbsp;2.png】无法通过URL获取路径，直接通过工具类提取文件后缀为【png】
<br/></td></tr><tr class='odd' onmouseover="this.className='highlight'" onmouseout="this.className='odd'">
<td align='right'>25-6-4 14:10:03</td><td class='wrappedText'>/sys/file/uploadProcessFiles?pmInsType=slideShowType&amp;pmInsTypePart=1&nbsp;POST</td><td class='wrappedText'>chenhong</td><td class='wrappedText'>WARN&nbsp;&nbsp;com.simbest.boot.util.AppFileUtil&nbsp;-&nbsp;文件URL路径【组&nbsp;2.png】无法通过URL获取路径，直接通过工具类提取文件名称为【组&nbsp;2】
<br/></td></tr><tr onmouseover="this.className='highlight'" onmouseout="this.className=''">
<td align='right'>25-6-4 14:10:03</td><td class='wrappedText'>/sys/file/uploadProcessFiles?pmInsType=slideShowType&amp;pmInsTypePart=1&nbsp;POST</td><td class='wrappedText'>chenhong</td><td class='wrappedText'>WARN&nbsp;&nbsp;com.simbest.boot.util.AppFileUtil&nbsp;-&nbsp;文件URL路径【组&nbsp;2.png】无法通过URL获取路径，直接通过工具类提取文件后缀为【png】
<br/></td></tr><tr class='odd' onmouseover="this.className='highlight'" onmouseout="this.className='odd'">
<td align='right'>25-6-4 14:10:03</td><td class='wrappedText'>/sys/file/uploadProcessFiles?pmInsType=slideShowType&amp;pmInsTypePart=1&nbsp;POST</td><td class='wrappedText'>chenhong</td><td class='wrappedText'>WARN&nbsp;&nbsp;com.simbest.boot.util.AppFileUtil&nbsp;-&nbsp;文件URL路径【组&nbsp;2.png】无法通过URL获取路径，直接通过工具类提取文件后缀为【png】
<br/></td></tr><tr onmouseover="this.className='highlight'" onmouseout="this.className=''">
<td align='right'>25-6-4 14:13:02</td><td class='wrappedText'>/sys/file/uploadProcessFiles?pmInsType=slideShowType&amp;pmInsTypePart=1&nbsp;POST</td><td class='wrappedText'>chenhong</td><td class='wrappedText'>WARN&nbsp;&nbsp;com.simbest.boot.util.AppFileUtil&nbsp;-&nbsp;文件URL路径【组&nbsp;2.png】无法通过URL获取路径，直接通过工具类提取文件全称为【组&nbsp;2.png】
<br/></td></tr><tr class='odd' onmouseover="this.className='highlight'" onmouseout="this.className='odd'">
<td align='right'>25-6-4 14:13:02</td><td class='wrappedText'>/sys/file/uploadProcessFiles?pmInsType=slideShowType&amp;pmInsTypePart=1&nbsp;POST</td><td class='wrappedText'>chenhong</td><td class='wrappedText'>WARN&nbsp;&nbsp;com.simbest.boot.util.AppFileUtil&nbsp;-&nbsp;文件URL路径【组&nbsp;2.png】无法通过URL获取路径，直接通过工具类提取文件后缀为【png】
<br/></td></tr><tr onmouseover="this.className='highlight'" onmouseout="this.className=''">
<td align='right'>25-6-4 14:13:02</td><td class='wrappedText'>/sys/file/uploadProcessFiles?pmInsType=slideShowType&amp;pmInsTypePart=1&nbsp;POST</td><td class='wrappedText'>chenhong</td><td class='wrappedText'>WARN&nbsp;&nbsp;com.simbest.boot.util.AppFileUtil&nbsp;-&nbsp;文件URL路径【组&nbsp;2.png】无法通过URL获取路径，直接通过工具类提取文件名称为【组&nbsp;2】
<br/></td></tr><tr class='odd' onmouseover="this.className='highlight'" onmouseout="this.className='odd'">
<td align='right'>25-6-4 14:13:02</td><td class='wrappedText'>/sys/file/uploadProcessFiles?pmInsType=slideShowType&amp;pmInsTypePart=1&nbsp;POST</td><td class='wrappedText'>chenhong</td><td class='wrappedText'>WARN&nbsp;&nbsp;com.simbest.boot.util.AppFileUtil&nbsp;-&nbsp;文件URL路径【组&nbsp;2.png】无法通过URL获取路径，直接通过工具类提取文件后缀为【png】
<br/></td></tr><tr onmouseover="this.className='highlight'" onmouseout="this.className=''">
<td align='right'>25-6-4 14:13:02</td><td class='wrappedText'>/sys/file/uploadProcessFiles?pmInsType=slideShowType&amp;pmInsTypePart=1&nbsp;POST</td><td class='wrappedText'>chenhong</td><td class='wrappedText'>WARN&nbsp;&nbsp;com.simbest.boot.util.AppFileUtil&nbsp;-&nbsp;文件URL路径【组&nbsp;2.png】无法通过URL获取路径，直接通过工具类提取文件后缀为【png】
<br/></td></tr><tr class='odd' onmouseover="this.className='highlight'" onmouseout="this.className='odd'">
<td align='right'>25-6-4 14:15:00</td><td class='wrappedText'>&nbsp;</td><td class='wrappedText'>&nbsp;</td><td class='wrappedText'>WARN&nbsp;&nbsp;c.s.b.b.r.CustomDynamicWhere&nbsp;-&nbsp;自定义查询SQL输出为：【select&nbsp;*&nbsp;from&nbsp;US_TODO_MODEL&nbsp;t&nbsp;&nbsp;where&nbsp;t.send_flag&nbsp;=&nbsp;0&nbsp;and&nbsp;t.work_flag&nbsp;=&nbsp;0&nbsp;and&nbsp;t.enabled&nbsp;=1】
<br/></td></tr><tr onmouseover="this.className='highlight'" onmouseout="this.className=''">
<td align='right'>25-6-4 14:15:00</td><td class='wrappedText'>&nbsp;</td><td class='wrappedText'>&nbsp;</td><td class='wrappedText'>WARN&nbsp;&nbsp;c.s.b.b.r.CustomDynamicWhere&nbsp;-&nbsp;自定义查询SQL输出为：【select&nbsp;*&nbsp;from&nbsp;US_TODO_MODEL&nbsp;t&nbsp;&nbsp;where&nbsp;t.send_flag&nbsp;=&nbsp;0&nbsp;and&nbsp;t.work_flag&nbsp;=&nbsp;1&nbsp;and&nbsp;t.enabled&nbsp;=1】
<br/></td></tr><tr class='odd' onmouseover="this.className='highlight'" onmouseout="this.className='odd'">
<td align='right'>25-6-4 14:15:44</td><td class='wrappedText'>&nbsp;</td><td class='wrappedText'>&nbsp;</td><td class='wrappedText'>WARN&nbsp;&nbsp;c.s.boot.component.GracefulShutdown&nbsp;-&nbsp;executor非ThreadPoolExecutor，具体类型为【org.apache.tomcat.util.threads.ThreadPoolExecutor】
<br/></td></tr><tr onmouseover="this.className='highlight'" onmouseout="this.className=''">
<td align='right'>25-6-4 14:15:44</td><td class='wrappedText'>&nbsp;</td><td class='wrappedText'>&nbsp;</td><td class='wrappedText'>ERROR&nbsp;c.s.boot.base.exception.Exceptions&nbsp;-&nbsp;java.io.IOException:&nbsp;Unable&nbsp;to&nbsp;delete&nbsp;file:&nbsp;E:\晟壁\党风廉政视窗\simbest-boot-hnjjwz\springboottmp\hnjjwz\hnjjwz_DESKTOP-HUIVSCK\javamelody.lock
<br/>	at&nbsp;org.apache.commons.io.FileUtils.forceDelete(FileUtils.java:2400)
<br/>	at&nbsp;org.apache.commons.io.FileUtils.cleanDirectory(FileUtils.java:1721)
<br/>	at&nbsp;org.apache.commons.io.FileUtils.deleteDirectory(FileUtils.java:1617)
<br/>	at&nbsp;org.apache.commons.io.FileUtils.forceDelete(FileUtils.java:2391)
<br/>	at&nbsp;org.apache.commons.io.FileUtils.cleanDirectory(FileUtils.java:1721)
<br/>	at&nbsp;com.simbest.boot.component.GracefulShutdown.onApplicationEvent(GracefulShutdown.java:166)
<br/>	at&nbsp;com.simbest.boot.component.GracefulShutdown.onApplicationEvent(GracefulShutdown.java:46)
<br/>	at&nbsp;org.springframework.context.event.SimpleApplicationEventMulticaster.doInvokeListener(SimpleApplicationEventMulticaster.java:172)
<br/>	at&nbsp;org.springframework.context.event.SimpleApplicationEventMulticaster.invokeListener(SimpleApplicationEventMulticaster.java:165)
<br/>	at&nbsp;</td></tr></tbody></table>
</div></div>
<h3 class='chapterTitle'><img src='?resource=hourglass.png' alt='当前请求'/>
<a name='currentRequests'></a>当前请求</h3>
空
<h3 class='chapterTitle'><img src='?resource=systeminfo.png' alt='系统信息'/>
<a name='systeminfo'></a>系统信息</h3>
<div align='center' class='noPrint'>
<a href='?action=gc' onclick="javascript:return confirm('你确认执行垃圾回收机制？（这也许要花费数秒的时间）');"><img src='?resource=broom.png' width='20' height='20' alt='执行垃圾回收' /> 执行垃圾回收</a>&nbsp;&nbsp;&nbsp;&nbsp;
<a href='?action=heap_dump' onclick="javascript:return confirm('您确定要生成一堆的临时目录服务器吗? 　　(这可能需要几分钟时间)');"><img src='?resource=heapdump.png' width='20' height='20' alt="生成一堆临时空间" /> 生成一堆临时空间</a>&nbsp;&nbsp;&nbsp;&nbsp;
<a href='?part=heaphisto'><img src='?resource=memory.png' width='20' height='20' alt="查看内存直方图" /> 查看内存直方图</a>&nbsp;&nbsp;&nbsp;&nbsp;
<a href='?action=invalidate_sessions' onclick="javascript:return confirm('你确认无效的http会话吗? 　　(用户将不得不重新)');"><img src='?resource=user-trash.png' width='18' height='18' alt="无效的http session" /> 无效的http session</a>&nbsp;&nbsp;&nbsp;&nbsp;
<a href='?part=sessions'><img src='?resource=system-users.png' width='20' height='20' alt="查看http sessions" /> 查看http sessions</a>
<br />
&nbsp;&nbsp;&nbsp;&nbsp;
<a href='?part=mbeans'><img src='?resource=mbeans.png' width='20' height='20' alt="MBeans" /> MBeans</a>&nbsp;&nbsp;&nbsp;&nbsp;
<a href='?part=processes'><img src='?resource=processes.png' width='20' height='20' alt="查看操作系统进程" /> 查看操作系统进程</a>&nbsp;&nbsp;&nbsp;&nbsp;
<a href='?part=jndi'><img src='?resource=jndi.png' width='20' height='20' alt="JNDI 树" /> JNDI 树</a>&nbsp;&nbsp;&nbsp;&nbsp;
<a href='?part=springBeans'><img src='?resource=beans.png' width='20' height='20' alt="Spring beans" /> Spring beans</a><br/></div>
<table align='left' border='0' summary='系统信息'>
<tr><td>主机: </td><td><b>DESKTOP-HUIVSCK@***********</b></td> </tr>
<tr><td>Java使用的内存: </td><td><a class='tooltip' href='?part=graph&amp;graph=usedMemory' onmouseover="document.getElementById('idusedMemory').src='?graph=usedMemory&amp;width=100&amp;height=50'; this.onmouseover=null;" ><em><img src='?resource=systeminfo.png' id='idusedMemory' alt='graph'/></em>1,326</a> MB / 7,214 MB&nbsp;&nbsp;&nbsp;</td><td>
<img src='?resource=bar/rb_a.gif' alt='+' title='18.39%' /><img src='?resource=bar/rb_5.gif' alt='+' title='18.39%' /><img src='?resource=bar/rb_4.gif' alt='+' title='18.39%' /><img src='?resource=bar/rb_0.gif' alt='+' title='18.39%' /><img src='?resource=bar/rb_0.gif' alt='+' title='18.39%' /><img src='?resource=bar/rb_0.gif' alt='+' title='18.39%' /><img src='?resource=bar/rb_0.gif' alt='+' title='18.39%' /><img src='?resource=bar/rb_0.gif' alt='+' title='18.39%' /><img src='?resource=bar/rb_0.gif' alt='+' title='18.39%' /><img src='?resource=bar/rb_0.gif' alt='+' title='18.39%' /><img src='?resource=bar/rb_0.gif' alt='+' title='18.39%' /><img src='?resource=bar/rb_b0.gif' alt='+' title='18.39%' />
</td> </tr>
<tr><td>Nb http sessions: </td><td><a class='tooltip' href='?part=graph&amp;graph=httpSessions' onmouseover="document.getElementById('idhttpSessions').src='?graph=httpSessions&amp;width=100&amp;height=50'; this.onmouseover=null;" ><em><img src='?resource=systeminfo.png' id='idhttpSessions' alt='graph'/></em>0</a></td><td></td> </tr>
<tr><td>Nb 活跃的线程数<br/>(当前HTTP请求): </td><td><a class='tooltip' href='?part=graph&amp;graph=activeThreads' onmouseover="document.getElementById('idactiveThreads').src='?graph=activeThreads&amp;width=100&amp;height=50'; this.onmouseover=null;" ><em><img src='?resource=systeminfo.png' id='idactiveThreads' alt='graph'/></em>0</a></td><td></td> </tr>
<tr><td>Nb 活跃的jdbc连接数: </td><td><a class='tooltip' href='?part=graph&amp;graph=activeConnections' onmouseover="document.getElementById('idactiveConnections').src='?graph=activeConnections&amp;width=100&amp;height=50'; this.onmouseover=null;" ><em><img src='?resource=systeminfo.png' id='idactiveConnections' alt='graph'/></em>0</a></td><td></td> </tr>
<tr><td>Nb 被使用的jdbc连接数<br/>(被打开的无数据源): </td><td><a class='tooltip' href='?part=graph&amp;graph=usedConnections' onmouseover="document.getElementById('idusedConnections').src='?graph=usedConnections&amp;width=100&amp;height=50'; this.onmouseover=null;" ><em><img src='?resource=systeminfo.png' id='idusedConnections' alt='graph'/></em>0</a></td> </tr>
<tr><td>% System CPU</td><td><a class='tooltip' href='?part=graph&amp;graph=systemCpuLoad' onmouseover="document.getElementById('idsystemCpuLoad').src='?graph=systemCpuLoad&amp;width=100&amp;height=50'; this.onmouseover=null;" ><em><img src='?resource=systeminfo.png' id='idsystemCpuLoad' alt='graph'/></em>15.51</a>&nbsp;&nbsp;&nbsp;</td><td>
<img src='?resource=bar/rb_a.gif' alt='+' title='15.51%' /><img src='?resource=bar/rb_5.gif' alt='+' title='15.51%' /><img src='?resource=bar/rb_2.gif' alt='+' title='15.51%' /><img src='?resource=bar/rb_0.gif' alt='+' title='15.51%' /><img src='?resource=bar/rb_0.gif' alt='+' title='15.51%' /><img src='?resource=bar/rb_0.gif' alt='+' title='15.51%' /><img src='?resource=bar/rb_0.gif' alt='+' title='15.51%' /><img src='?resource=bar/rb_0.gif' alt='+' title='15.51%' /><img src='?resource=bar/rb_0.gif' alt='+' title='15.51%' /><img src='?resource=bar/rb_0.gif' alt='+' title='15.51%' /><img src='?resource=bar/rb_0.gif' alt='+' title='15.51%' /><img src='?resource=bar/rb_b0.gif' alt='+' title='15.51%' />
</td> </tr>
</table>
<br/><br/><br/><br/><br/><br/><br/>&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;
<a href="javascript:showHide('detailsJava');" class='noPrint'><img id='detailsJavaImg' src='?resource=bullets/plus.png' alt=''/> 描述</a>
<br/><br/><br/>
<div id='detailsJava' style='display: none;'><div>
<table align='left' border='0' summary='系统描述'>
<tr><td>操作系统: </td><td>
<img src='?resource=servers/windows.png' alt='操作系统'/>
Windows 10, , amd64/64 (12 核心)</td></tr>
<tr><td>Java: </td><td>Java(TM) SE Runtime Environment, 1.8.0_201-b09</td></tr>
<tr><td>JVM: </td><td>Java HotSpot(TM) 64-Bit Server VM, 25.201-b09, mixed mode</td></tr>
<tr><td>进程中的PID: </td><td>40272</td></tr>
<tr><td>服务器: </td><td>
<img src='?resource=servers/tomcat.png' alt='服务器'/>
Apache Tomcat/9.0.65 </td></tr><tr><td>Webapp 的内容: </td><td>/hnjjwz </td></tr>
<tr><td>开始: </td><td>25-6-4 下午2:07</td></tr>
<tr><td valign='top'>虚拟机参数: </td><td>-XX:TieredStopAtLevel=1<br/>-Xverify:none<br/>-Dspring.profiles.active=obuat<br/>-Dspring.output.ansi.enabled=always<br/>-javaagent:C:\Program Files\JetBrains\IntelliJ IDEA 2021.2.3\lib\idea_rt.jar=63794:C:\Program Files\JetBrains\IntelliJ IDEA 2021.2.3\bin<br/>-Dcom.sun.management.jmxremote<br/>-Dspring.jmx.enabled=true<br/>-Dspring.liveBeansView.mbeanDomain<br/>-Dspring.application.admin.enabled=true<br/>-Dfile.encoding=UTF-8</td></tr>
<tr><td>平均http时间 (min): </td><td><a class='tooltip' href='?part=graph&amp;graph=httpSessionsMeanAge' onmouseover="document.getElementById('idhttpSessionsMeanAge').src='?graph=httpSessionsMeanAge&amp;width=100&amp;height=50'; this.onmouseover=null;" ><em><img src='?resource=systeminfo.png' id='idhttpSessionsMeanAge' alt='graph'/></em>-1</a></td></tr>
<tr><td valign='top'>内存: </td><td>Non heap memory = 184 MB (Perm Gen, Code Cache),<br/>Buffered memory = 0 MB,<br/>Loaded classes = 26,013,<br/>Garbage collection time = 796 ms,<br/>Process cpu time = 108,156 ms,<br/>Committed virtual memory = 3,284 MB,<br/>Free physical memory = 5,574 MB,<br/>Total physical memory = 32,456 MB</td></tr>
<tr><td>剩余的硬盘空间: </td><td>45,406 MB </td></tr>
<tr><td valign='top'>基本数据: </td><td>
com.alibaba.druid.pool.DataSourceClosedException: dataSource already closed at Wed Jun 04 14:16:08 CST 2025</td></tr>
<tr><td valign='top'>依赖: </td><td>
<a href='?part=dependencies' class='noPrint'>
<img src='?resource=beans.png' width='14' height='14' alt='依赖'/> 依赖</a>
</td></tr>
</table>
</div></div>
<h3 class='chapterTitle' style='clear:both;'><img src='?resource=threads.png' alt='线程'/>
<a name='threads'></a>线程</h3>
<b>线程 DESKTOP-HUIVSCK@***********: </b>数目= 77, 最大= 197, 总得开始= 940
&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;
<a href="javascript:showHide('threads_0');" class='noPrint'><img id='threads_0Img' src='?resource=bullets/plus.png' alt=''/> 描述</a>
<br/><br/><div id='threads_0' style='display: none;'>
<table class='sortable' width='100%' border='1' summary='线程'>
<thead><tr><th>线程</th><th>后台程序</th><th class='sorttable_numeric'>优先权</th><th>状态</th><th>执行方法</th><th class='sorttable_numeric'>Cpu使用时间 (ms)</th><th class='sorttable_numeric'>用户时间(ms)</th><th class='noPrint'>Interrupt</th>
<th class='noPrint'>杀掉</th>
</tr></thead><tbody>
<tr onmouseover="this.className='highlight'" onmouseout="this.className=''">
<td><div class='tooltip'>
<em>
Abandoned&nbsp;connection&nbsp;cleanup&nbsp;thread<br/>
java.lang.Object.wait(Native Method)<br/>java.lang.ref.ReferenceQueue.remove(<a href='?part=source&amp;class=java.lang.ref.ReferenceQueue#144' class='lightwindow' type='external' title='java.lang.ref.ReferenceQueue'>ReferenceQueue.java:144</a>)<br/>com.mysql.cj.jdbc.AbandonedConnectionCleanupThread.run(<a href='?part=source&amp;class=com.mysql.cj.jdbc.AbandonedConnectionCleanupThread#70' class='lightwindow' type='external' title='com.mysql.cj.jdbc.AbandonedConnectionCleanupThread'>AbandonedConnectionCleanupThread.java:70</a>)<br/>java.util.concurrent.ThreadPoolExecutor.runWorker(<a href='?part=source&amp;class=java.util.concurrent.ThreadPoolExecutor#1149' class='lightwindow' type='external' title='java.util.concurrent.ThreadPoolExecutor'>ThreadPoolExecutor.java:1149</a>)<br/>java.util.concurrent.ThreadPoolExecutor$Worker.run(<a href='?part=source&amp;class=java.util.concurrent.ThreadPoolExecutor$Worker#624' class='lightwindow' type='external' title='java.util.concurrent.ThreadPoolExecutor$Worker'>ThreadPoolExecutor.java:624</a>)<br/>java.lang.Thread.run(<a href='?part=source&amp;class=java.lang.Thread#748' class='lightwindow' type='external' title='java.lang.Thread'>Thread.java:748</a>)<br/></em>
Abandoned&nbsp;connection&nbsp;cleanup&nbsp;thread</div>
</td> <td align='center'>yes</td> <td align='right'>5</td> <td><img src='?resource=bullets/yellow.png' alt='TIMED_WAITING'/>TIMED_WAITING</td> <td>java.lang.Object.wait(Native Method)</td> <td align='right'>46</td> <td align='right'>15</td> <td align='center' class='noPrint'><a href='?action=send_thread_interrupt&amp;threadId=40272_***********_49' onclick="javascript:return confirm('Do you confirm the sending of a thread interrupt signal to Abandoned connection cleanup thread?');"><img width='16' height='16' src='?resource=action_interrupt.png' alt='Send a thread interrupt signal to Abandoned connection cleanup thread. The thread can test Thread.currentThread().isInterrupted() to stop itself.' title='Send a thread interrupt signal to Abandoned connection cleanup thread. The thread can test Thread.currentThread().isInterrupted() to stop itself.' /></a></td> <td align='center' class='noPrint'><a href='?action=kill_thread&amp;threadId=40272_***********_49' onclick="javascript:return confirm('杀掉一个Java线程是不推荐和不安全的，你确定要杀掉线程 Abandoned connection cleanup thread ?');"><img width='16' height='16' src='?resource=stop.png' alt='杀掉线程&nbsp;Abandoned&nbsp;connection&nbsp;cleanup&nbsp;thread' title='杀掉线程&nbsp;Abandoned&nbsp;connection&nbsp;cleanup&nbsp;thread' /></a></td></tr><tr class='odd' onmouseover="this.className='highlight'" onmouseout="this.className='odd'">
<td>Attach&nbsp;Listener</td> <td align='center'>yes</td> <td align='right'>5</td> <td><img src='?resource=bullets/green.png' alt='RUNNABLE'/>RUNNABLE</td> <td>&nbsp;</td> <td align='right'>0</td> <td align='right'>0</td> <td align='center' class='noPrint'><a href='?action=send_thread_interrupt&amp;threadId=40272_***********_5' onclick="javascript:return confirm('Do you confirm the sending of a thread interrupt signal to Attach Listener?');"><img width='16' height='16' src='?resource=action_interrupt.png' alt='Send a thread interrupt signal to Attach Listener. The thread can test Thread.currentThread().isInterrupted() to stop itself.' title='Send a thread interrupt signal to Attach Listener. The thread can test Thread.currentThread().isInterrupted() to stop itself.' /></a></td> <td align='center' class='noPrint'><a href='?action=kill_thread&amp;threadId=40272_***********_5' onclick="javascript:return confirm('杀掉一个Java线程是不推荐和不安全的，你确定要杀掉线程 Attach Listener ?');"><img width='16' height='16' src='?resource=stop.png' alt='杀掉线程&nbsp;Attach&nbsp;Listener' title='杀掉线程&nbsp;Attach&nbsp;Listener' /></a></td></tr><tr onmouseover="this.className='highlight'" onmouseout="this.className=''">
<td><div class='tooltip'>
<em>
Catalina-utility-1<br/>
sun.misc.Unsafe.park(Native Method)<br/>java.util.concurrent.locks.LockSupport.parkNanos(<a href='?part=source&amp;class=java.util.concurrent.locks.LockSupport#215' class='lightwindow' type='external' title='java.util.concurrent.locks.LockSupport'>LockSupport.java:215</a>)<br/>java.util.concurrent.locks.AbstractQueuedSynchronizer$ConditionObject.awaitNanos(<a href='?part=source&amp;class=java.util.concurrent.locks.AbstractQueuedSynchronizer$ConditionObject#2078' class='lightwindow' type='external' title='java.util.concurrent.locks.AbstractQueuedSynchronizer$ConditionObject'>AbstractQueuedSynchronizer.java:2078</a>)<br/>java.util.concurrent.ScheduledThreadPoolExecutor$DelayedWorkQueue.take(<a href='?part=source&amp;class=java.util.concurrent.ScheduledThreadPoolExecutor$DelayedWorkQueue#1093' class='lightwindow' type='external' title='java.util.concurrent.ScheduledThreadPoolExecutor$DelayedWorkQueue'>ScheduledThreadPoolExecutor.java:1093</a>)<br/>java.util.concurrent.ScheduledThreadPoolExecutor$DelayedWorkQueue.take(<a href='?part=source&amp;class=java.util.concurrent.ScheduledThreadPoolExecutor$DelayedWorkQueue#809' class='lightwindow' type='external' title='java.util.concurrent.ScheduledThreadPoolExecutor$DelayedWorkQueue'>ScheduledThreadPoolExecutor.java:809</a>)<br/>java.util.concurrent.ThreadPoolExecutor.getTask(<a href='?part=source&amp;class=java.util.concurrent.ThreadPoolExecutor#1074' class='lightwindow' type='external' title='java.util.concurrent.ThreadPoolExecutor'>ThreadPoolExecutor.java:1074</a>)<br/>java.util.concurrent.ThreadPoolExecutor.runWorker(<a href='?part=source&amp;class=java.util.concurrent.ThreadPoolExecutor#1134' class='lightwindow' type='external' title='java.util.concurrent.ThreadPoolExecutor'>ThreadPoolExecutor.java:1134</a>)<br/>java.util.concurrent.ThreadPoolExecutor$Worker.run(<a href='?part=source&amp;class=java.util.concurrent.ThreadPoolExecutor$Worker#624' class='lightwindow' type='external' title='java.util.concurrent.ThreadPoolExecutor$Worker'>ThreadPoolExecutor.java:624</a>)<br/>org.apache.tomcat.util.threads.TaskThread$WrappingRunnable.run(<a href='?part=source&amp;class=org.apache.tomcat.util.threads.TaskThread$WrappingRunnable#61' class='lightwindow' type='external' title='org.apache.tomcat.util.threads.TaskThread$WrappingRunnable'>TaskThread.java:61</a>)<br/>java.lang.Thread.run(<a href='?part=source&amp;class=java.lang.Thread#748' class='lightwindow' type='external' title='java.lang.Thread'>Thread.java:748</a>)<br/></em>
Catalina-utility-1</div>
</td> <td align='center'>no</td> <td align='right'>1</td> <td><img src='?resource=bullets/yellow.png' alt='TIMED_WAITING'/>TIMED_WAITING</td> <td>sun.misc.Unsafe.park(Native Method)</td> <td align='right'>62</td> <td align='right'>62</td> <td align='center' class='noPrint'><a href='?action=send_thread_interrupt&amp;threadId=40272_***********_83' onclick="javascript:return confirm('Do you confirm the sending of a thread interrupt signal to Catalina-utility-1?');"><img width='16' height='16' src='?resource=action_interrupt.png' alt='Send a thread interrupt signal to Catalina-utility-1. The thread can test Thread.currentThread().isInterrupted() to stop itself.' title='Send a thread interrupt signal to Catalina-utility-1. The thread can test Thread.currentThread().isInterrupted() to stop itself.' /></a></td> <td align='center' class='noPrint'><a href='?action=kill_thread&amp;threadId=40272_***********_83' onclick="javascript:return confirm('杀掉一个Java线程是不推荐和不安全的，你确定要杀掉线程 Catalina-utility-1 ?');"><img width='16' height='16' src='?resource=stop.png' alt='杀掉线程&nbsp;Catalina-utility-1' title='杀掉线程&nbsp;Catalina-utility-1' /></a></td></tr><tr class='odd' onmouseover="this.className='highlight'" onmouseout="this.className='odd'">
<td><div class='tooltip'>
<em>
Catalina-utility-2<br/>
sun.misc.Unsafe.park(Native Method)<br/>java.util.concurrent.locks.LockSupport.park(<a href='?part=source&amp;class=java.util.concurrent.locks.LockSupport#175' class='lightwindow' type='external' title='java.util.concurrent.locks.LockSupport'>LockSupport.java:175</a>)<br/>java.util.concurrent.locks.AbstractQueuedSynchronizer$ConditionObject.await(<a href='?part=source&amp;class=java.util.concurrent.locks.AbstractQueuedSynchronizer$ConditionObject#2039' class='lightwindow' type='external' title='java.util.concurrent.locks.AbstractQueuedSynchronizer$ConditionObject'>AbstractQueuedSynchronizer.java:2039</a>)<br/>java.util.concurrent.ScheduledThreadPoolExecutor$DelayedWorkQueue.take(<a href='?part=source&amp;class=java.util.concurrent.ScheduledThreadPoolExecutor$DelayedWorkQueue#1088' class='lightwindow' type='external' title='java.util.concurrent.ScheduledThreadPoolExecutor$DelayedWorkQueue'>ScheduledThreadPoolExecutor.java:1088</a>)<br/>java.util.concurrent.ScheduledThreadPoolExecutor$DelayedWorkQueue.take(<a href='?part=source&amp;class=java.util.concurrent.ScheduledThreadPoolExecutor$DelayedWorkQueue#809' class='lightwindow' type='external' title='java.util.concurrent.ScheduledThreadPoolExecutor$DelayedWorkQueue'>ScheduledThreadPoolExecutor.java:809</a>)<br/>java.util.concurrent.ThreadPoolExecutor.getTask(<a href='?part=source&amp;class=java.util.concurrent.ThreadPoolExecutor#1074' class='lightwindow' type='external' title='java.util.concurrent.ThreadPoolExecutor'>ThreadPoolExecutor.java:1074</a>)<br/>java.util.concurrent.ThreadPoolExecutor.runWorker(<a href='?part=source&amp;class=java.util.concurrent.ThreadPoolExecutor#1134' class='lightwindow' type='external' title='java.util.concurrent.ThreadPoolExecutor'>ThreadPoolExecutor.java:1134</a>)<br/>java.util.concurrent.ThreadPoolExecutor$Worker.run(<a href='?part=source&amp;class=java.util.concurrent.ThreadPoolExecutor$Worker#624' class='lightwindow' type='external' title='java.util.concurrent.ThreadPoolExecutor$Worker'>ThreadPoolExecutor.java:624</a>)<br/>org.apache.tomcat.util.threads.TaskThread$WrappingRunnable.run(<a href='?part=source&amp;class=org.apache.tomcat.util.threads.TaskThread$WrappingRunnable#61' class='lightwindow' type='external' title='org.apache.tomcat.util.threads.TaskThread$WrappingRunnable'>TaskThread.java:61</a>)<br/>java.lang.Thread.run(<a href='?part=source&amp;class=java.lang.Thread#748' class='lightwindow' type='external' title='java.lang.Thread'>Thread.java:748</a>)<br/></em>
Catalina-utility-2</div>
</td> <td align='center'>no</td> <td align='right'>1</td> <td><img src='?resource=bullets/yellow.png' alt='WAITING'/>WAITING</td> <td>sun.misc.Unsafe.park(Native Method)</td> <td align='right'>93</td> <td align='right'>15</td> <td align='center' class='noPrint'><a href='?action=send_thread_interrupt&amp;threadId=40272_***********_84' onclick="javascript:return confirm('Do you confirm the sending of a thread interrupt signal to Catalina-utility-2?');"><img width='16' height='16' src='?resource=action_interrupt.png' alt='Send a thread interrupt signal to Catalina-utility-2. The thread can test Thread.currentThread().isInterrupted() to stop itself.' title='Send a thread interrupt signal to Catalina-utility-2. The thread can test Thread.currentThread().isInterrupted() to stop itself.' /></a></td> <td align='center' class='noPrint'><a href='?action=kill_thread&amp;threadId=40272_***********_84' onclick="javascript:return confirm('杀掉一个Java线程是不推荐和不安全的，你确定要杀掉线程 Catalina-utility-2 ?');"><img width='16' height='16' src='?resource=stop.png' alt='杀掉线程&nbsp;Catalina-utility-2' title='杀掉线程&nbsp;Catalina-utility-2' /></a></td></tr><tr onmouseover="this.className='highlight'" onmouseout="this.className=''">
<td><div class='tooltip'>
<em>
com.alibaba.nacos.client.auth.ram.identify.watcher.0<br/>
sun.misc.Unsafe.park(Native Method)<br/>java.util.concurrent.locks.LockSupport.parkNanos(<a href='?part=source&amp;class=java.util.concurrent.locks.LockSupport#215' class='lightwindow' type='external' title='java.util.concurrent.locks.LockSupport'>LockSupport.java:215</a>)<br/>java.util.concurrent.locks.AbstractQueuedSynchronizer$ConditionObject.awaitNanos(<a href='?part=source&amp;class=java.util.concurrent.locks.AbstractQueuedSynchronizer$ConditionObject#2078' class='lightwindow' type='external' title='java.util.concurrent.locks.AbstractQueuedSynchronizer$ConditionObject'>AbstractQueuedSynchronizer.java:2078</a>)<br/>java.util.concurrent.ScheduledThreadPoolExecutor$DelayedWorkQueue.take(<a href='?part=source&amp;class=java.util.concurrent.ScheduledThreadPoolExecutor$DelayedWorkQueue#1093' class='lightwindow' type='external' title='java.util.concurrent.ScheduledThreadPoolExecutor$DelayedWorkQueue'>ScheduledThreadPoolExecutor.java:1093</a>)<br/>java.util.concurrent.ScheduledThreadPoolExecutor$DelayedWorkQueue.take(<a href='?part=source&amp;class=java.util.concurrent.ScheduledThreadPoolExecutor$DelayedWorkQueue#809' class='lightwindow' type='external' title='java.util.concurrent.ScheduledThreadPoolExecutor$DelayedWorkQueue'>ScheduledThreadPoolExecutor.java:809</a>)<br/>java.util.concurrent.ThreadPoolExecutor.getTask(<a href='?part=source&amp;class=java.util.concurrent.ThreadPoolExecutor#1074' class='lightwindow' type='external' title='java.util.concurrent.ThreadPoolExecutor'>ThreadPoolExecutor.java:1074</a>)<br/>java.util.concurrent.ThreadPoolExecutor.runWorker(<a href='?part=source&amp;class=java.util.concurrent.ThreadPoolExecutor#1134' class='lightwindow' type='external' title='java.util.concurrent.ThreadPoolExecutor'>ThreadPoolExecutor.java:1134</a>)<br/>java.util.concurrent.ThreadPoolExecutor$Worker.run(<a href='?part=source&amp;class=java.util.concurrent.ThreadPoolExecutor$Worker#624' class='lightwindow' type='external' title='java.util.concurrent.ThreadPoolExecutor$Worker'>ThreadPoolExecutor.java:624</a>)<br/>java.lang.Thread.run(<a href='?part=source&amp;class=java.lang.Thread#748' class='lightwindow' type='external' title='java.lang.Thread'>Thread.java:748</a>)<br/></em>
com.alibaba.nacos.client.auth.ram.identify.watcher.0</div>
</td> <td align='center'>yes</td> <td align='right'>5</td> <td><img src='?resource=bullets/yellow.png' alt='TIMED_WAITING'/>TIMED_WAITING</td> <td>sun.misc.Unsafe.park(Native Method)</td> <td align='right'>0</td> <td align='right'>0</td> <td align='center' class='noPrint'><a href='?action=send_thread_interrupt&amp;threadId=40272_***********_929' onclick="javascript:return confirm('Do you confirm the sending of a thread interrupt signal to com.alibaba.nacos.client.auth.ram.identify.watcher.0?');"><img width='16' height='16' src='?resource=action_interrupt.png' alt='Send a thread interrupt signal to com.alibaba.nacos.client.auth.ram.identify.watcher.0. The thread can test Thread.currentThread().isInterrupted() to stop itself.' title='Send a thread interrupt signal to com.alibaba.nacos.client.auth.ram.identify.watcher.0. The thread can test Thread.currentThread().isInterrupted() to stop itself.' /></a></td> <td align='center' class='noPrint'><a href='?action=kill_thread&amp;threadId=40272_***********_929' onclick="javascript:return confirm('杀掉一个Java线程是不推荐和不安全的，你确定要杀掉线程 com.alibaba.nacos.client.auth.ram.identify.watcher.0 ?');"><img width='16' height='16' src='?resource=stop.png' alt='杀掉线程&nbsp;com.alibaba.nacos.client.auth.ram.identify.watcher.0' title='杀掉线程&nbsp;com.alibaba.nacos.client.auth.ram.identify.watcher.0' /></a></td></tr><tr class='odd' onmouseover="this.className='highlight'" onmouseout="this.className='odd'">
<td><div class='tooltip'>
<em>
com.alibaba.nacos.client.remote.worker<br/>
sun.misc.Unsafe.park(Native Method)<br/>java.util.concurrent.locks.LockSupport.park(<a href='?part=source&amp;class=java.util.concurrent.locks.LockSupport#175' class='lightwindow' type='external' title='java.util.concurrent.locks.LockSupport'>LockSupport.java:175</a>)<br/>java.util.concurrent.locks.AbstractQueuedSynchronizer$ConditionObject.await(<a href='?part=source&amp;class=java.util.concurrent.locks.AbstractQueuedSynchronizer$ConditionObject#2039' class='lightwindow' type='external' title='java.util.concurrent.locks.AbstractQueuedSynchronizer$ConditionObject'>AbstractQueuedSynchronizer.java:2039</a>)<br/>java.util.concurrent.LinkedBlockingQueue.take(<a href='?part=source&amp;class=java.util.concurrent.LinkedBlockingQueue#442' class='lightwindow' type='external' title='java.util.concurrent.LinkedBlockingQueue'>LinkedBlockingQueue.java:442</a>)<br/>com.alibaba.nacos.common.remote.client.RpcClient.lambda$start$1(<a href='?part=source&amp;class=com.alibaba.nacos.common.remote.client.RpcClient#294' class='lightwindow' type='external' title='com.alibaba.nacos.common.remote.client.RpcClient'>RpcClient.java:294</a>)<br/>com.alibaba.nacos.common.remote.client.RpcClient$$Lambda$279/1822706213.run(Unknown Source)<br/>java.util.concurrent.Executors$RunnableAdapter.call(<a href='?part=source&amp;class=java.util.concurrent.Executors$RunnableAdapter#511' class='lightwindow' type='external' title='java.util.concurrent.Executors$RunnableAdapter'>Executors.java:511</a>)<br/>java.util.concurrent.FutureTask.run(<a href='?part=source&amp;class=java.util.concurrent.FutureTask#266' class='lightwindow' type='external' title='java.util.concurrent.FutureTask'>FutureTask.java:266</a>)<br/>java.util.concurrent.ScheduledThreadPoolExecutor$ScheduledFutureTask.access$201(<a href='?part=source&amp;class=java.util.concurrent.ScheduledThreadPoolExecutor$ScheduledFutureTask#180' class='lightwindow' type='external' title='java.util.concurrent.ScheduledThreadPoolExecutor$ScheduledFutureTask'>ScheduledThreadPoolExecutor.java:180</a>)<br/>java.util.concurrent.ScheduledThreadPoolExecutor$ScheduledFutureTask.run(<a href='?part=source&amp;class=java.util.concurrent.ScheduledThreadPoolExecutor$ScheduledFutureTask#293' class='lightwindow' type='external' title='java.util.concurrent.ScheduledThreadPoolExecutor$ScheduledFutureTask'>ScheduledThreadPoolExecutor.java:293</a>)<br/>java.util.concurrent.ThreadPoolExecutor.runWorker(<a href='?part=source&amp;class=java.util.concurrent.ThreadPoolExecutor#1149' class='lightwindow' type='external' title='java.util.concurrent.ThreadPoolExecutor'>ThreadPoolExecutor.java:1149</a>)<br/>java.util.concurrent.ThreadPoolExecutor$Worker.run(<a href='?part=source&amp;class=java.util.concurrent.ThreadPoolExecutor$Worker#624' class='lightwindow' type='external' title='java.util.concurrent.ThreadPoolExecutor$Worker'>ThreadPoolExecutor.java:624</a>)<br/>java.lang.Thread.run(<a href='?part=source&amp;class=java.lang.Thread#748' class='lightwindow' type='external' title='java.lang.Thread'>Thread.java:748</a>)<br/></em>
com.alibaba.nacos.client.remote.worker</div>
</td> <td align='center'>yes</td> <td align='right'>5</td> <td><img src='?resource=bullets/yellow.png' alt='WAITING'/>WAITING</td> <td>sun.misc.Unsafe.park(Native Method)</td> <td align='right'>0</td> <td align='right'>0</td> <td align='center' class='noPrint'><a href='?action=send_thread_interrupt&amp;threadId=40272_***********_28' onclick="javascript:return confirm('Do you confirm the sending of a thread interrupt signal to com.alibaba.nacos.client.remote.worker?');"><img width='16' height='16' src='?resource=action_interrupt.png' alt='Send a thread interrupt signal to com.alibaba.nacos.client.remote.worker. The thread can test Thread.currentThread().isInterrupted() to stop itself.' title='Send a thread interrupt signal to com.alibaba.nacos.client.remote.worker. The thread can test Thread.currentThread().isInterrupted() to stop itself.' /></a></td> <td align='center' class='noPrint'><a href='?action=kill_thread&amp;threadId=40272_***********_28' onclick="javascript:return confirm('杀掉一个Java线程是不推荐和不安全的，你确定要杀掉线程 com.alibaba.nacos.client.remote.worker ?');"><img width='16' height='16' src='?resource=stop.png' alt='杀掉线程&nbsp;com.alibaba.nacos.client.remote.worker' title='杀掉线程&nbsp;com.alibaba.nacos.client.remote.worker' /></a></td></tr><tr onmouseover="this.className='highlight'" onmouseout="this.className=''">
<td><div class='tooltip'>
<em>
com.alibaba.nacos.client.remote.worker<br/>
sun.misc.Unsafe.park(Native Method)<br/>java.util.concurrent.locks.LockSupport.parkNanos(<a href='?part=source&amp;class=java.util.concurrent.locks.LockSupport#215' class='lightwindow' type='external' title='java.util.concurrent.locks.LockSupport'>LockSupport.java:215</a>)<br/>java.util.concurrent.locks.AbstractQueuedSynchronizer$ConditionObject.awaitNanos(<a href='?part=source&amp;class=java.util.concurrent.locks.AbstractQueuedSynchronizer$ConditionObject#2078' class='lightwindow' type='external' title='java.util.concurrent.locks.AbstractQueuedSynchronizer$ConditionObject'>AbstractQueuedSynchronizer.java:2078</a>)<br/>java.util.concurrent.ArrayBlockingQueue.poll(<a href='?part=source&amp;class=java.util.concurrent.ArrayBlockingQueue#418' class='lightwindow' type='external' title='java.util.concurrent.ArrayBlockingQueue'>ArrayBlockingQueue.java:418</a>)<br/>com.alibaba.nacos.common.remote.client.RpcClient.lambda$start$2(<a href='?part=source&amp;class=com.alibaba.nacos.common.remote.client.RpcClient#313' class='lightwindow' type='external' title='com.alibaba.nacos.common.remote.client.RpcClient'>RpcClient.java:313</a>)<br/>com.alibaba.nacos.common.remote.client.RpcClient$$Lambda$280/1464113749.run(Unknown Source)<br/>java.util.concurrent.Executors$RunnableAdapter.call(<a href='?part=source&amp;class=java.util.concurrent.Executors$RunnableAdapter#511' class='lightwindow' type='external' title='java.util.concurrent.Executors$RunnableAdapter'>Executors.java:511</a>)<br/>java.util.concurrent.FutureTask.run(<a href='?part=source&amp;class=java.util.concurrent.FutureTask#266' class='lightwindow' type='external' title='java.util.concurrent.FutureTask'>FutureTask.java:266</a>)<br/>java.util.concurrent.ScheduledThreadPoolExecutor$ScheduledFutureTask.access$201(<a href='?part=source&amp;class=java.util.concurrent.ScheduledThreadPoolExecutor$ScheduledFutureTask#180' class='lightwindow' type='external' title='java.util.concurrent.ScheduledThreadPoolExecutor$ScheduledFutureTask'>ScheduledThreadPoolExecutor.java:180</a>)<br/>java.util.concurrent.ScheduledThreadPoolExecutor$ScheduledFutureTask.run(<a href='?part=source&amp;class=java.util.concurrent.ScheduledThreadPoolExecutor$ScheduledFutureTask#293' class='lightwindow' type='external' title='java.util.concurrent.ScheduledThreadPoolExecutor$ScheduledFutureTask'>ScheduledThreadPoolExecutor.java:293</a>)<br/>java.util.concurrent.ThreadPoolExecutor.runWorker(<a href='?part=source&amp;class=java.util.concurrent.ThreadPoolExecutor#1149' class='lightwindow' type='external' title='java.util.concurrent.ThreadPoolExecutor'>ThreadPoolExecutor.java:1149</a>)<br/>java.util.concurrent.ThreadPoolExecutor$Worker.run(<a href='?part=source&amp;class=java.util.concurrent.ThreadPoolExecutor$Worker#624' class='lightwindow' type='external' title='java.util.concurrent.ThreadPoolExecutor$Worker'>ThreadPoolExecutor.java:624</a>)<br/>java.lang.Thread.run(<a href='?part=source&amp;class=java.lang.Thread#748' class='lightwindow' type='external' title='java.lang.Thread'>Thread.java:748</a>)<br/></em>
com.alibaba.nacos.client.remote.worker</div>
</td> <td align='center'>yes</td> <td align='right'>5</td> <td><img src='?resource=bullets/yellow.png' alt='TIMED_WAITING'/>TIMED_WAITING</td> <td>sun.misc.Unsafe.park(Native Method)</td> <td align='right'>140</td> <td align='right'>109</td> <td align='center' class='noPrint'><a href='?action=send_thread_interrupt&amp;threadId=40272_***********_29' onclick="javascript:return confirm('Do you confirm the sending of a thread interrupt signal to com.alibaba.nacos.client.remote.worker?');"><img width='16' height='16' src='?resource=action_interrupt.png' alt='Send a thread interrupt signal to com.alibaba.nacos.client.remote.worker. The thread can test Thread.currentThread().isInterrupted() to stop itself.' title='Send a thread interrupt signal to com.alibaba.nacos.client.remote.worker. The thread can test Thread.currentThread().isInterrupted() to stop itself.' /></a></td> <td align='center' class='noPrint'><a href='?action=kill_thread&amp;threadId=40272_***********_29' onclick="javascript:return confirm('杀掉一个Java线程是不推荐和不安全的，你确定要杀掉线程 com.alibaba.nacos.client.remote.worker ?');"><img width='16' height='16' src='?resource=stop.png' alt='杀掉线程&nbsp;com.alibaba.nacos.client.remote.worker' title='杀掉线程&nbsp;com.alibaba.nacos.client.remote.worker' /></a></td></tr><tr class='odd' onmouseover="this.className='highlight'" onmouseout="this.className='odd'">
<td><div class='tooltip'>
<em>
com.alibaba.nacos.client.Worker<br/>
sun.misc.Unsafe.park(Native Method)<br/>java.util.concurrent.locks.LockSupport.park(<a href='?part=source&amp;class=java.util.concurrent.locks.LockSupport#175' class='lightwindow' type='external' title='java.util.concurrent.locks.LockSupport'>LockSupport.java:175</a>)<br/>java.util.concurrent.locks.AbstractQueuedSynchronizer$ConditionObject.await(<a href='?part=source&amp;class=java.util.concurrent.locks.AbstractQueuedSynchronizer$ConditionObject#2039' class='lightwindow' type='external' title='java.util.concurrent.locks.AbstractQueuedSynchronizer$ConditionObject'>AbstractQueuedSynchronizer.java:2039</a>)<br/>java.util.concurrent.ScheduledThreadPoolExecutor$DelayedWorkQueue.take(<a href='?part=source&amp;class=java.util.concurrent.ScheduledThreadPoolExecutor$DelayedWorkQueue#1088' class='lightwindow' type='external' title='java.util.concurrent.ScheduledThreadPoolExecutor$DelayedWorkQueue'>ScheduledThreadPoolExecutor.java:1088</a>)<br/>java.util.concurrent.ScheduledThreadPoolExecutor$DelayedWorkQueue.take(<a href='?part=source&amp;class=java.util.concurrent.ScheduledThreadPoolExecutor$DelayedWorkQueue#809' class='lightwindow' type='external' title='java.util.concurrent.ScheduledThreadPoolExecutor$DelayedWorkQueue'>ScheduledThreadPoolExecutor.java:809</a>)<br/>java.util.concurrent.ThreadPoolExecutor.getTask(<a href='?part=source&amp;class=java.util.concurrent.ThreadPoolExecutor#1074' class='lightwindow' type='external' title='java.util.concurrent.ThreadPoolExecutor'>ThreadPoolExecutor.java:1074</a>)<br/>java.util.concurrent.ThreadPoolExecutor.runWorker(<a href='?part=source&amp;class=java.util.concurrent.ThreadPoolExecutor#1134' class='lightwindow' type='external' title='java.util.concurrent.ThreadPoolExecutor'>ThreadPoolExecutor.java:1134</a>)<br/>java.util.concurrent.ThreadPoolExecutor$Worker.run(<a href='?part=source&amp;class=java.util.concurrent.ThreadPoolExecutor$Worker#624' class='lightwindow' type='external' title='java.util.concurrent.ThreadPoolExecutor$Worker'>ThreadPoolExecutor.java:624</a>)<br/>java.lang.Thread.run(<a href='?part=source&amp;class=java.lang.Thread#748' class='lightwindow' type='external' title='java.lang.Thread'>Thread.java:748</a>)<br/></em>
com.alibaba.nacos.client.Worker</div>
</td> <td align='center'>yes</td> <td align='right'>5</td> <td><img src='?resource=bullets/yellow.png' alt='WAITING'/>WAITING</td> <td>sun.misc.Unsafe.park(Native Method)</td> <td align='right'>0</td> <td align='right'>0</td> <td align='center' class='noPrint'><a href='?action=send_thread_interrupt&amp;threadId=40272_***********_58' onclick="javascript:return confirm('Do you confirm the sending of a thread interrupt signal to com.alibaba.nacos.client.Worker?');"><img width='16' height='16' src='?resource=action_interrupt.png' alt='Send a thread interrupt signal to com.alibaba.nacos.client.Worker. The thread can test Thread.currentThread().isInterrupted() to stop itself.' title='Send a thread interrupt signal to com.alibaba.nacos.client.Worker. The thread can test Thread.currentThread().isInterrupted() to stop itself.' /></a></td> <td align='center' class='noPrint'><a href='?action=kill_thread&amp;threadId=40272_***********_58' onclick="javascript:return confirm('杀掉一个Java线程是不推荐和不安全的，你确定要杀掉线程 com.alibaba.nacos.client.Worker ?');"><img width='16' height='16' src='?resource=stop.png' alt='杀掉线程&nbsp;com.alibaba.nacos.client.Worker' title='杀掉线程&nbsp;com.alibaba.nacos.client.Worker' /></a></td></tr><tr onmouseover="this.className='highlight'" onmouseout="this.className=''">
<td><div class='tooltip'>
<em>
com.alibaba.nacos.client.Worker<br/>
sun.misc.Unsafe.park(Native Method)<br/>java.util.concurrent.locks.LockSupport.park(<a href='?part=source&amp;class=java.util.concurrent.locks.LockSupport#175' class='lightwindow' type='external' title='java.util.concurrent.locks.LockSupport'>LockSupport.java:175</a>)<br/>java.util.concurrent.locks.AbstractQueuedSynchronizer$ConditionObject.await(<a href='?part=source&amp;class=java.util.concurrent.locks.AbstractQueuedSynchronizer$ConditionObject#2039' class='lightwindow' type='external' title='java.util.concurrent.locks.AbstractQueuedSynchronizer$ConditionObject'>AbstractQueuedSynchronizer.java:2039</a>)<br/>java.util.concurrent.ScheduledThreadPoolExecutor$DelayedWorkQueue.take(<a href='?part=source&amp;class=java.util.concurrent.ScheduledThreadPoolExecutor$DelayedWorkQueue#1088' class='lightwindow' type='external' title='java.util.concurrent.ScheduledThreadPoolExecutor$DelayedWorkQueue'>ScheduledThreadPoolExecutor.java:1088</a>)<br/>java.util.concurrent.ScheduledThreadPoolExecutor$DelayedWorkQueue.take(<a href='?part=source&amp;class=java.util.concurrent.ScheduledThreadPoolExecutor$DelayedWorkQueue#809' class='lightwindow' type='external' title='java.util.concurrent.ScheduledThreadPoolExecutor$DelayedWorkQueue'>ScheduledThreadPoolExecutor.java:809</a>)<br/>java.util.concurrent.ThreadPoolExecutor.getTask(<a href='?part=source&amp;class=java.util.concurrent.ThreadPoolExecutor#1074' class='lightwindow' type='external' title='java.util.concurrent.ThreadPoolExecutor'>ThreadPoolExecutor.java:1074</a>)<br/>java.util.concurrent.ThreadPoolExecutor.runWorker(<a href='?part=source&amp;class=java.util.concurrent.ThreadPoolExecutor#1134' class='lightwindow' type='external' title='java.util.concurrent.ThreadPoolExecutor'>ThreadPoolExecutor.java:1134</a>)<br/>java.util.concurrent.ThreadPoolExecutor$Worker.run(<a href='?part=source&amp;class=java.util.concurrent.ThreadPoolExecutor$Worker#624' class='lightwindow' type='external' title='java.util.concurrent.ThreadPoolExecutor$Worker'>ThreadPoolExecutor.java:624</a>)<br/>java.lang.Thread.run(<a href='?part=source&amp;class=java.lang.Thread#748' class='lightwindow' type='external' title='java.lang.Thread'>Thread.java:748</a>)<br/></em>
com.alibaba.nacos.client.Worker</div>
</td> <td align='center'>yes</td> <td align='right'>5</td> <td><img src='?resource=bullets/yellow.png' alt='WAITING'/>WAITING</td> <td>sun.misc.Unsafe.park(Native Method)</td> <td align='right'>0</td> <td align='right'>0</td> <td align='center' class='noPrint'><a href='?action=send_thread_interrupt&amp;threadId=40272_***********_60' onclick="javascript:return confirm('Do you confirm the sending of a thread interrupt signal to com.alibaba.nacos.client.Worker?');"><img width='16' height='16' src='?resource=action_interrupt.png' alt='Send a thread interrupt signal to com.alibaba.nacos.client.Worker. The thread can test Thread.currentThread().isInterrupted() to stop itself.' title='Send a thread interrupt signal to com.alibaba.nacos.client.Worker. The thread can test Thread.currentThread().isInterrupted() to stop itself.' /></a></td> <td align='center' class='noPrint'><a href='?action=kill_thread&amp;threadId=40272_***********_60' onclick="javascript:return confirm('杀掉一个Java线程是不推荐和不安全的，你确定要杀掉线程 com.alibaba.nacos.client.Worker ?');"><img width='16' height='16' src='?resource=stop.png' alt='杀掉线程&nbsp;com.alibaba.nacos.client.Worker' title='杀掉线程&nbsp;com.alibaba.nacos.client.Worker' /></a></td></tr><tr class='odd' onmouseover="this.className='highlight'" onmouseout="this.className='odd'">
<td><div class='tooltip'>
<em>
com.alibaba.nacos.client.Worker<br/>
sun.misc.Unsafe.park(Native Method)<br/>java.util.concurrent.locks.LockSupport.park(<a href='?part=source&amp;class=java.util.concurrent.locks.LockSupport#175' class='lightwindow' type='external' title='java.util.concurrent.locks.LockSupport'>LockSupport.java:175</a>)<br/>java.util.concurrent.locks.AbstractQueuedSynchronizer$ConditionObject.await(<a href='?part=source&amp;class=java.util.concurrent.locks.AbstractQueuedSynchronizer$ConditionObject#2039' class='lightwindow' type='external' title='java.util.concurrent.locks.AbstractQueuedSynchronizer$ConditionObject'>AbstractQueuedSynchronizer.java:2039</a>)<br/>java.util.concurrent.ScheduledThreadPoolExecutor$DelayedWorkQueue.take(<a href='?part=source&amp;class=java.util.concurrent.ScheduledThreadPoolExecutor$DelayedWorkQueue#1088' class='lightwindow' type='external' title='java.util.concurrent.ScheduledThreadPoolExecutor$DelayedWorkQueue'>ScheduledThreadPoolExecutor.java:1088</a>)<br/>java.util.concurrent.ScheduledThreadPoolExecutor$DelayedWorkQueue.take(<a href='?part=source&amp;class=java.util.concurrent.ScheduledThreadPoolExecutor$DelayedWorkQueue#809' class='lightwindow' type='external' title='java.util.concurrent.ScheduledThreadPoolExecutor$DelayedWorkQueue'>ScheduledThreadPoolExecutor.java:809</a>)<br/>java.util.concurrent.ThreadPoolExecutor.getTask(<a href='?part=source&amp;class=java.util.concurrent.ThreadPoolExecutor#1074' class='lightwindow' type='external' title='java.util.concurrent.ThreadPoolExecutor'>ThreadPoolExecutor.java:1074</a>)<br/>java.util.concurrent.ThreadPoolExecutor.runWorker(<a href='?part=source&amp;class=java.util.concurrent.ThreadPoolExecutor#1134' class='lightwindow' type='external' title='java.util.concurrent.ThreadPoolExecutor'>ThreadPoolExecutor.java:1134</a>)<br/>java.util.concurrent.ThreadPoolExecutor$Worker.run(<a href='?part=source&amp;class=java.util.concurrent.ThreadPoolExecutor$Worker#624' class='lightwindow' type='external' title='java.util.concurrent.ThreadPoolExecutor$Worker'>ThreadPoolExecutor.java:624</a>)<br/>java.lang.Thread.run(<a href='?part=source&amp;class=java.lang.Thread#748' class='lightwindow' type='external' title='java.lang.Thread'>Thread.java:748</a>)<br/></em>
com.alibaba.nacos.client.Worker</div>
</td> <td align='center'>yes</td> <td align='right'>5</td> <td><img src='?resource=bullets/yellow.png' alt='WAITING'/>WAITING</td> <td>sun.misc.Unsafe.park(Native Method)</td> <td align='right'>0</td> <td align='right'>0</td> <td align='center' class='noPrint'><a href='?action=send_thread_interrupt&amp;threadId=40272_***********_556' onclick="javascript:return confirm('Do you confirm the sending of a thread interrupt signal to com.alibaba.nacos.client.Worker?');"><img width='16' height='16' src='?resource=action_interrupt.png' alt='Send a thread interrupt signal to com.alibaba.nacos.client.Worker. The thread can test Thread.currentThread().isInterrupted() to stop itself.' title='Send a thread interrupt signal to com.alibaba.nacos.client.Worker. The thread can test Thread.currentThread().isInterrupted() to stop itself.' /></a></td> <td align='center' class='noPrint'><a href='?action=kill_thread&amp;threadId=40272_***********_556' onclick="javascript:return confirm('杀掉一个Java线程是不推荐和不安全的，你确定要杀掉线程 com.alibaba.nacos.client.Worker ?');"><img width='16' height='16' src='?resource=stop.png' alt='杀掉线程&nbsp;com.alibaba.nacos.client.Worker' title='杀掉线程&nbsp;com.alibaba.nacos.client.Worker' /></a></td></tr><tr onmouseover="this.className='highlight'" onmouseout="this.className=''">
<td><div class='tooltip'>
<em>
com.alibaba.nacos.client.Worker<br/>
sun.misc.Unsafe.park(Native Method)<br/>java.util.concurrent.locks.LockSupport.park(<a href='?part=source&amp;class=java.util.concurrent.locks.LockSupport#175' class='lightwindow' type='external' title='java.util.concurrent.locks.LockSupport'>LockSupport.java:175</a>)<br/>java.util.concurrent.locks.AbstractQueuedSynchronizer$ConditionObject.await(<a href='?part=source&amp;class=java.util.concurrent.locks.AbstractQueuedSynchronizer$ConditionObject#2039' class='lightwindow' type='external' title='java.util.concurrent.locks.AbstractQueuedSynchronizer$ConditionObject'>AbstractQueuedSynchronizer.java:2039</a>)<br/>java.util.concurrent.ScheduledThreadPoolExecutor$DelayedWorkQueue.take(<a href='?part=source&amp;class=java.util.concurrent.ScheduledThreadPoolExecutor$DelayedWorkQueue#1088' class='lightwindow' type='external' title='java.util.concurrent.ScheduledThreadPoolExecutor$DelayedWorkQueue'>ScheduledThreadPoolExecutor.java:1088</a>)<br/>java.util.concurrent.ScheduledThreadPoolExecutor$DelayedWorkQueue.take(<a href='?part=source&amp;class=java.util.concurrent.ScheduledThreadPoolExecutor$DelayedWorkQueue#809' class='lightwindow' type='external' title='java.util.concurrent.ScheduledThreadPoolExecutor$DelayedWorkQueue'>ScheduledThreadPoolExecutor.java:809</a>)<br/>java.util.concurrent.ThreadPoolExecutor.getTask(<a href='?part=source&amp;class=java.util.concurrent.ThreadPoolExecutor#1074' class='lightwindow' type='external' title='java.util.concurrent.ThreadPoolExecutor'>ThreadPoolExecutor.java:1074</a>)<br/>java.util.concurrent.ThreadPoolExecutor.runWorker(<a href='?part=source&amp;class=java.util.concurrent.ThreadPoolExecutor#1134' class='lightwindow' type='external' title='java.util.concurrent.ThreadPoolExecutor'>ThreadPoolExecutor.java:1134</a>)<br/>java.util.concurrent.ThreadPoolExecutor$Worker.run(<a href='?part=source&amp;class=java.util.concurrent.ThreadPoolExecutor$Worker#624' class='lightwindow' type='external' title='java.util.concurrent.ThreadPoolExecutor$Worker'>ThreadPoolExecutor.java:624</a>)<br/>java.lang.Thread.run(<a href='?part=source&amp;class=java.lang.Thread#748' class='lightwindow' type='external' title='java.lang.Thread'>Thread.java:748</a>)<br/></em>
com.alibaba.nacos.client.Worker</div>
</td> <td align='center'>yes</td> <td align='right'>5</td> <td><img src='?resource=bullets/yellow.png' alt='WAITING'/>WAITING</td> <td>sun.misc.Unsafe.park(Native Method)</td> <td align='right'>0</td> <td align='right'>0</td> <td align='center' class='noPrint'><a href='?action=send_thread_interrupt&amp;threadId=40272_***********_24' onclick="javascript:return confirm('Do you confirm the sending of a thread interrupt signal to com.alibaba.nacos.client.Worker?');"><img width='16' height='16' src='?resource=action_interrupt.png' alt='Send a thread interrupt signal to com.alibaba.nacos.client.Worker. The thread can test Thread.currentThread().isInterrupted() to stop itself.' title='Send a thread interrupt signal to com.alibaba.nacos.client.Worker. The thread can test Thread.currentThread().isInterrupted() to stop itself.' /></a></td> <td align='center' class='noPrint'><a href='?action=kill_thread&amp;threadId=40272_***********_24' onclick="javascript:return confirm('杀掉一个Java线程是不推荐和不安全的，你确定要杀掉线程 com.alibaba.nacos.client.Worker ?');"><img width='16' height='16' src='?resource=stop.png' alt='杀掉线程&nbsp;com.alibaba.nacos.client.Worker' title='杀掉线程&nbsp;com.alibaba.nacos.client.Worker' /></a></td></tr><tr class='odd' onmouseover="this.className='highlight'" onmouseout="this.className='odd'">
<td><div class='tooltip'>
<em>
com.alibaba.nacos.client.Worker<br/>
sun.misc.Unsafe.park(Native Method)<br/>java.util.concurrent.locks.LockSupport.park(<a href='?part=source&amp;class=java.util.concurrent.locks.LockSupport#175' class='lightwindow' type='external' title='java.util.concurrent.locks.LockSupport'>LockSupport.java:175</a>)<br/>java.util.concurrent.locks.AbstractQueuedSynchronizer$ConditionObject.await(<a href='?part=source&amp;class=java.util.concurrent.locks.AbstractQueuedSynchronizer$ConditionObject#2039' class='lightwindow' type='external' title='java.util.concurrent.locks.AbstractQueuedSynchronizer$ConditionObject'>AbstractQueuedSynchronizer.java:2039</a>)<br/>java.util.concurrent.ScheduledThreadPoolExecutor$DelayedWorkQueue.take(<a href='?part=source&amp;class=java.util.concurrent.ScheduledThreadPoolExecutor$DelayedWorkQueue#1088' class='lightwindow' type='external' title='java.util.concurrent.ScheduledThreadPoolExecutor$DelayedWorkQueue'>ScheduledThreadPoolExecutor.java:1088</a>)<br/>java.util.concurrent.ScheduledThreadPoolExecutor$DelayedWorkQueue.take(<a href='?part=source&amp;class=java.util.concurrent.ScheduledThreadPoolExecutor$DelayedWorkQueue#809' class='lightwindow' type='external' title='java.util.concurrent.ScheduledThreadPoolExecutor$DelayedWorkQueue'>ScheduledThreadPoolExecutor.java:809</a>)<br/>java.util.concurrent.ThreadPoolExecutor.getTask(<a href='?part=source&amp;class=java.util.concurrent.ThreadPoolExecutor#1074' class='lightwindow' type='external' title='java.util.concurrent.ThreadPoolExecutor'>ThreadPoolExecutor.java:1074</a>)<br/>java.util.concurrent.ThreadPoolExecutor.runWorker(<a href='?part=source&amp;class=java.util.concurrent.ThreadPoolExecutor#1134' class='lightwindow' type='external' title='java.util.concurrent.ThreadPoolExecutor'>ThreadPoolExecutor.java:1134</a>)<br/>java.util.concurrent.ThreadPoolExecutor$Worker.run(<a href='?part=source&amp;class=java.util.concurrent.ThreadPoolExecutor$Worker#624' class='lightwindow' type='external' title='java.util.concurrent.ThreadPoolExecutor$Worker'>ThreadPoolExecutor.java:624</a>)<br/>java.lang.Thread.run(<a href='?part=source&amp;class=java.lang.Thread#748' class='lightwindow' type='external' title='java.lang.Thread'>Thread.java:748</a>)<br/></em>
com.alibaba.nacos.client.Worker</div>
</td> <td align='center'>yes</td> <td align='right'>5</td> <td><img src='?resource=bullets/yellow.png' alt='WAITING'/>WAITING</td> <td>sun.misc.Unsafe.park(Native Method)</td> <td align='right'>0</td> <td align='right'>0</td> <td align='center' class='noPrint'><a href='?action=send_thread_interrupt&amp;threadId=40272_***********_672' onclick="javascript:return confirm('Do you confirm the sending of a thread interrupt signal to com.alibaba.nacos.client.Worker?');"><img width='16' height='16' src='?resource=action_interrupt.png' alt='Send a thread interrupt signal to com.alibaba.nacos.client.Worker. The thread can test Thread.currentThread().isInterrupted() to stop itself.' title='Send a thread interrupt signal to com.alibaba.nacos.client.Worker. The thread can test Thread.currentThread().isInterrupted() to stop itself.' /></a></td> <td align='center' class='noPrint'><a href='?action=kill_thread&amp;threadId=40272_***********_672' onclick="javascript:return confirm('杀掉一个Java线程是不推荐和不安全的，你确定要杀掉线程 com.alibaba.nacos.client.Worker ?');"><img width='16' height='16' src='?resource=stop.png' alt='杀掉线程&nbsp;com.alibaba.nacos.client.Worker' title='杀掉线程&nbsp;com.alibaba.nacos.client.Worker' /></a></td></tr><tr onmouseover="this.className='highlight'" onmouseout="this.className=''">
<td><div class='tooltip'>
<em>
com.alibaba.nacos.client.Worker<br/>
sun.misc.Unsafe.park(Native Method)<br/>java.util.concurrent.locks.LockSupport.park(<a href='?part=source&amp;class=java.util.concurrent.locks.LockSupport#175' class='lightwindow' type='external' title='java.util.concurrent.locks.LockSupport'>LockSupport.java:175</a>)<br/>java.util.concurrent.locks.AbstractQueuedSynchronizer$ConditionObject.await(<a href='?part=source&amp;class=java.util.concurrent.locks.AbstractQueuedSynchronizer$ConditionObject#2039' class='lightwindow' type='external' title='java.util.concurrent.locks.AbstractQueuedSynchronizer$ConditionObject'>AbstractQueuedSynchronizer.java:2039</a>)<br/>java.util.concurrent.ScheduledThreadPoolExecutor$DelayedWorkQueue.take(<a href='?part=source&amp;class=java.util.concurrent.ScheduledThreadPoolExecutor$DelayedWorkQueue#1088' class='lightwindow' type='external' title='java.util.concurrent.ScheduledThreadPoolExecutor$DelayedWorkQueue'>ScheduledThreadPoolExecutor.java:1088</a>)<br/>java.util.concurrent.ScheduledThreadPoolExecutor$DelayedWorkQueue.take(<a href='?part=source&amp;class=java.util.concurrent.ScheduledThreadPoolExecutor$DelayedWorkQueue#809' class='lightwindow' type='external' title='java.util.concurrent.ScheduledThreadPoolExecutor$DelayedWorkQueue'>ScheduledThreadPoolExecutor.java:809</a>)<br/>java.util.concurrent.ThreadPoolExecutor.getTask(<a href='?part=source&amp;class=java.util.concurrent.ThreadPoolExecutor#1074' class='lightwindow' type='external' title='java.util.concurrent.ThreadPoolExecutor'>ThreadPoolExecutor.java:1074</a>)<br/>java.util.concurrent.ThreadPoolExecutor.runWorker(<a href='?part=source&amp;class=java.util.concurrent.ThreadPoolExecutor#1134' class='lightwindow' type='external' title='java.util.concurrent.ThreadPoolExecutor'>ThreadPoolExecutor.java:1134</a>)<br/>java.util.concurrent.ThreadPoolExecutor$Worker.run(<a href='?part=source&amp;class=java.util.concurrent.ThreadPoolExecutor$Worker#624' class='lightwindow' type='external' title='java.util.concurrent.ThreadPoolExecutor$Worker'>ThreadPoolExecutor.java:624</a>)<br/>java.lang.Thread.run(<a href='?part=source&amp;class=java.lang.Thread#748' class='lightwindow' type='external' title='java.lang.Thread'>Thread.java:748</a>)<br/></em>
com.alibaba.nacos.client.Worker</div>
</td> <td align='center'>yes</td> <td align='right'>5</td> <td><img src='?resource=bullets/yellow.png' alt='WAITING'/>WAITING</td> <td>sun.misc.Unsafe.park(Native Method)</td> <td align='right'>0</td> <td align='right'>0</td> <td align='center' class='noPrint'><a href='?action=send_thread_interrupt&amp;threadId=40272_***********_87' onclick="javascript:return confirm('Do you confirm the sending of a thread interrupt signal to com.alibaba.nacos.client.Worker?');"><img width='16' height='16' src='?resource=action_interrupt.png' alt='Send a thread interrupt signal to com.alibaba.nacos.client.Worker. The thread can test Thread.currentThread().isInterrupted() to stop itself.' title='Send a thread interrupt signal to com.alibaba.nacos.client.Worker. The thread can test Thread.currentThread().isInterrupted() to stop itself.' /></a></td> <td align='center' class='noPrint'><a href='?action=kill_thread&amp;threadId=40272_***********_87' onclick="javascript:return confirm('杀掉一个Java线程是不推荐和不安全的，你确定要杀掉线程 com.alibaba.nacos.client.Worker ?');"><img width='16' height='16' src='?resource=stop.png' alt='杀掉线程&nbsp;com.alibaba.nacos.client.Worker' title='杀掉线程&nbsp;com.alibaba.nacos.client.Worker' /></a></td></tr><tr class='odd' onmouseover="this.className='highlight'" onmouseout="this.className='odd'">
<td><div class='tooltip'>
<em>
com.alibaba.nacos.client.Worker<br/>
sun.misc.Unsafe.park(Native Method)<br/>java.util.concurrent.locks.LockSupport.park(<a href='?part=source&amp;class=java.util.concurrent.locks.LockSupport#175' class='lightwindow' type='external' title='java.util.concurrent.locks.LockSupport'>LockSupport.java:175</a>)<br/>java.util.concurrent.locks.AbstractQueuedSynchronizer$ConditionObject.await(<a href='?part=source&amp;class=java.util.concurrent.locks.AbstractQueuedSynchronizer$ConditionObject#2039' class='lightwindow' type='external' title='java.util.concurrent.locks.AbstractQueuedSynchronizer$ConditionObject'>AbstractQueuedSynchronizer.java:2039</a>)<br/>java.util.concurrent.ScheduledThreadPoolExecutor$DelayedWorkQueue.take(<a href='?part=source&amp;class=java.util.concurrent.ScheduledThreadPoolExecutor$DelayedWorkQueue#1088' class='lightwindow' type='external' title='java.util.concurrent.ScheduledThreadPoolExecutor$DelayedWorkQueue'>ScheduledThreadPoolExecutor.java:1088</a>)<br/>java.util.concurrent.ScheduledThreadPoolExecutor$DelayedWorkQueue.take(<a href='?part=source&amp;class=java.util.concurrent.ScheduledThreadPoolExecutor$DelayedWorkQueue#809' class='lightwindow' type='external' title='java.util.concurrent.ScheduledThreadPoolExecutor$DelayedWorkQueue'>ScheduledThreadPoolExecutor.java:809</a>)<br/>java.util.concurrent.ThreadPoolExecutor.getTask(<a href='?part=source&amp;class=java.util.concurrent.ThreadPoolExecutor#1074' class='lightwindow' type='external' title='java.util.concurrent.ThreadPoolExecutor'>ThreadPoolExecutor.java:1074</a>)<br/>java.util.concurrent.ThreadPoolExecutor.runWorker(<a href='?part=source&amp;class=java.util.concurrent.ThreadPoolExecutor#1134' class='lightwindow' type='external' title='java.util.concurrent.ThreadPoolExecutor'>ThreadPoolExecutor.java:1134</a>)<br/>java.util.concurrent.ThreadPoolExecutor$Worker.run(<a href='?part=source&amp;class=java.util.concurrent.ThreadPoolExecutor$Worker#624' class='lightwindow' type='external' title='java.util.concurrent.ThreadPoolExecutor$Worker'>ThreadPoolExecutor.java:624</a>)<br/>java.lang.Thread.run(<a href='?part=source&amp;class=java.lang.Thread#748' class='lightwindow' type='external' title='java.lang.Thread'>Thread.java:748</a>)<br/></em>
com.alibaba.nacos.client.Worker</div>
</td> <td align='center'>yes</td> <td align='right'>5</td> <td><img src='?resource=bullets/yellow.png' alt='WAITING'/>WAITING</td> <td>sun.misc.Unsafe.park(Native Method)</td> <td align='right'>0</td> <td align='right'>0</td> <td align='center' class='noPrint'><a href='?action=send_thread_interrupt&amp;threadId=40272_***********_54' onclick="javascript:return confirm('Do you confirm the sending of a thread interrupt signal to com.alibaba.nacos.client.Worker?');"><img width='16' height='16' src='?resource=action_interrupt.png' alt='Send a thread interrupt signal to com.alibaba.nacos.client.Worker. The thread can test Thread.currentThread().isInterrupted() to stop itself.' title='Send a thread interrupt signal to com.alibaba.nacos.client.Worker. The thread can test Thread.currentThread().isInterrupted() to stop itself.' /></a></td> <td align='center' class='noPrint'><a href='?action=kill_thread&amp;threadId=40272_***********_54' onclick="javascript:return confirm('杀掉一个Java线程是不推荐和不安全的，你确定要杀掉线程 com.alibaba.nacos.client.Worker ?');"><img width='16' height='16' src='?resource=stop.png' alt='杀掉线程&nbsp;com.alibaba.nacos.client.Worker' title='杀掉线程&nbsp;com.alibaba.nacos.client.Worker' /></a></td></tr><tr onmouseover="this.className='highlight'" onmouseout="this.className=''">
<td><div class='tooltip'>
<em>
com.alibaba.nacos.client.Worker<br/>
sun.misc.Unsafe.park(Native Method)<br/>java.util.concurrent.locks.LockSupport.park(<a href='?part=source&amp;class=java.util.concurrent.locks.LockSupport#175' class='lightwindow' type='external' title='java.util.concurrent.locks.LockSupport'>LockSupport.java:175</a>)<br/>java.util.concurrent.locks.AbstractQueuedSynchronizer$ConditionObject.await(<a href='?part=source&amp;class=java.util.concurrent.locks.AbstractQueuedSynchronizer$ConditionObject#2039' class='lightwindow' type='external' title='java.util.concurrent.locks.AbstractQueuedSynchronizer$ConditionObject'>AbstractQueuedSynchronizer.java:2039</a>)<br/>java.util.concurrent.ScheduledThreadPoolExecutor$DelayedWorkQueue.take(<a href='?part=source&amp;class=java.util.concurrent.ScheduledThreadPoolExecutor$DelayedWorkQueue#1088' class='lightwindow' type='external' title='java.util.concurrent.ScheduledThreadPoolExecutor$DelayedWorkQueue'>ScheduledThreadPoolExecutor.java:1088</a>)<br/>java.util.concurrent.ScheduledThreadPoolExecutor$DelayedWorkQueue.take(<a href='?part=source&amp;class=java.util.concurrent.ScheduledThreadPoolExecutor$DelayedWorkQueue#809' class='lightwindow' type='external' title='java.util.concurrent.ScheduledThreadPoolExecutor$DelayedWorkQueue'>ScheduledThreadPoolExecutor.java:809</a>)<br/>java.util.concurrent.ThreadPoolExecutor.getTask(<a href='?part=source&amp;class=java.util.concurrent.ThreadPoolExecutor#1074' class='lightwindow' type='external' title='java.util.concurrent.ThreadPoolExecutor'>ThreadPoolExecutor.java:1074</a>)<br/>java.util.concurrent.ThreadPoolExecutor.runWorker(<a href='?part=source&amp;class=java.util.concurrent.ThreadPoolExecutor#1134' class='lightwindow' type='external' title='java.util.concurrent.ThreadPoolExecutor'>ThreadPoolExecutor.java:1134</a>)<br/>java.util.concurrent.ThreadPoolExecutor$Worker.run(<a href='?part=source&amp;class=java.util.concurrent.ThreadPoolExecutor$Worker#624' class='lightwindow' type='external' title='java.util.concurrent.ThreadPoolExecutor$Worker'>ThreadPoolExecutor.java:624</a>)<br/>java.lang.Thread.run(<a href='?part=source&amp;class=java.lang.Thread#748' class='lightwindow' type='external' title='java.lang.Thread'>Thread.java:748</a>)<br/></em>
com.alibaba.nacos.client.Worker</div>
</td> <td align='center'>yes</td> <td align='right'>5</td> <td><img src='?resource=bullets/yellow.png' alt='WAITING'/>WAITING</td> <td>sun.misc.Unsafe.park(Native Method)</td> <td align='right'>0</td> <td align='right'>0</td> <td align='center' class='noPrint'><a href='?action=send_thread_interrupt&amp;threadId=40272_***********_82' onclick="javascript:return confirm('Do you confirm the sending of a thread interrupt signal to com.alibaba.nacos.client.Worker?');"><img width='16' height='16' src='?resource=action_interrupt.png' alt='Send a thread interrupt signal to com.alibaba.nacos.client.Worker. The thread can test Thread.currentThread().isInterrupted() to stop itself.' title='Send a thread interrupt signal to com.alibaba.nacos.client.Worker. The thread can test Thread.currentThread().isInterrupted() to stop itself.' /></a></td> <td align='center' class='noPrint'><a href='?action=kill_thread&amp;threadId=40272_***********_82' onclick="javascript:return confirm('杀掉一个Java线程是不推荐和不安全的，你确定要杀掉线程 com.alibaba.nacos.client.Worker ?');"><img width='16' height='16' src='?resource=stop.png' alt='杀掉线程&nbsp;com.alibaba.nacos.client.Worker' title='杀掉线程&nbsp;com.alibaba.nacos.client.Worker' /></a></td></tr><tr class='odd' onmouseover="this.className='highlight'" onmouseout="this.className='odd'">
<td><div class='tooltip'>
<em>
com.alibaba.nacos.client.Worker<br/>
sun.misc.Unsafe.park(Native Method)<br/>java.util.concurrent.locks.LockSupport.park(<a href='?part=source&amp;class=java.util.concurrent.locks.LockSupport#175' class='lightwindow' type='external' title='java.util.concurrent.locks.LockSupport'>LockSupport.java:175</a>)<br/>java.util.concurrent.locks.AbstractQueuedSynchronizer$ConditionObject.await(<a href='?part=source&amp;class=java.util.concurrent.locks.AbstractQueuedSynchronizer$ConditionObject#2039' class='lightwindow' type='external' title='java.util.concurrent.locks.AbstractQueuedSynchronizer$ConditionObject'>AbstractQueuedSynchronizer.java:2039</a>)<br/>java.util.concurrent.ScheduledThreadPoolExecutor$DelayedWorkQueue.take(<a href='?part=source&amp;class=java.util.concurrent.ScheduledThreadPoolExecutor$DelayedWorkQueue#1088' class='lightwindow' type='external' title='java.util.concurrent.ScheduledThreadPoolExecutor$DelayedWorkQueue'>ScheduledThreadPoolExecutor.java:1088</a>)<br/>java.util.concurrent.ScheduledThreadPoolExecutor$DelayedWorkQueue.take(<a href='?part=source&amp;class=java.util.concurrent.ScheduledThreadPoolExecutor$DelayedWorkQueue#809' class='lightwindow' type='external' title='java.util.concurrent.ScheduledThreadPoolExecutor$DelayedWorkQueue'>ScheduledThreadPoolExecutor.java:809</a>)<br/>java.util.concurrent.ThreadPoolExecutor.getTask(<a href='?part=source&amp;class=java.util.concurrent.ThreadPoolExecutor#1074' class='lightwindow' type='external' title='java.util.concurrent.ThreadPoolExecutor'>ThreadPoolExecutor.java:1074</a>)<br/>java.util.concurrent.ThreadPoolExecutor.runWorker(<a href='?part=source&amp;class=java.util.concurrent.ThreadPoolExecutor#1134' class='lightwindow' type='external' title='java.util.concurrent.ThreadPoolExecutor'>ThreadPoolExecutor.java:1134</a>)<br/>java.util.concurrent.ThreadPoolExecutor$Worker.run(<a href='?part=source&amp;class=java.util.concurrent.ThreadPoolExecutor$Worker#624' class='lightwindow' type='external' title='java.util.concurrent.ThreadPoolExecutor$Worker'>ThreadPoolExecutor.java:624</a>)<br/>java.lang.Thread.run(<a href='?part=source&amp;class=java.lang.Thread#748' class='lightwindow' type='external' title='java.lang.Thread'>Thread.java:748</a>)<br/></em>
com.alibaba.nacos.client.Worker</div>
</td> <td align='center'>yes</td> <td align='right'>5</td> <td><img src='?resource=bullets/yellow.png' alt='WAITING'/>WAITING</td> <td>sun.misc.Unsafe.park(Native Method)</td> <td align='right'>0</td> <td align='right'>0</td> <td align='center' class='noPrint'><a href='?action=send_thread_interrupt&amp;threadId=40272_***********_89' onclick="javascript:return confirm('Do you confirm the sending of a thread interrupt signal to com.alibaba.nacos.client.Worker?');"><img width='16' height='16' src='?resource=action_interrupt.png' alt='Send a thread interrupt signal to com.alibaba.nacos.client.Worker. The thread can test Thread.currentThread().isInterrupted() to stop itself.' title='Send a thread interrupt signal to com.alibaba.nacos.client.Worker. The thread can test Thread.currentThread().isInterrupted() to stop itself.' /></a></td> <td align='center' class='noPrint'><a href='?action=kill_thread&amp;threadId=40272_***********_89' onclick="javascript:return confirm('杀掉一个Java线程是不推荐和不安全的，你确定要杀掉线程 com.alibaba.nacos.client.Worker ?');"><img width='16' height='16' src='?resource=stop.png' alt='杀掉线程&nbsp;com.alibaba.nacos.client.Worker' title='杀掉线程&nbsp;com.alibaba.nacos.client.Worker' /></a></td></tr><tr onmouseover="this.className='highlight'" onmouseout="this.className=''">
<td><div class='tooltip'>
<em>
com.alibaba.nacos.client.Worker<br/>
sun.misc.Unsafe.park(Native Method)<br/>java.util.concurrent.locks.LockSupport.parkNanos(<a href='?part=source&amp;class=java.util.concurrent.locks.LockSupport#215' class='lightwindow' type='external' title='java.util.concurrent.locks.LockSupport'>LockSupport.java:215</a>)<br/>java.util.concurrent.locks.AbstractQueuedSynchronizer$ConditionObject.awaitNanos(<a href='?part=source&amp;class=java.util.concurrent.locks.AbstractQueuedSynchronizer$ConditionObject#2078' class='lightwindow' type='external' title='java.util.concurrent.locks.AbstractQueuedSynchronizer$ConditionObject'>AbstractQueuedSynchronizer.java:2078</a>)<br/>java.util.concurrent.ScheduledThreadPoolExecutor$DelayedWorkQueue.take(<a href='?part=source&amp;class=java.util.concurrent.ScheduledThreadPoolExecutor$DelayedWorkQueue#1093' class='lightwindow' type='external' title='java.util.concurrent.ScheduledThreadPoolExecutor$DelayedWorkQueue'>ScheduledThreadPoolExecutor.java:1093</a>)<br/>java.util.concurrent.ScheduledThreadPoolExecutor$DelayedWorkQueue.take(<a href='?part=source&amp;class=java.util.concurrent.ScheduledThreadPoolExecutor$DelayedWorkQueue#809' class='lightwindow' type='external' title='java.util.concurrent.ScheduledThreadPoolExecutor$DelayedWorkQueue'>ScheduledThreadPoolExecutor.java:809</a>)<br/>java.util.concurrent.ThreadPoolExecutor.getTask(<a href='?part=source&amp;class=java.util.concurrent.ThreadPoolExecutor#1074' class='lightwindow' type='external' title='java.util.concurrent.ThreadPoolExecutor'>ThreadPoolExecutor.java:1074</a>)<br/>java.util.concurrent.ThreadPoolExecutor.runWorker(<a href='?part=source&amp;class=java.util.concurrent.ThreadPoolExecutor#1134' class='lightwindow' type='external' title='java.util.concurrent.ThreadPoolExecutor'>ThreadPoolExecutor.java:1134</a>)<br/>java.util.concurrent.ThreadPoolExecutor$Worker.run(<a href='?part=source&amp;class=java.util.concurrent.ThreadPoolExecutor$Worker#624' class='lightwindow' type='external' title='java.util.concurrent.ThreadPoolExecutor$Worker'>ThreadPoolExecutor.java:624</a>)<br/>java.lang.Thread.run(<a href='?part=source&amp;class=java.lang.Thread#748' class='lightwindow' type='external' title='java.lang.Thread'>Thread.java:748</a>)<br/></em>
com.alibaba.nacos.client.Worker</div>
</td> <td align='center'>yes</td> <td align='right'>5</td> <td><img src='?resource=bullets/yellow.png' alt='TIMED_WAITING'/>TIMED_WAITING</td> <td>sun.misc.Unsafe.park(Native Method)</td> <td align='right'>15</td> <td align='right'>15</td> <td align='center' class='noPrint'><a href='?action=send_thread_interrupt&amp;threadId=40272_***********_52' onclick="javascript:return confirm('Do you confirm the sending of a thread interrupt signal to com.alibaba.nacos.client.Worker?');"><img width='16' height='16' src='?resource=action_interrupt.png' alt='Send a thread interrupt signal to com.alibaba.nacos.client.Worker. The thread can test Thread.currentThread().isInterrupted() to stop itself.' title='Send a thread interrupt signal to com.alibaba.nacos.client.Worker. The thread can test Thread.currentThread().isInterrupted() to stop itself.' /></a></td> <td align='center' class='noPrint'><a href='?action=kill_thread&amp;threadId=40272_***********_52' onclick="javascript:return confirm('杀掉一个Java线程是不推荐和不安全的，你确定要杀掉线程 com.alibaba.nacos.client.Worker ?');"><img width='16' height='16' src='?resource=stop.png' alt='杀掉线程&nbsp;com.alibaba.nacos.client.Worker' title='杀掉线程&nbsp;com.alibaba.nacos.client.Worker' /></a></td></tr><tr class='odd' onmouseover="this.className='highlight'" onmouseout="this.className='odd'">
<td><div class='tooltip'>
<em>
com.alibaba.nacos.client.Worker<br/>
sun.misc.Unsafe.park(Native Method)<br/>java.util.concurrent.locks.LockSupport.parkNanos(<a href='?part=source&amp;class=java.util.concurrent.locks.LockSupport#215' class='lightwindow' type='external' title='java.util.concurrent.locks.LockSupport'>LockSupport.java:215</a>)<br/>java.util.concurrent.locks.AbstractQueuedSynchronizer$ConditionObject.awaitNanos(<a href='?part=source&amp;class=java.util.concurrent.locks.AbstractQueuedSynchronizer$ConditionObject#2078' class='lightwindow' type='external' title='java.util.concurrent.locks.AbstractQueuedSynchronizer$ConditionObject'>AbstractQueuedSynchronizer.java:2078</a>)<br/>java.util.concurrent.ArrayBlockingQueue.poll(<a href='?part=source&amp;class=java.util.concurrent.ArrayBlockingQueue#418' class='lightwindow' type='external' title='java.util.concurrent.ArrayBlockingQueue'>ArrayBlockingQueue.java:418</a>)<br/>com.alibaba.nacos.client.config.impl.ClientWorker$ConfigRpcTransportClient.lambda$startInternal$2(<a href='?part=source&amp;class=com.alibaba.nacos.client.config.impl.ClientWorker$ConfigRpcTransportClient#691' class='lightwindow' type='external' title='com.alibaba.nacos.client.config.impl.ClientWorker$ConfigRpcTransportClient'>ClientWorker.java:691</a>)<br/>com.alibaba.nacos.client.config.impl.ClientWorker$ConfigRpcTransportClient$$Lambda$258/703972090.run(Unknown Source)<br/>java.util.concurrent.Executors$RunnableAdapter.call(<a href='?part=source&amp;class=java.util.concurrent.Executors$RunnableAdapter#511' class='lightwindow' type='external' title='java.util.concurrent.Executors$RunnableAdapter'>Executors.java:511</a>)<br/>java.util.concurrent.FutureTask.run(<a href='?part=source&amp;class=java.util.concurrent.FutureTask#266' class='lightwindow' type='external' title='java.util.concurrent.FutureTask'>FutureTask.java:266</a>)<br/>java.util.concurrent.ScheduledThreadPoolExecutor$ScheduledFutureTask.access$201(<a href='?part=source&amp;class=java.util.concurrent.ScheduledThreadPoolExecutor$ScheduledFutureTask#180' class='lightwindow' type='external' title='java.util.concurrent.ScheduledThreadPoolExecutor$ScheduledFutureTask'>ScheduledThreadPoolExecutor.java:180</a>)<br/>java.util.concurrent.ScheduledThreadPoolExecutor$ScheduledFutureTask.run(<a href='?part=source&amp;class=java.util.concurrent.ScheduledThreadPoolExecutor$ScheduledFutureTask#293' class='lightwindow' type='external' title='java.util.concurrent.ScheduledThreadPoolExecutor$ScheduledFutureTask'>ScheduledThreadPoolExecutor.java:293</a>)<br/>java.util.concurrent.ThreadPoolExecutor.runWorker(<a href='?part=source&amp;class=java.util.concurrent.ThreadPoolExecutor#1149' class='lightwindow' type='external' title='java.util.concurrent.ThreadPoolExecutor'>ThreadPoolExecutor.java:1149</a>)<br/>java.util.concurrent.ThreadPoolExecutor$Worker.run(<a href='?part=source&amp;class=java.util.concurrent.ThreadPoolExecutor$Worker#624' class='lightwindow' type='external' title='java.util.concurrent.ThreadPoolExecutor$Worker'>ThreadPoolExecutor.java:624</a>)<br/>java.lang.Thread.run(<a href='?part=source&amp;class=java.lang.Thread#748' class='lightwindow' type='external' title='java.lang.Thread'>Thread.java:748</a>)<br/></em>
com.alibaba.nacos.client.Worker</div>
</td> <td align='center'>yes</td> <td align='right'>5</td> <td><img src='?resource=bullets/yellow.png' alt='TIMED_WAITING'/>TIMED_WAITING</td> <td>sun.misc.Unsafe.park(Native Method)</td> <td align='right'>0</td> <td align='right'>0</td> <td align='center' class='noPrint'><a href='?action=send_thread_interrupt&amp;threadId=40272_***********_22' onclick="javascript:return confirm('Do you confirm the sending of a thread interrupt signal to com.alibaba.nacos.client.Worker?');"><img width='16' height='16' src='?resource=action_interrupt.png' alt='Send a thread interrupt signal to com.alibaba.nacos.client.Worker. The thread can test Thread.currentThread().isInterrupted() to stop itself.' title='Send a thread interrupt signal to com.alibaba.nacos.client.Worker. The thread can test Thread.currentThread().isInterrupted() to stop itself.' /></a></td> <td align='center' class='noPrint'><a href='?action=kill_thread&amp;threadId=40272_***********_22' onclick="javascript:return confirm('杀掉一个Java线程是不推荐和不安全的，你确定要杀掉线程 com.alibaba.nacos.client.Worker ?');"><img width='16' height='16' src='?resource=stop.png' alt='杀掉线程&nbsp;com.alibaba.nacos.client.Worker' title='杀掉线程&nbsp;com.alibaba.nacos.client.Worker' /></a></td></tr><tr onmouseover="this.className='highlight'" onmouseout="this.className=''">
<td><div class='tooltip'>
<em>
com.alibaba.nacos.client.Worker<br/>
sun.misc.Unsafe.park(Native Method)<br/>java.util.concurrent.locks.LockSupport.park(<a href='?part=source&amp;class=java.util.concurrent.locks.LockSupport#175' class='lightwindow' type='external' title='java.util.concurrent.locks.LockSupport'>LockSupport.java:175</a>)<br/>java.util.concurrent.locks.AbstractQueuedSynchronizer$ConditionObject.await(<a href='?part=source&amp;class=java.util.concurrent.locks.AbstractQueuedSynchronizer$ConditionObject#2039' class='lightwindow' type='external' title='java.util.concurrent.locks.AbstractQueuedSynchronizer$ConditionObject'>AbstractQueuedSynchronizer.java:2039</a>)<br/>java.util.concurrent.ScheduledThreadPoolExecutor$DelayedWorkQueue.take(<a href='?part=source&amp;class=java.util.concurrent.ScheduledThreadPoolExecutor$DelayedWorkQueue#1088' class='lightwindow' type='external' title='java.util.concurrent.ScheduledThreadPoolExecutor$DelayedWorkQueue'>ScheduledThreadPoolExecutor.java:1088</a>)<br/>java.util.concurrent.ScheduledThreadPoolExecutor$DelayedWorkQueue.take(<a href='?part=source&amp;class=java.util.concurrent.ScheduledThreadPoolExecutor$DelayedWorkQueue#809' class='lightwindow' type='external' title='java.util.concurrent.ScheduledThreadPoolExecutor$DelayedWorkQueue'>ScheduledThreadPoolExecutor.java:809</a>)<br/>java.util.concurrent.ThreadPoolExecutor.getTask(<a href='?part=source&amp;class=java.util.concurrent.ThreadPoolExecutor#1074' class='lightwindow' type='external' title='java.util.concurrent.ThreadPoolExecutor'>ThreadPoolExecutor.java:1074</a>)<br/>java.util.concurrent.ThreadPoolExecutor.runWorker(<a href='?part=source&amp;class=java.util.concurrent.ThreadPoolExecutor#1134' class='lightwindow' type='external' title='java.util.concurrent.ThreadPoolExecutor'>ThreadPoolExecutor.java:1134</a>)<br/>java.util.concurrent.ThreadPoolExecutor$Worker.run(<a href='?part=source&amp;class=java.util.concurrent.ThreadPoolExecutor$Worker#624' class='lightwindow' type='external' title='java.util.concurrent.ThreadPoolExecutor$Worker'>ThreadPoolExecutor.java:624</a>)<br/>java.lang.Thread.run(<a href='?part=source&amp;class=java.lang.Thread#748' class='lightwindow' type='external' title='java.lang.Thread'>Thread.java:748</a>)<br/></em>
com.alibaba.nacos.client.Worker</div>
</td> <td align='center'>yes</td> <td align='right'>5</td> <td><img src='?resource=bullets/yellow.png' alt='WAITING'/>WAITING</td> <td>sun.misc.Unsafe.park(Native Method)</td> <td align='right'>0</td> <td align='right'>0</td> <td align='center' class='noPrint'><a href='?action=send_thread_interrupt&amp;threadId=40272_***********_48' onclick="javascript:return confirm('Do you confirm the sending of a thread interrupt signal to com.alibaba.nacos.client.Worker?');"><img width='16' height='16' src='?resource=action_interrupt.png' alt='Send a thread interrupt signal to com.alibaba.nacos.client.Worker. The thread can test Thread.currentThread().isInterrupted() to stop itself.' title='Send a thread interrupt signal to com.alibaba.nacos.client.Worker. The thread can test Thread.currentThread().isInterrupted() to stop itself.' /></a></td> <td align='center' class='noPrint'><a href='?action=kill_thread&amp;threadId=40272_***********_48' onclick="javascript:return confirm('杀掉一个Java线程是不推荐和不安全的，你确定要杀掉线程 com.alibaba.nacos.client.Worker ?');"><img width='16' height='16' src='?resource=stop.png' alt='杀掉线程&nbsp;com.alibaba.nacos.client.Worker' title='杀掉线程&nbsp;com.alibaba.nacos.client.Worker' /></a></td></tr><tr class='odd' onmouseover="this.className='highlight'" onmouseout="this.className='odd'">
<td><div class='tooltip'>
<em>
com.alibaba.nacos.client.Worker<br/>
sun.misc.Unsafe.park(Native Method)<br/>java.util.concurrent.locks.LockSupport.park(<a href='?part=source&amp;class=java.util.concurrent.locks.LockSupport#175' class='lightwindow' type='external' title='java.util.concurrent.locks.LockSupport'>LockSupport.java:175</a>)<br/>java.util.concurrent.locks.AbstractQueuedSynchronizer$ConditionObject.await(<a href='?part=source&amp;class=java.util.concurrent.locks.AbstractQueuedSynchronizer$ConditionObject#2039' class='lightwindow' type='external' title='java.util.concurrent.locks.AbstractQueuedSynchronizer$ConditionObject'>AbstractQueuedSynchronizer.java:2039</a>)<br/>java.util.concurrent.ScheduledThreadPoolExecutor$DelayedWorkQueue.take(<a href='?part=source&amp;class=java.util.concurrent.ScheduledThreadPoolExecutor$DelayedWorkQueue#1088' class='lightwindow' type='external' title='java.util.concurrent.ScheduledThreadPoolExecutor$DelayedWorkQueue'>ScheduledThreadPoolExecutor.java:1088</a>)<br/>java.util.concurrent.ScheduledThreadPoolExecutor$DelayedWorkQueue.take(<a href='?part=source&amp;class=java.util.concurrent.ScheduledThreadPoolExecutor$DelayedWorkQueue#809' class='lightwindow' type='external' title='java.util.concurrent.ScheduledThreadPoolExecutor$DelayedWorkQueue'>ScheduledThreadPoolExecutor.java:809</a>)<br/>java.util.concurrent.ThreadPoolExecutor.getTask(<a href='?part=source&amp;class=java.util.concurrent.ThreadPoolExecutor#1074' class='lightwindow' type='external' title='java.util.concurrent.ThreadPoolExecutor'>ThreadPoolExecutor.java:1074</a>)<br/>java.util.concurrent.ThreadPoolExecutor.runWorker(<a href='?part=source&amp;class=java.util.concurrent.ThreadPoolExecutor#1134' class='lightwindow' type='external' title='java.util.concurrent.ThreadPoolExecutor'>ThreadPoolExecutor.java:1134</a>)<br/>java.util.concurrent.ThreadPoolExecutor$Worker.run(<a href='?part=source&amp;class=java.util.concurrent.ThreadPoolExecutor$Worker#624' class='lightwindow' type='external' title='java.util.concurrent.ThreadPoolExecutor$Worker'>ThreadPoolExecutor.java:624</a>)<br/>java.lang.Thread.run(<a href='?part=source&amp;class=java.lang.Thread#748' class='lightwindow' type='external' title='java.lang.Thread'>Thread.java:748</a>)<br/></em>
com.alibaba.nacos.client.Worker</div>
</td> <td align='center'>yes</td> <td align='right'>5</td> <td><img src='?resource=bullets/yellow.png' alt='WAITING'/>WAITING</td> <td>sun.misc.Unsafe.park(Native Method)</td> <td align='right'>0</td> <td align='right'>0</td> <td align='center' class='noPrint'><a href='?action=send_thread_interrupt&amp;threadId=40272_***********_94' onclick="javascript:return confirm('Do you confirm the sending of a thread interrupt signal to com.alibaba.nacos.client.Worker?');"><img width='16' height='16' src='?resource=action_interrupt.png' alt='Send a thread interrupt signal to com.alibaba.nacos.client.Worker. The thread can test Thread.currentThread().isInterrupted() to stop itself.' title='Send a thread interrupt signal to com.alibaba.nacos.client.Worker. The thread can test Thread.currentThread().isInterrupted() to stop itself.' /></a></td> <td align='center' class='noPrint'><a href='?action=kill_thread&amp;threadId=40272_***********_94' onclick="javascript:return confirm('杀掉一个Java线程是不推荐和不安全的，你确定要杀掉线程 com.alibaba.nacos.client.Worker ?');"><img width='16' height='16' src='?resource=stop.png' alt='杀掉线程&nbsp;com.alibaba.nacos.client.Worker' title='杀掉线程&nbsp;com.alibaba.nacos.client.Worker' /></a></td></tr><tr onmouseover="this.className='highlight'" onmouseout="this.className=''">
<td><div class='tooltip'>
<em>
com.alibaba.nacos.client.Worker<br/>
sun.misc.Unsafe.park(Native Method)<br/>java.util.concurrent.locks.LockSupport.park(<a href='?part=source&amp;class=java.util.concurrent.locks.LockSupport#175' class='lightwindow' type='external' title='java.util.concurrent.locks.LockSupport'>LockSupport.java:175</a>)<br/>java.util.concurrent.locks.AbstractQueuedSynchronizer$ConditionObject.await(<a href='?part=source&amp;class=java.util.concurrent.locks.AbstractQueuedSynchronizer$ConditionObject#2039' class='lightwindow' type='external' title='java.util.concurrent.locks.AbstractQueuedSynchronizer$ConditionObject'>AbstractQueuedSynchronizer.java:2039</a>)<br/>java.util.concurrent.ScheduledThreadPoolExecutor$DelayedWorkQueue.take(<a href='?part=source&amp;class=java.util.concurrent.ScheduledThreadPoolExecutor$DelayedWorkQueue#1088' class='lightwindow' type='external' title='java.util.concurrent.ScheduledThreadPoolExecutor$DelayedWorkQueue'>ScheduledThreadPoolExecutor.java:1088</a>)<br/>java.util.concurrent.ScheduledThreadPoolExecutor$DelayedWorkQueue.take(<a href='?part=source&amp;class=java.util.concurrent.ScheduledThreadPoolExecutor$DelayedWorkQueue#809' class='lightwindow' type='external' title='java.util.concurrent.ScheduledThreadPoolExecutor$DelayedWorkQueue'>ScheduledThreadPoolExecutor.java:809</a>)<br/>java.util.concurrent.ThreadPoolExecutor.getTask(<a href='?part=source&amp;class=java.util.concurrent.ThreadPoolExecutor#1074' class='lightwindow' type='external' title='java.util.concurrent.ThreadPoolExecutor'>ThreadPoolExecutor.java:1074</a>)<br/>java.util.concurrent.ThreadPoolExecutor.runWorker(<a href='?part=source&amp;class=java.util.concurrent.ThreadPoolExecutor#1134' class='lightwindow' type='external' title='java.util.concurrent.ThreadPoolExecutor'>ThreadPoolExecutor.java:1134</a>)<br/>java.util.concurrent.ThreadPoolExecutor$Worker.run(<a href='?part=source&amp;class=java.util.concurrent.ThreadPoolExecutor$Worker#624' class='lightwindow' type='external' title='java.util.concurrent.ThreadPoolExecutor$Worker'>ThreadPoolExecutor.java:624</a>)<br/>java.lang.Thread.run(<a href='?part=source&amp;class=java.lang.Thread#748' class='lightwindow' type='external' title='java.lang.Thread'>Thread.java:748</a>)<br/></em>
com.alibaba.nacos.client.Worker</div>
</td> <td align='center'>yes</td> <td align='right'>5</td> <td><img src='?resource=bullets/yellow.png' alt='WAITING'/>WAITING</td> <td>sun.misc.Unsafe.park(Native Method)</td> <td align='right'>0</td> <td align='right'>0</td> <td align='center' class='noPrint'><a href='?action=send_thread_interrupt&amp;threadId=40272_***********_23' onclick="javascript:return confirm('Do you confirm the sending of a thread interrupt signal to com.alibaba.nacos.client.Worker?');"><img width='16' height='16' src='?resource=action_interrupt.png' alt='Send a thread interrupt signal to com.alibaba.nacos.client.Worker. The thread can test Thread.currentThread().isInterrupted() to stop itself.' title='Send a thread interrupt signal to com.alibaba.nacos.client.Worker. The thread can test Thread.currentThread().isInterrupted() to stop itself.' /></a></td> <td align='center' class='noPrint'><a href='?action=kill_thread&amp;threadId=40272_***********_23' onclick="javascript:return confirm('杀掉一个Java线程是不推荐和不安全的，你确定要杀掉线程 com.alibaba.nacos.client.Worker ?');"><img width='16' height='16' src='?resource=stop.png' alt='杀掉线程&nbsp;com.alibaba.nacos.client.Worker' title='杀掉线程&nbsp;com.alibaba.nacos.client.Worker' /></a></td></tr><tr class='odd' onmouseover="this.className='highlight'" onmouseout="this.className='odd'">
<td><div class='tooltip'>
<em>
com.alibaba.nacos.client.Worker<br/>
sun.misc.Unsafe.park(Native Method)<br/>java.util.concurrent.locks.LockSupport.park(<a href='?part=source&amp;class=java.util.concurrent.locks.LockSupport#175' class='lightwindow' type='external' title='java.util.concurrent.locks.LockSupport'>LockSupport.java:175</a>)<br/>java.util.concurrent.locks.AbstractQueuedSynchronizer$ConditionObject.await(<a href='?part=source&amp;class=java.util.concurrent.locks.AbstractQueuedSynchronizer$ConditionObject#2039' class='lightwindow' type='external' title='java.util.concurrent.locks.AbstractQueuedSynchronizer$ConditionObject'>AbstractQueuedSynchronizer.java:2039</a>)<br/>java.util.concurrent.ScheduledThreadPoolExecutor$DelayedWorkQueue.take(<a href='?part=source&amp;class=java.util.concurrent.ScheduledThreadPoolExecutor$DelayedWorkQueue#1088' class='lightwindow' type='external' title='java.util.concurrent.ScheduledThreadPoolExecutor$DelayedWorkQueue'>ScheduledThreadPoolExecutor.java:1088</a>)<br/>java.util.concurrent.ScheduledThreadPoolExecutor$DelayedWorkQueue.take(<a href='?part=source&amp;class=java.util.concurrent.ScheduledThreadPoolExecutor$DelayedWorkQueue#809' class='lightwindow' type='external' title='java.util.concurrent.ScheduledThreadPoolExecutor$DelayedWorkQueue'>ScheduledThreadPoolExecutor.java:809</a>)<br/>java.util.concurrent.ThreadPoolExecutor.getTask(<a href='?part=source&amp;class=java.util.concurrent.ThreadPoolExecutor#1074' class='lightwindow' type='external' title='java.util.concurrent.ThreadPoolExecutor'>ThreadPoolExecutor.java:1074</a>)<br/>java.util.concurrent.ThreadPoolExecutor.runWorker(<a href='?part=source&amp;class=java.util.concurrent.ThreadPoolExecutor#1134' class='lightwindow' type='external' title='java.util.concurrent.ThreadPoolExecutor'>ThreadPoolExecutor.java:1134</a>)<br/>java.util.concurrent.ThreadPoolExecutor$Worker.run(<a href='?part=source&amp;class=java.util.concurrent.ThreadPoolExecutor$Worker#624' class='lightwindow' type='external' title='java.util.concurrent.ThreadPoolExecutor$Worker'>ThreadPoolExecutor.java:624</a>)<br/>java.lang.Thread.run(<a href='?part=source&amp;class=java.lang.Thread#748' class='lightwindow' type='external' title='java.lang.Thread'>Thread.java:748</a>)<br/></em>
com.alibaba.nacos.client.Worker</div>
</td> <td align='center'>yes</td> <td align='right'>5</td> <td><img src='?resource=bullets/yellow.png' alt='WAITING'/>WAITING</td> <td>sun.misc.Unsafe.park(Native Method)</td> <td align='right'>0</td> <td align='right'>0</td> <td align='center' class='noPrint'><a href='?action=send_thread_interrupt&amp;threadId=40272_***********_671' onclick="javascript:return confirm('Do you confirm the sending of a thread interrupt signal to com.alibaba.nacos.client.Worker?');"><img width='16' height='16' src='?resource=action_interrupt.png' alt='Send a thread interrupt signal to com.alibaba.nacos.client.Worker. The thread can test Thread.currentThread().isInterrupted() to stop itself.' title='Send a thread interrupt signal to com.alibaba.nacos.client.Worker. The thread can test Thread.currentThread().isInterrupted() to stop itself.' /></a></td> <td align='center' class='noPrint'><a href='?action=kill_thread&amp;threadId=40272_***********_671' onclick="javascript:return confirm('杀掉一个Java线程是不推荐和不安全的，你确定要杀掉线程 com.alibaba.nacos.client.Worker ?');"><img width='16' height='16' src='?resource=stop.png' alt='杀掉线程&nbsp;com.alibaba.nacos.client.Worker' title='杀掉线程&nbsp;com.alibaba.nacos.client.Worker' /></a></td></tr><tr onmouseover="this.className='highlight'" onmouseout="this.className=''">
<td><div class='tooltip'>
<em>
com.alibaba.nacos.client.Worker<br/>
sun.misc.Unsafe.park(Native Method)<br/>java.util.concurrent.locks.LockSupport.park(<a href='?part=source&amp;class=java.util.concurrent.locks.LockSupport#175' class='lightwindow' type='external' title='java.util.concurrent.locks.LockSupport'>LockSupport.java:175</a>)<br/>java.util.concurrent.locks.AbstractQueuedSynchronizer$ConditionObject.await(<a href='?part=source&amp;class=java.util.concurrent.locks.AbstractQueuedSynchronizer$ConditionObject#2039' class='lightwindow' type='external' title='java.util.concurrent.locks.AbstractQueuedSynchronizer$ConditionObject'>AbstractQueuedSynchronizer.java:2039</a>)<br/>java.util.concurrent.ScheduledThreadPoolExecutor$DelayedWorkQueue.take(<a href='?part=source&amp;class=java.util.concurrent.ScheduledThreadPoolExecutor$DelayedWorkQueue#1088' class='lightwindow' type='external' title='java.util.concurrent.ScheduledThreadPoolExecutor$DelayedWorkQueue'>ScheduledThreadPoolExecutor.java:1088</a>)<br/>java.util.concurrent.ScheduledThreadPoolExecutor$DelayedWorkQueue.take(<a href='?part=source&amp;class=java.util.concurrent.ScheduledThreadPoolExecutor$DelayedWorkQueue#809' class='lightwindow' type='external' title='java.util.concurrent.ScheduledThreadPoolExecutor$DelayedWorkQueue'>ScheduledThreadPoolExecutor.java:809</a>)<br/>java.util.concurrent.ThreadPoolExecutor.getTask(<a href='?part=source&amp;class=java.util.concurrent.ThreadPoolExecutor#1074' class='lightwindow' type='external' title='java.util.concurrent.ThreadPoolExecutor'>ThreadPoolExecutor.java:1074</a>)<br/>java.util.concurrent.ThreadPoolExecutor.runWorker(<a href='?part=source&amp;class=java.util.concurrent.ThreadPoolExecutor#1134' class='lightwindow' type='external' title='java.util.concurrent.ThreadPoolExecutor'>ThreadPoolExecutor.java:1134</a>)<br/>java.util.concurrent.ThreadPoolExecutor$Worker.run(<a href='?part=source&amp;class=java.util.concurrent.ThreadPoolExecutor$Worker#624' class='lightwindow' type='external' title='java.util.concurrent.ThreadPoolExecutor$Worker'>ThreadPoolExecutor.java:624</a>)<br/>java.lang.Thread.run(<a href='?part=source&amp;class=java.lang.Thread#748' class='lightwindow' type='external' title='java.lang.Thread'>Thread.java:748</a>)<br/></em>
com.alibaba.nacos.client.Worker</div>
</td> <td align='center'>yes</td> <td align='right'>5</td> <td><img src='?resource=bullets/yellow.png' alt='WAITING'/>WAITING</td> <td>sun.misc.Unsafe.park(Native Method)</td> <td align='right'>0</td> <td align='right'>0</td> <td align='center' class='noPrint'><a href='?action=send_thread_interrupt&amp;threadId=40272_***********_56' onclick="javascript:return confirm('Do you confirm the sending of a thread interrupt signal to com.alibaba.nacos.client.Worker?');"><img width='16' height='16' src='?resource=action_interrupt.png' alt='Send a thread interrupt signal to com.alibaba.nacos.client.Worker. The thread can test Thread.currentThread().isInterrupted() to stop itself.' title='Send a thread interrupt signal to com.alibaba.nacos.client.Worker. The thread can test Thread.currentThread().isInterrupted() to stop itself.' /></a></td> <td align='center' class='noPrint'><a href='?action=kill_thread&amp;threadId=40272_***********_56' onclick="javascript:return confirm('杀掉一个Java线程是不推荐和不安全的，你确定要杀掉线程 com.alibaba.nacos.client.Worker ?');"><img width='16' height='16' src='?resource=stop.png' alt='杀掉线程&nbsp;com.alibaba.nacos.client.Worker' title='杀掉线程&nbsp;com.alibaba.nacos.client.Worker' /></a></td></tr><tr class='odd' onmouseover="this.className='highlight'" onmouseout="this.className='odd'">
<td><div class='tooltip'>
<em>
com.alibaba.nacos.naming.push.receiver<br/>
java.net.DualStackPlainDatagramSocketImpl.socketReceiveOrPeekData(Native Method)<br/>java.net.DualStackPlainDatagramSocketImpl.receive0(<a href='?part=source&amp;class=java.net.DualStackPlainDatagramSocketImpl#124' class='lightwindow' type='external' title='java.net.DualStackPlainDatagramSocketImpl'>DualStackPlainDatagramSocketImpl.java:124</a>)<br/>java.net.AbstractPlainDatagramSocketImpl.receive(<a href='?part=source&amp;class=java.net.AbstractPlainDatagramSocketImpl#143' class='lightwindow' type='external' title='java.net.AbstractPlainDatagramSocketImpl'>AbstractPlainDatagramSocketImpl.java:143</a>)<br/>java.net.DatagramSocket.receive(<a href='?part=source&amp;class=java.net.DatagramSocket#812' class='lightwindow' type='external' title='java.net.DatagramSocket'>DatagramSocket.java:812</a>)<br/>com.alibaba.nacos.client.naming.core.PushReceiver.run(<a href='?part=source&amp;class=com.alibaba.nacos.client.naming.core.PushReceiver#102' class='lightwindow' type='external' title='com.alibaba.nacos.client.naming.core.PushReceiver'>PushReceiver.java:102</a>)<br/>java.util.concurrent.Executors$RunnableAdapter.call(<a href='?part=source&amp;class=java.util.concurrent.Executors$RunnableAdapter#511' class='lightwindow' type='external' title='java.util.concurrent.Executors$RunnableAdapter'>Executors.java:511</a>)<br/>java.util.concurrent.FutureTask.run(<a href='?part=source&amp;class=java.util.concurrent.FutureTask#266' class='lightwindow' type='external' title='java.util.concurrent.FutureTask'>FutureTask.java:266</a>)<br/>java.util.concurrent.ScheduledThreadPoolExecutor$ScheduledFutureTask.access$201(<a href='?part=source&amp;class=java.util.concurrent.ScheduledThreadPoolExecutor$ScheduledFutureTask#180' class='lightwindow' type='external' title='java.util.concurrent.ScheduledThreadPoolExecutor$ScheduledFutureTask'>ScheduledThreadPoolExecutor.java:180</a>)<br/>java.util.concurrent.ScheduledThreadPoolExecutor$ScheduledFutureTask.run(<a href='?part=source&amp;class=java.util.concurrent.ScheduledThreadPoolExecutor$ScheduledFutureTask#293' class='lightwindow' type='external' title='java.util.concurrent.ScheduledThreadPoolExecutor$ScheduledFutureTask'>ScheduledThreadPoolExecutor.java:293</a>)<br/>java.util.concurrent.ThreadPoolExecutor.runWorker(<a href='?part=source&amp;class=java.util.concurrent.ThreadPoolExecutor#1149' class='lightwindow' type='external' title='java.util.concurrent.ThreadPoolExecutor'>ThreadPoolExecutor.java:1149</a>)<br/>java.util.concurrent.ThreadPoolExecutor$Worker.run(<a href='?part=source&amp;class=java.util.concurrent.ThreadPoolExecutor$Worker#624' class='lightwindow' type='external' title='java.util.concurrent.ThreadPoolExecutor$Worker'>ThreadPoolExecutor.java:624</a>)<br/>java.lang.Thread.run(<a href='?part=source&amp;class=java.lang.Thread#748' class='lightwindow' type='external' title='java.lang.Thread'>Thread.java:748</a>)<br/></em>
com.alibaba.nacos.naming.push.receiver</div>
</td> <td align='center'>yes</td> <td align='right'>5</td> <td><img src='?resource=bullets/green.png' alt='RUNNABLE'/>RUNNABLE</td> <td>java.net.DualStackPlainDatagramSocketImpl.socketReceiveOrPeekData(Native Method)</td> <td align='right'>0</td> <td align='right'>0</td> <td align='center' class='noPrint'><a href='?action=send_thread_interrupt&amp;threadId=40272_***********_607' onclick="javascript:return confirm('Do you confirm the sending of a thread interrupt signal to com.alibaba.nacos.naming.push.receiver?');"><img width='16' height='16' src='?resource=action_interrupt.png' alt='Send a thread interrupt signal to com.alibaba.nacos.naming.push.receiver. The thread can test Thread.currentThread().isInterrupted() to stop itself.' title='Send a thread interrupt signal to com.alibaba.nacos.naming.push.receiver. The thread can test Thread.currentThread().isInterrupted() to stop itself.' /></a></td> <td align='center' class='noPrint'><a href='?action=kill_thread&amp;threadId=40272_***********_607' onclick="javascript:return confirm('杀掉一个Java线程是不推荐和不安全的，你确定要杀掉线程 com.alibaba.nacos.naming.push.receiver ?');"><img width='16' height='16' src='?resource=stop.png' alt='杀掉线程&nbsp;com.alibaba.nacos.naming.push.receiver' title='杀掉线程&nbsp;com.alibaba.nacos.naming.push.receiver' /></a></td></tr><tr onmouseover="this.className='highlight'" onmouseout="this.className=''">
<td><div class='tooltip'>
<em>
container-0<br/>
java.lang.Thread.sleep(Native Method)<br/>org.apache.catalina.core.StandardServer.await(<a href='?part=source&amp;class=org.apache.catalina.core.StandardServer#566' class='lightwindow' type='external' title='org.apache.catalina.core.StandardServer'>StandardServer.java:566</a>)<br/>org.springframework.boot.web.embedded.tomcat.TomcatWebServer$1.run(<a href='?part=source&amp;class=org.springframework.boot.web.embedded.tomcat.TomcatWebServer$1#181' class='lightwindow' type='external' title='org.springframework.boot.web.embedded.tomcat.TomcatWebServer$1'>TomcatWebServer.java:181</a>)<br/></em>
container-0</div>
</td> <td align='center'>no</td> <td align='right'>5</td> <td><img src='?resource=bullets/blue.png' alt='TIMED_WAITING'/>TIMED_WAITING</td> <td>java.lang.Thread.sleep(Native Method)</td> <td align='right'>0</td> <td align='right'>0</td> <td align='center' class='noPrint'><a href='?action=send_thread_interrupt&amp;threadId=40272_***********_85' onclick="javascript:return confirm('Do you confirm the sending of a thread interrupt signal to container-0?');"><img width='16' height='16' src='?resource=action_interrupt.png' alt='Send a thread interrupt signal to container-0. The thread can test Thread.currentThread().isInterrupted() to stop itself.' title='Send a thread interrupt signal to container-0. The thread can test Thread.currentThread().isInterrupted() to stop itself.' /></a></td> <td align='center' class='noPrint'><a href='?action=kill_thread&amp;threadId=40272_***********_85' onclick="javascript:return confirm('杀掉一个Java线程是不推荐和不安全的，你确定要杀掉线程 container-0 ?');"><img width='16' height='16' src='?resource=stop.png' alt='杀掉线程&nbsp;container-0' title='杀掉线程&nbsp;container-0' /></a></td></tr><tr class='odd' onmouseover="this.className='highlight'" onmouseout="this.className='odd'">
<td>DestroyJavaVM</td> <td align='center'>no</td> <td align='right'>5</td> <td><img src='?resource=bullets/green.png' alt='RUNNABLE'/>RUNNABLE</td> <td>&nbsp;</td> <td align='right'>750</td> <td align='right'>515</td> <td align='center' class='noPrint'><a href='?action=send_thread_interrupt&amp;threadId=40272_***********_667' onclick="javascript:return confirm('Do you confirm the sending of a thread interrupt signal to DestroyJavaVM?');"><img width='16' height='16' src='?resource=action_interrupt.png' alt='Send a thread interrupt signal to DestroyJavaVM. The thread can test Thread.currentThread().isInterrupted() to stop itself.' title='Send a thread interrupt signal to DestroyJavaVM. The thread can test Thread.currentThread().isInterrupted() to stop itself.' /></a></td> <td align='center' class='noPrint'><a href='?action=kill_thread&amp;threadId=40272_***********_667' onclick="javascript:return confirm('杀掉一个Java线程是不推荐和不安全的，你确定要杀掉线程 DestroyJavaVM ?');"><img width='16' height='16' src='?resource=stop.png' alt='杀掉线程&nbsp;DestroyJavaVM' title='杀掉线程&nbsp;DestroyJavaVM' /></a></td></tr><tr onmouseover="this.className='highlight'" onmouseout="this.className=''">
<td><div class='tooltip'>
<em>
Finalizer<br/>
java.lang.Object.wait(Native Method)<br/>java.lang.ref.ReferenceQueue.remove(<a href='?part=source&amp;class=java.lang.ref.ReferenceQueue#144' class='lightwindow' type='external' title='java.lang.ref.ReferenceQueue'>ReferenceQueue.java:144</a>)<br/>java.lang.ref.ReferenceQueue.remove(<a href='?part=source&amp;class=java.lang.ref.ReferenceQueue#165' class='lightwindow' type='external' title='java.lang.ref.ReferenceQueue'>ReferenceQueue.java:165</a>)<br/>java.lang.ref.Finalizer$FinalizerThread.run(<a href='?part=source&amp;class=java.lang.ref.Finalizer$FinalizerThread#216' class='lightwindow' type='external' title='java.lang.ref.Finalizer$FinalizerThread'>Finalizer.java:216</a>)<br/></em>
Finalizer</div>
</td> <td align='center'>yes</td> <td align='right'>8</td> <td><img src='?resource=bullets/yellow.png' alt='WAITING'/>WAITING</td> <td>java.lang.Object.wait(Native Method)</td> <td align='right'>46</td> <td align='right'>31</td> <td align='center' class='noPrint'><a href='?action=send_thread_interrupt&amp;threadId=40272_***********_3' onclick="javascript:return confirm('Do you confirm the sending of a thread interrupt signal to Finalizer?');"><img width='16' height='16' src='?resource=action_interrupt.png' alt='Send a thread interrupt signal to Finalizer. The thread can test Thread.currentThread().isInterrupted() to stop itself.' title='Send a thread interrupt signal to Finalizer. The thread can test Thread.currentThread().isInterrupted() to stop itself.' /></a></td> <td align='center' class='noPrint'><a href='?action=kill_thread&amp;threadId=40272_***********_3' onclick="javascript:return confirm('杀掉一个Java线程是不推荐和不安全的，你确定要杀掉线程 Finalizer ?');"><img width='16' height='16' src='?resource=stop.png' alt='杀掉线程&nbsp;Finalizer' title='杀掉线程&nbsp;Finalizer' /></a></td></tr><tr class='odd' onmouseover="this.className='highlight'" onmouseout="this.className='odd'">
<td><div class='tooltip'>
<em>
GC&nbsp;Daemon<br/>
java.lang.Object.wait(Native Method)<br/>sun.misc.GC$Daemon.run(<a href='?part=source&amp;class=sun.misc.GC$Daemon#117' class='lightwindow' type='external' title='sun.misc.GC$Daemon'>GC.java:117</a>)<br/></em>
GC&nbsp;Daemon</div>
</td> <td align='center'>yes</td> <td align='right'>2</td> <td><img src='?resource=bullets/yellow.png' alt='TIMED_WAITING'/>TIMED_WAITING</td> <td>java.lang.Object.wait(Native Method)</td> <td align='right'>0</td> <td align='right'>0</td> <td align='center' class='noPrint'><a href='?action=send_thread_interrupt&amp;threadId=40272_***********_47' onclick="javascript:return confirm('Do you confirm the sending of a thread interrupt signal to GC Daemon?');"><img width='16' height='16' src='?resource=action_interrupt.png' alt='Send a thread interrupt signal to GC Daemon. The thread can test Thread.currentThread().isInterrupted() to stop itself.' title='Send a thread interrupt signal to GC Daemon. The thread can test Thread.currentThread().isInterrupted() to stop itself.' /></a></td> <td align='center' class='noPrint'><a href='?action=kill_thread&amp;threadId=40272_***********_47' onclick="javascript:return confirm('杀掉一个Java线程是不推荐和不安全的，你确定要杀掉线程 GC Daemon ?');"><img width='16' height='16' src='?resource=stop.png' alt='杀掉线程&nbsp;GC&nbsp;Daemon' title='杀掉线程&nbsp;GC&nbsp;Daemon' /></a></td></tr><tr onmouseover="this.className='highlight'" onmouseout="this.className=''">
<td><div class='tooltip'>
<em>
globalEventExecutor-2-4<br/>
sun.misc.Unsafe.park(Native Method)<br/>java.util.concurrent.locks.LockSupport.parkNanos(<a href='?part=source&amp;class=java.util.concurrent.locks.LockSupport#215' class='lightwindow' type='external' title='java.util.concurrent.locks.LockSupport'>LockSupport.java:215</a>)<br/>java.util.concurrent.locks.AbstractQueuedSynchronizer$ConditionObject.awaitNanos(<a href='?part=source&amp;class=java.util.concurrent.locks.AbstractQueuedSynchronizer$ConditionObject#2078' class='lightwindow' type='external' title='java.util.concurrent.locks.AbstractQueuedSynchronizer$ConditionObject'>AbstractQueuedSynchronizer.java:2078</a>)<br/>java.util.concurrent.LinkedBlockingQueue.poll(<a href='?part=source&amp;class=java.util.concurrent.LinkedBlockingQueue#467' class='lightwindow' type='external' title='java.util.concurrent.LinkedBlockingQueue'>LinkedBlockingQueue.java:467</a>)<br/>io.netty.util.concurrent.GlobalEventExecutor.takeTask(<a href='?part=source&amp;class=io.netty.util.concurrent.GlobalEventExecutor#94' class='lightwindow' type='external' title='io.netty.util.concurrent.GlobalEventExecutor'>GlobalEventExecutor.java:94</a>)<br/>io.netty.util.concurrent.GlobalEventExecutor$TaskRunner.run(<a href='?part=source&amp;class=io.netty.util.concurrent.GlobalEventExecutor$TaskRunner#247' class='lightwindow' type='external' title='io.netty.util.concurrent.GlobalEventExecutor$TaskRunner'>GlobalEventExecutor.java:247</a>)<br/>io.netty.util.internal.ThreadExecutorMap$2.run(<a href='?part=source&amp;class=io.netty.util.internal.ThreadExecutorMap$2#74' class='lightwindow' type='external' title='io.netty.util.internal.ThreadExecutorMap$2'>ThreadExecutorMap.java:74</a>)<br/>io.netty.util.concurrent.FastThreadLocalRunnable.run(<a href='?part=source&amp;class=io.netty.util.concurrent.FastThreadLocalRunnable#30' class='lightwindow' type='external' title='io.netty.util.concurrent.FastThreadLocalRunnable'>FastThreadLocalRunnable.java:30</a>)<br/>java.lang.Thread.run(<a href='?part=source&amp;class=java.lang.Thread#748' class='lightwindow' type='external' title='java.lang.Thread'>Thread.java:748</a>)<br/></em>
globalEventExecutor-2-4</div>
</td> <td align='center'>no</td> <td align='right'>5</td> <td><img src='?resource=bullets/yellow.png' alt='TIMED_WAITING'/>TIMED_WAITING</td> <td>sun.misc.Unsafe.park(Native Method)</td> <td align='right'>0</td> <td align='right'>0</td> <td align='center' class='noPrint'><a href='?action=send_thread_interrupt&amp;threadId=40272_***********_949' onclick="javascript:return confirm('Do you confirm the sending of a thread interrupt signal to globalEventExecutor-2-4?');"><img width='16' height='16' src='?resource=action_interrupt.png' alt='Send a thread interrupt signal to globalEventExecutor-2-4. The thread can test Thread.currentThread().isInterrupted() to stop itself.' title='Send a thread interrupt signal to globalEventExecutor-2-4. The thread can test Thread.currentThread().isInterrupted() to stop itself.' /></a></td> <td align='center' class='noPrint'><a href='?action=kill_thread&amp;threadId=40272_***********_949' onclick="javascript:return confirm('杀掉一个Java线程是不推荐和不安全的，你确定要杀掉线程 globalEventExecutor-2-4 ?');"><img width='16' height='16' src='?resource=stop.png' alt='杀掉线程&nbsp;globalEventExecutor-2-4' title='杀掉线程&nbsp;globalEventExecutor-2-4' /></a></td></tr><tr class='odd' onmouseover="this.className='highlight'" onmouseout="this.className='odd'">
<td><div class='tooltip'>
<em>
grpc-nio-worker-ELG-1-1<br/>
sun.nio.ch.WindowsSelectorImpl$SubSelector.poll0(Native Method)<br/>sun.nio.ch.WindowsSelectorImpl$SubSelector.poll(<a href='?part=source&amp;class=sun.nio.ch.WindowsSelectorImpl$SubSelector#296' class='lightwindow' type='external' title='sun.nio.ch.WindowsSelectorImpl$SubSelector'>WindowsSelectorImpl.java:296</a>)<br/>sun.nio.ch.WindowsSelectorImpl$SubSelector.access$400(<a href='?part=source&amp;class=sun.nio.ch.WindowsSelectorImpl$SubSelector#278' class='lightwindow' type='external' title='sun.nio.ch.WindowsSelectorImpl$SubSelector'>WindowsSelectorImpl.java:278</a>)<br/>sun.nio.ch.WindowsSelectorImpl.doSelect(<a href='?part=source&amp;class=sun.nio.ch.WindowsSelectorImpl#159' class='lightwindow' type='external' title='sun.nio.ch.WindowsSelectorImpl'>WindowsSelectorImpl.java:159</a>)<br/>sun.nio.ch.SelectorImpl.lockAndDoSelect(<a href='?part=source&amp;class=sun.nio.ch.SelectorImpl#86' class='lightwindow' type='external' title='sun.nio.ch.SelectorImpl'>SelectorImpl.java:86</a>)<br/>sun.nio.ch.SelectorImpl.select(<a href='?part=source&amp;class=sun.nio.ch.SelectorImpl#97' class='lightwindow' type='external' title='sun.nio.ch.SelectorImpl'>SelectorImpl.java:97</a>)<br/>com.alibaba.nacos.shaded.io.grpc.netty.shaded.io.netty.channel.nio.SelectedSelectionKeySetSelector.select(<a href='?part=source&amp;class=com.alibaba.nacos.shaded.io.grpc.netty.shaded.io.netty.channel.nio.SelectedSelectionKeySetSelector#62' class='lightwindow' type='external' title='com.alibaba.nacos.shaded.io.grpc.netty.shaded.io.netty.channel.nio.SelectedSelectionKeySetSelector'>SelectedSelectionKeySetSelector.java:62</a>)<br/>com.alibaba.nacos.shaded.io.grpc.netty.shaded.io.netty.channel.nio.NioEventLoop.select(<a href='?part=source&amp;class=com.alibaba.nacos.shaded.io.grpc.netty.shaded.io.netty.channel.nio.NioEventLoop#824' class='lightwindow' type='external' title='com.alibaba.nacos.shaded.io.grpc.netty.shaded.io.netty.channel.nio.NioEventLoop'>NioEventLoop.java:824</a>)<br/>com.alibaba.nacos.shaded.io.grpc.netty.shaded.io.netty.channel.nio.NioEventLoop.run(<a href='?part=source&amp;class=com.alibaba.nacos.shaded.io.grpc.netty.shaded.io.netty.channel.nio.NioEventLoop#457' class='lightwindow' type='external' title='com.alibaba.nacos.shaded.io.grpc.netty.shaded.io.netty.channel.nio.NioEventLoop'>NioEventLoop.java:457</a>)<br/>com.alibaba.nacos.shaded.io.grpc.netty.shaded.io.netty.util.concurrent.SingleThreadEventExecutor$6.run(<a href='?part=source&amp;class=com.alibaba.nacos.shaded.io.grpc.netty.shaded.io.netty.util.concurrent.SingleThreadEventExecutor$6#1044' class='lightwindow' type='external' title='com.alibaba.nacos.shaded.io.grpc.netty.shaded.io.netty.util.concurrent.SingleThreadEventExecutor$6'>SingleThreadEventExecutor.java:1044</a>)<br/>com.alibaba.nacos.shaded.io.grpc.netty.shaded.io.netty.util.internal.ThreadExecutorMap$2.run(<a href='?part=source&amp;class=com.alibaba.nacos.shaded.io.grpc.netty.shaded.io.netty.util.internal.ThreadExecutorMap$2#74' class='lightwindow' type='external' title='com.alibaba.nacos.shaded.io.grpc.netty.shaded.io.netty.util.internal.ThreadExecutorMap$2'>ThreadExecutorMap.java:74</a>)<br/>com.alibaba.nacos.shaded.io.grpc.netty.shaded.io.netty.util.concurrent.FastThreadLocalRunnable.run(<a href='?part=source&amp;class=com.alibaba.nacos.shaded.io.grpc.netty.shaded.io.netty.util.concurrent.FastThreadLocalRunnable#30' class='lightwindow' type='external' title='com.alibaba.nacos.shaded.io.grpc.netty.shaded.io.netty.util.concurrent.FastThreadLocalRunnable'>FastThreadLocalRunnable.java:30</a>)<br/>java.lang.Thread.run(<a href='?part=source&amp;class=java.lang.Thread#748' class='lightwindow' type='external' title='java.lang.Thread'>Thread.java:748</a>)<br/></em>
grpc-nio-worker-ELG-1-1</div>
</td> <td align='center'>yes</td> <td align='right'>5</td> <td><img src='?resource=bullets/green.png' alt='RUNNABLE'/>RUNNABLE</td> <td>sun.nio.ch.WindowsSelectorImpl$SubSelector.poll0(Native Method)</td> <td align='right'>0</td> <td align='right'>0</td> <td align='center' class='noPrint'><a href='?action=send_thread_interrupt&amp;threadId=40272_***********_30' onclick="javascript:return confirm('Do you confirm the sending of a thread interrupt signal to grpc-nio-worker-ELG-1-1?');"><img width='16' height='16' src='?resource=action_interrupt.png' alt='Send a thread interrupt signal to grpc-nio-worker-ELG-1-1. The thread can test Thread.currentThread().isInterrupted() to stop itself.' title='Send a thread interrupt signal to grpc-nio-worker-ELG-1-1. The thread can test Thread.currentThread().isInterrupted() to stop itself.' /></a></td> <td align='center' class='noPrint'><a href='?action=kill_thread&amp;threadId=40272_***********_30' onclick="javascript:return confirm('杀掉一个Java线程是不推荐和不安全的，你确定要杀掉线程 grpc-nio-worker-ELG-1-1 ?');"><img width='16' height='16' src='?resource=stop.png' alt='杀掉线程&nbsp;grpc-nio-worker-ELG-1-1' title='杀掉线程&nbsp;grpc-nio-worker-ELG-1-1' /></a></td></tr><tr onmouseover="this.className='highlight'" onmouseout="this.className=''">
<td><div class='tooltip'>
<em>
grpc-nio-worker-ELG-1-2<br/>
sun.nio.ch.WindowsSelectorImpl$SubSelector.poll0(Native Method)<br/>sun.nio.ch.WindowsSelectorImpl$SubSelector.poll(<a href='?part=source&amp;class=sun.nio.ch.WindowsSelectorImpl$SubSelector#296' class='lightwindow' type='external' title='sun.nio.ch.WindowsSelectorImpl$SubSelector'>WindowsSelectorImpl.java:296</a>)<br/>sun.nio.ch.WindowsSelectorImpl$SubSelector.access$400(<a href='?part=source&amp;class=sun.nio.ch.WindowsSelectorImpl$SubSelector#278' class='lightwindow' type='external' title='sun.nio.ch.WindowsSelectorImpl$SubSelector'>WindowsSelectorImpl.java:278</a>)<br/>sun.nio.ch.WindowsSelectorImpl.doSelect(<a href='?part=source&amp;class=sun.nio.ch.WindowsSelectorImpl#159' class='lightwindow' type='external' title='sun.nio.ch.WindowsSelectorImpl'>WindowsSelectorImpl.java:159</a>)<br/>sun.nio.ch.SelectorImpl.lockAndDoSelect(<a href='?part=source&amp;class=sun.nio.ch.SelectorImpl#86' class='lightwindow' type='external' title='sun.nio.ch.SelectorImpl'>SelectorImpl.java:86</a>)<br/>sun.nio.ch.SelectorImpl.select(<a href='?part=source&amp;class=sun.nio.ch.SelectorImpl#97' class='lightwindow' type='external' title='sun.nio.ch.SelectorImpl'>SelectorImpl.java:97</a>)<br/>com.alibaba.nacos.shaded.io.grpc.netty.shaded.io.netty.channel.nio.SelectedSelectionKeySetSelector.select(<a href='?part=source&amp;class=com.alibaba.nacos.shaded.io.grpc.netty.shaded.io.netty.channel.nio.SelectedSelectionKeySetSelector#62' class='lightwindow' type='external' title='com.alibaba.nacos.shaded.io.grpc.netty.shaded.io.netty.channel.nio.SelectedSelectionKeySetSelector'>SelectedSelectionKeySetSelector.java:62</a>)<br/>com.alibaba.nacos.shaded.io.grpc.netty.shaded.io.netty.channel.nio.NioEventLoop.select(<a href='?part=source&amp;class=com.alibaba.nacos.shaded.io.grpc.netty.shaded.io.netty.channel.nio.NioEventLoop#824' class='lightwindow' type='external' title='com.alibaba.nacos.shaded.io.grpc.netty.shaded.io.netty.channel.nio.NioEventLoop'>NioEventLoop.java:824</a>)<br/>com.alibaba.nacos.shaded.io.grpc.netty.shaded.io.netty.channel.nio.NioEventLoop.run(<a href='?part=source&amp;class=com.alibaba.nacos.shaded.io.grpc.netty.shaded.io.netty.channel.nio.NioEventLoop#457' class='lightwindow' type='external' title='com.alibaba.nacos.shaded.io.grpc.netty.shaded.io.netty.channel.nio.NioEventLoop'>NioEventLoop.java:457</a>)<br/>com.alibaba.nacos.shaded.io.grpc.netty.shaded.io.netty.util.concurrent.SingleThreadEventExecutor$6.run(<a href='?part=source&amp;class=com.alibaba.nacos.shaded.io.grpc.netty.shaded.io.netty.util.concurrent.SingleThreadEventExecutor$6#1044' class='lightwindow' type='external' title='com.alibaba.nacos.shaded.io.grpc.netty.shaded.io.netty.util.concurrent.SingleThreadEventExecutor$6'>SingleThreadEventExecutor.java:1044</a>)<br/>com.alibaba.nacos.shaded.io.grpc.netty.shaded.io.netty.util.internal.ThreadExecutorMap$2.run(<a href='?part=source&amp;class=com.alibaba.nacos.shaded.io.grpc.netty.shaded.io.netty.util.internal.ThreadExecutorMap$2#74' class='lightwindow' type='external' title='com.alibaba.nacos.shaded.io.grpc.netty.shaded.io.netty.util.internal.ThreadExecutorMap$2'>ThreadExecutorMap.java:74</a>)<br/>com.alibaba.nacos.shaded.io.grpc.netty.shaded.io.netty.util.concurrent.FastThreadLocalRunnable.run(<a href='?part=source&amp;class=com.alibaba.nacos.shaded.io.grpc.netty.shaded.io.netty.util.concurrent.FastThreadLocalRunnable#30' class='lightwindow' type='external' title='com.alibaba.nacos.shaded.io.grpc.netty.shaded.io.netty.util.concurrent.FastThreadLocalRunnable'>FastThreadLocalRunnable.java:30</a>)<br/>java.lang.Thread.run(<a href='?part=source&amp;class=java.lang.Thread#748' class='lightwindow' type='external' title='java.lang.Thread'>Thread.java:748</a>)<br/></em>
grpc-nio-worker-ELG-1-2</div>
</td> <td align='center'>yes</td> <td align='right'>5</td> <td><img src='?resource=bullets/green.png' alt='RUNNABLE'/>RUNNABLE</td> <td>sun.nio.ch.WindowsSelectorImpl$SubSelector.poll0(Native Method)</td> <td align='right'>234</td> <td align='right'>140</td> <td align='center' class='noPrint'><a href='?action=send_thread_interrupt&amp;threadId=40272_***********_32' onclick="javascript:return confirm('Do you confirm the sending of a thread interrupt signal to grpc-nio-worker-ELG-1-2?');"><img width='16' height='16' src='?resource=action_interrupt.png' alt='Send a thread interrupt signal to grpc-nio-worker-ELG-1-2. The thread can test Thread.currentThread().isInterrupted() to stop itself.' title='Send a thread interrupt signal to grpc-nio-worker-ELG-1-2. The thread can test Thread.currentThread().isInterrupted() to stop itself.' /></a></td> <td align='center' class='noPrint'><a href='?action=kill_thread&amp;threadId=40272_***********_32' onclick="javascript:return confirm('杀掉一个Java线程是不推荐和不安全的，你确定要杀掉线程 grpc-nio-worker-ELG-1-2 ?');"><img width='16' height='16' src='?resource=stop.png' alt='杀掉线程&nbsp;grpc-nio-worker-ELG-1-2' title='杀掉线程&nbsp;grpc-nio-worker-ELG-1-2' /></a></td></tr><tr class='odd' onmouseover="this.className='highlight'" onmouseout="this.className='odd'">
<td><div class='tooltip'>
<em>
grpc-nio-worker-ELG-1-3<br/>
sun.nio.ch.WindowsSelectorImpl$SubSelector.poll0(Native Method)<br/>sun.nio.ch.WindowsSelectorImpl$SubSelector.poll(<a href='?part=source&amp;class=sun.nio.ch.WindowsSelectorImpl$SubSelector#296' class='lightwindow' type='external' title='sun.nio.ch.WindowsSelectorImpl$SubSelector'>WindowsSelectorImpl.java:296</a>)<br/>sun.nio.ch.WindowsSelectorImpl$SubSelector.access$400(<a href='?part=source&amp;class=sun.nio.ch.WindowsSelectorImpl$SubSelector#278' class='lightwindow' type='external' title='sun.nio.ch.WindowsSelectorImpl$SubSelector'>WindowsSelectorImpl.java:278</a>)<br/>sun.nio.ch.WindowsSelectorImpl.doSelect(<a href='?part=source&amp;class=sun.nio.ch.WindowsSelectorImpl#159' class='lightwindow' type='external' title='sun.nio.ch.WindowsSelectorImpl'>WindowsSelectorImpl.java:159</a>)<br/>sun.nio.ch.SelectorImpl.lockAndDoSelect(<a href='?part=source&amp;class=sun.nio.ch.SelectorImpl#86' class='lightwindow' type='external' title='sun.nio.ch.SelectorImpl'>SelectorImpl.java:86</a>)<br/>sun.nio.ch.SelectorImpl.select(<a href='?part=source&amp;class=sun.nio.ch.SelectorImpl#97' class='lightwindow' type='external' title='sun.nio.ch.SelectorImpl'>SelectorImpl.java:97</a>)<br/>com.alibaba.nacos.shaded.io.grpc.netty.shaded.io.netty.channel.nio.SelectedSelectionKeySetSelector.select(<a href='?part=source&amp;class=com.alibaba.nacos.shaded.io.grpc.netty.shaded.io.netty.channel.nio.SelectedSelectionKeySetSelector#62' class='lightwindow' type='external' title='com.alibaba.nacos.shaded.io.grpc.netty.shaded.io.netty.channel.nio.SelectedSelectionKeySetSelector'>SelectedSelectionKeySetSelector.java:62</a>)<br/>com.alibaba.nacos.shaded.io.grpc.netty.shaded.io.netty.channel.nio.NioEventLoop.select(<a href='?part=source&amp;class=com.alibaba.nacos.shaded.io.grpc.netty.shaded.io.netty.channel.nio.NioEventLoop#824' class='lightwindow' type='external' title='com.alibaba.nacos.shaded.io.grpc.netty.shaded.io.netty.channel.nio.NioEventLoop'>NioEventLoop.java:824</a>)<br/>com.alibaba.nacos.shaded.io.grpc.netty.shaded.io.netty.channel.nio.NioEventLoop.run(<a href='?part=source&amp;class=com.alibaba.nacos.shaded.io.grpc.netty.shaded.io.netty.channel.nio.NioEventLoop#457' class='lightwindow' type='external' title='com.alibaba.nacos.shaded.io.grpc.netty.shaded.io.netty.channel.nio.NioEventLoop'>NioEventLoop.java:457</a>)<br/>com.alibaba.nacos.shaded.io.grpc.netty.shaded.io.netty.util.concurrent.SingleThreadEventExecutor$6.run(<a href='?part=source&amp;class=com.alibaba.nacos.shaded.io.grpc.netty.shaded.io.netty.util.concurrent.SingleThreadEventExecutor$6#1044' class='lightwindow' type='external' title='com.alibaba.nacos.shaded.io.grpc.netty.shaded.io.netty.util.concurrent.SingleThreadEventExecutor$6'>SingleThreadEventExecutor.java:1044</a>)<br/>com.alibaba.nacos.shaded.io.grpc.netty.shaded.io.netty.util.internal.ThreadExecutorMap$2.run(<a href='?part=source&amp;class=com.alibaba.nacos.shaded.io.grpc.netty.shaded.io.netty.util.internal.ThreadExecutorMap$2#74' class='lightwindow' type='external' title='com.alibaba.nacos.shaded.io.grpc.netty.shaded.io.netty.util.internal.ThreadExecutorMap$2'>ThreadExecutorMap.java:74</a>)<br/>com.alibaba.nacos.shaded.io.grpc.netty.shaded.io.netty.util.concurrent.FastThreadLocalRunnable.run(<a href='?part=source&amp;class=com.alibaba.nacos.shaded.io.grpc.netty.shaded.io.netty.util.concurrent.FastThreadLocalRunnable#30' class='lightwindow' type='external' title='com.alibaba.nacos.shaded.io.grpc.netty.shaded.io.netty.util.concurrent.FastThreadLocalRunnable'>FastThreadLocalRunnable.java:30</a>)<br/>java.lang.Thread.run(<a href='?part=source&amp;class=java.lang.Thread#748' class='lightwindow' type='external' title='java.lang.Thread'>Thread.java:748</a>)<br/></em>
grpc-nio-worker-ELG-1-3</div>
</td> <td align='center'>yes</td> <td align='right'>5</td> <td><img src='?resource=bullets/green.png' alt='RUNNABLE'/>RUNNABLE</td> <td>sun.nio.ch.WindowsSelectorImpl$SubSelector.poll0(Native Method)</td> <td align='right'>0</td> <td align='right'>0</td> <td align='center' class='noPrint'><a href='?action=send_thread_interrupt&amp;threadId=40272_***********_611' onclick="javascript:return confirm('Do you confirm the sending of a thread interrupt signal to grpc-nio-worker-ELG-1-3?');"><img width='16' height='16' src='?resource=action_interrupt.png' alt='Send a thread interrupt signal to grpc-nio-worker-ELG-1-3. The thread can test Thread.currentThread().isInterrupted() to stop itself.' title='Send a thread interrupt signal to grpc-nio-worker-ELG-1-3. The thread can test Thread.currentThread().isInterrupted() to stop itself.' /></a></td> <td align='center' class='noPrint'><a href='?action=kill_thread&amp;threadId=40272_***********_611' onclick="javascript:return confirm('杀掉一个Java线程是不推荐和不安全的，你确定要杀掉线程 grpc-nio-worker-ELG-1-3 ?');"><img width='16' height='16' src='?resource=stop.png' alt='杀掉线程&nbsp;grpc-nio-worker-ELG-1-3' title='杀掉线程&nbsp;grpc-nio-worker-ELG-1-3' /></a></td></tr><tr onmouseover="this.className='highlight'" onmouseout="this.className=''">
<td><div class='tooltip'>
<em>
grpc-nio-worker-ELG-1-4<br/>
sun.nio.ch.WindowsSelectorImpl$SubSelector.poll0(Native Method)<br/>sun.nio.ch.WindowsSelectorImpl$SubSelector.poll(<a href='?part=source&amp;class=sun.nio.ch.WindowsSelectorImpl$SubSelector#296' class='lightwindow' type='external' title='sun.nio.ch.WindowsSelectorImpl$SubSelector'>WindowsSelectorImpl.java:296</a>)<br/>sun.nio.ch.WindowsSelectorImpl$SubSelector.access$400(<a href='?part=source&amp;class=sun.nio.ch.WindowsSelectorImpl$SubSelector#278' class='lightwindow' type='external' title='sun.nio.ch.WindowsSelectorImpl$SubSelector'>WindowsSelectorImpl.java:278</a>)<br/>sun.nio.ch.WindowsSelectorImpl.doSelect(<a href='?part=source&amp;class=sun.nio.ch.WindowsSelectorImpl#159' class='lightwindow' type='external' title='sun.nio.ch.WindowsSelectorImpl'>WindowsSelectorImpl.java:159</a>)<br/>sun.nio.ch.SelectorImpl.lockAndDoSelect(<a href='?part=source&amp;class=sun.nio.ch.SelectorImpl#86' class='lightwindow' type='external' title='sun.nio.ch.SelectorImpl'>SelectorImpl.java:86</a>)<br/>sun.nio.ch.SelectorImpl.select(<a href='?part=source&amp;class=sun.nio.ch.SelectorImpl#97' class='lightwindow' type='external' title='sun.nio.ch.SelectorImpl'>SelectorImpl.java:97</a>)<br/>com.alibaba.nacos.shaded.io.grpc.netty.shaded.io.netty.channel.nio.SelectedSelectionKeySetSelector.select(<a href='?part=source&amp;class=com.alibaba.nacos.shaded.io.grpc.netty.shaded.io.netty.channel.nio.SelectedSelectionKeySetSelector#62' class='lightwindow' type='external' title='com.alibaba.nacos.shaded.io.grpc.netty.shaded.io.netty.channel.nio.SelectedSelectionKeySetSelector'>SelectedSelectionKeySetSelector.java:62</a>)<br/>com.alibaba.nacos.shaded.io.grpc.netty.shaded.io.netty.channel.nio.NioEventLoop.select(<a href='?part=source&amp;class=com.alibaba.nacos.shaded.io.grpc.netty.shaded.io.netty.channel.nio.NioEventLoop#824' class='lightwindow' type='external' title='com.alibaba.nacos.shaded.io.grpc.netty.shaded.io.netty.channel.nio.NioEventLoop'>NioEventLoop.java:824</a>)<br/>com.alibaba.nacos.shaded.io.grpc.netty.shaded.io.netty.channel.nio.NioEventLoop.run(<a href='?part=source&amp;class=com.alibaba.nacos.shaded.io.grpc.netty.shaded.io.netty.channel.nio.NioEventLoop#457' class='lightwindow' type='external' title='com.alibaba.nacos.shaded.io.grpc.netty.shaded.io.netty.channel.nio.NioEventLoop'>NioEventLoop.java:457</a>)<br/>com.alibaba.nacos.shaded.io.grpc.netty.shaded.io.netty.util.concurrent.SingleThreadEventExecutor$6.run(<a href='?part=source&amp;class=com.alibaba.nacos.shaded.io.grpc.netty.shaded.io.netty.util.concurrent.SingleThreadEventExecutor$6#1044' class='lightwindow' type='external' title='com.alibaba.nacos.shaded.io.grpc.netty.shaded.io.netty.util.concurrent.SingleThreadEventExecutor$6'>SingleThreadEventExecutor.java:1044</a>)<br/>com.alibaba.nacos.shaded.io.grpc.netty.shaded.io.netty.util.internal.ThreadExecutorMap$2.run(<a href='?part=source&amp;class=com.alibaba.nacos.shaded.io.grpc.netty.shaded.io.netty.util.internal.ThreadExecutorMap$2#74' class='lightwindow' type='external' title='com.alibaba.nacos.shaded.io.grpc.netty.shaded.io.netty.util.internal.ThreadExecutorMap$2'>ThreadExecutorMap.java:74</a>)<br/>com.alibaba.nacos.shaded.io.grpc.netty.shaded.io.netty.util.concurrent.FastThreadLocalRunnable.run(<a href='?part=source&amp;class=com.alibaba.nacos.shaded.io.grpc.netty.shaded.io.netty.util.concurrent.FastThreadLocalRunnable#30' class='lightwindow' type='external' title='com.alibaba.nacos.shaded.io.grpc.netty.shaded.io.netty.util.concurrent.FastThreadLocalRunnable'>FastThreadLocalRunnable.java:30</a>)<br/>java.lang.Thread.run(<a href='?part=source&amp;class=java.lang.Thread#748' class='lightwindow' type='external' title='java.lang.Thread'>Thread.java:748</a>)<br/></em>
grpc-nio-worker-ELG-1-4</div>
</td> <td align='center'>yes</td> <td align='right'>5</td> <td><img src='?resource=bullets/green.png' alt='RUNNABLE'/>RUNNABLE</td> <td>sun.nio.ch.WindowsSelectorImpl$SubSelector.poll0(Native Method)</td> <td align='right'>234</td> <td align='right'>156</td> <td align='center' class='noPrint'><a href='?action=send_thread_interrupt&amp;threadId=40272_***********_612' onclick="javascript:return confirm('Do you confirm the sending of a thread interrupt signal to grpc-nio-worker-ELG-1-4?');"><img width='16' height='16' src='?resource=action_interrupt.png' alt='Send a thread interrupt signal to grpc-nio-worker-ELG-1-4. The thread can test Thread.currentThread().isInterrupted() to stop itself.' title='Send a thread interrupt signal to grpc-nio-worker-ELG-1-4. The thread can test Thread.currentThread().isInterrupted() to stop itself.' /></a></td> <td align='center' class='noPrint'><a href='?action=kill_thread&amp;threadId=40272_***********_612' onclick="javascript:return confirm('杀掉一个Java线程是不推荐和不安全的，你确定要杀掉线程 grpc-nio-worker-ELG-1-4 ?');"><img width='16' height='16' src='?resource=stop.png' alt='杀掉线程&nbsp;grpc-nio-worker-ELG-1-4' title='杀掉线程&nbsp;grpc-nio-worker-ELG-1-4' /></a></td></tr><tr class='odd' onmouseover="this.className='highlight'" onmouseout="this.className='odd'">
<td><div class='tooltip'>
<em>
http-nio-8092-exec-1<br/>
sun.misc.Unsafe.park(Native Method)<br/>java.util.concurrent.locks.LockSupport.park(<a href='?part=source&amp;class=java.util.concurrent.locks.LockSupport#175' class='lightwindow' type='external' title='java.util.concurrent.locks.LockSupport'>LockSupport.java:175</a>)<br/>java.util.concurrent.locks.AbstractQueuedSynchronizer$ConditionObject.await(<a href='?part=source&amp;class=java.util.concurrent.locks.AbstractQueuedSynchronizer$ConditionObject#2039' class='lightwindow' type='external' title='java.util.concurrent.locks.AbstractQueuedSynchronizer$ConditionObject'>AbstractQueuedSynchronizer.java:2039</a>)<br/>java.util.concurrent.LinkedBlockingQueue.take(<a href='?part=source&amp;class=java.util.concurrent.LinkedBlockingQueue#442' class='lightwindow' type='external' title='java.util.concurrent.LinkedBlockingQueue'>LinkedBlockingQueue.java:442</a>)<br/>org.apache.tomcat.util.threads.TaskQueue.take(<a href='?part=source&amp;class=org.apache.tomcat.util.threads.TaskQueue#146' class='lightwindow' type='external' title='org.apache.tomcat.util.threads.TaskQueue'>TaskQueue.java:146</a>)<br/>org.apache.tomcat.util.threads.TaskQueue.take(<a href='?part=source&amp;class=org.apache.tomcat.util.threads.TaskQueue#33' class='lightwindow' type='external' title='org.apache.tomcat.util.threads.TaskQueue'>TaskQueue.java:33</a>)<br/>org.apache.tomcat.util.threads.ThreadPoolExecutor.getTask(<a href='?part=source&amp;class=org.apache.tomcat.util.threads.ThreadPoolExecutor#1114' class='lightwindow' type='external' title='org.apache.tomcat.util.threads.ThreadPoolExecutor'>ThreadPoolExecutor.java:1114</a>)<br/>org.apache.tomcat.util.threads.ThreadPoolExecutor.runWorker(<a href='?part=source&amp;class=org.apache.tomcat.util.threads.ThreadPoolExecutor#1176' class='lightwindow' type='external' title='org.apache.tomcat.util.threads.ThreadPoolExecutor'>ThreadPoolExecutor.java:1176</a>)<br/>org.apache.tomcat.util.threads.ThreadPoolExecutor$Worker.run(<a href='?part=source&amp;class=org.apache.tomcat.util.threads.ThreadPoolExecutor$Worker#659' class='lightwindow' type='external' title='org.apache.tomcat.util.threads.ThreadPoolExecutor$Worker'>ThreadPoolExecutor.java:659</a>)<br/>org.apache.tomcat.util.threads.TaskThread$WrappingRunnable.run(<a href='?part=source&amp;class=org.apache.tomcat.util.threads.TaskThread$WrappingRunnable#61' class='lightwindow' type='external' title='org.apache.tomcat.util.threads.TaskThread$WrappingRunnable'>TaskThread.java:61</a>)<br/>java.lang.Thread.run(<a href='?part=source&amp;class=java.lang.Thread#748' class='lightwindow' type='external' title='java.lang.Thread'>Thread.java:748</a>)<br/></em>
http-nio-8092-exec-1</div>
</td> <td align='center'>yes</td> <td align='right'>5</td> <td><img src='?resource=bullets/yellow.png' alt='WAITING'/>WAITING</td> <td>sun.misc.Unsafe.park(Native Method)</td> <td align='right'>93</td> <td align='right'>78</td> <td align='center' class='noPrint'><a href='?action=send_thread_interrupt&amp;threadId=40272_***********_641' onclick="javascript:return confirm('Do you confirm the sending of a thread interrupt signal to http-nio-8092-exec-1?');"><img width='16' height='16' src='?resource=action_interrupt.png' alt='Send a thread interrupt signal to http-nio-8092-exec-1. The thread can test Thread.currentThread().isInterrupted() to stop itself.' title='Send a thread interrupt signal to http-nio-8092-exec-1. The thread can test Thread.currentThread().isInterrupted() to stop itself.' /></a></td> <td align='center' class='noPrint'><a href='?action=kill_thread&amp;threadId=40272_***********_641' onclick="javascript:return confirm('杀掉一个Java线程是不推荐和不安全的，你确定要杀掉线程 http-nio-8092-exec-1 ?');"><img width='16' height='16' src='?resource=stop.png' alt='杀掉线程&nbsp;http-nio-8092-exec-1' title='杀掉线程&nbsp;http-nio-8092-exec-1' /></a></td></tr><tr onmouseover="this.className='highlight'" onmouseout="this.className=''">
<td><div class='tooltip'>
<em>
http-nio-8092-exec-10<br/>
sun.misc.Unsafe.park(Native Method)<br/>java.util.concurrent.locks.LockSupport.park(<a href='?part=source&amp;class=java.util.concurrent.locks.LockSupport#175' class='lightwindow' type='external' title='java.util.concurrent.locks.LockSupport'>LockSupport.java:175</a>)<br/>java.util.concurrent.locks.AbstractQueuedSynchronizer$ConditionObject.await(<a href='?part=source&amp;class=java.util.concurrent.locks.AbstractQueuedSynchronizer$ConditionObject#2039' class='lightwindow' type='external' title='java.util.concurrent.locks.AbstractQueuedSynchronizer$ConditionObject'>AbstractQueuedSynchronizer.java:2039</a>)<br/>java.util.concurrent.LinkedBlockingQueue.take(<a href='?part=source&amp;class=java.util.concurrent.LinkedBlockingQueue#442' class='lightwindow' type='external' title='java.util.concurrent.LinkedBlockingQueue'>LinkedBlockingQueue.java:442</a>)<br/>org.apache.tomcat.util.threads.TaskQueue.take(<a href='?part=source&amp;class=org.apache.tomcat.util.threads.TaskQueue#146' class='lightwindow' type='external' title='org.apache.tomcat.util.threads.TaskQueue'>TaskQueue.java:146</a>)<br/>org.apache.tomcat.util.threads.TaskQueue.take(<a href='?part=source&amp;class=org.apache.tomcat.util.threads.TaskQueue#33' class='lightwindow' type='external' title='org.apache.tomcat.util.threads.TaskQueue'>TaskQueue.java:33</a>)<br/>org.apache.tomcat.util.threads.ThreadPoolExecutor.getTask(<a href='?part=source&amp;class=org.apache.tomcat.util.threads.ThreadPoolExecutor#1114' class='lightwindow' type='external' title='org.apache.tomcat.util.threads.ThreadPoolExecutor'>ThreadPoolExecutor.java:1114</a>)<br/>org.apache.tomcat.util.threads.ThreadPoolExecutor.runWorker(<a href='?part=source&amp;class=org.apache.tomcat.util.threads.ThreadPoolExecutor#1176' class='lightwindow' type='external' title='org.apache.tomcat.util.threads.ThreadPoolExecutor'>ThreadPoolExecutor.java:1176</a>)<br/>org.apache.tomcat.util.threads.ThreadPoolExecutor$Worker.run(<a href='?part=source&amp;class=org.apache.tomcat.util.threads.ThreadPoolExecutor$Worker#659' class='lightwindow' type='external' title='org.apache.tomcat.util.threads.ThreadPoolExecutor$Worker'>ThreadPoolExecutor.java:659</a>)<br/>org.apache.tomcat.util.threads.TaskThread$WrappingRunnable.run(<a href='?part=source&amp;class=org.apache.tomcat.util.threads.TaskThread$WrappingRunnable#61' class='lightwindow' type='external' title='org.apache.tomcat.util.threads.TaskThread$WrappingRunnable'>TaskThread.java:61</a>)<br/>java.lang.Thread.run(<a href='?part=source&amp;class=java.lang.Thread#748' class='lightwindow' type='external' title='java.lang.Thread'>Thread.java:748</a>)<br/></em>
http-nio-8092-exec-10</div>
</td> <td align='center'>yes</td> <td align='right'>5</td> <td><img src='?resource=bullets/yellow.png' alt='WAITING'/>WAITING</td> <td>sun.misc.Unsafe.park(Native Method)</td> <td align='right'>31</td> <td align='right'>0</td> <td align='center' class='noPrint'><a href='?action=send_thread_interrupt&amp;threadId=40272_***********_650' onclick="javascript:return confirm('Do you confirm the sending of a thread interrupt signal to http-nio-8092-exec-10?');"><img width='16' height='16' src='?resource=action_interrupt.png' alt='Send a thread interrupt signal to http-nio-8092-exec-10. The thread can test Thread.currentThread().isInterrupted() to stop itself.' title='Send a thread interrupt signal to http-nio-8092-exec-10. The thread can test Thread.currentThread().isInterrupted() to stop itself.' /></a></td> <td align='center' class='noPrint'><a href='?action=kill_thread&amp;threadId=40272_***********_650' onclick="javascript:return confirm('杀掉一个Java线程是不推荐和不安全的，你确定要杀掉线程 http-nio-8092-exec-10 ?');"><img width='16' height='16' src='?resource=stop.png' alt='杀掉线程&nbsp;http-nio-8092-exec-10' title='杀掉线程&nbsp;http-nio-8092-exec-10' /></a></td></tr><tr class='odd' onmouseover="this.className='highlight'" onmouseout="this.className='odd'">
<td><div class='tooltip'>
<em>
http-nio-8092-exec-11<br/>
sun.misc.Unsafe.park(Native Method)<br/>java.util.concurrent.locks.LockSupport.park(<a href='?part=source&amp;class=java.util.concurrent.locks.LockSupport#175' class='lightwindow' type='external' title='java.util.concurrent.locks.LockSupport'>LockSupport.java:175</a>)<br/>java.util.concurrent.locks.AbstractQueuedSynchronizer$ConditionObject.await(<a href='?part=source&amp;class=java.util.concurrent.locks.AbstractQueuedSynchronizer$ConditionObject#2039' class='lightwindow' type='external' title='java.util.concurrent.locks.AbstractQueuedSynchronizer$ConditionObject'>AbstractQueuedSynchronizer.java:2039</a>)<br/>java.util.concurrent.LinkedBlockingQueue.take(<a href='?part=source&amp;class=java.util.concurrent.LinkedBlockingQueue#442' class='lightwindow' type='external' title='java.util.concurrent.LinkedBlockingQueue'>LinkedBlockingQueue.java:442</a>)<br/>org.apache.tomcat.util.threads.TaskQueue.take(<a href='?part=source&amp;class=org.apache.tomcat.util.threads.TaskQueue#146' class='lightwindow' type='external' title='org.apache.tomcat.util.threads.TaskQueue'>TaskQueue.java:146</a>)<br/>org.apache.tomcat.util.threads.TaskQueue.take(<a href='?part=source&amp;class=org.apache.tomcat.util.threads.TaskQueue#33' class='lightwindow' type='external' title='org.apache.tomcat.util.threads.TaskQueue'>TaskQueue.java:33</a>)<br/>org.apache.tomcat.util.threads.ThreadPoolExecutor.getTask(<a href='?part=source&amp;class=org.apache.tomcat.util.threads.ThreadPoolExecutor#1114' class='lightwindow' type='external' title='org.apache.tomcat.util.threads.ThreadPoolExecutor'>ThreadPoolExecutor.java:1114</a>)<br/>org.apache.tomcat.util.threads.ThreadPoolExecutor.runWorker(<a href='?part=source&amp;class=org.apache.tomcat.util.threads.ThreadPoolExecutor#1176' class='lightwindow' type='external' title='org.apache.tomcat.util.threads.ThreadPoolExecutor'>ThreadPoolExecutor.java:1176</a>)<br/>org.apache.tomcat.util.threads.ThreadPoolExecutor$Worker.run(<a href='?part=source&amp;class=org.apache.tomcat.util.threads.ThreadPoolExecutor$Worker#659' class='lightwindow' type='external' title='org.apache.tomcat.util.threads.ThreadPoolExecutor$Worker'>ThreadPoolExecutor.java:659</a>)<br/>org.apache.tomcat.util.threads.TaskThread$WrappingRunnable.run(<a href='?part=source&amp;class=org.apache.tomcat.util.threads.TaskThread$WrappingRunnable#61' class='lightwindow' type='external' title='org.apache.tomcat.util.threads.TaskThread$WrappingRunnable'>TaskThread.java:61</a>)<br/>java.lang.Thread.run(<a href='?part=source&amp;class=java.lang.Thread#748' class='lightwindow' type='external' title='java.lang.Thread'>Thread.java:748</a>)<br/></em>
http-nio-8092-exec-11</div>
</td> <td align='center'>yes</td> <td align='right'>5</td> <td><img src='?resource=bullets/yellow.png' alt='WAITING'/>WAITING</td> <td>sun.misc.Unsafe.park(Native Method)</td> <td align='right'>78</td> <td align='right'>46</td> <td align='center' class='noPrint'><a href='?action=send_thread_interrupt&amp;threadId=40272_***********_651' onclick="javascript:return confirm('Do you confirm the sending of a thread interrupt signal to http-nio-8092-exec-11?');"><img width='16' height='16' src='?resource=action_interrupt.png' alt='Send a thread interrupt signal to http-nio-8092-exec-11. The thread can test Thread.currentThread().isInterrupted() to stop itself.' title='Send a thread interrupt signal to http-nio-8092-exec-11. The thread can test Thread.currentThread().isInterrupted() to stop itself.' /></a></td> <td align='center' class='noPrint'><a href='?action=kill_thread&amp;threadId=40272_***********_651' onclick="javascript:return confirm('杀掉一个Java线程是不推荐和不安全的，你确定要杀掉线程 http-nio-8092-exec-11 ?');"><img width='16' height='16' src='?resource=stop.png' alt='杀掉线程&nbsp;http-nio-8092-exec-11' title='杀掉线程&nbsp;http-nio-8092-exec-11' /></a></td></tr><tr onmouseover="this.className='highlight'" onmouseout="this.className=''">
<td><div class='tooltip'>
<em>
http-nio-8092-exec-12<br/>
sun.misc.Unsafe.park(Native Method)<br/>java.util.concurrent.locks.LockSupport.park(<a href='?part=source&amp;class=java.util.concurrent.locks.LockSupport#175' class='lightwindow' type='external' title='java.util.concurrent.locks.LockSupport'>LockSupport.java:175</a>)<br/>java.util.concurrent.locks.AbstractQueuedSynchronizer$ConditionObject.await(<a href='?part=source&amp;class=java.util.concurrent.locks.AbstractQueuedSynchronizer$ConditionObject#2039' class='lightwindow' type='external' title='java.util.concurrent.locks.AbstractQueuedSynchronizer$ConditionObject'>AbstractQueuedSynchronizer.java:2039</a>)<br/>java.util.concurrent.LinkedBlockingQueue.take(<a href='?part=source&amp;class=java.util.concurrent.LinkedBlockingQueue#442' class='lightwindow' type='external' title='java.util.concurrent.LinkedBlockingQueue'>LinkedBlockingQueue.java:442</a>)<br/>org.apache.tomcat.util.threads.TaskQueue.take(<a href='?part=source&amp;class=org.apache.tomcat.util.threads.TaskQueue#146' class='lightwindow' type='external' title='org.apache.tomcat.util.threads.TaskQueue'>TaskQueue.java:146</a>)<br/>org.apache.tomcat.util.threads.TaskQueue.take(<a href='?part=source&amp;class=org.apache.tomcat.util.threads.TaskQueue#33' class='lightwindow' type='external' title='org.apache.tomcat.util.threads.TaskQueue'>TaskQueue.java:33</a>)<br/>org.apache.tomcat.util.threads.ThreadPoolExecutor.getTask(<a href='?part=source&amp;class=org.apache.tomcat.util.threads.ThreadPoolExecutor#1114' class='lightwindow' type='external' title='org.apache.tomcat.util.threads.ThreadPoolExecutor'>ThreadPoolExecutor.java:1114</a>)<br/>org.apache.tomcat.util.threads.ThreadPoolExecutor.runWorker(<a href='?part=source&amp;class=org.apache.tomcat.util.threads.ThreadPoolExecutor#1176' class='lightwindow' type='external' title='org.apache.tomcat.util.threads.ThreadPoolExecutor'>ThreadPoolExecutor.java:1176</a>)<br/>org.apache.tomcat.util.threads.ThreadPoolExecutor$Worker.run(<a href='?part=source&amp;class=org.apache.tomcat.util.threads.ThreadPoolExecutor$Worker#659' class='lightwindow' type='external' title='org.apache.tomcat.util.threads.ThreadPoolExecutor$Worker'>ThreadPoolExecutor.java:659</a>)<br/>org.apache.tomcat.util.threads.TaskThread$WrappingRunnable.run(<a href='?part=source&amp;class=org.apache.tomcat.util.threads.TaskThread$WrappingRunnable#61' class='lightwindow' type='external' title='org.apache.tomcat.util.threads.TaskThread$WrappingRunnable'>TaskThread.java:61</a>)<br/>java.lang.Thread.run(<a href='?part=source&amp;class=java.lang.Thread#748' class='lightwindow' type='external' title='java.lang.Thread'>Thread.java:748</a>)<br/></em>
http-nio-8092-exec-12</div>
</td> <td align='center'>yes</td> <td align='right'>5</td> <td><img src='?resource=bullets/yellow.png' alt='WAITING'/>WAITING</td> <td>sun.misc.Unsafe.park(Native Method)</td> <td align='right'>31</td> <td align='right'>15</td> <td align='center' class='noPrint'><a href='?action=send_thread_interrupt&amp;threadId=40272_***********_652' onclick="javascript:return confirm('Do you confirm the sending of a thread interrupt signal to http-nio-8092-exec-12?');"><img width='16' height='16' src='?resource=action_interrupt.png' alt='Send a thread interrupt signal to http-nio-8092-exec-12. The thread can test Thread.currentThread().isInterrupted() to stop itself.' title='Send a thread interrupt signal to http-nio-8092-exec-12. The thread can test Thread.currentThread().isInterrupted() to stop itself.' /></a></td> <td align='center' class='noPrint'><a href='?action=kill_thread&amp;threadId=40272_***********_652' onclick="javascript:return confirm('杀掉一个Java线程是不推荐和不安全的，你确定要杀掉线程 http-nio-8092-exec-12 ?');"><img width='16' height='16' src='?resource=stop.png' alt='杀掉线程&nbsp;http-nio-8092-exec-12' title='杀掉线程&nbsp;http-nio-8092-exec-12' /></a></td></tr><tr class='odd' onmouseover="this.className='highlight'" onmouseout="this.className='odd'">
<td><div class='tooltip'>
<em>
http-nio-8092-exec-13<br/>
sun.misc.Unsafe.park(Native Method)<br/>java.util.concurrent.locks.LockSupport.park(<a href='?part=source&amp;class=java.util.concurrent.locks.LockSupport#175' class='lightwindow' type='external' title='java.util.concurrent.locks.LockSupport'>LockSupport.java:175</a>)<br/>java.util.concurrent.locks.AbstractQueuedSynchronizer$ConditionObject.await(<a href='?part=source&amp;class=java.util.concurrent.locks.AbstractQueuedSynchronizer$ConditionObject#2039' class='lightwindow' type='external' title='java.util.concurrent.locks.AbstractQueuedSynchronizer$ConditionObject'>AbstractQueuedSynchronizer.java:2039</a>)<br/>java.util.concurrent.LinkedBlockingQueue.take(<a href='?part=source&amp;class=java.util.concurrent.LinkedBlockingQueue#442' class='lightwindow' type='external' title='java.util.concurrent.LinkedBlockingQueue'>LinkedBlockingQueue.java:442</a>)<br/>org.apache.tomcat.util.threads.TaskQueue.take(<a href='?part=source&amp;class=org.apache.tomcat.util.threads.TaskQueue#146' class='lightwindow' type='external' title='org.apache.tomcat.util.threads.TaskQueue'>TaskQueue.java:146</a>)<br/>org.apache.tomcat.util.threads.TaskQueue.take(<a href='?part=source&amp;class=org.apache.tomcat.util.threads.TaskQueue#33' class='lightwindow' type='external' title='org.apache.tomcat.util.threads.TaskQueue'>TaskQueue.java:33</a>)<br/>org.apache.tomcat.util.threads.ThreadPoolExecutor.getTask(<a href='?part=source&amp;class=org.apache.tomcat.util.threads.ThreadPoolExecutor#1114' class='lightwindow' type='external' title='org.apache.tomcat.util.threads.ThreadPoolExecutor'>ThreadPoolExecutor.java:1114</a>)<br/>org.apache.tomcat.util.threads.ThreadPoolExecutor.runWorker(<a href='?part=source&amp;class=org.apache.tomcat.util.threads.ThreadPoolExecutor#1176' class='lightwindow' type='external' title='org.apache.tomcat.util.threads.ThreadPoolExecutor'>ThreadPoolExecutor.java:1176</a>)<br/>org.apache.tomcat.util.threads.ThreadPoolExecutor$Worker.run(<a href='?part=source&amp;class=org.apache.tomcat.util.threads.ThreadPoolExecutor$Worker#659' class='lightwindow' type='external' title='org.apache.tomcat.util.threads.ThreadPoolExecutor$Worker'>ThreadPoolExecutor.java:659</a>)<br/>org.apache.tomcat.util.threads.TaskThread$WrappingRunnable.run(<a href='?part=source&amp;class=org.apache.tomcat.util.threads.TaskThread$WrappingRunnable#61' class='lightwindow' type='external' title='org.apache.tomcat.util.threads.TaskThread$WrappingRunnable'>TaskThread.java:61</a>)<br/>java.lang.Thread.run(<a href='?part=source&amp;class=java.lang.Thread#748' class='lightwindow' type='external' title='java.lang.Thread'>Thread.java:748</a>)<br/></em>
http-nio-8092-exec-13</div>
</td> <td align='center'>yes</td> <td align='right'>5</td> <td><img src='?resource=bullets/yellow.png' alt='WAITING'/>WAITING</td> <td>sun.misc.Unsafe.park(Native Method)</td> <td align='right'>125</td> <td align='right'>78</td> <td align='center' class='noPrint'><a href='?action=send_thread_interrupt&amp;threadId=40272_***********_653' onclick="javascript:return confirm('Do you confirm the sending of a thread interrupt signal to http-nio-8092-exec-13?');"><img width='16' height='16' src='?resource=action_interrupt.png' alt='Send a thread interrupt signal to http-nio-8092-exec-13. The thread can test Thread.currentThread().isInterrupted() to stop itself.' title='Send a thread interrupt signal to http-nio-8092-exec-13. The thread can test Thread.currentThread().isInterrupted() to stop itself.' /></a></td> <td align='center' class='noPrint'><a href='?action=kill_thread&amp;threadId=40272_***********_653' onclick="javascript:return confirm('杀掉一个Java线程是不推荐和不安全的，你确定要杀掉线程 http-nio-8092-exec-13 ?');"><img width='16' height='16' src='?resource=stop.png' alt='杀掉线程&nbsp;http-nio-8092-exec-13' title='杀掉线程&nbsp;http-nio-8092-exec-13' /></a></td></tr><tr onmouseover="this.className='highlight'" onmouseout="this.className=''">
<td><div class='tooltip'>
<em>
http-nio-8092-exec-14<br/>
sun.misc.Unsafe.park(Native Method)<br/>java.util.concurrent.locks.LockSupport.park(<a href='?part=source&amp;class=java.util.concurrent.locks.LockSupport#175' class='lightwindow' type='external' title='java.util.concurrent.locks.LockSupport'>LockSupport.java:175</a>)<br/>java.util.concurrent.locks.AbstractQueuedSynchronizer$ConditionObject.await(<a href='?part=source&amp;class=java.util.concurrent.locks.AbstractQueuedSynchronizer$ConditionObject#2039' class='lightwindow' type='external' title='java.util.concurrent.locks.AbstractQueuedSynchronizer$ConditionObject'>AbstractQueuedSynchronizer.java:2039</a>)<br/>java.util.concurrent.LinkedBlockingQueue.take(<a href='?part=source&amp;class=java.util.concurrent.LinkedBlockingQueue#442' class='lightwindow' type='external' title='java.util.concurrent.LinkedBlockingQueue'>LinkedBlockingQueue.java:442</a>)<br/>org.apache.tomcat.util.threads.TaskQueue.take(<a href='?part=source&amp;class=org.apache.tomcat.util.threads.TaskQueue#146' class='lightwindow' type='external' title='org.apache.tomcat.util.threads.TaskQueue'>TaskQueue.java:146</a>)<br/>org.apache.tomcat.util.threads.TaskQueue.take(<a href='?part=source&amp;class=org.apache.tomcat.util.threads.TaskQueue#33' class='lightwindow' type='external' title='org.apache.tomcat.util.threads.TaskQueue'>TaskQueue.java:33</a>)<br/>org.apache.tomcat.util.threads.ThreadPoolExecutor.getTask(<a href='?part=source&amp;class=org.apache.tomcat.util.threads.ThreadPoolExecutor#1114' class='lightwindow' type='external' title='org.apache.tomcat.util.threads.ThreadPoolExecutor'>ThreadPoolExecutor.java:1114</a>)<br/>org.apache.tomcat.util.threads.ThreadPoolExecutor.runWorker(<a href='?part=source&amp;class=org.apache.tomcat.util.threads.ThreadPoolExecutor#1176' class='lightwindow' type='external' title='org.apache.tomcat.util.threads.ThreadPoolExecutor'>ThreadPoolExecutor.java:1176</a>)<br/>org.apache.tomcat.util.threads.ThreadPoolExecutor$Worker.run(<a href='?part=source&amp;class=org.apache.tomcat.util.threads.ThreadPoolExecutor$Worker#659' class='lightwindow' type='external' title='org.apache.tomcat.util.threads.ThreadPoolExecutor$Worker'>ThreadPoolExecutor.java:659</a>)<br/>org.apache.tomcat.util.threads.TaskThread$WrappingRunnable.run(<a href='?part=source&amp;class=org.apache.tomcat.util.threads.TaskThread$WrappingRunnable#61' class='lightwindow' type='external' title='org.apache.tomcat.util.threads.TaskThread$WrappingRunnable'>TaskThread.java:61</a>)<br/>java.lang.Thread.run(<a href='?part=source&amp;class=java.lang.Thread#748' class='lightwindow' type='external' title='java.lang.Thread'>Thread.java:748</a>)<br/></em>
http-nio-8092-exec-14</div>
</td> <td align='center'>yes</td> <td align='right'>5</td> <td><img src='?resource=bullets/yellow.png' alt='WAITING'/>WAITING</td> <td>sun.misc.Unsafe.park(Native Method)</td> <td align='right'>109</td> <td align='right'>109</td> <td align='center' class='noPrint'><a href='?action=send_thread_interrupt&amp;threadId=40272_***********_654' onclick="javascript:return confirm('Do you confirm the sending of a thread interrupt signal to http-nio-8092-exec-14?');"><img width='16' height='16' src='?resource=action_interrupt.png' alt='Send a thread interrupt signal to http-nio-8092-exec-14. The thread can test Thread.currentThread().isInterrupted() to stop itself.' title='Send a thread interrupt signal to http-nio-8092-exec-14. The thread can test Thread.currentThread().isInterrupted() to stop itself.' /></a></td> <td align='center' class='noPrint'><a href='?action=kill_thread&amp;threadId=40272_***********_654' onclick="javascript:return confirm('杀掉一个Java线程是不推荐和不安全的，你确定要杀掉线程 http-nio-8092-exec-14 ?');"><img width='16' height='16' src='?resource=stop.png' alt='杀掉线程&nbsp;http-nio-8092-exec-14' title='杀掉线程&nbsp;http-nio-8092-exec-14' /></a></td></tr><tr class='odd' onmouseover="this.className='highlight'" onmouseout="this.className='odd'">
<td><div class='tooltip'>
<em>
http-nio-8092-exec-15<br/>
sun.misc.Unsafe.park(Native Method)<br/>java.util.concurrent.locks.LockSupport.park(<a href='?part=source&amp;class=java.util.concurrent.locks.LockSupport#175' class='lightwindow' type='external' title='java.util.concurrent.locks.LockSupport'>LockSupport.java:175</a>)<br/>java.util.concurrent.locks.AbstractQueuedSynchronizer$ConditionObject.await(<a href='?part=source&amp;class=java.util.concurrent.locks.AbstractQueuedSynchronizer$ConditionObject#2039' class='lightwindow' type='external' title='java.util.concurrent.locks.AbstractQueuedSynchronizer$ConditionObject'>AbstractQueuedSynchronizer.java:2039</a>)<br/>java.util.concurrent.LinkedBlockingQueue.take(<a href='?part=source&amp;class=java.util.concurrent.LinkedBlockingQueue#442' class='lightwindow' type='external' title='java.util.concurrent.LinkedBlockingQueue'>LinkedBlockingQueue.java:442</a>)<br/>org.apache.tomcat.util.threads.TaskQueue.take(<a href='?part=source&amp;class=org.apache.tomcat.util.threads.TaskQueue#146' class='lightwindow' type='external' title='org.apache.tomcat.util.threads.TaskQueue'>TaskQueue.java:146</a>)<br/>org.apache.tomcat.util.threads.TaskQueue.take(<a href='?part=source&amp;class=org.apache.tomcat.util.threads.TaskQueue#33' class='lightwindow' type='external' title='org.apache.tomcat.util.threads.TaskQueue'>TaskQueue.java:33</a>)<br/>org.apache.tomcat.util.threads.ThreadPoolExecutor.getTask(<a href='?part=source&amp;class=org.apache.tomcat.util.threads.ThreadPoolExecutor#1114' class='lightwindow' type='external' title='org.apache.tomcat.util.threads.ThreadPoolExecutor'>ThreadPoolExecutor.java:1114</a>)<br/>org.apache.tomcat.util.threads.ThreadPoolExecutor.runWorker(<a href='?part=source&amp;class=org.apache.tomcat.util.threads.ThreadPoolExecutor#1176' class='lightwindow' type='external' title='org.apache.tomcat.util.threads.ThreadPoolExecutor'>ThreadPoolExecutor.java:1176</a>)<br/>org.apache.tomcat.util.threads.ThreadPoolExecutor$Worker.run(<a href='?part=source&amp;class=org.apache.tomcat.util.threads.ThreadPoolExecutor$Worker#659' class='lightwindow' type='external' title='org.apache.tomcat.util.threads.ThreadPoolExecutor$Worker'>ThreadPoolExecutor.java:659</a>)<br/>org.apache.tomcat.util.threads.TaskThread$WrappingRunnable.run(<a href='?part=source&amp;class=org.apache.tomcat.util.threads.TaskThread$WrappingRunnable#61' class='lightwindow' type='external' title='org.apache.tomcat.util.threads.TaskThread$WrappingRunnable'>TaskThread.java:61</a>)<br/>java.lang.Thread.run(<a href='?part=source&amp;class=java.lang.Thread#748' class='lightwindow' type='external' title='java.lang.Thread'>Thread.java:748</a>)<br/></em>
http-nio-8092-exec-15</div>
</td> <td align='center'>yes</td> <td align='right'>5</td> <td><img src='?resource=bullets/yellow.png' alt='WAITING'/>WAITING</td> <td>sun.misc.Unsafe.park(Native Method)</td> <td align='right'>31</td> <td align='right'>15</td> <td align='center' class='noPrint'><a href='?action=send_thread_interrupt&amp;threadId=40272_***********_655' onclick="javascript:return confirm('Do you confirm the sending of a thread interrupt signal to http-nio-8092-exec-15?');"><img width='16' height='16' src='?resource=action_interrupt.png' alt='Send a thread interrupt signal to http-nio-8092-exec-15. The thread can test Thread.currentThread().isInterrupted() to stop itself.' title='Send a thread interrupt signal to http-nio-8092-exec-15. The thread can test Thread.currentThread().isInterrupted() to stop itself.' /></a></td> <td align='center' class='noPrint'><a href='?action=kill_thread&amp;threadId=40272_***********_655' onclick="javascript:return confirm('杀掉一个Java线程是不推荐和不安全的，你确定要杀掉线程 http-nio-8092-exec-15 ?');"><img width='16' height='16' src='?resource=stop.png' alt='杀掉线程&nbsp;http-nio-8092-exec-15' title='杀掉线程&nbsp;http-nio-8092-exec-15' /></a></td></tr><tr onmouseover="this.className='highlight'" onmouseout="this.className=''">
<td><div class='tooltip'>
<em>
http-nio-8092-exec-16<br/>
sun.misc.Unsafe.park(Native Method)<br/>java.util.concurrent.locks.LockSupport.park(<a href='?part=source&amp;class=java.util.concurrent.locks.LockSupport#175' class='lightwindow' type='external' title='java.util.concurrent.locks.LockSupport'>LockSupport.java:175</a>)<br/>java.util.concurrent.locks.AbstractQueuedSynchronizer$ConditionObject.await(<a href='?part=source&amp;class=java.util.concurrent.locks.AbstractQueuedSynchronizer$ConditionObject#2039' class='lightwindow' type='external' title='java.util.concurrent.locks.AbstractQueuedSynchronizer$ConditionObject'>AbstractQueuedSynchronizer.java:2039</a>)<br/>java.util.concurrent.LinkedBlockingQueue.take(<a href='?part=source&amp;class=java.util.concurrent.LinkedBlockingQueue#442' class='lightwindow' type='external' title='java.util.concurrent.LinkedBlockingQueue'>LinkedBlockingQueue.java:442</a>)<br/>org.apache.tomcat.util.threads.TaskQueue.take(<a href='?part=source&amp;class=org.apache.tomcat.util.threads.TaskQueue#146' class='lightwindow' type='external' title='org.apache.tomcat.util.threads.TaskQueue'>TaskQueue.java:146</a>)<br/>org.apache.tomcat.util.threads.TaskQueue.take(<a href='?part=source&amp;class=org.apache.tomcat.util.threads.TaskQueue#33' class='lightwindow' type='external' title='org.apache.tomcat.util.threads.TaskQueue'>TaskQueue.java:33</a>)<br/>org.apache.tomcat.util.threads.ThreadPoolExecutor.getTask(<a href='?part=source&amp;class=org.apache.tomcat.util.threads.ThreadPoolExecutor#1114' class='lightwindow' type='external' title='org.apache.tomcat.util.threads.ThreadPoolExecutor'>ThreadPoolExecutor.java:1114</a>)<br/>org.apache.tomcat.util.threads.ThreadPoolExecutor.runWorker(<a href='?part=source&amp;class=org.apache.tomcat.util.threads.ThreadPoolExecutor#1176' class='lightwindow' type='external' title='org.apache.tomcat.util.threads.ThreadPoolExecutor'>ThreadPoolExecutor.java:1176</a>)<br/>org.apache.tomcat.util.threads.ThreadPoolExecutor$Worker.run(<a href='?part=source&amp;class=org.apache.tomcat.util.threads.ThreadPoolExecutor$Worker#659' class='lightwindow' type='external' title='org.apache.tomcat.util.threads.ThreadPoolExecutor$Worker'>ThreadPoolExecutor.java:659</a>)<br/>org.apache.tomcat.util.threads.TaskThread$WrappingRunnable.run(<a href='?part=source&amp;class=org.apache.tomcat.util.threads.TaskThread$WrappingRunnable#61' class='lightwindow' type='external' title='org.apache.tomcat.util.threads.TaskThread$WrappingRunnable'>TaskThread.java:61</a>)<br/>java.lang.Thread.run(<a href='?part=source&amp;class=java.lang.Thread#748' class='lightwindow' type='external' title='java.lang.Thread'>Thread.java:748</a>)<br/></em>
http-nio-8092-exec-16</div>
</td> <td align='center'>yes</td> <td align='right'>5</td> <td><img src='?resource=bullets/yellow.png' alt='WAITING'/>WAITING</td> <td>sun.misc.Unsafe.park(Native Method)</td> <td align='right'>62</td> <td align='right'>31</td> <td align='center' class='noPrint'><a href='?action=send_thread_interrupt&amp;threadId=40272_***********_656' onclick="javascript:return confirm('Do you confirm the sending of a thread interrupt signal to http-nio-8092-exec-16?');"><img width='16' height='16' src='?resource=action_interrupt.png' alt='Send a thread interrupt signal to http-nio-8092-exec-16. The thread can test Thread.currentThread().isInterrupted() to stop itself.' title='Send a thread interrupt signal to http-nio-8092-exec-16. The thread can test Thread.currentThread().isInterrupted() to stop itself.' /></a></td> <td align='center' class='noPrint'><a href='?action=kill_thread&amp;threadId=40272_***********_656' onclick="javascript:return confirm('杀掉一个Java线程是不推荐和不安全的，你确定要杀掉线程 http-nio-8092-exec-16 ?');"><img width='16' height='16' src='?resource=stop.png' alt='杀掉线程&nbsp;http-nio-8092-exec-16' title='杀掉线程&nbsp;http-nio-8092-exec-16' /></a></td></tr><tr class='odd' onmouseover="this.className='highlight'" onmouseout="this.className='odd'">
<td><div class='tooltip'>
<em>
http-nio-8092-exec-17<br/>
sun.misc.Unsafe.park(Native Method)<br/>java.util.concurrent.locks.LockSupport.park(<a href='?part=source&amp;class=java.util.concurrent.locks.LockSupport#175' class='lightwindow' type='external' title='java.util.concurrent.locks.LockSupport'>LockSupport.java:175</a>)<br/>java.util.concurrent.locks.AbstractQueuedSynchronizer$ConditionObject.await(<a href='?part=source&amp;class=java.util.concurrent.locks.AbstractQueuedSynchronizer$ConditionObject#2039' class='lightwindow' type='external' title='java.util.concurrent.locks.AbstractQueuedSynchronizer$ConditionObject'>AbstractQueuedSynchronizer.java:2039</a>)<br/>java.util.concurrent.LinkedBlockingQueue.take(<a href='?part=source&amp;class=java.util.concurrent.LinkedBlockingQueue#442' class='lightwindow' type='external' title='java.util.concurrent.LinkedBlockingQueue'>LinkedBlockingQueue.java:442</a>)<br/>org.apache.tomcat.util.threads.TaskQueue.take(<a href='?part=source&amp;class=org.apache.tomcat.util.threads.TaskQueue#146' class='lightwindow' type='external' title='org.apache.tomcat.util.threads.TaskQueue'>TaskQueue.java:146</a>)<br/>org.apache.tomcat.util.threads.TaskQueue.take(<a href='?part=source&amp;class=org.apache.tomcat.util.threads.TaskQueue#33' class='lightwindow' type='external' title='org.apache.tomcat.util.threads.TaskQueue'>TaskQueue.java:33</a>)<br/>org.apache.tomcat.util.threads.ThreadPoolExecutor.getTask(<a href='?part=source&amp;class=org.apache.tomcat.util.threads.ThreadPoolExecutor#1114' class='lightwindow' type='external' title='org.apache.tomcat.util.threads.ThreadPoolExecutor'>ThreadPoolExecutor.java:1114</a>)<br/>org.apache.tomcat.util.threads.ThreadPoolExecutor.runWorker(<a href='?part=source&amp;class=org.apache.tomcat.util.threads.ThreadPoolExecutor#1176' class='lightwindow' type='external' title='org.apache.tomcat.util.threads.ThreadPoolExecutor'>ThreadPoolExecutor.java:1176</a>)<br/>org.apache.tomcat.util.threads.ThreadPoolExecutor$Worker.run(<a href='?part=source&amp;class=org.apache.tomcat.util.threads.ThreadPoolExecutor$Worker#659' class='lightwindow' type='external' title='org.apache.tomcat.util.threads.ThreadPoolExecutor$Worker'>ThreadPoolExecutor.java:659</a>)<br/>org.apache.tomcat.util.threads.TaskThread$WrappingRunnable.run(<a href='?part=source&amp;class=org.apache.tomcat.util.threads.TaskThread$WrappingRunnable#61' class='lightwindow' type='external' title='org.apache.tomcat.util.threads.TaskThread$WrappingRunnable'>TaskThread.java:61</a>)<br/>java.lang.Thread.run(<a href='?part=source&amp;class=java.lang.Thread#748' class='lightwindow' type='external' title='java.lang.Thread'>Thread.java:748</a>)<br/></em>
http-nio-8092-exec-17</div>
</td> <td align='center'>yes</td> <td align='right'>5</td> <td><img src='?resource=bullets/yellow.png' alt='WAITING'/>WAITING</td> <td>sun.misc.Unsafe.park(Native Method)</td> <td align='right'>62</td> <td align='right'>31</td> <td align='center' class='noPrint'><a href='?action=send_thread_interrupt&amp;threadId=40272_***********_657' onclick="javascript:return confirm('Do you confirm the sending of a thread interrupt signal to http-nio-8092-exec-17?');"><img width='16' height='16' src='?resource=action_interrupt.png' alt='Send a thread interrupt signal to http-nio-8092-exec-17. The thread can test Thread.currentThread().isInterrupted() to stop itself.' title='Send a thread interrupt signal to http-nio-8092-exec-17. The thread can test Thread.currentThread().isInterrupted() to stop itself.' /></a></td> <td align='center' class='noPrint'><a href='?action=kill_thread&amp;threadId=40272_***********_657' onclick="javascript:return confirm('杀掉一个Java线程是不推荐和不安全的，你确定要杀掉线程 http-nio-8092-exec-17 ?');"><img width='16' height='16' src='?resource=stop.png' alt='杀掉线程&nbsp;http-nio-8092-exec-17' title='杀掉线程&nbsp;http-nio-8092-exec-17' /></a></td></tr><tr onmouseover="this.className='highlight'" onmouseout="this.className=''">
<td><div class='tooltip'>
<em>
http-nio-8092-exec-18<br/>
sun.misc.Unsafe.park(Native Method)<br/>java.util.concurrent.locks.LockSupport.park(<a href='?part=source&amp;class=java.util.concurrent.locks.LockSupport#175' class='lightwindow' type='external' title='java.util.concurrent.locks.LockSupport'>LockSupport.java:175</a>)<br/>java.util.concurrent.locks.AbstractQueuedSynchronizer$ConditionObject.await(<a href='?part=source&amp;class=java.util.concurrent.locks.AbstractQueuedSynchronizer$ConditionObject#2039' class='lightwindow' type='external' title='java.util.concurrent.locks.AbstractQueuedSynchronizer$ConditionObject'>AbstractQueuedSynchronizer.java:2039</a>)<br/>java.util.concurrent.LinkedBlockingQueue.take(<a href='?part=source&amp;class=java.util.concurrent.LinkedBlockingQueue#442' class='lightwindow' type='external' title='java.util.concurrent.LinkedBlockingQueue'>LinkedBlockingQueue.java:442</a>)<br/>org.apache.tomcat.util.threads.TaskQueue.take(<a href='?part=source&amp;class=org.apache.tomcat.util.threads.TaskQueue#146' class='lightwindow' type='external' title='org.apache.tomcat.util.threads.TaskQueue'>TaskQueue.java:146</a>)<br/>org.apache.tomcat.util.threads.TaskQueue.take(<a href='?part=source&amp;class=org.apache.tomcat.util.threads.TaskQueue#33' class='lightwindow' type='external' title='org.apache.tomcat.util.threads.TaskQueue'>TaskQueue.java:33</a>)<br/>org.apache.tomcat.util.threads.ThreadPoolExecutor.getTask(<a href='?part=source&amp;class=org.apache.tomcat.util.threads.ThreadPoolExecutor#1114' class='lightwindow' type='external' title='org.apache.tomcat.util.threads.ThreadPoolExecutor'>ThreadPoolExecutor.java:1114</a>)<br/>org.apache.tomcat.util.threads.ThreadPoolExecutor.runWorker(<a href='?part=source&amp;class=org.apache.tomcat.util.threads.ThreadPoolExecutor#1176' class='lightwindow' type='external' title='org.apache.tomcat.util.threads.ThreadPoolExecutor'>ThreadPoolExecutor.java:1176</a>)<br/>org.apache.tomcat.util.threads.ThreadPoolExecutor$Worker.run(<a href='?part=source&amp;class=org.apache.tomcat.util.threads.ThreadPoolExecutor$Worker#659' class='lightwindow' type='external' title='org.apache.tomcat.util.threads.ThreadPoolExecutor$Worker'>ThreadPoolExecutor.java:659</a>)<br/>org.apache.tomcat.util.threads.TaskThread$WrappingRunnable.run(<a href='?part=source&amp;class=org.apache.tomcat.util.threads.TaskThread$WrappingRunnable#61' class='lightwindow' type='external' title='org.apache.tomcat.util.threads.TaskThread$WrappingRunnable'>TaskThread.java:61</a>)<br/>java.lang.Thread.run(<a href='?part=source&amp;class=java.lang.Thread#748' class='lightwindow' type='external' title='java.lang.Thread'>Thread.java:748</a>)<br/></em>
http-nio-8092-exec-18</div>
</td> <td align='center'>yes</td> <td align='right'>5</td> <td><img src='?resource=bullets/yellow.png' alt='WAITING'/>WAITING</td> <td>sun.misc.Unsafe.park(Native Method)</td> <td align='right'>46</td> <td align='right'>31</td> <td align='center' class='noPrint'><a href='?action=send_thread_interrupt&amp;threadId=40272_***********_658' onclick="javascript:return confirm('Do you confirm the sending of a thread interrupt signal to http-nio-8092-exec-18?');"><img width='16' height='16' src='?resource=action_interrupt.png' alt='Send a thread interrupt signal to http-nio-8092-exec-18. The thread can test Thread.currentThread().isInterrupted() to stop itself.' title='Send a thread interrupt signal to http-nio-8092-exec-18. The thread can test Thread.currentThread().isInterrupted() to stop itself.' /></a></td> <td align='center' class='noPrint'><a href='?action=kill_thread&amp;threadId=40272_***********_658' onclick="javascript:return confirm('杀掉一个Java线程是不推荐和不安全的，你确定要杀掉线程 http-nio-8092-exec-18 ?');"><img width='16' height='16' src='?resource=stop.png' alt='杀掉线程&nbsp;http-nio-8092-exec-18' title='杀掉线程&nbsp;http-nio-8092-exec-18' /></a></td></tr><tr class='odd' onmouseover="this.className='highlight'" onmouseout="this.className='odd'">
<td><div class='tooltip'>
<em>
http-nio-8092-exec-19<br/>
sun.misc.Unsafe.park(Native Method)<br/>java.util.concurrent.locks.LockSupport.park(<a href='?part=source&amp;class=java.util.concurrent.locks.LockSupport#175' class='lightwindow' type='external' title='java.util.concurrent.locks.LockSupport'>LockSupport.java:175</a>)<br/>java.util.concurrent.locks.AbstractQueuedSynchronizer$ConditionObject.await(<a href='?part=source&amp;class=java.util.concurrent.locks.AbstractQueuedSynchronizer$ConditionObject#2039' class='lightwindow' type='external' title='java.util.concurrent.locks.AbstractQueuedSynchronizer$ConditionObject'>AbstractQueuedSynchronizer.java:2039</a>)<br/>java.util.concurrent.LinkedBlockingQueue.take(<a href='?part=source&amp;class=java.util.concurrent.LinkedBlockingQueue#442' class='lightwindow' type='external' title='java.util.concurrent.LinkedBlockingQueue'>LinkedBlockingQueue.java:442</a>)<br/>org.apache.tomcat.util.threads.TaskQueue.take(<a href='?part=source&amp;class=org.apache.tomcat.util.threads.TaskQueue#146' class='lightwindow' type='external' title='org.apache.tomcat.util.threads.TaskQueue'>TaskQueue.java:146</a>)<br/>org.apache.tomcat.util.threads.TaskQueue.take(<a href='?part=source&amp;class=org.apache.tomcat.util.threads.TaskQueue#33' class='lightwindow' type='external' title='org.apache.tomcat.util.threads.TaskQueue'>TaskQueue.java:33</a>)<br/>org.apache.tomcat.util.threads.ThreadPoolExecutor.getTask(<a href='?part=source&amp;class=org.apache.tomcat.util.threads.ThreadPoolExecutor#1114' class='lightwindow' type='external' title='org.apache.tomcat.util.threads.ThreadPoolExecutor'>ThreadPoolExecutor.java:1114</a>)<br/>org.apache.tomcat.util.threads.ThreadPoolExecutor.runWorker(<a href='?part=source&amp;class=org.apache.tomcat.util.threads.ThreadPoolExecutor#1176' class='lightwindow' type='external' title='org.apache.tomcat.util.threads.ThreadPoolExecutor'>ThreadPoolExecutor.java:1176</a>)<br/>org.apache.tomcat.util.threads.ThreadPoolExecutor$Worker.run(<a href='?part=source&amp;class=org.apache.tomcat.util.threads.ThreadPoolExecutor$Worker#659' class='lightwindow' type='external' title='org.apache.tomcat.util.threads.ThreadPoolExecutor$Worker'>ThreadPoolExecutor.java:659</a>)<br/>org.apache.tomcat.util.threads.TaskThread$WrappingRunnable.run(<a href='?part=source&amp;class=org.apache.tomcat.util.threads.TaskThread$WrappingRunnable#61' class='lightwindow' type='external' title='org.apache.tomcat.util.threads.TaskThread$WrappingRunnable'>TaskThread.java:61</a>)<br/>java.lang.Thread.run(<a href='?part=source&amp;class=java.lang.Thread#748' class='lightwindow' type='external' title='java.lang.Thread'>Thread.java:748</a>)<br/></em>
http-nio-8092-exec-19</div>
</td> <td align='center'>yes</td> <td align='right'>5</td> <td><img src='?resource=bullets/yellow.png' alt='WAITING'/>WAITING</td> <td>sun.misc.Unsafe.park(Native Method)</td> <td align='right'>218</td> <td align='right'>171</td> <td align='center' class='noPrint'><a href='?action=send_thread_interrupt&amp;threadId=40272_***********_659' onclick="javascript:return confirm('Do you confirm the sending of a thread interrupt signal to http-nio-8092-exec-19?');"><img width='16' height='16' src='?resource=action_interrupt.png' alt='Send a thread interrupt signal to http-nio-8092-exec-19. The thread can test Thread.currentThread().isInterrupted() to stop itself.' title='Send a thread interrupt signal to http-nio-8092-exec-19. The thread can test Thread.currentThread().isInterrupted() to stop itself.' /></a></td> <td align='center' class='noPrint'><a href='?action=kill_thread&amp;threadId=40272_***********_659' onclick="javascript:return confirm('杀掉一个Java线程是不推荐和不安全的，你确定要杀掉线程 http-nio-8092-exec-19 ?');"><img width='16' height='16' src='?resource=stop.png' alt='杀掉线程&nbsp;http-nio-8092-exec-19' title='杀掉线程&nbsp;http-nio-8092-exec-19' /></a></td></tr><tr onmouseover="this.className='highlight'" onmouseout="this.className=''">
<td><div class='tooltip'>
<em>
http-nio-8092-exec-2<br/>
sun.misc.Unsafe.park(Native Method)<br/>java.util.concurrent.locks.LockSupport.park(<a href='?part=source&amp;class=java.util.concurrent.locks.LockSupport#175' class='lightwindow' type='external' title='java.util.concurrent.locks.LockSupport'>LockSupport.java:175</a>)<br/>java.util.concurrent.locks.AbstractQueuedSynchronizer$ConditionObject.await(<a href='?part=source&amp;class=java.util.concurrent.locks.AbstractQueuedSynchronizer$ConditionObject#2039' class='lightwindow' type='external' title='java.util.concurrent.locks.AbstractQueuedSynchronizer$ConditionObject'>AbstractQueuedSynchronizer.java:2039</a>)<br/>java.util.concurrent.LinkedBlockingQueue.take(<a href='?part=source&amp;class=java.util.concurrent.LinkedBlockingQueue#442' class='lightwindow' type='external' title='java.util.concurrent.LinkedBlockingQueue'>LinkedBlockingQueue.java:442</a>)<br/>org.apache.tomcat.util.threads.TaskQueue.take(<a href='?part=source&amp;class=org.apache.tomcat.util.threads.TaskQueue#146' class='lightwindow' type='external' title='org.apache.tomcat.util.threads.TaskQueue'>TaskQueue.java:146</a>)<br/>org.apache.tomcat.util.threads.TaskQueue.take(<a href='?part=source&amp;class=org.apache.tomcat.util.threads.TaskQueue#33' class='lightwindow' type='external' title='org.apache.tomcat.util.threads.TaskQueue'>TaskQueue.java:33</a>)<br/>org.apache.tomcat.util.threads.ThreadPoolExecutor.getTask(<a href='?part=source&amp;class=org.apache.tomcat.util.threads.ThreadPoolExecutor#1114' class='lightwindow' type='external' title='org.apache.tomcat.util.threads.ThreadPoolExecutor'>ThreadPoolExecutor.java:1114</a>)<br/>org.apache.tomcat.util.threads.ThreadPoolExecutor.runWorker(<a href='?part=source&amp;class=org.apache.tomcat.util.threads.ThreadPoolExecutor#1176' class='lightwindow' type='external' title='org.apache.tomcat.util.threads.ThreadPoolExecutor'>ThreadPoolExecutor.java:1176</a>)<br/>org.apache.tomcat.util.threads.ThreadPoolExecutor$Worker.run(<a href='?part=source&amp;class=org.apache.tomcat.util.threads.ThreadPoolExecutor$Worker#659' class='lightwindow' type='external' title='org.apache.tomcat.util.threads.ThreadPoolExecutor$Worker'>ThreadPoolExecutor.java:659</a>)<br/>org.apache.tomcat.util.threads.TaskThread$WrappingRunnable.run(<a href='?part=source&amp;class=org.apache.tomcat.util.threads.TaskThread$WrappingRunnable#61' class='lightwindow' type='external' title='org.apache.tomcat.util.threads.TaskThread$WrappingRunnable'>TaskThread.java:61</a>)<br/>java.lang.Thread.run(<a href='?part=source&amp;class=java.lang.Thread#748' class='lightwindow' type='external' title='java.lang.Thread'>Thread.java:748</a>)<br/></em>
http-nio-8092-exec-2</div>
</td> <td align='center'>yes</td> <td align='right'>5</td> <td><img src='?resource=bullets/yellow.png' alt='WAITING'/>WAITING</td> <td>sun.misc.Unsafe.park(Native Method)</td> <td align='right'>31</td> <td align='right'>15</td> <td align='center' class='noPrint'><a href='?action=send_thread_interrupt&amp;threadId=40272_***********_642' onclick="javascript:return confirm('Do you confirm the sending of a thread interrupt signal to http-nio-8092-exec-2?');"><img width='16' height='16' src='?resource=action_interrupt.png' alt='Send a thread interrupt signal to http-nio-8092-exec-2. The thread can test Thread.currentThread().isInterrupted() to stop itself.' title='Send a thread interrupt signal to http-nio-8092-exec-2. The thread can test Thread.currentThread().isInterrupted() to stop itself.' /></a></td> <td align='center' class='noPrint'><a href='?action=kill_thread&amp;threadId=40272_***********_642' onclick="javascript:return confirm('杀掉一个Java线程是不推荐和不安全的，你确定要杀掉线程 http-nio-8092-exec-2 ?');"><img width='16' height='16' src='?resource=stop.png' alt='杀掉线程&nbsp;http-nio-8092-exec-2' title='杀掉线程&nbsp;http-nio-8092-exec-2' /></a></td></tr><tr class='odd' onmouseover="this.className='highlight'" onmouseout="this.className='odd'">
<td><div class='tooltip'>
<em>
http-nio-8092-exec-20<br/>
sun.misc.Unsafe.park(Native Method)<br/>java.util.concurrent.locks.LockSupport.park(<a href='?part=source&amp;class=java.util.concurrent.locks.LockSupport#175' class='lightwindow' type='external' title='java.util.concurrent.locks.LockSupport'>LockSupport.java:175</a>)<br/>java.util.concurrent.locks.AbstractQueuedSynchronizer$ConditionObject.await(<a href='?part=source&amp;class=java.util.concurrent.locks.AbstractQueuedSynchronizer$ConditionObject#2039' class='lightwindow' type='external' title='java.util.concurrent.locks.AbstractQueuedSynchronizer$ConditionObject'>AbstractQueuedSynchronizer.java:2039</a>)<br/>java.util.concurrent.LinkedBlockingQueue.take(<a href='?part=source&amp;class=java.util.concurrent.LinkedBlockingQueue#442' class='lightwindow' type='external' title='java.util.concurrent.LinkedBlockingQueue'>LinkedBlockingQueue.java:442</a>)<br/>org.apache.tomcat.util.threads.TaskQueue.take(<a href='?part=source&amp;class=org.apache.tomcat.util.threads.TaskQueue#146' class='lightwindow' type='external' title='org.apache.tomcat.util.threads.TaskQueue'>TaskQueue.java:146</a>)<br/>org.apache.tomcat.util.threads.TaskQueue.take(<a href='?part=source&amp;class=org.apache.tomcat.util.threads.TaskQueue#33' class='lightwindow' type='external' title='org.apache.tomcat.util.threads.TaskQueue'>TaskQueue.java:33</a>)<br/>org.apache.tomcat.util.threads.ThreadPoolExecutor.getTask(<a href='?part=source&amp;class=org.apache.tomcat.util.threads.ThreadPoolExecutor#1114' class='lightwindow' type='external' title='org.apache.tomcat.util.threads.ThreadPoolExecutor'>ThreadPoolExecutor.java:1114</a>)<br/>org.apache.tomcat.util.threads.ThreadPoolExecutor.runWorker(<a href='?part=source&amp;class=org.apache.tomcat.util.threads.ThreadPoolExecutor#1176' class='lightwindow' type='external' title='org.apache.tomcat.util.threads.ThreadPoolExecutor'>ThreadPoolExecutor.java:1176</a>)<br/>org.apache.tomcat.util.threads.ThreadPoolExecutor$Worker.run(<a href='?part=source&amp;class=org.apache.tomcat.util.threads.ThreadPoolExecutor$Worker#659' class='lightwindow' type='external' title='org.apache.tomcat.util.threads.ThreadPoolExecutor$Worker'>ThreadPoolExecutor.java:659</a>)<br/>org.apache.tomcat.util.threads.TaskThread$WrappingRunnable.run(<a href='?part=source&amp;class=org.apache.tomcat.util.threads.TaskThread$WrappingRunnable#61' class='lightwindow' type='external' title='org.apache.tomcat.util.threads.TaskThread$WrappingRunnable'>TaskThread.java:61</a>)<br/>java.lang.Thread.run(<a href='?part=source&amp;class=java.lang.Thread#748' class='lightwindow' type='external' title='java.lang.Thread'>Thread.java:748</a>)<br/></em>
http-nio-8092-exec-20</div>
</td> <td align='center'>yes</td> <td align='right'>5</td> <td><img src='?resource=bullets/yellow.png' alt='WAITING'/>WAITING</td> <td>sun.misc.Unsafe.park(Native Method)</td> <td align='right'>109</td> <td align='right'>62</td> <td align='center' class='noPrint'><a href='?action=send_thread_interrupt&amp;threadId=40272_***********_660' onclick="javascript:return confirm('Do you confirm the sending of a thread interrupt signal to http-nio-8092-exec-20?');"><img width='16' height='16' src='?resource=action_interrupt.png' alt='Send a thread interrupt signal to http-nio-8092-exec-20. The thread can test Thread.currentThread().isInterrupted() to stop itself.' title='Send a thread interrupt signal to http-nio-8092-exec-20. The thread can test Thread.currentThread().isInterrupted() to stop itself.' /></a></td> <td align='center' class='noPrint'><a href='?action=kill_thread&amp;threadId=40272_***********_660' onclick="javascript:return confirm('杀掉一个Java线程是不推荐和不安全的，你确定要杀掉线程 http-nio-8092-exec-20 ?');"><img width='16' height='16' src='?resource=stop.png' alt='杀掉线程&nbsp;http-nio-8092-exec-20' title='杀掉线程&nbsp;http-nio-8092-exec-20' /></a></td></tr><tr onmouseover="this.className='highlight'" onmouseout="this.className=''">
<td><div class='tooltip'>
<em>
http-nio-8092-exec-3<br/>
sun.misc.Unsafe.park(Native Method)<br/>java.util.concurrent.locks.LockSupport.park(<a href='?part=source&amp;class=java.util.concurrent.locks.LockSupport#175' class='lightwindow' type='external' title='java.util.concurrent.locks.LockSupport'>LockSupport.java:175</a>)<br/>java.util.concurrent.locks.AbstractQueuedSynchronizer$ConditionObject.await(<a href='?part=source&amp;class=java.util.concurrent.locks.AbstractQueuedSynchronizer$ConditionObject#2039' class='lightwindow' type='external' title='java.util.concurrent.locks.AbstractQueuedSynchronizer$ConditionObject'>AbstractQueuedSynchronizer.java:2039</a>)<br/>java.util.concurrent.LinkedBlockingQueue.take(<a href='?part=source&amp;class=java.util.concurrent.LinkedBlockingQueue#442' class='lightwindow' type='external' title='java.util.concurrent.LinkedBlockingQueue'>LinkedBlockingQueue.java:442</a>)<br/>org.apache.tomcat.util.threads.TaskQueue.take(<a href='?part=source&amp;class=org.apache.tomcat.util.threads.TaskQueue#146' class='lightwindow' type='external' title='org.apache.tomcat.util.threads.TaskQueue'>TaskQueue.java:146</a>)<br/>org.apache.tomcat.util.threads.TaskQueue.take(<a href='?part=source&amp;class=org.apache.tomcat.util.threads.TaskQueue#33' class='lightwindow' type='external' title='org.apache.tomcat.util.threads.TaskQueue'>TaskQueue.java:33</a>)<br/>org.apache.tomcat.util.threads.ThreadPoolExecutor.getTask(<a href='?part=source&amp;class=org.apache.tomcat.util.threads.ThreadPoolExecutor#1114' class='lightwindow' type='external' title='org.apache.tomcat.util.threads.ThreadPoolExecutor'>ThreadPoolExecutor.java:1114</a>)<br/>org.apache.tomcat.util.threads.ThreadPoolExecutor.runWorker(<a href='?part=source&amp;class=org.apache.tomcat.util.threads.ThreadPoolExecutor#1176' class='lightwindow' type='external' title='org.apache.tomcat.util.threads.ThreadPoolExecutor'>ThreadPoolExecutor.java:1176</a>)<br/>org.apache.tomcat.util.threads.ThreadPoolExecutor$Worker.run(<a href='?part=source&amp;class=org.apache.tomcat.util.threads.ThreadPoolExecutor$Worker#659' class='lightwindow' type='external' title='org.apache.tomcat.util.threads.ThreadPoolExecutor$Worker'>ThreadPoolExecutor.java:659</a>)<br/>org.apache.tomcat.util.threads.TaskThread$WrappingRunnable.run(<a href='?part=source&amp;class=org.apache.tomcat.util.threads.TaskThread$WrappingRunnable#61' class='lightwindow' type='external' title='org.apache.tomcat.util.threads.TaskThread$WrappingRunnable'>TaskThread.java:61</a>)<br/>java.lang.Thread.run(<a href='?part=source&amp;class=java.lang.Thread#748' class='lightwindow' type='external' title='java.lang.Thread'>Thread.java:748</a>)<br/></em>
http-nio-8092-exec-3</div>
</td> <td align='center'>yes</td> <td align='right'>5</td> <td><img src='?resource=bullets/yellow.png' alt='WAITING'/>WAITING</td> <td>sun.misc.Unsafe.park(Native Method)</td> <td align='right'>125</td> <td align='right'>93</td> <td align='center' class='noPrint'><a href='?action=send_thread_interrupt&amp;threadId=40272_***********_643' onclick="javascript:return confirm('Do you confirm the sending of a thread interrupt signal to http-nio-8092-exec-3?');"><img width='16' height='16' src='?resource=action_interrupt.png' alt='Send a thread interrupt signal to http-nio-8092-exec-3. The thread can test Thread.currentThread().isInterrupted() to stop itself.' title='Send a thread interrupt signal to http-nio-8092-exec-3. The thread can test Thread.currentThread().isInterrupted() to stop itself.' /></a></td> <td align='center' class='noPrint'><a href='?action=kill_thread&amp;threadId=40272_***********_643' onclick="javascript:return confirm('杀掉一个Java线程是不推荐和不安全的，你确定要杀掉线程 http-nio-8092-exec-3 ?');"><img width='16' height='16' src='?resource=stop.png' alt='杀掉线程&nbsp;http-nio-8092-exec-3' title='杀掉线程&nbsp;http-nio-8092-exec-3' /></a></td></tr><tr class='odd' onmouseover="this.className='highlight'" onmouseout="this.className='odd'">
<td><div class='tooltip'>
<em>
http-nio-8092-exec-4<br/>
sun.misc.Unsafe.park(Native Method)<br/>java.util.concurrent.locks.LockSupport.park(<a href='?part=source&amp;class=java.util.concurrent.locks.LockSupport#175' class='lightwindow' type='external' title='java.util.concurrent.locks.LockSupport'>LockSupport.java:175</a>)<br/>java.util.concurrent.locks.AbstractQueuedSynchronizer$ConditionObject.await(<a href='?part=source&amp;class=java.util.concurrent.locks.AbstractQueuedSynchronizer$ConditionObject#2039' class='lightwindow' type='external' title='java.util.concurrent.locks.AbstractQueuedSynchronizer$ConditionObject'>AbstractQueuedSynchronizer.java:2039</a>)<br/>java.util.concurrent.LinkedBlockingQueue.take(<a href='?part=source&amp;class=java.util.concurrent.LinkedBlockingQueue#442' class='lightwindow' type='external' title='java.util.concurrent.LinkedBlockingQueue'>LinkedBlockingQueue.java:442</a>)<br/>org.apache.tomcat.util.threads.TaskQueue.take(<a href='?part=source&amp;class=org.apache.tomcat.util.threads.TaskQueue#146' class='lightwindow' type='external' title='org.apache.tomcat.util.threads.TaskQueue'>TaskQueue.java:146</a>)<br/>org.apache.tomcat.util.threads.TaskQueue.take(<a href='?part=source&amp;class=org.apache.tomcat.util.threads.TaskQueue#33' class='lightwindow' type='external' title='org.apache.tomcat.util.threads.TaskQueue'>TaskQueue.java:33</a>)<br/>org.apache.tomcat.util.threads.ThreadPoolExecutor.getTask(<a href='?part=source&amp;class=org.apache.tomcat.util.threads.ThreadPoolExecutor#1114' class='lightwindow' type='external' title='org.apache.tomcat.util.threads.ThreadPoolExecutor'>ThreadPoolExecutor.java:1114</a>)<br/>org.apache.tomcat.util.threads.ThreadPoolExecutor.runWorker(<a href='?part=source&amp;class=org.apache.tomcat.util.threads.ThreadPoolExecutor#1176' class='lightwindow' type='external' title='org.apache.tomcat.util.threads.ThreadPoolExecutor'>ThreadPoolExecutor.java:1176</a>)<br/>org.apache.tomcat.util.threads.ThreadPoolExecutor$Worker.run(<a href='?part=source&amp;class=org.apache.tomcat.util.threads.ThreadPoolExecutor$Worker#659' class='lightwindow' type='external' title='org.apache.tomcat.util.threads.ThreadPoolExecutor$Worker'>ThreadPoolExecutor.java:659</a>)<br/>org.apache.tomcat.util.threads.TaskThread$WrappingRunnable.run(<a href='?part=source&amp;class=org.apache.tomcat.util.threads.TaskThread$WrappingRunnable#61' class='lightwindow' type='external' title='org.apache.tomcat.util.threads.TaskThread$WrappingRunnable'>TaskThread.java:61</a>)<br/>java.lang.Thread.run(<a href='?part=source&amp;class=java.lang.Thread#748' class='lightwindow' type='external' title='java.lang.Thread'>Thread.java:748</a>)<br/></em>
http-nio-8092-exec-4</div>
</td> <td align='center'>yes</td> <td align='right'>5</td> <td><img src='?resource=bullets/yellow.png' alt='WAITING'/>WAITING</td> <td>sun.misc.Unsafe.park(Native Method)</td> <td align='right'>390</td> <td align='right'>375</td> <td align='center' class='noPrint'><a href='?action=send_thread_interrupt&amp;threadId=40272_***********_644' onclick="javascript:return confirm('Do you confirm the sending of a thread interrupt signal to http-nio-8092-exec-4?');"><img width='16' height='16' src='?resource=action_interrupt.png' alt='Send a thread interrupt signal to http-nio-8092-exec-4. The thread can test Thread.currentThread().isInterrupted() to stop itself.' title='Send a thread interrupt signal to http-nio-8092-exec-4. The thread can test Thread.currentThread().isInterrupted() to stop itself.' /></a></td> <td align='center' class='noPrint'><a href='?action=kill_thread&amp;threadId=40272_***********_644' onclick="javascript:return confirm('杀掉一个Java线程是不推荐和不安全的，你确定要杀掉线程 http-nio-8092-exec-4 ?');"><img width='16' height='16' src='?resource=stop.png' alt='杀掉线程&nbsp;http-nio-8092-exec-4' title='杀掉线程&nbsp;http-nio-8092-exec-4' /></a></td></tr><tr onmouseover="this.className='highlight'" onmouseout="this.className=''">
<td><div class='tooltip'>
<em>
http-nio-8092-exec-5<br/>
sun.misc.Unsafe.park(Native Method)<br/>java.util.concurrent.locks.LockSupport.park(<a href='?part=source&amp;class=java.util.concurrent.locks.LockSupport#175' class='lightwindow' type='external' title='java.util.concurrent.locks.LockSupport'>LockSupport.java:175</a>)<br/>java.util.concurrent.locks.AbstractQueuedSynchronizer$ConditionObject.await(<a href='?part=source&amp;class=java.util.concurrent.locks.AbstractQueuedSynchronizer$ConditionObject#2039' class='lightwindow' type='external' title='java.util.concurrent.locks.AbstractQueuedSynchronizer$ConditionObject'>AbstractQueuedSynchronizer.java:2039</a>)<br/>java.util.concurrent.LinkedBlockingQueue.take(<a href='?part=source&amp;class=java.util.concurrent.LinkedBlockingQueue#442' class='lightwindow' type='external' title='java.util.concurrent.LinkedBlockingQueue'>LinkedBlockingQueue.java:442</a>)<br/>org.apache.tomcat.util.threads.TaskQueue.take(<a href='?part=source&amp;class=org.apache.tomcat.util.threads.TaskQueue#146' class='lightwindow' type='external' title='org.apache.tomcat.util.threads.TaskQueue'>TaskQueue.java:146</a>)<br/>org.apache.tomcat.util.threads.TaskQueue.take(<a href='?part=source&amp;class=org.apache.tomcat.util.threads.TaskQueue#33' class='lightwindow' type='external' title='org.apache.tomcat.util.threads.TaskQueue'>TaskQueue.java:33</a>)<br/>org.apache.tomcat.util.threads.ThreadPoolExecutor.getTask(<a href='?part=source&amp;class=org.apache.tomcat.util.threads.ThreadPoolExecutor#1114' class='lightwindow' type='external' title='org.apache.tomcat.util.threads.ThreadPoolExecutor'>ThreadPoolExecutor.java:1114</a>)<br/>org.apache.tomcat.util.threads.ThreadPoolExecutor.runWorker(<a href='?part=source&amp;class=org.apache.tomcat.util.threads.ThreadPoolExecutor#1176' class='lightwindow' type='external' title='org.apache.tomcat.util.threads.ThreadPoolExecutor'>ThreadPoolExecutor.java:1176</a>)<br/>org.apache.tomcat.util.threads.ThreadPoolExecutor$Worker.run(<a href='?part=source&amp;class=org.apache.tomcat.util.threads.ThreadPoolExecutor$Worker#659' class='lightwindow' type='external' title='org.apache.tomcat.util.threads.ThreadPoolExecutor$Worker'>ThreadPoolExecutor.java:659</a>)<br/>org.apache.tomcat.util.threads.TaskThread$WrappingRunnable.run(<a href='?part=source&amp;class=org.apache.tomcat.util.threads.TaskThread$WrappingRunnable#61' class='lightwindow' type='external' title='org.apache.tomcat.util.threads.TaskThread$WrappingRunnable'>TaskThread.java:61</a>)<br/>java.lang.Thread.run(<a href='?part=source&amp;class=java.lang.Thread#748' class='lightwindow' type='external' title='java.lang.Thread'>Thread.java:748</a>)<br/></em>
http-nio-8092-exec-5</div>
</td> <td align='center'>yes</td> <td align='right'>5</td> <td><img src='?resource=bullets/yellow.png' alt='WAITING'/>WAITING</td> <td>sun.misc.Unsafe.park(Native Method)</td> <td align='right'>359</td> <td align='right'>250</td> <td align='center' class='noPrint'><a href='?action=send_thread_interrupt&amp;threadId=40272_***********_645' onclick="javascript:return confirm('Do you confirm the sending of a thread interrupt signal to http-nio-8092-exec-5?');"><img width='16' height='16' src='?resource=action_interrupt.png' alt='Send a thread interrupt signal to http-nio-8092-exec-5. The thread can test Thread.currentThread().isInterrupted() to stop itself.' title='Send a thread interrupt signal to http-nio-8092-exec-5. The thread can test Thread.currentThread().isInterrupted() to stop itself.' /></a></td> <td align='center' class='noPrint'><a href='?action=kill_thread&amp;threadId=40272_***********_645' onclick="javascript:return confirm('杀掉一个Java线程是不推荐和不安全的，你确定要杀掉线程 http-nio-8092-exec-5 ?');"><img width='16' height='16' src='?resource=stop.png' alt='杀掉线程&nbsp;http-nio-8092-exec-5' title='杀掉线程&nbsp;http-nio-8092-exec-5' /></a></td></tr><tr class='odd' onmouseover="this.className='highlight'" onmouseout="this.className='odd'">
<td><div class='tooltip'>
<em>
http-nio-8092-exec-6<br/>
sun.misc.Unsafe.park(Native Method)<br/>java.util.concurrent.locks.LockSupport.park(<a href='?part=source&amp;class=java.util.concurrent.locks.LockSupport#175' class='lightwindow' type='external' title='java.util.concurrent.locks.LockSupport'>LockSupport.java:175</a>)<br/>java.util.concurrent.locks.AbstractQueuedSynchronizer$ConditionObject.await(<a href='?part=source&amp;class=java.util.concurrent.locks.AbstractQueuedSynchronizer$ConditionObject#2039' class='lightwindow' type='external' title='java.util.concurrent.locks.AbstractQueuedSynchronizer$ConditionObject'>AbstractQueuedSynchronizer.java:2039</a>)<br/>java.util.concurrent.LinkedBlockingQueue.take(<a href='?part=source&amp;class=java.util.concurrent.LinkedBlockingQueue#442' class='lightwindow' type='external' title='java.util.concurrent.LinkedBlockingQueue'>LinkedBlockingQueue.java:442</a>)<br/>org.apache.tomcat.util.threads.TaskQueue.take(<a href='?part=source&amp;class=org.apache.tomcat.util.threads.TaskQueue#146' class='lightwindow' type='external' title='org.apache.tomcat.util.threads.TaskQueue'>TaskQueue.java:146</a>)<br/>org.apache.tomcat.util.threads.TaskQueue.take(<a href='?part=source&amp;class=org.apache.tomcat.util.threads.TaskQueue#33' class='lightwindow' type='external' title='org.apache.tomcat.util.threads.TaskQueue'>TaskQueue.java:33</a>)<br/>org.apache.tomcat.util.threads.ThreadPoolExecutor.getTask(<a href='?part=source&amp;class=org.apache.tomcat.util.threads.ThreadPoolExecutor#1114' class='lightwindow' type='external' title='org.apache.tomcat.util.threads.ThreadPoolExecutor'>ThreadPoolExecutor.java:1114</a>)<br/>org.apache.tomcat.util.threads.ThreadPoolExecutor.runWorker(<a href='?part=source&amp;class=org.apache.tomcat.util.threads.ThreadPoolExecutor#1176' class='lightwindow' type='external' title='org.apache.tomcat.util.threads.ThreadPoolExecutor'>ThreadPoolExecutor.java:1176</a>)<br/>org.apache.tomcat.util.threads.ThreadPoolExecutor$Worker.run(<a href='?part=source&amp;class=org.apache.tomcat.util.threads.ThreadPoolExecutor$Worker#659' class='lightwindow' type='external' title='org.apache.tomcat.util.threads.ThreadPoolExecutor$Worker'>ThreadPoolExecutor.java:659</a>)<br/>org.apache.tomcat.util.threads.TaskThread$WrappingRunnable.run(<a href='?part=source&amp;class=org.apache.tomcat.util.threads.TaskThread$WrappingRunnable#61' class='lightwindow' type='external' title='org.apache.tomcat.util.threads.TaskThread$WrappingRunnable'>TaskThread.java:61</a>)<br/>java.lang.Thread.run(<a href='?part=source&amp;class=java.lang.Thread#748' class='lightwindow' type='external' title='java.lang.Thread'>Thread.java:748</a>)<br/></em>
http-nio-8092-exec-6</div>
</td> <td align='center'>yes</td> <td align='right'>5</td> <td><img src='?resource=bullets/yellow.png' alt='WAITING'/>WAITING</td> <td>sun.misc.Unsafe.park(Native Method)</td> <td align='right'>140</td> <td align='right'>125</td> <td align='center' class='noPrint'><a href='?action=send_thread_interrupt&amp;threadId=40272_***********_646' onclick="javascript:return confirm('Do you confirm the sending of a thread interrupt signal to http-nio-8092-exec-6?');"><img width='16' height='16' src='?resource=action_interrupt.png' alt='Send a thread interrupt signal to http-nio-8092-exec-6. The thread can test Thread.currentThread().isInterrupted() to stop itself.' title='Send a thread interrupt signal to http-nio-8092-exec-6. The thread can test Thread.currentThread().isInterrupted() to stop itself.' /></a></td> <td align='center' class='noPrint'><a href='?action=kill_thread&amp;threadId=40272_***********_646' onclick="javascript:return confirm('杀掉一个Java线程是不推荐和不安全的，你确定要杀掉线程 http-nio-8092-exec-6 ?');"><img width='16' height='16' src='?resource=stop.png' alt='杀掉线程&nbsp;http-nio-8092-exec-6' title='杀掉线程&nbsp;http-nio-8092-exec-6' /></a></td></tr><tr onmouseover="this.className='highlight'" onmouseout="this.className=''">
<td><div class='tooltip'>
<em>
http-nio-8092-exec-7<br/>
sun.misc.Unsafe.park(Native Method)<br/>java.util.concurrent.locks.LockSupport.park(<a href='?part=source&amp;class=java.util.concurrent.locks.LockSupport#175' class='lightwindow' type='external' title='java.util.concurrent.locks.LockSupport'>LockSupport.java:175</a>)<br/>java.util.concurrent.locks.AbstractQueuedSynchronizer$ConditionObject.await(<a href='?part=source&amp;class=java.util.concurrent.locks.AbstractQueuedSynchronizer$ConditionObject#2039' class='lightwindow' type='external' title='java.util.concurrent.locks.AbstractQueuedSynchronizer$ConditionObject'>AbstractQueuedSynchronizer.java:2039</a>)<br/>java.util.concurrent.LinkedBlockingQueue.take(<a href='?part=source&amp;class=java.util.concurrent.LinkedBlockingQueue#442' class='lightwindow' type='external' title='java.util.concurrent.LinkedBlockingQueue'>LinkedBlockingQueue.java:442</a>)<br/>org.apache.tomcat.util.threads.TaskQueue.take(<a href='?part=source&amp;class=org.apache.tomcat.util.threads.TaskQueue#146' class='lightwindow' type='external' title='org.apache.tomcat.util.threads.TaskQueue'>TaskQueue.java:146</a>)<br/>org.apache.tomcat.util.threads.TaskQueue.take(<a href='?part=source&amp;class=org.apache.tomcat.util.threads.TaskQueue#33' class='lightwindow' type='external' title='org.apache.tomcat.util.threads.TaskQueue'>TaskQueue.java:33</a>)<br/>org.apache.tomcat.util.threads.ThreadPoolExecutor.getTask(<a href='?part=source&amp;class=org.apache.tomcat.util.threads.ThreadPoolExecutor#1114' class='lightwindow' type='external' title='org.apache.tomcat.util.threads.ThreadPoolExecutor'>ThreadPoolExecutor.java:1114</a>)<br/>org.apache.tomcat.util.threads.ThreadPoolExecutor.runWorker(<a href='?part=source&amp;class=org.apache.tomcat.util.threads.ThreadPoolExecutor#1176' class='lightwindow' type='external' title='org.apache.tomcat.util.threads.ThreadPoolExecutor'>ThreadPoolExecutor.java:1176</a>)<br/>org.apache.tomcat.util.threads.ThreadPoolExecutor$Worker.run(<a href='?part=source&amp;class=org.apache.tomcat.util.threads.ThreadPoolExecutor$Worker#659' class='lightwindow' type='external' title='org.apache.tomcat.util.threads.ThreadPoolExecutor$Worker'>ThreadPoolExecutor.java:659</a>)<br/>org.apache.tomcat.util.threads.TaskThread$WrappingRunnable.run(<a href='?part=source&amp;class=org.apache.tomcat.util.threads.TaskThread$WrappingRunnable#61' class='lightwindow' type='external' title='org.apache.tomcat.util.threads.TaskThread$WrappingRunnable'>TaskThread.java:61</a>)<br/>java.lang.Thread.run(<a href='?part=source&amp;class=java.lang.Thread#748' class='lightwindow' type='external' title='java.lang.Thread'>Thread.java:748</a>)<br/></em>
http-nio-8092-exec-7</div>
</td> <td align='center'>yes</td> <td align='right'>5</td> <td><img src='?resource=bullets/yellow.png' alt='WAITING'/>WAITING</td> <td>sun.misc.Unsafe.park(Native Method)</td> <td align='right'>281</td> <td align='right'>234</td> <td align='center' class='noPrint'><a href='?action=send_thread_interrupt&amp;threadId=40272_***********_647' onclick="javascript:return confirm('Do you confirm the sending of a thread interrupt signal to http-nio-8092-exec-7?');"><img width='16' height='16' src='?resource=action_interrupt.png' alt='Send a thread interrupt signal to http-nio-8092-exec-7. The thread can test Thread.currentThread().isInterrupted() to stop itself.' title='Send a thread interrupt signal to http-nio-8092-exec-7. The thread can test Thread.currentThread().isInterrupted() to stop itself.' /></a></td> <td align='center' class='noPrint'><a href='?action=kill_thread&amp;threadId=40272_***********_647' onclick="javascript:return confirm('杀掉一个Java线程是不推荐和不安全的，你确定要杀掉线程 http-nio-8092-exec-7 ?');"><img width='16' height='16' src='?resource=stop.png' alt='杀掉线程&nbsp;http-nio-8092-exec-7' title='杀掉线程&nbsp;http-nio-8092-exec-7' /></a></td></tr><tr class='odd' onmouseover="this.className='highlight'" onmouseout="this.className='odd'">
<td><div class='tooltip'>
<em>
http-nio-8092-exec-8<br/>
sun.misc.Unsafe.park(Native Method)<br/>java.util.concurrent.locks.LockSupport.park(<a href='?part=source&amp;class=java.util.concurrent.locks.LockSupport#175' class='lightwindow' type='external' title='java.util.concurrent.locks.LockSupport'>LockSupport.java:175</a>)<br/>java.util.concurrent.locks.AbstractQueuedSynchronizer$ConditionObject.await(<a href='?part=source&amp;class=java.util.concurrent.locks.AbstractQueuedSynchronizer$ConditionObject#2039' class='lightwindow' type='external' title='java.util.concurrent.locks.AbstractQueuedSynchronizer$ConditionObject'>AbstractQueuedSynchronizer.java:2039</a>)<br/>java.util.concurrent.LinkedBlockingQueue.take(<a href='?part=source&amp;class=java.util.concurrent.LinkedBlockingQueue#442' class='lightwindow' type='external' title='java.util.concurrent.LinkedBlockingQueue'>LinkedBlockingQueue.java:442</a>)<br/>org.apache.tomcat.util.threads.TaskQueue.take(<a href='?part=source&amp;class=org.apache.tomcat.util.threads.TaskQueue#146' class='lightwindow' type='external' title='org.apache.tomcat.util.threads.TaskQueue'>TaskQueue.java:146</a>)<br/>org.apache.tomcat.util.threads.TaskQueue.take(<a href='?part=source&amp;class=org.apache.tomcat.util.threads.TaskQueue#33' class='lightwindow' type='external' title='org.apache.tomcat.util.threads.TaskQueue'>TaskQueue.java:33</a>)<br/>org.apache.tomcat.util.threads.ThreadPoolExecutor.getTask(<a href='?part=source&amp;class=org.apache.tomcat.util.threads.ThreadPoolExecutor#1114' class='lightwindow' type='external' title='org.apache.tomcat.util.threads.ThreadPoolExecutor'>ThreadPoolExecutor.java:1114</a>)<br/>org.apache.tomcat.util.threads.ThreadPoolExecutor.runWorker(<a href='?part=source&amp;class=org.apache.tomcat.util.threads.ThreadPoolExecutor#1176' class='lightwindow' type='external' title='org.apache.tomcat.util.threads.ThreadPoolExecutor'>ThreadPoolExecutor.java:1176</a>)<br/>org.apache.tomcat.util.threads.ThreadPoolExecutor$Worker.run(<a href='?part=source&amp;class=org.apache.tomcat.util.threads.ThreadPoolExecutor$Worker#659' class='lightwindow' type='external' title='org.apache.tomcat.util.threads.ThreadPoolExecutor$Worker'>ThreadPoolExecutor.java:659</a>)<br/>org.apache.tomcat.util.threads.TaskThread$WrappingRunnable.run(<a href='?part=source&amp;class=org.apache.tomcat.util.threads.TaskThread$WrappingRunnable#61' class='lightwindow' type='external' title='org.apache.tomcat.util.threads.TaskThread$WrappingRunnable'>TaskThread.java:61</a>)<br/>java.lang.Thread.run(<a href='?part=source&amp;class=java.lang.Thread#748' class='lightwindow' type='external' title='java.lang.Thread'>Thread.java:748</a>)<br/></em>
http-nio-8092-exec-8</div>
</td> <td align='center'>yes</td> <td align='right'>5</td> <td><img src='?resource=bullets/yellow.png' alt='WAITING'/>WAITING</td> <td>sun.misc.Unsafe.park(Native Method)</td> <td align='right'>421</td> <td align='right'>328</td> <td align='center' class='noPrint'><a href='?action=send_thread_interrupt&amp;threadId=40272_***********_648' onclick="javascript:return confirm('Do you confirm the sending of a thread interrupt signal to http-nio-8092-exec-8?');"><img width='16' height='16' src='?resource=action_interrupt.png' alt='Send a thread interrupt signal to http-nio-8092-exec-8. The thread can test Thread.currentThread().isInterrupted() to stop itself.' title='Send a thread interrupt signal to http-nio-8092-exec-8. The thread can test Thread.currentThread().isInterrupted() to stop itself.' /></a></td> <td align='center' class='noPrint'><a href='?action=kill_thread&amp;threadId=40272_***********_648' onclick="javascript:return confirm('杀掉一个Java线程是不推荐和不安全的，你确定要杀掉线程 http-nio-8092-exec-8 ?');"><img width='16' height='16' src='?resource=stop.png' alt='杀掉线程&nbsp;http-nio-8092-exec-8' title='杀掉线程&nbsp;http-nio-8092-exec-8' /></a></td></tr><tr onmouseover="this.className='highlight'" onmouseout="this.className=''">
<td><div class='tooltip'>
<em>
http-nio-8092-exec-9<br/>
sun.misc.Unsafe.park(Native Method)<br/>java.util.concurrent.locks.LockSupport.park(<a href='?part=source&amp;class=java.util.concurrent.locks.LockSupport#175' class='lightwindow' type='external' title='java.util.concurrent.locks.LockSupport'>LockSupport.java:175</a>)<br/>java.util.concurrent.locks.AbstractQueuedSynchronizer$ConditionObject.await(<a href='?part=source&amp;class=java.util.concurrent.locks.AbstractQueuedSynchronizer$ConditionObject#2039' class='lightwindow' type='external' title='java.util.concurrent.locks.AbstractQueuedSynchronizer$ConditionObject'>AbstractQueuedSynchronizer.java:2039</a>)<br/>java.util.concurrent.LinkedBlockingQueue.take(<a href='?part=source&amp;class=java.util.concurrent.LinkedBlockingQueue#442' class='lightwindow' type='external' title='java.util.concurrent.LinkedBlockingQueue'>LinkedBlockingQueue.java:442</a>)<br/>org.apache.tomcat.util.threads.TaskQueue.take(<a href='?part=source&amp;class=org.apache.tomcat.util.threads.TaskQueue#146' class='lightwindow' type='external' title='org.apache.tomcat.util.threads.TaskQueue'>TaskQueue.java:146</a>)<br/>org.apache.tomcat.util.threads.TaskQueue.take(<a href='?part=source&amp;class=org.apache.tomcat.util.threads.TaskQueue#33' class='lightwindow' type='external' title='org.apache.tomcat.util.threads.TaskQueue'>TaskQueue.java:33</a>)<br/>org.apache.tomcat.util.threads.ThreadPoolExecutor.getTask(<a href='?part=source&amp;class=org.apache.tomcat.util.threads.ThreadPoolExecutor#1114' class='lightwindow' type='external' title='org.apache.tomcat.util.threads.ThreadPoolExecutor'>ThreadPoolExecutor.java:1114</a>)<br/>org.apache.tomcat.util.threads.ThreadPoolExecutor.runWorker(<a href='?part=source&amp;class=org.apache.tomcat.util.threads.ThreadPoolExecutor#1176' class='lightwindow' type='external' title='org.apache.tomcat.util.threads.ThreadPoolExecutor'>ThreadPoolExecutor.java:1176</a>)<br/>org.apache.tomcat.util.threads.ThreadPoolExecutor$Worker.run(<a href='?part=source&amp;class=org.apache.tomcat.util.threads.ThreadPoolExecutor$Worker#659' class='lightwindow' type='external' title='org.apache.tomcat.util.threads.ThreadPoolExecutor$Worker'>ThreadPoolExecutor.java:659</a>)<br/>org.apache.tomcat.util.threads.TaskThread$WrappingRunnable.run(<a href='?part=source&amp;class=org.apache.tomcat.util.threads.TaskThread$WrappingRunnable#61' class='lightwindow' type='external' title='org.apache.tomcat.util.threads.TaskThread$WrappingRunnable'>TaskThread.java:61</a>)<br/>java.lang.Thread.run(<a href='?part=source&amp;class=java.lang.Thread#748' class='lightwindow' type='external' title='java.lang.Thread'>Thread.java:748</a>)<br/></em>
http-nio-8092-exec-9</div>
</td> <td align='center'>yes</td> <td align='right'>5</td> <td><img src='?resource=bullets/yellow.png' alt='WAITING'/>WAITING</td> <td>sun.misc.Unsafe.park(Native Method)</td> <td align='right'>62</td> <td align='right'>62</td> <td align='center' class='noPrint'><a href='?action=send_thread_interrupt&amp;threadId=40272_***********_649' onclick="javascript:return confirm('Do you confirm the sending of a thread interrupt signal to http-nio-8092-exec-9?');"><img width='16' height='16' src='?resource=action_interrupt.png' alt='Send a thread interrupt signal to http-nio-8092-exec-9. The thread can test Thread.currentThread().isInterrupted() to stop itself.' title='Send a thread interrupt signal to http-nio-8092-exec-9. The thread can test Thread.currentThread().isInterrupted() to stop itself.' /></a></td> <td align='center' class='noPrint'><a href='?action=kill_thread&amp;threadId=40272_***********_649' onclick="javascript:return confirm('杀掉一个Java线程是不推荐和不安全的，你确定要杀掉线程 http-nio-8092-exec-9 ?');"><img width='16' height='16' src='?resource=stop.png' alt='杀掉线程&nbsp;http-nio-8092-exec-9' title='杀掉线程&nbsp;http-nio-8092-exec-9' /></a></td></tr><tr class='odd' onmouseover="this.className='highlight'" onmouseout="this.className='odd'">
<td><div class='tooltip'>
<em>
http-nio-8092-Poller<br/>
sun.nio.ch.WindowsSelectorImpl$SubSelector.poll0(Native Method)<br/>sun.nio.ch.WindowsSelectorImpl$SubSelector.poll(<a href='?part=source&amp;class=sun.nio.ch.WindowsSelectorImpl$SubSelector#296' class='lightwindow' type='external' title='sun.nio.ch.WindowsSelectorImpl$SubSelector'>WindowsSelectorImpl.java:296</a>)<br/>sun.nio.ch.WindowsSelectorImpl$SubSelector.access$400(<a href='?part=source&amp;class=sun.nio.ch.WindowsSelectorImpl$SubSelector#278' class='lightwindow' type='external' title='sun.nio.ch.WindowsSelectorImpl$SubSelector'>WindowsSelectorImpl.java:278</a>)<br/>sun.nio.ch.WindowsSelectorImpl.doSelect(<a href='?part=source&amp;class=sun.nio.ch.WindowsSelectorImpl#159' class='lightwindow' type='external' title='sun.nio.ch.WindowsSelectorImpl'>WindowsSelectorImpl.java:159</a>)<br/>sun.nio.ch.SelectorImpl.lockAndDoSelect(<a href='?part=source&amp;class=sun.nio.ch.SelectorImpl#86' class='lightwindow' type='external' title='sun.nio.ch.SelectorImpl'>SelectorImpl.java:86</a>)<br/>sun.nio.ch.SelectorImpl.select(<a href='?part=source&amp;class=sun.nio.ch.SelectorImpl#97' class='lightwindow' type='external' title='sun.nio.ch.SelectorImpl'>SelectorImpl.java:97</a>)<br/>org.apache.tomcat.util.net.NioEndpoint$Poller.run(<a href='?part=source&amp;class=org.apache.tomcat.util.net.NioEndpoint$Poller#805' class='lightwindow' type='external' title='org.apache.tomcat.util.net.NioEndpoint$Poller'>NioEndpoint.java:805</a>)<br/>java.lang.Thread.run(<a href='?part=source&amp;class=java.lang.Thread#748' class='lightwindow' type='external' title='java.lang.Thread'>Thread.java:748</a>)<br/></em>
http-nio-8092-Poller</div>
</td> <td align='center'>yes</td> <td align='right'>5</td> <td><img src='?resource=bullets/green.png' alt='RUNNABLE'/>RUNNABLE</td> <td>sun.nio.ch.WindowsSelectorImpl$SubSelector.poll0(Native Method)</td> <td align='right'>171</td> <td align='right'>93</td> <td align='center' class='noPrint'><a href='?action=send_thread_interrupt&amp;threadId=40272_***********_661' onclick="javascript:return confirm('Do you confirm the sending of a thread interrupt signal to http-nio-8092-Poller?');"><img width='16' height='16' src='?resource=action_interrupt.png' alt='Send a thread interrupt signal to http-nio-8092-Poller. The thread can test Thread.currentThread().isInterrupted() to stop itself.' title='Send a thread interrupt signal to http-nio-8092-Poller. The thread can test Thread.currentThread().isInterrupted() to stop itself.' /></a></td> <td align='center' class='noPrint'><a href='?action=kill_thread&amp;threadId=40272_***********_661' onclick="javascript:return confirm('杀掉一个Java线程是不推荐和不安全的，你确定要杀掉线程 http-nio-8092-Poller ?');"><img width='16' height='16' src='?resource=stop.png' alt='杀掉线程&nbsp;http-nio-8092-Poller' title='杀掉线程&nbsp;http-nio-8092-Poller' /></a></td></tr><tr onmouseover="this.className='highlight'" onmouseout="this.className=''">
<td><div class='tooltip'>
<em>
Java2D&nbsp;Disposer<br/>
java.lang.Object.wait(Native Method)<br/>java.lang.ref.ReferenceQueue.remove(<a href='?part=source&amp;class=java.lang.ref.ReferenceQueue#144' class='lightwindow' type='external' title='java.lang.ref.ReferenceQueue'>ReferenceQueue.java:144</a>)<br/>java.lang.ref.ReferenceQueue.remove(<a href='?part=source&amp;class=java.lang.ref.ReferenceQueue#165' class='lightwindow' type='external' title='java.lang.ref.ReferenceQueue'>ReferenceQueue.java:165</a>)<br/>sun.java2d.Disposer.run(<a href='?part=source&amp;class=sun.java2d.Disposer#148' class='lightwindow' type='external' title='sun.java2d.Disposer'>Disposer.java:148</a>)<br/>java.lang.Thread.run(<a href='?part=source&amp;class=java.lang.Thread#748' class='lightwindow' type='external' title='java.lang.Thread'>Thread.java:748</a>)<br/></em>
Java2D&nbsp;Disposer</div>
</td> <td align='center'>yes</td> <td align='right'>10</td> <td><img src='?resource=bullets/yellow.png' alt='WAITING'/>WAITING</td> <td>java.lang.Object.wait(Native Method)</td> <td align='right'>0</td> <td align='right'>0</td> <td align='center' class='noPrint'><a href='?action=send_thread_interrupt&amp;threadId=40272_***********_712' onclick="javascript:return confirm('Do you confirm the sending of a thread interrupt signal to Java2D Disposer?');"><img width='16' height='16' src='?resource=action_interrupt.png' alt='Send a thread interrupt signal to Java2D Disposer. The thread can test Thread.currentThread().isInterrupted() to stop itself.' title='Send a thread interrupt signal to Java2D Disposer. The thread can test Thread.currentThread().isInterrupted() to stop itself.' /></a></td> <td align='center' class='noPrint'><a href='?action=kill_thread&amp;threadId=40272_***********_712' onclick="javascript:return confirm('杀掉一个Java线程是不推荐和不安全的，你确定要杀掉线程 Java2D Disposer ?');"><img width='16' height='16' src='?resource=stop.png' alt='杀掉线程&nbsp;Java2D&nbsp;Disposer' title='杀掉线程&nbsp;Java2D&nbsp;Disposer' /></a></td></tr><tr class='odd' onmouseover="this.className='highlight'" onmouseout="this.className='odd'">
<td><div class='tooltip'>
<em>
javamelody&nbsp;hnjjwz<br/>
java.lang.Object.wait(Native Method)<br/>java.util.TimerThread.mainLoop(<a href='?part=source&amp;class=java.util.TimerThread#552' class='lightwindow' type='external' title='java.util.TimerThread'>Timer.java:552</a>)<br/>java.util.TimerThread.run(<a href='?part=source&amp;class=java.util.TimerThread#505' class='lightwindow' type='external' title='java.util.TimerThread'>Timer.java:505</a>)<br/></em>
javamelody&nbsp;hnjjwz</div>
</td> <td align='center'>yes</td> <td align='right'>5</td> <td><img src='?resource=bullets/yellow.png' alt='TIMED_WAITING'/>TIMED_WAITING</td> <td>java.lang.Object.wait(Native Method)</td> <td align='right'>421</td> <td align='right'>250</td> <td align='center' class='noPrint'><a href='?action=send_thread_interrupt&amp;threadId=40272_***********_81' onclick="javascript:return confirm('Do you confirm the sending of a thread interrupt signal to javamelody hnjjwz?');"><img width='16' height='16' src='?resource=action_interrupt.png' alt='Send a thread interrupt signal to javamelody hnjjwz. The thread can test Thread.currentThread().isInterrupted() to stop itself.' title='Send a thread interrupt signal to javamelody hnjjwz. The thread can test Thread.currentThread().isInterrupted() to stop itself.' /></a></td> <td align='center' class='noPrint'><a href='?action=kill_thread&amp;threadId=40272_***********_81' onclick="javascript:return confirm('杀掉一个Java线程是不推荐和不安全的，你确定要杀掉线程 javamelody hnjjwz ?');"><img width='16' height='16' src='?resource=stop.png' alt='杀掉线程&nbsp;javamelody&nbsp;hnjjwz' title='杀掉线程&nbsp;javamelody&nbsp;hnjjwz' /></a></td></tr><tr onmouseover="this.className='highlight'" onmouseout="this.className=''">
<td><div class='tooltip'>
<em>
Live&nbsp;Reload&nbsp;Server<br/>
java.net.DualStackPlainSocketImpl.accept0(Native Method)<br/>java.net.DualStackPlainSocketImpl.socketAccept(<a href='?part=source&amp;class=java.net.DualStackPlainSocketImpl#131' class='lightwindow' type='external' title='java.net.DualStackPlainSocketImpl'>DualStackPlainSocketImpl.java:131</a>)<br/>java.net.AbstractPlainSocketImpl.accept(<a href='?part=source&amp;class=java.net.AbstractPlainSocketImpl#409' class='lightwindow' type='external' title='java.net.AbstractPlainSocketImpl'>AbstractPlainSocketImpl.java:409</a>)<br/>java.net.PlainSocketImpl.accept(<a href='?part=source&amp;class=java.net.PlainSocketImpl#199' class='lightwindow' type='external' title='java.net.PlainSocketImpl'>PlainSocketImpl.java:199</a>)<br/>java.net.ServerSocket.implAccept(<a href='?part=source&amp;class=java.net.ServerSocket#545' class='lightwindow' type='external' title='java.net.ServerSocket'>ServerSocket.java:545</a>)<br/>java.net.ServerSocket.accept(<a href='?part=source&amp;class=java.net.ServerSocket#513' class='lightwindow' type='external' title='java.net.ServerSocket'>ServerSocket.java:513</a>)<br/>org.springframework.boot.devtools.livereload.LiveReloadServer.acceptConnections(<a href='?part=source&amp;class=org.springframework.boot.devtools.livereload.LiveReloadServer#145' class='lightwindow' type='external' title='org.springframework.boot.devtools.livereload.LiveReloadServer'>LiveReloadServer.java:145</a>)<br/>org.springframework.boot.devtools.livereload.LiveReloadServer$$Lambda$1501/749956955.run(Unknown Source)<br/>java.lang.Thread.run(<a href='?part=source&amp;class=java.lang.Thread#748' class='lightwindow' type='external' title='java.lang.Thread'>Thread.java:748</a>)<br/></em>
Live&nbsp;Reload&nbsp;Server</div>
</td> <td align='center'>yes</td> <td align='right'>5</td> <td><img src='?resource=bullets/green.png' alt='RUNNABLE'/>RUNNABLE</td> <td>java.net.DualStackPlainSocketImpl.accept0(Native Method)</td> <td align='right'>0</td> <td align='right'>0</td> <td align='center' class='noPrint'><a href='?action=send_thread_interrupt&amp;threadId=40272_***********_603' onclick="javascript:return confirm('Do you confirm the sending of a thread interrupt signal to Live Reload Server?');"><img width='16' height='16' src='?resource=action_interrupt.png' alt='Send a thread interrupt signal to Live Reload Server. The thread can test Thread.currentThread().isInterrupted() to stop itself.' title='Send a thread interrupt signal to Live Reload Server. The thread can test Thread.currentThread().isInterrupted() to stop itself.' /></a></td> <td align='center' class='noPrint'><a href='?action=kill_thread&amp;threadId=40272_***********_603' onclick="javascript:return confirm('杀掉一个Java线程是不推荐和不安全的，你确定要杀掉线程 Live Reload Server ?');"><img width='16' height='16' src='?resource=stop.png' alt='杀掉线程&nbsp;Live&nbsp;Reload&nbsp;Server' title='杀掉线程&nbsp;Live&nbsp;Reload&nbsp;Server' /></a></td></tr><tr class='odd' onmouseover="this.className='highlight'" onmouseout="this.className='odd'">
<td><div class='tooltip'>
<em>
Monitor&nbsp;Ctrl-Break<br/>
java.net.SocketInputStream.socketRead0(Native Method)<br/>java.net.SocketInputStream.socketRead(<a href='?part=source&amp;class=java.net.SocketInputStream#116' class='lightwindow' type='external' title='java.net.SocketInputStream'>SocketInputStream.java:116</a>)<br/>java.net.SocketInputStream.read(<a href='?part=source&amp;class=java.net.SocketInputStream#171' class='lightwindow' type='external' title='java.net.SocketInputStream'>SocketInputStream.java:171</a>)<br/>java.net.SocketInputStream.read(<a href='?part=source&amp;class=java.net.SocketInputStream#141' class='lightwindow' type='external' title='java.net.SocketInputStream'>SocketInputStream.java:141</a>)<br/>sun.nio.cs.StreamDecoder.readBytes(<a href='?part=source&amp;class=sun.nio.cs.StreamDecoder#284' class='lightwindow' type='external' title='sun.nio.cs.StreamDecoder'>StreamDecoder.java:284</a>)<br/>sun.nio.cs.StreamDecoder.implRead(<a href='?part=source&amp;class=sun.nio.cs.StreamDecoder#326' class='lightwindow' type='external' title='sun.nio.cs.StreamDecoder'>StreamDecoder.java:326</a>)<br/>sun.nio.cs.StreamDecoder.read(<a href='?part=source&amp;class=sun.nio.cs.StreamDecoder#178' class='lightwindow' type='external' title='sun.nio.cs.StreamDecoder'>StreamDecoder.java:178</a>)<br/>java.io.InputStreamReader.read(<a href='?part=source&amp;class=java.io.InputStreamReader#184' class='lightwindow' type='external' title='java.io.InputStreamReader'>InputStreamReader.java:184</a>)<br/>java.io.BufferedReader.fill(<a href='?part=source&amp;class=java.io.BufferedReader#161' class='lightwindow' type='external' title='java.io.BufferedReader'>BufferedReader.java:161</a>)<br/>java.io.BufferedReader.readLine(<a href='?part=source&amp;class=java.io.BufferedReader#324' class='lightwindow' type='external' title='java.io.BufferedReader'>BufferedReader.java:324</a>)<br/>java.io.BufferedReader.readLine(<a href='?part=source&amp;class=java.io.BufferedReader#389' class='lightwindow' type='external' title='java.io.BufferedReader'>BufferedReader.java:389</a>)<br/>com.intellij.rt.execution.application.AppMainV2$1.run(<a href='?part=source&amp;class=com.intellij.rt.execution.application.AppMainV2$1#49' class='lightwindow' type='external' title='com.intellij.rt.execution.application.AppMainV2$1'>AppMainV2.java:49</a>)<br/></em>
Monitor&nbsp;Ctrl-Break</div>
</td> <td align='center'>yes</td> <td align='right'>5</td> <td><img src='?resource=bullets/green.png' alt='RUNNABLE'/>RUNNABLE</td> <td>java.net.SocketInputStream.socketRead0(Native Method)</td> <td align='right'>15</td> <td align='right'>0</td> <td align='center' class='noPrint'><a href='?action=send_thread_interrupt&amp;threadId=40272_***********_6' onclick="javascript:return confirm('Do you confirm the sending of a thread interrupt signal to Monitor Ctrl-Break?');"><img width='16' height='16' src='?resource=action_interrupt.png' alt='Send a thread interrupt signal to Monitor Ctrl-Break. The thread can test Thread.currentThread().isInterrupted() to stop itself.' title='Send a thread interrupt signal to Monitor Ctrl-Break. The thread can test Thread.currentThread().isInterrupted() to stop itself.' /></a></td> <td align='center' class='noPrint'><a href='?action=kill_thread&amp;threadId=40272_***********_6' onclick="javascript:return confirm('杀掉一个Java线程是不推荐和不安全的，你确定要杀掉线程 Monitor Ctrl-Break ?');"><img width='16' height='16' src='?resource=stop.png' alt='杀掉线程&nbsp;Monitor&nbsp;Ctrl-Break' title='杀掉线程&nbsp;Monitor&nbsp;Ctrl-Break' /></a></td></tr><tr onmouseover="this.className='highlight'" onmouseout="this.className=''">
<td><div class='tooltip'>
<em>
nacos-grpc-client-executor-*************-121<br/>
sun.misc.Unsafe.park(Native Method)<br/>java.util.concurrent.locks.LockSupport.parkNanos(<a href='?part=source&amp;class=java.util.concurrent.locks.LockSupport#215' class='lightwindow' type='external' title='java.util.concurrent.locks.LockSupport'>LockSupport.java:215</a>)<br/>java.util.concurrent.locks.AbstractQueuedSynchronizer$ConditionObject.awaitNanos(<a href='?part=source&amp;class=java.util.concurrent.locks.AbstractQueuedSynchronizer$ConditionObject#2078' class='lightwindow' type='external' title='java.util.concurrent.locks.AbstractQueuedSynchronizer$ConditionObject'>AbstractQueuedSynchronizer.java:2078</a>)<br/>java.util.concurrent.LinkedBlockingQueue.poll(<a href='?part=source&amp;class=java.util.concurrent.LinkedBlockingQueue#467' class='lightwindow' type='external' title='java.util.concurrent.LinkedBlockingQueue'>LinkedBlockingQueue.java:467</a>)<br/>java.util.concurrent.ThreadPoolExecutor.getTask(<a href='?part=source&amp;class=java.util.concurrent.ThreadPoolExecutor#1073' class='lightwindow' type='external' title='java.util.concurrent.ThreadPoolExecutor'>ThreadPoolExecutor.java:1073</a>)<br/>java.util.concurrent.ThreadPoolExecutor.runWorker(<a href='?part=source&amp;class=java.util.concurrent.ThreadPoolExecutor#1134' class='lightwindow' type='external' title='java.util.concurrent.ThreadPoolExecutor'>ThreadPoolExecutor.java:1134</a>)<br/>java.util.concurrent.ThreadPoolExecutor$Worker.run(<a href='?part=source&amp;class=java.util.concurrent.ThreadPoolExecutor$Worker#624' class='lightwindow' type='external' title='java.util.concurrent.ThreadPoolExecutor$Worker'>ThreadPoolExecutor.java:624</a>)<br/>java.lang.Thread.run(<a href='?part=source&amp;class=java.lang.Thread#748' class='lightwindow' type='external' title='java.lang.Thread'>Thread.java:748</a>)<br/></em>
nacos-grpc-client-executor-*************-121</div>
</td> <td align='center'>yes</td> <td align='right'>5</td> <td><img src='?resource=bullets/yellow.png' alt='TIMED_WAITING'/>TIMED_WAITING</td> <td>sun.misc.Unsafe.park(Native Method)</td> <td align='right'>0</td> <td align='right'>0</td> <td align='center' class='noPrint'><a href='?action=send_thread_interrupt&amp;threadId=40272_***********_946' onclick="javascript:return confirm('Do you confirm the sending of a thread interrupt signal to nacos-grpc-client-executor-*************-121?');"><img width='16' height='16' src='?resource=action_interrupt.png' alt='Send a thread interrupt signal to nacos-grpc-client-executor-*************-121. The thread can test Thread.currentThread().isInterrupted() to stop itself.' title='Send a thread interrupt signal to nacos-grpc-client-executor-*************-121. The thread can test Thread.currentThread().isInterrupted() to stop itself.' /></a></td> <td align='center' class='noPrint'><a href='?action=kill_thread&amp;threadId=40272_***********_946' onclick="javascript:return confirm('杀掉一个Java线程是不推荐和不安全的，你确定要杀掉线程 nacos-grpc-client-executor-*************-121 ?');"><img width='16' height='16' src='?resource=stop.png' alt='杀掉线程&nbsp;nacos-grpc-client-executor-*************-121' title='杀掉线程&nbsp;nacos-grpc-client-executor-*************-121' /></a></td></tr><tr class='odd' onmouseover="this.className='highlight'" onmouseout="this.className='odd'">
<td><div class='tooltip'>
<em>
nacos-grpc-client-executor-*************-122<br/>
sun.misc.Unsafe.park(Native Method)<br/>java.util.concurrent.locks.LockSupport.parkNanos(<a href='?part=source&amp;class=java.util.concurrent.locks.LockSupport#215' class='lightwindow' type='external' title='java.util.concurrent.locks.LockSupport'>LockSupport.java:215</a>)<br/>java.util.concurrent.locks.AbstractQueuedSynchronizer$ConditionObject.awaitNanos(<a href='?part=source&amp;class=java.util.concurrent.locks.AbstractQueuedSynchronizer$ConditionObject#2078' class='lightwindow' type='external' title='java.util.concurrent.locks.AbstractQueuedSynchronizer$ConditionObject'>AbstractQueuedSynchronizer.java:2078</a>)<br/>java.util.concurrent.LinkedBlockingQueue.poll(<a href='?part=source&amp;class=java.util.concurrent.LinkedBlockingQueue#467' class='lightwindow' type='external' title='java.util.concurrent.LinkedBlockingQueue'>LinkedBlockingQueue.java:467</a>)<br/>java.util.concurrent.ThreadPoolExecutor.getTask(<a href='?part=source&amp;class=java.util.concurrent.ThreadPoolExecutor#1073' class='lightwindow' type='external' title='java.util.concurrent.ThreadPoolExecutor'>ThreadPoolExecutor.java:1073</a>)<br/>java.util.concurrent.ThreadPoolExecutor.runWorker(<a href='?part=source&amp;class=java.util.concurrent.ThreadPoolExecutor#1134' class='lightwindow' type='external' title='java.util.concurrent.ThreadPoolExecutor'>ThreadPoolExecutor.java:1134</a>)<br/>java.util.concurrent.ThreadPoolExecutor$Worker.run(<a href='?part=source&amp;class=java.util.concurrent.ThreadPoolExecutor$Worker#624' class='lightwindow' type='external' title='java.util.concurrent.ThreadPoolExecutor$Worker'>ThreadPoolExecutor.java:624</a>)<br/>java.lang.Thread.run(<a href='?part=source&amp;class=java.lang.Thread#748' class='lightwindow' type='external' title='java.lang.Thread'>Thread.java:748</a>)<br/></em>
nacos-grpc-client-executor-*************-122</div>
</td> <td align='center'>yes</td> <td align='right'>5</td> <td><img src='?resource=bullets/yellow.png' alt='TIMED_WAITING'/>TIMED_WAITING</td> <td>sun.misc.Unsafe.park(Native Method)</td> <td align='right'>0</td> <td align='right'>0</td> <td align='center' class='noPrint'><a href='?action=send_thread_interrupt&amp;threadId=40272_***********_947' onclick="javascript:return confirm('Do you confirm the sending of a thread interrupt signal to nacos-grpc-client-executor-*************-122?');"><img width='16' height='16' src='?resource=action_interrupt.png' alt='Send a thread interrupt signal to nacos-grpc-client-executor-*************-122. The thread can test Thread.currentThread().isInterrupted() to stop itself.' title='Send a thread interrupt signal to nacos-grpc-client-executor-*************-122. The thread can test Thread.currentThread().isInterrupted() to stop itself.' /></a></td> <td align='center' class='noPrint'><a href='?action=kill_thread&amp;threadId=40272_***********_947' onclick="javascript:return confirm('杀掉一个Java线程是不推荐和不安全的，你确定要杀掉线程 nacos-grpc-client-executor-*************-122 ?');"><img width='16' height='16' src='?resource=stop.png' alt='杀掉线程&nbsp;nacos-grpc-client-executor-*************-122' title='杀掉线程&nbsp;nacos-grpc-client-executor-*************-122' /></a></td></tr><tr onmouseover="this.className='highlight'" onmouseout="this.className=''">
<td><div class='tooltip'>
<em>
nacos.publisher-com.alibaba.nacos.client.naming.event.InstancesChangeEvent<br/>
sun.misc.Unsafe.park(Native Method)<br/>java.util.concurrent.locks.LockSupport.park(<a href='?part=source&amp;class=java.util.concurrent.locks.LockSupport#175' class='lightwindow' type='external' title='java.util.concurrent.locks.LockSupport'>LockSupport.java:175</a>)<br/>java.util.concurrent.locks.AbstractQueuedSynchronizer$ConditionObject.await(<a href='?part=source&amp;class=java.util.concurrent.locks.AbstractQueuedSynchronizer$ConditionObject#2039' class='lightwindow' type='external' title='java.util.concurrent.locks.AbstractQueuedSynchronizer$ConditionObject'>AbstractQueuedSynchronizer.java:2039</a>)<br/>java.util.concurrent.ArrayBlockingQueue.take(<a href='?part=source&amp;class=java.util.concurrent.ArrayBlockingQueue#403' class='lightwindow' type='external' title='java.util.concurrent.ArrayBlockingQueue'>ArrayBlockingQueue.java:403</a>)<br/>com.alibaba.nacos.common.notify.DefaultPublisher.openEventHandler(<a href='?part=source&amp;class=com.alibaba.nacos.common.notify.DefaultPublisher#117' class='lightwindow' type='external' title='com.alibaba.nacos.common.notify.DefaultPublisher'>DefaultPublisher.java:117</a>)<br/>com.alibaba.nacos.common.notify.DefaultPublisher.run(<a href='?part=source&amp;class=com.alibaba.nacos.common.notify.DefaultPublisher#95' class='lightwindow' type='external' title='com.alibaba.nacos.common.notify.DefaultPublisher'>DefaultPublisher.java:95</a>)<br/></em>
nacos.publisher-com.alibaba.nacos.client.naming.event.InstancesChangeEvent</div>
</td> <td align='center'>yes</td> <td align='right'>5</td> <td><img src='?resource=bullets/yellow.png' alt='WAITING'/>WAITING</td> <td>sun.misc.Unsafe.park(Native Method)</td> <td align='right'>0</td> <td align='right'>0</td> <td align='center' class='noPrint'><a href='?action=send_thread_interrupt&amp;threadId=40272_***********_604' onclick="javascript:return confirm('Do you confirm the sending of a thread interrupt signal to nacos.publisher-com.alibaba.nacos.client.naming.event.InstancesChangeEvent?');"><img width='16' height='16' src='?resource=action_interrupt.png' alt='Send a thread interrupt signal to nacos.publisher-com.alibaba.nacos.client.naming.event.InstancesChangeEvent. The thread can test Thread.currentThread().isInterrupted() to stop itself.' title='Send a thread interrupt signal to nacos.publisher-com.alibaba.nacos.client.naming.event.InstancesChangeEvent. The thread can test Thread.currentThread().isInterrupted() to stop itself.' /></a></td> <td align='center' class='noPrint'><a href='?action=kill_thread&amp;threadId=40272_***********_604' onclick="javascript:return confirm('杀掉一个Java线程是不推荐和不安全的，你确定要杀掉线程 nacos.publisher-com.alibaba.nacos.client.naming.event.InstancesChangeEvent ?');"><img width='16' height='16' src='?resource=stop.png' alt='杀掉线程&nbsp;nacos.publisher-com.alibaba.nacos.client.naming.event.InstancesChangeEvent' title='杀掉线程&nbsp;nacos.publisher-com.alibaba.nacos.client.naming.event.InstancesChangeEvent' /></a></td></tr><tr class='odd' onmouseover="this.className='highlight'" onmouseout="this.className='odd'">
<td><div class='tooltip'>
<em>
nacos.publisher-com.alibaba.nacos.common.notify.SlowEvent<br/>
sun.misc.Unsafe.park(Native Method)<br/>java.util.concurrent.locks.LockSupport.park(<a href='?part=source&amp;class=java.util.concurrent.locks.LockSupport#175' class='lightwindow' type='external' title='java.util.concurrent.locks.LockSupport'>LockSupport.java:175</a>)<br/>java.util.concurrent.locks.AbstractQueuedSynchronizer$ConditionObject.await(<a href='?part=source&amp;class=java.util.concurrent.locks.AbstractQueuedSynchronizer$ConditionObject#2039' class='lightwindow' type='external' title='java.util.concurrent.locks.AbstractQueuedSynchronizer$ConditionObject'>AbstractQueuedSynchronizer.java:2039</a>)<br/>java.util.concurrent.ArrayBlockingQueue.take(<a href='?part=source&amp;class=java.util.concurrent.ArrayBlockingQueue#403' class='lightwindow' type='external' title='java.util.concurrent.ArrayBlockingQueue'>ArrayBlockingQueue.java:403</a>)<br/>com.alibaba.nacos.common.notify.DefaultPublisher.openEventHandler(<a href='?part=source&amp;class=com.alibaba.nacos.common.notify.DefaultPublisher#117' class='lightwindow' type='external' title='com.alibaba.nacos.common.notify.DefaultPublisher'>DefaultPublisher.java:117</a>)<br/>com.alibaba.nacos.common.notify.DefaultPublisher.run(<a href='?part=source&amp;class=com.alibaba.nacos.common.notify.DefaultPublisher#95' class='lightwindow' type='external' title='com.alibaba.nacos.common.notify.DefaultPublisher'>DefaultPublisher.java:95</a>)<br/></em>
nacos.publisher-com.alibaba.nacos.common.notify.SlowEvent</div>
</td> <td align='center'>yes</td> <td align='right'>5</td> <td><img src='?resource=bullets/yellow.png' alt='WAITING'/>WAITING</td> <td>sun.misc.Unsafe.park(Native Method)</td> <td align='right'>0</td> <td align='right'>0</td> <td align='center' class='noPrint'><a href='?action=send_thread_interrupt&amp;threadId=40272_***********_26' onclick="javascript:return confirm('Do you confirm the sending of a thread interrupt signal to nacos.publisher-com.alibaba.nacos.common.notify.SlowEvent?');"><img width='16' height='16' src='?resource=action_interrupt.png' alt='Send a thread interrupt signal to nacos.publisher-com.alibaba.nacos.common.notify.SlowEvent. The thread can test Thread.currentThread().isInterrupted() to stop itself.' title='Send a thread interrupt signal to nacos.publisher-com.alibaba.nacos.common.notify.SlowEvent. The thread can test Thread.currentThread().isInterrupted() to stop itself.' /></a></td> <td align='center' class='noPrint'><a href='?action=kill_thread&amp;threadId=40272_***********_26' onclick="javascript:return confirm('杀掉一个Java线程是不推荐和不安全的，你确定要杀掉线程 nacos.publisher-com.alibaba.nacos.common.notify.SlowEvent ?');"><img width='16' height='16' src='?resource=stop.png' alt='杀掉线程&nbsp;nacos.publisher-com.alibaba.nacos.common.notify.SlowEvent' title='杀掉线程&nbsp;nacos.publisher-com.alibaba.nacos.common.notify.SlowEvent' /></a></td></tr><tr onmouseover="this.className='highlight'" onmouseout="this.className=''">
<td><div class='tooltip'>
<em>
pool-14-thread-1<br/>
sun.misc.Unsafe.park(Native Method)<br/>java.util.concurrent.locks.LockSupport.park(<a href='?part=source&amp;class=java.util.concurrent.locks.LockSupport#175' class='lightwindow' type='external' title='java.util.concurrent.locks.LockSupport'>LockSupport.java:175</a>)<br/>java.util.concurrent.locks.AbstractQueuedSynchronizer$ConditionObject.await(<a href='?part=source&amp;class=java.util.concurrent.locks.AbstractQueuedSynchronizer$ConditionObject#2039' class='lightwindow' type='external' title='java.util.concurrent.locks.AbstractQueuedSynchronizer$ConditionObject'>AbstractQueuedSynchronizer.java:2039</a>)<br/>java.util.concurrent.ScheduledThreadPoolExecutor$DelayedWorkQueue.take(<a href='?part=source&amp;class=java.util.concurrent.ScheduledThreadPoolExecutor$DelayedWorkQueue#1088' class='lightwindow' type='external' title='java.util.concurrent.ScheduledThreadPoolExecutor$DelayedWorkQueue'>ScheduledThreadPoolExecutor.java:1088</a>)<br/>java.util.concurrent.ScheduledThreadPoolExecutor$DelayedWorkQueue.take(<a href='?part=source&amp;class=java.util.concurrent.ScheduledThreadPoolExecutor$DelayedWorkQueue#809' class='lightwindow' type='external' title='java.util.concurrent.ScheduledThreadPoolExecutor$DelayedWorkQueue'>ScheduledThreadPoolExecutor.java:809</a>)<br/>java.util.concurrent.ThreadPoolExecutor.getTask(<a href='?part=source&amp;class=java.util.concurrent.ThreadPoolExecutor#1074' class='lightwindow' type='external' title='java.util.concurrent.ThreadPoolExecutor'>ThreadPoolExecutor.java:1074</a>)<br/>java.util.concurrent.ThreadPoolExecutor.runWorker(<a href='?part=source&amp;class=java.util.concurrent.ThreadPoolExecutor#1134' class='lightwindow' type='external' title='java.util.concurrent.ThreadPoolExecutor'>ThreadPoolExecutor.java:1134</a>)<br/>java.util.concurrent.ThreadPoolExecutor$Worker.run(<a href='?part=source&amp;class=java.util.concurrent.ThreadPoolExecutor$Worker#624' class='lightwindow' type='external' title='java.util.concurrent.ThreadPoolExecutor$Worker'>ThreadPoolExecutor.java:624</a>)<br/>java.lang.Thread.run(<a href='?part=source&amp;class=java.lang.Thread#748' class='lightwindow' type='external' title='java.lang.Thread'>Thread.java:748</a>)<br/></em>
pool-14-thread-1</div>
</td> <td align='center'>no</td> <td align='right'>5</td> <td><img src='?resource=bullets/yellow.png' alt='WAITING'/>WAITING</td> <td>sun.misc.Unsafe.park(Native Method)</td> <td align='right'>31</td> <td align='right'>31</td> <td align='center' class='noPrint'><a href='?action=send_thread_interrupt&amp;threadId=40272_***********_631' onclick="javascript:return confirm('Do you confirm the sending of a thread interrupt signal to pool-14-thread-1?');"><img width='16' height='16' src='?resource=action_interrupt.png' alt='Send a thread interrupt signal to pool-14-thread-1. The thread can test Thread.currentThread().isInterrupted() to stop itself.' title='Send a thread interrupt signal to pool-14-thread-1. The thread can test Thread.currentThread().isInterrupted() to stop itself.' /></a></td> <td align='center' class='noPrint'><a href='?action=kill_thread&amp;threadId=40272_***********_631' onclick="javascript:return confirm('杀掉一个Java线程是不推荐和不安全的，你确定要杀掉线程 pool-14-thread-1 ?');"><img width='16' height='16' src='?resource=stop.png' alt='杀掉线程&nbsp;pool-14-thread-1' title='杀掉线程&nbsp;pool-14-thread-1' /></a></td></tr><tr class='odd' onmouseover="this.className='highlight'" onmouseout="this.className='odd'">
<td><div class='tooltip'>
<em>
pool-14-thread-10<br/>
sun.misc.Unsafe.park(Native Method)<br/>java.util.concurrent.locks.LockSupport.park(<a href='?part=source&amp;class=java.util.concurrent.locks.LockSupport#175' class='lightwindow' type='external' title='java.util.concurrent.locks.LockSupport'>LockSupport.java:175</a>)<br/>java.util.concurrent.locks.AbstractQueuedSynchronizer$ConditionObject.await(<a href='?part=source&amp;class=java.util.concurrent.locks.AbstractQueuedSynchronizer$ConditionObject#2039' class='lightwindow' type='external' title='java.util.concurrent.locks.AbstractQueuedSynchronizer$ConditionObject'>AbstractQueuedSynchronizer.java:2039</a>)<br/>java.util.concurrent.ScheduledThreadPoolExecutor$DelayedWorkQueue.take(<a href='?part=source&amp;class=java.util.concurrent.ScheduledThreadPoolExecutor$DelayedWorkQueue#1088' class='lightwindow' type='external' title='java.util.concurrent.ScheduledThreadPoolExecutor$DelayedWorkQueue'>ScheduledThreadPoolExecutor.java:1088</a>)<br/>java.util.concurrent.ScheduledThreadPoolExecutor$DelayedWorkQueue.take(<a href='?part=source&amp;class=java.util.concurrent.ScheduledThreadPoolExecutor$DelayedWorkQueue#809' class='lightwindow' type='external' title='java.util.concurrent.ScheduledThreadPoolExecutor$DelayedWorkQueue'>ScheduledThreadPoolExecutor.java:809</a>)<br/>java.util.concurrent.ThreadPoolExecutor.getTask(<a href='?part=source&amp;class=java.util.concurrent.ThreadPoolExecutor#1074' class='lightwindow' type='external' title='java.util.concurrent.ThreadPoolExecutor'>ThreadPoolExecutor.java:1074</a>)<br/>java.util.concurrent.ThreadPoolExecutor.runWorker(<a href='?part=source&amp;class=java.util.concurrent.ThreadPoolExecutor#1134' class='lightwindow' type='external' title='java.util.concurrent.ThreadPoolExecutor'>ThreadPoolExecutor.java:1134</a>)<br/>java.util.concurrent.ThreadPoolExecutor$Worker.run(<a href='?part=source&amp;class=java.util.concurrent.ThreadPoolExecutor$Worker#624' class='lightwindow' type='external' title='java.util.concurrent.ThreadPoolExecutor$Worker'>ThreadPoolExecutor.java:624</a>)<br/>java.lang.Thread.run(<a href='?part=source&amp;class=java.lang.Thread#748' class='lightwindow' type='external' title='java.lang.Thread'>Thread.java:748</a>)<br/></em>
pool-14-thread-10</div>
</td> <td align='center'>no</td> <td align='right'>5</td> <td><img src='?resource=bullets/yellow.png' alt='WAITING'/>WAITING</td> <td>sun.misc.Unsafe.park(Native Method)</td> <td align='right'>15</td> <td align='right'>15</td> <td align='center' class='noPrint'><a href='?action=send_thread_interrupt&amp;threadId=40272_***********_640' onclick="javascript:return confirm('Do you confirm the sending of a thread interrupt signal to pool-14-thread-10?');"><img width='16' height='16' src='?resource=action_interrupt.png' alt='Send a thread interrupt signal to pool-14-thread-10. The thread can test Thread.currentThread().isInterrupted() to stop itself.' title='Send a thread interrupt signal to pool-14-thread-10. The thread can test Thread.currentThread().isInterrupted() to stop itself.' /></a></td> <td align='center' class='noPrint'><a href='?action=kill_thread&amp;threadId=40272_***********_640' onclick="javascript:return confirm('杀掉一个Java线程是不推荐和不安全的，你确定要杀掉线程 pool-14-thread-10 ?');"><img width='16' height='16' src='?resource=stop.png' alt='杀掉线程&nbsp;pool-14-thread-10' title='杀掉线程&nbsp;pool-14-thread-10' /></a></td></tr><tr onmouseover="this.className='highlight'" onmouseout="this.className=''">
<td><div class='tooltip'>
<em>
pool-14-thread-2<br/>
sun.misc.Unsafe.park(Native Method)<br/>java.util.concurrent.locks.LockSupport.park(<a href='?part=source&amp;class=java.util.concurrent.locks.LockSupport#175' class='lightwindow' type='external' title='java.util.concurrent.locks.LockSupport'>LockSupport.java:175</a>)<br/>java.util.concurrent.locks.AbstractQueuedSynchronizer$ConditionObject.await(<a href='?part=source&amp;class=java.util.concurrent.locks.AbstractQueuedSynchronizer$ConditionObject#2039' class='lightwindow' type='external' title='java.util.concurrent.locks.AbstractQueuedSynchronizer$ConditionObject'>AbstractQueuedSynchronizer.java:2039</a>)<br/>java.util.concurrent.ScheduledThreadPoolExecutor$DelayedWorkQueue.take(<a href='?part=source&amp;class=java.util.concurrent.ScheduledThreadPoolExecutor$DelayedWorkQueue#1088' class='lightwindow' type='external' title='java.util.concurrent.ScheduledThreadPoolExecutor$DelayedWorkQueue'>ScheduledThreadPoolExecutor.java:1088</a>)<br/>java.util.concurrent.ScheduledThreadPoolExecutor$DelayedWorkQueue.take(<a href='?part=source&amp;class=java.util.concurrent.ScheduledThreadPoolExecutor$DelayedWorkQueue#809' class='lightwindow' type='external' title='java.util.concurrent.ScheduledThreadPoolExecutor$DelayedWorkQueue'>ScheduledThreadPoolExecutor.java:809</a>)<br/>java.util.concurrent.ThreadPoolExecutor.getTask(<a href='?part=source&amp;class=java.util.concurrent.ThreadPoolExecutor#1074' class='lightwindow' type='external' title='java.util.concurrent.ThreadPoolExecutor'>ThreadPoolExecutor.java:1074</a>)<br/>java.util.concurrent.ThreadPoolExecutor.runWorker(<a href='?part=source&amp;class=java.util.concurrent.ThreadPoolExecutor#1134' class='lightwindow' type='external' title='java.util.concurrent.ThreadPoolExecutor'>ThreadPoolExecutor.java:1134</a>)<br/>java.util.concurrent.ThreadPoolExecutor$Worker.run(<a href='?part=source&amp;class=java.util.concurrent.ThreadPoolExecutor$Worker#624' class='lightwindow' type='external' title='java.util.concurrent.ThreadPoolExecutor$Worker'>ThreadPoolExecutor.java:624</a>)<br/>java.lang.Thread.run(<a href='?part=source&amp;class=java.lang.Thread#748' class='lightwindow' type='external' title='java.lang.Thread'>Thread.java:748</a>)<br/></em>
pool-14-thread-2</div>
</td> <td align='center'>no</td> <td align='right'>5</td> <td><img src='?resource=bullets/yellow.png' alt='WAITING'/>WAITING</td> <td>sun.misc.Unsafe.park(Native Method)</td> <td align='right'>15</td> <td align='right'>0</td> <td align='center' class='noPrint'><a href='?action=send_thread_interrupt&amp;threadId=40272_***********_632' onclick="javascript:return confirm('Do you confirm the sending of a thread interrupt signal to pool-14-thread-2?');"><img width='16' height='16' src='?resource=action_interrupt.png' alt='Send a thread interrupt signal to pool-14-thread-2. The thread can test Thread.currentThread().isInterrupted() to stop itself.' title='Send a thread interrupt signal to pool-14-thread-2. The thread can test Thread.currentThread().isInterrupted() to stop itself.' /></a></td> <td align='center' class='noPrint'><a href='?action=kill_thread&amp;threadId=40272_***********_632' onclick="javascript:return confirm('杀掉一个Java线程是不推荐和不安全的，你确定要杀掉线程 pool-14-thread-2 ?');"><img width='16' height='16' src='?resource=stop.png' alt='杀掉线程&nbsp;pool-14-thread-2' title='杀掉线程&nbsp;pool-14-thread-2' /></a></td></tr><tr class='odd' onmouseover="this.className='highlight'" onmouseout="this.className='odd'">
<td><div class='tooltip'>
<em>
pool-14-thread-3<br/>
sun.misc.Unsafe.park(Native Method)<br/>java.util.concurrent.locks.LockSupport.parkNanos(<a href='?part=source&amp;class=java.util.concurrent.locks.LockSupport#215' class='lightwindow' type='external' title='java.util.concurrent.locks.LockSupport'>LockSupport.java:215</a>)<br/>java.util.concurrent.locks.AbstractQueuedSynchronizer$ConditionObject.awaitNanos(<a href='?part=source&amp;class=java.util.concurrent.locks.AbstractQueuedSynchronizer$ConditionObject#2078' class='lightwindow' type='external' title='java.util.concurrent.locks.AbstractQueuedSynchronizer$ConditionObject'>AbstractQueuedSynchronizer.java:2078</a>)<br/>java.util.concurrent.ScheduledThreadPoolExecutor$DelayedWorkQueue.take(<a href='?part=source&amp;class=java.util.concurrent.ScheduledThreadPoolExecutor$DelayedWorkQueue#1093' class='lightwindow' type='external' title='java.util.concurrent.ScheduledThreadPoolExecutor$DelayedWorkQueue'>ScheduledThreadPoolExecutor.java:1093</a>)<br/>java.util.concurrent.ScheduledThreadPoolExecutor$DelayedWorkQueue.take(<a href='?part=source&amp;class=java.util.concurrent.ScheduledThreadPoolExecutor$DelayedWorkQueue#809' class='lightwindow' type='external' title='java.util.concurrent.ScheduledThreadPoolExecutor$DelayedWorkQueue'>ScheduledThreadPoolExecutor.java:809</a>)<br/>java.util.concurrent.ThreadPoolExecutor.getTask(<a href='?part=source&amp;class=java.util.concurrent.ThreadPoolExecutor#1074' class='lightwindow' type='external' title='java.util.concurrent.ThreadPoolExecutor'>ThreadPoolExecutor.java:1074</a>)<br/>java.util.concurrent.ThreadPoolExecutor.runWorker(<a href='?part=source&amp;class=java.util.concurrent.ThreadPoolExecutor#1134' class='lightwindow' type='external' title='java.util.concurrent.ThreadPoolExecutor'>ThreadPoolExecutor.java:1134</a>)<br/>java.util.concurrent.ThreadPoolExecutor$Worker.run(<a href='?part=source&amp;class=java.util.concurrent.ThreadPoolExecutor$Worker#624' class='lightwindow' type='external' title='java.util.concurrent.ThreadPoolExecutor$Worker'>ThreadPoolExecutor.java:624</a>)<br/>java.lang.Thread.run(<a href='?part=source&amp;class=java.lang.Thread#748' class='lightwindow' type='external' title='java.lang.Thread'>Thread.java:748</a>)<br/></em>
pool-14-thread-3</div>
</td> <td align='center'>no</td> <td align='right'>5</td> <td><img src='?resource=bullets/yellow.png' alt='TIMED_WAITING'/>TIMED_WAITING</td> <td>sun.misc.Unsafe.park(Native Method)</td> <td align='right'>31</td> <td align='right'>31</td> <td align='center' class='noPrint'><a href='?action=send_thread_interrupt&amp;threadId=40272_***********_633' onclick="javascript:return confirm('Do you confirm the sending of a thread interrupt signal to pool-14-thread-3?');"><img width='16' height='16' src='?resource=action_interrupt.png' alt='Send a thread interrupt signal to pool-14-thread-3. The thread can test Thread.currentThread().isInterrupted() to stop itself.' title='Send a thread interrupt signal to pool-14-thread-3. The thread can test Thread.currentThread().isInterrupted() to stop itself.' /></a></td> <td align='center' class='noPrint'><a href='?action=kill_thread&amp;threadId=40272_***********_633' onclick="javascript:return confirm('杀掉一个Java线程是不推荐和不安全的，你确定要杀掉线程 pool-14-thread-3 ?');"><img width='16' height='16' src='?resource=stop.png' alt='杀掉线程&nbsp;pool-14-thread-3' title='杀掉线程&nbsp;pool-14-thread-3' /></a></td></tr><tr onmouseover="this.className='highlight'" onmouseout="this.className=''">
<td><div class='tooltip'>
<em>
pool-14-thread-4<br/>
sun.misc.Unsafe.park(Native Method)<br/>java.util.concurrent.locks.LockSupport.park(<a href='?part=source&amp;class=java.util.concurrent.locks.LockSupport#175' class='lightwindow' type='external' title='java.util.concurrent.locks.LockSupport'>LockSupport.java:175</a>)<br/>java.util.concurrent.locks.AbstractQueuedSynchronizer$ConditionObject.await(<a href='?part=source&amp;class=java.util.concurrent.locks.AbstractQueuedSynchronizer$ConditionObject#2039' class='lightwindow' type='external' title='java.util.concurrent.locks.AbstractQueuedSynchronizer$ConditionObject'>AbstractQueuedSynchronizer.java:2039</a>)<br/>java.util.concurrent.ScheduledThreadPoolExecutor$DelayedWorkQueue.take(<a href='?part=source&amp;class=java.util.concurrent.ScheduledThreadPoolExecutor$DelayedWorkQueue#1088' class='lightwindow' type='external' title='java.util.concurrent.ScheduledThreadPoolExecutor$DelayedWorkQueue'>ScheduledThreadPoolExecutor.java:1088</a>)<br/>java.util.concurrent.ScheduledThreadPoolExecutor$DelayedWorkQueue.take(<a href='?part=source&amp;class=java.util.concurrent.ScheduledThreadPoolExecutor$DelayedWorkQueue#809' class='lightwindow' type='external' title='java.util.concurrent.ScheduledThreadPoolExecutor$DelayedWorkQueue'>ScheduledThreadPoolExecutor.java:809</a>)<br/>java.util.concurrent.ThreadPoolExecutor.getTask(<a href='?part=source&amp;class=java.util.concurrent.ThreadPoolExecutor#1074' class='lightwindow' type='external' title='java.util.concurrent.ThreadPoolExecutor'>ThreadPoolExecutor.java:1074</a>)<br/>java.util.concurrent.ThreadPoolExecutor.runWorker(<a href='?part=source&amp;class=java.util.concurrent.ThreadPoolExecutor#1134' class='lightwindow' type='external' title='java.util.concurrent.ThreadPoolExecutor'>ThreadPoolExecutor.java:1134</a>)<br/>java.util.concurrent.ThreadPoolExecutor$Worker.run(<a href='?part=source&amp;class=java.util.concurrent.ThreadPoolExecutor$Worker#624' class='lightwindow' type='external' title='java.util.concurrent.ThreadPoolExecutor$Worker'>ThreadPoolExecutor.java:624</a>)<br/>java.lang.Thread.run(<a href='?part=source&amp;class=java.lang.Thread#748' class='lightwindow' type='external' title='java.lang.Thread'>Thread.java:748</a>)<br/></em>
pool-14-thread-4</div>
</td> <td align='center'>no</td> <td align='right'>5</td> <td><img src='?resource=bullets/yellow.png' alt='WAITING'/>WAITING</td> <td>sun.misc.Unsafe.park(Native Method)</td> <td align='right'>62</td> <td align='right'>46</td> <td align='center' class='noPrint'><a href='?action=send_thread_interrupt&amp;threadId=40272_***********_634' onclick="javascript:return confirm('Do you confirm the sending of a thread interrupt signal to pool-14-thread-4?');"><img width='16' height='16' src='?resource=action_interrupt.png' alt='Send a thread interrupt signal to pool-14-thread-4. The thread can test Thread.currentThread().isInterrupted() to stop itself.' title='Send a thread interrupt signal to pool-14-thread-4. The thread can test Thread.currentThread().isInterrupted() to stop itself.' /></a></td> <td align='center' class='noPrint'><a href='?action=kill_thread&amp;threadId=40272_***********_634' onclick="javascript:return confirm('杀掉一个Java线程是不推荐和不安全的，你确定要杀掉线程 pool-14-thread-4 ?');"><img width='16' height='16' src='?resource=stop.png' alt='杀掉线程&nbsp;pool-14-thread-4' title='杀掉线程&nbsp;pool-14-thread-4' /></a></td></tr><tr class='odd' onmouseover="this.className='highlight'" onmouseout="this.className='odd'">
<td><div class='tooltip'>
<em>
pool-14-thread-5<br/>
sun.misc.Unsafe.park(Native Method)<br/>java.util.concurrent.locks.LockSupport.parkNanos(<a href='?part=source&amp;class=java.util.concurrent.locks.LockSupport#215' class='lightwindow' type='external' title='java.util.concurrent.locks.LockSupport'>LockSupport.java:215</a>)<br/>java.util.concurrent.locks.AbstractQueuedSynchronizer$ConditionObject.awaitNanos(<a href='?part=source&amp;class=java.util.concurrent.locks.AbstractQueuedSynchronizer$ConditionObject#2078' class='lightwindow' type='external' title='java.util.concurrent.locks.AbstractQueuedSynchronizer$ConditionObject'>AbstractQueuedSynchronizer.java:2078</a>)<br/>java.util.concurrent.ScheduledThreadPoolExecutor$DelayedWorkQueue.take(<a href='?part=source&amp;class=java.util.concurrent.ScheduledThreadPoolExecutor$DelayedWorkQueue#1093' class='lightwindow' type='external' title='java.util.concurrent.ScheduledThreadPoolExecutor$DelayedWorkQueue'>ScheduledThreadPoolExecutor.java:1093</a>)<br/>java.util.concurrent.ScheduledThreadPoolExecutor$DelayedWorkQueue.take(<a href='?part=source&amp;class=java.util.concurrent.ScheduledThreadPoolExecutor$DelayedWorkQueue#809' class='lightwindow' type='external' title='java.util.concurrent.ScheduledThreadPoolExecutor$DelayedWorkQueue'>ScheduledThreadPoolExecutor.java:809</a>)<br/>java.util.concurrent.ThreadPoolExecutor.getTask(<a href='?part=source&amp;class=java.util.concurrent.ThreadPoolExecutor#1074' class='lightwindow' type='external' title='java.util.concurrent.ThreadPoolExecutor'>ThreadPoolExecutor.java:1074</a>)<br/>java.util.concurrent.ThreadPoolExecutor.runWorker(<a href='?part=source&amp;class=java.util.concurrent.ThreadPoolExecutor#1134' class='lightwindow' type='external' title='java.util.concurrent.ThreadPoolExecutor'>ThreadPoolExecutor.java:1134</a>)<br/>java.util.concurrent.ThreadPoolExecutor$Worker.run(<a href='?part=source&amp;class=java.util.concurrent.ThreadPoolExecutor$Worker#624' class='lightwindow' type='external' title='java.util.concurrent.ThreadPoolExecutor$Worker'>ThreadPoolExecutor.java:624</a>)<br/>java.lang.Thread.run(<a href='?part=source&amp;class=java.lang.Thread#748' class='lightwindow' type='external' title='java.lang.Thread'>Thread.java:748</a>)<br/></em>
pool-14-thread-5</div>
</td> <td align='center'>no</td> <td align='right'>5</td> <td><img src='?resource=bullets/yellow.png' alt='TIMED_WAITING'/>TIMED_WAITING</td> <td>sun.misc.Unsafe.park(Native Method)</td> <td align='right'>62</td> <td align='right'>15</td> <td align='center' class='noPrint'><a href='?action=send_thread_interrupt&amp;threadId=40272_***********_635' onclick="javascript:return confirm('Do you confirm the sending of a thread interrupt signal to pool-14-thread-5?');"><img width='16' height='16' src='?resource=action_interrupt.png' alt='Send a thread interrupt signal to pool-14-thread-5. The thread can test Thread.currentThread().isInterrupted() to stop itself.' title='Send a thread interrupt signal to pool-14-thread-5. The thread can test Thread.currentThread().isInterrupted() to stop itself.' /></a></td> <td align='center' class='noPrint'><a href='?action=kill_thread&amp;threadId=40272_***********_635' onclick="javascript:return confirm('杀掉一个Java线程是不推荐和不安全的，你确定要杀掉线程 pool-14-thread-5 ?');"><img width='16' height='16' src='?resource=stop.png' alt='杀掉线程&nbsp;pool-14-thread-5' title='杀掉线程&nbsp;pool-14-thread-5' /></a></td></tr><tr onmouseover="this.className='highlight'" onmouseout="this.className=''">
<td><div class='tooltip'>
<em>
pool-14-thread-6<br/>
sun.misc.Unsafe.park(Native Method)<br/>java.util.concurrent.locks.LockSupport.park(<a href='?part=source&amp;class=java.util.concurrent.locks.LockSupport#175' class='lightwindow' type='external' title='java.util.concurrent.locks.LockSupport'>LockSupport.java:175</a>)<br/>java.util.concurrent.locks.AbstractQueuedSynchronizer$ConditionObject.await(<a href='?part=source&amp;class=java.util.concurrent.locks.AbstractQueuedSynchronizer$ConditionObject#2039' class='lightwindow' type='external' title='java.util.concurrent.locks.AbstractQueuedSynchronizer$ConditionObject'>AbstractQueuedSynchronizer.java:2039</a>)<br/>java.util.concurrent.ScheduledThreadPoolExecutor$DelayedWorkQueue.take(<a href='?part=source&amp;class=java.util.concurrent.ScheduledThreadPoolExecutor$DelayedWorkQueue#1088' class='lightwindow' type='external' title='java.util.concurrent.ScheduledThreadPoolExecutor$DelayedWorkQueue'>ScheduledThreadPoolExecutor.java:1088</a>)<br/>java.util.concurrent.ScheduledThreadPoolExecutor$DelayedWorkQueue.take(<a href='?part=source&amp;class=java.util.concurrent.ScheduledThreadPoolExecutor$DelayedWorkQueue#809' class='lightwindow' type='external' title='java.util.concurrent.ScheduledThreadPoolExecutor$DelayedWorkQueue'>ScheduledThreadPoolExecutor.java:809</a>)<br/>java.util.concurrent.ThreadPoolExecutor.getTask(<a href='?part=source&amp;class=java.util.concurrent.ThreadPoolExecutor#1074' class='lightwindow' type='external' title='java.util.concurrent.ThreadPoolExecutor'>ThreadPoolExecutor.java:1074</a>)<br/>java.util.concurrent.ThreadPoolExecutor.runWorker(<a href='?part=source&amp;class=java.util.concurrent.ThreadPoolExecutor#1134' class='lightwindow' type='external' title='java.util.concurrent.ThreadPoolExecutor'>ThreadPoolExecutor.java:1134</a>)<br/>java.util.concurrent.ThreadPoolExecutor$Worker.run(<a href='?part=source&amp;class=java.util.concurrent.ThreadPoolExecutor$Worker#624' class='lightwindow' type='external' title='java.util.concurrent.ThreadPoolExecutor$Worker'>ThreadPoolExecutor.java:624</a>)<br/>java.lang.Thread.run(<a href='?part=source&amp;class=java.lang.Thread#748' class='lightwindow' type='external' title='java.lang.Thread'>Thread.java:748</a>)<br/></em>
pool-14-thread-6</div>
</td> <td align='center'>no</td> <td align='right'>5</td> <td><img src='?resource=bullets/yellow.png' alt='WAITING'/>WAITING</td> <td>sun.misc.Unsafe.park(Native Method)</td> <td align='right'>46</td> <td align='right'>15</td> <td align='center' class='noPrint'><a href='?action=send_thread_interrupt&amp;threadId=40272_***********_636' onclick="javascript:return confirm('Do you confirm the sending of a thread interrupt signal to pool-14-thread-6?');"><img width='16' height='16' src='?resource=action_interrupt.png' alt='Send a thread interrupt signal to pool-14-thread-6. The thread can test Thread.currentThread().isInterrupted() to stop itself.' title='Send a thread interrupt signal to pool-14-thread-6. The thread can test Thread.currentThread().isInterrupted() to stop itself.' /></a></td> <td align='center' class='noPrint'><a href='?action=kill_thread&amp;threadId=40272_***********_636' onclick="javascript:return confirm('杀掉一个Java线程是不推荐和不安全的，你确定要杀掉线程 pool-14-thread-6 ?');"><img width='16' height='16' src='?resource=stop.png' alt='杀掉线程&nbsp;pool-14-thread-6' title='杀掉线程&nbsp;pool-14-thread-6' /></a></td></tr><tr class='odd' onmouseover="this.className='highlight'" onmouseout="this.className='odd'">
<td><div class='tooltip'>
<em>
pool-14-thread-7<br/>
sun.misc.Unsafe.park(Native Method)<br/>java.util.concurrent.locks.LockSupport.park(<a href='?part=source&amp;class=java.util.concurrent.locks.LockSupport#175' class='lightwindow' type='external' title='java.util.concurrent.locks.LockSupport'>LockSupport.java:175</a>)<br/>java.util.concurrent.locks.AbstractQueuedSynchronizer$ConditionObject.await(<a href='?part=source&amp;class=java.util.concurrent.locks.AbstractQueuedSynchronizer$ConditionObject#2039' class='lightwindow' type='external' title='java.util.concurrent.locks.AbstractQueuedSynchronizer$ConditionObject'>AbstractQueuedSynchronizer.java:2039</a>)<br/>java.util.concurrent.ScheduledThreadPoolExecutor$DelayedWorkQueue.take(<a href='?part=source&amp;class=java.util.concurrent.ScheduledThreadPoolExecutor$DelayedWorkQueue#1088' class='lightwindow' type='external' title='java.util.concurrent.ScheduledThreadPoolExecutor$DelayedWorkQueue'>ScheduledThreadPoolExecutor.java:1088</a>)<br/>java.util.concurrent.ScheduledThreadPoolExecutor$DelayedWorkQueue.take(<a href='?part=source&amp;class=java.util.concurrent.ScheduledThreadPoolExecutor$DelayedWorkQueue#809' class='lightwindow' type='external' title='java.util.concurrent.ScheduledThreadPoolExecutor$DelayedWorkQueue'>ScheduledThreadPoolExecutor.java:809</a>)<br/>java.util.concurrent.ThreadPoolExecutor.getTask(<a href='?part=source&amp;class=java.util.concurrent.ThreadPoolExecutor#1074' class='lightwindow' type='external' title='java.util.concurrent.ThreadPoolExecutor'>ThreadPoolExecutor.java:1074</a>)<br/>java.util.concurrent.ThreadPoolExecutor.runWorker(<a href='?part=source&amp;class=java.util.concurrent.ThreadPoolExecutor#1134' class='lightwindow' type='external' title='java.util.concurrent.ThreadPoolExecutor'>ThreadPoolExecutor.java:1134</a>)<br/>java.util.concurrent.ThreadPoolExecutor$Worker.run(<a href='?part=source&amp;class=java.util.concurrent.ThreadPoolExecutor$Worker#624' class='lightwindow' type='external' title='java.util.concurrent.ThreadPoolExecutor$Worker'>ThreadPoolExecutor.java:624</a>)<br/>java.lang.Thread.run(<a href='?part=source&amp;class=java.lang.Thread#748' class='lightwindow' type='external' title='java.lang.Thread'>Thread.java:748</a>)<br/></em>
pool-14-thread-7</div>
</td> <td align='center'>no</td> <td align='right'>5</td> <td><img src='?resource=bullets/yellow.png' alt='WAITING'/>WAITING</td> <td>sun.misc.Unsafe.park(Native Method)</td> <td align='right'>78</td> <td align='right'>31</td> <td align='center' class='noPrint'><a href='?action=send_thread_interrupt&amp;threadId=40272_***********_637' onclick="javascript:return confirm('Do you confirm the sending of a thread interrupt signal to pool-14-thread-7?');"><img width='16' height='16' src='?resource=action_interrupt.png' alt='Send a thread interrupt signal to pool-14-thread-7. The thread can test Thread.currentThread().isInterrupted() to stop itself.' title='Send a thread interrupt signal to pool-14-thread-7. The thread can test Thread.currentThread().isInterrupted() to stop itself.' /></a></td> <td align='center' class='noPrint'><a href='?action=kill_thread&amp;threadId=40272_***********_637' onclick="javascript:return confirm('杀掉一个Java线程是不推荐和不安全的，你确定要杀掉线程 pool-14-thread-7 ?');"><img width='16' height='16' src='?resource=stop.png' alt='杀掉线程&nbsp;pool-14-thread-7' title='杀掉线程&nbsp;pool-14-thread-7' /></a></td></tr><tr onmouseover="this.className='highlight'" onmouseout="this.className=''">
<td><div class='tooltip'>
<em>
pool-14-thread-8<br/>
sun.misc.Unsafe.park(Native Method)<br/>java.util.concurrent.locks.LockSupport.park(<a href='?part=source&amp;class=java.util.concurrent.locks.LockSupport#175' class='lightwindow' type='external' title='java.util.concurrent.locks.LockSupport'>LockSupport.java:175</a>)<br/>java.util.concurrent.locks.AbstractQueuedSynchronizer$ConditionObject.await(<a href='?part=source&amp;class=java.util.concurrent.locks.AbstractQueuedSynchronizer$ConditionObject#2039' class='lightwindow' type='external' title='java.util.concurrent.locks.AbstractQueuedSynchronizer$ConditionObject'>AbstractQueuedSynchronizer.java:2039</a>)<br/>java.util.concurrent.ScheduledThreadPoolExecutor$DelayedWorkQueue.take(<a href='?part=source&amp;class=java.util.concurrent.ScheduledThreadPoolExecutor$DelayedWorkQueue#1088' class='lightwindow' type='external' title='java.util.concurrent.ScheduledThreadPoolExecutor$DelayedWorkQueue'>ScheduledThreadPoolExecutor.java:1088</a>)<br/>java.util.concurrent.ScheduledThreadPoolExecutor$DelayedWorkQueue.take(<a href='?part=source&amp;class=java.util.concurrent.ScheduledThreadPoolExecutor$DelayedWorkQueue#809' class='lightwindow' type='external' title='java.util.concurrent.ScheduledThreadPoolExecutor$DelayedWorkQueue'>ScheduledThreadPoolExecutor.java:809</a>)<br/>java.util.concurrent.ThreadPoolExecutor.getTask(<a href='?part=source&amp;class=java.util.concurrent.ThreadPoolExecutor#1074' class='lightwindow' type='external' title='java.util.concurrent.ThreadPoolExecutor'>ThreadPoolExecutor.java:1074</a>)<br/>java.util.concurrent.ThreadPoolExecutor.runWorker(<a href='?part=source&amp;class=java.util.concurrent.ThreadPoolExecutor#1134' class='lightwindow' type='external' title='java.util.concurrent.ThreadPoolExecutor'>ThreadPoolExecutor.java:1134</a>)<br/>java.util.concurrent.ThreadPoolExecutor$Worker.run(<a href='?part=source&amp;class=java.util.concurrent.ThreadPoolExecutor$Worker#624' class='lightwindow' type='external' title='java.util.concurrent.ThreadPoolExecutor$Worker'>ThreadPoolExecutor.java:624</a>)<br/>java.lang.Thread.run(<a href='?part=source&amp;class=java.lang.Thread#748' class='lightwindow' type='external' title='java.lang.Thread'>Thread.java:748</a>)<br/></em>
pool-14-thread-8</div>
</td> <td align='center'>no</td> <td align='right'>5</td> <td><img src='?resource=bullets/yellow.png' alt='WAITING'/>WAITING</td> <td>sun.misc.Unsafe.park(Native Method)</td> <td align='right'>31</td> <td align='right'>31</td> <td align='center' class='noPrint'><a href='?action=send_thread_interrupt&amp;threadId=40272_***********_638' onclick="javascript:return confirm('Do you confirm the sending of a thread interrupt signal to pool-14-thread-8?');"><img width='16' height='16' src='?resource=action_interrupt.png' alt='Send a thread interrupt signal to pool-14-thread-8. The thread can test Thread.currentThread().isInterrupted() to stop itself.' title='Send a thread interrupt signal to pool-14-thread-8. The thread can test Thread.currentThread().isInterrupted() to stop itself.' /></a></td> <td align='center' class='noPrint'><a href='?action=kill_thread&amp;threadId=40272_***********_638' onclick="javascript:return confirm('杀掉一个Java线程是不推荐和不安全的，你确定要杀掉线程 pool-14-thread-8 ?');"><img width='16' height='16' src='?resource=stop.png' alt='杀掉线程&nbsp;pool-14-thread-8' title='杀掉线程&nbsp;pool-14-thread-8' /></a></td></tr><tr class='odd' onmouseover="this.className='highlight'" onmouseout="this.className='odd'">
<td><div class='tooltip'>
<em>
pool-14-thread-9<br/>
sun.misc.Unsafe.park(Native Method)<br/>java.util.concurrent.locks.LockSupport.park(<a href='?part=source&amp;class=java.util.concurrent.locks.LockSupport#175' class='lightwindow' type='external' title='java.util.concurrent.locks.LockSupport'>LockSupport.java:175</a>)<br/>java.util.concurrent.locks.AbstractQueuedSynchronizer$ConditionObject.await(<a href='?part=source&amp;class=java.util.concurrent.locks.AbstractQueuedSynchronizer$ConditionObject#2039' class='lightwindow' type='external' title='java.util.concurrent.locks.AbstractQueuedSynchronizer$ConditionObject'>AbstractQueuedSynchronizer.java:2039</a>)<br/>java.util.concurrent.ScheduledThreadPoolExecutor$DelayedWorkQueue.take(<a href='?part=source&amp;class=java.util.concurrent.ScheduledThreadPoolExecutor$DelayedWorkQueue#1088' class='lightwindow' type='external' title='java.util.concurrent.ScheduledThreadPoolExecutor$DelayedWorkQueue'>ScheduledThreadPoolExecutor.java:1088</a>)<br/>java.util.concurrent.ScheduledThreadPoolExecutor$DelayedWorkQueue.take(<a href='?part=source&amp;class=java.util.concurrent.ScheduledThreadPoolExecutor$DelayedWorkQueue#809' class='lightwindow' type='external' title='java.util.concurrent.ScheduledThreadPoolExecutor$DelayedWorkQueue'>ScheduledThreadPoolExecutor.java:809</a>)<br/>java.util.concurrent.ThreadPoolExecutor.getTask(<a href='?part=source&amp;class=java.util.concurrent.ThreadPoolExecutor#1074' class='lightwindow' type='external' title='java.util.concurrent.ThreadPoolExecutor'>ThreadPoolExecutor.java:1074</a>)<br/>java.util.concurrent.ThreadPoolExecutor.runWorker(<a href='?part=source&amp;class=java.util.concurrent.ThreadPoolExecutor#1134' class='lightwindow' type='external' title='java.util.concurrent.ThreadPoolExecutor'>ThreadPoolExecutor.java:1134</a>)<br/>java.util.concurrent.ThreadPoolExecutor$Worker.run(<a href='?part=source&amp;class=java.util.concurrent.ThreadPoolExecutor$Worker#624' class='lightwindow' type='external' title='java.util.concurrent.ThreadPoolExecutor$Worker'>ThreadPoolExecutor.java:624</a>)<br/>java.lang.Thread.run(<a href='?part=source&amp;class=java.lang.Thread#748' class='lightwindow' type='external' title='java.lang.Thread'>Thread.java:748</a>)<br/></em>
pool-14-thread-9</div>
</td> <td align='center'>no</td> <td align='right'>5</td> <td><img src='?resource=bullets/yellow.png' alt='WAITING'/>WAITING</td> <td>sun.misc.Unsafe.park(Native Method)</td> <td align='right'>31</td> <td align='right'>31</td> <td align='center' class='noPrint'><a href='?action=send_thread_interrupt&amp;threadId=40272_***********_639' onclick="javascript:return confirm('Do you confirm the sending of a thread interrupt signal to pool-14-thread-9?');"><img width='16' height='16' src='?resource=action_interrupt.png' alt='Send a thread interrupt signal to pool-14-thread-9. The thread can test Thread.currentThread().isInterrupted() to stop itself.' title='Send a thread interrupt signal to pool-14-thread-9. The thread can test Thread.currentThread().isInterrupted() to stop itself.' /></a></td> <td align='center' class='noPrint'><a href='?action=kill_thread&amp;threadId=40272_***********_639' onclick="javascript:return confirm('杀掉一个Java线程是不推荐和不安全的，你确定要杀掉线程 pool-14-thread-9 ?');"><img width='16' height='16' src='?resource=stop.png' alt='杀掉线程&nbsp;pool-14-thread-9' title='杀掉线程&nbsp;pool-14-thread-9' /></a></td></tr><tr onmouseover="this.className='highlight'" onmouseout="this.className=''">
<td><div class='tooltip'>
<em>
Reference&nbsp;Handler<br/>
java.lang.Object.wait(Native Method)<br/>java.lang.Object.wait(<a href='?part=source&amp;class=java.lang.Object#502' class='lightwindow' type='external' title='java.lang.Object'>Object.java:502</a>)<br/>java.lang.ref.Reference.tryHandlePending(<a href='?part=source&amp;class=java.lang.ref.Reference#191' class='lightwindow' type='external' title='java.lang.ref.Reference'>Reference.java:191</a>)<br/>java.lang.ref.Reference$ReferenceHandler.run(<a href='?part=source&amp;class=java.lang.ref.Reference$ReferenceHandler#153' class='lightwindow' type='external' title='java.lang.ref.Reference$ReferenceHandler'>Reference.java:153</a>)<br/></em>
Reference&nbsp;Handler</div>
</td> <td align='center'>yes</td> <td align='right'>10</td> <td><img src='?resource=bullets/yellow.png' alt='WAITING'/>WAITING</td> <td>java.lang.Object.wait(Native Method)</td> <td align='right'>46</td> <td align='right'>15</td> <td align='center' class='noPrint'><a href='?action=send_thread_interrupt&amp;threadId=40272_***********_2' onclick="javascript:return confirm('Do you confirm the sending of a thread interrupt signal to Reference Handler?');"><img width='16' height='16' src='?resource=action_interrupt.png' alt='Send a thread interrupt signal to Reference Handler. The thread can test Thread.currentThread().isInterrupted() to stop itself.' title='Send a thread interrupt signal to Reference Handler. The thread can test Thread.currentThread().isInterrupted() to stop itself.' /></a></td> <td align='center' class='noPrint'><a href='?action=kill_thread&amp;threadId=40272_***********_2' onclick="javascript:return confirm('杀掉一个Java线程是不推荐和不安全的，你确定要杀掉线程 Reference Handler ?');"><img width='16' height='16' src='?resource=stop.png' alt='杀掉线程&nbsp;Reference&nbsp;Handler' title='杀掉线程&nbsp;Reference&nbsp;Handler' /></a></td></tr><tr class='odd' onmouseover="this.className='highlight'" onmouseout="this.className='odd'">
<td><div class='tooltip'>
<em>
RMI&nbsp;TCP&nbsp;Accept-0<br/>
java.net.DualStackPlainSocketImpl.accept0(Native Method)<br/>java.net.DualStackPlainSocketImpl.socketAccept(<a href='?part=source&amp;class=java.net.DualStackPlainSocketImpl#131' class='lightwindow' type='external' title='java.net.DualStackPlainSocketImpl'>DualStackPlainSocketImpl.java:131</a>)<br/>java.net.AbstractPlainSocketImpl.accept(<a href='?part=source&amp;class=java.net.AbstractPlainSocketImpl#409' class='lightwindow' type='external' title='java.net.AbstractPlainSocketImpl'>AbstractPlainSocketImpl.java:409</a>)<br/>java.net.PlainSocketImpl.accept(<a href='?part=source&amp;class=java.net.PlainSocketImpl#199' class='lightwindow' type='external' title='java.net.PlainSocketImpl'>PlainSocketImpl.java:199</a>)<br/>java.net.ServerSocket.implAccept(<a href='?part=source&amp;class=java.net.ServerSocket#545' class='lightwindow' type='external' title='java.net.ServerSocket'>ServerSocket.java:545</a>)<br/>java.net.ServerSocket.accept(<a href='?part=source&amp;class=java.net.ServerSocket#513' class='lightwindow' type='external' title='java.net.ServerSocket'>ServerSocket.java:513</a>)<br/>sun.management.jmxremote.LocalRMIServerSocketFactory$1.accept(<a href='?part=source&amp;class=sun.management.jmxremote.LocalRMIServerSocketFactory$1#52' class='lightwindow' type='external' title='sun.management.jmxremote.LocalRMIServerSocketFactory$1'>LocalRMIServerSocketFactory.java:52</a>)<br/>sun.rmi.transport.tcp.TCPTransport$AcceptLoop.executeAcceptLoop(<a href='?part=source&amp;class=sun.rmi.transport.tcp.TCPTransport$AcceptLoop#405' class='lightwindow' type='external' title='sun.rmi.transport.tcp.TCPTransport$AcceptLoop'>TCPTransport.java:405</a>)<br/>sun.rmi.transport.tcp.TCPTransport$AcceptLoop.run(<a href='?part=source&amp;class=sun.rmi.transport.tcp.TCPTransport$AcceptLoop#377' class='lightwindow' type='external' title='sun.rmi.transport.tcp.TCPTransport$AcceptLoop'>TCPTransport.java:377</a>)<br/>java.lang.Thread.run(<a href='?part=source&amp;class=java.lang.Thread#748' class='lightwindow' type='external' title='java.lang.Thread'>Thread.java:748</a>)<br/></em>
RMI&nbsp;TCP&nbsp;Accept-0</div>
</td> <td align='center'>yes</td> <td align='right'>5</td> <td><img src='?resource=bullets/green.png' alt='RUNNABLE'/>RUNNABLE</td> <td>java.net.DualStackPlainSocketImpl.accept0(Native Method)</td> <td align='right'>0</td> <td align='right'>0</td> <td align='center' class='noPrint'><a href='?action=send_thread_interrupt&amp;threadId=40272_***********_13' onclick="javascript:return confirm('Do you confirm the sending of a thread interrupt signal to RMI TCP Accept-0?');"><img width='16' height='16' src='?resource=action_interrupt.png' alt='Send a thread interrupt signal to RMI TCP Accept-0. The thread can test Thread.currentThread().isInterrupted() to stop itself.' title='Send a thread interrupt signal to RMI TCP Accept-0. The thread can test Thread.currentThread().isInterrupted() to stop itself.' /></a></td> <td align='center' class='noPrint'><a href='?action=kill_thread&amp;threadId=40272_***********_13' onclick="javascript:return confirm('杀掉一个Java线程是不推荐和不安全的，你确定要杀掉线程 RMI TCP Accept-0 ?');"><img width='16' height='16' src='?resource=stop.png' alt='杀掉线程&nbsp;RMI&nbsp;TCP&nbsp;Accept-0' title='杀掉线程&nbsp;RMI&nbsp;TCP&nbsp;Accept-0' /></a></td></tr><tr onmouseover="this.className='highlight'" onmouseout="this.className=''">
<td>Signal&nbsp;Dispatcher</td> <td align='center'>yes</td> <td align='right'>9</td> <td><img src='?resource=bullets/green.png' alt='RUNNABLE'/>RUNNABLE</td> <td>&nbsp;</td> <td align='right'>0</td> <td align='right'>0</td> <td align='center' class='noPrint'><a href='?action=send_thread_interrupt&amp;threadId=40272_***********_4' onclick="javascript:return confirm('Do you confirm the sending of a thread interrupt signal to Signal Dispatcher?');"><img width='16' height='16' src='?resource=action_interrupt.png' alt='Send a thread interrupt signal to Signal Dispatcher. The thread can test Thread.currentThread().isInterrupted() to stop itself.' title='Send a thread interrupt signal to Signal Dispatcher. The thread can test Thread.currentThread().isInterrupted() to stop itself.' /></a></td> <td align='center' class='noPrint'><a href='?action=kill_thread&amp;threadId=40272_***********_4' onclick="javascript:return confirm('杀掉一个Java线程是不推荐和不安全的，你确定要杀掉线程 Signal Dispatcher ?');"><img width='16' height='16' src='?resource=stop.png' alt='杀掉线程&nbsp;Signal&nbsp;Dispatcher' title='杀掉线程&nbsp;Signal&nbsp;Dispatcher' /></a></td></tr><tr class='odd' onmouseover="this.className='highlight'" onmouseout="this.className='odd'">
<td><div class='tooltip'>
<em>
Thread-30<br/>
sun.net.dns.ResolverConfigurationImpl.notifyAddrChange0(Native Method)<br/>sun.net.dns.ResolverConfigurationImpl$AddressChangeListener.run(<a href='?part=source&amp;class=sun.net.dns.ResolverConfigurationImpl$AddressChangeListener#144' class='lightwindow' type='external' title='sun.net.dns.ResolverConfigurationImpl$AddressChangeListener'>ResolverConfigurationImpl.java:144</a>)<br/></em>
Thread-30</div>
</td> <td align='center'>yes</td> <td align='right'>5</td> <td><img src='?resource=bullets/green.png' alt='RUNNABLE'/>RUNNABLE</td> <td>sun.net.dns.ResolverConfigurationImpl.notifyAddrChange0(Native Method)</td> <td align='right'>0</td> <td align='right'>0</td> <td align='center' class='noPrint'><a href='?action=send_thread_interrupt&amp;threadId=40272_***********_93' onclick="javascript:return confirm('Do you confirm the sending of a thread interrupt signal to Thread-30?');"><img width='16' height='16' src='?resource=action_interrupt.png' alt='Send a thread interrupt signal to Thread-30. The thread can test Thread.currentThread().isInterrupted() to stop itself.' title='Send a thread interrupt signal to Thread-30. The thread can test Thread.currentThread().isInterrupted() to stop itself.' /></a></td> <td align='center' class='noPrint'><a href='?action=kill_thread&amp;threadId=40272_***********_93' onclick="javascript:return confirm('杀掉一个Java线程是不推荐和不安全的，你确定要杀掉线程 Thread-30 ?');"><img width='16' height='16' src='?resource=stop.png' alt='杀掉线程&nbsp;Thread-30' title='杀掉线程&nbsp;Thread-30' /></a></td></tr><tr onmouseover="this.className='highlight'" onmouseout="this.className=''">
<td><div class='tooltip'>
<em>
Thread-35<br/>
java.lang.Thread.dumpThreads(Native Method)<br/>java.lang.Thread.getAllStackTraces(<a href='?part=source&amp;class=java.lang.Thread#1610' class='lightwindow' type='external' title='java.lang.Thread'>Thread.java:1610</a>)<br/>net.bull.javamelody.internal.model.JavaInformations.buildThreadInformationsList(<a href='?part=source&amp;class=net.bull.javamelody.internal.model.JavaInformations#310' class='lightwindow' type='external' title='net.bull.javamelody.internal.model.JavaInformations'>JavaInformations.java:310</a>)<br/>net.bull.javamelody.internal.model.JavaInformations.&lt;init&gt;(<a href='?part=source&amp;class=net.bull.javamelody.internal.model.JavaInformations#187' class='lightwindow' type='external' title='net.bull.javamelody.internal.model.JavaInformations'>JavaInformations.java:187</a>)<br/>net.bull.javamelody.internal.web.HtmlController.writeHtmlToLastShutdownFile(<a href='?part=source&amp;class=net.bull.javamelody.internal.web.HtmlController#344' class='lightwindow' type='external' title='net.bull.javamelody.internal.web.HtmlController'>HtmlController.java:344</a>)<br/>net.bull.javamelody.internal.web.MonitoringController.writeHtmlToLastShutdownFile(<a href='?part=source&amp;class=net.bull.javamelody.internal.web.MonitoringController#294' class='lightwindow' type='external' title='net.bull.javamelody.internal.web.MonitoringController'>MonitoringController.java:294</a>)<br/>net.bull.javamelody.FilterContext.destroy(<a href='?part=source&amp;class=net.bull.javamelody.FilterContext#443' class='lightwindow' type='external' title='net.bull.javamelody.FilterContext'>FilterContext.java:443</a>)<br/>net.bull.javamelody.MonitoringFilter.destroy(<a href='?part=source&amp;class=net.bull.javamelody.MonitoringFilter#176' class='lightwindow' type='external' title='net.bull.javamelody.MonitoringFilter'>MonitoringFilter.java:176</a>)<br/>org.apache.catalina.core.ApplicationFilterConfig.release(<a href='?part=source&amp;class=org.apache.catalina.core.ApplicationFilterConfig#303' class='lightwindow' type='external' title='org.apache.catalina.core.ApplicationFilterConfig'>ApplicationFilterConfig.java:303</a>)<br/>org.apache.catalina.core.StandardContext.filterStop(<a href='?part=source&amp;class=org.apache.catalina.core.StandardContext#4647' class='lightwindow' type='external' title='org.apache.catalina.core.StandardContext'>StandardContext.java:4647</a>)<br/>org.apache.catalina.core.StandardContext.stopInternal(<a href='?part=source&amp;class=org.apache.catalina.core.StandardContext#5469' class='lightwindow' type='external' title='org.apache.catalina.core.StandardContext'>StandardContext.java:5469</a>)<br/>org.apache.catalina.util.LifecycleBase.stop(<a href='?part=source&amp;class=org.apache.catalina.util.LifecycleBase#257' class='lightwindow' type='external' title='org.apache.catalina.util.LifecycleBase'>LifecycleBase.java:257</a>)<br/>org.apache.catalina.core.ContainerBase$StopChild.call(<a href='?part=source&amp;class=org.apache.catalina.core.ContainerBase$StopChild#1412' class='lightwindow' type='external' title='org.apache.catalina.core.ContainerBase$StopChild'>ContainerBase.java:1412</a>)<br/>org.apache.catalina.core.ContainerBase$StopChild.call(<a href='?part=source&amp;class=org.apache.catalina.core.ContainerBase$StopChild#1401' class='lightwindow' type='external' title='org.apache.catalina.core.ContainerBase$StopChild'>ContainerBase.java:1401</a>)<br/>java.util.concurrent.FutureTask.run(<a href='?part=source&amp;class=java.util.concurrent.FutureTask#266' class='lightwindow' type='external' title='java.util.concurrent.FutureTask'>FutureTask.java:266</a>)<br/>org.apache.tomcat.util.threads.InlineExecutorService.execute(<a href='?part=source&amp;class=org.apache.tomcat.util.threads.InlineExecutorService#75' class='lightwindow' type='external' title='org.apache.tomcat.util.threads.InlineExecutorService'>InlineExecutorService.java:75</a>)<br/>java.util.concurrent.AbstractExecutorService.submit(<a href='?part=source&amp;class=java.util.concurrent.AbstractExecutorService#134' class='lightwindow' type='external' title='java.util.concurrent.AbstractExecutorService'>AbstractExecutorService.java:134</a>)<br/>org.apache.catalina.core.ContainerBase.stopInternal(<a href='?part=source&amp;class=org.apache.catalina.core.ContainerBase#986' class='lightwindow' type='external' title='org.apache.catalina.core.ContainerBase'>ContainerBase.java:986</a>)<br/>org.apache.catalina.util.LifecycleBase.stop(<a href='?part=source&amp;class=org.apache.catalina.util.LifecycleBase#257' class='lightwindow' type='external' title='org.apache.catalina.util.LifecycleBase'>LifecycleBase.java:257</a>)<br/>org.apache.catalina.core.ContainerBase$StopChild.call(<a href='?part=source&amp;class=org.apache.catalina.core.ContainerBase$StopChild#1412' class='lightwindow' type='external' title='org.apache.catalina.core.ContainerBase$StopChild'>ContainerBase.java:1412</a>)<br/>org.apache.catalina.core.ContainerBase$StopChild.call(<a href='?part=source&amp;class=org.apache.catalina.core.ContainerBase$StopChild#1401' class='lightwindow' type='external' title='org.apache.catalina.core.ContainerBase$StopChild'>ContainerBase.java:1401</a>)<br/>java.util.concurrent.FutureTask.run(<a href='?part=source&amp;class=java.util.concurrent.FutureTask#266' class='lightwindow' type='external' title='java.util.concurrent.FutureTask'>FutureTask.java:266</a>)<br/>org.apache.tomcat.util.threads.InlineExecutorService.execute(<a href='?part=source&amp;class=org.apache.tomcat.util.threads.InlineExecutorService#75' class='lightwindow' type='external' title='org.apache.tomcat.util.threads.InlineExecutorService'>InlineExecutorService.java:75</a>)<br/>java.util.concurrent.AbstractExecutorService.submit(<a href='?part=source&amp;class=java.util.concurrent.AbstractExecutorService#134' class='lightwindow' type='external' title='java.util.concurrent.AbstractExecutorService'>AbstractExecutorService.java:134</a>)<br/>org.apache.catalina.core.ContainerBase.stopInternal(<a href='?part=source&amp;class=org.apache.catalina.core.ContainerBase#986' class='lightwindow' type='external' title='org.apache.catalina.core.ContainerBase'>ContainerBase.java:986</a>)<br/>org.apache.catalina.util.LifecycleBase.stop(<a href='?part=source&amp;class=org.apache.catalina.util.LifecycleBase#257' class='lightwindow' type='external' title='org.apache.catalina.util.LifecycleBase'>LifecycleBase.java:257</a>)<br/>org.apache.catalina.core.StandardService.stopInternal(<a href='?part=source&amp;class=org.apache.catalina.core.StandardService#497' class='lightwindow' type='external' title='org.apache.catalina.core.StandardService'>StandardService.java:497</a>)<br/>org.apache.catalina.util.LifecycleBase.stop(<a href='?part=source&amp;class=org.apache.catalina.util.LifecycleBase#257' class='lightwindow' type='external' title='org.apache.catalina.util.LifecycleBase'>LifecycleBase.java:257</a>)<br/>org.apache.catalina.core.StandardServer.stopInternal(<a href='?part=source&amp;class=org.apache.catalina.core.StandardServer#982' class='lightwindow' type='external' title='org.apache.catalina.core.StandardServer'>StandardServer.java:982</a>)<br/>org.apache.catalina.util.LifecycleBase.stop(<a href='?part=source&amp;class=org.apache.catalina.util.LifecycleBase#257' class='lightwindow' type='external' title='org.apache.catalina.util.LifecycleBase'>LifecycleBase.java:257</a>)<br/>org.apache.catalina.startup.Tomcat.stop(<a href='?part=source&amp;class=org.apache.catalina.startup.Tomcat#496' class='lightwindow' type='external' title='org.apache.catalina.startup.Tomcat'>Tomcat.java:496</a>)<br/>org.springframework.boot.web.embedded.tomcat.TomcatWebServer.stopTomcat(<a href='?part=source&amp;class=org.springframework.boot.web.embedded.tomcat.TomcatWebServer#269' class='lightwindow' type='external' title='org.springframework.boot.web.embedded.tomcat.TomcatWebServer'>TomcatWebServer.java:269</a>)<br/>org.springframework.boot.web.embedded.tomcat.TomcatWebServer.stop(<a href='?part=source&amp;class=org.springframework.boot.web.embedded.tomcat.TomcatWebServer#324' class='lightwindow' type='external' title='org.springframework.boot.web.embedded.tomcat.TomcatWebServer'>TomcatWebServer.java:324</a>)<br/>org.springframework.boot.web.servlet.context.ServletWebServerApplicationContext.stopAndReleaseWebServer(<a href='?part=source&amp;class=org.springframework.boot.web.servlet.context.ServletWebServerApplicationContext#306' class='lightwindow' type='external' title='org.springframework.boot.web.servlet.context.ServletWebServerApplicationContext'>ServletWebServerApplicationContext.java:306</a>)<br/>org.springframework.boot.web.servlet.context.ServletWebServerApplicationContext.onClose(<a href='?part=source&amp;class=org.springframework.boot.web.servlet.context.ServletWebServerApplicationContext#172' class='lightwindow' type='external' title='org.springframework.boot.web.servlet.context.ServletWebServerApplicationContext'>ServletWebServerApplicationContext.java:172</a>)<br/>org.springframework.context.support.AbstractApplicationContext.doClose(<a href='?part=source&amp;class=org.springframework.context.support.AbstractApplicationContext#1035' class='lightwindow' type='external' title='org.springframework.context.support.AbstractApplicationContext'>AbstractApplicationContext.java:1035</a>)<br/>org.springframework.context.support.AbstractApplicationContext.close(<a href='?part=source&amp;class=org.springframework.context.support.AbstractApplicationContext#978' class='lightwindow' type='external' title='org.springframework.context.support.AbstractApplicationContext'>AbstractApplicationContext.java:978</a>)<br/>org.springframework.boot.builder.ParentContextCloserApplicationListener$ContextCloserListener.onApplicationEvent(<a href='?part=source&amp;class=org.springframework.boot.builder.ParentContextCloserApplicationListener$ContextCloserListener#94' class='lightwindow' type='external' title='org.springframework.boot.builder.ParentContextCloserApplicationListener$ContextCloserListener'>ParentContextCloserApplicationListener.java:94</a>)<br/>org.springframework.boot.builder.ParentContextCloserApplicationListener$ContextCloserListener.onApplicationEvent(<a href='?part=source&amp;class=org.springframework.boot.builder.ParentContextCloserApplicationListener$ContextCloserListener#82' class='lightwindow' type='external' title='org.springframework.boot.builder.ParentContextCloserApplicationListener$ContextCloserListener'>ParentContextCloserApplicationListener.java:82</a>)<br/>org.springframework.context.event.SimpleApplicationEventMulticaster.doInvokeListener(<a href='?part=source&amp;class=org.springframework.context.event.SimpleApplicationEventMulticaster#172' class='lightwindow' type='external' title='org.springframework.context.event.SimpleApplicationEventMulticaster'>SimpleApplicationEventMulticaster.java:172</a>)<br/>org.springframework.context.event.SimpleApplicationEventMulticaster.invokeListener(<a href='?part=source&amp;class=org.springframework.context.event.SimpleApplicationEventMulticaster#165' class='lightwindow' type='external' title='org.springframework.context.event.SimpleApplicationEventMulticaster'>SimpleApplicationEventMulticaster.java:165</a>)<br/>org.springframework.context.event.SimpleApplicationEventMulticaster.multicastEvent(<a href='?part=source&amp;class=org.springframework.context.event.SimpleApplicationEventMulticaster#139' class='lightwindow' type='external' title='org.springframework.context.event.SimpleApplicationEventMulticaster'>SimpleApplicationEventMulticaster.java:139</a>)<br/>org.springframework.context.support.AbstractApplicationContext.publishEvent(<a href='?part=source&amp;class=org.springframework.context.support.AbstractApplicationContext#403' class='lightwindow' type='external' title='org.springframework.context.support.AbstractApplicationContext'>AbstractApplicationContext.java:403</a>)<br/>org.springframework.context.support.AbstractApplicationContext.publishEvent(<a href='?part=source&amp;class=org.springframework.context.support.AbstractApplicationContext#360' class='lightwindow' type='external' title='org.springframework.context.support.AbstractApplicationContext'>AbstractApplicationContext.java:360</a>)<br/>org.springframework.context.support.AbstractApplicationContext.doClose(<a href='?part=source&amp;class=org.springframework.context.support.AbstractApplicationContext#1012' class='lightwindow' type='external' title='org.springframework.context.support.AbstractApplicationContext'>AbstractApplicationContext.java:1012</a>)<br/>org.springframework.context.support.AbstractApplicationContext.close(<a href='?part=source&amp;class=org.springframework.context.support.AbstractApplicationContext#978' class='lightwindow' type='external' title='org.springframework.context.support.AbstractApplicationContext'>AbstractApplicationContext.java:978</a>)<br/>org.springframework.boot.devtools.restart.Restarter.stop(<a href='?part=source&amp;class=org.springframework.boot.devtools.restart.Restarter#309' class='lightwindow' type='external' title='org.springframework.boot.devtools.restart.Restarter'>Restarter.java:309</a>)<br/>org.springframework.boot.devtools.restart.Restarter.lambda$restart$1(<a href='?part=source&amp;class=org.springframework.boot.devtools.restart.Restarter#251' class='lightwindow' type='external' title='org.springframework.boot.devtools.restart.Restarter'>Restarter.java:251</a>)<br/>org.springframework.boot.devtools.restart.Restarter$$Lambda$1727/838484333.call(Unknown Source)<br/>org.springframework.boot.devtools.restart.Restarter$LeakSafeThread.run(<a href='?part=source&amp;class=org.springframework.boot.devtools.restart.Restarter$LeakSafeThread#629' class='lightwindow' type='external' title='org.springframework.boot.devtools.restart.Restarter$LeakSafeThread'>Restarter.java:629</a>)<br/></em>
Thread-35</div>
</td> <td align='center'>no</td> <td align='right'>5</td> <td><img src='?resource=bullets/green.png' alt='RUNNABLE'/>RUNNABLE</td> <td>java.lang.Thread.dumpThreads(Native Method)</td> <td align='right'>265</td> <td align='right'>140</td> <td align='center' class='noPrint'><a href='?action=send_thread_interrupt&amp;threadId=40272_***********_602' onclick="javascript:return confirm('Do you confirm the sending of a thread interrupt signal to Thread-35?');"><img width='16' height='16' src='?resource=action_interrupt.png' alt='Send a thread interrupt signal to Thread-35. The thread can test Thread.currentThread().isInterrupted() to stop itself.' title='Send a thread interrupt signal to Thread-35. The thread can test Thread.currentThread().isInterrupted() to stop itself.' /></a></td> <td align='center' class='noPrint'><a href='?action=kill_thread&amp;threadId=40272_***********_602' onclick="javascript:return confirm('杀掉一个Java线程是不推荐和不安全的，你确定要杀掉线程 Thread-35 ?');"><img width='16' height='16' src='?resource=stop.png' alt='杀掉线程&nbsp;Thread-35' title='杀掉线程&nbsp;Thread-35' /></a></td></tr></tbody></table>
<div align='right'>
服务器开始测量的CPU时间和线程时间
</div>
<div align='right' class='noPrint'><br/>
<a href='?part=threadsDump'><img src='?resource=text.png' alt='Dump threads as text'/>&nbsp;Dump threads as text</a>
&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;
<a href='?part=threads'><img src='?resource=threads.png' alt='线程' width='16' height='16'/>&nbsp;View in a new page</a>
</div>
</div><br/>
<script type='text/javascript'>
function toggle(id) {
var el = document.getElementById(id);
if (el.getAttribute('class') == 'menuHide') {
  el.setAttribute('class', 'menuShow');
} else {
  el.setAttribute('class', 'menuHide');
} }
</script>
<div class='noPrint'> 
<div id='menuBox' class='menuHide'>
  <ul id='menuTab'><li><a href='javascript:toggle("menuBox");'><img id='menuToggle' src='?resource=menu.png' alt='menu' /></a></li></ul>
  <div id='menuLinks'><div id='menuDeco'>
    <div class='menuButton'><a href='#top'>状态</a></div>
    <div class='menuButton'><a href='#http'>状态 http</a></div>
    <div class='menuButton'><a href='#sql'>状态 sql</a></div>
    <div class='menuButton'><a href='#spring'>状态 spring</a></div>
    <div class='menuButton'><a href='#error'>状态 error</a></div>
    <div class='menuButton'><a href='#log'>状态 log</a></div>
    <div class='menuButton'><a href='#currentRequests'>当前请求</a></div>
    <div class='menuButton'><a href='#systeminfo'>系统信息</a></div>
    <div class='menuButton'><a href='#threads'>线程</a></div>
  </div></div>
</div></div>
<a name='bottom'></a>
<br/><div style='font-size: 11px;'>
最后的收集的时间: 220 ms<br/>
显示时间: 62 ms<br/>
内存开销估计: < 1 MB
<br/>Disk usage: 2 MB
&nbsp;&nbsp;&nbsp;<a href='?action=purge_obsolete_files' class='noPrint'>
<img width='14' height='14' src='?resource=user-trash.png' alt='Purge the obsolete files' title='Purge the obsolete files'/></a>
<br/><br/>JavaMelody 1.76.0
<br/>
<a href="javascript:showHide('debuggingLogs');" class='noPrint'><img id='debuggingLogsImg' src='?resource=bullets/plus.png' alt=''/> Debugging logs</a>
<br/><br/>
<div id='debuggingLogs' style='display: none;'>
<div class='severe'>Only the last 50 messages are displayed</div>
Wed Jun 04 14:07:23 CST 2025&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;DEBUG&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;spring interceptor initialized<br/>
Wed Jun 04 14:07:24 CST 2025&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;DEBUG&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;Spring datasource wrapped: defaultDataSource<br/>
Wed Jun 04 14:07:53 CST 2025&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;DEBUG&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;JavaMelody listener init started<br/>
Wed Jun 04 14:07:53 CST 2025&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;DEBUG&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;datasources found in JNDI: []<br/>
Wed Jun 04 14:07:53 CST 2025&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;DEBUG&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;JavaMelody listener init done in 3 ms<br/>
Wed Jun 04 14:07:53 CST 2025&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;DEBUG&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;JavaMelody listener init started<br/>
Wed Jun 04 14:07:53 CST 2025&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;DEBUG&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;datasources found in JNDI: []<br/>
Wed Jun 04 14:07:53 CST 2025&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;DEBUG&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;JavaMelody listener init done in 0 ms<br/>
Wed Jun 04 14:07:53 CST 2025&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;DEBUG&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;JavaMelody filter init started<br/>
Wed Jun 04 14:07:53 CST 2025&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;DEBUG&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;OS: Windows 10 , amd64/64<br/>
Wed Jun 04 14:07:53 CST 2025&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;DEBUG&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;Java: Java(TM) SE Runtime Environment, 1.8.0_201-b09<br/>
Wed Jun 04 14:07:53 CST 2025&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;DEBUG&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;Server: Apache Tomcat/9.0.65<br/>
Wed Jun 04 14:07:53 CST 2025&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;DEBUG&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;Webapp context: /hnjjwz<br/>
Wed Jun 04 14:07:53 CST 2025&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;DEBUG&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;JavaMelody version: 1.76.0<br/>
Wed Jun 04 14:07:53 CST 2025&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;DEBUG&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;JavaMelody classes loaded from: file:/C:/Users/<USER>/.m2/repository/net/bull/javamelody/javamelody-core/1.76.0/javamelody-core-1.76.0.jar<br/>
Wed Jun 04 14:07:53 CST 2025&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;DEBUG&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;Application type: Classic<br/>
Wed Jun 04 14:07:53 CST 2025&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;DEBUG&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;Application version: null<br/>
Wed Jun 04 14:07:53 CST 2025&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;DEBUG&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;Host: DESKTOP-HUIVSCK@***********<br/>
Wed Jun 04 14:07:53 CST 2025&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;DEBUG&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;parameter defined: storage-directory=E:\晟壁\党风廉政视窗\simbest-boot-hnjjwz/springboottmp/hnjjwz<br/>
Wed Jun 04 14:07:53 CST 2025&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;DEBUG&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;log listeners initialized<br/>
Wed Jun 04 14:07:53 CST 2025&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;DEBUG&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;datasources found in JNDI: []<br/>
Wed Jun 04 14:07:53 CST 2025&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;DEBUG&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;counters initialized<br/>
Wed Jun 04 14:07:53 CST 2025&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;DEBUG&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;counters data read from files in E:\晟壁\党风廉政视窗\simbest-boot-hnjjwz\springboottmp\hnjjwz\hnjjwz_DESKTOP-HUIVSCK<br/>
Wed Jun 04 14:07:53 CST 2025&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;DEBUG&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;collect task scheduled every 60s<br/>
Wed Jun 04 14:07:53 CST 2025&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;DEBUG&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;first collect of data done<br/>
Wed Jun 04 14:07:53 CST 2025&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;DEBUG&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;JavaMelody filter init done in 809 ms<br/>
Wed Jun 04 14:15:53 CST 2025&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;DEBUG&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;A JRobin file was deleted and created again: E:\晟壁\党风廉政视窗\simbest-boot-hnjjwz\springboottmp\hnjjwz\hnjjwz_DESKTOP-HUIVSCK\usedMemory.rrd<br/>
Wed Jun 04 14:15:53 CST 2025&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;DEBUG&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;A JRobin file was deleted and created again: E:\晟壁\党风廉政视窗\simbest-boot-hnjjwz\springboottmp\hnjjwz\hnjjwz_DESKTOP-HUIVSCK\cpu.rrd<br/>
Wed Jun 04 14:15:53 CST 2025&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;DEBUG&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;A JRobin file was deleted and created again: E:\晟壁\党风廉政视窗\simbest-boot-hnjjwz\springboottmp\hnjjwz\hnjjwz_DESKTOP-HUIVSCK\httpSessions.rrd<br/>
Wed Jun 04 14:15:53 CST 2025&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;DEBUG&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;A JRobin file was deleted and created again: E:\晟壁\党风廉政视窗\simbest-boot-hnjjwz\springboottmp\hnjjwz\hnjjwz_DESKTOP-HUIVSCK\activeThreads.rrd<br/>
Wed Jun 04 14:15:53 CST 2025&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;DEBUG&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;A JRobin file was deleted and created again: E:\晟壁\党风廉政视窗\simbest-boot-hnjjwz\springboottmp\hnjjwz\hnjjwz_DESKTOP-HUIVSCK\activeConnections.rrd<br/>
Wed Jun 04 14:15:53 CST 2025&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;DEBUG&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;A JRobin file was deleted and created again: E:\晟壁\党风廉政视窗\simbest-boot-hnjjwz\springboottmp\hnjjwz\hnjjwz_DESKTOP-HUIVSCK\usedConnections.rrd<br/>
Wed Jun 04 14:15:53 CST 2025&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;DEBUG&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;A JRobin file was deleted and created again: E:\晟壁\党风廉政视窗\simbest-boot-hnjjwz\springboottmp\hnjjwz\hnjjwz_DESKTOP-HUIVSCK\gc.rrd<br/>
Wed Jun 04 14:15:53 CST 2025&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;DEBUG&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;A JRobin file was deleted and created again: E:\晟壁\党风廉政视窗\simbest-boot-hnjjwz\springboottmp\hnjjwz\hnjjwz_DESKTOP-HUIVSCK\threadCount.rrd<br/>
Wed Jun 04 14:15:53 CST 2025&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;DEBUG&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;A JRobin file was deleted and created again: E:\晟壁\党风廉政视窗\simbest-boot-hnjjwz\springboottmp\hnjjwz\hnjjwz_DESKTOP-HUIVSCK\loadedClassesCount.rrd<br/>
Wed Jun 04 14:15:53 CST 2025&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;DEBUG&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;A JRobin file was deleted and created again: E:\晟壁\党风廉政视窗\simbest-boot-hnjjwz\springboottmp\hnjjwz\hnjjwz_DESKTOP-HUIVSCK\usedBufferedMemory.rrd<br/>
Wed Jun 04 14:15:53 CST 2025&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;DEBUG&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;A JRobin file was deleted and created again: E:\晟壁\党风廉政视窗\simbest-boot-hnjjwz\springboottmp\hnjjwz\hnjjwz_DESKTOP-HUIVSCK\usedNonHeapMemory.rrd<br/>
Wed Jun 04 14:15:53 CST 2025&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;DEBUG&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;A JRobin file was deleted and created again: E:\晟壁\党风廉政视窗\simbest-boot-hnjjwz\springboottmp\hnjjwz\hnjjwz_DESKTOP-HUIVSCK\usedPhysicalMemorySize.rrd<br/>
Wed Jun 04 14:15:53 CST 2025&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;DEBUG&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;A JRobin file was deleted and created again: E:\晟壁\党风廉政视窗\simbest-boot-hnjjwz\springboottmp\hnjjwz\hnjjwz_DESKTOP-HUIVSCK\usedSwapSpaceSize.rrd<br/>
Wed Jun 04 14:15:53 CST 2025&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;DEBUG&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;A JRobin file was deleted and created again: E:\晟壁\党风廉政视窗\simbest-boot-hnjjwz\springboottmp\hnjjwz\hnjjwz_DESKTOP-HUIVSCK\systemCpuLoad.rrd<br/>
Wed Jun 04 14:15:53 CST 2025&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;DEBUG&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;A JRobin file was deleted and created again: E:\晟壁\党风廉政视窗\simbest-boot-hnjjwz\springboottmp\hnjjwz\hnjjwz_DESKTOP-HUIVSCK\httpSessionsMeanAge.rrd<br/>
Wed Jun 04 14:15:53 CST 2025&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;DEBUG&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;A JRobin file was deleted and created again: E:\晟壁\党风廉政视窗\simbest-boot-hnjjwz\springboottmp\hnjjwz\hnjjwz_DESKTOP-HUIVSCK\transactionsRate.rrd<br/>
Wed Jun 04 14:15:53 CST 2025&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;DEBUG&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;A JRobin file was deleted and created again: E:\晟壁\党风廉政视窗\simbest-boot-hnjjwz\springboottmp\hnjjwz\hnjjwz_DESKTOP-HUIVSCK\Free_disk_space.rrd<br/>
Wed Jun 04 14:15:53 CST 2025&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;DEBUG&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;A JRobin file was deleted and created again: E:\晟壁\党风廉政视窗\simbest-boot-hnjjwz\springboottmp\hnjjwz\hnjjwz_DESKTOP-HUIVSCK\httpHitsRate.rrd<br/>
Wed Jun 04 14:15:53 CST 2025&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;DEBUG&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;A JRobin file was deleted and created again: E:\晟壁\党风廉政视窗\simbest-boot-hnjjwz\springboottmp\hnjjwz\hnjjwz_DESKTOP-HUIVSCK\sqlHitsRate.rrd<br/>
Wed Jun 04 14:15:53 CST 2025&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;DEBUG&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;A JRobin file was deleted and created again: E:\晟壁\党风廉政视窗\simbest-boot-hnjjwz\springboottmp\hnjjwz\hnjjwz_DESKTOP-HUIVSCK\sqlMeanTimes.rrd<br/>
Wed Jun 04 14:15:53 CST 2025&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;DEBUG&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;A JRobin file was deleted and created again: E:\晟壁\党风廉政视窗\simbest-boot-hnjjwz\springboottmp\hnjjwz\hnjjwz_DESKTOP-HUIVSCK\sqlSystemErrors.rrd<br/>
Wed Jun 04 14:15:53 CST 2025&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;DEBUG&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;A JRobin file was deleted and created again: E:\晟壁\党风廉政视窗\simbest-boot-hnjjwz\springboottmp\hnjjwz\hnjjwz_DESKTOP-HUIVSCK\springHitsRate.rrd<br/>
Wed Jun 04 14:15:53 CST 2025&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;DEBUG&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;A JRobin file was deleted and created again: E:\晟壁\党风廉政视窗\simbest-boot-hnjjwz\springboottmp\hnjjwz\hnjjwz_DESKTOP-HUIVSCK\springMeanTimes.rrd<br/>
Wed Jun 04 14:15:53 CST 2025&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;DEBUG&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;A JRobin file was deleted and created again: E:\晟壁\党风廉政视窗\simbest-boot-hnjjwz\springboottmp\hnjjwz\hnjjwz_DESKTOP-HUIVSCK\springSystemErrors.rrd<br/>
</div>
</div>
</body></html>
