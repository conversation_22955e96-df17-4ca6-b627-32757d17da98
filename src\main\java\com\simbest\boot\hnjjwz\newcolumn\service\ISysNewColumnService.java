package com.simbest.boot.hnjjwz.newcolumn.service;

import com.simbest.boot.base.service.ILogicService;
import com.simbest.boot.base.web.response.JsonResponse;
import com.simbest.boot.hnjjwz.newcolumn.model.SysNewColumnModel;

public interface ISysNewColumnService extends ILogicService<SysNewColumnModel,String> {
    JsonResponse createnewcolumn(SysNewColumnModel model);

    JsonResponse findAllByUser();

    JsonResponse findAllArticleByColumnId(String columnId);

    JsonResponse findColumnByArticleID(String columnId);

    JsonResponse findArticlePageByColumnId(String columnId, int page, int size);

    JsonResponse findAnnouncementPageByColumnId(String columnId, int page, int size);

    JsonResponse getDataArticleById(String columnId, String articleId);

    SysNewColumnModel findOneByAllColumnName(String programaDisplayName);

    JsonResponse recommendedArticle(String pmInsId);
}
