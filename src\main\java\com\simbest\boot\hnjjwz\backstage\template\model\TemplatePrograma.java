package com.simbest.boot.hnjjwz.backstage.template.model;

import com.simbest.boot.base.annotations.EntityIdPrefix;
import com.simbest.boot.base.model.LogicModel;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.*;
import org.hibernate.annotations.GenericGenerator;

import javax.persistence.Column;
import javax.persistence.Entity;
import javax.persistence.GeneratedValue;
import javax.persistence.Id;

/**
 * <AUTHOR>
 * @Data 2019/05/06
 * Description 模板栏目表
 */
@Data
@EqualsAndHashCode(callSuper = true)
@NoArgsConstructor
@AllArgsConstructor
@Entity(name = "us_template_programa")
@ApiModel(value = "模板栏目")
public class TemplatePrograma extends LogicModel {
    @Id
    @Column(name = "id", length = 40)
    @GeneratedValue(generator = "snowFlakeId")
    @GenericGenerator (name = "snowFlakeId", strategy = "com.simbest.boot.util.distribution.id.SnowflakeId")
    @EntityIdPrefix (prefix = "UTP") //主键前缀，此为可选项注解
    private String id;

    @Column(length = 200)
    @Setter
    @Getter
    @ApiModelProperty(value = "模板编码", required = true)
    private String templateCode;

    @Column(length = 200)
    @Setter
    @Getter
    @ApiModelProperty(value = "模板布局id", required = true)
    private String templateLayoutId;

    @Column(length = 200)
    @Setter
    @Getter
    @ApiModelProperty(value = "栏目编码", required = true)
    private String programaCode;

    @Column(length = 250)
    @Setter
    @Getter
    @ApiModelProperty(value = "额外字段1", required = true)
    private String spare1;

    @Column(length = 250)
    @Setter
    @Getter
    @ApiModelProperty(value = "额外字段2", required = true)
    private String spare2;

    @Column(length = 250)
    @Setter
    @Getter
    @ApiModelProperty(value = "额外字段3", required = true)
    private String spare3;



}
