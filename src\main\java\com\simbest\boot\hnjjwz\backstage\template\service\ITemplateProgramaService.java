package com.simbest.boot.hnjjwz.backstage.template.service;


import com.simbest.boot.base.service.ILogicService;
import com.simbest.boot.base.web.response.JsonResponse;
import com.simbest.boot.hnjjwz.backstage.template.model.TemplatePrograma;
import org.springframework.data.domain.Pageable;

import java.util.Map;

/**
 * <strong>Title : </strong><br>
 * <strong>Description : </strong><br>
 * <strong>Create on : 2018/6/19</strong><br>
 * <strong>Modify on : 2018/6/19</strong><br>
 * <strong>Copyright (C) Ltd.</strong><br>
 *
 * <AUTHOR> 刘萌@simbest.com.cn
 * @version <strong>V1.0.0</strong><br>
 * <strong>修改历史:</strong><br>
 * 修改人 修改日期 修改描述<br>
 * -------------------------------------------<br>
 */
public interface ITemplateProgramaService extends ILogicService<TemplatePrograma,String> {

    /**
     * 查询模板与栏目的列表并分页
     * @param mapObject
     * @param pageable
     * @return
     */
    JsonResponse findAllDim( Map<String,Object> mapObject, Pageable pageable);

    /**
     * 根据id查询模板与栏目的关联
     * @param id
     * @return
     */
    JsonResponse findByIdDim(String id);
}
