package com.simbest.boot.hnjjwz.backstage.programa.model;

import com.simbest.boot.base.annotations.EntityIdPrefix;
import com.simbest.boot.base.model.LogicModel;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.*;
import org.hibernate.annotations.GenericGenerator;
import org.hibernate.annotations.Table;

import javax.persistence.*;
import java.math.BigDecimal;

/**
 * <AUTHOR>
 * @Data 2019/03/29
 * Description 栏目分类信息。每一个分类就相当于是栏目详细信息表中的一个详细信息，父栏目为空。
 */
@Data
@EqualsAndHashCode(callSuper = true)
@NoArgsConstructor
@AllArgsConstructor
@Entity (name = "us_programa_classify_info")
@Table ( appliesTo="us_programa_classify_info",comment="栏目分类信息" )
@ApiModel(value = "栏目分类信息")
public class ProgramaClassifyInfo extends LogicModel{
    @Id
    @Column(name = "id", length = 40)
    @GeneratedValue(generator = "snowFlakeId")
    @GenericGenerator (name = "snowFlakeId", strategy = "com.simbest.boot.util.distribution.id.SnowflakeId")
    @EntityIdPrefix (prefix = "CAT") //主键前缀，此为可选项注解
    private String id;

    @Setter
    @Getter
    @ApiModelProperty(value = "栏目分类名称", required = true)
    private String programaClassifyName;

    @Setter
    @Getter
    @ApiModelProperty(value = "栏目类型", required = true)
    private String programaType;//用于区分唯一的栏目，子栏目的封面为图片的栏目，以及公告之类的栏目。放在数据字典中。

    @Transient
    private String programaTypeName;//栏目类型名称

    @Setter
    @Getter
    @ApiModelProperty(value = "栏目排序", required = true)
    private BigDecimal displayOrder;

    @Setter
    @Getter
    @ApiModelProperty(value = "额外字段1", required = true)
    private String spare1;

    @Setter
    @Getter
    @ApiModelProperty(value = "额外字段2", required = true)
    private String spare2;

    @Setter
    @Getter
    @ApiModelProperty(value = "额外字段3", required = true)
    private String spare3;
}
