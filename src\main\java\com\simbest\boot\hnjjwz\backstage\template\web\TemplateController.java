package com.simbest.boot.hnjjwz.backstage.template.web;

import com.simbest.boot.base.exception.Exceptions;
import com.simbest.boot.base.web.controller.LogicController;
import com.simbest.boot.base.web.response.JsonResponse;
import com.simbest.boot.hnjjwz.backstage.template.model.Template;
import com.simbest.boot.hnjjwz.backstage.template.service.ITemplateService;
import io.swagger.annotations.Api;
import io.swagger.annotations.ApiImplicitParam;
import io.swagger.annotations.ApiImplicitParams;
import io.swagger.annotations.ApiOperation;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.data.domain.Pageable;
import org.springframework.web.bind.annotation.*;

import java.util.List;
import java.util.Map;

/**
 * <AUTHOR>
 * @Data 2019/04/02
 * @Description 模板
 */
@Api(description = "模板")
@Slf4j
@RestController
@RequestMapping(value = "/action/template")
public class TemplateController extends LogicController<Template, String> {

    @Autowired
    private ITemplateService templateService;

    @Autowired
    public TemplateController(ITemplateService  templateService) {
        super(templateService);
        this.templateService = templateService;
    }

    /**
     * 根据模板名称(模糊)、是否使用此模板(精确)
     * @return
     */
    @ApiOperation (value = "根据模板名称(模糊)、是否使用此模板(精确)", notes = "根据模板名称(模糊)、是否使用此模板(精确)")
    @ApiImplicitParams ({ //
            @ApiImplicitParam (name = "page", value = "当前页码", dataType = "int", paramType = "query", //
                    required = true, example = "1"), //
            @ApiImplicitParam(name = "size", value = "每页数量", dataType = "int", paramType = "query", //
                    required = true, example = "10"), //
            @ApiImplicitParam(name = "direction", value = "排序规则（asc/desc）", dataType = "String", //
                    paramType = "query"), //
            @ApiImplicitParam(name = "properties", value = "排序规则（属性名称）", dataType = "String", //
                    paramType = "query")
    })
    @PostMapping (value = {"/findAllDim","/findAllDim/sso"})
    public JsonResponse findAllDim( @RequestParam (required = false, defaultValue = "1") int page, //
                                    @RequestParam(required = false, defaultValue = "10") int size, //
                                    @RequestParam(required = false) String direction, //
                                    @RequestParam(required = false) String properties,
                                    @RequestBody (required = false) Map<String,Object> mapObject ) {
        Pageable pageable = templateService.getPageable(page, size, direction, properties);
        return templateService.findAllDim( mapObject,pageable);
    }

    /**
     * 设置某一模板使用
     * @param id
     * @return
     */
    @ApiOperation (value = "设置某一模板使用", notes = "设置某一模板使用")
    @ApiImplicitParam (name = "id", value = "模板ID", dataType = "String", paramType = "query")
    @PostMapping (value = {"/setTemplateUse","/setTemplateUse/sso"})
    public JsonResponse setTemplateUse( @RequestParam (required = false) String id) {
        //设置某一模板可用，其他模板不可用
        try {
            Template template = templateService.findById( id );
            template.setTemplateUse( true );
            super.update(template);
           /* List<Template>  templateList = ( List<Template> )templateService.findAllNoPage();
            for(Template templateOther:templateList){
                if(templateOther!=null && !templateOther.getId().equals(id)){
                    templateOther.setTemplateUse( false );
                    super.update(templateOther);
                }
            }*/
            return JsonResponse.success( "设置成功！" );
        }catch ( Exception e ){
            Exceptions.printException( e );
            return JsonResponse.fail( "设置失败！" );
        }
    }

    /**
     * 获取可以使用的模板，如果有一个则只取一个
     * @return
     */
    @ApiOperation (value = "获取可以使用的模板，如果有一个则只取一个", notes = "获取可以使用的模板，如果有一个则只取一个")
    @PostMapping (value = {"/getTemplateUse","/getTemplateUse/sso"})
    public JsonResponse getTemplateUse( ) {
        return templateService.getTemplateUse();
    }

}
