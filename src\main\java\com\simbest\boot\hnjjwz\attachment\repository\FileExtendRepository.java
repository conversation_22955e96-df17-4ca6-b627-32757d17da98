package com.simbest.boot.hnjjwz.attachment.repository;

import com.simbest.boot.base.repository.LogicRepository;
import com.simbest.boot.sys.model.SysFile;
import org.springframework.data.jpa.repository.Modifying;
import org.springframework.data.jpa.repository.Query;
import org.springframework.data.repository.query.Param;
import org.springframework.stereotype.Repository;

import java.beans.Transient;
import java.util.List;

@Repository
public interface FileExtendRepository  extends LogicRepository<SysFile,String> {

    @Transient
    @Modifying
    @Query(
            value = "update  SYS_FILE t set t.pm_ins_id =:pmInsId,t.pm_ins_type=:pmInsType,t.pm_ins_type_part = :pmInsTypePart,t.enabled=1,t.removed_time=null where t.id =:id",
            nativeQuery = true
    )
    int updatePmInsId(@Param("pmInsId") String pmInsId, @Param("pmInsType") String pmInsType, @Param("id") String id,@Param( "pmInsTypePart" ) String pmInsTypePart);

    @Transient
    @Modifying
    @Query(
            value = "update  SYS_FILE t set t.pm_ins_id =:pmInsId , t.pm_ins_type_part = :pmInsTypePart where t.id = :id",
            nativeQuery = true
    )
    int updatePmInsIdPart ( @Param ( "pmInsId" ) String pmInsId, @Param ( "id" ) String id, @Param( "pmInsTypePart" ) String pmInsTypePart );

    @Transient
    @Query(
            value = "select * from SYS_FILE t where t.pm_ins_id =:pmInsId and t.removed_time is null and t.pm_ins_type_part = :typePart",
            nativeQuery = true
    )
    List<SysFile> getPartFile ( @Param ( "pmInsId" ) String pmInsId, @Param ( "typePart" ) String typePart );

    @Transient
    @Query(
            value = "select * from SYS_FILE t where t.pm_ins_id =:pmInsId and t.removed_time is null",
            nativeQuery = true
    )
    List<SysFile> getFile( @Param ( "pmInsId" ) String pmInsId);

    @Modifying
    @Query(value = "update sys_file t set t.enabled=0,t.removed_time=SYSDATE where t.pm_ins_id =:pmInsId",nativeQuery = true)
    int deleteByPmInsId(@Param("pmInsId") String pmInsId);
}
