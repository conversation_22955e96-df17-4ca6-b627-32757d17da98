/*
 * 版权所有 © 北京晟壁科技有限公司 2008-2027。保留一切权利!
 */
package com.simbest.boot.hnjjwz.examOnline.service.impl;

import com.google.common.collect.Lists;
import com.simbest.boot.base.exception.Exceptions;
import com.simbest.boot.base.web.response.JsonResponse;
import com.simbest.boot.hnjjwz.constants.Constants;
import com.simbest.boot.hnjjwz.examOnline.service.IExamSelectService;
import com.simbest.boot.security.IUser;
import com.simbest.boot.security.SimpleConfig;
import com.simbest.boot.util.encrypt.RsaEncryptor;
import com.simbest.boot.util.http.client.HttpClient;
import com.simbest.boot.util.json.JacksonUtils;
import com.simbest.boot.util.security.SecurityUtils;
import com.simbest.boot.uums.api.app.UumsSysAppConfigApi;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.context.annotation.DependsOn;
import org.springframework.stereotype.Service;

import javax.annotation.PostConstruct;
import java.util.List;

/**
 * <strong>Title : ExamSelectServiceImpl</strong><br>
 * <strong>Description : 成绩查询Service </strong><br>
 * <strong>Create on : 2020/11/12</strong><br>
 * <strong>Modify on : 2020/11/12</strong><br>
 * <strong>Copyright SimBest(C) Ltd.</strong><br>
 *
 * @<NAME_EMAIL>
 * @version <strong>V1.0.0</strong><br>
 * <strong>修改历史:</strong><br>
 * 修改人 修改日期 修改描述<br>
 * -------------------------------------------<br>
 */
@Service
@Slf4j
@DependsOn({"httpClient"})
public class ExamSelectServiceImpl implements IExamSelectService {

    @Autowired
    private UumsSysAppConfigApi uumsSysAppConfigApi;

    private SimpleConfig simpleConfig;

    @Autowired
    private RsaEncryptor rsaEncryptor;

    @PostConstruct
    private void init(){
        simpleConfig = uumsSysAppConfigApi.findAppConfigByStyle( Constants.TARGET_APP_CODE,Constants.ADMIN_USERNAEM, Constants.APP_CODE);
    }

    @Value( "${app.host.port}" )
    public String hostPost;

    @Override
    public JsonResponse findByExamInfoByUsername(String username) {
        JsonResponse jsonResponse = null;
        try {
            IUser user = SecurityUtils.getCurrentUser();
            String currUserName = user.getUsername();
            //jsonResponse = HttpClient.post( simpleConfig.getAddress() + Constants.HNJJWZ_NTXL_QUERY_EXAM_INFO +  "?loginuser=" + rsaEncryptor.encrypt( currUserName ) + "&appcode=" + Constants.APP_CODE)
            //jsonResponse = HttpClient.post( "http://************:8084/exam" + Constants.HNJJWZ_NTXL_QUERY_EXAM_INFO +  "?loginuser=" + rsaEncryptor.encrypt( currUserName ) + "&appcode=" + Constants.APP_CODE)
            jsonResponse = HttpClient.post( "http://*************:8088/exam" + Constants.HNJJWZ_NTXL_QUERY_EXAM_INFO +  "?loginuser=" + rsaEncryptor.encrypt( currUserName ) + "&appcode=" + Constants.APP_CODE)
                    .param("username",username)
                    .asBean( JsonResponse.class );
            int ret = jsonResponse.getErrcode();
            if ( ret != 0 ){
                jsonResponse = JsonResponse.fail( null,"成绩查询接口失败" );
                return jsonResponse;
            }
        }catch ( Exception e ){
            Exceptions.printException(new Exception( "成绩查询接口异常！",e.getCause() ));
        }
        return jsonResponse;

    }

    /**
     * 获取考试汇总权限
     *
     * @param examCode 考试编码
     * @return         返回考试权限相关数据
     */
    @Override
    public JsonResponse findEffectiveExam(String examCode) {
        JsonResponse jsonResponse = null;
        try {
            IUser user = SecurityUtils.getCurrentUser();
            String currUserName = user.getUsername();
            List<String> examCodes = Lists.newArrayList();
            examCodes.add(examCode);
            jsonResponse = HttpClient
                    .textBody(hostPost + "/exam" + Constants.HNJJWZ_NTXL_QUERY_EXAM_EFFECTIVE +  "?loginuser=" + rsaEncryptor.encrypt(currUserName) + "&appcode=" + Constants.APP_CODE)
                    .json(JacksonUtils.obj2json(examCodes))
                    .asBean(JsonResponse.class);
            int ret = jsonResponse.getErrcode();
            if ( ret != 0 ){
                jsonResponse = JsonResponse.fail( null,"考试权限查询接口失败" );
                return jsonResponse;
            }
        }catch ( Exception e ){
            Exceptions.printException(new Exception( "考试权限查询接口异常！",e.getCause()));
        }
        return jsonResponse;
    }
}
