<!DOCTYPE html>
<html xmlns="http://www.w3.org/1999/xhtml"
      xmlns:th="http://www.thymeleaf.org">
<head>
    <title>列表-清廉移动</title>
    <meta http-equiv="Content-Type" content="text/html; charset=utf-8"/>
    <meta http-equiv="X-UA-Compatible" content="IE=edge" />
    <meta name="viewport" content="width=device-width, initial-scale=1, shrink-to-fit=no"/>
    <!--
       <link href="http://************:8088/simbestui/fonts/iconfont/iconfont.css?v=svn.revision" th:href="@{http://************:8088/simbestui/fonts/iconfont/iconfont.css?v=svn.revision}" rel="stylesheet"/>
       -->
    <link href="../../fonts/iconfont/iconfont.css?v=svn.revision" th:href="@{/fonts/iconfont/iconfont.css?v=svn.revision}" rel="stylesheet"/>
    <link href="http://************:8088/simbestui/js/themes/default/easyui.css?v=svn.revision" th:href="@{http://************:8088/simbestui/js/themes/default/easyui.css?v=svn.revision}" rel="stylesheet"/>
    <link href="http://************:8088/simbestui/js/themes/icon.css?v=svn.revision" th:href="@{http://************:8088/simbestui/js/themes/icon.css?v=svn.revision}" rel="stylesheet"/>
    <link href="http://************:8088/simbestui/css/public.css?v=svn.revision" th:href="@{http://************:8088/simbestui/css/public.css?v=svn.revision}" rel="stylesheet"/>
    <link href="http://************:8088/simbestui/css/pages.css?v=svn.revision" th:href="@{http://************:8088/simbestui/css/pages.css?v=svn.revision}" rel="stylesheet"/>
    <link href="../../css/webSite.css?v=svn.revision" th:href="@{/css/webSite.css?v=svn.revision}" rel="stylesheet"/>
    <script src="http://************:8088/simbestui/js/jquery.min.js?v=svn.revision" th:src="@{http://************:8088/simbestui/js/jquery.min.js?v=svn.revision}" type="text/javascript"></script>
    <script src="http://************:8088/simbestui/js/jquery.easyui.min.js?v=svn.revision" th:src="@{http://************:8088/simbestui/js/jquery.easyui.min.js?v=svn.revision}" type="text/javascript"></script>
    <script src="http://************:8088/simbestui/js/easyui-lang-zh_CN.js?v=svn.revision" th:src="@{http://************:8088/simbestui/js/easyui-lang-zh_CN.js?v=svn.revision}" type="text/javascript"></script>
    <script src="../../js/jquery.config.js?v=svn.revision" th:src="@{/js/jquery.config.js?v=svn.revision}" type="text/javascript"></script>
    <script src="http://************:8088/simbestui/js/jquery.zjsfile.js?v=svn.revision" th:src="@{http://************:8088/simbestui/js/jquery.zjsfile.js?v=svn.revision}" type="text/javascript"></script>
    <script src="http://************:8088/simbestui/js/jquery.aduq.js?v=svn.revision" th:src="@{http://************:8088/simbestui/js/jquery.aduq.js?v=svn.revision}" type="text/javascript"></script>
    <script src="http://************:8088/simbestui/js/jquery.process.js?v=svn.revision" th:src="@{http://************:8088/simbestui/js/jquery.process.js?v=svn.revision}" type="text/javascript"></script>
    <script src="../../js/webSite.js?v=svn.revision" th:src="@{/js/webSite.js?v=svn.revision}" type="text/javascript"></script>
    <script type="text/javascript">
        $(function(){
            webSite();
            getData();
        });
        function getData(page){
            var data={"programaCode":gps.id,"page":page || 1,"size":20};
            var url=tourl("action/approvalForm/findDataDetailList",data);
            //console.log(gps.id);
            if(gps.id==999){
                 data={"page":page || 1,"size":20};
                 url=tourl("action/announcement/findAll",data);
            }

            ajaxgeneral({
                url: url,
                data:data,
                contentType:"application/json;charset=UTF-8",
                success: function (res) {
                    if(res.data.nav!=undefined){
                        /**
                         *普通渲染
                         */
                        var html=["<img class='mr5' src='../../images/er1.jpg'/><a target='_blank' href='index.html'>首页</a>"];
                        if(res.data.nav && res.data.nav.length>0){

                            var titN=res.data.nav[0].programaDisplayName.split("/");

                            if(res.data.nav[0].pointUrl!=null){
                                var titU=res.data.nav[0].pointUrl.split("/");
                            }else {
                                var titU="list.html";
                            }
                            for (var i = 0; i < res.data.nav[0].locationName.length; i += 3) {

                                var titI={"id": res.data.nav[0].locationName.substr(0, i+3), "name": titN[i / 3],"url":(titU[i / 3]=="null"?"list.html":titU[i / 3])};//[i / 3]

                                if(((i+3) / 3)>titU.length-1) {

                                    //console.log(titI);
                                    html.push(fastrenderRow(titI, "><a target='_blank'  href='{{url}}?id={{id}}'>{{name}}</a>"));
                                    //$("#h6").html(titI.name+"-清廉移动");
                                }else
                                    html.push(fastrenderRow(titI,"><a target='_blank' href='{{url}}?id={{id}}'>{{name}}</a>"));
                                    //$("#h6").html(titI.name+"-清廉移动");
                            }
                        }
                        $(".navTit").html(html.join(""));
                        if(res.data.contentDetial.length>0)
                            $("ul.list_square").html(fastrender(res.data.contentDetial,"<li><a target='_blank' href='listDetails.html?pmInsId={{PM_INS_ID}}'>{{TITLE}}<font class=\"fr\">{{CREATION_TIME}}</font></a></li>"));//[{{BELONG_DEPARTMENT_NAME}}]
                        else
                            $("#pages").html("<p>暂无数据！</p>");
                        if(!page && res.data.contentDetial.length>0) {
                            $("#pages").pagination({
                                total: res.data.totalElements,
                                pageSize: 20,
                                pageNumber:10,
                                layout: ["sep", "first", "prev", "links", "next", "last"],
                                displayMsg: '',
                                onSelectPage: function (num, size) {
                                    getData(num);
                                }
                            });
                            // 刷新当前页
                            $('#pages').pagination('select', 1);
                        }

                    }else {
                        /**
                         *公告渲染
                         **/
                        for(var i in res.data.content){
                            var html=["<img class='mr5' src='../../images/er1.jpg'/><a target='_blank' href='index.html'>首页</a>"];
                            if(res.data.content ){
                                var titN=res.data.content[0].programaDisplayName;
                                var titU="list.html";
                                var titI={"id": "999", "name": titN,"url":(titU=="null"?"list.html":titU)};//[i / 3]
                                html.push(fastrenderRow(titI,"><a target='_blank' href='{{url}}?id={{id}}'>{{name}}</a>"));
                                //$("#h6").html(titI.name+"-清廉移动");
                            }
                            $(".navTit").html(html.join(""));

                            if(res.data.content.length>0)
                                $("ul.list_square").html(fastrender(res.data.content,"<li><a target='_blank' href='listDetails.html?pmInsId={{pmInsId}}&notice=1'>{{announcementTitle}}<font class=\"fr\">{{createdTime}}</font></a></li>"));//[{{belongDepartmentName}}]
                            else
                                $("#pages").html("<p>暂无数据！</p>");
                            if(!page && res.data.content.length>0) {
                                $("#pages").pagination({
                                    total: res.data.totalElements,
                                    pageSize: 20,
                                    pageNumber:10,
                                    layout: ["sep", "first", "prev", "links", "next", "last"],
                                    displayMsg: '',
                                    onSelectPage: function (num, size) {
                                        getData(num);
                                    }
                                });
                                // 刷新当前页
                                $('#pages').pagination('select', 1);
                            }

                        }
                    }


                }
            });
        };
    </script>
</head>
<body>
<!--top-->

<div class="nav">
    <ul class="auto1024">
        <!--<li><a>首页</a></li>
        <li><a>信息公开</a></li>
        <li class="li_hover"><a>规章制度</a></li>
        <li><a>巡察工作</a></li>
        <li><a>嵌入式防控监督</a></li>
        <li><a>课题研究</a></li>
        <li><a>共享平台</a></li>
        <li>
            <a>数据报送</a>
            <ul>
                <li><a>领导人员廉洁情况活页夹</a></li>
                <li><a>信息报送</a></li>
            </ul>
        </li>
        <li class="nobor"><a>信访举报</a></li>-->
    </ul>
</div>
<!--center-->
<div class="auto1024 center">
    <div class="navTit"><!--<strong class="fl">中央及地方纪委</strong>--></div>
    <ul class="list list_square">
        <!--<li><a>中共中央办公厅印发《中国共产党纪律检查机关监督执纪工作规则》 [纪检监察室（巡察工作办公室）]<font class="fr">2019-01-01 11:20:22</font>
        </a></li>-->
    </ul>
    <div id="pages" style="background:#fff;text-align:center;margin:20px 0;"></div>
</div>
<!--copy-->
<div class="copy">©版权所有 中国移动通信集团河南有限公司</div>
</body>
</html>
