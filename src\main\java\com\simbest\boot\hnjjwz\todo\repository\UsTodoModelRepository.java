package com.simbest.boot.hnjjwz.todo.repository;


import com.simbest.boot.base.repository.LogicRepository;
import com.simbest.boot.hnjjwz.todo.model.UsTodoModel;
import org.springframework.data.jpa.repository.Modifying;
import org.springframework.data.jpa.repository.Query;

/**
 * <strong>Title : UsTodoModelRepository</strong><br>
 * <strong>Description : 业务待办数据库操作</strong><br>
 * <strong>Create on : $date$</strong><br>
 * <strong>Modify on : $date$</strong><br>
 * <strong>Copyright (C) Ltd.</strong><br>
 *
 * <AUTHOR> liji<PERSON><EMAIL>
 * @version <strong>V1.0.0</strong><br>
 * <strong>修改历史:</strong><br>
 * 修改人 修改日期 修改描述<br>
 * -------------------------------------------<br>
 */
public interface UsTodoModelRepository extends LogicRepository<UsTodoModel,String> {

    /**
     * 根据工作项ID更新待办状态为已办
     */
    String updateSql1 = "update US_TODO_MODEL t set t.work_flag=1,t.removed_time=sysdate,t.type_status=?2 where t.work_item_id=?1";
    @Modifying
    @Query (value = updateSql1,nativeQuery = true)
    int updateWorkFlagByWorkItemId(String workItemId,String typeStatus);
}
