﻿//声明字典命名空间
if(!window.web){ web={}; }
if(!window.dictionary){ dictionary={}; }
if(!window.dictionarys){ dictionarys={}; }
web.appCode="hnjjwz";
//ABC代表的是流程类型,后边的是对应的需要跳转的页面路径
web.appHtml = {
    "A": "html/apply/programaDataFormTabs.html",
    "B": "html/apply/programaDataFormTabs.html",
    "C": "html/announcement/announcementList.html",
    "D": "html/announcement/announcementList.html",
    "E": "html/slideShow/slideShowList.html",
    "F": "html/slideShow/slideShowList.html",
    "G": "html/taskStudy/TaskStudy.html",
    "H": "html/taskStudy/TaskStudy.html"

};
//ABC代表的是流程类型,后边的是对应的是流程名，会显示在详情页面的头部
web.appName = {"A": "省公司信息报送流程","B": "市公司信息报送流程", "C": "省公司公告报送流程","D": "市公司公告报送流程", "E": "省公司轮播图报送流程","F": "市公司轮播图报送流程", "G": "省公司课题研究流程","H": "市公司课题研究流程"};
web.procesType = "pmInsType-流程类型";