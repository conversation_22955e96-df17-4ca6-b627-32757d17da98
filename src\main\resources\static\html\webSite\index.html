
<!DOCTYPE html>
<html xmlns="http://www.w3.org/1999/xhtml"
      xmlns:th="http://www.thymeleaf.org">
<head>
    <title>首页-清廉移动</title>
    <meta http-equiv="Content-Type" content="text/html; charset=utf-8"/>
    <meta http-equiv="X-UA-Compatible" content="IE=edge" />
    <meta name="viewport" content="width=device-width, initial-scale=1, shrink-to-fit=no"/>
    <!--
       <link href="http://************:8088/simbestui/fonts/iconfont/iconfont.css?v=svn.revision" th:href="@{http://************:8088/simbestui/fonts/iconfont/iconfont.css?v=svn.revision}" rel="stylesheet"/>
       -->
    <link href="../../fonts/iconfont/iconfont.css?v=svn.revision" th:href="@{/fonts/iconfont/iconfont.css?v=svn.revision}" rel="stylesheet"/>
    <link href="http://************:8088/simbestui/js/themes/default/easyui.css?v=svn.revision" th:href="@{http://************:8088/simbestui/js/themes/default/easyui.css?v=svn.revision}" rel="stylesheet"/>
    <link href="http://************:8088/simbestui/js/themes/icon.css?v=svn.revision" th:href="@{http://************:8088/simbestui/js/themes/icon.css?v=svn.revision}" rel="stylesheet"/>
    <link href="http://************:8088/simbestui/css/public.css?v=svn.revision" th:href="@{http://************:8088/simbestui/css/public.css?v=svn.revision}" rel="stylesheet"/>
    <link href="http://************:8088/simbestui/css/pages.css?v=svn.revision" th:href="@{http://************:8088/simbestui/css/pages.css?v=svn.revision}" rel="stylesheet"/>
    <link href="../../css/webSite.css?v=svn.revision" th:href="@{/css/webSite.css?v=svn.revision}" rel="stylesheet"/>
    <script src="http://************:8088/simbestui/js/jquery.min.js?v=svn.revision" th:src="@{http://************:8088/simbestui/js/jquery.min.js?v=svn.revision}" type="text/javascript"></script>
    <script src="http://************:8088/simbestui/js/jquery.easyui.min.js?v=svn.revision" th:src="@{http://************:8088/simbestui/js/jquery.easyui.min.js?v=svn.revision}" type="text/javascript"></script>
    <script src="http://************:8088/simbestui/js/easyui-lang-zh_CN.js?v=svn.revision" th:src="@{http://************:8088/simbestui/js/easyui-lang-zh_CN.js?v=svn.revision}" type="text/javascript"></script>
    <script src="../../js/jquery.config.js?v=svn.revision" th:src="@{/js/jquery.config.js?v=svn.revision}" type="text/javascript"></script>
    <script src="http://************:8088/simbestui/js/jquery.zjsfile.js?v=svn.revision" th:src="@{http://************:8088/simbestui/js/jquery.zjsfile.js?v=svn.revision}" type="text/javascript"></script>
    <script src="http://************:8088/simbestui/js/jquery.aduq.js?v=svn.revision" th:src="@{http://************:8088/simbestui/js/jquery.aduq.js?v=svn.revision}" type="text/javascript"></script>
    <script src="http://************:8088/simbestui/js/jsencrypt.min.js?v=svn.revision" th:src="@{http://************:8088/simbestui/js/jsencrypt.min.js?v=svn.revision}" type="text/javascript"></script>
    <script src="http://************:8088/simbestui/js/jquery.process.js?v=svn.revision" th:src="@{http://************:8088/simbestui/js/jquery.process.js?v=svn.revision}" type="text/javascript"></script>
    <script src="../../js/webSite.js?v=svn.revision" th:src="@{/js/webSite.js?v=svn.revision}" type="text/javascript"></script>
    <style>
        /*.exam5_window{width:300px;height:150px;position:absolute;z-index: 999;cursor:pointer;overflow:hidden;display: none;}*/
        /*.exam5_window img{width: 300px; height:150px}*/
        /*.exam6_window{width:300px;height:150px;position:absolute;z-index: 999;cursor:pointer;overflow:hidden;display: none;}*/
        /*.exam6_window img{width: 300px; height:150px}*/
        /*.exam7_window{width:300px;height:150px;position:absolute;z-index: 999;cursor:pointer;overflow:hidden;display: none;}*/
        /*.exam7_window img{width: 300px; height:150px}*/
        /*.exam8_window{width:300px;height:150px;position:absolute;z-index: 999;cursor:pointer;overflow:hidden;display: none;}*/
        /*.exam8_window img{width: 300px; height:150px}*/
        .exam10_window{width:300px;height:150px;position:absolute;z-index: 999;cursor:pointer;overflow:hidden;}
        .exam10_window img{width: 300px; height:150px}
    </style>
    <script type="text/javascript">
        $(function(){
            webSite("index");
            var pcExamUrl = "";//考试跳转页面路径
            // var rollEle = document.getElementById("roll");
            // windowFly(rollEle,30,600,300);

/*            var rollEle = document.getElementById("roll");
            windowFly(rollEle,30,600,300);

            var examWindowEle = document.getElementById("exam_window");
            windowFly(examWindowEle,30,0,0);*/

        /*    var examWindowEle1 = document.getElementById("exam5_window");
            windowFly(examWindowEle1,30,1024,0);*/

            /*var examWindowEle1 = document.getElementById("zhgl_window");
            windowFly(examWindowEle1,30,1024,0);*/

            // var examWindowEle1 = document.getElementById("exam5_window");
            // windowFly(examWindowEle1,30,1024,0);
            //
            // var examWindowEle2 = document.getElementById("exam6_window");
            // windowFly(examWindowEle2,30,1024,160);
            //
            // var examWindowEle3 = document.getElementById("exam7_window");
            // windowFly(examWindowEle3,30,1024,320);

            // var examWindowEle4 = document.getElementById("exam8_window");
            // windowFly(examWindowEle4,30,1024,320);

            // var examWindowEle5 = document.getElementById("exam10_window");
            // windowFly(examWindowEle5,30,1024,320);

            //控制飘窗是否显示
            // ajaxgeneral({
            //     url: "action/exam/findEffectiveExam?examCode=zfjs-01",
            //     contentType: "application/json; charset=utf-8",
            //     success: function (res) {
            //         if(res.data){
            //             if(res.data.showFlag){
            //                 $("#exam5_window").show();
            //                 pcExamUrl = res.data.pcExamUrl+"?from=oa&examCode=zfjs-01&uid="+getRsa(web.currentUser.username);
            //                 // pcExamUrl = res.data.pcExamUrl+"?from=oa&examCode=zfjs-01&uid="+web.currentUser.username;
            //                 $("#exam5_window img").attr("src",res.data.pcImageUrl);
            //
            //                 var examWindowEle = document.getElementById("exam5_window");
            //                 windowFly(examWindowEle,30,0,0);
            //             }else{
            //                 $("#exam5_window").hide();
            //             }
            //         }
            //     }
            // });
            // ajaxgeneral({
            //     url: "action/exam/findEffectiveExam?examCode=zgcx-01",
            //     contentType: "application/json; charset=utf-8",
            //     success: function (res) {
            //         if(res.data){
            //             if(res.data.showFlag){
            //                 $("#exam6_window").show();
            //                 pcExamUrl = res.data.pcExamUrl+"?from=oa&examCode=zgcx-01&uid="+getRsa(web.currentUser.username);
            //                 // pcExamUrl = res.data.pcExamUrl+"?from=oa&examCode=zfjs-01&uid="+web.currentUser.username;
            //                 $("#exam6_window img").attr("src",res.data.pcImageUrl);
            //
            //                 var examWindowEle = document.getElementById("exam6_window");
            //                 windowFly(examWindowEle,30,0,0);
            //             }else{
            //                 $("#exam6_window").hide();
            //             }
            //         }
            //     }
            // });
            // ajaxgeneral({
            //     url: "action/exam/findEffectiveExam?examCode=zgcx-02",
            //     contentType: "application/json; charset=utf-8",
            //     success: function (res) {
            //         if(res.data){
            //             if(res.data.showFlag){
            //                 $("#exam7_window").show();
            //                 pcExamUrl = res.data.pcExamUrl+"?from=oa&examCode=zgcx-02&uid="+getRsa(web.currentUser.username);
            //                 // pcExamUrl = res.data.pcExamUrl+"?from=oa&examCode=zfjs-01&uid="+web.currentUser.username;
            //                 $("#exam7_window img").attr("src",res.data.pcImageUrl);
            //
            //                 var examWindowEle = document.getElementById("exam7_window");
            //                 windowFly(examWindowEle,30,0,0);
            //             }else{
            //                 $("#exam7_window").hide();
            //             }
            //         }
            //     }
            // });
            // ajaxgeneral({
            //     url: "action/exam/findEffectiveExam?examCode=ljfx-01",
            //     contentType: "application/json; charset=utf-8",
            //     success: function (res) {
            //         if(res.data){
            //             if(res.data.showFlag){
            //                 $("#exam8_window").show();
            //                 pcExamUrl = res.data.pcExamUrl+"?from=oa&examCode=ljfx-01&uid="+getRsa(web.currentUser.username);
            //                 // pcExamUrl = res.data.pcExamUrl+"?from=oa&examCode=zfjs-01&uid="+web.currentUser.username;
            //                 $("#exam8_window img").attr("src",res.data.pcImageUrl);
            //
            //                 var examWindowEle = document.getElementById("exam8_window");
            //                 windowFly(examWindowEle,30,0,0);
            //             }else{
            //                 $("#exam8_window").hide();
            //             }
            //         }
            //     }
            // });

            ajaxgeneral({
                url: "action/exam/findEffectiveExam?examCode=2021_CYZD",
                contentType: "application/json; charset=utf-8",
                success: function (res) {
                    if(res.data){
                        if(res.data.showFlag){
                            $("#exam10_window").show();
                            pcExamUrl = res.data.pcExamUrl+"?from=oa&examAppCode=2021_CYZD_01&examCode=2021_CYZD&uid="+getRsa(web.currentUser.username);
                            // pcExamUrl = res.data.pcExamUrl+"?from=oa&examCode=zfjs-01&uid="+web.currentUser.username;
                            $("#exam10_window img").attr("src",res.data.pcImageUrl);

                            var examWindowEle = document.getElementById("exam10_window");
                            windowFly(examWindowEle,30,0,0);
                        }else{
                            $("#exam10_window").hide();
                        }
                    }
                }
            });

            // 跳转到考试页面，页面关闭时判断是否答题完成，若完成隐藏飘窗
            $("#exam5_window").click(function(){
                var subWindows = window.open(pcExamUrl,"_blank");
                var closeTimer = setInterval(function(){
                    if(subWindows && subWindows.closed){
                        clearInterval(closeTimer);
                        ajaxgeneral({
                            url: "action/exam/findEffectiveExam?examCode=zfjs-01",
                            contentType: "application/json; charset=utf-8",
                            success: function (res) {
                                if(res.data){
                                    if(res.data.showFlag){
                                        $("#exam5_window").show();
                                    }else{
                                        $("#exam5_window").hide();
                                    }
                                }
                            }
                        });
                    }
                },1000);
            });

            $("#exam6_window").click(function(){
                var subWindows = window.open(pcExamUrl,"_blank");
                var closeTimer = setInterval(function(){
                    if(subWindows && subWindows.closed){
                        clearInterval(closeTimer);
                        ajaxgeneral({
                            url: "action/exam/findEffectiveExam?examCode=zgcx-01",
                            contentType: "application/json; charset=utf-8",
                            success: function (res) {
                                if(res.data){
                                    if(res.data.showFlag){
                                        $("#exam6_window").show();
                                    }else{
                                        $("#exam6_window").hide();
                                    }
                                }
                            }
                        });
                    }
                },1000);
            });

            $("#exam7_window").click(function(){
                var subWindows = window.open(pcExamUrl,"_blank");
                var closeTimer = setInterval(function(){
                    if(subWindows && subWindows.closed){
                        clearInterval(closeTimer);
                        ajaxgeneral({
                            url: "action/exam/findEffectiveExam?examCode=zgcx-02",
                            contentType: "application/json; charset=utf-8",
                            success: function (res) {
                                if(res.data){
                                    if(res.data.showFlag){
                                        $("#exam7_window").show();
                                    }else{
                                        $("#exam7_window").hide();
                                    }
                                }
                            }
                        });
                    }
                },1000);
            });

            // $("#exam8_window").click(function(){
            //     var subWindows = window.open(pcExamUrl,"_blank");
            //     var closeTimer = setInterval(function(){
            //         if(subWindows && subWindows.closed){
            //             clearInterval(closeTimer);
            //             ajaxgeneral({
            //                 url: "action/exam/findEffectiveExam?examCode=ljfx-01",
            //                 contentType: "application/json; charset=utf-8",
            //                 success: function (res) {
            //                     if(res.data){
            //                         if(res.data.showFlag){
            //                             $("#exam8_window").show();
            //                         }else{
            //                             $("#exam8_window").hide();
            //                         }
            //                     }
            //                 }
            //             });
            //         }
            //     },1000);
            // });

            $("#exam10_window").click(function(){
                var subWindows = window.open(pcExamUrl,"_blank");
                var closeTimer = setInterval(function(){
                    if(subWindows && subWindows.closed){
                        clearInterval(closeTimer);
                        ajaxgeneral({
                            url: "action/exam/findEffectiveExam?examCode=2021_CYZD",
                            contentType: "application/json; charset=utf-8",
                            success: function (res) {
                                if(res.data){
                                    if(res.data.showFlag){
                                        $("#exam10_window").show();
                                    }else{
                                        $("#exam10_window").hide();
                                    }
                                }
                            }
                        });
                    }
                },1000);
            });
        });
        // //rsa加密
        // function getRsa(val,type){
        //     var encrypt = new JSEncrypt();
        //     var key = "MIGfMA0GCSqGSIb3DQEBAQUAA4GNADCBiQKBgQC+K3y4fL71dFhFYC9c9bea9wPH" + "\r" +
        //         "youU86VI0nI1GtDiMbSd3/mFcf/Z14hixordW8W8Q0BftncjcbIOHOeHDK074hpV" + "\r" +
        //         "bMdJTgadisuksX1fISp5CXa5ETsDcHa6usb1wGd2EFSo8ws5Jfi5oGZVgRzF3YLI" + "\r" +
        //         "KgxYn+NZu7cvHOD0GwIDAQAB" + "\r";
        //     encrypt.setPublicKey(key);
        //     var pk=encrypt.encrypt(val);
        //     return type?encodeURIComponent(pk):encodeURI(pk);//encodeURIComponent
        // };
        // 飘窗
        function windowFly(ele,speed,x,y){
            var ggRoll={                               //创建对象直接量
                roll:ele,  //获取id属性为roll的对象
                speed:speed,                           //飘动速度，即为定时器函数多长时间执行一次
                statusX:20,                       //规定每执行一次函数，left属性值变化的幅度
                statusY:20,                       //规定每执行一次函数，top属性值变化的幅度
                x:x,                                 //规定初始状态下left属性的值
                y:y,                                 //规定初始状态下top属性的值
                //差值用来测算left属性值的极限
                winW:document.documentElement.clientWidth-ele.offsetWidth,
                //差值用来测算top属性值的极限
                winH:document.documentElement.clientHeight-ele.offsetHeight,
                //声明函数
                Go: function () {
                    //设置div的left属性值
                    this.roll.style.left = this.x + 'px';
                    //设置div的top属性值
                    this.roll.style.top = this.y + 'px';
                    //如果statusX=1则每次减少1px,否则减少1px
                    this.x = this.x + (this.statusX ? -1 : 1);
                    //如果left属性值小于0，也就是div要超出左边界了，就将statusX设置为0
                    if (this.x < 0) { this.statusX = 0; }
                    //如果top属性值大于winW，也就是div要超出右边界了，就将statusX设置为1
                    if (this.x > this.winW) { this.statusX = 1; }

                    this.y = this.y + (this.statusY ? -1 : 1);
                    if (this.y < 0) { this.statusY = 0; }
                    if (this.y > this.winH) { this.statusY = 1; }

                }
            };
            var interval=setInterval(function(){
                ggRoll.Go();
            },ggRoll.speed);

            // onmouseover属性：鼠标移动到元素上时触发
            ggRoll.roll.onmouseover=function(){
                clearInterval(interval);
            };
            // onmouseout属性:鼠标离开元素时触发
            ggRoll.roll.onmouseout=function(){
                interval=setInterval(function(){
                    ggRoll.Go();
                },ggRoll.speed)
            };
        };

        // 跑马灯
        var scrlSpeed=2;
        scrlSpeed=(document.all)? scrlSpeed : Math.max(1, scrlSpeed-1);
        function initScroll(container,object){
            if (document.getElementById(container) != null){
                var contObj=document.getElementById(container);
                var obj=document.getElementById(object);
                contObj.style.visibility = "visible";
                contObj.scrlSpeed = scrlSpeed;
                widthContainer = contObj.offsetWidth;
                obj.style.left=parseInt(widthContainer)+"px";
                widthObject=obj.offsetWidth;
                interval=setInterval("objScroll('"+ container +"','"+ object +"',"+ widthContainer +")",20);
                contObj.onmouseover = function(){
                    contObj.scrlSpeed=0;
                }
                contObj.onmouseout = function(){
                    contObj.scrlSpeed=scrlSpeed;
                }
            }
        };

        function objScroll(container,object,widthContainer){
            var contObj=document.getElementById(container);
            var obj=document.getElementById(object);
            widthObject=obj.offsetWidth;
            if (parseInt(obj.style.left)>(widthObject*(-1))){
                obj.style.left=parseInt(obj.style.left)-contObj.scrlSpeed+"px";
            } else {
                obj.style.left=parseInt(widthContainer)+"px";
            }
        };
        window.onload=function(){
            initScroll("marqueeContainer", "noticeDl");
        };

    </script>
    <style>
        .current_act {
            color: #CF321F;
            border-bottom: 2px solid #CF321F;
        }
        .tab_two_tabs_tab {
            font-size: 16px;
            padding: 5px 10px;
            cursor: pointer;
            margin-right: 40px;
        }
        .tab_two_tabs {
            position: relative;
            line-height: 30px;
            /*margin-top: 10px;*/
            padding-left: 15px;
        }
        .tab_two {
            height: 200px;
            float: right;
            width: 445px;
            overflow: hidden;
        }
        .tab_two_content {
            width: 100%;
            border-top: 1px dashed #CDCDCD;
        }
        .tab_two_tabs a{
            text-decoration: none;
        }
        .company ul.listT{
            padding-top: 6px;
        }
        /*以案示警*/
        .tab_one_tabs_tab {
            font-size: 16px;
            padding: 5px 10px;
            cursor: pointer;
            /*margin-right: 40px;*/
        }
        .tab_one_tabs {
            position: relative;
            line-height: 30px;
            /*margin-top: 10px;*/
            padding-left: 15px;
        }
        .tab_one {
            height: 200px;
            float: right;
            width:100%;
            overflow: hidden;
        }
        .tab_one_content {
            width: 100%;
            border-top: 1px dashed #CDCDCD;
        }
        .tab_one_tabs a{
            text-decoration: none;
        }

        .roll {
            width: 600px;
            height: 310px;
            position: absolute;
            z-index: 999;
            cursor: pointer;
            overflow: hidden;
        }
        .eightRegulations{
            width: 445px;
            height: 133.5px;
            float: right;
            background: url('/hnjjwz/html/eightRegulations/images/eight_regulations.jpg') no-repeat center;
            background-size: 100% 100%;
            position:relative;
            z-index: 100;
        }
        .eightRegulations a{
            display: block;
            width: 100%;height: 100%;
            cursor: pointer;
        }

    </style>
</head>
<body>
<!--top-->
<div class="header"><div class="auto1024"><h6></h6></div></div>
<div class="nav">
    <ul class="auto1024">
        <!--<li class="li_hover"><a>首页</a></li>
        <li><a>信息公开</a></li>
        <li><a>规章制度</a></li>
        <li><a>巡察工作</a></li>
        <li><a>嵌入式防控监督</a></li>
        <li><a>课题研究</a></li>
        <li><a>共享平台</a></li>
        <li>
            <a>数据报送</a>
            <ul>
                <li><a>领导人员廉洁情况活页夹</a></li>
                <li><a>信息报送</a></li>
            </ul>
        </li>
        <li class="nobor"><a>信访举报</a></li>-->
    </ul>
</div>
<!--notice-->
<div class="auto1024 notice">
    <!----><a target="_blank" class="more">更多>></a>
    <div class="noticeIcon">公告</div>
    <div class="marqueeContainer" id="marqueeContainer">
        <dl class="clearfix noticeDl" id="noticeDl">
            <dt class="fl" id="noticeTitle"><!--<a class="mr10" href="http://www.baidu.com">很重要的很长的你绝对想不到内容的公告标题1</a>--></dt>
            <dd class="fl" id="noticeSubTitle">
                <!--<a class="mr10" href="http://www.baidu.com">很重要的很长的你绝对想不到内容的公告标题1</a>
                <span></span>
                <a class="mr10" href="http://www.baidu.com">很重要的很长的你绝对想不到内容的公告标题1</a>-->
            </dd>
        </dl>
    </div>
    <!--<strong class="fl txtc"><i class="iconfont mr5">&#xe626;</i>公告</strong>
    <marquee class="fl" direction='left' scrollamount='2' onmousemove='this.stop()' onmouseout='this.start()'>
        <a class="mr10" href="http://www.baidu.com">很重要的很长的你绝对想不到内容的公告标题1</a>
        <a class="mr10" href="http://www.baidu.com">很重要的很长的你绝对想不到内容的公告标题2</a>
        <a class="mr10" href="http://www.baidu.com">很重要的很长的你绝对想不到内容的公告标题3</a>
    </marquee>-->
</div>
<!--focus-->
<div class="auto1024 focus of_hiddenmodTit clearfix">
    <div class="focusD fl">
        <div class="cfocus" id="indexCfocus" fixed="true" fadein="true" style="height: 310px;">
            <ul class="box">
                <!--<li class="item" style="background-image: url('http://photocdn.sohu.com/20111207/Img328215666.jpg');"><a><p class="txtc">幻灯片标题1</p></a></li>
                <li class="item" style="background-image: url('http://k.zol-img.com.cn/sjbbs/7692/a7691515_s.jpg');"><a><p class="txtc">幻灯片标题2</p></a></li>
                <li class="item" style="background-image: url('http://photocdn.sohu.com/20120213/Img334502641.jpg');"><a><p class="txtc">幻灯片标题3</p></a></li>-->
            </ul>
            <div class="page"></div>
        </div>
    </div>
    <div class="work fr of_hidden">
        <div class="modTit"><!--<strong class="fl">公司工作动态</strong><a class="fr">更多>></a>--></div>
        <ul class="list listT">
            <!--<li><a>·中共中央办公厅印发《中国共产党纪律检查机关监督执纪工作规则》 [纪检监察室（巡察工作办公室）] <font>(2019-01-07)</font></a></li>
            <li><a>·从通报看形势增定力之三:坚决清除两面人 [纪检监察室（巡察工作办公室）] <font>(2018-12-29)</font></a></li>
            <li><a>·从通报看形势增定力之四：严惩不收敛不收手者<font>(01-07)</font></a></li>
            <li><a>·从通报看形势增定力之五：在拧紧“总开关”上持续发力<font>(01-07)</font></a></li>
            <li><a>·中国共产党第十九届中央纪律检查委员会第三次全体会议公报<font>(01-14)</font></a></li>
            <li><a>·16个字，精读习近平对反腐斗争最新要求<font>(01-14)</font></a></li>
            <li><a>·中共中央办公厅印发《中国共产党纪律检查机关监督执纪工作规则》 [纪检监察室（巡察工作办公室）] <font>(2019-01-07)</font></a></li>
            <li><a>·从通报看形势增定力之三:坚决清除两面人 [纪检监察室（巡察工作办公室）] <font>(2018-12-29)</font></a></li>
            <li><a>·从通报看形势增定力之四：严惩不收敛不收手者<font>(01-07)</font></a></li>-->
        </ul>
    </div>
</div>
<!--cheap-->
<div class="auto1024 cheap of_hidden clearfix">
    <div class="cheapD fl of_hidden" style="min-height: 150px;">
        <div class="modTit"><!--<strong class="fl">廉闻要论</strong><a class="fr">更多>></a>--></div>
        <ul class="list listT">
            <!--<li><a>·杨晓渡：提高政治站位 深化专项治理 为打赢脱贫攻坚战提供坚强纪律保障</a><font>2019-01-07</font></li>
            <li><a>中共中央印发《中国共产党纪律处分条例》</a><font>2018-12-29</font></li>
            <li><a>·从通报看形势增定力之四：严惩不收敛不收手者</a><font>2019-01-07</font></li>
            <li><a>·从通报看形势增定力之五：在拧紧“总开关”上持续发力</a><font>2019-01-07</font></li>
            <li><a>·中国共产党第十九届中央纪律检查委员会第三次全体会议公报</a><font>2019-01-14</font></li>
            <li><a>·16个字，精读习近平对反腐斗争最新要求</a><font>2019-01-14</font></li>
            <li><a>·中共中央办公厅印发《中国共产党纪律检查机关监督执纪工作规则》 [纪检监察室（巡察工作办公室）] </a><font>2019-01-07</font></li>
            <li><a>·从通报看形势增定力之三:坚决清除两面人 [纪检监察室（巡察工作办公室）] </a><font>2018-12-29</font></li>
            <li><a>·从通报看形势增定力之四：严惩不收敛不收手者</a><font>2019-01-07</font></li>-->
        </ul>
    </div>
    <div class="eightRegulations"><a href="/hnjjwz/html/eightRegulations/index.html" target="_blank"></a></div>
    <div class="imgMod fr of_hidden" style="margin-top:-135px;position:relative;z-index: 99">

        <!--<a><img src="http://photocdn.sohu.com/20111207/Img328215666.jpg"/><font>信访举报</font></a>
        <a class="a_even"><img src="http://k.zol-img.com.cn/sjbbs/7692/a7691515_s.jpg"/><font>廉洁教育</font></a>
        <a><img src="http://photocdn.sohu.com/20120213/Img334502641.jpg"/><font>他山之石</font></a>
        <a class="a_even"><img src="http://photocdn.sohu.com/20111207/Img328215666.jpg"/><font>在线测试</font></a>-->
    </div>
</div>
<!--review-->
<div class="auto1024 review of_hidden clearfix">
    <div class="reviewD fl">
        <div class="reviewDL fl of_hidden">
            <div class="modTit"><strong class="fl">以案示警</strong><a target="_blank" href="warnCompany.html" class="fr">+</a></div>
            <ul class="list listRD">
                <div class="tab_one">
                    <div class="tab_one_tabs">
                        <a href="javascript:void(0)"><span class="tab_one_tabs_tab">以案示警</span></a>
                        <a href="javascript:void(0)"><span class="tab_one_tabs_tab">“四风”问题专区</span></a>
                    </div>
                    <div class="tab_one_content">
                        <ul style="cursor: pointer;" class="list listT"></ul>
                    </div>
                </div>
            </ul>
        </div>
        <div class="reviewDR fr of_hidden">
            <div class="modTit"><!--<strong class="fl">纪律审查与监督</strong><a class="fr">更多>></a>--></div>
            <ul class="list listT listRDL">
                <!--<li><a>·杨晓渡：提高政治站位 深化专项治理 为打赢脱贫攻坚战提供坚强纪律保障</a></li>
                <li><a>中共中央印发《中国共产党纪律处分条例》</a></li>
                <li><a>·从通报看形势增定力之四：严惩不收敛不收手者</a></li>-->
            </ul>
        </div>
        <div class="reviewDB fl of_hidden">
            <div class="modTit"><!--<strong class="fl">廉洁文化</strong><a class="fr">查看>></a>--></div>
            <ul class="listRB">
                <!--<li><a><img src="http://photocdn.sohu.com/20111207/Img328215666.jpg"/></a></li>
                <li><a><img src="http://k.zol-img.com.cn/sjbbs/7692/a7691515_s.jpg"/></a></li>
                <li><a><img src="http://photocdn.sohu.com/20120213/Img334502641.jpg"/></a></li>
                <li><a><img src="http://photocdn.sohu.com/20111207/Img328215666.jpg"/></a></li>
                <li><a><img src="http://k.zol-img.com.cn/sjbbs/7692/a7691515_s.jpg"/></a></li>-->
            </ul>
        </div>
    </div>
    <div class="company fr of_hidden">
        <div class="modTit"><strong class="fl">分公司动态</strong><a target="_blank" href="company.html" class="fr">+</a></div>
        <ul class="list listT">
            <!--<li><a>·中共中央办公厅印发《中国共产党纪律检查机关监督执纪工作规则》 [纪检监察室（巡察工作办公室）] <font>(2019-01-07)</font></a></li>
            <li><a>·从通报看形势增定力之三:坚决清除两面人 [纪检监察室（巡察工作办公室）] <font>(2018-12-29)</font></a></li>
            <li><a>·从通报看形势增定力之四：严惩不收敛不收手者<font>(01-07)</font></a></li>
            <li><a>·从通报看形势增定力之五：在拧紧“总开关”上持续发力<font>(01-07)</font></a></li>
            <li><a>·中国共产党第十九届中央纪律检查委员会第三次全体会议公报<font>(01-14)</font></a></li>
            <li><a>·16个字，精读习近平对反腐斗争最新要求<font>(01-14)</font></a></li>
            <li><a>·中共中央办公厅印发《中国共产党纪律检查机关监督执纪工作规则》 [纪检监察室（巡察工作办公室）] <font>(2019-01-07)</font></a></li>
            <li><a>·从通报看形势增定力之三:坚决清除两面人 [纪检监察室（巡察工作办公室）] <font>(2018-12-29)</font></a></li>
            <li><a>·从通报看形势增定力之四：严惩不收敛不收手者<font>(01-07)</font></a></li>-->
            <div class="tab_two">
                <div class="tab_two_tabs">
                    <a href="javascript:void(0)"><span class="tab_two_tabs_tab">党廉信息动态</span></a>
                    <a href="javascript:void(0)"><span class="tab_two_tabs_tab">纪检信息动态</span></a>
                </div>
                <div class="tab_two_content">
                    <ul style="cursor: pointer;" class="list listT"></ul>
                </div>
            </div>
        </ul>
    </div>
    <div class="tab_three">
        <div class="tab_three_tabs">
            <!--<a href="javascript:void(0)"><span class="tab_three_tabs_tab">嵌入式防控监督</span></a>-->
            <a href="javascript:void(0)"><span class="tab_three_tabs_tab">他山之石</span></a>
            <a href="javascript:void(0)"><span class="tab_three_tabs_tab">在线测试</span></a>
        </div>
        <div class="tab_three_content">
            <ul style="cursor: pointer;" class="list listT"></ul>
            <div class="img hide"><a href="" target="_blank"><img src="../../images/cjcx.png" alt=""></a></div>
        </div>
    </div>
</div>
<!--link-->
<div class="auto1024 link of_hidden clearfix">
    <div class="modTit2"><strong class="fl">友情链接</strong></div>
    <ul class="listRB linkU">
        <!--<li><a><img src="http://photocdn.sohu.com/20111207/Img328215666.jpg"/></a></li>
        <li><a><img src="http://k.zol-img.com.cn/sjbbs/7692/a7691515_s.jpg"/></a></li>
        <li><a><img src="http://photocdn.sohu.com/20120213/Img334502641.jpg"/></a></li>
        <li><a><img src="http://photocdn.sohu.com/20111207/Img328215666.jpg"/></a></li>-->
    </ul>
</div>
<!--copy-->
<div class="copy">©版权所有 中国移动通信集团河南有限公司</div>

<!--roll-->

<!--<<div id="roll" class="roll">-->
    <!--<a target='_blank' href='./dxInfo.html'><img src='../../images/dx.jpg' /></a>-->
<!--</div>-->



<!--<div id="roll" class="roll">
    <a target='_blank' href='http://************:8088/hnjjwz/html/webSite/familyWind.html'><img src='../../images/zhuanlan.jpg' /></a>
</div>

<div id="exam_window" class="exam_window">
    <a target='_blank' href='http://portal.ha.cmcc/was2/HaPortalSso/inner_sso/bps_exam.jsp'><img src='../../images/exam.png' /></a>
</div>

<div id="exam2_window" class="exam2_window">
    <a target='_blank' href='http://portal.ha.cmcc/was2/HaPortalSso/inner_sso/bps_exam4.jsp'><img src='../../images/exam2.png' /></a>
</div>

<div id="hnjjwz2020_window" class="hnjjwz2020_window">
    <a target='_blank' href='http://portal.ha.cmcc/was2/HaPortalSso/inner_sso/bps_hnjjwz5.jsp'><img src='../../images/banner.jpg' /></a>
</div>

<div id="exam3_window" class="exam3_window">
    <a target='_blank' href='http://portal.ha.cmcc/was2/HaPortalSso/inner_sso/bps_exam5.jsp'><img src='../../images/exam3.jpg' /></a>
</div>
<div id="video_list" class="video_list">
    <a target='_blank' href='http://************:8088/hnjjwz/html/webSite/VideoList.html'><img src='../../images/video_list.jpg' /></a>
&lt;!&ndash;
    <a target='_blank' href='http://localhost:8081/hnjjwz/html/webSite/VideoList.html'><img src='../../images/video_list.jpg' /></a>
&ndash;&gt;
</div>-->

<!--<div id="exam4_window" class="exam4_window">-->
<!--    <a target='_blank' href='http://portal.ha.cmcc/was2/HaPortalSso/inner_sso/bps_exam7.jsp'><img src='../../images/examGovernment.jpg' /></a>-->
<!--</div>-->

<!--<div id="zhgl_window" class="zhgl_window">-->
<!--    <a target='_blank' href='http://portal.ha.cmcc/was2/HaPortalSso/inner_sso/bps_zhgl_redCultureHomePage.jsp'><img src='../../images/bps_zhgl_redCultureHomePage.png' /></a>-->
<!--</div>-->
<!--<div id="exam5_window" class="exam5_window" style="width:300px;height:150px;position:absolute;z-index: 999;cursor:pointer;overflow:hidden; display: none;">
    <a target='_blank' href='http://portal.ha.cmcc/was2/HaPortalSso/inner_sso/bps_exam7.jsp'><img src='../../images/examGovernment.jpg' /></a>
</div>-->

<!--<div id="exam5_window" class="exam5_window">
    <a target='_blank' href='http://portal.ha.cmcc/was2/HaPortalSso/inner_sso/bps_exam8.jsp'><img src='../../images/zfjs.png' /></a>
</div>

<div id="exam6_window" class="exam6_window">
    <a target='_blank' href='http://portal.ha.cmcc/was2/HaPortalSso/inner_sso/bps_exam9.jsp'><img src='../../images/zgcx.png' /></a>
</div>

<div id="exam7_window" class="exam7_window">
    <a target='_blank' href='http://portal.ha.cmcc/was2/HaPortalSso/inner_sso/bps_exam10.jsp'><img src='../../images/zgcx.png' /></a>
</div>-->

<!--<div id="exam8_window" class="exam8_window">-->
<!--    <a target='_blank' href='http://portal.ha.cmcc/was2/HaPortalSso/inner_sso/bps_exam11.jsp'><img src='../../images/ljfx.png' /></a>-->
<!--</div>-->


<div id="exam10_window" class="exam10_window">
<!--    <a target='_blank' href='http://portal.ha.cmcc/was2/HaPortalSso/inner_sso/bps_exam13.jsp'><img src='../../images/cyzd.png' /></a>-->
    <a target='_blank'><img src='../../images/cyzd.png' /></a>
</div>
</body>
</html>
