package com.simbest.boot.hnjjwz.column.familyWind.web;/**
 * Created by KZH on 2019/7/31 9:26.
 */

import com.simbest.boot.base.web.controller.LogicController;
import com.simbest.boot.base.web.response.JsonResponse;
import com.simbest.boot.hnjjwz.column.familyWind.model.RecordPerson;
import com.simbest.boot.hnjjwz.column.familyWind.service.IRecordPersonService;
import io.swagger.annotations.Api;
import io.swagger.annotations.ApiOperation;
import lombok.extern.slf4j.Slf4j;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RestController;

import java.util.Map;

/**
 * <AUTHOR>
 * @create 2019-07-31 9:26
 * @desc
 **/
@Api(description = "人员数据")
@Slf4j
@RestController
@RequestMapping(value = "/action/recordPerson")
public class RecordPersonController extends LogicController<RecordPerson,String> {

    private IRecordPersonService iRecordPersonService;

    public RecordPersonController(IRecordPersonService service){

        super(service);
        this.iRecordPersonService=service;
    }

    @ApiOperation(value = "投票接口", notes = "投票接口")
    @PostMapping (value = {"/vote","/vote/sso"})
    public JsonResponse vote(@RequestBody(required = false) Map<String, String> sumMap){

        return iRecordPersonService.vote(sumMap);

    }
}
