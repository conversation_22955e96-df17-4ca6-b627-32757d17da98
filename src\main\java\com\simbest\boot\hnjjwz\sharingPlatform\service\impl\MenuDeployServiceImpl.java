package com.simbest.boot.hnjjwz.sharingPlatform.service.impl;

import com.simbest.boot.base.service.impl.LogicService;
import com.simbest.boot.base.web.response.JsonResponse;
import com.simbest.boot.hnjjwz.constants.Constants;
import com.simbest.boot.hnjjwz.sharingPlatform.model.MenuDeploy;
import com.simbest.boot.hnjjwz.sharingPlatform.repository.MenuDeployRepository;
import com.simbest.boot.hnjjwz.sharingPlatform.service.IMenuDeployService;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.data.domain.Page;
import org.springframework.data.domain.Pageable;
import org.springframework.stereotype.Service;

import java.util.Map;

/**
 * 作用：菜单配置
 * 作者：刘萌
 * 时间：2019/05/06
 */
@Slf4j
@Service
public class MenuDeployServiceImpl extends LogicService<MenuDeploy,String> implements IMenuDeployService {
    private MenuDeployRepository menuDeployRepository;

    @Autowired
    public MenuDeployServiceImpl(MenuDeployRepository menuDeployRepository) {
        super(menuDeployRepository);
        this.menuDeployRepository = menuDeployRepository;
    }

    /**
     * 根据菜单名(模糊)以及父菜单名(模糊)查询分页
     * @param menuDeployMap
     * @param pageable
     * @return
     */
    @Override
    public JsonResponse findAllDim ( Map<String, Object> menuDeployMap, Pageable pageable ) {
        String menuName = (String) menuDeployMap.get( Constants.MENU_NAME_KEY );
        String parentMenuName = (String) menuDeployMap.get( Constants.PARENT_MENU_NAME_KEY );
        Page<MenuDeploy> menuDeployPage = menuDeployRepository.findAllDim(menuName,parentMenuName, pageable);
        return JsonResponse.success( menuDeployPage );
    }
}
