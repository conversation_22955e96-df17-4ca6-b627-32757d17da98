package com.simbest.boot.hnjjwz.backstage.programaCite.service.impl;

import com.simbest.boot.base.exception.Exceptions;
import com.simbest.boot.base.service.impl.LogicService;
import com.simbest.boot.base.web.response.JsonResponse;
import com.simbest.boot.hnjjwz.backstage.programaCite.model.ProgramaCite;
import com.simbest.boot.hnjjwz.backstage.programaCite.repository.ProgramaCiteRepository;
import com.simbest.boot.hnjjwz.backstage.programaCite.service.IProgramaCiteService;
import com.simbest.boot.hnjjwz.constants.Constants;
import com.simbest.boot.sys.model.SysDictValue;
import com.simbest.boot.sys.service.ISysDictValueService;
import com.simbest.boot.util.MapUtil;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.data.domain.Page;
import org.springframework.data.domain.PageImpl;
import org.springframework.data.domain.Pageable;
import org.springframework.stereotype.Service;
import org.springframework.util.StringUtils;

import java.util.*;

/**
 * <AUTHOR>
 * @Data 2019/03/29
 * @Description
 */
@Slf4j
@Service
public class ProgramaCiteServiceImpl extends LogicService<ProgramaCite,String> implements IProgramaCiteService {

    private ProgramaCiteRepository programaCiteRepository;

    @Autowired
    public ProgramaCiteServiceImpl ( ProgramaCiteRepository programaCiteRepository) {
        super(programaCiteRepository);
        this.programaCiteRepository = programaCiteRepository;
    }

    @Autowired
    private ISysDictValueService sysDictValueService;

    /**
     * 根据栏目code获取栏目的中间信息
     * @param programaOneCode
     * @return
     */
    @Override
    public JsonResponse findRelationPrograma ( String programaOneCode ) {
        Set<Map<String,Object>> mapObjectSet = programaCiteRepository.findRelationPrograma( programaOneCode );
        Set<ProgramaCite> programaCiteSet = new LinkedHashSet<>(  );
        for(Map<String,Object> mapObject:mapObjectSet){
            try {
                ProgramaCite programaCite= (ProgramaCite)MapUtil.mapToObject( mapObject, ProgramaCite.class);
                programaCiteSet.add( programaCite );
            } catch ( Exception e ) {
                Exceptions.printException( e );
                return JsonResponse.fail( -1,"获取失败！" );
            }
        }
        return JsonResponse.success( programaCiteSet,"获取成功！" );
    }

    /**
     * 根据栏目关系类型(精确)以及栏目名(模糊)查询
     * @param programaName
     * @param programaCiteType
     * @param pageable
     * @return
     */
    @Override
    public JsonResponse findDimProgramaCite ( String programaName, String programaCiteType, Pageable pageable ) {
        if( StringUtils.isEmpty( programaName ) ){
            programaName = "";
        }
        List<String> programaCiteTypeList = new ArrayList<>(  );
        SysDictValue sysDictValue = new SysDictValue(  );
        sysDictValue.setDictType( Constants.PROGRAMA_CITE_TYPE );
        if(StringUtils.isEmpty( programaCiteType )){
            //查询所有的programaCiteType
            List<SysDictValue> sysDictValueList  = sysDictValueService.findDictValue ( sysDictValue);
            for(SysDictValue sysDictValueNew:sysDictValueList){
                programaCiteTypeList.add( sysDictValueNew.getValue() );
            }
        }else{
            programaCiteTypeList.add( programaCiteType );
        }
        Page<Map<String,Object>> programaCitePage = programaCiteRepository.findDim( programaName,programaCiteTypeList,pageable );
        List<Map<String,Object>> programaCiteList = programaCitePage.getContent();
        List<ProgramaCite> programaCiteListNew = new ArrayList<>(  );
        for(Map<String,Object> programaCiteMap:programaCiteList){
            try {
                ProgramaCite programaCite  = (ProgramaCite) MapUtil.mapToObject(programaCiteMap,ProgramaCite.class  );
                programaCiteListNew.add( programaCite );
            } catch ( Exception e ) {
                Exceptions.printException( e );
            }
        }
        Page<ProgramaCite> programaCitePageNew = new PageImpl<ProgramaCite>(programaCiteListNew,pageable,programaCitePage.getTotalElements() );

        return JsonResponse.success( programaCitePageNew );
    }
}
