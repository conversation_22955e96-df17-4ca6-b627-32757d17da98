2025-05-27 00:06:11.945 [pool-14-thread-4] INFO  com.simbest.boot.component.task.AbstractTaskSchedule.checkAndExecute Line:198 - 集群主控节点主机地址【10.92.82.161】运行端口【10031】,当前主机地址【10.87.57.73】运行端口【8092】, 无法执行定时任务【com.simbest.boot.bps.task.AutoCheckWithUpdateTask】【640】
2025-05-27 00:06:11.945 [pool-14-thread-3] INFO  com.simbest.boot.component.task.AbstractTaskSchedule.checkAndExecute Line:198 - 集群主控节点主机地址【10.92.82.161】运行端口【10031】,当前主机地址【10.87.57.73】运行端口【8092】, 无法执行定时任务【com.simbest.boot.component.task.HeartTestTask】【639】
2025-05-27 00:06:11.945 [pool-14-thread-1] INFO  com.simbest.boot.component.task.AbstractTaskSchedule.checkAndExecute Line:198 - 集群主控节点主机地址【10.92.82.161】运行端口【10031】,当前主机地址【10.87.57.73】运行端口【8092】, 无法执行定时任务【com.simbest.boot.processdata.pushWorkmanager.task.SyncProcessDataToWorkManagerTask】【637】
2025-05-27 00:06:11.945 [pool-14-thread-5] INFO  com.simbest.boot.component.task.AbstractTaskSchedule.checkAndExecute Line:198 - 集群主控节点主机地址【10.92.82.161】运行端口【10031】,当前主机地址【10.87.57.73】运行端口【8092】, 无法执行定时任务【com.simbest.boot.processdata.pushWorkmanager.task.SyncCommentDataToWorkManagerTask】【641】
2025-05-27 00:06:11.950 [pool-14-thread-4] INFO  com.simbest.boot.component.task.AbstractTaskSchedule.checkAndExecute Line:198 - 集群主控节点主机地址【10.92.82.161】运行端口【10031】,当前主机地址【10.87.57.73】运行端口【8092】, 无法执行定时任务【com.simbest.boot.cmcc.a4.task.A4TaskCreateXmlFile】【640】
2025-05-27 00:06:11.945 [pool-14-thread-6] INFO  com.simbest.boot.component.task.AbstractTaskSchedule.checkAndExecute Line:198 - 集群主控节点主机地址【10.92.82.161】运行端口【10031】,当前主机地址【10.87.57.73】运行端口【8092】, 无法执行定时任务【com.simbest.boot.bps.task.AutoAddTodoTask】【642】
2025-05-27 00:06:11.950 [pool-14-thread-3] INFO  com.simbest.boot.component.task.AbstractTaskSchedule.checkAndExecute Line:198 - 集群主控节点主机地址【10.92.82.161】运行端口【10031】,当前主机地址【10.87.57.73】运行端口【8092】, 无法执行定时任务【com.simbest.boot.component.task.LicenseCheckTask】【639】
2025-05-27 00:06:11.945 [pool-14-thread-9] INFO  com.simbest.boot.component.task.AbstractTaskSchedule.checkAndExecute Line:198 - 集群主控节点主机地址【10.92.82.161】运行端口【10031】,当前主机地址【10.87.57.73】运行端口【8092】, 无法执行定时任务【com.simbest.boot.processdata.pushWorkmanager.task.SyncTaskDataToWorkManagerTask】【645】
2025-05-27 00:06:11.945 [pool-14-thread-10] INFO  com.simbest.boot.component.task.AbstractTaskSchedule.checkAndExecute Line:198 - 集群主控节点主机地址【10.92.82.161】运行端口【10031】,当前主机地址【10.87.57.73】运行端口【8092】, 无法执行定时任务【com.simbest.boot.bps.task.AutoCloseTodoTask】【646】
2025-05-27 00:07:00.000 [pool-14-thread-1] INFO  com.simbest.boot.component.task.AbstractTaskSchedule.checkAndExecute Line:198 - 集群主控节点主机地址【10.92.82.161】运行端口【10031】,当前主机地址【10.87.57.73】运行端口【8092】, 无法执行定时任务【com.simbest.boot.component.task.HeartTestTask】【637】
2025-05-27 00:08:00.015 [pool-14-thread-10] INFO  com.simbest.boot.component.task.AbstractTaskSchedule.checkAndExecute Line:198 - 集群主控节点主机地址【10.92.82.161】运行端口【10031】,当前主机地址【10.87.57.73】运行端口【8092】, 无法执行定时任务【com.simbest.boot.component.task.HeartTestTask】【646】
2025-05-27 00:09:00.009 [pool-14-thread-2] INFO  com.simbest.boot.component.task.AbstractTaskSchedule.checkAndExecute Line:198 - 集群主控节点主机地址【10.92.82.161】运行端口【10031】,当前主机地址【10.87.57.73】运行端口【8092】, 无法执行定时任务【com.simbest.boot.component.task.HeartTestTask】【638】
2025-05-27 00:10:00.007 [pool-14-thread-9] INFO  com.simbest.boot.component.task.AbstractTaskSchedule.checkAndExecute Line:198 - 集群主控节点主机地址【10.92.82.161】运行端口【10031】,当前主机地址【10.87.57.73】运行端口【8092】, 无法执行定时任务【com.simbest.boot.bps.task.AutoCloseTodoTask】【645】
2025-05-27 00:10:00.007 [pool-14-thread-4] INFO  com.simbest.boot.component.task.AbstractTaskSchedule.checkAndExecute Line:198 - 集群主控节点主机地址【10.92.82.161】运行端口【10031】,当前主机地址【10.87.57.73】运行端口【8092】, 无法执行定时任务【com.simbest.boot.bps.task.AutoCheckWithUpdateTask】【640】
2025-05-27 00:10:00.008 [pool-14-thread-8] INFO  com.simbest.boot.component.task.AbstractTaskSchedule.checkAndExecute Line:198 - 集群主控节点主机地址【10.92.82.161】运行端口【10031】,当前主机地址【10.87.57.73】运行端口【8092】, 无法执行定时任务【com.simbest.boot.processdata.pushWorkmanager.task.SyncCommentDataToWorkManagerTask】【644】
2025-05-27 00:10:00.007 [pool-14-thread-2] INFO  com.simbest.boot.component.task.AbstractTaskSchedule.checkAndExecute Line:198 - 集群主控节点主机地址【10.92.82.161】运行端口【10031】,当前主机地址【10.87.57.73】运行端口【8092】, 无法执行定时任务【com.simbest.boot.processdata.pushWorkmanager.task.SyncProcessDataToWorkManagerTask】【638】
2025-05-27 00:10:00.008 [pool-14-thread-3] INFO  com.simbest.boot.component.task.AbstractTaskSchedule.checkAndExecute Line:198 - 集群主控节点主机地址【10.92.82.161】运行端口【10031】,当前主机地址【10.87.57.73】运行端口【8092】, 无法执行定时任务【com.simbest.boot.component.task.HeartTestTask】【639】
2025-05-27 00:10:00.007 [pool-14-thread-6] INFO  com.simbest.boot.component.task.AbstractTaskSchedule.checkAndExecute Line:198 - 集群主控节点主机地址【10.92.82.161】运行端口【10031】,当前主机地址【10.87.57.73】运行端口【8092】, 无法执行定时任务【com.simbest.boot.processdata.pushWorkmanager.task.SyncTaskDataToWorkManagerTask】【642】
2025-05-27 00:10:00.008 [pool-14-thread-5] INFO  com.simbest.boot.component.task.AbstractTaskSchedule.checkAndExecute Line:198 - 集群主控节点主机地址【10.92.82.161】运行端口【10031】,当前主机地址【10.87.57.73】运行端口【8092】, 无法执行定时任务【com.simbest.boot.bps.task.AutoAddTodoTask】【641】
2025-05-27 00:11:00.005 [pool-14-thread-3] INFO  com.simbest.boot.component.task.AbstractTaskSchedule.checkAndExecute Line:198 - 集群主控节点主机地址【10.92.82.161】运行端口【10031】,当前主机地址【10.87.57.73】运行端口【8092】, 无法执行定时任务【com.simbest.boot.component.task.HeartTestTask】【639】
2025-05-27 00:12:00.012 [pool-14-thread-2] INFO  com.simbest.boot.component.task.AbstractTaskSchedule.checkAndExecute Line:198 - 集群主控节点主机地址【10.92.82.161】运行端口【10031】,当前主机地址【10.87.57.73】运行端口【8092】, 无法执行定时任务【com.simbest.boot.component.task.HeartTestTask】【638】
2025-05-27 00:13:00.001 [pool-14-thread-2] INFO  com.simbest.boot.component.task.AbstractTaskSchedule.checkAndExecute Line:198 - 集群主控节点主机地址【10.92.82.161】运行端口【10031】,当前主机地址【10.87.57.73】运行端口【8092】, 无法执行定时任务【com.simbest.boot.component.task.HeartTestTask】【638】
2025-05-27 00:14:00.013 [pool-14-thread-1] INFO  com.simbest.boot.component.task.AbstractTaskSchedule.checkAndExecute Line:198 - 集群主控节点主机地址【10.92.82.161】运行端口【10031】,当前主机地址【10.87.57.73】运行端口【8092】, 无法执行定时任务【com.simbest.boot.component.task.HeartTestTask】【637】
2025-05-27 00:15:00.010 [pool-14-thread-8] INFO  com.simbest.boot.component.task.AbstractTaskSchedule.checkAndExecute Line:198 - 集群主控节点主机地址【10.92.82.161】运行端口【10031】,当前主机地址【10.87.57.73】运行端口【8092】, 无法执行定时任务【com.simbest.boot.processdata.pushWorkmanager.task.SyncProcessDataToWorkManagerTask】【644】
2025-05-27 00:15:00.010 [pool-14-thread-7] INFO  com.simbest.boot.component.task.AbstractTaskSchedule.checkAndExecute Line:198 - 集群主控节点主机地址【10.92.82.161】运行端口【10031】,当前主机地址【10.87.57.73】运行端口【8092】, 无法执行定时任务【com.simbest.boot.component.task.HeartTestTask】【643】
2025-05-27 00:15:00.010 [pool-14-thread-3] INFO  com.simbest.boot.component.task.AbstractTaskSchedule.checkAndExecute Line:198 - 集群主控节点主机地址【10.92.82.161】运行端口【10031】,当前主机地址【10.87.57.73】运行端口【8092】, 无法执行定时任务【com.simbest.boot.processdata.pushWorkmanager.task.SyncTaskDataToWorkManagerTask】【639】
2025-05-27 00:15:00.010 [pool-14-thread-4] INFO  com.simbest.boot.component.task.AbstractTaskSchedule.checkAndExecute Line:198 - 集群主控节点主机地址【10.92.82.161】运行端口【10031】,当前主机地址【10.87.57.73】运行端口【8092】, 无法执行定时任务【com.simbest.boot.processdata.pushWorkmanager.task.SyncCommentDataToWorkManagerTask】【640】
2025-05-27 00:15:00.011 [pool-14-thread-8] INFO  com.simbest.boot.component.task.AbstractTaskSchedule.checkAndExecute Line:198 - 集群主控节点主机地址【10.92.82.161】运行端口【10031】,当前主机地址【10.87.57.73】运行端口【8092】, 无法执行定时任务【com.simbest.boot.bps.task.AutoCloseTodoTask】【644】
2025-05-27 00:15:00.011 [pool-14-thread-10] INFO  com.simbest.boot.component.task.AbstractTaskSchedule.checkAndExecute Line:198 - 集群主控节点主机地址【10.92.82.161】运行端口【10031】,当前主机地址【10.87.57.73】运行端口【8092】, 无法执行定时任务【com.simbest.boot.bps.task.AutoAddTodoTask】【646】
2025-05-27 00:15:00.011 [pool-14-thread-9] INFO  com.simbest.boot.component.task.AbstractTaskSchedule.checkAndExecute Line:198 - 集群主控节点主机地址【10.92.82.161】运行端口【10031】,当前主机地址【10.87.57.73】运行端口【8092】, 无法执行定时任务【com.simbest.boot.bps.task.AutoCheckWithUpdateTask】【645】
2025-05-27 00:16:00.004 [pool-14-thread-4] INFO  com.simbest.boot.component.task.AbstractTaskSchedule.checkAndExecute Line:198 - 集群主控节点主机地址【10.92.82.161】运行端口【10031】,当前主机地址【10.87.57.73】运行端口【8092】, 无法执行定时任务【com.simbest.boot.component.task.HeartTestTask】【640】
2025-05-27 00:17:00.006 [pool-14-thread-3] INFO  com.simbest.boot.component.task.AbstractTaskSchedule.checkAndExecute Line:198 - 集群主控节点主机地址【10.92.82.161】运行端口【10031】,当前主机地址【10.87.57.73】运行端口【8092】, 无法执行定时任务【com.simbest.boot.component.task.HeartTestTask】【639】
2025-05-27 00:18:00.006 [pool-14-thread-3] INFO  com.simbest.boot.component.task.AbstractTaskSchedule.checkAndExecute Line:198 - 集群主控节点主机地址【10.92.82.161】运行端口【10031】,当前主机地址【10.87.57.73】运行端口【8092】, 无法执行定时任务【com.simbest.boot.component.task.HeartTestTask】【639】
2025-05-27 00:19:00.002 [pool-14-thread-2] INFO  com.simbest.boot.component.task.AbstractTaskSchedule.checkAndExecute Line:198 - 集群主控节点主机地址【10.92.82.161】运行端口【10031】,当前主机地址【10.87.57.73】运行端口【8092】, 无法执行定时任务【com.simbest.boot.component.task.HeartTestTask】【638】
2025-05-27 00:20:00.001 [pool-14-thread-2] INFO  com.simbest.boot.component.task.AbstractTaskSchedule.checkAndExecute Line:198 - 集群主控节点主机地址【10.92.82.161】运行端口【10031】,当前主机地址【10.87.57.73】运行端口【8092】, 无法执行定时任务【com.simbest.boot.bps.task.AutoCloseTodoTask】【638】
2025-05-27 00:20:00.001 [pool-14-thread-6] INFO  com.simbest.boot.component.task.AbstractTaskSchedule.checkAndExecute Line:198 - 集群主控节点主机地址【10.92.82.161】运行端口【10031】,当前主机地址【10.87.57.73】运行端口【8092】, 无法执行定时任务【com.simbest.boot.processdata.pushWorkmanager.task.SyncTaskDataToWorkManagerTask】【642】
2025-05-27 00:20:00.001 [pool-14-thread-5] INFO  com.simbest.boot.component.task.AbstractTaskSchedule.checkAndExecute Line:198 - 集群主控节点主机地址【10.92.82.161】运行端口【10031】,当前主机地址【10.87.57.73】运行端口【8092】, 无法执行定时任务【com.simbest.boot.processdata.pushWorkmanager.task.SyncCommentDataToWorkManagerTask】【641】
2025-05-27 00:20:00.001 [pool-14-thread-9] INFO  com.simbest.boot.component.task.AbstractTaskSchedule.checkAndExecute Line:198 - 集群主控节点主机地址【10.92.82.161】运行端口【10031】,当前主机地址【10.87.57.73】运行端口【8092】, 无法执行定时任务【com.simbest.boot.component.task.HeartTestTask】【645】
2025-05-27 00:20:00.001 [pool-14-thread-8] INFO  com.simbest.boot.component.task.AbstractTaskSchedule.checkAndExecute Line:198 - 集群主控节点主机地址【10.92.82.161】运行端口【10031】,当前主机地址【10.87.57.73】运行端口【8092】, 无法执行定时任务【com.simbest.boot.bps.task.AutoAddTodoTask】【644】
2025-05-27 00:20:00.001 [pool-14-thread-10] INFO  com.simbest.boot.component.task.AbstractTaskSchedule.checkAndExecute Line:198 - 集群主控节点主机地址【10.92.82.161】运行端口【10031】,当前主机地址【10.87.57.73】运行端口【8092】, 无法执行定时任务【com.simbest.boot.bps.task.AutoCheckWithUpdateTask】【646】
2025-05-27 00:20:00.001 [pool-14-thread-4] INFO  com.simbest.boot.component.task.AbstractTaskSchedule.checkAndExecute Line:198 - 集群主控节点主机地址【10.92.82.161】运行端口【10031】,当前主机地址【10.87.57.73】运行端口【8092】, 无法执行定时任务【com.simbest.boot.processdata.pushWorkmanager.task.SyncProcessDataToWorkManagerTask】【640】
2025-05-27 00:21:00.005 [pool-14-thread-9] INFO  com.simbest.boot.component.task.AbstractTaskSchedule.checkAndExecute Line:198 - 集群主控节点主机地址【10.92.82.161】运行端口【10031】,当前主机地址【10.87.57.73】运行端口【8092】, 无法执行定时任务【com.simbest.boot.component.task.HeartTestTask】【645】
2025-05-27 00:22:00.008 [pool-14-thread-9] INFO  com.simbest.boot.component.task.AbstractTaskSchedule.checkAndExecute Line:198 - 集群主控节点主机地址【10.92.82.161】运行端口【10031】,当前主机地址【10.87.57.73】运行端口【8092】, 无法执行定时任务【com.simbest.boot.component.task.HeartTestTask】【645】
2025-05-27 00:23:00.011 [pool-14-thread-9] INFO  com.simbest.boot.component.task.AbstractTaskSchedule.checkAndExecute Line:198 - 集群主控节点主机地址【10.92.82.161】运行端口【10031】,当前主机地址【10.87.57.73】运行端口【8092】, 无法执行定时任务【com.simbest.boot.component.task.HeartTestTask】【645】
2025-05-27 00:24:00.006 [pool-14-thread-7] INFO  com.simbest.boot.component.task.AbstractTaskSchedule.checkAndExecute Line:198 - 集群主控节点主机地址【10.92.82.161】运行端口【10031】,当前主机地址【10.87.57.73】运行端口【8092】, 无法执行定时任务【com.simbest.boot.component.task.HeartTestTask】【643】
2025-05-27 00:25:00.009 [pool-14-thread-4] INFO  com.simbest.boot.component.task.AbstractTaskSchedule.checkAndExecute Line:198 - 集群主控节点主机地址【10.92.82.161】运行端口【10031】,当前主机地址【10.87.57.73】运行端口【8092】, 无法执行定时任务【com.simbest.boot.processdata.pushWorkmanager.task.SyncCommentDataToWorkManagerTask】【640】
2025-05-27 00:25:00.009 [pool-14-thread-10] INFO  com.simbest.boot.component.task.AbstractTaskSchedule.checkAndExecute Line:198 - 集群主控节点主机地址【10.92.82.161】运行端口【10031】,当前主机地址【10.87.57.73】运行端口【8092】, 无法执行定时任务【com.simbest.boot.bps.task.AutoAddTodoTask】【646】
2025-05-27 00:25:00.009 [pool-14-thread-2] INFO  com.simbest.boot.component.task.AbstractTaskSchedule.checkAndExecute Line:198 - 集群主控节点主机地址【10.92.82.161】运行端口【10031】,当前主机地址【10.87.57.73】运行端口【8092】, 无法执行定时任务【com.simbest.boot.bps.task.AutoCloseTodoTask】【638】
2025-05-27 00:25:00.009 [pool-14-thread-7] INFO  com.simbest.boot.component.task.AbstractTaskSchedule.checkAndExecute Line:198 - 集群主控节点主机地址【10.92.82.161】运行端口【10031】,当前主机地址【10.87.57.73】运行端口【8092】, 无法执行定时任务【com.simbest.boot.bps.task.AutoCheckWithUpdateTask】【643】
2025-05-27 00:25:00.009 [pool-14-thread-1] INFO  com.simbest.boot.component.task.AbstractTaskSchedule.checkAndExecute Line:198 - 集群主控节点主机地址【10.92.82.161】运行端口【10031】,当前主机地址【10.87.57.73】运行端口【8092】, 无法执行定时任务【com.simbest.boot.processdata.pushWorkmanager.task.SyncProcessDataToWorkManagerTask】【637】
2025-05-27 00:25:00.009 [pool-14-thread-9] INFO  com.simbest.boot.component.task.AbstractTaskSchedule.checkAndExecute Line:198 - 集群主控节点主机地址【10.92.82.161】运行端口【10031】,当前主机地址【10.87.57.73】运行端口【8092】, 无法执行定时任务【com.simbest.boot.component.task.HeartTestTask】【645】
2025-05-27 00:25:00.009 [pool-14-thread-8] INFO  com.simbest.boot.component.task.AbstractTaskSchedule.checkAndExecute Line:198 - 集群主控节点主机地址【10.92.82.161】运行端口【10031】,当前主机地址【10.87.57.73】运行端口【8092】, 无法执行定时任务【com.simbest.boot.processdata.pushWorkmanager.task.SyncTaskDataToWorkManagerTask】【644】
2025-05-27 00:26:00.009 [pool-14-thread-9] INFO  com.simbest.boot.component.task.AbstractTaskSchedule.checkAndExecute Line:198 - 集群主控节点主机地址【10.92.82.161】运行端口【10031】,当前主机地址【10.87.57.73】运行端口【8092】, 无法执行定时任务【com.simbest.boot.component.task.HeartTestTask】【645】
2025-05-27 00:47:10.997 [pool-14-thread-4] INFO  com.simbest.boot.component.task.AbstractTaskSchedule.checkAndExecute Line:198 - 集群主控节点主机地址【10.92.82.161】运行端口【10031】,当前主机地址【10.87.57.73】运行端口【8092】, 无法执行定时任务【com.simbest.boot.bps.task.AutoCloseTodoTask】【640】
2025-05-27 00:47:10.997 [pool-14-thread-1] INFO  com.simbest.boot.component.task.AbstractTaskSchedule.checkAndExecute Line:198 - 集群主控节点主机地址【10.92.82.161】运行端口【10031】,当前主机地址【10.87.57.73】运行端口【8092】, 无法执行定时任务【com.simbest.boot.component.task.LicenseCheckTask】【637】
2025-05-27 00:47:10.997 [pool-14-thread-2] INFO  com.simbest.boot.component.task.AbstractTaskSchedule.checkAndExecute Line:198 - 集群主控节点主机地址【10.92.82.161】运行端口【10031】,当前主机地址【10.87.57.73】运行端口【8092】, 无法执行定时任务【com.simbest.boot.processdata.pushWorkmanager.task.SyncProcessDataToWorkManagerTask】【638】
2025-05-27 00:47:10.997 [pool-14-thread-7] INFO  com.simbest.boot.component.task.AbstractTaskSchedule.checkAndExecute Line:198 - 集群主控节点主机地址【10.92.82.161】运行端口【10031】,当前主机地址【10.87.57.73】运行端口【8092】, 无法执行定时任务【com.simbest.boot.processdata.pushWorkmanager.task.SyncTaskDataToWorkManagerTask】【643】
2025-05-27 00:47:10.997 [pool-14-thread-9] INFO  com.simbest.boot.component.task.AbstractTaskSchedule.checkAndExecute Line:198 - 集群主控节点主机地址【10.92.82.161】运行端口【10031】,当前主机地址【10.87.57.73】运行端口【8092】, 无法执行定时任务【com.simbest.boot.component.task.HeartTestTask】【645】
2025-05-27 00:47:10.997 [pool-14-thread-10] INFO  com.simbest.boot.component.task.AbstractTaskSchedule.checkAndExecute Line:198 - 集群主控节点主机地址【10.92.82.161】运行端口【10031】,当前主机地址【10.87.57.73】运行端口【8092】, 无法执行定时任务【com.simbest.boot.bps.task.AutoCheckWithUpdateTask】【646】
2025-05-27 00:47:10.997 [pool-14-thread-4] INFO  com.simbest.boot.component.task.AbstractTaskSchedule.checkAndExecute Line:198 - 集群主控节点主机地址【10.92.82.161】运行端口【10031】,当前主机地址【10.87.57.73】运行端口【8092】, 无法执行定时任务【com.simbest.boot.processdata.pushWorkmanager.task.SyncCommentDataToWorkManagerTask】【640】
2025-05-27 00:47:10.997 [pool-14-thread-1] INFO  com.simbest.boot.component.task.AbstractTaskSchedule.checkAndExecute Line:198 - 集群主控节点主机地址【10.92.82.161】运行端口【10031】,当前主机地址【10.87.57.73】运行端口【8092】, 无法执行定时任务【com.simbest.boot.bps.task.AutoAddTodoTask】【637】
2025-05-27 00:47:11.001 [pool-14-thread-6] INFO  com.simbest.boot.component.task.AbstractTaskSchedule.checkAndExecute Line:198 - 集群主控节点主机地址【10.92.82.161】运行端口【10031】,当前主机地址【10.87.57.73】运行端口【8092】, 无法执行定时任务【com.simbest.boot.hnjjwz.task.Scheduler】【642】
2025-05-27 00:48:00.003 [pool-14-thread-4] INFO  com.simbest.boot.component.task.AbstractTaskSchedule.checkAndExecute Line:198 - 集群主控节点主机地址【10.92.82.161】运行端口【10031】,当前主机地址【10.87.57.73】运行端口【8092】, 无法执行定时任务【com.simbest.boot.component.task.HeartTestTask】【640】
2025-05-27 00:49:00.007 [pool-14-thread-1] INFO  com.simbest.boot.component.task.AbstractTaskSchedule.checkAndExecute Line:198 - 集群主控节点主机地址【10.92.82.161】运行端口【10031】,当前主机地址【10.87.57.73】运行端口【8092】, 无法执行定时任务【com.simbest.boot.component.task.HeartTestTask】【637】
2025-05-27 00:50:00.004 [pool-14-thread-10] INFO  com.simbest.boot.component.task.AbstractTaskSchedule.checkAndExecute Line:198 - 集群主控节点主机地址【10.92.82.161】运行端口【10031】,当前主机地址【10.87.57.73】运行端口【8092】, 无法执行定时任务【com.simbest.boot.bps.task.AutoCloseTodoTask】【646】
2025-05-27 00:50:00.004 [pool-14-thread-1] INFO  com.simbest.boot.component.task.AbstractTaskSchedule.checkAndExecute Line:198 - 集群主控节点主机地址【10.92.82.161】运行端口【10031】,当前主机地址【10.87.57.73】运行端口【8092】, 无法执行定时任务【com.simbest.boot.bps.task.AutoCheckWithUpdateTask】【637】
2025-05-27 00:50:00.004 [pool-14-thread-7] INFO  com.simbest.boot.component.task.AbstractTaskSchedule.checkAndExecute Line:198 - 集群主控节点主机地址【10.92.82.161】运行端口【10031】,当前主机地址【10.87.57.73】运行端口【8092】, 无法执行定时任务【com.simbest.boot.processdata.pushWorkmanager.task.SyncTaskDataToWorkManagerTask】【643】
2025-05-27 00:50:00.005 [pool-14-thread-8] INFO  com.simbest.boot.component.task.AbstractTaskSchedule.checkAndExecute Line:198 - 集群主控节点主机地址【10.92.82.161】运行端口【10031】,当前主机地址【10.87.57.73】运行端口【8092】, 无法执行定时任务【com.simbest.boot.bps.task.AutoAddTodoTask】【644】
2025-05-27 00:50:00.004 [pool-14-thread-2] INFO  com.simbest.boot.component.task.AbstractTaskSchedule.checkAndExecute Line:198 - 集群主控节点主机地址【10.92.82.161】运行端口【10031】,当前主机地址【10.87.57.73】运行端口【8092】, 无法执行定时任务【com.simbest.boot.processdata.pushWorkmanager.task.SyncCommentDataToWorkManagerTask】【638】
2025-05-27 00:50:00.006 [pool-14-thread-10] INFO  com.simbest.boot.component.task.AbstractTaskSchedule.checkAndExecute Line:198 - 集群主控节点主机地址【10.92.82.161】运行端口【10031】,当前主机地址【10.87.57.73】运行端口【8092】, 无法执行定时任务【com.simbest.boot.component.task.HeartTestTask】【646】
2025-05-27 00:50:00.004 [pool-14-thread-3] INFO  com.simbest.boot.component.task.AbstractTaskSchedule.checkAndExecute Line:198 - 集群主控节点主机地址【10.92.82.161】运行端口【10031】,当前主机地址【10.87.57.73】运行端口【8092】, 无法执行定时任务【com.simbest.boot.processdata.pushWorkmanager.task.SyncProcessDataToWorkManagerTask】【639】
2025-05-27 00:51:00.013 [pool-14-thread-6] INFO  com.simbest.boot.component.task.AbstractTaskSchedule.checkAndExecute Line:198 - 集群主控节点主机地址【10.92.82.161】运行端口【10031】,当前主机地址【10.87.57.73】运行端口【8092】, 无法执行定时任务【com.simbest.boot.component.task.HeartTestTask】【642】
2025-05-27 00:52:00.010 [pool-14-thread-7] INFO  com.simbest.boot.component.task.AbstractTaskSchedule.checkAndExecute Line:198 - 集群主控节点主机地址【10.92.82.161】运行端口【10031】,当前主机地址【10.87.57.73】运行端口【8092】, 无法执行定时任务【com.simbest.boot.component.task.HeartTestTask】【643】
2025-05-27 00:53:00.002 [pool-14-thread-9] INFO  com.simbest.boot.component.task.AbstractTaskSchedule.checkAndExecute Line:198 - 集群主控节点主机地址【10.92.82.161】运行端口【10031】,当前主机地址【10.87.57.73】运行端口【8092】, 无法执行定时任务【com.simbest.boot.component.task.HeartTestTask】【645】
2025-05-27 00:54:00.014 [pool-14-thread-2] INFO  com.simbest.boot.component.task.AbstractTaskSchedule.checkAndExecute Line:198 - 集群主控节点主机地址【10.92.82.161】运行端口【10031】,当前主机地址【10.87.57.73】运行端口【8092】, 无法执行定时任务【com.simbest.boot.component.task.HeartTestTask】【638】
2025-05-27 00:55:00.013 [pool-14-thread-8] INFO  com.simbest.boot.component.task.AbstractTaskSchedule.checkAndExecute Line:198 - 集群主控节点主机地址【10.92.82.161】运行端口【10031】,当前主机地址【10.87.57.73】运行端口【8092】, 无法执行定时任务【com.simbest.boot.processdata.pushWorkmanager.task.SyncProcessDataToWorkManagerTask】【644】
2025-05-27 00:55:00.013 [pool-14-thread-10] INFO  com.simbest.boot.component.task.AbstractTaskSchedule.checkAndExecute Line:198 - 集群主控节点主机地址【10.92.82.161】运行端口【10031】,当前主机地址【10.87.57.73】运行端口【8092】, 无法执行定时任务【com.simbest.boot.bps.task.AutoCloseTodoTask】【646】
2025-05-27 00:55:00.013 [pool-14-thread-6] INFO  com.simbest.boot.component.task.AbstractTaskSchedule.checkAndExecute Line:198 - 集群主控节点主机地址【10.92.82.161】运行端口【10031】,当前主机地址【10.87.57.73】运行端口【8092】, 无法执行定时任务【com.simbest.boot.processdata.pushWorkmanager.task.SyncCommentDataToWorkManagerTask】【642】
2025-05-27 00:55:00.013 [pool-14-thread-7] INFO  com.simbest.boot.component.task.AbstractTaskSchedule.checkAndExecute Line:198 - 集群主控节点主机地址【10.92.82.161】运行端口【10031】,当前主机地址【10.87.57.73】运行端口【8092】, 无法执行定时任务【com.simbest.boot.processdata.pushWorkmanager.task.SyncTaskDataToWorkManagerTask】【643】
2025-05-27 00:55:00.013 [pool-14-thread-3] INFO  com.simbest.boot.component.task.AbstractTaskSchedule.checkAndExecute Line:198 - 集群主控节点主机地址【10.92.82.161】运行端口【10031】,当前主机地址【10.87.57.73】运行端口【8092】, 无法执行定时任务【com.simbest.boot.bps.task.AutoCheckWithUpdateTask】【639】
2025-05-27 00:55:00.013 [pool-14-thread-9] INFO  com.simbest.boot.component.task.AbstractTaskSchedule.checkAndExecute Line:198 - 集群主控节点主机地址【10.92.82.161】运行端口【10031】,当前主机地址【10.87.57.73】运行端口【8092】, 无法执行定时任务【com.simbest.boot.component.task.HeartTestTask】【645】
2025-05-27 00:55:00.013 [pool-14-thread-1] INFO  com.simbest.boot.component.task.AbstractTaskSchedule.checkAndExecute Line:198 - 集群主控节点主机地址【10.92.82.161】运行端口【10031】,当前主机地址【10.87.57.73】运行端口【8092】, 无法执行定时任务【com.simbest.boot.bps.task.AutoAddTodoTask】【637】
2025-05-27 00:56:00.007 [pool-14-thread-7] INFO  com.simbest.boot.component.task.AbstractTaskSchedule.checkAndExecute Line:198 - 集群主控节点主机地址【10.92.82.161】运行端口【10031】,当前主机地址【10.87.57.73】运行端口【8092】, 无法执行定时任务【com.simbest.boot.component.task.HeartTestTask】【643】
2025-05-27 00:57:00.001 [pool-14-thread-7] INFO  com.simbest.boot.component.task.AbstractTaskSchedule.checkAndExecute Line:198 - 集群主控节点主机地址【10.92.82.161】运行端口【10031】,当前主机地址【10.87.57.73】运行端口【8092】, 无法执行定时任务【com.simbest.boot.component.task.HeartTestTask】【643】
2025-05-27 00:58:00.002 [pool-14-thread-2] INFO  com.simbest.boot.component.task.AbstractTaskSchedule.checkAndExecute Line:198 - 集群主控节点主机地址【10.92.82.161】运行端口【10031】,当前主机地址【10.87.57.73】运行端口【8092】, 无法执行定时任务【com.simbest.boot.component.task.HeartTestTask】【638】
2025-05-27 00:59:00.005 [pool-14-thread-7] INFO  com.simbest.boot.component.task.AbstractTaskSchedule.checkAndExecute Line:198 - 集群主控节点主机地址【10.92.82.161】运行端口【10031】,当前主机地址【10.87.57.73】运行端口【8092】, 无法执行定时任务【com.simbest.boot.component.task.HeartTestTask】【643】
2025-05-27 00:59:59.970 [pool-14-thread-5] INFO  com.simbest.boot.component.task.AbstractTaskSchedule.checkAndExecute Line:198 - 集群主控节点主机地址【10.92.82.161】运行端口【10031】,当前主机地址【10.87.57.73】运行端口【8092】, 无法执行定时任务【com.simbest.boot.cmcc.a4.task.A4TaskCreateXmlFile】【641】
2025-05-27 01:00:00.002 [pool-14-thread-7] INFO  com.simbest.boot.component.task.AbstractTaskSchedule.checkAndExecute Line:198 - 集群主控节点主机地址【10.92.82.161】运行端口【10031】,当前主机地址【10.87.57.73】运行端口【8092】, 无法执行定时任务【com.simbest.boot.bps.task.AutoCloseTodoTask】【643】
2025-05-27 01:00:00.002 [pool-14-thread-2] INFO  com.simbest.boot.component.task.AbstractTaskSchedule.checkAndExecute Line:198 - 集群主控节点主机地址【10.92.82.161】运行端口【10031】,当前主机地址【10.87.57.73】运行端口【8092】, 无法执行定时任务【com.simbest.boot.bps.task.AutoCheckWithUpdateTask】【638】
2025-05-27 01:00:00.002 [pool-14-thread-1] INFO  com.simbest.boot.component.task.AbstractTaskSchedule.checkAndExecute Line:198 - 集群主控节点主机地址【10.92.82.161】运行端口【10031】,当前主机地址【10.87.57.73】运行端口【8092】, 无法执行定时任务【com.simbest.boot.processdata.pushWorkmanager.task.SyncTaskDataToWorkManagerTask】【637】
2025-05-27 01:00:00.003 [pool-14-thread-7] INFO  com.simbest.boot.component.task.AbstractTaskSchedule.checkAndExecute Line:198 - 集群主控节点主机地址【10.92.82.161】运行端口【10031】,当前主机地址【10.87.57.73】运行端口【8092】, 无法执行定时任务【com.simbest.boot.bps.task.AutoAddTodoTask】【643】
2025-05-27 01:00:00.002 [pool-14-thread-8] INFO  com.simbest.boot.component.task.AbstractTaskSchedule.checkAndExecute Line:198 - 集群主控节点主机地址【10.92.82.161】运行端口【10031】,当前主机地址【10.87.57.73】运行端口【8092】, 无法执行定时任务【com.simbest.boot.component.task.LicenseCheckTask】【644】
2025-05-27 01:00:00.002 [pool-14-thread-10] INFO  com.simbest.boot.component.task.AbstractTaskSchedule.checkAndExecute Line:198 - 集群主控节点主机地址【10.92.82.161】运行端口【10031】,当前主机地址【10.87.57.73】运行端口【8092】, 无法执行定时任务【com.simbest.boot.processdata.pushWorkmanager.task.SyncCommentDataToWorkManagerTask】【646】
2025-05-27 01:00:00.002 [pool-14-thread-9] INFO  com.simbest.boot.component.task.AbstractTaskSchedule.checkAndExecute Line:198 - 集群主控节点主机地址【10.92.82.161】运行端口【10031】,当前主机地址【10.87.57.73】运行端口【8092】, 无法执行定时任务【com.simbest.boot.processdata.pushWorkmanager.task.SyncProcessDataToWorkManagerTask】【645】
2025-05-27 01:00:00.002 [pool-14-thread-4] INFO  com.simbest.boot.component.task.AbstractTaskSchedule.checkAndExecute Line:198 - 集群主控节点主机地址【10.92.82.161】运行端口【10031】,当前主机地址【10.87.57.73】运行端口【8092】, 无法执行定时任务【com.simbest.boot.component.task.HeartTestTask】【640】
2025-05-27 01:00:01.007 [pool-14-thread-8] INFO  com.simbest.boot.component.task.AbstractTaskSchedule.checkAndExecute Line:198 - 集群主控节点主机地址【10.92.82.161】运行端口【10031】,当前主机地址【10.87.57.73】运行端口【8092】, 无法执行定时任务【com.simbest.boot.component.task.LicenseCheckTask】【644】
2025-05-27 01:00:02.015 [pool-14-thread-8] INFO  com.simbest.boot.component.task.AbstractTaskSchedule.checkAndExecute Line:198 - 集群主控节点主机地址【10.92.82.161】运行端口【10031】,当前主机地址【10.87.57.73】运行端口【8092】, 无法执行定时任务【com.simbest.boot.component.task.LicenseCheckTask】【644】
2025-05-27 01:00:03.006 [pool-14-thread-8] INFO  com.simbest.boot.component.task.AbstractTaskSchedule.checkAndExecute Line:198 - 集群主控节点主机地址【10.92.82.161】运行端口【10031】,当前主机地址【10.87.57.73】运行端口【8092】, 无法执行定时任务【com.simbest.boot.component.task.LicenseCheckTask】【644】
2025-05-27 01:00:04.011 [pool-14-thread-4] INFO  com.simbest.boot.component.task.AbstractTaskSchedule.checkAndExecute Line:198 - 集群主控节点主机地址【10.92.82.161】运行端口【10031】,当前主机地址【10.87.57.73】运行端口【8092】, 无法执行定时任务【com.simbest.boot.component.task.LicenseCheckTask】【640】
2025-05-27 01:00:05.003 [pool-14-thread-6] INFO  com.simbest.boot.component.task.AbstractTaskSchedule.checkAndExecute Line:198 - 集群主控节点主机地址【10.92.82.161】运行端口【10031】,当前主机地址【10.87.57.73】运行端口【8092】, 无法执行定时任务【com.simbest.boot.component.task.LicenseCheckTask】【642】
2025-05-27 01:00:06.009 [pool-14-thread-1] INFO  com.simbest.boot.component.task.AbstractTaskSchedule.checkAndExecute Line:198 - 集群主控节点主机地址【10.92.82.161】运行端口【10031】,当前主机地址【10.87.57.73】运行端口【8092】, 无法执行定时任务【com.simbest.boot.component.task.LicenseCheckTask】【637】
2025-05-27 01:00:07.002 [pool-14-thread-1] INFO  com.simbest.boot.component.task.AbstractTaskSchedule.checkAndExecute Line:198 - 集群主控节点主机地址【10.92.82.161】运行端口【10031】,当前主机地址【10.87.57.73】运行端口【8092】, 无法执行定时任务【com.simbest.boot.component.task.LicenseCheckTask】【637】
2025-05-27 01:00:08.011 [pool-14-thread-1] INFO  com.simbest.boot.component.task.AbstractTaskSchedule.checkAndExecute Line:198 - 集群主控节点主机地址【10.92.82.161】运行端口【10031】,当前主机地址【10.87.57.73】运行端口【8092】, 无法执行定时任务【com.simbest.boot.component.task.LicenseCheckTask】【637】
2025-05-27 01:00:09.015 [pool-14-thread-1] INFO  com.simbest.boot.component.task.AbstractTaskSchedule.checkAndExecute Line:198 - 集群主控节点主机地址【10.92.82.161】运行端口【10031】,当前主机地址【10.87.57.73】运行端口【8092】, 无法执行定时任务【com.simbest.boot.component.task.LicenseCheckTask】【637】
2025-05-27 01:00:10.007 [pool-14-thread-1] INFO  com.simbest.boot.component.task.AbstractTaskSchedule.checkAndExecute Line:198 - 集群主控节点主机地址【10.92.82.161】运行端口【10031】,当前主机地址【10.87.57.73】运行端口【8092】, 无法执行定时任务【com.simbest.boot.component.task.LicenseCheckTask】【637】
2025-05-27 01:00:11.012 [pool-14-thread-1] INFO  com.simbest.boot.component.task.AbstractTaskSchedule.checkAndExecute Line:198 - 集群主控节点主机地址【10.92.82.161】运行端口【10031】,当前主机地址【10.87.57.73】运行端口【8092】, 无法执行定时任务【com.simbest.boot.component.task.LicenseCheckTask】【637】
2025-05-27 01:00:12.004 [pool-14-thread-1] INFO  com.simbest.boot.component.task.AbstractTaskSchedule.checkAndExecute Line:198 - 集群主控节点主机地址【10.92.82.161】运行端口【10031】,当前主机地址【10.87.57.73】运行端口【8092】, 无法执行定时任务【com.simbest.boot.component.task.LicenseCheckTask】【637】
2025-05-27 01:00:13.005 [pool-14-thread-1] INFO  com.simbest.boot.component.task.AbstractTaskSchedule.checkAndExecute Line:198 - 集群主控节点主机地址【10.92.82.161】运行端口【10031】,当前主机地址【10.87.57.73】运行端口【8092】, 无法执行定时任务【com.simbest.boot.component.task.LicenseCheckTask】【637】
2025-05-27 01:00:14.011 [pool-14-thread-1] INFO  com.simbest.boot.component.task.AbstractTaskSchedule.checkAndExecute Line:198 - 集群主控节点主机地址【10.92.82.161】运行端口【10031】,当前主机地址【10.87.57.73】运行端口【8092】, 无法执行定时任务【com.simbest.boot.component.task.LicenseCheckTask】【637】
2025-05-27 01:00:15.005 [pool-14-thread-1] INFO  com.simbest.boot.component.task.AbstractTaskSchedule.checkAndExecute Line:198 - 集群主控节点主机地址【10.92.82.161】运行端口【10031】,当前主机地址【10.87.57.73】运行端口【8092】, 无法执行定时任务【com.simbest.boot.component.task.LicenseCheckTask】【637】
2025-05-27 01:00:16.014 [pool-14-thread-1] INFO  com.simbest.boot.component.task.AbstractTaskSchedule.checkAndExecute Line:198 - 集群主控节点主机地址【10.92.82.161】运行端口【10031】,当前主机地址【10.87.57.73】运行端口【8092】, 无法执行定时任务【com.simbest.boot.component.task.LicenseCheckTask】【637】
2025-05-27 01:00:17.007 [pool-14-thread-1] INFO  com.simbest.boot.component.task.AbstractTaskSchedule.checkAndExecute Line:198 - 集群主控节点主机地址【10.92.82.161】运行端口【10031】,当前主机地址【10.87.57.73】运行端口【8092】, 无法执行定时任务【com.simbest.boot.component.task.LicenseCheckTask】【637】
2025-05-27 01:00:18.010 [pool-14-thread-1] INFO  com.simbest.boot.component.task.AbstractTaskSchedule.checkAndExecute Line:198 - 集群主控节点主机地址【10.92.82.161】运行端口【10031】,当前主机地址【10.87.57.73】运行端口【8092】, 无法执行定时任务【com.simbest.boot.component.task.LicenseCheckTask】【637】
2025-05-27 01:00:19.003 [pool-14-thread-1] INFO  com.simbest.boot.component.task.AbstractTaskSchedule.checkAndExecute Line:198 - 集群主控节点主机地址【10.92.82.161】运行端口【10031】,当前主机地址【10.87.57.73】运行端口【8092】, 无法执行定时任务【com.simbest.boot.component.task.LicenseCheckTask】【637】
2025-05-27 01:00:20.013 [pool-14-thread-1] INFO  com.simbest.boot.component.task.AbstractTaskSchedule.checkAndExecute Line:198 - 集群主控节点主机地址【10.92.82.161】运行端口【10031】,当前主机地址【10.87.57.73】运行端口【8092】, 无法执行定时任务【com.simbest.boot.component.task.LicenseCheckTask】【637】
2025-05-27 01:00:21.007 [pool-14-thread-1] INFO  com.simbest.boot.component.task.AbstractTaskSchedule.checkAndExecute Line:198 - 集群主控节点主机地址【10.92.82.161】运行端口【10031】,当前主机地址【10.87.57.73】运行端口【8092】, 无法执行定时任务【com.simbest.boot.component.task.LicenseCheckTask】【637】
2025-05-27 01:00:22.012 [pool-14-thread-1] INFO  com.simbest.boot.component.task.AbstractTaskSchedule.checkAndExecute Line:198 - 集群主控节点主机地址【10.92.82.161】运行端口【10031】,当前主机地址【10.87.57.73】运行端口【8092】, 无法执行定时任务【com.simbest.boot.component.task.LicenseCheckTask】【637】
2025-05-27 01:00:23.004 [pool-14-thread-1] INFO  com.simbest.boot.component.task.AbstractTaskSchedule.checkAndExecute Line:198 - 集群主控节点主机地址【10.92.82.161】运行端口【10031】,当前主机地址【10.87.57.73】运行端口【8092】, 无法执行定时任务【com.simbest.boot.component.task.LicenseCheckTask】【637】
2025-05-27 01:00:24.013 [pool-14-thread-1] INFO  com.simbest.boot.component.task.AbstractTaskSchedule.checkAndExecute Line:198 - 集群主控节点主机地址【10.92.82.161】运行端口【10031】,当前主机地址【10.87.57.73】运行端口【8092】, 无法执行定时任务【com.simbest.boot.component.task.LicenseCheckTask】【637】
2025-05-27 01:00:25.008 [pool-14-thread-1] INFO  com.simbest.boot.component.task.AbstractTaskSchedule.checkAndExecute Line:198 - 集群主控节点主机地址【10.92.82.161】运行端口【10031】,当前主机地址【10.87.57.73】运行端口【8092】, 无法执行定时任务【com.simbest.boot.component.task.LicenseCheckTask】【637】
2025-05-27 01:00:26.012 [pool-14-thread-1] INFO  com.simbest.boot.component.task.AbstractTaskSchedule.checkAndExecute Line:198 - 集群主控节点主机地址【10.92.82.161】运行端口【10031】,当前主机地址【10.87.57.73】运行端口【8092】, 无法执行定时任务【com.simbest.boot.component.task.LicenseCheckTask】【637】
2025-05-27 01:00:27.005 [pool-14-thread-1] INFO  com.simbest.boot.component.task.AbstractTaskSchedule.checkAndExecute Line:198 - 集群主控节点主机地址【10.92.82.161】运行端口【10031】,当前主机地址【10.87.57.73】运行端口【8092】, 无法执行定时任务【com.simbest.boot.component.task.LicenseCheckTask】【637】
2025-05-27 01:00:28.006 [pool-14-thread-1] INFO  com.simbest.boot.component.task.AbstractTaskSchedule.checkAndExecute Line:198 - 集群主控节点主机地址【10.92.82.161】运行端口【10031】,当前主机地址【10.87.57.73】运行端口【8092】, 无法执行定时任务【com.simbest.boot.component.task.LicenseCheckTask】【637】
2025-05-27 01:00:29.014 [pool-14-thread-1] INFO  com.simbest.boot.component.task.AbstractTaskSchedule.checkAndExecute Line:198 - 集群主控节点主机地址【10.92.82.161】运行端口【10031】,当前主机地址【10.87.57.73】运行端口【8092】, 无法执行定时任务【com.simbest.boot.component.task.LicenseCheckTask】【637】
2025-05-27 01:00:30.010 [pool-14-thread-1] INFO  com.simbest.boot.component.task.AbstractTaskSchedule.checkAndExecute Line:198 - 集群主控节点主机地址【10.92.82.161】运行端口【10031】,当前主机地址【10.87.57.73】运行端口【8092】, 无法执行定时任务【com.simbest.boot.component.task.LicenseCheckTask】【637】
2025-05-27 01:00:31.009 [pool-14-thread-8] INFO  com.simbest.boot.component.task.AbstractTaskSchedule.checkAndExecute Line:198 - 集群主控节点主机地址【10.92.82.161】运行端口【10031】,当前主机地址【10.87.57.73】运行端口【8092】, 无法执行定时任务【com.simbest.boot.component.task.LicenseCheckTask】【644】
2025-05-27 01:00:32.014 [pool-14-thread-8] INFO  com.simbest.boot.component.task.AbstractTaskSchedule.checkAndExecute Line:198 - 集群主控节点主机地址【10.92.82.161】运行端口【10031】,当前主机地址【10.87.57.73】运行端口【8092】, 无法执行定时任务【com.simbest.boot.component.task.LicenseCheckTask】【644】
2025-05-27 01:00:33.006 [pool-14-thread-8] INFO  com.simbest.boot.component.task.AbstractTaskSchedule.checkAndExecute Line:198 - 集群主控节点主机地址【10.92.82.161】运行端口【10031】,当前主机地址【10.87.57.73】运行端口【8092】, 无法执行定时任务【com.simbest.boot.component.task.LicenseCheckTask】【644】
2025-05-27 01:00:34.013 [pool-14-thread-8] INFO  com.simbest.boot.component.task.AbstractTaskSchedule.checkAndExecute Line:198 - 集群主控节点主机地址【10.92.82.161】运行端口【10031】,当前主机地址【10.87.57.73】运行端口【8092】, 无法执行定时任务【com.simbest.boot.component.task.LicenseCheckTask】【644】
2025-05-27 01:00:35.008 [pool-14-thread-8] INFO  com.simbest.boot.component.task.AbstractTaskSchedule.checkAndExecute Line:198 - 集群主控节点主机地址【10.92.82.161】运行端口【10031】,当前主机地址【10.87.57.73】运行端口【8092】, 无法执行定时任务【com.simbest.boot.component.task.LicenseCheckTask】【644】
2025-05-27 01:00:36.013 [pool-14-thread-8] INFO  com.simbest.boot.component.task.AbstractTaskSchedule.checkAndExecute Line:198 - 集群主控节点主机地址【10.92.82.161】运行端口【10031】,当前主机地址【10.87.57.73】运行端口【8092】, 无法执行定时任务【com.simbest.boot.component.task.LicenseCheckTask】【644】
2025-05-27 01:00:37.007 [pool-14-thread-8] INFO  com.simbest.boot.component.task.AbstractTaskSchedule.checkAndExecute Line:198 - 集群主控节点主机地址【10.92.82.161】运行端口【10031】,当前主机地址【10.87.57.73】运行端口【8092】, 无法执行定时任务【com.simbest.boot.component.task.LicenseCheckTask】【644】
2025-05-27 01:00:38.014 [pool-14-thread-8] INFO  com.simbest.boot.component.task.AbstractTaskSchedule.checkAndExecute Line:198 - 集群主控节点主机地址【10.92.82.161】运行端口【10031】,当前主机地址【10.87.57.73】运行端口【8092】, 无法执行定时任务【com.simbest.boot.component.task.LicenseCheckTask】【644】
2025-05-27 01:00:39.006 [pool-14-thread-8] INFO  com.simbest.boot.component.task.AbstractTaskSchedule.checkAndExecute Line:198 - 集群主控节点主机地址【10.92.82.161】运行端口【10031】,当前主机地址【10.87.57.73】运行端口【8092】, 无法执行定时任务【com.simbest.boot.component.task.LicenseCheckTask】【644】
2025-05-27 01:00:40.008 [pool-14-thread-8] INFO  com.simbest.boot.component.task.AbstractTaskSchedule.checkAndExecute Line:198 - 集群主控节点主机地址【10.92.82.161】运行端口【10031】,当前主机地址【10.87.57.73】运行端口【8092】, 无法执行定时任务【com.simbest.boot.component.task.LicenseCheckTask】【644】
2025-05-27 01:00:41.015 [pool-14-thread-8] INFO  com.simbest.boot.component.task.AbstractTaskSchedule.checkAndExecute Line:198 - 集群主控节点主机地址【10.92.82.161】运行端口【10031】,当前主机地址【10.87.57.73】运行端口【8092】, 无法执行定时任务【com.simbest.boot.component.task.LicenseCheckTask】【644】
2025-05-27 01:00:42.006 [pool-14-thread-8] INFO  com.simbest.boot.component.task.AbstractTaskSchedule.checkAndExecute Line:198 - 集群主控节点主机地址【10.92.82.161】运行端口【10031】,当前主机地址【10.87.57.73】运行端口【8092】, 无法执行定时任务【com.simbest.boot.component.task.LicenseCheckTask】【644】
2025-05-27 01:00:43.012 [pool-14-thread-8] INFO  com.simbest.boot.component.task.AbstractTaskSchedule.checkAndExecute Line:198 - 集群主控节点主机地址【10.92.82.161】运行端口【10031】,当前主机地址【10.87.57.73】运行端口【8092】, 无法执行定时任务【com.simbest.boot.component.task.LicenseCheckTask】【644】
2025-05-27 01:00:44.013 [pool-14-thread-8] INFO  com.simbest.boot.component.task.AbstractTaskSchedule.checkAndExecute Line:198 - 集群主控节点主机地址【10.92.82.161】运行端口【10031】,当前主机地址【10.87.57.73】运行端口【8092】, 无法执行定时任务【com.simbest.boot.component.task.LicenseCheckTask】【644】
2025-05-27 01:00:45.004 [pool-14-thread-8] INFO  com.simbest.boot.component.task.AbstractTaskSchedule.checkAndExecute Line:198 - 集群主控节点主机地址【10.92.82.161】运行端口【10031】,当前主机地址【10.87.57.73】运行端口【8092】, 无法执行定时任务【com.simbest.boot.component.task.LicenseCheckTask】【644】
2025-05-27 01:00:46.011 [pool-14-thread-8] INFO  com.simbest.boot.component.task.AbstractTaskSchedule.checkAndExecute Line:198 - 集群主控节点主机地址【10.92.82.161】运行端口【10031】,当前主机地址【10.87.57.73】运行端口【8092】, 无法执行定时任务【com.simbest.boot.component.task.LicenseCheckTask】【644】
2025-05-27 01:00:47.005 [pool-14-thread-8] INFO  com.simbest.boot.component.task.AbstractTaskSchedule.checkAndExecute Line:198 - 集群主控节点主机地址【10.92.82.161】运行端口【10031】,当前主机地址【10.87.57.73】运行端口【8092】, 无法执行定时任务【com.simbest.boot.component.task.LicenseCheckTask】【644】
2025-05-27 01:00:48.013 [pool-14-thread-8] INFO  com.simbest.boot.component.task.AbstractTaskSchedule.checkAndExecute Line:198 - 集群主控节点主机地址【10.92.82.161】运行端口【10031】,当前主机地址【10.87.57.73】运行端口【8092】, 无法执行定时任务【com.simbest.boot.component.task.LicenseCheckTask】【644】
2025-05-27 01:00:49.015 [pool-14-thread-8] INFO  com.simbest.boot.component.task.AbstractTaskSchedule.checkAndExecute Line:198 - 集群主控节点主机地址【10.92.82.161】运行端口【10031】,当前主机地址【10.87.57.73】运行端口【8092】, 无法执行定时任务【com.simbest.boot.component.task.LicenseCheckTask】【644】
2025-05-27 01:00:50.003 [pool-14-thread-8] INFO  com.simbest.boot.component.task.AbstractTaskSchedule.checkAndExecute Line:198 - 集群主控节点主机地址【10.92.82.161】运行端口【10031】,当前主机地址【10.87.57.73】运行端口【8092】, 无法执行定时任务【com.simbest.boot.component.task.LicenseCheckTask】【644】
2025-05-27 01:00:51.006 [pool-14-thread-6] INFO  com.simbest.boot.component.task.AbstractTaskSchedule.checkAndExecute Line:198 - 集群主控节点主机地址【10.92.82.161】运行端口【10031】,当前主机地址【10.87.57.73】运行端口【8092】, 无法执行定时任务【com.simbest.boot.component.task.LicenseCheckTask】【642】
2025-05-27 01:00:52.002 [pool-14-thread-2] INFO  com.simbest.boot.component.task.AbstractTaskSchedule.checkAndExecute Line:198 - 集群主控节点主机地址【10.92.82.161】运行端口【10031】,当前主机地址【10.87.57.73】运行端口【8092】, 无法执行定时任务【com.simbest.boot.component.task.LicenseCheckTask】【638】
2025-05-27 01:00:53.007 [pool-14-thread-2] INFO  com.simbest.boot.component.task.AbstractTaskSchedule.checkAndExecute Line:198 - 集群主控节点主机地址【10.92.82.161】运行端口【10031】,当前主机地址【10.87.57.73】运行端口【8092】, 无法执行定时任务【com.simbest.boot.component.task.LicenseCheckTask】【638】
2025-05-27 01:00:54.010 [pool-14-thread-2] INFO  com.simbest.boot.component.task.AbstractTaskSchedule.checkAndExecute Line:198 - 集群主控节点主机地址【10.92.82.161】运行端口【10031】,当前主机地址【10.87.57.73】运行端口【8092】, 无法执行定时任务【com.simbest.boot.component.task.LicenseCheckTask】【638】
2025-05-27 01:00:55.003 [pool-14-thread-2] INFO  com.simbest.boot.component.task.AbstractTaskSchedule.checkAndExecute Line:198 - 集群主控节点主机地址【10.92.82.161】运行端口【10031】,当前主机地址【10.87.57.73】运行端口【8092】, 无法执行定时任务【com.simbest.boot.component.task.LicenseCheckTask】【638】
2025-05-27 01:00:56.007 [pool-14-thread-7] INFO  com.simbest.boot.component.task.AbstractTaskSchedule.checkAndExecute Line:198 - 集群主控节点主机地址【10.92.82.161】运行端口【10031】,当前主机地址【10.87.57.73】运行端口【8092】, 无法执行定时任务【com.simbest.boot.component.task.LicenseCheckTask】【643】
2025-05-27 01:00:57.015 [pool-14-thread-7] INFO  com.simbest.boot.component.task.AbstractTaskSchedule.checkAndExecute Line:198 - 集群主控节点主机地址【10.92.82.161】运行端口【10031】,当前主机地址【10.87.57.73】运行端口【8092】, 无法执行定时任务【com.simbest.boot.component.task.LicenseCheckTask】【643】
2025-05-27 01:00:58.005 [pool-14-thread-1] INFO  com.simbest.boot.component.task.AbstractTaskSchedule.checkAndExecute Line:198 - 集群主控节点主机地址【10.92.82.161】运行端口【10031】,当前主机地址【10.87.57.73】运行端口【8092】, 无法执行定时任务【com.simbest.boot.component.task.LicenseCheckTask】【637】
2025-05-27 01:00:59.010 [pool-14-thread-1] INFO  com.simbest.boot.component.task.AbstractTaskSchedule.checkAndExecute Line:198 - 集群主控节点主机地址【10.92.82.161】运行端口【10031】,当前主机地址【10.87.57.73】运行端口【8092】, 无法执行定时任务【com.simbest.boot.component.task.LicenseCheckTask】【637】
2025-05-27 01:01:00.001 [pool-14-thread-9] INFO  com.simbest.boot.component.task.AbstractTaskSchedule.checkAndExecute Line:198 - 集群主控节点主机地址【10.92.82.161】运行端口【10031】,当前主机地址【10.87.57.73】运行端口【8092】, 无法执行定时任务【com.simbest.boot.component.task.HeartTestTask】【645】
2025-05-27 01:02:00.013 [pool-14-thread-2] INFO  com.simbest.boot.component.task.AbstractTaskSchedule.checkAndExecute Line:198 - 集群主控节点主机地址【10.92.82.161】运行端口【10031】,当前主机地址【10.87.57.73】运行端口【8092】, 无法执行定时任务【com.simbest.boot.component.task.HeartTestTask】【638】
2025-05-27 01:03:00.006 [pool-14-thread-8] INFO  com.simbest.boot.component.task.AbstractTaskSchedule.checkAndExecute Line:198 - 集群主控节点主机地址【10.92.82.161】运行端口【10031】,当前主机地址【10.87.57.73】运行端口【8092】, 无法执行定时任务【com.simbest.boot.component.task.HeartTestTask】【644】
2025-05-27 01:04:00.002 [pool-14-thread-8] INFO  com.simbest.boot.component.task.AbstractTaskSchedule.checkAndExecute Line:198 - 集群主控节点主机地址【10.92.82.161】运行端口【10031】,当前主机地址【10.87.57.73】运行端口【8092】, 无法执行定时任务【com.simbest.boot.component.task.HeartTestTask】【644】
2025-05-27 01:05:00.011 [pool-14-thread-6] INFO  com.simbest.boot.component.task.AbstractTaskSchedule.checkAndExecute Line:198 - 集群主控节点主机地址【10.92.82.161】运行端口【10031】,当前主机地址【10.87.57.73】运行端口【8092】, 无法执行定时任务【com.simbest.boot.bps.task.AutoCloseTodoTask】【642】
2025-05-27 01:05:00.012 [pool-14-thread-5] INFO  com.simbest.boot.component.task.AbstractTaskSchedule.checkAndExecute Line:198 - 集群主控节点主机地址【10.92.82.161】运行端口【10031】,当前主机地址【10.87.57.73】运行端口【8092】, 无法执行定时任务【com.simbest.boot.processdata.pushWorkmanager.task.SyncProcessDataToWorkManagerTask】【641】
2025-05-27 01:05:00.011 [pool-14-thread-10] INFO  com.simbest.boot.component.task.AbstractTaskSchedule.checkAndExecute Line:198 - 集群主控节点主机地址【10.92.82.161】运行端口【10031】,当前主机地址【10.87.57.73】运行端口【8092】, 无法执行定时任务【com.simbest.boot.processdata.pushWorkmanager.task.SyncCommentDataToWorkManagerTask】【646】
2025-05-27 01:05:00.012 [pool-14-thread-4] INFO  com.simbest.boot.component.task.AbstractTaskSchedule.checkAndExecute Line:198 - 集群主控节点主机地址【10.92.82.161】运行端口【10031】,当前主机地址【10.87.57.73】运行端口【8092】, 无法执行定时任务【com.simbest.boot.component.task.HeartTestTask】【640】
2025-05-27 01:05:00.012 [pool-14-thread-8] INFO  com.simbest.boot.component.task.AbstractTaskSchedule.checkAndExecute Line:198 - 集群主控节点主机地址【10.92.82.161】运行端口【10031】,当前主机地址【10.87.57.73】运行端口【8092】, 无法执行定时任务【com.simbest.boot.processdata.pushWorkmanager.task.SyncTaskDataToWorkManagerTask】【644】
2025-05-27 01:05:00.012 [pool-14-thread-7] INFO  com.simbest.boot.component.task.AbstractTaskSchedule.checkAndExecute Line:198 - 集群主控节点主机地址【10.92.82.161】运行端口【10031】,当前主机地址【10.87.57.73】运行端口【8092】, 无法执行定时任务【com.simbest.boot.bps.task.AutoAddTodoTask】【643】
2025-05-27 01:05:00.012 [pool-14-thread-2] INFO  com.simbest.boot.component.task.AbstractTaskSchedule.checkAndExecute Line:198 - 集群主控节点主机地址【10.92.82.161】运行端口【10031】,当前主机地址【10.87.57.73】运行端口【8092】, 无法执行定时任务【com.simbest.boot.bps.task.AutoCheckWithUpdateTask】【638】
2025-05-27 01:06:00.011 [pool-14-thread-9] INFO  com.simbest.boot.component.task.AbstractTaskSchedule.checkAndExecute Line:198 - 集群主控节点主机地址【10.92.82.161】运行端口【10031】,当前主机地址【10.87.57.73】运行端口【8092】, 无法执行定时任务【com.simbest.boot.component.task.HeartTestTask】【645】
2025-05-27 01:27:11.042 [pool-14-thread-5] INFO  com.simbest.boot.component.task.AbstractTaskSchedule.checkAndExecute Line:198 - 集群主控节点主机地址【10.92.82.161】运行端口【10031】,当前主机地址【10.87.57.73】运行端口【8092】, 无法执行定时任务【com.simbest.boot.processdata.pushWorkmanager.task.SyncTaskDataToWorkManagerTask】【641】
2025-05-27 01:27:11.042 [pool-14-thread-9] INFO  com.simbest.boot.component.task.AbstractTaskSchedule.checkAndExecute Line:198 - 集群主控节点主机地址【10.92.82.161】运行端口【10031】,当前主机地址【10.87.57.73】运行端口【8092】, 无法执行定时任务【com.simbest.boot.processdata.pushWorkmanager.task.SyncProcessDataToWorkManagerTask】【645】
2025-05-27 01:27:11.042 [pool-14-thread-7] INFO  com.simbest.boot.component.task.AbstractTaskSchedule.checkAndExecute Line:198 - 集群主控节点主机地址【10.92.82.161】运行端口【10031】,当前主机地址【10.87.57.73】运行端口【8092】, 无法执行定时任务【com.simbest.boot.processdata.pushWorkmanager.task.SyncCommentDataToWorkManagerTask】【643】
2025-05-27 01:27:11.042 [pool-14-thread-6] INFO  com.simbest.boot.component.task.AbstractTaskSchedule.checkAndExecute Line:198 - 集群主控节点主机地址【10.92.82.161】运行端口【10031】,当前主机地址【10.87.57.73】运行端口【8092】, 无法执行定时任务【com.simbest.boot.bps.task.AutoCheckWithUpdateTask】【642】
2025-05-27 01:27:11.042 [pool-14-thread-2] INFO  com.simbest.boot.component.task.AbstractTaskSchedule.checkAndExecute Line:198 - 集群主控节点主机地址【10.92.82.161】运行端口【10031】,当前主机地址【10.87.57.73】运行端口【8092】, 无法执行定时任务【com.simbest.boot.bps.task.AutoAddTodoTask】【638】
2025-05-27 01:27:11.042 [pool-14-thread-8] INFO  com.simbest.boot.component.task.AbstractTaskSchedule.checkAndExecute Line:198 - 集群主控节点主机地址【10.92.82.161】运行端口【10031】,当前主机地址【10.87.57.73】运行端口【8092】, 无法执行定时任务【com.simbest.boot.component.task.HeartTestTask】【644】
2025-05-27 01:27:11.042 [pool-14-thread-1] INFO  com.simbest.boot.component.task.AbstractTaskSchedule.checkAndExecute Line:198 - 集群主控节点主机地址【10.92.82.161】运行端口【10031】,当前主机地址【10.87.57.73】运行端口【8092】, 无法执行定时任务【com.simbest.boot.bps.task.AutoCloseTodoTask】【637】
2025-05-27 01:28:00.004 [pool-14-thread-8] INFO  com.simbest.boot.component.task.AbstractTaskSchedule.checkAndExecute Line:198 - 集群主控节点主机地址【10.92.82.161】运行端口【10031】,当前主机地址【10.87.57.73】运行端口【8092】, 无法执行定时任务【com.simbest.boot.component.task.HeartTestTask】【644】
2025-05-27 01:29:00.013 [pool-14-thread-7] INFO  com.simbest.boot.component.task.AbstractTaskSchedule.checkAndExecute Line:198 - 集群主控节点主机地址【10.92.82.161】运行端口【10031】,当前主机地址【10.87.57.73】运行端口【8092】, 无法执行定时任务【com.simbest.boot.component.task.HeartTestTask】【643】
2025-05-27 01:29:59.970 [pool-14-thread-3] INFO  com.simbest.boot.component.task.AbstractTaskSchedule.checkAndExecute Line:198 - 集群主控节点主机地址【10.92.82.161】运行端口【10031】,当前主机地址【10.87.57.73】运行端口【8092】, 无法执行定时任务【com.simbest.boot.component.task.LicenseCheckTask】【639】
2025-05-27 01:30:00.001 [pool-14-thread-1] INFO  com.simbest.boot.component.task.AbstractTaskSchedule.checkAndExecute Line:198 - 集群主控节点主机地址【10.92.82.161】运行端口【10031】,当前主机地址【10.87.57.73】运行端口【8092】, 无法执行定时任务【com.simbest.boot.processdata.pushWorkmanager.task.SyncTaskDataToWorkManagerTask】【637】
2025-05-27 01:30:00.001 [pool-14-thread-5] INFO  com.simbest.boot.component.task.AbstractTaskSchedule.checkAndExecute Line:198 - 集群主控节点主机地址【10.92.82.161】运行端口【10031】,当前主机地址【10.87.57.73】运行端口【8092】, 无法执行定时任务【com.simbest.boot.processdata.pushWorkmanager.task.SyncCommentDataToWorkManagerTask】【641】
2025-05-27 01:30:00.001 [pool-14-thread-6] INFO  com.simbest.boot.component.task.AbstractTaskSchedule.checkAndExecute Line:198 - 集群主控节点主机地址【10.92.82.161】运行端口【10031】,当前主机地址【10.87.57.73】运行端口【8092】, 无法执行定时任务【com.simbest.boot.bps.task.AutoAddTodoTask】【642】
2025-05-27 01:30:00.001 [pool-14-thread-10] INFO  com.simbest.boot.component.task.AbstractTaskSchedule.checkAndExecute Line:198 - 集群主控节点主机地址【10.92.82.161】运行端口【10031】,当前主机地址【10.87.57.73】运行端口【8092】, 无法执行定时任务【com.simbest.boot.component.task.HeartTestTask】【646】
2025-05-27 01:30:00.002 [pool-14-thread-3] INFO  com.simbest.boot.component.task.AbstractTaskSchedule.checkAndExecute Line:198 - 集群主控节点主机地址【10.92.82.161】运行端口【10031】,当前主机地址【10.87.57.73】运行端口【8092】, 无法执行定时任务【com.simbest.boot.processdata.pushWorkmanager.task.SyncProcessDataToWorkManagerTask】【639】
2025-05-27 01:30:00.001 [pool-14-thread-9] INFO  com.simbest.boot.component.task.AbstractTaskSchedule.checkAndExecute Line:198 - 集群主控节点主机地址【10.92.82.161】运行端口【10031】,当前主机地址【10.87.57.73】运行端口【8092】, 无法执行定时任务【com.simbest.boot.bps.task.AutoCloseTodoTask】【645】
2025-05-27 01:30:00.001 [pool-14-thread-2] INFO  com.simbest.boot.component.task.AbstractTaskSchedule.checkAndExecute Line:198 - 集群主控节点主机地址【10.92.82.161】运行端口【10031】,当前主机地址【10.87.57.73】运行端口【8092】, 无法执行定时任务【com.simbest.boot.bps.task.AutoCheckWithUpdateTask】【638】
2025-05-27 01:30:01.007 [pool-14-thread-5] INFO  com.simbest.boot.component.task.AbstractTaskSchedule.checkAndExecute Line:198 - 集群主控节点主机地址【10.92.82.161】运行端口【10031】,当前主机地址【10.87.57.73】运行端口【8092】, 无法执行定时任务【com.simbest.boot.component.task.LicenseCheckTask】【641】
2025-05-27 01:30:02.015 [pool-14-thread-5] INFO  com.simbest.boot.component.task.AbstractTaskSchedule.checkAndExecute Line:198 - 集群主控节点主机地址【10.92.82.161】运行端口【10031】,当前主机地址【10.87.57.73】运行端口【8092】, 无法执行定时任务【com.simbest.boot.component.task.LicenseCheckTask】【641】
2025-05-27 01:30:03.007 [pool-14-thread-5] INFO  com.simbest.boot.component.task.AbstractTaskSchedule.checkAndExecute Line:198 - 集群主控节点主机地址【10.92.82.161】运行端口【10031】,当前主机地址【10.87.57.73】运行端口【8092】, 无法执行定时任务【com.simbest.boot.component.task.LicenseCheckTask】【641】
2025-05-27 01:30:04.012 [pool-14-thread-2] INFO  com.simbest.boot.component.task.AbstractTaskSchedule.checkAndExecute Line:198 - 集群主控节点主机地址【10.92.82.161】运行端口【10031】,当前主机地址【10.87.57.73】运行端口【8092】, 无法执行定时任务【com.simbest.boot.component.task.LicenseCheckTask】【638】
2025-05-27 01:30:05.006 [pool-14-thread-2] INFO  com.simbest.boot.component.task.AbstractTaskSchedule.checkAndExecute Line:198 - 集群主控节点主机地址【10.92.82.161】运行端口【10031】,当前主机地址【10.87.57.73】运行端口【8092】, 无法执行定时任务【com.simbest.boot.component.task.LicenseCheckTask】【638】
2025-05-27 01:30:06.000 [pool-14-thread-1] INFO  com.simbest.boot.component.task.AbstractTaskSchedule.checkAndExecute Line:198 - 集群主控节点主机地址【10.92.82.161】运行端口【10031】,当前主机地址【10.87.57.73】运行端口【8092】, 无法执行定时任务【com.simbest.boot.component.task.LicenseCheckTask】【637】
2025-05-27 01:30:07.008 [pool-14-thread-1] INFO  com.simbest.boot.component.task.AbstractTaskSchedule.checkAndExecute Line:198 - 集群主控节点主机地址【10.92.82.161】运行端口【10031】,当前主机地址【10.87.57.73】运行端口【8092】, 无法执行定时任务【com.simbest.boot.component.task.LicenseCheckTask】【637】
2025-05-27 01:30:08.012 [pool-14-thread-1] INFO  com.simbest.boot.component.task.AbstractTaskSchedule.checkAndExecute Line:198 - 集群主控节点主机地址【10.92.82.161】运行端口【10031】,当前主机地址【10.87.57.73】运行端口【8092】, 无法执行定时任务【com.simbest.boot.component.task.LicenseCheckTask】【637】
2025-05-27 01:30:09.008 [pool-14-thread-7] INFO  com.simbest.boot.component.task.AbstractTaskSchedule.checkAndExecute Line:198 - 集群主控节点主机地址【10.92.82.161】运行端口【10031】,当前主机地址【10.87.57.73】运行端口【8092】, 无法执行定时任务【com.simbest.boot.component.task.LicenseCheckTask】【643】
2025-05-27 01:30:10.012 [pool-14-thread-2] INFO  com.simbest.boot.component.task.AbstractTaskSchedule.checkAndExecute Line:198 - 集群主控节点主机地址【10.92.82.161】运行端口【10031】,当前主机地址【10.87.57.73】运行端口【8092】, 无法执行定时任务【com.simbest.boot.component.task.LicenseCheckTask】【638】
2025-05-27 01:30:11.014 [pool-14-thread-2] INFO  com.simbest.boot.component.task.AbstractTaskSchedule.checkAndExecute Line:198 - 集群主控节点主机地址【10.92.82.161】运行端口【10031】,当前主机地址【10.87.57.73】运行端口【8092】, 无法执行定时任务【com.simbest.boot.component.task.LicenseCheckTask】【638】
2025-05-27 01:30:12.007 [pool-14-thread-2] INFO  com.simbest.boot.component.task.AbstractTaskSchedule.checkAndExecute Line:198 - 集群主控节点主机地址【10.92.82.161】运行端口【10031】,当前主机地址【10.87.57.73】运行端口【8092】, 无法执行定时任务【com.simbest.boot.component.task.LicenseCheckTask】【638】
2025-05-27 01:30:13.000 [pool-14-thread-1] INFO  com.simbest.boot.component.task.AbstractTaskSchedule.checkAndExecute Line:198 - 集群主控节点主机地址【10.92.82.161】运行端口【10031】,当前主机地址【10.87.57.73】运行端口【8092】, 无法执行定时任务【com.simbest.boot.component.task.LicenseCheckTask】【637】
2025-05-27 01:30:14.005 [pool-14-thread-1] INFO  com.simbest.boot.component.task.AbstractTaskSchedule.checkAndExecute Line:198 - 集群主控节点主机地址【10.92.82.161】运行端口【10031】,当前主机地址【10.87.57.73】运行端口【8092】, 无法执行定时任务【com.simbest.boot.component.task.LicenseCheckTask】【637】
2025-05-27 01:30:15.009 [pool-14-thread-1] INFO  com.simbest.boot.component.task.AbstractTaskSchedule.checkAndExecute Line:198 - 集群主控节点主机地址【10.92.82.161】运行端口【10031】,当前主机地址【10.87.57.73】运行端口【8092】, 无法执行定时任务【com.simbest.boot.component.task.LicenseCheckTask】【637】
2025-05-27 01:30:16.011 [pool-14-thread-1] INFO  com.simbest.boot.component.task.AbstractTaskSchedule.checkAndExecute Line:198 - 集群主控节点主机地址【10.92.82.161】运行端口【10031】,当前主机地址【10.87.57.73】运行端口【8092】, 无法执行定时任务【com.simbest.boot.component.task.LicenseCheckTask】【637】
2025-05-27 01:30:17.002 [pool-14-thread-1] INFO  com.simbest.boot.component.task.AbstractTaskSchedule.checkAndExecute Line:198 - 集群主控节点主机地址【10.92.82.161】运行端口【10031】,当前主机地址【10.87.57.73】运行端口【8092】, 无法执行定时任务【com.simbest.boot.component.task.LicenseCheckTask】【637】
2025-05-27 01:30:18.010 [pool-14-thread-1] INFO  com.simbest.boot.component.task.AbstractTaskSchedule.checkAndExecute Line:198 - 集群主控节点主机地址【10.92.82.161】运行端口【10031】,当前主机地址【10.87.57.73】运行端口【8092】, 无法执行定时任务【com.simbest.boot.component.task.LicenseCheckTask】【637】
2025-05-27 01:30:19.005 [pool-14-thread-10] INFO  com.simbest.boot.component.task.AbstractTaskSchedule.checkAndExecute Line:198 - 集群主控节点主机地址【10.92.82.161】运行端口【10031】,当前主机地址【10.87.57.73】运行端口【8092】, 无法执行定时任务【com.simbest.boot.component.task.LicenseCheckTask】【646】
2025-05-27 01:30:20.011 [pool-14-thread-10] INFO  com.simbest.boot.component.task.AbstractTaskSchedule.checkAndExecute Line:198 - 集群主控节点主机地址【10.92.82.161】运行端口【10031】,当前主机地址【10.87.57.73】运行端口【8092】, 无法执行定时任务【com.simbest.boot.component.task.LicenseCheckTask】【646】
2025-05-27 01:30:21.003 [pool-14-thread-8] INFO  com.simbest.boot.component.task.AbstractTaskSchedule.checkAndExecute Line:198 - 集群主控节点主机地址【10.92.82.161】运行端口【10031】,当前主机地址【10.87.57.73】运行端口【8092】, 无法执行定时任务【com.simbest.boot.component.task.LicenseCheckTask】【644】
2025-05-27 01:30:22.014 [pool-14-thread-8] INFO  com.simbest.boot.component.task.AbstractTaskSchedule.checkAndExecute Line:198 - 集群主控节点主机地址【10.92.82.161】运行端口【10031】,当前主机地址【10.87.57.73】运行端口【8092】, 无法执行定时任务【com.simbest.boot.component.task.LicenseCheckTask】【644】
2025-05-27 01:30:23.002 [pool-14-thread-8] INFO  com.simbest.boot.component.task.AbstractTaskSchedule.checkAndExecute Line:198 - 集群主控节点主机地址【10.92.82.161】运行端口【10031】,当前主机地址【10.87.57.73】运行端口【8092】, 无法执行定时任务【com.simbest.boot.component.task.LicenseCheckTask】【644】
2025-05-27 01:30:24.009 [pool-14-thread-3] INFO  com.simbest.boot.component.task.AbstractTaskSchedule.checkAndExecute Line:198 - 集群主控节点主机地址【10.92.82.161】运行端口【10031】,当前主机地址【10.87.57.73】运行端口【8092】, 无法执行定时任务【com.simbest.boot.component.task.LicenseCheckTask】【639】
2025-05-27 01:30:25.016 [pool-14-thread-5] INFO  com.simbest.boot.component.task.AbstractTaskSchedule.checkAndExecute Line:198 - 集群主控节点主机地址【10.92.82.161】运行端口【10031】,当前主机地址【10.87.57.73】运行端口【8092】, 无法执行定时任务【com.simbest.boot.component.task.LicenseCheckTask】【641】
2025-05-27 01:30:26.008 [pool-14-thread-5] INFO  com.simbest.boot.component.task.AbstractTaskSchedule.checkAndExecute Line:198 - 集群主控节点主机地址【10.92.82.161】运行端口【10031】,当前主机地址【10.87.57.73】运行端口【8092】, 无法执行定时任务【com.simbest.boot.component.task.LicenseCheckTask】【641】
2025-05-27 01:30:27.016 [pool-14-thread-5] INFO  com.simbest.boot.component.task.AbstractTaskSchedule.checkAndExecute Line:198 - 集群主控节点主机地址【10.92.82.161】运行端口【10031】,当前主机地址【10.87.57.73】运行端口【8092】, 无法执行定时任务【com.simbest.boot.component.task.LicenseCheckTask】【641】
2025-05-27 01:30:28.006 [pool-14-thread-5] INFO  com.simbest.boot.component.task.AbstractTaskSchedule.checkAndExecute Line:198 - 集群主控节点主机地址【10.92.82.161】运行端口【10031】,当前主机地址【10.87.57.73】运行端口【8092】, 无法执行定时任务【com.simbest.boot.component.task.LicenseCheckTask】【641】
2025-05-27 01:30:29.008 [pool-14-thread-9] INFO  com.simbest.boot.component.task.AbstractTaskSchedule.checkAndExecute Line:198 - 集群主控节点主机地址【10.92.82.161】运行端口【10031】,当前主机地址【10.87.57.73】运行端口【8092】, 无法执行定时任务【com.simbest.boot.component.task.LicenseCheckTask】【645】
2025-05-27 01:30:30.015 [pool-14-thread-9] INFO  com.simbest.boot.component.task.AbstractTaskSchedule.checkAndExecute Line:198 - 集群主控节点主机地址【10.92.82.161】运行端口【10031】,当前主机地址【10.87.57.73】运行端口【8092】, 无法执行定时任务【com.simbest.boot.component.task.LicenseCheckTask】【645】
2025-05-27 01:30:31.001 [pool-14-thread-9] INFO  com.simbest.boot.component.task.AbstractTaskSchedule.checkAndExecute Line:198 - 集群主控节点主机地址【10.92.82.161】运行端口【10031】,当前主机地址【10.87.57.73】运行端口【8092】, 无法执行定时任务【com.simbest.boot.component.task.LicenseCheckTask】【645】
2025-05-27 01:30:32.007 [pool-14-thread-9] INFO  com.simbest.boot.component.task.AbstractTaskSchedule.checkAndExecute Line:198 - 集群主控节点主机地址【10.92.82.161】运行端口【10031】,当前主机地址【10.87.57.73】运行端口【8092】, 无法执行定时任务【com.simbest.boot.component.task.LicenseCheckTask】【645】
2025-05-27 01:30:33.015 [pool-14-thread-8] INFO  com.simbest.boot.component.task.AbstractTaskSchedule.checkAndExecute Line:198 - 集群主控节点主机地址【10.92.82.161】运行端口【10031】,当前主机地址【10.87.57.73】运行端口【8092】, 无法执行定时任务【com.simbest.boot.component.task.LicenseCheckTask】【644】
2025-05-27 01:30:34.007 [pool-14-thread-3] INFO  com.simbest.boot.component.task.AbstractTaskSchedule.checkAndExecute Line:198 - 集群主控节点主机地址【10.92.82.161】运行端口【10031】,当前主机地址【10.87.57.73】运行端口【8092】, 无法执行定时任务【com.simbest.boot.component.task.LicenseCheckTask】【639】
2025-05-27 01:30:35.013 [pool-14-thread-3] INFO  com.simbest.boot.component.task.AbstractTaskSchedule.checkAndExecute Line:198 - 集群主控节点主机地址【10.92.82.161】运行端口【10031】,当前主机地址【10.87.57.73】运行端口【8092】, 无法执行定时任务【com.simbest.boot.component.task.LicenseCheckTask】【639】
2025-05-27 01:30:36.014 [pool-14-thread-6] INFO  com.simbest.boot.component.task.AbstractTaskSchedule.checkAndExecute Line:198 - 集群主控节点主机地址【10.92.82.161】运行端口【10031】,当前主机地址【10.87.57.73】运行端口【8092】, 无法执行定时任务【com.simbest.boot.component.task.LicenseCheckTask】【642】
2025-05-27 01:30:37.004 [pool-14-thread-6] INFO  com.simbest.boot.component.task.AbstractTaskSchedule.checkAndExecute Line:198 - 集群主控节点主机地址【10.92.82.161】运行端口【10031】,当前主机地址【10.87.57.73】运行端口【8092】, 无法执行定时任务【com.simbest.boot.component.task.LicenseCheckTask】【642】
2025-05-27 01:30:38.011 [pool-14-thread-5] INFO  com.simbest.boot.component.task.AbstractTaskSchedule.checkAndExecute Line:198 - 集群主控节点主机地址【10.92.82.161】运行端口【10031】,当前主机地址【10.87.57.73】运行端口【8092】, 无法执行定时任务【com.simbest.boot.component.task.LicenseCheckTask】【641】
2025-05-27 01:30:39.005 [pool-14-thread-5] INFO  com.simbest.boot.component.task.AbstractTaskSchedule.checkAndExecute Line:198 - 集群主控节点主机地址【10.92.82.161】运行端口【10031】,当前主机地址【10.87.57.73】运行端口【8092】, 无法执行定时任务【com.simbest.boot.component.task.LicenseCheckTask】【641】
2025-05-27 01:30:40.003 [pool-14-thread-5] INFO  com.simbest.boot.component.task.AbstractTaskSchedule.checkAndExecute Line:198 - 集群主控节点主机地址【10.92.82.161】运行端口【10031】,当前主机地址【10.87.57.73】运行端口【8092】, 无法执行定时任务【com.simbest.boot.component.task.LicenseCheckTask】【641】
2025-05-27 01:30:41.007 [pool-14-thread-5] INFO  com.simbest.boot.component.task.AbstractTaskSchedule.checkAndExecute Line:198 - 集群主控节点主机地址【10.92.82.161】运行端口【10031】,当前主机地址【10.87.57.73】运行端口【8092】, 无法执行定时任务【com.simbest.boot.component.task.LicenseCheckTask】【641】
2025-05-27 01:30:42.003 [pool-14-thread-5] INFO  com.simbest.boot.component.task.AbstractTaskSchedule.checkAndExecute Line:198 - 集群主控节点主机地址【10.92.82.161】运行端口【10031】,当前主机地址【10.87.57.73】运行端口【8092】, 无法执行定时任务【com.simbest.boot.component.task.LicenseCheckTask】【641】
2025-05-27 01:30:43.006 [pool-14-thread-5] INFO  com.simbest.boot.component.task.AbstractTaskSchedule.checkAndExecute Line:198 - 集群主控节点主机地址【10.92.82.161】运行端口【10031】,当前主机地址【10.87.57.73】运行端口【8092】, 无法执行定时任务【com.simbest.boot.component.task.LicenseCheckTask】【641】
2025-05-27 01:30:44.011 [pool-14-thread-5] INFO  com.simbest.boot.component.task.AbstractTaskSchedule.checkAndExecute Line:198 - 集群主控节点主机地址【10.92.82.161】运行端口【10031】,当前主机地址【10.87.57.73】运行端口【8092】, 无法执行定时任务【com.simbest.boot.component.task.LicenseCheckTask】【641】
2025-05-27 01:30:45.002 [pool-14-thread-3] INFO  com.simbest.boot.component.task.AbstractTaskSchedule.checkAndExecute Line:198 - 集群主控节点主机地址【10.92.82.161】运行端口【10031】,当前主机地址【10.87.57.73】运行端口【8092】, 无法执行定时任务【com.simbest.boot.component.task.LicenseCheckTask】【639】
2025-05-27 01:30:46.007 [pool-14-thread-3] INFO  com.simbest.boot.component.task.AbstractTaskSchedule.checkAndExecute Line:198 - 集群主控节点主机地址【10.92.82.161】运行端口【10031】,当前主机地址【10.87.57.73】运行端口【8092】, 无法执行定时任务【com.simbest.boot.component.task.LicenseCheckTask】【639】
2025-05-27 01:30:47.003 [pool-14-thread-6] INFO  com.simbest.boot.component.task.AbstractTaskSchedule.checkAndExecute Line:198 - 集群主控节点主机地址【10.92.82.161】运行端口【10031】,当前主机地址【10.87.57.73】运行端口【8092】, 无法执行定时任务【com.simbest.boot.component.task.LicenseCheckTask】【642】
2025-05-27 01:30:48.012 [pool-14-thread-6] INFO  com.simbest.boot.component.task.AbstractTaskSchedule.checkAndExecute Line:198 - 集群主控节点主机地址【10.92.82.161】运行端口【10031】,当前主机地址【10.87.57.73】运行端口【8092】, 无法执行定时任务【com.simbest.boot.component.task.LicenseCheckTask】【642】
2025-05-27 01:30:49.003 [pool-14-thread-6] INFO  com.simbest.boot.component.task.AbstractTaskSchedule.checkAndExecute Line:198 - 集群主控节点主机地址【10.92.82.161】运行端口【10031】,当前主机地址【10.87.57.73】运行端口【8092】, 无法执行定时任务【com.simbest.boot.component.task.LicenseCheckTask】【642】
2025-05-27 01:30:50.013 [pool-14-thread-7] INFO  com.simbest.boot.component.task.AbstractTaskSchedule.checkAndExecute Line:198 - 集群主控节点主机地址【10.92.82.161】运行端口【10031】,当前主机地址【10.87.57.73】运行端口【8092】, 无法执行定时任务【com.simbest.boot.component.task.LicenseCheckTask】【643】
2025-05-27 01:30:51.008 [pool-14-thread-7] INFO  com.simbest.boot.component.task.AbstractTaskSchedule.checkAndExecute Line:198 - 集群主控节点主机地址【10.92.82.161】运行端口【10031】,当前主机地址【10.87.57.73】运行端口【8092】, 无法执行定时任务【com.simbest.boot.component.task.LicenseCheckTask】【643】
2025-05-27 01:30:52.012 [pool-14-thread-8] INFO  com.simbest.boot.component.task.AbstractTaskSchedule.checkAndExecute Line:198 - 集群主控节点主机地址【10.92.82.161】运行端口【10031】,当前主机地址【10.87.57.73】运行端口【8092】, 无法执行定时任务【com.simbest.boot.component.task.LicenseCheckTask】【644】
2025-05-27 01:30:53.000 [pool-14-thread-8] INFO  com.simbest.boot.component.task.AbstractTaskSchedule.checkAndExecute Line:198 - 集群主控节点主机地址【10.92.82.161】运行端口【10031】,当前主机地址【10.87.57.73】运行端口【8092】, 无法执行定时任务【com.simbest.boot.component.task.LicenseCheckTask】【644】
2025-05-27 01:30:54.005 [pool-14-thread-8] INFO  com.simbest.boot.component.task.AbstractTaskSchedule.checkAndExecute Line:198 - 集群主控节点主机地址【10.92.82.161】运行端口【10031】,当前主机地址【10.87.57.73】运行端口【8092】, 无法执行定时任务【com.simbest.boot.component.task.LicenseCheckTask】【644】
2025-05-27 01:30:55.013 [pool-14-thread-8] INFO  com.simbest.boot.component.task.AbstractTaskSchedule.checkAndExecute Line:198 - 集群主控节点主机地址【10.92.82.161】运行端口【10031】,当前主机地址【10.87.57.73】运行端口【8092】, 无法执行定时任务【com.simbest.boot.component.task.LicenseCheckTask】【644】
2025-05-27 01:30:56.003 [pool-14-thread-8] INFO  com.simbest.boot.component.task.AbstractTaskSchedule.checkAndExecute Line:198 - 集群主控节点主机地址【10.92.82.161】运行端口【10031】,当前主机地址【10.87.57.73】运行端口【8092】, 无法执行定时任务【com.simbest.boot.component.task.LicenseCheckTask】【644】
2025-05-27 01:30:57.007 [pool-14-thread-8] INFO  com.simbest.boot.component.task.AbstractTaskSchedule.checkAndExecute Line:198 - 集群主控节点主机地址【10.92.82.161】运行端口【10031】,当前主机地址【10.87.57.73】运行端口【8092】, 无法执行定时任务【com.simbest.boot.component.task.LicenseCheckTask】【644】
2025-05-27 01:30:58.002 [pool-14-thread-8] INFO  com.simbest.boot.component.task.AbstractTaskSchedule.checkAndExecute Line:198 - 集群主控节点主机地址【10.92.82.161】运行端口【10031】,当前主机地址【10.87.57.73】运行端口【8092】, 无法执行定时任务【com.simbest.boot.component.task.LicenseCheckTask】【644】
2025-05-27 01:30:59.006 [pool-14-thread-8] INFO  com.simbest.boot.component.task.AbstractTaskSchedule.checkAndExecute Line:198 - 集群主控节点主机地址【10.92.82.161】运行端口【10031】,当前主机地址【10.87.57.73】运行端口【8092】, 无法执行定时任务【com.simbest.boot.component.task.LicenseCheckTask】【644】
2025-05-27 01:31:00.009 [pool-14-thread-1] INFO  com.simbest.boot.component.task.AbstractTaskSchedule.checkAndExecute Line:198 - 集群主控节点主机地址【10.92.82.161】运行端口【10031】,当前主机地址【10.87.57.73】运行端口【8092】, 无法执行定时任务【com.simbest.boot.component.task.HeartTestTask】【637】
2025-05-27 01:32:00.010 [pool-14-thread-10] INFO  com.simbest.boot.component.task.AbstractTaskSchedule.checkAndExecute Line:198 - 集群主控节点主机地址【10.92.82.161】运行端口【10031】,当前主机地址【10.87.57.73】运行端口【8092】, 无法执行定时任务【com.simbest.boot.component.task.HeartTestTask】【646】
2025-05-27 01:33:00.015 [pool-14-thread-9] INFO  com.simbest.boot.component.task.AbstractTaskSchedule.checkAndExecute Line:198 - 集群主控节点主机地址【10.92.82.161】运行端口【10031】,当前主机地址【10.87.57.73】运行端口【8092】, 无法执行定时任务【com.simbest.boot.component.task.HeartTestTask】【645】
2025-05-27 01:34:00.005 [pool-14-thread-9] INFO  com.simbest.boot.component.task.AbstractTaskSchedule.checkAndExecute Line:198 - 集群主控节点主机地址【10.92.82.161】运行端口【10031】,当前主机地址【10.87.57.73】运行端口【8092】, 无法执行定时任务【com.simbest.boot.component.task.HeartTestTask】【645】
2025-05-27 01:35:00.000 [pool-14-thread-10] INFO  com.simbest.boot.component.task.AbstractTaskSchedule.checkAndExecute Line:198 - 集群主控节点主机地址【10.92.82.161】运行端口【10031】,当前主机地址【10.87.57.73】运行端口【8092】, 无法执行定时任务【com.simbest.boot.processdata.pushWorkmanager.task.SyncCommentDataToWorkManagerTask】【646】
2025-05-27 01:35:00.000 [pool-14-thread-6] INFO  com.simbest.boot.component.task.AbstractTaskSchedule.checkAndExecute Line:198 - 集群主控节点主机地址【10.92.82.161】运行端口【10031】,当前主机地址【10.87.57.73】运行端口【8092】, 无法执行定时任务【com.simbest.boot.bps.task.AutoCloseTodoTask】【642】
2025-05-27 01:35:00.000 [pool-14-thread-1] INFO  com.simbest.boot.component.task.AbstractTaskSchedule.checkAndExecute Line:198 - 集群主控节点主机地址【10.92.82.161】运行端口【10031】,当前主机地址【10.87.57.73】运行端口【8092】, 无法执行定时任务【com.simbest.boot.bps.task.AutoAddTodoTask】【637】
2025-05-27 01:35:00.000 [pool-14-thread-2] INFO  com.simbest.boot.component.task.AbstractTaskSchedule.checkAndExecute Line:198 - 集群主控节点主机地址【10.92.82.161】运行端口【10031】,当前主机地址【10.87.57.73】运行端口【8092】, 无法执行定时任务【com.simbest.boot.bps.task.AutoCheckWithUpdateTask】【638】
2025-05-27 01:35:00.000 [pool-14-thread-8] INFO  com.simbest.boot.component.task.AbstractTaskSchedule.checkAndExecute Line:198 - 集群主控节点主机地址【10.92.82.161】运行端口【10031】,当前主机地址【10.87.57.73】运行端口【8092】, 无法执行定时任务【com.simbest.boot.processdata.pushWorkmanager.task.SyncTaskDataToWorkManagerTask】【644】
2025-05-27 01:35:00.001 [pool-14-thread-10] INFO  com.simbest.boot.component.task.AbstractTaskSchedule.checkAndExecute Line:198 - 集群主控节点主机地址【10.92.82.161】运行端口【10031】,当前主机地址【10.87.57.73】运行端口【8092】, 无法执行定时任务【com.simbest.boot.component.task.HeartTestTask】【646】
2025-05-27 01:35:00.000 [pool-14-thread-3] INFO  com.simbest.boot.component.task.AbstractTaskSchedule.checkAndExecute Line:198 - 集群主控节点主机地址【10.92.82.161】运行端口【10031】,当前主机地址【10.87.57.73】运行端口【8092】, 无法执行定时任务【com.simbest.boot.processdata.pushWorkmanager.task.SyncProcessDataToWorkManagerTask】【639】
2025-05-27 01:36:00.011 [pool-14-thread-2] INFO  com.simbest.boot.component.task.AbstractTaskSchedule.checkAndExecute Line:198 - 集群主控节点主机地址【10.92.82.161】运行端口【10031】,当前主机地址【10.87.57.73】运行端口【8092】, 无法执行定时任务【com.simbest.boot.component.task.HeartTestTask】【638】
2025-05-27 01:37:00.011 [pool-14-thread-9] INFO  com.simbest.boot.component.task.AbstractTaskSchedule.checkAndExecute Line:198 - 集群主控节点主机地址【10.92.82.161】运行端口【10031】,当前主机地址【10.87.57.73】运行端口【8092】, 无法执行定时任务【com.simbest.boot.component.task.HeartTestTask】【645】
2025-05-27 01:38:00.005 [pool-14-thread-9] INFO  com.simbest.boot.component.task.AbstractTaskSchedule.checkAndExecute Line:198 - 集群主控节点主机地址【10.92.82.161】运行端口【10031】,当前主机地址【10.87.57.73】运行端口【8092】, 无法执行定时任务【com.simbest.boot.component.task.HeartTestTask】【645】
2025-05-27 01:39:00.005 [pool-14-thread-10] INFO  com.simbest.boot.component.task.AbstractTaskSchedule.checkAndExecute Line:198 - 集群主控节点主机地址【10.92.82.161】运行端口【10031】,当前主机地址【10.87.57.73】运行端口【8092】, 无法执行定时任务【com.simbest.boot.component.task.HeartTestTask】【646】
2025-05-27 01:40:00.008 [pool-14-thread-5] INFO  com.simbest.boot.component.task.AbstractTaskSchedule.checkAndExecute Line:198 - 集群主控节点主机地址【10.92.82.161】运行端口【10031】,当前主机地址【10.87.57.73】运行端口【8092】, 无法执行定时任务【com.simbest.boot.bps.task.AutoCheckWithUpdateTask】【641】
2025-05-27 01:40:00.008 [pool-14-thread-8] INFO  com.simbest.boot.component.task.AbstractTaskSchedule.checkAndExecute Line:198 - 集群主控节点主机地址【10.92.82.161】运行端口【10031】,当前主机地址【10.87.57.73】运行端口【8092】, 无法执行定时任务【com.simbest.boot.component.task.HeartTestTask】【644】
2025-05-27 01:40:00.008 [pool-14-thread-4] INFO  com.simbest.boot.component.task.AbstractTaskSchedule.checkAndExecute Line:198 - 集群主控节点主机地址【10.92.82.161】运行端口【10031】,当前主机地址【10.87.57.73】运行端口【8092】, 无法执行定时任务【com.simbest.boot.processdata.pushWorkmanager.task.SyncCommentDataToWorkManagerTask】【640】
2025-05-27 01:40:00.008 [pool-14-thread-6] INFO  com.simbest.boot.component.task.AbstractTaskSchedule.checkAndExecute Line:198 - 集群主控节点主机地址【10.92.82.161】运行端口【10031】,当前主机地址【10.87.57.73】运行端口【8092】, 无法执行定时任务【com.simbest.boot.processdata.pushWorkmanager.task.SyncTaskDataToWorkManagerTask】【642】
2025-05-27 01:40:00.008 [pool-14-thread-1] INFO  com.simbest.boot.component.task.AbstractTaskSchedule.checkAndExecute Line:198 - 集群主控节点主机地址【10.92.82.161】运行端口【10031】,当前主机地址【10.87.57.73】运行端口【8092】, 无法执行定时任务【com.simbest.boot.bps.task.AutoAddTodoTask】【637】
2025-05-27 01:40:00.008 [pool-14-thread-3] INFO  com.simbest.boot.component.task.AbstractTaskSchedule.checkAndExecute Line:198 - 集群主控节点主机地址【10.92.82.161】运行端口【10031】,当前主机地址【10.87.57.73】运行端口【8092】, 无法执行定时任务【com.simbest.boot.bps.task.AutoCloseTodoTask】【639】
2025-05-27 01:40:00.008 [pool-14-thread-7] INFO  com.simbest.boot.component.task.AbstractTaskSchedule.checkAndExecute Line:198 - 集群主控节点主机地址【10.92.82.161】运行端口【10031】,当前主机地址【10.87.57.73】运行端口【8092】, 无法执行定时任务【com.simbest.boot.processdata.pushWorkmanager.task.SyncProcessDataToWorkManagerTask】【643】
2025-05-27 01:41:00.013 [pool-14-thread-6] INFO  com.simbest.boot.component.task.AbstractTaskSchedule.checkAndExecute Line:198 - 集群主控节点主机地址【10.92.82.161】运行端口【10031】,当前主机地址【10.87.57.73】运行端口【8092】, 无法执行定时任务【com.simbest.boot.component.task.HeartTestTask】【642】
2025-05-27 01:42:00.013 [pool-14-thread-8] INFO  com.simbest.boot.component.task.AbstractTaskSchedule.checkAndExecute Line:198 - 集群主控节点主机地址【10.92.82.161】运行端口【10031】,当前主机地址【10.87.57.73】运行端口【8092】, 无法执行定时任务【com.simbest.boot.component.task.HeartTestTask】【644】
2025-05-27 01:43:00.014 [pool-14-thread-8] INFO  com.simbest.boot.component.task.AbstractTaskSchedule.checkAndExecute Line:198 - 集群主控节点主机地址【10.92.82.161】运行端口【10031】,当前主机地址【10.87.57.73】运行端口【8092】, 无法执行定时任务【com.simbest.boot.component.task.HeartTestTask】【644】
2025-05-27 01:44:00.015 [pool-14-thread-2] INFO  com.simbest.boot.component.task.AbstractTaskSchedule.checkAndExecute Line:198 - 集群主控节点主机地址【10.92.82.161】运行端口【10031】,当前主机地址【10.87.57.73】运行端口【8092】, 无法执行定时任务【com.simbest.boot.component.task.HeartTestTask】【638】
2025-05-27 01:45:00.000 [pool-14-thread-9] INFO  com.simbest.boot.component.task.AbstractTaskSchedule.checkAndExecute Line:198 - 集群主控节点主机地址【10.92.82.161】运行端口【10031】,当前主机地址【10.87.57.73】运行端口【8092】, 无法执行定时任务【com.simbest.boot.bps.task.AutoCloseTodoTask】【645】
2025-05-27 01:45:00.000 [pool-14-thread-1] INFO  com.simbest.boot.component.task.AbstractTaskSchedule.checkAndExecute Line:198 - 集群主控节点主机地址【10.92.82.161】运行端口【10031】,当前主机地址【10.87.57.73】运行端口【8092】, 无法执行定时任务【com.simbest.boot.processdata.pushWorkmanager.task.SyncProcessDataToWorkManagerTask】【637】
2025-05-27 01:45:00.004 [pool-14-thread-3] INFO  com.simbest.boot.component.task.AbstractTaskSchedule.checkAndExecute Line:198 - 集群主控节点主机地址【10.92.82.161】运行端口【10031】,当前主机地址【10.87.57.73】运行端口【8092】, 无法执行定时任务【com.simbest.boot.component.task.HeartTestTask】【639】
2025-05-27 01:45:00.006 [pool-14-thread-3] INFO  com.simbest.boot.component.task.AbstractTaskSchedule.checkAndExecute Line:198 - 集群主控节点主机地址【10.92.82.161】运行端口【10031】,当前主机地址【10.87.57.73】运行端口【8092】, 无法执行定时任务【com.simbest.boot.bps.task.AutoCheckWithUpdateTask】【639】
2025-05-27 01:45:00.006 [pool-14-thread-3] INFO  com.simbest.boot.component.task.AbstractTaskSchedule.checkAndExecute Line:198 - 集群主控节点主机地址【10.92.82.161】运行端口【10031】,当前主机地址【10.87.57.73】运行端口【8092】, 无法执行定时任务【com.simbest.boot.processdata.pushWorkmanager.task.SyncCommentDataToWorkManagerTask】【639】
2025-05-27 01:45:00.006 [pool-14-thread-3] INFO  com.simbest.boot.component.task.AbstractTaskSchedule.checkAndExecute Line:198 - 集群主控节点主机地址【10.92.82.161】运行端口【10031】,当前主机地址【10.87.57.73】运行端口【8092】, 无法执行定时任务【com.simbest.boot.processdata.pushWorkmanager.task.SyncTaskDataToWorkManagerTask】【639】
2025-05-27 01:45:00.006 [pool-14-thread-3] INFO  com.simbest.boot.component.task.AbstractTaskSchedule.checkAndExecute Line:198 - 集群主控节点主机地址【10.92.82.161】运行端口【10031】,当前主机地址【10.87.57.73】运行端口【8092】, 无法执行定时任务【com.simbest.boot.bps.task.AutoAddTodoTask】【639】
2025-05-27 01:46:00.002 [pool-14-thread-5] INFO  com.simbest.boot.component.task.AbstractTaskSchedule.checkAndExecute Line:198 - 集群主控节点主机地址【10.92.82.161】运行端口【10031】,当前主机地址【10.87.57.73】运行端口【8092】, 无法执行定时任务【com.simbest.boot.component.task.HeartTestTask】【641】
2025-05-27 01:47:00.014 [pool-14-thread-10] INFO  com.simbest.boot.component.task.AbstractTaskSchedule.checkAndExecute Line:198 - 集群主控节点主机地址【10.92.82.161】运行端口【10031】,当前主机地址【10.87.57.73】运行端口【8092】, 无法执行定时任务【com.simbest.boot.component.task.HeartTestTask】【646】
2025-05-27 02:08:11.123 [pool-14-thread-8] INFO  com.simbest.boot.component.task.AbstractTaskSchedule.checkAndExecute Line:198 - 集群主控节点主机地址【10.92.82.161】运行端口【10031】,当前主机地址【10.87.57.73】运行端口【8092】, 无法执行定时任务【com.simbest.boot.bps.task.AutoCheckWithUpdateTask】【644】
2025-05-27 02:08:11.123 [pool-14-thread-7] INFO  com.simbest.boot.component.task.AbstractTaskSchedule.checkAndExecute Line:198 - 集群主控节点主机地址【10.92.82.161】运行端口【10031】,当前主机地址【10.87.57.73】运行端口【8092】, 无法执行定时任务【com.simbest.boot.component.task.HeartTestTask】【643】
2025-05-27 02:08:11.123 [pool-14-thread-2] INFO  com.simbest.boot.component.task.AbstractTaskSchedule.checkAndExecute Line:198 - 集群主控节点主机地址【10.92.82.161】运行端口【10031】,当前主机地址【10.87.57.73】运行端口【8092】, 无法执行定时任务【com.simbest.boot.bps.task.AutoAddTodoTask】【638】
2025-05-27 02:08:11.123 [pool-14-thread-6] INFO  com.simbest.boot.component.task.AbstractTaskSchedule.checkAndExecute Line:198 - 集群主控节点主机地址【10.92.82.161】运行端口【10031】,当前主机地址【10.87.57.73】运行端口【8092】, 无法执行定时任务【com.simbest.boot.processdata.pushWorkmanager.task.SyncTaskDataToWorkManagerTask】【642】
2025-05-27 02:08:11.123 [pool-14-thread-5] INFO  com.simbest.boot.component.task.AbstractTaskSchedule.checkAndExecute Line:198 - 集群主控节点主机地址【10.92.82.161】运行端口【10031】,当前主机地址【10.87.57.73】运行端口【8092】, 无法执行定时任务【com.simbest.boot.processdata.pushWorkmanager.task.SyncCommentDataToWorkManagerTask】【641】
2025-05-27 02:08:11.124 [pool-14-thread-7] INFO  com.simbest.boot.component.task.AbstractTaskSchedule.checkAndExecute Line:198 - 集群主控节点主机地址【10.92.82.161】运行端口【10031】,当前主机地址【10.87.57.73】运行端口【8092】, 无法执行定时任务【com.simbest.boot.processdata.pushWorkmanager.task.SyncProcessDataToWorkManagerTask】【643】
2025-05-27 02:08:11.124 [pool-14-thread-9] INFO  com.simbest.boot.component.task.AbstractTaskSchedule.checkAndExecute Line:198 - 集群主控节点主机地址【10.92.82.161】运行端口【10031】,当前主机地址【10.87.57.73】运行端口【8092】, 无法执行定时任务【com.simbest.boot.component.task.LicenseCheckTask】【645】
2025-05-27 02:08:11.124 [pool-14-thread-2] INFO  com.simbest.boot.component.task.AbstractTaskSchedule.checkAndExecute Line:198 - 集群主控节点主机地址【10.92.82.161】运行端口【10031】,当前主机地址【10.87.57.73】运行端口【8092】, 无法执行定时任务【com.simbest.boot.cmcc.a4.task.A4TaskCreateXmlFile】【638】
2025-05-27 02:08:11.124 [pool-14-thread-8] INFO  com.simbest.boot.component.task.AbstractTaskSchedule.checkAndExecute Line:198 - 集群主控节点主机地址【10.92.82.161】运行端口【10031】,当前主机地址【10.87.57.73】运行端口【8092】, 无法执行定时任务【com.simbest.boot.bps.task.AutoCloseTodoTask】【644】
2025-05-27 02:09:00.002 [pool-14-thread-6] INFO  com.simbest.boot.component.task.AbstractTaskSchedule.checkAndExecute Line:198 - 集群主控节点主机地址【10.92.82.161】运行端口【10031】,当前主机地址【10.87.57.73】运行端口【8092】, 无法执行定时任务【com.simbest.boot.component.task.HeartTestTask】【642】
2025-05-27 02:10:00.000 [pool-14-thread-10] INFO  com.simbest.boot.component.task.AbstractTaskSchedule.checkAndExecute Line:198 - 集群主控节点主机地址【10.92.82.161】运行端口【10031】,当前主机地址【10.87.57.73】运行端口【8092】, 无法执行定时任务【com.simbest.boot.bps.task.AutoAddTodoTask】【646】
2025-05-27 02:10:00.000 [pool-14-thread-1] INFO  com.simbest.boot.component.task.AbstractTaskSchedule.checkAndExecute Line:198 - 集群主控节点主机地址【10.92.82.161】运行端口【10031】,当前主机地址【10.87.57.73】运行端口【8092】, 无法执行定时任务【com.simbest.boot.processdata.pushWorkmanager.task.SyncTaskDataToWorkManagerTask】【637】
2025-05-27 02:10:00.001 [pool-14-thread-9] INFO  com.simbest.boot.component.task.AbstractTaskSchedule.checkAndExecute Line:198 - 集群主控节点主机地址【10.92.82.161】运行端口【10031】,当前主机地址【10.87.57.73】运行端口【8092】, 无法执行定时任务【com.simbest.boot.bps.task.AutoCloseTodoTask】【645】
2025-05-27 02:10:00.000 [pool-14-thread-4] INFO  com.simbest.boot.component.task.AbstractTaskSchedule.checkAndExecute Line:198 - 集群主控节点主机地址【10.92.82.161】运行端口【10031】,当前主机地址【10.87.57.73】运行端口【8092】, 无法执行定时任务【com.simbest.boot.processdata.pushWorkmanager.task.SyncCommentDataToWorkManagerTask】【640】
2025-05-27 02:10:00.000 [pool-14-thread-6] INFO  com.simbest.boot.component.task.AbstractTaskSchedule.checkAndExecute Line:198 - 集群主控节点主机地址【10.92.82.161】运行端口【10031】,当前主机地址【10.87.57.73】运行端口【8092】, 无法执行定时任务【com.simbest.boot.component.task.HeartTestTask】【642】
2025-05-27 02:10:00.001 [pool-14-thread-5] INFO  com.simbest.boot.component.task.AbstractTaskSchedule.checkAndExecute Line:198 - 集群主控节点主机地址【10.92.82.161】运行端口【10031】,当前主机地址【10.87.57.73】运行端口【8092】, 无法执行定时任务【com.simbest.boot.bps.task.AutoCheckWithUpdateTask】【641】
2025-05-27 02:10:00.000 [pool-14-thread-2] INFO  com.simbest.boot.component.task.AbstractTaskSchedule.checkAndExecute Line:198 - 集群主控节点主机地址【10.92.82.161】运行端口【10031】,当前主机地址【10.87.57.73】运行端口【8092】, 无法执行定时任务【com.simbest.boot.processdata.pushWorkmanager.task.SyncProcessDataToWorkManagerTask】【638】
2025-05-27 02:11:00.001 [pool-14-thread-9] INFO  com.simbest.boot.component.task.AbstractTaskSchedule.checkAndExecute Line:198 - 集群主控节点主机地址【10.92.82.161】运行端口【10031】,当前主机地址【10.87.57.73】运行端口【8092】, 无法执行定时任务【com.simbest.boot.component.task.HeartTestTask】【645】
2025-05-27 02:12:00.010 [pool-14-thread-9] INFO  com.simbest.boot.component.task.AbstractTaskSchedule.checkAndExecute Line:198 - 集群主控节点主机地址【10.92.82.161】运行端口【10031】,当前主机地址【10.87.57.73】运行端口【8092】, 无法执行定时任务【com.simbest.boot.component.task.HeartTestTask】【645】
2025-05-27 02:13:00.010 [pool-14-thread-9] INFO  com.simbest.boot.component.task.AbstractTaskSchedule.checkAndExecute Line:198 - 集群主控节点主机地址【10.92.82.161】运行端口【10031】,当前主机地址【10.87.57.73】运行端口【8092】, 无法执行定时任务【com.simbest.boot.component.task.HeartTestTask】【645】
2025-05-27 02:14:00.014 [pool-14-thread-3] INFO  com.simbest.boot.component.task.AbstractTaskSchedule.checkAndExecute Line:198 - 集群主控节点主机地址【10.92.82.161】运行端口【10031】,当前主机地址【10.87.57.73】运行端口【8092】, 无法执行定时任务【com.simbest.boot.component.task.HeartTestTask】【639】
2025-05-27 02:15:00.011 [pool-14-thread-5] INFO  com.simbest.boot.component.task.AbstractTaskSchedule.checkAndExecute Line:198 - 集群主控节点主机地址【10.92.82.161】运行端口【10031】,当前主机地址【10.87.57.73】运行端口【8092】, 无法执行定时任务【com.simbest.boot.processdata.pushWorkmanager.task.SyncCommentDataToWorkManagerTask】【641】
2025-05-27 02:15:00.011 [pool-14-thread-4] INFO  com.simbest.boot.component.task.AbstractTaskSchedule.checkAndExecute Line:198 - 集群主控节点主机地址【10.92.82.161】运行端口【10031】,当前主机地址【10.87.57.73】运行端口【8092】, 无法执行定时任务【com.simbest.boot.bps.task.AutoCloseTodoTask】【640】
2025-05-27 02:15:00.011 [pool-14-thread-2] INFO  com.simbest.boot.component.task.AbstractTaskSchedule.checkAndExecute Line:198 - 集群主控节点主机地址【10.92.82.161】运行端口【10031】,当前主机地址【10.87.57.73】运行端口【8092】, 无法执行定时任务【com.simbest.boot.bps.task.AutoCheckWithUpdateTask】【638】
2025-05-27 02:15:00.011 [pool-14-thread-6] INFO  com.simbest.boot.component.task.AbstractTaskSchedule.checkAndExecute Line:198 - 集群主控节点主机地址【10.92.82.161】运行端口【10031】,当前主机地址【10.87.57.73】运行端口【8092】, 无法执行定时任务【com.simbest.boot.component.task.HeartTestTask】【642】
2025-05-27 02:15:00.012 [pool-14-thread-1] INFO  com.simbest.boot.component.task.AbstractTaskSchedule.checkAndExecute Line:198 - 集群主控节点主机地址【10.92.82.161】运行端口【10031】,当前主机地址【10.87.57.73】运行端口【8092】, 无法执行定时任务【com.simbest.boot.bps.task.AutoAddTodoTask】【637】
2025-05-27 02:15:00.012 [pool-14-thread-10] INFO  com.simbest.boot.component.task.AbstractTaskSchedule.checkAndExecute Line:198 - 集群主控节点主机地址【10.92.82.161】运行端口【10031】,当前主机地址【10.87.57.73】运行端口【8092】, 无法执行定时任务【com.simbest.boot.processdata.pushWorkmanager.task.SyncTaskDataToWorkManagerTask】【646】
2025-05-27 02:15:00.012 [pool-14-thread-3] INFO  com.simbest.boot.component.task.AbstractTaskSchedule.checkAndExecute Line:198 - 集群主控节点主机地址【10.92.82.161】运行端口【10031】,当前主机地址【10.87.57.73】运行端口【8092】, 无法执行定时任务【com.simbest.boot.processdata.pushWorkmanager.task.SyncProcessDataToWorkManagerTask】【639】
2025-05-27 02:16:00.015 [pool-14-thread-4] INFO  com.simbest.boot.component.task.AbstractTaskSchedule.checkAndExecute Line:198 - 集群主控节点主机地址【10.92.82.161】运行端口【10031】,当前主机地址【10.87.57.73】运行端口【8092】, 无法执行定时任务【com.simbest.boot.component.task.HeartTestTask】【640】
2025-05-27 02:17:00.014 [pool-14-thread-8] INFO  com.simbest.boot.component.task.AbstractTaskSchedule.checkAndExecute Line:198 - 集群主控节点主机地址【10.92.82.161】运行端口【10031】,当前主机地址【10.87.57.73】运行端口【8092】, 无法执行定时任务【com.simbest.boot.component.task.HeartTestTask】【644】
2025-05-27 02:18:00.012 [pool-14-thread-5] INFO  com.simbest.boot.component.task.AbstractTaskSchedule.checkAndExecute Line:198 - 集群主控节点主机地址【10.92.82.161】运行端口【10031】,当前主机地址【10.87.57.73】运行端口【8092】, 无法执行定时任务【com.simbest.boot.component.task.HeartTestTask】【641】
2025-05-27 02:19:00.009 [pool-14-thread-3] INFO  com.simbest.boot.component.task.AbstractTaskSchedule.checkAndExecute Line:198 - 集群主控节点主机地址【10.92.82.161】运行端口【10031】,当前主机地址【10.87.57.73】运行端口【8092】, 无法执行定时任务【com.simbest.boot.component.task.HeartTestTask】【639】
2025-05-27 02:20:00.011 [pool-14-thread-8] INFO  com.simbest.boot.component.task.AbstractTaskSchedule.checkAndExecute Line:198 - 集群主控节点主机地址【10.92.82.161】运行端口【10031】,当前主机地址【10.87.57.73】运行端口【8092】, 无法执行定时任务【com.simbest.boot.component.task.HeartTestTask】【644】
2025-05-27 02:20:00.011 [pool-14-thread-6] INFO  com.simbest.boot.component.task.AbstractTaskSchedule.checkAndExecute Line:198 - 集群主控节点主机地址【10.92.82.161】运行端口【10031】,当前主机地址【10.87.57.73】运行端口【8092】, 无法执行定时任务【com.simbest.boot.bps.task.AutoAddTodoTask】【642】
2025-05-27 02:20:00.011 [pool-14-thread-4] INFO  com.simbest.boot.component.task.AbstractTaskSchedule.checkAndExecute Line:198 - 集群主控节点主机地址【10.92.82.161】运行端口【10031】,当前主机地址【10.87.57.73】运行端口【8092】, 无法执行定时任务【com.simbest.boot.processdata.pushWorkmanager.task.SyncProcessDataToWorkManagerTask】【640】
2025-05-27 02:20:00.012 [pool-14-thread-9] INFO  com.simbest.boot.component.task.AbstractTaskSchedule.checkAndExecute Line:198 - 集群主控节点主机地址【10.92.82.161】运行端口【10031】,当前主机地址【10.87.57.73】运行端口【8092】, 无法执行定时任务【com.simbest.boot.processdata.pushWorkmanager.task.SyncTaskDataToWorkManagerTask】【645】
2025-05-27 02:20:00.011 [pool-14-thread-3] INFO  com.simbest.boot.component.task.AbstractTaskSchedule.checkAndExecute Line:198 - 集群主控节点主机地址【10.92.82.161】运行端口【10031】,当前主机地址【10.87.57.73】运行端口【8092】, 无法执行定时任务【com.simbest.boot.processdata.pushWorkmanager.task.SyncCommentDataToWorkManagerTask】【639】
2025-05-27 02:20:00.011 [pool-14-thread-2] INFO  com.simbest.boot.component.task.AbstractTaskSchedule.checkAndExecute Line:198 - 集群主控节点主机地址【10.92.82.161】运行端口【10031】,当前主机地址【10.87.57.73】运行端口【8092】, 无法执行定时任务【com.simbest.boot.bps.task.AutoCloseTodoTask】【638】
2025-05-27 02:20:00.011 [pool-14-thread-7] INFO  com.simbest.boot.component.task.AbstractTaskSchedule.checkAndExecute Line:198 - 集群主控节点主机地址【10.92.82.161】运行端口【10031】,当前主机地址【10.87.57.73】运行端口【8092】, 无法执行定时任务【com.simbest.boot.bps.task.AutoCheckWithUpdateTask】【643】
2025-05-27 02:21:00.012 [pool-14-thread-9] INFO  com.simbest.boot.component.task.AbstractTaskSchedule.checkAndExecute Line:198 - 集群主控节点主机地址【10.92.82.161】运行端口【10031】,当前主机地址【10.87.57.73】运行端口【8092】, 无法执行定时任务【com.simbest.boot.component.task.HeartTestTask】【645】
2025-05-27 02:22:00.012 [pool-14-thread-8] INFO  com.simbest.boot.component.task.AbstractTaskSchedule.checkAndExecute Line:198 - 集群主控节点主机地址【10.92.82.161】运行端口【10031】,当前主机地址【10.87.57.73】运行端口【8092】, 无法执行定时任务【com.simbest.boot.component.task.HeartTestTask】【644】
2025-05-27 02:23:00.011 [pool-14-thread-7] INFO  com.simbest.boot.component.task.AbstractTaskSchedule.checkAndExecute Line:198 - 集群主控节点主机地址【10.92.82.161】运行端口【10031】,当前主机地址【10.87.57.73】运行端口【8092】, 无法执行定时任务【com.simbest.boot.component.task.HeartTestTask】【643】
2025-05-27 02:24:00.000 [pool-14-thread-6] INFO  com.simbest.boot.component.task.AbstractTaskSchedule.checkAndExecute Line:198 - 集群主控节点主机地址【10.92.82.161】运行端口【10031】,当前主机地址【10.87.57.73】运行端口【8092】, 无法执行定时任务【com.simbest.boot.component.task.HeartTestTask】【642】
2025-05-27 02:25:00.014 [pool-14-thread-3] INFO  com.simbest.boot.component.task.AbstractTaskSchedule.checkAndExecute Line:198 - 集群主控节点主机地址【10.92.82.161】运行端口【10031】,当前主机地址【10.87.57.73】运行端口【8092】, 无法执行定时任务【com.simbest.boot.processdata.pushWorkmanager.task.SyncCommentDataToWorkManagerTask】【639】
2025-05-27 02:25:00.014 [pool-14-thread-7] INFO  com.simbest.boot.component.task.AbstractTaskSchedule.checkAndExecute Line:198 - 集群主控节点主机地址【10.92.82.161】运行端口【10031】,当前主机地址【10.87.57.73】运行端口【8092】, 无法执行定时任务【com.simbest.boot.processdata.pushWorkmanager.task.SyncTaskDataToWorkManagerTask】【643】
2025-05-27 02:25:00.014 [pool-14-thread-10] INFO  com.simbest.boot.component.task.AbstractTaskSchedule.checkAndExecute Line:198 - 集群主控节点主机地址【10.92.82.161】运行端口【10031】,当前主机地址【10.87.57.73】运行端口【8092】, 无法执行定时任务【com.simbest.boot.bps.task.AutoCheckWithUpdateTask】【646】
2025-05-27 02:25:00.014 [pool-14-thread-9] INFO  com.simbest.boot.component.task.AbstractTaskSchedule.checkAndExecute Line:198 - 集群主控节点主机地址【10.92.82.161】运行端口【10031】,当前主机地址【10.87.57.73】运行端口【8092】, 无法执行定时任务【com.simbest.boot.bps.task.AutoAddTodoTask】【645】
2025-05-27 02:25:00.015 [pool-14-thread-6] INFO  com.simbest.boot.component.task.AbstractTaskSchedule.checkAndExecute Line:198 - 集群主控节点主机地址【10.92.82.161】运行端口【10031】,当前主机地址【10.87.57.73】运行端口【8092】, 无法执行定时任务【com.simbest.boot.processdata.pushWorkmanager.task.SyncProcessDataToWorkManagerTask】【642】
2025-05-27 02:25:00.014 [pool-14-thread-5] INFO  com.simbest.boot.component.task.AbstractTaskSchedule.checkAndExecute Line:198 - 集群主控节点主机地址【10.92.82.161】运行端口【10031】,当前主机地址【10.87.57.73】运行端口【8092】, 无法执行定时任务【com.simbest.boot.bps.task.AutoCloseTodoTask】【641】
2025-05-27 02:25:00.014 [pool-14-thread-2] INFO  com.simbest.boot.component.task.AbstractTaskSchedule.checkAndExecute Line:198 - 集群主控节点主机地址【10.92.82.161】运行端口【10031】,当前主机地址【10.87.57.73】运行端口【8092】, 无法执行定时任务【com.simbest.boot.component.task.HeartTestTask】【638】
2025-05-27 02:26:00.001 [pool-14-thread-6] INFO  com.simbest.boot.component.task.AbstractTaskSchedule.checkAndExecute Line:198 - 集群主控节点主机地址【10.92.82.161】运行端口【10031】,当前主机地址【10.87.57.73】运行端口【8092】, 无法执行定时任务【com.simbest.boot.component.task.HeartTestTask】【642】
2025-05-27 02:27:00.004 [pool-14-thread-2] INFO  com.simbest.boot.component.task.AbstractTaskSchedule.checkAndExecute Line:198 - 集群主控节点主机地址【10.92.82.161】运行端口【10031】,当前主机地址【10.87.57.73】运行端口【8092】, 无法执行定时任务【com.simbest.boot.component.task.HeartTestTask】【638】
2025-05-27 02:28:00.009 [pool-14-thread-1] INFO  com.simbest.boot.component.task.AbstractTaskSchedule.checkAndExecute Line:198 - 集群主控节点主机地址【10.92.82.161】运行端口【10031】,当前主机地址【10.87.57.73】运行端口【8092】, 无法执行定时任务【com.simbest.boot.component.task.HeartTestTask】【637】
2025-05-27 02:49:11.869 [pool-14-thread-3] INFO  com.simbest.boot.component.task.AbstractTaskSchedule.checkAndExecute Line:198 - 集群主控节点主机地址【10.92.82.161】运行端口【10031】,当前主机地址【10.87.57.73】运行端口【8092】, 无法执行定时任务【com.simbest.boot.bps.task.AutoCloseTodoTask】【639】
2025-05-27 02:49:11.869 [pool-14-thread-8] INFO  com.simbest.boot.component.task.AbstractTaskSchedule.checkAndExecute Line:198 - 集群主控节点主机地址【10.92.82.161】运行端口【10031】,当前主机地址【10.87.57.73】运行端口【8092】, 无法执行定时任务【com.simbest.boot.processdata.pushWorkmanager.task.SyncCommentDataToWorkManagerTask】【644】
2025-05-27 02:49:11.869 [pool-14-thread-9] INFO  com.simbest.boot.component.task.AbstractTaskSchedule.checkAndExecute Line:198 - 集群主控节点主机地址【10.92.82.161】运行端口【10031】,当前主机地址【10.87.57.73】运行端口【8092】, 无法执行定时任务【com.simbest.boot.component.task.HeartTestTask】【645】
2025-05-27 02:49:11.869 [pool-14-thread-10] INFO  com.simbest.boot.component.task.AbstractTaskSchedule.checkAndExecute Line:198 - 集群主控节点主机地址【10.92.82.161】运行端口【10031】,当前主机地址【10.87.57.73】运行端口【8092】, 无法执行定时任务【com.simbest.boot.component.task.LicenseCheckTask】【646】
2025-05-27 02:49:11.869 [pool-14-thread-4] INFO  com.simbest.boot.component.task.AbstractTaskSchedule.checkAndExecute Line:198 - 集群主控节点主机地址【10.92.82.161】运行端口【10031】,当前主机地址【10.87.57.73】运行端口【8092】, 无法执行定时任务【com.simbest.boot.bps.task.AutoAddTodoTask】【640】
2025-05-27 02:49:11.869 [pool-14-thread-5] INFO  com.simbest.boot.component.task.AbstractTaskSchedule.checkAndExecute Line:198 - 集群主控节点主机地址【10.92.82.161】运行端口【10031】,当前主机地址【10.87.57.73】运行端口【8092】, 无法执行定时任务【com.simbest.boot.bps.task.AutoCheckWithUpdateTask】【641】
2025-05-27 02:49:11.869 [pool-14-thread-8] INFO  com.simbest.boot.component.task.AbstractTaskSchedule.checkAndExecute Line:198 - 集群主控节点主机地址【10.92.82.161】运行端口【10031】,当前主机地址【10.87.57.73】运行端口【8092】, 无法执行定时任务【com.simbest.boot.processdata.pushWorkmanager.task.SyncTaskDataToWorkManagerTask】【644】
2025-05-27 02:49:11.869 [pool-14-thread-2] INFO  com.simbest.boot.component.task.AbstractTaskSchedule.checkAndExecute Line:198 - 集群主控节点主机地址【10.92.82.161】运行端口【10031】,当前主机地址【10.87.57.73】运行端口【8092】, 无法执行定时任务【com.simbest.boot.processdata.pushWorkmanager.task.SyncProcessDataToWorkManagerTask】【638】
2025-05-27 02:50:00.009 [pool-14-thread-8] INFO  com.simbest.boot.component.task.AbstractTaskSchedule.checkAndExecute Line:198 - 集群主控节点主机地址【10.92.82.161】运行端口【10031】,当前主机地址【10.87.57.73】运行端口【8092】, 无法执行定时任务【com.simbest.boot.bps.task.AutoCloseTodoTask】【644】
2025-05-27 02:50:00.009 [pool-14-thread-2] INFO  com.simbest.boot.component.task.AbstractTaskSchedule.checkAndExecute Line:198 - 集群主控节点主机地址【10.92.82.161】运行端口【10031】,当前主机地址【10.87.57.73】运行端口【8092】, 无法执行定时任务【com.simbest.boot.processdata.pushWorkmanager.task.SyncCommentDataToWorkManagerTask】【638】
2025-05-27 02:50:00.009 [pool-14-thread-1] INFO  com.simbest.boot.component.task.AbstractTaskSchedule.checkAndExecute Line:198 - 集群主控节点主机地址【10.92.82.161】运行端口【10031】,当前主机地址【10.87.57.73】运行端口【8092】, 无法执行定时任务【com.simbest.boot.bps.task.AutoAddTodoTask】【637】
2025-05-27 02:50:00.009 [pool-14-thread-7] INFO  com.simbest.boot.component.task.AbstractTaskSchedule.checkAndExecute Line:198 - 集群主控节点主机地址【10.92.82.161】运行端口【10031】,当前主机地址【10.87.57.73】运行端口【8092】, 无法执行定时任务【com.simbest.boot.bps.task.AutoCheckWithUpdateTask】【643】
2025-05-27 02:50:00.009 [pool-14-thread-10] INFO  com.simbest.boot.component.task.AbstractTaskSchedule.checkAndExecute Line:198 - 集群主控节点主机地址【10.92.82.161】运行端口【10031】,当前主机地址【10.87.57.73】运行端口【8092】, 无法执行定时任务【com.simbest.boot.processdata.pushWorkmanager.task.SyncTaskDataToWorkManagerTask】【646】
2025-05-27 02:50:00.009 [pool-14-thread-9] INFO  com.simbest.boot.component.task.AbstractTaskSchedule.checkAndExecute Line:198 - 集群主控节点主机地址【10.92.82.161】运行端口【10031】,当前主机地址【10.87.57.73】运行端口【8092】, 无法执行定时任务【com.simbest.boot.component.task.HeartTestTask】【645】
2025-05-27 02:50:00.009 [pool-14-thread-4] INFO  com.simbest.boot.component.task.AbstractTaskSchedule.checkAndExecute Line:198 - 集群主控节点主机地址【10.92.82.161】运行端口【10031】,当前主机地址【10.87.57.73】运行端口【8092】, 无法执行定时任务【com.simbest.boot.processdata.pushWorkmanager.task.SyncProcessDataToWorkManagerTask】【640】
2025-05-27 02:51:00.009 [pool-14-thread-9] INFO  com.simbest.boot.component.task.AbstractTaskSchedule.checkAndExecute Line:198 - 集群主控节点主机地址【10.92.82.161】运行端口【10031】,当前主机地址【10.87.57.73】运行端口【8092】, 无法执行定时任务【com.simbest.boot.component.task.HeartTestTask】【645】
2025-05-27 02:52:00.012 [pool-14-thread-9] INFO  com.simbest.boot.component.task.AbstractTaskSchedule.checkAndExecute Line:198 - 集群主控节点主机地址【10.92.82.161】运行端口【10031】,当前主机地址【10.87.57.73】运行端口【8092】, 无法执行定时任务【com.simbest.boot.component.task.HeartTestTask】【645】
2025-05-27 02:53:00.005 [pool-14-thread-9] INFO  com.simbest.boot.component.task.AbstractTaskSchedule.checkAndExecute Line:198 - 集群主控节点主机地址【10.92.82.161】运行端口【10031】,当前主机地址【10.87.57.73】运行端口【8092】, 无法执行定时任务【com.simbest.boot.component.task.HeartTestTask】【645】
2025-05-27 02:54:00.008 [pool-14-thread-6] INFO  com.simbest.boot.component.task.AbstractTaskSchedule.checkAndExecute Line:198 - 集群主控节点主机地址【10.92.82.161】运行端口【10031】,当前主机地址【10.87.57.73】运行端口【8092】, 无法执行定时任务【com.simbest.boot.component.task.HeartTestTask】【642】
2025-05-27 02:55:00.010 [pool-14-thread-1] INFO  com.simbest.boot.component.task.AbstractTaskSchedule.checkAndExecute Line:198 - 集群主控节点主机地址【10.92.82.161】运行端口【10031】,当前主机地址【10.87.57.73】运行端口【8092】, 无法执行定时任务【com.simbest.boot.processdata.pushWorkmanager.task.SyncCommentDataToWorkManagerTask】【637】
2025-05-27 02:55:00.010 [pool-14-thread-6] INFO  com.simbest.boot.component.task.AbstractTaskSchedule.checkAndExecute Line:198 - 集群主控节点主机地址【10.92.82.161】运行端口【10031】,当前主机地址【10.87.57.73】运行端口【8092】, 无法执行定时任务【com.simbest.boot.processdata.pushWorkmanager.task.SyncProcessDataToWorkManagerTask】【642】
2025-05-27 02:55:00.010 [pool-14-thread-4] INFO  com.simbest.boot.component.task.AbstractTaskSchedule.checkAndExecute Line:198 - 集群主控节点主机地址【10.92.82.161】运行端口【10031】,当前主机地址【10.87.57.73】运行端口【8092】, 无法执行定时任务【com.simbest.boot.bps.task.AutoAddTodoTask】【640】
2025-05-27 02:55:00.010 [pool-14-thread-7] INFO  com.simbest.boot.component.task.AbstractTaskSchedule.checkAndExecute Line:198 - 集群主控节点主机地址【10.92.82.161】运行端口【10031】,当前主机地址【10.87.57.73】运行端口【8092】, 无法执行定时任务【com.simbest.boot.processdata.pushWorkmanager.task.SyncTaskDataToWorkManagerTask】【643】
2025-05-27 02:55:00.010 [pool-14-thread-5] INFO  com.simbest.boot.component.task.AbstractTaskSchedule.checkAndExecute Line:198 - 集群主控节点主机地址【10.92.82.161】运行端口【10031】,当前主机地址【10.87.57.73】运行端口【8092】, 无法执行定时任务【com.simbest.boot.bps.task.AutoCheckWithUpdateTask】【641】
2025-05-27 02:55:00.010 [pool-14-thread-2] INFO  com.simbest.boot.component.task.AbstractTaskSchedule.checkAndExecute Line:198 - 集群主控节点主机地址【10.92.82.161】运行端口【10031】,当前主机地址【10.87.57.73】运行端口【8092】, 无法执行定时任务【com.simbest.boot.bps.task.AutoCloseTodoTask】【638】
2025-05-27 02:55:00.010 [pool-14-thread-8] INFO  com.simbest.boot.component.task.AbstractTaskSchedule.checkAndExecute Line:198 - 集群主控节点主机地址【10.92.82.161】运行端口【10031】,当前主机地址【10.87.57.73】运行端口【8092】, 无法执行定时任务【com.simbest.boot.component.task.HeartTestTask】【644】
2025-05-27 02:56:00.010 [pool-14-thread-9] INFO  com.simbest.boot.component.task.AbstractTaskSchedule.checkAndExecute Line:198 - 集群主控节点主机地址【10.92.82.161】运行端口【10031】,当前主机地址【10.87.57.73】运行端口【8092】, 无法执行定时任务【com.simbest.boot.component.task.HeartTestTask】【645】
2025-05-27 02:57:00.010 [pool-14-thread-9] INFO  com.simbest.boot.component.task.AbstractTaskSchedule.checkAndExecute Line:198 - 集群主控节点主机地址【10.92.82.161】运行端口【10031】,当前主机地址【10.87.57.73】运行端口【8092】, 无法执行定时任务【com.simbest.boot.component.task.HeartTestTask】【645】
2025-05-27 02:58:00.014 [pool-14-thread-5] INFO  com.simbest.boot.component.task.AbstractTaskSchedule.checkAndExecute Line:198 - 集群主控节点主机地址【10.92.82.161】运行端口【10031】,当前主机地址【10.87.57.73】运行端口【8092】, 无法执行定时任务【com.simbest.boot.component.task.HeartTestTask】【641】
2025-05-27 02:59:00.006 [pool-14-thread-7] INFO  com.simbest.boot.component.task.AbstractTaskSchedule.checkAndExecute Line:198 - 集群主控节点主机地址【10.92.82.161】运行端口【10031】,当前主机地址【10.87.57.73】运行端口【8092】, 无法执行定时任务【com.simbest.boot.component.task.HeartTestTask】【643】
2025-05-27 02:59:59.981 [pool-14-thread-4] INFO  com.simbest.boot.component.task.AbstractTaskSchedule.checkAndExecute Line:198 - 集群主控节点主机地址【10.92.82.161】运行端口【10031】,当前主机地址【10.87.57.73】运行端口【8092】, 无法执行定时任务【com.simbest.boot.cmcc.a4.task.A4TaskCreateXmlFile】【640】
2025-05-27 03:00:00.012 [pool-14-thread-10] INFO  com.simbest.boot.component.task.AbstractTaskSchedule.checkAndExecute Line:198 - 集群主控节点主机地址【10.92.82.161】运行端口【10031】,当前主机地址【10.87.57.73】运行端口【8092】, 无法执行定时任务【com.simbest.boot.processdata.pushWorkmanager.task.SyncCommentDataToWorkManagerTask】【646】
2025-05-27 03:00:00.012 [pool-14-thread-1] INFO  com.simbest.boot.component.task.AbstractTaskSchedule.checkAndExecute Line:198 - 集群主控节点主机地址【10.92.82.161】运行端口【10031】,当前主机地址【10.87.57.73】运行端口【8092】, 无法执行定时任务【com.simbest.boot.bps.task.AutoAddTodoTask】【637】
2025-05-27 03:00:00.012 [pool-14-thread-2] INFO  com.simbest.boot.component.task.AbstractTaskSchedule.checkAndExecute Line:198 - 集群主控节点主机地址【10.92.82.161】运行端口【10031】,当前主机地址【10.87.57.73】运行端口【8092】, 无法执行定时任务【com.simbest.boot.processdata.pushWorkmanager.task.SyncProcessDataToWorkManagerTask】【638】
2025-05-27 03:00:00.012 [pool-14-thread-9] INFO  com.simbest.boot.component.task.AbstractTaskSchedule.checkAndExecute Line:198 - 集群主控节点主机地址【10.92.82.161】运行端口【10031】,当前主机地址【10.87.57.73】运行端口【8092】, 无法执行定时任务【com.simbest.boot.bps.task.AutoCloseTodoTask】【645】
2025-05-27 03:00:00.012 [pool-14-thread-10] INFO  com.simbest.boot.component.task.AbstractTaskSchedule.checkAndExecute Line:198 - 集群主控节点主机地址【10.92.82.161】运行端口【10031】,当前主机地址【10.87.57.73】运行端口【8092】, 无法执行定时任务【com.simbest.boot.component.task.LicenseCheckTask】【646】
2025-05-27 03:00:00.012 [pool-14-thread-5] INFO  com.simbest.boot.component.task.AbstractTaskSchedule.checkAndExecute Line:198 - 集群主控节点主机地址【10.92.82.161】运行端口【10031】,当前主机地址【10.87.57.73】运行端口【8092】, 无法执行定时任务【com.simbest.boot.processdata.pushWorkmanager.task.SyncTaskDataToWorkManagerTask】【641】
2025-05-27 03:00:00.012 [pool-14-thread-8] INFO  com.simbest.boot.component.task.AbstractTaskSchedule.checkAndExecute Line:198 - 集群主控节点主机地址【10.92.82.161】运行端口【10031】,当前主机地址【10.87.57.73】运行端口【8092】, 无法执行定时任务【com.simbest.boot.component.task.HeartTestTask】【644】
2025-05-27 03:00:00.012 [pool-14-thread-4] INFO  com.simbest.boot.component.task.AbstractTaskSchedule.checkAndExecute Line:198 - 集群主控节点主机地址【10.92.82.161】运行端口【10031】,当前主机地址【10.87.57.73】运行端口【8092】, 无法执行定时任务【com.simbest.boot.bps.task.AutoCheckWithUpdateTask】【640】
2025-05-27 03:00:01.012 [pool-14-thread-10] INFO  com.simbest.boot.component.task.AbstractTaskSchedule.checkAndExecute Line:198 - 集群主控节点主机地址【10.92.82.161】运行端口【10031】,当前主机地址【10.87.57.73】运行端口【8092】, 无法执行定时任务【com.simbest.boot.component.task.LicenseCheckTask】【646】
2025-05-27 03:00:02.001 [pool-14-thread-10] INFO  com.simbest.boot.component.task.AbstractTaskSchedule.checkAndExecute Line:198 - 集群主控节点主机地址【10.92.82.161】运行端口【10031】,当前主机地址【10.87.57.73】运行端口【8092】, 无法执行定时任务【com.simbest.boot.component.task.LicenseCheckTask】【646】
2025-05-27 03:00:03.011 [pool-14-thread-10] INFO  com.simbest.boot.component.task.AbstractTaskSchedule.checkAndExecute Line:198 - 集群主控节点主机地址【10.92.82.161】运行端口【10031】,当前主机地址【10.87.57.73】运行端口【8092】, 无法执行定时任务【com.simbest.boot.component.task.LicenseCheckTask】【646】
2025-05-27 03:00:04.006 [pool-14-thread-10] INFO  com.simbest.boot.component.task.AbstractTaskSchedule.checkAndExecute Line:198 - 集群主控节点主机地址【10.92.82.161】运行端口【10031】,当前主机地址【10.87.57.73】运行端口【8092】, 无法执行定时任务【com.simbest.boot.component.task.LicenseCheckTask】【646】
2025-05-27 03:00:05.010 [pool-14-thread-3] INFO  com.simbest.boot.component.task.AbstractTaskSchedule.checkAndExecute Line:198 - 集群主控节点主机地址【10.92.82.161】运行端口【10031】,当前主机地址【10.87.57.73】运行端口【8092】, 无法执行定时任务【com.simbest.boot.component.task.LicenseCheckTask】【639】
2025-05-27 03:00:06.013 [pool-14-thread-3] INFO  com.simbest.boot.component.task.AbstractTaskSchedule.checkAndExecute Line:198 - 集群主控节点主机地址【10.92.82.161】运行端口【10031】,当前主机地址【10.87.57.73】运行端口【8092】, 无法执行定时任务【com.simbest.boot.component.task.LicenseCheckTask】【639】
2025-05-27 03:00:07.013 [pool-14-thread-3] INFO  com.simbest.boot.component.task.AbstractTaskSchedule.checkAndExecute Line:198 - 集群主控节点主机地址【10.92.82.161】运行端口【10031】,当前主机地址【10.87.57.73】运行端口【8092】, 无法执行定时任务【com.simbest.boot.component.task.LicenseCheckTask】【639】
2025-05-27 03:00:08.007 [pool-14-thread-8] INFO  com.simbest.boot.component.task.AbstractTaskSchedule.checkAndExecute Line:198 - 集群主控节点主机地址【10.92.82.161】运行端口【10031】,当前主机地址【10.87.57.73】运行端口【8092】, 无法执行定时任务【com.simbest.boot.component.task.LicenseCheckTask】【644】
2025-05-27 03:00:09.012 [pool-14-thread-8] INFO  com.simbest.boot.component.task.AbstractTaskSchedule.checkAndExecute Line:198 - 集群主控节点主机地址【10.92.82.161】运行端口【10031】,当前主机地址【10.87.57.73】运行端口【8092】, 无法执行定时任务【com.simbest.boot.component.task.LicenseCheckTask】【644】
2025-05-27 03:00:10.015 [pool-14-thread-7] INFO  com.simbest.boot.component.task.AbstractTaskSchedule.checkAndExecute Line:198 - 集群主控节点主机地址【10.92.82.161】运行端口【10031】,当前主机地址【10.87.57.73】运行端口【8092】, 无法执行定时任务【com.simbest.boot.component.task.LicenseCheckTask】【643】
2025-05-27 03:00:11.008 [pool-14-thread-7] INFO  com.simbest.boot.component.task.AbstractTaskSchedule.checkAndExecute Line:198 - 集群主控节点主机地址【10.92.82.161】运行端口【10031】,当前主机地址【10.87.57.73】运行端口【8092】, 无法执行定时任务【com.simbest.boot.component.task.LicenseCheckTask】【643】
2025-05-27 03:00:12.010 [pool-14-thread-7] INFO  com.simbest.boot.component.task.AbstractTaskSchedule.checkAndExecute Line:198 - 集群主控节点主机地址【10.92.82.161】运行端口【10031】,当前主机地址【10.87.57.73】运行端口【8092】, 无法执行定时任务【com.simbest.boot.component.task.LicenseCheckTask】【643】
2025-05-27 03:00:13.015 [pool-14-thread-4] INFO  com.simbest.boot.component.task.AbstractTaskSchedule.checkAndExecute Line:198 - 集群主控节点主机地址【10.92.82.161】运行端口【10031】,当前主机地址【10.87.57.73】运行端口【8092】, 无法执行定时任务【com.simbest.boot.component.task.LicenseCheckTask】【640】
2025-05-27 03:00:14.008 [pool-14-thread-4] INFO  com.simbest.boot.component.task.AbstractTaskSchedule.checkAndExecute Line:198 - 集群主控节点主机地址【10.92.82.161】运行端口【10031】,当前主机地址【10.87.57.73】运行端口【8092】, 无法执行定时任务【com.simbest.boot.component.task.LicenseCheckTask】【640】
2025-05-27 03:00:15.014 [pool-14-thread-4] INFO  com.simbest.boot.component.task.AbstractTaskSchedule.checkAndExecute Line:198 - 集群主控节点主机地址【10.92.82.161】运行端口【10031】,当前主机地址【10.87.57.73】运行端口【8092】, 无法执行定时任务【com.simbest.boot.component.task.LicenseCheckTask】【640】
2025-05-27 03:00:16.009 [pool-14-thread-4] INFO  com.simbest.boot.component.task.AbstractTaskSchedule.checkAndExecute Line:198 - 集群主控节点主机地址【10.92.82.161】运行端口【10031】,当前主机地址【10.87.57.73】运行端口【8092】, 无法执行定时任务【com.simbest.boot.component.task.LicenseCheckTask】【640】
2025-05-27 03:00:17.012 [pool-14-thread-3] INFO  com.simbest.boot.component.task.AbstractTaskSchedule.checkAndExecute Line:198 - 集群主控节点主机地址【10.92.82.161】运行端口【10031】,当前主机地址【10.87.57.73】运行端口【8092】, 无法执行定时任务【com.simbest.boot.component.task.LicenseCheckTask】【639】
2025-05-27 03:00:18.008 [pool-14-thread-3] INFO  com.simbest.boot.component.task.AbstractTaskSchedule.checkAndExecute Line:198 - 集群主控节点主机地址【10.92.82.161】运行端口【10031】,当前主机地址【10.87.57.73】运行端口【8092】, 无法执行定时任务【com.simbest.boot.component.task.LicenseCheckTask】【639】
2025-05-27 03:00:19.010 [pool-14-thread-3] INFO  com.simbest.boot.component.task.AbstractTaskSchedule.checkAndExecute Line:198 - 集群主控节点主机地址【10.92.82.161】运行端口【10031】,当前主机地址【10.87.57.73】运行端口【8092】, 无法执行定时任务【com.simbest.boot.component.task.LicenseCheckTask】【639】
2025-05-27 03:00:20.008 [pool-14-thread-3] INFO  com.simbest.boot.component.task.AbstractTaskSchedule.checkAndExecute Line:198 - 集群主控节点主机地址【10.92.82.161】运行端口【10031】,当前主机地址【10.87.57.73】运行端口【8092】, 无法执行定时任务【com.simbest.boot.component.task.LicenseCheckTask】【639】
2025-05-27 03:00:21.000 [pool-14-thread-3] INFO  com.simbest.boot.component.task.AbstractTaskSchedule.checkAndExecute Line:198 - 集群主控节点主机地址【10.92.82.161】运行端口【10031】,当前主机地址【10.87.57.73】运行端口【8092】, 无法执行定时任务【com.simbest.boot.component.task.LicenseCheckTask】【639】
2025-05-27 03:00:22.006 [pool-14-thread-7] INFO  com.simbest.boot.component.task.AbstractTaskSchedule.checkAndExecute Line:198 - 集群主控节点主机地址【10.92.82.161】运行端口【10031】,当前主机地址【10.87.57.73】运行端口【8092】, 无法执行定时任务【com.simbest.boot.component.task.LicenseCheckTask】【643】
2025-05-27 03:00:23.012 [pool-14-thread-7] INFO  com.simbest.boot.component.task.AbstractTaskSchedule.checkAndExecute Line:198 - 集群主控节点主机地址【10.92.82.161】运行端口【10031】,当前主机地址【10.87.57.73】运行端口【8092】, 无法执行定时任务【com.simbest.boot.component.task.LicenseCheckTask】【643】
2025-05-27 03:00:24.003 [pool-14-thread-7] INFO  com.simbest.boot.component.task.AbstractTaskSchedule.checkAndExecute Line:198 - 集群主控节点主机地址【10.92.82.161】运行端口【10031】,当前主机地址【10.87.57.73】运行端口【8092】, 无法执行定时任务【com.simbest.boot.component.task.LicenseCheckTask】【643】
2025-05-27 03:00:25.000 [pool-14-thread-7] INFO  com.simbest.boot.component.task.AbstractTaskSchedule.checkAndExecute Line:198 - 集群主控节点主机地址【10.92.82.161】运行端口【10031】,当前主机地址【10.87.57.73】运行端口【8092】, 无法执行定时任务【com.simbest.boot.component.task.LicenseCheckTask】【643】
2025-05-27 03:00:26.008 [pool-14-thread-4] INFO  com.simbest.boot.component.task.AbstractTaskSchedule.checkAndExecute Line:198 - 集群主控节点主机地址【10.92.82.161】运行端口【10031】,当前主机地址【10.87.57.73】运行端口【8092】, 无法执行定时任务【com.simbest.boot.component.task.LicenseCheckTask】【640】
2025-05-27 03:00:27.001 [pool-14-thread-4] INFO  com.simbest.boot.component.task.AbstractTaskSchedule.checkAndExecute Line:198 - 集群主控节点主机地址【10.92.82.161】运行端口【10031】,当前主机地址【10.87.57.73】运行端口【8092】, 无法执行定时任务【com.simbest.boot.component.task.LicenseCheckTask】【640】
2025-05-27 03:00:28.003 [pool-14-thread-4] INFO  com.simbest.boot.component.task.AbstractTaskSchedule.checkAndExecute Line:198 - 集群主控节点主机地址【10.92.82.161】运行端口【10031】,当前主机地址【10.87.57.73】运行端口【8092】, 无法执行定时任务【com.simbest.boot.component.task.LicenseCheckTask】【640】
2025-05-27 03:00:29.012 [pool-14-thread-4] INFO  com.simbest.boot.component.task.AbstractTaskSchedule.checkAndExecute Line:198 - 集群主控节点主机地址【10.92.82.161】运行端口【10031】,当前主机地址【10.87.57.73】运行端口【8092】, 无法执行定时任务【com.simbest.boot.component.task.LicenseCheckTask】【640】
2025-05-27 03:00:30.003 [pool-14-thread-5] INFO  com.simbest.boot.component.task.AbstractTaskSchedule.checkAndExecute Line:198 - 集群主控节点主机地址【10.92.82.161】运行端口【10031】,当前主机地址【10.87.57.73】运行端口【8092】, 无法执行定时任务【com.simbest.boot.component.task.LicenseCheckTask】【641】
2025-05-27 03:00:31.013 [pool-14-thread-5] INFO  com.simbest.boot.component.task.AbstractTaskSchedule.checkAndExecute Line:198 - 集群主控节点主机地址【10.92.82.161】运行端口【10031】,当前主机地址【10.87.57.73】运行端口【8092】, 无法执行定时任务【com.simbest.boot.component.task.LicenseCheckTask】【641】
2025-05-27 03:00:32.002 [pool-14-thread-5] INFO  com.simbest.boot.component.task.AbstractTaskSchedule.checkAndExecute Line:198 - 集群主控节点主机地址【10.92.82.161】运行端口【10031】,当前主机地址【10.87.57.73】运行端口【8092】, 无法执行定时任务【com.simbest.boot.component.task.LicenseCheckTask】【641】
2025-05-27 03:00:33.010 [pool-14-thread-9] INFO  com.simbest.boot.component.task.AbstractTaskSchedule.checkAndExecute Line:198 - 集群主控节点主机地址【10.92.82.161】运行端口【10031】,当前主机地址【10.87.57.73】运行端口【8092】, 无法执行定时任务【com.simbest.boot.component.task.LicenseCheckTask】【645】
2025-05-27 03:00:34.002 [pool-14-thread-9] INFO  com.simbest.boot.component.task.AbstractTaskSchedule.checkAndExecute Line:198 - 集群主控节点主机地址【10.92.82.161】运行端口【10031】,当前主机地址【10.87.57.73】运行端口【8092】, 无法执行定时任务【com.simbest.boot.component.task.LicenseCheckTask】【645】
2025-05-27 03:00:35.010 [pool-14-thread-9] INFO  com.simbest.boot.component.task.AbstractTaskSchedule.checkAndExecute Line:198 - 集群主控节点主机地址【10.92.82.161】运行端口【10031】,当前主机地址【10.87.57.73】运行端口【8092】, 无法执行定时任务【com.simbest.boot.component.task.LicenseCheckTask】【645】
2025-05-27 03:00:36.003 [pool-14-thread-9] INFO  com.simbest.boot.component.task.AbstractTaskSchedule.checkAndExecute Line:198 - 集群主控节点主机地址【10.92.82.161】运行端口【10031】,当前主机地址【10.87.57.73】运行端口【8092】, 无法执行定时任务【com.simbest.boot.component.task.LicenseCheckTask】【645】
2025-05-27 03:00:37.008 [pool-14-thread-9] INFO  com.simbest.boot.component.task.AbstractTaskSchedule.checkAndExecute Line:198 - 集群主控节点主机地址【10.92.82.161】运行端口【10031】,当前主机地址【10.87.57.73】运行端口【8092】, 无法执行定时任务【com.simbest.boot.component.task.LicenseCheckTask】【645】
2025-05-27 03:00:38.016 [pool-14-thread-9] INFO  com.simbest.boot.component.task.AbstractTaskSchedule.checkAndExecute Line:198 - 集群主控节点主机地址【10.92.82.161】运行端口【10031】,当前主机地址【10.87.57.73】运行端口【8092】, 无法执行定时任务【com.simbest.boot.component.task.LicenseCheckTask】【645】
2025-05-27 03:00:39.003 [pool-14-thread-9] INFO  com.simbest.boot.component.task.AbstractTaskSchedule.checkAndExecute Line:198 - 集群主控节点主机地址【10.92.82.161】运行端口【10031】,当前主机地址【10.87.57.73】运行端口【8092】, 无法执行定时任务【com.simbest.boot.component.task.LicenseCheckTask】【645】
2025-05-27 03:00:40.009 [pool-14-thread-9] INFO  com.simbest.boot.component.task.AbstractTaskSchedule.checkAndExecute Line:198 - 集群主控节点主机地址【10.92.82.161】运行端口【10031】,当前主机地址【10.87.57.73】运行端口【8092】, 无法执行定时任务【com.simbest.boot.component.task.LicenseCheckTask】【645】
2025-05-27 03:00:41.013 [pool-14-thread-9] INFO  com.simbest.boot.component.task.AbstractTaskSchedule.checkAndExecute Line:198 - 集群主控节点主机地址【10.92.82.161】运行端口【10031】,当前主机地址【10.87.57.73】运行端口【8092】, 无法执行定时任务【com.simbest.boot.component.task.LicenseCheckTask】【645】
2025-05-27 03:00:42.004 [pool-14-thread-9] INFO  com.simbest.boot.component.task.AbstractTaskSchedule.checkAndExecute Line:198 - 集群主控节点主机地址【10.92.82.161】运行端口【10031】,当前主机地址【10.87.57.73】运行端口【8092】, 无法执行定时任务【com.simbest.boot.component.task.LicenseCheckTask】【645】
2025-05-27 03:00:43.009 [pool-14-thread-9] INFO  com.simbest.boot.component.task.AbstractTaskSchedule.checkAndExecute Line:198 - 集群主控节点主机地址【10.92.82.161】运行端口【10031】,当前主机地址【10.87.57.73】运行端口【8092】, 无法执行定时任务【com.simbest.boot.component.task.LicenseCheckTask】【645】
2025-05-27 03:00:44.002 [pool-14-thread-9] INFO  com.simbest.boot.component.task.AbstractTaskSchedule.checkAndExecute Line:198 - 集群主控节点主机地址【10.92.82.161】运行端口【10031】,当前主机地址【10.87.57.73】运行端口【8092】, 无法执行定时任务【com.simbest.boot.component.task.LicenseCheckTask】【645】
2025-05-27 03:00:45.004 [pool-14-thread-9] INFO  com.simbest.boot.component.task.AbstractTaskSchedule.checkAndExecute Line:198 - 集群主控节点主机地址【10.92.82.161】运行端口【10031】,当前主机地址【10.87.57.73】运行端口【8092】, 无法执行定时任务【com.simbest.boot.component.task.LicenseCheckTask】【645】
2025-05-27 03:00:46.003 [pool-14-thread-9] INFO  com.simbest.boot.component.task.AbstractTaskSchedule.checkAndExecute Line:198 - 集群主控节点主机地址【10.92.82.161】运行端口【10031】,当前主机地址【10.87.57.73】运行端口【8092】, 无法执行定时任务【com.simbest.boot.component.task.LicenseCheckTask】【645】
2025-05-27 03:00:47.007 [pool-14-thread-9] INFO  com.simbest.boot.component.task.AbstractTaskSchedule.checkAndExecute Line:198 - 集群主控节点主机地址【10.92.82.161】运行端口【10031】,当前主机地址【10.87.57.73】运行端口【8092】, 无法执行定时任务【com.simbest.boot.component.task.LicenseCheckTask】【645】
2025-05-27 03:00:48.014 [pool-14-thread-9] INFO  com.simbest.boot.component.task.AbstractTaskSchedule.checkAndExecute Line:198 - 集群主控节点主机地址【10.92.82.161】运行端口【10031】,当前主机地址【10.87.57.73】运行端口【8092】, 无法执行定时任务【com.simbest.boot.component.task.LicenseCheckTask】【645】
2025-05-27 03:00:49.002 [pool-14-thread-9] INFO  com.simbest.boot.component.task.AbstractTaskSchedule.checkAndExecute Line:198 - 集群主控节点主机地址【10.92.82.161】运行端口【10031】,当前主机地址【10.87.57.73】运行端口【8092】, 无法执行定时任务【com.simbest.boot.component.task.LicenseCheckTask】【645】
2025-05-27 03:00:50.011 [pool-14-thread-10] INFO  com.simbest.boot.component.task.AbstractTaskSchedule.checkAndExecute Line:198 - 集群主控节点主机地址【10.92.82.161】运行端口【10031】,当前主机地址【10.87.57.73】运行端口【8092】, 无法执行定时任务【com.simbest.boot.component.task.LicenseCheckTask】【646】
2025-05-27 03:00:51.001 [pool-14-thread-5] INFO  com.simbest.boot.component.task.AbstractTaskSchedule.checkAndExecute Line:198 - 集群主控节点主机地址【10.92.82.161】运行端口【10031】,当前主机地址【10.87.57.73】运行端口【8092】, 无法执行定时任务【com.simbest.boot.component.task.LicenseCheckTask】【641】
2025-05-27 03:00:52.010 [pool-14-thread-5] INFO  com.simbest.boot.component.task.AbstractTaskSchedule.checkAndExecute Line:198 - 集群主控节点主机地址【10.92.82.161】运行端口【10031】,当前主机地址【10.87.57.73】运行端口【8092】, 无法执行定时任务【com.simbest.boot.component.task.LicenseCheckTask】【641】
2025-05-27 03:00:53.015 [pool-14-thread-5] INFO  com.simbest.boot.component.task.AbstractTaskSchedule.checkAndExecute Line:198 - 集群主控节点主机地址【10.92.82.161】运行端口【10031】,当前主机地址【10.87.57.73】运行端口【8092】, 无法执行定时任务【com.simbest.boot.component.task.LicenseCheckTask】【641】
2025-05-27 03:00:54.004 [pool-14-thread-5] INFO  com.simbest.boot.component.task.AbstractTaskSchedule.checkAndExecute Line:198 - 集群主控节点主机地址【10.92.82.161】运行端口【10031】,当前主机地址【10.87.57.73】运行端口【8092】, 无法执行定时任务【com.simbest.boot.component.task.LicenseCheckTask】【641】
2025-05-27 03:00:55.010 [pool-14-thread-5] INFO  com.simbest.boot.component.task.AbstractTaskSchedule.checkAndExecute Line:198 - 集群主控节点主机地址【10.92.82.161】运行端口【10031】,当前主机地址【10.87.57.73】运行端口【8092】, 无法执行定时任务【com.simbest.boot.component.task.LicenseCheckTask】【641】
2025-05-27 03:00:56.004 [pool-14-thread-5] INFO  com.simbest.boot.component.task.AbstractTaskSchedule.checkAndExecute Line:198 - 集群主控节点主机地址【10.92.82.161】运行端口【10031】,当前主机地址【10.87.57.73】运行端口【8092】, 无法执行定时任务【com.simbest.boot.component.task.LicenseCheckTask】【641】
2025-05-27 03:00:57.011 [pool-14-thread-4] INFO  com.simbest.boot.component.task.AbstractTaskSchedule.checkAndExecute Line:198 - 集群主控节点主机地址【10.92.82.161】运行端口【10031】,当前主机地址【10.87.57.73】运行端口【8092】, 无法执行定时任务【com.simbest.boot.component.task.LicenseCheckTask】【640】
2025-05-27 03:00:58.002 [pool-14-thread-4] INFO  com.simbest.boot.component.task.AbstractTaskSchedule.checkAndExecute Line:198 - 集群主控节点主机地址【10.92.82.161】运行端口【10031】,当前主机地址【10.87.57.73】运行端口【8092】, 无法执行定时任务【com.simbest.boot.component.task.LicenseCheckTask】【640】
2025-05-27 03:00:59.005 [pool-14-thread-4] INFO  com.simbest.boot.component.task.AbstractTaskSchedule.checkAndExecute Line:198 - 集群主控节点主机地址【10.92.82.161】运行端口【10031】,当前主机地址【10.87.57.73】运行端口【8092】, 无法执行定时任务【com.simbest.boot.component.task.LicenseCheckTask】【640】
2025-05-27 03:01:00.012 [pool-14-thread-7] INFO  com.simbest.boot.component.task.AbstractTaskSchedule.checkAndExecute Line:198 - 集群主控节点主机地址【10.92.82.161】运行端口【10031】,当前主机地址【10.87.57.73】运行端口【8092】, 无法执行定时任务【com.simbest.boot.component.task.HeartTestTask】【643】
2025-05-27 03:02:00.004 [pool-14-thread-3] INFO  com.simbest.boot.component.task.AbstractTaskSchedule.checkAndExecute Line:198 - 集群主控节点主机地址【10.92.82.161】运行端口【10031】,当前主机地址【10.87.57.73】运行端口【8092】, 无法执行定时任务【com.simbest.boot.component.task.HeartTestTask】【639】
2025-05-27 03:03:00.010 [pool-14-thread-6] INFO  com.simbest.boot.component.task.AbstractTaskSchedule.checkAndExecute Line:198 - 集群主控节点主机地址【10.92.82.161】运行端口【10031】,当前主机地址【10.87.57.73】运行端口【8092】, 无法执行定时任务【com.simbest.boot.component.task.HeartTestTask】【642】
2025-05-27 03:04:00.002 [pool-14-thread-5] INFO  com.simbest.boot.component.task.AbstractTaskSchedule.checkAndExecute Line:198 - 集群主控节点主机地址【10.92.82.161】运行端口【10031】,当前主机地址【10.87.57.73】运行端口【8092】, 无法执行定时任务【com.simbest.boot.component.task.HeartTestTask】【641】
2025-05-27 03:05:00.001 [pool-14-thread-4] INFO  com.simbest.boot.component.task.AbstractTaskSchedule.checkAndExecute Line:198 - 集群主控节点主机地址【10.92.82.161】运行端口【10031】,当前主机地址【10.87.57.73】运行端口【8092】, 无法执行定时任务【com.simbest.boot.bps.task.AutoCheckWithUpdateTask】【640】
2025-05-27 03:05:00.001 [pool-14-thread-8] INFO  com.simbest.boot.component.task.AbstractTaskSchedule.checkAndExecute Line:198 - 集群主控节点主机地址【10.92.82.161】运行端口【10031】,当前主机地址【10.87.57.73】运行端口【8092】, 无法执行定时任务【com.simbest.boot.processdata.pushWorkmanager.task.SyncCommentDataToWorkManagerTask】【644】
2025-05-27 03:05:00.001 [pool-14-thread-3] INFO  com.simbest.boot.component.task.AbstractTaskSchedule.checkAndExecute Line:198 - 集群主控节点主机地址【10.92.82.161】运行端口【10031】,当前主机地址【10.87.57.73】运行端口【8092】, 无法执行定时任务【com.simbest.boot.processdata.pushWorkmanager.task.SyncTaskDataToWorkManagerTask】【639】
2025-05-27 03:05:00.001 [pool-14-thread-7] INFO  com.simbest.boot.component.task.AbstractTaskSchedule.checkAndExecute Line:198 - 集群主控节点主机地址【10.92.82.161】运行端口【10031】,当前主机地址【10.87.57.73】运行端口【8092】, 无法执行定时任务【com.simbest.boot.component.task.HeartTestTask】【643】
2025-05-27 03:05:00.001 [pool-14-thread-1] INFO  com.simbest.boot.component.task.AbstractTaskSchedule.checkAndExecute Line:198 - 集群主控节点主机地址【10.92.82.161】运行端口【10031】,当前主机地址【10.87.57.73】运行端口【8092】, 无法执行定时任务【com.simbest.boot.bps.task.AutoCloseTodoTask】【637】
2025-05-27 03:05:00.001 [pool-14-thread-2] INFO  com.simbest.boot.component.task.AbstractTaskSchedule.checkAndExecute Line:198 - 集群主控节点主机地址【10.92.82.161】运行端口【10031】,当前主机地址【10.87.57.73】运行端口【8092】, 无法执行定时任务【com.simbest.boot.bps.task.AutoAddTodoTask】【638】
2025-05-27 03:05:00.001 [pool-14-thread-10] INFO  com.simbest.boot.component.task.AbstractTaskSchedule.checkAndExecute Line:198 - 集群主控节点主机地址【10.92.82.161】运行端口【10031】,当前主机地址【10.87.57.73】运行端口【8092】, 无法执行定时任务【com.simbest.boot.processdata.pushWorkmanager.task.SyncProcessDataToWorkManagerTask】【646】
2025-05-27 03:06:00.015 [pool-14-thread-9] INFO  com.simbest.boot.component.task.AbstractTaskSchedule.checkAndExecute Line:198 - 集群主控节点主机地址【10.92.82.161】运行端口【10031】,当前主机地址【10.87.57.73】运行端口【8092】, 无法执行定时任务【com.simbest.boot.component.task.HeartTestTask】【645】
2025-05-27 03:07:00.004 [pool-14-thread-4] INFO  com.simbest.boot.component.task.AbstractTaskSchedule.checkAndExecute Line:198 - 集群主控节点主机地址【10.92.82.161】运行端口【10031】,当前主机地址【10.87.57.73】运行端口【8092】, 无法执行定时任务【com.simbest.boot.component.task.HeartTestTask】【640】
2025-05-27 03:08:00.007 [pool-14-thread-8] INFO  com.simbest.boot.component.task.AbstractTaskSchedule.checkAndExecute Line:198 - 集群主控节点主机地址【10.92.82.161】运行端口【10031】,当前主机地址【10.87.57.73】运行端口【8092】, 无法执行定时任务【com.simbest.boot.component.task.HeartTestTask】【644】
2025-05-27 03:29:12.088 [pool-14-thread-5] INFO  com.simbest.boot.component.task.AbstractTaskSchedule.checkAndExecute Line:198 - 集群主控节点主机地址【10.92.82.161】运行端口【10031】,当前主机地址【10.87.57.73】运行端口【8092】, 无法执行定时任务【com.simbest.boot.component.task.HeartTestTask】【641】
2025-05-27 03:29:12.088 [pool-14-thread-9] INFO  com.simbest.boot.component.task.AbstractTaskSchedule.checkAndExecute Line:198 - 集群主控节点主机地址【10.92.82.161】运行端口【10031】,当前主机地址【10.87.57.73】运行端口【8092】, 无法执行定时任务【com.simbest.boot.bps.task.AutoCheckWithUpdateTask】【645】
2025-05-27 03:29:12.089 [pool-14-thread-2] INFO  com.simbest.boot.component.task.AbstractTaskSchedule.checkAndExecute Line:198 - 集群主控节点主机地址【10.92.82.161】运行端口【10031】,当前主机地址【10.87.57.73】运行端口【8092】, 无法执行定时任务【com.simbest.boot.bps.task.AutoCloseTodoTask】【638】
2025-05-27 03:29:12.088 [pool-14-thread-3] INFO  com.simbest.boot.component.task.AbstractTaskSchedule.checkAndExecute Line:198 - 集群主控节点主机地址【10.92.82.161】运行端口【10031】,当前主机地址【10.87.57.73】运行端口【8092】, 无法执行定时任务【com.simbest.boot.bps.task.AutoAddTodoTask】【639】
2025-05-27 03:29:12.088 [pool-14-thread-4] INFO  com.simbest.boot.component.task.AbstractTaskSchedule.checkAndExecute Line:198 - 集群主控节点主机地址【10.92.82.161】运行端口【10031】,当前主机地址【10.87.57.73】运行端口【8092】, 无法执行定时任务【com.simbest.boot.processdata.pushWorkmanager.task.SyncCommentDataToWorkManagerTask】【640】
2025-05-27 03:29:12.088 [pool-14-thread-10] INFO  com.simbest.boot.component.task.AbstractTaskSchedule.checkAndExecute Line:198 - 集群主控节点主机地址【10.92.82.161】运行端口【10031】,当前主机地址【10.87.57.73】运行端口【8092】, 无法执行定时任务【com.simbest.boot.processdata.pushWorkmanager.task.SyncTaskDataToWorkManagerTask】【646】
2025-05-27 03:29:12.089 [pool-14-thread-7] INFO  com.simbest.boot.component.task.AbstractTaskSchedule.checkAndExecute Line:198 - 集群主控节点主机地址【10.92.82.161】运行端口【10031】,当前主机地址【10.87.57.73】运行端口【8092】, 无法执行定时任务【com.simbest.boot.processdata.pushWorkmanager.task.SyncProcessDataToWorkManagerTask】【643】
2025-05-27 03:29:59.977 [pool-14-thread-5] INFO  com.simbest.boot.component.task.AbstractTaskSchedule.checkAndExecute Line:198 - 集群主控节点主机地址【10.92.82.161】运行端口【10031】,当前主机地址【10.87.57.73】运行端口【8092】, 无法执行定时任务【com.simbest.boot.component.task.LicenseCheckTask】【641】
2025-05-27 03:30:00.008 [pool-14-thread-10] INFO  com.simbest.boot.component.task.AbstractTaskSchedule.checkAndExecute Line:198 - 集群主控节点主机地址【10.92.82.161】运行端口【10031】,当前主机地址【10.87.57.73】运行端口【8092】, 无法执行定时任务【com.simbest.boot.bps.task.AutoCloseTodoTask】【646】
2025-05-27 03:30:00.009 [pool-14-thread-9] INFO  com.simbest.boot.component.task.AbstractTaskSchedule.checkAndExecute Line:198 - 集群主控节点主机地址【10.92.82.161】运行端口【10031】,当前主机地址【10.87.57.73】运行端口【8092】, 无法执行定时任务【com.simbest.boot.bps.task.AutoAddTodoTask】【645】
2025-05-27 03:30:00.009 [pool-14-thread-4] INFO  com.simbest.boot.component.task.AbstractTaskSchedule.checkAndExecute Line:198 - 集群主控节点主机地址【10.92.82.161】运行端口【10031】,当前主机地址【10.87.57.73】运行端口【8092】, 无法执行定时任务【com.simbest.boot.processdata.pushWorkmanager.task.SyncTaskDataToWorkManagerTask】【640】
2025-05-27 03:30:00.009 [pool-14-thread-10] INFO  com.simbest.boot.component.task.AbstractTaskSchedule.checkAndExecute Line:198 - 集群主控节点主机地址【10.92.82.161】运行端口【10031】,当前主机地址【10.87.57.73】运行端口【8092】, 无法执行定时任务【com.simbest.boot.bps.task.AutoCheckWithUpdateTask】【646】
2025-05-27 03:30:00.010 [pool-14-thread-4] INFO  com.simbest.boot.component.task.AbstractTaskSchedule.checkAndExecute Line:198 - 集群主控节点主机地址【10.92.82.161】运行端口【10031】,当前主机地址【10.87.57.73】运行端口【8092】, 无法执行定时任务【com.simbest.boot.processdata.pushWorkmanager.task.SyncCommentDataToWorkManagerTask】【640】
2025-05-27 03:30:00.008 [pool-14-thread-3] INFO  com.simbest.boot.component.task.AbstractTaskSchedule.checkAndExecute Line:198 - 集群主控节点主机地址【10.92.82.161】运行端口【10031】,当前主机地址【10.87.57.73】运行端口【8092】, 无法执行定时任务【com.simbest.boot.component.task.HeartTestTask】【639】
2025-05-27 03:30:00.009 [pool-14-thread-8] INFO  com.simbest.boot.component.task.AbstractTaskSchedule.checkAndExecute Line:198 - 集群主控节点主机地址【10.92.82.161】运行端口【10031】,当前主机地址【10.87.57.73】运行端口【8092】, 无法执行定时任务【com.simbest.boot.processdata.pushWorkmanager.task.SyncProcessDataToWorkManagerTask】【644】
2025-05-27 03:30:01.009 [pool-14-thread-1] INFO  com.simbest.boot.component.task.AbstractTaskSchedule.checkAndExecute Line:198 - 集群主控节点主机地址【10.92.82.161】运行端口【10031】,当前主机地址【10.87.57.73】运行端口【8092】, 无法执行定时任务【com.simbest.boot.component.task.LicenseCheckTask】【637】
2025-05-27 03:30:02.000 [pool-14-thread-1] INFO  com.simbest.boot.component.task.AbstractTaskSchedule.checkAndExecute Line:198 - 集群主控节点主机地址【10.92.82.161】运行端口【10031】,当前主机地址【10.87.57.73】运行端口【8092】, 无法执行定时任务【com.simbest.boot.component.task.LicenseCheckTask】【637】
2025-05-27 03:30:03.012 [pool-14-thread-1] INFO  com.simbest.boot.component.task.AbstractTaskSchedule.checkAndExecute Line:198 - 集群主控节点主机地址【10.92.82.161】运行端口【10031】,当前主机地址【10.87.57.73】运行端口【8092】, 无法执行定时任务【com.simbest.boot.component.task.LicenseCheckTask】【637】
2025-05-27 03:30:04.013 [pool-14-thread-1] INFO  com.simbest.boot.component.task.AbstractTaskSchedule.checkAndExecute Line:198 - 集群主控节点主机地址【10.92.82.161】运行端口【10031】,当前主机地址【10.87.57.73】运行端口【8092】, 无法执行定时任务【com.simbest.boot.component.task.LicenseCheckTask】【637】
2025-05-27 03:30:05.004 [pool-14-thread-1] INFO  com.simbest.boot.component.task.AbstractTaskSchedule.checkAndExecute Line:198 - 集群主控节点主机地址【10.92.82.161】运行端口【10031】,当前主机地址【10.87.57.73】运行端口【8092】, 无法执行定时任务【com.simbest.boot.component.task.LicenseCheckTask】【637】
2025-05-27 03:30:06.015 [pool-14-thread-1] INFO  com.simbest.boot.component.task.AbstractTaskSchedule.checkAndExecute Line:198 - 集群主控节点主机地址【10.92.82.161】运行端口【10031】,当前主机地址【10.87.57.73】运行端口【8092】, 无法执行定时任务【com.simbest.boot.component.task.LicenseCheckTask】【637】
2025-05-27 03:30:07.004 [pool-14-thread-1] INFO  com.simbest.boot.component.task.AbstractTaskSchedule.checkAndExecute Line:198 - 集群主控节点主机地址【10.92.82.161】运行端口【10031】,当前主机地址【10.87.57.73】运行端口【8092】, 无法执行定时任务【com.simbest.boot.component.task.LicenseCheckTask】【637】
2025-05-27 03:30:08.011 [pool-14-thread-1] INFO  com.simbest.boot.component.task.AbstractTaskSchedule.checkAndExecute Line:198 - 集群主控节点主机地址【10.92.82.161】运行端口【10031】,当前主机地址【10.87.57.73】运行端口【8092】, 无法执行定时任务【com.simbest.boot.component.task.LicenseCheckTask】【637】
2025-05-27 03:30:09.004 [pool-14-thread-1] INFO  com.simbest.boot.component.task.AbstractTaskSchedule.checkAndExecute Line:198 - 集群主控节点主机地址【10.92.82.161】运行端口【10031】,当前主机地址【10.87.57.73】运行端口【8092】, 无法执行定时任务【com.simbest.boot.component.task.LicenseCheckTask】【637】
2025-05-27 03:30:10.008 [pool-14-thread-1] INFO  com.simbest.boot.component.task.AbstractTaskSchedule.checkAndExecute Line:198 - 集群主控节点主机地址【10.92.82.161】运行端口【10031】,当前主机地址【10.87.57.73】运行端口【8092】, 无法执行定时任务【com.simbest.boot.component.task.LicenseCheckTask】【637】
2025-05-27 03:30:11.013 [pool-14-thread-1] INFO  com.simbest.boot.component.task.AbstractTaskSchedule.checkAndExecute Line:198 - 集群主控节点主机地址【10.92.82.161】运行端口【10031】,当前主机地址【10.87.57.73】运行端口【8092】, 无法执行定时任务【com.simbest.boot.component.task.LicenseCheckTask】【637】
2025-05-27 03:30:12.005 [pool-14-thread-1] INFO  com.simbest.boot.component.task.AbstractTaskSchedule.checkAndExecute Line:198 - 集群主控节点主机地址【10.92.82.161】运行端口【10031】,当前主机地址【10.87.57.73】运行端口【8092】, 无法执行定时任务【com.simbest.boot.component.task.LicenseCheckTask】【637】
2025-05-27 03:30:13.002 [pool-14-thread-1] INFO  com.simbest.boot.component.task.AbstractTaskSchedule.checkAndExecute Line:198 - 集群主控节点主机地址【10.92.82.161】运行端口【10031】,当前主机地址【10.87.57.73】运行端口【8092】, 无法执行定时任务【com.simbest.boot.component.task.LicenseCheckTask】【637】
2025-05-27 03:30:14.005 [pool-14-thread-1] INFO  com.simbest.boot.component.task.AbstractTaskSchedule.checkAndExecute Line:198 - 集群主控节点主机地址【10.92.82.161】运行端口【10031】,当前主机地址【10.87.57.73】运行端口【8092】, 无法执行定时任务【com.simbest.boot.component.task.LicenseCheckTask】【637】
2025-05-27 03:30:15.013 [pool-14-thread-1] INFO  com.simbest.boot.component.task.AbstractTaskSchedule.checkAndExecute Line:198 - 集群主控节点主机地址【10.92.82.161】运行端口【10031】,当前主机地址【10.87.57.73】运行端口【8092】, 无法执行定时任务【com.simbest.boot.component.task.LicenseCheckTask】【637】
2025-05-27 03:30:16.013 [pool-14-thread-1] INFO  com.simbest.boot.component.task.AbstractTaskSchedule.checkAndExecute Line:198 - 集群主控节点主机地址【10.92.82.161】运行端口【10031】,当前主机地址【10.87.57.73】运行端口【8092】, 无法执行定时任务【com.simbest.boot.component.task.LicenseCheckTask】【637】
2025-05-27 03:30:17.003 [pool-14-thread-1] INFO  com.simbest.boot.component.task.AbstractTaskSchedule.checkAndExecute Line:198 - 集群主控节点主机地址【10.92.82.161】运行端口【10031】,当前主机地址【10.87.57.73】运行端口【8092】, 无法执行定时任务【com.simbest.boot.component.task.LicenseCheckTask】【637】
2025-05-27 03:30:18.010 [pool-14-thread-1] INFO  com.simbest.boot.component.task.AbstractTaskSchedule.checkAndExecute Line:198 - 集群主控节点主机地址【10.92.82.161】运行端口【10031】,当前主机地址【10.87.57.73】运行端口【8092】, 无法执行定时任务【com.simbest.boot.component.task.LicenseCheckTask】【637】
2025-05-27 03:30:19.002 [pool-14-thread-1] INFO  com.simbest.boot.component.task.AbstractTaskSchedule.checkAndExecute Line:198 - 集群主控节点主机地址【10.92.82.161】运行端口【10031】,当前主机地址【10.87.57.73】运行端口【8092】, 无法执行定时任务【com.simbest.boot.component.task.LicenseCheckTask】【637】
2025-05-27 03:30:20.008 [pool-14-thread-1] INFO  com.simbest.boot.component.task.AbstractTaskSchedule.checkAndExecute Line:198 - 集群主控节点主机地址【10.92.82.161】运行端口【10031】,当前主机地址【10.87.57.73】运行端口【8092】, 无法执行定时任务【com.simbest.boot.component.task.LicenseCheckTask】【637】
2025-05-27 03:30:21.014 [pool-14-thread-1] INFO  com.simbest.boot.component.task.AbstractTaskSchedule.checkAndExecute Line:198 - 集群主控节点主机地址【10.92.82.161】运行端口【10031】,当前主机地址【10.87.57.73】运行端口【8092】, 无法执行定时任务【com.simbest.boot.component.task.LicenseCheckTask】【637】
2025-05-27 03:30:22.006 [pool-14-thread-1] INFO  com.simbest.boot.component.task.AbstractTaskSchedule.checkAndExecute Line:198 - 集群主控节点主机地址【10.92.82.161】运行端口【10031】,当前主机地址【10.87.57.73】运行端口【8092】, 无法执行定时任务【com.simbest.boot.component.task.LicenseCheckTask】【637】
2025-05-27 03:30:23.009 [pool-14-thread-1] INFO  com.simbest.boot.component.task.AbstractTaskSchedule.checkAndExecute Line:198 - 集群主控节点主机地址【10.92.82.161】运行端口【10031】,当前主机地址【10.87.57.73】运行端口【8092】, 无法执行定时任务【com.simbest.boot.component.task.LicenseCheckTask】【637】
2025-05-27 03:30:24.011 [pool-14-thread-1] INFO  com.simbest.boot.component.task.AbstractTaskSchedule.checkAndExecute Line:198 - 集群主控节点主机地址【10.92.82.161】运行端口【10031】,当前主机地址【10.87.57.73】运行端口【8092】, 无法执行定时任务【com.simbest.boot.component.task.LicenseCheckTask】【637】
2025-05-27 03:30:25.011 [pool-14-thread-1] INFO  com.simbest.boot.component.task.AbstractTaskSchedule.checkAndExecute Line:198 - 集群主控节点主机地址【10.92.82.161】运行端口【10031】,当前主机地址【10.87.57.73】运行端口【8092】, 无法执行定时任务【com.simbest.boot.component.task.LicenseCheckTask】【637】
2025-05-27 03:30:26.001 [pool-14-thread-1] INFO  com.simbest.boot.component.task.AbstractTaskSchedule.checkAndExecute Line:198 - 集群主控节点主机地址【10.92.82.161】运行端口【10031】,当前主机地址【10.87.57.73】运行端口【8092】, 无法执行定时任务【com.simbest.boot.component.task.LicenseCheckTask】【637】
2025-05-27 03:30:27.007 [pool-14-thread-1] INFO  com.simbest.boot.component.task.AbstractTaskSchedule.checkAndExecute Line:198 - 集群主控节点主机地址【10.92.82.161】运行端口【10031】,当前主机地址【10.87.57.73】运行端口【8092】, 无法执行定时任务【com.simbest.boot.component.task.LicenseCheckTask】【637】
2025-05-27 03:30:28.014 [pool-14-thread-1] INFO  com.simbest.boot.component.task.AbstractTaskSchedule.checkAndExecute Line:198 - 集群主控节点主机地址【10.92.82.161】运行端口【10031】,当前主机地址【10.87.57.73】运行端口【8092】, 无法执行定时任务【com.simbest.boot.component.task.LicenseCheckTask】【637】
2025-05-27 03:30:29.004 [pool-14-thread-1] INFO  com.simbest.boot.component.task.AbstractTaskSchedule.checkAndExecute Line:198 - 集群主控节点主机地址【10.92.82.161】运行端口【10031】,当前主机地址【10.87.57.73】运行端口【8092】, 无法执行定时任务【com.simbest.boot.component.task.LicenseCheckTask】【637】
2025-05-27 03:30:30.013 [pool-14-thread-1] INFO  com.simbest.boot.component.task.AbstractTaskSchedule.checkAndExecute Line:198 - 集群主控节点主机地址【10.92.82.161】运行端口【10031】,当前主机地址【10.87.57.73】运行端口【8092】, 无法执行定时任务【com.simbest.boot.component.task.LicenseCheckTask】【637】
2025-05-27 03:30:31.008 [pool-14-thread-1] INFO  com.simbest.boot.component.task.AbstractTaskSchedule.checkAndExecute Line:198 - 集群主控节点主机地址【10.92.82.161】运行端口【10031】,当前主机地址【10.87.57.73】运行端口【8092】, 无法执行定时任务【com.simbest.boot.component.task.LicenseCheckTask】【637】
2025-05-27 03:30:32.016 [pool-14-thread-1] INFO  com.simbest.boot.component.task.AbstractTaskSchedule.checkAndExecute Line:198 - 集群主控节点主机地址【10.92.82.161】运行端口【10031】,当前主机地址【10.87.57.73】运行端口【8092】, 无法执行定时任务【com.simbest.boot.component.task.LicenseCheckTask】【637】
2025-05-27 03:30:33.011 [pool-14-thread-1] INFO  com.simbest.boot.component.task.AbstractTaskSchedule.checkAndExecute Line:198 - 集群主控节点主机地址【10.92.82.161】运行端口【10031】,当前主机地址【10.87.57.73】运行端口【8092】, 无法执行定时任务【com.simbest.boot.component.task.LicenseCheckTask】【637】
2025-05-27 03:30:34.004 [pool-14-thread-1] INFO  com.simbest.boot.component.task.AbstractTaskSchedule.checkAndExecute Line:198 - 集群主控节点主机地址【10.92.82.161】运行端口【10031】,当前主机地址【10.87.57.73】运行端口【8092】, 无法执行定时任务【com.simbest.boot.component.task.LicenseCheckTask】【637】
2025-05-27 03:30:35.013 [pool-14-thread-1] INFO  com.simbest.boot.component.task.AbstractTaskSchedule.checkAndExecute Line:198 - 集群主控节点主机地址【10.92.82.161】运行端口【10031】,当前主机地址【10.87.57.73】运行端口【8092】, 无法执行定时任务【com.simbest.boot.component.task.LicenseCheckTask】【637】
2025-05-27 03:30:36.005 [pool-14-thread-1] INFO  com.simbest.boot.component.task.AbstractTaskSchedule.checkAndExecute Line:198 - 集群主控节点主机地址【10.92.82.161】运行端口【10031】,当前主机地址【10.87.57.73】运行端口【8092】, 无法执行定时任务【com.simbest.boot.component.task.LicenseCheckTask】【637】
2025-05-27 03:30:37.002 [pool-14-thread-1] INFO  com.simbest.boot.component.task.AbstractTaskSchedule.checkAndExecute Line:198 - 集群主控节点主机地址【10.92.82.161】运行端口【10031】,当前主机地址【10.87.57.73】运行端口【8092】, 无法执行定时任务【com.simbest.boot.component.task.LicenseCheckTask】【637】
2025-05-27 03:30:38.003 [pool-14-thread-1] INFO  com.simbest.boot.component.task.AbstractTaskSchedule.checkAndExecute Line:198 - 集群主控节点主机地址【10.92.82.161】运行端口【10031】,当前主机地址【10.87.57.73】运行端口【8092】, 无法执行定时任务【com.simbest.boot.component.task.LicenseCheckTask】【637】
2025-05-27 03:30:39.003 [pool-14-thread-1] INFO  com.simbest.boot.component.task.AbstractTaskSchedule.checkAndExecute Line:198 - 集群主控节点主机地址【10.92.82.161】运行端口【10031】,当前主机地址【10.87.57.73】运行端口【8092】, 无法执行定时任务【com.simbest.boot.component.task.LicenseCheckTask】【637】
2025-05-27 03:30:40.011 [pool-14-thread-1] INFO  com.simbest.boot.component.task.AbstractTaskSchedule.checkAndExecute Line:198 - 集群主控节点主机地址【10.92.82.161】运行端口【10031】,当前主机地址【10.87.57.73】运行端口【8092】, 无法执行定时任务【com.simbest.boot.component.task.LicenseCheckTask】【637】
2025-05-27 03:30:41.000 [pool-14-thread-1] INFO  com.simbest.boot.component.task.AbstractTaskSchedule.checkAndExecute Line:198 - 集群主控节点主机地址【10.92.82.161】运行端口【10031】,当前主机地址【10.87.57.73】运行端口【8092】, 无法执行定时任务【com.simbest.boot.component.task.LicenseCheckTask】【637】
2025-05-27 03:30:42.008 [pool-14-thread-5] INFO  com.simbest.boot.component.task.AbstractTaskSchedule.checkAndExecute Line:198 - 集群主控节点主机地址【10.92.82.161】运行端口【10031】,当前主机地址【10.87.57.73】运行端口【8092】, 无法执行定时任务【com.simbest.boot.component.task.LicenseCheckTask】【641】
2025-05-27 03:30:43.010 [pool-14-thread-5] INFO  com.simbest.boot.component.task.AbstractTaskSchedule.checkAndExecute Line:198 - 集群主控节点主机地址【10.92.82.161】运行端口【10031】,当前主机地址【10.87.57.73】运行端口【8092】, 无法执行定时任务【com.simbest.boot.component.task.LicenseCheckTask】【641】
2025-05-27 03:30:44.012 [pool-14-thread-4] INFO  com.simbest.boot.component.task.AbstractTaskSchedule.checkAndExecute Line:198 - 集群主控节点主机地址【10.92.82.161】运行端口【10031】,当前主机地址【10.87.57.73】运行端口【8092】, 无法执行定时任务【com.simbest.boot.component.task.LicenseCheckTask】【640】
2025-05-27 03:30:45.006 [pool-14-thread-4] INFO  com.simbest.boot.component.task.AbstractTaskSchedule.checkAndExecute Line:198 - 集群主控节点主机地址【10.92.82.161】运行端口【10031】,当前主机地址【10.87.57.73】运行端口【8092】, 无法执行定时任务【com.simbest.boot.component.task.LicenseCheckTask】【640】
2025-05-27 03:30:46.013 [pool-14-thread-6] INFO  com.simbest.boot.component.task.AbstractTaskSchedule.checkAndExecute Line:198 - 集群主控节点主机地址【10.92.82.161】运行端口【10031】,当前主机地址【10.87.57.73】运行端口【8092】, 无法执行定时任务【com.simbest.boot.component.task.LicenseCheckTask】【642】
2025-05-27 03:30:47.004 [pool-14-thread-7] INFO  com.simbest.boot.component.task.AbstractTaskSchedule.checkAndExecute Line:198 - 集群主控节点主机地址【10.92.82.161】运行端口【10031】,当前主机地址【10.87.57.73】运行端口【8092】, 无法执行定时任务【com.simbest.boot.component.task.LicenseCheckTask】【643】
2025-05-27 03:30:48.013 [pool-14-thread-7] INFO  com.simbest.boot.component.task.AbstractTaskSchedule.checkAndExecute Line:198 - 集群主控节点主机地址【10.92.82.161】运行端口【10031】,当前主机地址【10.87.57.73】运行端口【8092】, 无法执行定时任务【com.simbest.boot.component.task.LicenseCheckTask】【643】
2025-05-27 03:30:49.003 [pool-14-thread-7] INFO  com.simbest.boot.component.task.AbstractTaskSchedule.checkAndExecute Line:198 - 集群主控节点主机地址【10.92.82.161】运行端口【10031】,当前主机地址【10.87.57.73】运行端口【8092】, 无法执行定时任务【com.simbest.boot.component.task.LicenseCheckTask】【643】
2025-05-27 03:30:50.006 [pool-14-thread-7] INFO  com.simbest.boot.component.task.AbstractTaskSchedule.checkAndExecute Line:198 - 集群主控节点主机地址【10.92.82.161】运行端口【10031】,当前主机地址【10.87.57.73】运行端口【8092】, 无法执行定时任务【com.simbest.boot.component.task.LicenseCheckTask】【643】
2025-05-27 03:30:51.011 [pool-14-thread-7] INFO  com.simbest.boot.component.task.AbstractTaskSchedule.checkAndExecute Line:198 - 集群主控节点主机地址【10.92.82.161】运行端口【10031】,当前主机地址【10.87.57.73】运行端口【8092】, 无法执行定时任务【com.simbest.boot.component.task.LicenseCheckTask】【643】
2025-05-27 03:30:52.003 [pool-14-thread-7] INFO  com.simbest.boot.component.task.AbstractTaskSchedule.checkAndExecute Line:198 - 集群主控节点主机地址【10.92.82.161】运行端口【10031】,当前主机地址【10.87.57.73】运行端口【8092】, 无法执行定时任务【com.simbest.boot.component.task.LicenseCheckTask】【643】
2025-05-27 03:30:53.009 [pool-14-thread-7] INFO  com.simbest.boot.component.task.AbstractTaskSchedule.checkAndExecute Line:198 - 集群主控节点主机地址【10.92.82.161】运行端口【10031】,当前主机地址【10.87.57.73】运行端口【8092】, 无法执行定时任务【com.simbest.boot.component.task.LicenseCheckTask】【643】
2025-05-27 03:30:54.007 [pool-14-thread-7] INFO  com.simbest.boot.component.task.AbstractTaskSchedule.checkAndExecute Line:198 - 集群主控节点主机地址【10.92.82.161】运行端口【10031】,当前主机地址【10.87.57.73】运行端口【8092】, 无法执行定时任务【com.simbest.boot.component.task.LicenseCheckTask】【643】
2025-05-27 03:30:55.001 [pool-14-thread-4] INFO  com.simbest.boot.component.task.AbstractTaskSchedule.checkAndExecute Line:198 - 集群主控节点主机地址【10.92.82.161】运行端口【10031】,当前主机地址【10.87.57.73】运行端口【8092】, 无法执行定时任务【com.simbest.boot.component.task.LicenseCheckTask】【640】
2025-05-27 03:30:56.006 [pool-14-thread-6] INFO  com.simbest.boot.component.task.AbstractTaskSchedule.checkAndExecute Line:198 - 集群主控节点主机地址【10.92.82.161】运行端口【10031】,当前主机地址【10.87.57.73】运行端口【8092】, 无法执行定时任务【com.simbest.boot.component.task.LicenseCheckTask】【642】
2025-05-27 03:30:57.013 [pool-14-thread-6] INFO  com.simbest.boot.component.task.AbstractTaskSchedule.checkAndExecute Line:198 - 集群主控节点主机地址【10.92.82.161】运行端口【10031】,当前主机地址【10.87.57.73】运行端口【8092】, 无法执行定时任务【com.simbest.boot.component.task.LicenseCheckTask】【642】
2025-05-27 03:30:58.008 [pool-14-thread-6] INFO  com.simbest.boot.component.task.AbstractTaskSchedule.checkAndExecute Line:198 - 集群主控节点主机地址【10.92.82.161】运行端口【10031】,当前主机地址【10.87.57.73】运行端口【8092】, 无法执行定时任务【com.simbest.boot.component.task.LicenseCheckTask】【642】
2025-05-27 03:30:59.016 [pool-14-thread-6] INFO  com.simbest.boot.component.task.AbstractTaskSchedule.checkAndExecute Line:198 - 集群主控节点主机地址【10.92.82.161】运行端口【10031】,当前主机地址【10.87.57.73】运行端口【8092】, 无法执行定时任务【com.simbest.boot.component.task.LicenseCheckTask】【642】
2025-05-27 03:31:00.007 [pool-14-thread-5] INFO  com.simbest.boot.component.task.AbstractTaskSchedule.checkAndExecute Line:198 - 集群主控节点主机地址【10.92.82.161】运行端口【10031】,当前主机地址【10.87.57.73】运行端口【8092】, 无法执行定时任务【com.simbest.boot.component.task.HeartTestTask】【641】
2025-05-27 03:32:00.005 [pool-14-thread-6] INFO  com.simbest.boot.component.task.AbstractTaskSchedule.checkAndExecute Line:198 - 集群主控节点主机地址【10.92.82.161】运行端口【10031】,当前主机地址【10.87.57.73】运行端口【8092】, 无法执行定时任务【com.simbest.boot.component.task.HeartTestTask】【642】
2025-05-27 03:33:00.006 [pool-14-thread-5] INFO  com.simbest.boot.component.task.AbstractTaskSchedule.checkAndExecute Line:198 - 集群主控节点主机地址【10.92.82.161】运行端口【10031】,当前主机地址【10.87.57.73】运行端口【8092】, 无法执行定时任务【com.simbest.boot.component.task.HeartTestTask】【641】
2025-05-27 03:34:00.001 [pool-14-thread-5] INFO  com.simbest.boot.component.task.AbstractTaskSchedule.checkAndExecute Line:198 - 集群主控节点主机地址【10.92.82.161】运行端口【10031】,当前主机地址【10.87.57.73】运行端口【8092】, 无法执行定时任务【com.simbest.boot.component.task.HeartTestTask】【641】
2025-05-27 03:35:00.005 [pool-14-thread-6] INFO  com.simbest.boot.component.task.AbstractTaskSchedule.checkAndExecute Line:198 - 集群主控节点主机地址【10.92.82.161】运行端口【10031】,当前主机地址【10.87.57.73】运行端口【8092】, 无法执行定时任务【com.simbest.boot.processdata.pushWorkmanager.task.SyncProcessDataToWorkManagerTask】【642】
2025-05-27 03:35:00.005 [pool-14-thread-4] INFO  com.simbest.boot.component.task.AbstractTaskSchedule.checkAndExecute Line:198 - 集群主控节点主机地址【10.92.82.161】运行端口【10031】,当前主机地址【10.87.57.73】运行端口【8092】, 无法执行定时任务【com.simbest.boot.bps.task.AutoCloseTodoTask】【640】
2025-05-27 03:35:00.005 [pool-14-thread-5] INFO  com.simbest.boot.component.task.AbstractTaskSchedule.checkAndExecute Line:198 - 集群主控节点主机地址【10.92.82.161】运行端口【10031】,当前主机地址【10.87.57.73】运行端口【8092】, 无法执行定时任务【com.simbest.boot.processdata.pushWorkmanager.task.SyncCommentDataToWorkManagerTask】【641】
2025-05-27 03:35:00.005 [pool-14-thread-7] INFO  com.simbest.boot.component.task.AbstractTaskSchedule.checkAndExecute Line:198 - 集群主控节点主机地址【10.92.82.161】运行端口【10031】,当前主机地址【10.87.57.73】运行端口【8092】, 无法执行定时任务【com.simbest.boot.component.task.HeartTestTask】【643】
2025-05-27 03:35:00.005 [pool-14-thread-1] INFO  com.simbest.boot.component.task.AbstractTaskSchedule.checkAndExecute Line:198 - 集群主控节点主机地址【10.92.82.161】运行端口【10031】,当前主机地址【10.87.57.73】运行端口【8092】, 无法执行定时任务【com.simbest.boot.bps.task.AutoCheckWithUpdateTask】【637】
2025-05-27 03:35:00.005 [pool-14-thread-10] INFO  com.simbest.boot.component.task.AbstractTaskSchedule.checkAndExecute Line:198 - 集群主控节点主机地址【10.92.82.161】运行端口【10031】,当前主机地址【10.87.57.73】运行端口【8092】, 无法执行定时任务【com.simbest.boot.bps.task.AutoAddTodoTask】【646】
2025-05-27 03:35:00.007 [pool-14-thread-9] INFO  com.simbest.boot.component.task.AbstractTaskSchedule.checkAndExecute Line:198 - 集群主控节点主机地址【10.92.82.161】运行端口【10031】,当前主机地址【10.87.57.73】运行端口【8092】, 无法执行定时任务【com.simbest.boot.processdata.pushWorkmanager.task.SyncTaskDataToWorkManagerTask】【645】
2025-05-27 03:36:00.024 [pool-14-thread-6] INFO  com.simbest.boot.component.task.AbstractTaskSchedule.checkAndExecute Line:198 - 集群主控节点主机地址【10.92.82.161】运行端口【10031】,当前主机地址【10.87.57.73】运行端口【8092】, 无法执行定时任务【com.simbest.boot.component.task.HeartTestTask】【642】
2025-05-27 03:37:00.012 [pool-14-thread-3] INFO  com.simbest.boot.component.task.AbstractTaskSchedule.checkAndExecute Line:198 - 集群主控节点主机地址【10.92.82.161】运行端口【10031】,当前主机地址【10.87.57.73】运行端口【8092】, 无法执行定时任务【com.simbest.boot.component.task.HeartTestTask】【639】
2025-05-27 03:38:00.003 [pool-14-thread-1] INFO  com.simbest.boot.component.task.AbstractTaskSchedule.checkAndExecute Line:198 - 集群主控节点主机地址【10.92.82.161】运行端口【10031】,当前主机地址【10.87.57.73】运行端口【8092】, 无法执行定时任务【com.simbest.boot.component.task.HeartTestTask】【637】
2025-05-27 03:39:00.007 [pool-14-thread-1] INFO  com.simbest.boot.component.task.AbstractTaskSchedule.checkAndExecute Line:198 - 集群主控节点主机地址【10.92.82.161】运行端口【10031】,当前主机地址【10.87.57.73】运行端口【8092】, 无法执行定时任务【com.simbest.boot.component.task.HeartTestTask】【637】
2025-05-27 03:40:00.013 [pool-14-thread-10] INFO  com.simbest.boot.component.task.AbstractTaskSchedule.checkAndExecute Line:198 - 集群主控节点主机地址【10.92.82.161】运行端口【10031】,当前主机地址【10.87.57.73】运行端口【8092】, 无法执行定时任务【com.simbest.boot.bps.task.AutoCheckWithUpdateTask】【646】
2025-05-27 03:40:00.013 [pool-14-thread-8] INFO  com.simbest.boot.component.task.AbstractTaskSchedule.checkAndExecute Line:198 - 集群主控节点主机地址【10.92.82.161】运行端口【10031】,当前主机地址【10.87.57.73】运行端口【8092】, 无法执行定时任务【com.simbest.boot.bps.task.AutoAddTodoTask】【644】
2025-05-27 03:40:00.015 [pool-14-thread-2] INFO  com.simbest.boot.component.task.AbstractTaskSchedule.checkAndExecute Line:198 - 集群主控节点主机地址【10.92.82.161】运行端口【10031】,当前主机地址【10.87.57.73】运行端口【8092】, 无法执行定时任务【com.simbest.boot.processdata.pushWorkmanager.task.SyncProcessDataToWorkManagerTask】【638】
2025-05-27 03:40:00.015 [pool-14-thread-9] INFO  com.simbest.boot.component.task.AbstractTaskSchedule.checkAndExecute Line:198 - 集群主控节点主机地址【10.92.82.161】运行端口【10031】,当前主机地址【10.87.57.73】运行端口【8092】, 无法执行定时任务【com.simbest.boot.bps.task.AutoCloseTodoTask】【645】
2025-05-27 03:40:00.015 [pool-14-thread-4] INFO  com.simbest.boot.component.task.AbstractTaskSchedule.checkAndExecute Line:198 - 集群主控节点主机地址【10.92.82.161】运行端口【10031】,当前主机地址【10.87.57.73】运行端口【8092】, 无法执行定时任务【com.simbest.boot.processdata.pushWorkmanager.task.SyncCommentDataToWorkManagerTask】【640】
2025-05-27 03:40:00.015 [pool-14-thread-5] INFO  com.simbest.boot.component.task.AbstractTaskSchedule.checkAndExecute Line:198 - 集群主控节点主机地址【10.92.82.161】运行端口【10031】,当前主机地址【10.87.57.73】运行端口【8092】, 无法执行定时任务【com.simbest.boot.processdata.pushWorkmanager.task.SyncTaskDataToWorkManagerTask】【641】
2025-05-27 03:40:00.016 [pool-14-thread-1] INFO  com.simbest.boot.component.task.AbstractTaskSchedule.checkAndExecute Line:198 - 集群主控节点主机地址【10.92.82.161】运行端口【10031】,当前主机地址【10.87.57.73】运行端口【8092】, 无法执行定时任务【com.simbest.boot.component.task.HeartTestTask】【637】
2025-05-27 03:41:00.013 [pool-14-thread-10] INFO  com.simbest.boot.component.task.AbstractTaskSchedule.checkAndExecute Line:198 - 集群主控节点主机地址【10.92.82.161】运行端口【10031】,当前主机地址【10.87.57.73】运行端口【8092】, 无法执行定时任务【com.simbest.boot.component.task.HeartTestTask】【646】
2025-05-27 03:42:00.006 [pool-14-thread-10] INFO  com.simbest.boot.component.task.AbstractTaskSchedule.checkAndExecute Line:198 - 集群主控节点主机地址【10.92.82.161】运行端口【10031】,当前主机地址【10.87.57.73】运行端口【8092】, 无法执行定时任务【com.simbest.boot.component.task.HeartTestTask】【646】
2025-05-27 03:43:00.013 [pool-14-thread-1] INFO  com.simbest.boot.component.task.AbstractTaskSchedule.checkAndExecute Line:198 - 集群主控节点主机地址【10.92.82.161】运行端口【10031】,当前主机地址【10.87.57.73】运行端口【8092】, 无法执行定时任务【com.simbest.boot.component.task.HeartTestTask】【637】
2025-05-27 03:44:00.013 [pool-14-thread-1] INFO  com.simbest.boot.component.task.AbstractTaskSchedule.checkAndExecute Line:198 - 集群主控节点主机地址【10.92.82.161】运行端口【10031】,当前主机地址【10.87.57.73】运行端口【8092】, 无法执行定时任务【com.simbest.boot.component.task.HeartTestTask】【637】
2025-05-27 03:45:00.011 [pool-14-thread-10] INFO  com.simbest.boot.component.task.AbstractTaskSchedule.checkAndExecute Line:198 - 集群主控节点主机地址【10.92.82.161】运行端口【10031】,当前主机地址【10.87.57.73】运行端口【8092】, 无法执行定时任务【com.simbest.boot.processdata.pushWorkmanager.task.SyncTaskDataToWorkManagerTask】【646】
2025-05-27 03:45:00.011 [pool-14-thread-6] INFO  com.simbest.boot.component.task.AbstractTaskSchedule.checkAndExecute Line:198 - 集群主控节点主机地址【10.92.82.161】运行端口【10031】,当前主机地址【10.87.57.73】运行端口【8092】, 无法执行定时任务【com.simbest.boot.processdata.pushWorkmanager.task.SyncProcessDataToWorkManagerTask】【642】
2025-05-27 03:45:00.011 [pool-14-thread-7] INFO  com.simbest.boot.component.task.AbstractTaskSchedule.checkAndExecute Line:198 - 集群主控节点主机地址【10.92.82.161】运行端口【10031】,当前主机地址【10.87.57.73】运行端口【8092】, 无法执行定时任务【com.simbest.boot.bps.task.AutoCloseTodoTask】【643】
2025-05-27 03:45:00.011 [pool-14-thread-3] INFO  com.simbest.boot.component.task.AbstractTaskSchedule.checkAndExecute Line:198 - 集群主控节点主机地址【10.92.82.161】运行端口【10031】,当前主机地址【10.87.57.73】运行端口【8092】, 无法执行定时任务【com.simbest.boot.bps.task.AutoCheckWithUpdateTask】【639】
2025-05-27 03:45:00.011 [pool-14-thread-1] INFO  com.simbest.boot.component.task.AbstractTaskSchedule.checkAndExecute Line:198 - 集群主控节点主机地址【10.92.82.161】运行端口【10031】,当前主机地址【10.87.57.73】运行端口【8092】, 无法执行定时任务【com.simbest.boot.component.task.HeartTestTask】【637】
2025-05-27 03:45:00.011 [pool-14-thread-2] INFO  com.simbest.boot.component.task.AbstractTaskSchedule.checkAndExecute Line:198 - 集群主控节点主机地址【10.92.82.161】运行端口【10031】,当前主机地址【10.87.57.73】运行端口【8092】, 无法执行定时任务【com.simbest.boot.processdata.pushWorkmanager.task.SyncCommentDataToWorkManagerTask】【638】
2025-05-27 03:45:00.011 [pool-14-thread-4] INFO  com.simbest.boot.component.task.AbstractTaskSchedule.checkAndExecute Line:198 - 集群主控节点主机地址【10.92.82.161】运行端口【10031】,当前主机地址【10.87.57.73】运行端口【8092】, 无法执行定时任务【com.simbest.boot.bps.task.AutoAddTodoTask】【640】
2025-05-27 03:46:00.006 [pool-14-thread-5] INFO  com.simbest.boot.component.task.AbstractTaskSchedule.checkAndExecute Line:198 - 集群主控节点主机地址【10.92.82.161】运行端口【10031】,当前主机地址【10.87.57.73】运行端口【8092】, 无法执行定时任务【com.simbest.boot.component.task.HeartTestTask】【641】
2025-05-27 03:47:00.013 [pool-14-thread-5] INFO  com.simbest.boot.component.task.AbstractTaskSchedule.checkAndExecute Line:198 - 集群主控节点主机地址【10.92.82.161】运行端口【10031】,当前主机地址【10.87.57.73】运行端口【8092】, 无法执行定时任务【com.simbest.boot.component.task.HeartTestTask】【641】
2025-05-27 03:48:00.009 [pool-14-thread-5] INFO  com.simbest.boot.component.task.AbstractTaskSchedule.checkAndExecute Line:198 - 集群主控节点主机地址【10.92.82.161】运行端口【10031】,当前主机地址【10.87.57.73】运行端口【8092】, 无法执行定时任务【com.simbest.boot.component.task.HeartTestTask】【641】
2025-05-27 03:49:00.006 [pool-14-thread-2] INFO  com.simbest.boot.component.task.AbstractTaskSchedule.checkAndExecute Line:198 - 集群主控节点主机地址【10.92.82.161】运行端口【10031】,当前主机地址【10.87.57.73】运行端口【8092】, 无法执行定时任务【com.simbest.boot.component.task.HeartTestTask】【638】
2025-05-27 04:10:10.800 [pool-14-thread-10] INFO  com.simbest.boot.component.task.AbstractTaskSchedule.checkAndExecute Line:198 - 集群主控节点主机地址【10.92.82.161】运行端口【10031】,当前主机地址【10.87.57.73】运行端口【8092】, 无法执行定时任务【com.simbest.boot.bps.task.AutoAddTodoTask】【646】
2025-05-27 04:10:10.800 [pool-14-thread-1] INFO  com.simbest.boot.component.task.AbstractTaskSchedule.checkAndExecute Line:198 - 集群主控节点主机地址【10.92.82.161】运行端口【10031】,当前主机地址【10.87.57.73】运行端口【8092】, 无法执行定时任务【com.simbest.boot.bps.task.AutoCheckWithUpdateTask】【637】
2025-05-27 04:10:10.800 [pool-14-thread-6] INFO  com.simbest.boot.component.task.AbstractTaskSchedule.checkAndExecute Line:198 - 集群主控节点主机地址【10.92.82.161】运行端口【10031】,当前主机地址【10.87.57.73】运行端口【8092】, 无法执行定时任务【com.simbest.boot.processdata.pushWorkmanager.task.SyncProcessDataToWorkManagerTask】【642】
2025-05-27 04:10:10.800 [pool-14-thread-2] INFO  com.simbest.boot.component.task.AbstractTaskSchedule.checkAndExecute Line:198 - 集群主控节点主机地址【10.92.82.161】运行端口【10031】,当前主机地址【10.87.57.73】运行端口【8092】, 无法执行定时任务【com.simbest.boot.processdata.pushWorkmanager.task.SyncTaskDataToWorkManagerTask】【638】
2025-05-27 04:10:10.800 [pool-14-thread-5] INFO  com.simbest.boot.component.task.AbstractTaskSchedule.checkAndExecute Line:198 - 集群主控节点主机地址【10.92.82.161】运行端口【10031】,当前主机地址【10.87.57.73】运行端口【8092】, 无法执行定时任务【com.simbest.boot.component.task.LicenseCheckTask】【641】
2025-05-27 04:10:10.800 [pool-14-thread-7] INFO  com.simbest.boot.component.task.AbstractTaskSchedule.checkAndExecute Line:198 - 集群主控节点主机地址【10.92.82.161】运行端口【10031】,当前主机地址【10.87.57.73】运行端口【8092】, 无法执行定时任务【com.simbest.boot.component.task.HeartTestTask】【643】
2025-05-27 04:10:10.800 [pool-14-thread-8] INFO  com.simbest.boot.component.task.AbstractTaskSchedule.checkAndExecute Line:198 - 集群主控节点主机地址【10.92.82.161】运行端口【10031】,当前主机地址【10.87.57.73】运行端口【8092】, 无法执行定时任务【com.simbest.boot.processdata.pushWorkmanager.task.SyncCommentDataToWorkManagerTask】【644】
2025-05-27 04:10:10.800 [pool-14-thread-10] INFO  com.simbest.boot.component.task.AbstractTaskSchedule.checkAndExecute Line:198 - 集群主控节点主机地址【10.92.82.161】运行端口【10031】,当前主机地址【10.87.57.73】运行端口【8092】, 无法执行定时任务【com.simbest.boot.bps.task.AutoCloseTodoTask】【646】
2025-05-27 04:10:10.800 [pool-14-thread-1] INFO  com.simbest.boot.component.task.AbstractTaskSchedule.checkAndExecute Line:198 - 集群主控节点主机地址【10.92.82.161】运行端口【10031】,当前主机地址【10.87.57.73】运行端口【8092】, 无法执行定时任务【com.simbest.boot.cmcc.a4.task.A4TaskCreateXmlFile】【637】
2025-05-27 04:11:00.013 [pool-14-thread-7] INFO  com.simbest.boot.component.task.AbstractTaskSchedule.checkAndExecute Line:198 - 集群主控节点主机地址【10.92.82.161】运行端口【10031】,当前主机地址【10.87.57.73】运行端口【8092】, 无法执行定时任务【com.simbest.boot.component.task.HeartTestTask】【643】
2025-05-27 04:12:00.011 [pool-14-thread-7] INFO  com.simbest.boot.component.task.AbstractTaskSchedule.checkAndExecute Line:198 - 集群主控节点主机地址【10.92.82.161】运行端口【10031】,当前主机地址【10.87.57.73】运行端口【8092】, 无法执行定时任务【com.simbest.boot.component.task.HeartTestTask】【643】
2025-05-27 04:13:00.004 [pool-14-thread-7] INFO  com.simbest.boot.component.task.AbstractTaskSchedule.checkAndExecute Line:198 - 集群主控节点主机地址【10.92.82.161】运行端口【10031】,当前主机地址【10.87.57.73】运行端口【8092】, 无法执行定时任务【com.simbest.boot.component.task.HeartTestTask】【643】
2025-05-27 04:14:00.003 [pool-14-thread-9] INFO  com.simbest.boot.component.task.AbstractTaskSchedule.checkAndExecute Line:198 - 集群主控节点主机地址【10.92.82.161】运行端口【10031】,当前主机地址【10.87.57.73】运行端口【8092】, 无法执行定时任务【com.simbest.boot.component.task.HeartTestTask】【645】
2025-05-27 04:15:00.007 [pool-14-thread-9] INFO  com.simbest.boot.component.task.AbstractTaskSchedule.checkAndExecute Line:198 - 集群主控节点主机地址【10.92.82.161】运行端口【10031】,当前主机地址【10.87.57.73】运行端口【8092】, 无法执行定时任务【com.simbest.boot.bps.task.AutoCheckWithUpdateTask】【645】
2025-05-27 04:15:00.007 [pool-14-thread-4] INFO  com.simbest.boot.component.task.AbstractTaskSchedule.checkAndExecute Line:198 - 集群主控节点主机地址【10.92.82.161】运行端口【10031】,当前主机地址【10.87.57.73】运行端口【8092】, 无法执行定时任务【com.simbest.boot.processdata.pushWorkmanager.task.SyncTaskDataToWorkManagerTask】【640】
2025-05-27 04:15:00.007 [pool-14-thread-6] INFO  com.simbest.boot.component.task.AbstractTaskSchedule.checkAndExecute Line:198 - 集群主控节点主机地址【10.92.82.161】运行端口【10031】,当前主机地址【10.87.57.73】运行端口【8092】, 无法执行定时任务【com.simbest.boot.component.task.HeartTestTask】【642】
2025-05-27 04:15:00.007 [pool-14-thread-2] INFO  com.simbest.boot.component.task.AbstractTaskSchedule.checkAndExecute Line:198 - 集群主控节点主机地址【10.92.82.161】运行端口【10031】,当前主机地址【10.87.57.73】运行端口【8092】, 无法执行定时任务【com.simbest.boot.bps.task.AutoCloseTodoTask】【638】
2025-05-27 04:15:00.007 [pool-14-thread-3] INFO  com.simbest.boot.component.task.AbstractTaskSchedule.checkAndExecute Line:198 - 集群主控节点主机地址【10.92.82.161】运行端口【10031】,当前主机地址【10.87.57.73】运行端口【8092】, 无法执行定时任务【com.simbest.boot.processdata.pushWorkmanager.task.SyncProcessDataToWorkManagerTask】【639】
2025-05-27 04:15:00.007 [pool-14-thread-10] INFO  com.simbest.boot.component.task.AbstractTaskSchedule.checkAndExecute Line:198 - 集群主控节点主机地址【10.92.82.161】运行端口【10031】,当前主机地址【10.87.57.73】运行端口【8092】, 无法执行定时任务【com.simbest.boot.processdata.pushWorkmanager.task.SyncCommentDataToWorkManagerTask】【646】
2025-05-27 04:15:00.007 [pool-14-thread-8] INFO  com.simbest.boot.component.task.AbstractTaskSchedule.checkAndExecute Line:198 - 集群主控节点主机地址【10.92.82.161】运行端口【10031】,当前主机地址【10.87.57.73】运行端口【8092】, 无法执行定时任务【com.simbest.boot.bps.task.AutoAddTodoTask】【644】
2025-05-27 04:16:00.014 [pool-14-thread-2] INFO  com.simbest.boot.component.task.AbstractTaskSchedule.checkAndExecute Line:198 - 集群主控节点主机地址【10.92.82.161】运行端口【10031】,当前主机地址【10.87.57.73】运行端口【8092】, 无法执行定时任务【com.simbest.boot.component.task.HeartTestTask】【638】
2025-05-27 04:17:00.011 [pool-14-thread-2] INFO  com.simbest.boot.component.task.AbstractTaskSchedule.checkAndExecute Line:198 - 集群主控节点主机地址【10.92.82.161】运行端口【10031】,当前主机地址【10.87.57.73】运行端口【8092】, 无法执行定时任务【com.simbest.boot.component.task.HeartTestTask】【638】
2025-05-27 04:18:00.009 [pool-14-thread-3] INFO  com.simbest.boot.component.task.AbstractTaskSchedule.checkAndExecute Line:198 - 集群主控节点主机地址【10.92.82.161】运行端口【10031】,当前主机地址【10.87.57.73】运行端口【8092】, 无法执行定时任务【com.simbest.boot.component.task.HeartTestTask】【639】
2025-05-27 04:19:00.007 [pool-14-thread-4] INFO  com.simbest.boot.component.task.AbstractTaskSchedule.checkAndExecute Line:198 - 集群主控节点主机地址【10.92.82.161】运行端口【10031】,当前主机地址【10.87.57.73】运行端口【8092】, 无法执行定时任务【com.simbest.boot.component.task.HeartTestTask】【640】
2025-05-27 04:20:00.003 [pool-14-thread-6] INFO  com.simbest.boot.component.task.AbstractTaskSchedule.checkAndExecute Line:198 - 集群主控节点主机地址【10.92.82.161】运行端口【10031】,当前主机地址【10.87.57.73】运行端口【8092】, 无法执行定时任务【com.simbest.boot.bps.task.AutoCheckWithUpdateTask】【642】
2025-05-27 04:20:00.003 [pool-14-thread-4] INFO  com.simbest.boot.component.task.AbstractTaskSchedule.checkAndExecute Line:198 - 集群主控节点主机地址【10.92.82.161】运行端口【10031】,当前主机地址【10.87.57.73】运行端口【8092】, 无法执行定时任务【com.simbest.boot.component.task.HeartTestTask】【640】
2025-05-27 04:20:00.004 [pool-14-thread-5] INFO  com.simbest.boot.component.task.AbstractTaskSchedule.checkAndExecute Line:198 - 集群主控节点主机地址【10.92.82.161】运行端口【10031】,当前主机地址【10.87.57.73】运行端口【8092】, 无法执行定时任务【com.simbest.boot.bps.task.AutoCloseTodoTask】【641】
2025-05-27 04:20:00.004 [pool-14-thread-1] INFO  com.simbest.boot.component.task.AbstractTaskSchedule.checkAndExecute Line:198 - 集群主控节点主机地址【10.92.82.161】运行端口【10031】,当前主机地址【10.87.57.73】运行端口【8092】, 无法执行定时任务【com.simbest.boot.processdata.pushWorkmanager.task.SyncCommentDataToWorkManagerTask】【637】
2025-05-27 04:20:00.003 [pool-14-thread-2] INFO  com.simbest.boot.component.task.AbstractTaskSchedule.checkAndExecute Line:198 - 集群主控节点主机地址【10.92.82.161】运行端口【10031】,当前主机地址【10.87.57.73】运行端口【8092】, 无法执行定时任务【com.simbest.boot.processdata.pushWorkmanager.task.SyncTaskDataToWorkManagerTask】【638】
2025-05-27 04:20:00.004 [pool-14-thread-9] INFO  com.simbest.boot.component.task.AbstractTaskSchedule.checkAndExecute Line:198 - 集群主控节点主机地址【10.92.82.161】运行端口【10031】,当前主机地址【10.87.57.73】运行端口【8092】, 无法执行定时任务【com.simbest.boot.processdata.pushWorkmanager.task.SyncProcessDataToWorkManagerTask】【645】
2025-05-27 04:20:00.004 [pool-14-thread-6] INFO  com.simbest.boot.component.task.AbstractTaskSchedule.checkAndExecute Line:198 - 集群主控节点主机地址【10.92.82.161】运行端口【10031】,当前主机地址【10.87.57.73】运行端口【8092】, 无法执行定时任务【com.simbest.boot.bps.task.AutoAddTodoTask】【642】
2025-05-27 04:21:00.012 [pool-14-thread-5] INFO  com.simbest.boot.component.task.AbstractTaskSchedule.checkAndExecute Line:198 - 集群主控节点主机地址【10.92.82.161】运行端口【10031】,当前主机地址【10.87.57.73】运行端口【8092】, 无法执行定时任务【com.simbest.boot.component.task.HeartTestTask】【641】
2025-05-27 04:22:00.013 [pool-14-thread-4] INFO  com.simbest.boot.component.task.AbstractTaskSchedule.checkAndExecute Line:198 - 集群主控节点主机地址【10.92.82.161】运行端口【10031】,当前主机地址【10.87.57.73】运行端口【8092】, 无法执行定时任务【com.simbest.boot.component.task.HeartTestTask】【640】
2025-05-27 04:23:00.009 [pool-14-thread-9] INFO  com.simbest.boot.component.task.AbstractTaskSchedule.checkAndExecute Line:198 - 集群主控节点主机地址【10.92.82.161】运行端口【10031】,当前主机地址【10.87.57.73】运行端口【8092】, 无法执行定时任务【com.simbest.boot.component.task.HeartTestTask】【645】
2025-05-27 04:24:00.012 [pool-14-thread-4] INFO  com.simbest.boot.component.task.AbstractTaskSchedule.checkAndExecute Line:198 - 集群主控节点主机地址【10.92.82.161】运行端口【10031】,当前主机地址【10.87.57.73】运行端口【8092】, 无法执行定时任务【com.simbest.boot.component.task.HeartTestTask】【640】
2025-05-27 04:25:00.006 [pool-14-thread-6] INFO  com.simbest.boot.component.task.AbstractTaskSchedule.checkAndExecute Line:198 - 集群主控节点主机地址【10.92.82.161】运行端口【10031】,当前主机地址【10.87.57.73】运行端口【8092】, 无法执行定时任务【com.simbest.boot.bps.task.AutoCheckWithUpdateTask】【642】
2025-05-27 04:25:00.007 [pool-14-thread-7] INFO  com.simbest.boot.component.task.AbstractTaskSchedule.checkAndExecute Line:198 - 集群主控节点主机地址【10.92.82.161】运行端口【10031】,当前主机地址【10.87.57.73】运行端口【8092】, 无法执行定时任务【com.simbest.boot.processdata.pushWorkmanager.task.SyncTaskDataToWorkManagerTask】【643】
2025-05-27 04:25:00.006 [pool-14-thread-3] INFO  com.simbest.boot.component.task.AbstractTaskSchedule.checkAndExecute Line:198 - 集群主控节点主机地址【10.92.82.161】运行端口【10031】,当前主机地址【10.87.57.73】运行端口【8092】, 无法执行定时任务【com.simbest.boot.processdata.pushWorkmanager.task.SyncCommentDataToWorkManagerTask】【639】
2025-05-27 04:25:00.006 [pool-14-thread-8] INFO  com.simbest.boot.component.task.AbstractTaskSchedule.checkAndExecute Line:198 - 集群主控节点主机地址【10.92.82.161】运行端口【10031】,当前主机地址【10.87.57.73】运行端口【8092】, 无法执行定时任务【com.simbest.boot.bps.task.AutoCloseTodoTask】【644】
2025-05-27 04:25:00.006 [pool-14-thread-4] INFO  com.simbest.boot.component.task.AbstractTaskSchedule.checkAndExecute Line:198 - 集群主控节点主机地址【10.92.82.161】运行端口【10031】,当前主机地址【10.87.57.73】运行端口【8092】, 无法执行定时任务【com.simbest.boot.component.task.HeartTestTask】【640】
2025-05-27 04:25:00.007 [pool-14-thread-5] INFO  com.simbest.boot.component.task.AbstractTaskSchedule.checkAndExecute Line:198 - 集群主控节点主机地址【10.92.82.161】运行端口【10031】,当前主机地址【10.87.57.73】运行端口【8092】, 无法执行定时任务【com.simbest.boot.bps.task.AutoAddTodoTask】【641】
2025-05-27 04:25:00.006 [pool-14-thread-9] INFO  com.simbest.boot.component.task.AbstractTaskSchedule.checkAndExecute Line:198 - 集群主控节点主机地址【10.92.82.161】运行端口【10031】,当前主机地址【10.87.57.73】运行端口【8092】, 无法执行定时任务【com.simbest.boot.processdata.pushWorkmanager.task.SyncProcessDataToWorkManagerTask】【645】
2025-05-27 04:26:00.005 [pool-14-thread-8] INFO  com.simbest.boot.component.task.AbstractTaskSchedule.checkAndExecute Line:198 - 集群主控节点主机地址【10.92.82.161】运行端口【10031】,当前主机地址【10.87.57.73】运行端口【8092】, 无法执行定时任务【com.simbest.boot.component.task.HeartTestTask】【644】
2025-05-27 04:27:00.009 [pool-14-thread-7] INFO  com.simbest.boot.component.task.AbstractTaskSchedule.checkAndExecute Line:198 - 集群主控节点主机地址【10.92.82.161】运行端口【10031】,当前主机地址【10.87.57.73】运行端口【8092】, 无法执行定时任务【com.simbest.boot.component.task.HeartTestTask】【643】
2025-05-27 04:28:00.007 [pool-14-thread-7] INFO  com.simbest.boot.component.task.AbstractTaskSchedule.checkAndExecute Line:198 - 集群主控节点主机地址【10.92.82.161】运行端口【10031】,当前主机地址【10.87.57.73】运行端口【8092】, 无法执行定时任务【com.simbest.boot.component.task.HeartTestTask】【643】
2025-05-27 04:29:00.010 [pool-14-thread-2] INFO  com.simbest.boot.component.task.AbstractTaskSchedule.checkAndExecute Line:198 - 集群主控节点主机地址【10.92.82.161】运行端口【10031】,当前主机地址【10.87.57.73】运行端口【8092】, 无法执行定时任务【com.simbest.boot.component.task.HeartTestTask】【638】
2025-05-27 04:30:00.013 [pool-14-thread-9] INFO  com.simbest.boot.component.task.AbstractTaskSchedule.checkAndExecute Line:198 - 集群主控节点主机地址【10.92.82.161】运行端口【10031】,当前主机地址【10.87.57.73】运行端口【8092】, 无法执行定时任务【com.simbest.boot.component.task.HeartTestTask】【645】
2025-05-27 04:30:00.014 [pool-14-thread-10] INFO  com.simbest.boot.component.task.AbstractTaskSchedule.checkAndExecute Line:198 - 集群主控节点主机地址【10.92.82.161】运行端口【10031】,当前主机地址【10.87.57.73】运行端口【8092】, 无法执行定时任务【com.simbest.boot.bps.task.AutoAddTodoTask】【646】
2025-05-27 04:30:00.013 [pool-14-thread-8] INFO  com.simbest.boot.component.task.AbstractTaskSchedule.checkAndExecute Line:198 - 集群主控节点主机地址【10.92.82.161】运行端口【10031】,当前主机地址【10.87.57.73】运行端口【8092】, 无法执行定时任务【com.simbest.boot.component.task.LicenseCheckTask】【644】
2025-05-27 04:30:00.013 [pool-14-thread-5] INFO  com.simbest.boot.component.task.AbstractTaskSchedule.checkAndExecute Line:198 - 集群主控节点主机地址【10.92.82.161】运行端口【10031】,当前主机地址【10.87.57.73】运行端口【8092】, 无法执行定时任务【com.simbest.boot.processdata.pushWorkmanager.task.SyncCommentDataToWorkManagerTask】【641】
2025-05-27 04:30:00.014 [pool-14-thread-4] INFO  com.simbest.boot.component.task.AbstractTaskSchedule.checkAndExecute Line:198 - 集群主控节点主机地址【10.92.82.161】运行端口【10031】,当前主机地址【10.87.57.73】运行端口【8092】, 无法执行定时任务【com.simbest.boot.processdata.pushWorkmanager.task.SyncProcessDataToWorkManagerTask】【640】
2025-05-27 04:30:00.014 [pool-14-thread-7] INFO  com.simbest.boot.component.task.AbstractTaskSchedule.checkAndExecute Line:198 - 集群主控节点主机地址【10.92.82.161】运行端口【10031】,当前主机地址【10.87.57.73】运行端口【8092】, 无法执行定时任务【com.simbest.boot.bps.task.AutoCheckWithUpdateTask】【643】
2025-05-27 04:30:00.013 [pool-14-thread-6] INFO  com.simbest.boot.component.task.AbstractTaskSchedule.checkAndExecute Line:198 - 集群主控节点主机地址【10.92.82.161】运行端口【10031】,当前主机地址【10.87.57.73】运行端口【8092】, 无法执行定时任务【com.simbest.boot.processdata.pushWorkmanager.task.SyncTaskDataToWorkManagerTask】【642】
2025-05-27 04:30:00.013 [pool-14-thread-1] INFO  com.simbest.boot.component.task.AbstractTaskSchedule.checkAndExecute Line:198 - 集群主控节点主机地址【10.92.82.161】运行端口【10031】,当前主机地址【10.87.57.73】运行端口【8092】, 无法执行定时任务【com.simbest.boot.bps.task.AutoCloseTodoTask】【637】
2025-05-27 04:30:01.016 [pool-14-thread-8] INFO  com.simbest.boot.component.task.AbstractTaskSchedule.checkAndExecute Line:198 - 集群主控节点主机地址【10.92.82.161】运行端口【10031】,当前主机地址【10.87.57.73】运行端口【8092】, 无法执行定时任务【com.simbest.boot.component.task.LicenseCheckTask】【644】
2025-05-27 04:30:02.009 [pool-14-thread-8] INFO  com.simbest.boot.component.task.AbstractTaskSchedule.checkAndExecute Line:198 - 集群主控节点主机地址【10.92.82.161】运行端口【10031】,当前主机地址【10.87.57.73】运行端口【8092】, 无法执行定时任务【com.simbest.boot.component.task.LicenseCheckTask】【644】
2025-05-27 04:30:03.006 [pool-14-thread-8] INFO  com.simbest.boot.component.task.AbstractTaskSchedule.checkAndExecute Line:198 - 集群主控节点主机地址【10.92.82.161】运行端口【10031】,当前主机地址【10.87.57.73】运行端口【8092】, 无法执行定时任务【com.simbest.boot.component.task.LicenseCheckTask】【644】
2025-05-27 04:30:04.016 [pool-14-thread-8] INFO  com.simbest.boot.component.task.AbstractTaskSchedule.checkAndExecute Line:198 - 集群主控节点主机地址【10.92.82.161】运行端口【10031】,当前主机地址【10.87.57.73】运行端口【8092】, 无法执行定时任务【com.simbest.boot.component.task.LicenseCheckTask】【644】
2025-05-27 04:30:05.007 [pool-14-thread-8] INFO  com.simbest.boot.component.task.AbstractTaskSchedule.checkAndExecute Line:198 - 集群主控节点主机地址【10.92.82.161】运行端口【10031】,当前主机地址【10.87.57.73】运行端口【8092】, 无法执行定时任务【com.simbest.boot.component.task.LicenseCheckTask】【644】
2025-05-27 04:30:06.014 [pool-14-thread-8] INFO  com.simbest.boot.component.task.AbstractTaskSchedule.checkAndExecute Line:198 - 集群主控节点主机地址【10.92.82.161】运行端口【10031】,当前主机地址【10.87.57.73】运行端口【8092】, 无法执行定时任务【com.simbest.boot.component.task.LicenseCheckTask】【644】
2025-05-27 04:30:07.002 [pool-14-thread-4] INFO  com.simbest.boot.component.task.AbstractTaskSchedule.checkAndExecute Line:198 - 集群主控节点主机地址【10.92.82.161】运行端口【10031】,当前主机地址【10.87.57.73】运行端口【8092】, 无法执行定时任务【com.simbest.boot.component.task.LicenseCheckTask】【640】
2025-05-27 04:30:08.010 [pool-14-thread-6] INFO  com.simbest.boot.component.task.AbstractTaskSchedule.checkAndExecute Line:198 - 集群主控节点主机地址【10.92.82.161】运行端口【10031】,当前主机地址【10.87.57.73】运行端口【8092】, 无法执行定时任务【com.simbest.boot.component.task.LicenseCheckTask】【642】
2025-05-27 04:30:09.001 [pool-14-thread-6] INFO  com.simbest.boot.component.task.AbstractTaskSchedule.checkAndExecute Line:198 - 集群主控节点主机地址【10.92.82.161】运行端口【10031】,当前主机地址【10.87.57.73】运行端口【8092】, 无法执行定时任务【com.simbest.boot.component.task.LicenseCheckTask】【642】
2025-05-27 04:30:10.005 [pool-14-thread-2] INFO  com.simbest.boot.component.task.AbstractTaskSchedule.checkAndExecute Line:198 - 集群主控节点主机地址【10.92.82.161】运行端口【10031】,当前主机地址【10.87.57.73】运行端口【8092】, 无法执行定时任务【com.simbest.boot.component.task.LicenseCheckTask】【638】
2025-05-27 04:30:11.001 [pool-14-thread-2] INFO  com.simbest.boot.component.task.AbstractTaskSchedule.checkAndExecute Line:198 - 集群主控节点主机地址【10.92.82.161】运行端口【10031】,当前主机地址【10.87.57.73】运行端口【8092】, 无法执行定时任务【com.simbest.boot.component.task.LicenseCheckTask】【638】
2025-05-27 04:30:12.007 [pool-14-thread-2] INFO  com.simbest.boot.component.task.AbstractTaskSchedule.checkAndExecute Line:198 - 集群主控节点主机地址【10.92.82.161】运行端口【10031】,当前主机地址【10.87.57.73】运行端口【8092】, 无法执行定时任务【com.simbest.boot.component.task.LicenseCheckTask】【638】
2025-05-27 04:30:13.012 [pool-14-thread-2] INFO  com.simbest.boot.component.task.AbstractTaskSchedule.checkAndExecute Line:198 - 集群主控节点主机地址【10.92.82.161】运行端口【10031】,当前主机地址【10.87.57.73】运行端口【8092】, 无法执行定时任务【com.simbest.boot.component.task.LicenseCheckTask】【638】
2025-05-27 04:30:14.003 [pool-14-thread-2] INFO  com.simbest.boot.component.task.AbstractTaskSchedule.checkAndExecute Line:198 - 集群主控节点主机地址【10.92.82.161】运行端口【10031】,当前主机地址【10.87.57.73】运行端口【8092】, 无法执行定时任务【com.simbest.boot.component.task.LicenseCheckTask】【638】
2025-05-27 04:30:15.012 [pool-14-thread-2] INFO  com.simbest.boot.component.task.AbstractTaskSchedule.checkAndExecute Line:198 - 集群主控节点主机地址【10.92.82.161】运行端口【10031】,当前主机地址【10.87.57.73】运行端口【8092】, 无法执行定时任务【com.simbest.boot.component.task.LicenseCheckTask】【638】
2025-05-27 04:30:16.002 [pool-14-thread-2] INFO  com.simbest.boot.component.task.AbstractTaskSchedule.checkAndExecute Line:198 - 集群主控节点主机地址【10.92.82.161】运行端口【10031】,当前主机地址【10.87.57.73】运行端口【8092】, 无法执行定时任务【com.simbest.boot.component.task.LicenseCheckTask】【638】
2025-05-27 04:30:17.011 [pool-14-thread-2] INFO  com.simbest.boot.component.task.AbstractTaskSchedule.checkAndExecute Line:198 - 集群主控节点主机地址【10.92.82.161】运行端口【10031】,当前主机地址【10.87.57.73】运行端口【8092】, 无法执行定时任务【com.simbest.boot.component.task.LicenseCheckTask】【638】
2025-05-27 04:30:18.010 [pool-14-thread-2] INFO  com.simbest.boot.component.task.AbstractTaskSchedule.checkAndExecute Line:198 - 集群主控节点主机地址【10.92.82.161】运行端口【10031】,当前主机地址【10.87.57.73】运行端口【8092】, 无法执行定时任务【com.simbest.boot.component.task.LicenseCheckTask】【638】
2025-05-27 04:50:31.217 [pool-14-thread-2] INFO  com.simbest.boot.component.task.AbstractTaskSchedule.checkAndExecute Line:198 - 集群主控节点主机地址【10.92.82.161】运行端口【10031】,当前主机地址【10.87.57.73】运行端口【8092】, 无法执行定时任务【com.simbest.boot.component.task.LicenseCheckTask】【638】
2025-05-27 04:50:31.223 [pool-14-thread-8] INFO  com.simbest.boot.component.task.AbstractTaskSchedule.checkAndExecute Line:198 - 集群主控节点主机地址【10.92.82.161】运行端口【10031】,当前主机地址【10.87.57.73】运行端口【8092】, 无法执行定时任务【com.simbest.boot.component.task.HeartTestTask】【644】
2025-05-27 04:50:31.223 [pool-14-thread-1] INFO  com.simbest.boot.component.task.AbstractTaskSchedule.checkAndExecute Line:198 - 集群主控节点主机地址【10.92.82.161】运行端口【10031】,当前主机地址【10.87.57.73】运行端口【8092】, 无法执行定时任务【com.simbest.boot.processdata.pushWorkmanager.task.SyncCommentDataToWorkManagerTask】【637】
2025-05-27 04:50:31.224 [pool-14-thread-7] INFO  com.simbest.boot.component.task.AbstractTaskSchedule.checkAndExecute Line:198 - 集群主控节点主机地址【10.92.82.161】运行端口【10031】,当前主机地址【10.87.57.73】运行端口【8092】, 无法执行定时任务【com.simbest.boot.processdata.pushWorkmanager.task.SyncProcessDataToWorkManagerTask】【643】
2025-05-27 04:50:31.226 [pool-14-thread-3] INFO  com.simbest.boot.component.task.AbstractTaskSchedule.checkAndExecute Line:198 - 集群主控节点主机地址【10.92.82.161】运行端口【10031】,当前主机地址【10.87.57.73】运行端口【8092】, 无法执行定时任务【com.simbest.boot.bps.task.AutoCloseTodoTask】【639】
2025-05-27 04:50:31.224 [pool-14-thread-4] INFO  com.simbest.boot.component.task.AbstractTaskSchedule.checkAndExecute Line:198 - 集群主控节点主机地址【10.92.82.161】运行端口【10031】,当前主机地址【10.87.57.73】运行端口【8092】, 无法执行定时任务【com.simbest.boot.bps.task.AutoAddTodoTask】【640】
2025-05-27 04:50:31.225 [pool-14-thread-9] INFO  com.simbest.boot.component.task.AbstractTaskSchedule.checkAndExecute Line:198 - 集群主控节点主机地址【10.92.82.161】运行端口【10031】,当前主机地址【10.87.57.73】运行端口【8092】, 无法执行定时任务【com.simbest.boot.processdata.pushWorkmanager.task.SyncTaskDataToWorkManagerTask】【645】
2025-05-27 04:50:31.226 [pool-14-thread-6] INFO  com.simbest.boot.component.task.AbstractTaskSchedule.checkAndExecute Line:198 - 集群主控节点主机地址【10.92.82.161】运行端口【10031】,当前主机地址【10.87.57.73】运行端口【8092】, 无法执行定时任务【com.simbest.boot.bps.task.AutoCheckWithUpdateTask】【642】
2025-05-27 04:51:00.001 [pool-14-thread-8] INFO  com.simbest.boot.component.task.AbstractTaskSchedule.checkAndExecute Line:198 - 集群主控节点主机地址【10.92.82.161】运行端口【10031】,当前主机地址【10.87.57.73】运行端口【8092】, 无法执行定时任务【com.simbest.boot.component.task.HeartTestTask】【644】
2025-05-27 04:52:00.015 [pool-14-thread-7] INFO  com.simbest.boot.component.task.AbstractTaskSchedule.checkAndExecute Line:198 - 集群主控节点主机地址【10.92.82.161】运行端口【10031】,当前主机地址【10.87.57.73】运行端口【8092】, 无法执行定时任务【com.simbest.boot.component.task.HeartTestTask】【643】
2025-05-27 04:53:00.008 [pool-14-thread-4] INFO  com.simbest.boot.component.task.AbstractTaskSchedule.checkAndExecute Line:198 - 集群主控节点主机地址【10.92.82.161】运行端口【10031】,当前主机地址【10.87.57.73】运行端口【8092】, 无法执行定时任务【com.simbest.boot.component.task.HeartTestTask】【640】
2025-05-27 04:54:00.009 [pool-14-thread-5] INFO  com.simbest.boot.component.task.AbstractTaskSchedule.checkAndExecute Line:198 - 集群主控节点主机地址【10.92.82.161】运行端口【10031】,当前主机地址【10.87.57.73】运行端口【8092】, 无法执行定时任务【com.simbest.boot.component.task.HeartTestTask】【641】
2025-05-27 04:55:00.012 [pool-14-thread-8] INFO  com.simbest.boot.component.task.AbstractTaskSchedule.checkAndExecute Line:198 - 集群主控节点主机地址【10.92.82.161】运行端口【10031】,当前主机地址【10.87.57.73】运行端口【8092】, 无法执行定时任务【com.simbest.boot.bps.task.AutoCheckWithUpdateTask】【644】
2025-05-27 04:55:00.012 [pool-14-thread-9] INFO  com.simbest.boot.component.task.AbstractTaskSchedule.checkAndExecute Line:198 - 集群主控节点主机地址【10.92.82.161】运行端口【10031】,当前主机地址【10.87.57.73】运行端口【8092】, 无法执行定时任务【com.simbest.boot.bps.task.AutoCloseTodoTask】【645】
2025-05-27 04:55:00.012 [pool-14-thread-1] INFO  com.simbest.boot.component.task.AbstractTaskSchedule.checkAndExecute Line:198 - 集群主控节点主机地址【10.92.82.161】运行端口【10031】,当前主机地址【10.87.57.73】运行端口【8092】, 无法执行定时任务【com.simbest.boot.bps.task.AutoAddTodoTask】【637】
2025-05-27 04:55:00.012 [pool-14-thread-6] INFO  com.simbest.boot.component.task.AbstractTaskSchedule.checkAndExecute Line:198 - 集群主控节点主机地址【10.92.82.161】运行端口【10031】,当前主机地址【10.87.57.73】运行端口【8092】, 无法执行定时任务【com.simbest.boot.processdata.pushWorkmanager.task.SyncTaskDataToWorkManagerTask】【642】
2025-05-27 04:55:00.012 [pool-14-thread-5] INFO  com.simbest.boot.component.task.AbstractTaskSchedule.checkAndExecute Line:198 - 集群主控节点主机地址【10.92.82.161】运行端口【10031】,当前主机地址【10.87.57.73】运行端口【8092】, 无法执行定时任务【com.simbest.boot.processdata.pushWorkmanager.task.SyncCommentDataToWorkManagerTask】【641】
2025-05-27 04:55:00.012 [pool-14-thread-10] INFO  com.simbest.boot.component.task.AbstractTaskSchedule.checkAndExecute Line:198 - 集群主控节点主机地址【10.92.82.161】运行端口【10031】,当前主机地址【10.87.57.73】运行端口【8092】, 无法执行定时任务【com.simbest.boot.processdata.pushWorkmanager.task.SyncProcessDataToWorkManagerTask】【646】
2025-05-27 04:55:00.012 [pool-14-thread-7] INFO  com.simbest.boot.component.task.AbstractTaskSchedule.checkAndExecute Line:198 - 集群主控节点主机地址【10.92.82.161】运行端口【10031】,当前主机地址【10.87.57.73】运行端口【8092】, 无法执行定时任务【com.simbest.boot.component.task.HeartTestTask】【643】
2025-05-27 04:56:00.015 [pool-14-thread-3] INFO  com.simbest.boot.component.task.AbstractTaskSchedule.checkAndExecute Line:198 - 集群主控节点主机地址【10.92.82.161】运行端口【10031】,当前主机地址【10.87.57.73】运行端口【8092】, 无法执行定时任务【com.simbest.boot.component.task.HeartTestTask】【639】
2025-05-27 04:57:00.014 [pool-14-thread-2] INFO  com.simbest.boot.component.task.AbstractTaskSchedule.checkAndExecute Line:198 - 集群主控节点主机地址【10.92.82.161】运行端口【10031】,当前主机地址【10.87.57.73】运行端口【8092】, 无法执行定时任务【com.simbest.boot.component.task.HeartTestTask】【638】
2025-05-27 04:58:00.003 [pool-14-thread-6] INFO  com.simbest.boot.component.task.AbstractTaskSchedule.checkAndExecute Line:198 - 集群主控节点主机地址【10.92.82.161】运行端口【10031】,当前主机地址【10.87.57.73】运行端口【8092】, 无法执行定时任务【com.simbest.boot.component.task.HeartTestTask】【642】
2025-05-27 04:59:00.009 [pool-14-thread-6] INFO  com.simbest.boot.component.task.AbstractTaskSchedule.checkAndExecute Line:198 - 集群主控节点主机地址【10.92.82.161】运行端口【10031】,当前主机地址【10.87.57.73】运行端口【8092】, 无法执行定时任务【com.simbest.boot.component.task.HeartTestTask】【642】
2025-05-27 04:59:59.392 [pool-14-thread-4] INFO  com.simbest.boot.component.task.AbstractTaskSchedule.checkAndExecute Line:198 - 集群主控节点主机地址【10.92.82.161】运行端口【10031】,当前主机地址【10.87.57.73】运行端口【8092】, 无法执行定时任务【com.simbest.boot.uums.schedule.UumsSysUserTask】【640】
2025-05-27 04:59:59.974 [pool-14-thread-3] INFO  com.simbest.boot.component.task.AbstractTaskSchedule.checkAndExecute Line:198 - 集群主控节点主机地址【10.92.82.161】运行端口【10031】,当前主机地址【10.87.57.73】运行端口【8092】, 无法执行定时任务【com.simbest.boot.cmcc.a4.task.A4TaskCreateXmlFile】【639】
2025-05-27 05:00:00.005 [pool-14-thread-8] INFO  com.simbest.boot.component.task.AbstractTaskSchedule.checkAndExecute Line:198 - 集群主控节点主机地址【10.92.82.161】运行端口【10031】,当前主机地址【10.87.57.73】运行端口【8092】, 无法执行定时任务【com.simbest.boot.processdata.pushWorkmanager.task.SyncProcessDataToWorkManagerTask】【644】
2025-05-27 05:00:00.005 [pool-14-thread-1] INFO  com.simbest.boot.component.task.AbstractTaskSchedule.checkAndExecute Line:198 - 集群主控节点主机地址【10.92.82.161】运行端口【10031】,当前主机地址【10.87.57.73】运行端口【8092】, 无法执行定时任务【com.simbest.boot.bps.task.AutoCloseTodoTask】【637】
2025-05-27 05:00:00.005 [pool-14-thread-3] INFO  com.simbest.boot.component.task.AbstractTaskSchedule.checkAndExecute Line:198 - 集群主控节点主机地址【10.92.82.161】运行端口【10031】,当前主机地址【10.87.57.73】运行端口【8092】, 无法执行定时任务【com.simbest.boot.processdata.pushWorkmanager.task.SyncTaskDataToWorkManagerTask】【639】
2025-05-27 05:00:00.005 [pool-14-thread-10] INFO  com.simbest.boot.component.task.AbstractTaskSchedule.checkAndExecute Line:198 - 集群主控节点主机地址【10.92.82.161】运行端口【10031】,当前主机地址【10.87.57.73】运行端口【8092】, 无法执行定时任务【com.simbest.boot.component.task.LicenseCheckTask】【646】
2025-05-27 05:00:00.005 [pool-14-thread-4] INFO  com.simbest.boot.component.task.AbstractTaskSchedule.checkAndExecute Line:198 - 集群主控节点主机地址【10.92.82.161】运行端口【10031】,当前主机地址【10.87.57.73】运行端口【8092】, 无法执行定时任务【com.simbest.boot.bps.task.AutoAddTodoTask】【640】
2025-05-27 05:00:00.005 [pool-14-thread-7] INFO  com.simbest.boot.component.task.AbstractTaskSchedule.checkAndExecute Line:198 - 集群主控节点主机地址【10.92.82.161】运行端口【10031】,当前主机地址【10.87.57.73】运行端口【8092】, 无法执行定时任务【com.simbest.boot.bps.task.AutoCheckWithUpdateTask】【643】
2025-05-27 05:00:00.005 [pool-14-thread-6] INFO  com.simbest.boot.component.task.AbstractTaskSchedule.checkAndExecute Line:198 - 集群主控节点主机地址【10.92.82.161】运行端口【10031】,当前主机地址【10.87.57.73】运行端口【8092】, 无法执行定时任务【com.simbest.boot.processdata.pushWorkmanager.task.SyncCommentDataToWorkManagerTask】【642】
2025-05-27 05:00:00.005 [pool-14-thread-8] INFO  com.simbest.boot.component.task.AbstractTaskSchedule.checkAndExecute Line:198 - 集群主控节点主机地址【10.92.82.161】运行端口【10031】,当前主机地址【10.87.57.73】运行端口【8092】, 无法执行定时任务【com.simbest.boot.component.task.HeartTestTask】【644】
2025-05-27 05:00:01.010 [pool-14-thread-10] INFO  com.simbest.boot.component.task.AbstractTaskSchedule.checkAndExecute Line:198 - 集群主控节点主机地址【10.92.82.161】运行端口【10031】,当前主机地址【10.87.57.73】运行端口【8092】, 无法执行定时任务【com.simbest.boot.component.task.LicenseCheckTask】【646】
2025-05-27 05:00:02.014 [pool-14-thread-10] INFO  com.simbest.boot.component.task.AbstractTaskSchedule.checkAndExecute Line:198 - 集群主控节点主机地址【10.92.82.161】运行端口【10031】,当前主机地址【10.87.57.73】运行端口【8092】, 无法执行定时任务【com.simbest.boot.component.task.LicenseCheckTask】【646】
2025-05-27 05:00:03.004 [pool-14-thread-10] INFO  com.simbest.boot.component.task.AbstractTaskSchedule.checkAndExecute Line:198 - 集群主控节点主机地址【10.92.82.161】运行端口【10031】,当前主机地址【10.87.57.73】运行端口【8092】, 无法执行定时任务【com.simbest.boot.component.task.LicenseCheckTask】【646】
2025-05-27 05:00:04.011 [pool-14-thread-10] INFO  com.simbest.boot.component.task.AbstractTaskSchedule.checkAndExecute Line:198 - 集群主控节点主机地址【10.92.82.161】运行端口【10031】,当前主机地址【10.87.57.73】运行端口【8092】, 无法执行定时任务【com.simbest.boot.component.task.LicenseCheckTask】【646】
2025-05-27 05:00:05.005 [pool-14-thread-10] INFO  com.simbest.boot.component.task.AbstractTaskSchedule.checkAndExecute Line:198 - 集群主控节点主机地址【10.92.82.161】运行端口【10031】,当前主机地址【10.87.57.73】运行端口【8092】, 无法执行定时任务【com.simbest.boot.component.task.LicenseCheckTask】【646】
2025-05-27 05:00:06.011 [pool-14-thread-10] INFO  com.simbest.boot.component.task.AbstractTaskSchedule.checkAndExecute Line:198 - 集群主控节点主机地址【10.92.82.161】运行端口【10031】,当前主机地址【10.87.57.73】运行端口【8092】, 无法执行定时任务【com.simbest.boot.component.task.LicenseCheckTask】【646】
2025-05-27 05:00:07.007 [pool-14-thread-10] INFO  com.simbest.boot.component.task.AbstractTaskSchedule.checkAndExecute Line:198 - 集群主控节点主机地址【10.92.82.161】运行端口【10031】,当前主机地址【10.87.57.73】运行端口【8092】, 无法执行定时任务【com.simbest.boot.component.task.LicenseCheckTask】【646】
2025-05-27 05:00:08.011 [pool-14-thread-10] INFO  com.simbest.boot.component.task.AbstractTaskSchedule.checkAndExecute Line:198 - 集群主控节点主机地址【10.92.82.161】运行端口【10031】,当前主机地址【10.87.57.73】运行端口【8092】, 无法执行定时任务【com.simbest.boot.component.task.LicenseCheckTask】【646】
2025-05-27 05:00:09.010 [pool-14-thread-10] INFO  com.simbest.boot.component.task.AbstractTaskSchedule.checkAndExecute Line:198 - 集群主控节点主机地址【10.92.82.161】运行端口【10031】,当前主机地址【10.87.57.73】运行端口【8092】, 无法执行定时任务【com.simbest.boot.component.task.LicenseCheckTask】【646】
2025-05-27 05:00:10.001 [pool-14-thread-10] INFO  com.simbest.boot.component.task.AbstractTaskSchedule.checkAndExecute Line:198 - 集群主控节点主机地址【10.92.82.161】运行端口【10031】,当前主机地址【10.87.57.73】运行端口【8092】, 无法执行定时任务【com.simbest.boot.component.task.LicenseCheckTask】【646】
2025-05-27 05:00:11.011 [pool-14-thread-10] INFO  com.simbest.boot.component.task.AbstractTaskSchedule.checkAndExecute Line:198 - 集群主控节点主机地址【10.92.82.161】运行端口【10031】,当前主机地址【10.87.57.73】运行端口【8092】, 无法执行定时任务【com.simbest.boot.component.task.LicenseCheckTask】【646】
2025-05-27 05:00:12.014 [pool-14-thread-10] INFO  com.simbest.boot.component.task.AbstractTaskSchedule.checkAndExecute Line:198 - 集群主控节点主机地址【10.92.82.161】运行端口【10031】,当前主机地址【10.87.57.73】运行端口【8092】, 无法执行定时任务【com.simbest.boot.component.task.LicenseCheckTask】【646】
2025-05-27 05:00:13.009 [pool-14-thread-10] INFO  com.simbest.boot.component.task.AbstractTaskSchedule.checkAndExecute Line:198 - 集群主控节点主机地址【10.92.82.161】运行端口【10031】,当前主机地址【10.87.57.73】运行端口【8092】, 无法执行定时任务【com.simbest.boot.component.task.LicenseCheckTask】【646】
2025-05-27 05:00:14.008 [pool-14-thread-10] INFO  com.simbest.boot.component.task.AbstractTaskSchedule.checkAndExecute Line:198 - 集群主控节点主机地址【10.92.82.161】运行端口【10031】,当前主机地址【10.87.57.73】运行端口【8092】, 无法执行定时任务【com.simbest.boot.component.task.LicenseCheckTask】【646】
2025-05-27 05:00:15.014 [pool-14-thread-10] INFO  com.simbest.boot.component.task.AbstractTaskSchedule.checkAndExecute Line:198 - 集群主控节点主机地址【10.92.82.161】运行端口【10031】,当前主机地址【10.87.57.73】运行端口【8092】, 无法执行定时任务【com.simbest.boot.component.task.LicenseCheckTask】【646】
2025-05-27 05:00:16.003 [pool-14-thread-10] INFO  com.simbest.boot.component.task.AbstractTaskSchedule.checkAndExecute Line:198 - 集群主控节点主机地址【10.92.82.161】运行端口【10031】,当前主机地址【10.87.57.73】运行端口【8092】, 无法执行定时任务【com.simbest.boot.component.task.LicenseCheckTask】【646】
2025-05-27 05:00:17.006 [pool-14-thread-10] INFO  com.simbest.boot.component.task.AbstractTaskSchedule.checkAndExecute Line:198 - 集群主控节点主机地址【10.92.82.161】运行端口【10031】,当前主机地址【10.87.57.73】运行端口【8092】, 无法执行定时任务【com.simbest.boot.component.task.LicenseCheckTask】【646】
2025-05-27 05:00:18.008 [pool-14-thread-10] INFO  com.simbest.boot.component.task.AbstractTaskSchedule.checkAndExecute Line:198 - 集群主控节点主机地址【10.92.82.161】运行端口【10031】,当前主机地址【10.87.57.73】运行端口【8092】, 无法执行定时任务【com.simbest.boot.component.task.LicenseCheckTask】【646】
2025-05-27 05:00:19.001 [pool-14-thread-10] INFO  com.simbest.boot.component.task.AbstractTaskSchedule.checkAndExecute Line:198 - 集群主控节点主机地址【10.92.82.161】运行端口【10031】,当前主机地址【10.87.57.73】运行端口【8092】, 无法执行定时任务【com.simbest.boot.component.task.LicenseCheckTask】【646】
2025-05-27 05:00:20.009 [pool-14-thread-10] INFO  com.simbest.boot.component.task.AbstractTaskSchedule.checkAndExecute Line:198 - 集群主控节点主机地址【10.92.82.161】运行端口【10031】,当前主机地址【10.87.57.73】运行端口【8092】, 无法执行定时任务【com.simbest.boot.component.task.LicenseCheckTask】【646】
2025-05-27 05:00:21.014 [pool-14-thread-10] INFO  com.simbest.boot.component.task.AbstractTaskSchedule.checkAndExecute Line:198 - 集群主控节点主机地址【10.92.82.161】运行端口【10031】,当前主机地址【10.87.57.73】运行端口【8092】, 无法执行定时任务【com.simbest.boot.component.task.LicenseCheckTask】【646】
2025-05-27 05:00:22.007 [pool-14-thread-10] INFO  com.simbest.boot.component.task.AbstractTaskSchedule.checkAndExecute Line:198 - 集群主控节点主机地址【10.92.82.161】运行端口【10031】,当前主机地址【10.87.57.73】运行端口【8092】, 无法执行定时任务【com.simbest.boot.component.task.LicenseCheckTask】【646】
2025-05-27 05:00:23.013 [pool-14-thread-10] INFO  com.simbest.boot.component.task.AbstractTaskSchedule.checkAndExecute Line:198 - 集群主控节点主机地址【10.92.82.161】运行端口【10031】,当前主机地址【10.87.57.73】运行端口【8092】, 无法执行定时任务【com.simbest.boot.component.task.LicenseCheckTask】【646】
2025-05-27 05:00:24.006 [pool-14-thread-10] INFO  com.simbest.boot.component.task.AbstractTaskSchedule.checkAndExecute Line:198 - 集群主控节点主机地址【10.92.82.161】运行端口【10031】,当前主机地址【10.87.57.73】运行端口【8092】, 无法执行定时任务【com.simbest.boot.component.task.LicenseCheckTask】【646】
2025-05-27 05:00:25.012 [pool-14-thread-10] INFO  com.simbest.boot.component.task.AbstractTaskSchedule.checkAndExecute Line:198 - 集群主控节点主机地址【10.92.82.161】运行端口【10031】,当前主机地址【10.87.57.73】运行端口【8092】, 无法执行定时任务【com.simbest.boot.component.task.LicenseCheckTask】【646】
2025-05-27 05:00:26.013 [pool-14-thread-6] INFO  com.simbest.boot.component.task.AbstractTaskSchedule.checkAndExecute Line:198 - 集群主控节点主机地址【10.92.82.161】运行端口【10031】,当前主机地址【10.87.57.73】运行端口【8092】, 无法执行定时任务【com.simbest.boot.component.task.LicenseCheckTask】【642】
2025-05-27 05:00:27.005 [pool-14-thread-6] INFO  com.simbest.boot.component.task.AbstractTaskSchedule.checkAndExecute Line:198 - 集群主控节点主机地址【10.92.82.161】运行端口【10031】,当前主机地址【10.87.57.73】运行端口【8092】, 无法执行定时任务【com.simbest.boot.component.task.LicenseCheckTask】【642】
2025-05-27 05:00:28.012 [pool-14-thread-6] INFO  com.simbest.boot.component.task.AbstractTaskSchedule.checkAndExecute Line:198 - 集群主控节点主机地址【10.92.82.161】运行端口【10031】,当前主机地址【10.87.57.73】运行端口【8092】, 无法执行定时任务【com.simbest.boot.component.task.LicenseCheckTask】【642】
2025-05-27 05:00:29.002 [pool-14-thread-6] INFO  com.simbest.boot.component.task.AbstractTaskSchedule.checkAndExecute Line:198 - 集群主控节点主机地址【10.92.82.161】运行端口【10031】,当前主机地址【10.87.57.73】运行端口【8092】, 无法执行定时任务【com.simbest.boot.component.task.LicenseCheckTask】【642】
2025-05-27 05:00:30.009 [pool-14-thread-6] INFO  com.simbest.boot.component.task.AbstractTaskSchedule.checkAndExecute Line:198 - 集群主控节点主机地址【10.92.82.161】运行端口【10031】,当前主机地址【10.87.57.73】运行端口【8092】, 无法执行定时任务【com.simbest.boot.component.task.LicenseCheckTask】【642】
2025-05-27 05:00:31.014 [pool-14-thread-8] INFO  com.simbest.boot.component.task.AbstractTaskSchedule.checkAndExecute Line:198 - 集群主控节点主机地址【10.92.82.161】运行端口【10031】,当前主机地址【10.87.57.73】运行端口【8092】, 无法执行定时任务【com.simbest.boot.component.task.LicenseCheckTask】【644】
2025-05-27 05:00:32.002 [pool-14-thread-8] INFO  com.simbest.boot.component.task.AbstractTaskSchedule.checkAndExecute Line:198 - 集群主控节点主机地址【10.92.82.161】运行端口【10031】,当前主机地址【10.87.57.73】运行端口【8092】, 无法执行定时任务【com.simbest.boot.component.task.LicenseCheckTask】【644】
2025-05-27 05:00:33.005 [pool-14-thread-8] INFO  com.simbest.boot.component.task.AbstractTaskSchedule.checkAndExecute Line:198 - 集群主控节点主机地址【10.92.82.161】运行端口【10031】,当前主机地址【10.87.57.73】运行端口【8092】, 无法执行定时任务【com.simbest.boot.component.task.LicenseCheckTask】【644】
2025-05-27 05:00:34.013 [pool-14-thread-4] INFO  com.simbest.boot.component.task.AbstractTaskSchedule.checkAndExecute Line:198 - 集群主控节点主机地址【10.92.82.161】运行端口【10031】,当前主机地址【10.87.57.73】运行端口【8092】, 无法执行定时任务【com.simbest.boot.component.task.LicenseCheckTask】【640】
2025-05-27 05:00:35.006 [pool-14-thread-4] INFO  com.simbest.boot.component.task.AbstractTaskSchedule.checkAndExecute Line:198 - 集群主控节点主机地址【10.92.82.161】运行端口【10031】,当前主机地址【10.87.57.73】运行端口【8092】, 无法执行定时任务【com.simbest.boot.component.task.LicenseCheckTask】【640】
2025-05-27 05:00:36.008 [pool-14-thread-4] INFO  com.simbest.boot.component.task.AbstractTaskSchedule.checkAndExecute Line:198 - 集群主控节点主机地址【10.92.82.161】运行端口【10031】,当前主机地址【10.87.57.73】运行端口【8092】, 无法执行定时任务【com.simbest.boot.component.task.LicenseCheckTask】【640】
2025-05-27 05:00:37.003 [pool-14-thread-2] INFO  com.simbest.boot.component.task.AbstractTaskSchedule.checkAndExecute Line:198 - 集群主控节点主机地址【10.92.82.161】运行端口【10031】,当前主机地址【10.87.57.73】运行端口【8092】, 无法执行定时任务【com.simbest.boot.component.task.LicenseCheckTask】【638】
2025-05-27 05:00:38.012 [pool-14-thread-2] INFO  com.simbest.boot.component.task.AbstractTaskSchedule.checkAndExecute Line:198 - 集群主控节点主机地址【10.92.82.161】运行端口【10031】,当前主机地址【10.87.57.73】运行端口【8092】, 无法执行定时任务【com.simbest.boot.component.task.LicenseCheckTask】【638】
2025-05-27 05:00:39.016 [pool-14-thread-2] INFO  com.simbest.boot.component.task.AbstractTaskSchedule.checkAndExecute Line:198 - 集群主控节点主机地址【10.92.82.161】运行端口【10031】,当前主机地址【10.87.57.73】运行端口【8092】, 无法执行定时任务【com.simbest.boot.component.task.LicenseCheckTask】【638】
2025-05-27 05:00:40.004 [pool-14-thread-2] INFO  com.simbest.boot.component.task.AbstractTaskSchedule.checkAndExecute Line:198 - 集群主控节点主机地址【10.92.82.161】运行端口【10031】,当前主机地址【10.87.57.73】运行端口【8092】, 无法执行定时任务【com.simbest.boot.component.task.LicenseCheckTask】【638】
2025-05-27 05:00:41.014 [pool-14-thread-5] INFO  com.simbest.boot.component.task.AbstractTaskSchedule.checkAndExecute Line:198 - 集群主控节点主机地址【10.92.82.161】运行端口【10031】,当前主机地址【10.87.57.73】运行端口【8092】, 无法执行定时任务【com.simbest.boot.component.task.LicenseCheckTask】【641】
2025-05-27 05:00:42.002 [pool-14-thread-5] INFO  com.simbest.boot.component.task.AbstractTaskSchedule.checkAndExecute Line:198 - 集群主控节点主机地址【10.92.82.161】运行端口【10031】,当前主机地址【10.87.57.73】运行端口【8092】, 无法执行定时任务【com.simbest.boot.component.task.LicenseCheckTask】【641】
2025-05-27 05:00:43.008 [pool-14-thread-5] INFO  com.simbest.boot.component.task.AbstractTaskSchedule.checkAndExecute Line:198 - 集群主控节点主机地址【10.92.82.161】运行端口【10031】,当前主机地址【10.87.57.73】运行端口【8092】, 无法执行定时任务【com.simbest.boot.component.task.LicenseCheckTask】【641】
2025-05-27 05:00:44.001 [pool-14-thread-5] INFO  com.simbest.boot.component.task.AbstractTaskSchedule.checkAndExecute Line:198 - 集群主控节点主机地址【10.92.82.161】运行端口【10031】,当前主机地址【10.87.57.73】运行端口【8092】, 无法执行定时任务【com.simbest.boot.component.task.LicenseCheckTask】【641】
2025-05-27 05:00:45.003 [pool-14-thread-5] INFO  com.simbest.boot.component.task.AbstractTaskSchedule.checkAndExecute Line:198 - 集群主控节点主机地址【10.92.82.161】运行端口【10031】,当前主机地址【10.87.57.73】运行端口【8092】, 无法执行定时任务【com.simbest.boot.component.task.LicenseCheckTask】【641】
2025-05-27 05:00:46.012 [pool-14-thread-5] INFO  com.simbest.boot.component.task.AbstractTaskSchedule.checkAndExecute Line:198 - 集群主控节点主机地址【10.92.82.161】运行端口【10031】,当前主机地址【10.87.57.73】运行端口【8092】, 无法执行定时任务【com.simbest.boot.component.task.LicenseCheckTask】【641】
2025-05-27 05:00:47.009 [pool-14-thread-5] INFO  com.simbest.boot.component.task.AbstractTaskSchedule.checkAndExecute Line:198 - 集群主控节点主机地址【10.92.82.161】运行端口【10031】,当前主机地址【10.87.57.73】运行端口【8092】, 无法执行定时任务【com.simbest.boot.component.task.LicenseCheckTask】【641】
2025-05-27 05:00:48.014 [pool-14-thread-5] INFO  com.simbest.boot.component.task.AbstractTaskSchedule.checkAndExecute Line:198 - 集群主控节点主机地址【10.92.82.161】运行端口【10031】,当前主机地址【10.87.57.73】运行端口【8092】, 无法执行定时任务【com.simbest.boot.component.task.LicenseCheckTask】【641】
2025-05-27 05:00:49.007 [pool-14-thread-5] INFO  com.simbest.boot.component.task.AbstractTaskSchedule.checkAndExecute Line:198 - 集群主控节点主机地址【10.92.82.161】运行端口【10031】,当前主机地址【10.87.57.73】运行端口【8092】, 无法执行定时任务【com.simbest.boot.component.task.LicenseCheckTask】【641】
2025-05-27 05:00:50.012 [pool-14-thread-5] INFO  com.simbest.boot.component.task.AbstractTaskSchedule.checkAndExecute Line:198 - 集群主控节点主机地址【10.92.82.161】运行端口【10031】,当前主机地址【10.87.57.73】运行端口【8092】, 无法执行定时任务【com.simbest.boot.component.task.LicenseCheckTask】【641】
2025-05-27 05:00:51.004 [pool-14-thread-5] INFO  com.simbest.boot.component.task.AbstractTaskSchedule.checkAndExecute Line:198 - 集群主控节点主机地址【10.92.82.161】运行端口【10031】,当前主机地址【10.87.57.73】运行端口【8092】, 无法执行定时任务【com.simbest.boot.component.task.LicenseCheckTask】【641】
2025-05-27 05:00:52.008 [pool-14-thread-8] INFO  com.simbest.boot.component.task.AbstractTaskSchedule.checkAndExecute Line:198 - 集群主控节点主机地址【10.92.82.161】运行端口【10031】,当前主机地址【10.87.57.73】运行端口【8092】, 无法执行定时任务【com.simbest.boot.component.task.LicenseCheckTask】【644】
2025-05-27 05:00:53.020 [pool-14-thread-8] INFO  com.simbest.boot.component.task.AbstractTaskSchedule.checkAndExecute Line:198 - 集群主控节点主机地址【10.92.82.161】运行端口【10031】,当前主机地址【10.87.57.73】运行端口【8092】, 无法执行定时任务【com.simbest.boot.component.task.LicenseCheckTask】【644】
2025-05-27 05:00:54.012 [pool-14-thread-8] INFO  com.simbest.boot.component.task.AbstractTaskSchedule.checkAndExecute Line:198 - 集群主控节点主机地址【10.92.82.161】运行端口【10031】,当前主机地址【10.87.57.73】运行端口【8092】, 无法执行定时任务【com.simbest.boot.component.task.LicenseCheckTask】【644】
2025-05-27 05:00:55.002 [pool-14-thread-8] INFO  com.simbest.boot.component.task.AbstractTaskSchedule.checkAndExecute Line:198 - 集群主控节点主机地址【10.92.82.161】运行端口【10031】,当前主机地址【10.87.57.73】运行端口【8092】, 无法执行定时任务【com.simbest.boot.component.task.LicenseCheckTask】【644】
2025-05-27 05:00:56.007 [pool-14-thread-8] INFO  com.simbest.boot.component.task.AbstractTaskSchedule.checkAndExecute Line:198 - 集群主控节点主机地址【10.92.82.161】运行端口【10031】,当前主机地址【10.87.57.73】运行端口【8092】, 无法执行定时任务【com.simbest.boot.component.task.LicenseCheckTask】【644】
2025-05-27 05:00:57.002 [pool-14-thread-8] INFO  com.simbest.boot.component.task.AbstractTaskSchedule.checkAndExecute Line:198 - 集群主控节点主机地址【10.92.82.161】运行端口【10031】,当前主机地址【10.87.57.73】运行端口【8092】, 无法执行定时任务【com.simbest.boot.component.task.LicenseCheckTask】【644】
2025-05-27 05:00:58.009 [pool-14-thread-8] INFO  com.simbest.boot.component.task.AbstractTaskSchedule.checkAndExecute Line:198 - 集群主控节点主机地址【10.92.82.161】运行端口【10031】,当前主机地址【10.87.57.73】运行端口【8092】, 无法执行定时任务【com.simbest.boot.component.task.LicenseCheckTask】【644】
2025-05-27 05:00:59.001 [pool-14-thread-8] INFO  com.simbest.boot.component.task.AbstractTaskSchedule.checkAndExecute Line:198 - 集群主控节点主机地址【10.92.82.161】运行端口【10031】,当前主机地址【10.87.57.73】运行端口【8092】, 无法执行定时任务【com.simbest.boot.component.task.LicenseCheckTask】【644】
2025-05-27 05:01:00.004 [pool-14-thread-3] INFO  com.simbest.boot.component.task.AbstractTaskSchedule.checkAndExecute Line:198 - 集群主控节点主机地址【10.92.82.161】运行端口【10031】,当前主机地址【10.87.57.73】运行端口【8092】, 无法执行定时任务【com.simbest.boot.component.task.HeartTestTask】【639】
2025-05-27 05:02:00.010 [pool-14-thread-3] INFO  com.simbest.boot.component.task.AbstractTaskSchedule.checkAndExecute Line:198 - 集群主控节点主机地址【10.92.82.161】运行端口【10031】,当前主机地址【10.87.57.73】运行端口【8092】, 无法执行定时任务【com.simbest.boot.component.task.HeartTestTask】【639】
2025-05-27 05:03:00.014 [pool-14-thread-4] INFO  com.simbest.boot.component.task.AbstractTaskSchedule.checkAndExecute Line:198 - 集群主控节点主机地址【10.92.82.161】运行端口【10031】,当前主机地址【10.87.57.73】运行端口【8092】, 无法执行定时任务【com.simbest.boot.component.task.HeartTestTask】【640】
2025-05-27 05:04:00.006 [pool-14-thread-4] INFO  com.simbest.boot.component.task.AbstractTaskSchedule.checkAndExecute Line:198 - 集群主控节点主机地址【10.92.82.161】运行端口【10031】,当前主机地址【10.87.57.73】运行端口【8092】, 无法执行定时任务【com.simbest.boot.component.task.HeartTestTask】【640】
2025-05-27 05:05:00.008 [pool-14-thread-1] INFO  com.simbest.boot.component.task.AbstractTaskSchedule.checkAndExecute Line:198 - 集群主控节点主机地址【10.92.82.161】运行端口【10031】,当前主机地址【10.87.57.73】运行端口【8092】, 无法执行定时任务【com.simbest.boot.processdata.pushWorkmanager.task.SyncProcessDataToWorkManagerTask】【637】
2025-05-27 05:05:00.008 [pool-14-thread-5] INFO  com.simbest.boot.component.task.AbstractTaskSchedule.checkAndExecute Line:198 - 集群主控节点主机地址【10.92.82.161】运行端口【10031】,当前主机地址【10.87.57.73】运行端口【8092】, 无法执行定时任务【com.simbest.boot.bps.task.AutoCloseTodoTask】【641】
2025-05-27 05:05:00.009 [pool-14-thread-4] INFO  com.simbest.boot.component.task.AbstractTaskSchedule.checkAndExecute Line:198 - 集群主控节点主机地址【10.92.82.161】运行端口【10031】,当前主机地址【10.87.57.73】运行端口【8092】, 无法执行定时任务【com.simbest.boot.component.task.HeartTestTask】【640】
2025-05-27 05:05:00.009 [pool-14-thread-7] INFO  com.simbest.boot.component.task.AbstractTaskSchedule.checkAndExecute Line:198 - 集群主控节点主机地址【10.92.82.161】运行端口【10031】,当前主机地址【10.87.57.73】运行端口【8092】, 无法执行定时任务【com.simbest.boot.bps.task.AutoAddTodoTask】【643】
2025-05-27 05:05:00.009 [pool-14-thread-8] INFO  com.simbest.boot.component.task.AbstractTaskSchedule.checkAndExecute Line:198 - 集群主控节点主机地址【10.92.82.161】运行端口【10031】,当前主机地址【10.87.57.73】运行端口【8092】, 无法执行定时任务【com.simbest.boot.processdata.pushWorkmanager.task.SyncCommentDataToWorkManagerTask】【644】
2025-05-27 05:05:00.009 [pool-14-thread-2] INFO  com.simbest.boot.component.task.AbstractTaskSchedule.checkAndExecute Line:198 - 集群主控节点主机地址【10.92.82.161】运行端口【10031】,当前主机地址【10.87.57.73】运行端口【8092】, 无法执行定时任务【com.simbest.boot.bps.task.AutoCheckWithUpdateTask】【638】
2025-05-27 05:05:00.008 [pool-14-thread-9] INFO  com.simbest.boot.component.task.AbstractTaskSchedule.checkAndExecute Line:198 - 集群主控节点主机地址【10.92.82.161】运行端口【10031】,当前主机地址【10.87.57.73】运行端口【8092】, 无法执行定时任务【com.simbest.boot.processdata.pushWorkmanager.task.SyncTaskDataToWorkManagerTask】【645】
2025-05-27 05:06:00.006 [pool-14-thread-6] INFO  com.simbest.boot.component.task.AbstractTaskSchedule.checkAndExecute Line:198 - 集群主控节点主机地址【10.92.82.161】运行端口【10031】,当前主机地址【10.87.57.73】运行端口【8092】, 无法执行定时任务【com.simbest.boot.component.task.HeartTestTask】【642】
2025-05-27 05:07:00.007 [pool-14-thread-6] INFO  com.simbest.boot.component.task.AbstractTaskSchedule.checkAndExecute Line:198 - 集群主控节点主机地址【10.92.82.161】运行端口【10031】,当前主机地址【10.87.57.73】运行端口【8092】, 无法执行定时任务【com.simbest.boot.component.task.HeartTestTask】【642】
2025-05-27 05:08:00.006 [pool-14-thread-1] INFO  com.simbest.boot.component.task.AbstractTaskSchedule.checkAndExecute Line:198 - 集群主控节点主机地址【10.92.82.161】运行端口【10031】,当前主机地址【10.87.57.73】运行端口【8092】, 无法执行定时任务【com.simbest.boot.component.task.HeartTestTask】【637】
2025-05-27 05:09:00.009 [pool-14-thread-1] INFO  com.simbest.boot.component.task.AbstractTaskSchedule.checkAndExecute Line:198 - 集群主控节点主机地址【10.92.82.161】运行端口【10031】,当前主机地址【10.87.57.73】运行端口【8092】, 无法执行定时任务【com.simbest.boot.component.task.HeartTestTask】【637】
2025-05-27 05:10:00.003 [pool-14-thread-9] INFO  com.simbest.boot.component.task.AbstractTaskSchedule.checkAndExecute Line:198 - 集群主控节点主机地址【10.92.82.161】运行端口【10031】,当前主机地址【10.87.57.73】运行端口【8092】, 无法执行定时任务【com.simbest.boot.processdata.pushWorkmanager.task.SyncTaskDataToWorkManagerTask】【645】
2025-05-27 05:10:00.003 [pool-14-thread-7] INFO  com.simbest.boot.component.task.AbstractTaskSchedule.checkAndExecute Line:198 - 集群主控节点主机地址【10.92.82.161】运行端口【10031】,当前主机地址【10.87.57.73】运行端口【8092】, 无法执行定时任务【com.simbest.boot.component.task.HeartTestTask】【643】
2025-05-27 05:10:00.003 [pool-14-thread-3] INFO  com.simbest.boot.component.task.AbstractTaskSchedule.checkAndExecute Line:198 - 集群主控节点主机地址【10.92.82.161】运行端口【10031】,当前主机地址【10.87.57.73】运行端口【8092】, 无法执行定时任务【com.simbest.boot.bps.task.AutoAddTodoTask】【639】
2025-05-27 05:10:00.003 [pool-14-thread-6] INFO  com.simbest.boot.component.task.AbstractTaskSchedule.checkAndExecute Line:198 - 集群主控节点主机地址【10.92.82.161】运行端口【10031】,当前主机地址【10.87.57.73】运行端口【8092】, 无法执行定时任务【com.simbest.boot.bps.task.AutoCloseTodoTask】【642】
2025-05-27 05:10:00.003 [pool-14-thread-8] INFO  com.simbest.boot.component.task.AbstractTaskSchedule.checkAndExecute Line:198 - 集群主控节点主机地址【10.92.82.161】运行端口【10031】,当前主机地址【10.87.57.73】运行端口【8092】, 无法执行定时任务【com.simbest.boot.processdata.pushWorkmanager.task.SyncProcessDataToWorkManagerTask】【644】
2025-05-27 05:10:00.003 [pool-14-thread-2] INFO  com.simbest.boot.component.task.AbstractTaskSchedule.checkAndExecute Line:198 - 集群主控节点主机地址【10.92.82.161】运行端口【10031】,当前主机地址【10.87.57.73】运行端口【8092】, 无法执行定时任务【com.simbest.boot.processdata.pushWorkmanager.task.SyncCommentDataToWorkManagerTask】【638】
2025-05-27 05:10:00.004 [pool-14-thread-4] INFO  com.simbest.boot.component.task.AbstractTaskSchedule.checkAndExecute Line:198 - 集群主控节点主机地址【10.92.82.161】运行端口【10031】,当前主机地址【10.87.57.73】运行端口【8092】, 无法执行定时任务【com.simbest.boot.bps.task.AutoCheckWithUpdateTask】【640】
2025-05-27 05:11:00.002 [pool-14-thread-6] INFO  com.simbest.boot.component.task.AbstractTaskSchedule.checkAndExecute Line:198 - 集群主控节点主机地址【10.92.82.161】运行端口【10031】,当前主机地址【10.87.57.73】运行端口【8092】, 无法执行定时任务【com.simbest.boot.component.task.HeartTestTask】【642】
2025-05-27 05:32:10.608 [pool-14-thread-5] INFO  com.simbest.boot.component.task.AbstractTaskSchedule.checkAndExecute Line:198 - 集群主控节点主机地址【10.92.82.161】运行端口【10031】,当前主机地址【10.87.57.73】运行端口【8092】, 无法执行定时任务【com.simbest.boot.component.task.HeartTestTask】【641】
2025-05-27 05:32:10.609 [pool-14-thread-7] INFO  com.simbest.boot.component.task.AbstractTaskSchedule.checkAndExecute Line:198 - 集群主控节点主机地址【10.92.82.161】运行端口【10031】,当前主机地址【10.87.57.73】运行端口【8092】, 无法执行定时任务【com.simbest.boot.bps.task.AutoCheckWithUpdateTask】【643】
2025-05-27 05:32:10.609 [pool-14-thread-4] INFO  com.simbest.boot.component.task.AbstractTaskSchedule.checkAndExecute Line:198 - 集群主控节点主机地址【10.92.82.161】运行端口【10031】,当前主机地址【10.87.57.73】运行端口【8092】, 无法执行定时任务【com.simbest.boot.processdata.pushWorkmanager.task.SyncTaskDataToWorkManagerTask】【640】
2025-05-27 05:32:10.609 [pool-14-thread-5] INFO  com.simbest.boot.component.task.AbstractTaskSchedule.checkAndExecute Line:198 - 集群主控节点主机地址【10.92.82.161】运行端口【10031】,当前主机地址【10.87.57.73】运行端口【8092】, 无法执行定时任务【com.simbest.boot.bps.task.AutoAddTodoTask】【641】
2025-05-27 05:32:10.609 [pool-14-thread-7] INFO  com.simbest.boot.component.task.AbstractTaskSchedule.checkAndExecute Line:198 - 集群主控节点主机地址【10.92.82.161】运行端口【10031】,当前主机地址【10.87.57.73】运行端口【8092】, 无法执行定时任务【com.simbest.boot.component.task.LicenseCheckTask】【643】
2025-05-27 05:32:10.608 [pool-14-thread-8] INFO  com.simbest.boot.component.task.AbstractTaskSchedule.checkAndExecute Line:198 - 集群主控节点主机地址【10.92.82.161】运行端口【10031】,当前主机地址【10.87.57.73】运行端口【8092】, 无法执行定时任务【com.simbest.boot.processdata.pushWorkmanager.task.SyncProcessDataToWorkManagerTask】【644】
2025-05-27 05:32:10.608 [pool-14-thread-10] INFO  com.simbest.boot.component.task.AbstractTaskSchedule.checkAndExecute Line:198 - 集群主控节点主机地址【10.92.82.161】运行端口【10031】,当前主机地址【10.87.57.73】运行端口【8092】, 无法执行定时任务【com.simbest.boot.bps.task.AutoCloseTodoTask】【646】
2025-05-27 05:32:10.608 [pool-14-thread-2] INFO  com.simbest.boot.component.task.AbstractTaskSchedule.checkAndExecute Line:198 - 集群主控节点主机地址【10.92.82.161】运行端口【10031】,当前主机地址【10.87.57.73】运行端口【8092】, 无法执行定时任务【com.simbest.boot.processdata.pushWorkmanager.task.SyncCommentDataToWorkManagerTask】【638】
2025-05-27 05:33:00.000 [pool-14-thread-5] INFO  com.simbest.boot.component.task.AbstractTaskSchedule.checkAndExecute Line:198 - 集群主控节点主机地址【10.92.82.161】运行端口【10031】,当前主机地址【10.87.57.73】运行端口【8092】, 无法执行定时任务【com.simbest.boot.component.task.HeartTestTask】【641】
2025-05-27 05:34:00.004 [pool-14-thread-9] INFO  com.simbest.boot.component.task.AbstractTaskSchedule.checkAndExecute Line:198 - 集群主控节点主机地址【10.92.82.161】运行端口【10031】,当前主机地址【10.87.57.73】运行端口【8092】, 无法执行定时任务【com.simbest.boot.component.task.HeartTestTask】【645】
2025-05-27 05:35:00.013 [pool-14-thread-4] INFO  com.simbest.boot.component.task.AbstractTaskSchedule.checkAndExecute Line:198 - 集群主控节点主机地址【10.92.82.161】运行端口【10031】,当前主机地址【10.87.57.73】运行端口【8092】, 无法执行定时任务【com.simbest.boot.processdata.pushWorkmanager.task.SyncCommentDataToWorkManagerTask】【640】
2025-05-27 05:35:00.013 [pool-14-thread-4] INFO  com.simbest.boot.component.task.AbstractTaskSchedule.checkAndExecute Line:198 - 集群主控节点主机地址【10.92.82.161】运行端口【10031】,当前主机地址【10.87.57.73】运行端口【8092】, 无法执行定时任务【com.simbest.boot.processdata.pushWorkmanager.task.SyncProcessDataToWorkManagerTask】【640】
2025-05-27 05:35:00.013 [pool-14-thread-9] INFO  com.simbest.boot.component.task.AbstractTaskSchedule.checkAndExecute Line:198 - 集群主控节点主机地址【10.92.82.161】运行端口【10031】,当前主机地址【10.87.57.73】运行端口【8092】, 无法执行定时任务【com.simbest.boot.bps.task.AutoCheckWithUpdateTask】【645】
2025-05-27 05:35:00.013 [pool-14-thread-5] INFO  com.simbest.boot.component.task.AbstractTaskSchedule.checkAndExecute Line:198 - 集群主控节点主机地址【10.92.82.161】运行端口【10031】,当前主机地址【10.87.57.73】运行端口【8092】, 无法执行定时任务【com.simbest.boot.component.task.HeartTestTask】【641】
2025-05-27 05:35:00.013 [pool-14-thread-1] INFO  com.simbest.boot.component.task.AbstractTaskSchedule.checkAndExecute Line:198 - 集群主控节点主机地址【10.92.82.161】运行端口【10031】,当前主机地址【10.87.57.73】运行端口【8092】, 无法执行定时任务【com.simbest.boot.bps.task.AutoAddTodoTask】【637】
2025-05-27 05:35:00.013 [pool-14-thread-8] INFO  com.simbest.boot.component.task.AbstractTaskSchedule.checkAndExecute Line:198 - 集群主控节点主机地址【10.92.82.161】运行端口【10031】,当前主机地址【10.87.57.73】运行端口【8092】, 无法执行定时任务【com.simbest.boot.processdata.pushWorkmanager.task.SyncTaskDataToWorkManagerTask】【644】
2025-05-27 05:35:00.013 [pool-14-thread-6] INFO  com.simbest.boot.component.task.AbstractTaskSchedule.checkAndExecute Line:198 - 集群主控节点主机地址【10.92.82.161】运行端口【10031】,当前主机地址【10.87.57.73】运行端口【8092】, 无法执行定时任务【com.simbest.boot.bps.task.AutoCloseTodoTask】【642】
2025-05-27 05:36:00.003 [pool-14-thread-5] INFO  com.simbest.boot.component.task.AbstractTaskSchedule.checkAndExecute Line:198 - 集群主控节点主机地址【10.92.82.161】运行端口【10031】,当前主机地址【10.87.57.73】运行端口【8092】, 无法执行定时任务【com.simbest.boot.component.task.HeartTestTask】【641】
2025-05-27 05:37:00.009 [pool-14-thread-2] INFO  com.simbest.boot.component.task.AbstractTaskSchedule.checkAndExecute Line:198 - 集群主控节点主机地址【10.92.82.161】运行端口【10031】,当前主机地址【10.87.57.73】运行端口【8092】, 无法执行定时任务【com.simbest.boot.component.task.HeartTestTask】【638】
2025-05-27 05:38:00.012 [pool-14-thread-10] INFO  com.simbest.boot.component.task.AbstractTaskSchedule.checkAndExecute Line:198 - 集群主控节点主机地址【10.92.82.161】运行端口【10031】,当前主机地址【10.87.57.73】运行端口【8092】, 无法执行定时任务【com.simbest.boot.component.task.HeartTestTask】【646】
2025-05-27 05:39:00.007 [pool-14-thread-10] INFO  com.simbest.boot.component.task.AbstractTaskSchedule.checkAndExecute Line:198 - 集群主控节点主机地址【10.92.82.161】运行端口【10031】,当前主机地址【10.87.57.73】运行端口【8092】, 无法执行定时任务【com.simbest.boot.component.task.HeartTestTask】【646】
2025-05-27 05:40:00.006 [pool-14-thread-1] INFO  com.simbest.boot.component.task.AbstractTaskSchedule.checkAndExecute Line:198 - 集群主控节点主机地址【10.92.82.161】运行端口【10031】,当前主机地址【10.87.57.73】运行端口【8092】, 无法执行定时任务【com.simbest.boot.processdata.pushWorkmanager.task.SyncProcessDataToWorkManagerTask】【637】
2025-05-27 05:40:00.006 [pool-14-thread-3] INFO  com.simbest.boot.component.task.AbstractTaskSchedule.checkAndExecute Line:198 - 集群主控节点主机地址【10.92.82.161】运行端口【10031】,当前主机地址【10.87.57.73】运行端口【8092】, 无法执行定时任务【com.simbest.boot.bps.task.AutoAddTodoTask】【639】
2025-05-27 05:40:00.008 [pool-14-thread-5] INFO  com.simbest.boot.component.task.AbstractTaskSchedule.checkAndExecute Line:198 - 集群主控节点主机地址【10.92.82.161】运行端口【10031】,当前主机地址【10.87.57.73】运行端口【8092】, 无法执行定时任务【com.simbest.boot.component.task.HeartTestTask】【641】
2025-05-27 05:40:00.006 [pool-14-thread-9] INFO  com.simbest.boot.component.task.AbstractTaskSchedule.checkAndExecute Line:198 - 集群主控节点主机地址【10.92.82.161】运行端口【10031】,当前主机地址【10.87.57.73】运行端口【8092】, 无法执行定时任务【com.simbest.boot.processdata.pushWorkmanager.task.SyncTaskDataToWorkManagerTask】【645】
2025-05-27 05:40:00.006 [pool-14-thread-4] INFO  com.simbest.boot.component.task.AbstractTaskSchedule.checkAndExecute Line:198 - 集群主控节点主机地址【10.92.82.161】运行端口【10031】,当前主机地址【10.87.57.73】运行端口【8092】, 无法执行定时任务【com.simbest.boot.bps.task.AutoCloseTodoTask】【640】
2025-05-27 05:40:00.006 [pool-14-thread-2] INFO  com.simbest.boot.component.task.AbstractTaskSchedule.checkAndExecute Line:198 - 集群主控节点主机地址【10.92.82.161】运行端口【10031】,当前主机地址【10.87.57.73】运行端口【8092】, 无法执行定时任务【com.simbest.boot.bps.task.AutoCheckWithUpdateTask】【638】
2025-05-27 05:40:00.006 [pool-14-thread-7] INFO  com.simbest.boot.component.task.AbstractTaskSchedule.checkAndExecute Line:198 - 集群主控节点主机地址【10.92.82.161】运行端口【10031】,当前主机地址【10.87.57.73】运行端口【8092】, 无法执行定时任务【com.simbest.boot.processdata.pushWorkmanager.task.SyncCommentDataToWorkManagerTask】【643】
2025-05-27 05:41:00.012 [pool-14-thread-8] INFO  com.simbest.boot.component.task.AbstractTaskSchedule.checkAndExecute Line:198 - 集群主控节点主机地址【10.92.82.161】运行端口【10031】,当前主机地址【10.87.57.73】运行端口【8092】, 无法执行定时任务【com.simbest.boot.component.task.HeartTestTask】【644】
2025-05-27 05:42:00.005 [pool-14-thread-3] INFO  com.simbest.boot.component.task.AbstractTaskSchedule.checkAndExecute Line:198 - 集群主控节点主机地址【10.92.82.161】运行端口【10031】,当前主机地址【10.87.57.73】运行端口【8092】, 无法执行定时任务【com.simbest.boot.component.task.HeartTestTask】【639】
2025-05-27 05:43:00.000 [pool-14-thread-7] INFO  com.simbest.boot.component.task.AbstractTaskSchedule.checkAndExecute Line:198 - 集群主控节点主机地址【10.92.82.161】运行端口【10031】,当前主机地址【10.87.57.73】运行端口【8092】, 无法执行定时任务【com.simbest.boot.component.task.HeartTestTask】【643】
2025-05-27 05:44:00.006 [pool-14-thread-6] INFO  com.simbest.boot.component.task.AbstractTaskSchedule.checkAndExecute Line:198 - 集群主控节点主机地址【10.92.82.161】运行端口【10031】,当前主机地址【10.87.57.73】运行端口【8092】, 无法执行定时任务【com.simbest.boot.component.task.HeartTestTask】【642】
2025-05-27 05:45:00.004 [pool-14-thread-6] INFO  com.simbest.boot.component.task.AbstractTaskSchedule.checkAndExecute Line:198 - 集群主控节点主机地址【10.92.82.161】运行端口【10031】,当前主机地址【10.87.57.73】运行端口【8092】, 无法执行定时任务【com.simbest.boot.processdata.pushWorkmanager.task.SyncCommentDataToWorkManagerTask】【642】
2025-05-27 05:45:00.004 [pool-14-thread-4] INFO  com.simbest.boot.component.task.AbstractTaskSchedule.checkAndExecute Line:198 - 集群主控节点主机地址【10.92.82.161】运行端口【10031】,当前主机地址【10.87.57.73】运行端口【8092】, 无法执行定时任务【com.simbest.boot.component.task.HeartTestTask】【640】
2025-05-27 05:45:00.004 [pool-14-thread-5] INFO  com.simbest.boot.component.task.AbstractTaskSchedule.checkAndExecute Line:198 - 集群主控节点主机地址【10.92.82.161】运行端口【10031】,当前主机地址【10.87.57.73】运行端口【8092】, 无法执行定时任务【com.simbest.boot.bps.task.AutoCloseTodoTask】【641】
2025-05-27 05:45:00.005 [pool-14-thread-7] INFO  com.simbest.boot.component.task.AbstractTaskSchedule.checkAndExecute Line:198 - 集群主控节点主机地址【10.92.82.161】运行端口【10031】,当前主机地址【10.87.57.73】运行端口【8092】, 无法执行定时任务【com.simbest.boot.bps.task.AutoAddTodoTask】【643】
2025-05-27 05:45:00.004 [pool-14-thread-3] INFO  com.simbest.boot.component.task.AbstractTaskSchedule.checkAndExecute Line:198 - 集群主控节点主机地址【10.92.82.161】运行端口【10031】,当前主机地址【10.87.57.73】运行端口【8092】, 无法执行定时任务【com.simbest.boot.processdata.pushWorkmanager.task.SyncTaskDataToWorkManagerTask】【639】
2025-05-27 05:45:00.004 [pool-14-thread-8] INFO  com.simbest.boot.component.task.AbstractTaskSchedule.checkAndExecute Line:198 - 集群主控节点主机地址【10.92.82.161】运行端口【10031】,当前主机地址【10.87.57.73】运行端口【8092】, 无法执行定时任务【com.simbest.boot.processdata.pushWorkmanager.task.SyncProcessDataToWorkManagerTask】【644】
2025-05-27 05:45:00.004 [pool-14-thread-2] INFO  com.simbest.boot.component.task.AbstractTaskSchedule.checkAndExecute Line:198 - 集群主控节点主机地址【10.92.82.161】运行端口【10031】,当前主机地址【10.87.57.73】运行端口【8092】, 无法执行定时任务【com.simbest.boot.bps.task.AutoCheckWithUpdateTask】【638】
2025-05-27 05:46:00.008 [pool-14-thread-5] INFO  com.simbest.boot.component.task.AbstractTaskSchedule.checkAndExecute Line:198 - 集群主控节点主机地址【10.92.82.161】运行端口【10031】,当前主机地址【10.87.57.73】运行端口【8092】, 无法执行定时任务【com.simbest.boot.component.task.HeartTestTask】【641】
2025-05-27 05:47:00.008 [pool-14-thread-4] INFO  com.simbest.boot.component.task.AbstractTaskSchedule.checkAndExecute Line:198 - 集群主控节点主机地址【10.92.82.161】运行端口【10031】,当前主机地址【10.87.57.73】运行端口【8092】, 无法执行定时任务【com.simbest.boot.component.task.HeartTestTask】【640】
2025-05-27 05:48:00.014 [pool-14-thread-7] INFO  com.simbest.boot.component.task.AbstractTaskSchedule.checkAndExecute Line:198 - 集群主控节点主机地址【10.92.82.161】运行端口【10031】,当前主机地址【10.87.57.73】运行端口【8092】, 无法执行定时任务【com.simbest.boot.component.task.HeartTestTask】【643】
2025-05-27 05:49:00.000 [pool-14-thread-9] INFO  com.simbest.boot.component.task.AbstractTaskSchedule.checkAndExecute Line:198 - 集群主控节点主机地址【10.92.82.161】运行端口【10031】,当前主机地址【10.87.57.73】运行端口【8092】, 无法执行定时任务【com.simbest.boot.component.task.HeartTestTask】【645】
2025-05-27 05:50:00.000 [pool-14-thread-4] INFO  com.simbest.boot.component.task.AbstractTaskSchedule.checkAndExecute Line:198 - 集群主控节点主机地址【10.92.82.161】运行端口【10031】,当前主机地址【10.87.57.73】运行端口【8092】, 无法执行定时任务【com.simbest.boot.processdata.pushWorkmanager.task.SyncProcessDataToWorkManagerTask】【640】
2025-05-27 05:50:00.000 [pool-14-thread-1] INFO  com.simbest.boot.component.task.AbstractTaskSchedule.checkAndExecute Line:198 - 集群主控节点主机地址【10.92.82.161】运行端口【10031】,当前主机地址【10.87.57.73】运行端口【8092】, 无法执行定时任务【com.simbest.boot.bps.task.AutoCheckWithUpdateTask】【637】
2025-05-27 05:50:00.000 [pool-14-thread-2] INFO  com.simbest.boot.component.task.AbstractTaskSchedule.checkAndExecute Line:198 - 集群主控节点主机地址【10.92.82.161】运行端口【10031】,当前主机地址【10.87.57.73】运行端口【8092】, 无法执行定时任务【com.simbest.boot.processdata.pushWorkmanager.task.SyncTaskDataToWorkManagerTask】【638】
2025-05-27 05:50:00.002 [pool-14-thread-4] INFO  com.simbest.boot.component.task.AbstractTaskSchedule.checkAndExecute Line:198 - 集群主控节点主机地址【10.92.82.161】运行端口【10031】,当前主机地址【10.87.57.73】运行端口【8092】, 无法执行定时任务【com.simbest.boot.bps.task.AutoCloseTodoTask】【640】
2025-05-27 05:50:00.000 [pool-14-thread-3] INFO  com.simbest.boot.component.task.AbstractTaskSchedule.checkAndExecute Line:198 - 集群主控节点主机地址【10.92.82.161】运行端口【10031】,当前主机地址【10.87.57.73】运行端口【8092】, 无法执行定时任务【com.simbest.boot.component.task.HeartTestTask】【639】
2025-05-27 05:50:00.000 [pool-14-thread-9] INFO  com.simbest.boot.component.task.AbstractTaskSchedule.checkAndExecute Line:198 - 集群主控节点主机地址【10.92.82.161】运行端口【10031】,当前主机地址【10.87.57.73】运行端口【8092】, 无法执行定时任务【com.simbest.boot.processdata.pushWorkmanager.task.SyncCommentDataToWorkManagerTask】【645】
2025-05-27 05:50:00.002 [pool-14-thread-8] INFO  com.simbest.boot.component.task.AbstractTaskSchedule.checkAndExecute Line:198 - 集群主控节点主机地址【10.92.82.161】运行端口【10031】,当前主机地址【10.87.57.73】运行端口【8092】, 无法执行定时任务【com.simbest.boot.bps.task.AutoAddTodoTask】【644】
2025-05-27 05:51:00.013 [pool-14-thread-2] INFO  com.simbest.boot.component.task.AbstractTaskSchedule.checkAndExecute Line:198 - 集群主控节点主机地址【10.92.82.161】运行端口【10031】,当前主机地址【10.87.57.73】运行端口【8092】, 无法执行定时任务【com.simbest.boot.component.task.HeartTestTask】【638】
2025-05-27 06:12:11.571 [pool-14-thread-1] INFO  com.simbest.boot.component.task.AbstractTaskSchedule.checkAndExecute Line:198 - 集群主控节点主机地址【10.92.82.161】运行端口【10031】,当前主机地址【10.87.57.73】运行端口【8092】, 无法执行定时任务【com.simbest.boot.processdata.pushWorkmanager.task.SyncProcessDataToWorkManagerTask】【637】
2025-05-27 06:12:11.571 [pool-14-thread-4] INFO  com.simbest.boot.component.task.AbstractTaskSchedule.checkAndExecute Line:198 - 集群主控节点主机地址【10.92.82.161】运行端口【10031】,当前主机地址【10.87.57.73】运行端口【8092】, 无法执行定时任务【com.simbest.boot.component.task.HeartTestTask】【640】
2025-05-27 06:12:11.572 [pool-14-thread-7] INFO  com.simbest.boot.component.task.AbstractTaskSchedule.checkAndExecute Line:198 - 集群主控节点主机地址【10.92.82.161】运行端口【10031】,当前主机地址【10.87.57.73】运行端口【8092】, 无法执行定时任务【com.simbest.boot.bps.task.AutoAddTodoTask】【643】
2025-05-27 06:12:11.572 [pool-14-thread-5] INFO  com.simbest.boot.component.task.AbstractTaskSchedule.checkAndExecute Line:198 - 集群主控节点主机地址【10.92.82.161】运行端口【10031】,当前主机地址【10.87.57.73】运行端口【8092】, 无法执行定时任务【com.simbest.boot.bps.task.AutoCloseTodoTask】【641】
2025-05-27 06:12:11.572 [pool-14-thread-9] INFO  com.simbest.boot.component.task.AbstractTaskSchedule.checkAndExecute Line:198 - 集群主控节点主机地址【10.92.82.161】运行端口【10031】,当前主机地址【10.87.57.73】运行端口【8092】, 无法执行定时任务【com.simbest.boot.bps.task.AutoCheckWithUpdateTask】【645】
2025-05-27 06:12:11.572 [pool-14-thread-3] INFO  com.simbest.boot.component.task.AbstractTaskSchedule.checkAndExecute Line:198 - 集群主控节点主机地址【10.92.82.161】运行端口【10031】,当前主机地址【10.87.57.73】运行端口【8092】, 无法执行定时任务【com.simbest.boot.processdata.pushWorkmanager.task.SyncCommentDataToWorkManagerTask】【639】
2025-05-27 06:12:11.572 [pool-14-thread-4] INFO  com.simbest.boot.component.task.AbstractTaskSchedule.checkAndExecute Line:198 - 集群主控节点主机地址【10.92.82.161】运行端口【10031】,当前主机地址【10.87.57.73】运行端口【8092】, 无法执行定时任务【com.simbest.boot.cmcc.a4.task.A4TaskCreateXmlFile】【640】
2025-05-27 06:12:11.572 [pool-14-thread-8] INFO  com.simbest.boot.component.task.AbstractTaskSchedule.checkAndExecute Line:198 - 集群主控节点主机地址【10.92.82.161】运行端口【10031】,当前主机地址【10.87.57.73】运行端口【8092】, 无法执行定时任务【com.simbest.boot.processdata.pushWorkmanager.task.SyncTaskDataToWorkManagerTask】【644】
2025-05-27 06:12:11.572 [pool-14-thread-1] INFO  com.simbest.boot.component.task.AbstractTaskSchedule.checkAndExecute Line:198 - 集群主控节点主机地址【10.92.82.161】运行端口【10031】,当前主机地址【10.87.57.73】运行端口【8092】, 无法执行定时任务【com.simbest.boot.component.task.LicenseCheckTask】【637】
2025-05-27 06:13:00.014 [pool-14-thread-7] INFO  com.simbest.boot.component.task.AbstractTaskSchedule.checkAndExecute Line:198 - 集群主控节点主机地址【10.92.82.161】运行端口【10031】,当前主机地址【10.87.57.73】运行端口【8092】, 无法执行定时任务【com.simbest.boot.component.task.HeartTestTask】【643】
2025-05-27 06:14:00.001 [pool-14-thread-7] INFO  com.simbest.boot.component.task.AbstractTaskSchedule.checkAndExecute Line:198 - 集群主控节点主机地址【10.92.82.161】运行端口【10031】,当前主机地址【10.87.57.73】运行端口【8092】, 无法执行定时任务【com.simbest.boot.component.task.HeartTestTask】【643】
2025-05-27 06:15:00.002 [pool-14-thread-4] INFO  com.simbest.boot.component.task.AbstractTaskSchedule.checkAndExecute Line:198 - 集群主控节点主机地址【10.92.82.161】运行端口【10031】,当前主机地址【10.87.57.73】运行端口【8092】, 无法执行定时任务【com.simbest.boot.processdata.pushWorkmanager.task.SyncTaskDataToWorkManagerTask】【640】
2025-05-27 06:15:00.002 [pool-14-thread-10] INFO  com.simbest.boot.component.task.AbstractTaskSchedule.checkAndExecute Line:198 - 集群主控节点主机地址【10.92.82.161】运行端口【10031】,当前主机地址【10.87.57.73】运行端口【8092】, 无法执行定时任务【com.simbest.boot.processdata.pushWorkmanager.task.SyncProcessDataToWorkManagerTask】【646】
2025-05-27 06:15:00.002 [pool-14-thread-1] INFO  com.simbest.boot.component.task.AbstractTaskSchedule.checkAndExecute Line:198 - 集群主控节点主机地址【10.92.82.161】运行端口【10031】,当前主机地址【10.87.57.73】运行端口【8092】, 无法执行定时任务【com.simbest.boot.bps.task.AutoCloseTodoTask】【637】
2025-05-27 06:15:00.002 [pool-14-thread-3] INFO  com.simbest.boot.component.task.AbstractTaskSchedule.checkAndExecute Line:198 - 集群主控节点主机地址【10.92.82.161】运行端口【10031】,当前主机地址【10.87.57.73】运行端口【8092】, 无法执行定时任务【com.simbest.boot.bps.task.AutoCheckWithUpdateTask】【639】
2025-05-27 06:15:00.002 [pool-14-thread-8] INFO  com.simbest.boot.component.task.AbstractTaskSchedule.checkAndExecute Line:198 - 集群主控节点主机地址【10.92.82.161】运行端口【10031】,当前主机地址【10.87.57.73】运行端口【8092】, 无法执行定时任务【com.simbest.boot.bps.task.AutoAddTodoTask】【644】
2025-05-27 06:15:00.002 [pool-14-thread-9] INFO  com.simbest.boot.component.task.AbstractTaskSchedule.checkAndExecute Line:198 - 集群主控节点主机地址【10.92.82.161】运行端口【10031】,当前主机地址【10.87.57.73】运行端口【8092】, 无法执行定时任务【com.simbest.boot.processdata.pushWorkmanager.task.SyncCommentDataToWorkManagerTask】【645】
2025-05-27 06:15:00.002 [pool-14-thread-6] INFO  com.simbest.boot.component.task.AbstractTaskSchedule.checkAndExecute Line:198 - 集群主控节点主机地址【10.92.82.161】运行端口【10031】,当前主机地址【10.87.57.73】运行端口【8092】, 无法执行定时任务【com.simbest.boot.component.task.HeartTestTask】【642】
2025-05-27 06:16:00.009 [pool-14-thread-5] INFO  com.simbest.boot.component.task.AbstractTaskSchedule.checkAndExecute Line:198 - 集群主控节点主机地址【10.92.82.161】运行端口【10031】,当前主机地址【10.87.57.73】运行端口【8092】, 无法执行定时任务【com.simbest.boot.component.task.HeartTestTask】【641】
2025-05-27 06:17:00.008 [pool-14-thread-1] INFO  com.simbest.boot.component.task.AbstractTaskSchedule.checkAndExecute Line:198 - 集群主控节点主机地址【10.92.82.161】运行端口【10031】,当前主机地址【10.87.57.73】运行端口【8092】, 无法执行定时任务【com.simbest.boot.component.task.HeartTestTask】【637】
2025-05-27 06:18:00.009 [pool-14-thread-10] INFO  com.simbest.boot.component.task.AbstractTaskSchedule.checkAndExecute Line:198 - 集群主控节点主机地址【10.92.82.161】运行端口【10031】,当前主机地址【10.87.57.73】运行端口【8092】, 无法执行定时任务【com.simbest.boot.component.task.HeartTestTask】【646】
2025-05-27 06:19:00.011 [pool-14-thread-5] INFO  com.simbest.boot.component.task.AbstractTaskSchedule.checkAndExecute Line:198 - 集群主控节点主机地址【10.92.82.161】运行端口【10031】,当前主机地址【10.87.57.73】运行端口【8092】, 无法执行定时任务【com.simbest.boot.component.task.HeartTestTask】【641】
2025-05-27 06:20:00.004 [pool-14-thread-3] INFO  com.simbest.boot.component.task.AbstractTaskSchedule.checkAndExecute Line:198 - 集群主控节点主机地址【10.92.82.161】运行端口【10031】,当前主机地址【10.87.57.73】运行端口【8092】, 无法执行定时任务【com.simbest.boot.component.task.HeartTestTask】【639】
2025-05-27 06:20:00.004 [pool-14-thread-2] INFO  com.simbest.boot.component.task.AbstractTaskSchedule.checkAndExecute Line:198 - 集群主控节点主机地址【10.92.82.161】运行端口【10031】,当前主机地址【10.87.57.73】运行端口【8092】, 无法执行定时任务【com.simbest.boot.bps.task.AutoAddTodoTask】【638】
2025-05-27 06:20:00.004 [pool-14-thread-9] INFO  com.simbest.boot.component.task.AbstractTaskSchedule.checkAndExecute Line:198 - 集群主控节点主机地址【10.92.82.161】运行端口【10031】,当前主机地址【10.87.57.73】运行端口【8092】, 无法执行定时任务【com.simbest.boot.bps.task.AutoCheckWithUpdateTask】【645】
2025-05-27 06:20:00.004 [pool-14-thread-1] INFO  com.simbest.boot.component.task.AbstractTaskSchedule.checkAndExecute Line:198 - 集群主控节点主机地址【10.92.82.161】运行端口【10031】,当前主机地址【10.87.57.73】运行端口【8092】, 无法执行定时任务【com.simbest.boot.processdata.pushWorkmanager.task.SyncTaskDataToWorkManagerTask】【637】
2025-05-27 06:20:00.004 [pool-14-thread-5] INFO  com.simbest.boot.component.task.AbstractTaskSchedule.checkAndExecute Line:198 - 集群主控节点主机地址【10.92.82.161】运行端口【10031】,当前主机地址【10.87.57.73】运行端口【8092】, 无法执行定时任务【com.simbest.boot.processdata.pushWorkmanager.task.SyncProcessDataToWorkManagerTask】【641】
2025-05-27 06:20:00.004 [pool-14-thread-6] INFO  com.simbest.boot.component.task.AbstractTaskSchedule.checkAndExecute Line:198 - 集群主控节点主机地址【10.92.82.161】运行端口【10031】,当前主机地址【10.87.57.73】运行端口【8092】, 无法执行定时任务【com.simbest.boot.processdata.pushWorkmanager.task.SyncCommentDataToWorkManagerTask】【642】
2025-05-27 06:20:00.004 [pool-14-thread-10] INFO  com.simbest.boot.component.task.AbstractTaskSchedule.checkAndExecute Line:198 - 集群主控节点主机地址【10.92.82.161】运行端口【10031】,当前主机地址【10.87.57.73】运行端口【8092】, 无法执行定时任务【com.simbest.boot.bps.task.AutoCloseTodoTask】【646】
2025-05-27 06:21:00.006 [pool-14-thread-2] INFO  com.simbest.boot.component.task.AbstractTaskSchedule.checkAndExecute Line:198 - 集群主控节点主机地址【10.92.82.161】运行端口【10031】,当前主机地址【10.87.57.73】运行端口【8092】, 无法执行定时任务【com.simbest.boot.component.task.HeartTestTask】【638】
2025-05-27 06:22:00.015 [pool-14-thread-8] INFO  com.simbest.boot.component.task.AbstractTaskSchedule.checkAndExecute Line:198 - 集群主控节点主机地址【10.92.82.161】运行端口【10031】,当前主机地址【10.87.57.73】运行端口【8092】, 无法执行定时任务【com.simbest.boot.component.task.HeartTestTask】【644】
2025-05-27 06:23:00.014 [pool-14-thread-7] INFO  com.simbest.boot.component.task.AbstractTaskSchedule.checkAndExecute Line:198 - 集群主控节点主机地址【10.92.82.161】运行端口【10031】,当前主机地址【10.87.57.73】运行端口【8092】, 无法执行定时任务【com.simbest.boot.component.task.HeartTestTask】【643】
2025-05-27 06:24:00.006 [pool-14-thread-3] INFO  com.simbest.boot.component.task.AbstractTaskSchedule.checkAndExecute Line:198 - 集群主控节点主机地址【10.92.82.161】运行端口【10031】,当前主机地址【10.87.57.73】运行端口【8092】, 无法执行定时任务【com.simbest.boot.component.task.HeartTestTask】【639】
2025-05-27 06:25:00.012 [pool-14-thread-1] INFO  com.simbest.boot.component.task.AbstractTaskSchedule.checkAndExecute Line:198 - 集群主控节点主机地址【10.92.82.161】运行端口【10031】,当前主机地址【10.87.57.73】运行端口【8092】, 无法执行定时任务【com.simbest.boot.bps.task.AutoCheckWithUpdateTask】【637】
2025-05-27 06:25:00.012 [pool-14-thread-10] INFO  com.simbest.boot.component.task.AbstractTaskSchedule.checkAndExecute Line:198 - 集群主控节点主机地址【10.92.82.161】运行端口【10031】,当前主机地址【10.87.57.73】运行端口【8092】, 无法执行定时任务【com.simbest.boot.component.task.HeartTestTask】【646】
2025-05-27 06:25:00.013 [pool-14-thread-5] INFO  com.simbest.boot.component.task.AbstractTaskSchedule.checkAndExecute Line:198 - 集群主控节点主机地址【10.92.82.161】运行端口【10031】,当前主机地址【10.87.57.73】运行端口【8092】, 无法执行定时任务【com.simbest.boot.processdata.pushWorkmanager.task.SyncCommentDataToWorkManagerTask】【641】
2025-05-27 06:25:00.012 [pool-14-thread-6] INFO  com.simbest.boot.component.task.AbstractTaskSchedule.checkAndExecute Line:198 - 集群主控节点主机地址【10.92.82.161】运行端口【10031】,当前主机地址【10.87.57.73】运行端口【8092】, 无法执行定时任务【com.simbest.boot.bps.task.AutoAddTodoTask】【642】
2025-05-27 06:25:00.012 [pool-14-thread-4] INFO  com.simbest.boot.component.task.AbstractTaskSchedule.checkAndExecute Line:198 - 集群主控节点主机地址【10.92.82.161】运行端口【10031】,当前主机地址【10.87.57.73】运行端口【8092】, 无法执行定时任务【com.simbest.boot.processdata.pushWorkmanager.task.SyncTaskDataToWorkManagerTask】【640】
2025-05-27 06:25:00.012 [pool-14-thread-3] INFO  com.simbest.boot.component.task.AbstractTaskSchedule.checkAndExecute Line:198 - 集群主控节点主机地址【10.92.82.161】运行端口【10031】,当前主机地址【10.87.57.73】运行端口【8092】, 无法执行定时任务【com.simbest.boot.bps.task.AutoCloseTodoTask】【639】
2025-05-27 06:25:00.012 [pool-14-thread-9] INFO  com.simbest.boot.component.task.AbstractTaskSchedule.checkAndExecute Line:198 - 集群主控节点主机地址【10.92.82.161】运行端口【10031】,当前主机地址【10.87.57.73】运行端口【8092】, 无法执行定时任务【com.simbest.boot.processdata.pushWorkmanager.task.SyncProcessDataToWorkManagerTask】【645】
2025-05-27 06:26:00.009 [pool-14-thread-5] INFO  com.simbest.boot.component.task.AbstractTaskSchedule.checkAndExecute Line:198 - 集群主控节点主机地址【10.92.82.161】运行端口【10031】,当前主机地址【10.87.57.73】运行端口【8092】, 无法执行定时任务【com.simbest.boot.component.task.HeartTestTask】【641】
2025-05-27 06:27:00.003 [pool-14-thread-10] INFO  com.simbest.boot.component.task.AbstractTaskSchedule.checkAndExecute Line:198 - 集群主控节点主机地址【10.92.82.161】运行端口【10031】,当前主机地址【10.87.57.73】运行端口【8092】, 无法执行定时任务【com.simbest.boot.component.task.HeartTestTask】【646】
2025-05-27 06:28:00.008 [pool-14-thread-10] INFO  com.simbest.boot.component.task.AbstractTaskSchedule.checkAndExecute Line:198 - 集群主控节点主机地址【10.92.82.161】运行端口【10031】,当前主机地址【10.87.57.73】运行端口【8092】, 无法执行定时任务【com.simbest.boot.component.task.HeartTestTask】【646】
2025-05-27 06:29:00.001 [pool-14-thread-3] INFO  com.simbest.boot.component.task.AbstractTaskSchedule.checkAndExecute Line:198 - 集群主控节点主机地址【10.92.82.161】运行端口【10031】,当前主机地址【10.87.57.73】运行端口【8092】, 无法执行定时任务【com.simbest.boot.component.task.HeartTestTask】【639】
2025-05-27 06:30:00.007 [pool-14-thread-2] INFO  com.simbest.boot.component.task.AbstractTaskSchedule.checkAndExecute Line:198 - 集群主控节点主机地址【10.92.82.161】运行端口【10031】,当前主机地址【10.87.57.73】运行端口【8092】, 无法执行定时任务【com.simbest.boot.component.task.HeartTestTask】【638】
2025-05-27 06:30:00.007 [pool-14-thread-9] INFO  com.simbest.boot.component.task.AbstractTaskSchedule.checkAndExecute Line:198 - 集群主控节点主机地址【10.92.82.161】运行端口【10031】,当前主机地址【10.87.57.73】运行端口【8092】, 无法执行定时任务【com.simbest.boot.bps.task.AutoAddTodoTask】【645】
2025-05-27 06:30:00.007 [pool-14-thread-4] INFO  com.simbest.boot.component.task.AbstractTaskSchedule.checkAndExecute Line:198 - 集群主控节点主机地址【10.92.82.161】运行端口【10031】,当前主机地址【10.87.57.73】运行端口【8092】, 无法执行定时任务【com.simbest.boot.processdata.pushWorkmanager.task.SyncTaskDataToWorkManagerTask】【640】
2025-05-27 06:30:00.008 [pool-14-thread-10] INFO  com.simbest.boot.component.task.AbstractTaskSchedule.checkAndExecute Line:198 - 集群主控节点主机地址【10.92.82.161】运行端口【10031】,当前主机地址【10.87.57.73】运行端口【8092】, 无法执行定时任务【com.simbest.boot.processdata.pushWorkmanager.task.SyncProcessDataToWorkManagerTask】【646】
2025-05-27 06:30:00.007 [pool-14-thread-7] INFO  com.simbest.boot.component.task.AbstractTaskSchedule.checkAndExecute Line:198 - 集群主控节点主机地址【10.92.82.161】运行端口【10031】,当前主机地址【10.87.57.73】运行端口【8092】, 无法执行定时任务【com.simbest.boot.component.task.LicenseCheckTask】【643】
2025-05-27 06:30:00.007 [pool-14-thread-6] INFO  com.simbest.boot.component.task.AbstractTaskSchedule.checkAndExecute Line:198 - 集群主控节点主机地址【10.92.82.161】运行端口【10031】,当前主机地址【10.87.57.73】运行端口【8092】, 无法执行定时任务【com.simbest.boot.processdata.pushWorkmanager.task.SyncCommentDataToWorkManagerTask】【642】
2025-05-27 06:30:00.007 [pool-14-thread-1] INFO  com.simbest.boot.component.task.AbstractTaskSchedule.checkAndExecute Line:198 - 集群主控节点主机地址【10.92.82.161】运行端口【10031】,当前主机地址【10.87.57.73】运行端口【8092】, 无法执行定时任务【com.simbest.boot.bps.task.AutoCheckWithUpdateTask】【637】
2025-05-27 06:30:00.007 [pool-14-thread-3] INFO  com.simbest.boot.component.task.AbstractTaskSchedule.checkAndExecute Line:198 - 集群主控节点主机地址【10.92.82.161】运行端口【10031】,当前主机地址【10.87.57.73】运行端口【8092】, 无法执行定时任务【com.simbest.boot.bps.task.AutoCloseTodoTask】【639】
2025-05-27 06:30:01.014 [pool-14-thread-7] INFO  com.simbest.boot.component.task.AbstractTaskSchedule.checkAndExecute Line:198 - 集群主控节点主机地址【10.92.82.161】运行端口【10031】,当前主机地址【10.87.57.73】运行端口【8092】, 无法执行定时任务【com.simbest.boot.component.task.LicenseCheckTask】【643】
2025-05-27 06:30:02.012 [pool-14-thread-7] INFO  com.simbest.boot.component.task.AbstractTaskSchedule.checkAndExecute Line:198 - 集群主控节点主机地址【10.92.82.161】运行端口【10031】,当前主机地址【10.87.57.73】运行端口【8092】, 无法执行定时任务【com.simbest.boot.component.task.LicenseCheckTask】【643】
2025-05-27 06:30:03.014 [pool-14-thread-9] INFO  com.simbest.boot.component.task.AbstractTaskSchedule.checkAndExecute Line:198 - 集群主控节点主机地址【10.92.82.161】运行端口【10031】,当前主机地址【10.87.57.73】运行端口【8092】, 无法执行定时任务【com.simbest.boot.component.task.LicenseCheckTask】【645】
2025-05-27 06:30:04.009 [pool-14-thread-9] INFO  com.simbest.boot.component.task.AbstractTaskSchedule.checkAndExecute Line:198 - 集群主控节点主机地址【10.92.82.161】运行端口【10031】,当前主机地址【10.87.57.73】运行端口【8092】, 无法执行定时任务【com.simbest.boot.component.task.LicenseCheckTask】【645】
2025-05-27 06:30:05.006 [pool-14-thread-9] INFO  com.simbest.boot.component.task.AbstractTaskSchedule.checkAndExecute Line:198 - 集群主控节点主机地址【10.92.82.161】运行端口【10031】,当前主机地址【10.87.57.73】运行端口【8092】, 无法执行定时任务【com.simbest.boot.component.task.LicenseCheckTask】【645】
2025-05-27 06:30:06.012 [pool-14-thread-9] INFO  com.simbest.boot.component.task.AbstractTaskSchedule.checkAndExecute Line:198 - 集群主控节点主机地址【10.92.82.161】运行端口【10031】,当前主机地址【10.87.57.73】运行端口【8092】, 无法执行定时任务【com.simbest.boot.component.task.LicenseCheckTask】【645】
2025-05-27 06:30:07.016 [pool-14-thread-9] INFO  com.simbest.boot.component.task.AbstractTaskSchedule.checkAndExecute Line:198 - 集群主控节点主机地址【10.92.82.161】运行端口【10031】,当前主机地址【10.87.57.73】运行端口【8092】, 无法执行定时任务【com.simbest.boot.component.task.LicenseCheckTask】【645】
2025-05-27 06:30:08.009 [pool-14-thread-9] INFO  com.simbest.boot.component.task.AbstractTaskSchedule.checkAndExecute Line:198 - 集群主控节点主机地址【10.92.82.161】运行端口【10031】,当前主机地址【10.87.57.73】运行端口【8092】, 无法执行定时任务【com.simbest.boot.component.task.LicenseCheckTask】【645】
2025-05-27 06:30:09.015 [pool-14-thread-9] INFO  com.simbest.boot.component.task.AbstractTaskSchedule.checkAndExecute Line:198 - 集群主控节点主机地址【10.92.82.161】运行端口【10031】,当前主机地址【10.87.57.73】运行端口【8092】, 无法执行定时任务【com.simbest.boot.component.task.LicenseCheckTask】【645】
2025-05-27 06:30:10.011 [pool-14-thread-9] INFO  com.simbest.boot.component.task.AbstractTaskSchedule.checkAndExecute Line:198 - 集群主控节点主机地址【10.92.82.161】运行端口【10031】,当前主机地址【10.87.57.73】运行端口【8092】, 无法执行定时任务【com.simbest.boot.component.task.LicenseCheckTask】【645】
2025-05-27 06:30:11.006 [pool-14-thread-9] INFO  com.simbest.boot.component.task.AbstractTaskSchedule.checkAndExecute Line:198 - 集群主控节点主机地址【10.92.82.161】运行端口【10031】,当前主机地址【10.87.57.73】运行端口【8092】, 无法执行定时任务【com.simbest.boot.component.task.LicenseCheckTask】【645】
2025-05-27 06:30:12.003 [pool-14-thread-9] INFO  com.simbest.boot.component.task.AbstractTaskSchedule.checkAndExecute Line:198 - 集群主控节点主机地址【10.92.82.161】运行端口【10031】,当前主机地址【10.87.57.73】运行端口【8092】, 无法执行定时任务【com.simbest.boot.component.task.LicenseCheckTask】【645】
2025-05-27 06:30:13.009 [pool-14-thread-9] INFO  com.simbest.boot.component.task.AbstractTaskSchedule.checkAndExecute Line:198 - 集群主控节点主机地址【10.92.82.161】运行端口【10031】,当前主机地址【10.87.57.73】运行端口【8092】, 无法执行定时任务【com.simbest.boot.component.task.LicenseCheckTask】【645】
2025-05-27 06:30:14.006 [pool-14-thread-9] INFO  com.simbest.boot.component.task.AbstractTaskSchedule.checkAndExecute Line:198 - 集群主控节点主机地址【10.92.82.161】运行端口【10031】,当前主机地址【10.87.57.73】运行端口【8092】, 无法执行定时任务【com.simbest.boot.component.task.LicenseCheckTask】【645】
2025-05-27 06:30:15.011 [pool-14-thread-9] INFO  com.simbest.boot.component.task.AbstractTaskSchedule.checkAndExecute Line:198 - 集群主控节点主机地址【10.92.82.161】运行端口【10031】,当前主机地址【10.87.57.73】运行端口【8092】, 无法执行定时任务【com.simbest.boot.component.task.LicenseCheckTask】【645】
2025-05-27 06:30:16.016 [pool-14-thread-10] INFO  com.simbest.boot.component.task.AbstractTaskSchedule.checkAndExecute Line:198 - 集群主控节点主机地址【10.92.82.161】运行端口【10031】,当前主机地址【10.87.57.73】运行端口【8092】, 无法执行定时任务【com.simbest.boot.component.task.LicenseCheckTask】【646】
2025-05-27 06:30:17.004 [pool-14-thread-6] INFO  com.simbest.boot.component.task.AbstractTaskSchedule.checkAndExecute Line:198 - 集群主控节点主机地址【10.92.82.161】运行端口【10031】,当前主机地址【10.87.57.73】运行端口【8092】, 无法执行定时任务【com.simbest.boot.component.task.LicenseCheckTask】【642】
2025-05-27 06:30:18.003 [pool-14-thread-6] INFO  com.simbest.boot.component.task.AbstractTaskSchedule.checkAndExecute Line:198 - 集群主控节点主机地址【10.92.82.161】运行端口【10031】,当前主机地址【10.87.57.73】运行端口【8092】, 无法执行定时任务【com.simbest.boot.component.task.LicenseCheckTask】【642】
2025-05-27 06:30:19.011 [pool-14-thread-6] INFO  com.simbest.boot.component.task.AbstractTaskSchedule.checkAndExecute Line:198 - 集群主控节点主机地址【10.92.82.161】运行端口【10031】,当前主机地址【10.87.57.73】运行端口【8092】, 无法执行定时任务【com.simbest.boot.component.task.LicenseCheckTask】【642】
2025-05-27 06:30:20.015 [pool-14-thread-6] INFO  com.simbest.boot.component.task.AbstractTaskSchedule.checkAndExecute Line:198 - 集群主控节点主机地址【10.92.82.161】运行端口【10031】,当前主机地址【10.87.57.73】运行端口【8092】, 无法执行定时任务【com.simbest.boot.component.task.LicenseCheckTask】【642】
2025-05-27 06:30:21.007 [pool-14-thread-6] INFO  com.simbest.boot.component.task.AbstractTaskSchedule.checkAndExecute Line:198 - 集群主控节点主机地址【10.92.82.161】运行端口【10031】,当前主机地址【10.87.57.73】运行端口【8092】, 无法执行定时任务【com.simbest.boot.component.task.LicenseCheckTask】【642】
2025-05-27 06:30:22.013 [pool-14-thread-6] INFO  com.simbest.boot.component.task.AbstractTaskSchedule.checkAndExecute Line:198 - 集群主控节点主机地址【10.92.82.161】运行端口【10031】,当前主机地址【10.87.57.73】运行端口【8092】, 无法执行定时任务【com.simbest.boot.component.task.LicenseCheckTask】【642】
2025-05-27 06:30:23.006 [pool-14-thread-6] INFO  com.simbest.boot.component.task.AbstractTaskSchedule.checkAndExecute Line:198 - 集群主控节点主机地址【10.92.82.161】运行端口【10031】,当前主机地址【10.87.57.73】运行端口【8092】, 无法执行定时任务【com.simbest.boot.component.task.LicenseCheckTask】【642】
2025-05-27 06:30:24.012 [pool-14-thread-6] INFO  com.simbest.boot.component.task.AbstractTaskSchedule.checkAndExecute Line:198 - 集群主控节点主机地址【10.92.82.161】运行端口【10031】,当前主机地址【10.87.57.73】运行端口【8092】, 无法执行定时任务【com.simbest.boot.component.task.LicenseCheckTask】【642】
2025-05-27 06:30:25.007 [pool-14-thread-6] INFO  com.simbest.boot.component.task.AbstractTaskSchedule.checkAndExecute Line:198 - 集群主控节点主机地址【10.92.82.161】运行端口【10031】,当前主机地址【10.87.57.73】运行端口【8092】, 无法执行定时任务【com.simbest.boot.component.task.LicenseCheckTask】【642】
2025-05-27 06:30:26.012 [pool-14-thread-10] INFO  com.simbest.boot.component.task.AbstractTaskSchedule.checkAndExecute Line:198 - 集群主控节点主机地址【10.92.82.161】运行端口【10031】,当前主机地址【10.87.57.73】运行端口【8092】, 无法执行定时任务【com.simbest.boot.component.task.LicenseCheckTask】【646】
2025-05-27 06:30:27.004 [pool-14-thread-5] INFO  com.simbest.boot.component.task.AbstractTaskSchedule.checkAndExecute Line:198 - 集群主控节点主机地址【10.92.82.161】运行端口【10031】,当前主机地址【10.87.57.73】运行端口【8092】, 无法执行定时任务【com.simbest.boot.component.task.LicenseCheckTask】【641】
2025-05-27 06:30:28.007 [pool-14-thread-5] INFO  com.simbest.boot.component.task.AbstractTaskSchedule.checkAndExecute Line:198 - 集群主控节点主机地址【10.92.82.161】运行端口【10031】,当前主机地址【10.87.57.73】运行端口【8092】, 无法执行定时任务【com.simbest.boot.component.task.LicenseCheckTask】【641】
2025-05-27 06:30:29.010 [pool-14-thread-5] INFO  com.simbest.boot.component.task.AbstractTaskSchedule.checkAndExecute Line:198 - 集群主控节点主机地址【10.92.82.161】运行端口【10031】,当前主机地址【10.87.57.73】运行端口【8092】, 无法执行定时任务【com.simbest.boot.component.task.LicenseCheckTask】【641】
2025-05-27 06:30:30.012 [pool-14-thread-5] INFO  com.simbest.boot.component.task.AbstractTaskSchedule.checkAndExecute Line:198 - 集群主控节点主机地址【10.92.82.161】运行端口【10031】,当前主机地址【10.87.57.73】运行端口【8092】, 无法执行定时任务【com.simbest.boot.component.task.LicenseCheckTask】【641】
2025-05-27 06:30:31.007 [pool-14-thread-3] INFO  com.simbest.boot.component.task.AbstractTaskSchedule.checkAndExecute Line:198 - 集群主控节点主机地址【10.92.82.161】运行端口【10031】,当前主机地址【10.87.57.73】运行端口【8092】, 无法执行定时任务【com.simbest.boot.component.task.LicenseCheckTask】【639】
2025-05-27 06:30:32.015 [pool-14-thread-3] INFO  com.simbest.boot.component.task.AbstractTaskSchedule.checkAndExecute Line:198 - 集群主控节点主机地址【10.92.82.161】运行端口【10031】,当前主机地址【10.87.57.73】运行端口【8092】, 无法执行定时任务【com.simbest.boot.component.task.LicenseCheckTask】【639】
2025-05-27 06:30:33.007 [pool-14-thread-4] INFO  com.simbest.boot.component.task.AbstractTaskSchedule.checkAndExecute Line:198 - 集群主控节点主机地址【10.92.82.161】运行端口【10031】,当前主机地址【10.87.57.73】运行端口【8092】, 无法执行定时任务【com.simbest.boot.component.task.LicenseCheckTask】【640】
2025-05-27 06:30:34.013 [pool-14-thread-4] INFO  com.simbest.boot.component.task.AbstractTaskSchedule.checkAndExecute Line:198 - 集群主控节点主机地址【10.92.82.161】运行端口【10031】,当前主机地址【10.87.57.73】运行端口【8092】, 无法执行定时任务【com.simbest.boot.component.task.LicenseCheckTask】【640】
2025-05-27 06:30:35.003 [pool-14-thread-6] INFO  com.simbest.boot.component.task.AbstractTaskSchedule.checkAndExecute Line:198 - 集群主控节点主机地址【10.92.82.161】运行端口【10031】,当前主机地址【10.87.57.73】运行端口【8092】, 无法执行定时任务【com.simbest.boot.component.task.LicenseCheckTask】【642】
2025-05-27 06:30:36.010 [pool-14-thread-6] INFO  com.simbest.boot.component.task.AbstractTaskSchedule.checkAndExecute Line:198 - 集群主控节点主机地址【10.92.82.161】运行端口【10031】,当前主机地址【10.87.57.73】运行端口【8092】, 无法执行定时任务【com.simbest.boot.component.task.LicenseCheckTask】【642】
2025-05-27 06:30:37.001 [pool-14-thread-6] INFO  com.simbest.boot.component.task.AbstractTaskSchedule.checkAndExecute Line:198 - 集群主控节点主机地址【10.92.82.161】运行端口【10031】,当前主机地址【10.87.57.73】运行端口【8092】, 无法执行定时任务【com.simbest.boot.component.task.LicenseCheckTask】【642】
2025-05-27 06:30:38.008 [pool-14-thread-6] INFO  com.simbest.boot.component.task.AbstractTaskSchedule.checkAndExecute Line:198 - 集群主控节点主机地址【10.92.82.161】运行端口【10031】,当前主机地址【10.87.57.73】运行端口【8092】, 无法执行定时任务【com.simbest.boot.component.task.LicenseCheckTask】【642】
2025-05-27 06:30:39.015 [pool-14-thread-6] INFO  com.simbest.boot.component.task.AbstractTaskSchedule.checkAndExecute Line:198 - 集群主控节点主机地址【10.92.82.161】运行端口【10031】,当前主机地址【10.87.57.73】运行端口【8092】, 无法执行定时任务【com.simbest.boot.component.task.LicenseCheckTask】【642】
2025-05-27 06:30:40.010 [pool-14-thread-5] INFO  com.simbest.boot.component.task.AbstractTaskSchedule.checkAndExecute Line:198 - 集群主控节点主机地址【10.92.82.161】运行端口【10031】,当前主机地址【10.87.57.73】运行端口【8092】, 无法执行定时任务【com.simbest.boot.component.task.LicenseCheckTask】【641】
2025-05-27 06:30:41.013 [pool-14-thread-2] INFO  com.simbest.boot.component.task.AbstractTaskSchedule.checkAndExecute Line:198 - 集群主控节点主机地址【10.92.82.161】运行端口【10031】,当前主机地址【10.87.57.73】运行端口【8092】, 无法执行定时任务【com.simbest.boot.component.task.LicenseCheckTask】【638】
2025-05-27 06:30:42.010 [pool-14-thread-2] INFO  com.simbest.boot.component.task.AbstractTaskSchedule.checkAndExecute Line:198 - 集群主控节点主机地址【10.92.82.161】运行端口【10031】,当前主机地址【10.87.57.73】运行端口【8092】, 无法执行定时任务【com.simbest.boot.component.task.LicenseCheckTask】【638】
2025-05-27 06:30:43.002 [pool-14-thread-2] INFO  com.simbest.boot.component.task.AbstractTaskSchedule.checkAndExecute Line:198 - 集群主控节点主机地址【10.92.82.161】运行端口【10031】,当前主机地址【10.87.57.73】运行端口【8092】, 无法执行定时任务【com.simbest.boot.component.task.LicenseCheckTask】【638】
2025-05-27 06:30:44.013 [pool-14-thread-2] INFO  com.simbest.boot.component.task.AbstractTaskSchedule.checkAndExecute Line:198 - 集群主控节点主机地址【10.92.82.161】运行端口【10031】,当前主机地址【10.87.57.73】运行端口【8092】, 无法执行定时任务【com.simbest.boot.component.task.LicenseCheckTask】【638】
2025-05-27 06:30:45.003 [pool-14-thread-2] INFO  com.simbest.boot.component.task.AbstractTaskSchedule.checkAndExecute Line:198 - 集群主控节点主机地址【10.92.82.161】运行端口【10031】,当前主机地址【10.87.57.73】运行端口【8092】, 无法执行定时任务【com.simbest.boot.component.task.LicenseCheckTask】【638】
2025-05-27 06:30:46.012 [pool-14-thread-8] INFO  com.simbest.boot.component.task.AbstractTaskSchedule.checkAndExecute Line:198 - 集群主控节点主机地址【10.92.82.161】运行端口【10031】,当前主机地址【10.87.57.73】运行端口【8092】, 无法执行定时任务【com.simbest.boot.component.task.LicenseCheckTask】【644】
2025-05-27 06:30:47.003 [pool-14-thread-8] INFO  com.simbest.boot.component.task.AbstractTaskSchedule.checkAndExecute Line:198 - 集群主控节点主机地址【10.92.82.161】运行端口【10031】,当前主机地址【10.87.57.73】运行端口【8092】, 无法执行定时任务【com.simbest.boot.component.task.LicenseCheckTask】【644】
2025-05-27 06:30:48.008 [pool-14-thread-8] INFO  com.simbest.boot.component.task.AbstractTaskSchedule.checkAndExecute Line:198 - 集群主控节点主机地址【10.92.82.161】运行端口【10031】,当前主机地址【10.87.57.73】运行端口【8092】, 无法执行定时任务【com.simbest.boot.component.task.LicenseCheckTask】【644】
2025-05-27 06:30:49.010 [pool-14-thread-8] INFO  com.simbest.boot.component.task.AbstractTaskSchedule.checkAndExecute Line:198 - 集群主控节点主机地址【10.92.82.161】运行端口【10031】,当前主机地址【10.87.57.73】运行端口【8092】, 无法执行定时任务【com.simbest.boot.component.task.LicenseCheckTask】【644】
2025-05-27 06:30:50.006 [pool-14-thread-8] INFO  com.simbest.boot.component.task.AbstractTaskSchedule.checkAndExecute Line:198 - 集群主控节点主机地址【10.92.82.161】运行端口【10031】,当前主机地址【10.87.57.73】运行端口【8092】, 无法执行定时任务【com.simbest.boot.component.task.LicenseCheckTask】【644】
2025-05-27 06:30:51.013 [pool-14-thread-8] INFO  com.simbest.boot.component.task.AbstractTaskSchedule.checkAndExecute Line:198 - 集群主控节点主机地址【10.92.82.161】运行端口【10031】,当前主机地址【10.87.57.73】运行端口【8092】, 无法执行定时任务【com.simbest.boot.component.task.LicenseCheckTask】【644】
2025-05-27 06:30:52.014 [pool-14-thread-8] INFO  com.simbest.boot.component.task.AbstractTaskSchedule.checkAndExecute Line:198 - 集群主控节点主机地址【10.92.82.161】运行端口【10031】,当前主机地址【10.87.57.73】运行端口【8092】, 无法执行定时任务【com.simbest.boot.component.task.LicenseCheckTask】【644】
2025-05-27 06:30:53.010 [pool-14-thread-8] INFO  com.simbest.boot.component.task.AbstractTaskSchedule.checkAndExecute Line:198 - 集群主控节点主机地址【10.92.82.161】运行端口【10031】,当前主机地址【10.87.57.73】运行端口【8092】, 无法执行定时任务【com.simbest.boot.component.task.LicenseCheckTask】【644】
2025-05-27 06:30:54.016 [pool-14-thread-8] INFO  com.simbest.boot.component.task.AbstractTaskSchedule.checkAndExecute Line:198 - 集群主控节点主机地址【10.92.82.161】运行端口【10031】,当前主机地址【10.87.57.73】运行端口【8092】, 无法执行定时任务【com.simbest.boot.component.task.LicenseCheckTask】【644】
2025-05-27 06:30:55.009 [pool-14-thread-8] INFO  com.simbest.boot.component.task.AbstractTaskSchedule.checkAndExecute Line:198 - 集群主控节点主机地址【10.92.82.161】运行端口【10031】,当前主机地址【10.87.57.73】运行端口【8092】, 无法执行定时任务【com.simbest.boot.component.task.LicenseCheckTask】【644】
2025-05-27 06:30:56.011 [pool-14-thread-8] INFO  com.simbest.boot.component.task.AbstractTaskSchedule.checkAndExecute Line:198 - 集群主控节点主机地址【10.92.82.161】运行端口【10031】,当前主机地址【10.87.57.73】运行端口【8092】, 无法执行定时任务【com.simbest.boot.component.task.LicenseCheckTask】【644】
2025-05-27 06:30:57.003 [pool-14-thread-8] INFO  com.simbest.boot.component.task.AbstractTaskSchedule.checkAndExecute Line:198 - 集群主控节点主机地址【10.92.82.161】运行端口【10031】,当前主机地址【10.87.57.73】运行端口【8092】, 无法执行定时任务【com.simbest.boot.component.task.LicenseCheckTask】【644】
2025-05-27 06:30:58.007 [pool-14-thread-8] INFO  com.simbest.boot.component.task.AbstractTaskSchedule.checkAndExecute Line:198 - 集群主控节点主机地址【10.92.82.161】运行端口【10031】,当前主机地址【10.87.57.73】运行端口【8092】, 无法执行定时任务【com.simbest.boot.component.task.LicenseCheckTask】【644】
2025-05-27 06:30:59.008 [pool-14-thread-8] INFO  com.simbest.boot.component.task.AbstractTaskSchedule.checkAndExecute Line:198 - 集群主控节点主机地址【10.92.82.161】运行端口【10031】,当前主机地址【10.87.57.73】运行端口【8092】, 无法执行定时任务【com.simbest.boot.component.task.LicenseCheckTask】【644】
2025-05-27 06:31:00.008 [pool-14-thread-3] INFO  com.simbest.boot.component.task.AbstractTaskSchedule.checkAndExecute Line:198 - 集群主控节点主机地址【10.92.82.161】运行端口【10031】,当前主机地址【10.87.57.73】运行端口【8092】, 无法执行定时任务【com.simbest.boot.component.task.HeartTestTask】【639】
2025-05-27 06:32:00.011 [pool-14-thread-1] INFO  com.simbest.boot.component.task.AbstractTaskSchedule.checkAndExecute Line:198 - 集群主控节点主机地址【10.92.82.161】运行端口【10031】,当前主机地址【10.87.57.73】运行端口【8092】, 无法执行定时任务【com.simbest.boot.component.task.HeartTestTask】【637】
2025-05-27 06:53:11.908 [pool-14-thread-4] INFO  com.simbest.boot.component.task.AbstractTaskSchedule.checkAndExecute Line:198 - 集群主控节点主机地址【10.92.82.161】运行端口【10031】,当前主机地址【10.87.57.73】运行端口【8092】, 无法执行定时任务【com.simbest.boot.processdata.pushWorkmanager.task.SyncTaskDataToWorkManagerTask】【640】
2025-05-27 06:53:11.908 [pool-14-thread-10] INFO  com.simbest.boot.component.task.AbstractTaskSchedule.checkAndExecute Line:198 - 集群主控节点主机地址【10.92.82.161】运行端口【10031】,当前主机地址【10.87.57.73】运行端口【8092】, 无法执行定时任务【com.simbest.boot.bps.task.AutoAddTodoTask】【646】
2025-05-27 06:53:11.908 [pool-14-thread-2] INFO  com.simbest.boot.component.task.AbstractTaskSchedule.checkAndExecute Line:198 - 集群主控节点主机地址【10.92.82.161】运行端口【10031】,当前主机地址【10.87.57.73】运行端口【8092】, 无法执行定时任务【com.simbest.boot.component.task.HeartTestTask】【638】
2025-05-27 06:53:11.908 [pool-14-thread-3] INFO  com.simbest.boot.component.task.AbstractTaskSchedule.checkAndExecute Line:198 - 集群主控节点主机地址【10.92.82.161】运行端口【10031】,当前主机地址【10.87.57.73】运行端口【8092】, 无法执行定时任务【com.simbest.boot.processdata.pushWorkmanager.task.SyncProcessDataToWorkManagerTask】【639】
2025-05-27 06:53:11.908 [pool-14-thread-1] INFO  com.simbest.boot.component.task.AbstractTaskSchedule.checkAndExecute Line:198 - 集群主控节点主机地址【10.92.82.161】运行端口【10031】,当前主机地址【10.87.57.73】运行端口【8092】, 无法执行定时任务【com.simbest.boot.bps.task.AutoCloseTodoTask】【637】
2025-05-27 06:53:11.909 [pool-14-thread-6] INFO  com.simbest.boot.component.task.AbstractTaskSchedule.checkAndExecute Line:198 - 集群主控节点主机地址【10.92.82.161】运行端口【10031】,当前主机地址【10.87.57.73】运行端口【8092】, 无法执行定时任务【com.simbest.boot.bps.task.AutoCheckWithUpdateTask】【642】
2025-05-27 06:53:11.908 [pool-14-thread-8] INFO  com.simbest.boot.component.task.AbstractTaskSchedule.checkAndExecute Line:198 - 集群主控节点主机地址【10.92.82.161】运行端口【10031】,当前主机地址【10.87.57.73】运行端口【8092】, 无法执行定时任务【com.simbest.boot.processdata.pushWorkmanager.task.SyncCommentDataToWorkManagerTask】【644】
2025-05-27 06:54:00.004 [pool-14-thread-5] INFO  com.simbest.boot.component.task.AbstractTaskSchedule.checkAndExecute Line:198 - 集群主控节点主机地址【10.92.82.161】运行端口【10031】,当前主机地址【10.87.57.73】运行端口【8092】, 无法执行定时任务【com.simbest.boot.component.task.HeartTestTask】【641】
2025-05-27 06:55:00.004 [pool-14-thread-10] INFO  com.simbest.boot.component.task.AbstractTaskSchedule.checkAndExecute Line:198 - 集群主控节点主机地址【10.92.82.161】运行端口【10031】,当前主机地址【10.87.57.73】运行端口【8092】, 无法执行定时任务【com.simbest.boot.processdata.pushWorkmanager.task.SyncCommentDataToWorkManagerTask】【646】
2025-05-27 06:55:00.004 [pool-14-thread-4] INFO  com.simbest.boot.component.task.AbstractTaskSchedule.checkAndExecute Line:198 - 集群主控节点主机地址【10.92.82.161】运行端口【10031】,当前主机地址【10.87.57.73】运行端口【8092】, 无法执行定时任务【com.simbest.boot.bps.task.AutoCloseTodoTask】【640】
2025-05-27 06:55:00.004 [pool-14-thread-6] INFO  com.simbest.boot.component.task.AbstractTaskSchedule.checkAndExecute Line:198 - 集群主控节点主机地址【10.92.82.161】运行端口【10031】,当前主机地址【10.87.57.73】运行端口【8092】, 无法执行定时任务【com.simbest.boot.bps.task.AutoAddTodoTask】【642】
2025-05-27 06:55:00.004 [pool-14-thread-7] INFO  com.simbest.boot.component.task.AbstractTaskSchedule.checkAndExecute Line:198 - 集群主控节点主机地址【10.92.82.161】运行端口【10031】,当前主机地址【10.87.57.73】运行端口【8092】, 无法执行定时任务【com.simbest.boot.processdata.pushWorkmanager.task.SyncTaskDataToWorkManagerTask】【643】
2025-05-27 06:55:00.004 [pool-14-thread-1] INFO  com.simbest.boot.component.task.AbstractTaskSchedule.checkAndExecute Line:198 - 集群主控节点主机地址【10.92.82.161】运行端口【10031】,当前主机地址【10.87.57.73】运行端口【8092】, 无法执行定时任务【com.simbest.boot.component.task.HeartTestTask】【637】
2025-05-27 06:55:00.004 [pool-14-thread-2] INFO  com.simbest.boot.component.task.AbstractTaskSchedule.checkAndExecute Line:198 - 集群主控节点主机地址【10.92.82.161】运行端口【10031】,当前主机地址【10.87.57.73】运行端口【8092】, 无法执行定时任务【com.simbest.boot.processdata.pushWorkmanager.task.SyncProcessDataToWorkManagerTask】【638】
2025-05-27 06:55:00.005 [pool-14-thread-10] INFO  com.simbest.boot.component.task.AbstractTaskSchedule.checkAndExecute Line:198 - 集群主控节点主机地址【10.92.82.161】运行端口【10031】,当前主机地址【10.87.57.73】运行端口【8092】, 无法执行定时任务【com.simbest.boot.bps.task.AutoCheckWithUpdateTask】【646】
2025-05-27 06:56:00.014 [pool-14-thread-1] INFO  com.simbest.boot.component.task.AbstractTaskSchedule.checkAndExecute Line:198 - 集群主控节点主机地址【10.92.82.161】运行端口【10031】,当前主机地址【10.87.57.73】运行端口【8092】, 无法执行定时任务【com.simbest.boot.component.task.HeartTestTask】【637】
2025-05-27 06:57:00.012 [pool-14-thread-8] INFO  com.simbest.boot.component.task.AbstractTaskSchedule.checkAndExecute Line:198 - 集群主控节点主机地址【10.92.82.161】运行端口【10031】,当前主机地址【10.87.57.73】运行端口【8092】, 无法执行定时任务【com.simbest.boot.component.task.HeartTestTask】【644】
2025-05-27 06:58:00.006 [pool-14-thread-8] INFO  com.simbest.boot.component.task.AbstractTaskSchedule.checkAndExecute Line:198 - 集群主控节点主机地址【10.92.82.161】运行端口【10031】,当前主机地址【10.87.57.73】运行端口【8092】, 无法执行定时任务【com.simbest.boot.component.task.HeartTestTask】【644】
2025-05-27 06:59:00.013 [pool-14-thread-9] INFO  com.simbest.boot.component.task.AbstractTaskSchedule.checkAndExecute Line:198 - 集群主控节点主机地址【10.92.82.161】运行端口【10031】,当前主机地址【10.87.57.73】运行端口【8092】, 无法执行定时任务【com.simbest.boot.component.task.HeartTestTask】【645】
2025-05-27 06:59:59.968 [pool-14-thread-6] INFO  com.simbest.boot.component.task.AbstractTaskSchedule.checkAndExecute Line:198 - 集群主控节点主机地址【10.92.82.161】运行端口【10031】,当前主机地址【10.87.57.73】运行端口【8092】, 无法执行定时任务【com.simbest.boot.cmcc.a4.task.A4TaskCreateXmlFile】【642】
2025-05-27 06:59:59.968 [pool-14-thread-10] INFO  com.simbest.boot.component.task.AbstractTaskSchedule.checkAndExecute Line:198 - 集群主控节点主机地址【10.92.82.161】运行端口【10031】,当前主机地址【10.87.57.73】运行端口【8092】, 无法执行定时任务【com.simbest.boot.component.task.LicenseCheckTask】【646】
2025-05-27 07:00:00.014 [pool-14-thread-6] INFO  com.simbest.boot.component.task.AbstractTaskSchedule.checkAndExecute Line:198 - 集群主控节点主机地址【10.92.82.161】运行端口【10031】,当前主机地址【10.87.57.73】运行端口【8092】, 无法执行定时任务【com.simbest.boot.bps.task.AutoCloseTodoTask】【642】
2025-05-27 07:00:00.014 [pool-14-thread-7] INFO  com.simbest.boot.component.task.AbstractTaskSchedule.checkAndExecute Line:198 - 集群主控节点主机地址【10.92.82.161】运行端口【10031】,当前主机地址【10.87.57.73】运行端口【8092】, 无法执行定时任务【com.simbest.boot.component.task.HeartTestTask】【643】
2025-05-27 07:00:00.014 [pool-14-thread-10] INFO  com.simbest.boot.component.task.AbstractTaskSchedule.checkAndExecute Line:198 - 集群主控节点主机地址【10.92.82.161】运行端口【10031】,当前主机地址【10.87.57.73】运行端口【8092】, 无法执行定时任务【com.simbest.boot.processdata.pushWorkmanager.task.SyncCommentDataToWorkManagerTask】【646】
2025-05-27 07:00:00.014 [pool-14-thread-1] INFO  com.simbest.boot.component.task.AbstractTaskSchedule.checkAndExecute Line:198 - 集群主控节点主机地址【10.92.82.161】运行端口【10031】,当前主机地址【10.87.57.73】运行端口【8092】, 无法执行定时任务【com.simbest.boot.bps.task.AutoAddTodoTask】【637】
2025-05-27 07:00:00.014 [pool-14-thread-8] INFO  com.simbest.boot.component.task.AbstractTaskSchedule.checkAndExecute Line:198 - 集群主控节点主机地址【10.92.82.161】运行端口【10031】,当前主机地址【10.87.57.73】运行端口【8092】, 无法执行定时任务【com.simbest.boot.processdata.pushWorkmanager.task.SyncProcessDataToWorkManagerTask】【644】
2025-05-27 07:00:00.014 [pool-14-thread-3] INFO  com.simbest.boot.component.task.AbstractTaskSchedule.checkAndExecute Line:198 - 集群主控节点主机地址【10.92.82.161】运行端口【10031】,当前主机地址【10.87.57.73】运行端口【8092】, 无法执行定时任务【com.simbest.boot.bps.task.AutoCheckWithUpdateTask】【639】
2025-05-27 07:00:00.014 [pool-14-thread-4] INFO  com.simbest.boot.component.task.AbstractTaskSchedule.checkAndExecute Line:198 - 集群主控节点主机地址【10.92.82.161】运行端口【10031】,当前主机地址【10.87.57.73】运行端口【8092】, 无法执行定时任务【com.simbest.boot.processdata.pushWorkmanager.task.SyncTaskDataToWorkManagerTask】【640】
2025-05-27 07:00:01.003 [pool-14-thread-7] INFO  com.simbest.boot.component.task.AbstractTaskSchedule.checkAndExecute Line:198 - 集群主控节点主机地址【10.92.82.161】运行端口【10031】,当前主机地址【10.87.57.73】运行端口【8092】, 无法执行定时任务【com.simbest.boot.component.task.LicenseCheckTask】【643】
2025-05-27 07:00:02.009 [pool-14-thread-7] INFO  com.simbest.boot.component.task.AbstractTaskSchedule.checkAndExecute Line:198 - 集群主控节点主机地址【10.92.82.161】运行端口【10031】,当前主机地址【10.87.57.73】运行端口【8092】, 无法执行定时任务【com.simbest.boot.component.task.LicenseCheckTask】【643】
2025-05-27 07:00:03.013 [pool-14-thread-7] INFO  com.simbest.boot.component.task.AbstractTaskSchedule.checkAndExecute Line:198 - 集群主控节点主机地址【10.92.82.161】运行端口【10031】,当前主机地址【10.87.57.73】运行端口【8092】, 无法执行定时任务【com.simbest.boot.component.task.LicenseCheckTask】【643】
2025-05-27 07:00:04.014 [pool-14-thread-7] INFO  com.simbest.boot.component.task.AbstractTaskSchedule.checkAndExecute Line:198 - 集群主控节点主机地址【10.92.82.161】运行端口【10031】,当前主机地址【10.87.57.73】运行端口【8092】, 无法执行定时任务【com.simbest.boot.component.task.LicenseCheckTask】【643】
2025-05-27 07:00:05.006 [pool-14-thread-7] INFO  com.simbest.boot.component.task.AbstractTaskSchedule.checkAndExecute Line:198 - 集群主控节点主机地址【10.92.82.161】运行端口【10031】,当前主机地址【10.87.57.73】运行端口【8092】, 无法执行定时任务【com.simbest.boot.component.task.LicenseCheckTask】【643】
2025-05-27 07:00:06.001 [pool-14-thread-7] INFO  com.simbest.boot.component.task.AbstractTaskSchedule.checkAndExecute Line:198 - 集群主控节点主机地址【10.92.82.161】运行端口【10031】,当前主机地址【10.87.57.73】运行端口【8092】, 无法执行定时任务【com.simbest.boot.component.task.LicenseCheckTask】【643】
2025-05-27 07:00:07.005 [pool-14-thread-7] INFO  com.simbest.boot.component.task.AbstractTaskSchedule.checkAndExecute Line:198 - 集群主控节点主机地址【10.92.82.161】运行端口【10031】,当前主机地址【10.87.57.73】运行端口【8092】, 无法执行定时任务【com.simbest.boot.component.task.LicenseCheckTask】【643】
2025-05-27 07:00:08.009 [pool-14-thread-7] INFO  com.simbest.boot.component.task.AbstractTaskSchedule.checkAndExecute Line:198 - 集群主控节点主机地址【10.92.82.161】运行端口【10031】,当前主机地址【10.87.57.73】运行端口【8092】, 无法执行定时任务【com.simbest.boot.component.task.LicenseCheckTask】【643】
2025-05-27 07:00:09.011 [pool-14-thread-7] INFO  com.simbest.boot.component.task.AbstractTaskSchedule.checkAndExecute Line:198 - 集群主控节点主机地址【10.92.82.161】运行端口【10031】,当前主机地址【10.87.57.73】运行端口【8092】, 无法执行定时任务【com.simbest.boot.component.task.LicenseCheckTask】【643】
2025-05-27 07:00:10.009 [pool-14-thread-7] INFO  com.simbest.boot.component.task.AbstractTaskSchedule.checkAndExecute Line:198 - 集群主控节点主机地址【10.92.82.161】运行端口【10031】,当前主机地址【10.87.57.73】运行端口【8092】, 无法执行定时任务【com.simbest.boot.component.task.LicenseCheckTask】【643】
2025-05-27 07:00:11.010 [pool-14-thread-8] INFO  com.simbest.boot.component.task.AbstractTaskSchedule.checkAndExecute Line:198 - 集群主控节点主机地址【10.92.82.161】运行端口【10031】,当前主机地址【10.87.57.73】运行端口【8092】, 无法执行定时任务【com.simbest.boot.component.task.LicenseCheckTask】【644】
2025-05-27 07:00:12.006 [pool-14-thread-8] INFO  com.simbest.boot.component.task.AbstractTaskSchedule.checkAndExecute Line:198 - 集群主控节点主机地址【10.92.82.161】运行端口【10031】,当前主机地址【10.87.57.73】运行端口【8092】, 无法执行定时任务【com.simbest.boot.component.task.LicenseCheckTask】【644】
2025-05-27 07:00:13.001 [pool-14-thread-8] INFO  com.simbest.boot.component.task.AbstractTaskSchedule.checkAndExecute Line:198 - 集群主控节点主机地址【10.92.82.161】运行端口【10031】,当前主机地址【10.87.57.73】运行端口【8092】, 无法执行定时任务【com.simbest.boot.component.task.LicenseCheckTask】【644】
2025-05-27 07:00:14.014 [pool-14-thread-8] INFO  com.simbest.boot.component.task.AbstractTaskSchedule.checkAndExecute Line:198 - 集群主控节点主机地址【10.92.82.161】运行端口【10031】,当前主机地址【10.87.57.73】运行端口【8092】, 无法执行定时任务【com.simbest.boot.component.task.LicenseCheckTask】【644】
2025-05-27 07:00:15.007 [pool-14-thread-8] INFO  com.simbest.boot.component.task.AbstractTaskSchedule.checkAndExecute Line:198 - 集群主控节点主机地址【10.92.82.161】运行端口【10031】,当前主机地址【10.87.57.73】运行端口【8092】, 无法执行定时任务【com.simbest.boot.component.task.LicenseCheckTask】【644】
2025-05-27 07:00:16.015 [pool-14-thread-3] INFO  com.simbest.boot.component.task.AbstractTaskSchedule.checkAndExecute Line:198 - 集群主控节点主机地址【10.92.82.161】运行端口【10031】,当前主机地址【10.87.57.73】运行端口【8092】, 无法执行定时任务【com.simbest.boot.component.task.LicenseCheckTask】【639】
2025-05-27 07:00:17.005 [pool-14-thread-9] INFO  com.simbest.boot.component.task.AbstractTaskSchedule.checkAndExecute Line:198 - 集群主控节点主机地址【10.92.82.161】运行端口【10031】,当前主机地址【10.87.57.73】运行端口【8092】, 无法执行定时任务【com.simbest.boot.component.task.LicenseCheckTask】【645】
2025-05-27 07:00:18.013 [pool-14-thread-9] INFO  com.simbest.boot.component.task.AbstractTaskSchedule.checkAndExecute Line:198 - 集群主控节点主机地址【10.92.82.161】运行端口【10031】,当前主机地址【10.87.57.73】运行端口【8092】, 无法执行定时任务【com.simbest.boot.component.task.LicenseCheckTask】【645】
2025-05-27 07:00:19.006 [pool-14-thread-9] INFO  com.simbest.boot.component.task.AbstractTaskSchedule.checkAndExecute Line:198 - 集群主控节点主机地址【10.92.82.161】运行端口【10031】,当前主机地址【10.87.57.73】运行端口【8092】, 无法执行定时任务【com.simbest.boot.component.task.LicenseCheckTask】【645】
2025-05-27 07:00:20.008 [pool-14-thread-9] INFO  com.simbest.boot.component.task.AbstractTaskSchedule.checkAndExecute Line:198 - 集群主控节点主机地址【10.92.82.161】运行端口【10031】,当前主机地址【10.87.57.73】运行端口【8092】, 无法执行定时任务【com.simbest.boot.component.task.LicenseCheckTask】【645】
2025-05-27 07:00:21.011 [pool-14-thread-9] INFO  com.simbest.boot.component.task.AbstractTaskSchedule.checkAndExecute Line:198 - 集群主控节点主机地址【10.92.82.161】运行端口【10031】,当前主机地址【10.87.57.73】运行端口【8092】, 无法执行定时任务【com.simbest.boot.component.task.LicenseCheckTask】【645】
2025-05-27 07:00:22.014 [pool-14-thread-2] INFO  com.simbest.boot.component.task.AbstractTaskSchedule.checkAndExecute Line:198 - 集群主控节点主机地址【10.92.82.161】运行端口【10031】,当前主机地址【10.87.57.73】运行端口【8092】, 无法执行定时任务【com.simbest.boot.component.task.LicenseCheckTask】【638】
2025-05-27 07:00:23.005 [pool-14-thread-10] INFO  com.simbest.boot.component.task.AbstractTaskSchedule.checkAndExecute Line:198 - 集群主控节点主机地址【10.92.82.161】运行端口【10031】,当前主机地址【10.87.57.73】运行端口【8092】, 无法执行定时任务【com.simbest.boot.component.task.LicenseCheckTask】【646】
2025-05-27 07:00:24.012 [pool-14-thread-10] INFO  com.simbest.boot.component.task.AbstractTaskSchedule.checkAndExecute Line:198 - 集群主控节点主机地址【10.92.82.161】运行端口【10031】,当前主机地址【10.87.57.73】运行端口【8092】, 无法执行定时任务【com.simbest.boot.component.task.LicenseCheckTask】【646】
2025-05-27 07:00:25.005 [pool-14-thread-10] INFO  com.simbest.boot.component.task.AbstractTaskSchedule.checkAndExecute Line:198 - 集群主控节点主机地址【10.92.82.161】运行端口【10031】,当前主机地址【10.87.57.73】运行端口【8092】, 无法执行定时任务【com.simbest.boot.component.task.LicenseCheckTask】【646】
2025-05-27 07:00:26.010 [pool-14-thread-10] INFO  com.simbest.boot.component.task.AbstractTaskSchedule.checkAndExecute Line:198 - 集群主控节点主机地址【10.92.82.161】运行端口【10031】,当前主机地址【10.87.57.73】运行端口【8092】, 无法执行定时任务【com.simbest.boot.component.task.LicenseCheckTask】【646】
2025-05-27 07:00:27.005 [pool-14-thread-10] INFO  com.simbest.boot.component.task.AbstractTaskSchedule.checkAndExecute Line:198 - 集群主控节点主机地址【10.92.82.161】运行端口【10031】,当前主机地址【10.87.57.73】运行端口【8092】, 无法执行定时任务【com.simbest.boot.component.task.LicenseCheckTask】【646】
2025-05-27 07:00:28.011 [pool-14-thread-10] INFO  com.simbest.boot.component.task.AbstractTaskSchedule.checkAndExecute Line:198 - 集群主控节点主机地址【10.92.82.161】运行端口【10031】,当前主机地址【10.87.57.73】运行端口【8092】, 无法执行定时任务【com.simbest.boot.component.task.LicenseCheckTask】【646】
2025-05-27 07:00:29.013 [pool-14-thread-10] INFO  com.simbest.boot.component.task.AbstractTaskSchedule.checkAndExecute Line:198 - 集群主控节点主机地址【10.92.82.161】运行端口【10031】,当前主机地址【10.87.57.73】运行端口【8092】, 无法执行定时任务【com.simbest.boot.component.task.LicenseCheckTask】【646】
2025-05-27 07:00:30.003 [pool-14-thread-5] INFO  com.simbest.boot.component.task.AbstractTaskSchedule.checkAndExecute Line:198 - 集群主控节点主机地址【10.92.82.161】运行端口【10031】,当前主机地址【10.87.57.73】运行端口【8092】, 无法执行定时任务【com.simbest.boot.component.task.LicenseCheckTask】【641】
2025-05-27 07:00:31.010 [pool-14-thread-9] INFO  com.simbest.boot.component.task.AbstractTaskSchedule.checkAndExecute Line:198 - 集群主控节点主机地址【10.92.82.161】运行端口【10031】,当前主机地址【10.87.57.73】运行端口【8092】, 无法执行定时任务【com.simbest.boot.component.task.LicenseCheckTask】【645】
2025-05-27 07:00:32.001 [pool-14-thread-9] INFO  com.simbest.boot.component.task.AbstractTaskSchedule.checkAndExecute Line:198 - 集群主控节点主机地址【10.92.82.161】运行端口【10031】,当前主机地址【10.87.57.73】运行端口【8092】, 无法执行定时任务【com.simbest.boot.component.task.LicenseCheckTask】【645】
2025-05-27 07:00:33.007 [pool-14-thread-7] INFO  com.simbest.boot.component.task.AbstractTaskSchedule.checkAndExecute Line:198 - 集群主控节点主机地址【10.92.82.161】运行端口【10031】,当前主机地址【10.87.57.73】运行端口【8092】, 无法执行定时任务【com.simbest.boot.component.task.LicenseCheckTask】【643】
2025-05-27 07:00:34.012 [pool-14-thread-7] INFO  com.simbest.boot.component.task.AbstractTaskSchedule.checkAndExecute Line:198 - 集群主控节点主机地址【10.92.82.161】运行端口【10031】,当前主机地址【10.87.57.73】运行端口【8092】, 无法执行定时任务【com.simbest.boot.component.task.LicenseCheckTask】【643】
2025-05-27 07:00:35.004 [pool-14-thread-7] INFO  com.simbest.boot.component.task.AbstractTaskSchedule.checkAndExecute Line:198 - 集群主控节点主机地址【10.92.82.161】运行端口【10031】,当前主机地址【10.87.57.73】运行端口【8092】, 无法执行定时任务【com.simbest.boot.component.task.LicenseCheckTask】【643】
2025-05-27 07:00:36.010 [pool-14-thread-7] INFO  com.simbest.boot.component.task.AbstractTaskSchedule.checkAndExecute Line:198 - 集群主控节点主机地址【10.92.82.161】运行端口【10031】,当前主机地址【10.87.57.73】运行端口【8092】, 无法执行定时任务【com.simbest.boot.component.task.LicenseCheckTask】【643】
2025-05-27 07:00:37.006 [pool-14-thread-7] INFO  com.simbest.boot.component.task.AbstractTaskSchedule.checkAndExecute Line:198 - 集群主控节点主机地址【10.92.82.161】运行端口【10031】,当前主机地址【10.87.57.73】运行端口【8092】, 无法执行定时任务【com.simbest.boot.component.task.LicenseCheckTask】【643】
2025-05-27 07:00:38.014 [pool-14-thread-7] INFO  com.simbest.boot.component.task.AbstractTaskSchedule.checkAndExecute Line:198 - 集群主控节点主机地址【10.92.82.161】运行端口【10031】,当前主机地址【10.87.57.73】运行端口【8092】, 无法执行定时任务【com.simbest.boot.component.task.LicenseCheckTask】【643】
2025-05-27 07:00:39.003 [pool-14-thread-7] INFO  com.simbest.boot.component.task.AbstractTaskSchedule.checkAndExecute Line:198 - 集群主控节点主机地址【10.92.82.161】运行端口【10031】,当前主机地址【10.87.57.73】运行端口【8092】, 无法执行定时任务【com.simbest.boot.component.task.LicenseCheckTask】【643】
2025-05-27 07:00:40.011 [pool-14-thread-7] INFO  com.simbest.boot.component.task.AbstractTaskSchedule.checkAndExecute Line:198 - 集群主控节点主机地址【10.92.82.161】运行端口【10031】,当前主机地址【10.87.57.73】运行端口【8092】, 无法执行定时任务【com.simbest.boot.component.task.LicenseCheckTask】【643】
2025-05-27 07:00:41.006 [pool-14-thread-7] INFO  com.simbest.boot.component.task.AbstractTaskSchedule.checkAndExecute Line:198 - 集群主控节点主机地址【10.92.82.161】运行端口【10031】,当前主机地址【10.87.57.73】运行端口【8092】, 无法执行定时任务【com.simbest.boot.component.task.LicenseCheckTask】【643】
2025-05-27 07:00:42.013 [pool-14-thread-7] INFO  com.simbest.boot.component.task.AbstractTaskSchedule.checkAndExecute Line:198 - 集群主控节点主机地址【10.92.82.161】运行端口【10031】,当前主机地址【10.87.57.73】运行端口【8092】, 无法执行定时任务【com.simbest.boot.component.task.LicenseCheckTask】【643】
2025-05-27 07:00:43.004 [pool-14-thread-7] INFO  com.simbest.boot.component.task.AbstractTaskSchedule.checkAndExecute Line:198 - 集群主控节点主机地址【10.92.82.161】运行端口【10031】,当前主机地址【10.87.57.73】运行端口【8092】, 无法执行定时任务【com.simbest.boot.component.task.LicenseCheckTask】【643】
2025-05-27 07:00:44.009 [pool-14-thread-7] INFO  com.simbest.boot.component.task.AbstractTaskSchedule.checkAndExecute Line:198 - 集群主控节点主机地址【10.92.82.161】运行端口【10031】,当前主机地址【10.87.57.73】运行端口【8092】, 无法执行定时任务【com.simbest.boot.component.task.LicenseCheckTask】【643】
2025-05-27 07:00:45.002 [pool-14-thread-7] INFO  com.simbest.boot.component.task.AbstractTaskSchedule.checkAndExecute Line:198 - 集群主控节点主机地址【10.92.82.161】运行端口【10031】,当前主机地址【10.87.57.73】运行端口【8092】, 无法执行定时任务【com.simbest.boot.component.task.LicenseCheckTask】【643】
2025-05-27 07:00:46.010 [pool-14-thread-7] INFO  com.simbest.boot.component.task.AbstractTaskSchedule.checkAndExecute Line:198 - 集群主控节点主机地址【10.92.82.161】运行端口【10031】,当前主机地址【10.87.57.73】运行端口【8092】, 无法执行定时任务【com.simbest.boot.component.task.LicenseCheckTask】【643】
2025-05-27 07:00:47.002 [pool-14-thread-7] INFO  com.simbest.boot.component.task.AbstractTaskSchedule.checkAndExecute Line:198 - 集群主控节点主机地址【10.92.82.161】运行端口【10031】,当前主机地址【10.87.57.73】运行端口【8092】, 无法执行定时任务【com.simbest.boot.component.task.LicenseCheckTask】【643】
2025-05-27 07:00:48.009 [pool-14-thread-10] INFO  com.simbest.boot.component.task.AbstractTaskSchedule.checkAndExecute Line:198 - 集群主控节点主机地址【10.92.82.161】运行端口【10031】,当前主机地址【10.87.57.73】运行端口【8092】, 无法执行定时任务【com.simbest.boot.component.task.LicenseCheckTask】【646】
2025-05-27 07:00:49.004 [pool-14-thread-10] INFO  com.simbest.boot.component.task.AbstractTaskSchedule.checkAndExecute Line:198 - 集群主控节点主机地址【10.92.82.161】运行端口【10031】,当前主机地址【10.87.57.73】运行端口【8092】, 无法执行定时任务【com.simbest.boot.component.task.LicenseCheckTask】【646】
2025-05-27 07:00:50.011 [pool-14-thread-10] INFO  com.simbest.boot.component.task.AbstractTaskSchedule.checkAndExecute Line:198 - 集群主控节点主机地址【10.92.82.161】运行端口【10031】,当前主机地址【10.87.57.73】运行端口【8092】, 无法执行定时任务【com.simbest.boot.component.task.LicenseCheckTask】【646】
2025-05-27 07:00:51.014 [pool-14-thread-10] INFO  com.simbest.boot.component.task.AbstractTaskSchedule.checkAndExecute Line:198 - 集群主控节点主机地址【10.92.82.161】运行端口【10031】,当前主机地址【10.87.57.73】运行端口【8092】, 无法执行定时任务【com.simbest.boot.component.task.LicenseCheckTask】【646】
2025-05-27 07:00:52.009 [pool-14-thread-10] INFO  com.simbest.boot.component.task.AbstractTaskSchedule.checkAndExecute Line:198 - 集群主控节点主机地址【10.92.82.161】运行端口【10031】,当前主机地址【10.87.57.73】运行端口【8092】, 无法执行定时任务【com.simbest.boot.component.task.LicenseCheckTask】【646】
2025-05-27 07:00:53.002 [pool-14-thread-10] INFO  com.simbest.boot.component.task.AbstractTaskSchedule.checkAndExecute Line:198 - 集群主控节点主机地址【10.92.82.161】运行端口【10031】,当前主机地址【10.87.57.73】运行端口【8092】, 无法执行定时任务【com.simbest.boot.component.task.LicenseCheckTask】【646】
2025-05-27 07:00:54.006 [pool-14-thread-10] INFO  com.simbest.boot.component.task.AbstractTaskSchedule.checkAndExecute Line:198 - 集群主控节点主机地址【10.92.82.161】运行端口【10031】,当前主机地址【10.87.57.73】运行端口【8092】, 无法执行定时任务【com.simbest.boot.component.task.LicenseCheckTask】【646】
2025-05-27 07:00:55.014 [pool-14-thread-1] INFO  com.simbest.boot.component.task.AbstractTaskSchedule.checkAndExecute Line:198 - 集群主控节点主机地址【10.92.82.161】运行端口【10031】,当前主机地址【10.87.57.73】运行端口【8092】, 无法执行定时任务【com.simbest.boot.component.task.LicenseCheckTask】【637】
2025-05-27 07:00:56.001 [pool-14-thread-1] INFO  com.simbest.boot.component.task.AbstractTaskSchedule.checkAndExecute Line:198 - 集群主控节点主机地址【10.92.82.161】运行端口【10031】,当前主机地址【10.87.57.73】运行端口【8092】, 无法执行定时任务【com.simbest.boot.component.task.LicenseCheckTask】【637】
2025-05-27 07:00:57.007 [pool-14-thread-1] INFO  com.simbest.boot.component.task.AbstractTaskSchedule.checkAndExecute Line:198 - 集群主控节点主机地址【10.92.82.161】运行端口【10031】,当前主机地址【10.87.57.73】运行端口【8092】, 无法执行定时任务【com.simbest.boot.component.task.LicenseCheckTask】【637】
2025-05-27 07:00:58.012 [pool-14-thread-1] INFO  com.simbest.boot.component.task.AbstractTaskSchedule.checkAndExecute Line:198 - 集群主控节点主机地址【10.92.82.161】运行端口【10031】,当前主机地址【10.87.57.73】运行端口【8092】, 无法执行定时任务【com.simbest.boot.component.task.LicenseCheckTask】【637】
2025-05-27 07:00:59.001 [pool-14-thread-1] INFO  com.simbest.boot.component.task.AbstractTaskSchedule.checkAndExecute Line:198 - 集群主控节点主机地址【10.92.82.161】运行端口【10031】,当前主机地址【10.87.57.73】运行端口【8092】, 无法执行定时任务【com.simbest.boot.component.task.LicenseCheckTask】【637】
2025-05-27 07:01:00.003 [pool-14-thread-3] INFO  com.simbest.boot.component.task.AbstractTaskSchedule.checkAndExecute Line:198 - 集群主控节点主机地址【10.92.82.161】运行端口【10031】,当前主机地址【10.87.57.73】运行端口【8092】, 无法执行定时任务【com.simbest.boot.component.task.HeartTestTask】【639】
2025-05-27 07:02:00.005 [pool-14-thread-3] INFO  com.simbest.boot.component.task.AbstractTaskSchedule.checkAndExecute Line:198 - 集群主控节点主机地址【10.92.82.161】运行端口【10031】,当前主机地址【10.87.57.73】运行端口【8092】, 无法执行定时任务【com.simbest.boot.component.task.HeartTestTask】【639】
2025-05-27 07:03:00.003 [pool-14-thread-3] INFO  com.simbest.boot.component.task.AbstractTaskSchedule.checkAndExecute Line:198 - 集群主控节点主机地址【10.92.82.161】运行端口【10031】,当前主机地址【10.87.57.73】运行端口【8092】, 无法执行定时任务【com.simbest.boot.component.task.HeartTestTask】【639】
2025-05-27 07:04:00.008 [pool-14-thread-5] INFO  com.simbest.boot.component.task.AbstractTaskSchedule.checkAndExecute Line:198 - 集群主控节点主机地址【10.92.82.161】运行端口【10031】,当前主机地址【10.87.57.73】运行端口【8092】, 无法执行定时任务【com.simbest.boot.component.task.HeartTestTask】【641】
2025-05-27 07:05:00.004 [pool-14-thread-5] INFO  com.simbest.boot.component.task.AbstractTaskSchedule.checkAndExecute Line:198 - 集群主控节点主机地址【10.92.82.161】运行端口【10031】,当前主机地址【10.87.57.73】运行端口【8092】, 无法执行定时任务【com.simbest.boot.bps.task.AutoAddTodoTask】【641】
2025-05-27 07:05:00.004 [pool-14-thread-9] INFO  com.simbest.boot.component.task.AbstractTaskSchedule.checkAndExecute Line:198 - 集群主控节点主机地址【10.92.82.161】运行端口【10031】,当前主机地址【10.87.57.73】运行端口【8092】, 无法执行定时任务【com.simbest.boot.processdata.pushWorkmanager.task.SyncProcessDataToWorkManagerTask】【645】
2025-05-27 07:05:00.004 [pool-14-thread-8] INFO  com.simbest.boot.component.task.AbstractTaskSchedule.checkAndExecute Line:198 - 集群主控节点主机地址【10.92.82.161】运行端口【10031】,当前主机地址【10.87.57.73】运行端口【8092】, 无法执行定时任务【com.simbest.boot.processdata.pushWorkmanager.task.SyncCommentDataToWorkManagerTask】【644】
2025-05-27 07:05:00.004 [pool-14-thread-2] INFO  com.simbest.boot.component.task.AbstractTaskSchedule.checkAndExecute Line:198 - 集群主控节点主机地址【10.92.82.161】运行端口【10031】,当前主机地址【10.87.57.73】运行端口【8092】, 无法执行定时任务【com.simbest.boot.bps.task.AutoCloseTodoTask】【638】
2025-05-27 07:05:00.004 [pool-14-thread-1] INFO  com.simbest.boot.component.task.AbstractTaskSchedule.checkAndExecute Line:198 - 集群主控节点主机地址【10.92.82.161】运行端口【10031】,当前主机地址【10.87.57.73】运行端口【8092】, 无法执行定时任务【com.simbest.boot.processdata.pushWorkmanager.task.SyncTaskDataToWorkManagerTask】【637】
2025-05-27 07:05:00.004 [pool-14-thread-10] INFO  com.simbest.boot.component.task.AbstractTaskSchedule.checkAndExecute Line:198 - 集群主控节点主机地址【10.92.82.161】运行端口【10031】,当前主机地址【10.87.57.73】运行端口【8092】, 无法执行定时任务【com.simbest.boot.bps.task.AutoCheckWithUpdateTask】【646】
2025-05-27 07:05:00.005 [pool-14-thread-6] INFO  com.simbest.boot.component.task.AbstractTaskSchedule.checkAndExecute Line:198 - 集群主控节点主机地址【10.92.82.161】运行端口【10031】,当前主机地址【10.87.57.73】运行端口【8092】, 无法执行定时任务【com.simbest.boot.component.task.HeartTestTask】【642】
2025-05-27 07:06:00.002 [pool-14-thread-6] INFO  com.simbest.boot.component.task.AbstractTaskSchedule.checkAndExecute Line:198 - 集群主控节点主机地址【10.92.82.161】运行端口【10031】,当前主机地址【10.87.57.73】运行端口【8092】, 无法执行定时任务【com.simbest.boot.component.task.HeartTestTask】【642】
2025-05-27 07:07:00.008 [pool-14-thread-6] INFO  com.simbest.boot.component.task.AbstractTaskSchedule.checkAndExecute Line:198 - 集群主控节点主机地址【10.92.82.161】运行端口【10031】,当前主机地址【10.87.57.73】运行端口【8092】, 无法执行定时任务【com.simbest.boot.component.task.HeartTestTask】【642】
2025-05-27 07:08:00.000 [pool-14-thread-3] INFO  com.simbest.boot.component.task.AbstractTaskSchedule.checkAndExecute Line:198 - 集群主控节点主机地址【10.92.82.161】运行端口【10031】,当前主机地址【10.87.57.73】运行端口【8092】, 无法执行定时任务【com.simbest.boot.component.task.HeartTestTask】【639】
2025-05-27 07:09:00.011 [pool-14-thread-8] INFO  com.simbest.boot.component.task.AbstractTaskSchedule.checkAndExecute Line:198 - 集群主控节点主机地址【10.92.82.161】运行端口【10031】,当前主机地址【10.87.57.73】运行端口【8092】, 无法执行定时任务【com.simbest.boot.component.task.HeartTestTask】【644】
2025-05-27 07:10:00.007 [pool-14-thread-1] INFO  com.simbest.boot.component.task.AbstractTaskSchedule.checkAndExecute Line:198 - 集群主控节点主机地址【10.92.82.161】运行端口【10031】,当前主机地址【10.87.57.73】运行端口【8092】, 无法执行定时任务【com.simbest.boot.bps.task.AutoCloseTodoTask】【637】
2025-05-27 07:10:00.008 [pool-14-thread-7] INFO  com.simbest.boot.component.task.AbstractTaskSchedule.checkAndExecute Line:198 - 集群主控节点主机地址【10.92.82.161】运行端口【10031】,当前主机地址【10.87.57.73】运行端口【8092】, 无法执行定时任务【com.simbest.boot.component.task.HeartTestTask】【643】
2025-05-27 07:10:00.008 [pool-14-thread-2] INFO  com.simbest.boot.component.task.AbstractTaskSchedule.checkAndExecute Line:198 - 集群主控节点主机地址【10.92.82.161】运行端口【10031】,当前主机地址【10.87.57.73】运行端口【8092】, 无法执行定时任务【com.simbest.boot.processdata.pushWorkmanager.task.SyncCommentDataToWorkManagerTask】【638】
2025-05-27 07:10:00.008 [pool-14-thread-8] INFO  com.simbest.boot.component.task.AbstractTaskSchedule.checkAndExecute Line:198 - 集群主控节点主机地址【10.92.82.161】运行端口【10031】,当前主机地址【10.87.57.73】运行端口【8092】, 无法执行定时任务【com.simbest.boot.processdata.pushWorkmanager.task.SyncProcessDataToWorkManagerTask】【644】
2025-05-27 07:10:00.008 [pool-14-thread-9] INFO  com.simbest.boot.component.task.AbstractTaskSchedule.checkAndExecute Line:198 - 集群主控节点主机地址【10.92.82.161】运行端口【10031】,当前主机地址【10.87.57.73】运行端口【8092】, 无法执行定时任务【com.simbest.boot.bps.task.AutoAddTodoTask】【645】
2025-05-27 07:10:00.008 [pool-14-thread-10] INFO  com.simbest.boot.component.task.AbstractTaskSchedule.checkAndExecute Line:198 - 集群主控节点主机地址【10.92.82.161】运行端口【10031】,当前主机地址【10.87.57.73】运行端口【8092】, 无法执行定时任务【com.simbest.boot.processdata.pushWorkmanager.task.SyncTaskDataToWorkManagerTask】【646】
2025-05-27 07:10:00.008 [pool-14-thread-6] INFO  com.simbest.boot.component.task.AbstractTaskSchedule.checkAndExecute Line:198 - 集群主控节点主机地址【10.92.82.161】运行端口【10031】,当前主机地址【10.87.57.73】运行端口【8092】, 无法执行定时任务【com.simbest.boot.bps.task.AutoCheckWithUpdateTask】【642】
2025-05-27 07:11:00.007 [pool-14-thread-8] INFO  com.simbest.boot.component.task.AbstractTaskSchedule.checkAndExecute Line:198 - 集群主控节点主机地址【10.92.82.161】运行端口【10031】,当前主机地址【10.87.57.73】运行端口【8092】, 无法执行定时任务【com.simbest.boot.component.task.HeartTestTask】【644】
2025-05-27 07:12:00.015 [pool-14-thread-8] INFO  com.simbest.boot.component.task.AbstractTaskSchedule.checkAndExecute Line:198 - 集群主控节点主机地址【10.92.82.161】运行端口【10031】,当前主机地址【10.87.57.73】运行端口【8092】, 无法执行定时任务【com.simbest.boot.component.task.HeartTestTask】【644】
2025-05-27 07:13:00.009 [pool-14-thread-2] INFO  com.simbest.boot.component.task.AbstractTaskSchedule.checkAndExecute Line:198 - 集群主控节点主机地址【10.92.82.161】运行端口【10031】,当前主机地址【10.87.57.73】运行端口【8092】, 无法执行定时任务【com.simbest.boot.component.task.HeartTestTask】【638】
2025-05-27 07:34:09.918 [pool-14-thread-5] INFO  com.simbest.boot.component.task.AbstractTaskSchedule.checkAndExecute Line:198 - 集群主控节点主机地址【10.92.82.161】运行端口【10031】,当前主机地址【10.87.57.73】运行端口【8092】, 无法执行定时任务【com.simbest.boot.processdata.pushWorkmanager.task.SyncProcessDataToWorkManagerTask】【641】
2025-05-27 07:34:09.918 [pool-14-thread-3] INFO  com.simbest.boot.component.task.AbstractTaskSchedule.checkAndExecute Line:198 - 集群主控节点主机地址【10.92.82.161】运行端口【10031】,当前主机地址【10.87.57.73】运行端口【8092】, 无法执行定时任务【com.simbest.boot.component.task.HeartTestTask】【639】
2025-05-27 07:34:09.919 [pool-14-thread-5] INFO  com.simbest.boot.component.task.AbstractTaskSchedule.checkAndExecute Line:198 - 集群主控节点主机地址【10.92.82.161】运行端口【10031】,当前主机地址【10.87.57.73】运行端口【8092】, 无法执行定时任务【com.simbest.boot.bps.task.AutoAddTodoTask】【641】
2025-05-27 07:34:09.919 [pool-14-thread-3] INFO  com.simbest.boot.component.task.AbstractTaskSchedule.checkAndExecute Line:198 - 集群主控节点主机地址【10.92.82.161】运行端口【10031】,当前主机地址【10.87.57.73】运行端口【8092】, 无法执行定时任务【com.simbest.boot.bps.task.AutoCheckWithUpdateTask】【639】
2025-05-27 07:34:09.919 [pool-14-thread-5] INFO  com.simbest.boot.component.task.AbstractTaskSchedule.checkAndExecute Line:198 - 集群主控节点主机地址【10.92.82.161】运行端口【10031】,当前主机地址【10.87.57.73】运行端口【8092】, 无法执行定时任务【com.simbest.boot.bps.task.AutoCloseTodoTask】【641】
2025-05-27 07:34:09.919 [pool-14-thread-9] INFO  com.simbest.boot.component.task.AbstractTaskSchedule.checkAndExecute Line:198 - 集群主控节点主机地址【10.92.82.161】运行端口【10031】,当前主机地址【10.87.57.73】运行端口【8092】, 无法执行定时任务【com.simbest.boot.processdata.pushWorkmanager.task.SyncCommentDataToWorkManagerTask】【645】
2025-05-27 07:34:09.919 [pool-14-thread-2] INFO  com.simbest.boot.component.task.AbstractTaskSchedule.checkAndExecute Line:198 - 集群主控节点主机地址【10.92.82.161】运行端口【10031】,当前主机地址【10.87.57.73】运行端口【8092】, 无法执行定时任务【com.simbest.boot.processdata.pushWorkmanager.task.SyncTaskDataToWorkManagerTask】【638】
2025-05-27 07:34:09.919 [pool-14-thread-6] INFO  com.simbest.boot.component.task.AbstractTaskSchedule.checkAndExecute Line:198 - 集群主控节点主机地址【10.92.82.161】运行端口【10031】,当前主机地址【10.87.57.73】运行端口【8092】, 无法执行定时任务【com.simbest.boot.component.task.LicenseCheckTask】【642】
2025-05-27 07:35:00.007 [pool-14-thread-6] INFO  com.simbest.boot.component.task.AbstractTaskSchedule.checkAndExecute Line:198 - 集群主控节点主机地址【10.92.82.161】运行端口【10031】,当前主机地址【10.87.57.73】运行端口【8092】, 无法执行定时任务【com.simbest.boot.bps.task.AutoAddTodoTask】【642】
2025-05-27 07:35:00.007 [pool-14-thread-7] INFO  com.simbest.boot.component.task.AbstractTaskSchedule.checkAndExecute Line:198 - 集群主控节点主机地址【10.92.82.161】运行端口【10031】,当前主机地址【10.87.57.73】运行端口【8092】, 无法执行定时任务【com.simbest.boot.processdata.pushWorkmanager.task.SyncTaskDataToWorkManagerTask】【643】
2025-05-27 07:35:00.007 [pool-14-thread-5] INFO  com.simbest.boot.component.task.AbstractTaskSchedule.checkAndExecute Line:198 - 集群主控节点主机地址【10.92.82.161】运行端口【10031】,当前主机地址【10.87.57.73】运行端口【8092】, 无法执行定时任务【com.simbest.boot.bps.task.AutoCloseTodoTask】【641】
2025-05-27 07:35:00.007 [pool-14-thread-9] INFO  com.simbest.boot.component.task.AbstractTaskSchedule.checkAndExecute Line:198 - 集群主控节点主机地址【10.92.82.161】运行端口【10031】,当前主机地址【10.87.57.73】运行端口【8092】, 无法执行定时任务【com.simbest.boot.processdata.pushWorkmanager.task.SyncCommentDataToWorkManagerTask】【645】
2025-05-27 07:35:00.007 [pool-14-thread-10] INFO  com.simbest.boot.component.task.AbstractTaskSchedule.checkAndExecute Line:198 - 集群主控节点主机地址【10.92.82.161】运行端口【10031】,当前主机地址【10.87.57.73】运行端口【8092】, 无法执行定时任务【com.simbest.boot.bps.task.AutoCheckWithUpdateTask】【646】
2025-05-27 07:35:00.007 [pool-14-thread-3] INFO  com.simbest.boot.component.task.AbstractTaskSchedule.checkAndExecute Line:198 - 集群主控节点主机地址【10.92.82.161】运行端口【10031】,当前主机地址【10.87.57.73】运行端口【8092】, 无法执行定时任务【com.simbest.boot.component.task.HeartTestTask】【639】
2025-05-27 07:35:00.007 [pool-14-thread-8] INFO  com.simbest.boot.component.task.AbstractTaskSchedule.checkAndExecute Line:198 - 集群主控节点主机地址【10.92.82.161】运行端口【10031】,当前主机地址【10.87.57.73】运行端口【8092】, 无法执行定时任务【com.simbest.boot.processdata.pushWorkmanager.task.SyncProcessDataToWorkManagerTask】【644】
2025-05-27 07:36:00.008 [pool-14-thread-3] INFO  com.simbest.boot.component.task.AbstractTaskSchedule.checkAndExecute Line:198 - 集群主控节点主机地址【10.92.82.161】运行端口【10031】,当前主机地址【10.87.57.73】运行端口【8092】, 无法执行定时任务【com.simbest.boot.component.task.HeartTestTask】【639】
2025-05-27 07:37:00.016 [pool-14-thread-4] INFO  com.simbest.boot.component.task.AbstractTaskSchedule.checkAndExecute Line:198 - 集群主控节点主机地址【10.92.82.161】运行端口【10031】,当前主机地址【10.87.57.73】运行端口【8092】, 无法执行定时任务【com.simbest.boot.component.task.HeartTestTask】【640】
2025-05-27 07:38:00.012 [pool-14-thread-8] INFO  com.simbest.boot.component.task.AbstractTaskSchedule.checkAndExecute Line:198 - 集群主控节点主机地址【10.92.82.161】运行端口【10031】,当前主机地址【10.87.57.73】运行端口【8092】, 无法执行定时任务【com.simbest.boot.component.task.HeartTestTask】【644】
2025-05-27 07:39:00.007 [pool-14-thread-5] INFO  com.simbest.boot.component.task.AbstractTaskSchedule.checkAndExecute Line:198 - 集群主控节点主机地址【10.92.82.161】运行端口【10031】,当前主机地址【10.87.57.73】运行端口【8092】, 无法执行定时任务【com.simbest.boot.component.task.HeartTestTask】【641】
2025-05-27 07:40:00.002 [pool-14-thread-8] INFO  com.simbest.boot.component.task.AbstractTaskSchedule.checkAndExecute Line:198 - 集群主控节点主机地址【10.92.82.161】运行端口【10031】,当前主机地址【10.87.57.73】运行端口【8092】, 无法执行定时任务【com.simbest.boot.processdata.pushWorkmanager.task.SyncProcessDataToWorkManagerTask】【644】
2025-05-27 07:40:00.002 [pool-14-thread-5] INFO  com.simbest.boot.component.task.AbstractTaskSchedule.checkAndExecute Line:198 - 集群主控节点主机地址【10.92.82.161】运行端口【10031】,当前主机地址【10.87.57.73】运行端口【8092】, 无法执行定时任务【com.simbest.boot.bps.task.AutoCloseTodoTask】【641】
2025-05-27 07:40:00.002 [pool-14-thread-6] INFO  com.simbest.boot.component.task.AbstractTaskSchedule.checkAndExecute Line:198 - 集群主控节点主机地址【10.92.82.161】运行端口【10031】,当前主机地址【10.87.57.73】运行端口【8092】, 无法执行定时任务【com.simbest.boot.component.task.HeartTestTask】【642】
2025-05-27 07:40:00.002 [pool-14-thread-7] INFO  com.simbest.boot.component.task.AbstractTaskSchedule.checkAndExecute Line:198 - 集群主控节点主机地址【10.92.82.161】运行端口【10031】,当前主机地址【10.87.57.73】运行端口【8092】, 无法执行定时任务【com.simbest.boot.processdata.pushWorkmanager.task.SyncTaskDataToWorkManagerTask】【643】
2025-05-27 07:40:00.002 [pool-14-thread-2] INFO  com.simbest.boot.component.task.AbstractTaskSchedule.checkAndExecute Line:198 - 集群主控节点主机地址【10.92.82.161】运行端口【10031】,当前主机地址【10.87.57.73】运行端口【8092】, 无法执行定时任务【com.simbest.boot.processdata.pushWorkmanager.task.SyncCommentDataToWorkManagerTask】【638】
2025-05-27 07:40:00.002 [pool-14-thread-9] INFO  com.simbest.boot.component.task.AbstractTaskSchedule.checkAndExecute Line:198 - 集群主控节点主机地址【10.92.82.161】运行端口【10031】,当前主机地址【10.87.57.73】运行端口【8092】, 无法执行定时任务【com.simbest.boot.bps.task.AutoAddTodoTask】【645】
2025-05-27 07:40:00.002 [pool-14-thread-3] INFO  com.simbest.boot.component.task.AbstractTaskSchedule.checkAndExecute Line:198 - 集群主控节点主机地址【10.92.82.161】运行端口【10031】,当前主机地址【10.87.57.73】运行端口【8092】, 无法执行定时任务【com.simbest.boot.bps.task.AutoCheckWithUpdateTask】【639】
2025-05-27 07:41:00.010 [pool-14-thread-10] INFO  com.simbest.boot.component.task.AbstractTaskSchedule.checkAndExecute Line:198 - 集群主控节点主机地址【10.92.82.161】运行端口【10031】,当前主机地址【10.87.57.73】运行端口【8092】, 无法执行定时任务【com.simbest.boot.component.task.HeartTestTask】【646】
2025-05-27 07:42:00.004 [pool-14-thread-4] INFO  com.simbest.boot.component.task.AbstractTaskSchedule.checkAndExecute Line:198 - 集群主控节点主机地址【10.92.82.161】运行端口【10031】,当前主机地址【10.87.57.73】运行端口【8092】, 无法执行定时任务【com.simbest.boot.component.task.HeartTestTask】【640】
2025-05-27 07:43:00.004 [pool-14-thread-3] INFO  com.simbest.boot.component.task.AbstractTaskSchedule.checkAndExecute Line:198 - 集群主控节点主机地址【10.92.82.161】运行端口【10031】,当前主机地址【10.87.57.73】运行端口【8092】, 无法执行定时任务【com.simbest.boot.component.task.HeartTestTask】【639】
2025-05-27 07:44:00.014 [pool-14-thread-2] INFO  com.simbest.boot.component.task.AbstractTaskSchedule.checkAndExecute Line:198 - 集群主控节点主机地址【10.92.82.161】运行端口【10031】,当前主机地址【10.87.57.73】运行端口【8092】, 无法执行定时任务【com.simbest.boot.component.task.HeartTestTask】【638】
2025-05-27 07:45:00.007 [pool-14-thread-2] INFO  com.simbest.boot.component.task.AbstractTaskSchedule.checkAndExecute Line:198 - 集群主控节点主机地址【10.92.82.161】运行端口【10031】,当前主机地址【10.87.57.73】运行端口【8092】, 无法执行定时任务【com.simbest.boot.bps.task.AutoCheckWithUpdateTask】【638】
2025-05-27 07:45:00.007 [pool-14-thread-3] INFO  com.simbest.boot.component.task.AbstractTaskSchedule.checkAndExecute Line:198 - 集群主控节点主机地址【10.92.82.161】运行端口【10031】,当前主机地址【10.87.57.73】运行端口【8092】, 无法执行定时任务【com.simbest.boot.bps.task.AutoAddTodoTask】【639】
2025-05-27 07:45:00.007 [pool-14-thread-4] INFO  com.simbest.boot.component.task.AbstractTaskSchedule.checkAndExecute Line:198 - 集群主控节点主机地址【10.92.82.161】运行端口【10031】,当前主机地址【10.87.57.73】运行端口【8092】, 无法执行定时任务【com.simbest.boot.processdata.pushWorkmanager.task.SyncTaskDataToWorkManagerTask】【640】
2025-05-27 07:45:00.007 [pool-14-thread-5] INFO  com.simbest.boot.component.task.AbstractTaskSchedule.checkAndExecute Line:198 - 集群主控节点主机地址【10.92.82.161】运行端口【10031】,当前主机地址【10.87.57.73】运行端口【8092】, 无法执行定时任务【com.simbest.boot.component.task.HeartTestTask】【641】
2025-05-27 07:45:00.007 [pool-14-thread-9] INFO  com.simbest.boot.component.task.AbstractTaskSchedule.checkAndExecute Line:198 - 集群主控节点主机地址【10.92.82.161】运行端口【10031】,当前主机地址【10.87.57.73】运行端口【8092】, 无法执行定时任务【com.simbest.boot.processdata.pushWorkmanager.task.SyncProcessDataToWorkManagerTask】【645】
2025-05-27 07:45:00.007 [pool-14-thread-8] INFO  com.simbest.boot.component.task.AbstractTaskSchedule.checkAndExecute Line:198 - 集群主控节点主机地址【10.92.82.161】运行端口【10031】,当前主机地址【10.87.57.73】运行端口【8092】, 无法执行定时任务【com.simbest.boot.processdata.pushWorkmanager.task.SyncCommentDataToWorkManagerTask】【644】
2025-05-27 07:45:00.010 [pool-14-thread-1] INFO  com.simbest.boot.component.task.AbstractTaskSchedule.checkAndExecute Line:198 - 集群主控节点主机地址【10.92.82.161】运行端口【10031】,当前主机地址【10.87.57.73】运行端口【8092】, 无法执行定时任务【com.simbest.boot.bps.task.AutoCloseTodoTask】【637】
2025-05-27 07:46:00.002 [pool-14-thread-5] INFO  com.simbest.boot.component.task.AbstractTaskSchedule.checkAndExecute Line:198 - 集群主控节点主机地址【10.92.82.161】运行端口【10031】,当前主机地址【10.87.57.73】运行端口【8092】, 无法执行定时任务【com.simbest.boot.component.task.HeartTestTask】【641】
2025-05-27 07:47:00.013 [pool-14-thread-4] INFO  com.simbest.boot.component.task.AbstractTaskSchedule.checkAndExecute Line:198 - 集群主控节点主机地址【10.92.82.161】运行端口【10031】,当前主机地址【10.87.57.73】运行端口【8092】, 无法执行定时任务【com.simbest.boot.component.task.HeartTestTask】【640】
2025-05-27 07:48:00.000 [pool-14-thread-4] INFO  com.simbest.boot.component.task.AbstractTaskSchedule.checkAndExecute Line:198 - 集群主控节点主机地址【10.92.82.161】运行端口【10031】,当前主机地址【10.87.57.73】运行端口【8092】, 无法执行定时任务【com.simbest.boot.component.task.HeartTestTask】【640】
2025-05-27 07:49:00.013 [pool-14-thread-10] INFO  com.simbest.boot.component.task.AbstractTaskSchedule.checkAndExecute Line:198 - 集群主控节点主机地址【10.92.82.161】运行端口【10031】,当前主机地址【10.87.57.73】运行端口【8092】, 无法执行定时任务【com.simbest.boot.component.task.HeartTestTask】【646】
2025-05-27 07:50:00.005 [pool-14-thread-7] INFO  com.simbest.boot.component.task.AbstractTaskSchedule.checkAndExecute Line:198 - 集群主控节点主机地址【10.92.82.161】运行端口【10031】,当前主机地址【10.87.57.73】运行端口【8092】, 无法执行定时任务【com.simbest.boot.processdata.pushWorkmanager.task.SyncTaskDataToWorkManagerTask】【643】
2025-05-27 07:50:00.006 [pool-14-thread-9] INFO  com.simbest.boot.component.task.AbstractTaskSchedule.checkAndExecute Line:198 - 集群主控节点主机地址【10.92.82.161】运行端口【10031】,当前主机地址【10.87.57.73】运行端口【8092】, 无法执行定时任务【com.simbest.boot.bps.task.AutoCloseTodoTask】【645】
2025-05-27 07:50:00.005 [pool-14-thread-1] INFO  com.simbest.boot.component.task.AbstractTaskSchedule.checkAndExecute Line:198 - 集群主控节点主机地址【10.92.82.161】运行端口【10031】,当前主机地址【10.87.57.73】运行端口【8092】, 无法执行定时任务【com.simbest.boot.bps.task.AutoCheckWithUpdateTask】【637】
2025-05-27 07:50:00.005 [pool-14-thread-6] INFO  com.simbest.boot.component.task.AbstractTaskSchedule.checkAndExecute Line:198 - 集群主控节点主机地址【10.92.82.161】运行端口【10031】,当前主机地址【10.87.57.73】运行端口【8092】, 无法执行定时任务【com.simbest.boot.processdata.pushWorkmanager.task.SyncCommentDataToWorkManagerTask】【642】
2025-05-27 07:50:00.005 [pool-14-thread-5] INFO  com.simbest.boot.component.task.AbstractTaskSchedule.checkAndExecute Line:198 - 集群主控节点主机地址【10.92.82.161】运行端口【10031】,当前主机地址【10.87.57.73】运行端口【8092】, 无法执行定时任务【com.simbest.boot.processdata.pushWorkmanager.task.SyncProcessDataToWorkManagerTask】【641】
2025-05-27 07:50:00.005 [pool-14-thread-4] INFO  com.simbest.boot.component.task.AbstractTaskSchedule.checkAndExecute Line:198 - 集群主控节点主机地址【10.92.82.161】运行端口【10031】,当前主机地址【10.87.57.73】运行端口【8092】, 无法执行定时任务【com.simbest.boot.bps.task.AutoAddTodoTask】【640】
2025-05-27 07:50:00.005 [pool-14-thread-8] INFO  com.simbest.boot.component.task.AbstractTaskSchedule.checkAndExecute Line:198 - 集群主控节点主机地址【10.92.82.161】运行端口【10031】,当前主机地址【10.87.57.73】运行端口【8092】, 无法执行定时任务【com.simbest.boot.component.task.HeartTestTask】【644】
2025-05-27 07:51:00.008 [pool-14-thread-1] INFO  com.simbest.boot.component.task.AbstractTaskSchedule.checkAndExecute Line:198 - 集群主控节点主机地址【10.92.82.161】运行端口【10031】,当前主机地址【10.87.57.73】运行端口【8092】, 无法执行定时任务【com.simbest.boot.component.task.HeartTestTask】【637】
2025-05-27 07:52:00.010 [pool-14-thread-8] INFO  com.simbest.boot.component.task.AbstractTaskSchedule.checkAndExecute Line:198 - 集群主控节点主机地址【10.92.82.161】运行端口【10031】,当前主机地址【10.87.57.73】运行端口【8092】, 无法执行定时任务【com.simbest.boot.component.task.HeartTestTask】【644】
2025-05-27 07:53:00.012 [pool-14-thread-2] INFO  com.simbest.boot.component.task.AbstractTaskSchedule.checkAndExecute Line:198 - 集群主控节点主机地址【10.92.82.161】运行端口【10031】,当前主机地址【10.87.57.73】运行端口【8092】, 无法执行定时任务【com.simbest.boot.component.task.HeartTestTask】【638】
2025-05-27 08:14:42.402 [pool-14-thread-6] INFO  com.simbest.boot.component.task.AbstractTaskSchedule.checkAndExecute Line:198 - 集群主控节点主机地址【10.92.82.161】运行端口【10031】,当前主机地址【10.87.57.73】运行端口【8092】, 无法执行定时任务【com.simbest.boot.bps.task.AutoCloseTodoTask】【642】
2025-05-27 08:14:42.402 [pool-14-thread-3] INFO  com.simbest.boot.component.task.AbstractTaskSchedule.checkAndExecute Line:198 - 集群主控节点主机地址【10.92.82.161】运行端口【10031】,当前主机地址【10.87.57.73】运行端口【8092】, 无法执行定时任务【com.simbest.boot.bps.task.AutoCheckWithUpdateTask】【639】
2025-05-27 08:14:42.403 [pool-14-thread-8] INFO  com.simbest.boot.component.task.AbstractTaskSchedule.checkAndExecute Line:198 - 集群主控节点主机地址【10.92.82.161】运行端口【10031】,当前主机地址【10.87.57.73】运行端口【8092】, 无法执行定时任务【com.simbest.boot.processdata.pushWorkmanager.task.SyncProcessDataToWorkManagerTask】【644】
2025-05-27 08:14:42.403 [pool-14-thread-6] INFO  com.simbest.boot.component.task.AbstractTaskSchedule.checkAndExecute Line:198 - 集群主控节点主机地址【10.92.82.161】运行端口【10031】,当前主机地址【10.87.57.73】运行端口【8092】, 无法执行定时任务【com.simbest.boot.component.task.LicenseCheckTask】【642】
2025-05-27 08:14:42.402 [pool-14-thread-9] INFO  com.simbest.boot.component.task.AbstractTaskSchedule.checkAndExecute Line:198 - 集群主控节点主机地址【10.92.82.161】运行端口【10031】,当前主机地址【10.87.57.73】运行端口【8092】, 无法执行定时任务【com.simbest.boot.processdata.pushWorkmanager.task.SyncCommentDataToWorkManagerTask】【645】
2025-05-27 08:14:42.402 [pool-14-thread-5] INFO  com.simbest.boot.component.task.AbstractTaskSchedule.checkAndExecute Line:198 - 集群主控节点主机地址【10.92.82.161】运行端口【10031】,当前主机地址【10.87.57.73】运行端口【8092】, 无法执行定时任务【com.simbest.boot.component.task.HeartTestTask】【641】
2025-05-27 08:14:42.403 [pool-14-thread-10] INFO  com.simbest.boot.component.task.AbstractTaskSchedule.checkAndExecute Line:198 - 集群主控节点主机地址【10.92.82.161】运行端口【10031】,当前主机地址【10.87.57.73】运行端口【8092】, 无法执行定时任务【com.simbest.boot.bps.task.AutoAddTodoTask】【646】
2025-05-27 08:14:42.403 [pool-14-thread-1] INFO  com.simbest.boot.component.task.AbstractTaskSchedule.checkAndExecute Line:198 - 集群主控节点主机地址【10.92.82.161】运行端口【10031】,当前主机地址【10.87.57.73】运行端口【8092】, 无法执行定时任务【com.simbest.boot.processdata.pushWorkmanager.task.SyncTaskDataToWorkManagerTask】【637】
2025-05-27 08:14:42.403 [pool-14-thread-3] INFO  com.simbest.boot.component.task.AbstractTaskSchedule.checkAndExecute Line:198 - 集群主控节点主机地址【10.92.82.161】运行端口【10031】,当前主机地址【10.87.57.73】运行端口【8092】, 无法执行定时任务【com.simbest.boot.cmcc.a4.task.A4TaskCreateXmlFile】【639】
2025-05-27 08:15:00.014 [pool-14-thread-9] INFO  com.simbest.boot.component.task.AbstractTaskSchedule.checkAndExecute Line:198 - 集群主控节点主机地址【10.92.82.161】运行端口【10031】,当前主机地址【10.87.57.73】运行端口【8092】, 无法执行定时任务【com.simbest.boot.bps.task.AutoCheckWithUpdateTask】【645】
2025-05-27 08:15:00.014 [pool-14-thread-4] INFO  com.simbest.boot.component.task.AbstractTaskSchedule.checkAndExecute Line:198 - 集群主控节点主机地址【10.92.82.161】运行端口【10031】,当前主机地址【10.87.57.73】运行端口【8092】, 无法执行定时任务【com.simbest.boot.processdata.pushWorkmanager.task.SyncTaskDataToWorkManagerTask】【640】
2025-05-27 08:15:00.014 [pool-14-thread-1] INFO  com.simbest.boot.component.task.AbstractTaskSchedule.checkAndExecute Line:198 - 集群主控节点主机地址【10.92.82.161】运行端口【10031】,当前主机地址【10.87.57.73】运行端口【8092】, 无法执行定时任务【com.simbest.boot.component.task.HeartTestTask】【637】
2025-05-27 08:15:00.014 [pool-14-thread-10] INFO  com.simbest.boot.component.task.AbstractTaskSchedule.checkAndExecute Line:198 - 集群主控节点主机地址【10.92.82.161】运行端口【10031】,当前主机地址【10.87.57.73】运行端口【8092】, 无法执行定时任务【com.simbest.boot.processdata.pushWorkmanager.task.SyncCommentDataToWorkManagerTask】【646】
2025-05-27 08:15:00.014 [pool-14-thread-6] INFO  com.simbest.boot.component.task.AbstractTaskSchedule.checkAndExecute Line:198 - 集群主控节点主机地址【10.92.82.161】运行端口【10031】,当前主机地址【10.87.57.73】运行端口【8092】, 无法执行定时任务【com.simbest.boot.bps.task.AutoCloseTodoTask】【642】
2025-05-27 08:15:00.014 [pool-14-thread-3] INFO  com.simbest.boot.component.task.AbstractTaskSchedule.checkAndExecute Line:198 - 集群主控节点主机地址【10.92.82.161】运行端口【10031】,当前主机地址【10.87.57.73】运行端口【8092】, 无法执行定时任务【com.simbest.boot.bps.task.AutoAddTodoTask】【639】
2025-05-27 08:15:00.014 [pool-14-thread-5] INFO  com.simbest.boot.component.task.AbstractTaskSchedule.checkAndExecute Line:198 - 集群主控节点主机地址【10.92.82.161】运行端口【10031】,当前主机地址【10.87.57.73】运行端口【8092】, 无法执行定时任务【com.simbest.boot.processdata.pushWorkmanager.task.SyncProcessDataToWorkManagerTask】【641】
2025-05-27 08:16:00.015 [pool-14-thread-9] INFO  com.simbest.boot.component.task.AbstractTaskSchedule.checkAndExecute Line:198 - 集群主控节点主机地址【10.92.82.161】运行端口【10031】,当前主机地址【10.87.57.73】运行端口【8092】, 无法执行定时任务【com.simbest.boot.component.task.HeartTestTask】【645】
2025-05-27 08:17:00.003 [pool-14-thread-10] INFO  com.simbest.boot.component.task.AbstractTaskSchedule.checkAndExecute Line:198 - 集群主控节点主机地址【10.92.82.161】运行端口【10031】,当前主机地址【10.87.57.73】运行端口【8092】, 无法执行定时任务【com.simbest.boot.component.task.HeartTestTask】【646】
2025-05-27 08:18:00.008 [pool-14-thread-7] INFO  com.simbest.boot.component.task.AbstractTaskSchedule.checkAndExecute Line:198 - 集群主控节点主机地址【10.92.82.161】运行端口【10031】,当前主机地址【10.87.57.73】运行端口【8092】, 无法执行定时任务【com.simbest.boot.component.task.HeartTestTask】【643】
2025-05-27 08:19:00.002 [pool-14-thread-2] INFO  com.simbest.boot.component.task.AbstractTaskSchedule.checkAndExecute Line:198 - 集群主控节点主机地址【10.92.82.161】运行端口【10031】,当前主机地址【10.87.57.73】运行端口【8092】, 无法执行定时任务【com.simbest.boot.component.task.HeartTestTask】【638】
2025-05-27 08:20:00.011 [pool-14-thread-1] INFO  com.simbest.boot.component.task.AbstractTaskSchedule.checkAndExecute Line:198 - 集群主控节点主机地址【10.92.82.161】运行端口【10031】,当前主机地址【10.87.57.73】运行端口【8092】, 无法执行定时任务【com.simbest.boot.processdata.pushWorkmanager.task.SyncCommentDataToWorkManagerTask】【637】
2025-05-27 08:20:00.011 [pool-14-thread-3] INFO  com.simbest.boot.component.task.AbstractTaskSchedule.checkAndExecute Line:198 - 集群主控节点主机地址【10.92.82.161】运行端口【10031】,当前主机地址【10.87.57.73】运行端口【8092】, 无法执行定时任务【com.simbest.boot.bps.task.AutoCheckWithUpdateTask】【639】
2025-05-27 08:20:00.013 [pool-14-thread-1] INFO  com.simbest.boot.component.task.AbstractTaskSchedule.checkAndExecute Line:198 - 集群主控节点主机地址【10.92.82.161】运行端口【10031】,当前主机地址【10.87.57.73】运行端口【8092】, 无法执行定时任务【com.simbest.boot.bps.task.AutoCloseTodoTask】【637】
2025-05-27 08:20:00.012 [pool-14-thread-4] INFO  com.simbest.boot.component.task.AbstractTaskSchedule.checkAndExecute Line:198 - 集群主控节点主机地址【10.92.82.161】运行端口【10031】,当前主机地址【10.87.57.73】运行端口【8092】, 无法执行定时任务【com.simbest.boot.processdata.pushWorkmanager.task.SyncProcessDataToWorkManagerTask】【640】
2025-05-27 08:20:00.013 [pool-14-thread-1] INFO  com.simbest.boot.component.task.AbstractTaskSchedule.checkAndExecute Line:198 - 集群主控节点主机地址【10.92.82.161】运行端口【10031】,当前主机地址【10.87.57.73】运行端口【8092】, 无法执行定时任务【com.simbest.boot.processdata.pushWorkmanager.task.SyncTaskDataToWorkManagerTask】【637】
2025-05-27 08:20:00.013 [pool-14-thread-4] INFO  com.simbest.boot.component.task.AbstractTaskSchedule.checkAndExecute Line:198 - 集群主控节点主机地址【10.92.82.161】运行端口【10031】,当前主机地址【10.87.57.73】运行端口【8092】, 无法执行定时任务【com.simbest.boot.bps.task.AutoAddTodoTask】【640】
2025-05-27 08:20:00.013 [pool-14-thread-2] INFO  com.simbest.boot.component.task.AbstractTaskSchedule.checkAndExecute Line:198 - 集群主控节点主机地址【10.92.82.161】运行端口【10031】,当前主机地址【10.87.57.73】运行端口【8092】, 无法执行定时任务【com.simbest.boot.component.task.HeartTestTask】【638】
2025-05-27 08:21:00.007 [pool-14-thread-8] INFO  com.simbest.boot.component.task.AbstractTaskSchedule.checkAndExecute Line:198 - 集群主控节点主机地址【10.92.82.161】运行端口【10031】,当前主机地址【10.87.57.73】运行端口【8092】, 无法执行定时任务【com.simbest.boot.component.task.HeartTestTask】【644】
2025-05-27 08:22:00.003 [pool-14-thread-10] INFO  com.simbest.boot.component.task.AbstractTaskSchedule.checkAndExecute Line:198 - 集群主控节点主机地址【10.92.82.161】运行端口【10031】,当前主机地址【10.87.57.73】运行端口【8092】, 无法执行定时任务【com.simbest.boot.component.task.HeartTestTask】【646】
2025-05-27 08:23:00.012 [pool-14-thread-7] INFO  com.simbest.boot.component.task.AbstractTaskSchedule.checkAndExecute Line:198 - 集群主控节点主机地址【10.92.82.161】运行端口【10031】,当前主机地址【10.87.57.73】运行端口【8092】, 无法执行定时任务【com.simbest.boot.component.task.HeartTestTask】【643】
2025-05-27 08:24:00.001 [pool-14-thread-10] INFO  com.simbest.boot.component.task.AbstractTaskSchedule.checkAndExecute Line:198 - 集群主控节点主机地址【10.92.82.161】运行端口【10031】,当前主机地址【10.87.57.73】运行端口【8092】, 无法执行定时任务【com.simbest.boot.component.task.HeartTestTask】【646】
2025-05-27 08:25:00.022 [pool-14-thread-1] INFO  com.simbest.boot.component.task.AbstractTaskSchedule.checkAndExecute Line:198 - 集群主控节点主机地址【10.92.82.161】运行端口【10031】,当前主机地址【10.87.57.73】运行端口【8092】, 无法执行定时任务【com.simbest.boot.bps.task.AutoCloseTodoTask】【637】
2025-05-27 08:25:00.022 [pool-14-thread-8] INFO  com.simbest.boot.component.task.AbstractTaskSchedule.checkAndExecute Line:198 - 集群主控节点主机地址【10.92.82.161】运行端口【10031】,当前主机地址【10.87.57.73】运行端口【8092】, 无法执行定时任务【com.simbest.boot.processdata.pushWorkmanager.task.SyncProcessDataToWorkManagerTask】【644】
2025-05-27 08:25:00.023 [pool-14-thread-1] INFO  com.simbest.boot.component.task.AbstractTaskSchedule.checkAndExecute Line:198 - 集群主控节点主机地址【10.92.82.161】运行端口【10031】,当前主机地址【10.87.57.73】运行端口【8092】, 无法执行定时任务【com.simbest.boot.processdata.pushWorkmanager.task.SyncTaskDataToWorkManagerTask】【637】
2025-05-27 08:25:00.023 [pool-14-thread-6] INFO  com.simbest.boot.component.task.AbstractTaskSchedule.checkAndExecute Line:198 - 集群主控节点主机地址【10.92.82.161】运行端口【10031】,当前主机地址【10.87.57.73】运行端口【8092】, 无法执行定时任务【com.simbest.boot.processdata.pushWorkmanager.task.SyncCommentDataToWorkManagerTask】【642】
2025-05-27 08:25:00.023 [pool-14-thread-6] INFO  com.simbest.boot.component.task.AbstractTaskSchedule.checkAndExecute Line:198 - 集群主控节点主机地址【10.92.82.161】运行端口【10031】,当前主机地址【10.87.57.73】运行端口【8092】, 无法执行定时任务【com.simbest.boot.bps.task.AutoAddTodoTask】【642】
2025-05-27 08:25:00.023 [pool-14-thread-6] INFO  com.simbest.boot.component.task.AbstractTaskSchedule.checkAndExecute Line:198 - 集群主控节点主机地址【10.92.82.161】运行端口【10031】,当前主机地址【10.87.57.73】运行端口【8092】, 无法执行定时任务【com.simbest.boot.bps.task.AutoCheckWithUpdateTask】【642】
2025-05-27 08:25:00.024 [pool-14-thread-6] INFO  com.simbest.boot.component.task.AbstractTaskSchedule.checkAndExecute Line:198 - 集群主控节点主机地址【10.92.82.161】运行端口【10031】,当前主机地址【10.87.57.73】运行端口【8092】, 无法执行定时任务【com.simbest.boot.component.task.HeartTestTask】【642】
2025-05-27 08:26:00.006 [pool-14-thread-6] INFO  com.simbest.boot.component.task.AbstractTaskSchedule.checkAndExecute Line:198 - 集群主控节点主机地址【10.92.82.161】运行端口【10031】,当前主机地址【10.87.57.73】运行端口【8092】, 无法执行定时任务【com.simbest.boot.component.task.HeartTestTask】【642】
2025-05-27 08:27:00.011 [pool-14-thread-5] INFO  com.simbest.boot.component.task.AbstractTaskSchedule.checkAndExecute Line:198 - 集群主控节点主机地址【10.92.82.161】运行端口【10031】,当前主机地址【10.87.57.73】运行端口【8092】, 无法执行定时任务【com.simbest.boot.component.task.HeartTestTask】【641】
2025-05-27 08:28:00.009 [pool-14-thread-5] INFO  com.simbest.boot.component.task.AbstractTaskSchedule.checkAndExecute Line:198 - 集群主控节点主机地址【10.92.82.161】运行端口【10031】,当前主机地址【10.87.57.73】运行端口【8092】, 无法执行定时任务【com.simbest.boot.component.task.HeartTestTask】【641】
2025-05-27 08:29:00.011 [pool-14-thread-5] INFO  com.simbest.boot.component.task.AbstractTaskSchedule.checkAndExecute Line:198 - 集群主控节点主机地址【10.92.82.161】运行端口【10031】,当前主机地址【10.87.57.73】运行端口【8092】, 无法执行定时任务【com.simbest.boot.component.task.HeartTestTask】【641】
2025-05-27 08:30:00.006 [pool-14-thread-8] INFO  com.simbest.boot.component.task.AbstractTaskSchedule.checkAndExecute Line:198 - 集群主控节点主机地址【10.92.82.161】运行端口【10031】,当前主机地址【10.87.57.73】运行端口【8092】, 无法执行定时任务【com.simbest.boot.bps.task.AutoCloseTodoTask】【644】
2025-05-27 08:30:00.006 [pool-14-thread-6] INFO  com.simbest.boot.component.task.AbstractTaskSchedule.checkAndExecute Line:198 - 集群主控节点主机地址【10.92.82.161】运行端口【10031】,当前主机地址【10.87.57.73】运行端口【8092】, 无法执行定时任务【com.simbest.boot.bps.task.AutoCheckWithUpdateTask】【642】
2025-05-27 08:30:00.006 [pool-14-thread-1] INFO  com.simbest.boot.component.task.AbstractTaskSchedule.checkAndExecute Line:198 - 集群主控节点主机地址【10.92.82.161】运行端口【10031】,当前主机地址【10.87.57.73】运行端口【8092】, 无法执行定时任务【com.simbest.boot.processdata.pushWorkmanager.task.SyncProcessDataToWorkManagerTask】【637】
2025-05-27 08:30:00.007 [pool-14-thread-10] INFO  com.simbest.boot.component.task.AbstractTaskSchedule.checkAndExecute Line:198 - 集群主控节点主机地址【10.92.82.161】运行端口【10031】,当前主机地址【10.87.57.73】运行端口【8092】, 无法执行定时任务【com.simbest.boot.processdata.pushWorkmanager.task.SyncTaskDataToWorkManagerTask】【646】
2025-05-27 08:30:00.007 [pool-14-thread-6] INFO  com.simbest.boot.component.task.AbstractTaskSchedule.checkAndExecute Line:198 - 集群主控节点主机地址【10.92.82.161】运行端口【10031】,当前主机地址【10.87.57.73】运行端口【8092】, 无法执行定时任务【com.simbest.boot.processdata.pushWorkmanager.task.SyncCommentDataToWorkManagerTask】【642】
2025-05-27 08:30:00.007 [pool-14-thread-2] INFO  com.simbest.boot.component.task.AbstractTaskSchedule.checkAndExecute Line:198 - 集群主控节点主机地址【10.92.82.161】运行端口【10031】,当前主机地址【10.87.57.73】运行端口【8092】, 无法执行定时任务【com.simbest.boot.bps.task.AutoAddTodoTask】【638】
2025-05-27 08:30:00.006 [pool-14-thread-9] INFO  com.simbest.boot.component.task.AbstractTaskSchedule.checkAndExecute Line:198 - 集群主控节点主机地址【10.92.82.161】运行端口【10031】,当前主机地址【10.87.57.73】运行端口【8092】, 无法执行定时任务【com.simbest.boot.component.task.LicenseCheckTask】【645】
2025-05-27 08:30:00.007 [pool-14-thread-1] INFO  com.simbest.boot.component.task.AbstractTaskSchedule.checkAndExecute Line:198 - 集群主控节点主机地址【10.92.82.161】运行端口【10031】,当前主机地址【10.87.57.73】运行端口【8092】, 无法执行定时任务【com.simbest.boot.component.task.HeartTestTask】【637】
2025-05-27 08:30:01.013 [pool-14-thread-9] INFO  com.simbest.boot.component.task.AbstractTaskSchedule.checkAndExecute Line:198 - 集群主控节点主机地址【10.92.82.161】运行端口【10031】,当前主机地址【10.87.57.73】运行端口【8092】, 无法执行定时任务【com.simbest.boot.component.task.LicenseCheckTask】【645】
2025-05-27 08:30:02.009 [pool-14-thread-9] INFO  com.simbest.boot.component.task.AbstractTaskSchedule.checkAndExecute Line:198 - 集群主控节点主机地址【10.92.82.161】运行端口【10031】,当前主机地址【10.87.57.73】运行端口【8092】, 无法执行定时任务【com.simbest.boot.component.task.LicenseCheckTask】【645】
2025-05-27 08:30:03.005 [pool-14-thread-9] INFO  com.simbest.boot.component.task.AbstractTaskSchedule.checkAndExecute Line:198 - 集群主控节点主机地址【10.92.82.161】运行端口【10031】,当前主机地址【10.87.57.73】运行端口【8092】, 无法执行定时任务【com.simbest.boot.component.task.LicenseCheckTask】【645】
2025-05-27 08:30:04.015 [pool-14-thread-9] INFO  com.simbest.boot.component.task.AbstractTaskSchedule.checkAndExecute Line:198 - 集群主控节点主机地址【10.92.82.161】运行端口【10031】,当前主机地址【10.87.57.73】运行端口【8092】, 无法执行定时任务【com.simbest.boot.component.task.LicenseCheckTask】【645】
2025-05-27 08:30:05.010 [pool-14-thread-8] INFO  com.simbest.boot.component.task.AbstractTaskSchedule.checkAndExecute Line:198 - 集群主控节点主机地址【10.92.82.161】运行端口【10031】,当前主机地址【10.87.57.73】运行端口【8092】, 无法执行定时任务【com.simbest.boot.component.task.LicenseCheckTask】【644】
2025-05-27 08:30:06.003 [pool-14-thread-8] INFO  com.simbest.boot.component.task.AbstractTaskSchedule.checkAndExecute Line:198 - 集群主控节点主机地址【10.92.82.161】运行端口【10031】,当前主机地址【10.87.57.73】运行端口【8092】, 无法执行定时任务【com.simbest.boot.component.task.LicenseCheckTask】【644】
2025-05-27 08:30:07.005 [pool-14-thread-8] INFO  com.simbest.boot.component.task.AbstractTaskSchedule.checkAndExecute Line:198 - 集群主控节点主机地址【10.92.82.161】运行端口【10031】,当前主机地址【10.87.57.73】运行端口【8092】, 无法执行定时任务【com.simbest.boot.component.task.LicenseCheckTask】【644】
2025-05-27 08:30:08.002 [pool-14-thread-8] INFO  com.simbest.boot.component.task.AbstractTaskSchedule.checkAndExecute Line:198 - 集群主控节点主机地址【10.92.82.161】运行端口【10031】,当前主机地址【10.87.57.73】运行端口【8092】, 无法执行定时任务【com.simbest.boot.component.task.LicenseCheckTask】【644】
2025-05-27 08:30:09.012 [pool-14-thread-8] INFO  com.simbest.boot.component.task.AbstractTaskSchedule.checkAndExecute Line:198 - 集群主控节点主机地址【10.92.82.161】运行端口【10031】,当前主机地址【10.87.57.73】运行端口【8092】, 无法执行定时任务【com.simbest.boot.component.task.LicenseCheckTask】【644】
2025-05-27 08:30:10.007 [pool-14-thread-8] INFO  com.simbest.boot.component.task.AbstractTaskSchedule.checkAndExecute Line:198 - 集群主控节点主机地址【10.92.82.161】运行端口【10031】,当前主机地址【10.87.57.73】运行端口【8092】, 无法执行定时任务【com.simbest.boot.component.task.LicenseCheckTask】【644】
2025-05-27 08:30:11.012 [pool-14-thread-8] INFO  com.simbest.boot.component.task.AbstractTaskSchedule.checkAndExecute Line:198 - 集群主控节点主机地址【10.92.82.161】运行端口【10031】,当前主机地址【10.87.57.73】运行端口【8092】, 无法执行定时任务【com.simbest.boot.component.task.LicenseCheckTask】【644】
2025-05-27 08:30:12.004 [pool-14-thread-8] INFO  com.simbest.boot.component.task.AbstractTaskSchedule.checkAndExecute Line:198 - 集群主控节点主机地址【10.92.82.161】运行端口【10031】,当前主机地址【10.87.57.73】运行端口【8092】, 无法执行定时任务【com.simbest.boot.component.task.LicenseCheckTask】【644】
2025-05-27 08:30:13.011 [pool-14-thread-8] INFO  com.simbest.boot.component.task.AbstractTaskSchedule.checkAndExecute Line:198 - 集群主控节点主机地址【10.92.82.161】运行端口【10031】,当前主机地址【10.87.57.73】运行端口【8092】, 无法执行定时任务【com.simbest.boot.component.task.LicenseCheckTask】【644】
2025-05-27 08:30:14.004 [pool-14-thread-9] INFO  com.simbest.boot.component.task.AbstractTaskSchedule.checkAndExecute Line:198 - 集群主控节点主机地址【10.92.82.161】运行端口【10031】,当前主机地址【10.87.57.73】运行端口【8092】, 无法执行定时任务【com.simbest.boot.component.task.LicenseCheckTask】【645】
2025-05-27 08:30:15.007 [pool-14-thread-9] INFO  com.simbest.boot.component.task.AbstractTaskSchedule.checkAndExecute Line:198 - 集群主控节点主机地址【10.92.82.161】运行端口【10031】,当前主机地址【10.87.57.73】运行端口【8092】, 无法执行定时任务【com.simbest.boot.component.task.LicenseCheckTask】【645】
2025-05-27 08:30:16.004 [pool-14-thread-9] INFO  com.simbest.boot.component.task.AbstractTaskSchedule.checkAndExecute Line:198 - 集群主控节点主机地址【10.92.82.161】运行端口【10031】,当前主机地址【10.87.57.73】运行端口【8092】, 无法执行定时任务【com.simbest.boot.component.task.LicenseCheckTask】【645】
2025-05-27 08:30:17.004 [pool-14-thread-9] INFO  com.simbest.boot.component.task.AbstractTaskSchedule.checkAndExecute Line:198 - 集群主控节点主机地址【10.92.82.161】运行端口【10031】,当前主机地址【10.87.57.73】运行端口【8092】, 无法执行定时任务【com.simbest.boot.component.task.LicenseCheckTask】【645】
2025-05-27 08:30:18.010 [pool-14-thread-9] INFO  com.simbest.boot.component.task.AbstractTaskSchedule.checkAndExecute Line:198 - 集群主控节点主机地址【10.92.82.161】运行端口【10031】,当前主机地址【10.87.57.73】运行端口【8092】, 无法执行定时任务【com.simbest.boot.component.task.LicenseCheckTask】【645】
2025-05-27 08:30:19.015 [pool-14-thread-9] INFO  com.simbest.boot.component.task.AbstractTaskSchedule.checkAndExecute Line:198 - 集群主控节点主机地址【10.92.82.161】运行端口【10031】,当前主机地址【10.87.57.73】运行端口【8092】, 无法执行定时任务【com.simbest.boot.component.task.LicenseCheckTask】【645】
2025-05-27 08:30:20.008 [pool-14-thread-10] INFO  com.simbest.boot.component.task.AbstractTaskSchedule.checkAndExecute Line:198 - 集群主控节点主机地址【10.92.82.161】运行端口【10031】,当前主机地址【10.87.57.73】运行端口【8092】, 无法执行定时任务【com.simbest.boot.component.task.LicenseCheckTask】【646】
2025-05-27 08:30:21.001 [pool-14-thread-10] INFO  com.simbest.boot.component.task.AbstractTaskSchedule.checkAndExecute Line:198 - 集群主控节点主机地址【10.92.82.161】运行端口【10031】,当前主机地址【10.87.57.73】运行端口【8092】, 无法执行定时任务【com.simbest.boot.component.task.LicenseCheckTask】【646】
2025-05-27 08:30:22.008 [pool-14-thread-10] INFO  com.simbest.boot.component.task.AbstractTaskSchedule.checkAndExecute Line:198 - 集群主控节点主机地址【10.92.82.161】运行端口【10031】,当前主机地址【10.87.57.73】运行端口【8092】, 无法执行定时任务【com.simbest.boot.component.task.LicenseCheckTask】【646】
2025-05-27 08:30:23.014 [pool-14-thread-10] INFO  com.simbest.boot.component.task.AbstractTaskSchedule.checkAndExecute Line:198 - 集群主控节点主机地址【10.92.82.161】运行端口【10031】,当前主机地址【10.87.57.73】运行端口【8092】, 无法执行定时任务【com.simbest.boot.component.task.LicenseCheckTask】【646】
2025-05-27 08:30:24.004 [pool-14-thread-10] INFO  com.simbest.boot.component.task.AbstractTaskSchedule.checkAndExecute Line:198 - 集群主控节点主机地址【10.92.82.161】运行端口【10031】,当前主机地址【10.87.57.73】运行端口【8092】, 无法执行定时任务【com.simbest.boot.component.task.LicenseCheckTask】【646】
2025-05-27 08:30:25.014 [pool-14-thread-10] INFO  com.simbest.boot.component.task.AbstractTaskSchedule.checkAndExecute Line:198 - 集群主控节点主机地址【10.92.82.161】运行端口【10031】,当前主机地址【10.87.57.73】运行端口【8092】, 无法执行定时任务【com.simbest.boot.component.task.LicenseCheckTask】【646】
2025-05-27 08:30:26.005 [pool-14-thread-10] INFO  com.simbest.boot.component.task.AbstractTaskSchedule.checkAndExecute Line:198 - 集群主控节点主机地址【10.92.82.161】运行端口【10031】,当前主机地址【10.87.57.73】运行端口【8092】, 无法执行定时任务【com.simbest.boot.component.task.LicenseCheckTask】【646】
2025-05-27 08:30:27.013 [pool-14-thread-10] INFO  com.simbest.boot.component.task.AbstractTaskSchedule.checkAndExecute Line:198 - 集群主控节点主机地址【10.92.82.161】运行端口【10031】,当前主机地址【10.87.57.73】运行端口【8092】, 无法执行定时任务【com.simbest.boot.component.task.LicenseCheckTask】【646】
2025-05-27 08:30:28.008 [pool-14-thread-10] INFO  com.simbest.boot.component.task.AbstractTaskSchedule.checkAndExecute Line:198 - 集群主控节点主机地址【10.92.82.161】运行端口【10031】,当前主机地址【10.87.57.73】运行端口【8092】, 无法执行定时任务【com.simbest.boot.component.task.LicenseCheckTask】【646】
2025-05-27 08:30:29.001 [pool-14-thread-10] INFO  com.simbest.boot.component.task.AbstractTaskSchedule.checkAndExecute Line:198 - 集群主控节点主机地址【10.92.82.161】运行端口【10031】,当前主机地址【10.87.57.73】运行端口【8092】, 无法执行定时任务【com.simbest.boot.component.task.LicenseCheckTask】【646】
2025-05-27 08:30:30.012 [pool-14-thread-10] INFO  com.simbest.boot.component.task.AbstractTaskSchedule.checkAndExecute Line:198 - 集群主控节点主机地址【10.92.82.161】运行端口【10031】,当前主机地址【10.87.57.73】运行端口【8092】, 无法执行定时任务【com.simbest.boot.component.task.LicenseCheckTask】【646】
2025-05-27 08:30:31.007 [pool-14-thread-10] INFO  com.simbest.boot.component.task.AbstractTaskSchedule.checkAndExecute Line:198 - 集群主控节点主机地址【10.92.82.161】运行端口【10031】,当前主机地址【10.87.57.73】运行端口【8092】, 无法执行定时任务【com.simbest.boot.component.task.LicenseCheckTask】【646】
2025-05-27 08:30:32.013 [pool-14-thread-10] INFO  com.simbest.boot.component.task.AbstractTaskSchedule.checkAndExecute Line:198 - 集群主控节点主机地址【10.92.82.161】运行端口【10031】,当前主机地址【10.87.57.73】运行端口【8092】, 无法执行定时任务【com.simbest.boot.component.task.LicenseCheckTask】【646】
2025-05-27 08:30:33.007 [pool-14-thread-10] INFO  com.simbest.boot.component.task.AbstractTaskSchedule.checkAndExecute Line:198 - 集群主控节点主机地址【10.92.82.161】运行端口【10031】,当前主机地址【10.87.57.73】运行端口【8092】, 无法执行定时任务【com.simbest.boot.component.task.LicenseCheckTask】【646】
2025-05-27 08:30:34.011 [pool-14-thread-10] INFO  com.simbest.boot.component.task.AbstractTaskSchedule.checkAndExecute Line:198 - 集群主控节点主机地址【10.92.82.161】运行端口【10031】,当前主机地址【10.87.57.73】运行端口【8092】, 无法执行定时任务【com.simbest.boot.component.task.LicenseCheckTask】【646】
2025-05-27 08:30:35.000 [pool-14-thread-10] INFO  com.simbest.boot.component.task.AbstractTaskSchedule.checkAndExecute Line:198 - 集群主控节点主机地址【10.92.82.161】运行端口【10031】,当前主机地址【10.87.57.73】运行端口【8092】, 无法执行定时任务【com.simbest.boot.component.task.LicenseCheckTask】【646】
2025-05-27 08:30:36.006 [pool-14-thread-10] INFO  com.simbest.boot.component.task.AbstractTaskSchedule.checkAndExecute Line:198 - 集群主控节点主机地址【10.92.82.161】运行端口【10031】,当前主机地址【10.87.57.73】运行端口【8092】, 无法执行定时任务【com.simbest.boot.component.task.LicenseCheckTask】【646】
2025-05-27 08:30:37.011 [pool-14-thread-5] INFO  com.simbest.boot.component.task.AbstractTaskSchedule.checkAndExecute Line:198 - 集群主控节点主机地址【10.92.82.161】运行端口【10031】,当前主机地址【10.87.57.73】运行端口【8092】, 无法执行定时任务【com.simbest.boot.component.task.LicenseCheckTask】【641】
2025-05-27 08:30:38.007 [pool-14-thread-5] INFO  com.simbest.boot.component.task.AbstractTaskSchedule.checkAndExecute Line:198 - 集群主控节点主机地址【10.92.82.161】运行端口【10031】,当前主机地址【10.87.57.73】运行端口【8092】, 无法执行定时任务【com.simbest.boot.component.task.LicenseCheckTask】【641】
2025-05-27 08:30:39.015 [pool-14-thread-5] INFO  com.simbest.boot.component.task.AbstractTaskSchedule.checkAndExecute Line:198 - 集群主控节点主机地址【10.92.82.161】运行端口【10031】,当前主机地址【10.87.57.73】运行端口【8092】, 无法执行定时任务【com.simbest.boot.component.task.LicenseCheckTask】【641】
2025-05-27 08:30:40.010 [pool-14-thread-5] INFO  com.simbest.boot.component.task.AbstractTaskSchedule.checkAndExecute Line:198 - 集群主控节点主机地址【10.92.82.161】运行端口【10031】,当前主机地址【10.87.57.73】运行端口【8092】, 无法执行定时任务【com.simbest.boot.component.task.LicenseCheckTask】【641】
2025-05-27 08:30:41.003 [pool-14-thread-5] INFO  com.simbest.boot.component.task.AbstractTaskSchedule.checkAndExecute Line:198 - 集群主控节点主机地址【10.92.82.161】运行端口【10031】,当前主机地址【10.87.57.73】运行端口【8092】, 无法执行定时任务【com.simbest.boot.component.task.LicenseCheckTask】【641】
2025-05-27 08:30:42.005 [pool-14-thread-5] INFO  com.simbest.boot.component.task.AbstractTaskSchedule.checkAndExecute Line:198 - 集群主控节点主机地址【10.92.82.161】运行端口【10031】,当前主机地址【10.87.57.73】运行端口【8092】, 无法执行定时任务【com.simbest.boot.component.task.LicenseCheckTask】【641】
2025-05-27 08:30:43.015 [pool-14-thread-5] INFO  com.simbest.boot.component.task.AbstractTaskSchedule.checkAndExecute Line:198 - 集群主控节点主机地址【10.92.82.161】运行端口【10031】,当前主机地址【10.87.57.73】运行端口【8092】, 无法执行定时任务【com.simbest.boot.component.task.LicenseCheckTask】【641】
2025-05-27 08:30:44.006 [pool-14-thread-5] INFO  com.simbest.boot.component.task.AbstractTaskSchedule.checkAndExecute Line:198 - 集群主控节点主机地址【10.92.82.161】运行端口【10031】,当前主机地址【10.87.57.73】运行端口【8092】, 无法执行定时任务【com.simbest.boot.component.task.LicenseCheckTask】【641】
2025-05-27 08:30:45.013 [pool-14-thread-5] INFO  com.simbest.boot.component.task.AbstractTaskSchedule.checkAndExecute Line:198 - 集群主控节点主机地址【10.92.82.161】运行端口【10031】,当前主机地址【10.87.57.73】运行端口【8092】, 无法执行定时任务【com.simbest.boot.component.task.LicenseCheckTask】【641】
2025-05-27 08:30:46.006 [pool-14-thread-10] INFO  com.simbest.boot.component.task.AbstractTaskSchedule.checkAndExecute Line:198 - 集群主控节点主机地址【10.92.82.161】运行端口【10031】,当前主机地址【10.87.57.73】运行端口【8092】, 无法执行定时任务【com.simbest.boot.component.task.LicenseCheckTask】【646】
2025-05-27 08:30:47.014 [pool-14-thread-10] INFO  com.simbest.boot.component.task.AbstractTaskSchedule.checkAndExecute Line:198 - 集群主控节点主机地址【10.92.82.161】运行端口【10031】,当前主机地址【10.87.57.73】运行端口【8092】, 无法执行定时任务【com.simbest.boot.component.task.LicenseCheckTask】【646】
2025-05-27 08:30:48.012 [pool-14-thread-10] INFO  com.simbest.boot.component.task.AbstractTaskSchedule.checkAndExecute Line:198 - 集群主控节点主机地址【10.92.82.161】运行端口【10031】,当前主机地址【10.87.57.73】运行端口【8092】, 无法执行定时任务【com.simbest.boot.component.task.LicenseCheckTask】【646】
2025-05-27 08:30:49.015 [pool-14-thread-10] INFO  com.simbest.boot.component.task.AbstractTaskSchedule.checkAndExecute Line:198 - 集群主控节点主机地址【10.92.82.161】运行端口【10031】,当前主机地址【10.87.57.73】运行端口【8092】, 无法执行定时任务【com.simbest.boot.component.task.LicenseCheckTask】【646】
2025-05-27 08:30:50.007 [pool-14-thread-10] INFO  com.simbest.boot.component.task.AbstractTaskSchedule.checkAndExecute Line:198 - 集群主控节点主机地址【10.92.82.161】运行端口【10031】,当前主机地址【10.87.57.73】运行端口【8092】, 无法执行定时任务【com.simbest.boot.component.task.LicenseCheckTask】【646】
2025-05-27 08:30:51.014 [pool-14-thread-10] INFO  com.simbest.boot.component.task.AbstractTaskSchedule.checkAndExecute Line:198 - 集群主控节点主机地址【10.92.82.161】运行端口【10031】,当前主机地址【10.87.57.73】运行端口【8092】, 无法执行定时任务【com.simbest.boot.component.task.LicenseCheckTask】【646】
2025-05-27 08:30:52.011 [pool-14-thread-10] INFO  com.simbest.boot.component.task.AbstractTaskSchedule.checkAndExecute Line:198 - 集群主控节点主机地址【10.92.82.161】运行端口【10031】,当前主机地址【10.87.57.73】运行端口【8092】, 无法执行定时任务【com.simbest.boot.component.task.LicenseCheckTask】【646】
2025-05-27 08:30:53.006 [pool-14-thread-10] INFO  com.simbest.boot.component.task.AbstractTaskSchedule.checkAndExecute Line:198 - 集群主控节点主机地址【10.92.82.161】运行端口【10031】,当前主机地址【10.87.57.73】运行端口【8092】, 无法执行定时任务【com.simbest.boot.component.task.LicenseCheckTask】【646】
2025-05-27 08:30:54.002 [pool-14-thread-10] INFO  com.simbest.boot.component.task.AbstractTaskSchedule.checkAndExecute Line:198 - 集群主控节点主机地址【10.92.82.161】运行端口【10031】,当前主机地址【10.87.57.73】运行端口【8092】, 无法执行定时任务【com.simbest.boot.component.task.LicenseCheckTask】【646】
2025-05-27 08:30:55.011 [pool-14-thread-10] INFO  com.simbest.boot.component.task.AbstractTaskSchedule.checkAndExecute Line:198 - 集群主控节点主机地址【10.92.82.161】运行端口【10031】,当前主机地址【10.87.57.73】运行端口【8092】, 无法执行定时任务【com.simbest.boot.component.task.LicenseCheckTask】【646】
2025-05-27 08:30:56.005 [pool-14-thread-10] INFO  com.simbest.boot.component.task.AbstractTaskSchedule.checkAndExecute Line:198 - 集群主控节点主机地址【10.92.82.161】运行端口【10031】,当前主机地址【10.87.57.73】运行端口【8092】, 无法执行定时任务【com.simbest.boot.component.task.LicenseCheckTask】【646】
2025-05-27 08:30:57.015 [pool-14-thread-10] INFO  com.simbest.boot.component.task.AbstractTaskSchedule.checkAndExecute Line:198 - 集群主控节点主机地址【10.92.82.161】运行端口【10031】,当前主机地址【10.87.57.73】运行端口【8092】, 无法执行定时任务【com.simbest.boot.component.task.LicenseCheckTask】【646】
2025-05-27 08:30:58.007 [pool-14-thread-10] INFO  com.simbest.boot.component.task.AbstractTaskSchedule.checkAndExecute Line:198 - 集群主控节点主机地址【10.92.82.161】运行端口【10031】,当前主机地址【10.87.57.73】运行端口【8092】, 无法执行定时任务【com.simbest.boot.component.task.LicenseCheckTask】【646】
2025-05-27 08:30:59.015 [pool-14-thread-10] INFO  com.simbest.boot.component.task.AbstractTaskSchedule.checkAndExecute Line:198 - 集群主控节点主机地址【10.92.82.161】运行端口【10031】,当前主机地址【10.87.57.73】运行端口【8092】, 无法执行定时任务【com.simbest.boot.component.task.LicenseCheckTask】【646】
2025-05-27 08:31:00.009 [pool-14-thread-5] INFO  com.simbest.boot.component.task.AbstractTaskSchedule.checkAndExecute Line:198 - 集群主控节点主机地址【10.92.82.161】运行端口【10031】,当前主机地址【10.87.57.73】运行端口【8092】, 无法执行定时任务【com.simbest.boot.component.task.HeartTestTask】【641】
2025-05-27 08:32:00.012 [pool-14-thread-4] INFO  com.simbest.boot.component.task.AbstractTaskSchedule.checkAndExecute Line:198 - 集群主控节点主机地址【10.92.82.161】运行端口【10031】,当前主机地址【10.87.57.73】运行端口【8092】, 无法执行定时任务【com.simbest.boot.component.task.HeartTestTask】【640】
2025-05-27 08:33:00.006 [pool-14-thread-4] INFO  com.simbest.boot.component.task.AbstractTaskSchedule.checkAndExecute Line:198 - 集群主控节点主机地址【10.92.82.161】运行端口【10031】,当前主机地址【10.87.57.73】运行端口【8092】, 无法执行定时任务【com.simbest.boot.component.task.HeartTestTask】【640】
2025-05-27 08:34:00.008 [pool-14-thread-8] INFO  com.simbest.boot.component.task.AbstractTaskSchedule.checkAndExecute Line:198 - 集群主控节点主机地址【10.92.82.161】运行端口【10031】,当前主机地址【10.87.57.73】运行端口【8092】, 无法执行定时任务【com.simbest.boot.component.task.HeartTestTask】【644】
2025-05-27 08:35:00.011 [pool-14-thread-4] INFO  com.simbest.boot.component.task.AbstractTaskSchedule.checkAndExecute Line:198 - 集群主控节点主机地址【10.92.82.161】运行端口【10031】,当前主机地址【10.87.57.73】运行端口【8092】, 无法执行定时任务【com.simbest.boot.bps.task.AutoCloseTodoTask】【640】
2025-05-27 08:35:00.011 [pool-14-thread-8] INFO  com.simbest.boot.component.task.AbstractTaskSchedule.checkAndExecute Line:198 - 集群主控节点主机地址【10.92.82.161】运行端口【10031】,当前主机地址【10.87.57.73】运行端口【8092】, 无法执行定时任务【com.simbest.boot.processdata.pushWorkmanager.task.SyncCommentDataToWorkManagerTask】【644】
2025-05-27 08:35:00.012 [pool-14-thread-5] INFO  com.simbest.boot.component.task.AbstractTaskSchedule.checkAndExecute Line:198 - 集群主控节点主机地址【10.92.82.161】运行端口【10031】,当前主机地址【10.87.57.73】运行端口【8092】, 无法执行定时任务【com.simbest.boot.bps.task.AutoCheckWithUpdateTask】【641】
2025-05-27 08:35:00.011 [pool-14-thread-3] INFO  com.simbest.boot.component.task.AbstractTaskSchedule.checkAndExecute Line:198 - 集群主控节点主机地址【10.92.82.161】运行端口【10031】,当前主机地址【10.87.57.73】运行端口【8092】, 无法执行定时任务【com.simbest.boot.processdata.pushWorkmanager.task.SyncProcessDataToWorkManagerTask】【639】
2025-05-27 08:35:00.011 [pool-14-thread-10] INFO  com.simbest.boot.component.task.AbstractTaskSchedule.checkAndExecute Line:198 - 集群主控节点主机地址【10.92.82.161】运行端口【10031】,当前主机地址【10.87.57.73】运行端口【8092】, 无法执行定时任务【com.simbest.boot.processdata.pushWorkmanager.task.SyncTaskDataToWorkManagerTask】【646】
2025-05-27 08:35:00.012 [pool-14-thread-6] INFO  com.simbest.boot.component.task.AbstractTaskSchedule.checkAndExecute Line:198 - 集群主控节点主机地址【10.92.82.161】运行端口【10031】,当前主机地址【10.87.57.73】运行端口【8092】, 无法执行定时任务【com.simbest.boot.bps.task.AutoAddTodoTask】【642】
2025-05-27 08:35:00.012 [pool-14-thread-9] INFO  com.simbest.boot.component.task.AbstractTaskSchedule.checkAndExecute Line:198 - 集群主控节点主机地址【10.92.82.161】运行端口【10031】,当前主机地址【10.87.57.73】运行端口【8092】, 无法执行定时任务【com.simbest.boot.component.task.HeartTestTask】【645】
2025-05-27 08:36:00.003 [pool-14-thread-9] INFO  com.simbest.boot.component.task.AbstractTaskSchedule.checkAndExecute Line:198 - 集群主控节点主机地址【10.92.82.161】运行端口【10031】,当前主机地址【10.87.57.73】运行端口【8092】, 无法执行定时任务【com.simbest.boot.component.task.HeartTestTask】【645】
2025-05-27 08:37:00.006 [pool-14-thread-7] INFO  com.simbest.boot.component.task.AbstractTaskSchedule.checkAndExecute Line:198 - 集群主控节点主机地址【10.92.82.161】运行端口【10031】,当前主机地址【10.87.57.73】运行端口【8092】, 无法执行定时任务【com.simbest.boot.component.task.HeartTestTask】【643】
2025-05-27 08:38:00.009 [pool-14-thread-7] INFO  com.simbest.boot.component.task.AbstractTaskSchedule.checkAndExecute Line:198 - 集群主控节点主机地址【10.92.82.161】运行端口【10031】,当前主机地址【10.87.57.73】运行端口【8092】, 无法执行定时任务【com.simbest.boot.component.task.HeartTestTask】【643】
2025-05-27 08:39:00.006 [pool-14-thread-2] INFO  com.simbest.boot.component.task.AbstractTaskSchedule.checkAndExecute Line:198 - 集群主控节点主机地址【10.92.82.161】运行端口【10031】,当前主机地址【10.87.57.73】运行端口【8092】, 无法执行定时任务【com.simbest.boot.component.task.HeartTestTask】【638】
2025-05-27 08:40:00.008 [pool-14-thread-6] INFO  com.simbest.boot.component.task.AbstractTaskSchedule.checkAndExecute Line:198 - 集群主控节点主机地址【10.92.82.161】运行端口【10031】,当前主机地址【10.87.57.73】运行端口【8092】, 无法执行定时任务【com.simbest.boot.bps.task.AutoCheckWithUpdateTask】【642】
2025-05-27 08:40:00.009 [pool-14-thread-7] INFO  com.simbest.boot.component.task.AbstractTaskSchedule.checkAndExecute Line:198 - 集群主控节点主机地址【10.92.82.161】运行端口【10031】,当前主机地址【10.87.57.73】运行端口【8092】, 无法执行定时任务【com.simbest.boot.processdata.pushWorkmanager.task.SyncProcessDataToWorkManagerTask】【643】
2025-05-27 08:40:00.009 [pool-14-thread-10] INFO  com.simbest.boot.component.task.AbstractTaskSchedule.checkAndExecute Line:198 - 集群主控节点主机地址【10.92.82.161】运行端口【10031】,当前主机地址【10.87.57.73】运行端口【8092】, 无法执行定时任务【com.simbest.boot.bps.task.AutoAddTodoTask】【646】
2025-05-27 08:40:00.009 [pool-14-thread-4] INFO  com.simbest.boot.component.task.AbstractTaskSchedule.checkAndExecute Line:198 - 集群主控节点主机地址【10.92.82.161】运行端口【10031】,当前主机地址【10.87.57.73】运行端口【8092】, 无法执行定时任务【com.simbest.boot.component.task.HeartTestTask】【640】
2025-05-27 08:40:00.009 [pool-14-thread-1] INFO  com.simbest.boot.component.task.AbstractTaskSchedule.checkAndExecute Line:198 - 集群主控节点主机地址【10.92.82.161】运行端口【10031】,当前主机地址【10.87.57.73】运行端口【8092】, 无法执行定时任务【com.simbest.boot.bps.task.AutoCloseTodoTask】【637】
2025-05-27 08:40:00.008 [pool-14-thread-9] INFO  com.simbest.boot.component.task.AbstractTaskSchedule.checkAndExecute Line:198 - 集群主控节点主机地址【10.92.82.161】运行端口【10031】,当前主机地址【10.87.57.73】运行端口【8092】, 无法执行定时任务【com.simbest.boot.processdata.pushWorkmanager.task.SyncCommentDataToWorkManagerTask】【645】
2025-05-27 08:40:00.009 [pool-14-thread-2] INFO  com.simbest.boot.component.task.AbstractTaskSchedule.checkAndExecute Line:198 - 集群主控节点主机地址【10.92.82.161】运行端口【10031】,当前主机地址【10.87.57.73】运行端口【8092】, 无法执行定时任务【com.simbest.boot.processdata.pushWorkmanager.task.SyncTaskDataToWorkManagerTask】【638】
2025-05-27 08:41:00.015 [pool-14-thread-7] INFO  com.simbest.boot.component.task.AbstractTaskSchedule.checkAndExecute Line:198 - 集群主控节点主机地址【10.92.82.161】运行端口【10031】,当前主机地址【10.87.57.73】运行端口【8092】, 无法执行定时任务【com.simbest.boot.component.task.HeartTestTask】【643】
2025-05-27 08:42:00.001 [pool-14-thread-8] INFO  com.simbest.boot.component.task.AbstractTaskSchedule.checkAndExecute Line:198 - 集群主控节点主机地址【10.92.82.161】运行端口【10031】,当前主机地址【10.87.57.73】运行端口【8092】, 无法执行定时任务【com.simbest.boot.component.task.HeartTestTask】【644】
2025-05-27 08:43:00.011 [pool-14-thread-6] INFO  com.simbest.boot.component.task.AbstractTaskSchedule.checkAndExecute Line:198 - 集群主控节点主机地址【10.92.82.161】运行端口【10031】,当前主机地址【10.87.57.73】运行端口【8092】, 无法执行定时任务【com.simbest.boot.component.task.HeartTestTask】【642】
2025-05-27 08:44:00.001 [pool-14-thread-10] INFO  com.simbest.boot.component.task.AbstractTaskSchedule.checkAndExecute Line:198 - 集群主控节点主机地址【10.92.82.161】运行端口【10031】,当前主机地址【10.87.57.73】运行端口【8092】, 无法执行定时任务【com.simbest.boot.component.task.HeartTestTask】【646】
2025-05-27 08:45:00.010 [pool-14-thread-3] INFO  com.simbest.boot.component.task.AbstractTaskSchedule.checkAndExecute Line:198 - 集群主控节点主机地址【10.92.82.161】运行端口【10031】,当前主机地址【10.87.57.73】运行端口【8092】, 无法执行定时任务【com.simbest.boot.bps.task.AutoAddTodoTask】【639】
2025-05-27 08:45:00.010 [pool-14-thread-2] INFO  com.simbest.boot.component.task.AbstractTaskSchedule.checkAndExecute Line:198 - 集群主控节点主机地址【10.92.82.161】运行端口【10031】,当前主机地址【10.87.57.73】运行端口【8092】, 无法执行定时任务【com.simbest.boot.component.task.HeartTestTask】【638】
2025-05-27 08:45:00.010 [pool-14-thread-8] INFO  com.simbest.boot.component.task.AbstractTaskSchedule.checkAndExecute Line:198 - 集群主控节点主机地址【10.92.82.161】运行端口【10031】,当前主机地址【10.87.57.73】运行端口【8092】, 无法执行定时任务【com.simbest.boot.bps.task.AutoCloseTodoTask】【644】
2025-05-27 08:45:00.011 [pool-14-thread-5] INFO  com.simbest.boot.component.task.AbstractTaskSchedule.checkAndExecute Line:198 - 集群主控节点主机地址【10.92.82.161】运行端口【10031】,当前主机地址【10.87.57.73】运行端口【8092】, 无法执行定时任务【com.simbest.boot.bps.task.AutoCheckWithUpdateTask】【641】
2025-05-27 08:45:00.010 [pool-14-thread-4] INFO  com.simbest.boot.component.task.AbstractTaskSchedule.checkAndExecute Line:198 - 集群主控节点主机地址【10.92.82.161】运行端口【10031】,当前主机地址【10.87.57.73】运行端口【8092】, 无法执行定时任务【com.simbest.boot.processdata.pushWorkmanager.task.SyncTaskDataToWorkManagerTask】【640】
2025-05-27 08:45:00.010 [pool-14-thread-10] INFO  com.simbest.boot.component.task.AbstractTaskSchedule.checkAndExecute Line:198 - 集群主控节点主机地址【10.92.82.161】运行端口【10031】,当前主机地址【10.87.57.73】运行端口【8092】, 无法执行定时任务【com.simbest.boot.processdata.pushWorkmanager.task.SyncCommentDataToWorkManagerTask】【646】
2025-05-27 08:45:00.011 [pool-14-thread-6] INFO  com.simbest.boot.component.task.AbstractTaskSchedule.checkAndExecute Line:198 - 集群主控节点主机地址【10.92.82.161】运行端口【10031】,当前主机地址【10.87.57.73】运行端口【8092】, 无法执行定时任务【com.simbest.boot.processdata.pushWorkmanager.task.SyncProcessDataToWorkManagerTask】【642】
