<!DOCTYPE html>
<html xmlns="http://www.w3.org/1999/xhtml"
      xmlns:th="http://www.thymeleaf.org">
<head>
    <title>详情-深入贯彻中央八项规定精神学习教育——警示教育专区-清廉移动</title>
    <meta http-equiv="Content-Type" content="text/html; charset=utf-8"/>
    <meta http-equiv="X-UA-Compatible" content="IE=edge" />
    <meta name="viewport" content="width=device-width, initial-scale=1, shrink-to-fit=no"/>
    <link href="../../fonts/iconfont/iconfont.css?v=svn.revision" th:href="@{/fonts/iconfont/iconfont.css?v=svn.revision}" rel="stylesheet"/>
    <link href="https://cdnjs.cloudflare.com/ajax/libs/font-awesome/5.15.4/css/all.min.css" rel="stylesheet"/>
    <link href="http://************:8088/simbestui/js/themes/default/easyui.css?v=svn.revision" th:href="@{http://************:8088/simbestui/js/themes/default/easyui.css?v=svn.revision}" rel="stylesheet"/>
    <link href="http://************:8088/simbestui/js/themes/icon.css?v=svn.revision" th:href="@{http://************:8088/simbestui/js/themes/icon.css?v=svn.revision}" rel="stylesheet"/>
    <link href="http://************:8088/simbestui/css/public.css?v=svn.revision" th:href="@{http://************:8088/simbestui/css/public.css?v=svn.revision}" rel="stylesheet"/>
    <link href="http://************:8088/simbestui/css/pages.css?v=svn.revision" th:href="@{http://************:8088/simbestui/css/pages.css?v=svn.revision}" rel="stylesheet"/>
    <link href="../../css/webSite.css?v=svn.revision" th:href="@{/css/webSite.css?v=svn.revision}" rel="stylesheet"/>
    <script src="http://************:8088/simbestui/js/jquery.min.js?v=svn.revision" th:src="@{http://************:8088/simbestui/js/jquery.min.js?v=svn.revision}" type="text/javascript"></script>
    <script src="http://************:8088/simbestui/js/jquery.easyui.min.js?v=svn.revision" th:src="@{http://************:8088/simbestui/js/jquery.easyui.min.js?v=svn.revision}" type="text/javascript"></script>
    <script src="http://************:8088/simbestui/js/easyui-lang-zh_CN.js?v=svn.revision" th:src="@{http://************:8088/simbestui/js/easyui-lang-zh_CN.js?v=svn.revision}" type="text/javascript"></script>
    <script src="../../js/jquery.config.js?v=svn.revision" th:src="@{/js/jquery.config.js?v=svn.revision}" type="text/javascript"></script>
    <script src="http://************:8088/simbestui/js/jquery.zjsfile.js?v=svn.revision" th:src="@{http://************:8088/simbestui/js/jquery.zjsfile.js?v=svn.revision}" type="text/javascript"></script>
    <script src="http://************:8088/simbestui/js/jquery.aduq.js?v=svn.revision" th:src="@{http://************:8088/simbestui/js/jquery.aduq.js?v=svn.revision}" type="text/javascript"></script>
    <script src="http://************:8088/simbestui/js/jquery.process.js?v=svn.revision" th:src="@{http://************:8088/simbestui/js/jquery.process.js?v=svn.revision}" type="text/javascript"></script>
    <script src="../../js/webSite.js?v=svn.revision" th:src="@{/js/webSite.js?v=svn.revision}" type="text/javascript"></script>
    <script type="text/javascript">
        $(function(){
            webSite();
            getArticleDetails();
        });

        // 解析URL参数
        function getUrlParam(name) {
            var reg = new RegExp("(^|&)" + name + "=([^&]*)(&|$)");
            var r = window.location.search.substr(1).match(reg);
            if (r != null) return decodeURIComponent(r[2]); return null;
        }

        // 获取文章详情
        function getArticleDetails(){
            var pmInsId = getUrlParam("pmInsId");
            var title = decodeURIComponent(getUrlParam("title") || "");

            if(pmInsId) {
                // 调用后端接口获取文章详情
                console.log('调用文章详情接口，pmInsId:', pmInsId);
                ajaxgeneral({
                    url: "action/approvalForm/getApprovalFromDetail",
                    data: JSON.stringify({"pmInstId": pmInsId}),
                    type: "POST",
                    contentType: "application/json;charset=UTF-8",
                    success: function(res) {
                        console.log('Article details response:', res);
                        if(res.data) {
                            renderArticle(res.data);
                        } else {
                            showMockArticle(title);
                        }
                    },
                    error: function(xhr, status, error) {
                        console.error('Failed to get article details:', error);
                        showMockArticle(title);
                    }
                });
            } else {
                showMockArticle(title);
            }
        }

        // 渲染文章内容
        function renderArticle(data) {
            console.log('Rendering article with data:', data);

            // 获取文章标题和内容
            var title = data.title || data.TITLE;
            var content = data.mainBody || data.MAIN_BODY || '';
            var creationTime = data.creationTime || data.CREATION_TIME || '';
            var source = data.source || data.SOURCE || '清廉移动';
            var viewCount = data.viewCount || data.VIEW_COUNT || 0;

            // 设置页面标题
            document.title = title + "-深入贯彻中央八项规定精神学习教育——警示教育专区-清廉移动";
            $("#h6").html("深入贯彻中央八项规定精神学习教育——警示教育专区");

            // 导航路径
            var html = ["<img class='mr5' src='images/er1.jpg'/><a target='_blank' href='/hnjjwz/html/webSite/index.html'>首页</a>"];
            html.push("><a href='/hnjjwz/html/eightRegulations/index.html'>深入贯彻中央八项规定精神学习教育——警示教育专区</a>");
            html.push("><a href='javascript:void(0);'>" + title + "</a>");
            $(".navTit").html(html.join(""));

            // 文章标题
            $(".article-title").html(title);

            // 文章信息
            $(".article-info").html("发布时间：" + creationTime +
                                   "&nbsp;&nbsp;&nbsp;来源：" + source +
                                   "&nbsp;&nbsp;&nbsp;浏览次数：" + viewCount);

            // 处理文章内容
            if (content) {
                // 如果内容是HTML格式，直接显示
                $(".article-content").html(content);
            } else {
                // 如果没有内容，显示默认消息
                $(".article-content").html("<p style='text-align:center;padding:30px;'>暂无内容</p>");
            }

            // 处理附件
            if (data.accessoryFileList && data.accessoryFileList.length > 0) {
                var attachmentsHtml = "<div class='article-attachments'><h3>附件列表：</h3><ul>";
                for (var i = 0; i < data.accessoryFileList.length; i++) {
                    var file = data.accessoryFileList[i];
                    attachmentsHtml += "<li><a href='" + file.fileUrl + "' target='_blank'>" + file.fileName + "</a></li>";
                }
                attachmentsHtml += "</ul></div>";
                $(".article-content").append(attachmentsHtml);
            }
        }

        // 显示模拟文章（在后端接口未实现时使用）
        function showMockArticle(title) {
            // 设置页面标题
            var mockTitle = title || "习近平在中央纪委三次全会上强调：一刻不停推进党风廉政建设和反腐败斗争";
            document.title = mockTitle + "-深入贯彻中央八项规定精神学习教育——警示教育专区-清廉移动";
            $("#h6").html("深入贯彻中央八项规定精神学习教育——警示教育专区");

            // 导航路径
            var html = ["<img class='mr5' src='images/er1.jpg'/><a target='_blank' href='/hnjjwz/html/webSite/index.html'>首页</a>"];
            html.push("><a href='/hnjjwz/html/eightRegulations/index.html'>深入贯彻中央八项规定精神学习教育——警示教育专区</a>");
            html.push("><a href='javascript:void(0);'>" + mockTitle + "</a>");
            $(".navTit").html(html.join(""));

            // 文章标题
            $(".article-title").html(mockTitle);

            // 文章信息
            $(".article-info").html("发布时间：2023-01-09&nbsp;&nbsp;&nbsp;来源：人民日报&nbsp;&nbsp;&nbsp;浏览次数：3256");

            // 文章内容
            var mockContent = "";

            if(title.indexOf("习近平在中央纪委三次全会上强调") >= 0) {
                mockContent = "<p style='text-indent:2em;line-height:1.8;margin-bottom:15px;'>新华社北京1月9日电 中共中央总书记、国家主席、中央军委主席习近平9日上午在中国共产党第十九届中央纪律检查委员会第三次全体会议上发表重要讲话。他强调，要以新时代中国特色社会主义思想为指导，增强"四个意识"、坚定"四个自信"、做到"两个维护"，以党的政治建设为统领全面推进党的建设，取得全面从严治党更大战略性成果，巩固发展反腐败斗争压倒性胜利，一体推进不敢腐、不能腐、不想腐，健全党和国家监督体系，确保党的十九大精神和党中央重大决策部署坚决贯彻落实到位，以优异成绩庆祝中华人民共和国成立70周年。</p>";
                mockContent += "<p style='text-indent:2em;line-height:1.8;margin-bottom:15px;'>习近平指出，党的十九大以来，我们贯彻落实新时代党的建设总要求，坚持把党的政治建设摆在首位，深化运用监督执纪"四种形态"，夺取反腐败斗争压倒性胜利，着力惩治群众身边的腐败问题，完善党和国家监督体系，取得了新的重大成果，为实现党和国家事业新发展提供了坚强保障。</p>";
                mockContent += "<p style='text-indent:2em;line-height:1.8;margin-bottom:15px;'>习近平强调，一年多来，经过全党共同努力，党的集中统一领导更加坚强有力，党的建设新的伟大工程全方位加强，全面从严治党实效性不断提高，党内政治生态进一步改善，党在新时代新征程中焕发出更加强大的生机活力。</p>";
                mockContent += "<p style='text-indent:2em;line-height:1.8;margin-bottom:15px;'>习近平指出，反腐败斗争取得压倒性胜利，不是反腐败斗争的完成时，而是反腐败斗争进入了一个新的阶段。一体推进不敢腐、不能腐、不想腐，不仅是反腐败斗争的基本方针，也是新时代全面从严治党的重要目标。不敢腐，就是要让党员干部因违纪违法行为付出代价，形成震慑；不能腐，就是要让党员干部不想腐的思想自觉成为不敢腐的必然选择，严格日常管理监督，加强党内监督，防范腐败行为发生；不想腐，就是要做好思想教育工作，使党员干部不断增强不想腐的自觉，强化道德修养，增强拒腐防变能力。</p>";
            } else if(title.indexOf("中共中央办公厅印发") >= 0) {
                mockContent = "<p style='text-indent:2em;line-height:1.8;margin-bottom:15px;'>近日，中共中央办公厅印发了《关于解决形式主义突出问题为基层减负的通知》，明确提出将2019年作为"基层减负年"。</p>";
                mockContent += "<p style='text-indent:2em;line-height:1.8;margin-bottom:15px;'>《通知》指出，党的十八大以来，以习近平同志为核心的党中央一直高度重视解决形式主义、官僚主义问题。当前，"四风"问题树倒根存，形式主义、官僚主义问题依然突出，与党中央要求和人民期待差距较大，要认真贯彻落实习近平总书记关于加强党的作风建设的重要指示精神，树立正确政绩观，不断为基层减负松绑，让干部有更多时间和精力抓落实。</p>";
                mockContent += "<p style='text-indent:2em;line-height:1.8;margin-bottom:15px;'>《通知》强调，要加强对形式主义、官僚主义新表现的梳理排查，深刻剖析根源，找准靶子，精准施策，力戒形式主义、官僚主义，把干部从一些无谓的事务中解脱出来。</p>";
                mockContent += "<p style='text-indent:2em;line-height:1.8;margin-bottom:15px;'>《通知》明确提出了将2019年作为"基层减负年"，要从以下几个方面着力解决：</p>";
                mockContent += "<p style='text-indent:2em;line-height:1.8;margin-bottom:15px;'>一是切实精简文件和会议。严格控制发文总量和规格，减少"文山会海"。</p>";
                mockContent += "<p style='text-indent:2em;line-height:1.8;margin-bottom:15px;'>二是进一步改进督查检查考核方式。强化结果导向，坚决纠正机械式做法。</p>";
                mockContent += "<p style='text-indent:2em;line-height:1.8;margin-bottom:15px;'>三是着力解决干部不敢担当作为的问题。建立健全容错纠错机制，旗帜鲜明为敢于担当的干部撑腰鼓劲。</p>";
                mockContent += "<p style='text-indent:2em;line-height:1.8;margin-bottom:15px;'>四是持续整治形式主义、官僚主义。紧盯形式主义新动向、新表现，坚决破除形式主义顽疾。</p>";
            } else if(title.indexOf("中央纪委国家监委通报") >= 0) {
                mockContent = "<p style='text-indent:2em;line-height:1.8;margin-bottom:15px;'>中央纪委国家监委通报2023年上半年全国纪检监察机关落实中央八项规定精神情况。2023年上半年，全国纪检监察机关共查处违反中央八项规定精神问题25,368起，批评教育帮助和处理35,128人，其中给予党纪政务处分19,079人。</p>";
                mockContent += "<p style='text-indent:2em;line-height:1.8;margin-bottom:15px;'>从查处问题类型看，违规收送礼品礼金问题5,421起，违规发放津补贴或福利问题4,892起，违规吃喝问题4,218起，违规配备使用公务用车问题2,156起，违规操办婚丧喜庆问题1,987起，公款旅游以及违规接受管理和服务对象宴请问题1,854起，楼堂馆所违规问题和提供或接受超标准接待问题1,632起，其他问题3,208起。</p>";
                mockContent += "<p style='text-indent:2em;line-height:1.8;margin-bottom:15px;'>通报强调，各级纪检监察机关要深入学习贯彻习近平总书记关于作风建设的重要论述，认真落实中央纪委国家监委工作部署，持续深化纠治"四风"工作，坚决防止反弹回潮。要紧盯"四风"隐形变异新动向，深挖细查顶风违纪行为，对违规吃喝、公款旅游等老问题露头就打，对收送电子红包、借培训中心培训基地等名义搞公款吃喝等隐形变异问题精准纠治，对违规收送名贵特产和礼品礼金、违规接受管理和服务对象宴请等问题从严查处。</p>";
                mockContent += "<p style='text-indent:2em;line-height:1.8;margin-bottom:15px;'>通报要求，各级纪检监察机关要坚持纠"四风"和树新风并举，既严肃查处不正之风，又积极倡导新风正气。要把握好节点，加强监督检查，对中秋国庆等重要节点期间的"四风"问题早提醒、早部署、早防范，对顶风违纪行为发现一起、查处一起，持续释放越往后执纪越严的强烈信号。</p>";
            } else if(title.indexOf("中央纪委国家监委部署加强春节") >= 0) {
                mockContent = "<p style='text-indent:2em;line-height:1.8;margin-bottom:15px;'>近日，中央纪委国家监委发出通知，要求各级纪检监察机关加强春节假日期间正风肃纪工作，严防"四风"问题反弹回潮。</p>";
                mockContent += "<p style='text-indent:2em;line-height:1.8;margin-bottom:15px;'>通知强调，春节期间是"四风"问题易发多发的重要节点，各级纪检监察机关要提高政治站位，深入学习贯彻习近平总书记关于作风建设的重要论述，认真落实中央八项规定及其实施细则精神，坚决防止"四风"问题反弹回潮。</p>";
                mockContent += "<p style='text-indent:2em;line-height:1.8;margin-bottom:15px;'>通知要求，要紧盯违规收送礼品礼金、违规吃喝、违规发放津补贴或福利、公款旅游、违规接受管理和服务对象宴请等重点问题，加大监督检查力度。要畅通监督举报渠道，及时受理群众举报，对反映集中、性质恶劣的问题线索优先处置。</p>";
                mockContent += "<p style='text-indent:2em;line-height:1.8;margin-bottom:15px;'>通知强调，对节日期间发现的"四风"问题，要严肃查处、通报曝光，持续释放越往后执纪越严的强烈信号。要坚持纠"四风"和树新风并举，既严肃查处不正之风，又积极倡导勤俭节约、文明过节的新风正气。</p>";
                mockContent += "<p style='text-indent:2em;line-height:1.8;margin-bottom:15px;'>通知还要求，各级纪检监察机关要加强自身建设，严格落实中央八项规定精神，带头廉洁文明过节，以良好作风和形象取信于民。</p>";
            } else {
                mockContent = "<p style='text-indent:2em;line-height:1.8;margin-bottom:15px;'>中央八项规定是党的十八大以来以习近平同志为核心的党中央为加强党的作风建设而制定的重要规定。2012年12月4日，中共中央政治局召开会议，审议通过了关于改进工作作风、密切联系群众的八项规定。</p>";
                mockContent += "<p style='text-indent:2em;line-height:1.8;margin-bottom:15px;'>中央八项规定主要内容包括：</p>";
                mockContent += "<p style='text-indent:2em;line-height:1.8;margin-bottom:15px;'>一、要改进调查研究，到基层调研要深入了解真实情况，总结经验、研究问题、解决困难、指导工作，向群众学习、向实践学习，多同群众座谈，多同干部谈心，多商量讨论，多解剖典型，多到困难和矛盾集中、群众意见多的地方去，切忌走过场、搞形式主义；要轻车简从、减少陪同、简化接待，不张贴悬挂标语横幅，不安排群众迎送，不铺设迎宾地毯，不摆放花草，不安排宴请。</p>";
                mockContent += "<p style='text-indent:2em;line-height:1.8;margin-bottom:15px;'>二、要精简会议活动，切实改进会风，严格控制以中央名义召开的各类全国性会议和举行的大型会议活动，不开泛泛部署工作和提要求的会，未经中央批准一律不得召开全国性会议和举行大型会议活动。提高会议实效，开短会、讲短话，力戒空话、套话。</p>";
                mockContent += "<p style='text-indent:2em;line-height:1.8;margin-bottom:15px;'>三、要精简文件简报，切实改进文风，没有实质内容、可发可不发的文件、简报一律不发。</p>";
                mockContent += "<p style='text-indent:2em;line-height:1.8;margin-bottom:15px;'>四、要规范出访活动，从外交工作大局需要出发合理安排出访活动，严格控制出访随行人员，严格按照规定乘坐交通工具，一般不安排中资机构、华侨华人、留学生代表等到机场迎送。</p>";
                mockContent += "<p style='text-indent:2em;line-height:1.8;margin-bottom:15px;'>五、要改进警卫工作，坚持有利于联系群众的原则，减少交通管制，一般情况下不得封路、不清场闭馆。</p>";
                mockContent += "<p style='text-indent:2em;line-height:1.8;margin-bottom:15px;'>六、要改进新闻报道，中央政治局同志出席会议和活动应根据工作需要、新闻价值、社会效果决定是否报道，进一步压缩报道的数量、字数、时长。</p>";
                mockContent += "<p style='text-indent:2em;line-height:1.8;margin-bottom:15px;'>七、要严格文稿发表，除中央统一安排外，个人不公开出版著作、讲话单行本，不发贺信、贺电，不题词、题字。</p>";
                mockContent += "<p style='text-indent:2em;line-height:1.8;margin-bottom:15px;'>八、要厉行勤俭节约，严格遵守廉洁从政有关规定，严格执行住房、车辆配备等有关工作和生活待遇的规定。</p>";
                mockContent += "<p style='text-indent:2em;line-height:1.8;margin-bottom:15px;'>中央八项规定的出台和严格执行，有力地推动了党风政风的好转，赢得了广大人民群众的衷心拥护和支持。我们要持续深入学习贯彻中央八项规定精神，坚决纠正"四风"问题，不断巩固和拓展落实中央八项规定精神成果。</p>";
            }

            $(".article-content").html(mockContent);
        }
    </script>
    <style>
        /* 整体布局样式 */
        body {
            font-family: "Microsoft YaHei", Arial, sans-serif;
            background-color: #f5f5f5;
            margin: 0;
            padding: 0;
        }

        /* 头部样式 */
        .header {
            height: 340px;
            background: url('images/banner.png') no-repeat center;
            background-size: cover;
            padding: 0;
        }

        /* 标题样式 */
        #h6 {
            font-size: 24px;
            color: #fff;
            text-align: center;
            line-height: 340px;
            text-shadow: 1px 1px 3px rgba(0, 0, 0, 0.5);
        }

        /* 文章容器样式 */
        .article-container {
            width: 1200px;
            margin: 20px auto;
            background-color: #fff;
            padding: 30px;
            box-shadow: 0 2px 5px rgba(0, 0, 0, 0.1);
            border-radius: 4px;
            border-left: 4px solid #e60000;
        }

        .article-title {
            font-size: 24px;
            font-weight: bold;
            text-align: center;
            margin-bottom: 20px;
            color: #e60000;
            position: relative;
            padding-bottom: 15px;
        }

        .article-title::after {
            content: "";
            position: absolute;
            bottom: 0;
            left: 50%;
            transform: translateX(-50%);
            width: 120px;
            height: 3px;
            background: linear-gradient(to right, #e60000, #ffcc00);
        }

        .article-info {
            text-align: center;
            color: #666;
            font-size: 14px;
            padding-bottom: 20px;
            border-bottom: 1px dashed #ddd;
            margin-bottom: 20px;
        }

        .article-content {
            line-height: 1.8;
            color: #333;
            font-size: 16px;
        }

        .article-content p {
            margin-bottom: 15px;
            text-indent: 2em;
        }

        .article-content img {
            max-width: 100%;
            height: auto;
            display: block;
            margin: 15px auto;
        }

        /* 页脚样式 */
        .footer {
            background-color: #e60000;
            color: #fff;
            padding: 10px 0;
            text-align: center;
            font-size: 12px;
            line-height: 30px;
        }

        .footer p {
            margin: 0;
        }

        /* 悬浮返回按钮样式 */
        .float-button {
            position: fixed;
            right: 30px;
            bottom: 100px;
            width: 60px;
            height: 60px;
            background: linear-gradient(to right, #e60000, #ffcc00);
            border-radius: 50%;
            display: flex;
            flex-direction: column;
            align-items: center;
            justify-content: center;
            color: white;
            box-shadow: 0 4px 10px rgba(0, 0, 0, 0.2);
            cursor: pointer;
            z-index: 999;
            transition: all 0.3s ease;
        }

        .float-button:hover {
            transform: translateY(-5px);
            box-shadow: 0 6px 15px rgba(0, 0, 0, 0.3);
        }

        .float-button i {
            font-size: 24px;
            margin-bottom: 2px;
        }

        .float-button span {
            font-size: 12px;
            text-align: center;
        }
    </style>
</head>
<body>
<!--top-->
<div class="header"><div class="auto1024"><h6 id="h6">深入贯彻中央八项规定精神学习教育——警示教育专区</h6></div></div>
<!-- 导航栏已隐藏 -->

<!--center-->
<div class="auto1024 center">
    <div class="navTit">
        <!-- 导航路径将由JavaScript动态生成 -->
    </div>

    <!-- 文章内容 -->
    <div class="article-container">
        <div class="article-title">
            <!-- 文章标题将由JavaScript动态生成 -->
        </div>
        <div class="article-info">
            <!-- 文章信息将由JavaScript动态生成 -->
        </div>
        <div class="article-content">
            <!-- 文章内容将由JavaScript动态生成 -->
        </div>
    </div>
</div>

<!-- 悬浮返回按钮 -->
<div class="float-button" onclick="window.location.href='http://*************:8088/hnjjwz/html/webSite/index.html'; window.close();">
    <i class="fas fa-home"></i>
    <span>返回首页</span>
</div>

<!--copy-->
<div class="footer">
    <p>&copy;版权所有 中国移动通信集团河南有限公司</p>
</div>
</body>
</html>
