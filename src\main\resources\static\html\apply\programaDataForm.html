<!DOCTYPE html>
<html xmlns="http://www.w3.org/1999/xhtml"
      xmlns:th="http://www.thymeleaf.org">
<head>
    <title>栏目内容</title>
    <meta http-equiv="Content-Type" content="text/html; charset=utf-8"/>
    <meta http-equiv="X-UA-Compatible" content="IE=edge" />
    <meta name="viewport" content="width=device-width, initial-scale=1, shrink-to-fit=no"/>
    <!--
    <link href="http://************:8088/simbestui/fonts/iconfont/iconfont.css?v=svn.revision" th:href="@{http://************:8088/simbestui/fonts/iconfont/iconfont.css?v=svn.revision}" rel="stylesheet"/>
    -->
    <link href="../../fonts/iconfont/iconfont.css?v=svn.revision" th:href="@{/fonts/iconfont/iconfont.css?v=svn.revision}" rel="stylesheet"/>
    <link href="http://************:8088/simbestui/js/themes/default/easyui.css?v=svn.revision" th:href="@{http://************:8088/simbestui/js/themes/default/easyui.css?v=svn.revision}" rel="stylesheet"/>
    <link href="http://************:8088/simbestui/js/themes/icon.css?v=svn.revision" th:href="@{http://************:8088/simbestui/js/themes/icon.css?v=svn.revision}" rel="stylesheet"/>
    <link href="http://************:8088/simbestui/css/public.css?v=svn.revision" th:href="@{http://************:8088/simbestui/css/public.css?v=svn.revision}" rel="stylesheet"/>
    <link href="http://************:8088/simbestui/css/pages.css?v=svn.revision" th:href="@{http://************:8088/simbestui/css/pages.css?v=svn.revision}" rel="stylesheet"/>
    <script src="http://************:8088/simbestui/js/jquery.min.js?v=svn.revision" th:src="@{http://************:8088/simbestui/js/jquery.min.js?v=svn.revision}" type="text/javascript"></script>
    <script src="http://************:8088/simbestui/js/jquery.easyui.min.js?v=svn.revision" th:src="@{http://************:8088/simbestui/js/jquery.easyui.min.js?v=svn.revision}" type="text/javascript"></script>
    <script src="http://************:8088/simbestui/js/easyui-lang-zh_CN.js?v=svn.revision" th:src="@{http://************:8088/simbestui/js/easyui-lang-zh_CN.js?v=svn.revision}" type="text/javascript"></script>
    <script src="http://************:8088/simbestui/js/kindeditor/kindeditor.min.js?v=svn.revision" th:src="@{http://************:8088/simbestui/js/kindeditor.min.js?v=svn.revision}" type="text/javascript"></script>
    <script src="../../js/jquery.config.js?v=svn.revision" th:src="@{/js/jquery.config.js?v=svn.revision}" type="text/javascript"></script>
    <script src="http://************:8088/simbestui/js/jquery.zjsfile.js?v=svn.revision" th:src="@{http://************:8088/simbestui/js/jquery.zjsfile.js?v=svn.revision}" type="text/javascript"></script>
    <script src="http://************:8088/simbestui/js/jquery.aduq.js?v=svn.revision" th:src="@{http://************:8088/simbestui/js/jquery.aduq.js?v=svn.revision}" type="text/javascript"></script>
    <script src="http://************:8088/simbestui/js/jquery.process.js?v=svn.revision" th:src="@{http://************:8088/simbestui/js/jquery.process.js?v=svn.revision}" type="text/javascript"></script>
	<script type="text/javascript">	
		var aF;

        //获取当前登录人
        getCurrent();

        //查看当前登录人是属于分公司还是省公司
        var CompanyName=web.currentUser.belongCompanyName;

        var authPositions=web.currentUser.authPositions;
        var state=false;

        for(i in authPositions){
            if(authPositions[i].id==57){
                state=true;
                break;
            }
        }

        //console.log();
		//提交之前校验
		function beforesubmit(data){
			return true;
		    //return activeD();
		};
        function loadF(){
            $("#belongCompanyTypeDictValue").val(web.currentUser.belongCompanyTypeDictValue);
            $("#belongDepartmentCode").val(web.currentUser.belongDepartmentCode);
            $("#belongDepartmentName").val(web.currentUser.belongCompanyTypeDictValue=="03"?web.currentUser.belongCompanyName:web.currentUser.belongDepartmentName);
            $("#belongCompanyCode").val(web.currentUser.belongCompanyCode);
        };
		//校验
		function activeMoney(){
			if(aF){
				clearTimeout(aF);
			}
			aF=setTimeout(function(){			
				activeD();
			},1500);
		};
        //保存草稿关闭页面
        function approvalS(data){
            // top.tabClick("processTask");
            // top.tabClose("li_meetingForm");
        };
		$(function(){
            var param={
                "htmlName":"programaDataForm",
                "formId":"programaDataForm",
                "processNextCmd":"action/approvalForm/startSubmitProcess",
                "processDeleteCmd":"action/approvalForm/deleteProcess",
                "processDraftDeleteCmd": "action/approvalForm/deleteDraft"
            };
            loadProcess(param);

            // 加载表单
            // loadForm("programaDataForm",{"pmInstId":gps.pmInstId});

            //点击打开栏目树
            $(".chooseProgramaName").on("click",function(){
                var gps=getQueryString();
                //第一个参数是页面的url(multi为0表示单选，为1表示多选),第二个参数是当前对话框对应iframe的name（若是有好多次iframe请按从内到外的顺序以|分隔）,第三个参数选择人界面的标题,
                //第四个参数是选择人完成之后的回调函数名称,第五个参数对话框是否有底部按钮来设置表示默认有(若需要设置宽高,若需要就写false 不需要写true) 第6是宽默认1000，第7是高默认550
                //第二个参数必须是唯一的
                var href={"multi":"0","name":"chooseProgramaNameVal"};
                var chooseRow=top.chooseWeb.chooseProgramaNameVal?top.chooseWeb.chooseProgramaNameVal.data:[];
                if($("#programaCode").val()!=""){
                    var datas=[];
                    var names=$("#programaDisplayName").val().split(",");
                    var codes=$("#programaCode").val().split(",");
                    for(var i in codes){
                        var datai={};
                        datai.id=codes[i];
                        datai.name=names[i];
                        datas.push(datai);
                    }
                    top.chooseWeb.chooseProgramaNameVal={"data":datas};
                }else{//表示新增
                    top.chooseWeb.chooseProgramaNameVal={"data":[]};
                }
                var url=tourl((gps.form?"hnjjwz/":"")+'html/programaClassifyInfo/programaInfoTree.html?',href);

                if(!gps.location){//起草
                    top.dialogP(url,gps.form?"programaDataForm":'programaDataForm','选择栏目','chooseProgramaName',false,'800');
                }else{//草稿 auditF
                    top.dialogP(url,gps.form?"auditF":'auditF','选择栏目','chooseProgramaName',false,'800');
                }
                //记住decisionOptF这个参数是打开的页面里的名字
            });
		});
        //选择栏目树后用于回显
        window.chooseProgramaName=function(data){
            var programaCode = "";
            var programaName = "";
            var dataLength = data.data.length;
            for(var i=0;i<dataLength;i++){
                programaCode = programaCode + data.data[i].orgCode;
                programaName =  programaName + data.data[i].displayName;
                if(i<dataLength-1){
                    programaCode = programaCode + ",";
                    programaName = programaName + ",";
                }
            }
            $("#programaCode").val(programaCode);
            $("#programaDisplayName").val(programaName);
        };
        function getcallback(data){
            //处理渲染数据之后的操作代码,data为接口返回数据。一般用于对html标签的处理。
            if( ("hnjjwz.start"==gps.location || undefined == gps.location ) && "examine"!=gps.type ){

            }else if("hnjjwz.general_manager"==gps.location&&"join"==gps.type){
                //formReadonlyNo("programaDataForm");
                $(".nextBtn").hide();
                //$(".wfmgModifyBtn").hide();
                $(".cancel").hide();
                $(".formReset").hide();
                //$(".printOut").hide();
                idReadonly("belongDepartmentName");
            }else if("hnjjwz.general_manager"==gps.location&&"task"==gps.type){
                formReadonlyNo("programaDataForm");
                $(".cancel").hide();
                $(".formReset").hide();
                //$("#td3").hide();
            }
            var ljwh_sp= $("#programaCode").val();
       /*     if(true){
                $("#videoFile").css('display','table-row');
            }*/
            var ljwhMenu=ljwh_sp.substring(0,4);
            if ("012004"==ljwh_sp){
                $("#videoFile").css('display','table-row');

            }else if(ljwhMenu="廉洁文化"){
                $("#authBookFile").show();
                $("#authBookFile2").show();
                $("#coverImageFile").show();
                $("#coverImageFile2").show();
                $("#accessoryFile").hide();
                $("#accessoryFileList").hide();
            }else{
                $("#authBookFile").hide();
                $("#authBookFile2").hide();
                $("#coverImageFile").hide();
                $("#coverImageFile2").hide();
            }
            if("examine"==gps.type){
                //formReadonly("programaDataForm");
            }
            if(state){
                $("#mainBody").removeAttr("readonly").removeClass("textAndInput_readonly");
                $("#mainBody").trigger("readonlyNo");

                $("#title").removeAttr("readonly").removeClass("textAndInput_readonly");
                $("#title").trigger("readonlyNo");
            }

            if(gps.type=="examine" || gps.type=="join" || gps.type=="task"){
                $("#tableForm").css('width', '100%');
                $("#tableForm .textAndInput_readonly").removeClass("textAndInput_readonly");
            }

        };

        function nextBtnOther() {
            if(CompanyName=="省公司"){
                return {"processType": "A"};
            }else {
                return {"processType": "B"};
            }

        };

        //保存草稿
        $(document).on("click", ".saveDraft", function () {
            if(formValidate("programaDataForm")){
                top.tabClick("processDraft");
                top.tabClose("li_workOrder");
            }
        });
	</script>
</head>
<body class="body_page" style="padding-top:85px;"><!--noNextUserLocation无下一审批人的节点比如归档，可以为多个节点中间用|隔开-->
<form id="programaDataForm" method="post" formLocation="hnjjwz.start" archiveLocation="hnjjwz.confirm" noNextUserDecisionId="end" contentType="application/json; charset=utf-8"
      nextBtnOther="nextBtnOther()" cmd-select="action/approvalForm/getApprovalFromDetail" onSubmit="beforesubmit()" submitcallback="approvalS()" getcallback="getcallback()" >
<div class="pageInfo">
	<div class="pageInfoD">
        <a class="hide btn small fl mr15 nextBtn"><i class="iconfont">&#xe688;</i><font>流转下一步</font></a>
        <a class="hide btn small fl mr15 saveDraft" onclick="formsubmit('programaDataForm','action/approvalForm/saveDraft')"><i class="iconfont">&#xe63a;</i><font>保存草稿</font></a>
        <a class="hide btn small fl mr15 abolish"><i class="iconfont">&#xe6ec;</i><font>废除草稿</font></a>
        <a class="hide btn small fl mr15 cancel"><i class="iconfont">&#xe6ec;</i><font>注销</font></a>
        <a class="hide btn small fl mr15 flowTrack"><i class="iconfont">&#xe68c;</i><font>流程跟踪</font></a>
		<a class="hide btn small fl mr15 viewComments"><i class="iconfont">&#xe629;</i><font>查看意见</font></a>
        <a class="hide btn small fl mr15 formReset" onclick="formreset('programaDataForm')"><i class="iconfont">&#xe646;</i><font>重置</font></a>
        <!--<a class="hide btn small fl mr15 printOut"><i class="iconfont">&#xe678;</i><font>打印</font></a>-->
        <a class="hide btn small fl mr15 processImg"><i class="iconfont">&#xe6bd;</i><font>流程图</font></a>
	</div>
</div>
<fieldset class="title">
	<legend><font>栏目内容信息</font></legend>
</fieldset>
    <input id="id" name="id" type="hidden"/>
    <input id="belongCompanyTypeDictValue" name="belongCompanyTypeDictValue" type="hidden" noReset="true"/>
    <input id="belongDepartmentCode" name="belongDepartmentCode" type="hidden" noReset="true"/>
    <input id="belongCompanyCode" name="belongCompanyCode" type="hidden" noReset="true"/>
    <input id="pmInsId" name="pmInsId" type="hidden"/>
    <input id="describe" name="describe" type="hidden"/>
    <input id="programaCode" name="programaCode" type="hidden"/>
<table border="1" cellpadding="0" cellspacing="15" class="tabForm"  style="width: 1100px" id="tableForm">
    <tr>
        <td align="right" width="110" class="tit"><font class="col_r">*</font>板块</td>
        <td  >
            <input id="programaDisplayName" name="programaDisplayName" type="text"  class="easyui-validatebox" readonly="readonly" required="true" />
        </td>
        <td width="50" id="td3" >
            <a class="btn fr a_warning chooseProgramaName" style="width:100%;"><i class=" iconfont" >&#xe634;</i></a>
        </td>
    </tr>
    <tr>
        <td align="right" width="110" class="tit">申请部门</td>
        <td colspan="2">
            <input id="belongDepartmentName" name="belongDepartmentName" type="text" readonly="readonly" noReset="true" required="true"/>
        </td>
    </tr>
    <tr>
        <td align="right" width="110" class="tit"><font class="col_r">*</font>标题</td>
        <td colspan="2">
            <input id="title" name="title" type="text" class="easyui-validatebox"  required="true"/>
        </td>
    </tr>
<!--    <tr>
        <td align="right" width="110" class="tit" >摘要</td>
        <td  colspan="2">
            <textarea id="digest" name="digest" class="easyui-validatebox" validType="maxLength[2000]" style="width: 100%; height: 120px; resize: none;" ></textarea>
        </td>
    </tr>-->
    <tr>
        <td align="right" width="110" class="tit"><font class="col_r">*</font>正文</td>
        <td colspan="2">
            <textarea id="mainBody" name="mainBody" class="kindeditor" style="width:100%;min-height:1200px;visibility:hidden;"  required="true"></textarea>
        </td>
    </tr>
    <tr>
        <td align="right" width="110" class="tit" style="line-height:38px;" id="coverImageFile">封面图像</td><!--流程中能看到，但在首页看不到-->
        <td   colspan="2" valign="top" style="padding:3px;" id="coverImageFile2">
            <input id="coverImageFileList" name="coverImageFileList" type="text" mulaccept="image/*" class="cselectorImageUpload" sizelength="1" extension="gif,jpg,jpeg,png"
                   btnmsg="<i class='iconfont' title='添加'>&#xe641;</i>" href="sys/file/uploadProcessFiles?pmInsType=A&pmInsTypePart=1" />
        </td>
    </tr>
    <tr>
        <td align="right" width="110" class="tit" style="line-height:38px;" id="authBookFile">授权书</td><!--流程中能看到，但在首页看不到-->
        <td  colspan="2"  valign="top" style="padding:3px;"  id="authBookFile2">
            <input id="authBookFileList" name="authBookFileList" type="text" file="true" class="cselectorImageUpload" mulaccept="true" btnmsg="<i class='iconfont' title='添加'>&#xe641;</i>"
                   href="sys/file/uploadProcessFiles?pmInsType=A&pmInsTypePart=2"/>
        </td>
    </tr>
    <tr>
        <td align="right" width="110" class="tit" style="line-height:38px;" id="accessoryFile">附件</td>
        <td  colspan="2"  valign="top" style="padding:3px;">
            <input id="accessoryFileList" name="accessoryFileList" type="text" file="true" class="cselectorImageUpload" mulaccept="true" btnmsg="<i class='iconfont' title='添加'>&#xe641;</i>"
                href="sys/file/uploadProcessFiles?pmInsType=A&pmInsTypePart=3"/>
        </td>
    </tr>
    <tr id="videoFile" style="display: none">
        <td align="right" width="110" class="tit" style="line-height:38px;">视频<font class="col_r">支持mp4、rm、rmvb、flv格式</font></td>
        <td  colspan="2" valign="top" style="padding:3px;">
            <input id="videoFileList" name="videoFileList" type="text" file="true" class="cselectorImageUpload" mulaccept="true" btnmsg="<i class='iconfont' title='添加'>&#xe641;</i>"
                   href="sys/file/uploadProcessFiles?pmInsType=A&pmInsTypePart=4"/>
        </td>
    </tr>
</table>
</form>
</body>
</html>
