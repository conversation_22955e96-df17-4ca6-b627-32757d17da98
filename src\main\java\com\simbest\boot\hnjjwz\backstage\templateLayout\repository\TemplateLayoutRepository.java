package com.simbest.boot.hnjjwz.backstage.templateLayout.repository;/**
 * Created by KZH on 2019/5/30 17:22.
 */

import com.simbest.boot.base.repository.LogicRepository;
import com.simbest.boot.hnjjwz.backstage.templateLayout.model.TemplateLayout;
import org.springframework.data.jpa.repository.Query;
import org.springframework.data.repository.query.Param;

import java.util.List;

/**
 * <AUTHOR>
 * @create 2019-05-30 17:22
 * @desc
 **/
public interface TemplateLayoutRepository extends LogicRepository<TemplateLayout,String> {


    /**
     *  首页模板
     * @return
     */
    @Query(value = "select  t.* from us_template_layout t where  t.location_type='indexTemplate'  and t.enabled=1  ORDER BY t.created_time",
            nativeQuery = true)
    List<TemplateLayout> findTemplateLayout();


    /**
     * 规章制度模板
     */
    @Query(value = "select  t.* from us_template_layout t where  t.location_type='rulesGauge'  and t.enabled=1  ORDER BY t.created_time",
            nativeQuery = true)
    List<TemplateLayout> findRulesGaugeLayout();

    /**
     * 巡察工作模板
     */
    @Query(value = "select  t.* from us_template_layout t where  t.location_type='inspectionWork'  and t.enabled=1  ORDER BY t.created_time",
            nativeQuery = true)
    List<TemplateLayout> findInspectionWorkLayout();

    /**
     *嵌入式防控监督模板
     */
    @Query(value = "select  t.* from us_template_layout t where  t.location_type='embedded'  and t.enabled=1  ORDER BY t.created_time",
            nativeQuery = true)
    List<TemplateLayout> findEmbeddedLayout();

    /**
     * 课题研究模板
     */
    @Query(value = "select  t.* from us_template_layout t where  t.location_type='research'  and t.enabled=1  ORDER BY t.created_time",
            nativeQuery = true)
    List<TemplateLayout> findResearchLayout();

    /**
     * 廉洁教育模板
     */
    @Query(value = "select  t.* from us_template_layout t where  t.location_type='education'  and t.enabled=1  ORDER BY t.created_time",
            nativeQuery = true)
    List<TemplateLayout> findEducationLayout();

    /**
     * 廉洁文化模板
     */
    @Query(value = "select  t.* from us_template_layout t where  t.location_type='culture'  and t.enabled=1  ORDER BY t.created_time",
            nativeQuery = true)
    List<TemplateLayout> findCultureLayout();

    /**
     * 信访举报模板
     */
    @Query(value = "select  t.* from us_template_layout t where  t.location_type='petitionLetter'  and t.enabled=1  ORDER BY t.created_time",
            nativeQuery = true)
    List<TemplateLayout> findPetitionLetter();

    /**
     *
     */
    @Query(value = "select  t.* from us_template_layout t where  t.location_id=:id  and  t.enabled=1  ORDER BY t.created_time",
            nativeQuery = true)
    List<TemplateLayout> findtemplateLayout(@Param("id") String id);

    /**
     * 纪律审查与监督模板
     */
    @Query(value = "select  t.* from us_template_layout t where  t.location_type='superviSion'  and t.enabled=1  ORDER BY t.created_time",
            nativeQuery = true)
    List<TemplateLayout> findSupervisionLayout();

    /**
     * 家风栏目模板
     */
    @Query(value = "select  t.* from us_template_layout t where  t.location_type=:locationType  and t.enabled=1  ORDER BY t.created_time",
            nativeQuery = true)
    List<TemplateLayout> findDanColumnsLayout(@Param("locationType")String locationType);

    /**
     * 修改图片
     */
    @Query(value = "select  t.* from us_template_layout t where  t.location_name=:locationName  and  t.enabled=1 ",
            nativeQuery = true)
    TemplateLayout findlocationName(@Param("locationName")String locationName);


    /**
     * 2021拆分分公司为党廉信息动态,纪检信息动态
     */
    @Query(value = "select  t.* from us_template_layout t where  t.location_type='indexTemplate'  and t.enabled=1 and template_rows='downCompany' ORDER BY t.created_time",
            nativeQuery = true)
    List<TemplateLayout> findIndexTemplateDownCompany();

    /**
     * 2021拆分以案示警为以案示警,四风八规曝光台
     */
    @Query(value = "select  t.* from us_template_layout t where  t.location_type='indexTemplate'  and t.enabled=1 and template_rows='warnCompany' ORDER BY t.created_time",
            nativeQuery = true)
    List<TemplateLayout> findIndexTemplateWarnCompany();
}
