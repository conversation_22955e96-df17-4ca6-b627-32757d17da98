package com.simbest.boot.hnjjwz.sharingPlatform.repository;

import com.simbest.boot.base.repository.LogicRepository;
import com.simbest.boot.hnjjwz.sharingPlatform.model.MenuDeploy;
import org.springframework.data.domain.Page;
import org.springframework.data.domain.Pageable;
import org.springframework.data.jpa.repository.Query;
import org.springframework.data.repository.query.Param;
import org.springframework.stereotype.Repository;

@Repository
public interface MenuDeployRepository  extends LogicRepository<MenuDeploy,String> {

    /**
     * 根据菜单名(模糊)以及父菜单名(模糊)查询分页
     * @param menuName
     * @param parentMenuName
     * @return
     */
    @Query (value = " SELECT * FROM us_menu_deploy umd " +
            " WHERE umd.menu_name LIKE CONCAT( CONCAT('%',:menuName),'%') " +
            " AND umd.parent_menu_name LIKE CONCAT( CONCAT('%',:parentMenuName),'%') " +
            " AND umd.enabled =1 AND umd.removed_time IS NULL ",
            countQuery = "SELECT COUNT (*)" +
                    " FROM us_menu_deploy umd " +
            " WHERE umd.menu_name LIKE CONCAT( CONCAT('%',:menuName),'%') " +
            " AND umd.parent_menu_name LIKE CONCAT( CONCAT('%',:parentMenuName),'%') " +
            " AND umd.enabled =1 AND umd.removed_time IS NULL ",nativeQuery = true)
    Page<MenuDeploy> findAllDim( @Param ("menuName") String menuName, @Param("parentMenuName")String parentMenuName, Pageable pageable );
}
