package com.simbest.boot.hnjjwz.todo;

import cn.hutool.core.util.ObjectUtil;
import cn.hutool.core.util.StrUtil;
import com.cmcc.mss.importsrvresponse.ImportSrvResponse;
import com.cmcc.mss.sb_oa_oa_importtodocloselistinfosrv.SBOAOAImportToDoCloseListInfoSrvInputItem;
import com.cmcc.mss.sb_oa_oa_importtodoopenlistinfosrv.SBOAOAImportToDoOpenListInfoSrvInputItem;
import com.google.common.collect.Maps;
import com.simbest.boot.base.exception.Exceptions;
import com.simbest.boot.base.repository.LogicRepository;
import com.simbest.boot.base.service.impl.LogicService;
import com.simbest.boot.bps.enums.ToDoEnum;
import com.simbest.boot.bps.process.bussiness.model.ActBusinessStatus;
import com.simbest.boot.cmcc.ntodo.CloseTodoSrvClient;
import com.simbest.boot.cmcc.ntodo.OpenTodoSrvClient;
import com.simbest.boot.hnjjwz.constants.Constants;
import com.simbest.boot.hnjjwz.todo.model.UsTodoModel;
import com.simbest.boot.hnjjwz.todo.service.IUsTodoModelService;
import com.simbest.boot.hnjjwz.util.BpsConfig;
import com.simbest.boot.hnjjwz.util.SMSTool;
import com.simbest.boot.util.DateUtil;
import com.simbest.boot.util.json.JacksonUtils;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.data.jpa.domain.Specification;
import org.springframework.stereotype.Service;

import java.math.BigDecimal;
import java.time.LocalDateTime;
import java.util.Map;

/**
 * <strong>Title : TodoBusOperatorService</strong><br>
 * <strong>Description : 统一代办业务操作</strong><br>
 * <strong>Create on : $date$</strong><br>
 * <strong>Modify on : $date$</strong><br>
 * <strong>Copyright (C) Ltd.</strong><br>
 *
 * @<NAME_EMAIL>
 * @version <strong>V1.0.0</strong><br>
 * <strong>修改历史:</strong><br>
 * 修改人 修改日期 修改描述<br>
 * -------------------------------------------<br>
 */
@Slf4j
@Service
public class TodoBusOperatorService extends LogicService<UsTodoModel,String> {

    @Autowired
    private BpsConfig bpsConfig;

    @Autowired
    private IUsTodoModelService usTodoModelService;

    public static final Map<String,String> todoHtml;

    @Autowired
    private SMSTool smsTool;

    static {
        Map<String,String> todoHtmlTmp = Maps.newConcurrentMap( );
        //三个流程对应使用同一个页面，这是测试的数据，对应bps数据库的WFPROCESSDEFINE表。需要在BPS工作空间中
        //部署了BPS流程包后才会有。
        //正式的也是，正式的数据库在BPS_PRD7374中，用户名为bps75，密码为PASSWORD

        todoHtmlTmp.put("com.hnjjwz.flow.county_company","/html/apply/programaDataFormTabs.html");
        todoHtmlTmp.put("com.hnjjwz.flow.provincial_company","/html/apply/programaDataFormTabs.html");
        todoHtmlTmp.put("slideShow","/html/slideShow/slideShowList.html");//轮播新闻
        todoHtmlTmp.put("announcement","/html/announcement/announcementList.html");//公告

        //正式数据
        todoHtml = todoHtmlTmp;
    }

    public TodoBusOperatorService(LogicRepository<UsTodoModel, String> logicRepository) {
        super(logicRepository);
    }

    /**
     * 推送统一代办
     * @param businessStatus                 业务状态操作对象
     * @param userName                          审批人
     */
    public void openTodo( ActBusinessStatus businessStatus,String userName){
        log.debug( "TodoBusOperatorService>>>>>>>openTodo>>>>>调用接口平台推送统一代办开始");
        Boolean sendFalg = false;
        String oaHtmlUrl = "";

        String receiptCode = businessStatus.getReceiptCode();
        if (receiptCode.startsWith("C")){
            oaHtmlUrl = bpsConfig.hostPost+ "/" + Constants.APP_CODE + todoHtml.get("announcement");//公告
        }else if(receiptCode.startsWith("E")){
            oaHtmlUrl = bpsConfig.hostPost+ "/" + Constants.APP_CODE + todoHtml.get("slideShow");//轮播新闻
        }
        else {
            oaHtmlUrl = bpsConfig.hostPost+ "/" + Constants.APP_CODE +  todoHtml.get(businessStatus.getProcessDefName());
        }
        //代办回调路径后面带的参数，即url ?后面的数据
        String urlParams = "?type=task&processInstId=" + businessStatus.getProcessInstId() + "&location="+ businessStatus.getActivityDefId()  + "&processDefName="+ businessStatus.getProcessDefName()  +"&workItemId="+ businessStatus.getWorkItemId()  +"&name=auditVal&appcode=hnjjwz&from=oa"+"&pmInsType="+(businessStatus.getReceiptCode()).substring(0,1);
        try {
            log.debug( "businessStatus>>>>>>>>"+businessStatus.toString());
            //代办回调的路径
        /*        UsTodoModel usTodoModel = new UsTodoModel();
            usTodoModel.setWorkItemId(businessStatus.getWorkItemId().toString());
            usTodoModel.setActivityDefId(businessStatus.getActivityDefId());
            usTodoModel.setProcessInstanceId(businessStatus.getProcessInstId().toString());
            usTodoModel.setBusinessKey(businessStatus.getBusinessKey());
            usTodoModel.setBusinessStatusId(businessStatus.getId());
            usTodoModel.setProcessDefId(businessStatus.getProcessDefId().toString());
            usTodoModel.setCreator(businessStatus.getCreateUserCode());
            usTodoModel.setCreatedTime(businessStatus.getCreateTime());
            usTodoModel.setModifiedTime(businessStatus.getEndTime());
            //usTodoModel.setRemovedTime(LocalDateTime.now());
            usTodoModel.setModifier(userName);
            usTodoModel.setUserName(userName);
            usTodoModel.setSender(userName);
            usTodoModel.setTitle(businessStatus.getReceiptTitle());
            usTodoModel.setTypeStatus(ToDoEnum.open.getValue());
            usTodoModel.setOaHtmlUrl(oaHtmlUrl);
            usTodoModel.setUrlParams(urlParams);
            usTodoModel.setWorkFlag(false);
            usTodoModel.setEnabled(true);
            usTodoModel.setSendFlag(true);
            usTodoModel.setSendDate(businessStatus.getEndTime());
            //先保存本地推送记录，然后再调用接口平台推送代办
            log.debug( "usTodoModelService>>>insert>>>>>开始"+usTodoModel.toString());
            //UsTodoModel usTodoModel1 = usTodoModelService.insert( usTodoModel );
            UsTodoModel usTodoModel1 = usTodoModelService.savaLocalTodoData( usTodoModel );
            log.debug( "usTodoModelService>>>insert>>>>>"+usTodoModel1.toString());
            log.debug( "usTodoModelService>>>insert>>>>>结束");*/
            SBOAOAImportToDoOpenListInfoSrvInputItem inputItem = new SBOAOAImportToDoOpenListInfoSrvInputItem();
            inputItem.setPRIKEY(businessStatus.getBusinessKey());
            inputItem.setDOCID( String.valueOf(businessStatus.getProcessInstId()) );
            inputItem.setWORKID( String.valueOf(businessStatus.getWorkItemId()) );
            inputItem.setUSERID(userName);
            inputItem.setTITLE(businessStatus.getReceiptTitle() );
            inputItem.setTYPE( "1" );
            inputItem.setSTARTTIME( DateUtil.localDateTimeToXmlDate(LocalDateTime.now()) );
            inputItem.setURL( oaHtmlUrl + urlParams );
            inputItem.setSENDER( userName);
            //统一代办唯一标识
            inputItem.setSYSID( BigDecimal.valueOf( Integer.parseInt(Constants.APP_SYS_ID)));
            inputItem.setDOCTYPE( Constants.APP_NAME );
            inputItem.setSOURCEID( "PR" );
            ImportSrvResponse importSrvResponse =  OpenTodoSrvClient.openTodo( Constants.APP_CODE, inputItem);
            String serviceflag = importSrvResponse.getSERVICEFLAG();
            if (StrUtil.equals(serviceflag, Constants.SUCCESS_FLAG)) {
                sendFalg = true;
            }
        }catch ( Exception e ){
            log.error( "TodoBusOperatorService>>>>>>>openTodo>>>>>调用接口平台推送统一代办异常"+ e.getMessage());
            Exceptions.printException( e );
            //发送推送统一待办异常短信
            String msgContent = Constants.APP_NAME.concat(":【").concat(businessStatus.getReceiptTitle()).concat("】").concat("，待办人：【").concat(userName).concat("】").concat("，调用接口平台推送统一代办异常");
            smsTool.sendMsgUtil(msgContent);
            throw e;
        }
        saveTodoModel(businessStatus,userName,oaHtmlUrl,urlParams,sendFalg,Constants.APP_SYS_ID,Constants.APP_NAME,null);
        log.debug( "TodoBusOperatorService>>>>>>>openTodo>>>>>调用接口平台推送统一代办结束");
    }

    /**
     *  核销统一代办
     * @param businessStatus                 业务状态操作对象
     * @param userName                          审批人
     */
    public void closeTodo(ActBusinessStatus businessStatus,String userName){
        //location=nma.depart_manager&processInstId=1841&processDefName=com.nma.flow.provincial_company&workItemId=2262&name=auditVal
        log.debug( "TodoBusOperatorService>>>>>>>closeTodo>>>>>调用接口平台推送统一代办开始");
        Boolean sendFalg = false;
        try {
            log.debug( "businessStatus>>>>>>>>"+businessStatus.toString());
          /*  UsTodoModel usTodoModel = new UsTodoModel();
            usTodoModel.setWorkItemId(businessStatus.getWorkItemId().toString());
            usTodoModel.setActivityDefId(businessStatus.getActivityDefId());
            usTodoModel.setProcessInstanceId(businessStatus.getProcessInstId().toString());
            usTodoModel.setBusinessKey(businessStatus.getBusinessKey());
            usTodoModel.setBusinessStatusId(businessStatus.getId());
            usTodoModel.setProcessDefId(businessStatus.getProcessDefId().toString());
            usTodoModel.setRemovedTime(LocalDateTime.now());
            usTodoModel.setUserName(userName);
            usTodoModel.setSender(userName);
            usTodoModel.setTitle(businessStatus.getReceiptTitle());
            usTodoModel.setTypeStatus(ToDoEnum.close.getValue());
            usTodoModel.setEnabled(true);
            usTodoModel.setWorkFlag(true);
            usTodoModel.setSendFlag(false);
            usTodoModel.setSendDate(businessStatus.getEndTime());
            usTodoModelService.updateLocalTodoData( usTodoModel );*/
            SBOAOAImportToDoCloseListInfoSrvInputItem inputItem = new SBOAOAImportToDoCloseListInfoSrvInputItem();
            inputItem.setPRIKEY(businessStatus.getBusinessKey());
            inputItem.setDOCID( String.valueOf(businessStatus.getProcessInstId()));
            inputItem.setWORKID(String.valueOf(businessStatus.getWorkItemId()));
            inputItem.setUSERID( userName);
            inputItem.setSOURCEID("PR");
            inputItem.setSYSID( BigDecimal.valueOf( Integer.parseInt(Constants.APP_SYS_ID) ) );
            inputItem.setCLOSETIME( DateUtil.localDateTimeToXmlDate(LocalDateTime.now()) );
            ImportSrvResponse importSrvResponse =CloseTodoSrvClient.closeTodo(Constants.APP_CODE, inputItem);
            String serviceflag = importSrvResponse.getSERVICEFLAG();
            if (StrUtil.equals(serviceflag, Constants.SUCCESS_FLAG)) {
                sendFalg = true;
            }
        }catch ( Exception e ){
            log.error( "TodoBusOperatorService>>>>>>>closeTodo>>>>>调用接口平台推送统一代办异常"+ e.getMessage());
            Exceptions.printException( e );
            String msgContent = Constants.APP_NAME.concat(":【").concat(businessStatus.getReceiptTitle()).concat("】").concat("，待办人：【").concat(userName).concat("】").concat("，调用接口平台核销统一代办异常");;
            smsTool.sendMsgUtil(msgContent);
            throw e;

        }
        closeTodoModel(businessStatus,userName,sendFalg,null);
        log.debug( "TodoBusOperatorService>>>>>>>closeTodo>>>>>调用接口平台推送统一代办结束");
    }

    /**
     * 删除统一代办
     * @param actBusinessStatus                 业务状态操作对象
     * @param userName                          审批人
     */
    public void cancelTodo(ActBusinessStatus actBusinessStatus,String userName){}


    @SuppressWarnings("unused")
    private UsTodoModel saveTodoModel(ActBusinessStatus businessStatus,String todoUser,String oaHtmlUrl,String urlParams,Boolean sendFalg,String sysId,String sysName,String staus){
        UsTodoModel usTodoModel = new UsTodoModel();
        try {
            usTodoModel.setBusinessKey(businessStatus.getBusinessKey());
            usTodoModel.setWorkItemId(StrUtil.toString(businessStatus.getWorkItemId()));
            Specification<UsTodoModel> usTodoModelSpecification = usTodoModelService.getSpecification(usTodoModel);
            usTodoModel = usTodoModelService.findOne(usTodoModelSpecification);
        }catch (Exception e){
            Exceptions.printException( e );
        }finally {
            if (cn.hutool.core.util.ObjectUtil.isEmpty(usTodoModel)) {
                usTodoModel = new UsTodoModel();
                usTodoModel.setWorkItemId(businessStatus.getWorkItemId().toString());
                usTodoModel.setActivityDefId(businessStatus.getActivityDefId());
                usTodoModel.setProcessInstanceId(StrUtil.toString(businessStatus.getProcessInstId()));
                usTodoModel.setBusinessKey(businessStatus.getBusinessKey());
                usTodoModel.setBusinessStatusId(businessStatus.getId());
                usTodoModel.setProcessDefId(StrUtil.toString(businessStatus.getProcessDefId()));
                usTodoModel.setCreator(businessStatus.getCreateUserCode());
                usTodoModel.setCreatedTime(businessStatus.getCreateTime());
                usTodoModel.setModifiedTime(businessStatus.getEndTime());
                usTodoModel.setModifier(todoUser);
                usTodoModel.setUserName(todoUser);
                usTodoModel.setSender(todoUser);
                usTodoModel.setTitle(businessStatus.getReceiptTitle());
                usTodoModel.setTypeStatus(ToDoEnum.open.getValue());
                usTodoModel.setOaHtmlUrl(oaHtmlUrl);
                usTodoModel.setUrlParams(urlParams);
                usTodoModel.setWorkFlag(true);
                usTodoModel.setEnabled(true);
                usTodoModel.setSendFlag(sendFalg);
                usTodoModel.setSendDate(businessStatus.getEndTime());
                usTodoModel.setSysId(sysId);
                usTodoModel.setSysName(sysName);
                usTodoModel = usTodoModelService.savaLocalTodoData(usTodoModel);
            }
        }
        log.warn("usTodoModelService>>>insert>>>>>【{}】", JacksonUtils.obj2json(usTodoModel));
        return usTodoModel;
    }

    @SuppressWarnings("unused")
    private UsTodoModel closeTodoModel(ActBusinessStatus businessStatus,String todoUser,Boolean sendFalg,String status){
        UsTodoModel usTodoModel = new UsTodoModel();
        try {
            usTodoModel.setBusinessKey(businessStatus.getBusinessKey());
            usTodoModel.setSender(todoUser);
            usTodoModel.setWorkFlag(true);
            usTodoModel.setWorkItemId(StrUtil.toString(businessStatus.getWorkItemId()));
            Specification<UsTodoModel> usTodoModelSpecification = usTodoModelService.getSpecification(usTodoModel);
            usTodoModel = usTodoModelService.findOne(usTodoModelSpecification);
            log.warn("TodoBusOperatorService>>>>>>>usTodoModel>>>>>获取参数"+JacksonUtils.obj2json(usTodoModel));
        }catch (Exception e){
            Exceptions.printException( e );
        }finally {
            if (ObjectUtil.isNotEmpty(usTodoModel)){
                usTodoModel.setWorkFlag(false);
                usTodoModel.setSendFlag(sendFalg);
                usTodoModel.setTypeStatus(ToDoEnum.open.getValue() + "-" + ToDoEnum.close.getValue());
                usTodoModel = usTodoModelService.savaLocalTodoData(usTodoModel);
            }
        }
        log.warn("usTodoModelService>>>close>>>>>【{}】",JacksonUtils.obj2json(usTodoModel));
        return usTodoModel;
    }











}
