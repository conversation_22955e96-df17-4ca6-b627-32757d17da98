package com.simbest.boot.hnjjwz.sharingPlatform.service;

import com.simbest.boot.base.service.ILogicService;
import com.simbest.boot.base.web.response.JsonResponse;
import com.simbest.boot.hnjjwz.sharingPlatform.model.SharingPlatform;
import org.springframework.data.domain.Pageable;

import java.util.Map;

/**
 * 作用：共享平台
 * 作者：刘萌
 * 时间：2019/05/05
 */
public interface ISharingPlatformService extends ILogicService<SharingPlatform,String> {

    JsonResponse findAllDim( Map<String,Object> sharingPlatformMap, Pageable pageable );
}
