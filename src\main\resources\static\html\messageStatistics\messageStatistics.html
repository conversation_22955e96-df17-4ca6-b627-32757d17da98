<!DOCTYPE html>
<html xmlns="http://www.w3.org/1999/xhtml"
      xmlns:th="http://www.thymeleaf.org">
<head>
    <title>信息统计</title>
    <meta http-equiv="Content-Type" content="text/html; charset=utf-8"/>
    <meta http-equiv="X-UA-Compatible" content="IE=edge" />
    <meta name="viewport" content="width=device-width, initial-scale=1, shrink-to-fit=no"/>
    <!--
    <link href="http://************:8088/simbestui/fonts/iconfont/iconfont.css?v=svn.revision" th:href="@{http://************:8088/simbestui/fonts/iconfont/iconfont.css?v=svn.revision}" rel="stylesheet"/>
    -->
    <link href="../../fonts/iconfont/iconfont.css?v=svn.revision" th:href="@{/fonts/iconfont/iconfont.css?v=svn.revision}" rel="stylesheet"/>
    <link href="http://************:8088/simbestui/js/themes/default/easyui.css?v=svn.revision" th:href="@{http://************:8088/simbestui/js/themes/default/easyui.css?v=svn.revision}" rel="stylesheet"/>
    <link href="http://************:8088/simbestui/js/themes/icon.css?v=svn.revision" th:href="@{http://************:8088/simbestui/js/themes/icon.css?v=svn.revision}" rel="stylesheet"/>
    <link href="http://************:8088/simbestui/css/public.css?v=svn.revision" th:href="@{http://************:8088/simbestui/css/public.css?v=svn.revision}" rel="stylesheet"/>
    <link href="http://************:8088/simbestui/css/pages.css?v=svn.revision" th:href="@{http://************:8088/simbestui/css/pages.css?v=svn.revision}" rel="stylesheet"/>
    <script src="http://************:8088/simbestui/js/jquery.min.js?v=svn.revision" th:src="@{http://************:8088/simbestui/js/jquery.min.js?v=svn.revision}" type="text/javascript"></script>
    <script src="http://************:8088/simbestui/js/jquery.easyui.min.js?v=svn.revision" th:src="@{http://************:8088/simbestui/js/jquery.easyui.min.js?v=svn.revision}" type="text/javascript"></script>
    <script src="http://************:8088/simbestui/js/easyui-lang-zh_CN.js?v=svn.revision" th:src="@{http://************:8088/simbestui/js/easyui-lang-zh_CN.js?v=svn.revision}" type="text/javascript"></script>
    <script src="../../js/jquery.config.js?v=svn.revision" th:src="@{/js/jquery.config.js?v=svn.revision}" type="text/javascript"></script>
    <script src="http://************:8088/simbestui/js/jquery.zjsfile.js?v=svn.revision" th:src="@{http://************:8088/simbestui/js/jquery.zjsfile.js?v=svn.revision}" type="text/javascript"></script>
    <script src="http://************:8088/simbestui/js/jquery.aduq.js?v=svn.revision" th:src="@{http://************:8088/simbestui/js/jquery.aduq.js?v=svn.revision}" type="text/javascript"></script>
    <script src="http://************:8088/simbestui/js/jquery.process.js?v=svn.revision" th:src="@{http://************:8088/simbestui/js/jquery.process.js?v=svn.revision}" type="text/javascript"></script>
    <script type="text/javascript">

        $(function(){
            //showDialog做为打开对话框的公共class名称   showDialogindex做为打开修改对话框传行的索引   delete做为删除按钮配置命令   deleteid做为删除传值  searchtable做为条件查询的按钮公共class名称
            var pageparam={
                "listtable":{
                    /*"idFiled":"ID", 用于自定义id的情况，当接口查出的id不为id，比如是ID时，在这里设置字段名 */
                    "listname":"#messageStatisticsTable",//table列表的id名称，需加# menuExpalinTable
                    "querycmd":"action/Message/findMessageStatistics",//table列表的查询命令
                    "checkboxall":true,
                    "contentType":"application/json; charset=utf-8",//table列表的传参方式，如果是json就写，不写默认kv
                    "nowrap": true,//把数据显示在一行里,默认true
                    "styleClass": "noScroll",
                    "pageSize":20,
                    "pageList":[20],
                    "frozenColumns":[[
                    ]],//固定在左侧的列
                    "columns":[[//列
                        { title: "单位", field: "COMPANY", width: 150},
                        { title: "上报信息量", field: "SBL", width: 150},
                        { title: "采编量", field: "SPTG", width: 150,sortable:true},
                        { title: "未采编量", field: "WCBL", width: 150,sortable:true},
                        { title: "采编率", field: "CBL", width: 150 ,sortable:true}
                        /*{
                            field: "opt", title: "操作", width: 110, rowspan: 1,//align：对齐此列的数据，可以用left、right、center
                            formatter: function (value, row,index) {//单元格的格式化函数，需要三个参数：value：字段的值；rowData：行的记录数据；rowIndex：行的索引
                                var g = "<a href='#' class='showDialog'  showDialogindex='" + index + "'>【修改】</a>"
                                    +"<a href='#' delete='action/indexMenu/deleteById' deleteid='"+row.id+"'>【删除】</a>";
                                return g;
                            }
                        }*/
                    ] ]
                    /*"pagerbar": [{
                        id:"deleteall",
                        iconCls: 'icon-remove',
                        text:"批量删除&nbsp;"
                    }],
                    "deleteall":{//批量删除deleteall.id要与pagerbar.id相同
                        "id":"deleteall",
                        "url":"action/indexMenu/deleteAllByIds",
                        "contentType":"application/json; charset=utf-8"
                    }*/
                },
                "dialogform":{
                    "dialogid":"#buttons",//对话框的id
                    "formname":"#messageStatisticsTableAddForm",//新增或修改对话框的formid需加#
                    //"insertcmd":"action/indexMenu/create",//新增命令
                    "updatacmd":"action/Message/update",//修改命令
                    "onSubmit":function(data){
                    }
                }
            };
            loadGrid(pageparam);


            //导出Excel
            $(".export").on("click",function(){

                $("#exportForm .department").val($("#department").combobox("getValue"));
                $("#exportForm .year").val($("#year").combobox("getValue"));
                $("#exportForm .month").val($("#month").combobox("getValue"));
                $("#exportForm .quarter").val($("#quarter").combobox("getValue"));
                $("#exportForm .semiyearly").val($("#semiyearly").combobox("getValue"));
                $("#exportForm .messageType").val($("#messageType").combobox("getValue"));

                $("#exportForm").attr("action",web.rootdir+"action/Message/exportNDExcel");
                $("#exportForm .exportSubmit").trigger("click");
            });

            $(":radio").click(function(){
                var flag = $(this).attr("flag");
                if(flag == "月度") {
                    $('#quarter').val("");
                    $('#semiyearly').val("");
                    $('#month').combobox({
                        disabled: false
                    });
                    $('#quarter').combobox({
                        disabled: true
                    });
                    $('#semiyearly').combobox({
                        disabled: true
                    });
                }
                if(flag == "季度") {
                    $('#month').val("");
                    $('#semiyearly').val("");
                    $('#month').combobox({
                        disabled: true
                    });
                    $('#quarter').combobox({
                        disabled: false
                    });
                    $('#semiyearly').combobox({
                        disabled: true
                    });
                }
                if(flag == "半年") {
                    $('#month').val("");
                    $('#quarter').val("");
                    $('#month').combobox({
                        disabled: true
                    });
                    $('#quarter').combobox({
                        disabled: true
                    });
                    $('#semiyearly').combobox({
                        disabled: false
                    });
                }
            });


        });
        //form表单填充数据之前做什么,data为填充数据的集合,isupdate表示是新增还是修改,若为新增则data为{}
        function beforerender(data,isupdate){
            if(isupdate){
                $('.update-readonly').hide();
            }else{
                $('.update-readonly').show();
            }
        };
        //回调函数
        window.programaInfo=function(data){
        };




    </script>
    <style>

        .datagrid-body{
            height: auto!important;
        }
    </style>
</head>
<body class="body_page">
<!--searchform messageStatisticsTable-->
<form id="messageStatisticsTableQueryForm" >
    <table border="0" cellpadding="0" cellspacing="4" width="100%">
        <tr>
            <td align="centre" style="width:80px;">单位：</td>
            <td>
                <input id="department" name="department" class="easyui-combobox" value=""
                           style="width: 180px; height: 32px;" data-options="
                     valueField: 'value',
                     ischooseall:true,
                     ischooseallTxt:'请选择',
                     textField: 'value',
                     contentType:'application/json; charset=utf-8',
                     url: web.rootdir+'action/Message/findDictValue?dictType=departmentType'"/>
            </td>

            <td  align="centre" style="width:80px;">年份：</td>
            <td>
                <input id="year" name="year" class="easyui-combobox" value=""
                       style="width: 180px; height: 32px;" data-options="
                     valueField: 'value',
                     ischooseall:true,
                     ischooseallTxt:'请选择',
                     textField: 'value',
                     contentType:'application/json; charset=utf-8',
                     url: web.rootdir+'action/Message/findDictValue?dictType=yearType'"/>
            </td>

            <td align="centre" style="width:80px;"><input class='wauto' type="radio" name="monthRadio" flag="月度"><label>月度：</label></td>
            <td>
                <input id="month" name="month" class="easyui-combobox" value=""
                       style="width: 180px; height: 32px;" data-options="
                     valueField: 'value',
                     ischooseall:true,
                     ischooseallTxt:'请选择',
                     textField: 'name',
                     contentType:'application/json; charset=utf-8',
                     url: web.rootdir+'action/Message/findDictValue?dictType=monthType'"/>
            </td>
            <td width="300px"></td>
        </tr>
        <tr>

            <td align="centre" style="width:80px;"><input class='wauto' type="radio" name="monthRadio" flag="季度"><label>季度：</label></td>
            <td>
                <input id="quarter" name="quarter" class="easyui-combobox" value=""
                       style="width: 180px; height: 32px;" data-options="
                     valueField: 'value',
                     ischooseall:true,
                     ischooseallTxt:'请选择',
                     textField: 'name',
                     contentType:'application/json; charset=utf-8',
                     url: web.rootdir+'action/Message/findDictValue?dictType=quarterType'"/>
            </td>
            <td align="centre" style="width:80px;"><input class='wauto' type="radio" name="monthRadio" flag="半年"><label>半年：</label></td>
            <td>
                <!--<input name="needType" type="text" value=""/>-->
                <select id="semiyearly" name="semiyearly" class="easyui-combobox" data-options="panelHeight:'auto'" style="width: 180px;height:32px;">
                    <option value="">--请选择--</option>
                    <option value="01,02,03,04,05,06">上半年</option>
                    <option value="07,08,09,10,11,12">下半年</option>
                </select>
            </td>



            <td align="centre" style="width:80px;">信息类型：</td>
            <td>
                <input id="messageType" name="messageType" class="easyui-combobox" value=""
                       style="width: 180px; height: 32px;" data-options="
                     valueField: 'programaCode',
                     textField: 'programaName',
                     contentType:'application/json; charset=utf-8',
                     url: web.rootdir+'action/programaInfo/findAllNoPage'"/>
            </td>

            <td  colspan=2 align="centre" style="width:80px;" >
                <div class="w100">
                    <a class="btn fr searchtable"><font>生成报表</font></a>
                    <a class="btn btn a_success fr export mr10"><span>导出Excel</span></a>

                </div>
            </td>
        </tr>
    </table>
</form>
<!--table-->
<div class="messageStatisticsTable"><table id="messageStatisticsTable"></table></div>

<form id="exportForm" class="hide" method="post">
    <input  class="department" name="department" type="hidden"/>
    <input  class="year" name="year" type="hidden"/>
    <input  class="month" name="month" type="hidden"/>
    <input  class="quarter" name="quarter" type="hidden"/>
    <input  class="semiyearly" name="semiyearly" type="hidden"/>
    <input  class="messageType" name="messageType" type="hidden"/>
    <input type="submit" class="exportSubmit"/>
</form>
<!--新增修改的dialog页面-->
<div id="buttons" title="新增或修改" class="easyui-dialog" style="width:800px;height:500px;">
</div>
</body>
</html>
