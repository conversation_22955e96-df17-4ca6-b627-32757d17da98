package com.simbest.boot.hnjjwz.newcolumn.service.impl;

import com.simbest.boot.base.repository.LogicRepository;
import com.simbest.boot.base.service.impl.LogicService;
import com.simbest.boot.hnjjwz.backstage.announcement.model.Announcement;
import com.simbest.boot.hnjjwz.backstage.slideshow.model.SlideShow;
import com.simbest.boot.hnjjwz.newcolumn.model.SysNewColumnModel;
import com.simbest.boot.hnjjwz.newcolumn.repository.SysNewColumnRepository;
import com.simbest.boot.hnjjwz.newcolumn.repository.SysNewProgramaRepository;
import com.simbest.boot.hnjjwz.newcolumn.service.ISysNewProgramaService;
import com.simbest.boot.hnjjwz.process.wf.model.ProgramaDataForm;
import com.simbest.boot.hnjjwz.process.wf.service.IProgramaDataFormService;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.scheduling.annotation.Async;
import org.springframework.stereotype.Service;

@Service
@Slf4j
public class SysNewProgramaServiceImpl extends LogicService<ProgramaDataForm, String> implements ISysNewProgramaService {
    private SysNewProgramaRepository repository;

    @Autowired
    public SysNewProgramaServiceImpl(SysNewProgramaRepository repository) {
        super(repository);
        this.repository = repository;
    }

    @Autowired
    private IProgramaDataFormService programaDataFormService;

    @Async
    @Override
    public void saveSlideShow(SlideShow resultApprovalForm) {
        programaDataFormService.saveSlideShow(resultApprovalForm);
    }

    @Async
    @Override
    public void saveAnnouncement(Announcement announcement) {
        programaDataFormService.saveAnnouncement(announcement);
    }
}
