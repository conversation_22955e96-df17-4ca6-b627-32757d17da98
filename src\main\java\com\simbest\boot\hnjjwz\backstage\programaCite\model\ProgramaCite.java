package com.simbest.boot.hnjjwz.backstage.programaCite.model;

import com.simbest.boot.base.annotations.EntityIdPrefix;
import com.simbest.boot.base.model.LogicModel;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.*;
import org.hibernate.annotations.GenericGenerator;

import javax.persistence.*;

/**
 * <AUTHOR>
 * @Data 2019/04/01
 * Description 栏目之间的关联关系
 */
@Data
@EqualsAndHashCode(callSuper = true)
@NoArgsConstructor
@AllArgsConstructor
@Entity(name = "us_programa_cite")
@ApiModel(value = "栏目之间的关联关系")
public class ProgramaCite extends LogicModel {
    @Id
    @Column(name = "id", length = 40)
    @GeneratedValue(generator = "snowFlakeId")
    @GenericGenerator (name = "snowFlakeId", strategy = "com.simbest.boot.util.distribution.id.SnowflakeId")
    @EntityIdPrefix (prefix = "PC") //主键前缀，此为可选项注解
    private String id;

    @Setter
    @Getter
    @ApiModelProperty(value = "其中一个栏目编码", required = true)
    private String programaOneCode;

    @Transient
    private String programaOneName;//其中一个栏目名

    @Setter
    @Getter
    @ApiModelProperty(value = "另一个栏目编码", required = true)
    private String programaAnotherCode;

    @Transient
    private String programaAnotherName;//另一个栏目名

    @Setter
    @Getter
    @ApiModelProperty(value = "关联关系类型", required = true)
    private String programaCiteType;

    @Transient
    private String programaCiteName;//关联关系名

    @Setter
    @Getter
    @ApiModelProperty(value = "额外字段1", required = true)
    private String spare1;

    @Setter
    @Getter
    @ApiModelProperty(value = "额外字段2", required = true)
    private String spare2;

    @Setter
    @Getter
    @ApiModelProperty(value = "额外字段3", required = true)
    private String spare3;

}
