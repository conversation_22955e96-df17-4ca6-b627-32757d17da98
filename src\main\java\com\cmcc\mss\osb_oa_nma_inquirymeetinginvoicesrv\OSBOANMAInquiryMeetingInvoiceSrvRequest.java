
package com.cmcc.mss.osb_oa_nma_inquirymeetinginvoicesrv;

import com.cmcc.mss.msgheader.MsgHeader;
import com.simbest.boot.ws.adapter.RelaxedXMLGregorianCalendarAdapter;

import javax.xml.bind.annotation.*;
import javax.xml.bind.annotation.adapters.XmlJavaTypeAdapter;
import javax.xml.datatype.XMLGregorianCalendar;


/**
 * <p>OSB_OA_NMA_InquiryMeetingInvoiceSrvRequest complex type的 Java 类。
 * 
 * <p>以下模式片段指定包含在此类中的预期内容。
 * 
 * <pre>
 * &lt;complexType name="OSB_OA_NMA_InquiryMeetingInvoiceSrvRequest"&gt;
 *   &lt;complexContent&gt;
 *     &lt;restriction base="{http://www.w3.org/2001/XMLSchema}anyType"&gt;
 *       &lt;sequence&gt;
 *         &lt;element name="MsgHeader" type="{http://mss.cmcc.com/MsgHeader}MsgHeader"/&gt;
 *         &lt;element name="USERNAME" type="{http://www.w3.org/2001/XMLSchema}string"/&gt;
 *         &lt;element name="RECORD_NUM" type="{http://www.w3.org/2001/XMLSchema}string"/&gt;
 *         &lt;element name="LAST_UPDATE_START_DATE" type="{http://www.w3.org/2001/XMLSchema}dateTime"/&gt;
 *         &lt;element name="LAST_UPDATE_END_DATE" type="{http://www.w3.org/2001/XMLSchema}dateTime"/&gt;
 *       &lt;/sequence&gt;
 *     &lt;/restriction&gt;
 *   &lt;/complexContent&gt;
 * &lt;/complexType&gt;
 * </pre>
 * 
 * 
 */
@XmlAccessorType(XmlAccessType.FIELD)
@XmlType(name = "OSB_OA_NMA_InquiryMeetingInvoiceSrvRequest", propOrder = {
    "msgHeader",
    "username",
    "recordnum",
    "lastupdatestartdate",
    "lastupdateenddate"
})
public class OSBOANMAInquiryMeetingInvoiceSrvRequest {

    @XmlElement(name = "MsgHeader", required = true)
    protected MsgHeader msgHeader;

    @XmlElement(name = "USERNAME", required = true, nillable = true)
    protected String username;

    @XmlElement(name = "RECORD_NUM", required = true, nillable = true)
    protected String recordnum;

    @XmlElement(name = "LAST_UPDATE_START_DATE", required = true, nillable = true)
    @XmlSchemaType(name = "dateTime")
    @XmlJavaTypeAdapter (RelaxedXMLGregorianCalendarAdapter.class)
    protected XMLGregorianCalendar lastupdatestartdate;

    @XmlElement(name = "LAST_UPDATE_END_DATE", required = true, nillable = true)
    @XmlSchemaType(name = "dateTime")
    @XmlJavaTypeAdapter (RelaxedXMLGregorianCalendarAdapter.class)
    protected XMLGregorianCalendar lastupdateenddate;

    /**
     * 获取msgHeader属性的值。
     * 
     * @return
     *     possible object is
     *     {@link MsgHeader }
     *     
     */
    public MsgHeader getMsgHeader() {
        return msgHeader;
    }

    /**
     * 设置msgHeader属性的值。
     * 
     * @param value
     *     allowed object is
     *     {@link MsgHeader }
     *     
     */
    public void setMsgHeader(MsgHeader value) {
        this.msgHeader = value;
    }

    /**
     * 获取username属性的值。
     * 
     * @return
     *     possible object is
     *     {@link String }
     *     
     */
    public String getUSERNAME() {
        return username;
    }

    /**
     * 设置username属性的值。
     * 
     * @param value
     *     allowed object is
     *     {@link String }
     *     
     */
    public void setUSERNAME(String value) {
        this.username = value;
    }

    /**
     * 获取recordnum属性的值。
     * 
     * @return
     *     possible object is
     *     {@link String }
     *     
     */
    public String getRECORDNUM() {
        return recordnum;
    }

    /**
     * 设置recordnum属性的值。
     * 
     * @param value
     *     allowed object is
     *     {@link String }
     *     
     */
    public void setRECORDNUM(String value) {
        this.recordnum = value;
    }

    /**
     * 获取lastupdatestartdate属性的值。
     * 
     * @return
     *     possible object is
     *     {@link XMLGregorianCalendar }
     *     
     */
    public XMLGregorianCalendar getLASTUPDATESTARTDATE() {
        return lastupdatestartdate;
    }

    /**
     * 设置lastupdatestartdate属性的值。
     * 
     * @param value
     *     allowed object is
     *     {@link XMLGregorianCalendar }
     *     
     */
    public void setLASTUPDATESTARTDATE(XMLGregorianCalendar value) {
        this.lastupdatestartdate = value;
    }

    /**
     * 获取lastupdateenddate属性的值。
     * 
     * @return
     *     possible object is
     *     {@link XMLGregorianCalendar }
     *     
     */
    public XMLGregorianCalendar getLASTUPDATEENDDATE() {
        return lastupdateenddate;
    }

    /**
     * 设置lastupdateenddate属性的值。
     * 
     * @param value
     *     allowed object is
     *     {@link XMLGregorianCalendar }
     *     
     */
    public void setLASTUPDATEENDDATE(XMLGregorianCalendar value) {
        this.lastupdateenddate = value;
    }

}
