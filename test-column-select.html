<!DOCTYPE html>
<html>
<head>
    <title>栏目三级联动测试</title>
    <meta charset="utf-8">
    <script src="https://code.jquery.com/jquery-3.6.0.min.js"></script>
    <style>
        body {
            font-family: Arial, sans-serif;
            margin: 20px;
            background-color: #f5f5f5;
        }
        
        .container {
            max-width: 1200px;
            margin: 0 auto;
            background-color: white;
            padding: 20px;
            border-radius: 8px;
            box-shadow: 0 2px 10px rgba(0,0,0,0.1);
        }
        
        h1 {
            color: #333;
            text-align: center;
            margin-bottom: 30px;
        }
        
        /* 栏目选择样式 */
        .lan {
            display: flex;
            justify-content: space-between;
            align-items: center;
            margin: 20px 0;
            padding: 15px;
            background-color: #f8f9fa;
            border: 1px solid #e9ecef;
            border-radius: 4px;
        }

        .lan .left {
            display: flex;
            align-items: center;
            flex: 1;
        }

        .lan .label {
            font-weight: bold;
            margin-right: 15px;
            color: #333;
            min-width: 80px;
        }

        .lan-select {
            display: flex;
            gap: 10px;
            flex: 1;
        }

        .column-select {
            flex: 1;
            height: 32px;
            padding: 5px 10px;
            border: 1px solid #ddd;
            border-radius: 4px;
            background-color: #fff;
            font-size: 14px;
            color: #333;
            cursor: pointer;
        }

        .column-select:focus {
            border-color: #007bff;
            outline: none;
            box-shadow: 0 0 0 2px rgba(0, 123, 255, 0.25);
        }

        .column-select:disabled {
            background-color: #f5f5f5;
            color: #999;
            cursor: not-allowed;
        }

        .lan .right {
            margin-left: 15px;
        }
        
        .info-panel {
            margin-top: 20px;
            padding: 15px;
            background-color: #e7f3ff;
            border: 1px solid #b3d9ff;
            border-radius: 4px;
        }
        
        .info-panel h3 {
            margin-top: 0;
            color: #0066cc;
        }
        
        .info-item {
            margin: 5px 0;
            font-family: monospace;
        }
        
        .btn {
            background-color: #007bff;
            color: white;
            border: none;
            padding: 8px 16px;
            border-radius: 4px;
            cursor: pointer;
            font-size: 14px;
        }
        
        .btn:hover {
            background-color: #0056b3;
        }
    </style>
</head>
<body>
    <div class="container">
        <h1>栏目三级联动选择器测试</h1>
        
        <div class="lan">
            <div class="left">
                <div class="label">所属栏目：</div>
                <div class="lan-select">
                    <select id="firstLevelColumn" name="firstLevelColumn" class="column-select">
                        <option value="">--请选择一级栏目--</option>
                    </select>
                    <select id="secondLevelColumn" name="secondLevelColumn" class="column-select">
                        <option value="">--请选择二级栏目--</option>
                    </select>
                    <select id="thirdLevelColumn" name="thirdLevelColumn" class="column-select">
                        <option value="">--请选择三级栏目--</option>
                    </select>
                </div>
            </div>
            <div class="right">
                <button class="btn" onclick="showSelectedInfo()">显示选中信息</button>
            </div>
        </div>
        
        <div class="info-panel">
            <h3>选中的栏目信息：</h3>
            <div id="selectedInfo">请选择栏目...</div>
        </div>
    </div>

    <script>
        // 全局变量存储栏目数据
        var columnData = [
            {
                "id": "SNCM865307641443266560",
                "columnName": "警示曝光",
                "columnId": "030",
                "parentColumnId": "030",
                "rootColumnId": "030",
                "columnAllName": "警示曝光",
                "columnLevel": 1,
                "columnOrder": 2000,
                "isDisplay": true
            },
            {
                "id": "SNCM865307704039059456",
                "columnName": "警钟长鸣",
                "columnId": "025003001",
                "parentColumnId": "030",
                "rootColumnId": "030",
                "columnAllName": "警示曝光/警钟长鸣",
                "columnLevel": 2,
                "columnOrder": 100,
                "isDisplay": true
            },
            {
                "id": "SNCM865307709965611008",
                "columnName": "四风问题专区",
                "columnId": "010002",
                "parentColumnId": "025003001",
                "rootColumnId": "030",
                "columnAllName": "警示曝光/警钟长鸣/四风问题专区",
                "columnLevel": 3,
                "columnOrder": 200,
                "isDisplay": true
            }
        ];

        // 初始化栏目选择器
        function initColumnSelects() {
            console.log("初始化栏目选择器，数据:", columnData);
            
            // 清空所有下拉框
            $("#firstLevelColumn").empty().append('<option value="">--请选择一级栏目--</option>');
            $("#secondLevelColumn").empty().append('<option value="">--请选择二级栏目--</option>').prop('disabled', true);
            $("#thirdLevelColumn").empty().append('<option value="">--请选择三级栏目--</option>').prop('disabled', true);
            
            // 填充一级栏目
            var firstLevelColumns = columnData.filter(function(item) {
                return item.columnLevel === 1 && item.isDisplay;
            });
            
            firstLevelColumns.forEach(function(column) {
                $("#firstLevelColumn").append('<option value="' + column.columnId + '">' + column.columnName + '</option>');
            });
            
            // 绑定一级栏目变化事件
            $("#firstLevelColumn").off('change').on('change', function() {
                var selectedValue = $(this).val();
                loadSecondLevelColumns(selectedValue);
                updateSelectedInfo();
            });
            
            // 绑定二级栏目变化事件
            $("#secondLevelColumn").off('change').on('change', function() {
                var selectedValue = $(this).val();
                loadThirdLevelColumns(selectedValue);
                updateSelectedInfo();
            });
            
            // 绑定三级栏目变化事件
            $("#thirdLevelColumn").off('change').on('change', function() {
                updateSelectedInfo();
            });
        }

        // 加载二级栏目
        function loadSecondLevelColumns(parentColumnId) {
            $("#secondLevelColumn").empty().append('<option value="">--请选择二级栏目--</option>');
            $("#thirdLevelColumn").empty().append('<option value="">--请选择三级栏目--</option>').prop('disabled', true);
            
            if (!parentColumnId) {
                $("#secondLevelColumn").prop('disabled', true);
                return;
            }
            
            var secondLevelColumns = columnData.filter(function(item) {
                return item.columnLevel === 2 && item.parentColumnId === parentColumnId && item.isDisplay;
            });
            
            if (secondLevelColumns.length > 0) {
                $("#secondLevelColumn").prop('disabled', false);
                secondLevelColumns.forEach(function(column) {
                    $("#secondLevelColumn").append('<option value="' + column.columnId + '">' + column.columnName + '</option>');
                });
            } else {
                $("#secondLevelColumn").prop('disabled', true);
            }
        }

        // 加载三级栏目
        function loadThirdLevelColumns(parentColumnId) {
            $("#thirdLevelColumn").empty().append('<option value="">--请选择三级栏目--</option>');
            
            if (!parentColumnId) {
                $("#thirdLevelColumn").prop('disabled', true);
                return;
            }
            
            var thirdLevelColumns = columnData.filter(function(item) {
                return item.columnLevel === 3 && item.parentColumnId === parentColumnId && item.isDisplay;
            });
            
            if (thirdLevelColumns.length > 0) {
                $("#thirdLevelColumn").prop('disabled', false);
                thirdLevelColumns.forEach(function(column) {
                    $("#thirdLevelColumn").append('<option value="' + column.columnId + '">' + column.columnName + '</option>');
                });
            } else {
                $("#thirdLevelColumn").prop('disabled', true);
            }
        }

        // 获取选中的栏目信息
        function getSelectedColumnInfo() {
            var firstLevel = $("#firstLevelColumn").val();
            var secondLevel = $("#secondLevelColumn").val();
            var thirdLevel = $("#thirdLevelColumn").val();
            
            var selectedColumn = null;
            if (thirdLevel) {
                selectedColumn = columnData.find(function(item) {
                    return item.columnId === thirdLevel;
                });
            } else if (secondLevel) {
                selectedColumn = columnData.find(function(item) {
                    return item.columnId === secondLevel;
                });
            } else if (firstLevel) {
                selectedColumn = columnData.find(function(item) {
                    return item.columnId === firstLevel;
                });
            }
            
            return selectedColumn;
        }

        // 更新选中信息显示
        function updateSelectedInfo() {
            var selectedColumn = getSelectedColumnInfo();
            var infoDiv = $("#selectedInfo");
            
            if (selectedColumn) {
                var html = '<div class="info-item"><strong>栏目ID:</strong> ' + selectedColumn.columnId + '</div>' +
                          '<div class="info-item"><strong>栏目名称:</strong> ' + selectedColumn.columnName + '</div>' +
                          '<div class="info-item"><strong>栏目层级:</strong> ' + selectedColumn.columnLevel + '</div>' +
                          '<div class="info-item"><strong>完整路径:</strong> ' + selectedColumn.columnAllName + '</div>';
                infoDiv.html(html);
            } else {
                infoDiv.html('请选择栏目...');
            }
        }

        // 显示选中信息（按钮点击事件）
        function showSelectedInfo() {
            var selectedColumn = getSelectedColumnInfo();
            if (selectedColumn) {
                alert('选中栏目：\n' + 
                      '栏目ID: ' + selectedColumn.columnId + '\n' +
                      '栏目名称: ' + selectedColumn.columnName + '\n' +
                      '栏目层级: ' + selectedColumn.columnLevel + '\n' +
                      '完整路径: ' + selectedColumn.columnAllName);
            } else {
                alert('请先选择栏目！');
            }
        }

        // 页面加载完成后初始化
        $(document).ready(function() {
            initColumnSelects();
        });
    </script>
</body>
</html>
