package com.simbest.boot.hnjjwz.examOnline.service.impl;/**
 * Created by KZH on 2019/6/17 16:11.
 */

import com.simbest.boot.base.service.impl.LogicService;
import com.simbest.boot.hnjjwz.examOnline.model.ExamAttribute;
import com.simbest.boot.hnjjwz.examOnline.repository.ExamAttributeRepository;
import com.simbest.boot.hnjjwz.examOnline.service.IExamAttributeService;
import lombok.extern.slf4j.Slf4j;
import org.springframework.stereotype.Service;

import java.util.List;

/**
 * <AUTHOR>
 * @create 2019-06-17 16:11
 * @desc
 **/
@Slf4j
@Service
public class ExamAttributeServiceImpl extends LogicService<ExamAttribute,String> implements IExamAttributeService {

    private ExamAttributeRepository examAttributeRepository;

    public ExamAttributeServiceImpl(ExamAttributeRepository examAttributeRepository) {
        super(examAttributeRepository);
        this.examAttributeRepository=examAttributeRepository;
    }

    @Override
    public ExamAttribute findExamAttribute(String questionBankCode) {
        return examAttributeRepository.customfindOne(questionBankCode);
    }
}
