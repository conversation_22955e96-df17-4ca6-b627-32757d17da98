package com.simbest.boot.hnjjwz.backstage.programa.model;/**
 * Created by GZJ on 2019/6/26 14:58.
 */

import com.simbest.boot.base.annotations.EntityIdPrefix;
import com.simbest.boot.base.model.LogicModel;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.*;
import org.hibernate.annotations.GenericGenerator;
import org.hibernate.annotations.Table;

import javax.persistence.Column;
import javax.persistence.Entity;
import javax.persistence.GeneratedValue;
import javax.persistence.Id;

/**
 * <AUTHOR>
 * @create 2019-06-26 14:58
 * @desc 栏目权限控制
 **/
@Data
@EqualsAndHashCode(callSuper = true)
@NoArgsConstructor
@AllArgsConstructor
@Entity(name = "us_programa_power")
@Table( appliesTo="us_programa_power",comment="栏目权限控制表" )
@ApiModel(value = "栏目权限控制表")
public class ProgramaPower extends LogicModel {

    @Id
    @Column(name = "id", length = 40)
    @GeneratedValue(generator = "snowFlakeId")
    @GenericGenerator(name = "snowFlakeId", strategy = "com.simbest.boot.util.distribution.id.SnowflakeId")
    @EntityIdPrefix(prefix = "PP") //主键前缀，此为可选项注解
    private String id;

    @Setter
    @Getter
    @Column(length=250)
    @ApiModelProperty(value = "角色")
    private String userRole;

    @Setter
    @Getter
    @Column(length=250)
    @ApiModelProperty(value = "拥有栏目权限")
    private String possessPower;

    @Setter
    @Getter
    @Column(length=250)
    @ApiModelProperty(value = "拥有栏目编码")
    private String possessCode;

    @Setter
    @Getter
    @Column(length=250)
    @ApiModelProperty(value = "独自拥有的栏目编码")
    private String aloneCode;
}
