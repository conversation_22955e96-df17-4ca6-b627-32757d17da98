package com.simbest.boot.hnjjwz.newcolumn.service.impl;

import cn.hutool.core.collection.CollectionUtil;
import cn.hutool.core.util.ObjectUtil;
import com.simbest.boot.base.service.impl.LogicService;
import com.simbest.boot.base.web.response.JsonResponse;
import com.simbest.boot.constants.ApplicationConstants;
import com.simbest.boot.hnjjwz.backstage.announcement.model.Announcement;
import com.simbest.boot.hnjjwz.backstage.announcement.service.IAnnouncementService;
import com.simbest.boot.hnjjwz.backstage.slideshow.model.SlideShow;
import com.simbest.boot.hnjjwz.backstage.slideshow.service.ISlideShowService;
import com.simbest.boot.hnjjwz.constants.Constants;
import com.simbest.boot.hnjjwz.newcolumn.model.SysNewColumnModel;
import com.simbest.boot.hnjjwz.newcolumn.repository.SysNewColumnRepository;
import com.simbest.boot.hnjjwz.newcolumn.service.ISysNewColumnService;
import com.simbest.boot.hnjjwz.process.wf.model.ProgramaDataForm;
import com.simbest.boot.hnjjwz.process.wf.service.IProgramaDataFormService;
import com.simbest.boot.security.IUser;
import com.simbest.boot.util.MapUtil;
import com.simbest.boot.util.security.SecurityUtils;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.data.domain.Page;
import org.springframework.stereotype.Service;

import java.util.ArrayList;
import java.util.HashMap;
import java.util.List;
import java.util.Map;
import java.util.logging.Handler;

@Service
@Slf4j
public class SysNewColumnServiceImpl extends LogicService<SysNewColumnModel, String> implements ISysNewColumnService {
    private SysNewColumnRepository repository;

    @Autowired
    public SysNewColumnServiceImpl(SysNewColumnRepository repository) {
        super(repository);
        this.repository = repository;
    }


    @Autowired
    private IProgramaDataFormService programaDataFormService;

    @Autowired
    private IAnnouncementService announcementService;

    @Autowired
    private ISlideShowService slideShowService;

    @Override
    public JsonResponse createnewcolumn(SysNewColumnModel model) {
        model.setCreator(ApplicationConstants.ADMINISTRATOR);
        model.setModifier(ApplicationConstants.ADMINISTRATOR);
        model.setEnabled(Boolean.TRUE);
        SysNewColumnModel insert = this.insert(model);
        return JsonResponse.success(insert);
    }

    @Override
    public JsonResponse findAllByUser() {
        List<SysNewColumnModel> allNoPage = new ArrayList<>();
        IUser iUser = SecurityUtils.getCurrentUser();
        //如果是纪委办的人则查询所有的栏目
        String belongDepartmentCode = iUser.getBelongDepartmentCode();
        if ("4772340095222653632".equals(belongDepartmentCode)) {
            allNoPage = repository.findAllColumn();

        } else {
            List<String> values = new ArrayList<>();
            values.add(iUser.getBelongOrgCode());
            values.add(iUser.getUsername());
            allNoPage = repository.findColumnByValues(values);
        }
        return JsonResponse.success(allNoPage);
    }

    @Override
    public JsonResponse findAllArticleByColumnId(String columnId) {
        Map<String, Object> resultMap = new HashMap<>();
        String articleType = "programaData";//普通栏目的默认类型
        Object articleList = new Object();
        if ("099".equals(columnId)) {
            articleType = "announcement";//公告
            articleList = this.getAnnouncementArticleList(columnId);
        } else if ("098".equals(columnId)) {
            articleType = "slideShow";//轮播图
            articleList = this.getSlideShowArticleList(columnId);
        } else {
            articleList = this.getProgramaDataArticleList(columnId);
        }
        resultMap.put("articleType", articleType);
        resultMap.put("articleList", articleList);
        return JsonResponse.success(resultMap, "");
    }

    @Override
    public JsonResponse findColumnByArticleID(String columnId) {
        List<SysNewColumnModel> allColumnByColumnIds = new ArrayList<>();
        try {
            //获取栏目的根栏目ID
            SysNewColumnModel oneByColumnId = repository.findOneByColumnId(columnId);
            if ("099".equals(columnId) || "098".equals(columnId)) {
                allColumnByColumnIds.add(oneByColumnId);
                return JsonResponse.success(allColumnByColumnIds);
            } else {
                String rootColumnId = oneByColumnId.getRootColumnId();
                //获取栏目下的所有栏目ID信息
                List<String> columnIdsByRootId = repository.findColumnIdsByRootId(rootColumnId);
                allColumnByColumnIds = repository.findAllColumnByColumnIds(columnIdsByRootId);
                return JsonResponse.success(allColumnByColumnIds);
            }
        } catch (Exception e) {
            e.printStackTrace();
        }
        return JsonResponse.defaultErrorResponse();
    }


    /**
     * @description 查询首页栏目下新闻
     * <AUTHOR> enlai
     * @time 25-05-23
     */
    private List<ProgramaDataForm> getProgramaDataArticleList(String columnId) {
        //获取栏目下的所有栏目ID信息
        List<String> columnIds = repository.findColumnIdsByRootId(columnId);
        //取栏目下的所有新闻动态
        List<ProgramaDataForm> programaDataList = programaDataFormService.getProgramaDataList(columnIds);
        return programaDataList;
    }

    /**
     * @description 查询首页轮播图
     * <AUTHOR> enlai
     * @time 25-05-23
     */
    private List<SlideShow> getSlideShowArticleList(String columnId) {
        List<SlideShow> mapList = slideShowService.getSlideShowArticleList();
        return mapList;
    }

    /**
     * @description 查询首页公告
     * <AUTHOR> enlai
     * @time 25-05-23
     */
    private List<Announcement> getAnnouncementArticleList(String columnId) {
        List<Announcement> mapList = announcementService.getAnnouncementArticleList();
        return mapList;
    }


    @Override
    public JsonResponse findArticlePageByColumnId(String columnId, int page, int size) {
        List<String> columnIds = new ArrayList<>();
        SysNewColumnModel oneByColumnId = repository.findOneByColumnId(columnId);
        Integer columnLevel = oneByColumnId.getColumnLevel();
        if (3 == columnLevel) {
            columnIds.add(columnId);
        } else if (2 == columnLevel) {
            List<String> columnIdsByParentId = repository.findColumnIdsByParentId(columnId);
            columnIds.add(columnId);
            columnIds.addAll(columnIdsByParentId);
        } else if (1 == columnLevel) {
            columnIds = repository.findColumnIdsByRootId(columnId);
        }
        Page<ProgramaDataForm> pageMap = programaDataFormService.findArticlePageByColumnId(columnIds, page, size);
        return JsonResponse.success(pageMap);
    }

    @Override
    public JsonResponse findAnnouncementPageByColumnId(String columnId, int page, int size) {
        Page<Announcement> pageMap = announcementService.findAnnouncementPageByColumnId(columnId, page, size);
        return JsonResponse.success(pageMap);
    }

    @Override
    public JsonResponse getDataArticleById(String columnId, String articleId) {
        JsonResponse jsonResponse = new JsonResponse();
        if (columnId.equals("099")) {
            Announcement announcement = announcementService.getDataArticleById(articleId);
            ProgramaDataForm programaDataForm = programaDataFormService.saveAnnouncement(announcement);
            return JsonResponse.success(programaDataForm);
        } else if (columnId.equals("098")) {
            SlideShow slideShow = slideShowService.getDataArticleById(articleId);
            ProgramaDataForm programaDataForm = programaDataFormService.saveSlideShow(slideShow);
            return JsonResponse.success(programaDataForm);
        } else {
            jsonResponse = programaDataFormService.getDataArticleById(articleId);
        }
        return jsonResponse;
    }

    @Override
    public SysNewColumnModel findOneByAllColumnName(String programaDisplayName) {
        return repository.findOneByAllColumnName(programaDisplayName);
    }

    @Override
    public JsonResponse recommendedArticle(String pmInsId) {
        if (pmInsId.startsWith("C")){
            return JsonResponse.defaultSuccessResponse();
        }else if(pmInsId.startsWith("E")){
            return JsonResponse.defaultSuccessResponse();
        }
        return programaDataFormService.recommendedArticle(pmInsId);
    }
}
