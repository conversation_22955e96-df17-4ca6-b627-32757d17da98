
package com.cmcc.mss.osb_oa_nma_inquirymeetinginvoicesrv;

import java.math.BigDecimal;
import javax.xml.bind.annotation.XmlAccessType;
import javax.xml.bind.annotation.XmlAccessorType;
import javax.xml.bind.annotation.XmlElement;
import javax.xml.bind.annotation.XmlSchemaType;
import javax.xml.bind.annotation.XmlType;
import javax.xml.datatype.XMLGregorianCalendar;


/**
 * <p>DATAS_Item complex type的 Java 类。
 * 
 * <p>以下模式片段指定包含在此类中的预期内容。
 * 
 * <pre>
 * &lt;complexType name="DATAS_Item"&gt;
 *   &lt;complexContent&gt;
 *     &lt;restriction base="{http://www.w3.org/2001/XMLSchema}anyType"&gt;
 *       &lt;sequence&gt;
 *         &lt;element name="RECORD_NUM" type="{http://www.w3.org/2001/XMLSchema}string"/&gt;
 *         &lt;element name="COMPANY" type="{http://www.w3.org/2001/XMLSchema}string"/&gt;
 *         &lt;element name="COMPANY_CODE" type="{http://www.w3.org/2001/XMLSchema}string"/&gt;
 *         &lt;element name="DEPARTMENT" type="{http://www.w3.org/2001/XMLSchema}string"/&gt;
 *         &lt;element name="DEPARTMENT_CODE" type="{http://www.w3.org/2001/XMLSchema}string"/&gt;
 *         &lt;element name="USERNAME" type="{http://www.w3.org/2001/XMLSchema}string"/&gt;
 *         &lt;element name="TRUENAME" type="{http://www.w3.org/2001/XMLSchema}string"/&gt;
 *         &lt;element name="TITLE" type="{http://www.w3.org/2001/XMLSchema}string"/&gt;
 *         &lt;element name="CONTENT" type="{http://www.w3.org/2001/XMLSchema}string"/&gt;
 *         &lt;element name="PERSONS" type="{http://www.w3.org/2001/XMLSchema}string"/&gt;
 *         &lt;element name="PERSONS_NUMBER" type="{http://www.w3.org/2001/XMLSchema}decimal"/&gt;
 *         &lt;element name="ASSIST_CONTENT" type="{http://www.w3.org/2001/XMLSchema}string"/&gt;
 *         &lt;element name="BEGIN_TIME" type="{http://www.w3.org/2001/XMLSchema}dateTime"/&gt;
 *         &lt;element name="END_TIME" type="{http://www.w3.org/2001/XMLSchema}dateTime"/&gt;
 *         &lt;element name="ADDRESS" type="{http://www.w3.org/2001/XMLSchema}string"/&gt;
 *         &lt;element name="APPLY_TYPE" type="{http://www.w3.org/2001/XMLSchema}string"/&gt;
 *         &lt;element name="APPLY_STANDARD" type="{http://www.w3.org/2001/XMLSchema}string"/&gt;
 *         &lt;element name="APPLY_BUDGET" type="{http://www.w3.org/2001/XMLSchema}decimal"/&gt;
 *         &lt;element name="APPLY_BUDGET_MAX" type="{http://www.w3.org/2001/XMLSchema}decimal"/&gt;
 *         &lt;element name="IRESERVED_1" type="{http://www.w3.org/2001/XMLSchema}string"/&gt;
 *         &lt;element name="IRESERVED_2" type="{http://www.w3.org/2001/XMLSchema}string"/&gt;
 *         &lt;element name="IRESERVED_3" type="{http://www.w3.org/2001/XMLSchema}string"/&gt;
 *         &lt;element name="IRESERVED_4" type="{http://www.w3.org/2001/XMLSchema}string"/&gt;
 *         &lt;element name="IRESERVED_5" type="{http://www.w3.org/2001/XMLSchema}string"/&gt;
 *         &lt;element name="LAST_UPDATE_DATE" type="{http://www.w3.org/2001/XMLSchema}dateTime"/&gt;
 *       &lt;/sequence&gt;
 *     &lt;/restriction&gt;
 *   &lt;/complexContent&gt;
 * &lt;/complexType&gt;
 * </pre>
 * 
 * 
 */
@XmlAccessorType(XmlAccessType.FIELD)
@XmlType(name = "DATAS_Item", propOrder = {
    "recordnum",
    "company",
    "companycode",
    "department",
    "departmentcode",
    "username",
    "truename",
    "title",
    "content",
    "persons",
    "personsnumber",
    "assistcontent",
    "begintime",
    "endtime",
    "address",
    "applytype",
    "applystandard",
    "applybudget",
    "applybudgetmax",
    "ireserved1",
    "ireserved2",
    "ireserved3",
    "ireserved4",
    "ireserved5",
    "lastupdatedate"
})
public class DATASItem {

    @XmlElement(name = "RECORD_NUM", required = true, nillable = true)
    protected String recordnum;
    @XmlElement(name = "COMPANY", required = true, nillable = true)
    protected String company;
    @XmlElement(name = "COMPANY_CODE", required = true, nillable = true)
    protected String companycode;
    @XmlElement(name = "DEPARTMENT", required = true, nillable = true)
    protected String department;
    @XmlElement(name = "DEPARTMENT_CODE", required = true, nillable = true)
    protected String departmentcode;
    @XmlElement(name = "USERNAME", required = true, nillable = true)
    protected String username;
    @XmlElement(name = "TRUENAME", required = true, nillable = true)
    protected String truename;
    @XmlElement(name = "TITLE", required = true, nillable = true)
    protected String title;
    @XmlElement(name = "CONTENT", required = true, nillable = true)
    protected String content;
    @XmlElement(name = "PERSONS", required = true, nillable = true)
    protected String persons;
    @XmlElement(name = "PERSONS_NUMBER", required = true, nillable = true)
    protected BigDecimal personsnumber;
    @XmlElement(name = "ASSIST_CONTENT", required = true, nillable = true)
    protected String assistcontent;
    @XmlElement(name = "BEGIN_TIME", required = true, nillable = true)
    @XmlSchemaType(name = "dateTime")
    protected XMLGregorianCalendar begintime;
    @XmlElement(name = "END_TIME", required = true, nillable = true)
    @XmlSchemaType(name = "dateTime")
    protected XMLGregorianCalendar endtime;
    @XmlElement(name = "ADDRESS", required = true, nillable = true)
    protected String address;
    @XmlElement(name = "APPLY_TYPE", required = true, nillable = true)
    protected String applytype;
    @XmlElement(name = "APPLY_STANDARD", required = true, nillable = true)
    protected String applystandard;
    @XmlElement(name = "APPLY_BUDGET", required = true, nillable = true)
    protected BigDecimal applybudget;
    @XmlElement(name = "APPLY_BUDGET_MAX", required = true, nillable = true)
    protected BigDecimal applybudgetmax;
    @XmlElement(name = "IRESERVED_1", required = true, nillable = true)
    protected String ireserved1;
    @XmlElement(name = "IRESERVED_2", required = true, nillable = true)
    protected String ireserved2;
    @XmlElement(name = "IRESERVED_3", required = true, nillable = true)
    protected String ireserved3;
    @XmlElement(name = "IRESERVED_4", required = true, nillable = true)
    protected String ireserved4;
    @XmlElement(name = "IRESERVED_5", required = true, nillable = true)
    protected String ireserved5;
    @XmlElement(name = "LAST_UPDATE_DATE", required = true, nillable = true)
    @XmlSchemaType(name = "dateTime")
    protected XMLGregorianCalendar lastupdatedate;

    /**
     * 获取recordnum属性的值。
     * 
     * @return
     *     possible object is
     *     {@link String }
     *     
     */
    public String getRECORDNUM() {
        return recordnum;
    }

    /**
     * 设置recordnum属性的值。
     * 
     * @param value
     *     allowed object is
     *     {@link String }
     *     
     */
    public void setRECORDNUM(String value) {
        this.recordnum = value;
    }

    /**
     * 获取company属性的值。
     * 
     * @return
     *     possible object is
     *     {@link String }
     *     
     */
    public String getCOMPANY() {
        return company;
    }

    /**
     * 设置company属性的值。
     * 
     * @param value
     *     allowed object is
     *     {@link String }
     *     
     */
    public void setCOMPANY(String value) {
        this.company = value;
    }

    /**
     * 获取companycode属性的值。
     * 
     * @return
     *     possible object is
     *     {@link String }
     *     
     */
    public String getCOMPANYCODE() {
        return companycode;
    }

    /**
     * 设置companycode属性的值。
     * 
     * @param value
     *     allowed object is
     *     {@link String }
     *     
     */
    public void setCOMPANYCODE(String value) {
        this.companycode = value;
    }

    /**
     * 获取department属性的值。
     * 
     * @return
     *     possible object is
     *     {@link String }
     *     
     */
    public String getDEPARTMENT() {
        return department;
    }

    /**
     * 设置department属性的值。
     * 
     * @param value
     *     allowed object is
     *     {@link String }
     *     
     */
    public void setDEPARTMENT(String value) {
        this.department = value;
    }

    /**
     * 获取departmentcode属性的值。
     * 
     * @return
     *     possible object is
     *     {@link String }
     *     
     */
    public String getDEPARTMENTCODE() {
        return departmentcode;
    }

    /**
     * 设置departmentcode属性的值。
     * 
     * @param value
     *     allowed object is
     *     {@link String }
     *     
     */
    public void setDEPARTMENTCODE(String value) {
        this.departmentcode = value;
    }

    /**
     * 获取username属性的值。
     * 
     * @return
     *     possible object is
     *     {@link String }
     *     
     */
    public String getUSERNAME() {
        return username;
    }

    /**
     * 设置username属性的值。
     * 
     * @param value
     *     allowed object is
     *     {@link String }
     *     
     */
    public void setUSERNAME(String value) {
        this.username = value;
    }

    /**
     * 获取truename属性的值。
     * 
     * @return
     *     possible object is
     *     {@link String }
     *     
     */
    public String getTRUENAME() {
        return truename;
    }

    /**
     * 设置truename属性的值。
     * 
     * @param value
     *     allowed object is
     *     {@link String }
     *     
     */
    public void setTRUENAME(String value) {
        this.truename = value;
    }

    /**
     * 获取title属性的值。
     * 
     * @return
     *     possible object is
     *     {@link String }
     *     
     */
    public String getTITLE() {
        return title;
    }

    /**
     * 设置title属性的值。
     * 
     * @param value
     *     allowed object is
     *     {@link String }
     *     
     */
    public void setTITLE(String value) {
        this.title = value;
    }

    /**
     * 获取content属性的值。
     * 
     * @return
     *     possible object is
     *     {@link String }
     *     
     */
    public String getCONTENT() {
        return content;
    }

    /**
     * 设置content属性的值。
     * 
     * @param value
     *     allowed object is
     *     {@link String }
     *     
     */
    public void setCONTENT(String value) {
        this.content = value;
    }

    /**
     * 获取persons属性的值。
     * 
     * @return
     *     possible object is
     *     {@link String }
     *     
     */
    public String getPERSONS() {
        return persons;
    }

    /**
     * 设置persons属性的值。
     * 
     * @param value
     *     allowed object is
     *     {@link String }
     *     
     */
    public void setPERSONS(String value) {
        this.persons = value;
    }

    /**
     * 获取personsnumber属性的值。
     * 
     * @return
     *     possible object is
     *     {@link BigDecimal }
     *     
     */
    public BigDecimal getPERSONSNUMBER() {
        return personsnumber;
    }

    /**
     * 设置personsnumber属性的值。
     * 
     * @param value
     *     allowed object is
     *     {@link BigDecimal }
     *     
     */
    public void setPERSONSNUMBER(BigDecimal value) {
        this.personsnumber = value;
    }

    /**
     * 获取assistcontent属性的值。
     * 
     * @return
     *     possible object is
     *     {@link String }
     *     
     */
    public String getASSISTCONTENT() {
        return assistcontent;
    }

    /**
     * 设置assistcontent属性的值。
     * 
     * @param value
     *     allowed object is
     *     {@link String }
     *     
     */
    public void setASSISTCONTENT(String value) {
        this.assistcontent = value;
    }

    /**
     * 获取begintime属性的值。
     * 
     * @return
     *     possible object is
     *     {@link XMLGregorianCalendar }
     *     
     */
    public XMLGregorianCalendar getBEGINTIME() {
        return begintime;
    }

    /**
     * 设置begintime属性的值。
     * 
     * @param value
     *     allowed object is
     *     {@link XMLGregorianCalendar }
     *     
     */
    public void setBEGINTIME(XMLGregorianCalendar value) {
        this.begintime = value;
    }

    /**
     * 获取endtime属性的值。
     * 
     * @return
     *     possible object is
     *     {@link XMLGregorianCalendar }
     *     
     */
    public XMLGregorianCalendar getENDTIME() {
        return endtime;
    }

    /**
     * 设置endtime属性的值。
     * 
     * @param value
     *     allowed object is
     *     {@link XMLGregorianCalendar }
     *     
     */
    public void setENDTIME(XMLGregorianCalendar value) {
        this.endtime = value;
    }

    /**
     * 获取address属性的值。
     * 
     * @return
     *     possible object is
     *     {@link String }
     *     
     */
    public String getADDRESS() {
        return address;
    }

    /**
     * 设置address属性的值。
     * 
     * @param value
     *     allowed object is
     *     {@link String }
     *     
     */
    public void setADDRESS(String value) {
        this.address = value;
    }

    /**
     * 获取applytype属性的值。
     * 
     * @return
     *     possible object is
     *     {@link String }
     *     
     */
    public String getAPPLYTYPE() {
        return applytype;
    }

    /**
     * 设置applytype属性的值。
     * 
     * @param value
     *     allowed object is
     *     {@link String }
     *     
     */
    public void setAPPLYTYPE(String value) {
        this.applytype = value;
    }

    /**
     * 获取applystandard属性的值。
     * 
     * @return
     *     possible object is
     *     {@link String }
     *     
     */
    public String getAPPLYSTANDARD() {
        return applystandard;
    }

    /**
     * 设置applystandard属性的值。
     * 
     * @param value
     *     allowed object is
     *     {@link String }
     *     
     */
    public void setAPPLYSTANDARD(String value) {
        this.applystandard = value;
    }

    /**
     * 获取applybudget属性的值。
     * 
     * @return
     *     possible object is
     *     {@link BigDecimal }
     *     
     */
    public BigDecimal getAPPLYBUDGET() {
        return applybudget;
    }

    /**
     * 设置applybudget属性的值。
     * 
     * @param value
     *     allowed object is
     *     {@link BigDecimal }
     *     
     */
    public void setAPPLYBUDGET(BigDecimal value) {
        this.applybudget = value;
    }

    /**
     * 获取applybudgetmax属性的值。
     * 
     * @return
     *     possible object is
     *     {@link BigDecimal }
     *     
     */
    public BigDecimal getAPPLYBUDGETMAX() {
        return applybudgetmax;
    }

    /**
     * 设置applybudgetmax属性的值。
     * 
     * @param value
     *     allowed object is
     *     {@link BigDecimal }
     *     
     */
    public void setAPPLYBUDGETMAX(BigDecimal value) {
        this.applybudgetmax = value;
    }

    /**
     * 获取ireserved1属性的值。
     * 
     * @return
     *     possible object is
     *     {@link String }
     *     
     */
    public String getIRESERVED1() {
        return ireserved1;
    }

    /**
     * 设置ireserved1属性的值。
     * 
     * @param value
     *     allowed object is
     *     {@link String }
     *     
     */
    public void setIRESERVED1(String value) {
        this.ireserved1 = value;
    }

    /**
     * 获取ireserved2属性的值。
     * 
     * @return
     *     possible object is
     *     {@link String }
     *     
     */
    public String getIRESERVED2() {
        return ireserved2;
    }

    /**
     * 设置ireserved2属性的值。
     * 
     * @param value
     *     allowed object is
     *     {@link String }
     *     
     */
    public void setIRESERVED2(String value) {
        this.ireserved2 = value;
    }

    /**
     * 获取ireserved3属性的值。
     * 
     * @return
     *     possible object is
     *     {@link String }
     *     
     */
    public String getIRESERVED3() {
        return ireserved3;
    }

    /**
     * 设置ireserved3属性的值。
     * 
     * @param value
     *     allowed object is
     *     {@link String }
     *     
     */
    public void setIRESERVED3(String value) {
        this.ireserved3 = value;
    }

    /**
     * 获取ireserved4属性的值。
     * 
     * @return
     *     possible object is
     *     {@link String }
     *     
     */
    public String getIRESERVED4() {
        return ireserved4;
    }

    /**
     * 设置ireserved4属性的值。
     * 
     * @param value
     *     allowed object is
     *     {@link String }
     *     
     */
    public void setIRESERVED4(String value) {
        this.ireserved4 = value;
    }

    /**
     * 获取ireserved5属性的值。
     * 
     * @return
     *     possible object is
     *     {@link String }
     *     
     */
    public String getIRESERVED5() {
        return ireserved5;
    }

    /**
     * 设置ireserved5属性的值。
     * 
     * @param value
     *     allowed object is
     *     {@link String }
     *     
     */
    public void setIRESERVED5(String value) {
        this.ireserved5 = value;
    }

    /**
     * 获取lastupdatedate属性的值。
     * 
     * @return
     *     possible object is
     *     {@link XMLGregorianCalendar }
     *     
     */
    public XMLGregorianCalendar getLASTUPDATEDATE() {
        return lastupdatedate;
    }

    /**
     * 设置lastupdatedate属性的值。
     * 
     * @param value
     *     allowed object is
     *     {@link XMLGregorianCalendar }
     *     
     */
    public void setLASTUPDATEDATE(XMLGregorianCalendar value) {
        this.lastupdatedate = value;
    }

}
