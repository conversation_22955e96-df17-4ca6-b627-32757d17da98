<!DOCTYPE html>
<html xmlns="http://www.w3.org/1999/xhtml"
      xmlns:th="http://www.thymeleaf.org">
<head>
    <title>栏目关系管理</title>
    <meta http-equiv="Content-Type" content="text/html; charset=utf-8"/>
    <meta http-equiv="X-UA-Compatible" content="IE=edge" />
    <meta name="viewport" content="width=device-width, initial-scale=1, shrink-to-fit=no"/>
    <!--
    <link href="http://************:8088/simbestui/fonts/iconfont/iconfont.css?v=svn.revision" th:href="@{http://************:8088/simbestui/fonts/iconfont/iconfont.css?v=svn.revision}" rel="stylesheet"/>
    -->
    <link href="../../fonts/iconfont/iconfont.css?v=svn.revision" th:href="@{/fonts/iconfont/iconfont.css?v=svn.revision}" rel="stylesheet"/>
    <link href="http://************:8088/simbestui/js/themes/default/easyui.css?v=svn.revision" th:href="@{http://************:8088/simbestui/js/themes/default/easyui.css?v=svn.revision}" rel="stylesheet"/>
    <link href="http://************:8088/simbestui/js/themes/icon.css?v=svn.revision" th:href="@{http://************:8088/simbestui/js/themes/icon.css?v=svn.revision}" rel="stylesheet"/>
    <link href="http://************:8088/simbestui/css/public.css?v=svn.revision" th:href="@{http://************:8088/simbestui/css/public.css?v=svn.revision}" rel="stylesheet"/>
    <link href="http://************:8088/simbestui/css/pages.css?v=svn.revision" th:href="@{http://************:8088/simbestui/css/pages.css?v=svn.revision}" rel="stylesheet"/>
    <script src="http://************:8088/simbestui/js/jquery.min.js?v=svn.revision" th:src="@{http://************:8088/simbestui/js/jquery.min.js?v=svn.revision}" type="text/javascript"></script>
    <script src="http://************:8088/simbestui/js/jquery.easyui.min.js?v=svn.revision" th:src="@{http://************:8088/simbestui/js/jquery.easyui.min.js?v=svn.revision}" type="text/javascript"></script>
    <script src="http://************:8088/simbestui/js/easyui-lang-zh_CN.js?v=svn.revision" th:src="@{http://************:8088/simbestui/js/easyui-lang-zh_CN.js?v=svn.revision}" type="text/javascript"></script>
    <script src="../../js/jquery.config.js?v=svn.revision" th:src="@{/js/jquery.config.js?v=svn.revision}" type="text/javascript"></script>
    <script src="http://************:8088/simbestui/js/jquery.zjsfile.js?v=svn.revision" th:src="@{http://************:8088/simbestui/js/jquery.zjsfile.js?v=svn.revision}" type="text/javascript"></script>
    <script src="http://************:8088/simbestui/js/jquery.aduq.js?v=svn.revision" th:src="@{http://************:8088/simbestui/js/jquery.aduq.js?v=svn.revision}" type="text/javascript"></script>
    <script src="http://************:8088/simbestui/js/jquery.process.js?v=svn.revision" th:src="@{http://************:8088/simbestui/js/jquery.process.js?v=svn.revision}" type="text/javascript"></script>
    <script type="text/javascript">
        $(function(){
            //showDialog做为打开对话框的公共class名称   showDialogindex做为打开修改对话框传行的索引   delete做为删除按钮配置命令   deleteid做为删除传值  searchtable做为条件查询的按钮公共class名称
            var pageparam={
                "listtable":{
                    "listname":"#programaCiteTable",//table列表的id名称，需加#
                    "querycmd":"action/programaCite/findDimProgramaCite",//table列表的查询命令
                    "checkboxall":true,
                    "contentType":"application/json; charset=utf-8",//table列表的传参方式，如果是json就写，不写默认kv
                    "nowrap": true,//把数据显示在一行里,默认true
                    "styleClass": "noScroll",
                    "frozenColumns":[[
                        { field: "ck",checkbox:true}
                    ]],//固定在左侧的列
                    "columns":[[//列
                        { title: "栏目编码", field: "programaOneCode", width: 120},
                        { title: "栏目名", field: "programaOneName", width: 120},
                        { title: "关联栏目编码", field: "programaAnotherCode", width: 120},
                        { title: "关联栏目名", field: "programaAnotherName", width: 120},
                        { title: "关联关系类型", field: "programaCiteName", width: 120},
                        {
                            field: "opt", title: "操作", width: 110, rowspan: 1,//align：对齐此列的数据，可以用left、right、center
                            formatter: function (value, row,index) {//单元格的格式化函数，需要三个参数：value：字段的值；rowData：行的记录数据；rowIndex：行的索引
                                var g = "<a href='#' class='showDialog'  showDialogindex='" + index + "'>【修改】</a>"
                                    +"<a href='#' delete='action/programaCite/deleteById' deleteid='"+row.id+"'>【删除】</a>";
                                return g;
                            }
                        }
                    ] ],
                    "pagerbar": [{
                        id:"deleteall",
                        iconCls: 'icon-remove',
                        text:"批量删除&nbsp;"
                    }],
                    "deleteall":{//批量删除deleteall.id要与pagerbar.id相同
                        "id":"deleteall",
                        "url":"action/programaCite/deleteAllByIds",
                        "contentType":"application/json; charset=utf-8"
                    }
                },
                "dialogform":{
                    "dialogid":"#buttons",//对话框的id
                    "formname":"#programaCiteTableAddForm",//新增或修改对话框的formid需加#
                    "insertcmd":"action/programaCite/create",//新增命令
                    "updatacmd":"action/programaCite/update",//修改命令
                    "onSubmit":function(data){
                        if($("#id").val()=="") data.displayOrder="1";
                        if($("#roleCode").attr("codeError")){
                            top.mesAlert("提示信息","角色编码已存在,请重新输入！", 'error');
                            return false;
                        }
                        return true;
                    }
                }
            };
            loadGrid(pageparam);
            //点击打开栏目树
            $(".chooseProgramaOne").on("click",function(){
                var gps=getQueryString();
                //第一个参数是页面的url(multi为0表示单选，为1表示多选),第二个参数是当前对话框对应iframe的name（若是有好多次iframe请按从内到外的顺序以|分隔）,第三个参数选择人界面的标题,
                //第四个参数是选择人完成之后的回调函数名称,第五个参数对话框是否有底部按钮来设置表示默认有(若需要设置宽高,若需要就写false 不需要写true) 第6是宽默认1000，第7是高默认550
                //第二个参数必须是唯一的
                var href={"multi":"0","name":"chooseProgramaCiteOneVal"};
                var chooseRow=top.chooseWeb.chooseProgramaCiteOneVal?top.chooseWeb.chooseProgramaCiteOneVal.data:[];
                if($("#programaOneCode").val()!=""){
                    var datas=[];
                    var names=$("#programaOneName").val().split(",");
                    var codes=$("#programaOneCode").val().split(",");
                    for(var i in codes){
                        var datai={};
                        datai.id=codes[i];
                        datai.name=names[i];
                        datas.push(datai);
                    }
                    top.chooseWeb.chooseProgramaCiteOneVal={"data":datas};
                }else{//表示新增
                    top.chooseWeb.chooseProgramaCiteOneVal={"data":[]};
                }
                var url=tourl((gps.form?"hnjjwz/":"")+'html/programaClassifyInfo/programaInfoTree.html?',href);
                top.dialogP(url,gps.form?"programaCiteList":'programaCiteList','选择栏目','chooseProgramaCiteOne',false,'800');
                //记住decisionOptF这个参数是打开的页面里的名字
            });
            //点击打开关联栏目树
            $(".chooseProgramaAnother").on("click",function(){
                var gps=getQueryString();
                //第一个参数是页面的url(multi为0表示单选，为1表示多选),第二个参数是当前对话框对应iframe的name（若是有好多次iframe请按从内到外的顺序以|分隔）,第三个参数选择人界面的标题,
                //第四个参数是选择人完成之后的回调函数名称,第五个参数对话框是否有底部按钮来设置表示默认有(若需要设置宽高,若需要就写false 不需要写true) 第6是宽默认1000，第7是高默认550
                //第二个参数必须是唯一的
                var href={"multi":"0","name":"chooseProgramaCiteVal"};
                var chooseRow=top.chooseWeb.chooseProgramaCiteVal?top.chooseWeb.chooseProgramaCiteVal.data:[];
                if($("#programaAnotherCode").val()!=""){
                    var datas=[];
                    var names=$("#programaAnotherName").val().split(",");
                    var codes=$("#programaAnotherCode").val().split(",");
                    for(var i in codes){
                        var datai={};
                        datai.id=codes[i];
                        datai.name=names[i];
                        datas.push(datai);
                    }
                    top.chooseWeb.chooseProgramaCiteVal={"data":datas};
                }else{//表示新增
                    top.chooseWeb.chooseProgramaCiteVal={"data":[]};
                }
                var url=tourl((gps.form?"hnjjwz/":"")+'html/programaClassifyInfo/programaInfoTree.html?',href);
                top.dialogP(url,gps.form?"programaCiteList":'programaCiteList','选择关联栏目','chooseProgramaCite',false,'800');
                //记住decisionOptF这个参数是打开的页面里的名字
            });
        });
        //form表单填充数据之前做什么,data为填充数据的集合,isupdate表示是新增还是修改,若为新增则data为{}
        function beforerender(data,isupdate){
            if(isupdate){
                $('.update-readonly').hide();
            }else{
                $('.update-readonly').show();
            }
        };
        //选择栏目树后用于回显
        window.chooseProgramaCite=function(data){
            var programaAnotherCode = "";
            var programaAnotherName = "";
            var dataLength = data.data.length;
            for(var i=0;i<dataLength;i++){
                programaAnotherCode = programaAnotherCode + data.data[i].orgCode;
                programaAnotherName =  programaAnotherName + data.data[i].displayName;
                if(i<dataLength-1){
                    programaAnotherCode = programaAnotherCode + ",";
                    programaAnotherName = programaAnotherName + ",";
                }
            }
            $("#programaAnotherCode").val(programaAnotherCode);
            $("#programaAnotherName").val(programaAnotherName);
        };
        window.chooseProgramaCiteOne=function(data){
            var programaOneCode = "";
            var programaOneName = "";
            var dataLength = data.data.length;
            for(var i=0;i<dataLength;i++){
                programaOneCode = programaOneCode + data.data[i].orgCode;
                programaOneName =  programaOneName + data.data[i].displayName;
                if(i<dataLength-1){
                    programaOneCode = programaOneCode + ",";
                    programaOneName = programaOneName + ",";
                }
            }
            $("#programaOneCode").val(programaOneCode);
            $("#programaOneName").val(programaOneName);
        };
    </script>
</head>
<style>
    .formTable { width: 100%; margin-top: 30px; border-spacing: 0; border-top: 1px solid #dedede; border-left: 1px solid #dedede; }
    .formTable>tbody>tr:not(:first-child)>td { border-right: 1px solid #dedede; border-bottom: 1px solid #dedede; font-size: 14px; height: 36px; }
    .formTable>tbody>tr>td input,.formTable>tbody>tr>td span,.formTable>tbody>tr>td textarea,.formTable>tbody>tr>td .textbox .textbox-text{ border: none; font-size: 14px; }
    .formTable td.lable{ background-color: #ddf1fe; padding: 5px; text-align: left; }
    .formTable td .textAndInput_readonly, .formTable td .textAndInput_readonly .validatebox-readonly{ background-color: #fff; }
    .cselectorImageUL .btn{ right: 3px; top: 3px; }
    .cselectorImageUL input[type='file']{ right: 25px; top: 3px; }
    textarea{ line-height: 20px; letter-spacing: 1px; }
    table.tabForm{ border-color: #dedede; }
</style>
<body class="body_page">
<!--searchform-->
<form id="programaCiteTableQueryForm" >
    <table border="0" cellpadding="0" cellspacing="6" width="100%">
		<tr>
            <td width="100" align="right">栏目名</td><td width="150"><input name="programaOneName" type="text" value="" /></td>
            <td width="100" align="right">关联关系类型</td><td width="150">
            <input id="searchProgramaCiteType" name="searchProgramaCiteType" class="easyui-combobox"  style="width: 100%; height: 32px;" data-options="
                         valueField: 'value',
                          panelHeight:'auto',
                          ischooseall:true,
                          ischooseallTxt:'请选择',
                          textField: 'name',
                          queryParams:{'dictType':'programaCiteType'},
                          contentType:'application/json; charset=utf-8',
                          url: web.rootdir+'sys/dictValue/findDictValue'"/>
        </td>
            <td>
                <div class="w100">
                    <a class="btn a_primary fr searchtable"><span>查询</span></a>   
					<a class="btn a_success showDialog fr mr10"><span>新增</span></a>
                </div>
                </td>
        </tr>
    </table>
</form>
<!--table-->
<div class="programaCiteTable"><table id="programaCiteTable"></table></div>
<!--dialog-->
<div id="buttons" title="新增或修改" class="easyui-dialog" style="width:800px;height:500px;">
<form id="programaCiteTableAddForm" method="post" contentType="application/json; charset=utf-8" cmd-select="action/programaCite/findById" beforerender="beforerender()">
	<input id="id" name="id" type="hidden" />
	<table border="0" style="width: 100%;" class="tabForm formTable" cellpadding="0" cellspacing="10" >
        <tr>
            <td width="20%"></td>
            <td width="80%"></td>
        </tr>
        <tr>
            <td align="right" class="lable">栏目<font class="col_r">*</font></td>
            <td>
                <input id="programaOneName" name="programaOneName"  type="text" class="easyui-validatebox" readonly="readonly" required='required'  style="width:calc(100% - 60px)" />
                <a class="btn a_warning chooseProgramaOne fr"><i class="iconfont">&#xe634;</i></a>
            </td>
        </tr>
        <tr style="display: none">
            <td align="right" class="lable">栏目编码</td>
            <td>
                <input id="programaOneCode" name="programaOneCode" type="text" readonly="readonly" class="easyui-validatebox"  />
            </td>
        </tr>
        <tr>
            <td align="right" class="lable">关联栏目<font class="col_r">*</font></td>
            <td>
                <input id="programaAnotherName" name="programaAnotherName" type="text" readonly="readonly" class="easyui-validatebox"  required='required'  style="width:calc(100% - 60px)"  />
                <a class="btn a_warning chooseProgramaAnother fr"><i class="iconfont">&#xe634;</i></a>
            </td>
        </tr>
        <tr style="display: none">
            <td align="right" class="lable">关联栏目编码</td>
            <td><input id="programaAnotherCode" name="programaAnotherCode" type="text" readonly="readonly" class="easyui-validatebox"  /></td>
        </tr>
        <tr>
            <td align="right" class="lable">关联关系类型<font class="col_r">*</font></td>
            <td>
                <input id="programaCiteType" name="programaCiteType" class="easyui-combobox"  style="width: 100%; height: 32px;" required="true" data-options="
                            valueField: 'value',
                            panelHeight:'auto',
                            ischooseall:true,
                            ischooseallTxt:'请选择',
                            textField: 'name',
                            queryParams:{'dictType':'programaCiteType'},
                            contentType:'application/json; charset=utf-8',
                            url: web.rootdir+'sys/dictValue/findDictValue'"/>
            </td>
            <td></td>
        </tr>
    </table>
</form>
</div>
</body>
</html>
