package com.simbest.boot.hnjjwz.examOnline.model;

import com.simbest.boot.base.annotations.EntityIdPrefix;
import com.simbest.boot.base.model.LogicModel;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.*;
import org.hibernate.annotations.GenericGenerator;

import javax.persistence.*;
import java.util.List;

/**
 * <AUTHOR>
 * @Data 2019/05/08
 * Description 题目表
 */
@Data
@EqualsAndHashCode(callSuper = true)
@NoArgsConstructor
@AllArgsConstructor
@Entity(name = "us_exam_question")
@ApiModel(value = "题目表")
public class ExamQuestion extends LogicModel {
    @Id
    @Column(name = "id", length = 40)
    @GeneratedValue(generator = "snowFlakeId")
    @GenericGenerator (name = "snowFlakeId", strategy = "com.simbest.boot.util.distribution.id.SnowflakeId")
    @EntityIdPrefix (prefix = "QB") //主键前缀，此为可选项注解
    private String id;

    @Column(length = 250)
    @Setter
    @Getter
    @ApiModelProperty(value = "题目编码", required = true)
    private String questionCode;

    @Column(length = 250)
    @Setter
    @Getter
    @ApiModelProperty(value = "题目名", required = true)
    private String questionName;

    @Column(length = 250)
    @Setter
    @Getter
    @ApiModelProperty(value = "题目类型", required = true)
    private String questionType;

    @Transient
    private String bigLocation;//大题位置

    @Transient
    private String smallLocation;//小题位置

    @Transient
    private String publishName;

    @Transient
    private String publishOrgCode;//发布人所在组织code

    @Transient
    private String publishOrgName;//发布人所在组织名

    @Transient
    private String publishDisplayName;//发布人所在组织全路径

    @Column(length = 250)
    @Setter
    @Getter
    @ApiModelProperty(value = "额外字段1", required = true)
    private String spare1;

    @Column(length = 250)
    @Setter
    @Getter
    @ApiModelProperty(value = "额外字段2", required = true)
    private String spare2;

    @Column(length = 250)
    @Setter
    @Getter
    @ApiModelProperty(value = "额外字段3", required = true)
    private String spare3;

    @Transient
    private List<ExamAnswer> examAnswerList;
}
