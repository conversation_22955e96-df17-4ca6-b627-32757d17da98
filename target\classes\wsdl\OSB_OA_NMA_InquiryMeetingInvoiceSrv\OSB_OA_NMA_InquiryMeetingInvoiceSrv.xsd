<schema attributeFormDefault="unqualified"
        elementFormDefault="qualified"
        targetNamespace="http://mss.cmcc.com/OSB_OA_NMA_InquiryMeetingInvoiceSrv"
        xmlns:tns="http://mss.cmcc.com/OSB_OA_NMA_InquiryMeetingInvoiceSrv"
        xmlns:msg="http://mss.cmcc.com/MsgHeader"
        xmlns="http://www.w3.org/2001/XMLSchema">
        <import namespace="http://mss.cmcc.com/MsgHeader" schemaLocation="MsgHeader.xsd"/>
 
        <element name="OSB_OA_NMA_InquiryMeetingInvoiceSrvRequest" type="tns:OSB_OA_NMA_InquiryMeetingInvoiceSrvRequest"/>
        <element name="OSB_OA_NMA_InquiryMeetingInvoiceSrvResponse" type="tns:OSB_OA_NMA_InquiryMeetingInvoiceSrvResponse"/>
                <complexType name="OSB_OA_NMA_InquiryMeetingInvoiceSrvRequest">
                         <sequence>
                                <element name="MsgHeader" type="msg:MsgHeader"/>
                                <element name="USERNAME" type="string" nillable="true"/>
                                <element name="RECORD_NUM" type="string" nillable="true"/>
                                <element name="LAST_UPDATE_START_DATE" type="dateTime" nillable="true"/>
                                <element name="LAST_UPDATE_END_DATE" type="dateTime" nillable="true"/>
                          </sequence>
                 </complexType>
 
                 <complexType name="OSB_OA_NMA_InquiryMeetingInvoiceSrvResponse">
                          <sequence>
                                <element name="SERVICE_FLAG" type="string" nillable="true"/>
                                <element name="SERVICE_MESSAGE" type="string" nillable="true"/>
                                <element name="INSTANCE_ID" type="decimal" nillable="true"/>
                                <element name="TOTAL_RECORD" type="decimal" nillable="true"/>
                                <element name="TOTAL_PAGE" type="decimal" nillable="true"/>
                                <element name="PAGE_SIZE" type="decimal" nillable="true"/>
                                <element name="CURRENT_PAGE" type="decimal" nillable="true"/>
                                <element name="OSB_OA_NMA_InquiryMeetingInvoiceSrvOutputCollection" type="tns:OSB_OA_NMA_InquiryMeetingInvoiceSrvOutputCollection"/>
                           </sequence>
                 </complexType>
 
                 <complexType  name="OSB_OA_NMA_InquiryMeetingInvoiceSrvOutputCollection">
                           <sequence>
                                <element name="OSB_OA_NMA_InquiryMeetingInvoiceSrvOutputItem" type="tns:OSB_OA_NMA_InquiryMeetingInvoiceSrvOutputItem" minOccurs="0" maxOccurs="unbounded"/>
                           </sequence>
                 </complexType>
                 <complexType  name="OSB_OA_NMA_InquiryMeetingInvoiceSrvOutputItem">
                           <sequence>
                                <element name="DATAS_Collection" type="tns:DATAS_Collection"/>
                           </sequence>
                 </complexType>
                 <complexType  name="DATAS_Collection">
                           <sequence>
                                <element name="DATAS_Item" type="tns:DATAS_Item" minOccurs="0" maxOccurs="unbounded"/>
                           </sequence>
                 </complexType>
                 <complexType  name="DATAS_Item">
                           <sequence>
                                <element name="RECORD_NUM" type="string" nillable="true"/>
                                <element name="COMPANY" type="string" nillable="true"/>
                                <element name="COMPANY_CODE" type="string" nillable="true"/>
                                <element name="DEPARTMENT" type="string" nillable="true"/>
                                <element name="DEPARTMENT_CODE" type="string" nillable="true"/>
                                <element name="USERNAME" type="string" nillable="true"/>
                                <element name="TRUENAME" type="string" nillable="true"/>
                                <element name="TITLE" type="string" nillable="true"/>
                                <element name="CONTENT" type="string" nillable="true"/>
                                <element name="PERSONS" type="string" nillable="true"/>
                                <element name="PERSONS_NUMBER" type="decimal" nillable="true"/>
                                <element name="ASSIST_CONTENT" type="string" nillable="true"/>
                                <element name="BEGIN_TIME" type="dateTime" nillable="true"/>
                                <element name="END_TIME" type="dateTime" nillable="true"/>
                                <element name="ADDRESS" type="string" nillable="true"/>
                                <element name="APPLY_TYPE" type="string" nillable="true"/>
                                <element name="APPLY_STANDARD" type="string" nillable="true"/>
                                <element name="APPLY_BUDGET" type="decimal" nillable="true"/>
                                <element name="APPLY_BUDGET_MAX" type="decimal" nillable="true"/>
                                <element name="IRESERVED_1" type="string" nillable="true"/>
                                <element name="IRESERVED_2" type="string" nillable="true"/>
                                <element name="IRESERVED_3" type="string" nillable="true"/>
                                <element name="IRESERVED_4" type="string" nillable="true"/>
                                <element name="IRESERVED_5" type="string" nillable="true"/>
                                <element name="LAST_UPDATE_DATE" type="dateTime" nillable="true"/>
                           </sequence>
                 </complexType>
</schema> 
