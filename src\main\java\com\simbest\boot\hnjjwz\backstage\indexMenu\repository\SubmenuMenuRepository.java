package com.simbest.boot.hnjjwz.backstage.indexMenu.repository;/**
 * Created by KZH on 2019/5/30 16:51.
 */

import com.simbest.boot.base.repository.LogicRepository;
import com.simbest.boot.hnjjwz.backstage.indexMenu.model.SubmenuMenu;
import org.springframework.data.jpa.repository.Query;

import java.util.List;

/**
 * <AUTHOR>
 * @create 2019-05-30 16:51
 * @desc 子菜单
 **/
public interface SubmenuMenuRepository extends LogicRepository<SubmenuMenu,String> {

    @Query(value = "select  t.* from us_submenu_menu t where   t.enabled=1",
            nativeQuery = true)
    List<SubmenuMenu> findSubmenuMenu();
}
