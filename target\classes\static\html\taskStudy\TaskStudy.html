<!DOCTYPE html>
<html xmlns="http://www.w3.org/1999/xhtml"
      xmlns:th="http://www.thymeleaf.org">
<head>
    <title>课题研究起草内容</title>
    <meta http-equiv="Content-Type" content="text/html; charset=utf-8"/>
    <meta http-equiv="X-UA-Compatible" content="IE=edge" />
    <meta name="viewport" content="width=device-width, initial-scale=1, shrink-to-fit=no"/>
    <!--
    <link href="http://************:8088/simbestui/fonts/iconfont/iconfont.css?v=svn.revision" th:href="@{http://************:8088/simbestui/fonts/iconfont/iconfont.css?v=svn.revision}" rel="stylesheet"/>
    -->
    <link href="../../fonts/iconfont/iconfont.css?v=svn.revision" th:href="@{/fonts/iconfont/iconfont.css?v=svn.revision}" rel="stylesheet"/>
    <link href="http://************:8088/simbestui/js/themes/default/easyui.css?v=svn.revision" th:href="@{http://************:8088/simbestui/js/themes/default/easyui.css?v=svn.revision}" rel="stylesheet"/>
    <link href="http://************:8088/simbestui/js/themes/icon.css?v=svn.revision" th:href="@{http://************:8088/simbestui/js/themes/icon.css?v=svn.revision}" rel="stylesheet"/>
    <link href="http://************:8088/simbestui/css/public.css?v=svn.revision" th:href="@{http://************:8088/simbestui/css/public.css?v=svn.revision}" rel="stylesheet"/>
    <link href="http://************:8088/simbestui/css/pages.css?v=svn.revision" th:href="@{http://************:8088/simbestui/css/pages.css?v=svn.revision}" rel="stylesheet"/>
    <script src="http://************:8088/simbestui/js/jquery.min.js?v=svn.revision" th:src="@{http://************:8088/simbestui/js/jquery.min.js?v=svn.revision}" type="text/javascript"></script>
    <script src="http://************:8088/simbestui/js/jquery.easyui.min.js?v=svn.revision" th:src="@{http://************:8088/simbestui/js/jquery.easyui.min.js?v=svn.revision}" type="text/javascript"></script>
    <script src="http://************:8088/simbestui/js/easyui-lang-zh_CN.js?v=svn.revision" th:src="@{http://************:8088/simbestui/js/easyui-lang-zh_CN.js?v=svn.revision}" type="text/javascript"></script>
    <script src="http://************:8088/simbestui/js/kindeditor/kindeditor.min.js?v=svn.revision" th:src="@{http://************:8088/simbestui/js/kindeditor.min.js?v=svn.revision}" type="text/javascript"></script>
    <script src="../../js/jquery.config.js?v=svn.revision" th:src="@{/js/jquery.config.js?v=svn.revision}" type="text/javascript"></script>
    <script src="http://************:8088/simbestui/js/jquery.zjsfile.js?v=svn.revision" th:src="@{http://************:8088/simbestui/js/jquery.zjsfile.js?v=svn.revision}" type="text/javascript"></script>
    <script src="http://************:8088/simbestui/js/jquery.aduq.js?v=svn.revision" th:src="@{http://************:8088/simbestui/js/jquery.aduq.js?v=svn.revision}" type="text/javascript"></script>
    <script src="http://************:8088/simbestui/js/jquery.process.js?v=svn.revision" th:src="@{http://************:8088/simbestui/js/jquery.process.js?v=svn.revision}" type="text/javascript"></script>
    <script type="text/javascript">
        var aF;

        //获取当前登录人
        getCurrent();

        //查看当前登录人是属于分公司还是省公司
        var CompanyName=web.currentUser.belongCompanyName;
        //提交之前校验
        function beforesubmit(data){
            return true;
            //return activeD();
        };
        function loadF(){
            $("#belongCompanyTypeDictValue").val(web.currentUser.belongCompanyTypeDictValue);
            $("#belongDepartmentCode").val(web.currentUser.belongDepartmentCode);
            $("#belongDepartmentName").val(web.currentUser.belongCompanyTypeDictValue=="03"?web.currentUser.belongCompanyName:web.currentUser.belongDepartmentName);
            $("#belongCompanyCode").val(web.currentUser.belongCompanyCode);
        };
        //校验
        function activeMoney(){
            if(aF){
                clearTimeout(aF);
            }
            aF=setTimeout(function(){
                activeD();
            },1500);
        };
        //保存草稿关闭页面
        function approvalS(data){
            top.tabClick("processTask");
            top.tabClose("li_meetingForm");
        };
        $(function(){

            // 加载表单
            //loadForm("taskStudyTable",{"pmInstId":gps.pmInstId});
            var param={
                "htmlName":"TaskStudy",//跟html文件名一致
                "formId":"taskStudyTable",
                "processNextCmd":"action/taskStudy/startSubmitProcess",
                "processDeleteCmd":"action/taskStudy/deleteProcess",
                "processDraftDeleteCmd": "action/taskStudy/deleteDraft"
            };
            loadProcess(param);

        });

        function getcallback(data){
            //处理渲染数据之后的操作代码,data为接口返回数据。一般用于对html标签的处理。
            if( ("hnjjwz.start"==gps.location || undefined == gps.location ) && "examine"!=gps.type ){
            }else if("hnjjwz.general_manager"==gps.location&&"join"==gps.type){
                //formReadonlyNo("taskStudyTable");
                $(".nextBtn").hide();
                //$(".wfmgModifyBtn").hide();
                $(".cancel").hide();
                $(".formReset").hide();
                //$(".printOut").hide();
                idReadonly("belongDepartmentName");
            }else if("hnjjwz.general_manager"==gps.location&&"task"==gps.type){
                formReadonlyNo("taskStudyTable");
                $(".cancel").hide();
                $(".formReset").hide();
                //$("#td3").hide();
            }
            if(true){
                $("#videoFile").css('display','table-row');
            }
            if("examine"==gps.type){
                formReadonly("taskStudyTable");
            }

            if(gps.type=="examine" || gps.type=="join" || gps.type=="task"){
                $("#tableForm").css('width', '100%');
                $("#tableForm .textAndInput_readonly").removeClass("textAndInput_readonly");
            }
        };

        function nextBtnOther() {
            if(CompanyName=="省公司"){
                return {"processType": "A"};
            }else {
                return {"processType": "B"};
            }

        };
        //保存草稿
        $(document).on("click", ".saveDraft", function () {
            if(formValidate("taskStudyTable")){
                // formsubmit('taskStudyTable','action/taskStudy/saveDraft?source=PC');
                top.tabClick("processDraft");
                top.tabClose("li_workOrder");
            }
        });
    </script>
</head>
<style>
    .formTable { width: 100%; margin-top: 30px; border-spacing: 0; border-top: 1px solid #dedede; border-left: 1px solid #dedede; }
    .formTable>tbody>tr:not(:first-child)>td { border-right: 1px solid #dedede; border-bottom: 1px solid #dedede; font-size: 14px; height: 36px; }
    .formTable>tbody>tr>td input,.formTable>tbody>tr>td span,.formTable>tbody>tr>td textarea,.formTable>tbody>tr>td .textbox .textbox-text{ border: none; font-size: 14px; }
    .formTable td.lable{ background-color: #ddf1fe; padding: 5px; text-align: left; }
    .formTable td .textAndInput_readonly, .formTable td .textAndInput_readonly .validatebox-readonly{ background-color: #fff; }
    .cselectorImageUL .btn{ right: 3px; top: 3px; }
    .cselectorImageUL input[type='file']{ right: 25px; top: 3px; }
    textarea{ line-height: 20px; letter-spacing: 1px; }
    table.tabForm{ border-color: #dedede; }
</style>
<body class="body_page" style="padding-top:85px;"><!--noNextUserLocation无下一审批人的节点比如归档，可以为多个节点中间用|隔开-->
<form id="taskStudyTable" method="post" formLocation="hnjjwz.start" archiveLocation="hnjjwz.confirm" noNextUserDecisionId="end" contentType="application/json; charset=utf-8"
      nextBtnOther="nextBtnOther()" cmd-select="action/taskStudy/getApprovalFromDetail" onSubmit="beforesubmit()" submitcallback="approvalS()" getcallback="getcallback()" >
    <div class="pageInfo">
        <div class="pageInfoD">
            <a class="hide btn small fl mr15 nextBtn"><i class="iconfont">&#xe688;</i><font>流转下一步</font></a>
            <a class="hide btn small fl mr15 saveDraft" onclick="formsubmit('taskStudyTable','action/taskStudy/saveDraft')"><i class="iconfont">&#xe63a;</i><font>保存草稿</font></a>
            <a class="hide btn small fl mr15 abolish"><i class="iconfont">&#xe6ec;</i><font>废除草稿</font></a>
            <a class="hide btn small fl mr15 cancel"><i class="iconfont">&#xe6ec;</i><font>注销</font></a>
            <a class="hide btn small fl mr15 flowTrack"><i class="iconfont">&#xe68c;</i><font>流程跟踪</font></a>
            <a class="hide btn small fl mr15 viewComments"><i class="iconfont">&#xe629;</i><font>查看意见</font></a>
            <a class="hide btn small fl mr15 formReset" onclick="formreset('taskStudyTable')"><i class="iconfont">&#xe646;</i><font>重置</font></a>
<!--            <a class="hide btn small fl mr15 printOut"><i class="iconfont">&#xe678;</i><font>打印</font></a>-->
            <a class="hide btn small fl mr15 processImg"><i class="iconfont">&#xe6bd;</i><font>流程图</font></a>
        </div>
    </div>
    <fieldset class="title">
        <legend><font>课题研究内容信息</font></legend>
    </fieldset>
    <input id="id" name="id" type="hidden" noReset="true"/>
    <input id="belongCompanyTypeDictValue" name="belongCompanyTypeDictValue" type="hidden" noReset="true"/>
    <input id="belongDepartmentCode" name="belongDepartmentCode" type="hidden" noReset="true"/>
    <input id="belongCompanyCode" name="belongCompanyCode" type="hidden" noReset="true"/>
    <input id="pmInsId" name="pmInsId" type="hidden" noReset="true"/>
    <input id="describe" name="describe" type="hidden" noReset="true"/>
    <table border="0" style="width: 100%;" class="tabForm formTable" cellpadding="0" cellspacing="10" id="tableForm">
        <tr>
            <td width="20%"></td>
            <td width="80%"></td>
        </tr>
        <tr >
            <td class="lable">栏目<font class="col_r">*</font></td>
            <td>
                <!-- <select class="easyui-combobox" data-options="panelHeight:'auto'" id="programaDisplayName" name="programaDisplayName" required="true"
                        style="width:  100%; height: 32px;">
                    <option selected value="">--请选择--</option>
                    <option  value="课题部署">课题研究/课题部署</option>
                    <option  value="成果表彰">课题研究/成果表彰</option>
                    <option  value="成果共享">课题研究/成果共享</option>
                </select> -->

                <input id="programaDisplayName" name="programaDisplayName" style="width: 100%; height: 32px;"  type="text" required 
                    class="easyui-combobox" data-options="
                    valueField: 'label',
                    textField: 'value',
                    ischooseall:true,
                    panelHeight:'auto',
                    editable:false,
                    data: [{ label: '课题研究/课题部署', value: '课题部署' },{ label: '课题研究/成果表彰', value: '成果表彰' },{ label: '课题研究/成果共享', value: '成果共享' }]"/>

            </td>
        </tr>
        <tr>
            <td class="lable">课题研究标题<font class="col_r">*</font></td>
            <td>
                <input id="taskStudyTitle" name="taskStudyTitle" type="text" class="easyui-validatebox"  required="true"/>
            </td>
        </tr>
        <tr>
            <td align="right" width="100" height="100" class="lable" >课题研究附件</td>
            <td valign="top" style="padding:3px;" >
                <input id="taskStudyFile" name="taskStudyFile" type="text" mulaccept="image/*" class="cselectorImageUpload" sizelength="1"
                       btnmsg="<i class='iconfont' title='添加'>&#xe641;</i>" href="sys/file/uploadProcessFiles?pmInsType=slideShowType&pmInsTypePart=1" />
            </td>
        </tr>
    </table>
</form>
</body>
</html>
