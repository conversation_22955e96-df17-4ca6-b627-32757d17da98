package com.simbest.boot.hnjjwz.examOnline.repository;

import com.simbest.boot.base.repository.LogicRepository;
import com.simbest.boot.hnjjwz.examOnline.model.ExamInfo;
import org.springframework.data.domain.Page;
import org.springframework.data.domain.Pageable;
import org.springframework.data.jpa.repository.Query;
import org.springframework.data.repository.query.Param;

import java.util.List;
import java.util.Map;
import java.util.Set;

/**
 * <AUTHOR>
 * @Data 201/05/08
 * @Description 试卷信息
 */
public interface ExamInfoRepository extends LogicRepository<ExamInfo,String> {

    /**
     * 查询保存后的试卷内容
     * @param onlyRecord
     * @return
     */
    @Query (value =  " SELECT * " +
            " FROM us_exam_question_result ueqs,us_exam_info uei,us_exam_question_bank ueqb,us_exam_question ueq," +
            " us_exam_answer uea " +
            " WHERE uei.only_record = :onlyRecord " +
            " AND ueqs.only_record = ues.only_record AND ueqs.question_bank_code = ueqb.question_bank_code " +
            " AND ueqs.question_code = ueq.question_code AND ueqs.answer_code = uea.answer_code " +
            " AND uei.enabled = 1 AND uei.removed_time IS NULL " +
            " AND ueqb.enabled = 1 AND ueqb.removed_time IS NULL " +
            " AND ueq.enabled = 1 AND ueq.removed_time IS NULL " +
            " AND uea.enabled = 1 AND uea.removed_time IS NULL ",
            nativeQuery = true)
    Set<Map<String,Object>> findSaveQuestion(@Param( "onlyRecord" ) String onlyRecord );


    @Query(value = "select * from US_EXAM_INFO t where t.publish_name=:username and t.enabled=1",
    nativeQuery = true)
    Page<ExamInfo> findOneself(@Param( "username" ) String username, Pageable pageable);




}
