package com.simbest.boot.hnjjwz.task;/**
 * Created by GZJ on 2019/5/13 9:11.
 */

import com.simbest.boot.component.distributed.lock.AppRuntimeMaster;
import com.simbest.boot.component.task.AbstractTaskSchedule;
import com.simbest.boot.hnjjwz.column.familyWind.service.IRecordPersonService;
import com.simbest.boot.sys.repository.SysTaskExecutedLogRepository;
import lombok.extern.slf4j.Slf4j;

import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.scheduling.annotation.Scheduled;
import org.springframework.stereotype.Component;



/**
 * <AUTHOR>
 * @create 2019-05-13 9:11
 * @desc 定时器任务
 **/
@Slf4j
@Component
public class Scheduler extends AbstractTaskSchedule {

    public Scheduler(AppRuntimeMaster appRuntime, SysTaskExecutedLogRepository repository) {
        super(appRuntime, repository);
    }


    @Autowired
    private IRecordPersonService iRecordPersonService;

    /**
     * 开启定时器
     */

    @Scheduled(cron="${app.task.time.vote}")
    //@Scheduled(fixedRate = 2000)
    public void checkAndExecute() {
        super.checkAndExecute(true);
    }

    @Override
    public String execute() {
        //TODO 注意此方法中需要加try Catch，不然会导致定时器当前执行的工作停掉，开始下一次。

        log.info("测试执行-------------------------------");


        try {

            Boolean aBoolean = iRecordPersonService.updateOnVoteQuantity();
            if(aBoolean){
                log.info("初始化票数完成-------------------------------");
                return "ok";

            }

            log.info("初始化票数失败-------------------------------");
            return "error";


        } catch (Exception e) {
            e.printStackTrace();
        }

        return null;
    }


}
