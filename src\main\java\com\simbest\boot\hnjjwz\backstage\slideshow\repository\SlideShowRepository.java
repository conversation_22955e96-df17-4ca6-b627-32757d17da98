package com.simbest.boot.hnjjwz.backstage.slideshow.repository;

import com.simbest.boot.base.repository.LogicRepository;
import com.simbest.boot.hnjjwz.backstage.announcement.model.Announcement;
import com.simbest.boot.hnjjwz.backstage.slideshow.model.SlideShow;
import org.springframework.data.domain.Page;
import org.springframework.data.domain.Pageable;
import org.springframework.data.jpa.repository.Modifying;
import org.springframework.data.jpa.repository.Query;
import org.springframework.data.repository.query.Param;
import org.springframework.transaction.annotation.Transactional;

import java.util.List;
import java.util.Map;

/**
 * <AUTHOR>
 * @Data 201/04/01
 * @Description 栏目的关联关系
 */
public interface SlideShowRepository extends LogicRepository<SlideShow,String> {

    @Query(value = "             select t.*" +
            "            from (select t.*" +
            "            from us_slide_show t" +
            "            where t.IS_PUBLISH = 1" +
            "            and t.enabled = 1" +
            "            and t.creation_time is not null" +
            "            order by t.creation_time desc) t where  rownum <= 5",
            nativeQuery = true)
    List<SlideShow> findSlideShow();

    @Transactional
    @Query(
            value = "select a.* from us_slide_show a where a.enabled = 1  and a.id=:id",
            nativeQuery = true
    )
    SlideShow getApprovalFromDetail(@Param("id") String id);

    @Query(
            value = "select a.* from us_slide_show a where a.enabled = 1  and a.pm_ins_id=:pmInstId ",
            nativeQuery = true
    )
    SlideShow getApprovalFromDetailFromInstId(@Param("pmInstId") String pmInstId);

 /*   *//**
     * 查询当前登录人起草审批的的报送
     * @param trueName
     * @param pageable
     * @return
     *//*
    @Query(
            value = "select t.id,t.created_time," +
                    "       t.pm_ins_id,"+
                    "       t.department_name, " +
                    "       t.programa_display_name,"+
                    "       t.truename,t.slide_show_title," +
                    "       t.creation_time, " +
                    "       vuoo.displayname as belongDepartmentDisplayName" +
                    "  from us_slide_show t, uums.v_org vuoo" +
                    " WHERE t.username = :trueName" +
                    "   AND t.belong_department_code = vuoo.orgcode" +
                    "   AND t.enabled = 1 order by t.creation_time desc",
            countQuery = "select count(*)" +
                    "  from us_slide_show t, uums.v_org vuoo" +
                    " WHERE t.username = :trueName" +
                    "   AND t.belong_department_code = vuoo.orgcode" +
                    "   AND t.enabled = 1 order by t.creation_time desc",
            nativeQuery = true
    )
    Page<Map<String,Object>> findUserNameDetailList(@Param ("trueName")String trueName, Pageable pageable);*/


    /**
     * 查看栏目列表，选择了栏目时
     * @param title
     * @param pageable
     * @return
     */
    @Query (value =  " select pdi.id,pdi.created_time," +
            " pdi.pm_ins_id,"+
            " pdi.department_name, " +
            " pdi.truename,pdi.slide_show_title," +
            " pdi.programa_display_name,"+
            " pdi.creation_time" +
            " from us_slide_show pdi" +
            " WHERE pdi.slide_show_title LIKE concat( concat('%',:title),'%') " +
            " AND pdi.enabled=1 AND pdi.removed_time IS NULL AND pdi.is_publish = 1 order by pdi.creation_time desc",
            countQuery = "SELECT count(*) " +
                    " from us_slide_show pdi" +
                    " WHERE pdi.slide_show_title LIKE concat( concat('%',:title),'%') " +
                    " AND pdi.enabled=1 AND pdi.removed_time IS NULL AND pdi.is_publish = 1 order by pdi.creation_time desc",
            nativeQuery = true)
    Page<Map<String,Object>> findDataDetailList(@Param ( "title" ) String title, Pageable pageable );


    /**
     * 查看栏目列表
     * @param
     * @param pageable
     * @return
     */
    @Query (value =  " select pdi.id,pdi.created_time," +
            " pdi.pm_ins_id,"+
            " pdi.programa_display_name,"+
            " pdi.department_name, " +
            " pdi.truename,pdi.slide_show_title," +
            " pdi.creation_time" +
            " from us_slide_show pdi WHERE pdi.enabled=1 " +
            " AND pdi.removed_time IS NULL " +
            " AND pdi.is_publish = 1 order by pdi.creation_time desc",
            countQuery = "SELECT count(*) " +
                    " from us_slide_show pdi WHERE pdi.enabled=1 " +
                    " AND pdi.removed_time IS NULL " +
                    " AND pdi.is_publish = 1 order by pdi.creation_time desc",
            nativeQuery = true)
    Page<Map<String,Object>> findDataDetailListNoCode(Pageable pageable );

/*    *//**
     * 查询当前登录人审批过的的报送
     * @param trueName
     * @param pageable
     * @return
     *//*
    @Query(
            value = "select t.id,t.created_time," +
                    "  t.pm_ins_id,"+
                    "  t.programa_display_name,"+
                    "  t.department_name, " +
                    "  t.truename,t.slide_show_title," +
                    "  t.creation_time, " +
                    "  vuoo.displayname as belongDepartmentDisplayName" +
                    "  from us_slide_show t, uums.v_org vuoo, WF_WORKITEM_MODEL wf  " +
                    "  where  wf.assistant= :trueName" +
                    "  and t.pm_ins_id=wf.receipt_code"+
                    "  and t.belong_department_code = vuoo.orgcode" +
                    "  and t.enabled = 1 ",
            countQuery = "select count(*)" +
                    "  from us_slide_show t, uums.v_org vuoo, WF_WORKITEM_MODEL wf  " +
                    "  where  wf.assistant= :trueName"+
                    "  and t.pm_ins_id=wf.receipt_code"+
                    "  and t.belong_department_code = vuoo.orgcode"+
                    "  and t.enabled = 1 ",
            nativeQuery = true
    )
    Page<Map<String,Object>> findUserNameApprovalDetailList(@Param ("trueName")String trueName,Pageable pageable);*/

    /**
     * 根据Id获取实体
     * @param
     * @return
     */
    @Query(value = "select  t.* from us_slide_show t where  t.id=:id and t.IS_PUBLISH=1  and t.enabled=1",
            nativeQuery = true)
    SlideShow findSlideShowId(@Param ( "id" )String id);

    /**
     * 倒叙取第一个
     * @param
     * @return
     */
    @Query(value = "select t.* from (select t.* from us_slide_show t where   t.IS_PUBLISH=1  and t.enabled=1 order by t.stick_flag  desc ) t  where ROWNUM <=1",
            nativeQuery = true)
    SlideShow findFlashback();

    @Transactional
    @Modifying
    @Query(value = "update us_slide_show set enabled=0 where   pm_ins_id=:pmInsId",
            nativeQuery = true)
    void deleteByPmInsId(@Param ( "pmInsId" )String pmInsId);

    @Query(value = "    SELECT  *  FROM" +
            "            (" +
            "            SELECT" +
            "            t.*" +
            "            FROM" +
            "            US_SLIDE_SHOW t" +
            "            WHERE" +
            "            ENABLED = 1" +
            "            AND IS_PUBLISH = 1" +
            "            and creation_Time is not null" +
            "            ORDER BY" +
            "            DISPLAY_ORDER ASC," +
            "            creation_Time DESC" +
            "            )" +
            "            WHERE   ROWNUM <= 5",nativeQuery = true)
    List<SlideShow> getSlideShowArticleList();
}
