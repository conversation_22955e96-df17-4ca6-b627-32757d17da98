package com.simbest.boot.hnjjwz.examOnline.service;


import com.simbest.boot.base.service.ILogicService;
import com.simbest.boot.base.web.response.JsonResponse;
import com.simbest.boot.hnjjwz.examOnline.model.ExamInfo;

import org.springframework.data.domain.Pageable;
import java.util.List;
import java.util.Map;

/**
 * <strong>Title : </strong><br>
 * <strong>Description : </strong><br>
 * <strong>Create on : 2018/6/19</strong><br>
 * <strong>Modify on : 2018/6/19</strong><br>
 * <strong>Copyright (C) Ltd.</strong><br>
 * 试卷表
 * <AUTHOR> 刘萌@simbest.com.cn
 * @version <strong>V1.0.0</strong><br>
 * <strong>修改历史:</strong><br>
 * 修改人 修改日期 修改描述<br>
 * -------------------------------------------<br>
 */
public interface IExamInfoService extends ILogicService<ExamInfo,String> {

    /**
     * 查询保存后的试卷内容
     * @param onlyRecord
     * @return
     */
    JsonResponse findSaveQuestion(String onlyRecord);

    /**
     * 保存试卷
     * @return
     */
    JsonResponse saveExamInfo(ExamInfo examInfo);

    /**
     * 进行评分
     * @param examInfo
     * @return
     */
    JsonResponse gradExam(ExamInfo examInfo);

    /**
     * 生成试卷
     * @return
     */
    Long createExamInfo() throws Exception;

    /**
     * 随机试卷
     * @return
     */
    JsonResponse randomExam(String questionBankCode);

    /**
     * 评测试卷
     * @param map
     * @return
     */
    JsonResponse evaluatingExam(Map<Object, List<Object>> map);

    /**
     * 查询自己的试卷
     * @return
     */
    JsonResponse findOneself(Pageable pageable);

}
