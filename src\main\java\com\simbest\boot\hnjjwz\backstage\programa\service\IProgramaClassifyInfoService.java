package com.simbest.boot.hnjjwz.backstage.programa.service;


import com.simbest.boot.base.service.ILogicService;
import com.simbest.boot.base.web.response.JsonResponse;
import com.simbest.boot.hnjjwz.backstage.programa.model.ProgramaClassifyInfo;
import org.springframework.data.domain.Pageable;

import java.util.List;

/**
 * <strong>Title : </strong><br>
 * <strong>Description : </strong><br>
 * <strong>Create on : 2018/6/19</strong><br>
 * <strong>Modify on : 2018/6/19</strong><br>
 * <strong>Copyright (C) Ltd.</strong><br>
 *
 * <AUTHOR> 刘萌@simbest.com.cn
 * @version <strong>V1.0.0</strong><br>
 * <strong>修改历史:</strong><br>
 * 修改人 修改日期 修改描述<br>
 * -------------------------------------------<br>
 */
public interface IProgramaClassifyInfoService extends ILogicService<ProgramaClassifyInfo,String> {

    /**
     * 根据栏目类型(精确)以及栏目分类名称(模糊)查询
     * @param programaClassifyName
     * @param programaType
     * @param pageable
     * @return
     */
    JsonResponse findDimProgramaClassify( String programaClassifyName, String programaType, Pageable pageable );

    /**
     * 查询所有的栏目类型id，放入list中
     * @return
     */
    List<String> findAllProgramaClassifyIds();
}
