package com.simbest.boot.hnjjwz.process.wf.repository;


import com.simbest.boot.base.repository.LogicRepository;
import com.simbest.boot.hnjjwz.process.wf.model.PmInstence;
import org.springframework.data.jpa.repository.Modifying;
import org.springframework.data.jpa.repository.Query;
import org.springframework.data.repository.query.Param;
import org.springframework.transaction.annotation.Transactional;

import java.util.Map;

/**
 * <strong>Title : </strong><br>
 * <strong>Description : </strong><br>
 * <strong>Create on : 2018/6/19</strong><br>
 * <strong>Modify on : 2018/6/19</strong><br>
 * <strong>Copyright (C) Ltd.</strong><br>
 *
 * @<NAME_EMAIL>
 * @version <strong>V1.0.0</strong><br>
 * <strong>修改历史:</strong><br>
 * 修改人 修改日期 修改描述<br>
 * -------------------------------------------<br>
 */
public interface PmInstenceRepository extends LogicRepository<PmInstence,String> {

    String sql1 = "select * from us_pm_instence a WHERE a.pm_ins_id=:pmInsId";
    @Query (value = sql1,nativeQuery = true)
    PmInstence findByPmInsId( @Param ( "pmInsId" ) String pmInsId);

    @Transactional
    @Modifying
    @Query(
            value = "update us_pm_instence set enabled=0 where id = :id",
            nativeQuery = true
    )
    int deleteByFromId(@Param("id") String id);

    /**
     * 获取US_PM_INSTENCE和US_APPROVAL_FORM的所有信息
     * @param bussinessKey
     * @return
     */
    @Query (value = " SELECT * FROM US_PM_INSTENCE upi,US_APPROVAL_FORM uaf WHERE upi.pm_ins_id = uaf.pm_ins_id " +
            " AND upi.id=:bussinessKey "
            ,nativeQuery = true)
    Map<String,Object> findPmApproByBuKey( @Param ( "bussinessKey" ) String bussinessKey);
}
