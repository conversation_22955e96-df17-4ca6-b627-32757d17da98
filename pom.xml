<project xmlns="http://maven.apache.org/POM/4.0.0" xmlns:xsi="http://www.w3.org/2001/XMLSchema-instance"
         xsi:schemaLocation="http://maven.apache.org/POM/4.0.0 http://maven.apache.org/xsd/maven-4.0.0.xsd">
    <modelVersion>4.0.0</modelVersion>
    <parent>
        <groupId>com.simbest.boot</groupId>
        <artifactId>simbest-boot-parent</artifactId>
        <version>0.3</version>
        <relativePath>../simbest-boot-parent</relativePath>
    </parent>

    <groupId>com.simbest.boot</groupId>
    <artifactId>hnjjwz</artifactId>
    <version>0.1</version>
    <packaging>jar</packaging>

    <name>${project.artifactId}</name>
    <description>河南移动纪检监察网站</description>

    <properties>
        <simbest.version>0.1</simbest.version>
        <simbest.new.version>0.3</simbest.new.version>
        <project.build.sourceEncoding>UTF-8</project.build.sourceEncoding>
        <profileActive>dev</profileActive>

        <server.servlet.context-path>/${project.artifactId}</server.servlet.context-path>
        <server.servlet.session.timeout>14400</server.servlet.session.timeout>
        <server.servlet.session.cookie.max-age>${server.servlet.session.timeout}</server.servlet.session.cookie.max-age>
        <spring.servlet.multipart.max-file-size>10MB</spring.servlet.multipart.max-file-size>
        <spring.servlet.multipart.max-request-size>10MB</spring.servlet.multipart.max-request-size>
        <spring.datasource.type>com.alibaba.druid.pool.DruidDataSource</spring.datasource.type>
        <spring.datasource.database.type>h2</spring.datasource.database.type>
        <spring.datasource.driver-class-name>org.h2.Driver</spring.datasource.driver-class-name>
        <spring.datasource.url>jdbc:h2:mem:testdb;DB_CLOSE_DELAY=-1;MODE=Oracle;AUTO_RECONNECT=TRUE;DB_CLOSE_ON_EXIT=FALSE</spring.datasource.url>
        <spring.datasource.username>sa</spring.datasource.username>
        <spring.datasource.password></spring.datasource.password>
        <spring.jpa.database-platform>org.hibernate.dialect.H2Dialect</spring.jpa.database-platform>
        <spring.datasource.sql-script-encoding>${project.build.sourceEncoding}</spring.datasource.sql-script-encoding>
        <spring.datasource.druid.initial-size>5</spring.datasource.druid.initial-size>
        <spring.datasource.druid.min-idle>10</spring.datasource.druid.min-idle>
        <spring.datasource.druid.max-active>20</spring.datasource.druid.max-active>
        <spring.datasource.druid.max-wait>60000</spring.datasource.druid.max-wait>
        <spring.datasource.druid.time-between-eviction-runs-millis>60000</spring.datasource.druid.time-between-eviction-runs-millis>
        <spring.datasource.druid.min-evictable-idle-time-millis>300000</spring.datasource.druid.min-evictable-idle-time-millis>
        <spring.datasource.druid.validation-query>SELECT 1 FROM DUAL</spring.datasource.druid.validation-query>
        <spring.datasource.druid.filter.stat.slow-sql-millis>5000</spring.datasource.druid.filter.stat.slow-sql-millis>
        <spring.datasource.druid.filter.wall.enabled>false</spring.datasource.druid.filter.wall.enabled>
        <spring.datasource.druid.stat-view-servlet.login-username>hadmin</spring.datasource.druid.stat-view-servlet.login-username>
        <spring.datasource.druid.stat-view-servlet.login-password>pass180418</spring.datasource.druid.stat-view-servlet.login-password>
        <spring.redis.cluster.nodes>localhost:6379</spring.redis.cluster.nodes>
        <spring.redis.cluster.password>123456</spring.redis.cluster.password>
        <spring.redis.cluster.max-redirects>3</spring.redis.cluster.max-redirects>
        <spring.cache.redis.key-prefix>cache:key:${project.artifactId}:</spring.cache.redis.key-prefix>
        <spring.console.enabled>true</spring.console.enabled>
        <spring.console.path>/h2-console</spring.console.path>
        <spring.jpa.show-sql>true</spring.jpa.show-sql>
        <spring.jpa.generate-ddl>true</spring.jpa.generate-ddl>
        <spring.jpa.hibernate.ddl-auto>update</spring.jpa.hibernate.ddl-auto>
        <spring.jpa.database>${spring.datasource.database.type}</spring.jpa.database>
        <spring.jpa.properties.hibernate.dialect>org.hibernate.dialect.Oracle9iDialect</spring.jpa.properties.hibernate.dialect>
        <spring.messages.encoding>${project.build.sourceEncoding}</spring.messages.encoding>
        <spring.http.encoding.charset>${project.build.sourceEncoding}</spring.http.encoding.charset>
        <spring.thymeleaf.cache>false</spring.thymeleaf.cache>
        <spring.thymeleaf.enable-spring-el-compiler>true</spring.thymeleaf.enable-spring-el-compiler>
        <spring.session.store-type>redis</spring.session.store-type>
        <spring.session.redis.cleanup-cron>0 * * * * *</spring.session.redis.cleanup-cron>
        <spring.session.redis.namespace>spring:session:${project.artifactId}</spring.session.redis.namespace>
        <management.endpoints.web.exposure.include>*</management.endpoints.web.exposure.include>

        <logback.groupId>${project.groupId}</logback.groupId>
        <logback.artifactId>${project.artifactId}</logback.artifactId>
        <app.file.upload.path>D:/file</app.file.upload.path>
        <app.file.upload.location>disk</app.file.upload.location>
        <app.oa.portal.token>SIMBEST_SSO</app.oa.portal.token>
        <app.uums.address>http://************:8088/uums</app.uums.address>
        <!--<app.uums.address>http://localhost:8001/uums</app.uums.address>-->
        <app.swagger.address>http://localhost:8080${server.servlet.context-path}/swagger-ui.html</app.swagger.address>
        <app.druid.monitor.address>http://localhost:8080${server.servlet.context-path}/druid</app.druid.monitor.address>
        <app.actuator.monitor.address>http://localhost:8080${server.servlet.context-path}/actuator</app.actuator.monitor.address>
        <app.task.heart.test.job>0 0/5 * * * ?</app.task.heart.test.job>
        <app.captcha.enable>false</app.captcha.enable>

        <!--保存考卷的定时器，每隔两分钟保存一次-->
        <app.task.sync.exam>0 0/2 * * * ?</app.task.sync.exam>
        <app.task.time.vote>0 30 0 * * ?</app.task.time.vote><!--每天更新投票次数-->


        <!--bps的路径端口号-->
        <app.host.port>http://***********:8088</app.host.port>

        <!-- 是否开启多租户 -->
        <tenant>true</tenant>
        <!-- 多租户ID -->
        <tenant_id>${project.artifactId}</tenant_id>

        <!-- BPS配置 -->
        <bps.logicName>default</bps.logicName><!-- 对应BPS应用名称 -->
        <bps.uddiHost>***********</bps.uddiHost><!-- 对应BPS服务器IP -->
        <bps.uddiPort>8888</bps.uddiPort><!-- 对应BPS服务器端口 -->
        <bps.uddiAdminPort>6200</bps.uddiAdminPort><!-- 对应BPS管理端口端口 -->

        <!-- 统一待办 -->
        <cxf.openTodoUrl>http://***********:8011/OA/proxy/SB_OA_OA_ImportToDoOpenListInfoSrv?wsdl</cxf.openTodoUrl>
        <cxf.closeTodoUrl>http://***********:8011/OA/proxy/SB_OA_OA_ImportToDoCloseListInfoSrv?wsdl</cxf.closeTodoUrl>
        <cxf.cancelTodoUrl>http://***********:8011/OA/proxy/SB_OA_OA_ImportToDoCancelListInfoSrv?wsdl</cxf.cancelTodoUrl>

        <!-- 非港区地址 -->
        <spring.cloud.nacos.config.server-addr>*************:8088</spring.cloud.nacos.config.server-addr>
        <spring.cloud.nacos.config.username>nacos</spring.cloud.nacos.config.username>
        <spring.cloud.nacos.config.password>Best@2008</spring.cloud.nacos.config.password>
    </properties>

    <dependencies>
        <dependency>
            <groupId>com.simbest.boot</groupId>
            <artifactId>simbest-boot-procssdata-push-workmanager</artifactId>
            <version>0.1</version>
        </dependency>
        <dependency>
            <groupId>com.simbest.boot</groupId>
            <artifactId>simbest-boot-cmcc</artifactId>
            <version>0.3</version>
        </dependency>
        <dependency>
            <groupId>com.simbest.boot</groupId>
            <artifactId>simbest-boot-cores</artifactId>
            <version>0.3</version>
        </dependency>

        <dependency>
            <groupId>org.apache.cxf</groupId>
            <artifactId>cxf-spring-boot-starter-jaxws</artifactId>
            <version>3.2.4</version>
            <!-- <scope>system</scope>
             <systemPath>${project.basedir}/lib/cxf-spring-boot-starter-jaxws-3.2.4.jar</systemPath>-->
        </dependency>
        <!--流程引擎接口-->
        <!-- -->
        <dependency>
            <groupId>com.simbest.boot</groupId>
            <artifactId>simbest-boot-wf</artifactId>
            <version>0.1</version>
        </dependency>

        <dependency>
            <groupId>com.simbest.boot</groupId>
            <artifactId>simbest-boot-bps</artifactId>
            <!--<version>${simbest.version}</version>-->
            <version>0.2</version>
        </dependency>

        <!-- 热部署模块 -->
        <dependency>
            <groupId>org.springframework.boot</groupId>
            <artifactId>spring-boot-devtools</artifactId>
            <optional>true</optional> <!-- 这个需要为 true 热部署才有效 -->
        </dependency>

        <!-- BPS -->
        <dependency>
            <groupId>bps.simbest</groupId>
            <artifactId>bfs_client</artifactId>
            <version>0.x</version>
        </dependency>
        <dependency>
            <groupId>bps.simbest</groupId>
            <artifactId>bfs_client_eos</artifactId>
            <version>0.x</version>
        </dependency>
        <dependency>
            <groupId>bps.simbest</groupId>
            <artifactId>bfs_common</artifactId>
            <version>0.x</version>
        </dependency>
        <dependency>
            <groupId>bps.simbest</groupId>
            <artifactId>bfs_console</artifactId>
            <version>0.x</version>
        </dependency>
        <dependency>
            <groupId>bps.simbest</groupId>
            <artifactId>bfs_engineall</artifactId>
            <version>0.x</version>
        </dependency>
        <dependency>
            <groupId>bps.simbest</groupId>
            <artifactId>bfs_interface</artifactId>
            <version>0.x</version>
        </dependency>
        <dependency>
            <groupId>bps.simbest</groupId>
            <artifactId>bps-api</artifactId>
            <version>7.0.0.0</version>
        </dependency>
        <dependency>
            <groupId>bps.simbest</groupId>
            <artifactId>com.primeton.eos.foundation</artifactId>
            <version>7.1.3.0</version>
        </dependency>
        <dependency>
            <groupId>bps.simbest</groupId>
            <artifactId>eos-server-access</artifactId>
            <version>7.1.3.0</version>
        </dependency>
        <dependency>
            <groupId>bps.simbest</groupId>
            <artifactId>eos-server-access-ejb</artifactId>
            <version>7.1.3.0</version>
        </dependency>
        <dependency>
            <groupId>bps.simbest</groupId>
            <artifactId>eos-server-common</artifactId>
            <version>7.1.3.0</version>
        </dependency>
        <dependency>
            <groupId>bps.simbest</groupId>
            <artifactId>eos-server-das</artifactId>
            <version>7.1.3.0</version>
        </dependency>
        <dependency>
            <groupId>bps.simbest</groupId>
            <artifactId>eos-server-data</artifactId>
            <version>7.1.3.0</version>
        </dependency>
        <dependency>
            <groupId>bps.simbest</groupId>
            <artifactId>eos-server-engine</artifactId>
            <version>7.1.3.0</version>
        </dependency>
        <dependency>
            <groupId>bps.simbest</groupId>
            <artifactId>eos-server-runtime</artifactId>
            <version>7.1.3.0</version>
        </dependency>
        <dependency>
            <groupId>bps.simbest</groupId>
            <artifactId>eos-server-sca</artifactId>
            <version>7.1.3.0</version>
        </dependency>
        <dependency>
            <groupId>bps.simbest</groupId>
            <artifactId>eos-server-spring</artifactId>
            <version>7.1.3.0</version>
        </dependency>
        <dependency>
            <groupId>bps.simbest</groupId>
            <artifactId>eos-server-system</artifactId>
            <version>7.1.3.0</version>
        </dependency>
        <dependency>
            <groupId>bps.simbest</groupId>
            <artifactId>ptp-server-commons</artifactId>
            <version>*******</version>
        </dependency>
        <dependency>
            <groupId>bps.simbest</groupId>
            <artifactId>ptp-server-das</artifactId>
            <version>*******</version>
        </dependency>
        <dependency>
            <groupId>bps.simbest</groupId>
            <artifactId>ptp-server-data</artifactId>
            <version>*******</version>
        </dependency>
        <dependency>
            <groupId>bps.simbest</groupId>
            <artifactId>ptp-server-datacontext</artifactId>
            <version>*******</version>
        </dependency>
        <dependency>
            <groupId>bps.simbest</groupId>
            <artifactId>ptp-server-deploy</artifactId>
            <version>*******</version>
        </dependency>
        <dependency>
            <groupId>bps.simbest</groupId>
            <artifactId>ptp-server-engine</artifactId>
            <version>*******</version>
        </dependency>
        <dependency>
            <groupId>bps.simbest</groupId>
            <artifactId>ptp-server-jdbc</artifactId>
            <version>*******</version>
        </dependency>
        <dependency>
            <groupId>bps.simbest</groupId>
            <artifactId>ptp-server-l7e</artifactId>
            <version>*******</version>
        </dependency>
        <dependency>
            <groupId>bps.simbest</groupId>
            <artifactId>ptp-server-runtime</artifactId>
            <version>*******</version>
        </dependency>
        <dependency>
            <groupId>bps.simbest</groupId>
            <artifactId>ptp-server-scriptengine</artifactId>
            <version>*******</version>
        </dependency>
        <dependency>
            <groupId>bps.simbest</groupId>
            <artifactId>ptp-server-spring</artifactId>
            <version>*******</version>
        </dependency>
        <dependency>
            <groupId>bps.simbest</groupId>
            <artifactId>ptp-server-spring4das</artifactId>
            <version>*******</version>
        </dependency>
        <dependency>
            <groupId>bps.simbest</groupId>
            <artifactId>ptp-server-statistic</artifactId>
            <version>*******</version>
        </dependency>
        <dependency>
            <groupId>bps.simbest</groupId>
            <artifactId>ptp-server-wsclient</artifactId>
            <version>*******</version>
        </dependency>
        <!-- BPS end-->
        <!-- BPS 3rd-->
        <dependency>
            <groupId>stax</groupId>
            <artifactId>stax-api</artifactId>
            <version>1.0.1</version>
        </dependency>
        <dependency>
            <groupId>org.codehaus.woodstox</groupId>
            <artifactId>wstx-asl</artifactId>
            <version>3.2.9</version>
        </dependency>
        <dependency>
            <groupId>xalan</groupId>
            <artifactId>xalan</artifactId>
            <version>2.5.0</version>
        </dependency>
        <dependency>
            <groupId>xerces</groupId>
            <artifactId>xercesImpl</artifactId>
            <version>2.8.1</version>
        </dependency>
        <dependency>
            <groupId>com.thoughtworks.xstream</groupId>
            <artifactId>xstream</artifactId>
            <version>1.2.2</version>
        </dependency>
        <dependency>
            <groupId>ognl</groupId>
            <artifactId>ognl</artifactId>
            <version>2.6.9</version>
        </dependency>
        <dependency>
            <groupId>org.eclipse.emf.commonj</groupId>
            <artifactId>sdo</artifactId>
            <version>2.3.0-v200706262000</version>
        </dependency>

        <dependency>
            <groupId>org.springframework.boot</groupId>
            <artifactId>spring-boot-starter-data-ldap</artifactId>
        </dependency>
        <!--性能监控-->
        <dependency>
            <groupId>net.bull.javamelody</groupId>
            <artifactId>javamelody-spring-boot-starter</artifactId>
            <version>1.76.0</version>
        </dependency>
        <!-- BPS 3rd end-->

        <!-- https://mvnrepository.com/artifact/log4j/log4j -->
        <dependency>
            <groupId>log4j</groupId>
            <artifactId>log4j</artifactId>
            <version>1.2.17</version>
        </dependency>
        <!-- https://mvnrepository.com/artifact/commons-lang/commons-lang -->
        <dependency>
            <groupId>commons-lang</groupId>
            <artifactId>commons-lang</artifactId>
            <version>2.6</version>
        </dependency>

        <dependency>
            <groupId>com.alibaba.cloud</groupId>
            <artifactId>spring-cloud-starter-alibaba-nacos-config</artifactId>
            <version>2.2.9.RELEASE</version>
        </dependency>

        <!--spring-cloud NACOS 注册中心-->
        <dependency>
            <groupId>com.alibaba.cloud</groupId>
            <artifactId>spring-cloud-starter-alibaba-nacos-discovery</artifactId>
            <version>2.2.9.RELEASE</version>
        </dependency>

        <dependency>
            <groupId>com.oceanbase</groupId>
            <artifactId>oceanbase-client</artifactId>
            <version>2.4.9</version>
        </dependency>

        <!-- https://mvnrepository.com/artifact/cn.hutool/hutool-all -->
        <!--4A上传问题原因冲突-->
<!--        <dependency>
            <groupId>cn.hutool</groupId>
            <artifactId>hutool-all</artifactId>
            <version>4.6.6</version>
        </dependency>-->
    </dependencies>

    <dependencyManagement>
        <dependencies>

        </dependencies>
    </dependencyManagement>

    <build>
        <finalName>${project.artifactId}</finalName>
        <plugins>
            <plugin>
                <groupId>org.springframework.boot</groupId>
                <artifactId>spring-boot-maven-plugin</artifactId>
                <configuration>
                    <fork>true</fork>
                    <addResources>false</addResources>
                    <profiles>
                        <profile>dev</profile>
                        <profile>uat</profile>
                        <profile>obuat</profile>
                        <profile>prd</profile>
                        <profile>obprd</profile>
                    </profiles>
                </configuration>
                <executions>
                    <execution>
                        <goals>
                            <goal>repackage</goal>
                        </goals>
                    </execution>
                </executions>
            </plugin>
        </plugins>

        <pluginManagement>

        </pluginManagement>
    </build>

    <profiles>
        <profile>
            <id>dev</id>
            <properties>
                <profileActive>dev</profileActive>
            </properties>
            <activation>
                <activeByDefault>true</activeByDefault>
            </activation>
        </profile>
        <profile>
            <id>uat</id>
            <properties>
                <profileActive>uat</profileActive>
                <spring.datasource.database.type>oracle</spring.datasource.database.type>
                <spring.datasource.driver-class-name>oracle.jdbc.OracleDriver</spring.datasource.driver-class-name>
<!--                <spring.datasource.url>*****************************************************= (PROTOCOL=TCP)(HOST=***********)(PORT=1521)))(CONNECT_DATA=(SERVICE_NAME = bpsdb)))</spring.datasource.url>-->
                <spring.datasource.url>*****************************************************= (PROTOCOL=TCP)(HOST=************)(PORT=1521)))(CONNECT_DATA=(SERVICE_NAME = orcl)))</spring.datasource.url>
                <spring.datasource.username>${project.artifactId}</spring.datasource.username>
                <spring.datasource.password>Test44Hz</spring.datasource.password>
<!--                <spring.redis.cluster.nodes>***********:6379</spring.redis.cluster.nodes>-->
<!--                <spring.redis.cluster.password>Xianzai@2605</spring.redis.cluster.password>-->
                <spring.redis.config.type>dictValueRedis</spring.redis.config.type>
                <spring.redis.config.type.redis>redis-161</spring.redis.config.type.redis>
                <spring.redis.cluster.password>Xianzai@0528</spring.redis.cluster.password>

                <spring.jpa.database-platform>org.hibernate.dialect.Oracle10gDialect</spring.jpa.database-platform>
                <spring.datasource.druid.filter.wall.enabled>true</spring.datasource.druid.filter.wall.enabled>
                <app.file.upload.path>/home/<USER>/simbestboot/uploadFiles/hnjjwz</app.file.upload.path>
                <app.host.port>http://************:8088</app.host.port>
                <!-- 统一待办 -->
                <cxf.openTodoUrl>http://************:8088/ntodo/services/openTodoService?wsdl</cxf.openTodoUrl>
                <cxf.closeTodoUrl>http://************:8088/ntodo/services/closeTodoService?wsdl</cxf.closeTodoUrl>
                <cxf.cancelTodoUrl>http://************:8088/ntodo/services/cancelTodoService?wsdl</cxf.cancelTodoUrl>
            </properties>
        </profile>
        <profile>
            <id>obuat</id>
            <properties>
                <profileActive>obuat</profileActive>
                <spring.jpa.database>oracle</spring.jpa.database>
                <spring.datasource.database.type>oceanbase</spring.datasource.database.type>
                <spring.datasource.driver-class-name>com.oceanbase.jdbc.Driver</spring.datasource.driver-class-name>
                <spring.datasource.url>*************************************************************************************************************</spring.datasource.url>
                <spring.datasource.username>hnjjwz@obtest#obdemo</spring.datasource.username>
                <spring.datasource.password>Test44Hz</spring.datasource.password>

                <spring.redis.config.type>dictValueRedis</spring.redis.config.type>
                <spring.redis.config.type.redis>redis-161</spring.redis.config.type.redis>
                <spring.redis.cluster.password>Xianzai@0528</spring.redis.cluster.password>

                <spring.jpa.database-platform>org.hibernate.dialect.Oracle10gDialect</spring.jpa.database-platform>
                <spring.datasource.druid.filter.wall.enabled>true</spring.datasource.druid.filter.wall.enabled>
                <app.file.upload.path>/home/<USER>/simbestboot/uploadFiles/hnjjwz</app.file.upload.path>
                <app.host.port>http://************:8088</app.host.port>
                <!-- 统一待办 -->
                <cxf.openTodoUrl>http://************:8088/ntodo/services/openTodoService?wsdl</cxf.openTodoUrl>
                <cxf.closeTodoUrl>http://************:8088/ntodo/services/closeTodoService?wsdl</cxf.closeTodoUrl>
                <cxf.cancelTodoUrl>http://************:8088/ntodo/services/cancelTodoService?wsdl</cxf.cancelTodoUrl>
            </properties>
        </profile>
        <profile>
            <id>prd</id>
            <properties>
                <profileActive>prd</profileActive>
                <app.uums.address>http://************:8088/uums</app.uums.address>
                <spring.jpa.generate-ddl>false</spring.jpa.generate-ddl>
                <spring.datasource.database.type>oracle</spring.datasource.database.type>
                <spring.datasource.driver-class-name>oracle.jdbc.OracleDriver</spring.datasource.driver-class-name>
                <spring.datasource.url>*****************************************************=(PROTOCOL=TCP)(HOST=***********)(PORT=1521))(ADDRESS=(PROTOCOL=TCP)(HOST=***********)(PORT=1521))(FAILOVER=on)(LOAD_BALANCE=YES))(CONNECT_DATA=(SERVER=DEDICATED)(SERVICE_NAME=oradb)(failover_mode=(type=select)(method=basic))))</spring.datasource.url>
                <spring.datasource.username>${project.artifactId}</spring.datasource.username>
                <spring.datasource.password>Prd7374Hz</spring.datasource.password>
<!--                <spring.redis.cluster.nodes>************:26379,************:26389,************:26399,************:26379,************:26389,************:26399</spring.redis.cluster.nodes>-->
<!--                <spring.redis.cluster.password>Xianzai@0528</spring.redis.cluster.password>-->
                <spring.redis.config.type>dictValueRedis</spring.redis.config.type>
                <spring.redis.config.type.redis>redis-163-169-6666</spring.redis.config.type.redis>
                <spring.redis.cluster.password>Xianzai@0528</spring.redis.cluster.password>

                <spring.jpa.database-platform>org.hibernate.dialect.Oracle10gDialect</spring.jpa.database-platform>
                <spring.datasource.druid.filter.wall.enabled>true</spring.datasource.druid.filter.wall.enabled>
                <app.captcha.enable>true</app.captcha.enable>

                <app.file.upload.location>disk</app.file.upload.location>
                <app.file.upload.path>/bpsAttachment/bpsAttachment/hnjjwz</app.file.upload.path>
                <!--bps的路径端口号-->
                <app.host.port>http://************:8088</app.host.port>
                <!-- BPS配置 -->
				<bps.logicName>default</bps.logicName><!-- 对应BPS应用名称 -->
                <bps.uddiHost>**************</bps.uddiHost><!-- 对应BPS服务器IP ************ -->
                <bps.uddiPort>8090</bps.uddiPort><!-- 对应BPS服务器端口 -->
                <bps.uddiAdminPort>6200</bps.uddiAdminPort><!-- 对应BPS管理端口端口 -->

                <spring.cloud.nacos.config.server-addr>**************:8088</spring.cloud.nacos.config.server-addr>
                <!-- 统一待办 -->
                <cxf.openTodoUrl>http://************:8088/ntodo/services/openTodoService?wsdl</cxf.openTodoUrl>
                <cxf.closeTodoUrl>http://************:8088/ntodo/services/closeTodoService?wsdl</cxf.closeTodoUrl>
                <cxf.cancelTodoUrl>http://************:8088/ntodo/services/cancelTodoService?wsdl</cxf.cancelTodoUrl>
            </properties>
        </profile>

        <profile>
            <id>obprd</id>
            <properties>
                <profileActive>obprd</profileActive>
                <!-- BPS配置，非bps应用可不配置 -->
                <bps.logicName>default</bps.logicName><!-- 对应BPS应用名称 -->
                <bps.uddiHost>************</bps.uddiHost><!-- 对应BPS服务器IP -->
                <bps.uddiPort>8089</bps.uddiPort><!-- 对应BPS服务器端口 -->
                <bps.uddiAdminPort>6200</bps.uddiAdminPort><!-- 对应BPS管理端口端口 -->

                <!--nacos-->
                <spring.cloud.nacos.config.server-addr>**************:8088</spring.cloud.nacos.config.server-addr>
                <spring.cloud.nacos.config.username>nacos</spring.cloud.nacos.config.username>
                <spring.cloud.nacos.config.password>Best@2008</spring.cloud.nacos.config.password>
            </properties>
        </profile>

    </profiles>

    <!--Nexus 私服-->
    <repositories>
        <repository>
            <id>releases</id>
            <url>http://***********:8082/nexus/repository/releases/</url>
            <releases>
                <enabled>true</enabled>
                <updatePolicy>always</updatePolicy>
            </releases>
        </repository>
        <repository>
            <id>maven-thirdparty</id>
            <url>http://***********:8082/nexus/repository/maven-thirdparty/</url>
            <releases>
                <enabled>true</enabled>
                <updatePolicy>always</updatePolicy>
            </releases>
        </repository>
    </repositories>

    <distributionManagement>
        <repository>
            <id>releases</id>
            <url>http://***********:8082/nexus/repository/releases/</url>
        </repository>
        <snapshotRepository>
            <id>maven-snapshots</id>
            <url>http://***********:8082/nexus/repository/maven-snapshots/</url>
        </snapshotRepository>
    </distributionManagement>

</project>
