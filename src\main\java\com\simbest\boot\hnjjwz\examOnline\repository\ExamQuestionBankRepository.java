package com.simbest.boot.hnjjwz.examOnline.repository;

import com.simbest.boot.base.repository.LogicRepository;
import com.simbest.boot.hnjjwz.examOnline.model.ExamQuestionBank;
import org.springframework.data.jpa.repository.Modifying;
import org.springframework.data.jpa.repository.Query;
import org.springframework.data.repository.query.Param;
import org.springframework.transaction.annotation.Transactional;

/**
 * <AUTHOR>
 * @Data 201/05/08
 * @Description 题库
 */
public interface ExamQuestionBankRepository extends LogicRepository<ExamQuestionBank,String> {


    @Query(value = "select count(*) from US_EXAM_QUESTION_BANK t where t.question_bank_code=:questionBankCode and t.enabled=1",
            nativeQuery = true)
    int findWhetherDistinctBy(@Param("questionBankCode") String questionBankCode);

    @Query(value = "select t.* from US_EXAM_QUESTION_BANK t where t.question_bank_code=:questionBankCode and t.enabled=1",
            nativeQuery = true)
    ExamQuestionBank findSizeExamQuestionBank(@Param("questionBankCode") String questionBankCode);



    String sq1 = "update US_EXAM_QUESTION_BANK t set t.question_bank_size =:questionBankSize where t.question_bank_code=:questionBankCode  and t.enabled=1 ";
    @Modifying
    @Transactional
    @Query(value = sq1,
            nativeQuery = true)
    int updateQuestionBankSize(@Param("questionBankSize") String questionBankSize,@Param("questionBankCode") String questionBankCode);

}
