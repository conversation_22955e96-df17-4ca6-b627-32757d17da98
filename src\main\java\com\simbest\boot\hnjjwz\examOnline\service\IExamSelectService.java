/*
 * 版权所有 © 北京晟壁科技有限公司 2008-2027。保留一切权利!
 */
package com.simbest.boot.hnjjwz.examOnline.service;

import com.simbest.boot.base.web.response.JsonResponse;

/**
 * <strong>Title : IExamSelectService</strong><br>
 * <strong>Description : 成绩查询Service </strong><br>
 * <strong>Create on : 2020/11/12</strong><br>
 * <strong>Modify on : 2020/11/12</strong><br>
 * <strong>Copyright SimBest(C) Ltd.</strong><br>
 *
 * @<NAME_EMAIL>
 * @version <strong>V1.0.0</strong><br>
 * <strong>修改历史:</strong><br>
 * 修改人 修改日期 修改描述<br>
 * -------------------------------------------<br>
 */
public interface IExamSelectService {

    /**
     * 调用exam应用获取考试成绩
     * @param username 账户
     * @return JsonResponse
     */
    JsonResponse findByExamInfoByUsername(String username);

    /**
     * 获取考试汇总权限
     */
    JsonResponse findEffectiveExam(String examCode);
}
