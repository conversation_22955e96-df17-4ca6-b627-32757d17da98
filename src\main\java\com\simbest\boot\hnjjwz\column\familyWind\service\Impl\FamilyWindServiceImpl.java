package com.simbest.boot.hnjjwz.column.familyWind.service.Impl;/**
 * Created by KZH on 2019/7/30 17:21.
 */

import com.simbest.boot.base.service.impl.LogicService;
import com.simbest.boot.hnjjwz.column.familyWind.model.FamilyWind;
import com.simbest.boot.hnjjwz.column.familyWind.repository.FamilyWindRepository;
import com.simbest.boot.hnjjwz.column.familyWind.service.IFamilyWindService;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;

import java.util.List;

/**
 * <AUTHOR>
 * @create 2019-07-30 17:21
 * @desc 家风栏目Service实现
 **/
@Slf4j
@Service
public class FamilyWindServiceImpl extends LogicService<FamilyWind,String> implements IFamilyWindService {

    private FamilyWindRepository familyWindRepository;

    @Autowired
    public FamilyWindServiceImpl(FamilyWindRepository repository){
        super(repository);
        this.familyWindRepository=repository;

    }

    /**
     * 根据familyType获取到数据
     * @param familyType
     * @return
     */
    @Override
    public List<FamilyWind> findFamilyType(String familyType) {
        return familyWindRepository.findFamilyType(familyType);
    }

    /**
     *  根据Id进行增加投票数
     * @param Id
     * @return
     */
    @Override
    public Boolean updateVoteQuantity(String Id) {

        FamilyWind FamilyWind = this.findById(Id);
        Integer voteQuantity = FamilyWind.getVoteQuantity();
        if(voteQuantity==null){
            voteQuantity=0;
        }
        FamilyWind.setVoteQuantity(voteQuantity+1);
        FamilyWind FamilyWind2 =this.update(FamilyWind);
        if(FamilyWind2!=null){
            return  true;
        }
        return false;
    }
}
