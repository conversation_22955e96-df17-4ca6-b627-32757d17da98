package com.simbest.boot.hnjjwz.backstage.announcement.service.impl;

import cn.hutool.core.collection.CollUtil;
import cn.hutool.core.util.ObjectUtil;
import cn.hutool.core.util.StrUtil;
import com.fasterxml.jackson.core.type.TypeReference;
import com.google.common.collect.Maps;
import com.simbest.boot.base.exception.Exceptions;
import com.simbest.boot.base.service.impl.LogicService;
import com.simbest.boot.base.web.response.JsonResponse;
import com.simbest.boot.bps.process.bussiness.model.ActBusinessStatus;
import com.simbest.boot.bps.process.bussiness.service.IActBusinessStatusService;
import com.simbest.boot.bps.process.listener.model.WfWorkItemModel;
import com.simbest.boot.cmcc.nagent.NagentOperatorService;
import com.simbest.boot.constants.ApplicationConstants;
import com.simbest.boot.hnjjwz.attachment.service.IFileExtendService;
import com.simbest.boot.hnjjwz.backstage.announcement.model.Announcement;
import com.simbest.boot.hnjjwz.backstage.announcement.repository.AnnouncementRepository;
import com.simbest.boot.hnjjwz.backstage.announcement.service.IAnnouncementService;
import com.simbest.boot.hnjjwz.backstage.programa.model.ProgramaInfo;
import com.simbest.boot.hnjjwz.backstage.slideshow.model.SlideShow;
import com.simbest.boot.hnjjwz.constants.Constants;
import com.simbest.boot.hnjjwz.newcolumn.service.ISysNewProgramaService;
import com.simbest.boot.hnjjwz.process.wf.model.PmInstence;
import com.simbest.boot.hnjjwz.process.wf.model.ProgramaDataForm;
import com.simbest.boot.hnjjwz.process.wf.service.IPmInstenceService;
import com.simbest.boot.hnjjwz.process.wf.service.IProgramaDataFormService;
import com.simbest.boot.hnjjwz.util.GZIPUtils;
import com.simbest.boot.hnjjwz.util.ImgToBase64Tool;
import com.simbest.boot.hnjjwz.util.OperateLogTool;
import com.simbest.boot.hnjjwz.util.UumsQuerySqlUtil;
import com.simbest.boot.security.*;
import com.simbest.boot.sys.model.SysFile;
import com.simbest.boot.sys.model.SysOperateLog;
import com.simbest.boot.sys.repository.SysDictValueRepository;
import com.simbest.boot.sys.service.ISysDictValueService;
import com.simbest.boot.sys.service.ISysFileService;
import com.simbest.boot.sys.service.ISysOperateLogService;
import com.simbest.boot.templates.MessageEnum;
import com.simbest.boot.util.DateUtil;
import com.simbest.boot.util.distribution.id.IdGenerator;
import com.simbest.boot.util.distribution.id.RedisIdGenerator;
import com.simbest.boot.util.json.JacksonUtils;
import com.simbest.boot.util.security.LoginUtils;
import com.simbest.boot.util.security.SecurityUtils;
import com.simbest.boot.uums.api.app.UumsSysAppDecisionApi;
import com.simbest.boot.uums.api.group.UumsSysGroupApi;
import com.simbest.boot.uums.api.org.UumsSysOrgApi;
import com.simbest.boot.uums.api.user.UumsSysUserinfoApi;
import com.simbest.boot.wf.process.service.IProcessInstanceService;
import com.simbest.boot.wf.process.service.IWfOptMsgService;
import com.simbest.boot.wf.process.service.IWorkItemService;
import com.simbest.boot.wf.unitfytodo.IProcessTodoDataService;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.data.domain.Page;
import org.springframework.data.domain.PageImpl;
import org.springframework.data.domain.Pageable;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;
import org.springframework.transaction.interceptor.TransactionAspectSupport;
import org.springframework.util.StringUtils;

import java.time.LocalDateTime;
import java.util.*;

/**
 * <AUTHOR>
 * @Data 2019/05/06
 * @Description
 */
@Slf4j
@Service
public class AnnouncementServiceImpl extends LogicService<Announcement, String> implements IAnnouncementService {

    private AnnouncementRepository announcementRepository;

    @Autowired
    private ISysFileService sysFileService;

    @Autowired
    private IFileExtendService fileExtendService;

    @Autowired
    private IProgramaDataFormService iProgramaDataFormService;

    @Autowired
    private IProcessInstanceService iProcessInstanceService;

    @Autowired
    private IProcessTodoDataService iProcessTodoDataService;

    @Autowired
    private UumsSysGroupApi uumsSysGroupApi;

    @Autowired
    private ImgToBase64Tool imgToBase64Tool;

    @Autowired
    private UumsQuerySqlUtil uumsQuerySqlUtil;

    @Autowired
    public AnnouncementServiceImpl(AnnouncementRepository announcementRepository) {
        super(announcementRepository);
        this.announcementRepository = announcementRepository;
    }

    @Autowired
    private UumsSysUserinfoApi uumsSysUserinfoApi;

    @Autowired
    private UumsSysOrgApi uumsSysOrgApi;

    @Autowired
    private IPmInstenceService pmInstenceService;

    @Autowired
    private IProcessInstanceService processInstanceService;

    @Autowired
    private IWorkItemService workItemService;

    @Autowired
    private UumsSysAppDecisionApi uumsSysAppDecisionApi;

    @Autowired
    private IActBusinessStatusService statusService;

    @Autowired
    private IWorkItemService workItemManager;

    @Autowired
    private RedisIdGenerator idGenerator;

    @Autowired
    private ISysOperateLogService operateLogService;

    @Autowired
    private OperateLogTool operateLogTool;

    @Value("${app.host.port}")
    private String appIp;

    private final String param1 = "/action/announcement";

    @Autowired
    public ISysDictValueService sysDictValueService;

    @Autowired
    private ISysNewProgramaService newProgramaService;

    /**
     * 根据公告标题(模糊)、发布人姓名(模糊)、是否显示在首页公告滚动处(精确)
     *
     * @param mapObject
     * @param pageable
     * @return
     */
    @Override
    public JsonResponse findAllDim(Map<String, Object> mapObject, Pageable pageable) {
        String announcementTitle = (String) mapObject.get(Constants.ANNOUNCEMENT_TITLE_KEY);
        String creator = (String) mapObject.get(Constants.CREATOR_KEY);
        String isDisplay = (String) mapObject.get(Constants.IS_DISPLAY_KEY);
        List<Boolean> isDisplayList = new ArrayList<>();
        if (StringUtils.isEmpty(announcementTitle)) {
            announcementTitle = ApplicationConstants.EMPTY;
        }
        if (StringUtils.isEmpty(creator)) {
            creator = ApplicationConstants.EMPTY;
        }
        if (StringUtils.isEmpty(isDisplay)) {
            isDisplayList.add(true);
            isDisplayList.add(false);
        } else {
            if ("true".equals(isDisplay)) {
                isDisplayList.add(true);
            }
            if ("false".equals(isDisplay)) {
                isDisplayList.add(false);
            }
        }
        Page<Announcement> announcementPage = announcementRepository.findAllDim(announcementTitle, creator, isDisplayList, pageable);
        List<Announcement> announcementList = announcementPage.getContent();
        List<Announcement> announcementListNew = new ArrayList<>();
        try {
            for (Announcement announcement : announcementList) {
                String creatorNew = announcement.getCreator();
                SimpleUser user = uumsSysUserinfoApi.findByUsernameFromCurrent(creatorNew, Constants.APP_CODE);
                if (user != null) {
                    String orgCode = user.getBelongOrgCode();
                    announcement.setPublishName(user.getTruename());
                    announcement.setPublishOrgCode(orgCode);
                    announcement.setPublishOrgName(user.getBelongOrgName());
                    SimpleOrg simpleOrg = uumsSysOrgApi.findListByOrgCode(Constants.APP_CODE, orgCode);
                    if (simpleOrg != null) {
                        announcement.setPublishDisplayName(simpleOrg.getDisplayName());
                    }
                }
                announcementListNew.add(announcement);
            }
        } catch (Exception e) {
            Exceptions.printException(e);
            return JsonResponse.fail(null, "获取失败！");
        }
        Page<Announcement> announcementPageNew = new PageImpl<Announcement>(announcementListNew, pageable, announcementPage.getTotalElements());
        return JsonResponse.success(announcementPageNew);
    }

    @Override
    public List<Announcement> findAnnouncement() {
        return announcementRepository.findAnnouncement();
    }


    /**
     * 流转下一步
     *
     * @param currentUserCode
     * @param workItemId
     * @param outcome
     * @param location
     * @param bodyParam       前台传来的下一步的数据
     * @return
     */
    @Override
    public JsonResponse nextStep(String currentUserCode, String workItemId, String outcome, String location, String formId, String source, Map<String, Object> bodyParam) {
        if (source == null && !("PC".equals(source) || "MOBILE".equals(source))) {
            source = "PC";
        }
        Map flowParam = (Map) bodyParam.get("flowParam");
        String message = (String) flowParam.get("message");
        //获取form表单
        Object formData = flowParam.get("formData");
        Announcement announcement = new Announcement();
        if (formData != null) {
            String formDataJson = JacksonUtils.obj2json(formData);
            announcement = JacksonUtils.json2Type(formDataJson, new TypeReference<Announcement>() {
            });
        } else {
            if (!StringUtils.isEmpty(formId) && "MOBILE".equals(source)) {
                announcement = this.findById(formId);
            }
        }
        Object nextUserNameObject = flowParam.get("nextUserName");
        String nextUsernameJson = JacksonUtils.obj2json(nextUserNameObject);
        List<Map<String, Object>> nextUsernameList = JacksonUtils.json2Type(nextUsernameJson, new TypeReference<List<Map<String, Object>>>() {
        });
        String nextUserName = new String();
        int resultNum = 0;
        /**
         *
         * 废除归档
         */
        if (Constants.ACTIVITY_REJECT_END.equals(flowParam.get("decisionId"))) {

            for (Map<String, Object> nextUsernameMap : nextUsernameList) {
                String display = (String) nextUsernameMap.get("display");
                if ("user".equals(display)) {
                    resultNum = saveSubmitTask(announcement, workItemId, outcome, message, nextUserName, location, source, currentUserCode);//审批
                }
                if (resultNum == 0) {
                    return JsonResponse.fail(-1, "流转不成功！");
                }
            }
        } else if (Constants.ACTIVITY_END.equals(outcome)) {

            //如果是归档，更新发布时间以及是否发布

            resultNum = saveSubmitTask(announcement, workItemId, outcome, message, nextUserName, location, source, currentUserCode);//审批
            if (resultNum == 0) {
                return JsonResponse.fail(-1, "流转不成功！");
            }

        } else {

            if (nextUsernameList == null || nextUsernameList.isEmpty()) {
                return JsonResponse.fail(-1, "流转时没有人！");
            }

            for (Map<String, Object> nextUsernameMap : nextUsernameList) {
                String display = (String) nextUsernameMap.get("display");
                if ("user".equals(display)) {
                    nextUserName = (String) nextUsernameMap.get("value");


                    //纪检网站没有归档，所以如果是省公司流程中的部门领导审批(不传人)或者是分公司流程中省公司部门领导审核(不传人)或者传人的时候才可以流转
                    if (Constants.ACTIVITY_PROVINCE_DEPART.equals(location) || Constants.ACTIVITY_FILIALE_DEPART.equals(location) || (!StringUtils.isEmpty(nextUserName) && !"null".equals(nextUserName))) {

                        if (announcement.getId() != null && workItemId != null && !"".equals(workItemId)) {

                            resultNum = saveSubmitTask(announcement, workItemId, outcome, message, nextUserName, location, source, currentUserCode);//审批
                        } else {
                            resultNum = startProcess(announcement, nextUserName, outcome, message, source, currentUserCode);//创建提交
                        }
                    } else {
                        return JsonResponse.fail(null, "流转时的下一个人不能为空！");
                    }
                    if (resultNum > 0) {
                        continue;
                    } else {
                        return JsonResponse.fail(-1, "流转不成功！");
                    }
                } else {
                    //没有为组织的情况
                    log.debug("传来的人员组织信息不正确！不能为组织！");
                    return JsonResponse.fail(-1, "传来的人员组织信息不正确！不能为组织！");
                }
            }
        }


        String showMessage = this.getTemplate(nextUserName);
        return JsonResponse.success(1, showMessage);
    }


    /**
     * 起草发起流程
     *
     * @param announcement 会议活动表单
     * @param nextUserName 审批人
     * @param outcome      连线规则
     * @param message      审批意见
     */
    @Override
    public int startProcess(Announcement announcement, String nextUserName, String outcome, String message, String source, String currentUserCode) {
        int ret = 1;
        //准备操作日志参数
        SysOperateLog operateLog = new SysOperateLog();
        String param2 = "/startSubmitProcess";
        String params = "plan=" + announcement.toString() + ",source=" + source + ",userCode=" + currentUserCode + ",outcome=" + outcome + ",message=" + message + ",nextUserName=" + nextUserName;
        operateLog.setInterfaceParam(params);
        //主单据
        PmInstence pmInstence = new PmInstence();
        try {
            //判断是否是从手机端还是PC端记录操作日志
            JsonResponse returnObj = operateLogTool.operationSource(source, currentUserCode, param1, param2, operateLog);
            if (returnObj != null) {
                log.debug("判断是手机端还是pc端操作日志有误！");
                ret = 0;
            }
            IUser iuser = SecurityUtils.getCurrentUser();
            String companyTypeDictDesc = iuser.getBelongCompanyTypeDictValue();
            /**校验表单和下一步审批人是否为空**/
            if (StrUtil.isNotEmpty(nextUserName)) {
                /**获取登录人所在公司应启动的流程**/
                String processUserType = "";
                if ("01".equals(companyTypeDictDesc)) {
                    processUserType = "C";
                } else {
                    processUserType = "D";
                }
                Map<String, String> map = this.getUserProcessMap(processUserType);
                String processDefId = map.get("processName");
                String processType = map.get("processType");
                if (StrUtil.isNotEmpty(processDefId) && StrUtil.isNotEmpty(processType)) {
                    boolean flag = false;
                    //草稿判断
                    PmInstence usPmInstence = new PmInstence();
                    if (announcement.getId() != null && announcement.getPmInsId() != null) {
                        super.update(announcement);
                        this.updateFileByPmInsId(announcement, announcement.getPmInsId(), usPmInstence.getPmInsType());
                        usPmInstence = pmInstenceService.findByPmInsId(announcement.getPmInsId());
                        if (!StrUtil.equals(announcement.getAnnouncementTitle(), usPmInstence.getPmInsTitle())) {
                            usPmInstence.setPmInsTitle(announcement.getAnnouncementTitle());
                            pmInstenceService.update(usPmInstence);
                        }
                        flag = true;
                    } else {
                        usPmInstence.setPmInsType(processType);
                        /**保存业务数据**/
                        flag = this.savePlanTask(announcement, usPmInstence);
                    }
                    /**启动发起流程**/
                    if (flag) {
                        Map<String, Object> variables = Maps.newHashMap();
                        String currentUserCode1 = iuser.getUsername();
                        String currentUserName = iuser.getTruename();
                        variables.put("inputUserId", currentUserCode1);
                        variables.put("receiptId", usPmInstence.getId());
                        variables.put("title", usPmInstence.getPmInsTitle());
                        variables.put("code", usPmInstence.getPmInsId());
                        variables.put("currentUserCode", currentUserCode1);
                        variables.put("activityDefID", Constants.ACTIVITY_START);
                        variables.put("appCode", Constants.APP_CODE);

                        //第一个参数为流程定义名称
                        Long workItemId = processInstanceService.startProcessAndSetRelativeData(processDefId, usPmInstence.getPmInsTitle(), usPmInstence.getPmInsTitle(), false, variables);
                        if (workItemId != 0) {
                            /**提交表单审批处理**/
                            if (StrUtil.isNotEmpty(nextUserName)) {
                                ret = this.processApproval(workItemId, currentUserCode1, currentUserName, nextUserName, outcome, message, usPmInstence);
                            } else {
                                operateLog.setErrorMsg("获取审批人失败");
                                JsonResponse.fail(null, "获取审批人失败");
                            }
                        } else {
                            operateLog.setErrorMsg("启动流程失败");
                            JsonResponse.fail(null, "启动流程失败");
                        }
                    } else {
                        operateLog.setErrorMsg("保存失败");
                        JsonResponse.fail(null, "保存失败");
                    }
                } else {
                    operateLog.setErrorMsg("获取流程失败");
                    JsonResponse.fail(null, "操作失败，获取流程失败!");
                }
            } else {
                operateLog.setErrorMsg("表单为空或审批人为空");
                JsonResponse.fail(null, "操作失败，请确认申请表单和审批人!");
            }
        } catch (Exception e) {
            ret = 0;
            operateLog.setErrorMsg(e.toString());
            Exceptions.printException(e);
        } finally {
            /**保存操作记录**/
            operateLogService.saveLog(operateLog);
            return ret;
        }
    }


    /**
     * 工单流转
     *
     * @param announcement    会议活动表单
     * @param workItemID      工作项ID
     * @param outcome         连线规则
     * @param message         审批意见
     * @param nextUserName    审批人
     * @param location        当前环节
     * @param source          来源
     * @param currentUserCode
     * @return
     */
    @Override
    public int saveSubmitTask(Announcement announcement, String workItemID, String outcome, String message, String nextUserName, String location, String source, String currentUserCode) {
        int ret = 1;
        //获取用户，手机端和pc端是不一样的
        IUser user = null;
        //source默认为PC
        if (source == null && !("PC".equals(source) || "MOBILE".equals(source))) {
            source = "PC";
        }
        //准备操作参数，用于日志记录
        SysOperateLog operateLog = new SysOperateLog();
        String param2 = "/startSubmitProcess";
        String params = "plan=" + ",workItemId=" + workItemID + ",outcome=" + outcome + ",message=" + message + ",nextUserName=" + nextUserName + ",location=" + location + ",copyLocation=" + null + ",copyMessage"
                + null + ",copyNextUserNames=" + null + ",notificationId=" + null + ",source=" + source + ",userCode=" + currentUserCode;
        operateLog.setInterfaceParam(params);
        try {
            //表单数据不为空
            if (announcement != null) {
                //如果是归档，更新发布时间以及是否发布
                if (Constants.ACTIVITY_END.equals(outcome)) {
                    announcement.setCreationTime(DateUtil.getCurrentTimestamp());
                    announcement.setIsPublish(true);
                }


                String pmInsId = announcement.getPmInsId();
                //废除归档环节
                if (Constants.ACTIVITY_REJECT_END.equals(outcome)) {
                    if (pmInsId != null) {
                        // ActBusinessStatus actBusinessStatus = statusService.getByProcessInst(processInstId);
                       /* ActBusinessStatus actBusinessStatus = (ActBusinessStatus)iProcessTodoDataService.queryActBusinessStatusByPmInstId(pmInsId);
                        ret = this.terminateProcessInst(actBusinessStatus.getProcessInstId());*/
                        announcement.setIsPublish(false);

                    } else {
                        operateLog.setErrorMsg("pmInstence为空！pmInsId = " + pmInsId);
                        ret = 0;
                    }

                }

                PmInstence pmInstence = pmInstenceService.getByPmInsId(announcement);
                if (Constants.ACTIVITY_START.equals(location) && org.apache.commons.lang3.StringUtils.isNotEmpty(workItemID) || "hnjjwz.general_manager".equals(location)) {
                    announcement.setInMobile(false);//退回修改不能修改
                } else {

                }
                if (pmInstence != null) {
                    //判断是否是从手机端还是PC端记录操作日志
                    JsonResponse returnObj = operateLogTool.operationSource(source, currentUserCode, param1, param2, operateLog);
                    if (returnObj != null) {
                        log.debug("判断失败！");
                        operateLog.setErrorMsg("判断失败！！pmInsId = " + pmInsId);
                        ret = 0;
                    }
                    //获取用户

                    user = judgeUsername(source, currentUserCode, operateLog);
                    log.debug("user的值为" + user);
                    if (user == null) {
                        ret = 0;
                    }
                    pmInstence.setPmInsCurrentActivity(location);
                    pmInstence.setPmInsTitle(announcement.getAnnouncementTitle());
                    pmInstence.setPmInsTitle(announcement.getAnnouncementTitle());
                    //更新US_PM_INSTENCE表
                    pmInstenceService.update(pmInstence);
                    //更新US_APPROVAL_FORM表
                    Announcement resultApprovalForm = this.update(announcement);
                    //如果是已发布需要向栏目表插入一条记录
                    if(resultApprovalForm.getIsPublish()){
                        newProgramaService.saveAnnouncement(resultApprovalForm);
                    }
                    //更新ACT_BUSINESS_STATUS表的RECEIPT_TITLE字段，需要调用wf的接口
                    int isSuccess = processInstanceService.updateTitleByBusinessKey(pmInstence.getId(), announcement.getAnnouncementTitle());
                    if (isSuccess == 0) {
                        throw new Exception("更新ACT_BUSINESS_STATUS表的RECEIPT_TITLE字段失败！");
                    }

                    this.updatePmInsId(announcement);//更新附件。
                    ret = processApproval(Long.parseLong(workItemID), user.getUsername(), user.getTruename(), nextUserName, outcome, message, pmInstence);

                } else {
                    operateLog.setErrorMsg("pmInstence为空！pmInsId = " + pmInsId);
                    ret = 0;
                }
            } else {
                operateLog.setErrorMsg("approvalForm为空！plan = " + announcement.toString());
                ret = 0;
            }
        } catch (Exception e) {
            Exceptions.printException(e);
            operateLog.setErrorMsg(e.toString());
            ret = 0;
        } finally {
            operateLogService.saveLog(operateLog);
            log.debug("ret的值为：" + ret);
            return ret;
        }

    }

    /**
     * 流程审批
     *
     * @param workItemID      工作项id
     * @param currentUserCode 当前登录人code
     * @param currentUserName 当前登录人名称
     * @param nextUserName    审批人
     * @param outcome         连线规则
     * @param message         审批意见
     * @return
     */
    @Override
    public int processApproval(Long workItemID, String currentUserCode, String currentUserName, String nextUserName, String outcome, String message, PmInstence pmInstence) {
        int ret = 0;
        Map<String, Object> map = new HashMap<>();
        if (nextUserName != null) {
            map.put("inputUserId", nextUserName);//指定下一审批人
        }
        map.put("outcome", outcome);
        map.put("receiptId", String.valueOf(pmInstence.getId()));
        map.put("title", pmInstence.getPmInsTitle());
        map.put("code", pmInstence.getPmInsId());
        map.put("currentUserCode", currentUserCode);
        map.put("appCode", Constants.APP_CODE);
        try {
            //添加流程审批意见
            int status = workItemService.submitApprovalMsg(workItemID, message);
            if (status == 1) {
                //根据工作项ID完成工作项 如果第三个参数为true，则启用事务分割；如果第二个参数为false，则不启用事务分割
                long workItemIdNext = workItemService.finishWorkItemWithRelativeData(workItemID, map, false);
                if (workItemIdNext > 0) {
                    ret = 1;
                } else {
                    ret = 0;
                }
            } else {
                ret = 0;
            }
        } catch (Exception e) {
            e.printStackTrace();
            ret = 0;
        }
        return ret;
    }

    /**
     * 注销流程2
     *
     * @param pmInstId
     * @return
     */
    @Override
    public int terminateProcessInst(Long pmInstId) {
        return iProcessInstanceService.terminateProcessInst(pmInstId);
    }


    /**
     * 获取详情
     *
     * @param processInstId 流程实例id
     * @return
     */
    @Override
    public JsonResponse getApprovalFromDetail(Long processInstId, String source, String currentUserCode, String pmInstId, String location) {
        if (source == null && !("PC".equals(source) || "MOBILE".equals(source))) {
            source = "PC";
        }
        /**操作日志记录参数**/
        SysOperateLog operateLog = new SysOperateLog();
        String param2 = "/getApprovalFromDetail";
        String params = "source=" + source + ",currentUserCode" + currentUserCode;
        operateLog.setInterfaceParam(params);
        Announcement announcement = new Announcement();
        try {
            /**判断是否是从手机端还是PC端记录操作日志**/
            JsonResponse returnObj = operateLogTool.operationSource(source, currentUserCode, param1, param2, operateLog);
            if (returnObj != null) {
                return returnObj;
            }
            if (processInstId != null) {
                ActBusinessStatus actBusinessStatus = statusService.getByProcessInst(processInstId);
                if (actBusinessStatus != null) {
                    String id = actBusinessStatus.getReceiptCode();
                    announcement = announcementRepository.getApprovalFromDetailFromInstId(id);
             /*       String imgToBase64 = imgToBase64Tool.ImgToBase64(announcement.getAnnouncementInfo());
                    String compressBase =  GZIPUtils.compress(imgToBase64);
                    announcement.setCompressBase(compressBase);*/
                    String compressBase = GZIPUtils.compress(announcement.getAnnouncementInfo());
                    announcement.setCompressBase(compressBase);
                /*    if (!"PC".equals(source)){
                        announcement.setAnnouncementInfo(null);
                    }*/
                    String de = GZIPUtils.uncompress(announcement.getCompressBase());
                }

            } else {
                announcement = announcementRepository.getApprovalFromDetailFromInstId(pmInstId);
            }
            if (announcement.getPmInsId() != null) {
                //获取附件信息
                List<SysFile> imageFileList = fileExtendService.getPartFile(announcement.getPmInsId(), Constants.IMAGE_FILE);
                if (imageFileList.size() > 0) {
                    putFile(imageFileList, announcement, Constants.IMAGE_FILE);

                }
            }
            if (Constants.MOBILE.equals(source) && announcement != null) {
                if (Constants.ACTIVITY_START.equals(location)) {
                    announcement.setInMobile(false);//退回修改不能修改
                } else {
                    announcement.setInMobile(true);//
                }
            }


        } catch (Exception e) {
            operateLog.setErrorMsg(e.toString());
            Exceptions.printException(e);
        } finally {
            operateLogService.saveLog(operateLog);
            return JsonResponse.success(announcement);
        }
    }

    @Override
    public JsonResponse findDataDetailList2(Map<String, Object> paramsMap, Pageable pageable) {
        String username = SecurityUtils.getCurrentUserName();
        IUser currentUser = SecurityUtils.getCurrentUser();
        Page<Map<String, Object>> programaDataFormPage;
        Map<String, Object> sqlMap = CollUtil.newHashMap();
        String title = (String) paramsMap.get("title");

        boolean Super_Control = false;
        String programaCodes = (String) paramsMap.get("programaCode");

        Set<SimpleRole> authRoles = (Set<SimpleRole>) currentUser.getAuthRoles();

        for (SimpleRole simpleRole : authRoles) {//遍历所有的角色
            if (Constants.SUPER_CONTROL.equals(simpleRole.getRoleCode()) || Constants.ROLE_S_CJGLY_APPLY.equals(simpleRole.getRoleCode()) || Constants.ROLE_MANAGEMENT.equals(simpleRole.getRoleCode())) {
                Super_Control = true;// 如果所有角色中有纪检超级管理员就设置超级管理状态 进行管理查询
            }
        }

        if (Super_Control) {
            if (title == null) {
                title = "";
            }
            if (StringUtils.isEmpty(programaCodes) && StringUtils.isEmpty(title)) {
                programaDataFormPage = announcementRepository.findDataDetailListNoCode(pageable);//超级查询
            } else {
                //List<String> programaCodeList = Arrays.asList( programaCodes.split( ApplicationConstants.COMMA ) );
                List<String> programaCodeList = new ArrayList<>();
                programaCodeList.add("999");//单独公告 强制999
                programaDataFormPage = announcementRepository.findDataDetailList(programaCodeList, title, pageable);
            }
        } else {//普通权限
            if (StringUtils.isEmpty(programaCodes) && StringUtils.isEmpty(title)) {
//                programaDataFormPage= announcementRepository.findUserNameDetailList(username, pageable);
                Map<String, String> sql1 = new HashMap<>();
                sql1.put("firstParam", username);
                List<Map<String, Object>> query = this.uumsQuerySqlUtil.uumsSelectBySql(sql1, "hnjjwz_001");
                programaDataFormPage = new PageImpl(query, pageable, query.size());
            } else {
                //List<String> programaCodeList = Arrays.asList( programaCodes.split( ApplicationConstants.COMMA ) );
                List<String> programaCodeList = new ArrayList<>();
                programaCodeList.add("999");//单独公告 强制999
                programaDataFormPage = announcementRepository.findDataDetailList(programaCodeList, title, pageable);
            }
        }

        if (programaDataFormPage == null) {//如果都是空
            //如果当前登录人 没有起草过 就去查审批过的起草进行显示
//            programaDataFormPage= announcementRepository.findUserNameApprovalDetailList(username, pageable);
            Map<String, String> sql1 = new HashMap<>();
            sql1.put("firstParam", username);
            List<Map<String, Object>> query = this.uumsQuerySqlUtil.uumsSelectBySql(sql1, "hnjjwz_002");
            programaDataFormPage = new PageImpl(query, pageable, query.size());
        }
        return JsonResponse.success(programaDataFormPage);
    }

    @Override
    public JsonResponse stick(String id) {
        Announcement announcement = announcementRepository.findFlashback();

        int stickFlagBack = announcement.getStickFlag() + 1;
        Announcement relation = announcementRepository.findAnnouncementId(id);
        relation.setStickFlag(stickFlagBack);
        Announcement update = this.update(relation);
        if (update != null) {
            return JsonResponse.success(1, "置顶成功");
        } else {
            return JsonResponse.fail(-1, "置顶失败");
        }

    }

    @Override
    public Announcement getAnnouncementPmInsId(String pmInsId) {
        return announcementRepository.getApprovalFromDetailFromInstId(pmInsId);
    }

    /**
     * 向获得的工单信息中放入附件信息
     *
     * @param fileList
     * @param announcement
     */
    private void putFile(List<SysFile> fileList, Announcement announcement, String fileType) {
        if (fileList != null && fileList.size() > 0) {
            if (Constants.IMAGE_FILE.equals(fileType)) {
                announcement.setAnnouncementFileList(fileList);
            }

        }
    }

    /**
     * 起草阶段根据起草人所在公司起草相应的流程，针对pc端
     *
     * @return
     */
    private String getProcessName() {
        String processName = null;
        IUser iUser = SecurityUtils.getCurrentUser();
        /**根据起草人所在的公司起草相应的流程**/
        /**根据起草人所在的公司起草相应的流程**/
        switch (iUser.getBelongCompanyTypeDictValue()) {
            case Constants.PROVINCIAL_CODE:
                processName = Constants.FLOW_PROVINCIAL;
                break;
            case Constants.BRANCH_CODE_03:
                processName = Constants.FLOW_BRANCH;
                break;
            case Constants.BRANCH_CODE:
                processName = Constants.FLOW_BRANCH;
                break;
            default:
                processName = Constants.FLOW_BRANCH;
                break;
        }
        return processName;
    }

    /**
     * 起草阶段根据起草人所在公司起草相应的流程，针对手机端
     *
     * @param iUser 当前人
     * @return
     */
    private String getProcessNameMobile(IUser iUser) {
        String processName = null;
        /**根据起草人所在的公司起草相应的流程**/
        /**根据起草人所在的公司起草相应的流程**/
        switch (iUser.getBelongCompanyTypeDictValue()) {
            case Constants.PROVINCIAL_CODE:
                processName = Constants.FLOW_PROVINCIAL;
                break;
            case Constants.BRANCH_CODE_03:
                processName = Constants.FLOW_BRANCH;
                break;
            case Constants.BRANCH_CODE:
                processName = Constants.FLOW_BRANCH;
                break;
            default:
                processName = Constants.FLOW_BRANCH;
                break;
        }
        return processName;
    }

    /**
     * 起草阶段根据流程类型生成pmInstId，用于PC端
     *
     * @return
     */
    private String getPmInstId() {
        IUser iUser = SecurityUtils.getCurrentUser();
        return pmInstIdNormal(iUser);
    }

    /**
     * 起草阶段根据起草人所在公司生成相应的订单编号，用于手机端
     *
     * @param iUser
     * @return
     */
    private String getPmInstIdMobile(IUser iUser) {
        return pmInstIdNormal(iUser);
    }

    /**
     * 通用获取pmInstId
     *
     * @param iUser
     * @return
     */
    private String pmInstIdNormal(IUser iUser) {
        //获取会议类型
        String pmInstId = idGenerator.getDateId().toString();
        switch (iUser.getBelongCompanyTypeDictValue()) {
            case Constants.PROVINCIAL_CODE:
                pmInstId = Constants.PROCESS_C + pmInstId;
                break;
            case Constants.BRANCH_CODE:
                pmInstId = Constants.PROCESS_D + pmInstId;
                break;
            default:
                break;
        }
        return pmInstId;
    }


    /**
     * 获取到流转到下一步提示信息
     *
     * @param nextUserName 审批人
     * @return
     */
    private String getTemplate(String nextUserName) {
        Map<String, String> paramMap = Maps.newHashMap();
        String showMessage = "";
        try {
            if (!StringUtils.isEmpty(nextUserName)) {
                // 有代理时才放开
                // String agentUser = getAgentUser( nextUserName );
                IUser user = uumsSysUserinfoApi.findByKey(nextUserName, IAuthService.KeyType.username, Constants.APP_CODE); //审批人
                if (user != null) {
                    List<SimplePosition> simplePositionList = new ArrayList(user.getAuthPositions());
                    paramMap.put("companyName", user.getBelongCompanyName());
                    paramMap.put("departmentName", user.getBelongDepartmentName());
                    paramMap.put("trueName", user.getTruename());
                    paramMap.put("positionName", simplePositionList != null ? simplePositionList.get(0).getPositionName() : "");
                    //有代理时才放开
                    /* if(!StringUtils.isEmpty( agentUser )){
                        IUser userAgent = uumsSysUserinfoApi.findByKey(agentUser, IAuthService.KeyType.username, Constants.APP_CODE); //代理人
                        List<SimplePosition> simpleAgentPositionList = new ArrayList(userAgent.getAuthPositions());
                        paramMap.put("agentedCompanyName", userAgent.getBelongCompanyName());
                        paramMap.put("agentedDepartmentName", userAgent.getBelongDepartmentName());
                        paramMap.put("agentedTrueName", userAgent.getTruename());
                        paramMap.put("agentedPositionName", simpleAgentPositionList != null ? simpleAgentPositionList.get(0).getPositionName() : "");
                        showMessage = MessageEnum.MW000005.getMessage((Map) paramMap);
                    }else{*/
                    showMessage = MessageEnum.MW000001.getMessage((Map) paramMap);
                    //}
                }
            } else {
                showMessage = Constants.SHOWMESSAGESUCCESS;
            }
        } catch (Exception e) {
            log.debug(e.toString());
        }
        return showMessage;
    }

    /**
     * 更新附件sys_file的pm_ins_id字段
     *
     * @param announcement
     */
    private void updatePmInsId(Announcement announcement) {
        List<SysFile> imageFileList = announcement.getAnnouncementFileList();

        String pmInsId = announcement.getPmInsId();
        try {
            if (imageFileList != null && imageFileList.size() > 0) {
                for (SysFile imageFile : imageFileList) {
                    fileExtendService.updatePmInsIdPart(pmInsId, imageFile.getId(), Constants.IMAGE_FILE);
                }
            }

        } catch (Exception e) {
            e.printStackTrace();
        }
    }

    /**
     * 获取用户
     *
     * @param source
     * @param currentUserCode
     * @param operateLog
     * @return
     */
    private IUser judgeUsername(String source, String currentUserCode, SysOperateLog operateLog) {
        IUser user = null;
        Map map = new LinkedHashMap();
        //获取用户
        user = SecurityUtils.getCurrentUser();
        if (user == null) {
            log.debug("用户为空！");
            operateLog.setErrorMsg("请联系管理员，用户信息为空！");
            return null;
        }
        return user;
    }

    /**
     * 保存草稿
     *
     * @return
     */
    @Override
    @Transactional
    public JsonResponse saveDraft(String source, String currentUserCode, Announcement newTopicFrom) {
        /**准备操作参数**/
        SysOperateLog operateLog = new SysOperateLog();
        String param2 = "/saveDraft";
        String params = "Announcement=" + newTopicFrom.toString() + ",source=" + source + ",userCode=" + currentUserCode;
        operateLog.setInterfaceParam(params);
        try {
            /**判断是否是从手机端还是PC端记录操作日志**/
            JsonResponse returnObj = operateLogTool.operationSource(source, currentUserCode, param1, param2, operateLog);
            if (returnObj != null) {
                return returnObj;
            }

            // 保存草稿
            if (StrUtil.isEmpty(newTopicFrom.getId())) {
                newTopicFrom.setId(null);
                IUser iuser = SecurityUtils.getCurrentUser();
                String companyTypeDictDesc = iuser.getBelongCompanyTypeDictValue();
                /**校验表单和下一步审批人是否为空**/
                /**获取登录人所在公司应启动的流程**/
                String processUserType = "";
                if ("01".equals(companyTypeDictDesc)) {
                    processUserType = Constants.PROCESS_C;
                } else {
                    processUserType = Constants.PROCESS_D;
                }
                // 保存业务单据信息
                PmInstence usPmInstence = new PmInstence();
                usPmInstence.setPmInsType(processUserType);
                this.savePlanTask(newTopicFrom, usPmInstence);
            }
            // 更新草稿
            else {
                // 更新表单数据
                newTopicFrom.setModifiedTime(LocalDateTime.now());
                this.update(newTopicFrom);
                // 更新主单据
                PmInstence selectPm = pmInstenceService.findByPmInsId(newTopicFrom.getPmInsId());
                selectPm.setPmInsTitle(newTopicFrom.getAnnouncementTitle());
                selectPm.setModifiedTime(LocalDateTime.now());
                PmInstence newSelectPm = pmInstenceService.update(selectPm);
                //  更新附件
                this.updateFileByPmInsId(newTopicFrom, newTopicFrom.getPmInsId(), newSelectPm.getPmInsType());
            }
        } catch (Exception e) {
            operateLog.setErrorMsg(e.toString());
            Exceptions.printException(e);
            return JsonResponse.fail(null, Constants.MESSAGE_FAIL);
        } finally {
            operateLogService.saveLog(operateLog);
        }
        return JsonResponse.success(newTopicFrom, Constants.MESSAGE_SUCCESS);
    }

    /**
     * 废除草稿
     *
     * @return
     */
    @Override
    @Transactional
    public JsonResponse deleteDraft(String source, String currentUserCode, String pmInsId, Announcement innovationTopicForm) {
        /**准备操作参数**/
        SysOperateLog operateLog = new SysOperateLog();
        String param2 = "/deleteDraft";
        String params = "pmInsId=" + pmInsId + ",source=" + source + ",userCode=" + currentUserCode;
        operateLog.setInterfaceParam(params);
        try {
            /**判断是否是从手机端还是PC端记录操作日志**/
            JsonResponse returnObj = operateLogTool.operationSource(source, currentUserCode, param1, param2, operateLog);
            if (returnObj != null) {
                return returnObj;
            }
            // 删除主单据数据
            PmInstence pmInstence = pmInstenceService.findByPmInsId(pmInsId);
            announcementRepository.deleteByPmInsId(pmInsId);
            // 删除业务单据数据
//            this.delete(innovationTopicForm);
        } catch (Exception e) {
            operateLog.setErrorMsg(e.toString());
            Exceptions.printException(e);
            return JsonResponse.fail(null, Constants.MESSAGE_FAIL);
        } finally {
            operateLogService.saveLog(operateLog);
        }
        return JsonResponse.success(null, Constants.MESSAGE_SUCCESS);
    }

    @Override
    public List<Announcement> getAnnouncementArticleList() {
        List<Announcement> announcementArticleList = announcementRepository.getAnnouncementArticleList();
        if (CollUtil.isNotEmpty(announcementArticleList)) {
            for (Announcement announcement : announcementArticleList) {
                //获取附件信息
                List<SysFile> imageFileList = fileExtendService.getPartFile(announcement.getPmInsId(), Constants.IMAGE_FILE);
                if (imageFileList.size() > 0) {
                    putFile(imageFileList, announcement, Constants.IMAGE_FILE);
                }
            }
        }
        return announcementArticleList;
    }

    @Override
    public Page<Announcement> findAnnouncementPageByColumnId(String columnId, int page, int size) {
        Pageable pageable = this.getPageable(page, size, null, null);
        Page<Announcement> articlePageByColumnId = announcementRepository.findArticlePageByColumnId(pageable);
        List<Announcement> content = articlePageByColumnId.getContent();
        if (CollUtil.isNotEmpty(content)) {
            for (Announcement announcement : content) {
                //获取附件信息
                List<SysFile> imageFileList = fileExtendService.getPartFile(announcement.getPmInsId(), Constants.IMAGE_FILE);
                if (imageFileList.size() > 0) {
                    putFile(imageFileList, announcement, Constants.IMAGE_FILE);
                }
            }
        }

        return articlePageByColumnId;
    }

    @Override
    public Announcement getDataArticleById(String articleId) {
        Announcement byId = this.findById(articleId);
        if (ObjectUtil.isNotEmpty(byId)) {
            int viewsNumber = byId.getViewsNumber();
            byId.setViewsNumber(viewsNumber + 1);
            Announcement update = this.update(byId);

            List<SysFile> imageFileList = fileExtendService.getPartFile(update.getPmInsId(), Constants.IMAGE_FILE);
            if (imageFileList.size() > 0) {
                putFile(imageFileList, update, Constants.IMAGE_FILE);
            }
            return update;
        }
        return byId;
    }

    /**
     * 根据登录人所在公司获取流程
     *
     * @return
     */
    private Map<String, String> getUserProcessMap(String processUserType) {
        Map<String, String> map = Maps.newHashMap();
        switch (processUserType) {
            case Constants.PROCESS_C:
                map.put("processName", Constants.FLOW_PROVINCIAL);
                map.put("processType", "C"); //省公司人员流程
                break;
            case Constants.PROCESS_D:
                map.put("processName", Constants.FLOW_BRANCH);
                map.put("processType", "D");//市公司人员流程
                break;
            default:
                throw new IllegalStateException("Unexpected value: " + processUserType);
        }
        return map;
    }

    /**
     * 更新附件
     *
     * @param announcement 表单
     */
    private void updateFileByPmInsId(Announcement announcement, String pmInsId, String pmInsType) {
        List<SysFile> files = announcement.getAnnouncementFileList();
        try {
            fileExtendService.deleteByPmInsId(pmInsId);
            if (files != null && !files.isEmpty()) {
                for (SysFile file : files) {
                    fileExtendService.updatePmInsId(pmInsId, pmInsType, file.getId(), Constants.IMAGE_FILE);
                }
            }

        } catch (Exception e) {
            e.printStackTrace();
        }
    }

    /**
     * 保存业务数据
     *
     * @return
     * @throws Exception
     */
    boolean savePlanTask(Announcement announcement, PmInstence pmInstence) {
        boolean flag = false;
        IUser iuser = SecurityUtils.getCurrentUser();
        /**保存申请表单任务**/
        try {
            /**保存主单据**/
            if (StrUtil.isEmpty(announcement.getId())) {
                String pmInsId = pmInstence.getPmInsType() + String.valueOf(IdGenerator.idWorker.nextId());//获取到pmInsId
                pmInstence.setPmInsId(pmInsId);
                pmInstence.setPmInsTitle(announcement.getAnnouncementTitle());
                pmInstence.setPmInsCurrentActivity(Constants.ACTIVITY_START);
                pmInstence.setPmInsCreatorCode(iuser.getUsername());
                pmInstence.setPmInsCreatorName(iuser.getTruename());
                pmInstence.setBelongCompanyCode(iuser.getBelongCompanyCode());
                pmInstence.setBelongCompanyTypeDictValue(iuser.getBelongCompanyTypeDictValue());
                pmInstence.setBelongDepartmentCode(iuser.getBelongDepartmentCode());
                pmInstence.setBelongOrgCode(iuser.getBelongOrgCode());
                pmInstenceService.insert(pmInstence);
            }
            /**保存表单数据**/
            if (pmInstence.getId() != null) {
//                Map<String, Object> viewUserOrg = iProgramaDataFormService.findViewUserOrg(iuser.getUsername());
                IUser user = uumsSysUserinfoApi.findByKey(iuser.getUsername(), IAuthService.KeyType.username, Constants.APP_CODE); //审批人
                announcement.setUsername(iuser.getUsername());
                announcement.setTruename(iuser.getTruename());
                announcement.setBelongCompanyCode(iuser.getBelongCompanyCode());
                announcement.setBelongCompanyName(iuser.getBelongCompanyName());
                announcement.setBelongCompanyTypeDictValue(iuser.getBelongCompanyTypeDictValue());
                announcement.setBelongDepartmentCode(iuser.getBelongDepartmentCode());
                announcement.setBelongDepartmentName(iuser.getBelongDepartmentName());
                announcement.setBelongOrgCode(iuser.getBelongOrgCode());
                announcement.setBelongOrgName(iuser.getBelongOrgName());
                announcement.setPmInsId(pmInstence.getPmInsId());
                announcement.setIsPublish(false);
//                announcement.setCompany(String.valueOf(viewUserOrg.get("COMPANYNAME")));//起草人公司
                announcement.setCompany(iuser.getBelongCompanyName());//起草人公司
                announcement.setDepartmentName(iuser.getBelongDepartmentName());//起草人部门
                announcement.setDisplayName(iuser.getBelongCompanyName() + "\\" + iuser.getBelongDepartmentName());//起草人组织路径
//                announcement.setDepartmentName(String.valueOf(viewUserOrg.get("DEPARTMENTNAME")));//起草人部门
//                announcement.setDisplayName(String.valueOf(viewUserOrg.get("DISPLAYNAME")));//起草人组织路径
                //announcement.setProgramaDisplayName("公告");
                announcement.setAnnouncementCoding("999");
                //announcement.setStickFlag("0");
                List<SysFile> imageFileList = announcement.getAnnouncementFileList();

                if (imageFileList != null) {
                    for (SysFile sysFile : imageFileList) {
                        announcement.setAnnouncementAccessoryId(sysFile.getId());
                    }
                }
                Announcement announcement1 = this.insert(announcement);
                this.updatePmInsId(announcement);//更新附件
                if (announcement1 != null) {
                    flag = true;
                }
            }
        } catch (Exception e) {
            flag = false;
            TransactionAspectSupport.currentTransactionStatus().setRollbackOnly();
            log.debug(e.getMessage());
            return flag;
        }
        return flag;
    }

}
