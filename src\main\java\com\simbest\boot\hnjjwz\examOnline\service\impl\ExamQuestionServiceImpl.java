package com.simbest.boot.hnjjwz.examOnline.service.impl;

import com.simbest.boot.base.exception.Exceptions;
import com.simbest.boot.base.service.impl.LogicService;
import com.simbest.boot.base.web.response.JsonResponse;
import com.simbest.boot.hnjjwz.constants.Constants;
import com.simbest.boot.hnjjwz.examOnline.model.ExamAnswer;
import com.simbest.boot.hnjjwz.examOnline.model.ExamQuestion;
import com.simbest.boot.hnjjwz.examOnline.repository.ExamQuestionRepository;
import com.simbest.boot.hnjjwz.examOnline.service.IExamAnswerService;
import com.simbest.boot.hnjjwz.examOnline.service.IExamInfoService;
import com.simbest.boot.hnjjwz.examOnline.service.IExamQuestionService;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.data.domain.Page;
import org.springframework.data.domain.Pageable;
import org.springframework.stereotype.Service;

import java.util.LinkedHashMap;
import java.util.List;
import java.util.Map;
import java.util.Set;

/**
 * <AUTHOR>
 * @Data 2019/05/08
 * @Description 问题
 */
@Slf4j
@Service
public class ExamQuestionServiceImpl extends LogicService<ExamQuestion,String> implements IExamQuestionService {

    private ExamQuestionRepository examQuestionRepository;

    @Autowired
    public ExamQuestionServiceImpl ( ExamQuestionRepository examQuestionRepository) {
        super(examQuestionRepository);
        this.examQuestionRepository = examQuestionRepository;
    }

    @Autowired
    private IExamInfoService examInfoServiceImpl;

    @Autowired
    private IExamAnswerService iExamAnswerService;

    /**
     * 查询可用的题库内容
     * @return
     */
    @Override
    public JsonResponse findEnabledExamQuestion ( ) {
        //查询可用的题库内容
        Set<Map<String,Object>> mapSet = examQuestionRepository.findEnabledExamQuestion();
        Long onlyRecord =0L;
        //查询之后，生成试卷
        try{
            onlyRecord = examInfoServiceImpl.createExamInfo();
        }catch ( Exception e ){
            Exceptions.printException( e );
            return JsonResponse.fail( null,"获取题库内容失败！" );
        }
        Map<String,Object> map = new LinkedHashMap<>(  );
        map.put( Constants.QUESTION_BANK_KEY ,mapSet );
        map.put( Constants.ONLY_RECORD_KEY, onlyRecord);


        return JsonResponse.success( map );
    }

    /**
     *获取随机试题
     * @param questionBankCode
     * @param choice
     * @param questionType
     * @return
     */
    @Override
    public List<ExamQuestion> customFindAll(String questionBankCode,String choice,String questionType) {
        return examQuestionRepository.customFindAll(questionBankCode,choice,questionType);
    }

    /**
     *获取全部试题
     * @return
     */
    @Override
    public JsonResponse findAllExamQuestion(Pageable pageable) {

        Page<ExamQuestion> allExamQuestion = examQuestionRepository.findAllExamQuestion(pageable);//查出全部试题

        /*List<ExamQuestion> content = allExamQuestion.getContent();
        for(ExamQuestion examQuestion:content){
            List<ExamAnswer> examAnswerQuestionCode = iExamAnswerService.customFindExamAnswer(examQuestion.getQuestionCode());
            if(examAnswerQuestionCode.size()>0){
                examQuestion.setExamAnswerList(examAnswerQuestionCode);

            }

        }*/

        return JsonResponse.success(allExamQuestion);
    }


}
