package com.simbest.boot.hnjjwz.backstage.templateLayout.service.Impl;/**
 * Created by KZH on 2019/5/30 17:23.
 */

import cn.hutool.core.map.MapUtil;
import com.simbest.boot.base.service.impl.LogicService;
import com.simbest.boot.base.web.response.JsonResponse;
import com.simbest.boot.hnjjwz.attachment.service.IFileExtendService;
import com.simbest.boot.hnjjwz.backstage.announcement.service.IAnnouncementService;
import com.simbest.boot.hnjjwz.backstage.indexMenu.model.IndexMenu;
import com.simbest.boot.hnjjwz.backstage.indexMenu.model.SubmenuMenu;
import com.simbest.boot.hnjjwz.backstage.indexMenu.service.IIndexMenuService;
import com.simbest.boot.hnjjwz.backstage.indexMenu.service.ISubmenuMenuService;
import com.simbest.boot.hnjjwz.backstage.programa.model.ProgramaInfo;
import com.simbest.boot.hnjjwz.backstage.programa.service.IProgramaInfoService;
import com.simbest.boot.hnjjwz.backstage.slideshow.service.ISlideShowService;
import com.simbest.boot.hnjjwz.backstage.taskStudy.model.TaskStudy;
import com.simbest.boot.hnjjwz.backstage.taskStudy.service.ITaskStudyService;
import com.simbest.boot.hnjjwz.backstage.templateLayout.model.Menu;
import com.simbest.boot.hnjjwz.backstage.templateLayout.model.TemplateLayout;
import com.simbest.boot.hnjjwz.backstage.templateLayout.repository.TemplateLayoutRepository;
import com.simbest.boot.hnjjwz.backstage.templateLayout.service.ITemplateLayoutService;
import com.simbest.boot.hnjjwz.column.familyWind.model.FamilyWind;
import com.simbest.boot.hnjjwz.column.familyWind.model.Record;
import com.simbest.boot.hnjjwz.column.familyWind.service.IFamilyWindService;
import com.simbest.boot.hnjjwz.column.familyWind.service.IRecordService;
import com.simbest.boot.hnjjwz.constants.Constants;
import com.simbest.boot.hnjjwz.process.wf.model.ProgramaDataForm;
import com.simbest.boot.hnjjwz.process.wf.service.IProgramaDataFormService;
import com.simbest.boot.security.IUser;
import com.simbest.boot.sys.model.SysFile;
import com.simbest.boot.sys.model.SysLogLogin;
import com.simbest.boot.sys.service.ISysFileService;
import com.simbest.boot.sys.service.ISysLogLoginService;
import com.simbest.boot.util.DateUtil;
import com.simbest.boot.util.security.LoginUtils;
import com.simbest.boot.util.security.SecurityUtils;
import com.simbest.boot.util.server.HostUtil;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.data.domain.Pageable;
import org.springframework.stereotype.Service;

import javax.servlet.http.HttpServletRequest;
import java.io.File;
import java.util.ArrayList;
import java.util.List;
import java.util.Map;

/**
 * <AUTHOR>
 * @create 2019-05-30 17:23
 * @desc
 **/
@Slf4j
@Service
public class TemplateLayoutServiceImpl extends LogicService<TemplateLayout,String> implements ITemplateLayoutService {

    private TemplateLayoutRepository templateLayoutRepository;

    @Autowired
    private IIndexMenuService iIndexMenuService;//父菜单

    @Autowired
    private ISubmenuMenuService iSubmenuMenuService;//子菜单

    @Autowired
    private ISlideShowService slideShowService;//轮播图接口

    @Autowired
    private IAnnouncementService announcementService;//公告接口

    @Autowired
    private IProgramaDataFormService iProgramaDataFormService;//栏目详情信息

    @Autowired
    private ITaskStudyService iTaskStudyService;

    @Autowired
    private LoginUtils loginUtils;

    @Autowired
    private IFileExtendService fileExtendService;

    @Autowired
    private IProgramaInfoService iProgramaInfoService;

    @Autowired
    private IFamilyWindService iFamilyWindService;

    @Autowired
    private IRecordService iRecordService;

    @Autowired
    private ISysLogLoginService logLoginService;

    @Autowired
    public TemplateLayoutServiceImpl(TemplateLayoutRepository templateLayoutRepository) {
        super(templateLayoutRepository);
        this.templateLayoutRepository = templateLayoutRepository;

    }

    /**
     * 首页模板
     */
    @Override
    public JsonResponse findTemplateLayout() {
        List<TemplateLayout> templateList = templateLayoutRepository.findTemplateLayout();

        for (TemplateLayout templateLayout : templateList) {

            if (templateLayout.getTemplateUrl() == null) {
                if (templateLayout.getTemplateType().contains("indexmenu")) {
                    List<IndexMenu> indexMenuList = iIndexMenuService.findIndexMenu();
                    List<SubmenuMenu> submenuMenuList = iSubmenuMenuService.findSubmenuMenu();

                    List<Menu> list = new ArrayList<>();//初始化菜单
                    for (IndexMenu indeMenu : indexMenuList) {
                        Menu menu = new Menu();

                        List<SubmenuMenu> list3 = new ArrayList<>();//初始化子集合

                        for (SubmenuMenu submenuMenu : submenuMenuList) {
                            if (submenuMenu.getMenuName().equals(indeMenu.getMenuName())) {//如果子父名字相等存 父名字 子集合
                                menu.setId(indeMenu.getId());
                                menu.setMenuName(indeMenu.getMenuName());
                                menu.setMenuUrl(indeMenu.getMenuUrl());
                                list3.add(submenuMenu);
                                menu.setMenuChildren(list3);
                            } else {
                                menu.setId(indeMenu.getId());
                                menu.setMenuName(indeMenu.getMenuName());
                                menu.setMenuUrl(indeMenu.getMenuUrl());
                                menu.setMenuChildren(list3);
                            }
                        }
                        list.add(menu);
                    }
                    templateLayout.setTemplateData(list);//获取菜单
                } else if (templateLayout.getTemplateType().contains("affiche")) {
                    templateLayout.setTemplateData(announcementService.findAnnouncement());//获取公告
                } else if (templateLayout.getTemplateType().contains("slideshow")) {
                    templateLayout.setTemplateData(slideShowService.findSlideShow());//获取轮播图
                } else if (templateLayout.getTemplateType().contains("columnlist")) {
                    templateLayout.setTemplateData(iProgramaDataFormService.findProgramaDataForm("004"));
                }else if(templateLayout.getTemplateType().contains("Tab1")){
                    templateLayout.setTemplateData(iProgramaDataFormService.findProgramaDataForm(templateLayout.getLocationId()));
                }else if(templateLayout.getTemplateType().contains("Tab2")){
                    templateLayout.setTemplateData(iProgramaDataFormService.findProgramaDataForm(templateLayout.getLocationId()));
                }else if(templateLayout.getTemplateType().contains("Tab3")){
                    templateLayout.setTemplateData(iProgramaDataFormService.findProgramaDataForm(templateLayout.getLocationId()));
                }else if(templateLayout.getTemplateType().contains("Tab4")){
                    templateLayout.setTemplateData(iProgramaDataFormService.findProgramaDataForm(templateLayout.getLocationId()));
                }else if(templateLayout.getTemplateType().contains("Tab5")){
                    templateLayout.setTemplateData(iProgramaDataFormService.findProgramaDataForm(templateLayout.getLocationId()));
                }else if(templateLayout.getTemplateType().contains("Tab6")){
                    templateLayout.setTemplateData(iProgramaDataFormService.findProgramaDataForm(templateLayout.getLocationId()));
                }else if(templateLayout.getTemplateType().contains("Tab7")){
                    templateLayout.setTemplateData(iProgramaDataFormService.findProgramaDataForm(templateLayout.getLocationId()));
                }

            }

        }
        return JsonResponse.success(templateList);
    }

    /**
     * 规章制度模板
     */
    @Override
    public JsonResponse findRulesGaugeLayout() {

        List<TemplateLayout> templateList = templateLayoutRepository.findRulesGaugeLayout();
        for (TemplateLayout templateLayout : templateList) {
            if(templateLayout.getLocationId()!=null){
                String locationId = templateLayout.getLocationId();//获取栏目编码
                //获取栏目列表集合
                List<ProgramaInfo> sonByProgramaInfo = iProgramaInfoService.findSonByProgramaInfo(locationId);
                if(sonByProgramaInfo.size()!=0){//说明能够获取到子栏目
                    for (ProgramaInfo programaInfo :sonByProgramaInfo ) {
                        List<ProgramaDataForm> programaDataForm = iProgramaDataFormService.findProgramaDataForm(programaInfo.getProgramaCode());
                        //if(programaDataForm.size()!=0){
                            programaInfo.setTemplateData(programaDataForm);
                        //}else {
                        //    return JsonResponse.fail(-1,"获取子栏目详细信息失败");
                        //}
                    }
                    templateLayout.setTemplateData(sonByProgramaInfo);
                }else {
                    List<ProgramaDataForm> programaDataForm = iProgramaDataFormService.findProgramaDataForm(locationId);
                    if(programaDataForm.size()!=0){
                        templateLayout.setTemplateData(programaDataForm);
                    }else {
                        return JsonResponse.fail(-1,"获取栏目详细信息失败");
                    }

                }

            }

        }

        return JsonResponse.success(templateList);
    }

    /**
     * 巡察工作模板
     */
    @Override
    public JsonResponse findInspectionWorkLayout() {
        List<TemplateLayout> templateList = templateLayoutRepository.findInspectionWorkLayout();

        return this.universal(templateList);
    }

    /**
     * 嵌入式防控监督模板
     */
    @Override
    public JsonResponse findEmbeddedLayout() {
        List<TemplateLayout> templateList = templateLayoutRepository.findEmbeddedLayout();

        return this.universal(templateList);
    }

    /**
     * 课题研究模板
     */
    @Override
    public JsonResponse findResearchLayout() {
        List<TemplateLayout> templateList = templateLayoutRepository.findResearchLayout();

        for (TemplateLayout templateLayout:templateList) {
            if(templateLayout.getLocationId()!=null){
                String programaDisplayName = templateLayout.getLocationName();
                List<TaskStudy> taskStudy = iTaskStudyService.findTaskStudy(programaDisplayName);
                for (TaskStudy t:taskStudy) {
                    List<SysFile> imageFileList2 = fileExtendService.getFile(t.getPmInsId());
                    t.setTaskStudyFile(imageFileList2);
                }

                templateLayout.setTemplateData(taskStudy);
            }

        }
        return JsonResponse.success(templateList);
    }

    /**
     * 廉洁教育模板
     */
    @Override
    public JsonResponse findEducationLayout() {
        List<TemplateLayout> templateList = templateLayoutRepository.findEducationLayout();

        return this.universal(templateList);
    }

    /**
     * 廉洁文化模板
     */
    @Override
    public JsonResponse findCultureLayout() {
        List<TemplateLayout> templateList = templateLayoutRepository.findCultureLayout();

        return this.universal(templateList);
    }

    /**
     * 信访举报模板
     */
    @Override
    public JsonResponse findPetitionLetter() {
        List<TemplateLayout> templateList = templateLayoutRepository.findPetitionLetter();

        return this.universal(templateList);
    }

    /**
     * 纪律审查与监督模板
     */
    @Override
    public JsonResponse findSupervisionLayout() {
        List<TemplateLayout> templateList = templateLayoutRepository.findSupervisionLayout();

        return this.universal(templateList);
    }

    /**
     * 家风栏目模板
     * @return
     */
    @Override
    public JsonResponse findDanColumnsLayout(String locationType) {
        List<TemplateLayout> templateList = templateLayoutRepository.findDanColumnsLayout(locationType);

        IUser currentUser = SecurityUtils.getCurrentUser();//取出当前登录人对象
        String username = currentUser.getUsername();//获取真实姓名
        List<Record> recordList = iRecordService.findRecordList(username);
        for (TemplateLayout templateLayout : templateList){
            List<FamilyWind> familyType = iFamilyWindService.findFamilyType(templateLayout.getLocationName());//使用位置名称获取到响应的数据
            if(recordList.size()>0){
                 for(FamilyWind familyWind:familyType){
                    for(Record record: recordList){
                        if(record.getOnVoteId().equals(familyWind.getId())){
                            // 是否允许投票
                            familyWind.setVote(false);
                            break;
                        }

                    }
                 }
            }
            templateLayout.setTemplateData(familyType);
        }
        return JsonResponse.success(templateList);
    }

    @Override
    public void simulatedLanding(String loginUser, String appCode) {

         loginUtils.manualLogin( loginUser , appCode);
    }

    @Override
    public void recordSuccessLogin(Map<String, Object> paramMap, HttpServletRequest request) {

        SysLogLogin logLogin = SysLogLogin.builder()
                .account(MapUtil.getStr(paramMap,"username"))
                .loginEntry(1) //CAS登录入口
                .loginType(0)  //用户名登录方式
                .loginTime(DateUtil.getCurrent())
                .isSuccess(true)
                .trueName(MapUtil.getStr(paramMap,"truename"))
                .belongOrgName(MapUtil.getStr(paramMap,"belongOrgName"))
                .build();
        logLogin.setIp(HostUtil.getClientIpAddress(request));
        logLogin.setSessionid(request.getSession().getId());
        logLoginService.insert(logLogin);
    }

    @Override
    public List<TemplateLayout> findtemplateLayout(String id) {
        return templateLayoutRepository.findtemplateLayout(id);
    }

    @Override
    public TemplateLayout findlocationName(String locationName) {
        return templateLayoutRepository.findlocationName(locationName);
    }

    @Override
    /**
     * 分公司拆分模块
     */
    public JsonResponse findIndexTemplateDownCompany() {
            List<TemplateLayout> templateList = templateLayoutRepository.findIndexTemplateDownCompany();
            return this.universal(templateList);
    }

    @Override
    /**
     * 以案示警拆分模块
     */
    public JsonResponse findIndexTemplateWarnCompany() {
        List<TemplateLayout> templateList = templateLayoutRepository.findIndexTemplateWarnCompany();
        return this.universal(templateList);
    }
    /**
     * 通用
     */
    private JsonResponse universal(List<TemplateLayout> templateList){

        for (TemplateLayout templateLayout : templateList) {
            if(templateLayout.getLocationId()!=null){
                String locationId = templateLayout.getLocationId();
                List<ProgramaDataForm> programaDataForm = iProgramaDataFormService.findProgramaDataForm(locationId);
                templateLayout.setTemplateData(programaDataForm);
            }

        }
        return JsonResponse.success(templateList);
    }
}
